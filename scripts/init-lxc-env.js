#!/usr/bin/env node

/**
 * LXC Environment Initialization Script
 * 
 * This script initializes the LXC environment by creating the necessary directories
 * and setting the correct permissions.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

// Configuration
const config = {
  // LXC paths
  lxcPaths: [
    path.join(os.homedir(), '.lxc'),
    path.join(process.cwd(), '.lxc'),
    path.join(process.cwd(), 'data', 'lxc')
  ],
  // LXC config paths
  configPaths: [
    path.join(os.homedir(), '.lxc', 'config')
  ],
  // Directories to create in each LXC path
  directories: [
    'containers',
    'images',
    'logs',
    'cache',
    'templates'
  ]
};

/**
 * Create a directory if it doesn't exist
 */
function ensureDirectoryExists(dirPath) {
  try {
    if (!fs.existsSync(dirPath)) {
      console.log(`Creating directory: ${dirPath}`);
      fs.mkdirSync(dirPath, { recursive: true });
    }
  } catch (error) {
    console.error(`Error creating directory ${dirPath}:`, error);
  }
}

/**
 * Check if a path is writable
 */
function isPathWritable(dirPath) {
  try {
    // Check if the directory exists
    if (!fs.existsSync(dirPath)) {
      ensureDirectoryExists(dirPath);
    }

    // Try to create a temporary file in the directory
    const testFile = path.join(dirPath, `.test-write-${Date.now()}`);
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    return true;
  } catch (error) {
    console.error(`Path ${dirPath} is not writable:`, error);
    return false;
  }
}

/**
 * Initialize LXC environment
 */
function initializeLxcEnvironment() {
  console.log('Initializing LXC environment...');

  // Create LXC paths
  for (const lxcPath of config.lxcPaths) {
    ensureDirectoryExists(lxcPath);
    
    // Create subdirectories
    for (const dir of config.directories) {
      ensureDirectoryExists(path.join(lxcPath, dir));
    }
    
    // Check if the path is writable
    if (isPathWritable(lxcPath)) {
      console.log(`LXC path ${lxcPath} is writable.`);
    } else {
      console.warn(`LXC path ${lxcPath} is not writable. You may need to run this script with sudo.`);
    }
  }

  // Create LXC config paths
  for (const configPath of config.configPaths) {
    ensureDirectoryExists(configPath);
    
    // Check if the path is writable
    if (isPathWritable(configPath)) {
      console.log(`LXC config path ${configPath} is writable.`);
    } else {
      console.warn(`LXC config path ${configPath} is not writable. You may need to run this script with sudo.`);
    }
  }

  // Create a default LXC config file if it doesn't exist
  const defaultConfigPath = path.join(os.homedir(), '.lxc', 'config', 'default.conf');
  if (!fs.existsSync(defaultConfigPath)) {
    try {
      console.log(`Creating default LXC config file: ${defaultConfigPath}`);
      fs.writeFileSync(defaultConfigPath, `
# Default LXC configuration
lxc.net.0.type = veth
lxc.net.0.flags = up
lxc.net.0.link = lxcbr0
lxc.apparmor.profile = generated
lxc.apparmor.allow_nesting = 1
`);
    } catch (error) {
      console.error(`Error creating default LXC config file:`, error);
    }
  }

  console.log('LXC environment initialization complete.');
}

// Run the initialization
initializeLxcEnvironment();
