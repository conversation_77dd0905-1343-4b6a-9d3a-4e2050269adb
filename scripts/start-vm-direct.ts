#!/usr/bin/env ts-node

/**
 * Start VM Directly Script
 * 
 * This script creates and starts a MicroVM using our modules directly,
 * bypassing the API layer.
 */

import * as path from 'path';
import { MicroVmManager } from '../lib/containerization/microvm/core';
import { loadImagesForMicroVM, getAllAvailableImages } from '../lib/containerization/microvm/image-loader';

// Configure process.cwd() to be the project root for proper path resolution
process.env.APP_ROOT = path.resolve(__dirname, '..');
process.chdir(process.env.APP_ROOT);

// Create a basic logger for testing (to avoid dependency on the app logger)
const logger = {
  info: (...args: any[]) => console.log('[INFO]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
  debug: (...args: any[]) => console.log('[DEBUG]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args)
};

// Mock the logger module
// Note: We're doing this with require overrides instead of Jest mocks
// to avoid TypeScript errors with Jest types
const originalRequire = require;
require = function(modulePath: string) {
  if (modulePath === '@/lib/logger') {
    return { logger };
  }
  return originalRequire.apply(this, arguments);
};

async function main() {
  console.log('\n==== Starting VM Directly ====\n');
  
  try {
    // Initialize MicroVmManager
    const vmManager = new MicroVmManager();
    
    // VM configuration
    const vmName = `direct-vm-${Date.now().toString().slice(-6)}`;
    const memSizeMib = 2048;
    const vcpuCount = 2;
    
    console.log('Scanning for available images...');
    const { kernelImages, rootfsImages } = await getAllAvailableImages();
    
    if (kernelImages.length === 0) {
      throw new Error('No kernel images found. Please check if your images are in the correct location.');
    }
    
    if (rootfsImages.length === 0) {
      throw new Error('No rootfs images found. Please check if your images are in the correct location.');
    }
    
    // Use Ubuntu Bionic if available, otherwise use the first rootfs
    const preferredRootfs = 'bionic';
    console.log(`Looking for preferred rootfs type: ${preferredRootfs}`);
    
    // Get the image paths
    const { kernelPath, rootfsPath } = await loadImagesForMicroVM(preferredRootfs);
    
    console.log(`\nSelected kernel: ${kernelPath}`);
    console.log(`Selected rootfs: ${rootfsPath}`);
    console.log(`\nCreating VM: ${vmName}`);
    console.log(`Memory: ${memSizeMib} MiB`);
    console.log(`CPUs: ${vcpuCount}\n`);
    
    // Create the MicroVM
    const microvm = await vmManager.createMicroVm({
      name: vmName,
      memSizeMib,
      vcpuCount,
      kernel: {
        path: kernelPath,
        bootArgs: "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp"
      },
      rootfs: {
        path: rootfsPath,
        readOnly: false
      },
      labels: {
        createdBy: 'start-vm-direct.ts',
        createdAt: new Date().toISOString(),
      }
    });
    
    console.log('Starting the VM...');
    await microvm.start();
    
    const vmInfo = await microvm.getInfo();
    console.log('\nVM Info:', JSON.stringify(vmInfo, null, 2));
    
    console.log('\n==== VM Started Successfully ====');
    console.log(`VM ID: ${vmInfo.id}`);
    console.log(`VM Name: ${vmInfo.name}`);
    console.log(`VM State: ${vmInfo.state}`);
    
    console.log('\nPress Ctrl+C to exit (VM will continue running in the background)');
    
    // Keep the process running to show VM info
    process.stdin.resume();
    
  } catch (error: any) {
    console.error('Error starting VM:', error.message);
    process.exit(1);
  }
}

main(); 