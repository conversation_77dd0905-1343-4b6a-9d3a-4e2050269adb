#!/usr/bin/env node

/**
 * Check Firecracker Requirements Script
 * 
 * This script checks if Firecracker is properly installed and configured.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

// Colors for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Locations to check for Firecracker binary
const firecrackerPaths = [
  '/usr/bin/firecracker',
  '/usr/local/bin/firecracker',
  '/opt/firecracker/firecracker',
  path.join(os.homedir(), 'firecracker')
];

// Locations to check for jailer binary
const jailerPaths = [
  '/usr/bin/jailer',
  '/usr/local/bin/jailer',
  '/opt/firecracker/jailer',
  path.join(os.homedir(), 'jailer')
];

// Kernel and rootfs paths
const kernelPath = path.join(process.cwd(), 'resources', 'kernels', 'vmlinux.bin');
const rootfsPath = path.join(process.cwd(), 'resources', 'lxc-images', 'bionic.rootfs.ext4');

// Function to check if a path exists
function checkPath(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Function to run a command and return stdout
function runCommand(command, ignoreError = false) {
  try {
    return execSync(command, { encoding: 'utf-8' }).trim();
  } catch (error) {
    if (ignoreError) {
      return '';
    }
    throw error;
  }
}

// Function to check the user's permissions
function checkUserPermissions() {
  // Check if running as root
  const isRoot = process.getuid && process.getuid() === 0;
  
  // Check if user is in kvm group
  let inKvmGroup = false;
  try {
    const groups = runCommand('groups');
    inKvmGroup = groups.includes('kvm');
  } catch (error) {
    // Ignore
  }
  
  return { isRoot, inKvmGroup };
}

// Function to check availability of /dev/kvm
function checkKvm() {
  const kvmExists = checkPath('/dev/kvm');
  let kvmAccessible = false;
  
  if (kvmExists) {
    try {
      runCommand('ls -la /dev/kvm');
      kvmAccessible = true;
    } catch (error) {
      // KVM exists but not accessible
    }
  }
  
  return { kvmExists, kvmAccessible };
}

// Main check function
function checkFirecracker() {
  console.log(`${colors.cyan}==== Firecracker Requirements Check ====${colors.reset}\n`);
  
  // 1. Check system requirements
  console.log(`${colors.blue}1. Checking system requirements:${colors.reset}`);
  
  // Check Linux kernel version
  const kernelVersion = runCommand('uname -r');
  const isKernelValid = parseFloat(kernelVersion) >= 4.14;
  console.log(`   - Linux Kernel: ${kernelVersion} ${isKernelValid ? colors.green + '✓' : colors.red + '✗'} (Minimum 4.14 required)${colors.reset}`);
  
  // Check CPU architecture
  const cpuArch = runCommand('uname -m');
  const isArchValid = cpuArch === 'x86_64' || cpuArch === 'aarch64';
  console.log(`   - CPU Architecture: ${cpuArch} ${isArchValid ? colors.green + '✓' : colors.red + '✗'} (x86_64 or aarch64 required)${colors.reset}`);
  
  // 2. Check KVM
  console.log(`\n${colors.blue}2. Checking KVM:${colors.reset}`);
  const { kvmExists, kvmAccessible } = checkKvm();
  console.log(`   - KVM Device: ${kvmExists ? colors.green + '✓' : colors.red + '✗'} (Device exists)${colors.reset}`);
  console.log(`   - KVM Accessible: ${kvmAccessible ? colors.green + '✓' : colors.red + '✗'} (Current user can access)${colors.reset}`);
  
  // 3. Check User Permissions
  console.log(`\n${colors.blue}3. Checking user permissions:${colors.reset}`);
  const { isRoot, inKvmGroup } = checkUserPermissions();
  console.log(`   - Running as root: ${isRoot ? colors.green + '✓' : colors.yellow + '✗'} ${colors.reset}`);
  console.log(`   - User in kvm group: ${inKvmGroup ? colors.green + '✓' : colors.yellow + '✗'} ${colors.reset}`);
  
  // 4. Check Firecracker binary
  console.log(`\n${colors.blue}4. Checking Firecracker binaries:${colors.reset}`);
  
  // Find firecracker binary
  let firecrackerFound = false;
  let firecrackerPath = '';
  for (const binPath of firecrackerPaths) {
    if (checkPath(binPath)) {
      firecrackerFound = true;
      firecrackerPath = binPath;
      break;
    }
  }
  console.log(`   - Firecracker binary: ${firecrackerFound ? colors.green + '✓' + colors.reset + ' (' + firecrackerPath + ')' : colors.red + '✗' + colors.reset + ' (Not found)'}`);
  
  // Find jailer binary
  let jailerFound = false;
  let jailerPath = '';
  for (const binPath of jailerPaths) {
    if (checkPath(binPath)) {
      jailerFound = true;
      jailerPath = binPath;
      break;
    }
  }
  console.log(`   - Jailer binary: ${jailerFound ? colors.green + '✓' + colors.reset + ' (' + jailerPath + ')' : colors.yellow + '✗' + colors.reset + ' (Optional, not found)'}`);
  
  // Check firecracker version if found
  if (firecrackerFound) {
    try {
      const fcVersion = runCommand(`${firecrackerPath} --version`, true);
      console.log(`   - Firecracker version: ${fcVersion || 'Unknown'}`);
    } catch (error) {
      console.log(`   - ${colors.red}Failed to get Firecracker version${colors.reset}`);
    }
  }
  
  // 5. Check VM Images
  console.log(`\n${colors.blue}5. Checking VM Images:${colors.reset}`);
  
  // Check kernel image
  const kernelExists = checkPath(kernelPath);
  console.log(`   - Kernel image: ${kernelExists ? colors.green + '✓' + colors.reset + ' (' + kernelPath + ')' : colors.red + '✗' + colors.reset + ' (Not found)'}`);
  
  // Check rootfs image
  const rootfsExists = checkPath(rootfsPath);
  console.log(`   - Rootfs image: ${rootfsExists ? colors.green + '✓' + colors.reset + ' (' + rootfsPath + ')' : colors.red + '✗' + colors.reset + ' (Not found)'}`);
  
  // 6. Summary
  console.log(`\n${colors.cyan}==== Summary ====${colors.reset}`);
  const allRequired = isKernelValid && isArchValid && kvmExists && kvmAccessible && firecrackerFound && kernelExists && rootfsExists;
  
  if (allRequired) {
    console.log(`\n${colors.green}✓ All required components are present and properly configured!${colors.reset}`);
  } else {
    console.log(`\n${colors.red}✗ Some required components are missing or improperly configured.${colors.reset}`);
    
    // Print missing requirements
    console.log(`\n${colors.yellow}Missing or Misconfigured Items:${colors.reset}`);
    if (!isKernelValid) console.log(`   - ${colors.red}Linux kernel version is too old${colors.reset}`);
    if (!isArchValid) console.log(`   - ${colors.red}Unsupported CPU architecture${colors.reset}`);
    if (!kvmExists) console.log(`   - ${colors.red}/dev/kvm is missing${colors.reset}`);
    if (kvmExists && !kvmAccessible) console.log(`   - ${colors.red}User doesn't have permission to access /dev/kvm${colors.reset}`);
    if (!firecrackerFound) console.log(`   - ${colors.red}Firecracker binary not found${colors.reset}`);
    if (!kernelExists) console.log(`   - ${colors.red}Kernel image not found at ${kernelPath}${colors.reset}`);
    if (!rootfsExists) console.log(`   - ${colors.red}Rootfs image not found at ${rootfsPath}${colors.reset}`);
    
    // Installation instructions
    console.log(`\n${colors.cyan}Installation Steps:${colors.reset}`);
    
    if (!firecrackerFound) {
      console.log(`\n1. ${colors.yellow}Install Firecracker:${colors.reset}`);
      console.log(`   mkdir -p release
   curl -fsSL -o firecracker.tgz "https://github.com/firecracker-microvm/firecracker/releases/download/v1.4.0/firecracker-v1.4.0-x86_64.tgz" 
   tar -xf firecracker.tgz -C release
   sudo cp release/release-v1.4.0-x86_64/firecracker-v1.4.0-x86_64 /usr/local/bin/firecracker
   sudo cp release/release-v1.4.0-x86_64/jailer-v1.4.0-x86_64 /usr/local/bin/jailer
   sudo chmod +x /usr/local/bin/firecracker /usr/local/bin/jailer`);
    }
    
    if (!kvmExists || !kvmAccessible) {
      console.log(`\n2. ${colors.yellow}Set up KVM permissions:${colors.reset}`);
      console.log(`   sudo modprobe kvm
   sudo chmod 666 /dev/kvm
   # For permanent access:
   sudo usermod -aG kvm ${os.userInfo().username}`);
    }
  }
}

// Run the check
checkFirecracker(); 