#!/bin/bash

# Desktop VM Setup Script
# 
# This script sets up the necessary components for desktop VM functionality
# including VNC server, Guacamole, and desktop environments.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check available disk space (at least 5GB)
    available_space=$(df / | awk 'NR==2 {print $4}')
    required_space=5242880  # 5GB in KB
    
    if [[ $available_space -lt $required_space ]]; then
        log_error "Insufficient disk space. At least 5GB is required."
        exit 1
    fi
    
    log_success "System requirements check passed"
}

# Install VNC server components
install_vnc() {
    log_info "Installing VNC server components..."
    
    # Update package lists
    sudo apt-get update
    
    # Install VNC server and related packages
    sudo apt-get install -y \
        tightvncserver \
        x11vnc \
        xvfb \
        novnc \
        websockify \
        xorg \
        dbus-x11
    
    log_success "VNC server components installed"
}

# Install desktop environments
install_desktop_environments() {
    log_info "Installing desktop environments..."
    
    # Install XFCE (lightweight)
    sudo apt-get install -y \
        xfce4 \
        xfce4-goodies \
        xfce4-terminal \
        thunar \
        mousepad \
        ristretto \
        task-xfce-desktop
    
    # Install additional desktop environments (optional)
    read -p "Do you want to install additional desktop environments? (y/N): " install_additional
    
    if [[ $install_additional =~ ^[Yy]$ ]]; then
        # Install GNOME
        log_info "Installing GNOME desktop environment..."
        sudo apt-get install -y ubuntu-desktop-minimal
        
        # Install KDE Plasma
        log_info "Installing KDE Plasma desktop environment..."
        sudo apt-get install -y kde-plasma-desktop
        
        # Install LXDE
        log_info "Installing LXDE desktop environment..."
        sudo apt-get install -y lxde
    fi
    
    log_success "Desktop environments installed"
}

# Install Guacamole
install_guacamole() {
    log_info "Installing Apache Guacamole..."
    
    # Create Guacamole directory
    mkdir -p ~/guacamole
    cd ~/guacamole
    
    # Download Guacamole Docker Compose configuration
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  guacd:
    image: guacamole/guacd:latest
    container_name: guacd
    restart: unless-stopped
    volumes:
      - ./drive:/drive:rw
      - ./record:/record:rw
    networks:
      - guacamole

  postgres:
    image: postgres:13
    container_name: guacamole-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: guacamole_db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: guacamole_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d:ro
    networks:
      - guacamole

  guacamole:
    image: guacamole/guacamole:latest
    container_name: guacamole
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      GUACD_HOSTNAME: guacd
      POSTGRES_DATABASE: guacamole_db
      POSTGRES_HOSTNAME: postgres
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: guacamole_pass
    depends_on:
      - guacd
      - postgres
    networks:
      - guacamole

volumes:
  postgres_data:

networks:
  guacamole:
    driver: bridge
EOF
    
    # Create directories for Guacamole
    mkdir -p drive record init
    
    # Download Guacamole database initialization script
    curl -o init/initdb.sql https://raw.githubusercontent.com/apache/guacamole-server/master/src/protocols/rdp/guac_rdp_keymap.c
    
    # Start Guacamole services
    docker-compose up -d
    
    log_success "Apache Guacamole installed and started"
    log_info "Guacamole web interface available at: http://localhost:8080/guacamole"
    log_info "Default credentials: guacadmin / guacadmin"
}

# Install additional tools
install_tools() {
    log_info "Installing additional tools..."
    
    # Install browser and common applications
    sudo apt-get install -y \
        firefox \
        chromium-browser \
        libreoffice \
        gimp \
        vlc \
        file-manager \
        gedit \
        calculator \
        system-monitor
    
    # Install development tools
    sudo apt-get install -y \
        git \
        curl \
        wget \
        vim \
        nano \
        htop \
        tree \
        unzip \
        build-essential
    
    # Install Node.js and npm
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # Install Python and pip
    sudo apt-get install -y python3 python3-pip
    
    log_success "Additional tools installed"
}

# Configure firewall
configure_firewall() {
    log_info "Configuring firewall..."
    
    # Install UFW if not already installed
    sudo apt-get install -y ufw
    
    # Configure UFW rules
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow VNC ports
    sudo ufw allow 5900:5910/tcp
    
    # Allow Guacamole
    sudo ufw allow 8080/tcp
    
    # Allow NoVNC
    sudo ufw allow 6080/tcp
    
    # Enable UFW
    sudo ufw --force enable
    
    log_success "Firewall configured"
}

# Create systemd services
create_services() {
    log_info "Creating systemd services..."
    
    # Create VNC service
    sudo tee /etc/systemd/system/vncserver@.service > /dev/null << 'EOF'
[Unit]
Description=Start TightVNC server at startup
After=syslog.target network.target

[Service]
Type=forking
User=%i
Group=%i
WorkingDirectory=/home/<USER>

PIDFile=/home/<USER>/.vnc/%H:%i.pid
ExecStartPre=-/usr/bin/vncserver -kill :%i > /dev/null 2>&1
ExecStart=/usr/bin/vncserver -depth 24 -geometry 1920x1080 :%i
ExecStop=/usr/bin/vncserver -kill :%i

[Install]
WantedBy=multi-user.target
EOF
    
    # Create NoVNC service
    sudo tee /etc/systemd/system/novnc.service > /dev/null << 'EOF'
[Unit]
Description=NoVNC Web Server
After=network.target

[Service]
Type=simple
User=nobody
ExecStart=/usr/share/novnc/utils/launch.sh --vnc localhost:5901 --listen 6080
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd
    sudo systemctl daemon-reload
    
    log_success "Systemd services created"
}

# Setup user environment
setup_user_environment() {
    log_info "Setting up user environment..."
    
    # Create VNC password for current user
    mkdir -p ~/.vnc
    echo "vncpassword" | vncpasswd -f > ~/.vnc/passwd
    chmod 600 ~/.vnc/passwd
    
    # Create VNC startup script
    cat > ~/.vnc/xstartup << 'EOF'
#!/bin/bash
xrdb $HOME/.Xresources
startxfce4 &
EOF
    
    chmod +x ~/.vnc/xstartup
    
    # Create desktop directories
    mkdir -p ~/Desktop ~/Documents ~/Downloads ~/Pictures ~/Videos
    
    log_success "User environment configured"
}

# Main installation function
main() {
    log_info "Starting Desktop VM setup..."
    
    check_root
    check_requirements
    
    # Install components
    install_vnc
    install_desktop_environments
    install_guacamole
    install_tools
    
    # Configure system
    configure_firewall
    create_services
    setup_user_environment
    
    log_success "Desktop VM setup completed successfully!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Start VNC server: systemctl --<NAME_EMAIL>"
    log_info "2. Start NoVNC: sudo systemctl enable novnc.service"
    log_info "3. Access desktop via VNC: vnc://localhost:5901"
    log_info "4. Access desktop via web: http://localhost:6080"
    log_info "5. Access Guacamole: http://localhost:8080/guacamole"
    log_info ""
    log_info "Default VNC password: vncpassword"
    log_info "Default Guacamole credentials: guacadmin / guacadmin"
}

# Run main function
main "$@"
