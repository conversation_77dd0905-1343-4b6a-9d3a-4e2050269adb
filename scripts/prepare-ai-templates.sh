#!/bin/bash
#
# Prepare AI-ready rootfs images for MicroVMs
#
# This script creates rootfs images for different AI development environments.
# It requires qemu-img, debootstrap, and other standard Linux utilities.
#

set -e

# Configuration
TEMPLATES_DIR=${TEMPLATES_DIR:-/var/lib/ai-templates}
KERNEL_PATH=${KERNEL_PATH:-/boot/vmlinuz-$(uname -r)}
IMAGE_SIZE=${IMAGE_SIZE:-4G}
MOUNT_DIR=${MOUNT_DIR:-/mnt/rootfs-tmp}

# Check for required tools
command -v qemu-img >/dev/null 2>&1 || { echo "Error: qemu-img is required but not installed."; exit 1; }
command -v debootstrap >/dev/null 2>&1 || { echo "Error: debootstrap is required but not installed."; exit 1; }
command -v chroot >/dev/null 2>&1 || { echo "Error: chroot is required but not installed."; exit 1; }
command -v mkfs.ext4 >/dev/null 2>&1 || { echo "Error: mkfs.ext4 is required but not installed."; exit 1; }

# Create templates directory if it doesn't exist
mkdir -p ${TEMPLATES_DIR}

# Copy kernel to templates directory
echo "Copying kernel to ${TEMPLATES_DIR}/vmlinux..."
cp ${KERNEL_PATH} ${TEMPLATES_DIR}/vmlinux

# Function to create a base rootfs image
create_base_rootfs() {
  local image_path=$1
  
  echo "Creating base rootfs image at ${image_path}..."
  
  # Create an empty image
  qemu-img create -f raw ${image_path} ${IMAGE_SIZE}
  
  # Format the image
  mkfs.ext4 ${image_path}
  
  # Create a mount point
  mkdir -p ${MOUNT_DIR}
  
  # Mount the image
  mount -o loop ${image_path} ${MOUNT_DIR}
  
  # Install a minimal Debian system
  debootstrap --variant=minbase bullseye ${MOUNT_DIR} http://deb.debian.org/debian/
  
  # Configure the system
  cat > ${MOUNT_DIR}/etc/fstab << EOF
# /etc/fstab
/dev/vda / ext4 defaults 0 1
EOF
  
  # Set hostname
  echo "ai-microvm" > ${MOUNT_DIR}/etc/hostname
  
  # Configure network
  cat > ${MOUNT_DIR}/etc/network/interfaces << EOF
# /etc/network/interfaces
auto lo
iface lo inet loopback

auto eth0
iface eth0 inet dhcp
EOF
  
  # Set up DNS
  cat > ${MOUNT_DIR}/etc/resolv.conf << EOF
nameserver *******
nameserver *******
EOF
  
  # Configure apt sources
  cat > ${MOUNT_DIR}/etc/apt/sources.list << EOF
deb http://deb.debian.org/debian bullseye main
deb http://deb.debian.org/debian bullseye-updates main
deb http://security.debian.org/debian-security bullseye-security main
EOF
  
  # Install common packages
  chroot ${MOUNT_DIR} apt-get update
  chroot ${MOUNT_DIR} apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    procps \
    net-tools \
    iputils-ping \
    iproute2 \
    openssh-server \
    sudo \
    locales
  
  # Configure locale
  chroot ${MOUNT_DIR} locale-gen en_US.UTF-8
  chroot ${MOUNT_DIR} update-locale LANG=en_US.UTF-8
  
  # Create a user
  chroot ${MOUNT_DIR} useradd -m -s /bin/bash -G sudo aiuser
  chroot ${MOUNT_DIR} bash -c 'echo "aiuser:aiuser" | chpasswd'
  
  # Configure sudo
  echo "aiuser ALL=(ALL) NOPASSWD:ALL" > ${MOUNT_DIR}/etc/sudoers.d/aiuser
  chmod 440 ${MOUNT_DIR}/etc/sudoers.d/aiuser
  
  # Clean up
  chroot ${MOUNT_DIR} apt-get clean
  
  # Unmount
  umount ${MOUNT_DIR}
}

# Function to customize the rootfs for Node.js
customize_nodejs() {
  local base_image=$1
  local output_image=$2
  
  echo "Creating Node.js rootfs image at ${output_image}..."
  
  # Copy the base image
  cp ${base_image} ${output_image}
  
  # Mount the image
  mount -o loop ${output_image} ${MOUNT_DIR}
  
  # Add Node.js repository
  chroot ${MOUNT_DIR} curl -fsSL https://deb.nodesource.com/setup_18.x | chroot ${MOUNT_DIR} bash -
  
  # Install Node.js and development tools
  chroot ${MOUNT_DIR} apt-get update
  chroot ${MOUNT_DIR} apt-get install -y --no-install-recommends \
    nodejs \
    build-essential \
    python3 \
    python3-pip \
    zip \
    unzip
  
  # Install global npm packages
  chroot ${MOUNT_DIR} npm install -g yarn pnpm typescript ts-node nodemon
  
  # Clean up
  chroot ${MOUNT_DIR} apt-get clean
  
  # Unmount
  umount ${MOUNT_DIR}
}

# Function to customize the rootfs for Python
customize_python() {
  local base_image=$1
  local output_image=$2
  
  echo "Creating Python rootfs image at ${output_image}..."
  
  # Copy the base image
  cp ${base_image} ${output_image}
  
  # Mount the image
  mount -o loop ${output_image} ${MOUNT_DIR}
  
  # Install Python and development tools
  chroot ${MOUNT_DIR} apt-get update
  chroot ${MOUNT_DIR} apt-get install -y --no-install-recommends \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    build-essential \
    libssl-dev \
    libffi-dev
  
  # Install common Python packages
  chroot ${MOUNT_DIR} pip3 install --no-cache-dir \
    numpy \
    pandas \
    matplotlib \
    scikit-learn \
    jupyter \
    ipython
  
  # Clean up
  chroot ${MOUNT_DIR} apt-get clean
  
  # Unmount
  umount ${MOUNT_DIR}
}

# Function to customize the rootfs for full-stack development
customize_fullstack() {
  local base_image=$1
  local output_image=$2
  
  echo "Creating full-stack rootfs image at ${output_image}..."
  
  # Copy the base image
  cp ${base_image} ${output_image}
  
  # Mount the image
  mount -o loop ${output_image} ${MOUNT_DIR}
  
  # Add Node.js repository
  chroot ${MOUNT_DIR} curl -fsSL https://deb.nodesource.com/setup_18.x | chroot ${MOUNT_DIR} bash -
  
  # Install development tools
  chroot ${MOUNT_DIR} apt-get update
  chroot ${MOUNT_DIR} apt-get install -y --no-install-recommends \
    nodejs \
    build-essential \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    postgresql \
    postgresql-client \
    mysql-server \
    mysql-client \
    redis-server \
    mongodb \
    nginx \
    zip \
    unzip
  
  # Install global npm packages
  chroot ${MOUNT_DIR} npm install -g yarn pnpm typescript ts-node nodemon @angular/cli @vue/cli create-react-app
  
  # Install common Python packages
  chroot ${MOUNT_DIR} pip3 install --no-cache-dir \
    flask \
    django \
    fastapi \
    uvicorn \
    sqlalchemy \
    alembic
  
  # Clean up
  chroot ${MOUNT_DIR} apt-get clean
  
  # Unmount
  umount ${MOUNT_DIR}
}

# Create base rootfs
BASE_ROOTFS=${TEMPLATES_DIR}/base-rootfs.ext4
create_base_rootfs ${BASE_ROOTFS}

# Create minimal rootfs (just a copy of the base)
cp ${BASE_ROOTFS} ${TEMPLATES_DIR}/minimal-rootfs.ext4

# Create Node.js rootfs
customize_nodejs ${BASE_ROOTFS} ${TEMPLATES_DIR}/nodejs-rootfs.ext4

# Create Python rootfs
customize_python ${BASE_ROOTFS} ${TEMPLATES_DIR}/python-rootfs.ext4

# Create full-stack rootfs
customize_fullstack ${BASE_ROOTFS} ${TEMPLATES_DIR}/fullstack-rootfs.ext4

echo "All rootfs images have been created successfully!"
echo "Kernel: ${TEMPLATES_DIR}/vmlinux"
echo "Base rootfs: ${BASE_ROOTFS}"
echo "Minimal rootfs: ${TEMPLATES_DIR}/minimal-rootfs.ext4"
echo "Node.js rootfs: ${TEMPLATES_DIR}/nodejs-rootfs.ext4"
echo "Python rootfs: ${TEMPLATES_DIR}/python-rootfs.ext4"
echo "Full-stack rootfs: ${TEMPLATES_DIR}/fullstack-rootfs.ext4" 