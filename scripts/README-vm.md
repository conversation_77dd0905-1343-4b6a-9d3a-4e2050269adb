# MicroVM Image Loading and Starting

This directory contains scripts to detect, load and start MicroVMs using Firecracker.

## Available Scripts

1. **list-vm-images.js** - Scans and lists available VM images in the configured directories
2. **start-vm.js** - Creates and starts a VM using the API
3. **start-direct-vm.js** - Directly creates and starts a VM using Firecracker
4. **check-firecracker.js** - Checks if Firecracker is properly installed and configured
5. **start-vm-debug.js** - Debug script with verbose logging

## Requirements

- Firecracker binary installed at `/usr/bin/firecracker`
- KVM access enabled for the current user
- Kernel image in `resources/kernels/vmlinux.bin`
- Rootfs image in `resources/lxc-images/` (e.g., `bionic.rootfs.ext4`)
- `logs` directory with write permissions

## Usage

### List Available Images

```bash
node scripts/list-vm-images.js
```

### Check Firecracker Installation

```bash
node scripts/check-firecracker.js
```

### Start a VM Directly

This is the most reliable method:

```bash
node scripts/start-direct-vm.js
```

### Start a VM Using API

First, ensure the development server is running on port 3083:

```bash
npm run dev -- -p 3083
```

Then in another terminal:

```bash
node scripts/start-vm.js
```

## Troubleshooting

- **Log Access Issues**: Create the logs directory with proper permissions:
  ```bash
  mkdir -p logs && touch logs/firecracker.log && chmod 777 logs/firecracker.log
  ```

- **API Connection Errors**: Check if the server is running on the expected port
  
- **Firecracker Binary Not Found**: Install Firecracker:
  ```bash
  mkdir -p release
  curl -fsSL -o firecracker.tgz "https://github.com/firecracker-microvm/firecracker/releases/download/v1.4.0/firecracker-v1.4.0-x86_64.tgz"
  tar -xf firecracker.tgz -C release
  sudo cp release/release-v1.4.0-x86_64/firecracker-v1.4.0-x86_64 /usr/local/bin/firecracker
  sudo cp release/release-v1.4.0-x86_64/jailer-v1.4.0-x86_64 /usr/local/bin/jailer
  sudo chmod +x /usr/local/bin/firecracker /usr/local/bin/jailer
  ```

- **KVM Access Issues**:
  ```bash
  sudo modprobe kvm
  sudo chmod 666 /dev/kvm
  # For permanent access:
  sudo usermod -aG kvm $USER
  ``` 