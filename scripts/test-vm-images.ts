#!/usr/bin/env ts-node

/**
 * Test script for VM image loader
 * 
 * This script tests the MicroVM image loader functionality
 * by finding and listing all available VM images.
 */

import * as path from 'path';
import * as fs from 'fs';
import { getAllAvailableImages, loadImagesForMicroVM, getLxcImages } from '../lib/containerization/microvm/image-loader';

// Configure process.cwd() to be the project root for proper path resolution
// when running from scripts directory
process.env.APP_ROOT = path.resolve(__dirname, '..');
process.chdir(process.env.APP_ROOT);

// Create a basic logger for testing (to avoid dependency on the app logger)
const logger = {
  info: (...args: any[]) => console.log('[INFO]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
  debug: (...args: any[]) => console.log('[DEBUG]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args)
};

// Mock the logger import
jest.mock('@/lib/logger', () => ({
  logger
}));

async function main() {
  console.log('\n==== Testing VM Image Loader ====\n');
  
  try {
    // Get all available images
    console.log('Finding all available VM images...');
    const { kernelImages, rootfsImages } = await getAllAvailableImages();
    
    // Log results
    console.log(`\nFound ${kernelImages.length} kernel images:`);
    kernelImages.forEach((image, index) => {
      console.log(`  ${index + 1}. ${image.name} (${(image.size / 1024 / 1024).toFixed(2)} MB)`);
      console.log(`     Path: ${image.path}`);
      console.log(`     Last modified: ${image.lastModified.toLocaleString()}`);
    });
    
    console.log(`\nFound ${rootfsImages.length} rootfs images:`);
    rootfsImages.forEach((image, index) => {
      console.log(`  ${index + 1}. ${image.name} (${(image.size / 1024 / 1024).toFixed(2)} MB)`);
      console.log(`     Path: ${image.path}`);
      console.log(`     Last modified: ${image.lastModified.toLocaleString()}`);
    });
    
    // Test loadImagesForMicroVM
    if (kernelImages.length > 0 && rootfsImages.length > 0) {
      console.log('\nTesting loadImagesForMicroVM function...');
      const { kernelPath, rootfsPath } = await loadImagesForMicroVM();
      console.log(`Selected kernel: ${kernelPath}`);
      console.log(`Selected rootfs: ${rootfsPath}`);
      
      // Check if both files exist
      const kernelExists = fs.existsSync(kernelPath);
      const rootfsExists = fs.existsSync(rootfsPath);
      
      console.log(`Kernel file exists: ${kernelExists}`);
      console.log(`Rootfs file exists: ${rootfsExists}`);
    }
    
    // Get LXC images
    console.log('\nFinding LXC images...');
    const lxcImages = await getLxcImages();
    
    console.log(`\nFound ${lxcImages.length} LXC images:`);
    lxcImages.forEach((image, index) => {
      console.log(`  ${index + 1}. ${image.name}`);
      console.log(`     Path: ${image.path}`);
      console.log(`     Description: ${image.description}`);
    });
    
    console.log('\n==== VM Image Loader Test Complete ====');
  } catch (error) {
    console.error('Error:', error);
  }
}

main(); 