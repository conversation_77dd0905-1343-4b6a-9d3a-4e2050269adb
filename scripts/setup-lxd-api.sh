#!/bin/bash

# Setup LXD API Script
# This script sets up the LXD REST API for remote access

set -e

# Text formatting
BOLD="\033[1m"
RED="\033[31m"
GREEN="\033[32m"
YELLOW="\033[33m"
BLUE="\033[34m"
RESET="\033[0m"

# Default values
ADDRESS="[::]"
PORT=8443
TRUST_PASSWORD=""
VERBOSE=false
FORCE=false

# Print usage information
usage() {
  cat <<EOF
Usage: $0 [OPTIONS]

Options:
  -h, --help                 Show this help message and exit
  -a, --address ADDRESS      API address (default: $ADDRESS)
  -p, --port PORT            API port (default: $PORT)
  -t, --trust-password PWD   Trust password for client authentication
  -f, --force                Force operations without prompting
  -v, --verbose              Enable verbose output

Examples:
  $0 --address 0.0.0.0 --port 8443 --trust-password mysecretpassword
  $0 --address [::]
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -h|--help)
      usage
      exit 0
      ;;
    -a|--address)
      ADDRESS="$2"
      shift 2
      ;;
    -p|--port)
      PORT="$2"
      shift 2
      ;;
    -t|--trust-password)
      TRUST_PASSWORD="$2"
      shift 2
      ;;
    -f|--force)
      FORCE=true
      shift
      ;;
    -v|--verbose)
      VERBOSE=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
  esac
done

# Logging functions
log() {
  local level="$1"
  local message="$2"
  
  case "$level" in
    "INFO")
      echo -e "${BLUE}[INFO]${RESET} $message"
      ;;
    "SUCCESS")
      echo -e "${GREEN}[SUCCESS]${RESET} $message"
      ;;
    "WARNING")
      echo -e "${YELLOW}[WARNING]${RESET} $message"
      ;;
    "ERROR")
      echo -e "${RED}[ERROR]${RESET} $message"
      ;;
  esac
}

# Verbose logging
log_verbose() {
  if [ "$VERBOSE" = true ]; then
    log "INFO" "$1"
  fi
}

# Check if LXD is installed
check_lxd() {
  if ! command -v lxd >/dev/null 2>&1; then
    log "ERROR" "LXD is not installed"
    log "INFO" "Please install LXD first"
    exit 1
  fi
}

# Check if LXD is initialized
check_lxd_initialized() {
  if ! lxc list >/dev/null 2>&1; then
    log "ERROR" "LXD is not initialized"
    log "INFO" "Please run 'lxd init' first"
    exit 1
  fi
}

# Enable LXD API
enable_lxd_api() {
  log "INFO" "Enabling LXD API at ${ADDRESS}:${PORT}"
  
  # Check if API is already enabled
  local current_address=$(lxc config get core.https_address 2>/dev/null || echo "")
  
  if [ -n "$current_address" ]; then
    if [ "$FORCE" = true ]; then
      log "WARNING" "LXD API is already enabled at $current_address, reconfiguring"
    else
      log "WARNING" "LXD API is already enabled at $current_address"
      log "INFO" "Use --force to reconfigure"
      return
    fi
  fi
  
  # Set API address
  lxc config set core.https_address "${ADDRESS}:${PORT}"
  log "SUCCESS" "LXD API enabled at ${ADDRESS}:${PORT}"
  
  # Set trust password if provided
  if [ -n "$TRUST_PASSWORD" ]; then
    log "INFO" "Setting trust password"
    lxc config set core.trust_password "$TRUST_PASSWORD"
    log "SUCCESS" "Trust password set"
  else
    log "WARNING" "No trust password provided, clients will need to be added manually"
  fi
}

# Print summary
print_summary() {
  echo
  echo -e "${BOLD}LXD API Setup Summary:${RESET}"
  echo -e "  API Address: ${BLUE}${ADDRESS}:${PORT}${RESET}"
  echo -e "  Trust Password: ${BLUE}$([ -n "$TRUST_PASSWORD" ] && echo "Set" || echo "Not set")${RESET}"
  echo
  echo -e "${BOLD}Next Steps:${RESET}"
  echo -e "  1. Make sure your firewall allows connections to port ${PORT}"
  echo -e "  2. Connect to the API using a client with the trust password"
  echo -e "  3. For security, remove the trust password after adding all clients:"
  echo -e "     ${BLUE}lxc config unset core.trust_password${RESET}"
  echo
}

# Main function
main() {
  echo -e "${BOLD}LXD API Setup${RESET}"
  echo
  
  # Check requirements
  check_lxd
  check_lxd_initialized
  
  # Print configuration
  echo -e "${BOLD}Configuration:${RESET}"
  echo -e "  API Address: ${BLUE}${ADDRESS}:${PORT}${RESET}"
  echo -e "  Trust Password: ${BLUE}$([ -n "$TRUST_PASSWORD" ] && echo "Set" || echo "Not set")${RESET}"
  echo
  
  # Confirm setup
  if [ "$FORCE" = false ]; then
    read -p "Continue with setup? [y/N] " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
      log "INFO" "Setup cancelled"
      exit 0
    fi
  fi
  
  # Perform setup
  enable_lxd_api
  
  # Print summary
  print_summary
}

# Run main function
main
