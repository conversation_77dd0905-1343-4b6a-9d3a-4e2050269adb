#!/usr/bin/env node

/**
 * Start VM Script
 * 
 * This script creates and starts a MicroVM using the detected images.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// VM configuration
const config = {
  name: `test-vm-${Date.now().toString().slice(-6)}`,
  kernelPath: path.join(process.cwd(), 'resources', 'kernels', 'vmlinux.bin'),
  rootfsPath: path.join(process.cwd(), 'resources', 'lxc-images', 'bionic.rootfs.ext4'), // Using Ubuntu 18.04 as it's smaller
  memSizeMib: 2048,
  vcpuCount: 2,
  networkEnabled: true
};

async function main() {
  // Check if the kernel and rootfs images exist
  if (!fs.existsSync(config.kernelPath)) {
    console.error(`Kernel image not found: ${config.kernelPath}`);
    process.exit(1);
  }
  
  if (!fs.existsSync(config.rootfsPath)) {
    console.error(`Rootfs image not found: ${config.rootfsPath}`);
    process.exit(1);
  }
  
  console.log(`\n==== Starting VM: ${config.name} ====\n`);
  console.log(`Using kernel: ${config.kernelPath}`);
  console.log(`Using rootfs: ${config.rootfsPath}`);
  console.log(`Memory: ${config.memSizeMib} MiB`);
  console.log(`CPUs: ${config.vcpuCount}`);
  console.log('\nSending request to the API...\n');

  try {
    // Use curl to send a request to our API
    const requestBody = {
      name: config.name,
      kernelImagePath: config.kernelPath,
      rootfsImagePath: config.rootfsPath,
      memSizeMib: config.memSizeMib,
      vcpuCount: config.vcpuCount,
      networkEnabled: config.networkEnabled
    };
    
    const curlCommand = `curl -X POST http://localhost:3082/api/containerization/microvm/create-from-image \
      -H "Content-Type: application/json" \
      -d '${JSON.stringify(requestBody)}'`;
    
    console.log(`Executing: ${curlCommand}\n`);
    const result = execSync(curlCommand, { encoding: 'utf-8' });
    
    console.log('API Response:');
    console.log(JSON.parse(result));
    console.log('\n==== VM Started Successfully ====\n');
  } catch (error) {
    console.error('Error starting VM:', error.message);
    
    // Provide alternative approach if API fails
    console.log('\n==== Alternative Approach ====');
    console.log('If the API is not running, you can start your VM using this command:');
    console.log(`
# Make sure the API server is running first on port 3082
npm run dev -- -p 3082

# Then in another terminal:
curl -X POST http://localhost:3082/api/containerization/microvm/create-from-image \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify({
    name: config.name,
    kernelImagePath: config.kernelPath,
    rootfsImagePath: config.rootfsPath,
    memSizeMib: config.memSizeMib,
    vcpuCount: config.vcpuCount,
    networkEnabled: config.networkEnabled
  })}'
`);
  }
}

main(); 