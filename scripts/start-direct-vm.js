#!/usr/bin/env node

/**
 * Start Direct VM Script
 * 
 * This script directly starts a VM using Firecracker.
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const os = require('os');

// VM configuration
const config = {
  name: `direct-vm-${Date.now().toString().slice(-6)}`,
  kernelPath: path.join(process.cwd(), 'resources', 'kernels', 'vmlinux.bin'),
  rootfsPath: path.join(process.cwd(), 'resources', 'lxc-images', 'bionic.rootfs.ext4'),
  memSizeMib: 2048,
  vcpuCount: 2,
  socketPath: path.join(os.tmpdir(), `firecracker-${Date.now()}.socket`),
};

console.log('==== Starting Direct VM ====\n');
console.log(`VM Name: ${config.name}`);
console.log(`Kernel: ${config.kernelPath}`);
console.log(`Rootfs: ${config.rootfsPath}`);
console.log(`Memory: ${config.memSizeMib} MiB`);
console.log(`CPUs: ${config.vcpuCount}`);
console.log(`Socket: ${config.socketPath}\n`);

// Check for Firecracker binary
const firecrackerPath = '/usr/bin/firecracker';
if (!fs.existsSync(firecrackerPath)) {
  console.error('Firecracker binary not found at /usr/bin/firecracker');
  process.exit(1);
}

// Check that kernel and rootfs exist
if (!fs.existsSync(config.kernelPath)) {
  console.error(`Kernel image not found: ${config.kernelPath}`);
  process.exit(1);
}

if (!fs.existsSync(config.rootfsPath)) {
  console.error(`Rootfs image not found: ${config.rootfsPath}`);
  process.exit(1);
}

// Remove socket if it exists
if (fs.existsSync(config.socketPath)) {
  fs.unlinkSync(config.socketPath);
}

// Start Firecracker
console.log('Starting Firecracker...');
const fcProcess = spawn(firecrackerPath, [
  '--api-sock', config.socketPath,
  // No log file parameters - use console output instead
]);

// Log process output
fcProcess.stdout.on('data', (data) => {
  console.log(`[FC] ${data.toString().trim()}`);
});

fcProcess.stderr.on('data', (data) => {
  console.error(`[FC Error] ${data.toString().trim()}`);
});

// Handle process exit
fcProcess.on('close', (code) => {
  console.log(`Firecracker process exited with code ${code}`);
});

// Wait a bit for the socket to be created
setTimeout(() => {
  // Create VM config using curl requests to the API socket
  console.log('\nSetting up VM configuration...');

  const bootSource = {
    kernel_image_path: config.kernelPath,
    boot_args: 'console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp'
  };

  const drives = [{
    drive_id: 'rootfs',
    path_on_host: config.rootfsPath,
    is_root_device: true,
    is_read_only: false
  }];

  const machineConfig = {
    vcpu_count: config.vcpuCount,
    mem_size_mib: config.memSizeMib
  };

  // Function to send a PUT request to the socket
  function putRequest(path, body) {
    const curlCmd = `curl --unix-socket ${config.socketPath} -X PUT http://localhost${path} \
      -H "Content-Type: application/json" \
      -d '${JSON.stringify(body)}'`;
    
    console.log(`Executing: ${curlCmd}`);
    
    try {
      require('child_process').execSync(curlCmd, { 
        encoding: 'utf-8',
        stdio: ['ignore', 'pipe', 'pipe']
      });
      return true;
    } catch (error) {
      console.error(`Error: ${error.message}`);
      return false;
    }
  }

  // Set boot source
  console.log('Setting boot source...');
  putRequest('/boot-source', bootSource);

  // Set rootfs drive
  console.log('Setting rootfs drive...');
  putRequest('/drives/rootfs', drives[0]);

  // Set machine config
  console.log('Setting machine config...');
  putRequest('/machine-config', machineConfig);

  // Start the VM
  console.log('Starting the VM...');
  putRequest('/actions', { action_type: 'InstanceStart' });

  console.log('\nVM is starting...');
  console.log('\nPress Ctrl+C to exit this script (VM will continue running)');
}, 1000); 