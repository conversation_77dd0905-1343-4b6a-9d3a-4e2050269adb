#!/usr/bin/env node

/**
 * List VM Images Script
 * 
 * A standalone script to scan and list available VM images
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// Define the search paths
const searchPaths = [
  // Project resources directory  
  path.join(process.cwd(), 'resources', 'lxc-images'),
  // Kernels directory
  path.join(process.cwd(), 'resources', 'kernels'),
  // Standard system directories
  '/opt/firecracker',
  '/var/lib/firecracker',
  // User's home directory
  path.join(os.homedir(), '.firecracker'),
];

// Main function
async function main() {
  console.log('\n==== VM Image Finder ====\n');
  
  const kernelImages = [];
  const rootfsImages = [];
  const lxcImages = [];
  
  console.log(`Searching for VM images in paths:\n${searchPaths.map(p => `- ${p}`).join('\n')}\n`);
  
  // Search all directories
  for (const dirPath of searchPaths) {
    try {
      if (!fs.existsSync(dirPath)) {
        console.log(`Directory does not exist: ${dirPath}`);
        continue;
      }
      
      console.log(`Scanning directory: ${dirPath}`);
      const files = fs.readdirSync(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (!stats.isFile()) continue;
        
        // Skip index files
        if (file === 'index.ts' || file === 'index.js') continue;
        
        // Kernel image detection
        if (file.includes('vmlinux') || file.includes('kernel') || file.endsWith('.bin')) {
          kernelImages.push({
            name: file,
            description: `Kernel image found in ${dirPath}`,
            type: 'kernel',
            path: filePath,
            size: stats.size,
            lastModified: stats.mtime
          });
        }
        
        // Rootfs image detection
        if (file.endsWith('.ext4') || file.endsWith('.img') || file.includes('rootfs')) {
          rootfsImages.push({
            name: file,
            description: `Root filesystem image found in ${dirPath}`,
            type: 'rootfs',
            path: filePath,
            size: stats.size,
            lastModified: stats.mtime
          });
        }
        
        // LXC image detection (in the lxc-images directory)
        if (dirPath.includes('lxc-images') && !file.includes('index')) {
          lxcImages.push({
            name: file,
            path: filePath,
            description: `LXC image (${(stats.size / 1024 / 1024).toFixed(2)} MB)`
          });
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dirPath}:`, error);
    }
  }
  
  // Log results
  console.log(`\nFound ${kernelImages.length} kernel images:`);
  kernelImages.forEach((image, index) => {
    console.log(`  ${index + 1}. ${image.name} (${(image.size / 1024 / 1024).toFixed(2)} MB)`);
    console.log(`     Path: ${image.path}`);
    console.log(`     Last modified: ${image.lastModified.toLocaleString()}`);
  });
  
  console.log(`\nFound ${rootfsImages.length} rootfs images:`);
  rootfsImages.forEach((image, index) => {
    console.log(`  ${index + 1}. ${image.name} (${(image.size / 1024 / 1024).toFixed(2)} MB)`);
    console.log(`     Path: ${image.path}`);
    console.log(`     Last modified: ${image.lastModified.toLocaleString()}`);
  });
  
  console.log(`\nFound ${lxcImages.length} LXC images:`);
  lxcImages.forEach((image, index) => {
    console.log(`  ${index + 1}. ${image.name}`);
    console.log(`     Path: ${image.path}`);
    console.log(`     Description: ${image.description}`);
  });
  
  console.log('\n==== VM Image Finder Complete ====');
}

// Run the main function
main(); 