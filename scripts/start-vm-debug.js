#!/usr/bin/env node

/**
 * Start VM Debug Script
 * 
 * This script creates and starts a MicroVM with detailed debug output.
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const os = require('os');

// VM configuration
const config = {
  name: `test-vm-${Date.now().toString().slice(-6)}`,
  kernelPath: path.join(process.cwd(), 'resources', 'kernels', 'vmlinux.bin'),
  rootfsPath: path.join(process.cwd(), 'resources', 'lxc-images', 'bionic.rootfs.ext4'),
  memSizeMib: 2048,
  vcpuCount: 2,
  networkEnabled: true,
  metadataFilePath: path.join(process.cwd(), 'firecracker-metadata.json'),
  configFilePath: path.join(process.cwd(), 'firecracker-config.json')
};

// Colors for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

async function main() {
  // Check if the kernel and rootfs images exist
  if (!fs.existsSync(config.kernelPath)) {
    console.error(`Kernel image not found: ${config.kernelPath}`);
    process.exit(1);
  }
  
  if (!fs.existsSync(config.rootfsPath)) {
    console.error(`Rootfs image not found: ${config.rootfsPath}`);
    process.exit(1);
  }
  
  console.log(`${colors.cyan}==== Starting VM: ${config.name} ====${colors.reset}\n`);
  console.log(`${colors.blue}Using kernel:${colors.reset} ${config.kernelPath}`);
  console.log(`${colors.blue}Using rootfs:${colors.reset} ${config.rootfsPath}`);
  console.log(`${colors.blue}Memory:${colors.reset} ${config.memSizeMib} MiB`);
  console.log(`${colors.blue}CPUs:${colors.reset} ${config.vcpuCount}`);
  
  try {
    // Method 1: Try the API first
    console.log(`\n${colors.magenta}1. Attempting to start VM via API...${colors.reset}`);
    try {
      await startVmViaApi();
      console.log(`${colors.green}✓ VM started successfully via API${colors.reset}`);
    } catch (apiError) {
      console.log(`${colors.yellow}✗ Failed to start VM via API: ${apiError.message}${colors.reset}`);
      console.log(`${colors.yellow}Falling back to direct Firecracker execution...${colors.reset}`);
      
      // Method 2: Direct execution
      console.log(`\n${colors.magenta}2. Attempting to start VM directly with Firecracker...${colors.reset}`);
      await startVmDirectly();
    }
  } catch (error) {
    console.error(`${colors.red}Failed to start VM: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Start VM using the API
async function startVmViaApi() {
  console.log('Preparing API request...');
  const requestBody = {
    name: config.name,
    kernelImagePath: config.kernelPath,
    rootfsImagePath: config.rootfsPath,
    memSizeMib: config.memSizeMib,
    vcpuCount: config.vcpuCount,
    networkEnabled: config.networkEnabled
  };
  
  console.log('Sending request to API...');
  const curlCommand = `curl -X POST http://localhost:3083/api/containerization/microvm/create-from-image \
    -H "Content-Type: application/json" \
    -d '${JSON.stringify(requestBody)}'`;
  
  console.log(`Executing: ${curlCommand}\n`);
  const result = execSync(curlCommand, { encoding: 'utf-8' });
  
  console.log('API Response:');
  const response = JSON.parse(result);
  
  if (response.error) {
    throw new Error(response.message || 'API returned an error');
  }
  
  return response;
}

// Start VM directly using Firecracker
async function startVmDirectly() {
  // Find the firecracker executable
  const firecrackerPath = findFirecrackerPath();
  console.log(`Found Firecracker at: ${firecrackerPath}`);
  
  // Create a socket file
  const socketPath = path.join(os.tmpdir(), `firecracker-${config.name}.socket`);
  if (fs.existsSync(socketPath)) {
    fs.unlinkSync(socketPath);
  }
  
  // Create a VM config JSON file
  const vmConfig = {
    boot_source: {
      kernel_image_path: config.kernelPath,
      boot_args: "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp"
    },
    drives: [
      {
        drive_id: "rootfs",
        path_on_host: config.rootfsPath,
        is_root_device: true,
        is_read_only: false
      }
    ],
    machine_config: {
      vcpu_count: config.vcpuCount,
      mem_size_mib: config.memSizeMib,
      ht_enabled: false
    }
  };
  
  console.log(`Writing VM config to ${config.configFilePath}...`);
  fs.writeFileSync(config.configFilePath, JSON.stringify(vmConfig, null, 2));
  
  // Start Firecracker process
  console.log(`Starting Firecracker with socket ${socketPath}...`);
  // Note: No log file path provided - output will go to stdout/stderr
  const fcProcess = spawn(firecrackerPath, ['--api-sock', socketPath, '--config-file', config.configFilePath]);
  
  // Handle process output
  fcProcess.stdout.on('data', (data) => {
    console.log(`${colors.green}[Firecracker stdout] ${data.toString().trim()}${colors.reset}`);
  });
  
  fcProcess.stderr.on('data', (data) => {
    console.log(`${colors.red}[Firecracker stderr] ${data.toString().trim()}${colors.reset}`);
  });
  
  fcProcess.on('close', (code) => {
    if (code !== 0) {
      console.log(`${colors.red}Firecracker process exited with code ${code}${colors.reset}`);
    } else {
      console.log(`${colors.green}Firecracker process exited successfully${colors.reset}`);
    }
  });
  
  console.log(`${colors.green}✓ Firecracker started with PID ${fcProcess.pid}${colors.reset}`);
  
  // Keep the script running
  console.log('\nPress Ctrl+C to stop the script (Firecracker will continue running)');
  
  return new Promise((resolve) => {
    // This will keep the script running
    process.stdin.resume();
    
    // Handle Ctrl+C to gracefully exit
    process.on('SIGINT', () => {
      console.log('\nExiting script. Firecracker VM continues to run in the background.');
      resolve({ pid: fcProcess.pid, socketPath });
    });
  });
}

// Function to find Firecracker binary
function findFirecrackerPath() {
  const paths = [
    '/usr/bin/firecracker',
    '/usr/local/bin/firecracker',
    '/opt/firecracker/firecracker'
  ];
  
  for (const path of paths) {
    if (fs.existsSync(path)) {
      return path;
    }
  }
  
  throw new Error('Firecracker binary not found. Please install Firecracker first.');
}

main();