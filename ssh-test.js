/**
 * SSH Test Script
 * 
 * This script tests SSH connectivity to the LXD container.
 */

const { Client } = require('ssh2');
const fs = require('fs');

// Configuration
const config = {
  host: '*************',
  port: 22,
  username: 'root',
  password: 'password',
  readyTimeout: 5000
};

// Create SSH client
const conn = new Client();

// Connect to the container
conn.on('ready', () => {
  console.log('SSH connection established');
  
  // Execute a command
  conn.exec('uname -a', (err, stream) => {
    if (err) {
      console.error('Failed to execute command:', err);
      conn.end();
      return;
    }
    
    // Handle command output
    let output = '';
    
    stream.on('data', (data) => {
      output += data.toString();
    });
    
    stream.stderr.on('data', (data) => {
      console.error('STDERR:', data.toString());
    });
    
    stream.on('close', (code, signal) => {
      console.log('Command output:', output);
      console.log('Command exit code:', code);
      
      // Close the connection
      conn.end();
    });
  });
});

// Handle connection errors
conn.on('error', (err) => {
  console.error('SSH connection error:', err);
});

// Connect to the container
console.log(`Connecting to ${config.host}:${config.port} as ${config.username}...`);
conn.connect(config);
