#!/bin/bash

# Test script for Firecracker MicroVM
# This script creates and starts a simple MicroVM using Firecracker

set -e

# Define paths
KERNEL="/var/lib/firecracker/kernels/vmlinux.bin"
ROOTFS="/var/lib/firecracker/rootfs/bionic.rootfs.ext4"
SOCKET="/tmp/firecracker.socket"
CONFIG_FILE="/tmp/firecracker-config.json"

# Clean up previous socket if it exists
rm -f $SOCKET

# Create the configuration file
cat > $CONFIG_FILE << EOF
{
  "boot-source": {
    "kernel_image_path": "$KERNEL",
    "boot_args": "console=ttyS0 reboot=k panic=1 pci=off"
  },
  "drives": [
    {
      "drive_id": "rootfs",
      "path_on_host": "$ROOTFS",
      "is_root_device": true,
      "is_read_only": false
    }
  ],
  "machine-config": {
    "vcpu_count": 1,
    "mem_size_mib": 512,
    "ht_enabled": false
  },
  "network-interfaces": []
}
EOF

# Start Firecracker in the background
firecracker --api-sock $SOCKET --config-file $CONFIG_FILE &
FC_PID=$!

echo "Firecracker MicroVM started with PID: $FC_PID"
echo "Press Ctrl+C to stop the MicroVM"

# Wait for Firecracker to exit
wait $FC_PID
