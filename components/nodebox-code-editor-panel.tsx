/**
 * Nodebox Code Editor Panel
 * 
 * Enhanced code editor panel that integrates with Nodebox store
 * for seamless file management and AI agent interactions
 */

"use client";

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@/components/ui/resizable';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Save,
  Play,
  Terminal,
  FileText,
  FolderTree,
  Monitor,
  Loader2,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Plus,
  Code
} from 'lucide-react';
import MonacoEditor from '@monaco-editor/react';
import {
  useNodeboxIntegration,
  useActiveNodeboxInstance,
  useNodeboxFileSystem,
  useNodeboxProcesses,
  useNodeboxPreviews
} from '@/lib/stores/nodebox-store';
import { useFileStore } from '@/lib/stores/file-store';
import { useOpenFilesStore } from '@/lib/stores/open-files-store';
import { FileNode } from '@/components/file-explorer/file-tree';

interface NodeboxCodeEditorPanelProps {
  className?: string;
  projectId?: string;
}

export function NodeboxCodeEditorPanel({
  className,
  projectId
}: NodeboxCodeEditorPanelProps) {
  // Nodebox integration
  const {
    activeInstance,
    isLoading,
    error,
    quickReadFile,
    quickWriteFile,
    quickRunCommand,
    loadFileSystem
  } = useNodeboxIntegration(projectId);

  // File system state
  const files = useNodeboxFileSystem(activeInstance?.id);
  const processes = useNodeboxProcesses(activeInstance?.id);
  const previews = useNodeboxPreviews(activeInstance?.id);

  // Editor state
  const { selectedFilePath, selectFile } = useFileStore();
  const { openFiles, activeFileId, openFile, setActiveFile } = useOpenFilesStore();
  
  // Local state
  const [editorContent, setEditorContent] = useState('');
  const [isFileLoading, setIsFileLoading] = useState(false);
  const [isSaving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Load file system when instance changes
  useEffect(() => {
    if (activeInstance) {
      loadFileSystem(activeInstance.id);
    }
  }, [activeInstance, loadFileSystem]);

  // Load file content when selected file changes
  useEffect(() => {
    if (selectedFilePath && activeInstance) {
      setIsFileLoading(true);
      quickReadFile(selectedFilePath)
        .then(content => {
          if (content) {
            setEditorContent(content);
          }
        })
        .catch(console.error)
        .finally(() => setIsFileLoading(false));
    }
  }, [selectedFilePath, activeInstance, quickReadFile]);

  // Handle file selection from tree
  const handleFileSelect = (file: FileNode) => {
    if (file.type === 'file') {
      selectFile(file.path);
      openFile(file);
    }
  };

  // Handle editor content change
  const handleEditorChange = (value: string | undefined) => {
    setEditorContent(value || '');
  };

  // Handle file save
  const handleSave = async () => {
    if (!selectedFilePath || !activeInstance) return;

    setSaving(true);
    try {
      const success = await quickWriteFile(selectedFilePath, editorContent);
      if (success) {
        setLastSaved(new Date());
      }
    } catch (error) {
      console.error('Error saving file:', error);
    } finally {
      setSaving(false);
    }
  };

  // Handle running dev server
  const handleRunDev = async () => {
    if (!activeInstance) return;
    await quickRunCommand('npm', ['run', 'dev']);
  };

  // Handle running build
  const handleRunBuild = async () => {
    if (!activeInstance) return;
    await quickRunCommand('npm', ['run', 'build']);
  };

  // Get file language for Monaco editor
  const getFileLanguage = (filePath: string): string => {
    const extension = filePath.split('.').pop()?.toLowerCase() || '';
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'json': 'json',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'md': 'markdown',
      'py': 'python',
      'yml': 'yaml',
      'yaml': 'yaml'
    };
    return languageMap[extension] || 'plaintext';
  };

  // Render file tree
  const renderFileTree = (files: any[], level = 0) => {
    return files.map((file) => (
      <div key={file.path} style={{ paddingLeft: `${level * 16}px` }}>
        <div
          className={cn(
            "flex items-center gap-2 p-1 rounded cursor-pointer hover:bg-muted/50",
            selectedFilePath === file.path && "bg-muted"
          )}
          onClick={() => handleFileSelect(file)}
        >
          {file.type === 'directory' ? (
            <FolderTree className="h-4 w-4 text-muted-foreground" />
          ) : (
            <FileText className="h-4 w-4 text-muted-foreground" />
          )}
          <span className="text-sm">{file.name}</span>
        </div>
        {file.children && renderFileTree(file.children, level + 1)}
      </div>
    ));
  };

  return (
    <div className={cn("flex h-full bg-background", className)}>
      <ResizablePanelGroup direction="horizontal">
        {/* File Explorer Panel */}
        <ResizablePanel defaultSize={25} minSize={20} maxSize={40}>
          <Card className="h-full rounded-none border-r">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <FolderTree className="h-4 w-4" />
                Explorer
                {activeInstance && (
                  <Badge variant="outline" className="ml-auto">
                    {files.length} files
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-2">
              {activeInstance ? (
                <ScrollArea className="h-[calc(100vh-120px)]">
                  {files.length > 0 ? (
                    <div className="space-y-1">
                      {renderFileTree(files)}
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      <FileText className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm">No files found</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() => activeInstance && loadFileSystem(activeInstance.id)}
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Refresh
                      </Button>
                    </div>
                  )}
                </ScrollArea>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  <Code className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">No active instance</p>
                </div>
              )}
            </CardContent>
          </Card>
        </ResizablePanel>

        <ResizableHandle withHandle />

        {/* Editor Panel */}
        <ResizablePanel defaultSize={75}>
          <div className="flex flex-col h-full">
            {/* Editor Header */}
            <div className="flex items-center justify-between p-2 border-b bg-muted/30">
              <div className="flex items-center gap-2">
                {selectedFilePath ? (
                  <>
                    <FileText className="h-4 w-4" />
                    <span className="text-sm font-medium">{selectedFilePath}</span>
                    {lastSaved && (
                      <Badge variant="outline" className="text-xs">
                        Saved {lastSaved.toLocaleTimeString()}
                      </Badge>
                    )}
                  </>
                ) : (
                  <span className="text-sm text-muted-foreground">No file selected</span>
                )}
              </div>

              <div className="flex items-center gap-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleSave}
                        disabled={!selectedFilePath || isSaving}
                      >
                        {isSaving ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Save file</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleRunDev}
                        disabled={!activeInstance}
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Run dev server</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleRunBuild}
                        disabled={!activeInstance}
                      >
                        <Terminal className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Build project</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            {/* Editor Content */}
            <div className="flex-1">
              {isFileLoading ? (
                <div className="flex items-center justify-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : selectedFilePath ? (
                <MonacoEditor
                  language={getFileLanguage(selectedFilePath)}
                  theme="vs-dark"
                  value={editorContent}
                  options={{
                    minimap: { enabled: true },
                    scrollBeyondLastLine: false,
                    automaticLayout: true,
                    fontSize: 14,
                    lineNumbers: 'on',
                    wordWrap: 'on',
                    folding: true,
                    fontFamily: "'JetBrains Mono', 'Fira Code', Menlo, Monaco, 'Courier New', monospace",
                    renderLineHighlight: 'all',
                    cursorBlinking: 'smooth',
                    smoothScrolling: true,
                    padding: { top: 16 },
                  }}
                  onChange={handleEditorChange}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <FileText className="h-16 w-16 mx-auto mb-4" />
                    <p>Select a file to start editing</p>
                  </div>
                </div>
              )}
            </div>

            {/* Status Bar */}
            <div className="flex items-center justify-between p-2 border-t bg-muted/30 text-xs">
              <div className="flex items-center gap-4">
                {activeInstance && (
                  <>
                    <span>Instance: {activeInstance.config.name}</span>
                    <span>Template: {activeInstance.config.template}</span>
                  </>
                )}
              </div>
              
              <div className="flex items-center gap-4">
                {processes.length > 0 && (
                  <span className="flex items-center gap-1">
                    <Terminal className="h-3 w-3" />
                    {processes.length} processes
                  </span>
                )}
                {previews.length > 0 && (
                  <span className="flex items-center gap-1">
                    <Monitor className="h-3 w-3" />
                    {previews.length} previews
                  </span>
                )}
                {error && (
                  <span className="flex items-center gap-1 text-destructive">
                    <AlertCircle className="h-3 w-3" />
                    Error
                  </span>
                )}
                {isLoading && (
                  <span className="flex items-center gap-1">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    Loading
                  </span>
                )}
              </div>
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}
