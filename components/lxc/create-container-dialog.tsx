"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { LxcTemplate } from "@/hooks/use-lxc-templates"
import { CreateContainerOptions } from "@/hooks/use-lxc-containers"
import { Loader2 } from "lucide-react"

interface CreateContainerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  templates: LxcTemplate[]
  onCreateContainer: (options: CreateContainerOptions) => Promise<any>
}

export function CreateContainerDialog({
  open,
  onOpenChange,
  templates,
  onCreateContainer,
}: CreateContainerDialogProps) {
  const [name, setName] = useState("")
  const [template, setTemplate] = useState("")
  const [autostart, setAutostart] = useState(true)
  const [creating, setCreating] = useState(false)

  const selectedTemplate = templates.find((t) => t.id === template)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name || !template) {
      return
    }

    try {
      setCreating(true)
      
      const options: CreateContainerOptions = {
        name,
        template: selectedTemplate?.name || "",
        release: selectedTemplate?.release,
        arch: selectedTemplate?.arch,
        variant: selectedTemplate?.variant,
        config: {
          autostart,
        },
      }
      
      await onCreateContainer(options)
      onOpenChange(false)
      
      // Reset form
      setName("")
      setTemplate("")
      setAutostart(true)
    } finally {
      setCreating(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create Container</DialogTitle>
            <DialogDescription>
              Create a new LXC container with the specified settings.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                placeholder="my-container"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template" className="text-right">
                Template
              </Label>
              <div className="col-span-3">
                <Select value={template} onValueChange={setTemplate} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a template" />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.length === 0 ? (
                      <div className="p-2 text-center text-muted-foreground">
                        No templates available
                      </div>
                    ) : (
                      templates.map((t) => (
                        <SelectItem key={t.id} value={t.id}>
                          {t.name} ({t.release || ""} {t.arch || ""})
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-span-4 flex items-center space-x-2">
                <Checkbox
                  id="autostart"
                  checked={autostart}
                  onCheckedChange={(checked) => setAutostart(checked as boolean)}
                />
                <Label htmlFor="autostart">Start container automatically</Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating || !name || !template}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
