"use client"

import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Loader2 } from "lucide-react"
import { NodeInfo, StorageInfo } from "@/lib/proxmox/types"
import { useToast } from "@/components/ui/use-toast"

// Form schema
const formSchema = z.object({
  node: z.string({
    required_error: "Please select a node",
  }),
  vmid: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 100, {
    message: "VMID must be a number greater than 100",
  }),
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  cores: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: "Cores must be a positive number",
  }),
  memory: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: "Memory must be a positive number",
  }),
  storage: z.string({
    required_error: "Please select a storage",
  }),
  diskSize: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: "Disk size must be a positive number",
  }),
  diskFormat: z.enum(["raw", "qcow2", "vmdk"]),
  networkModel: z.enum(["virtio", "e1000", "rtl8139"]),
  bridge: z.string().optional(),
  onBoot: z.boolean().default(false),
  start: z.boolean().default(false),
})

type FormValues = z.infer<typeof formSchema>

interface CreateVMFormProps {
  nodes: NodeInfo[]
  storage: StorageInfo[]
  onSubmit: (values: any) => Promise<void>
  className?: string
}

export function CreateVMForm({ nodes, storage, onSubmit, className }: CreateVMFormProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // Default values
  const defaultValues: Partial<FormValues> = {
    cores: "1",
    memory: "512",
    diskSize: "8",
    diskFormat: "qcow2",
    networkModel: "virtio",
    bridge: "vmbr0",
    onBoot: true,
    start: true,
  }
  
  // Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  })
  
  // Handle form submission
  const handleSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true)
      
      // Convert values to appropriate types
      const formattedValues = {
        node: values.node,
        vmid: parseInt(values.vmid),
        name: values.name,
        description: values.description,
        cores: parseInt(values.cores),
        memory: parseInt(values.memory),
        disks: [
          {
            storage: values.storage,
            size: parseInt(values.diskSize),
            format: values.diskFormat,
          },
        ],
        networks: [
          {
            model: values.networkModel,
            bridge: values.bridge,
          },
        ],
        onBoot: values.onBoot,
        start: values.start,
      }
      
      await onSubmit(formattedValues)
      
      toast({
        title: "VM Creation Started",
        description: "Your virtual machine is being created. This may take a few minutes.",
      })
      
      // Reset form
      form.reset(defaultValues)
    } catch (error) {
      console.error("Error creating VM:", error)
      toast({
        title: "Error",
        description: "Failed to create VM. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={`space-y-6 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Node Selection */}
          <FormField
            control={form.control}
            name="node"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Node</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a node" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {nodes.map((node) => (
                      <SelectItem key={node.id} value={node.id}>
                        {node.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  The physical server where the VM will be created
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* VMID */}
          <FormField
            control={form.control}
            name="vmid"
            render={({ field }) => (
              <FormItem>
                <FormLabel>VM ID</FormLabel>
                <FormControl>
                  <Input placeholder="100" {...field} />
                </FormControl>
                <FormDescription>
                  Unique identifier for the VM (number > 100)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Basic VM Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="my-vm" {...field} />
                </FormControl>
                <FormDescription>
                  Name of the virtual machine
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Input placeholder="Optional description" {...field} />
                </FormControl>
                <FormDescription>
                  Optional description for the VM
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <Separator />
        
        {/* Resources */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="cores"
            render={({ field }) => (
              <FormItem>
                <FormLabel>CPU Cores</FormLabel>
                <FormControl>
                  <Input type="number" min="1" {...field} />
                </FormControl>
                <FormDescription>
                  Number of CPU cores
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="memory"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Memory (MB)</FormLabel>
                <FormControl>
                  <Input type="number" min="128" step="128" {...field} />
                </FormControl>
                <FormDescription>
                  Amount of RAM in MB (e.g., 512, 1024, 2048)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <Separator />
        
        {/* Storage */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <FormField
            control={form.control}
            name="storage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Storage</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select storage" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {storage.map((s) => (
                      <SelectItem key={s.id} value={s.id}>
                        {s.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Storage pool for the VM disk
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="diskSize"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Disk Size (GB)</FormLabel>
                <FormControl>
                  <Input type="number" min="1" {...field} />
                </FormControl>
                <FormDescription>
                  Size of the disk in GB
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="diskFormat"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Disk Format</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="raw">Raw</SelectItem>
                    <SelectItem value="qcow2">QCOW2</SelectItem>
                    <SelectItem value="vmdk">VMDK</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Format of the disk image
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <Separator />
        
        {/* Network */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="networkModel"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Network Model</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="virtio">VirtIO</SelectItem>
                    <SelectItem value="e1000">Intel E1000</SelectItem>
                    <SelectItem value="rtl8139">Realtek RTL8139</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Network interface model
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="bridge"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bridge</FormLabel>
                <FormControl>
                  <Input placeholder="vmbr0" {...field} />
                </FormControl>
                <FormDescription>
                  Network bridge to connect to
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <Separator />
        
        {/* Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="onBoot"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Start on Boot</FormLabel>
                  <FormDescription>
                    Automatically start this VM when the node boots
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="start"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Start After Creation</FormLabel>
                  <FormDescription>
                    Start the VM immediately after creation
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
        </div>
        
        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating VM...
            </>
          ) : (
            "Create VM"
          )}
        </Button>
      </form>
    </Form>
  )
}
