"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

// Form schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  vmstate: z.boolean().default(false),
})

type FormValues = z.infer<typeof formSchema>

interface CreateSnapshotFormProps {
  vmId: string
  node: string
  onSubmit: (values: any) => Promise<void>
  className?: string
}

export function CreateSnapshotForm({ vmId, node, onSubmit, className }: CreateSnapshotFormProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // Default values
  const defaultValues: Partial<FormValues> = {
    name: `snap_${new Date().toISOString().replace(/[:.]/g, '-')}`,
    vmstate: true,
  }
  
  // Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  })
  
  // Handle form submission
  const handleSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true)
      
      await onSubmit({
        node,
        vmid: vmId,
        ...values,
      })
      
      toast({
        title: "Snapshot Created",
        description: "Your snapshot has been created successfully.",
      })
      
      // Reset form
      form.reset(defaultValues)
    } catch (error) {
      console.error("Error creating snapshot:", error)
      toast({
        title: "Error",
        description: "Failed to create snapshot. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={`space-y-6 ${className}`}>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Snapshot Name</FormLabel>
              <FormControl>
                <Input placeholder="my-snapshot" {...field} />
              </FormControl>
              <FormDescription>
                A unique name for this snapshot
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Optional description of this snapshot" 
                  className="resize-none"
                  {...field} 
                />
              </FormControl>
              <FormDescription>
                Optional description to help identify this snapshot later
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="vmstate"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Include VM State</FormLabel>
                <FormDescription>
                  Save the VM's running state (RAM contents) in the snapshot
                </FormDescription>
              </div>
            </FormItem>
          )}
        />
        
        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Snapshot...
            </>
          ) : (
            "Create Snapshot"
          )}
        </Button>
      </form>
    </Form>
  )
}
