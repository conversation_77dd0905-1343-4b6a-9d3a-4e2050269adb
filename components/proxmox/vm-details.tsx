"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { 
  HardDrive, 
  Server, 
  Cpu, 
  Memory, 
  Network, 
  Clock, 
  Calendar, 
  Camera, 
  ArrowLeft,
  RefreshCw
} from "lucide-react"
import { VMInfo } from "@/lib/proxmox/types"
import { StatusBadge } from "./status-badge"
import { ResourceMeter } from "./resource-meter"
import { VMActions } from "./vm-actions"
import { SnapshotList } from "./snapshot-list"
import { CreateSnapshotForm } from "./create-snapshot-form"
import { cn } from "@/lib/utils"

interface VMDetailsProps {
  vm: VMInfo
  snapshots: any[]
  onBack: () => void
  onRefresh: () => void
  onAction: (action: string, vmId: string) => Promise<void>
  onCreateSnapshot: (values: any) => Promise<void>
  onRollbackSnapshot: (snapshot: string) => Promise<void>
  onDeleteSnapshot: (snapshot: string) => Promise<void>
  className?: string
}

export function VMDetails({ 
  vm, 
  snapshots,
  onBack,
  onRefresh,
  onAction,
  onCreateSnapshot,
  onRollbackSnapshot,
  onDeleteSnapshot,
  className 
}: VMDetailsProps) {
  const [activeTab, setActiveTab] = useState("overview")
  
  // Format uptime
  const formatUptime = (seconds?: number) => {
    if (!seconds) return 'N/A'
    
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m ${seconds % 60}s`
    }
  }
  
  // Format date
  const formatDate = (date?: Date) => {
    if (!date) return 'N/A'
    return date.toLocaleString()
  }

  return (
    <div className={className}>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">{vm.name}</h1>
          <StatusBadge status={vm.status} />
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
          
          <VMActions
            vmId={vm.id}
            status={vm.status}
            node={vm.node}
            onAction={onAction}
            size="sm"
            showLabels
          />
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="snapshots">Snapshots</TabsTrigger>
          <TabsTrigger value="console">Console</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>General Information</CardTitle>
                <CardDescription>Basic VM details and configuration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">VM ID</p>
                    <p className="text-sm">{vm.id}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Node</p>
                    <p className="text-sm flex items-center">
                      <Server className="mr-1 h-3 w-3 text-muted-foreground" />
                      {vm.node}
                    </p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Type</p>
                    <p className="text-sm">{vm.type}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Status</p>
                    <StatusBadge status={vm.status} size="sm" />
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Uptime</p>
                    <p className="text-sm flex items-center">
                      <Clock className="mr-1 h-3 w-3 text-muted-foreground" />
                      {formatUptime(vm.uptime)}
                    </p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Start on Boot</p>
                    <p className="text-sm">{vm.config?.onBoot ? 'Yes' : 'No'}</p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Description</p>
                  <p className="text-sm">{vm.config?.description || 'No description'}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Resources</CardTitle>
                <CardDescription>CPU, memory, and disk usage</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium flex items-center">
                      <Cpu className="mr-1 h-4 w-4 text-muted-foreground" />
                      CPU
                    </p>
                    <p className="text-sm">{vm.config?.cpu?.cores || 1} cores</p>
                  </div>
                  
                  {vm.cpuUsage !== undefined && (
                    <ResourceMeter
                      label=""
                      value={vm.cpuUsage}
                      max={1}
                      unit=""
                      showPercentage={true}
                      size="sm"
                    />
                  )}
                </div>
                
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium flex items-center">
                      <Memory className="mr-1 h-4 w-4 text-muted-foreground" />
                      Memory
                    </p>
                    <p className="text-sm">{vm.config?.memory || 'N/A'} MB</p>
                  </div>
                  
                  {vm.memoryUsage !== undefined && vm.config?.memory && (
                    <ResourceMeter
                      label=""
                      value={vm.memoryUsage}
                      max={vm.config.memory * 1024 * 1024}
                      unit="MB"
                      showPercentage={true}
                      size="sm"
                    />
                  )}
                </div>
                
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium flex items-center">
                      <HardDrive className="mr-1 h-4 w-4 text-muted-foreground" />
                      Disk
                    </p>
                    <p className="text-sm">
                      {vm.diskUsage !== undefined 
                        ? `${(vm.diskUsage / 1024 / 1024 / 1024).toFixed(1)} GB used` 
                        : 'N/A'}
                    </p>
                  </div>
                  
                  {vm.diskUsage !== undefined && (
                    <ResourceMeter
                      label=""
                      value={vm.diskUsage}
                      max={100 * 1024 * 1024 * 1024} // Assuming 100GB total disk
                      unit="GB"
                      showPercentage={true}
                      size="sm"
                    />
                  )}
                </div>
                
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium flex items-center">
                      <Network className="mr-1 h-4 w-4 text-muted-foreground" />
                      Network
                    </p>
                  </div>
                  
                  {vm.config?.networks?.map((network, index) => (
                    <div key={index} className="text-sm pl-6">
                      <p>
                        <span className="font-medium">net{index}:</span> {network.model}
                        {network.bridge && ` (bridge: ${network.bridge})`}
                        {network.vlan && ` (vlan: ${network.vlan})`}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="snapshots">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Snapshots</CardTitle>
                  <CardDescription>VM state snapshots for backup and recovery</CardDescription>
                </CardHeader>
                <CardContent>
                  <SnapshotList
                    snapshots={snapshots}
                    vmId={vm.id}
                    node={vm.node}
                    onRollback={onRollbackSnapshot}
                    onDelete={onDeleteSnapshot}
                  />
                </CardContent>
              </Card>
            </div>
            
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Create Snapshot</CardTitle>
                  <CardDescription>Save the current state of your VM</CardDescription>
                </CardHeader>
                <CardContent>
                  <CreateSnapshotForm
                    vmId={vm.id}
                    node={vm.node}
                    onSubmit={onCreateSnapshot}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="console">
          <Card>
            <CardHeader>
              <CardTitle>VM Console</CardTitle>
              <CardDescription>Access the VM's console</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md text-center">
                <p>Console access is not yet implemented in this interface.</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Please use the Proxmox web interface to access the console.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="logs">
          <Card>
            <CardHeader>
              <CardTitle>VM Logs</CardTitle>
              <CardDescription>View logs related to this VM</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md text-center">
                <p>Log viewing is not yet implemented in this interface.</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Please use the Proxmox web interface to view logs.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
