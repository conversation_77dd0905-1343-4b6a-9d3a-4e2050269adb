"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Server, Clock } from "lucide-react"
import { NodeInfo } from "@/lib/proxmox/types"
import { StatusBadge } from "./status-badge"
import { ResourceMeter } from "./resource-meter"
import { cn } from "@/lib/utils"
import { formatUptime } from "@/lib/utils/format"

interface NodeCardProps {
  node: NodeInfo
  selected?: boolean
  onSelect?: (nodeId: string) => void
  className?: string
}

export function NodeCard({ node, selected = false, onSelect, className }: NodeCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  
  const handleClick = () => {
    if (onSelect) {
      onSelect(node.id)
    }
  }

  // Format uptime
  const uptimeFormatted = node.uptime ? formatUptime(node.uptime) : 'N/A'

  return (
    <Card 
      className={cn(
        "transition-all duration-200 overflow-hidden",
        selected ? "border-primary ring-1 ring-primary" : "",
        onSelect ? "cursor-pointer hover:shadow-md" : "",
        isHovered ? "shadow-md" : "",
        className
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center">
            <Server className="mr-2 h-5 w-5 text-muted-foreground" />
            <CardTitle>{node.name}</CardTitle>
          </div>
          <StatusBadge status={node.status} />
        </div>
        {node.ip && (
          <CardDescription>IP: {node.ip}</CardDescription>
        )}
      </CardHeader>
      
      <CardContent className="pb-2">
        <div className="space-y-3">
          {node.cpu && (
            <ResourceMeter
              label="CPU Usage"
              value={node.cpu.usage}
              max={1}
              unit=""
              showPercentage={true}
              size="sm"
            />
          )}
          
          {node.memory && (
            <ResourceMeter
              label="Memory Usage"
              value={node.memory.used}
              max={node.memory.total}
              unit="GB"
              showPercentage={true}
              size="sm"
            />
          )}
          
          <div className="flex items-center text-sm mt-2">
            <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="font-medium">Uptime:</span>
            <span className="ml-2">{uptimeFormatted}</span>
          </div>
        </div>
      </CardContent>
      
      {node.cpu?.model && (
        <CardFooter className="pt-0 text-xs text-muted-foreground">
          {node.cpu.model} ({node.cpu.cores} cores)
        </CardFooter>
      )}
    </Card>
  )
}
