"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2, Save, RotateCcw, Trash2, AlertCircle } from "lucide-react"
import { useVMBackups } from "@/hooks/use-vm-backup"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface VMBackupsProps {
  serverId: string
  nodeId: string
  vmId: string
}

const backupFormSchema = z.object({
  mode: z.enum(["snapshot", "suspend", "stop"], {
    required_error: "You need to select a backup mode",
  }),
  compress: z.boolean().default(true),
  description: z.string().optional(),
})

type BackupFormValues = z.infer<typeof backupFormSchema>

export function VMBackups({ serverId, nodeId, vmId }: VMBackupsProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedBackup, setSelectedBackup] = useState<any>(null)

  const {
    backups,
    isLoading,
    error,
    createBackup,
    isCreatingBackup,
    restoreBackup,
    isRestoringBackup,
    deleteBackup,
    isDeletingBackup,
  } = useVMBackups(serverId, nodeId, vmId)

  const form = useForm<BackupFormValues>({
    resolver: zodResolver(backupFormSchema),
    defaultValues: {
      mode: "snapshot",
      compress: true,
      description: "",
    },
  })

  function onCreateBackup(values: BackupFormValues) {
    createBackup({
      mode: values.mode,
      compress: values.compress,
      description: values.description,
    })
    setIsCreateDialogOpen(false)
  }

  function onRestoreBackup() {
    if (selectedBackup) {
      restoreBackup(selectedBackup.volid)
      setIsRestoreDialogOpen(false)
    }
  }

  function onDeleteBackup() {
    if (selectedBackup) {
      deleteBackup(selectedBackup.volid)
      setIsDeleteDialogOpen(false)
    }
  }

  function formatDate(timestamp: number) {
    return new Date(timestamp * 1000).toLocaleString()
  }

  function formatSize(bytes: number) {
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"]
    if (bytes === 0) return "0 Byte"
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i)) + " " + sizes[i]
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Backups</CardTitle>
          <CardDescription>Manage VM backups and snapshots</CardDescription>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Save className="mr-2 h-4 w-4" />
              Create Backup
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Backup</DialogTitle>
              <DialogDescription>Create a new backup or snapshot of the virtual machine</DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onCreateBackup)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="mode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Backup Mode</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-1"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="snapshot" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Snapshot - Create a snapshot (VM keeps running)
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="suspend" />
                            </FormControl>
                            <FormLabel className="font-normal">Suspend - Suspend the VM during backup</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="stop" />
                            </FormControl>
                            <FormLabel className="font-normal">Stop - Stop the VM during backup</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="compress"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Compress backup</FormLabel>
                        <FormDescription>Compress the backup to save storage space</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Backup description" {...field} />
                      </FormControl>
                      <FormDescription>A description to help identify this backup</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isCreatingBackup}>
                    {isCreatingBackup ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Backup"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error instanceof Error ? error.message : "Failed to load backups"}</AlertDescription>
          </Alert>
        ) : backups.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Save className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold">No backups found</h3>
            <p className="text-muted-foreground mt-1">Create a backup to protect your virtual machine</p>
          </div>
        ) : (
          <div className="space-y-4">
            {backups.map((backup) => (
              <div
                key={backup.volid}
                className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-lg"
              >
                <div className="space-y-1 mb-2 sm:mb-0">
                  <div className="font-medium">{backup.volid}</div>
                  <div className="text-sm text-muted-foreground">
                    Created: {formatDate(backup.ctime)} | Size: {formatSize(backup.size)}
                  </div>
                  {backup.description && <div className="text-sm">{backup.description}</div>}
                </div>
                <div className="flex space-x-2">
                  <Dialog
                    open={isRestoreDialogOpen && selectedBackup?.volid === backup.volid}
                    onOpenChange={(open) => {
                      setIsRestoreDialogOpen(open)
                      if (open) setSelectedBackup(backup)
                    }}
                  >
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <RotateCcw className="h-4 w-4 mr-1" />
                        Restore
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Restore Backup</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to restore this backup? This will revert the VM to the state it was in
                          when the backup was created.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => setIsRestoreDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={onRestoreBackup} disabled={isRestoringBackup} variant="destructive">
                          {isRestoringBackup ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Restoring...
                            </>
                          ) : (
                            "Restore Backup"
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <Dialog
                    open={isDeleteDialogOpen && selectedBackup?.volid === backup.volid}
                    onOpenChange={(open) => {
                      setIsDeleteDialogOpen(open)
                      if (open) setSelectedBackup(backup)
                    }}
                  >
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Delete Backup</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to delete this backup? This action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={onDeleteBackup} disabled={isDeletingBackup} variant="destructive">
                          {isDeletingBackup ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Deleting...
                            </>
                          ) : (
                            "Delete Backup"
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
