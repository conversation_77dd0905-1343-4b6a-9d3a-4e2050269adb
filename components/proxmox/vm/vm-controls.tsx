"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Play, Square, RotateCw } from "lucide-react"
import { useVirtualMachine } from "@/hooks/use-virtual-machines"

interface VmControlsProps {
  serverId: string
  vmId: string
}

export function VirtualMachineControls({ serverId, vmId }: VmControlsProps) {
  const { vm, startVm, stopVm, resetVm, isActionLoading } = useVirtualMachine(serverId, vmId)

  const isRunning = vm?.status === "running"

  return (
    <div className="flex space-x-2">
      <Button variant="outline" disabled={isRunning || isActionLoading} onClick={startVm}>
        <Play className="mr-2 h-4 w-4" />
        Start
      </Button>
      <Button variant="outline" disabled={!isRunning || isActionLoading} onClick={stopVm}>
        <Square className="mr-2 h-4 w-4" />
        Stop
      </Button>
      <Button variant="outline" disabled={!isRunning || isActionLoading} onClick={resetVm}>
        <RotateCw className="mr-2 h-4 w-4" />
        Reset
      </Button>
    </div>
  )
}
