"use client"

import { useState, useEffect } from "react"
// Using Iconify icons instead of Next.js Image
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Loader2, Search, Download, Star, Filter, ArrowUpDown } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Iconify } from "@/components/ui/iconify"

interface MarketplaceImage {
  id: string
  name: string
  description: string
  osType: string
  version: string
  size: string
  category: string
  tags: string[]
  downloadUrl: string
  iconifyIcon: string
  iconColor?: string
  publisher: string
  popularity: number
  featured: boolean
}

interface ImageMarketplaceProps {
  serverId: string
  node: string
  storage: string
  onImageSelected: (imageName: string) => void
}

export function ImageMarketplace({ serverId, node, storage, onImageSelected }: ImageMarketplaceProps) {
  const [images, setImages] = useState<MarketplaceImage[]>([])
  const [filteredImages, setFilteredImages] = useState<MarketplaceImage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedOsType, setSelectedOsType] = useState("all")
  const [sortBy, setSortBy] = useState("popularity")
  const [sortOrder, setSortOrder] = useState("desc")
  const [showFilters, setShowFilters] = useState(false)
  const [selectedImage, setSelectedImage] = useState<MarketplaceImage | null>(null)
  const [isDownloading, setIsDownloading] = useState(false)
  const [downloadProgress, setDownloadProgress] = useState(0)
  const [downloadTaskId, setDownloadTaskId] = useState<string | null>(null)

  // Fetch marketplace images
  useEffect(() => {
    async function fetchImages() {
      try {
        setIsLoading(true)
        const response = await fetch("/api/marketplace/images")

        if (!response.ok) {
          throw new Error("Failed to fetch marketplace images")
        }

        const data = await response.json()

        if (data.success) {
          setImages(data.data)
          setFilteredImages(data.data)
        } else {
          throw new Error(data.error || "Failed to load marketplace images")
        }
      } catch (error) {
        console.error("Error fetching marketplace images:", error)
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load marketplace images",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchImages()
  }, [])

  // Filter and sort images
  useEffect(() => {
    let result = [...images]

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        (image) =>
          image.name.toLowerCase().includes(query) ||
          image.description.toLowerCase().includes(query) ||
          image.tags.some((tag) => tag.toLowerCase().includes(query)) ||
          image.publisher.toLowerCase().includes(query)
      )
    }

    // Apply category filter
    if (selectedCategory !== "all") {
      result = result.filter((image) => image.category === selectedCategory)
    }

    // Apply OS type filter
    if (selectedOsType !== "all") {
      result = result.filter((image) => image.osType === selectedOsType)
    }

    // Apply sorting
    result.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name)
          break
        case "popularity":
          comparison = a.popularity - b.popularity
          break
        case "size":
          comparison = parseFloat(a.size) - parseFloat(b.size)
          break
        case "publisher":
          comparison = a.publisher.localeCompare(b.publisher)
          break
        default:
          comparison = 0
      }

      return sortOrder === "asc" ? comparison : -comparison
    })

    setFilteredImages(result)
  }, [images, searchQuery, selectedCategory, selectedOsType, sortBy, sortOrder])

  // Download image
  const downloadImage = async (image: MarketplaceImage) => {
    if (!serverId || !node || !storage) {
      toast({
        title: "Error",
        description: "Missing server, node, or storage information",
        variant: "destructive",
      })
      return
    }

    try {
      setIsDownloading(true)
      setDownloadProgress(0)

      // Extract filename from URL
      const urlParts = image.downloadUrl.split("/")
      const filename = urlParts[urlParts.length - 1]

      // Start download
      const response = await fetch(`/api/proxmox/marketplace/download`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          node,
          storage,
          imageUrl: image.downloadUrl,
          imageName: filename,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to download image")
      }

      const data = await response.json()

      if (data.success) {
        setDownloadTaskId(data.data.taskId)

        // Start progress tracking
        const progressInterval = setInterval(async () => {
          try {
            // Check task status
            const statusResponse = await fetch(`/api/proxmox/tasks/${data.data.taskId}`)
            const statusData = await statusResponse.json()

            if (statusData.success) {
              if (statusData.data.status === "running") {
                // Update progress
                setDownloadProgress(statusData.data.progress * 100 || 0)
              } else if (statusData.data.status === "stopped") {
                // Download completed or failed
                clearInterval(progressInterval)
                setIsDownloading(false)

                if (statusData.data.exitstatus === "OK") {
                  toast({
                    title: "Success",
                    description: `${image.name} downloaded successfully`,
                  })
                  // Notify parent component
                  onImageSelected(filename)
                } else {
                  throw new Error(`Download failed: ${statusData.data.exitstatus}`)
                }
              }
            }
          } catch (error) {
            console.error("Error checking download status:", error)
            clearInterval(progressInterval)
            setIsDownloading(false)
            throw error
          }
        }, 2000)
      } else {
        throw new Error(data.error || "Failed to download image")
      }
    } catch (error) {
      console.error("Error downloading image:", error)
      setIsDownloading(false)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to download image",
        variant: "destructive",
      })
    }
  }

  // Cancel download
  const cancelDownload = async () => {
    if (!downloadTaskId) return

    try {
      const response = await fetch(`/api/proxmox/tasks/${downloadTaskId}/cancel`, {
        method: "POST",
      })

      if (!response.ok) {
        throw new Error("Failed to cancel download")
      }

      setIsDownloading(false)
      setDownloadProgress(0)
      setDownloadTaskId(null)

      toast({
        title: "Download Cancelled",
        description: "The image download has been cancelled",
      })
    } catch (error) {
      console.error("Error cancelling download:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cancel download",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="flex-1 w-full">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search images..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex gap-2 w-full md:w-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </Button>

          <Select
            value={sortBy}
            onValueChange={setSortBy}
          >
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <ArrowUpDown className="mr-2 h-4 w-4" />
                <span>Sort by</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="popularity">Popularity</SelectItem>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="size">Size</SelectItem>
              <SelectItem value="publisher">Publisher</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
          >
            {sortOrder === "asc" ? "↑" : "↓"}
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="mb-2 block">OS Type</Label>
                <Select
                  value={selectedOsType}
                  onValueChange={setSelectedOsType}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select OS type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All OS Types</SelectItem>
                    <SelectItem value="linux">Linux</SelectItem>
                    <SelectItem value="windows">Windows</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="mb-2 block">Category</Label>
                <Select
                  value={selectedCategory}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="Server">Server</SelectItem>
                    <SelectItem value="Desktop">Desktop</SelectItem>
                    <SelectItem value="Appliance">Appliance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Loading marketplace images...</p>
        </div>
      ) : filteredImages.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <p className="text-muted-foreground mb-2">No images found matching your criteria</p>
          <Button variant="outline" onClick={() => {
            setSearchQuery("")
            setSelectedCategory("all")
            setSelectedOsType("all")
          }}>
            Clear Filters
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredImages.map((image) => (
            <Card key={image.id} className="overflow-hidden">
              <CardHeader className="p-4 pb-0">
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <div className="w-10 h-10 mr-3 relative">
                      <div className="w-10 h-10 bg-muted rounded-md flex items-center justify-center">
                        <Iconify
                          icon={image.iconifyIcon}
                          width={32}
                          height={32}
                          color={image.iconColor}
                        />
                      </div>
                    </div>
                    <div>
                      <CardTitle className="text-base">{image.name}</CardTitle>
                      <CardDescription className="text-xs">
                        {image.publisher} • {image.version}
                      </CardDescription>
                    </div>
                  </div>
                  {image.featured && (
                    <Badge variant="secondary" className="ml-2">
                      <Star className="h-3 w-3 mr-1 fill-current" />
                      Featured
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-4">
                <p className="text-sm text-muted-foreground line-clamp-2 h-10">
                  {image.description}
                </p>
                <div className="flex flex-wrap gap-1 mt-2">
                  <Badge variant="outline" className="text-xs">
                    {image.size}
                  </Badge>
                  {image.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="p-4 pt-0">
                <Button
                  className="w-full"
                  onClick={() => setSelectedImage(image)}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download & Use
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Image Details Dialog */}
      <Dialog open={!!selectedImage} onOpenChange={(open) => !open && setSelectedImage(null)}>
        <DialogContent className="max-w-2xl">
          {selectedImage && (
            <>
              <DialogHeader>
                <div className="flex items-center">
                  <div className="w-12 h-12 mr-4 relative">
                    <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
                      <Iconify
                        icon={selectedImage.iconifyIcon}
                        width={40}
                        height={40}
                        color={selectedImage.iconColor}
                      />
                    </div>
                  </div>
                  <div>
                    <DialogTitle>{selectedImage.name}</DialogTitle>
                    <DialogDescription>
                      {selectedImage.publisher} • Version {selectedImage.version}
                    </DialogDescription>
                  </div>
                </div>
              </DialogHeader>

              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-1">Description</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedImage.description}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium mb-1">OS Type</h4>
                    <p className="text-sm text-muted-foreground capitalize">
                      {selectedImage.osType}
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium mb-1">Size</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedImage.size}
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-1">Tags</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedImage.tags.map((tag) => (
                      <Badge key={tag} variant="outline">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                {isDownloading && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">Downloading...</h4>
                      <span className="text-sm text-muted-foreground">
                        {downloadProgress.toFixed(0)}%
                      </span>
                    </div>
                    <Progress value={downloadProgress} className="h-2" />
                  </div>
                )}
              </div>

              <DialogFooter>
                {isDownloading ? (
                  <Button variant="destructive" onClick={cancelDownload}>
                    Cancel Download
                  </Button>
                ) : (
                  <>
                    <Button variant="outline" onClick={() => setSelectedImage(null)}>
                      Cancel
                    </Button>
                    <Button onClick={() => downloadImage(selectedImage)}>
                      <Download className="mr-2 h-4 w-4" />
                      Download & Use
                    </Button>
                  </>
                )}
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
