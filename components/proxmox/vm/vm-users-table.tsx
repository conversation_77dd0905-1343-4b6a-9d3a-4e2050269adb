"use client"

import { useState } from "react"
import { useVMUsers, useRemoveUserFromVM } from "@/hooks/use-vm-users"
import { Button } from "@/components/ui/button"
import { Icon } from "@/components/ui/icon"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { formatDistanceToNow } from "date-fns"
import { VMUserAssignmentDialog } from "@/components/vm/vm-user-assignment-dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Skeleton } from "@/components/ui/skeleton"

interface VMUsersTableProps {
  vmId: string
  isOwner: boolean
  isAdmin: boolean
}

export function VMUsersTable({ vmId, isOwner, isAdmin }: VMUsersTableProps) {
  const { data: users, isLoading, error } = useVMUsers(vmId)
  const removeUser = useRemoveUserFromVM(vmId)
  
  const [editingUser, setEditingUser] = useState<string | null>(null)
  const [userToRemove, setUserToRemove] = useState<string | null>(null)
  const [showAddUserDialog, setShowAddUserDialog] = useState(false)

  // Get role badge color
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "default"
      case "OPERATOR":
        return "secondary"
      case "VIEWER":
      default:
        return "outline"
    }
  }

  // Get user initials for avatar fallback
  const getUserInitials = (name: string | undefined, email: string) => {
    if (name) {
      return name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .substring(0, 2)
    }
    return email.substring(0, 2).toUpperCase()
  }

  // Handle user removal
  const handleRemoveUser = async () => {
    if (userToRemove) {
      await removeUser.mutateAsync(userToRemove)
      setUserToRemove(null)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">VM Users</h3>
          <Skeleton className="h-9 w-[120px]" />
        </div>
        <div className="border rounded-md">
          <div className="p-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 py-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-md bg-destructive/10 p-4">
        <div className="flex items-center gap-2 text-destructive">
          <Icon name="mdi:alert-circle" size="sm" />
          <p className="text-sm font-medium">Failed to load VM users</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">VM Users</h3>
        {(isOwner || isAdmin) && (
          <Button size="sm" onClick={() => setShowAddUserDialog(true)}>
            <Icon name="mdi:account-plus" size="sm" className="mr-2" />
            Add User
          </Button>
        )}
      </div>

      {users && users.length > 0 ? (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Added</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((assignment) => (
                <TableRow key={assignment.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={assignment.user?.image || ""} />
                        <AvatarFallback>
                          {getUserInitials(assignment.user?.name, assignment.user?.email || "")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{assignment.user?.name || "Unnamed User"}</div>
                        <div className="text-sm text-muted-foreground">{assignment.user?.email}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(assignment.role)}>
                      {assignment.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(assignment.createdAt), { addSuffix: true })}
                    </span>
                  </TableCell>
                  <TableCell>
                    {(isOwner || isAdmin) && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <Icon name="mdi:dots-vertical" size="sm" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => setEditingUser(assignment.userId)}>
                            <Icon name="mdi:pencil" size="sm" className="mr-2" />
                            Edit Role
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => setUserToRemove(assignment.userId)}
                          >
                            <Icon name="mdi:delete" size="sm" className="mr-2" />
                            Remove
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="border rounded-md p-8 text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
            <Icon name="mdi:account-multiple" size="md" className="text-primary" />
          </div>
          <h3 className="mt-4 text-lg font-medium">No users assigned</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            This VM doesn't have any users assigned to it yet.
          </p>
          {(isOwner || isAdmin) && (
            <Button className="mt-4" onClick={() => setShowAddUserDialog(true)}>
              <Icon name="mdi:account-plus" size="sm" className="mr-2" />
              Add User
            </Button>
          )}
        </div>
      )}

      {/* Add User Dialog */}
      <VMUserAssignmentDialog
        vmId={vmId}
        open={showAddUserDialog}
        onOpenChange={setShowAddUserDialog}
      />

      {/* Edit User Dialog */}
      {editingUser && (
        <VMUserAssignmentDialog
          vmId={vmId}
          userId={editingUser}
          open={!!editingUser}
          onOpenChange={(open) => !open && setEditingUser(null)}
          mode="edit"
        />
      )}

      {/* Remove User Confirmation Dialog */}
      <AlertDialog open={!!userToRemove} onOpenChange={(open) => !open && setUserToRemove(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove User</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this user from the VM? They will lose all access to this VM.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRemoveUser}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
