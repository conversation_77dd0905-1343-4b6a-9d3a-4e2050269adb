"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { SSHDialog } from "@/components/ssh/ssh-dialog"
import { SSHAIDialog } from "@/components/ai-assistant/ssh-ai-dialog"
import { Icon } from "@/components/ui/icon"
import { sshIcons } from "@/lib/icon-config"
import { toast } from "@/hooks/use-toast"

interface VMSSHProps {
  vmId: string
  vmName: string
  ipAddress?: string
  status: string
  username?: string
  port?: number
  serverId?: string
  osType?: string
}

export function VMSSH({
  vmId,
  vmName,
  ipAddress,
  status,
  username = "root",
  port = 22,
  serverId,
  osType = "linux"
}: VMSSHProps) {
  const [dialogOpen, setDialogOpen] = useState(false)

  const handleSSHClick = () => {
    if (status !== "running") {
      toast({
        title: "VM Not Running",
        description: "The VM must be running to establish an SSH connection.",
        variant: "destructive"
      })
      return
    }

    if (!ipAddress) {
      toast({
        title: "No IP Address",
        description: "Could not determine the VM's IP address. Please make sure the VM has a valid network configuration.",
        variant: "destructive"
      })
      return
    }

    setDialogOpen(true)
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">SSH Connection</CardTitle>
          <CardDescription>
            Connect to this VM via SSH
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Status</span>
              <span className={status === "running" ? "text-green-600" : "text-amber-600"}>
                {status === "running" ? "Available" : "Unavailable (VM not running)"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">IP Address</span>
              <span>{ipAddress || "Unknown"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Username</span>
              <span>{username}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Port</span>
              <span>{port}</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col gap-2">
          <Button
            onClick={handleSSHClick}
            disabled={status !== "running" || !ipAddress}
            className="w-full"
          >
            <Icon name={sshIcons.terminal} size="sm" className="mr-2" />
            Connect via SSH
          </Button>

          <SSHAIDialog
            defaultHost={ipAddress}
            defaultPort={port}
            defaultUsername={username}
            vmId={vmId}
            vmName={vmName}
            serverId={serverId}
            osType={osType}
          >
            <Button
              variant="outline"
              disabled={status !== "running" || !ipAddress}
              className="w-full"
            >
              <Icon name="bot" size="sm" className="mr-2" />
              SSH with AI Assistant
            </Button>
          </SSHAIDialog>
        </CardFooter>
      </Card>

      <SSHDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        defaultHost={ipAddress}
        defaultPort={port}
        defaultUsername={username}
        vmId={vmId}
        vmName={vmName}
      />
    </>
  )
}
