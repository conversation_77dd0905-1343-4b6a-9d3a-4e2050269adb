"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { useResourceUsage } from "@/hooks/use-resource-usage"

interface ResourceUsageChartsProps {
  serverId: string
  vmId: string
}

export function ResourceUsageCharts({ serverId, vmId }: ResourceUsageChartsProps) {
  const { history, isLoading, error } = useResourceUsage(serverId, "vm", vmId)

  if (isLoading && history.cpu.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Resource Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error && history.cpu.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Resource Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive">
            {error instanceof Error ? error.message : "Failed to load resource usage data"}
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Resource Usage</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="cpu">
          <TabsList className="mb-4">
            <TabsTrigger value="cpu">CPU</TabsTrigger>
            <TabsTrigger value="memory">Memory</TabsTrigger>
            <TabsTrigger value="disk">Disk I/O</TabsTrigger>
            <TabsTrigger value="network">Network</TabsTrigger>
          </TabsList>

          <TabsContent value="cpu" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={history.cpu}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis unit="%" domain={[0, 100]} />
                <Tooltip formatter={(value) => [`${value}%`, "CPU Usage"]} />
                <Line type="monotone" dataKey="value" stroke="#3b82f6" strokeWidth={2} name="CPU Usage" />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="memory" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={history.memory}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis unit="%" domain={[0, 100]} />
                <Tooltip formatter={(value) => [`${value.toFixed(1)}%`, "Memory Usage"]} />
                <Line type="monotone" dataKey="value" stroke="#10b981" strokeWidth={2} name="Memory Usage" />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="disk" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={history.diskRead}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="value" stroke="#f59e0b" strokeWidth={2} name="Disk Read" />
                <Line
                  type="monotone"
                  data={history.diskWrite}
                  dataKey="value"
                  stroke="#ef4444"
                  strokeWidth={2}
                  name="Disk Write"
                />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="network" className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={history.netIn}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="value" stroke="#8b5cf6" strokeWidth={2} name="Network In" />
                <Line
                  type="monotone"
                  data={history.netOut}
                  dataKey="value"
                  stroke="#ec4899"
                  strokeWidth={2}
                  name="Network Out"
                />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
