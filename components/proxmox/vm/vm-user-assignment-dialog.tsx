"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useAvailableUsersForVM } from "@/hooks/use-available-users"
import { useVMUserAssignment, useAssignUserToVM, useUpdateVMUserAssignment } from "@/hooks/use-vm-users"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Combobox } from "@/components/ui/combobox"
import { Icon } from "@/components/ui/icon"
import { Skeleton } from "@/components/ui/skeleton"

// Form schema
const formSchema = z.object({
  userId: z.string().uuid("Please select a user"),
  role: z.enum(["ADMIN", "OPERATOR", "VIEWER"], {
    required_error: "Please select a role",
  }),
})

type FormValues = z.infer<typeof formSchema>

interface VMUserAssignmentDialogProps {
  vmId: string
  userId?: string
  open: boolean
  onOpenChange: (open: boolean) => void
  mode?: "add" | "edit"
}

export function VMUserAssignmentDialog({
  vmId,
  userId,
  open,
  onOpenChange,
  mode = "add",
}: VMUserAssignmentDialogProps) {
  const { data: availableUsers, isLoading: isLoadingUsers } = useAvailableUsersForVM(vmId)
  const { data: existingAssignment, isLoading: isLoadingAssignment } = useVMUserAssignment(
    vmId,
    userId || ""
  )
  const assignUser = useAssignUserToVM(vmId)
  const updateAssignment = useUpdateVMUserAssignment(vmId, userId || "")

  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      userId: userId || "",
      role: "VIEWER",
    },
  })

  // Update form values when editing an existing assignment
  useEffect(() => {
    if (mode === "edit" && existingAssignment) {
      form.reset({
        userId: existingAssignment.userId,
        role: existingAssignment.role,
      })
    }
  }, [existingAssignment, form, mode])

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)
    try {
      if (mode === "add") {
        await assignUser.mutateAsync(values)
      } else {
        await updateAssignment.mutateAsync({ role: values.role })
      }
      onOpenChange(false)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format user options for the combobox
  const userOptions = availableUsers
    ? availableUsers.map((user) => ({
        label: user.name || user.email,
        value: user.id,
        description: user.name ? user.email : undefined,
      }))
    : []

  // Loading state
  const isLoading = (mode === "add" && isLoadingUsers) || (mode === "edit" && isLoadingAssignment)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{mode === "add" ? "Add User to VM" : "Edit User Role"}</DialogTitle>
          <DialogDescription>
            {mode === "add"
              ? "Assign a user to this VM with specific permissions."
              : "Update the user's role and permissions for this VM."}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-4 py-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
              {mode === "add" && (
                <FormField
                  control={form.control}
                  name="userId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>User</FormLabel>
                      <FormControl>
                        <Combobox
                          options={userOptions}
                          placeholder="Select a user"
                          {...field}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription>
                        Select a user to assign to this VM.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ADMIN">
                          <div className="flex items-center gap-2">
                            <Icon name="mdi:shield" size="sm" className="text-primary" />
                            <div>
                              <span>Admin</span>
                              <p className="text-xs text-muted-foreground">
                                Full control over the VM
                              </p>
                            </div>
                          </div>
                        </SelectItem>
                        <SelectItem value="OPERATOR">
                          <div className="flex items-center gap-2">
                            <Icon name="mdi:account-wrench" size="sm" className="text-primary" />
                            <div>
                              <span>Operator</span>
                              <p className="text-xs text-muted-foreground">
                                Can start, stop, and restart the VM
                              </p>
                            </div>
                          </div>
                        </SelectItem>
                        <SelectItem value="VIEWER">
                          <div className="flex items-center gap-2">
                            <Icon name="mdi:account-eye" size="sm" className="text-primary" />
                            <div>
                              <span>Viewer</span>
                              <p className="text-xs text-muted-foreground">
                                Read-only access to the VM
                              </p>
                            </div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The role determines what actions the user can perform.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Icon name="mdi:loading" size="sm" className="mr-2 animate-spin" />}
                  {mode === "add" ? "Add User" : "Update Role"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  )
}
