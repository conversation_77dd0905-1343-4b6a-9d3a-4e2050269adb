import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Icon } from "@/components/ui/icon"
import { getStatusIcon } from "@/lib/get-icon"

interface VMStatusBadgeProps {
  status: string
  className?: string
}

export function VMStatusBadge({ status, className }: VMStatusBadgeProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "running":
        return "bg-green-100 text-green-800"
      case "stopped":
      case "stop":
        return "bg-red-100 text-red-800"
      case "paused":
        return "bg-amber-100 text-amber-800"
      case "suspended":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case "running":
        return "Running"
      case "stopped":
      case "stop":
        return "Stopped"
      case "paused":
        return "Paused"
      case "suspended":
        return "Suspended"
      default:
        return status.charAt(0).toUpperCase() + status.slice(1)
    }
  }

  return (
    <Badge
      variant="outline"
      className={cn(getStatusColor(status), className)}
    >
      <Icon
        name={getStatusIcon(status)}
        size="xs"
        className="mr-1"
      />
      {getStatusText(status)}
    </Badge>
  )
}