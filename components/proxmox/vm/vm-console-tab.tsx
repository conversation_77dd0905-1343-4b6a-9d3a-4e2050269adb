"use client"

import { VMVNC } from "@/components/vm/vm-vnc"
import { VMSSH } from "@/components/vm/vm-ssh"
import { useVirtualMachine } from "@/hooks/use-virtual-machines"

interface VMConsoleTabProps {
  serverId: string
  vmId: string
}

export function VMConsoleTab({ serverId, vmId }: VMConsoleTabProps) {
  const { vm, isLoading } = useVirtualMachine(serverId, vmId)
  
  // Get VM status and IP address
  const status = vm?.status || "stopped"
  const ipAddress = vm?.ipAddress || ""
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <VMVNC 
        serverId={serverId} 
        vmId={vmId} 
        vmName={vm?.name || `VM ${vmId}`}
        status={status} 
      />
      <VMSSH 
        vmId={vmId}
        vmName={vm?.name || `VM ${vmId}`}
        ipAddress={ipAddress}
        status={status}
        serverId={serverId}
      />
    </div>
  )
}
