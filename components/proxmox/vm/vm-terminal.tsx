"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Icon } from "@/components/ui/icon"
import { toast } from "@/hooks/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Loader2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useQueryClient } from "@tanstack/react-query"

// Import xterm.js and addons
import { Terminal } from "@xterm/xterm"
import { FitAddon } from "@xterm/addon-fit"
import { WebLinksAddon } from "@xterm/addon-web-links"
import { SearchAddon } from "@xterm/addon-search"

// Import xterm.js styles
import "@xterm/xterm/css/xterm.css"

// Constants for retry logic
const MAX_RETRY_ATTEMPTS = 3
const RETRY_DELAY_MS = 2000

interface VMTerminalProps {
  serverId: string
  vmId: string
  vmName?: string
  status: string
  ipAddress?: string
}

export function VMTerminal({ serverId, vmId, vmName = "VM", status, ipAddress }: VMTerminalProps) {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [username, setUsername] = useState("root")
  const [password, setPassword] = useState("")
  const [port, setPort] = useState("22")
  const [host, setHost] = useState(ipAddress || "")
  const [retryCount, setRetryCount] = useState(0)
  const [useProxmoxProxy, setUseProxmoxProxy] = useState(true)

  const terminalRef = useRef<HTMLDivElement>(null)
  const terminalInstance = useRef<Terminal | null>(null)
  const fitAddon = useRef<FitAddon | null>(null)
  const searchAddon = useRef<SearchAddon | null>(null)
  const socketRef = useRef<WebSocket | null>(null)

  const queryClient = useQueryClient()

  // Initialize terminal when dialog opens
  useEffect(() => {
    if (dialogOpen && terminalRef.current && !terminalInstance.current) {
      // Create terminal instance
      const terminal = new Terminal({
        cursorBlink: true,
        fontSize: 14,
        fontFamily: "Menlo, Monaco, 'Courier New', monospace",
        theme: {
          background: '#1a1b26',
          foreground: '#c0caf5',
          cursor: '#c0caf5',
          selectionBackground: '#28344a',
          black: '#414868',
          red: '#f7768e',
          green: '#9ece6a',
          yellow: '#e0af68',
          blue: '#7aa2f7',
          magenta: '#bb9af7',
          cyan: '#7dcfff',
          white: '#c0caf5',
          brightBlack: '#414868',
          brightRed: '#f7768e',
          brightGreen: '#9ece6a',
          brightYellow: '#e0af68',
          brightBlue: '#7aa2f7',
          brightMagenta: '#bb9af7',
          brightCyan: '#7dcfff',
          brightWhite: '#c0caf5'
        }
      })

      // Create addons
      const fit = new FitAddon()
      const webLinks = new WebLinksAddon()
      const search = new SearchAddon()

      // Store refs
      fitAddon.current = fit
      searchAddon.current = search

      // Add addons
      terminal.loadAddon(fit)
      terminal.loadAddon(webLinks)
      terminal.loadAddon(search)

      // Open terminal
      terminal.open(terminalRef.current)

      // Fit terminal to container
      setTimeout(() => {
        if (fit) fit.fit()
      }, 100)

      // Store terminal instance
      terminalInstance.current = terminal

      // Initial welcome message
      terminal.writeln("Welcome to SSH Terminal")
      terminal.writeln(`Connect to ${vmName} (${vmId}) via SSH`)
      terminal.writeln("")
      terminal.writeln("Enter connection details and click Connect to start")
      terminal.writeln("")

      // Handle window resize
      const handleResize = () => {
        if (fitAddon.current) {
          try {
            fitAddon.current.fit()
          } catch (e) {
            console.warn("Error fitting terminal on resize:", e)
          }
        }
      }

      window.addEventListener('resize', handleResize)

      // Clean up on unmount
      return () => {
        window.removeEventListener('resize', handleResize)
      }
    }

    // Clean up terminal when dialog closes
    if (!dialogOpen && terminalInstance.current) {
      if (socketRef.current) {
        socketRef.current.close()
        socketRef.current = null
      }

      try {
        terminalInstance.current.dispose()
      } catch (e) {
        console.warn("Error disposing terminal:", e)
      }

      terminalInstance.current = null
      fitAddon.current = null
      searchAddon.current = null
      setIsConnected(false)
      setIsConnecting(false)
      setError(null)
      setRetryCount(0)
    }
  }, [dialogOpen, vmName, vmId])

  // Get node information for the VM
  const getNodeForVM = useCallback(async (): Promise<string> => {
    try {
      if (!serverId) {
        throw new Error("Server ID is required")
      }

      // Get node information from the API
      const response = await fetch(`/api/proxmox/vms/${vmId}?serverId=${serverId}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to get VM information")
      }

      const vmData = await response.json()

      if (!vmData.node) {
        throw new Error("Could not determine node for VM")
      }

      return vmData.node
    } catch (error) {
      console.error("Error getting node for VM:", error)
      throw error
    }
  }, [serverId, vmId])

  // Connect to SSH via Proxmox WebSocket
  const connectViaProxmox = useCallback(async (retry = 0) => {
    if (!terminalInstance.current) return

    try {
      setIsConnecting(true)
      setError(null)

      // Clear terminal
      terminalInstance.current.clear()
      terminalInstance.current.writeln("Connecting to SSH via Proxmox...")

      // Get the node for this VM
      const node = await getNodeForVM()

      terminalInstance.current.writeln(`Found VM on node: ${node}`)
      terminalInstance.current.writeln(`Establishing connection to ${host}:${port} as ${username}...`)

      // Create WebSocket connection to Proxmox terminal
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:"
      const wsUrl = `${protocol}//${window.location.host}/api/proxmox/terminal?serverId=${serverId}&node=${node}&vmId=${vmId}&type=ssh&host=${host}&port=${port}&username=${username}`

      // Close existing connection
      if (socketRef.current) {
        socketRef.current.close()
      }

      // Create new connection
      const socket = new WebSocket(wsUrl)
      socketRef.current = socket

      socket.onopen = () => {
        if (terminalInstance.current) {
          terminalInstance.current.writeln("WebSocket connection established")

          // Send password if provided
          if (password) {
            socket.send(JSON.stringify({ type: 'auth', password }))
          }
        }
      }

      socket.onmessage = (event) => {
        if (terminalInstance.current) {
          try {
            const data = JSON.parse(event.data)

            if (data.type === 'data') {
              terminalInstance.current.write(data.data)
            } else if (data.type === 'connected') {
              setIsConnected(true)
              setIsConnecting(false)
              setRetryCount(0)

              toast({
                title: "Connected",
                description: `Connected to ${vmName} via SSH`,
              })

              // Invalidate VM status query to ensure we have fresh data
              queryClient.invalidateQueries({ queryKey: ["vm", vmId] })
            } else if (data.type === 'error') {
              setError(data.message)
              terminalInstance.current.writeln(`\r\nError: ${data.message}`)

              toast({
                title: "SSH Error",
                description: data.message,
                variant: "destructive",
              })

              // If authentication error, show password prompt again
              if (data.message.includes("Authentication") || data.message.includes("auth")) {
                terminalInstance.current.writeln("\r\nAuthentication failed. Please check your credentials.")
              }
            }
          } catch (e) {
            // If not JSON, treat as raw data
            terminalInstance.current.write(event.data)
          }
        }
      }

      socket.onclose = (event) => {
        if (terminalInstance.current) {
          terminalInstance.current.writeln("\r\nConnection closed")

          if (!event.wasClean) {
            terminalInstance.current.writeln("Connection terminated unexpectedly")
          }
        }

        setIsConnected(false)
        socketRef.current = null
      }

      socket.onerror = (error) => {
        console.error("WebSocket error:", error)

        // Implement retry logic
        if (retry < MAX_RETRY_ATTEMPTS - 1) {
          if (terminalInstance.current) {
            terminalInstance.current.writeln(`\r\nConnection attempt ${retry + 1} failed. Retrying...`)
          }

          setRetryCount(retry + 1)

          // Wait before retrying
          setTimeout(() => {
            connectViaProxmox(retry + 1)
          }, RETRY_DELAY_MS)
          return
        }

        // All retries failed
        setError("Failed to connect to SSH server")
        setIsConnected(false)
        setIsConnecting(false)

        toast({
          title: "Connection Error",
          description: "Failed to connect to SSH server",
          variant: "destructive",
        })
      }

      // Set up terminal input
      terminalInstance.current.onData((data) => {
        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
          socketRef.current.send(JSON.stringify({ type: 'data', data }))
        }
      })

    } catch (error) {
      console.error("Error connecting to SSH:", error)
      setIsConnecting(false)
      setIsConnected(false)
      setError(error instanceof Error ? error.message : "Failed to connect to SSH")

      toast({
        title: "Connection Error",
        description: error instanceof Error ? error.message : "Failed to connect to SSH",
        variant: "destructive",
      })
    }
  }, [serverId, vmId, host, port, username, password, vmName, getNodeForVM, queryClient])

  // Connect to SSH directly (fallback method)
  const connectDirectSSH = useCallback(async (retry = 0) => {
    if (!terminalInstance.current) return

    try {
      setIsConnecting(true)
      setError(null)

      // Clear terminal
      terminalInstance.current.clear()
      terminalInstance.current.writeln("Connecting directly to SSH...")

      // Create WebSocket connection
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:"
      const wsUrl = `${protocol}//${window.location.host}/api/ssh/connect?serverId=${serverId}&vmId=${vmId}&host=${host}&port=${port}&username=${username}`

      // Close existing connection
      if (socketRef.current) {
        socketRef.current.close()
      }

      // Create new connection
      const socket = new WebSocket(wsUrl)
      socketRef.current = socket

      socket.onopen = () => {
        if (terminalInstance.current) {
          terminalInstance.current.writeln("Connected to SSH server")

          // Send password if provided
          if (password) {
            socket.send(JSON.stringify({ type: 'auth', method: 'password', password }))
          }

          setIsConnected(true)
          setIsConnecting(false)
          setRetryCount(0)

          toast({
            title: "Connected",
            description: `Connected to ${vmName} via SSH`,
          })

          // Invalidate VM status query to ensure we have fresh data
          queryClient.invalidateQueries({ queryKey: ["vm", vmId] })
        }
      }

      socket.onmessage = (event) => {
        if (terminalInstance.current) {
          try {
            const data = JSON.parse(event.data)

            if (data.type === 'data') {
              terminalInstance.current.write(data.data)
            } else if (data.type === 'error') {
              setError(data.message)
              terminalInstance.current.writeln(`\r\nError: ${data.message}`)

              toast({
                title: "SSH Error",
                description: data.message,
                variant: "destructive",
              })
            }
          } catch (e) {
            // If not JSON, treat as raw data
            terminalInstance.current.write(event.data)
          }
        }
      }

      socket.onclose = () => {
        if (terminalInstance.current) {
          terminalInstance.current.writeln("\r\nConnection closed")
        }
        setIsConnected(false)
        socketRef.current = null
      }

      socket.onerror = (error) => {
        console.error("WebSocket error:", error)

        // Implement retry logic
        if (retry < MAX_RETRY_ATTEMPTS - 1) {
          if (terminalInstance.current) {
            terminalInstance.current.writeln(`\r\nConnection attempt ${retry + 1} failed. Retrying...`)
          }

          setRetryCount(retry + 1)

          // Wait before retrying
          setTimeout(() => {
            connectDirectSSH(retry + 1)
          }, RETRY_DELAY_MS)
          return
        }

        // All retries failed
        setError("Failed to connect to SSH server")
        setIsConnected(false)
        setIsConnecting(false)

        toast({
          title: "Connection Error",
          description: "Failed to connect to SSH server",
          variant: "destructive",
        })
      }

      // Set up terminal input
      terminalInstance.current.onData((data) => {
        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
          socketRef.current.send(JSON.stringify({ type: 'data', data }))
        }
      })

    } catch (error) {
      console.error("Error connecting to SSH:", error)
      setIsConnecting(false)
      setIsConnected(false)
      setError(error instanceof Error ? error.message : "Failed to connect to SSH")

      toast({
        title: "Connection Error",
        description: error instanceof Error ? error.message : "Failed to connect to SSH",
        variant: "destructive",
      })
    }
  }, [serverId, vmId, host, port, username, password, vmName, queryClient])

  // Connect to SSH using the selected method
  const connectToSSH = useCallback(() => {
    if (useProxmoxProxy) {
      connectViaProxmox(0)
    } else {
      connectDirectSSH(0)
    }
  }, [useProxmoxProxy, connectViaProxmox, connectDirectSSH])

  // Function to retry connection
  const retryConnection = useCallback(() => {
    if (useProxmoxProxy) {
      connectViaProxmox(0)
    } else {
      connectDirectSSH(0)
    }
  }, [useProxmoxProxy, connectViaProxmox, connectDirectSSH])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Icon name="terminal" className="mr-2" />
          SSH Terminal
        </CardTitle>
        <CardDescription>
          Access the VM via secure shell
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-sm space-y-2">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Status</span>
            <span className={status === "running" ? "text-green-500" : "text-yellow-500"}>
              {status === "running" ? "Available" : "VM not running"}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Connection Type</span>
            <span>SSH over WebSocket (Encrypted)</span>
          </div>
          {ipAddress && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">IP Address</span>
              <span>{ipAddress}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button
              className="w-full"
              disabled={status !== "running"}
            >
              <Icon name="terminal" className="mr-2" />
              Open SSH Terminal
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px] max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>{vmName} Terminal</DialogTitle>
              <DialogDescription>
                SSH connection to {vmName} ({vmId})
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="host">Host</Label>
                  <Input
                    id="host"
                    value={host}
                    onChange={(e) => setHost(e.target.value)}
                    placeholder="IP Address"
                    disabled={isConnected}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="port">Port</Label>
                  <Input
                    id="port"
                    value={port}
                    onChange={(e) => setPort(e.target.value)}
                    placeholder="22"
                    disabled={isConnected}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="root"
                    disabled={isConnected}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Password"
                    disabled={isConnected}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="use-proxmox"
                  checked={useProxmoxProxy}
                  onChange={(e) => setUseProxmoxProxy(e.target.checked)}
                  className="h-4 w-4"
                  disabled={isConnected}
                />
                <Label htmlFor="use-proxmox">Use Proxmox SSH proxy (recommended)</Label>
                <span
                  className="inline-flex cursor-help"
                  title="Using the Proxmox proxy provides better security and reliability"
                >
                  <Icon
                    name="info"
                    className="h-4 w-4 text-muted-foreground"
                  />
                </span>
              </div>

              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => {
                    if (socketRef.current) {
                      socketRef.current.close()
                      socketRef.current = null
                    }
                    setIsConnected(false)
                    setIsConnecting(false)
                    setError(null)
                  }}
                  disabled={!isConnected}
                >
                  <Icon name="power" className="mr-2" />
                  Disconnect
                </Button>

                <Button
                  onClick={connectToSSH}
                  disabled={isConnecting || !host}
                >
                  {isConnecting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Connecting...
                    </>
                  ) : isConnected ? (
                    "Reconnect"
                  ) : (
                    <>
                      <Icon name="terminal" className="mr-2" />
                      Connect
                    </>
                  )}
                </Button>
              </div>

              {error && (
                <div className="p-3 text-sm bg-destructive/10 border border-destructive/20 rounded-md text-destructive">
                  <div className="font-semibold flex items-center">
                    <Icon name="alert-triangle" className="mr-2" />
                    Connection Error
                  </div>
                  <p>{error}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={retryConnection}
                  >
                    <Icon name="refresh-cw" className="mr-2 h-3 w-3" />
                    Retry Connection
                  </Button>
                </div>
              )}
            </div>

            <div className="relative min-h-[400px] border rounded-md overflow-hidden bg-[#1a1b26]">
              {retryCount > 0 && isConnecting && (
                <div className="absolute top-2 right-2 bg-background/80 text-xs px-2 py-1 rounded-md z-10">
                  Attempt {retryCount + 1}/{MAX_RETRY_ATTEMPTS}
                </div>
              )}
              <div
                ref={terminalRef}
                className="w-full h-[400px]"
              />
            </div>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  )
}
