"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Loader2, Download } from "lucide-react"
import { useServerContext } from "@/providers/server-provider"
import { useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import { getProxmoxClient } from "@/lib/api-client"
import { ImageMarketplace } from "@/components/vm/image-marketplace"

const formSchema = z.object({
  name: z.string().min(1, "VM name is required"),
  node: z.string().min(1, "Node is required"),
  osType: z.string().min(1, "OS type is required"),
  cores: z.number().min(1).max(128),
  memory: z.number().min(512).max(1048576),
  storage: z.string().min(1, "Storage is required"),
  diskSize: z.number().min(1).max(10000),
  networkBridge: z.string().min(1, "Network bridge is required"),
  startAfterCreation: z.boolean().default(false),
  useMarketplaceImage: z.boolean().default(false),
  marketplaceImage: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

export function CreateVirtualMachineForm() {
  const router = useRouter()
  const queryClient = useQueryClient()
  const { selectedServer } = useServerContext()
  const [isLoading, setIsLoading] = useState(false)
  const [nodes, setNodes] = useState<{ node: string; status: string }[]>([])
  const [storages, setStorages] = useState<{ storage: string; type: string }[]>([])
  const [bridges, setBridges] = useState<{ name: string; active: boolean }[]>([])
  const [activeTab, setActiveTab] = useState("basic")
  const [useMarketplaceImage, setUseMarketplaceImage] = useState(false)

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      node: "",
      osType: "linux",
      cores: 2,
      memory: 2048,
      storage: "",
      diskSize: 32,
      networkBridge: "vmbr0",
      startAfterCreation: true,
      useMarketplaceImage: false,
      marketplaceImage: "",
    },
  })

  // Fetch nodes, storages, and bridges when the component mounts
  useEffect(() => {
    if (!selectedServer) return

    const fetchData = async () => {
      try {
        const client = getProxmoxClient(selectedServer.id)

        // Fetch nodes
        const nodesResponse = await client.getServerInfo()
        setNodes(
          nodesResponse.data.map((node: any) => ({
            node: node.node,
            status: node.status,
          })),
        )

        // If we have nodes, fetch storages for the first node
        if (nodesResponse.data.length > 0) {
          const firstNode = nodesResponse.data[0].node
          form.setValue("node", firstNode)

          // Fetch storages
          const storagesResponse = await client.get(`/nodes/${firstNode}/storage`)
          setStorages(
            storagesResponse.data.map((storage: any) => ({
              storage: storage.storage,
              type: storage.type,
            })),
          )

          // Set default storage if available
          if (storagesResponse.data.length > 0) {
            form.setValue("storage", storagesResponse.data[0].storage)
          }

          // Fetch network bridges
          const networksResponse = await client.get(`/nodes/${firstNode}/network`)
          const bridges = networksResponse.data.filter((net: any) => net.type === "bridge")
          setBridges(
            bridges.map((bridge: any) => ({
              name: bridge.iface,
              active: bridge.active,
            })),
          )
        }
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch server resources",
          variant: "destructive",
        })
      }
    }

    fetchData()
  }, [selectedServer, form])

  async function onSubmit(values: FormValues) {
    if (!selectedServer) {
      toast({
        title: "Error",
        description: "No server selected",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      let response;

      if (values.useMarketplaceImage && values.marketplaceImage) {
        // Create VM from marketplace image
        response = await fetch(`/api/proxmox/${selectedServer.id}/marketplace/create-vm`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            node: values.node,
            name: values.name,
            memory: values.memory,
            cores: values.cores,
            storage: values.storage,
            diskSize: values.diskSize,
            osType: values.osType,
            networkBridge: values.networkBridge,
            startAfterCreation: values.startAfterCreation,
            templateImage: values.marketplaceImage,
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to create VM from template")
        }

        await response.json()
      } else {
        // Create VM normally
        const client = getProxmoxClient(selectedServer.id)

        response = await client.post(`/nodes/${values.node}/qemu`, {
          vmid: 0, // Let Proxmox assign a VM ID
          name: values.name,
          ostype: values.osType,
          cores: values.cores,
          memory: values.memory,
          storage: values.storage,
          disksize: `${values.diskSize}G`,
          net0: `model=virtio,bridge=${values.networkBridge}`,
          start: values.startAfterCreation,
        })
      }

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["vms", selectedServer.id] })

      toast({
        title: "Success",
        description: "Virtual machine created successfully",
      })

      // Redirect to the VM list
      router.push(`/servers/${selectedServer.id}`)
    } catch (error) {
      console.error("Error creating VM:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create virtual machine",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create Virtual Machine</CardTitle>
        <CardDescription>Configure and create a new virtual machine on your Proxmox server</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="basic">Basic</TabsTrigger>
                <TabsTrigger value="system">System</TabsTrigger>
                <TabsTrigger value="disks">Disks</TabsTrigger>
                <TabsTrigger value="network">Network</TabsTrigger>
                <TabsTrigger value="marketplace" className="flex items-center">
                  <Download className="mr-2 h-4 w-4" />
                  Marketplace
                </TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>VM Name</FormLabel>
                      <FormControl>
                        <Input placeholder="my-vm" {...field} />
                      </FormControl>
                      <FormDescription>A unique name for your virtual machine</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="node"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Node</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a node" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {nodes.map((node) => (
                            <SelectItem key={node.node} value={node.node}>
                              {node.node} ({node.status})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>The Proxmox node where the VM will be created</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="osType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>OS Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an OS type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="linux">Linux</SelectItem>
                          <SelectItem value="windows">Windows</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>The type of operating system</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="system" className="space-y-4">
                <FormField
                  control={form.control}
                  name="cores"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CPU Cores: {field.value}</FormLabel>
                      <FormControl>
                        <Slider
                          min={1}
                          max={32}
                          step={1}
                          value={[field.value]}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormDescription>Number of CPU cores</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="memory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Memory: {field.value} MB ({(field.value / 1024).toFixed(1)} GB)
                      </FormLabel>
                      <FormControl>
                        <Slider
                          min={512}
                          max={65536}
                          step={512}
                          value={[field.value]}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormDescription>Amount of memory in MB</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="disks" className="space-y-4">
                <FormField
                  control={form.control}
                  name="storage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Storage</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select storage" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {storages.map((storage) => (
                            <SelectItem key={storage.storage} value={storage.storage}>
                              {storage.storage} ({storage.type})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>Storage for the VM disk</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="diskSize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Disk Size: {field.value} GB</FormLabel>
                      <FormControl>
                        <Slider
                          min={1}
                          max={1000}
                          step={1}
                          value={[field.value]}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormDescription>Size of the disk in GB</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="network" className="space-y-4">
                <FormField
                  control={form.control}
                  name="networkBridge"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Network Bridge</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select network bridge" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {bridges.map((bridge) => (
                            <SelectItem key={bridge.name} value={bridge.name}>
                              {bridge.name} ({bridge.active ? "Active" : "Inactive"})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>Network bridge for the VM</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="startAfterCreation"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Start after creation</FormLabel>
                        <FormDescription>Automatically start the VM after it has been created</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="marketplace" className="space-y-4">
                <div className="bg-muted/50 rounded-lg p-4 mb-4">
                  <h3 className="text-lg font-medium mb-2">Image Marketplace</h3>
                  <p className="text-sm text-muted-foreground">
                    Choose from a variety of pre-built images to quickly create your virtual machine.
                    These images come with pre-installed operating systems and are ready to use.
                  </p>
                </div>

                <FormField
                  control={form.control}
                  name="useMarketplaceImage"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 mb-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Use marketplace image</FormLabel>
                        <FormDescription>Create VM from a pre-built marketplace image</FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            field.onChange(checked)
                            setUseMarketplaceImage(checked)
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {useMarketplaceImage && form.watch("node") && form.watch("storage") && (
                  <div className="border rounded-lg p-4">
                    <ImageMarketplace
                      serverId={selectedServer?.id || ""}
                      node={form.watch("node")}
                      storage={form.watch("storage")}
                      onImageSelected={(imageName) => {
                        form.setValue("marketplaceImage", imageName)
                        toast({
                          title: "Image Selected",
                          description: `${imageName} has been selected for your VM`,
                        })
                      }}
                    />
                  </div>
                )}

                {!useMarketplaceImage && (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <p className="text-muted-foreground mb-4">
                      Enable "Use marketplace image" to browse and select from available images
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        form.setValue("useMarketplaceImage", true)
                        setUseMarketplaceImage(true)
                      }}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Browse Marketplace
                    </Button>
                  </div>
                )}
              </TabsContent>
            </Tabs>

            <CardFooter className="flex justify-between px-0">
              <Button type="button" variant="outline" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Virtual Machine"
                )}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
