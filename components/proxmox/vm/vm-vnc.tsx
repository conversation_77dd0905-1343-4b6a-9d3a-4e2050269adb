"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Icon } from "@/components/ui/icon"
import { toast } from "@/hooks/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Loader2 } from "lucide-react"
import { useQueryClient } from "@tanstack/react-query"

// Import React VNC component
import { VncScreen } from "react-vnc"
import type { VncScreenHandle } from "react-vnc"

interface VMVNCProps {
  serverId: string
  vmId: string
  vmName?: string
  status: string
}

// Constants for retry logic
const MAX_RETRY_ATTEMPTS = 3
const RETRY_DELAY_MS = 2000

export function VMVNC({ serverId, vmId, vmName = "VM", status }: VMVNCProps) {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [vncUrl, setVncUrl] = useState<string | null>(null)
  const [vncPassword, setVncPassword] = useState<string>("")
  const [directWebsocketUrl, setDirectWebsocketUrl] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [fullscreen, setFullscreen] = useState(false)

  const vncRef = useRef<VncScreenHandle>(null)
  const queryClient = useQueryClient()

  // Function to get VNC connection details with retry logic and performance optimizations
  const getVNCConnectionDetails = useCallback(async (retry = 0) => {
    try {
      setIsConnecting(true)
      setError(null)

      // Add a cache-busting parameter to avoid stale data
      const timestamp = Date.now()

      // Use AbortController for timeout handling
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

      try {
        // Try to get the node from the VM ID if it's in the format "node:vmid"
      let node = "";
      if (vmId.includes(':')) {
        const parts = vmId.split(':');
        node = parts[0];
      }

      const response = await fetch(
          `/api/proxmox/vnc?serverId=${serverId}&vmId=${vmId}${node ? `&node=${node}` : ''}&_t=${timestamp}`,
          {
            signal: controller.signal,
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache'
            }
          }
        )

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorData = await response.json()

          // Special handling for 501 errors with direct access information
          if (response.status === 501 && errorData.directAccess && errorData.websocketUrlTemplate) {
            console.log("Received 501 error with direct access information", errorData)

            // Try to construct a direct WebSocket URL from the template
            try {
              // The server should provide a template with placeholders for PORT and TICKET
              // We need to get these values from a separate API call to vncproxy
              const proxyResponse = await fetch(
                `/api/proxmox?path=nodes/${errorData.host ? encodeURIComponent(errorData.host) : 'pve'}/qemu/${vmId}/vncproxy&method=POST&params=${JSON.stringify({websocket: 1})}`,
                {
                  headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache'
                  }
                }
              )

              if (proxyResponse.ok) {
                const proxyData = await proxyResponse.json()
                if (proxyData.data && proxyData.data.port && proxyData.data.ticket) {
                  // Replace placeholders in the template
                  const directUrl = errorData.websocketUrlTemplate
                    .replace('PORT', proxyData.data.port)
                    .replace('TICKET', encodeURIComponent(proxyData.data.ticket))

                  console.log("Constructed direct WebSocket URL:", directUrl)
                  setDirectWebsocketUrl(directUrl)
                  setVncPassword(proxyData.data.password || "")
                  setRetryCount(0)
                  return { ...errorData, ...proxyData.data, websocketUrl: directUrl }
                }
              }
            } catch (proxyError) {
              console.error("Error getting proxy details:", proxyError)
            }
          }

          throw new Error(errorData.error || "Failed to get VNC connection details")
        }

        const connectionDetails = await response.json()

        // Validate the response data
        if (!connectionDetails.token || !connectionDetails.port) {
          throw new Error("Invalid VNC connection details received from server")
        }

        // Check if we have a direct WebSocket URL from the server
        if (connectionDetails.websocketUrl) {
          console.log("Using direct WebSocket URL from server:", connectionDetails.websocketUrl)
          setDirectWebsocketUrl(connectionDetails.websocketUrl)

          // Also set up the proxy URL as fallback
          const protocol = window.location.protocol === "https:" ? "wss:" : "ws:"
          const proxyUrl = `${protocol}//${window.location.host}/api/proxmox/vnc/proxy?token=${connectionDetails.token}`
          setVncUrl(proxyUrl)
        } else {
          // Fallback to proxy URL if no direct WebSocket URL
          const protocol = window.location.protocol === "https:" ? "wss:" : "ws:"
          const proxyUrl = `${protocol}//${window.location.host}/api/proxmox/vnc/proxy?token=${connectionDetails.token}`
          setVncUrl(proxyUrl)
        }

        setVncPassword(connectionDetails.password || "")
        setRetryCount(0) // Reset retry count on success

        return connectionDetails
      } catch (fetchError) {
        clearTimeout(timeoutId)
        throw fetchError
      }
    } catch (error) {
      // Handle AbortController timeout
      if (error instanceof Error && error.name === 'AbortError') {
        console.error(`VNC connection timed out (attempt ${retry + 1}/${MAX_RETRY_ATTEMPTS})`)
        throw new Error("Connection timed out. Please try again.")
      }

      console.error(`Error getting VNC connection details (attempt ${retry + 1}/${MAX_RETRY_ATTEMPTS}):`, error)

      // Implement retry logic with exponential backoff
      if (retry < MAX_RETRY_ATTEMPTS - 1) {
        setIsConnecting(true)
        setError(`Connection attempt ${retry + 1} failed. Retrying...`)

        // Wait before retrying with exponential backoff
        const backoffDelay = RETRY_DELAY_MS * Math.pow(1.5, retry)
        await new Promise(resolve => setTimeout(resolve, backoffDelay))
        setRetryCount(retry + 1)
        return getVNCConnectionDetails(retry + 1)
      }

      // All retries failed
      setIsConnecting(false)
      const errorMessage = error instanceof Error ? error.message : "Failed to get VNC connection details"
      setError(errorMessage)

      toast({
        title: "Connection Error",
        description: errorMessage,
        variant: "destructive",
      })

      throw error
    }
  }, [serverId, vmId])

  // Handle connection events
  const handleConnect = useCallback(() => {
    setIsConnected(true)
    setIsConnecting(false)
    toast({
      title: "Connected",
      description: `Connected to ${vmName} console`,
    })

    // Invalidate VM status query to ensure we have fresh data
    queryClient.invalidateQueries({ queryKey: ["vm", vmId] })
  }, [vmName, vmId, queryClient])

  const handleDisconnect = useCallback((event: any) => {
    setIsConnected(false)
    setIsConnecting(false)

    if (event?.detail?.clean) {
      toast({
        title: "Disconnected",
        description: "VNC connection closed",
      })
    } else {
      // Only show error if dialog is still open (avoid errors when user closes dialog)
      if (dialogOpen) {
        setError("Connection failed or was disconnected")
        toast({
          title: "Connection Error",
          description: "VNC connection failed or was disconnected",
          variant: "destructive",
        })
      }
    }
  }, [dialogOpen])

  const handleCredentialsRequired = useCallback(() => {
    // Send credentials when required
    if (vncRef.current && vncPassword) {
      vncRef.current.sendCredentials({ password: vncPassword })
    } else {
      setError("Authentication failed: No password available")
    }
  }, [vncPassword])

  // Handle security errors
  const handleSecurityFailure = useCallback((event: any) => {
    setError(`Security error: ${event?.detail?.message || 'Unknown security error'}`)
    setIsConnecting(false)

    toast({
      title: "Security Error",
      description: "Failed to establish a secure connection",
      variant: "destructive",
    })
  }, [])

  // Initialize VNC connection when dialog opens
  useEffect(() => {
    if (dialogOpen && !vncUrl && !directWebsocketUrl) {
      getVNCConnectionDetails()
    }

    // Add a connection status indicator
    if (dialogOpen && (vncUrl || directWebsocketUrl)) {
      // If we have a direct WebSocket URL, show a message
      if (directWebsocketUrl) {
        toast({
          title: "Using Direct Connection",
          description: "Connected directly to Proxmox VNC WebSocket",
          variant: "default",
        })
      }
    }

    // Disconnect when dialog closes
    if (!dialogOpen && vncRef.current) {
      vncRef.current.disconnect()
      setVncUrl(null)
      setDirectWebsocketUrl(null)
      setIsConnected(false)
      setIsConnecting(false)
      setFullscreen(false)
    }

    // Clean up on unmount
    return () => {
      if (vncRef.current) {
        vncRef.current.disconnect()
      }
    }
  }, [dialogOpen, vncUrl, directWebsocketUrl, getVNCConnectionDetails, toast])

  // Toggle fullscreen mode
  const toggleFullscreen = useCallback(() => {
    setFullscreen(prev => !prev)
  }, [])

  // Function to retry connection
  const retryConnection = () => {
    setVncUrl(null)
    setDirectWebsocketUrl(null)
    setRetryCount(0)
    getVNCConnectionDetails()
  }

  // Function to send Ctrl+Alt+Del
  const sendCtrlAltDel = () => {
    if (vncRef.current) {
      vncRef.current.sendCtrlAltDel()
      toast({
        title: "Sent Ctrl+Alt+Del",
        description: "Ctrl+Alt+Del sent to the VM",
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Icon name="monitor" className="mr-2" />
          VNC Console
        </CardTitle>
        <CardDescription>
          Access the VM graphical console via VNC
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-sm space-y-2">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Status</span>
            <span className={status === "running" ? "text-green-500" : "text-yellow-500"}>
              {status === "running" ? "Available" : "VM not running"}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Connection Type</span>
            <span>VNC over WebSocket (Encrypted)</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Performance</span>
            <span>Optimized for low latency</span>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button
              className="w-full"
              disabled={status !== "running"}
            >
              <Icon name="monitor" className="mr-2" />
              Open VNC Console
            </Button>
          </DialogTrigger>
          <DialogContent className={`sm:max-w-[800px] max-h-[90vh] ${fullscreen ? 'fixed inset-0 w-screen h-screen max-w-none max-h-none rounded-none p-0' : ''}`}>
            {!fullscreen && (
              <DialogHeader>
                <DialogTitle>{vmName} Console</DialogTitle>
                <DialogDescription>
                  VNC connection to {vmName} ({vmId})
                </DialogDescription>
              </DialogHeader>
            )}

            <div className={`relative ${fullscreen ? 'h-screen' : 'min-h-[400px]'} border rounded-md overflow-hidden`}>
              {isConnecting && !(directWebsocketUrl || vncUrl) && (
                <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
                  <div className="flex flex-col items-center gap-2">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <p>Connecting to console...</p>
                    {retryCount > 0 && (
                      <p className="text-sm text-muted-foreground">Attempt {retryCount + 1} of {MAX_RETRY_ATTEMPTS}</p>
                    )}
                  </div>
                </div>
              )}

              {error && !isConnecting && (
                <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
                  <div className="flex flex-col items-center gap-2 text-destructive text-center p-4">
                    <Icon name="alert-triangle" size="lg" />
                    <p>{error}</p>
                    <Button onClick={retryConnection} variant="outline" size="sm">
                      Retry Connection
                    </Button>
                  </div>
                </div>
              )}

              {(directWebsocketUrl || vncUrl) && (
                <VncScreen
                  ref={vncRef}
                  url={directWebsocketUrl || vncUrl || ''}
                  scaleViewport
                  // Enable debug mode to show connection info
                  debug={true}
                  resizeSession
                  clipViewport
                  background="#000000"
                  autoConnect
                  style={{
                    width: '100%',
                    height: fullscreen ? '100vh' : '400px',
                  }}
                  onConnect={handleConnect}
                  onDisconnect={handleDisconnect}
                  onCredentialsRequired={handleCredentialsRequired}
                  onSecurityFailure={handleSecurityFailure}
                  rfbOptions={{
                    credentials: {
                      password: vncPassword
                    },
                    wsProtocols: ['binary', 'base64'],
                    // Production-optimized settings
                    qualityLevel: process.env.NODE_ENV === 'production' ? 7 : 6, // Higher quality in production
                    compressionLevel: process.env.NODE_ENV === 'production' ? 1 : 2, // Less compression in production for better quality
                    showDotCursor: true,
                    enableWebRTC: true, // Enable WebRTC for better performance if available
                    // Performance optimizations for production
                    scaleViewport: true,
                    clipViewport: true
                  }}
                />
              )}

              {/* Connection controls */}
              {isConnected && (
                <div className="absolute top-2 right-2 z-20 flex gap-2">
                  {/* Connection type indicator */}
                  <div className="px-2 py-1 text-xs rounded bg-background/50 flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full ${directWebsocketUrl ? 'bg-green-500' : 'bg-blue-500'}`}></div>
                    <span>{directWebsocketUrl ? 'Direct' : 'Proxy'}</span>
                  </div>

                  {/* Fullscreen toggle button */}
                  <Button
                    variant="outline"
                    size="icon"
                    className="bg-background/50 hover:bg-background/80"
                    onClick={toggleFullscreen}
                  >
                    <Icon name={fullscreen ? "minimize" : "maximize"} size="sm" />
                  </Button>
                </div>
              )}
            </div>

            <div className="flex justify-between mt-4">
              <Button
                variant="outline"
                onClick={() => setDialogOpen(false)}
              >
                Close
              </Button>

              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={retryConnection}
                  disabled={isConnecting}
                >
                  <Icon name="refresh-cw" className="mr-2" />
                  Reconnect
                </Button>

                <Button
                  variant="default"
                  onClick={sendCtrlAltDel}
                  disabled={!isConnected}
                >
                  <Icon name="power" className="mr-2" />
                  Send Ctrl+Alt+Del
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  )
}
