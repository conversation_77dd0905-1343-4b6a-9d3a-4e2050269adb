"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useVirtualMachine } from "@/hooks/use-virtual-machines"

interface VmDetailsProps {
  serverId: string
  vmId: string
}

export function VirtualMachineDetails({ serverId, vmId }: VmDetailsProps) {
  const { vm, isLoading, error } = useVirtualMachine(serverId, vmId)

  function formatMemory(bytes: number) {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-8 w-[200px]" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-destructive">{error instanceof Error ? error.message : "Failed to load VM details"}</p>
        </CardContent>
      </Card>
    )
  }

  if (!vm) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">No VM information available</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">{vm.name || `VM ${vmId}`}</h2>
            <Badge variant={vm.status === "running" ? "success" : "secondary"}>{vm.status}</Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">VM ID</p>
              <p className="font-medium">{vmId}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Node</p>
              <p className="font-medium">{vm.node}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">CPUs</p>
              <p className="font-medium">{vm.cpus || 0}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Memory</p>
              <p className="font-medium">{formatMemory(vm.maxmem || 0)}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">OS Type</p>
              <p className="font-medium">{vm.ostype || "Unknown"}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
