"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Play, 
  Square, 
  RotateCw, 
  Trash2, 
  <PERSON>py, 
  Camera, 
  HardDrive,
  MoreHorizontal
} from "lucide-react"
import { VMStatus } from "@/lib/proxmox/types"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useToast } from "@/components/ui/use-toast"

interface VMActionsProps {
  vmId: string
  status: VMStatus
  node: string
  onAction: (action: string, vmId: string) => Promise<void>
  size?: "default" |"sm" | "md" | "lg" | "icon" | undefined
  variant?: "default" | "outline" | "ghost"
  showLabels?: boolean
  showTooltips?: boolean
  className?: string
}

export function VMActions({
  vmId,
  status,
  node,
  onAction,
  size = "sm",
  variant = "outline",
  showLabels = false,
  showTooltips = true,
  className
}: VMActionsProps) {
  const { toast } = useToast()
  const [loading, setLoading] = useState<string | null>(null)

  const handleAction = async (action: string) => {
    try {
      setLoading(action)
      await onAction(action, vmId)
      toast({
        title: "Success",
        description: `VM ${action} operation completed successfully`,
      })
    } catch (error) {
      console.error(`Error performing ${action} on VM ${vmId}:`, error)
      toast({
        title: "Error",
        description: `Failed to ${action} VM. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setLoading(null)
    }
  }

  const ActionButton = ({ 
    action, 
    icon, 
    label, 
    disabled = false,
    destructive = false
  }: { 
    action: string, 
    icon: React.ReactNode, 
    label: string, 
    disabled?: boolean,
    destructive?: boolean
  }) => {
    const isLoading = loading === action
    
    const button = (
      <Button
        size={size}
        variant={destructive ? "destructive" : variant}
        disabled={disabled || loading !== null}
        onClick={() => handleAction(action)}
        className={showLabels ? "gap-2" : ""}
      >
        {icon}
        {showLabels && <span>{label}</span>}
        {isLoading && <span className="ml-2 animate-spin">⟳</span>}
      </Button>
    )
    
    if (showTooltips && !showLabels) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {button}
            </TooltipTrigger>
            <TooltipContent>
              <p>{label}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }
    
    return button
  }

  return (
    <div className={`flex ${showLabels ? 'flex-col space-y-2' : 'flex-row space-x-2'} ${className}`}>
      {status !== VMStatus.RUNNING && (
        <ActionButton
          action="start"
          icon={<Play className="h-4 w-4" />}
          label="Start"
        />
      )}
      
      {status === VMStatus.RUNNING && (
        <>
          <ActionButton
            action="stop"
            icon={<Square className="h-4 w-4" />}
            label="Stop"
          />
          
          <ActionButton
            action="restart"
            icon={<RotateCw className="h-4 w-4" />}
            label="Restart"
          />
        </>
      )}
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button size={size} variant={variant}>
            <MoreHorizontal className="h-4 w-4" />
            {showLabels && <span>More</span>}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>VM Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            onClick={() => handleAction("snapshot")}
            disabled={loading !== null}
          >
            <Camera className="mr-2 h-4 w-4" />
            <span>Create Snapshot</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => handleAction("clone")}
            disabled={loading !== null}
          >
            <Copy className="mr-2 h-4 w-4" />
            <span>Clone VM</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => handleAction("template")}
            disabled={loading !== null || status !== VMStatus.STOPPED}
          >
            <HardDrive className="mr-2 h-4 w-4" />
            <span>Convert to Template</span>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            onClick={() => {
              if (window.confirm(`Are you sure you want to delete VM ${vmId}?`)) {
                handleAction("delete")
              }
            }}
            disabled={loading !== null}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            <span>Delete VM</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
