"use client"

import { TaskInfo, TaskStatus } from "@/lib/proxmox/types"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, User, AlertCircle, CheckCircle, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface TaskItemProps {
  task: TaskInfo
  className?: string
}

export function TaskItem({ task, className }: TaskItemProps) {
  // Format dates
  const startTime = new Date(task.startTime).toLocaleString()
  const endTime = task.endTime ? new Date(task.endTime).toLocaleString() : null
  
  // Calculate duration
  const duration = task.endTime 
    ? Math.floor((task.endTime.getTime() - task.startTime.getTime()) / 1000)
    : Math.floor((Date.now() - task.startTime.getTime()) / 1000)
  
  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`
  }
  
  // Status icon and color
  let StatusIcon = Loader2
  let statusColor = "text-muted-foreground"
  
  switch (task.status) {
    case TaskStatus.COMPLETED:
      StatusIcon = CheckCircle
      statusColor = "text-green-500"
      break
    case TaskStatus.FAILED:
      StatusIcon = AlertCircle
      statusColor = "text-destructive"
      break
    case TaskStatus.RUNNING:
      StatusIcon = Loader2
      statusColor = "text-blue-500"
      break
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-base">{task.description || task.type}</CardTitle>
          <Badge 
            variant={
              task.status === TaskStatus.COMPLETED 
                ? "default" 
                : task.status === TaskStatus.FAILED 
                  ? "destructive" 
                  : "outline"
            }
            className="flex items-center gap-1"
          >
            {task.status === TaskStatus.RUNNING && (
              <Loader2 className="h-3 w-3 animate-spin" />
            )}
            {task.status === TaskStatus.COMPLETED && (
              <CheckCircle className="h-3 w-3" />
            )}
            {task.status === TaskStatus.FAILED && (
              <AlertCircle className="h-3 w-3" />
            )}
            {task.status}
          </Badge>
        </div>
        <CardDescription>
          Node: {task.node} • Task ID: {task.id.split(':').pop()}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pb-3 pt-0">
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex items-center">
            <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
            <span>Started: {startTime}</span>
          </div>
          
          <div className="flex items-center">
            <User className="mr-2 h-4 w-4 text-muted-foreground" />
            <span>User: {task.user}</span>
          </div>
          
          {endTime && (
            <div className="flex items-center">
              <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>Ended: {endTime}</span>
            </div>
          )}
          
          <div className="flex items-center">
            <StatusIcon className={cn("mr-2 h-4 w-4", statusColor, task.status === TaskStatus.RUNNING && "animate-spin")} />
            <span>Duration: {formatDuration(duration)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
