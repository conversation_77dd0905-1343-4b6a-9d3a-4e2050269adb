"use client"

import { Badge } from "@/components/ui/badge"
import { VMStatus, NodeStatus } from "@/lib/proxmox/types"

interface StatusBadgeProps {
  status: VMStatus | NodeStatus | string
  showDot?: boolean
  size?: "sm" | "md" | "lg"
}

export function StatusBadge({ status, showDot = true, size = "md" }: StatusBadgeProps) {
  let variant: "default" | "secondary" | "destructive" | "outline" = "outline"
  let color = "bg-gray-500"
  let label = status

  // Determine variant and color based on status
  switch (status) {
    // VM statuses
    case VMStatus.RUNNING:
      variant = "default"
      color = "bg-green-500"
      break
    case VMStatus.STOPPED:
      variant = "destructive"
      color = "bg-red-500"
      break
    case VMStatus.PAUSED:
      variant = "secondary"
      color = "bg-yellow-500"
      break
    case VMStatus.SUSPENDED:
      variant = "secondary"
      color = "bg-blue-500"
      break
    
    // Node statuses
    case NodeStatus.ONLINE:
      variant = "default"
      color = "bg-green-500"
      break
    case NodeStatus.OFFLINE:
      variant = "destructive"
      color = "bg-red-500"
      break
    
    // Default for unknown statuses
    default:
      variant = "outline"
      color = "bg-gray-500"
  }

  // Size classes
  const sizeClasses = {
    sm: "text-xs px-1.5 py-0.5",
    md: "text-sm px-2 py-0.5",
    lg: "px-2.5 py-0.5"
  }

  return (
    <Badge variant={variant} className={sizeClasses[size]}>
      {showDot && (
        <span className={`mr-1.5 inline-block h-2 w-2 rounded-full ${color}`} />
      )}
      {label}
    </Badge>
  )
}
