"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { HardDrive, Server, Cpu, Memory } from "lucide-react"
import { VMInfo } from "@/lib/proxmox/types"
import { StatusBadge } from "./status-badge"
import { ResourceMeter } from "./resource-meter"
import { VMActions } from "./vm-actions"
import { cn } from "@/lib/utils"

interface VMCardProps {
  vm: VMInfo
  selected?: boolean
  onSelect?: (vmId: string) => void
  onAction: (action: string, vmId: string) => Promise<void>
  className?: string
}

export function VMCard({ vm, selected = false, onSelect, onAction, className }: VMCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  
  const handleClick = () => {
    if (onSelect) {
      onSelect(vm.id)
    }
  }

  return (
    <Card 
      className={cn(
        "transition-all duration-200 overflow-hidden",
        selected ? "border-primary ring-1 ring-primary" : "",
        onSelect ? "cursor-pointer hover:shadow-md" : "",
        isHovered ? "shadow-md" : "",
        className
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center">
            <HardDrive className="mr-2 h-5 w-5 text-muted-foreground" />
            <div>
              <CardTitle className="text-lg">{vm.name}</CardTitle>
              <CardDescription>ID: {vm.id}</CardDescription>
            </div>
          </div>
          <StatusBadge status={vm.status} />
        </div>
      </CardHeader>
      
      <CardContent className="pb-2">
        <div className="space-y-3">
          <div className="flex items-center text-sm">
            <Server className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="font-medium">Node:</span>
            <span className="ml-2">{vm.node}</span>
          </div>
          
          <div className="flex items-center text-sm">
            <Cpu className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="font-medium">CPU:</span>
            <span className="ml-2">{vm.config?.cpu?.cores || 1} cores</span>
          </div>
          
          <div className="flex items-center text-sm">
            <Memory className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="font-medium">Memory:</span>
            <span className="ml-2">{vm.config?.memory || 'N/A'} MB</span>
          </div>
          
          {vm.cpuUsage !== undefined && (
            <ResourceMeter
              label="CPU Usage"
              value={vm.cpuUsage}
              max={1}
              unit=""
              showPercentage={true}
              size="sm"
            />
          )}
          
          {vm.memoryUsage !== undefined && vm.config?.memory && (
            <ResourceMeter
              label="Memory Usage"
              value={vm.memoryUsage}
              max={vm.config.memory * 1024 * 1024}
              unit="MB"
              showPercentage={true}
              size="sm"
            />
          )}
          
          {vm.diskUsage !== undefined && (
            <ResourceMeter
              label="Disk Usage"
              value={vm.diskUsage}
              max={100 * 1024 * 1024 * 1024} // Assuming 100GB total disk
              unit="GB"
              showPercentage={true}
              size="sm"
            />
          )}
        </div>
      </CardContent>
      
      <CardFooter className="pt-2 flex justify-end">
        <VMActions
          vmId={vm.id}
          status={vm.status}
          node={vm.node}
          onAction={onAction}
          size="sm"
        />
      </CardFooter>
    </Card>
  )
}
