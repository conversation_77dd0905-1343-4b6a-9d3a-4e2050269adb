"use client"

import { useState } from "react"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  RotateCcw, 
  Trash2, 
  Camera, 
  Check, 
  Loader2,
  AlertCircle
} from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/components/ui/use-toast"

interface SnapshotInfo {
  name: string
  description?: string
  timestamp: Date
  vmstate: boolean
  current?: boolean
}

interface SnapshotListProps {
  snapshots: SnapshotInfo[]
  vmId: string
  node: string
  onRollback: (snapshot: string) => Promise<void>
  onDelete: (snapshot: string) => Promise<void>
  className?: string
}

export function SnapshotList({ 
  snapshots, 
  vmId, 
  node, 
  onRollback, 
  onDelete,
  className 
}: SnapshotListProps) {
  const { toast } = useToast()
  const [actionInProgress, setActionInProgress] = useState<{
    snapshot: string;
    action: 'rollback' | 'delete';
  } | null>(null)
  
  const handleRollback = async (snapshot: string) => {
    try {
      setActionInProgress({ snapshot, action: 'rollback' })
      await onRollback(snapshot)
      toast({
        title: "Rollback Successful",
        description: `VM has been rolled back to snapshot "${snapshot}"`,
      })
    } catch (error) {
      console.error(`Error rolling back to snapshot ${snapshot}:`, error)
      toast({
        title: "Rollback Failed",
        description: "Failed to roll back to snapshot. Please try again.",
        variant: "destructive",
      })
    } finally {
      setActionInProgress(null)
    }
  }
  
  const handleDelete = async (snapshot: string) => {
    try {
      setActionInProgress({ snapshot, action: 'delete' })
      await onDelete(snapshot)
      toast({
        title: "Snapshot Deleted",
        description: `Snapshot "${snapshot}" has been deleted`,
      })
    } catch (error) {
      console.error(`Error deleting snapshot ${snapshot}:`, error)
      toast({
        title: "Deletion Failed",
        description: "Failed to delete snapshot. Please try again.",
        variant: "destructive",
      })
    } finally {
      setActionInProgress(null)
    }
  }

  if (snapshots.length === 0) {
    return (
      <div className="text-center py-8 border rounded-md bg-muted/20">
        <Camera className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-2 text-lg font-medium">No Snapshots</h3>
        <p className="mt-1 text-sm text-muted-foreground">
          This VM doesn't have any snapshots yet.
        </p>
      </div>
    )
  }

  return (
    <div className={className}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>VM State</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {snapshots.map((snapshot) => (
            <TableRow key={snapshot.name}>
              <TableCell className="font-medium">
                <div className="flex items-center">
                  {snapshot.name}
                  {snapshot.current && (
                    <Badge variant="outline" className="ml-2 bg-primary/10">
                      <Check className="mr-1 h-3 w-3" />
                      Current
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell>
                {snapshot.timestamp.toLocaleString()}
              </TableCell>
              <TableCell>
                {snapshot.description || "-"}
              </TableCell>
              <TableCell>
                {snapshot.vmstate ? (
                  <Badge variant="outline" className="bg-blue-500/10 text-blue-500">
                    Included
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-muted text-muted-foreground">
                    Not Included
                  </Badge>
                )}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm"
                        disabled={
                          snapshot.current || 
                          (actionInProgress !== null && 
                            (actionInProgress.snapshot === snapshot.name || 
                             actionInProgress.action === 'rollback'))
                        }
                      >
                        {actionInProgress?.snapshot === snapshot.name && 
                         actionInProgress?.action === 'rollback' ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <RotateCcw className="h-4 w-4" />
                        )}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Roll Back to Snapshot</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to roll back to snapshot "{snapshot.name}"? 
                          This will revert the VM to the state it was in when the snapshot was taken.
                          Any changes made after the snapshot will be lost.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                          onClick={() => handleRollback(snapshot.name)}
                          className="bg-blue-500 hover:bg-blue-600"
                        >
                          Roll Back
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm"
                        disabled={
                          snapshot.current || 
                          (actionInProgress !== null && 
                            (actionInProgress.snapshot === snapshot.name || 
                             actionInProgress.action === 'delete'))
                        }
                      >
                        {actionInProgress?.snapshot === snapshot.name && 
                         actionInProgress?.action === 'delete' ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4 text-destructive" />
                        )}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Snapshot</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete snapshot "{snapshot.name}"?
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                          onClick={() => handleDelete(snapshot.name)}
                          className="bg-destructive hover:bg-destructive/90"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
