"use client"

import { cn } from "@/lib/utils"

interface ResourceMeterProps {
  label: string
  value: number
  max: number
  unit?: string
  showPercentage?: boolean
  colorThresholds?: {
    warning: number
    critical: number
  }
  size?: "sm" | "md" | "lg"
  className?: string
}

export function ResourceMeter({
  label,
  value,
  max,
  unit = "",
  showPercentage = true,
  colorThresholds = { warning: 70, critical: 90 },
  size = "md",
  className
}: ResourceMeterProps) {
  const percentage = max > 0 ? (value / max) * 100 : 0
  
  // Determine color based on percentage and thresholds
  let barColor = "bg-primary"
  if (percentage >= colorThresholds.critical) {
    barColor = "bg-destructive"
  } else if (percentage >= colorThresholds.warning) {
    barColor = "bg-warning"
  }
  
  // Format values
  const formattedValue = unit === "MB" 
    ? `${Math.round(value / 1024 / 1024)} MB` 
    : unit === "GB" 
      ? `${(value / 1024 / 1024 / 1024).toFixed(1)} GB`
      : `${value}${unit}`
  
  const formattedMax = unit === "MB" 
    ? `${Math.round(max / 1024 / 1024)} MB` 
    : unit === "GB" 
      ? `${(max / 1024 / 1024 / 1024).toFixed(1)} GB`
      : `${max}${unit}`
  
  // Size classes
  const sizeClasses = {
    sm: {
      text: "text-xs",
      height: "h-1.5",
      spacing: "space-y-1"
    },
    md: {
      text: "text-sm",
      height: "h-2",
      spacing: "space-y-1.5"
    },
    lg: {
      text: "text-base",
      height: "h-3",
      spacing: "space-y-2"
    }
  }

  return (
    <div className={cn(sizeClasses[size].spacing, className)}>
      <div className="flex justify-between items-center">
        <span className={cn("font-medium", sizeClasses[size].text)}>{label}</span>
        <span className={cn("text-muted-foreground", sizeClasses[size].text)}>
          {formattedValue} / {formattedMax}
          {showPercentage && ` (${percentage.toFixed(1)}%)`}
        </span>
      </div>
      <div className="w-full bg-secondary rounded-full overflow-hidden">
        <div 
          className={cn(barColor, sizeClasses[size].height, "rounded-full transition-all duration-300")} 
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  )
}
