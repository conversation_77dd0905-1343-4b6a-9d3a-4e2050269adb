"use client";

import * as React from "react";
import { useState, useEffect, useCallback } from "react";
import { cn } from "@/lib/utils";
import { FileTree, FileNode } from "@/components/file-explorer/file-tree";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { 
  File, 
  Save, 
  X, 
  Edit, 
  Copy, 
  Download,
  Trash2,
  Folder,
  FolderPlus,
  FilePlus,
  RefreshCw,
  Loader2
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface MicroVmFileExplorerProps {
  className?: string;
  vmId?: string;
  rootPath?: string;
  readOnly?: boolean;
}

export function MicroVmFileExplorer({
  className,
  vmId,
  rootPath = "/app",
  readOnly = false
}: MicroVmFileExplorerProps) {
  // State variables
  const [files, setFiles] = useState<FileNode[]>([]);
  const [selectedFilePath, setSelectedFilePath] = useState<string | null>(null);
  const [fileContent, setFileContent] = useState<string | null>(null);
  const [fileLanguage, setFileLanguage] = useState<string>("plaintext");
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isCreatingFile, setIsCreatingFile] = useState(false);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newItemName, setNewItemName] = useState("");
  const [newItemParentPath, setNewItemParentPath] = useState(rootPath);

  // Fetch file listing
  const fetchFiles = useCallback(async () => {
    if (!vmId) return;

    setIsLoading(true);
    try {
      const response = await fetch("/api/microvm/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "list",
          path: rootPath
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch files: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      // Transform the file list into a nested tree structure
      const fileTree = buildFileTree(data.contents || [], rootPath);
      setFiles(fileTree);
    } catch (error) {
      console.error("Error fetching files:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch files",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [vmId, rootPath]);

  // Transform flat file list into nested tree structure
  const buildFileTree = (files: any[], basePath: string): FileNode[] => {
    const root: FileNode = {
      id: 'root',
      name: basePath.split('/').pop() || 'Project',
      path: basePath,
      type: 'directory',
      children: []
    };

    const dirMap: Record<string, FileNode> = {
      [basePath]: root
    };

    // First pass: create all directories
    files.forEach(file => {
      if (file.type === 'directory') {
        const path = file.path.endsWith('/') ? file.path : `${file.path}/`;
        const name = path.split('/').filter(Boolean).pop() || '';
        
        dirMap[path] = {
          id: `dir-${path}`,
          name,
          path,
          type: 'directory',
          children: []
        };
      }
    });

    // Second pass: create all files and link everything
    files.forEach(file => {
      const parentPath = getParentPath(file.path);
      const parent = dirMap[parentPath] || root;

      if (file.type === 'file') {
        const name = file.path.split('/').filter(Boolean).pop() || '';
        parent.children = parent.children || [];
        parent.children.push({
          id: `file-${file.path}`,
          name,
          path: file.path,
          type: 'file'
        });
      } else if (file.type === 'directory') {
        const path = file.path.endsWith('/') ? file.path : `${file.path}/`;
        if (path !== basePath) {
          const dir = dirMap[path];
          const parentOfDir = dirMap[getParentPath(path)] || root;
          parentOfDir.children = parentOfDir.children || [];
          parentOfDir.children.push(dir);
        }
      }
    });

    // Sort directories first, then files alphabetically
    const sortDir = (dir: FileNode) => {
      if (dir.children) {
        dir.children.sort((a, b) => {
          if (a.type === 'directory' && b.type === 'file') return -1;
          if (a.type === 'file' && b.type === 'directory') return 1;
          return a.name.localeCompare(b.name);
        });
        
        dir.children.forEach(child => {
          if (child.type === 'directory') {
            sortDir(child);
          }
        });
      }
    };
    
    sortDir(root);
    return [root];
  };

  // Get parent path
  const getParentPath = (path: string): string => {
    // Remove trailing slash
    const normalizedPath = path.endsWith('/') ? path.slice(0, -1) : path;
    // Get directory name
    const parts = normalizedPath.split('/').filter(Boolean);
    parts.pop(); // Remove the last part
    return `/${parts.join('/')}/`;
  };

  // Fetch file content
  const fetchFileContent = useCallback(async (filePath: string) => {
    if (!vmId) return;

    setIsLoading(true);
    try {
      const response = await fetch("/api/microvm/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "read",
          path: filePath
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch file content: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      setFileContent(data.content || "");
      
      // Set language based on file extension
      const fileExt = filePath.split('.').pop()?.toLowerCase() || "";
      setFileLanguage(getLanguageFromExtension(fileExt));
      
    } catch (error) {
      console.error("Error fetching file content:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch file content",
        variant: "destructive"
      });
      setFileContent(null);
    } finally {
      setIsLoading(false);
    }
  }, [vmId]);

  // Map file extensions to languages for syntax highlighting
  const getLanguageFromExtension = (ext: string): string => {
    const langMap: Record<string, string> = {
      js: "javascript",
      jsx: "jsx",
      ts: "typescript",
      tsx: "tsx",
      html: "html",
      css: "css",
      json: "json",
      md: "markdown",
      py: "python",
      go: "go",
      rs: "rust",
      sh: "bash",
      c: "c",
      cpp: "cpp",
      java: "java",
      php: "php",
    };
    return langMap[ext] || "plaintext";
  };

  // Save file content
  const saveFileContent = async () => {
    if (!vmId || !selectedFilePath) return;

    setIsLoading(true);
    try {
      const response = await fetch("/api/microvm/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "write",
          path: selectedFilePath,
          content: editContent
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to save file: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      setFileContent(editContent);
      setIsEditing(false);
      
      toast({
        title: "Success",
        description: "File saved successfully",
      });
      
    } catch (error) {
      console.error("Error saving file:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save file",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Delete file or folder
  const deleteItem = async () => {
    if (!vmId || !selectedFilePath) return;

    setIsLoading(true);
    try {
      const response = await fetch("/api/microvm/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "delete",
          path: selectedFilePath
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to delete item: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      setSelectedFilePath(null);
      setFileContent(null);
      
      // Refresh file list
      setRefreshTrigger(prev => prev + 1);
      
      toast({
        title: "Success",
        description: "Item deleted successfully",
      });
      
    } catch (error) {
      console.error("Error deleting item:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete item",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create new file
  const createNewFile = async () => {
    if (!vmId || !newItemName) return;

    const filePath = `${newItemParentPath}/${newItemName}`.replace(/\/+/g, '/');
    
    setIsLoading(true);
    try {
      const response = await fetch("/api/microvm/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "write",
          path: filePath,
          content: ""
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to create file: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      // Reset state
      setNewItemName("");
      setIsCreatingFile(false);
      
      // Refresh file list and select new file
      setRefreshTrigger(prev => prev + 1);
      
      toast({
        title: "Success",
        description: "File created successfully",
      });
      
      // Select the new file after refresh
      setTimeout(() => {
        setSelectedFilePath(filePath);
        fetchFileContent(filePath);
      }, 500);
      
    } catch (error) {
      console.error("Error creating file:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create file",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create new folder
  const createNewFolder = async () => {
    if (!vmId || !newItemName) return;

    const folderPath = `${newItemParentPath}/${newItemName}`.replace(/\/+/g, '/');
    
    setIsLoading(true);
    try {
      // Use mkdir command to create directory
      const response = await fetch("/api/microvm/command", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          command: `mkdir -p "${folderPath}"`
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to create folder: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      // Reset state
      setNewItemName("");
      setIsCreatingFolder(false);
      
      // Refresh file list
      setRefreshTrigger(prev => prev + 1);
      
      toast({
        title: "Success",
        description: "Folder created successfully",
      });
      
    } catch (error) {
      console.error("Error creating folder:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create folder",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle file selection
  const handleFileSelect = (file: FileNode) => {
    if (file.type === 'file') {
      setSelectedFilePath(file.path);
      fetchFileContent(file.path);
    } else if (file.type === 'directory') {
      // Set as parent path for new items
      setNewItemParentPath(file.path);
    }
  };

  // Copy file content to clipboard
  const handleCopyClick = () => {
    if (fileContent !== null) {
      navigator.clipboard.writeText(fileContent);
      toast({
        title: "Copied",
        description: "File content copied to clipboard",
      });
    }
  };

  // Handle refresh button click
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Initialize file list and setup refresh on VM ID change
  useEffect(() => {
    if (vmId) {
      fetchFiles();
    }
  }, [vmId, fetchFiles, refreshTrigger]);

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="p-3 border-b flex-row items-center justify-between">
        <CardTitle className="text-sm font-medium">File Explorer</CardTitle>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7"
            onClick={() => {
              setIsCreatingFile(true);
              setIsCreatingFolder(false);
            }}
            disabled={isLoading || readOnly}
            title="New File"
          >
            <FilePlus className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7"
            onClick={() => {
              setIsCreatingFolder(true);
              setIsCreatingFile(false);
            }}
            disabled={isLoading || readOnly}
            title="New Folder"
          >
            <FolderPlus className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7"
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      {/* New Item Creation Form */}
      {(isCreatingFile || isCreatingFolder) && (
        <div className="p-2 border-b flex items-center gap-2">
          <Input
            value={newItemName}
            onChange={(e) => setNewItemName(e.target.value)}
            placeholder={isCreatingFile ? "File name" : "Folder name"}
            className="h-8 text-sm"
            autoFocus
          />
          <Button
            size="sm"
            variant="default"
            className="h-8"
            onClick={isCreatingFile ? createNewFile : createNewFolder}
            disabled={!newItemName.trim() || isLoading}
          >
            Create
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 px-2"
            onClick={() => {
              setIsCreatingFile(false);
              setIsCreatingFolder(false);
              setNewItemName("");
            }}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      <div className="flex flex-1 overflow-hidden">
        {/* File Tree */}
        <div className="w-1/3 border-r overflow-auto">
          {isLoading && files.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <FileTree
              files={files}
              onFileSelect={handleFileSelect}
              selectedFilePath={selectedFilePath || undefined}
              expandedByDefault={true}
              className="border-0 rounded-none p-0"
            />
          )}
        </div>
        
        {/* File Content */}
        <div className="flex-1 flex flex-col">
          {selectedFilePath && fileContent !== null ? (
            <>
              {/* File Header */}
              <div className="flex items-center justify-between p-2 border-b bg-muted/30">
                <div className="flex items-center">
                  <File className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="text-sm font-medium truncate">
                    {selectedFilePath.split('/').pop()}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  {!readOnly && (
                    <>
                      {isEditing ? (
                        <>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7"
                            onClick={saveFileContent}
                            disabled={isLoading}
                          >
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => setIsEditing(false)}
                            disabled={isLoading}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => {
                            setEditContent(fileContent);
                            setIsEditing(true);
                          }}
                          disabled={isLoading}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                    </>
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={handleCopyClick}
                    disabled={isLoading}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  {!readOnly && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 text-destructive hover:text-destructive"
                      onClick={deleteItem}
                      disabled={isLoading}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
              
              {/* File Content */}
              <div className="flex-1 overflow-hidden">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                  </div>
                ) : isEditing ? (
                  <textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="w-full h-full p-4 font-mono text-sm resize-none focus:outline-none bg-background"
                  />
                ) : (
                  <ScrollArea className="h-full">
                    <SyntaxHighlighter
                      language={fileLanguage}
                      style={vscDarkPlus}
                      showLineNumbers={true}
                      customStyle={{
                        margin: 0,
                        padding: '1rem',
                        background: 'transparent',
                        fontSize: '14px'
                      }}
                    >
                      {fileContent}
                    </SyntaxHighlighter>
                  </ScrollArea>
                )}
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
              <File className="h-16 w-16 mb-4 opacity-30" />
              <h3 className="text-lg font-medium mb-2">No file selected</h3>
              <p className="text-sm max-w-md text-center">
                Select a file from the explorer to view its content
              </p>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
} 