import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { toast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

interface MicroVm {
  id: string;
  name: string;
  status: string;
  createdAt: string;
}

interface MicroVmSelectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string | null;
  onSelectVM: (vmId: string) => void;
}

export function MicroVmSelectionDialog({
  open,
  onOpenChange,
  projectId,
  onSelectVM,
}: MicroVmSelectionDialogProps) {
  const [tab, setTab] = useState('select');
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [existingVMs, setExistingVMs] = useState<MicroVm[]>([]);
  const [selectedVM, setSelectedVM] = useState<string | null>(null);

  // Form state for creating a new VM
  const [vmName, setVmName] = useState('');
  const [cpuCount, setCpuCount] = useState(1);
  const [memoryMB, setMemoryMB] = useState(512);
  const [diskSizeGB, setDiskSizeGB] = useState(5);
  const [kernelPath, setKernelPath] = useState('/var/lib/firecracker/kernels/vmlinux.bin');
  const [rootfsPath, setRootfsPath] = useState('/var/lib/firecracker/rootfs/bionic.rootfs.ext4');

  // Fetch existing VMs
  useEffect(() => {
    if (open) {
      fetchExistingVMs();
    }
  }, [open]);

  const fetchExistingVMs = async () => {
    try {
      setLoading(true);

      // Try the new Firecracker API first
      try {
        const firecrackerResponse = await fetch('/api/containerization/firecracker');

        if (firecrackerResponse.ok) {
          const firecrackerData = await firecrackerResponse.json();

          // Check if there's a warning
          if (firecrackerData.warning) {
            console.warn('Firecracker warning:', firecrackerData.warning);
            toast({
              title: 'Firecracker Warning',
              description: firecrackerData.suggestion || firecrackerData.warning,
              variant: 'warning',
            });
          }

          const vms = firecrackerData.vms || [];

          // Convert to MicroVM format
          setExistingVMs(vms.map((vm: any) => ({
            id: vm.id,
            name: vm.name,
            status: vm.status,
            createdAt: vm.createdAt,
          })));

          return;
        }
      } catch (firecrackerError) {
        console.warn('Error fetching Firecracker VMs, falling back to MicroVM API:', firecrackerError);
      }

      // Fall back to the original MicroVM API
      const response = await fetch('/api/containerization/microvm');

      if (!response.ok) {
        throw new Error('Failed to fetch MicroVMs');
      }

      const data = await response.json();
      setExistingVMs(data.microvms || []);
    } catch (error) {
      console.error('Error fetching MicroVMs:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch existing MicroVMs',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSelectVM = () => {
    if (!selectedVM) {
      toast({
        title: 'Error',
        description: 'Please select a MicroVM',
        variant: 'destructive',
      });
      return;
    }

    onSelectVM(selectedVM);
    onOpenChange(false);
  };

  const handleCreateVM = async () => {
    if (!vmName) {
      toast({
        title: 'Error',
        description: 'Please enter a name for the MicroVM',
        variant: 'destructive',
      });
      return;
    }

    try {
      setCreating(true);

      // Try the new Firecracker API first
      try {
        // Create the Firecracker VM
        const firecrackerResponse = await fetch('/api/containerization/firecracker', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: vmName,
            projectId,
            vcpu: cpuCount,
            memoryMb: memoryMB,
            diskSizeGb: diskSizeGB,
          }),
        });

        if (firecrackerResponse.ok) {
          const firecrackerData = await firecrackerResponse.json();

          toast({
            title: 'Success',
            description: 'MicroVM created successfully using Firecracker',
          });

          onSelectVM(firecrackerData.id);
          onOpenChange(false);
          return;
        } else {
          // Handle error response
          const errorData = await firecrackerResponse.json();

          if (errorData.error === 'Firecracker not installed') {
            toast({
              title: 'Firecracker Not Installed',
              description: errorData.suggestion || 'Please install Firecracker to use this feature.',
              variant: 'destructive',
            });

            // Continue with fallback
            console.warn('Firecracker not installed, falling back to MicroVM API');
          } else {
            // Log the error but don't show a toast, as we'll fall back to the MicroVM API
            console.warn('Error creating Firecracker VM:', errorData.error, errorData.details);
          }
        }
      } catch (firecrackerError) {
        console.warn('Error creating Firecracker VM, falling back to MicroVM API:', firecrackerError);
      }

      // Fall back to the original MicroVM API
      // Prepare environment first
      const prepareResponse = await fetch('/api/containerization/microvm/prepare-environment', {
        method: 'POST',
      });

      if (!prepareResponse.ok) {
        throw new Error('Failed to prepare environment for MicroVM');
      }

      const prepareData = await prepareResponse.json();

      // Check if kernel and rootfs files exist
      if (!prepareData.kernelExists || !prepareData.rootfsExists) {
        toast({
          title: 'Warning',
          description: `Some required files are missing: ${!prepareData.kernelExists ? 'kernel' : ''} ${!prepareData.rootfsExists ? 'rootfs' : ''}`,
          variant: 'destructive',
        });
      }

      // Create the MicroVM
      const response = await fetch('/api/containerization/microvm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: vmName,
          projectId,
          vcpuCount: cpuCount,
          memSizeMib: memoryMB,
          diskSizeGb: diskSizeGB,
          kernelImagePath: kernelPath,
          rootfsPath: rootfsPath,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create MicroVM');
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: 'MicroVM created successfully',
      });

      onSelectVM(data.id);
      onOpenChange(false);
    } catch (error) {
      console.error('Error creating MicroVM:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create MicroVM',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Select or Create MicroVM</DialogTitle>
          <DialogDescription>
            Choose an existing MicroVM or create a new one for your project.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={tab} onValueChange={setTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="select">Select Existing</TabsTrigger>
            <TabsTrigger value="create">Create New</TabsTrigger>
          </TabsList>

          <TabsContent value="select" className="space-y-4 py-4">
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : existingVMs.length > 0 ? (
              <div className="space-y-4">
                <Select value={selectedVM || ''} onValueChange={setSelectedVM}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a MicroVM" />
                  </SelectTrigger>
                  <SelectContent>
                    {existingVMs.map((vm) => (
                      <SelectItem key={vm.id} value={vm.id}>
                        {vm.name} ({vm.status})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                No existing MicroVMs found. Create a new one.
              </div>
            )}
          </TabsContent>

          <TabsContent value="create" className="space-y-4 py-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={vmName}
                  onChange={(e) => setVmName(e.target.value)}
                  placeholder="My MicroVM"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="cpu">CPU Cores: {cpuCount}</Label>
                <Slider
                  id="cpu"
                  min={1}
                  max={4}
                  step={1}
                  value={[cpuCount]}
                  onValueChange={(value) => setCpuCount(value[0])}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="memory">Memory (MB): {memoryMB}</Label>
                <Slider
                  id="memory"
                  min={256}
                  max={4096}
                  step={256}
                  value={[memoryMB]}
                  onValueChange={(value) => setMemoryMB(value[0])}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="disk">Disk Size (GB): {diskSizeGB}</Label>
                <Slider
                  id="disk"
                  min={1}
                  max={20}
                  step={1}
                  value={[diskSizeGB]}
                  onValueChange={(value) => setDiskSizeGB(value[0])}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="kernel">Kernel Image Path</Label>
                <Input
                  id="kernel"
                  value={kernelPath}
                  onChange={(e) => setKernelPath(e.target.value)}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="rootfs">Root Filesystem Path</Label>
                <Input
                  id="rootfs"
                  value={rootfsPath}
                  onChange={(e) => setRootfsPath(e.target.value)}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          {tab === 'select' ? (
            <Button onClick={handleSelectVM} disabled={!selectedVM || loading}>
              {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Select
            </Button>
          ) : (
            <Button onClick={handleCreateVM} disabled={!vmName || creating}>
              {creating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Create
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
