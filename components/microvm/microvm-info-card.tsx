"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Cpu, MemoryStick, HardDrive, Network, Play, Pause, RefreshCw, Terminal, Trash2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface MicroVmInfoCardProps {
  id: string;
  name: string;
  status: string;
  template?: string;
  memSizeMib?: number;
  vcpuCount?: number;
  onTerminal?: (id: string) => void;
  onStart?: (id: string) => void;
  onStop?: (id: string) => void;
  onRestart?: (id: string) => void;
  onDelete?: (id: string) => void;
}

interface MicroVmMetrics {
  cpuUsage: number;
  memoryUsage: number;
  networkRx: number;
  networkTx: number;
  diskRead: number;
  diskWrite: number;
  uptime: number;
}

export function MicroVmInfoCard({
  id,
  name,
  status,
  template = 'unknown',
  memSizeMib = 0,
  vcpuCount = 0,
  onTerminal,
  onStart,
  onStop,
  onRestart,
  onDelete,
}: MicroVmInfoCardProps) {
  const [metrics, setMetrics] = useState<MicroVmMetrics | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Format bytes to human-readable format
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format uptime to human-readable format
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / (24 * 60 * 60));
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((seconds % (60 * 60)) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'bg-green-500';
      case 'stopped':
        return 'bg-gray-500';
      case 'starting':
        return 'bg-blue-500';
      case 'stopping':
        return 'bg-yellow-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Get template badge color
  const getTemplateColor = (template: string) => {
    switch (template.toLowerCase()) {
      case 'nodejs':
        return 'bg-green-700';
      case 'python':
        return 'bg-blue-700';
      case 'full-stack':
        return 'bg-purple-700';
      case 'minimal':
        return 'bg-gray-700';
      default:
        return 'bg-gray-700';
    }
  };

  // Fetch metrics
  const fetchMetrics = async () => {
    if (status.toLowerCase() !== 'running') {
      setMetrics(null);
      return;
    }

    try {
      const response = await fetch(`/api/containerization/microvm/${id}/metrics`);
      if (response.ok) {
        const data = await response.json();
        setMetrics(data);
      }
    } catch (error) {
      console.error('Error fetching metrics:', error);
    }
  };

  // Fetch metrics periodically
  useEffect(() => {
    if (status.toLowerCase() === 'running') {
      fetchMetrics();
      const interval = setInterval(fetchMetrics, 5000);
      return () => clearInterval(interval);
    }
  }, [id, status]);

  // Handle terminal button click
  const handleTerminal = () => {
    if (onTerminal) {
      onTerminal(id);
    }
  };

  // Handle start button click
  const handleStart = async () => {
    if (onStart) {
      setLoading(true);
      try {
        await onStart(id);
        toast({
          title: "MicroVM Starting",
          description: `MicroVM '${name}' is starting...`,
        });
      } catch (error) {
        toast({
          title: "Error",
          description: `Failed to start MicroVM '${name}'`,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle stop button click
  const handleStop = async () => {
    if (onStop) {
      setLoading(true);
      try {
        await onStop(id);
        toast({
          title: "MicroVM Stopping",
          description: `MicroVM '${name}' is stopping...`,
        });
      } catch (error) {
        toast({
          title: "Error",
          description: `Failed to stop MicroVM '${name}'`,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle restart button click
  const handleRestart = async () => {
    if (onRestart) {
      setLoading(true);
      try {
        await onRestart(id);
        toast({
          title: "MicroVM Restarting",
          description: `MicroVM '${name}' is restarting...`,
        });
      } catch (error) {
        toast({
          title: "Error",
          description: `Failed to restart MicroVM '${name}'`,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle delete button click
  const handleDelete = async () => {
    if (onDelete) {
      setLoading(true);
      try {
        await onDelete(id);
        toast({
          title: "MicroVM Deleted",
          description: `MicroVM '${name}' has been deleted.`,
        });
      } catch (error) {
        toast({
          title: "Error",
          description: `Failed to delete MicroVM '${name}'`,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Card className="w-full shadow-md hover:shadow-lg transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl">{name}</CardTitle>
            <CardDescription className="text-sm text-gray-500">ID: {id.substring(0, 8)}...</CardDescription>
          </div>
          <div className="flex gap-2">
            <Badge className={getTemplateColor(template)}>{template}</Badge>
            <Badge className={getStatusColor(status)}>{status}</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center">
            <Cpu className="h-4 w-4 mr-2" />
            <span className="text-sm">{vcpuCount} vCPUs</span>
          </div>
          <div className="flex items-center">
            <MemoryStick className="h-4 w-4 mr-2" />
            <span className="text-sm">{memSizeMib} MiB RAM</span>
          </div>
        </div>

        {metrics && status.toLowerCase() === 'running' && (
          <div className="space-y-4">
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>CPU Usage</span>
                <span>{metrics.cpuUsage.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.cpuUsage} className="h-1" />
            </div>

            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>Memory Usage</span>
                <span>{formatBytes(metrics.memoryUsage)} / {formatBytes(memSizeMib * 1024 * 1024)}</span>
              </div>
              <Progress value={(metrics.memoryUsage / (memSizeMib * 1024 * 1024)) * 100} className="h-1" />
            </div>

            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="flex items-center">
                  <Network className="h-3 w-3 mr-1" /> Network
                </span>
                <div className="mt-1">
                  <div>↓ {formatBytes(metrics.networkRx)}</div>
                  <div>↑ {formatBytes(metrics.networkTx)}</div>
                </div>
              </div>
              <div>
                <span className="flex items-center">
                  <HardDrive className="h-3 w-3 mr-1" /> Disk I/O
                </span>
                <div className="mt-1">
                  <div>Read: {formatBytes(metrics.diskRead)}</div>
                  <div>Write: {formatBytes(metrics.diskWrite)}</div>
                </div>
              </div>
            </div>

            <div className="text-xs text-gray-500">
              Uptime: {formatUptime(metrics.uptime)}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-2">
        <div className="flex justify-between w-full">
          <div>
            {status.toLowerCase() === 'running' ? (
              <Button variant="outline" size="sm" onClick={handleStop} disabled={loading}>
                <Pause className="h-4 w-4 mr-1" /> Stop
              </Button>
            ) : (
              <Button variant="outline" size="sm" onClick={handleStart} disabled={loading}>
                <Play className="h-4 w-4 mr-1" /> Start
              </Button>
            )}
            <Button variant="outline" size="sm" className="ml-2" onClick={handleRestart} disabled={loading || status.toLowerCase() !== 'running'}>
              <RefreshCw className="h-4 w-4 mr-1" /> Restart
            </Button>
          </div>
          <div>
            <Button variant="outline" size="sm" onClick={handleTerminal} disabled={loading || status.toLowerCase() !== 'running'}>
              <Terminal className="h-4 w-4 mr-1" /> Terminal
            </Button>
            <Button variant="outline" size="sm" className="ml-2" onClick={handleDelete} disabled={loading}>
              <Trash2 className="h-4 w-4 mr-1 text-red-500" />
            </Button>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
} 