/**
 * MicroVM Selection Dialog
 * 
 * This component provides a dialog for selecting or creating a MicroVM when the workspace loads.
 */

import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle, Plus, RefreshCw, Cpu } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface MicroVM {
  id: string;
  name: string;
  state: string;
  createdAt: string;
  updatedAt: string;
  projectId: string | null;
}

interface MicroVmSelectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string | null;
  onSelectVM: (vmId: string) => void;
}

export function MicroVmSelectionDialog({
  open,
  onOpenChange,
  projectId,
  onSelectVM,
}: MicroVmSelectionDialogProps) {
  const [activeTab, setActiveTab] = useState<string>("select");
  const [microvms, setMicrovms] = useState<MicroVM[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVmId, setSelectedVmId] = useState<string>("");
  const [creating, setCreating] = useState(false);
  const [starting, setStarting] = useState(false);
  
  // New VM form state
  const [newVmName, setNewVmName] = useState('');
  const [newVmMemory, setNewVmMemory] = useState('1024');
  const [newVmCpus, setNewVmCpus] = useState('2');
  const [newVmImage, setNewVmImage] = useState('ubuntu-22.04');

  // Fetch MicroVMs
  const fetchMicroVMs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const url = projectId 
        ? `/api/containerization/microvm?projectId=${projectId}`
        : '/api/containerization/microvm';
      
      const response = await fetch(url);
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to fetch MicroVMs');
      }
      
      const data = await response.json();
      const vms = data.microvms || [];
      
      setMicrovms(vms);
      
      // If there are VMs for this project, select the first one
      if (vms.length > 0 && projectId) {
        const projectVms = vms.filter(vm => vm.projectId === projectId);
        if (projectVms.length > 0) {
          setSelectedVmId(projectVms[0].id);
        } else if (vms.length > 0) {
          setSelectedVmId(vms[0].id);
        }
      }
      
      // If no VMs, switch to create tab
      if (vms.length === 0) {
        setActiveTab("create");
      }
    } catch (error) {
      console.error('Error fetching MicroVMs:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Create MicroVM
  const createMicroVM = async () => {
    try {
      setCreating(true);
      setError(null);
      
      const response = await fetch('/api/containerization/microvm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newVmName || `microvm-${projectId ? projectId.slice(-4) : Date.now()}`,
          memSizeMib: parseInt(newVmMemory),
          vcpuCount: parseInt(newVmCpus),
          rootfs: {
            path: `/var/lib/microvm/images/${newVmImage}.ext4`,
            readOnly: false,
          },
          kernel: {
            path: '/var/lib/microvm/kernels/vmlinux',
            bootArgs: 'console=ttyS0 reboot=k panic=1 pci=off',
          },
          networkInterfaces: [
            {
              type: 'tap',
              name: 'tap0',
            },
          ],
          projectId,
        }),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to create MicroVM');
      }
      
      const data = await response.json();
      
      toast({
        title: 'Success',
        description: `MicroVM ${data.name} created successfully`,
      });
      
      // Start the newly created VM
      await startMicroVM(data.id);
      
      // Select the newly created VM
      onSelectVM(data.id);
      
      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error('Error creating MicroVM:', error);
      setError(error.message);
    } finally {
      setCreating(false);
    }
  };

  // Start MicroVM
  const startMicroVM = async (id: string) => {
    try {
      setStarting(true);
      setError(null);
      
      const response = await fetch(`/api/containerization/microvm/${id}/start`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to start MicroVM');
      }
      
      return true;
    } catch (error) {
      console.error('Error starting MicroVM:', error);
      setError(error.message);
      return false;
    } finally {
      setStarting(false);
    }
  };

  // Handle VM selection
  const handleSelectVM = async () => {
    if (!selectedVmId) {
      toast({
        title: 'Error',
        description: 'Please select a MicroVM',
        variant: 'destructive',
      });
      return;
    }
    
    // Get the selected VM
    const selectedVM = microvms.find(vm => vm.id === selectedVmId);
    
    // If VM is not running, start it
    if (selectedVM && selectedVM.state !== 'RUNNING') {
      const started = await startMicroVM(selectedVmId);
      if (!started) return;
    }
    
    // Select the VM
    onSelectVM(selectedVmId);
    
    // Close the dialog
    onOpenChange(false);
  };

  // Fetch MicroVMs on mount and when projectId changes
  useEffect(() => {
    if (open) {
      fetchMicroVMs();
      
      // Set default VM name based on project ID
      if (projectId) {
        setNewVmName(`microvm-${projectId.slice(-4)}`);
      }
    }
  }, [open, projectId]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Cpu className="h-5 w-5 mr-2" />
            MicroVM Environment
          </DialogTitle>
          <DialogDescription>
            Select an existing MicroVM or create a new one for your workspace.
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="select">Select Existing</TabsTrigger>
            <TabsTrigger value="create">Create New</TabsTrigger>
          </TabsList>
          
          <TabsContent value="select" className="py-4">
            {loading ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            ) : microvms.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-muted-foreground">No MicroVMs found</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => setActiveTab("create")}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create MicroVM
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="vm-select">Select a MicroVM</Label>
                  <Select value={selectedVmId} onValueChange={setSelectedVmId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a MicroVM" />
                    </SelectTrigger>
                    <SelectContent>
                      {microvms.map((vm) => (
                        <SelectItem key={vm.id} value={vm.id}>
                          {vm.name} ({vm.state})
                          {vm.projectId === projectId ? " (Current Project)" : ""}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  {selectedVmId && (
                    <div>
                      <p>
                        <strong>Selected VM:</strong>{" "}
                        {microvms.find((vm) => vm.id === selectedVmId)?.name}
                      </p>
                      <p>
                        <strong>State:</strong>{" "}
                        {microvms.find((vm) => vm.id === selectedVmId)?.state}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="create" className="py-4">
            <div className="space-y-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={newVmName}
                  onChange={(e) => setNewVmName(e.target.value)}
                  className="col-span-3"
                  placeholder={`microvm-${projectId ? projectId.slice(-4) : 'new'}`}
                />
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="memory" className="text-right">
                  Memory (MB)
                </Label>
                <Input
                  id="memory"
                  type="number"
                  value={newVmMemory}
                  onChange={(e) => setNewVmMemory(e.target.value)}
                  className="col-span-3"
                />
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="cpus" className="text-right">
                  CPUs
                </Label>
                <Input
                  id="cpus"
                  type="number"
                  value={newVmCpus}
                  onChange={(e) => setNewVmCpus(e.target.value)}
                  className="col-span-3"
                />
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="image" className="text-right">
                  Image
                </Label>
                <Select
                  value={newVmImage}
                  onValueChange={setNewVmImage}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select an image" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ubuntu-22.04">Ubuntu 22.04</SelectItem>
                    <SelectItem value="ubuntu-20.04">Ubuntu 20.04</SelectItem>
                    <SelectItem value="debian-11">Debian 11</SelectItem>
                    <SelectItem value="alpine-3.16">Alpine 3.16</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          {activeTab === "select" ? (
            <Button 
              onClick={handleSelectVM} 
              disabled={!selectedVmId || starting}
            >
              {starting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Starting...
                </>
              ) : (
                'Select & Continue'
              )}
            </Button>
          ) : (
            <Button 
              onClick={createMicroVM} 
              disabled={creating}
            >
              {creating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create & Continue'
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
