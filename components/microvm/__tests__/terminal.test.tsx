/**
 * MicroVmTerminal Tests
 *
 * This file contains tests for the MicroVmTerminal component.
 */

import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { MicroVmTerminal } from '../terminal';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { SearchAddon } from '@xterm/addon-search';

// Mock dependencies
jest.mock('@xterm/xterm', () => ({
  Terminal: jest.fn().mockImplementation(() => ({
    loadAddon: jest.fn(),
    open: jest.fn(),
    writeln: jest.fn(),
    write: jest.fn(),
    clear: jest.fn(),
    onData: jest.fn(),
    dispose: jest.fn(),
    cols: 80,
    rows: 24,
  })),
}));

jest.mock('@xterm/addon-fit', () => ({
  FitAddon: jest.fn().mockImplementation(() => ({
    fit: jest.fn(),
  })),
}));

jest.mock('@xterm/addon-web-links', () => ({
  WebLinksAddon: jest.fn().mockImplementation(() => ({})),
}));

jest.mock('@xterm/addon-search', () => ({
  SearchAddon: jest.fn().mockImplementation(() => ({})),
}));

// Mock WebSocket
class MockWebSocket {
  url: string;
  onopen: ((this: WebSocket, ev: Event) => any) | null = null;
  onmessage: ((this: WebSocket, ev: MessageEvent) => any) | null = null;
  onclose: ((this: WebSocket, ev: CloseEvent) => any) | null = null;
  onerror: ((this: WebSocket, ev: Event) => any) | null = null;
  readyState: number = WebSocket.CONNECTING;

  constructor(url: string) {
    this.url = url;
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      if (this.onopen) this.onopen(new Event('open'));
    }, 0);
  }

  send(data: string): void {
    // Mock implementation
  }

  close(): void {
    this.readyState = WebSocket.CLOSED;
    if (this.onclose) this.onclose(new CloseEvent('close'));
  }
}

// Replace global WebSocket with mock
(global as any).WebSocket = MockWebSocket;

describe('MicroVmTerminal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the terminal container', () => {
    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={false} />);

    // Assert
    expect(screen.getByClass('microvm-terminal')).toBeInTheDocument();
    expect(screen.getByClass('terminal-container')).toBeInTheDocument();
  });

  it('should initialize terminal on mount', () => {
    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={false} />);

    // Assert
    expect(Terminal).toHaveBeenCalled();
    expect(FitAddon).toHaveBeenCalled();
    expect(WebLinksAddon).toHaveBeenCalled();
    expect(SearchAddon).toHaveBeenCalled();
  });

  it('should auto-connect if autoConnect is true', async () => {
    // Arrange
    const connectSpy = jest.spyOn(MicroVmTerminal.prototype as any, 'connect');

    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={true} />);

    // Assert
    await waitFor(() => {
      expect(connectSpy).toHaveBeenCalled();
    });

    // Cleanup
    connectSpy.mockRestore();
  });

  it('should not auto-connect if autoConnect is false', () => {
    // Arrange
    const connectSpy = jest.spyOn(MicroVmTerminal.prototype as any, 'connect');

    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={false} />);

    // Assert
    expect(connectSpy).not.toHaveBeenCalled();

    // Cleanup
    connectSpy.mockRestore();
  });

  it('should show connect button when not connected', () => {
    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={false} />);

    // Assert
    expect(screen.getByText('Connect')).toBeInTheDocument();
  });

  it('should connect when connect button is clicked', async () => {
    // Arrange
    const connectSpy = jest.spyOn(MicroVmTerminal.prototype as any, 'connect');

    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={false} />);
    fireEvent.click(screen.getByText('Connect'));

    // Assert
    expect(connectSpy).toHaveBeenCalled();

    // Cleanup
    connectSpy.mockRestore();
  });

  it('should establish WebSocket connection on connect', async () => {
    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={true} />);

    // Assert
    await waitFor(() => {
      expect(global.WebSocket).toHaveBeenCalledWith(expect.stringContaining('/api/microvms/test-vm/terminal'));
    });
  });

  it('should handle WebSocket messages', async () => {
    // Arrange
    const terminalWriteSpy = jest.fn();
    (Terminal as jest.Mock).mockImplementation(() => ({
      loadAddon: jest.fn(),
      open: jest.fn(),
      writeln: jest.fn(),
      write: terminalWriteSpy,
      clear: jest.fn(),
      onData: jest.fn(),
      dispose: jest.fn(),
      cols: 80,
      rows: 24,
    }));

    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={true} />);

    // Wait for WebSocket connection
    await waitFor(() => {
      expect(global.WebSocket).toHaveBeenCalled();
    });

    // Simulate WebSocket message
    const mockWebSocket = (global.WebSocket as jest.Mock).mock.instances[0];
    act(() => {
      mockWebSocket.onmessage!({
        data: JSON.stringify({ type: 'data', data: 'Hello, terminal!' }),
      } as any);
    });

    // Assert
    expect(terminalWriteSpy).toHaveBeenCalledWith('Hello, terminal!');
  });

  it('should handle WebSocket errors', async () => {
    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={true} />);

    // Wait for WebSocket connection
    await waitFor(() => {
      expect(global.WebSocket).toHaveBeenCalled();
    });

    // Simulate WebSocket error
    const mockWebSocket = (global.WebSocket as jest.Mock).mock.instances[0];
    act(() => {
      mockWebSocket.onmessage!({
        data: JSON.stringify({ type: 'error', message: 'Connection error' }),
      } as any);
    });

    // Assert
    expect(screen.getByText('Connection error')).toBeInTheDocument();
  });

  it('should send terminal input to WebSocket', async () => {
    // Arrange
    const sendSpy = jest.fn();
    (global.WebSocket as jest.Mock).mockImplementation(() => ({
      url: '',
      send: sendSpy,
      readyState: WebSocket.OPEN,
      onopen: null,
      onmessage: null,
      onclose: null,
      onerror: null,
      close: jest.fn(),
    }));

    let onDataCallback: (data: string) => void;
    (Terminal as jest.Mock).mockImplementation(() => ({
      loadAddon: jest.fn(),
      open: jest.fn(),
      writeln: jest.fn(),
      write: jest.fn(),
      clear: jest.fn(),
      onData: (callback: (data: string) => void) => {
        onDataCallback = callback;
      },
      dispose: jest.fn(),
      cols: 80,
      rows: 24,
    }));

    // Act
    render(<MicroVmTerminal vmId="test-vm" autoConnect={true} />);

    // Wait for WebSocket connection
    await waitFor(() => {
      expect(global.WebSocket).toHaveBeenCalled();
    });

    // Simulate terminal input
    act(() => {
      onDataCallback('ls -la');
    });

    // Assert
    expect(sendSpy).toHaveBeenCalledWith(JSON.stringify({ type: 'data', data: 'ls -la' }));
  });

  it('should clean up on unmount', () => {
    // Arrange
    const terminalDisposeSpy = jest.fn();
    const webSocketCloseSpy = jest.fn();

    (Terminal as jest.Mock).mockImplementation(() => ({
      loadAddon: jest.fn(),
      open: jest.fn(),
      writeln: jest.fn(),
      write: jest.fn(),
      clear: jest.fn(),
      onData: jest.fn(),
      dispose: terminalDisposeSpy,
      cols: 80,
      rows: 24,
    }));

    (global.WebSocket as jest.Mock).mockImplementation(() => ({
      url: '',
      send: jest.fn(),
      readyState: WebSocket.OPEN,
      onopen: null,
      onmessage: null,
      onclose: null,
      onerror: null,
      close: webSocketCloseSpy,
    }));

    // Act
    const { unmount } = render(<MicroVmTerminal vmId="test-vm" autoConnect={true} />);

    // Wait for WebSocket connection
    await waitFor(() => {
      expect(global.WebSocket).toHaveBeenCalled();
    });

    // Unmount component
    unmount();

    // Assert
    expect(terminalDisposeSpy).toHaveBeenCalled();
    expect(webSocketCloseSpy).toHaveBeenCalled();
  });
});
