"use client";

import React, { useState, useEffect } from 'react';
import { MicroVmInfoCard } from './microvm-info-card';
import { Button } from '@/components/ui/button';
import { PlusCircle, RefreshCw, Server } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface MicroVm {
  id: string;
  name: string;
  status: string;
  template: string;
  resources: {
    memSizeMib: number;
    vcpuCount: number;
  };
  networkInterfaces: Array<{ id: string; hostDevName?: string }>;
}

interface MicroVmDashboardProps {
  projectId?: string;
  onOpenTerminal?: (vmId: string) => void;
}

export function MicroVmDashboard({ projectId, onOpenTerminal }: MicroVmDashboardProps) {
  const [vms, setVms] = useState<MicroVm[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newVmName, setNewVmName] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('nodejs');
  const [memorySize, setMemorySize] = useState('2048');
  const [cpuCount, setCpuCount] = useState('2');
  const [activeTab, setActiveTab] = useState('all');
  const { toast } = useToast();

  // Fetch MicroVMs
  const fetchVms = async () => {
    setRefreshing(true);
    try {
      const response = await fetch('/api/containerization/microvm/list');
      if (response.ok) {
        const data = await response.json();
        setVms(data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to fetch MicroVMs',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching MicroVMs:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch MicroVMs',
        variant: 'destructive',
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Fetch VMs on mount
  useEffect(() => {
    fetchVms();
    // Set up polling
    const interval = setInterval(fetchVms, 10000);
    return () => clearInterval(interval);
  }, []);

  // Handle create MicroVM
  const handleCreateVm = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/containerization/microvm/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newVmName || undefined,
          template: selectedTemplate,
          memSizeMib: parseInt(memorySize),
          vcpuCount: parseInt(cpuCount),
          projectId,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: 'MicroVM Created',
          description: `MicroVM '${data.name}' created successfully`,
        });
        setCreateDialogOpen(false);
        fetchVms();
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.message || 'Failed to create MicroVM',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating MicroVM:', error);
      toast({
        title: 'Error',
        description: 'Failed to create MicroVM',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle start MicroVM
  const handleStartVm = async (id: string) => {
    try {
      const response = await fetch(`/api/containerization/microvm/${id}/start`, {
        method: 'POST',
      });
      if (response.ok) {
        fetchVms();
        return true;
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.message || 'Failed to start MicroVM',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Error starting MicroVM:', error);
      toast({
        title: 'Error',
        description: 'Failed to start MicroVM',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Handle stop MicroVM
  const handleStopVm = async (id: string) => {
    try {
      const response = await fetch(`/api/containerization/microvm/${id}/stop`, {
        method: 'POST',
      });
      if (response.ok) {
        fetchVms();
        return true;
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.message || 'Failed to stop MicroVM',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Error stopping MicroVM:', error);
      toast({
        title: 'Error',
        description: 'Failed to stop MicroVM',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Handle restart MicroVM
  const handleRestartVm = async (id: string) => {
    try {
      const response = await fetch(`/api/containerization/microvm/${id}/restart`, {
        method: 'POST',
      });
      if (response.ok) {
        fetchVms();
        return true;
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.message || 'Failed to restart MicroVM',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Error restarting MicroVM:', error);
      toast({
        title: 'Error',
        description: 'Failed to restart MicroVM',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Handle delete MicroVM
  const handleDeleteVm = async (id: string) => {
    try {
      const response = await fetch(`/api/containerization/microvm/${id}/delete`, {
        method: 'DELETE',
      });
      if (response.ok) {
        fetchVms();
        return true;
      } else {
        const errorData = await response.json();
        toast({
          title: 'Error',
          description: errorData.message || errorData.error || 'Failed to delete MicroVM',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Error deleting MicroVM:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete MicroVM',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Handle terminal button
  const handleOpenTerminal = (id: string) => {
    if (onOpenTerminal) {
      onOpenTerminal(id);
    }
  };

  // Filter VMs based on active tab
  const filteredVms = vms.filter(vm => {
    if (activeTab === 'all') return true;
    if (activeTab === 'running') return vm.status.toLowerCase() === 'running';
    if (activeTab === 'stopped') return vm.status.toLowerCase() === 'stopped';
    if (activeTab === 'nodejs') return vm.template === 'nodejs';
    if (activeTab === 'python') return vm.template === 'python';
    if (activeTab === 'full-stack') return vm.template === 'full-stack';
    return true;
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">MicroVM Dashboard</h2>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchVms}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="h-4 w-4 mr-2" />
                Create MicroVM
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New MicroVM</DialogTitle>
                <DialogDescription>
                  Create a new MicroVM for AI development and testing.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    placeholder="ai-vm-001"
                    className="col-span-3"
                    value={newVmName}
                    onChange={(e) => setNewVmName(e.target.value)}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="template" className="text-right">
                    Template
                  </Label>
                  <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select template" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="nodejs">Node.js Development</SelectItem>
                      <SelectItem value="python">Python Development</SelectItem>
                      <SelectItem value="full-stack">Full-Stack Development</SelectItem>
                      <SelectItem value="minimal">Minimal Linux</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="memory" className="text-right">
                    Memory (MiB)
                  </Label>
                  <Select value={memorySize} onValueChange={setMemorySize}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select memory size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1024">1024 MiB</SelectItem>
                      <SelectItem value="2048">2048 MiB</SelectItem>
                      <SelectItem value="4096">4096 MiB</SelectItem>
                      <SelectItem value="8192">8192 MiB</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="cpu" className="text-right">
                    vCPUs
                  </Label>
                  <Select value={cpuCount} onValueChange={setCpuCount}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select CPU count" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 vCPU</SelectItem>
                      <SelectItem value="2">2 vCPUs</SelectItem>
                      <SelectItem value="4">4 vCPUs</SelectItem>
                      <SelectItem value="8">8 vCPUs</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateVm} disabled={loading}>
                  {loading ? 'Creating...' : 'Create MicroVM'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="running">Running</TabsTrigger>
          <TabsTrigger value="stopped">Stopped</TabsTrigger>
          <TabsTrigger value="nodejs">Node.js</TabsTrigger>
          <TabsTrigger value="python">Python</TabsTrigger>
          <TabsTrigger value="full-stack">Full-Stack</TabsTrigger>
        </TabsList>
        <TabsContent value={activeTab} className="mt-4">
          {filteredVms.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-10 text-center">
              <Server className="h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium">No MicroVMs found</h3>
              <p className="text-sm text-gray-500 mt-2">
                {activeTab === 'all'
                  ? 'Create a new MicroVM to get started.'
                  : `No ${activeTab} MicroVMs found.`}
              </p>
              {activeTab === 'all' && (
                <Button
                  className="mt-4"
                  onClick={() => setCreateDialogOpen(true)}
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create MicroVM
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredVms.map((vm) => (
                <MicroVmInfoCard
                  key={vm.id}
                  id={vm.id}
                  name={vm.name}
                  status={vm.status}
                  template={vm.template}
                  memSizeMib={vm.resources.memSizeMib}
                  vcpuCount={vm.resources.vcpuCount}
                  onTerminal={handleOpenTerminal}
                  onStart={handleStartVm}
                  onStop={handleStopVm}
                  onRestart={handleRestartVm}
                  onDelete={handleDeleteVm}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
} 