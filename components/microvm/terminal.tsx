'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { SearchAddon } from '@xterm/addon-search';
import '@xterm/xterm/css/xterm.css';

interface MicroVmTerminalProps {
  vmId: string;
  height?: string;
  width?: string;
  className?: string;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onData?: (data: string) => void;
  onResize?: (cols: number, rows: number) => void;
  autoConnect?: boolean;
  theme?: {
    background?: string;
    foreground?: string;
    cursor?: string;
    selection?: string;
    black?: string;
    red?: string;
    green?: string;
    yellow?: string;
    blue?: string;
    magenta?: string;
    cyan?: string;
    white?: string;
    brightBlack?: string;
    brightRed?: string;
    brightGreen?: string;
    brightYellow?: string;
    brightBlue?: string;
    brightMagenta?: string;
    brightCyan?: string;
    brightWhite?: string;
  };
}

/**
 * MicroVM Terminal Component
 *
 * A terminal component for interacting with MicroVMs using Xterm.js
 */
export const MicroVmTerminal: React.FC<MicroVmTerminalProps> = ({
  vmId,
  height = '400px',
  width = '100%',
  className = '',
  onConnect,
  onDisconnect,
  onData,
  onResize,
  autoConnect = true,
  theme = {
    background: '#1a1b26',
    foreground: '#c0caf5',
    cursor: '#c0caf5',
    selection: '#28344a',
    black: '#414868',
    red: '#f7768e',
    green: '#9ece6a',
    yellow: '#e0af68',
    blue: '#7aa2f7',
    magenta: '#bb9af7',
    cyan: '#7dcfff',
    white: '#c0caf5',
    brightBlack: '#414868',
    brightRed: '#f7768e',
    brightGreen: '#9ece6a',
    brightYellow: '#e0af68',
    brightBlue: '#7aa2f7',
    brightMagenta: '#bb9af7',
    brightCyan: '#7dcfff',
    brightWhite: '#c0caf5'
  }
}) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const terminalInstance = useRef<Terminal | null>(null);
  const fitAddon = useRef<FitAddon | null>(null);
  const searchAddon = useRef<SearchAddon | null>(null);
  const socketRef = useRef<WebSocket | null>(null);

  const [connected, setConnected] = useState<boolean>(false);
  const [connecting, setConnecting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize terminal
  useEffect(() => {
    if (!terminalRef.current || terminalInstance.current) return;

    // Create terminal instance
    const terminal = new Terminal({
      cursorBlink: true,
      fontSize: 14,
      fontFamily: "Menlo, Monaco, 'Courier New', monospace",
      theme,
      allowTransparency: true,
    });

    // Create addons
    const fit = new FitAddon();
    const webLinks = new WebLinksAddon();
    const search = new SearchAddon();

    // Store refs
    fitAddon.current = fit;
    searchAddon.current = search;

    // Add addons
    terminal.loadAddon(fit);
    terminal.loadAddon(webLinks);
    terminal.loadAddon(search);

    // Open terminal
    terminal.open(terminalRef.current);

    // Fit terminal to container
    setTimeout(() => {
      if (fit) fit.fit();

      // Notify about resize
      if (onResize) {
        onResize(terminal.cols, terminal.rows);
      }
    }, 100);

    // Store terminal instance
    terminalInstance.current = terminal;

    // Initial welcome message
    terminal.writeln("MicroVM Terminal");
    terminal.writeln(`Connecting to MicroVM: ${vmId}`);
    terminal.writeln("");

    // Handle window resize
    const handleResize = () => {
      if (fitAddon.current) {
        try {
          fitAddon.current.fit();

          // Notify about resize
          if (onResize && terminalInstance.current) {
            onResize(terminalInstance.current.cols, terminalInstance.current.rows);
          }
        } catch (e) {
          console.warn("Error fitting terminal on resize:", e);
        }
      }
    };

    window.addEventListener('resize', handleResize);

    // Clean up on unmount
    return () => {
      window.removeEventListener('resize', handleResize);

      // Close socket
      if (socketRef.current) {
        socketRef.current.close();
      }

      // Dispose terminal
      if (terminalInstance.current) {
        terminalInstance.current.dispose();
      }
    };
  }, [vmId, onResize, theme]);

  // Connect to WebSocket
  const connect = async () => {
    if (!terminalInstance.current) return;

    try {
      setConnecting(true);
      setError(null);

      // Clear terminal
      terminalInstance.current.clear();
      terminalInstance.current.writeln("Connecting to MicroVM terminal...");

      // Create WebSocket connection
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
      const wsUrl = `${protocol}//${window.location.host}/api/microvms/${vmId}/terminal`;

      // Close existing connection
      if (socketRef.current) {
        socketRef.current.close();
      }

      // Create new connection
      const socket = new WebSocket(wsUrl);
      socketRef.current = socket;

      socket.onopen = () => {
        if (terminalInstance.current) {
          terminalInstance.current.writeln("Connected to MicroVM terminal");
          setConnected(true);
          setConnecting(false);

          // Notify about connection
          if (onConnect) {
            onConnect();
          }

          // Send terminal size
          if (terminalInstance.current) {
            socket.send(JSON.stringify({
              type: 'resize',
              cols: terminalInstance.current.cols,
              rows: terminalInstance.current.rows
            }));
          }
        }
      };

      socket.onmessage = (event) => {
        if (terminalInstance.current) {
          try {
            const data = JSON.parse(event.data);

            if (data.type === 'data') {
              terminalInstance.current.write(data.data);
            } else if (data.type === 'error') {
              setError(data.message);
              terminalInstance.current.writeln(`\r\nError: ${data.message}`);
            }
          } catch (e) {
            // If not JSON, assume it's raw data
            terminalInstance.current.write(event.data);
          }
        }
      };

      socket.onclose = () => {
        if (terminalInstance.current) {
          terminalInstance.current.writeln("\r\nConnection closed");
          setConnected(false);
          setConnecting(false);

          // Notify about disconnection
          if (onDisconnect) {
            onDisconnect();
          }
        }
      };

      socket.onerror = (error) => {
        if (terminalInstance.current) {
          terminalInstance.current.writeln(`\r\nWebSocket error: ${error}`);
          setError(`WebSocket error: ${error}`);
          setConnected(false);
          setConnecting(false);
        }
      };

      // Set up terminal input
      terminalInstance.current.onData((data) => {
        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
          socketRef.current.send(JSON.stringify({ type: 'data', data }));

          // Notify about data
          if (onData) {
            onData(data);
          }
        }
      });

    } catch (error) {
      console.error("Error connecting to MicroVM terminal:", error);
      setConnecting(false);
      setConnected(false);
      setError(error instanceof Error ? error.message : "Failed to connect to MicroVM terminal");

      if (terminalInstance.current) {
        terminalInstance.current.writeln(`\r\nError: ${error instanceof Error ? error.message : "Failed to connect to MicroVM terminal"}`);
      }
    }
  };

  // Auto-connect
  useEffect(() => {
    if (autoConnect && terminalInstance.current && !connected && !connecting) {
      connect();
    }
  }, [autoConnect, connected, connecting]);

  return (
    <div className={`microvm-terminal ${className}`} style={{ height, width }}>
      <div
        ref={terminalRef}
        className="terminal-container"
        style={{ height: '100%', width: '100%' }}
      />

      {error && (
        <div className="terminal-error">
          {error}
        </div>
      )}

      {!connected && !connecting && (
        <div className="terminal-controls">
          <button
            onClick={connect}
            className="terminal-connect-button"
          >
            Connect
          </button>
        </div>
      )}
    </div>
  );
};

export default MicroVmTerminal;
