/**
 * MicroVM Manager Component
 * 
 * This component provides a UI for managing MicroVMs.
 */

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Plus, RefreshCw, Play, Square, RotateCw, Trash2, Terminal as TerminalIcon } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { useWorkspaceStore } from '@/lib/stores/workspace-store';

interface MicroVM {
  id: string;
  name: string;
  state: string;
  createdAt: string;
  updatedAt: string;
  projectId: string | null;
}

interface MicroVmManagerProps {
  projectId?: string;
  onSelectVM?: (vmId: string) => void;
  className?: string;
}

export function MicroVmManager({ projectId, onSelectVM, className = '' }: MicroVmManagerProps) {
  const [microvms, setMicrovms] = useState<MicroVM[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newVmName, setNewVmName] = useState('');
  const [newVmMemory, setNewVmMemory] = useState('1024');
  const [newVmCpus, setNewVmCpus] = useState('2');
  const [newVmImage, setNewVmImage] = useState('ubuntu-22.04');
  const [creating, setCreating] = useState(false);
  const [actionInProgress, setActionInProgress] = useState<string | null>(null);

  const { setContainerType } = useWorkspaceStore();

  // Fetch MicroVMs
  const fetchMicroVMs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const url = projectId 
        ? `/api/containerization/microvm?projectId=${projectId}`
        : '/api/containerization/microvm';
      
      const response = await fetch(url);
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to fetch MicroVMs');
      }
      
      const data = await response.json();
      setMicrovms(data.microvms || []);
    } catch (error) {
      console.error('Error fetching MicroVMs:', error);
      setError(error.message);
      toast({
        title: 'Error',
        description: `Failed to fetch MicroVMs: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh MicroVMs
  const refreshMicroVMs = () => {
    setRefreshing(true);
    fetchMicroVMs();
  };

  // Create MicroVM
  const createMicroVM = async () => {
    try {
      setCreating(true);
      
      const response = await fetch('/api/containerization/microvm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newVmName,
          memSizeMib: parseInt(newVmMemory),
          vcpuCount: parseInt(newVmCpus),
          rootfs: {
            path: `/var/lib/microvm/images/${newVmImage}.ext4`,
            readOnly: false,
          },
          kernel: {
            path: '/var/lib/microvm/kernels/vmlinux',
            bootArgs: 'console=ttyS0 reboot=k panic=1 pci=off',
          },
          networkInterfaces: [
            {
              type: 'tap',
              name: 'tap0',
            },
          ],
          projectId,
        }),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to create MicroVM');
      }
      
      const data = await response.json();
      
      toast({
        title: 'Success',
        description: `MicroVM ${data.name} created successfully`,
      });
      
      setCreateDialogOpen(false);
      setNewVmName('');
      fetchMicroVMs();
    } catch (error) {
      console.error('Error creating MicroVM:', error);
      toast({
        title: 'Error',
        description: `Failed to create MicroVM: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  // Start MicroVM
  const startMicroVM = async (id: string) => {
    try {
      setActionInProgress(id);
      
      const response = await fetch(`/api/containerization/microvm/${id}/start`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to start MicroVM');
      }
      
      toast({
        title: 'Success',
        description: 'MicroVM started successfully',
      });
      
      fetchMicroVMs();
    } catch (error) {
      console.error('Error starting MicroVM:', error);
      toast({
        title: 'Error',
        description: `Failed to start MicroVM: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setActionInProgress(null);
    }
  };

  // Stop MicroVM
  const stopMicroVM = async (id: string) => {
    try {
      setActionInProgress(id);
      
      const response = await fetch(`/api/containerization/microvm/${id}/stop`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to stop MicroVM');
      }
      
      toast({
        title: 'Success',
        description: 'MicroVM stopped successfully',
      });
      
      fetchMicroVMs();
    } catch (error) {
      console.error('Error stopping MicroVM:', error);
      toast({
        title: 'Error',
        description: `Failed to stop MicroVM: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setActionInProgress(null);
    }
  };

  // Restart MicroVM
  const restartMicroVM = async (id: string) => {
    try {
      setActionInProgress(id);
      
      const response = await fetch(`/api/containerization/microvm/${id}/restart`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to restart MicroVM');
      }
      
      toast({
        title: 'Success',
        description: 'MicroVM restarted successfully',
      });
      
      fetchMicroVMs();
    } catch (error) {
      console.error('Error restarting MicroVM:', error);
      toast({
        title: 'Error',
        description: `Failed to restart MicroVM: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setActionInProgress(null);
    }
  };

  // Delete MicroVM
  const deleteMicroVM = async (id: string) => {
    try {
      setActionInProgress(id);
      
      const response = await fetch(`/api/containerization/microvm/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete MicroVM');
      }
      
      toast({
        title: 'Success',
        description: 'MicroVM deleted successfully',
      });
      
      fetchMicroVMs();
    } catch (error) {
      console.error('Error deleting MicroVM:', error);
      toast({
        title: 'Error',
        description: `Failed to delete MicroVM: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setActionInProgress(null);
    }
  };

  // Open terminal for MicroVM
  const openTerminal = (id: string) => {
    // Set container type to microvm
    setContainerType('microvm');
    
    // Call onSelectVM callback if provided
    if (onSelectVM) {
      onSelectVM(id);
    }
  };

  // Fetch MicroVMs on mount
  useEffect(() => {
    fetchMicroVMs();
  }, [projectId]);

  return (
    <div className={`microvm-manager ${className}`}>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>MicroVMs</CardTitle>
            <CardDescription>
              Manage your MicroVMs
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshMicroVMs}
              disabled={refreshing}
            >
              {refreshing ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
            
            <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  New MicroVM
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New MicroVM</DialogTitle>
                  <DialogDescription>
                    Configure and create a new MicroVM
                  </DialogDescription>
                </DialogHeader>
                
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">
                      Name
                    </Label>
                    <Input
                      id="name"
                      value={newVmName}
                      onChange={(e) => setNewVmName(e.target.value)}
                      className="col-span-3"
                    />
                  </div>
                  
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="memory" className="text-right">
                      Memory (MB)
                    </Label>
                    <Input
                      id="memory"
                      type="number"
                      value={newVmMemory}
                      onChange={(e) => setNewVmMemory(e.target.value)}
                      className="col-span-3"
                    />
                  </div>
                  
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="cpus" className="text-right">
                      CPUs
                    </Label>
                    <Input
                      id="cpus"
                      type="number"
                      value={newVmCpus}
                      onChange={(e) => setNewVmCpus(e.target.value)}
                      className="col-span-3"
                    />
                  </div>
                  
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="image" className="text-right">
                      Image
                    </Label>
                    <Select
                      value={newVmImage}
                      onValueChange={setNewVmImage}
                    >
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select an image" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ubuntu-22.04">Ubuntu 22.04</SelectItem>
                        <SelectItem value="ubuntu-20.04">Ubuntu 20.04</SelectItem>
                        <SelectItem value="debian-11">Debian 11</SelectItem>
                        <SelectItem value="alpine-3.16">Alpine 3.16</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <DialogFooter>
                  <Button
                    onClick={createMicroVM}
                    disabled={creating || !newVmName}
                  >
                    {creating ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      'Create'
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {loading ? (
            <div className="space-y-2">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : microvms.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No MicroVMs found</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => setCreateDialogOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create MicroVM
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>State</TableHead>
                  <TableHead>Project</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {microvms.map((vm) => (
                  <TableRow key={vm.id}>
                    <TableCell className="font-medium">{vm.name}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          vm.state === 'RUNNING'
                            ? 'success'
                            : vm.state === 'STOPPED'
                            ? 'secondary'
                            : 'outline'
                        }
                      >
                        {vm.state}
                      </Badge>
                    </TableCell>
                    <TableCell>{vm.projectId || '-'}</TableCell>
                    <TableCell>{new Date(vm.createdAt).toLocaleString()}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        {vm.state === 'RUNNING' ? (
                          <>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => stopMicroVM(vm.id)}
                              disabled={actionInProgress === vm.id}
                            >
                              {actionInProgress === vm.id ? (
                                <RefreshCw className="h-4 w-4 animate-spin" />
                              ) : (
                                <Square className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => restartMicroVM(vm.id)}
                              disabled={actionInProgress === vm.id}
                            >
                              {actionInProgress === vm.id ? (
                                <RefreshCw className="h-4 w-4 animate-spin" />
                              ) : (
                                <RotateCw className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => openTerminal(vm.id)}
                            >
                              <TerminalIcon className="h-4 w-4" />
                            </Button>
                          </>
                        ) : (
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => startMicroVM(vm.id)}
                            disabled={actionInProgress === vm.id}
                          >
                            {actionInProgress === vm.id ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => deleteMicroVM(vm.id)}
                          disabled={actionInProgress === vm.id}
                        >
                          {actionInProgress === vm.id ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
