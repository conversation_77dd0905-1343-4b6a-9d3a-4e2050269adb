"use client";

import * as React from "react";
import { useState, useEffect, useCallback } from "react";
import { cn } from "@/lib/utils";
import { FileTree, FileNode } from "@/components/file-explorer/file-tree";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { 
  File, 
  Save, 
  X, 
  Edit, 
  Copy, 
  Download,
  Trash2,
  Folder,
  FolderPlus,
  FilePlus,
  RefreshCw,
  Loader2,
  ExternalLink
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface DirectVmFileExplorerProps {
  className?: string;
  vmId?: string;
  rootPath?: string;
  readOnly?: boolean;
}

export function DirectVmFileExplorer({
  className,
  vmId,
  rootPath = "/",
  readOnly = false
}: DirectVmFileExplorerProps) {
  // State variables
  const [files, setFiles] = useState<FileNode[]>([]);
  const [selectedFilePath, setSelectedFilePath] = useState<string | null>(null);
  const [fileContent, setFileContent] = useState<string | null>(null);
  const [fileLanguage, setFileLanguage] = useState<string>("plaintext");
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isCreatingFile, setIsCreatingFile] = useState(false);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newItemName, setNewItemName] = useState("");
  const [newItemParentPath, setNewItemParentPath] = useState(rootPath);
  const [currentPath, setCurrentPath] = useState(rootPath);

  // Fetch file listing
  const fetchFiles = useCallback(async () => {
    if (!vmId) return;

    setIsLoading(true);
    try {
      console.log(`Fetching files for VM ${vmId} at path ${currentPath}`);
      const response = await fetch("/api/containerization/firecracker/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "list",
          path: currentPath
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`Failed to fetch files: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      console.log(`Received ${data.contents?.length || 0} items from API`);
      
      // Transform the file list into a nested tree structure
      const fileTree = buildFileTree(data.contents || [], currentPath);
      setFiles(fileTree);
    } catch (error) {
      console.error("Error fetching files:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch files",
        variant: "destructive"
      });
      // Set an empty file tree on error
      setFiles([{
        id: 'root',
        name: currentPath.split('/').pop() || 'Root',
        path: currentPath,
        type: 'directory',
        children: []
      }]);
    } finally {
      setIsLoading(false);
    }
  }, [vmId, currentPath]);

  // Transform flat file list into nested tree structure
  const buildFileTree = (files: any[], basePath: string): FileNode[] => {
    const root: FileNode = {
      id: 'root',
      name: basePath.split('/').pop() || 'Root',
      path: basePath,
      type: 'directory',
      children: []
    };

    // Filter out "." and ".." entries
    const filteredFiles = files.filter(file => file.name !== '.' && file.name !== '..');
    
    // Sort by type (directories first) then name
    filteredFiles.sort((a, b) => {
      if (a.type === 'directory' && b.type !== 'directory') return -1;
      if (a.type !== 'directory' && b.type === 'directory') return 1;
      return a.name.localeCompare(b.name);
    });
    
    // Add all files and directories directly to root for now
    // This simplifies the view and makes it easier to navigate
    root.children = filteredFiles.map(file => ({
      id: `${file.type}-${file.path}`,
      name: file.name,
      path: file.path,
      type: file.type === 'directory' ? 'directory' : 'file',
      size: file.size,
      modifiedDate: file.modified
    }));
    
    return [root];
  };

  // Fetch file content
  const fetchFileContent = useCallback(async (filePath: string) => {
    if (!vmId) return;

    setIsLoading(true);
    try {
      const response = await fetch("/api/containerization/firecracker/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "read",
          path: filePath
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch file content: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      setFileContent(data.content || "");
      
      // Set language based on file extension
      const fileExt = filePath.split('.').pop()?.toLowerCase() || "";
      setFileLanguage(getLanguageFromExtension(fileExt));
      
      // Set edit content to match the file content
      setEditContent(data.content || "");
      
    } catch (error) {
      console.error("Error fetching file content:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch file content",
        variant: "destructive"
      });
      setFileContent(null);
    } finally {
      setIsLoading(false);
    }
  }, [vmId]);

  // Map file extensions to languages for syntax highlighting
  const getLanguageFromExtension = (ext: string): string => {
    const langMap: Record<string, string> = {
      js: "javascript",
      jsx: "jsx",
      ts: "typescript",
      tsx: "tsx",
      html: "html",
      css: "css",
      json: "json",
      md: "markdown",
      py: "python",
      go: "go",
      rs: "rust",
      sh: "bash",
      c: "c",
      cpp: "cpp",
      java: "java",
      php: "php",
    };
    return langMap[ext] || "plaintext";
  };

  // Save file content
  const saveFile = async () => {
    if (!vmId || !selectedFilePath) return;

    setIsLoading(true);
    try {
      const response = await fetch("/api/containerization/firecracker/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "write",
          path: selectedFilePath,
          content: editContent
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to save file: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      setFileContent(editContent);
      setIsEditing(false);
      toast({
        title: "File Saved",
        description: `Successfully saved ${selectedFilePath}`,
      });
    } catch (error) {
      console.error("Error saving file:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save file",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Delete file or directory
  const deleteItem = async () => {
    if (!vmId || !selectedFilePath) return;

    if (!confirm(`Are you sure you want to delete ${selectedFilePath}?`)) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/containerization/firecracker/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "delete",
          path: selectedFilePath
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to delete item: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      setSelectedFilePath(null);
      setFileContent(null);
      setRefreshTrigger(prev => prev + 1); // Trigger a refresh
      toast({
        title: "Item Deleted",
        description: `Successfully deleted ${selectedFilePath}`,
      });
    } catch (error) {
      console.error("Error deleting item:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete item",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new file
  const createNewFile = async () => {
    if (!vmId || !newItemName) return;

    setIsLoading(true);
    try {
      const filePath = `${newItemParentPath}/${newItemName}`.replace(/\/\//g, '/');
      
      const response = await fetch("/api/containerization/firecracker/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "write",
          path: filePath,
          content: ""
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to create file: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      setIsCreatingFile(false);
      setNewItemName("");
      setRefreshTrigger(prev => prev + 1); // Trigger a refresh
      toast({
        title: "File Created",
        description: `Successfully created ${filePath}`,
      });
    } catch (error) {
      console.error("Error creating file:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create file",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new directory
  const createNewFolder = async () => {
    if (!vmId || !newItemName) return;

    setIsLoading(true);
    try {
      const dirPath = `${newItemParentPath}/${newItemName}`.replace(/\/\//g, '/');
      
      const response = await fetch("/api/containerization/firecracker/filesystem", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmId,
          operation: "mkdir",
          path: dirPath
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to create directory: ${response.status}`);
      }

      const data = await response.json();
      if (data.error) {
        throw new Error(data.error);
      }

      setIsCreatingFolder(false);
      setNewItemName("");
      setRefreshTrigger(prev => prev + 1); // Trigger a refresh
      toast({
        title: "Folder Created",
        description: `Successfully created ${dirPath}`,
      });
    } catch (error) {
      console.error("Error creating directory:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create directory",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle file/directory selection
  const handleFileSelect = (file: FileNode) => {
    if (file.type === 'file') {
      setSelectedFilePath(file.path);
      fetchFileContent(file.path);
    } else if (file.type === 'directory') {
      // Navigate into the directory
      setCurrentPath(file.path);
      setSelectedFilePath(null);
      setFileContent(null);
    }
  };

  // Handle going up one directory
  const goUpDirectory = () => {
    if (currentPath === '/' || currentPath === rootPath) return;
    
    const parts = currentPath.split('/').filter(Boolean);
    parts.pop();
    const newPath = parts.length ? `/${parts.join('/')}` : '/';
    setCurrentPath(newPath);
  };

  // Copy file content to clipboard
  const handleCopyClick = () => {
    if (fileContent) {
      navigator.clipboard.writeText(fileContent);
      toast({
        title: "Copied",
        description: "File content copied to clipboard",
      });
    }
  };

  // Download file
  const handleDownloadClick = () => {
    if (fileContent && selectedFilePath) {
      const blob = new Blob([fileContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = selectedFilePath.split('/').pop() || 'download.txt';
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }
  };

  // Refresh file listing
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Load files on mount and when dependencies change
  useEffect(() => {
    fetchFiles();
  }, [fetchFiles, refreshTrigger]);

  // Breadcrumb navigation
  const renderBreadcrumbs = () => {
    const parts = currentPath.split('/').filter(Boolean);
    if (parts.length === 0) {
      return (
        <div className="flex items-center text-sm">
          <span className="font-medium">/</span>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-1 text-sm overflow-x-auto">
        <button 
          className="hover:underline"
          onClick={() => setCurrentPath('/')}
        >
          /
        </button>
        {parts.map((part, i) => (
          <React.Fragment key={i}>
            <span>/</span>
            <button
              className="hover:underline truncate max-w-[100px]"
              onClick={() => {
                const path = `/${parts.slice(0, i + 1).join('/')}`;
                setCurrentPath(path);
              }}
            >
              {part}
            </button>
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header with actions */}
      <div className="flex items-center justify-between p-2 border-b">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={goUpDirectory}
            title="Go up one directory"
            disabled={currentPath === '/' || currentPath === rootPath}
          >
            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.5 11L3 6.5L7.5 2M3 6.5H12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Button>
          <div className="overflow-hidden">
            {renderBreadcrumbs()}
          </div>
        </div>
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          {!readOnly && (
            <>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setIsCreatingFile(true);
                  setNewItemParentPath(currentPath);
                }}
                title="New File"
              >
                <FilePlus className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setIsCreatingFolder(true);
                  setNewItemParentPath(currentPath);
                }}
                title="New Folder"
              >
                <FolderPlus className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-0 flex-grow overflow-hidden">
        {/* File tree panel */}
        <div className="border-r overflow-auto h-full">
          {isLoading && files.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <ScrollArea className="h-full">
              {files.length > 0 && files[0].children && files[0].children.length === 0 ? (
                <div className="p-4 text-muted-foreground text-center">
                  <p>No files found in this directory.</p>
                </div>
              ) : (
                <div className="p-2">
                  {files.length > 0 && files[0].children?.map((file, index) => (
                    <div
                      key={file.id || index}
                      className={cn(
                        "flex items-center gap-2 px-2 py-1 rounded cursor-pointer hover:bg-accent group",
                        selectedFilePath === file.path && "bg-accent"
                      )}
                      onClick={() => handleFileSelect(file)}
                    >
                      {file.type === 'directory' ? (
                        <Folder className="h-4 w-4 text-blue-500" />
                      ) : (
                        <File className="h-4 w-4 text-muted-foreground" />
                      )}
                      <span className="truncate flex-grow">{file.name}</span>
                      {file.size !== undefined && (
                        <span className="text-xs text-muted-foreground">
                          {formatFileSize(file.size)}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          )}
        </div>

        {/* File content panel */}
        <div className="overflow-auto h-full">
          {selectedFilePath ? (
            isLoading && fileContent === null ? (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="flex flex-col h-full">
                {/* File toolbar */}
                <div className="flex items-center justify-between p-2 border-b bg-muted/40">
                  <div className="truncate flex-grow">
                    <span className="text-sm font-medium">{selectedFilePath.split('/').pop()}</span>
                  </div>
                  <div className="flex gap-1">
                    {!readOnly && (
                      <>
                        {isEditing ? (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={saveFile}
                              disabled={isLoading}
                            >
                              {isLoading ? (
                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              ) : (
                                <Save className="h-3 w-3 mr-1" />
                              )}
                              Save
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setIsEditing(false);
                                setEditContent(fileContent || "");
                              }}
                            >
                              <X className="h-3 w-3 mr-1" />
                              Cancel
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setIsEditing(true)}
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={deleteItem}
                              disabled={isLoading}
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              Delete
                            </Button>
                          </>
                        )}
                      </>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCopyClick}
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleDownloadClick}
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>

                {/* File content */}
                <div className="flex-grow overflow-auto">
                  {isEditing ? (
                    <textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      className="w-full h-full p-4 font-mono text-sm focus:outline-none resize-none"
                      spellCheck={false}
                    />
                  ) : (
                    <SyntaxHighlighter
                      language={fileLanguage}
                      style={vscDarkPlus}
                      className="!m-0 !bg-transparent h-full"
                      customStyle={{
                        margin: 0,
                        padding: '1rem',
                        height: '100%',
                        fontSize: '0.875rem',
                      }}
                    >
                      {fileContent || ""}
                    </SyntaxHighlighter>
                  )}
                </div>
              </div>
            )
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground p-4">
              <Folder className="h-10 w-10 mb-2" />
              <p>Select a file to view or edit</p>
              {!readOnly && (
                <div className="flex gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsCreatingFile(true);
                      setNewItemParentPath(currentPath);
                    }}
                  >
                    <FilePlus className="h-4 w-4 mr-1" />
                    New File
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsCreatingFolder(true);
                      setNewItemParentPath(currentPath);
                    }}
                  >
                    <FolderPlus className="h-4 w-4 mr-1" />
                    New Folder
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Create file modal */}
      {isCreatingFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-96">
            <CardHeader>
              <CardTitle className="text-lg">Create New File</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">
                  Location: <span className="font-mono">{newItemParentPath}</span>
                </div>
                <Input
                  placeholder="File name (e.g., example.txt)"
                  value={newItemName}
                  onChange={(e) => setNewItemName(e.target.value)}
                  autoFocus
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="ghost"
                onClick={() => {
                  setIsCreatingFile(false);
                  setNewItemName("");
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={createNewFile}
                disabled={!newItemName || isLoading}
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : null}
                Create File
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}

      {/* Create folder modal */}
      {isCreatingFolder && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-96">
            <CardHeader>
              <CardTitle className="text-lg">Create New Folder</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">
                  Location: <span className="font-mono">{newItemParentPath}</span>
                </div>
                <Input
                  placeholder="Folder name (e.g., new-folder)"
                  value={newItemName}
                  onChange={(e) => setNewItemName(e.target.value)}
                  autoFocus
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="ghost"
                onClick={() => {
                  setIsCreatingFolder(false);
                  setNewItemName("");
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={createNewFolder}
                disabled={!newItemName || isLoading}
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : null}
                Create Folder
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  );
}

// Helper to format file size
function formatFileSize(bytes: number): string {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
} 