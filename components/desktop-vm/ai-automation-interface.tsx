/**
 * AI Automation Interface Component
 * 
 * Provides AI-powered automation controls for desktop VM interactions,
 * including screenshot capture, input automation, and session recording.
 */

"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Bot, 
  Camera, 
  MousePointer, 
  Keyboard, 
  Video, 
  Play, 
  Square, 
  Download,
  Upload,
  RefreshCw,
  Loader2,
  AlertCircle,
  CheckCircle,
  Eye,
  Target,
  Zap,
  History,
  Settings,
  Code,
  Image as ImageIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import { useAiDesktopAutomation } from '@/lib/ai-desktop-automation/hooks/use-ai-desktop-automation';
import { AutomationSession, AutomationAction, ScreenshotData } from '@/lib/ai-desktop-automation/types';

interface AiAutomationInterfaceProps {
  vmId: string;
  className?: string;
  onSessionStart?: (session: AutomationSession) => void;
  onSessionEnd?: (session: AutomationSession) => void;
  onActionExecuted?: (action: AutomationAction) => void;
}

export function AiAutomationInterface({
  vmId,
  className,
  onSessionStart,
  onSessionEnd,
  onActionExecuted,
}: AiAutomationInterfaceProps) {
  // State management
  const [activeTab, setActiveTab] = useState('control');
  const [automationPrompt, setAutomationPrompt] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [lastScreenshot, setLastScreenshot] = useState<ScreenshotData | null>(null);
  const [actionHistory, setActionHistory] = useState<AutomationAction[]>([]);

  // Custom hooks
  const {
    isAutomationActive,
    currentSession,
    automationError,
    startAutomation,
    stopAutomation,
    executeAutomationPrompt,
    takeScreenshot,
    executeMouseAction,
    executeKeyboardAction,
    startSessionRecording,
    stopSessionRecording,
    getSessionHistory,
    exportSession,
    importSession,
  } = useAiDesktopAutomation(vmId);

  // Load action history on component mount
  useEffect(() => {
    loadActionHistory();
  }, [vmId]);

  // Load action history
  const loadActionHistory = useCallback(async () => {
    try {
      const history = await getSessionHistory();
      setActionHistory(history);
    } catch (error: any) {
      console.error('Failed to load action history:', error);
    }
  }, [getSessionHistory]);

  // Handle automation start
  const handleStartAutomation = useCallback(async () => {
    try {
      const session = await startAutomation();
      
      if (onSessionStart) {
        onSessionStart(session);
      }
      
      toast({
        title: "AI Automation Started",
        description: "AI automation session has been started.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to start automation: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [startAutomation, onSessionStart]);

  // Handle automation stop
  const handleStopAutomation = useCallback(async () => {
    try {
      const session = await stopAutomation();
      
      if (onSessionEnd) {
        onSessionEnd(session);
      }
      
      toast({
        title: "AI Automation Stopped",
        description: "AI automation session has been stopped.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to stop automation: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [stopAutomation, onSessionEnd]);

  // Handle automation prompt execution
  const handleExecutePrompt = useCallback(async () => {
    if (!automationPrompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter an automation prompt.",
        variant: "destructive",
      });
      return;
    }

    setIsExecuting(true);
    
    try {
      const actions = await executeAutomationPrompt(automationPrompt);
      
      // Add actions to history
      setActionHistory(prev => [...prev, ...actions]);
      
      // Notify parent
      actions.forEach(action => {
        if (onActionExecuted) {
          onActionExecuted(action);
        }
      });
      
      toast({
        title: "Automation Executed",
        description: `Executed ${actions.length} automation actions.`,
      });
      
      // Clear prompt
      setAutomationPrompt('');
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to execute automation: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  }, [automationPrompt, executeAutomationPrompt, onActionExecuted]);

  // Handle screenshot capture
  const handleTakeScreenshot = useCallback(async () => {
    try {
      const screenshot = await takeScreenshot();
      setLastScreenshot(screenshot);
      
      toast({
        title: "Screenshot Captured",
        description: "Desktop screenshot has been captured successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to capture screenshot: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [takeScreenshot]);

  // Handle mouse action
  const handleMouseAction = useCallback(async (action: 'click' | 'double-click' | 'right-click', x: number, y: number) => {
    try {
      const actionResult = await executeMouseAction(action, x, y);
      
      // Add to history
      setActionHistory(prev => [...prev, actionResult]);
      
      if (onActionExecuted) {
        onActionExecuted(actionResult);
      }
      
      toast({
        title: "Mouse Action Executed",
        description: `Executed ${action} at (${x}, ${y})`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to execute mouse action: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [executeMouseAction, onActionExecuted]);

  // Handle keyboard action
  const handleKeyboardAction = useCallback(async (text: string, keys?: string[]) => {
    try {
      const actionResult = await executeKeyboardAction(text, keys);
      
      // Add to history
      setActionHistory(prev => [...prev, actionResult]);
      
      if (onActionExecuted) {
        onActionExecuted(actionResult);
      }
      
      toast({
        title: "Keyboard Action Executed",
        description: `Typed: ${text}`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to execute keyboard action: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [executeKeyboardAction, onActionExecuted]);

  // Handle session export
  const handleExportSession = useCallback(async () => {
    if (!currentSession) return;
    
    try {
      const sessionData = await exportSession(currentSession.id);
      
      // Create download link
      const blob = new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `automation-session-${currentSession.id}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Session Exported",
        description: "Automation session has been exported successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to export session: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [currentSession, exportSession]);

  // Format action type for display
  const formatActionType = (type: string) => {
    return type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  // Get action icon
  const getActionIcon = (type: string) => {
    switch (type) {
      case 'mouse_click':
      case 'mouse_double_click':
      case 'mouse_right_click':
        return <MousePointer className="h-4 w-4" />;
      case 'keyboard_type':
      case 'keyboard_key':
        return <Keyboard className="h-4 w-4" />;
      case 'screenshot':
        return <Camera className="h-4 w-4" />;
      case 'wait':
        return <Clock className="h-4 w-4" />;
      default:
        return <Zap className="h-4 w-4" />;
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <h3 className="text-lg font-semibold">AI Automation</h3>
            {isAutomationActive && (
              <Badge variant="default" className="animate-pulse">
                Active
              </Badge>
            )}
          </div>
          
          {currentSession && (
            <div className="text-sm text-muted-foreground">
              Session: {currentSession.id.slice(0, 8)}...
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {!isAutomationActive ? (
            <Button onClick={handleStartAutomation} size="sm">
              <Play className="h-4 w-4 mr-2" />
              Start Session
            </Button>
          ) : (
            <Button onClick={handleStopAutomation} size="sm" variant="outline">
              <Square className="h-4 w-4 mr-2" />
              Stop Session
            </Button>
          )}
          
          <Button onClick={handleTakeScreenshot} size="sm" variant="outline">
            <Camera className="h-4 w-4 mr-2" />
            Screenshot
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {automationError && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Automation Error</span>
            </div>
            <p className="text-sm text-muted-foreground mt-1">{automationError}</p>
          </CardContent>
        </Card>
      )}

      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="control">Control</TabsTrigger>
          <TabsTrigger value="screenshot">Screenshot</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="control" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span>AI Automation Control</span>
              </CardTitle>
              <CardDescription>
                Describe what you want the AI to do on the desktop, and it will execute the actions automatically.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="prompt">Automation Prompt</Label>
                <Textarea
                  id="prompt"
                  placeholder="e.g., Open a web browser, navigate to google.com, and search for 'AI automation'"
                  value={automationPrompt}
                  onChange={(e) => setAutomationPrompt(e.target.value)}
                  rows={3}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Button 
                  onClick={handleExecutePrompt} 
                  disabled={isExecuting || !automationPrompt.trim() || !isAutomationActive}
                  className="flex-1"
                >
                  {isExecuting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Zap className="h-4 w-4 mr-2" />
                  )}
                  Execute Automation
                </Button>
                
                <Button 
                  onClick={() => setAutomationPrompt('')} 
                  variant="outline"
                  disabled={!automationPrompt.trim()}
                >
                  Clear
                </Button>
              </div>
              
              {!isAutomationActive && (
                <div className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                  <AlertCircle className="h-4 w-4 inline mr-2" />
                  Start an automation session to enable AI control.
                </div>
              )}
            </CardContent>
          </Card>

          {/* Manual Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>Manual Controls</span>
              </CardTitle>
              <CardDescription>
                Execute individual actions manually for testing and debugging.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Mouse Actions</Label>
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleMouseAction('click', 100, 100)}
                      disabled={!isAutomationActive}
                    >
                      <MousePointer className="h-3 w-3 mr-1" />
                      Click
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleMouseAction('double-click', 100, 100)}
                      disabled={!isAutomationActive}
                    >
                      Double
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleMouseAction('right-click', 100, 100)}
                      disabled={!isAutomationActive}
                    >
                      Right
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>Keyboard Actions</Label>
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleKeyboardAction('Hello World')}
                      disabled={!isAutomationActive}
                    >
                      <Keyboard className="h-3 w-3 mr-1" />
                      Type
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleKeyboardAction('', ['ctrl', 'c'])}
                      disabled={!isAutomationActive}
                    >
                      Copy
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleKeyboardAction('', ['ctrl', 'v'])}
                      disabled={!isAutomationActive}
                    >
                      Paste
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="screenshot" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ImageIcon className="h-5 w-5" />
                <span>Screenshot Viewer</span>
              </CardTitle>
              <CardDescription>
                View and analyze desktop screenshots for AI automation.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {lastScreenshot ? (
                <div className="space-y-4">
                  <div className="border rounded-lg overflow-hidden">
                    <img 
                      src={lastScreenshot.dataUrl} 
                      alt="Desktop Screenshot"
                      className="w-full h-auto max-h-96 object-contain"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>
                      Captured: {new Date(lastScreenshot.timestamp).toLocaleString()}
                    </span>
                    <span>
                      {lastScreenshot.width} × {lastScreenshot.height}
                    </span>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleTakeScreenshot}>
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Refresh
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <ImageIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground mb-4">No screenshot available</p>
                  <Button onClick={handleTakeScreenshot}>
                    <Camera className="h-4 w-4 mr-2" />
                    Take Screenshot
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <History className="h-5 w-5" />
                <span>Action History</span>
              </CardTitle>
              <CardDescription>
                View and replay automation actions from current and previous sessions.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {actionHistory.length > 0 ? (
                <ScrollArea className="h-96">
                  <div className="space-y-2">
                    {actionHistory.map((action, index) => (
                      <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                        <div className="flex-shrink-0">
                          {getActionIcon(action.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{formatActionType(action.type)}</span>
                            <Badge variant="outline" className="text-xs">
                              {action.status}
                            </Badge>
                          </div>
                          {action.description && (
                            <p className="text-sm text-muted-foreground truncate">
                              {action.description}
                            </p>
                          )}
                        </div>
                        <div className="flex-shrink-0 text-xs text-muted-foreground">
                          {new Date(action.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center py-12">
                  <History className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">No automation actions recorded yet</p>
                </div>
              )}
              
              {actionHistory.length > 0 && (
                <div className="flex space-x-2 mt-4">
                  <Button size="sm" variant="outline" onClick={handleExportSession}>
                    <Download className="h-3 w-3 mr-1" />
                    Export Session
                  </Button>
                  <Button size="sm" variant="outline" onClick={loadActionHistory}>
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Refresh
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Automation Settings</span>
              </CardTitle>
              <CardDescription>
                Configure AI automation behavior and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Settings className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">Settings interface coming soon...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
