/**
 * Desktop VM Workspace Component
 * 
 * A React component that provides a desktop VM interface using Apache Guacamole
 * for AI-powered computer use tasks with XFCE desktop environment.
 */

"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  Monitor, 
  Play, 
  Square, 
  RefreshCw, 
  Settings, 
  Camera,
  MousePointer,
  Keyboard,
  Video,
  Bot,
  Loader2,
  AlertCircle,
  CheckCircle,
  Maximize2,
  Minimize2,
  RotateCcw,
  Power,
  Terminal
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import { VMGuacamoleClient } from '@/components/vm-interface/VMGuacamoleClient';
import { useDesktopVmManager } from '@/lib/containerization/desktop-vm/hooks/use-desktop-vm-manager';
import { useAiDesktopAutomation } from '@/lib/ai-desktop-automation/hooks/use-ai-desktop-automation';
import { DesktopVmStatus, DesktopVmConfig, AutomationSession } from '@/lib/containerization/desktop-vm/types';

interface DesktopVmWorkspaceProps {
  vmId?: string;
  className?: string;
  height?: string | number;
  width?: string | number;
  autoStart?: boolean;
  enableAiAutomation?: boolean;
  onVmReady?: (vmId: string) => void;
  onAutomationStart?: (session: AutomationSession) => void;
  onAutomationEnd?: (session: AutomationSession) => void;
}

export function DesktopVmWorkspace({
  vmId: initialVmId,
  className,
  height = '100%',
  width = '100%',
  autoStart = true,
  enableAiAutomation = true,
  onVmReady,
  onAutomationStart,
  onAutomationEnd,
}: DesktopVmWorkspaceProps) {
  // State management
  const [activeTab, setActiveTab] = useState('desktop');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedResolution, setSelectedResolution] = useState('1920x1080');
  const [isRecording, setIsRecording] = useState(false);
  const [connectionToken, setConnectionToken] = useState<string | null>(null);
  
  // Refs
  const workspaceRef = useRef<HTMLDivElement>(null);
  
  // Custom hooks
  const {
    vmId,
    vmStatus,
    vmConfig,
    isLoading: vmLoading,
    error: vmError,
    createDesktopVm,
    startVm,
    stopVm,
    restartVm,
    deleteVm,
    updateVmConfig,
    getConnectionToken,
  } = useDesktopVmManager(initialVmId);
  
  const {
    isAutomationActive,
    currentSession,
    automationError,
    startAutomation,
    stopAutomation,
    takeScreenshot,
    executeMouseAction,
    executeKeyboardAction,
    startSessionRecording,
    stopSessionRecording,
  } = useAiDesktopAutomation(vmId);

  // Available resolutions
  const resolutions = [
    { value: '1920x1080', label: '1920x1080 (Full HD)' },
    { value: '1366x768', label: '1366x768 (HD)' },
    { value: '1280x720', label: '1280x720 (HD)' },
    { value: '1024x768', label: '1024x768 (XGA)' },
    { value: '800x600', label: '800x600 (SVGA)' },
  ];

  // Initialize VM on component mount
  useEffect(() => {
    if (!vmId && autoStart) {
      handleCreateVm();
    } else if (vmId && autoStart && vmStatus === 'stopped') {
      handleStartVm();
    }
  }, [vmId, autoStart, vmStatus]);

  // Get connection token when VM is ready
  useEffect(() => {
    if (vmId && vmStatus === 'running' && !connectionToken) {
      handleGetConnectionToken();
    }
  }, [vmId, vmStatus, connectionToken]);

  // Notify parent when VM is ready
  useEffect(() => {
    if (vmId && vmStatus === 'running' && connectionToken && onVmReady) {
      onVmReady(vmId);
    }
  }, [vmId, vmStatus, connectionToken, onVmReady]);

  // Handle VM creation
  const handleCreateVm = useCallback(async () => {
    try {
      const config: DesktopVmConfig = {
        name: `desktop-vm-${Date.now()}`,
        desktopEnvironment: 'xfce',
        resolution: selectedResolution,
        enableAudio: true,
        enableClipboard: true,
        enableFileSharing: true,
        aiAutomationEnabled: enableAiAutomation,
      };
      
      await createDesktopVm(config);
      toast({
        title: "Desktop VM Created",
        description: "Desktop VM has been created successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to create desktop VM: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [selectedResolution, enableAiAutomation, createDesktopVm]);

  // Handle VM start
  const handleStartVm = useCallback(async () => {
    if (!vmId) return;
    
    try {
      await startVm();
      toast({
        title: "Desktop VM Started",
        description: "Desktop VM is starting up...",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to start desktop VM: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [vmId, startVm]);

  // Handle VM stop
  const handleStopVm = useCallback(async () => {
    if (!vmId) return;
    
    try {
      await stopVm();
      setConnectionToken(null);
      toast({
        title: "Desktop VM Stopped",
        description: "Desktop VM has been stopped.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to stop desktop VM: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [vmId, stopVm]);

  // Handle VM restart
  const handleRestartVm = useCallback(async () => {
    if (!vmId) return;
    
    try {
      await restartVm();
      setConnectionToken(null);
      toast({
        title: "Desktop VM Restarted",
        description: "Desktop VM is restarting...",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to restart desktop VM: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [vmId, restartVm]);

  // Handle getting connection token
  const handleGetConnectionToken = useCallback(async () => {
    if (!vmId) return;
    
    try {
      const token = await getConnectionToken();
      setConnectionToken(token);
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to get connection token: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [vmId, getConnectionToken]);

  // Handle resolution change
  const handleResolutionChange = useCallback(async (resolution: string) => {
    setSelectedResolution(resolution);
    
    if (vmId && vmStatus === 'running') {
      try {
        await updateVmConfig({ resolution });
        toast({
          title: "Resolution Updated",
          description: `Desktop resolution changed to ${resolution}`,
        });
      } catch (error: any) {
        toast({
          title: "Error",
          description: `Failed to update resolution: ${error.message}`,
          variant: "destructive",
        });
      }
    }
  }, [vmId, vmStatus, updateVmConfig]);

  // Handle screenshot capture
  const handleTakeScreenshot = useCallback(async () => {
    if (!vmId) return;
    
    try {
      const screenshot = await takeScreenshot();
      toast({
        title: "Screenshot Captured",
        description: "Desktop screenshot has been captured successfully.",
      });
      return screenshot;
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to capture screenshot: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [vmId, takeScreenshot]);

  // Handle automation start
  const handleStartAutomation = useCallback(async () => {
    if (!vmId || !enableAiAutomation) return;
    
    try {
      const session = await startAutomation();
      setIsRecording(true);
      
      if (onAutomationStart) {
        onAutomationStart(session);
      }
      
      toast({
        title: "AI Automation Started",
        description: "AI automation session has been started.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to start automation: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [vmId, enableAiAutomation, startAutomation, onAutomationStart]);

  // Handle automation stop
  const handleStopAutomation = useCallback(async () => {
    if (!vmId || !isAutomationActive) return;
    
    try {
      const session = await stopAutomation();
      setIsRecording(false);
      
      if (onAutomationEnd) {
        onAutomationEnd(session);
      }
      
      toast({
        title: "AI Automation Stopped",
        description: "AI automation session has been stopped.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to stop automation: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [vmId, isAutomationActive, stopAutomation, onAutomationEnd]);

  // Handle fullscreen toggle
  const handleToggleFullscreen = useCallback(() => {
    if (!workspaceRef.current) return;
    
    if (!isFullscreen) {
      if (workspaceRef.current.requestFullscreen) {
        workspaceRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // Get status badge variant
  const getStatusBadgeVariant = (status: DesktopVmStatus) => {
    switch (status) {
      case 'running':
        return 'default';
      case 'starting':
      case 'stopping':
        return 'secondary';
      case 'stopped':
        return 'outline';
      case 'error':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Get status icon
  const getStatusIcon = (status: DesktopVmStatus) => {
    switch (status) {
      case 'running':
        return <CheckCircle className="h-4 w-4" />;
      case 'starting':
      case 'stopping':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'stopped':
        return <Square className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Square className="h-4 w-4" />;
    }
  };

  return (
    <div 
      ref={workspaceRef}
      className={cn("flex flex-col h-full bg-background", className)}
      style={{ height, width }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Monitor className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Desktop VM</h2>
            {vmId && (
              <Badge variant={getStatusBadgeVariant(vmStatus)} className="flex items-center space-x-1">
                {getStatusIcon(vmStatus)}
                <span className="capitalize">{vmStatus}</span>
              </Badge>
            )}
          </div>
          
          {enableAiAutomation && (
            <div className="flex items-center space-x-2">
              <Bot className="h-4 w-4" />
              <span className="text-sm text-muted-foreground">AI Automation</span>
              {isAutomationActive && (
                <Badge variant="default" className="animate-pulse">
                  Active
                </Badge>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Resolution Selector */}
          <Select value={selectedResolution} onValueChange={handleResolutionChange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {resolutions.map((resolution) => (
                <SelectItem key={resolution.value} value={resolution.value}>
                  {resolution.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Control Buttons */}
          {vmStatus === 'stopped' && (
            <Button onClick={handleStartVm} disabled={vmLoading} size="sm">
              <Play className="h-4 w-4 mr-2" />
              Start
            </Button>
          )}
          
          {vmStatus === 'running' && (
            <>
              <Button onClick={handleStopVm} disabled={vmLoading} size="sm" variant="outline">
                <Square className="h-4 w-4 mr-2" />
                Stop
              </Button>
              
              <Button onClick={handleRestartVm} disabled={vmLoading} size="sm" variant="outline">
                <RotateCcw className="h-4 w-4 mr-2" />
                Restart
              </Button>
            </>
          )}

          <Button onClick={handleToggleFullscreen} size="sm" variant="outline">
            {isFullscreen ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="desktop">Desktop</TabsTrigger>
            <TabsTrigger value="automation">AI Automation</TabsTrigger>
            <TabsTrigger value="terminal">Terminal</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="desktop" className="flex-1 mt-0">
            {vmStatus === 'running' && connectionToken ? (
              <VMGuacamoleClient
                vmId={vmId!}
                protocol="vnc"
                hostname="localhost"
                port={5901}
                width="100%"
                height="100%"
                autoConnect={true}
                connectionToken={connectionToken}
                className="h-full"
              />
            ) : (
              <div className="flex items-center justify-center h-full bg-muted/30">
                <div className="text-center">
                  {vmLoading ? (
                    <>
                      <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-primary" />
                      <p className="text-muted-foreground">
                        {vmStatus === 'starting' ? 'Starting desktop environment...' : 'Loading...'}
                      </p>
                    </>
                  ) : vmError ? (
                    <>
                      <AlertCircle className="h-12 w-12 mx-auto mb-4 text-destructive" />
                      <p className="text-destructive font-medium">Error</p>
                      <p className="text-muted-foreground">{vmError}</p>
                      <Button onClick={handleCreateVm} className="mt-4">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Retry
                      </Button>
                    </>
                  ) : !vmId ? (
                    <>
                      <Monitor className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-muted-foreground mb-4">No desktop VM available</p>
                      <Button onClick={handleCreateVm}>
                        <Power className="h-4 w-4 mr-2" />
                        Create Desktop VM
                      </Button>
                    </>
                  ) : (
                    <>
                      <Square className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-muted-foreground mb-4">Desktop VM is stopped</p>
                      <Button onClick={handleStartVm}>
                        <Play className="h-4 w-4 mr-2" />
                        Start Desktop VM
                      </Button>
                    </>
                  )}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="automation" className="flex-1 mt-0">
            {/* AI Automation Interface will be added here */}
            <div className="p-4">
              <p className="text-muted-foreground">AI Automation interface coming soon...</p>
            </div>
          </TabsContent>

          <TabsContent value="terminal" className="flex-1 mt-0">
            {/* Terminal Interface will be added here */}
            <div className="p-4">
              <p className="text-muted-foreground">Terminal interface coming soon...</p>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="flex-1 mt-0">
            {/* Settings Interface will be added here */}
            <div className="p-4">
              <p className="text-muted-foreground">Settings interface coming soon...</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
