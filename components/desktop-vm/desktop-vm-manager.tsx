/**
 * Desktop VM Manager Component
 * 
 * Manages multiple desktop VMs with lifecycle operations, monitoring,
 * and AI automation capabilities.
 */

"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Monitor, 
  Plus, 
  Play, 
  Square, 
  Trash2, 
  RefreshCw, 
  Settings, 
  Bot,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock,
  HardDrive,
  Cpu,
  MemoryStick,
  Network,
  Eye,
  Edit,
  Copy
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import { useDesktopVmManager } from '@/lib/containerization/desktop-vm/hooks/use-desktop-vm-manager';
import { DesktopVm, DesktopVmConfig, DesktopVmStatus } from '@/lib/containerization/desktop-vm/types';
import { formatDistanceToNow } from 'date-fns';

interface DesktopVmManagerProps {
  className?: string;
  onVmSelect?: (vmId: string) => void;
  selectedVmId?: string;
}

export function DesktopVmManager({
  className,
  onVmSelect,
  selectedVmId,
}: DesktopVmManagerProps) {
  // State management
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingVm, setEditingVm] = useState<DesktopVm | null>(null);
  const [newVmConfig, setNewVmConfig] = useState<Partial<DesktopVmConfig>>({
    name: '',
    desktopEnvironment: 'xfce',
    resolution: '1920x1080',
    enableAudio: true,
    enableClipboard: true,
    enableFileSharing: true,
    aiAutomationEnabled: true,
  });

  // Custom hooks
  const {
    vms,
    isLoading,
    error,
    createDesktopVm,
    startVm,
    stopVm,
    deleteVm,
    updateVmConfig,
    refreshVms,
  } = useDesktopVmManager();

  // Refresh VMs on component mount
  useEffect(() => {
    refreshVms();
  }, [refreshVms]);

  // Handle VM creation
  const handleCreateVm = useCallback(async () => {
    if (!newVmConfig.name?.trim()) {
      toast({
        title: "Error",
        description: "VM name is required.",
        variant: "destructive",
      });
      return;
    }

    try {
      const config: DesktopVmConfig = {
        name: newVmConfig.name,
        desktopEnvironment: newVmConfig.desktopEnvironment || 'xfce',
        resolution: newVmConfig.resolution || '1920x1080',
        enableAudio: newVmConfig.enableAudio ?? true,
        enableClipboard: newVmConfig.enableClipboard ?? true,
        enableFileSharing: newVmConfig.enableFileSharing ?? true,
        aiAutomationEnabled: newVmConfig.aiAutomationEnabled ?? true,
      };

      await createDesktopVm(config);
      setIsCreateDialogOpen(false);
      setNewVmConfig({
        name: '',
        desktopEnvironment: 'xfce',
        resolution: '1920x1080',
        enableAudio: true,
        enableClipboard: true,
        enableFileSharing: true,
        aiAutomationEnabled: true,
      });

      toast({
        title: "Desktop VM Created",
        description: `Desktop VM "${config.name}" has been created successfully.`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to create desktop VM: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [newVmConfig, createDesktopVm]);

  // Handle VM start
  const handleStartVm = useCallback(async (vmId: string) => {
    try {
      await startVm(vmId);
      toast({
        title: "Desktop VM Started",
        description: "Desktop VM is starting up...",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to start desktop VM: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [startVm]);

  // Handle VM stop
  const handleStopVm = useCallback(async (vmId: string) => {
    try {
      await stopVm(vmId);
      toast({
        title: "Desktop VM Stopped",
        description: "Desktop VM has been stopped.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to stop desktop VM: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [stopVm]);

  // Handle VM deletion
  const handleDeleteVm = useCallback(async (vmId: string, vmName: string) => {
    if (!confirm(`Are you sure you want to delete the desktop VM "${vmName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteVm(vmId);
      toast({
        title: "Desktop VM Deleted",
        description: `Desktop VM "${vmName}" has been deleted.`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to delete desktop VM: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [deleteVm]);

  // Handle VM selection
  const handleVmSelect = useCallback((vmId: string) => {
    if (onVmSelect) {
      onVmSelect(vmId);
    }
  }, [onVmSelect]);

  // Handle VM edit
  const handleEditVm = useCallback((vm: DesktopVm) => {
    setEditingVm(vm);
    setIsEditDialogOpen(true);
  }, []);

  // Handle VM clone
  const handleCloneVm = useCallback((vm: DesktopVm) => {
    setNewVmConfig({
      name: `${vm.config.name} (Copy)`,
      desktopEnvironment: vm.config.desktopEnvironment,
      resolution: vm.config.resolution,
      enableAudio: vm.config.enableAudio,
      enableClipboard: vm.config.enableClipboard,
      enableFileSharing: vm.config.enableFileSharing,
      aiAutomationEnabled: vm.config.aiAutomationEnabled,
    });
    setIsCreateDialogOpen(true);
  }, []);

  // Get status badge variant
  const getStatusBadgeVariant = (status: DesktopVmStatus) => {
    switch (status) {
      case 'running':
        return 'default';
      case 'starting':
      case 'stopping':
        return 'secondary';
      case 'stopped':
        return 'outline';
      case 'error':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Get status icon
  const getStatusIcon = (status: DesktopVmStatus) => {
    switch (status) {
      case 'running':
        return <CheckCircle className="h-4 w-4" />;
      case 'starting':
      case 'stopping':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'stopped':
        return <Square className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Square className="h-4 w-4" />;
    }
  };

  // Available desktop environments
  const desktopEnvironments = [
    { value: 'xfce', label: 'XFCE (Lightweight)' },
    { value: 'gnome', label: 'GNOME (Full-featured)' },
    { value: 'kde', label: 'KDE Plasma (Modern)' },
    { value: 'lxde', label: 'LXDE (Minimal)' },
  ];

  // Available resolutions
  const resolutions = [
    { value: '1920x1080', label: '1920x1080 (Full HD)' },
    { value: '1366x768', label: '1366x768 (HD)' },
    { value: '1280x720', label: '1280x720 (HD)' },
    { value: '1024x768', label: '1024x768 (XGA)' },
    { value: '800x600', label: '800x600 (SVGA)' },
  ];

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Desktop VMs</h2>
          <p className="text-muted-foreground">
            Manage your desktop virtual machines for AI automation tasks
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button onClick={refreshVms} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create VM
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Create Desktop VM</DialogTitle>
                <DialogDescription>
                  Create a new desktop virtual machine for AI automation tasks.
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={newVmConfig.name || ''}
                    onChange={(e) => setNewVmConfig({ ...newVmConfig, name: e.target.value })}
                    className="col-span-3"
                    placeholder="My Desktop VM"
                  />
                </div>
                
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="desktop" className="text-right">
                    Desktop
                  </Label>
                  <Select
                    value={newVmConfig.desktopEnvironment || 'xfce'}
                    onValueChange={(value) => setNewVmConfig({ ...newVmConfig, desktopEnvironment: value as any })}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {desktopEnvironments.map((env) => (
                        <SelectItem key={env.value} value={env.value}>
                          {env.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="resolution" className="text-right">
                    Resolution
                  </Label>
                  <Select
                    value={newVmConfig.resolution || '1920x1080'}
                    onValueChange={(value) => setNewVmConfig({ ...newVmConfig, resolution: value })}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {resolutions.map((res) => (
                        <SelectItem key={res.value} value={res.value}>
                          {res.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateVm}>
                  Create VM
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* VM List */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-destructive" />
          <p className="text-destructive font-medium">Error loading desktop VMs</p>
          <p className="text-muted-foreground">{error}</p>
          <Button onClick={refreshVms} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      ) : vms.length === 0 ? (
        <div className="text-center py-12">
          <Monitor className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground mb-4">No desktop VMs found</p>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create your first Desktop VM
          </Button>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {vms.map((vm) => (
            <Card 
              key={vm.id} 
              className={cn(
                "cursor-pointer transition-colors hover:bg-muted/50",
                selectedVmId === vm.id && "ring-2 ring-primary"
              )}
              onClick={() => handleVmSelect(vm.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{vm.config.name}</CardTitle>
                  <Badge variant={getStatusBadgeVariant(vm.status)} className="flex items-center space-x-1">
                    {getStatusIcon(vm.status)}
                    <span className="capitalize">{vm.status}</span>
                  </Badge>
                </div>
                <CardDescription className="flex items-center space-x-2">
                  <Monitor className="h-4 w-4" />
                  <span>{vm.config.desktopEnvironment.toUpperCase()}</span>
                  <span>•</span>
                  <span>{vm.config.resolution}</span>
                  {vm.config.aiAutomationEnabled && (
                    <>
                      <span>•</span>
                      <Bot className="h-4 w-4" />
                      <span>AI</span>
                    </>
                  )}
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  {/* Resource Usage */}
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div className="flex items-center space-x-1">
                      <Cpu className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">CPU:</span>
                      <span>{vm.resources?.cpu || '0%'}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MemoryStick className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">RAM:</span>
                      <span>{vm.resources?.memory || '0MB'}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <HardDrive className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">Disk:</span>
                      <span>{vm.resources?.disk || '0GB'}</span>
                    </div>
                  </div>

                  {/* Last Activity */}
                  {vm.lastActivity && (
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>Last active {formatDistanceToNow(new Date(vm.lastActivity))} ago</span>
                    </div>
                  )}

                  <Separator />

                  {/* Actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1">
                      {vm.status === 'stopped' && (
                        <Button 
                          size="sm" 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleStartVm(vm.id);
                          }}
                        >
                          <Play className="h-3 w-3 mr-1" />
                          Start
                        </Button>
                      )}
                      
                      {vm.status === 'running' && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleStopVm(vm.id);
                          }}
                        >
                          <Square className="h-3 w-3 mr-1" />
                          Stop
                        </Button>
                      )}
                    </div>

                    <div className="flex items-center space-x-1">
                      <Button 
                        size="sm" 
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleVmSelect(vm.id);
                        }}
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                      
                      <Button 
                        size="sm" 
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditVm(vm);
                        }}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      
                      <Button 
                        size="sm" 
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCloneVm(vm);
                        }}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      
                      <Button 
                        size="sm" 
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteVm(vm.id, vm.config.name);
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
