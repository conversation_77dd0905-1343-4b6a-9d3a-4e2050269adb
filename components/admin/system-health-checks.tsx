"use client"

import { 
  <PERSON><PERSON><PERSON><PERSON>2, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>Circle,
  Refresh<PERSON><PERSON>,
  Info
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"

interface HealthCheck {
  id: string
  name: string
  status: "healthy" | "warning" | "error"
  details: string
}

interface SystemHealthChecksProps {
  checks: HealthCheck[]
}

export function SystemHealthChecks({ checks }: SystemHealthChecksProps) {
  const { toast } = useToast()
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [expandedChecks, setExpandedChecks] = useState<Record<string, boolean>>({})
  
  const toggleCheck = (checkId: string) => {
    setExpandedChecks(prev => ({
      ...prev,
      [checkId]: !prev[checkId]
    }))
  }
  
  const handleRefresh = async () => {
    setIsRefreshing(true)
    
    // In a real application, this would refresh the health checks
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setIsRefreshing(false)
    toast({
      title: "Health Checks Refreshed",
      description: "System health checks have been updated.",
    })
  }
  
  // Count checks by status
  const healthyCounts = checks.filter(check => check.status === "healthy").length
  const warningCounts = checks.filter(check => check.status === "warning").length
  const errorCounts = checks.filter(check => check.status === "error").length
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <div className="flex items-center">
            <CheckCircle2 className="h-4 w-4 text-green-500 mr-1" />
            <span className="text-sm">{healthyCounts} Healthy</span>
          </div>
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1" />
            <span className="text-sm">{warningCounts} Warnings</span>
          </div>
          <div className="flex items-center">
            <XCircle className="h-4 w-4 text-red-500 mr-1" />
            <span className="text-sm">{errorCounts} Errors</span>
          </div>
        </div>
        
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
        </Button>
      </div>
      
      <div className="space-y-2">
        {checks.map(check => (
          <div 
            key={check.id}
            className={cn(
              "p-3 rounded-md border transition-colors",
              check.status === "healthy" && "border-green-200 bg-green-50 dark:bg-green-950/20 dark:border-green-900",
              check.status === "warning" && "border-yellow-200 bg-yellow-50 dark:bg-yellow-950/20 dark:border-yellow-900",
              check.status === "error" && "border-red-200 bg-red-50 dark:bg-red-950/20 dark:border-red-900"
            )}
          >
            <div 
              className="flex items-center cursor-pointer"
              onClick={() => toggleCheck(check.id)}
            >
              {check.status === "healthy" && (
                <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              )}
              {check.status === "warning" && (
                <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0" />
              )}
              {check.status === "error" && (
                <XCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" />
              )}
              
              <div className="flex-1">
                <h4 className="font-medium">{check.name}</h4>
                <p className={cn(
                  "text-sm",
                  check.status === "healthy" && "text-green-700 dark:text-green-300",
                  check.status === "warning" && "text-yellow-700 dark:text-yellow-300",
                  check.status === "error" && "text-red-700 dark:text-red-300"
                )}>
                  {check.details}
                </p>
              </div>
              
              <Info className={cn(
                "h-4 w-4 ml-2 flex-shrink-0",
                expandedChecks[check.id] ? "opacity-100" : "opacity-50",
                check.status === "healthy" && "text-green-500",
                check.status === "warning" && "text-yellow-500",
                check.status === "error" && "text-red-500"
              )} />
            </div>
            
            {expandedChecks[check.id] && (
              <div className="mt-2 pt-2 border-t border-dashed text-sm space-y-1">
                <p className="font-medium">Diagnostic Information:</p>
                <p className="text-muted-foreground">
                  {check.status === "healthy" && "All checks passed successfully. No action required."}
                  {check.status === "warning" && "Warning condition detected. Monitor the situation and take action if it persists."}
                  {check.status === "error" && "Error condition detected. Immediate attention required."}
                </p>
                <p className="font-medium mt-2">Recommended Actions:</p>
                <p className="text-muted-foreground">
                  {check.status === "healthy" && "Continue regular monitoring."}
                  {check.status === "warning" && "Check system logs for more details. Consider scheduling maintenance."}
                  {check.status === "error" && "Check system logs immediately. Resolve the issue as soon as possible to prevent service disruption."}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
