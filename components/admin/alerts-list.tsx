"use client"

import { useState } from "react"
import { 
  <PERSON>ert<PERSON><PERSON>gle, 
  Bell, 
  CheckCircle2, 
  Clock,
  Filter,
  Search,
  XCircle
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface Alert {
  id: string
  severity: "low" | "medium" | "high" | "critical"
  message: string
  source: string
  timestamp: Date
  acknowledged: boolean
}

interface AlertsListProps {
  alerts: Alert[]
  onAcknowledge: (alertId: string) => void
}

export function AlertsList({ alerts, onAcknowledge }: AlertsListProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [severityFilter, setSeverityFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  
  // Filter alerts based on search and filters
  const filteredAlerts = alerts.filter(alert => {
    const matchesSearch = alert.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         alert.source.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesSeverity = severityFilter === "all" || alert.severity === severityFilter
    const matchesStatus = statusFilter === "all" || 
                         (statusFilter === "acknowledged" && alert.acknowledged) ||
                         (statusFilter === "unacknowledged" && !alert.acknowledged)
    
    return matchesSearch && matchesSeverity && matchesStatus
  })
  
  // Format relative time
  const formatRelativeTime = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    const diffHour = Math.floor(diffMin / 60)
    const diffDay = Math.floor(diffHour / 24)
    
    if (diffDay > 0) {
      return `${diffDay}d ago`
    } else if (diffHour > 0) {
      return `${diffHour}h ago`
    } else if (diffMin > 0) {
      return `${diffMin}m ago`
    } else {
      return `${diffSec}s ago`
    }
  }
  
  // Get severity icon and color
  const getSeverityDetails = (severity: string) => {
    switch (severity) {
      case "critical":
        return { icon: <XCircle className="h-4 w-4" />, color: "text-red-500 bg-red-100 dark:bg-red-950/50" }
      case "high":
        return { icon: <AlertTriangle className="h-4 w-4" />, color: "text-orange-500 bg-orange-100 dark:bg-orange-950/50" }
      case "medium":
        return { icon: <AlertTriangle className="h-4 w-4" />, color: "text-yellow-500 bg-yellow-100 dark:bg-yellow-950/50" }
      case "low":
        return { icon: <Bell className="h-4 w-4" />, color: "text-blue-500 bg-blue-100 dark:bg-blue-950/50" }
      default:
        return { icon: <Bell className="h-4 w-4" />, color: "text-gray-500 bg-gray-100 dark:bg-gray-800" }
    }
  }
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search alerts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        
        <div className="flex gap-2">
          <Select value={severityFilter} onValueChange={setSeverityFilter}>
            <SelectTrigger className="w-[130px]">
              <AlertTriangle className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Severity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Severities</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <CheckCircle2 className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="acknowledged">Acknowledged</SelectItem>
              <SelectItem value="unacknowledged">Unacknowledged</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {filteredAlerts.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No alerts found matching your filters.
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Severity</TableHead>
                <TableHead>Message</TableHead>
                <TableHead>Source</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAlerts.map(alert => {
                const { icon, color } = getSeverityDetails(alert.severity)
                
                return (
                  <TableRow key={alert.id}>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={cn("flex items-center gap-1", color)}
                      >
                        {icon}
                        <span className="capitalize">{alert.severity}</span>
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">{alert.message}</TableCell>
                    <TableCell>{alert.source}</TableCell>
                    <TableCell className="whitespace-nowrap">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatRelativeTime(alert.timestamp)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {alert.acknowledged ? (
                        <Badge variant="outline" className="bg-green-100 text-green-700 dark:bg-green-950/50 dark:text-green-400">
                          Acknowledged
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-yellow-100 text-yellow-700 dark:bg-yellow-950/50 dark:text-yellow-400">
                          Unacknowledged
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      {!alert.acknowledged && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => onAcknowledge(alert.id)}
                        >
                          <CheckCircle2 className="h-4 w-4 mr-2" />
                          Acknowledge
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
