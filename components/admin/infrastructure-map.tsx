"use client"

import { useState } from "react"
import { NodeInfo, VMInfo } from "@/lib/proxmox/types"
import { Card, CardContent } from "@/components/ui/card"
import { StatusBadge } from "@/components/proxmox/status-badge"
import { 
  Server, 
  HardDrive, 
  Box, 
  ChevronDown, 
  ChevronRight,
  Network
} from "lucide-react"
import { cn } from "@/lib/utils"

interface LxdContainer {
  id: string
  name: string
  status: string
  host: string
  ipAddress: string
  cpu: { usage: number, cores: number }
  memory: { used: number, total: number }
  disk: { used: number, total: number }
  uptime: number
}

interface InfrastructureMapProps {
  nodes: NodeInfo[]
  vms: VMInfo[]
  containers: LxdContainer[]
}

export function InfrastructureMap({ nodes, vms, containers }: InfrastructureMapProps) {
  const [expandedNodes, setExpandedNodes] = useState<Record<string, boolean>>(
    nodes.reduce((acc, node) => ({ ...acc, [node.id]: true }), {})
  )
  
  const [expandedVMs, setExpandedVMs] = useState<Record<string, boolean>>(
    vms.reduce((acc, vm) => ({ ...acc, [vm.id]: true }), {})
  )
  
  const toggleNode = (nodeId: string) => {
    setExpandedNodes(prev => ({
      ...prev,
      [nodeId]: !prev[nodeId]
    }))
  }
  
  const toggleVM = (vmId: string) => {
    setExpandedVMs(prev => ({
      ...prev,
      [vmId]: !prev[vmId]
    }))
  }
  
  // Group VMs by node
  const vmsByNode = vms.reduce((acc, vm) => {
    if (!acc[vm.node]) {
      acc[vm.node] = []
    }
    acc[vm.node].push(vm)
    return acc
  }, {} as Record<string, VMInfo[]>)
  
  // Group containers by VM
  const containersByVM = containers.reduce((acc, container) => {
    if (!acc[container.host]) {
      acc[container.host] = []
    }
    acc[container.host].push(container)
    return acc
  }, {} as Record<string, LxdContainer[]>)
  
  return (
    <div className="space-y-4">
      {nodes.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No nodes found. Please check your Proxmox connection.
        </div>
      ) : (
        nodes.map(node => (
          <div key={node.id} className="space-y-2">
            <div 
              className="flex items-center p-2 bg-muted/30 rounded-md cursor-pointer hover:bg-muted/50"
              onClick={() => toggleNode(node.id)}
            >
              {expandedNodes[node.id] ? (
                <ChevronDown className="h-4 w-4 mr-2 text-muted-foreground" />
              ) : (
                <ChevronRight className="h-4 w-4 mr-2 text-muted-foreground" />
              )}
              <Server className="h-5 w-5 mr-2 text-blue-500" />
              <span className="font-medium">{node.name}</span>
              <StatusBadge status={node.status} className="ml-2" size="sm" />
              <span className="ml-auto text-xs text-muted-foreground">
                {vmsByNode[node.id]?.length || 0} VMs
              </span>
            </div>
            
            {expandedNodes[node.id] && (
              <div className="pl-8 space-y-2">
                {!vmsByNode[node.id] || vmsByNode[node.id].length === 0 ? (
                  <div className="text-sm text-muted-foreground py-1">
                    No virtual machines on this node
                  </div>
                ) : (
                  vmsByNode[node.id].map(vm => (
                    <div key={vm.id} className="space-y-2">
                      <div 
                        className="flex items-center p-2 bg-muted/20 rounded-md cursor-pointer hover:bg-muted/30"
                        onClick={() => toggleVM(vm.id)}
                      >
                        {expandedVMs[vm.id] ? (
                          <ChevronDown className="h-4 w-4 mr-2 text-muted-foreground" />
                        ) : (
                          <ChevronRight className="h-4 w-4 mr-2 text-muted-foreground" />
                        )}
                        <HardDrive className="h-4 w-4 mr-2 text-purple-500" />
                        <span className="font-medium">{vm.name}</span>
                        <StatusBadge status={vm.status} className="ml-2" size="sm" />
                        <span className="ml-auto text-xs text-muted-foreground">
                          {containersByVM[vm.id]?.length || 0} Containers
                        </span>
                      </div>
                      
                      {expandedVMs[vm.id] && (
                        <div className="pl-8 space-y-2">
                          {!containersByVM[vm.id] || containersByVM[vm.id].length === 0 ? (
                            <div className="text-sm text-muted-foreground py-1">
                              No LXD containers on this VM
                            </div>
                          ) : (
                            containersByVM[vm.id].map(container => (
                              <div 
                                key={container.id}
                                className="flex items-center p-2 bg-muted/10 rounded-md"
                              >
                                <Box className="h-4 w-4 mr-2 text-green-500" />
                                <span className="font-medium">{container.name}</span>
                                <StatusBadge 
                                  status={container.status} 
                                  className="ml-2" 
                                  size="sm" 
                                />
                                <div className="ml-auto flex items-center">
                                  <Network className="h-3 w-3 mr-1 text-muted-foreground" />
                                  <span className="text-xs text-muted-foreground">
                                    {container.ipAddress}
                                  </span>
                                </div>
                              </div>
                            ))
                          )}
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        ))
      )}
      
      <div className="pt-4 text-xs text-muted-foreground">
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center">
            <Server className="h-4 w-4 mr-1 text-blue-500" />
            <span>Proxmox Node</span>
          </div>
          <div className="flex items-center">
            <HardDrive className="h-4 w-4 mr-1 text-purple-500" />
            <span>Virtual Machine</span>
          </div>
          <div className="flex items-center">
            <Box className="h-4 w-4 mr-1 text-green-500" />
            <span>LXD Container</span>
          </div>
        </div>
      </div>
    </div>
  )
}
