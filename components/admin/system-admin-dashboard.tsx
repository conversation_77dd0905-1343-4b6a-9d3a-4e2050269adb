"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  RefreshCw, 
  Server, 
  Box, 
  Network, 
  HardDrive, 
  Activity,
  AlertTriangle,
  CheckCircle2,
  Loader2
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useProxmoxContext } from "@/lib/contexts/proxmox-context"
import { VMStatus } from "@/lib/proxmox/types"
import { StatusBadge } from "@/components/proxmox/status-badge"
import { ResourceMeter } from "@/components/proxmox/resource-meter"
import { InfrastructureMap } from "@/components/admin/infrastructure-map"
import { LxdContainerList } from "@/components/admin/lxd-container-list"
import { SystemHealthChecks } from "@/components/admin/system-health-checks"
import { ResourceUsageChart } from "@/components/admin/resource-usage-chart"
import { AlertsList } from "@/components/admin/alerts-list"

// Mock data for LXD containers
const mockLxdContainers = [
  {
    id: "lxd-container-1",
    name: "web-server-1",
    status: "running",
    host: "vm-101",
    ipAddress: "**********",
    cpu: { usage: 0.25, cores: 2 },
    memory: { used: 512 * 1024 * 1024, total: 2048 * 1024 * 1024 },
    disk: { used: 5 * 1024 * 1024 * 1024, total: 20 * 1024 * 1024 * 1024 },
    uptime: 432000, // 5 days
  },
  {
    id: "lxd-container-2",
    name: "db-server-1",
    status: "running",
    host: "vm-102",
    ipAddress: "**********",
    cpu: { usage: 0.45, cores: 4 },
    memory: { used: 3072 * 1024 * 1024, total: 8192 * 1024 * 1024 },
    disk: { used: 80 * 1024 * 1024 * 1024, total: 200 * 1024 * 1024 * 1024 },
    uptime: 345600, // 4 days
  },
  {
    id: "lxd-container-3",
    name: "cache-server-1",
    status: "running",
    host: "vm-101",
    ipAddress: "**********",
    cpu: { usage: 0.15, cores: 1 },
    memory: { used: 256 * 1024 * 1024, total: 1024 * 1024 * 1024 },
    disk: { used: 2 * 1024 * 1024 * 1024, total: 10 * 1024 * 1024 * 1024 },
    uptime: 259200, // 3 days
  },
  {
    id: "lxd-container-4",
    name: "app-server-1",
    status: "stopped",
    host: "vm-103",
    ipAddress: "**********",
    cpu: { usage: 0, cores: 2 },
    memory: { used: 0, total: 2048 * 1024 * 1024 },
    disk: { used: 8 * 1024 * 1024 * 1024, total: 30 * 1024 * 1024 * 1024 },
    uptime: 0,
  },
  {
    id: "lxd-container-5",
    name: "monitoring-server",
    status: "running",
    host: "vm-102",
    ipAddress: "**********",
    cpu: { usage: 0.35, cores: 2 },
    memory: { used: 1536 * 1024 * 1024, total: 4096 * 1024 * 1024 },
    disk: { used: 12 * 1024 * 1024 * 1024, total: 50 * 1024 * 1024 * 1024 },
    uptime: 604800, // 7 days
  },
];

// Mock data for system health checks
const mockHealthChecks = [
  { id: "check-1", name: "Disk Space", status: "healthy", details: "All VMs have sufficient disk space" },
  { id: "check-2", name: "Memory Usage", status: "warning", details: "High memory usage on db-server-1" },
  { id: "check-3", name: "CPU Load", status: "healthy", details: "CPU load within normal parameters" },
  { id: "check-4", name: "Network Connectivity", status: "healthy", details: "All network connections are stable" },
  { id: "check-5", name: "LXD Cluster Status", status: "healthy", details: "LXD cluster is fully operational" },
  { id: "check-6", name: "Backup Status", status: "error", details: "Last backup failed for app-server-1" },
];

// Mock data for alerts
const mockAlerts = [
  { 
    id: "alert-1", 
    severity: "high", 
    message: "High memory usage on db-server-1", 
    source: "db-server-1", 
    timestamp: new Date(Date.now() - 30 * 60 * 1000), 
    acknowledged: false 
  },
  { 
    id: "alert-2", 
    severity: "critical", 
    message: "Backup failed for app-server-1", 
    source: "app-server-1", 
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), 
    acknowledged: false 
  },
  { 
    id: "alert-3", 
    severity: "low", 
    message: "CPU usage spike on web-server-1", 
    source: "web-server-1", 
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), 
    acknowledged: true 
  },
  { 
    id: "alert-4", 
    severity: "medium", 
    message: "Disk space below 20% on monitoring-server", 
    source: "monitoring-server", 
    timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), 
    acknowledged: false 
  },
];

export function SystemAdminDashboard() {
  const { toast } = useToast();
  const {
    nodes,
    vms,
    loading,
    refreshAll,
  } = useProxmoxContext();

  const [activeTab, setActiveTab] = useState("overview");
  const [lxdContainers, setLxdContainers] = useState(mockLxdContainers);
  const [healthChecks, setHealthChecks] = useState(mockHealthChecks);
  const [alerts, setAlerts] = useState(mockAlerts);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Calculate system-wide statistics
  const totalVMs = vms.length;
  const runningVMs = vms.filter(vm => vm.status === VMStatus.RUNNING).length;
  const totalContainers = lxdContainers.length;
  const runningContainers = lxdContainers.filter(container => container.status === "running").length;
  const criticalAlerts = alerts.filter(alert => alert.severity === "critical" && !alert.acknowledged).length;
  const highAlerts = alerts.filter(alert => alert.severity === "high" && !alert.acknowledged).length;
  
  // Calculate overall health status
  const getOverallHealth = () => {
    if (criticalAlerts > 0) return "critical";
    if (highAlerts > 0 || healthChecks.some(check => check.status === "error")) return "warning";
    return "healthy";
  };
  
  const overallHealth = getOverallHealth();

  // Handle refresh
  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await refreshAll();
      
      // In a real application, you would also refresh LXD container data here
      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Refresh Complete",
        description: "System information has been updated.",
      });
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast({
        title: "Refresh Failed",
        description: "Failed to update system information.",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle acknowledging an alert
  const handleAcknowledgeAlert = (alertId: string) => {
    setAlerts(alerts.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
    
    toast({
      title: "Alert Acknowledged",
      description: "The alert has been acknowledged.",
    });
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">System Administration</h1>
          <p className="text-muted-foreground">
            Manage VMs and LXD containers across your infrastructure
          </p>
        </div>
        <Button 
          onClick={handleRefresh} 
          disabled={loading.nodes || loading.vms || isRefreshing}
        >
          {isRefreshing ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="mr-2 h-4 w-4" />
          )}
          Refresh
        </Button>
      </div>

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              {overallHealth === "healthy" && (
                <CheckCircle2 className="h-8 w-8 text-green-500 mr-2" />
              )}
              {overallHealth === "warning" && (
                <AlertTriangle className="h-8 w-8 text-yellow-500 mr-2" />
              )}
              {overallHealth === "critical" && (
                <AlertTriangle className="h-8 w-8 text-red-500 mr-2" />
              )}
              <div>
                <p className="text-2xl font-bold">
                  {overallHealth === "healthy" && "Healthy"}
                  {overallHealth === "warning" && "Warning"}
                  {overallHealth === "critical" && "Critical"}
                </p>
                <p className="text-xs text-muted-foreground">
                  {criticalAlerts > 0 
                    ? `${criticalAlerts} critical alerts` 
                    : highAlerts > 0 
                      ? `${highAlerts} high alerts` 
                      : "All systems operational"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Virtual Machines</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Server className="h-8 w-8 text-blue-500 mr-2" />
              <div>
                <p className="text-2xl font-bold">{runningVMs} / {totalVMs}</p>
                <p className="text-xs text-muted-foreground">Running VMs</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">LXD Containers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Box className="h-8 w-8 text-purple-500 mr-2" />
              <div>
                <p className="text-2xl font-bold">{runningContainers} / {totalContainers}</p>
                <p className="text-xs text-muted-foreground">Running Containers</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-red-500 mr-2" />
              <div>
                <p className="text-2xl font-bold">
                  {alerts.filter(alert => !alert.acknowledged).length}
                </p>
                <p className="text-xs text-muted-foreground">
                  {criticalAlerts > 0 
                    ? `${criticalAlerts} critical, ${highAlerts} high` 
                    : highAlerts > 0 
                      ? `${highAlerts} high alerts` 
                      : "No critical alerts"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="vms">Virtual Machines</TabsTrigger>
          <TabsTrigger value="containers">LXD Containers</TabsTrigger>
          <TabsTrigger value="health">System Health</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Infrastructure Map</CardTitle>
                <CardDescription>
                  Visual representation of VMs and LXD containers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <InfrastructureMap 
                  nodes={nodes} 
                  vms={vms} 
                  containers={lxdContainers} 
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Resource Usage</CardTitle>
                <CardDescription>
                  System-wide resource utilization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResourceUsageChart 
                  vms={vms} 
                  containers={lxdContainers} 
                />
              </CardContent>
            </Card>

            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Recent Alerts</CardTitle>
                <CardDescription>
                  Latest system alerts and warnings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AlertsList 
                  alerts={alerts.filter(a => !a.acknowledged).slice(0, 5)} 
                  onAcknowledge={handleAcknowledgeAlert}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Health Checks</CardTitle>
                <CardDescription>
                  System health status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SystemHealthChecks 
                  checks={healthChecks.slice(0, 5)} 
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="vms">
          {/* VM management content will go here */}
          <p>VM management content will be implemented here</p>
        </TabsContent>

        <TabsContent value="containers">
          <Card>
            <CardHeader>
              <CardTitle>LXD Containers</CardTitle>
              <CardDescription>
                Manage LXD containers across your infrastructure
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LxdContainerList 
                containers={lxdContainers} 
                vms={vms}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="health">
          <Card>
            <CardHeader>
              <CardTitle>System Health</CardTitle>
              <CardDescription>
                Comprehensive health checks and diagnostics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SystemHealthChecks 
                checks={healthChecks} 
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>
                Manage and respond to system alerts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AlertsList 
                alerts={alerts} 
                onAcknowledge={handleAcknowledgeAlert}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
