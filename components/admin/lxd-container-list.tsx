"use client"

import { useState } from "react"
import { VMInfo } from "@/lib/proxmox/types"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { StatusBadge } from "@/components/proxmox/status-badge"
import { ResourceMeter } from "@/components/proxmox/resource-meter"
import { 
  Play, 
  Square, 
  RotateCw, 
  Trash2, 
  Terminal, 
  Copy,
  Search,
  Filter,
  Box
} from "lucide-react"
import { formatUptime } from "@/lib/utils/format"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"

interface LxdContainer {
  id: string
  name: string
  status: string
  host: string
  ipAddress: string
  cpu: { usage: number, cores: number }
  memory: { used: number, total: number }
  disk: { used: number, total: number }
  uptime: number
}

interface LxdContainerListProps {
  containers: LxdContainer[]
  vms: VMInfo[]
}

export function LxdContainerList({ containers, vms }: LxdContainerListProps) {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [hostFilter, setHostFilter] = useState<string>("all")
  
  // Get unique VM hosts for filter
  const uniqueHosts = Array.from(new Set(containers.map(c => c.host)))
  
  // Filter containers based on search and filters
  const filteredContainers = containers.filter(container => {
    const matchesSearch = container.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         container.ipAddress.includes(searchTerm)
    
    const matchesStatus = statusFilter === "all" || container.status === statusFilter
    const matchesHost = hostFilter === "all" || container.host === hostFilter
    
    return matchesSearch && matchesStatus && matchesHost
  })
  
  // Handle container actions
  const handleContainerAction = (action: string, container: LxdContainer) => {
    // In a real application, this would call the LXD API
    toast({
      title: "Container Action",
      description: `${action} action triggered on ${container.name}`,
    })
  }
  
  // Find VM by ID
  const getVmName = (vmId: string) => {
    const vm = vms.find(vm => vm.id === vmId)
    return vm ? vm.name : vmId
  }
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search containers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[130px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="running">Running</SelectItem>
              <SelectItem value="stopped">Stopped</SelectItem>
              <SelectItem value="frozen">Frozen</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={hostFilter} onValueChange={setHostFilter}>
            <SelectTrigger className="w-[130px]">
              <Box className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Host VM" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Hosts</SelectItem>
              {uniqueHosts.map(host => (
                <SelectItem key={host} value={host}>
                  {getVmName(host)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {filteredContainers.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No containers found matching your filters.
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Host VM</TableHead>
                <TableHead>IP Address</TableHead>
                <TableHead>CPU</TableHead>
                <TableHead>Memory</TableHead>
                <TableHead>Uptime</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredContainers.map(container => (
                <TableRow key={container.id}>
                  <TableCell className="font-medium">{container.name}</TableCell>
                  <TableCell>
                    <StatusBadge status={container.status} />
                  </TableCell>
                  <TableCell>{getVmName(container.host)}</TableCell>
                  <TableCell>{container.ipAddress}</TableCell>
                  <TableCell>
                    <ResourceMeter
                      label=""
                      value={container.cpu.usage}
                      max={1}
                      showPercentage={true}
                      size="sm"
                    />
                  </TableCell>
                  <TableCell>
                    <ResourceMeter
                      label=""
                      value={container.memory.used}
                      max={container.memory.total}
                      unit="MB"
                      showPercentage={true}
                      size="sm"
                    />
                  </TableCell>
                  <TableCell>
                    {container.status === "running" 
                      ? formatUptime(container.uptime) 
                      : "—"}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      {container.status !== "running" && (
                        <Button 
                          size="icon" 
                          variant="ghost" 
                          onClick={() => handleContainerAction("start", container)}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                      )}
                      
                      {container.status === "running" && (
                        <>
                          <Button 
                            size="icon" 
                            variant="ghost" 
                            onClick={() => handleContainerAction("stop", container)}
                          >
                            <Square className="h-4 w-4" />
                          </Button>
                          
                          <Button 
                            size="icon" 
                            variant="ghost" 
                            onClick={() => handleContainerAction("restart", container)}
                          >
                            <RotateCw className="h-4 w-4" />
                          </Button>
                          
                          <Button 
                            size="icon" 
                            variant="ghost" 
                            onClick={() => handleContainerAction("terminal", container)}
                          >
                            <Terminal className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="icon" variant="ghost">
                            <Filter className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Container Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          
                          <DropdownMenuItem onClick={() => handleContainerAction("snapshot", container)}>
                            Create Snapshot
                          </DropdownMenuItem>
                          
                          <DropdownMenuItem onClick={() => handleContainerAction("clone", container)}>
                            Clone Container
                          </DropdownMenuItem>
                          
                          <DropdownMenuItem onClick={() => handleContainerAction("migrate", container)}>
                            Migrate to Another VM
                          </DropdownMenuItem>
                          
                          <DropdownMenuSeparator />
                          
                          <DropdownMenuItem 
                            className="text-destructive focus:text-destructive"
                            onClick={() => handleContainerAction("delete", container)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Container
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
