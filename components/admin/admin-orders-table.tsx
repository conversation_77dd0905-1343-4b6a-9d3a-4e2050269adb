"use client"

import { useState } from "react"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  MoreHorizontal, 
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"

interface AdminOrdersTableProps {
  orders: any[]
  searchTerm: string
}

export function AdminOrdersTable({ orders, searchTerm }: AdminOrdersTableProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  
  // Filter orders based on search term
  const filteredOrders = orders.filter(order => {
    const searchLower = searchTerm.toLowerCase()
    return (
      (order.vmName && order.vmName.toLowerCase().includes(searchLower)) ||
      (order.vmId && order.vmId.toLowerCase().includes(searchLower)) ||
      order.template.toLowerCase().includes(searchLower) ||
      order.user.toLowerCase().includes(searchLower) ||
      order.id.toLowerCase().includes(searchLower)
    )
  })
  
  // Handle order action
  const handleOrderAction = async (action: string, order: any) => {
    setIsLoading(true)
    
    try {
      // In a real application, this would call an API
      console.log(`Performing ${action} on order ${order.id}`)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Success",
        description: `Order ${action} operation completed successfully`,
      })
    } catch (error) {
      console.error(`Error performing ${action} on order ${order.id}:`, error)
      toast({
        title: "Error",
        description: `Failed to ${action} order. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge className="bg-green-500 text-white flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Completed
          </Badge>
        )
      case "pending":
        return (
          <Badge className="bg-yellow-500 text-white flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        )
      case "failed":
        return (
          <Badge className="bg-red-500 text-white flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            Failed
          </Badge>
        )
      case "processing":
        return (
          <Badge className="bg-blue-500 text-white flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            Processing
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        )
    }
  }
  
  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }
  
  // Format duration
  const formatDuration = (startDate: Date, endDate: Date | null) => {
    if (!endDate) return "In progress"
    
    const diffMs = endDate.getTime() - startDate.getTime()
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    
    if (diffMin < 1) {
      return `${diffSec} seconds`
    } else if (diffMin < 60) {
      return `${diffMin} minutes`
    } else {
      const diffHour = Math.floor(diffMin / 60)
      const remainingMin = diffMin % 60
      return `${diffHour} hours, ${remainingMin} minutes`
    }
  }
  
  return (
    <div>
      {filteredOrders.length === 0 ? (
        <div className="text-center py-8 border rounded-md">
          <p className="text-muted-foreground">
            No orders found matching your search criteria
          </p>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order ID</TableHead>
                <TableHead>VM</TableHead>
                <TableHead>Template</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Price</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.id}</TableCell>
                  <TableCell>
                    {order.vmId ? (
                      <div>
                        <div>{order.vmName}</div>
                        <div className="text-xs text-muted-foreground">{order.vmId}</div>
                      </div>
                    ) : (
                      <div className="text-muted-foreground italic">Pending creation</div>
                    )}
                  </TableCell>
                  <TableCell>{order.template}</TableCell>
                  <TableCell>{order.user}</TableCell>
                  <TableCell>{getStatusBadge(order.status)}</TableCell>
                  <TableCell>{formatDate(order.createdAt)}</TableCell>
                  <TableCell>
                    {order.completedAt 
                      ? formatDuration(order.createdAt, order.completedAt)
                      : "In progress"}
                  </TableCell>
                  <TableCell>${order.price}/mo</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button 
                        size="icon" 
                        variant="ghost"
                        onClick={() => handleOrderAction("view", order)}
                        disabled={isLoading}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      
                      {order.status === "pending" && (
                        <Button 
                          size="sm"
                          variant="outline" 
                          onClick={() => handleOrderAction("approve", order)}
                          disabled={isLoading}
                        >
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Approve
                        </Button>
                      )}
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            size="icon" 
                            variant="ghost"
                            disabled={isLoading}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Order Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleOrderAction("viewDetails", order)}>
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleOrderAction("viewUser", order)}>
                            View User
                          </DropdownMenuItem>
                          {order.vmId && (
                            <DropdownMenuItem onClick={() => handleOrderAction("viewVM", order)}>
                              View VM
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          {order.status === "pending" && (
                            <>
                              <DropdownMenuItem onClick={() => handleOrderAction("approve", order)}>
                                <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                Approve Order
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleOrderAction("reject", order)}>
                                <XCircle className="mr-2 h-4 w-4 text-red-500" />
                                Reject Order
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
