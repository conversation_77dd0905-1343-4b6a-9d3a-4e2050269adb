"use client"

import { useState } from "react"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { 
  MoreHorizontal, 
  Edit,
  Copy,
  Trash2,
  Server,
  Globe,
  Database,
  Brain,
  Box,
  Cpu
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"

interface AdminTemplateTableProps {
  templates: any[]
  searchTerm: string
}

export function AdminTemplateTable({ templates, searchTerm }: AdminTemplateTableProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [activeTemplates, setActiveTemplates] = useState<Record<string, boolean>>(
    templates.reduce((acc, template) => ({
      ...acc,
      [template.id]: template.active
    }), {})
  )
  
  // Filter templates based on search term
  const filteredTemplates = templates.filter(template => {
    const searchLower = searchTerm.toLowerCase()
    return (
      template.name.toLowerCase().includes(searchLower) ||
      template.description.toLowerCase().includes(searchLower) ||
      template.category.toLowerCase().includes(searchLower) ||
      template.os.toLowerCase().includes(searchLower)
    )
  })
  
  // Handle template action
  const handleTemplateAction = async (action: string, template: any) => {
    setIsLoading(true)
    
    try {
      // In a real application, this would call an API
      console.log(`Performing ${action} on template ${template.id}`)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Success",
        description: `Template ${action} operation completed successfully`,
      })
    } catch (error) {
      console.error(`Error performing ${action} on template ${template.id}:`, error)
      toast({
        title: "Error",
        description: `Failed to ${action} template. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Handle template activation toggle
  const handleToggleActive = async (templateId: string, active: boolean) => {
    setIsLoading(true)
    
    try {
      // In a real application, this would call an API
      console.log(`Setting template ${templateId} active status to ${active}`)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update local state
      setActiveTemplates(prev => ({
        ...prev,
        [templateId]: active
      }))
      
      toast({
        title: "Success",
        description: `Template ${active ? 'activated' : 'deactivated'} successfully`,
      })
    } catch (error) {
      console.error(`Error toggling template ${templateId} active status:`, error)
      toast({
        title: "Error",
        description: `Failed to update template status. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Get icon component based on template icon name
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case "server":
        return <Server className="h-5 w-5 text-primary" />
      case "globe":
        return <Globe className="h-5 w-5 text-blue-500" />
      case "database":
        return <Database className="h-5 w-5 text-purple-500" />
      case "brain":
        return <Brain className="h-5 w-5 text-pink-500" />
      case "box":
        return <Box className="h-5 w-5 text-green-500" />
      case "cpu":
        return <Cpu className="h-5 w-5 text-orange-500" />
      default:
        return <Server className="h-5 w-5 text-primary" />
    }
  }
  
  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date)
  }
  
  return (
    <div>
      {filteredTemplates.length === 0 ? (
        <div className="text-center py-8 border rounded-md">
          <p className="text-muted-foreground">
            No templates found matching your search criteria
          </p>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Template</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Resources</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>OS</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Active</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTemplates.map((template) => (
                <TableRow key={template.id}>
                  <TableCell>
                    <div className="flex items-center">
                      <div className="mr-2">
                        {getIconComponent(template.icon)}
                      </div>
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-xs text-muted-foreground">{template.description}</div>
                      </div>
                      {template.popular && (
                        <Badge className="ml-2 bg-primary">Popular</Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {template.category}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>{template.specs.cpu} CPU, {template.specs.memory} GB RAM</div>
                      <div className="text-xs text-muted-foreground">
                        {template.specs.storage} GB Storage, {template.specs.bandwidth} Bandwidth
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">${template.price}/mo</div>
                  </TableCell>
                  <TableCell>{template.os}</TableCell>
                  <TableCell>{formatDate(template.createdAt)}</TableCell>
                  <TableCell>
                    <Switch
                      checked={activeTemplates[template.id]}
                      onCheckedChange={(checked) => handleToggleActive(template.id, checked)}
                      disabled={isLoading}
                    />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button 
                        size="icon" 
                        variant="ghost"
                        onClick={() => handleTemplateAction("edit", template)}
                        disabled={isLoading}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      
                      <Button 
                        size="icon" 
                        variant="ghost"
                        onClick={() => handleTemplateAction("duplicate", template)}
                        disabled={isLoading}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            size="icon" 
                            variant="ghost"
                            disabled={isLoading}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Template Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleTemplateAction("setPopular", template)}>
                            {template.popular ? "Remove Popular Tag" : "Set as Popular"}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleTemplateAction("updatePrice", template)}>
                            Update Price
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-destructive focus:text-destructive"
                            onClick={() => handleTemplateAction("delete", template)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Template
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
