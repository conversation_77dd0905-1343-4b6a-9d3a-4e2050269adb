"use client"

import { VMInfo, VMStatus } from "@/lib/proxmox/types"
import { ResourceMeter } from "@/components/proxmox/resource-meter"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Cpu, Memory, HardDrive, Network } from "lucide-react"
import { formatBytes } from "@/lib/utils/format"

interface LxdContainer {
  id: string
  name: string
  status: string
  host: string
  ipAddress: string
  cpu: { usage: number, cores: number }
  memory: { used: number, total: number }
  disk: { used: number, total: number }
  uptime: number
}

interface ResourceUsageChartProps {
  vms: VMInfo[]
  containers: LxdContainer[]
}

export function ResourceUsageChart({ vms, containers }: ResourceUsageChartProps) {
  // Calculate total resources
  const calculateTotalResources = () => {
    // CPU cores
    const totalVmCores = vms.reduce((sum, vm) => sum + (vm.config?.cpu?.cores || 1), 0)
    const totalContainerCores = containers.reduce((sum, container) => sum + container.cpu.cores, 0)
    
    // Memory
    const totalVmMemory = vms.reduce((sum, vm) => sum + (vm.config?.memory || 0) * 1024 * 1024, 0)
    const totalContainerMemory = containers.reduce((sum, container) => sum + container.memory.total, 0)
    
    // Disk
    const totalVmDisk = vms.reduce((sum, vm) => {
      // This is a simplification - in a real app, you'd get actual disk usage
      return sum + (vm.diskUsage || 0)
    }, 0)
    const totalContainerDisk = containers.reduce((sum, container) => sum + container.disk.total, 0)
    
    return {
      cpu: {
        vmCores: totalVmCores,
        containerCores: totalContainerCores,
        totalCores: totalVmCores + totalContainerCores
      },
      memory: {
        vmMemory: totalVmMemory,
        containerMemory: totalContainerMemory,
        totalMemory: totalVmMemory + totalContainerMemory
      },
      disk: {
        vmDisk: totalVmDisk,
        containerDisk: totalContainerDisk,
        totalDisk: totalVmDisk + totalContainerDisk
      }
    }
  }
  
  // Calculate used resources
  const calculateUsedResources = () => {
    // CPU usage
    const usedVmCpu = vms
      .filter(vm => vm.status === VMStatus.RUNNING)
      .reduce((sum, vm) => sum + (vm.cpuUsage || 0), 0)
    
    const usedContainerCpu = containers
      .filter(container => container.status === "running")
      .reduce((sum, container) => sum + container.cpu.usage, 0)
    
    // Memory usage
    const usedVmMemory = vms
      .filter(vm => vm.status === VMStatus.RUNNING)
      .reduce((sum, vm) => sum + (vm.memoryUsage || 0), 0)
    
    const usedContainerMemory = containers
      .filter(container => container.status === "running")
      .reduce((sum, container) => sum + container.memory.used, 0)
    
    // Disk usage
    const usedVmDisk = vms.reduce((sum, vm) => sum + (vm.diskUsage || 0), 0)
    
    const usedContainerDisk = containers.reduce((sum, container) => sum + container.disk.used, 0)
    
    return {
      cpu: {
        vmCpuUsage: usedVmCpu,
        containerCpuUsage: usedContainerCpu,
        totalCpuUsage: usedVmCpu + usedContainerCpu
      },
      memory: {
        vmMemoryUsage: usedVmMemory,
        containerMemoryUsage: usedContainerMemory,
        totalMemoryUsage: usedVmMemory + usedContainerMemory
      },
      disk: {
        vmDiskUsage: usedVmDisk,
        containerDiskUsage: usedContainerDisk,
        totalDiskUsage: usedVmDisk + usedContainerDisk
      }
    }
  }
  
  const totalResources = calculateTotalResources()
  const usedResources = calculateUsedResources()
  
  return (
    <Tabs defaultValue="cpu">
      <TabsList className="grid grid-cols-3 mb-4">
        <TabsTrigger value="cpu">
          <Cpu className="h-4 w-4 mr-2" />
          CPU
        </TabsTrigger>
        <TabsTrigger value="memory">
          <Memory className="h-4 w-4 mr-2" />
          Memory
        </TabsTrigger>
        <TabsTrigger value="disk">
          <HardDrive className="h-4 w-4 mr-2" />
          Disk
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="cpu" className="space-y-4">
        <ResourceMeter
          label="Total CPU Usage"
          value={usedResources.cpu.totalCpuUsage}
          max={totalResources.cpu.totalCores}
          unit="cores"
          showPercentage={true}
          size="lg"
          colorThresholds={{ warning: 70, critical: 90 }}
        />
        
        <div className="grid grid-cols-2 gap-4">
          <ResourceMeter
            label="VM CPU Usage"
            value={usedResources.cpu.vmCpuUsage}
            max={totalResources.cpu.vmCores}
            unit="cores"
            showPercentage={true}
            size="md"
          />
          
          <ResourceMeter
            label="Container CPU Usage"
            value={usedResources.cpu.containerCpuUsage}
            max={totalResources.cpu.containerCores}
            unit="cores"
            showPercentage={true}
            size="md"
          />
        </div>
        
        <div className="text-xs text-muted-foreground mt-2">
          <p>Total CPU Cores: {totalResources.cpu.totalCores}</p>
          <p>Running VMs: {vms.filter(vm => vm.status === VMStatus.RUNNING).length}</p>
          <p>Running Containers: {containers.filter(c => c.status === "running").length}</p>
        </div>
      </TabsContent>
      
      <TabsContent value="memory" className="space-y-4">
        <ResourceMeter
          label="Total Memory Usage"
          value={usedResources.memory.totalMemoryUsage}
          max={totalResources.memory.totalMemory}
          unit="GB"
          showPercentage={true}
          size="lg"
          colorThresholds={{ warning: 70, critical: 90 }}
        />
        
        <div className="grid grid-cols-2 gap-4">
          <ResourceMeter
            label="VM Memory Usage"
            value={usedResources.memory.vmMemoryUsage}
            max={totalResources.memory.vmMemory}
            unit="GB"
            showPercentage={true}
            size="md"
          />
          
          <ResourceMeter
            label="Container Memory Usage"
            value={usedResources.memory.containerMemoryUsage}
            max={totalResources.memory.containerMemory}
            unit="GB"
            showPercentage={true}
            size="md"
          />
        </div>
        
        <div className="text-xs text-muted-foreground mt-2">
          <p>Total Memory: {formatBytes(totalResources.memory.totalMemory)}</p>
          <p>VM Memory: {formatBytes(totalResources.memory.vmMemory)}</p>
          <p>Container Memory: {formatBytes(totalResources.memory.containerMemory)}</p>
        </div>
      </TabsContent>
      
      <TabsContent value="disk" className="space-y-4">
        <ResourceMeter
          label="Total Disk Usage"
          value={usedResources.disk.totalDiskUsage}
          max={totalResources.disk.totalDisk}
          unit="GB"
          showPercentage={true}
          size="lg"
          colorThresholds={{ warning: 70, critical: 90 }}
        />
        
        <div className="grid grid-cols-2 gap-4">
          <ResourceMeter
            label="VM Disk Usage"
            value={usedResources.disk.vmDiskUsage}
            max={totalResources.disk.vmDisk}
            unit="GB"
            showPercentage={true}
            size="md"
          />
          
          <ResourceMeter
            label="Container Disk Usage"
            value={usedResources.disk.containerDiskUsage}
            max={totalResources.disk.containerDisk}
            unit="GB"
            showPercentage={true}
            size="md"
          />
        </div>
        
        <div className="text-xs text-muted-foreground mt-2">
          <p>Total Disk Space: {formatBytes(totalResources.disk.totalDisk)}</p>
          <p>VM Disk Space: {formatBytes(totalResources.disk.vmDisk)}</p>
          <p>Container Disk Space: {formatBytes(totalResources.disk.containerDisk)}</p>
        </div>
      </TabsContent>
    </Tabs>
  )
}
