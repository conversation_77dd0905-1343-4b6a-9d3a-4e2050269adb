"use client"

import { useState } from "react"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Play, 
  Square, 
  RotateCw, 
  MoreHorizontal, 
  Eye,
  Trash2,
  Terminal,
  Box
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { formatBytes } from "@/lib/utils/format"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"

interface AdminVMTableProps {
  vms: any[]
  searchTerm: string
}

export function AdminVMTable({ vms, searchTerm }: AdminVMTableProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  
  // Filter VMs based on search term
  const filteredVMs = vms.filter(vm => {
    const searchLower = searchTerm.toLowerCase()
    return (
      vm.name.toLowerCase().includes(searchLower) ||
      vm.description.toLowerCase().includes(searchLower) ||
      vm.ipAddress.includes(searchTerm) ||
      vm.owner.toLowerCase().includes(searchLower) ||
      vm.id.toLowerCase().includes(searchLower)
    )
  })
  
  // Handle VM action
  const handleVMAction = async (action: string, vm: any) => {
    setIsLoading(true)
    
    try {
      // In a real application, this would call an API
      console.log(`Performing ${action} on VM ${vm.id}`)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Success",
        description: `VM ${action} operation completed successfully`,
      })
    } catch (error) {
      console.error(`Error performing ${action} on VM ${vm.id}:`, error)
      toast({
        title: "Error",
        description: `Failed to ${action} VM. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "bg-green-500 text-white"
      case "stopped":
        return "bg-red-500 text-white"
      case "starting":
        return "bg-blue-500 text-white"
      case "stopping":
        return "bg-yellow-500 text-white"
      default:
        return "bg-gray-500 text-white"
    }
  }
  
  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date)
  }
  
  return (
    <div>
      {filteredVMs.length === 0 ? (
        <div className="text-center py-8 border rounded-md">
          <p className="text-muted-foreground">
            No virtual machines found matching your search criteria
          </p>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Owner</TableHead>
                <TableHead>Host</TableHead>
                <TableHead>IP Address</TableHead>
                <TableHead>Resources</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVMs.map((vm) => (
                <TableRow key={vm.id}>
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-medium">{vm.name}</div>
                      <div className="text-xs text-muted-foreground">{vm.id}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(vm.status)}>
                      {vm.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{vm.owner}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>{vm.host}</div>
                      <div className="text-xs text-muted-foreground">{vm.node}</div>
                    </div>
                  </TableCell>
                  <TableCell>{vm.ipAddress}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>{vm.cpu.cores} CPU, {formatBytes(vm.memory.total).split(' ')[0]} {formatBytes(vm.memory.total).split(' ')[1]} RAM</div>
                      <div className="text-xs text-muted-foreground">
                        {formatBytes(vm.storage.total)} Storage
                        {vm.containers > 0 && `, ${vm.containers} Containers`}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(vm.createdAt)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-1">
                      <Button 
                        size="icon" 
                        variant="ghost"
                        onClick={() => handleVMAction("view", vm)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      
                      {vm.status === "stopped" && (
                        <Button 
                          size="icon" 
                          variant="ghost"
                          onClick={() => handleVMAction("start", vm)}
                          disabled={isLoading}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                      )}
                      
                      {vm.status === "running" && (
                        <>
                          <Button 
                            size="icon" 
                            variant="ghost"
                            onClick={() => handleVMAction("stop", vm)}
                            disabled={isLoading}
                          >
                            <Square className="h-4 w-4" />
                          </Button>
                          
                          <Button 
                            size="icon" 
                            variant="ghost"
                            onClick={() => handleVMAction("restart", vm)}
                            disabled={isLoading}
                          >
                            <RotateCw className="h-4 w-4" />
                          </Button>
                          
                          <Button 
                            size="icon" 
                            variant="ghost"
                            onClick={() => handleVMAction("console", vm)}
                            disabled={isLoading}
                          >
                            <Terminal className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            size="icon" 
                            variant="ghost"
                            disabled={isLoading}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>VM Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleVMAction("edit", vm)}>
                            Edit VM
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleVMAction("migrate", vm)}>
                            Migrate VM
                          </DropdownMenuItem>
                          {vm.containers > 0 && (
                            <DropdownMenuItem onClick={() => handleVMAction("containers", vm)}>
                              <Box className="mr-2 h-4 w-4" />
                              Manage Containers
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-destructive focus:text-destructive"
                            onClick={() => handleVMAction("delete", vm)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete VM
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
