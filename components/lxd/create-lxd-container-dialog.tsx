"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { useSession } from "next-auth/react"
import { Loader2 } from "lucide-react"

interface CreateLxdContainerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateContainer: (options: {
    name: string
    image: string
    autostart?: boolean
    projectId?: string
  }) => Promise<boolean>
}

interface LxdImage {
  fingerprint: string
  aliases: string[]
  properties: {
    description?: string
    os?: string
    release?: string
  }
}

export function CreateLxdContainerDialog({
  open,
  onOpenChange,
  onCreateContainer,
}: CreateLxdContainerDialogProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [name, setName] = useState("")
  const [image, setImage] = useState("")
  const [autostart, setAutostart] = useState(true)
  const [images, setImages] = useState<LxdImage[]>([])
  const [loadingImages, setLoadingImages] = useState(false)
  const [creating, setCreating] = useState(false)

  const fetchImages = async () => {
    if (!session?.user) return

    try {
      setLoadingImages(true)
      const response = await fetch("/api/containerization/lxd/images")
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch images")
      }

      const data = await response.json()
      setImages(data.images || [])
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to fetch images: ${error.message}`,
        variant: "destructive",
      })
    } finally {
      setLoadingImages(false)
    }
  }

  useEffect(() => {
    if (open && session?.user) {
      fetchImages()
    }
  }, [open, session])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name || !image) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    try {
      setCreating(true)
      const success = await onCreateContainer({
        name,
        image,
        autostart,
      })

      if (success) {
        onOpenChange(false)
        setName("")
        setImage("")
        setAutostart(true)
      }
    } finally {
      setCreating(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create LXD Container</DialogTitle>
            <DialogDescription>
              Create a new LXD container with the specified settings.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                placeholder="my-container"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="image" className="text-right">
                Image
              </Label>
              <div className="col-span-3">
                <Select value={image} onValueChange={setImage} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an image" />
                  </SelectTrigger>
                  <SelectContent>
                    {loadingImages ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading...
                      </div>
                    ) : images.length === 0 ? (
                      <div className="p-2 text-center text-muted-foreground">
                        No images available
                      </div>
                    ) : (
                      images.map((img) => (
                        <SelectItem key={img.fingerprint} value={img.fingerprint}>
                          {img.aliases.length > 0
                            ? `${img.aliases[0]} (${img.properties.os || "Unknown"} ${
                                img.properties.release || ""
                              })`
                            : img.fingerprint.substring(0, 12)}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-span-4 flex items-center space-x-2">
                <Checkbox
                  id="autostart"
                  checked={autostart}
                  onCheckedChange={(checked) => setAutostart(checked as boolean)}
                />
                <Label htmlFor="autostart">Start container automatically</Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating || !name || !image}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
