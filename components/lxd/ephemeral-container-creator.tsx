"use client";

import * as React from "react";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Loader2, Terminal, Boxes } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { useWorkspaceStore } from "@/lib/stores/workspace-store";

// Form schema for ephemeral container creation
const formSchema = z.object({
  name: z.string().optional(),
  useLocalImage: z.boolean().default(true),
  imagePath: z.string().min(1, "Image path is required when using local image"),
  expiresInMinutes: z.coerce.number().min(1).max(1440).default(60),
  projectId: z.string().optional(),
});

interface EphemeralContainerCreatorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId?: string | null;
  defaultImagePath?: string;
}

export function EphemeralContainerCreator({
  open,
  onOpenChange,
  projectId = null,
  defaultImagePath = "/home/<USER>/Documents/Webdev/app-gen/resources/lxc-images/jammy-server-cloudimg-amd64.img",
}: EphemeralContainerCreatorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { setContainerId, setContainerType, setContainerStatus, addTab } = useWorkspaceStore();

  // Initialize form with default values
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: `ephemeral-${Date.now().toString().slice(-6)}`,
      useLocalImage: true,
      imagePath: defaultImagePath,
      expiresInMinutes: 60,
      projectId: projectId || undefined,
    },
  });

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    
    try {
      // Create the ephemeral container
      const response = await fetch("/api/containerization/lxd/ephemeral", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: values.name,
          imagePath: values.useLocalImage ? values.imagePath : undefined,
          projectId: values.projectId || projectId,
          expiresInMinutes: values.expiresInMinutes,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create ephemeral container");
      }

      const containerData = await response.json();

      // Update workspace state
      setContainerId(containerData.id);
      setContainerType("lxd-ephemeral");
      setContainerStatus("running");

      // Add terminal tab for the new container
      addTab({
        id: `lxd-ephemeral-terminal-${containerData.id}`,
        title: `${containerData.name} Terminal`,
        icon: <Terminal className="h-4 w-4" />,
        content: null,
        isActive: true,
      });

      // Show success toast
      toast({
        title: "Ephemeral Container Created",
        description: `Container '${containerData.name}' created successfully. It will expire in ${values.expiresInMinutes} minutes.`,
      });

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error("Error creating ephemeral container:", error);
      toast({
        title: "Error Creating Container",
        description: (error as Error).message || "Failed to create ephemeral container",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create Ephemeral LXD Container</DialogTitle>
          <DialogDescription>
            Create a temporary LXD container that will be automatically destroyed after a specified time.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Container Name (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="ephemeral-container" {...field} />
                  </FormControl>
                  <FormDescription>
                    A unique name will be generated if left empty
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="useLocalImage"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Use Local Image</FormLabel>
                    <FormDescription>
                      Use a local image file instead of a remote image
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            {form.watch("useLocalImage") && (
              <FormField
                control={form.control}
                name="imagePath"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Image Path</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      Path to the local image file
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="expiresInMinutes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expiration Time (minutes)</FormLabel>
                  <FormControl>
                    <Input type="number" min={1} max={1440} {...field} />
                  </FormControl>
                  <FormDescription>
                    The container will be automatically destroyed after this time
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Boxes className="mr-2 h-4 w-4" />
                    Create Container
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
