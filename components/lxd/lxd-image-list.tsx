"use client"

import { useState, useEffect } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Download, MoreVertical, RefreshCw, Trash2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useSession } from "next-auth/react"

interface LxdImage {
  id: string;
  fingerprint: string;
  aliases: string[];
  properties: {
    description?: string;
    os?: string;
    release?: string;
    architecture?: string;
  };
  public: boolean;
  size: number;
  createdAt: string;
  expiresAt?: string;
}

export function LxdImageList() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [images, setImages] = useState<LxdImage[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  const fetchImages = async () => {
    if (!session?.user) {
      setError('Not authenticated')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/containerization/lxd/images')
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch images')
      }

      const data = await response.json()
      setImages(data.images || [])
    } catch (error: any) {
      setError(error.message)
      toast({
        title: 'Error',
        description: `Failed to fetch images: ${error.message}`,
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const deleteImage = async (fingerprint: string) => {
    if (!session?.user) {
      setError('Not authenticated')
      return false
    }

    try {
      setActionLoading(`delete-${fingerprint}`)

      const response = await fetch(`/api/containerization/lxd/images/${fingerprint}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete image')
      }

      // Refresh image list
      await fetchImages()
      
      toast({
        title: 'Success',
        description: 'Image deleted successfully',
      })
      
      return true
    } catch (error: any) {
      toast({
        title: 'Error',
        description: `Failed to delete image: ${error.message}`,
        variant: 'destructive',
      })
      return false
    } finally {
      setActionLoading(null)
    }
  }

  // Load images on mount
  useEffect(() => {
    if (session?.user) {
      fetchImages()
    }
  }, [session])

  const formatSize = (bytes: number) => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`
  }

  if (loading && images.length === 0) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 border border-destructive/50 rounded-md bg-destructive/10 text-destructive">
        <p>Error: {error}</p>
      </div>
    )
  }

  if (images.length === 0) {
    return (
      <div className="p-8 text-center border rounded-md">
        <p className="text-muted-foreground">No images found</p>
        <Button variant="outline" className="mt-4" onClick={fetchImages}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Button variant="outline" size="sm" onClick={fetchImages}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>
      
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Alias</TableHead>
            <TableHead>OS</TableHead>
            <TableHead>Release</TableHead>
            <TableHead>Architecture</TableHead>
            <TableHead>Size</TableHead>
            <TableHead>Public</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {images.map((image) => (
            <TableRow key={image.id}>
              <TableCell className="font-medium">
                {image.aliases.length > 0 ? image.aliases.join(', ') : image.fingerprint.substring(0, 12)}
              </TableCell>
              <TableCell>{image.properties.os || '-'}</TableCell>
              <TableCell>{image.properties.release || '-'}</TableCell>
              <TableCell>{image.properties.architecture || '-'}</TableCell>
              <TableCell>{formatSize(image.size)}</TableCell>
              <TableCell>
                {image.public ? (
                  <Badge variant="success">Public</Badge>
                ) : (
                  <Badge variant="secondary">Private</Badge>
                )}
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => {}}>
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => deleteImage(image.fingerprint)}
                      className="text-destructive focus:text-destructive"
                      disabled={actionLoading === `delete-${image.fingerprint}`}
                    >
                      {actionLoading === `delete-${image.fingerprint}` ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Deleting...
                        </>
                      ) : (
                        <>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </>
                      )}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
