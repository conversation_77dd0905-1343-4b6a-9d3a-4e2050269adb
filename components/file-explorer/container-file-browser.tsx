"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { FileTree, FileNode } from "@/components/file-explorer/file-tree";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  FolderTree, 
  File, 
  FolderPlus, 
  FilePlus, 
  RefreshCw,
  Search,
  AlertCircle
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { useContainer } from "@/lib/contexts/container-context";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface ContainerFileBrowserProps {
  onFileSelect: (file: FileNode) => void;
  className?: string;
  initialPath?: string;
}

export function ContainerFileBrowser({ 
  onFileSelect, 
  className,
  initialPath = "/app" 
}: ContainerFileBrowserProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPath, setCurrentPath] = useState(initialPath);
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [selectedFilePath, setSelectedFilePath] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get container context
  const { 
    containerId, 
    containerStatus, 
    listFiles, 
    createDirectory, 
    writeFile,
    deleteFile
  } = useContainer();

  // Load files when the component mounts or the current path changes
  useEffect(() => {
    if (containerStatus === "ready" && containerId) {
      loadFiles(currentPath);
    }
  }, [containerId, containerStatus, currentPath]);

  // Load files from the container
  const loadFiles = async (path: string) => {
    if (containerStatus !== "ready" || !containerId) {
      setError("Container is not ready");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const files = await listFiles(path);
      
      // Convert the files to the FileNode format
      const fileNodes: FileNode[] = files.map((file: any) => ({
        id: `${path}/${file.name}`.replace(/\/+/g, '/'),
        name: file.name,
        type: file.type === "directory" ? "directory" : "file",
        path: `${path}/${file.name}`.replace(/\/+/g, '/'),
        extension: file.name.includes('.') ? file.name.split('.').pop() : '',
        size: file.size,
        lastModified: file.lastModified ? new Date(file.lastModified) : undefined,
        children: file.type === "directory" ? [] : undefined
      }));

      setFileTree(fileNodes);
    } catch (error: any) {
      console.error("Error loading files:", error);
      setError(error.message || "Failed to load files");
      toast.error("Failed to load files from container");
    } finally {
      setIsLoading(false);
    }
  };

  // Filter files based on search query
  const filteredFileTree = React.useMemo(() => {
    if (!searchQuery) return fileTree;
    
    // Helper function to filter nodes
    const filterNode = (node: FileNode): FileNode | null => {
      if (node.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return node;
      }
      
      if (node.type === 'directory' && node.children) {
        const filteredChildren = node.children
          .map(filterNode)
          .filter(Boolean) as FileNode[];
        
        if (filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren
          };
        }
      }
      
      return null;
    };
    
    return fileTree.map(filterNode).filter(Boolean) as FileNode[];
  }, [fileTree, searchQuery]);
  
  // Handle file selection
  const handleFileSelect = (file: FileNode) => {
    if (file.type === 'file') {
      setSelectedFilePath(file.path);
      onFileSelect(file);
    } else if (file.type === 'directory') {
      // Navigate to the directory
      setCurrentPath(file.path);
    }
  };
  
  // Handle new file button click
  const handleNewFile = async () => {
    const fileName = prompt('Enter file name:');
    if (!fileName) return;
    
    try {
      const path = `${currentPath}/${fileName}`.replace(/\/+/g, '/');
      await writeFile(path, '');
      toast.success(`File ${fileName} created`);
      loadFiles(currentPath);
    } catch (error: any) {
      console.error("Error creating file:", error);
      toast.error(`Failed to create file: ${error.message}`);
    }
  };
  
  // Handle new folder button click
  const handleNewFolder = async () => {
    const folderName = prompt('Enter folder name:');
    if (!folderName) return;
    
    try {
      const path = `${currentPath}/${folderName}`.replace(/\/+/g, '/');
      await createDirectory(path);
      toast.success(`Directory ${folderName} created`);
      loadFiles(currentPath);
    } catch (error: any) {
      console.error("Error creating directory:", error);
      toast.error(`Failed to create directory: ${error.message}`);
    }
  };

  // Handle refresh button click
  const handleRefresh = () => {
    loadFiles(currentPath);
  };

  // Navigate to parent directory
  const navigateToParent = () => {
    if (currentPath === '/') return;
    
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
    setCurrentPath(parentPath);
  };
  
  return (
    <div className={cn("flex flex-col h-full border-r border-border", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b border-border bg-muted/30">
        <div className="flex items-center">
          <FolderTree className="h-4 w-4 mr-2 text-muted-foreground" />
          <span className="text-sm font-medium">Container Files</span>
        </div>
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handleNewFile}>
            <FilePlus className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handleNewFolder}>
            <FolderPlus className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handleRefresh}>
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      </div>
      
      {/* Path navigation */}
      <div className="p-2 border-b border-border">
        <div className="flex items-center gap-1">
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-7 w-7" 
            onClick={navigateToParent}
            disabled={currentPath === '/'}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M15 18l-6-6 6-6" />
            </svg>
          </Button>
          <div className="flex-1 text-sm truncate bg-muted/50 px-2 py-1 rounded">
            {currentPath}
          </div>
        </div>
      </div>
      
      {/* Search */}
      <div className="p-2 border-b border-border">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search files"
            className="pl-8 h-9 text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      
      {/* Container status warning */}
      {containerStatus !== "ready" && (
        <Alert className="m-2">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Container not ready</AlertTitle>
          <AlertDescription>
            The container is not running. Start the container to access files.
          </AlertDescription>
        </Alert>
      )}
      
      {/* Error message */}
      {error && (
        <Alert className="m-2" variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {/* File Tree */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <FileTree
            files={filteredFileTree}
            onFileSelect={handleFileSelect}
            selectedFilePath={selectedFilePath || undefined}
            expandedByDefault={true}
            className="border-0 rounded-none h-full"
          />
        )}
      </div>
    </div>
  );
}
