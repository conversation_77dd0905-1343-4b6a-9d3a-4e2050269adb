"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ResourceMeter } from "@/components/proxmox/resource-meter"
import { VMActivityLog } from "@/components/dashboard/vm-activity-log"
import { LxdContainerList } from "@/components/admin/lxd-container-list"
import { 
  ArrowLeft, 
  Server, 
  Play, 
  Square, 
  RotateCw, 
  Terminal, 
  RefreshCw,
  Loader2,
  Cpu,
  Memory,
  HardDrive,
  Wifi,
  Globe,
  Calendar,
  DollarSign,
  Shield,
  Box
} from "lucide-react"
import { formatBytes } from "@/lib/utils/format"
import { cn } from "@/lib/utils"

interface UserVMDetailsProps {
  vm: any
  activityLog: any[]
  onBack: () => void
  onAction: (action: string, vmId: string) => void
  isLoading: boolean
}

export function UserVMDetails({ vm, activityLog, onBack, onAction, isLoading }: UserVMDetailsProps) {
  const [activeTab, setActiveTab] = useState("overview")
  
  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "bg-green-500 text-white"
      case "stopped":
        return "bg-red-500 text-white"
      case "starting":
        return "bg-blue-500 text-white"
      case "stopping":
        return "bg-yellow-500 text-white"
      default:
        return "bg-gray-500 text-white"
    }
  }
  
  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }
  
  // Mock LXD containers for this VM
  const mockContainers = vm.containers > 0 ? Array.from({ length: vm.containers }, (_, i) => ({
    id: `lxd-container-${i + 1}`,
    name: `${vm.name}-container-${i + 1}`,
    status: "running",
    host: vm.id,
    ipAddress: `10.0.${vm.id.split('-')[1]}.${i + 1}`,
    cpu: { usage: Math.random() * 0.5, cores: 1 },
    memory: { used: Math.random() * 512 * 1024 * 1024, total: 1024 * 1024 * 1024 },
    disk: { used: Math.random() * 10 * 1024 * 1024 * 1024, total: 20 * 1024 * 1024 * 1024 },
    uptime: Math.random() * 604800, // Up to 7 days
  })) : []
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">{vm.name}</h1>
          <Badge className={cn("ml-2", getStatusColor(vm.status))}>
            {vm.status}
          </Badge>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => onAction("refresh", vm.id)}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          
          {vm.status === "stopped" && (
            <Button 
              onClick={() => onAction("start", vm.id)}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Play className="mr-2 h-4 w-4" />
              )}
              Start
            </Button>
          )}
          
          {vm.status === "running" && (
            <>
              <Button 
                variant="outline"
                onClick={() => onAction("restart", vm.id)}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <RotateCw className="mr-2 h-4 w-4" />
                )}
                Restart
              </Button>
              
              <Button 
                variant="outline"
                onClick={() => onAction("stop", vm.id)}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Square className="mr-2 h-4 w-4" />
                )}
                Stop
              </Button>
              
              <Button 
                onClick={() => onAction("console", vm.id)}
                disabled={isLoading}
              >
                <Terminal className="mr-2 h-4 w-4" />
                Console
              </Button>
            </>
          )}
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="containers">
            Containers
            {vm.containers > 0 && (
              <Badge variant="secondary" className="ml-2">{vm.containers}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="backups">Backups</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>VM Information</CardTitle>
                <CardDescription>
                  Details about your virtual machine
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium flex items-center">
                      <Server className="mr-2 h-4 w-4 text-muted-foreground" />
                      VM ID
                    </p>
                    <p>{vm.id}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium flex items-center">
                      <Globe className="mr-2 h-4 w-4 text-muted-foreground" />
                      IP Address
                    </p>
                    <p>{vm.ipAddress}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium flex items-center">
                      <Globe className="mr-2 h-4 w-4 text-muted-foreground" />
                      Region
                    </p>
                    <p>{vm.region}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium flex items-center">
                      <Shield className="mr-2 h-4 w-4 text-muted-foreground" />
                      Operating System
                    </p>
                    <p>{vm.os}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      Created
                    </p>
                    <p>{formatDate(vm.createdAt)}</p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium flex items-center">
                      <DollarSign className="mr-2 h-4 w-4 text-muted-foreground" />
                      Monthly Cost
                    </p>
                    <p>${vm.price.toFixed(2)}/month</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Resources</h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Cpu className="h-4 w-4 text-muted-foreground mr-2" />
                        <span>CPU</span>
                      </div>
                      <span>{vm.cpu.cores} cores</span>
                    </div>
                    
                    {vm.status === "running" && (
                      <ResourceMeter
                        label=""
                        value={vm.cpu.usage}
                        max={1}
                        showPercentage={true}
                        size="md"
                      />
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Memory className="h-4 w-4 text-muted-foreground mr-2" />
                        <span>Memory</span>
                      </div>
                      <span>{formatBytes(vm.memory.total)}</span>
                    </div>
                    
                    {vm.status === "running" && (
                      <ResourceMeter
                        label=""
                        value={vm.memory.used}
                        max={vm.memory.total}
                        unit="GB"
                        showPercentage={true}
                        size="md"
                      />
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <HardDrive className="h-4 w-4 text-muted-foreground mr-2" />
                        <span>Storage</span>
                      </div>
                      <span>{formatBytes(vm.storage.total)}</span>
                    </div>
                    
                    <ResourceMeter
                      label=""
                      value={vm.storage.used}
                      max={vm.storage.total}
                      unit="GB"
                      showPercentage={true}
                      size="md"
                    />
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Wifi className="h-4 w-4 text-muted-foreground mr-2" />
                        <span>Bandwidth</span>
                      </div>
                      <span>{formatBytes(vm.bandwidth.total)}</span>
                    </div>
                    
                    <ResourceMeter
                      label=""
                      value={vm.bandwidth.used}
                      max={vm.bandwidth.total}
                      unit="GB"
                      showPercentage={true}
                      size="md"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => onAction("snapshot", vm.id)}
                  >
                    <Camera className="mr-2 h-4 w-4" />
                    Create Snapshot
                  </Button>
                  
                  <Button 
                    className="w-full justify-start" 
                    variant="outline"
                    onClick={() => onAction("resize", vm.id)}
                  >
                    <Cpu className="mr-2 h-4 w-4" />
                    Resize VM
                  </Button>
                  
                  {vm.containers > 0 && (
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => setActiveTab("containers")}
                    >
                      <Box className="mr-2 h-4 w-4" />
                      Manage Containers
                    </Button>
                  )}
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <VMActivityLog 
                    activities={activityLog.slice(0, 5)} 
                    vms={[vm]} 
                  />
                </CardContent>
                <CardFooter>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="w-full"
                    onClick={() => setActiveTab("activity")}
                  >
                    View All Activity
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="containers">
          <Card>
            <CardHeader>
              <CardTitle>LXD Containers</CardTitle>
              <CardDescription>
                Manage LXD containers running on this VM
              </CardDescription>
            </CardHeader>
            <CardContent>
              {vm.containers === 0 ? (
                <div className="text-center py-8 border rounded-md">
                  <Box className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                  <h3 className="text-lg font-medium">No Containers</h3>
                  <p className="text-muted-foreground mb-4">
                    This VM doesn't have any LXD containers yet
                  </p>
                  <Button onClick={() => onAction("create-container", vm.id)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Container
                  </Button>
                </div>
              ) : (
                <LxdContainerList 
                  containers={mockContainers} 
                  vms={[vm]} 
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>Activity Log</CardTitle>
              <CardDescription>
                Recent actions performed on this VM
              </CardDescription>
            </CardHeader>
            <CardContent>
              <VMActivityLog 
                activities={activityLog} 
                vms={[vm]} 
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="backups">
          <Card>
            <CardHeader>
              <CardTitle>Backups & Snapshots</CardTitle>
              <CardDescription>
                Manage backups and snapshots for this VM
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 border rounded-md">
                <Camera className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                <h3 className="text-lg font-medium">No Backups or Snapshots</h3>
                <p className="text-muted-foreground mb-4">
                  You haven't created any backups or snapshots for this VM yet
                </p>
                <div className="flex justify-center gap-4">
                  <Button onClick={() => onAction("snapshot", vm.id)}>
                    Create Snapshot
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => onAction("backup", vm.id)}
                  >
                    Create Backup
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>VM Settings</CardTitle>
              <CardDescription>
                Configure settings for your virtual machine
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  VM settings management is not implemented in this demo
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
