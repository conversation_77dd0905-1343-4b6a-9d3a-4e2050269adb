"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Server, 
  Cpu, 
  Memory, 
  HardDrive, 
  Wifi,
  DollarSign
} from "lucide-react"
import { formatBytes } from "@/lib/utils/format"

interface VMUsageStatsProps {
  vms: any[]
}

export function VMUsageStats({ vms }: VMUsageStatsProps) {
  // Calculate total VMs
  const totalVMs = vms.length
  const runningVMs = vms.filter(vm => vm.status === "running").length
  
  // Calculate total resources
  const totalCPUCores = vms.reduce((sum, vm) => sum + vm.cpu.cores, 0)
  const totalMemory = vms.reduce((sum, vm) => sum + vm.memory.total, 0)
  const totalStorage = vms.reduce((sum, vm) => sum + vm.storage.total, 0)
  
  // Calculate used resources (only for running VMs)
  const usedCPU = vms
    .filter(vm => vm.status === "running")
    .reduce((sum, vm) => sum + (vm.cpu.usage * vm.cpu.cores), 0)
  
  const usedMemory = vms
    .filter(vm => vm.status === "running")
    .reduce((sum, vm) => sum + vm.memory.used, 0)
  
  const usedStorage = vms.reduce((sum, vm) => sum + vm.storage.used, 0)
  
  // Calculate total monthly cost
  const totalMonthlyCost = vms.reduce((sum, vm) => sum + vm.price, 0)
  
  // Calculate total containers
  const totalContainers = vms.reduce((sum, vm) => sum + (vm.containers || 0), 0)
  
  return (
    <>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Virtual Machines</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center">
            <Server className="h-8 w-8 text-primary mr-2" />
            <div>
              <p className="text-2xl font-bold">{runningVMs} / {totalVMs}</p>
              <p className="text-xs text-muted-foreground">Running VMs</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center">
            <Cpu className="h-8 w-8 text-blue-500 mr-2" />
            <div>
              <p className="text-2xl font-bold">{usedCPU.toFixed(1)} / {totalCPUCores}</p>
              <p className="text-xs text-muted-foreground">CPU Cores</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center">
            <Memory className="h-8 w-8 text-purple-500 mr-2" />
            <div>
              <p className="text-2xl font-bold">
                {formatBytes(usedMemory, 1).split(' ')[0]} / {formatBytes(totalMemory, 1).split(' ')[0]} {formatBytes(totalMemory, 1).split(' ')[1]}
              </p>
              <p className="text-xs text-muted-foreground">Memory</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Monthly Cost</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-500 mr-2" />
            <div>
              <p className="text-2xl font-bold">${totalMonthlyCost}</p>
              <p className="text-xs text-muted-foreground">Per Month</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}
