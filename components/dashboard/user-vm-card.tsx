"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Server, 
  Play, 
  Square, 
  RotateCw, 
  Terminal, 
  MoreHorizontal,
  Loader2,
  Box,
  ChevronRight
} from "lucide-react"
import { formatBytes } from "@/lib/utils/format"
import { ResourceMeter } from "@/components/proxmox/resource-meter"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

interface UserVMCardProps {
  vm: any
  onSelect: () => void
  onAction: (action: string) => void
  isLoading: boolean
}

export function UserVMCard({ vm, onSelect, onAction, isLoading }: UserVMCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  
  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "bg-green-500 text-white"
      case "stopped":
        return "bg-red-500 text-white"
      case "starting":
        return "bg-blue-500 text-white"
      case "stopping":
        return "bg-yellow-500 text-white"
      default:
        return "bg-gray-500 text-white"
    }
  }
  
  return (
    <Card 
      className={cn(
        "transition-all duration-200",
        isHovered ? "shadow-md" : "",
        "overflow-hidden"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="bg-muted rounded-md p-2 mt-1">
              <Server className="h-6 w-6 text-primary" />
            </div>
            
            <div>
              <div className="flex items-center">
                <h3 className="font-medium text-lg">{vm.name}</h3>
                <Badge className={cn("ml-2", getStatusColor(vm.status))}>
                  {vm.status}
                </Badge>
              </div>
              
              <p className="text-sm text-muted-foreground">{vm.description}</p>
              
              <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-1 text-sm">
                <div className="flex items-center">
                  <span className="text-muted-foreground mr-1">IP:</span>
                  <span>{vm.ipAddress}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-muted-foreground mr-1">Region:</span>
                  <span>{vm.region}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-muted-foreground mr-1">CPU:</span>
                  <span>{vm.cpu.cores} cores</span>
                </div>
                <div className="flex items-center">
                  <span className="text-muted-foreground mr-1">Memory:</span>
                  <span>{formatBytes(vm.memory.total)}</span>
                </div>
              </div>
              
              {vm.status === "running" && (
                <div className="mt-3 space-y-2">
                  <ResourceMeter
                    label="CPU"
                    value={vm.cpu.usage}
                    max={1}
                    showPercentage={true}
                    size="sm"
                  />
                  <ResourceMeter
                    label="Memory"
                    value={vm.memory.used}
                    max={vm.memory.total}
                    unit="GB"
                    showPercentage={true}
                    size="sm"
                  />
                </div>
              )}
              
              {vm.containers > 0 && (
                <div className="mt-3 flex items-center text-sm">
                  <Box className="h-4 w-4 text-green-500 mr-1" />
                  <span>{vm.containers} LXD containers</span>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex flex-col items-end space-y-2">
            <div className="flex space-x-1">
              {vm.status === "stopped" && (
                <Button 
                  size="icon" 
                  variant="outline"
                  onClick={() => onAction("start")}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                </Button>
              )}
              
              {vm.status === "running" && (
                <>
                  <Button 
                    size="icon" 
                    variant="outline"
                    onClick={() => onAction("stop")}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Square className="h-4 w-4" />
                    )}
                  </Button>
                  
                  <Button 
                    size="icon" 
                    variant="outline"
                    onClick={() => onAction("restart")}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RotateCw className="h-4 w-4" />
                    )}
                  </Button>
                  
                  <Button 
                    size="icon" 
                    variant="outline"
                    onClick={() => onAction("console")}
                    disabled={isLoading}
                  >
                    <Terminal className="h-4 w-4" />
                  </Button>
                </>
              )}
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    size="icon" 
                    variant="outline"
                    disabled={isLoading}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>VM Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={onSelect}>
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onAction("snapshot")}>
                    Create Snapshot
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onAction("backup")}>
                    Create Backup
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onAction("resize")}>
                    Resize VM
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="text-destructive focus:text-destructive"
                    onClick={() => onAction("delete")}
                  >
                    Delete VM
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-xs"
              onClick={onSelect}
            >
              View Details
              <ChevronRight className="ml-1 h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
