"use client"

import { 
  Play, 
  Square, 
  RotateCw, 
  Plus, 
  Camera, 
  Clock,
  Server
} from "lucide-react"
import { cn } from "@/lib/utils"

interface Activity {
  id: string
  vmId: string
  action: string
  timestamp: Date
  user: string
  details: string
}

interface VMActivityLogProps {
  activities: Activity[]
  vms: any[]
}

export function VMActivityLog({ activities, vms }: VMActivityLogProps) {
  // Get VM name by ID
  const getVMName = (vmId: string) => {
    const vm = vms.find(vm => vm.id === vmId)
    return vm ? vm.name : vmId
  }
  
  // Format relative time
  const formatRelativeTime = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    const diffHour = Math.floor(diffMin / 60)
    const diffDay = Math.floor(diffHour / 24)
    
    if (diffDay > 0) {
      return `${diffDay}d ago`
    } else if (diffHour > 0) {
      return `${diffHour}h ago`
    } else if (diffMin > 0) {
      return `${diffMin}m ago`
    } else {
      return `${diffSec}s ago`
    }
  }
  
  // Get action icon
  const getActionIcon = (action: string) => {
    switch (action) {
      case "start":
        return <Play className="h-4 w-4 text-green-500" />
      case "stop":
        return <Square className="h-4 w-4 text-red-500" />
      case "restart":
        return <RotateCw className="h-4 w-4 text-blue-500" />
      case "create":
        return <Plus className="h-4 w-4 text-purple-500" />
      case "snapshot":
        return <Camera className="h-4 w-4 text-orange-500" />
      default:
        return <Server className="h-4 w-4 text-gray-500" />
    }
  }
  
  // Get action color
  const getActionColor = (action: string) => {
    switch (action) {
      case "start":
        return "text-green-500 bg-green-50 dark:bg-green-950/20"
      case "stop":
        return "text-red-500 bg-red-50 dark:bg-red-950/20"
      case "restart":
        return "text-blue-500 bg-blue-50 dark:bg-blue-950/20"
      case "create":
        return "text-purple-500 bg-purple-50 dark:bg-purple-950/20"
      case "snapshot":
        return "text-orange-500 bg-orange-50 dark:bg-orange-950/20"
      default:
        return "text-gray-500 bg-gray-50 dark:bg-gray-800/20"
    }
  }
  
  return (
    <div className="space-y-4">
      {activities.length === 0 ? (
        <div className="text-center py-6 text-muted-foreground">
          No recent activity
        </div>
      ) : (
        <div className="space-y-3">
          {activities.map((activity) => (
            <div 
              key={activity.id}
              className="flex items-start space-x-3"
            >
              <div className={cn(
                "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                getActionColor(activity.action)
              )}>
                {getActionIcon(activity.action)}
              </div>
              
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium">
                  <span className="capitalize">{activity.action}</span> VM: {getVMName(activity.vmId)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {activity.details}
                </p>
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatRelativeTime(activity.timestamp)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
