"use client";

import * as React from "react";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useWorkspaceStore } from "@/lib/stores/workspace-store";
import {
  Cpu,
  Server,
  Timer,
  HardDrive,
  Package,
  FileDown,
  Network,
  RefreshCw,
  Sparkles,
  PlusCircle,
  Trash2,
  Clock,
  <PERSON>arm<PERSON><PERSON>,
} from "lucide-react";

export interface EphemeralVmConfig {
  name?: string;
  template: string;
  memSizeMib: number;
  vcpuCount: number;
  diskSizeGB: number;
  networkEnabled: boolean;
  autoShutdownMinutes: number;
  rootfsImage?: string;
  kernelImage?: string;
}

export interface EphemeralVmCreatorProps {
  onVmCreated?: (vmId: string) => void;
  onError?: (error: Error) => void;
  projectId?: string;
  className?: string;
}

export function EphemeralVmCreator({
  onVmCreated,
  onError,
  projectId,
  className,
}: EphemeralVmCreatorProps) {
  // Initial VM configuration
  const defaultConfig: EphemeralVmConfig = {
    template: "ubuntu",
    memSizeMib: 2048,
    vcpuCount: 2,
    diskSizeGB: 10,
    networkEnabled: true,
    autoShutdownMinutes: 30,
  };

  // State variables
  const [vmConfig, setVmConfig] = useState<EphemeralVmConfig>(defaultConfig);
  const [creationMethod, setCreationMethod] = useState<"template" | "image">("template");
  const [isLoading, setIsLoading] = useState(false);
  const [availableImages, setAvailableImages] = useState<{
    rootfs: { name: string; path: string }[];
    kernel: { name: string; path: string }[];
  }>({ rootfs: [], kernel: [] });
  
  // Get containerId setter from workspace store
  const { setContainerId, setContainerType, setContainerStatus } = useWorkspaceStore();

  // Load available images when creation method changes to "image"
  React.useEffect(() => {
    if (creationMethod === "image") {
      fetchAvailableImages();
    }
  }, [creationMethod]);

  // Function to fetch available VM images
  const fetchAvailableImages = async () => {
    try {
      const response = await fetch("/api/containerization/microvm/images");
      if (response.ok) {
        const data = await response.json();
        setAvailableImages({
          rootfs: data.rootfsImages || [],
          kernel: data.kernelImages || [],
        });
      } else {
        console.error("Failed to fetch VM images");
      }
    } catch (error) {
      console.error("Error fetching VM images:", error);
    }
  };

  // Function to create an ephemeral VM
  const createEphemeralVM = async () => {
    setIsLoading(true);
    
    try {
      // Generate a random name if not provided
      const vmName = vmConfig.name || `ephemeral-${Date.now().toString().slice(-6)}`;
      
      let endpoint, payload;
      
      if (creationMethod === "template") {
        // Create from template
        endpoint = "/api/containerization/microvm/create";
        payload = {
          name: vmName,
          projectId: projectId || "default",
          template: vmConfig.template,
          memSizeMib: vmConfig.memSizeMib,
          vcpuCount: vmConfig.vcpuCount,
          networkEnabled: vmConfig.networkEnabled,
          ephemeral: true,
          expiresInMinutes: vmConfig.autoShutdownMinutes
        };
      } else {
        // Create from image
        endpoint = "/api/containerization/microvm/create-from-image";
        payload = {
          name: vmName,
          projectId: projectId || "default",
          kernelImagePath: vmConfig.kernelImage,
          rootfsImagePath: vmConfig.rootfsImage,
          memSizeMib: vmConfig.memSizeMib,
          vcpuCount: vmConfig.vcpuCount,
          networkEnabled: vmConfig.networkEnabled,
          ephemeral: true,
          expiresInMinutes: vmConfig.autoShutdownMinutes
        };
      }
      
      // Send the request
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create VM: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      const vmId = data.id || data.vmId;
      
      if (!vmId) {
        throw new Error("No VM ID returned from creation API");
      }
      
      // Update workspace store
      setContainerId(vmId);
      setContainerType("microvm");
      setContainerStatus("running");
      
      // Show success message
      toast({
        title: "Ephemeral VM Created",
        description: `VM ${vmName} created and will automatically shutdown after ${vmConfig.autoShutdownMinutes} minutes`,
      });
      
      // Call the callback if provided
      if (onVmCreated) {
        onVmCreated(vmId);
      }
    } catch (error) {
      console.error("Error creating ephemeral VM:", error);
      
      toast({
        title: "VM Creation Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
      
      if (onError && error instanceof Error) {
        onError(error);
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // Reset configuration to defaults
  const resetConfig = () => {
    setVmConfig(defaultConfig);
  };
  
  return (
    <Card className={`w-full max-w-3xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-500" />
            <span>Create Ephemeral VM</span>
          </div>
          <Badge variant="outline" className="text-amber-600 border-amber-300 bg-amber-50 dark:bg-amber-950 dark:border-amber-800">
            <Clock className="h-3 w-3 mr-1" />
            Auto-Cleanup
          </Badge>
        </CardTitle>
        <CardDescription>
          Create a temporary VM that will automatically be cleaned up after the specified duration
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="template" onValueChange={(value) => setCreationMethod(value as "template" | "image")}>
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="template">
              <Package className="h-4 w-4 mr-2" />
              From Template
            </TabsTrigger>
            <TabsTrigger value="image">
              <HardDrive className="h-4 w-4 mr-2" />
              From Image
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="template" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="vm-name">VM Name (Optional)</Label>
              <Input
                id="vm-name"
                placeholder="Auto-generated if empty"
                value={vmConfig.name || ""}
                onChange={(e) => setVmConfig((prev) => ({ ...prev, name: e.target.value }))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="template">Template</Label>
              <Select
                value={vmConfig.template}
                onValueChange={(value) => setVmConfig((prev) => ({ ...prev, template: value }))}
              >
                <SelectTrigger id="template">
                  <SelectValue placeholder="Select template" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ubuntu">Ubuntu 22.04 LTS</SelectItem>
                  <SelectItem value="alpine">Alpine Linux 3.14</SelectItem>
                  <SelectItem value="debian">Debian 11</SelectItem>
                  <SelectItem value="nodejs">Node.js Development</SelectItem>
                  <SelectItem value="python">Python Development</SelectItem>
                  <SelectItem value="minimal">Minimal</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </TabsContent>
          
          <TabsContent value="image" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="vm-name-image">VM Name (Optional)</Label>
              <Input
                id="vm-name-image"
                placeholder="Auto-generated if empty"
                value={vmConfig.name || ""}
                onChange={(e) => setVmConfig((prev) => ({ ...prev, name: e.target.value }))}
              />
            </div>
            
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="kernel-image">Kernel Image</Label>
                <Select
                  value={vmConfig.kernelImage}
                  onValueChange={(value) => setVmConfig((prev) => ({ ...prev, kernelImage: value }))}
                >
                  <SelectTrigger id="kernel-image">
                    <SelectValue placeholder="Select kernel image" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableImages.kernel.length > 0 ? (
                      availableImages.kernel.map((image) => (
                        <SelectItem key={image.path} value={image.path}>
                          {image.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="" disabled>
                        No kernel images available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="rootfs-image">Root Filesystem Image</Label>
                <Select
                  value={vmConfig.rootfsImage}
                  onValueChange={(value) => setVmConfig((prev) => ({ ...prev, rootfsImage: value }))}
                >
                  <SelectTrigger id="rootfs-image">
                    <SelectValue placeholder="Select rootfs image" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableImages.rootfs.length > 0 ? (
                      availableImages.rootfs.map((image) => (
                        <SelectItem key={image.path} value={image.path}>
                          {image.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="" disabled>
                        No rootfs images available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
          
          <div className="space-y-6 pt-6">
            <div className="space-y-4">
              <h3 className="text-sm font-medium leading-none">Resources</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="memory">Memory (MB)</Label>
                    <span className="text-sm text-muted-foreground">{vmConfig.memSizeMib} MB</span>
                  </div>
                  <Slider
                    id="memory"
                    min={512}
                    max={8192}
                    step={512}
                    value={[vmConfig.memSizeMib]}
                    onValueChange={(value) => setVmConfig((prev) => ({ ...prev, memSizeMib: value[0] }))}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>512 MB</span>
                    <span>8 GB</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="cpu">CPU Cores</Label>
                    <span className="text-sm text-muted-foreground">{vmConfig.vcpuCount} cores</span>
                  </div>
                  <Slider
                    id="cpu"
                    min={1}
                    max={8}
                    step={1}
                    value={[vmConfig.vcpuCount]}
                    onValueChange={(value) => setVmConfig((prev) => ({ ...prev, vcpuCount: value[0] }))}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>1 core</span>
                    <span>8 cores</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="network"
                  checked={vmConfig.networkEnabled}
                  onCheckedChange={(checked) =>
                    setVmConfig((prev) => ({ ...prev, networkEnabled: checked }))
                  }
                />
                <Label htmlFor="network" className="flex items-center gap-2">
                  <Network className="h-4 w-4" />
                  Enable network
                </Label>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-sm font-medium leading-none">Auto-Shutdown</h3>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="shutdown">Auto-shutdown after</Label>
                  <span className="text-sm text-muted-foreground">{vmConfig.autoShutdownMinutes} minutes</span>
                </div>
                <Slider
                  id="shutdown"
                  min={5}
                  max={120}
                  step={5}
                  value={[vmConfig.autoShutdownMinutes]}
                  onValueChange={(value) => setVmConfig((prev) => ({ ...prev, autoShutdownMinutes: value[0] }))}
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>5 minutes</span>
                  <span>2 hours</span>
                </div>
              </div>
            </div>
          </div>
        </Tabs>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={resetConfig} disabled={isLoading}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Reset
        </Button>
        
        <div className="flex gap-2">
          <Button
            variant="default"
            onClick={createEphemeralVM}
            disabled={
              isLoading || 
              (creationMethod === "image" && (!vmConfig.kernelImage || !vmConfig.rootfsImage))
            }
          >
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <PlusCircle className="h-4 w-4 mr-2" />
                Create Ephemeral VM
              </>
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
} 