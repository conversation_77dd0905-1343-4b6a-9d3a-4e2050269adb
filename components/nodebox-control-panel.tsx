/**
 * Nodebox Control Panel
 * 
 * A comprehensive control panel for managing Nodebox instances
 * Demonstrates the usage of the new Nodebox store
 */

"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Square, 
  Trash2, 
  Plus, 
  Terminal, 
  Monitor, 
  FileText, 
  Settings,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { 
  useNodeboxInstances,
  useActiveNodeboxInstance,
  useNodeboxLoading,
  useNodeboxError,
  useNodeboxTemplates,
  useNodeboxActions,
  useNodeboxProcesses,
  useNodeboxPreviews
} from '@/lib/stores/nodebox-store';
import { ProjectTemplate } from '@/lib/nodebox-runtime/api/nodebox-types';

interface NodeboxControlPanelProps {
  projectId?: string;
  className?: string;
}

export function NodeboxControlPanel({ projectId, className }: NodeboxControlPanelProps) {
  // Store state
  const instances = useNodeboxInstances();
  const activeInstance = useActiveNodeboxInstance();
  const isLoading = useNodeboxLoading();
  const error = useNodeboxError();
  const templates = useNodeboxTemplates();
  const actions = useNodeboxActions();
  
  // Get processes and previews for active instance
  const processes = useNodeboxProcesses(activeInstance?.id);
  const previews = useNodeboxPreviews(activeInstance?.id);

  // Local state for new instance creation
  const [newInstanceName, setNewInstanceName] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<ProjectTemplate>('nextjs');
  const [commandInput, setCommandInput] = useState('npm run dev');

  // Handle creating a new instance
  const handleCreateInstance = async () => {
    if (!newInstanceName.trim()) return;
    
    await actions.createFromTemplate(selectedTemplate, newInstanceName, projectId);
    setNewInstanceName('');
  };

  // Handle running a command
  const handleRunCommand = async () => {
    if (!activeInstance || !commandInput.trim()) return;
    
    const [command, ...args] = commandInput.split(' ');
    await actions.runCommand(activeInstance.id, command, args);
    setCommandInput('');
  };

  // Handle getting preview
  const handleGetPreview = async () => {
    if (!activeInstance) return;
    await actions.getPreview(activeInstance.id);
  };

  return (
    <div className={`space-y-6 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Nodebox Control Panel</h2>
          <p className="text-muted-foreground">
            Manage your Nodebox instances and development environment
          </p>
        </div>
        
        {/* Status indicator */}
        <div className="flex items-center gap-2">
          {isLoading ? (
            <Badge variant="secondary" className="gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              Loading
            </Badge>
          ) : error ? (
            <Badge variant="destructive" className="gap-1">
              <AlertCircle className="h-3 w-3" />
              Error
            </Badge>
          ) : activeInstance ? (
            <Badge variant="default" className="gap-1">
              <CheckCircle className="h-3 w-3" />
              Connected
            </Badge>
          ) : (
            <Badge variant="outline" className="gap-1">
              <Clock className="h-3 w-3" />
              Ready
            </Badge>
          )}
        </div>
      </div>

      {/* Error display */}
      {error && (
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{error.message}</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2"
              onClick={actions.clearError}
            >
              Clear Error
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Create New Instance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create New Instance
            </CardTitle>
            <CardDescription>
              Create a new Nodebox instance from a template
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="instance-name">Instance Name</Label>
              <Input
                id="instance-name"
                placeholder="My awesome app"
                value={newInstanceName}
                onChange={(e) => setNewInstanceName(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="template">Template</Label>
              <Select value={selectedTemplate} onValueChange={(value) => setSelectedTemplate(value as ProjectTemplate)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a template" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(templates).map(([key, template]) => (
                    <SelectItem key={key} value={key}>
                      {template.name} - {template.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Button 
              onClick={handleCreateInstance} 
              disabled={!newInstanceName.trim() || isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Instance
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Active Instance Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Active Instance
            </CardTitle>
            <CardDescription>
              Information about the currently active instance
            </CardDescription>
          </CardHeader>
          <CardContent>
            {activeInstance ? (
              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium">Name</Label>
                  <p className="text-sm text-muted-foreground">{activeInstance.config.name}</p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium">Template</Label>
                  <p className="text-sm text-muted-foreground">{activeInstance.config.template || 'Custom'}</p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <Badge variant={activeInstance.status === 'ready' ? 'default' : 'secondary'}>
                    {activeInstance.status}
                  </Badge>
                </div>
                
                <div>
                  <Label className="text-sm font-medium">Created</Label>
                  <p className="text-sm text-muted-foreground">
                    {activeInstance.createdAt.toLocaleString()}
                  </p>
                </div>
                
                <Separator />
                
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => actions.loadFileSystem(activeInstance.id)}
                  >
                    <FileText className="mr-1 h-3 w-3" />
                    Load Files
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleGetPreview}
                  >
                    <Monitor className="mr-1 h-3 w-3" />
                    Get Preview
                  </Button>
                  
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => actions.destroyInstance(activeInstance.id)}
                  >
                    <Trash2 className="mr-1 h-3 w-3" />
                    Delete
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No active instance. Create one to get started.
              </p>
            )}
          </CardContent>
        </Card>

        {/* Terminal Commands */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Terminal className="h-4 w-4" />
              Terminal Commands
            </CardTitle>
            <CardDescription>
              Run commands in the active instance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input
                placeholder="npm run dev"
                value={commandInput}
                onChange={(e) => setCommandInput(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleRunCommand()}
                disabled={!activeInstance}
              />
              <Button 
                onClick={handleRunCommand}
                disabled={!activeInstance || !commandInput.trim()}
              >
                <Play className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Recent processes */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Recent Processes</Label>
              <ScrollArea className="h-32 border rounded p-2">
                {processes.length > 0 ? (
                  <div className="space-y-1">
                    {processes.slice(-5).map((process) => (
                      <div key={process.id} className="flex items-center justify-between text-xs">
                        <span className="font-mono">{process.command} {process.args.join(' ')}</span>
                        <Badge variant={
                          process.status === 'completed' ? 'default' :
                          process.status === 'running' ? 'secondary' :
                          process.status === 'failed' ? 'destructive' : 'outline'
                        }>
                          {process.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-xs text-muted-foreground">No processes yet</p>
                )}
              </ScrollArea>
            </div>
          </CardContent>
        </Card>

        {/* All Instances */}
        <Card>
          <CardHeader>
            <CardTitle>All Instances ({instances.length})</CardTitle>
            <CardDescription>
              Manage all your Nodebox instances
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-48">
              {instances.length > 0 ? (
                <div className="space-y-2">
                  {instances.map((instance) => (
                    <div 
                      key={instance.id} 
                      className={`flex items-center justify-between p-2 border rounded cursor-pointer hover:bg-muted/50 ${
                        activeInstance?.id === instance.id ? 'bg-muted' : ''
                      }`}
                      onClick={() => actions.setActiveInstance(instance.id)}
                    >
                      <div>
                        <p className="text-sm font-medium">{instance.config.name}</p>
                        <p className="text-xs text-muted-foreground">{instance.config.template}</p>
                      </div>
                      <Badge variant={instance.status === 'ready' ? 'default' : 'secondary'}>
                        {instance.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No instances created yet</p>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
