"use client"

import React, { useState, useEffect } from 'react'
import { 
  Plugin, 
  PluginSettings as PluginSettingsType, 
  PluginSettingsDefinition,
  getPluginRegistry 
} from '@/lib/plugins'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Loader2, Save, RotateCcw } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useToast } from '@/components/ui/use-toast'

interface PluginSettingsProps {
  plugin: Plugin
  onSave?: (settings: PluginSettingsType) => void
  onCancel?: () => void
  className?: string
}

/**
 * PluginSettings component
 * 
 * Renders a form for editing plugin settings
 */
export function PluginSettings({ plugin, onSave, onCancel, className }: PluginSettingsProps) {
  const [settings, setSettings] = useState<PluginSettingsType>({})
  const [originalSettings, setOriginalSettings] = useState<PluginSettingsType>({})
  const [saving, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const { toast } = useToast()

  // Initialize settings
  useEffect(() => {
    if (plugin) {
      setSettings({ ...plugin.settings })
      setOriginalSettings({ ...plugin.settings })
      setHasChanges(false)
    }
  }, [plugin])

  // Check for changes
  useEffect(() => {
    if (plugin) {
      const changed = JSON.stringify(settings) !== JSON.stringify(originalSettings)
      setHasChanges(changed)
    }
  }, [settings, originalSettings, plugin])

  // Handle setting change
  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // Handle save
  const handleSave = async () => {
    if (!plugin) return

    setSaving(true)

    try {
      // Update settings
      const registry = getPluginRegistry()
      await registry.updatePluginSettings(plugin.manifest.metadata.id, settings)

      // Update original settings
      setOriginalSettings({ ...settings })
      setHasChanges(false)

      // Call onSave callback
      if (onSave) {
        onSave(settings)
      }

      // Show success toast
      toast({
        title: 'Settings saved',
        description: `Settings for ${plugin.manifest.metadata.name} have been saved.`,
      })
    } catch (error) {
      console.error('Failed to save settings:', error)
      
      // Show error toast
      toast({
        title: 'Failed to save settings',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      })
    } finally {
      setSaving(false)
    }
  }

  // Handle reset
  const handleReset = () => {
    if (!plugin) return
    
    setSettings({ ...originalSettings })
    setHasChanges(false)
    
    // Call onCancel callback
    if (onCancel) {
      onCancel()
    }
  }

  // Render setting field
  const renderSettingField = (key: string, definition: PluginSettingsDefinition[string]) => {
    const value = settings[key]
    
    switch (definition.type) {
      case 'string':
        return (
          <div className="grid gap-2">
            <Label htmlFor={key}>{definition.label}</Label>
            <Input
              id={key}
              value={value || ''}
              onChange={(e) => handleSettingChange(key, e.target.value)}
              placeholder={definition.description}
              disabled={saving}
            />
            {definition.description && (
              <p className="text-xs text-muted-foreground">{definition.description}</p>
            )}
          </div>
        )
      
      case 'number':
        return (
          <div className="grid gap-2">
            <Label htmlFor={key}>{definition.label}</Label>
            <Input
              id={key}
              type="number"
              value={value || 0}
              onChange={(e) => handleSettingChange(key, Number(e.target.value))}
              min={definition.min}
              max={definition.max}
              disabled={saving}
            />
            {definition.description && (
              <p className="text-xs text-muted-foreground">{definition.description}</p>
            )}
          </div>
        )
      
      case 'boolean':
        return (
          <div className="flex items-center justify-between gap-2">
            <div>
              <Label htmlFor={key}>{definition.label}</Label>
              {definition.description && (
                <p className="text-xs text-muted-foreground">{definition.description}</p>
              )}
            </div>
            <Switch
              id={key}
              checked={!!value}
              onCheckedChange={(checked) => handleSettingChange(key, checked)}
              disabled={saving}
            />
          </div>
        )
      
      case 'select':
        return (
          <div className="grid gap-2">
            <Label htmlFor={key}>{definition.label}</Label>
            <Select
              value={value || definition.default}
              onValueChange={(value) => handleSettingChange(key, value)}
              disabled={saving}
            >
              <SelectTrigger id={key}>
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent>
                {definition.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {definition.description && (
              <p className="text-xs text-muted-foreground">{definition.description}</p>
            )}
          </div>
        )
      
      case 'multiselect':
        // Convert value to array if not already
        const selectedValues = Array.isArray(value) ? value : value ? [value] : []
        
        return (
          <div className="grid gap-2">
            <Label>{definition.label}</Label>
            <div className="grid gap-2">
              {definition.options?.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${key}-${option.value}`}
                    checked={selectedValues.includes(option.value)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        handleSettingChange(key, [...selectedValues, option.value])
                      } else {
                        handleSettingChange(key, selectedValues.filter(v => v !== option.value))
                      }
                    }}
                    disabled={saving}
                  />
                  <Label htmlFor={`${key}-${option.value}`}>{option.label}</Label>
                </div>
              ))}
            </div>
            {definition.description && (
              <p className="text-xs text-muted-foreground">{definition.description}</p>
            )}
          </div>
        )
      
      case 'color':
        return (
          <div className="grid gap-2">
            <Label htmlFor={key}>{definition.label}</Label>
            <div className="flex gap-2">
              <Input
                id={key}
                type="color"
                value={value || '#000000'}
                onChange={(e) => handleSettingChange(key, e.target.value)}
                className="w-12 h-8 p-1"
                disabled={saving}
              />
              <Input
                value={value || '#000000'}
                onChange={(e) => handleSettingChange(key, e.target.value)}
                className="flex-1"
                disabled={saving}
              />
            </div>
            {definition.description && (
              <p className="text-xs text-muted-foreground">{definition.description}</p>
            )}
          </div>
        )
      
      default:
        return null
    }
  }

  if (!plugin || !plugin.manifest.settings) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle>No Settings Available</CardTitle>
          <CardDescription>
            This plugin does not have any configurable settings.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>Settings: {plugin.manifest.metadata.name}</CardTitle>
        <CardDescription>
          Configure settings for {plugin.manifest.metadata.name} v{plugin.manifest.metadata.version}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {Object.entries(plugin.manifest.settings).map(([key, definition]) => (
          <div key={key} className="py-2">
            {renderSettingField(key, definition)}
          </div>
        ))}
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleReset}
          disabled={saving || !hasChanges}
        >
          <RotateCcw className="mr-2 h-4 w-4" />
          Reset
        </Button>
        
        <Button
          onClick={handleSave}
          disabled={saving || !hasChanges}
        >
          {saving ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Save className="mr-2 h-4 w-4" />
          )}
          Save Changes
        </Button>
      </CardFooter>
    </Card>
  )
}
