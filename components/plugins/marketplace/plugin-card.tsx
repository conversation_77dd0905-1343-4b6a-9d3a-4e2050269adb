"use client"

import React from 'react'
import { PluginPackage, InstallationStatus } from '@/lib/plugins/marketplace'
import { useMarketplace } from './marketplace-provider'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Download, 
  Check, 
  Loader2, 
  ExternalLink, 
  Star, 
  Calendar, 
  Tag
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface PluginCardProps {
  plugin: PluginPackage
  onSelect?: (plugin: PluginPackage) => void
  className?: string
}

/**
 * PluginCard component
 * 
 * Renders a card for a plugin in the marketplace
 */
export function PluginCard({ plugin, onSelect, className }: PluginCardProps) {
  const { 
    installPlugin, 
    isPluginInstalled, 
    installationStatus, 
    loading 
  } = useMarketplace()

  const installed = isPluginInstalled(plugin.id)
  const installing = loading.installation && installationStatus !== InstallationStatus.COMPLETED

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString()
  }

  // Handle install
  const handleInstall = (e: React.MouseEvent) => {
    e.stopPropagation()
    installPlugin(plugin)
  }

  // Handle select
  const handleSelect = () => {
    if (onSelect) {
      onSelect(plugin)
    }
  }

  return (
    <Card 
      className={cn("cursor-pointer hover:border-primary/50 transition-colors", className)}
      onClick={handleSelect}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="flex items-center">
              {plugin.icon && <span className="mr-2">{plugin.icon}</span>}
              {plugin.name}
            </CardTitle>
            <CardDescription>v{plugin.version}</CardDescription>
          </div>
          
          {plugin.rating && (
            <div className="flex items-center text-sm text-muted-foreground">
              <Star className="h-4 w-4 mr-1 fill-yellow-400 text-yellow-400" />
              <span>{plugin.rating.toFixed(1)}</span>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pb-2">
        <p className="text-sm mb-3">{plugin.description}</p>
        
        <div className="flex flex-wrap gap-1 mb-2">
          {plugin.tags?.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
        
        <div className="flex flex-wrap justify-between text-xs text-muted-foreground">
          <div className="flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            {formatDate(plugin.updatedAt)}
          </div>
          
          {plugin.downloads && (
            <div className="flex items-center">
              <Download className="h-3 w-3 mr-1" />
              {plugin.downloads.toLocaleString()}
            </div>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="pt-2">
        {installed ? (
          <Button variant="outline" size="sm" className="w-full" disabled>
            <Check className="h-4 w-4 mr-2" />
            Installed
          </Button>
        ) : (
          <Button 
            variant="default" 
            size="sm" 
            className="w-full"
            onClick={handleInstall}
            disabled={installing}
          >
            {installing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Installing...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Install
              </>
            )}
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
