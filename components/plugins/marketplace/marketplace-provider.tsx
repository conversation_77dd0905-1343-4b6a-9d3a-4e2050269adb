"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { 
  getMarketplaceService, 
  PluginPackage, 
  PluginCategory,
  SearchOptions,
  SearchResult,
  InstallationStatus
} from '@/lib/plugins/marketplace'
import { useToast } from '@/components/ui/use-toast'

interface MarketplaceContextType {
  // Data
  featuredPlugins: PluginPackage[]
  popularPlugins: PluginPackage[]
  recentPlugins: PluginPackage[]
  categories: PluginCategory[]
  searchResults: SearchResult | null
  selectedPlugin: PluginPackage | null
  
  // Loading states
  loading: {
    featured: boolean
    popular: boolean
    recent: boolean
    categories: boolean
    search: boolean
    plugin: boolean
    installation: boolean
  }
  
  // Installation
  installationStatus: InstallationStatus
  installationError: string | null
  
  // Actions
  searchPlugins: (options: SearchOptions) => Promise<void>
  getPlugin: (id: string) => Promise<void>
  installPlugin: (plugin: PluginPackage) => Promise<void>
  uninstallPlugin: (pluginId: string) => Promise<void>
  isPluginInstalled: (pluginId: string) => boolean
  clearSelectedPlugin: () => void
}

const MarketplaceContext = createContext<MarketplaceContextType>({
  // Data
  featuredPlugins: [],
  popularPlugins: [],
  recentPlugins: [],
  categories: [],
  searchResults: null,
  selectedPlugin: null,
  
  // Loading states
  loading: {
    featured: false,
    popular: false,
    recent: false,
    categories: false,
    search: false,
    plugin: false,
    installation: false,
  },
  
  // Installation
  installationStatus: InstallationStatus.IDLE,
  installationError: null,
  
  // Actions
  searchPlugins: async () => {},
  getPlugin: async () => {},
  installPlugin: async () => {},
  uninstallPlugin: async () => {},
  isPluginInstalled: () => false,
  clearSelectedPlugin: () => {},
})

/**
 * Use marketplace context hook
 */
export const useMarketplace = () => useContext(MarketplaceContext)

interface MarketplaceProviderProps {
  children: React.ReactNode
}

/**
 * MarketplaceProvider component
 * 
 * Provides marketplace context to the application
 */
export function MarketplaceProvider({ children }: MarketplaceProviderProps) {
  // Data
  const [featuredPlugins, setFeaturedPlugins] = useState<PluginPackage[]>([])
  const [popularPlugins, setPopularPlugins] = useState<PluginPackage[]>([])
  const [recentPlugins, setRecentPlugins] = useState<PluginPackage[]>([])
  const [categories, setCategories] = useState<PluginCategory[]>([])
  const [searchResults, setSearchResults] = useState<SearchResult | null>(null)
  const [selectedPlugin, setSelectedPlugin] = useState<PluginPackage | null>(null)
  
  // Loading states
  const [loading, setLoading] = useState({
    featured: false,
    popular: false,
    recent: false,
    categories: false,
    search: false,
    plugin: false,
    installation: false,
  })
  
  // Installation
  const [installationStatus, setInstallationStatus] = useState<InstallationStatus>(InstallationStatus.IDLE)
  const [installationError, setInstallationError] = useState<string | null>(null)
  
  const { toast } = useToast()

  // Load initial data
  useEffect(() => {
    loadInitialData()
  }, [])

  // Load initial data
  const loadInitialData = async () => {
    try {
      // Get marketplace service
      const service = getMarketplaceService()
      
      // Load featured plugins
      setLoading(prev => ({ ...prev, featured: true }))
      const featured = await service.getFeaturedPlugins()
      setFeaturedPlugins(featured)
      setLoading(prev => ({ ...prev, featured: false }))
      
      // Load popular plugins
      setLoading(prev => ({ ...prev, popular: true }))
      const popular = await service.getPopularPlugins()
      setPopularPlugins(popular)
      setLoading(prev => ({ ...prev, popular: false }))
      
      // Load recent plugins
      setLoading(prev => ({ ...prev, recent: true }))
      const recent = await service.getRecentPlugins()
      setRecentPlugins(recent)
      setLoading(prev => ({ ...prev, recent: false }))
      
      // Load categories
      setLoading(prev => ({ ...prev, categories: true }))
      const cats = await service.getCategories()
      setCategories(cats)
      setLoading(prev => ({ ...prev, categories: false }))
    } catch (error) {
      console.error('Failed to load initial marketplace data:', error)
      toast({
        title: 'Error',
        description: 'Failed to load marketplace data',
        variant: 'destructive',
      })
    }
  }

  // Search plugins
  const searchPlugins = async (options: SearchOptions) => {
    try {
      setLoading(prev => ({ ...prev, search: true }))
      
      // Get marketplace service
      const service = getMarketplaceService()
      
      // Search plugins
      const results = await service.searchPlugins(options)
      setSearchResults(results)
    } catch (error) {
      console.error('Failed to search plugins:', error)
      toast({
        title: 'Error',
        description: 'Failed to search plugins',
        variant: 'destructive',
      })
    } finally {
      setLoading(prev => ({ ...prev, search: false }))
    }
  }

  // Get plugin
  const getPlugin = async (id: string) => {
    try {
      setLoading(prev => ({ ...prev, plugin: true }))
      
      // Get marketplace service
      const service = getMarketplaceService()
      
      // Get plugin
      const plugin = await service.getPlugin(id)
      setSelectedPlugin(plugin)
    } catch (error) {
      console.error(`Failed to get plugin ${id}:`, error)
      toast({
        title: 'Error',
        description: `Failed to get plugin ${id}`,
        variant: 'destructive',
      })
    } finally {
      setLoading(prev => ({ ...prev, plugin: false }))
    }
  }

  // Install plugin
  const installPlugin = async (plugin: PluginPackage) => {
    try {
      setLoading(prev => ({ ...prev, installation: true }))
      setInstallationStatus(InstallationStatus.DOWNLOADING)
      setInstallationError(null)
      
      // Get marketplace service
      const service = getMarketplaceService()
      
      // Install plugin
      const result = await service.installPlugin(plugin)
      
      // Update status
      setInstallationStatus(result.status)
      
      // Handle error
      if (result.status === InstallationStatus.FAILED) {
        setInstallationError(result.error || 'Unknown error')
        toast({
          title: 'Installation Failed',
          description: result.error || 'Failed to install plugin',
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Installation Complete',
          description: `${plugin.name} has been installed successfully`,
        })
      }
    } catch (error) {
      console.error(`Failed to install plugin ${plugin.id}:`, error)
      setInstallationStatus(InstallationStatus.FAILED)
      setInstallationError(error instanceof Error ? error.message : 'Unknown error')
      toast({
        title: 'Installation Failed',
        description: error instanceof Error ? error.message : 'Failed to install plugin',
        variant: 'destructive',
      })
    } finally {
      setLoading(prev => ({ ...prev, installation: false }))
    }
  }

  // Uninstall plugin
  const uninstallPlugin = async (pluginId: string) => {
    try {
      setLoading(prev => ({ ...prev, installation: true }))
      
      // Get marketplace service
      const service = getMarketplaceService()
      
      // Uninstall plugin
      const success = await service.uninstallPlugin(pluginId)
      
      if (success) {
        toast({
          title: 'Uninstallation Complete',
          description: 'Plugin has been uninstalled successfully',
        })
      } else {
        toast({
          title: 'Uninstallation Failed',
          description: 'Plugin is not installed',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error(`Failed to uninstall plugin ${pluginId}:`, error)
      toast({
        title: 'Uninstallation Failed',
        description: error instanceof Error ? error.message : 'Failed to uninstall plugin',
        variant: 'destructive',
      })
    } finally {
      setLoading(prev => ({ ...prev, installation: false }))
    }
  }

  // Check if plugin is installed
  const isPluginInstalled = (pluginId: string) => {
    const service = getMarketplaceService()
    return service.isPluginInstalled(pluginId)
  }

  // Clear selected plugin
  const clearSelectedPlugin = () => {
    setSelectedPlugin(null)
  }

  const value = {
    // Data
    featuredPlugins,
    popularPlugins,
    recentPlugins,
    categories,
    searchResults,
    selectedPlugin,
    
    // Loading states
    loading,
    
    // Installation
    installationStatus,
    installationError,
    
    // Actions
    searchPlugins,
    getPlugin,
    installPlugin,
    uninstallPlugin,
    isPluginInstalled,
    clearSelectedPlugin,
  }

  return (
    <MarketplaceContext.Provider value={value}>
      {children}
    </MarketplaceContext.Provider>
  )
}
