"use client"

import React, { useState } from 'react'
import { useMarketplace } from './marketplace-provider'
import { MarketplaceSearch } from './marketplace-search'
import { PluginCard } from './plugin-card'
import { PluginDetails } from './plugin-details'
import { PluginPackage } from '@/lib/plugins/marketplace'
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs'
import { 
  Sparkles, 
  TrendingUp, 
  Clock, 
  Search, 
  Loader2 
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'

interface MarketplaceProps {
  className?: string
}

/**
 * Marketplace component
 * 
 * Renders the marketplace UI
 */
export function Marketplace({ className }: MarketplaceProps) {
  const { 
    featuredPlugins,
    popularPlugins,
    recentPlugins,
    searchResults,
    selectedPlugin,
    loading,
    getPlugin,
    clearSelectedPlugin
  } = useMarketplace()
  
  const [activeTab, setActiveTab] = useState('featured')

  // Handle plugin select
  const handlePluginSelect = (plugin: PluginPackage) => {
    getPlugin(plugin.id)
  }

  // Handle back
  const handleBack = () => {
    clearSelectedPlugin()
  }

  // If a plugin is selected, show its details
  if (selectedPlugin) {
    return (
      <ScrollArea className={cn("h-full", className)}>
        <div className="p-4">
          <PluginDetails plugin={selectedPlugin} onBack={handleBack} />
        </div>
      </ScrollArea>
    )
  }

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <div className="p-4 border-b">
        <h1 className="text-2xl font-bold mb-4">Plugin Marketplace</h1>
        <MarketplaceSearch />
      </div>
      
      <ScrollArea className="flex-1">
        <div className="p-4">
          {searchResults ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold flex items-center">
                  <Search className="h-5 w-5 mr-2" />
                  Search Results
                </h2>
                <span className="text-sm text-muted-foreground">
                  {searchResults.total} plugins found
                </span>
              </div>
              
              {loading.search ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : searchResults.plugins.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No plugins found matching your search criteria
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {searchResults.plugins.map((plugin) => (
                    <PluginCard 
                      key={plugin.id} 
                      plugin={plugin} 
                      onSelect={handlePluginSelect}
                    />
                  ))}
                </div>
              )}
            </div>
          ) : (
            <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="featured">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Featured
                </TabsTrigger>
                <TabsTrigger value="popular">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Popular
                </TabsTrigger>
                <TabsTrigger value="recent">
                  <Clock className="h-4 w-4 mr-2" />
                  Recent
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="featured" className="space-y-4">
                <h2 className="text-xl font-semibold flex items-center">
                  <Sparkles className="h-5 w-5 mr-2" />
                  Featured Plugins
                </h2>
                
                {loading.featured ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : featuredPlugins.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No featured plugins available
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {featuredPlugins.map((plugin) => (
                      <PluginCard 
                        key={plugin.id} 
                        plugin={plugin} 
                        onSelect={handlePluginSelect}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="popular" className="space-y-4">
                <h2 className="text-xl font-semibold flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Popular Plugins
                </h2>
                
                {loading.popular ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : popularPlugins.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No popular plugins available
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {popularPlugins.map((plugin) => (
                      <PluginCard 
                        key={plugin.id} 
                        plugin={plugin} 
                        onSelect={handlePluginSelect}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="recent" className="space-y-4">
                <h2 className="text-xl font-semibold flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Recently Updated
                </h2>
                
                {loading.recent ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : recentPlugins.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No recent plugins available
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {recentPlugins.map((plugin) => (
                      <PluginCard 
                        key={plugin.id} 
                        plugin={plugin} 
                        onSelect={handlePluginSelect}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
