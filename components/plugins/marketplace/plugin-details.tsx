"use client"

import React from 'react'
import { PluginPackage, InstallationStatus } from '@/lib/plugins/marketplace'
import { useMarketplace } from './marketplace-provider'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Download, 
  Check, 
  Loader2, 
  ExternalLink, 
  Star, 
  Calendar, 
  Tag,
  User,
  Package,
  Github,
  Home,
  ArrowLeft,
  Trash2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Separator } from '@/components/ui/separator'

interface PluginDetailsProps {
  plugin: PluginPackage
  onBack?: () => void
  className?: string
}

/**
 * PluginDetails component
 * 
 * Renders detailed information about a plugin
 */
export function PluginDetails({ plugin, onBack, className }: PluginDetailsProps) {
  const { 
    installPlugin, 
    uninstallPlugin,
    isPluginInstalled, 
    installationStatus, 
    loading 
  } = useMarketplace()

  const installed = isPluginInstalled(plugin.id)
  const installing = loading.installation && installationStatus !== InstallationStatus.COMPLETED

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString()
  }

  // Handle install
  const handleInstall = () => {
    installPlugin(plugin)
  }

  // Handle uninstall
  const handleUninstall = () => {
    uninstallPlugin(plugin.id)
  }

  // Handle back
  const handleBack = () => {
    if (onBack) {
      onBack()
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="mr-2"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Plugin Details</h1>
      </div>
      
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center text-2xl">
                {plugin.icon && <span className="mr-2 text-2xl">{plugin.icon}</span>}
                {plugin.name}
              </CardTitle>
              <CardDescription className="text-base">v{plugin.version}</CardDescription>
            </div>
            
            {plugin.rating && (
              <div className="flex items-center text-base text-muted-foreground">
                <Star className="h-5 w-5 mr-1 fill-yellow-400 text-yellow-400" />
                <span>{plugin.rating.toFixed(1)}</span>
                {plugin.ratingCount && (
                  <span className="text-xs ml-1">({plugin.ratingCount})</span>
                )}
              </div>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <p>{plugin.description}</p>
          
          <div className="flex flex-wrap gap-1">
            {plugin.tags?.map((tag) => (
              <Badge key={tag} variant="secondary">
                {tag}
              </Badge>
            ))}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <User className="h-4 w-4 mr-2" />
                <span className="text-muted-foreground">Author:</span>
                <span className="ml-2">{plugin.author}</span>
              </div>
              
              <div className="flex items-center text-sm">
                <Package className="h-4 w-4 mr-2" />
                <span className="text-muted-foreground">License:</span>
                <span className="ml-2">{plugin.license || 'Unknown'}</span>
              </div>
              
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 mr-2" />
                <span className="text-muted-foreground">Updated:</span>
                <span className="ml-2">{formatDate(plugin.updatedAt)}</span>
              </div>
              
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 mr-2" />
                <span className="text-muted-foreground">Created:</span>
                <span className="ml-2">{formatDate(plugin.createdAt)}</span>
              </div>
            </div>
            
            <div className="space-y-2">
              {plugin.downloads && (
                <div className="flex items-center text-sm">
                  <Download className="h-4 w-4 mr-2" />
                  <span className="text-muted-foreground">Downloads:</span>
                  <span className="ml-2">{plugin.downloads.toLocaleString()}</span>
                </div>
              )}
              
              {plugin.category && (
                <div className="flex items-center text-sm">
                  <Tag className="h-4 w-4 mr-2" />
                  <span className="text-muted-foreground">Category:</span>
                  <span className="ml-2">{plugin.category}</span>
                </div>
              )}
              
              {plugin.homepage && (
                <div className="flex items-center text-sm">
                  <Home className="h-4 w-4 mr-2" />
                  <span className="text-muted-foreground">Homepage:</span>
                  <a 
                    href={plugin.homepage} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="ml-2 text-blue-500 hover:underline flex items-center"
                    onClick={(e) => e.stopPropagation()}
                  >
                    Visit
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
              )}
              
              {plugin.repository && (
                <div className="flex items-center text-sm">
                  <Github className="h-4 w-4 mr-2" />
                  <span className="text-muted-foreground">Repository:</span>
                  <a 
                    href={plugin.repository} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="ml-2 text-blue-500 hover:underline flex items-center"
                    onClick={(e) => e.stopPropagation()}
                  >
                    View Source
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
              )}
            </div>
          </div>
          
          {plugin.readme && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-medium mb-2">README</h3>
                <div className="prose prose-sm dark:prose-invert max-w-none">
                  {/* In a real implementation, this would render markdown */}
                  <p>{plugin.readme}</p>
                </div>
              </div>
            </>
          )}
        </CardContent>
        
        <CardFooter className="flex justify-between">
          {installed ? (
            <>
              <Button variant="outline" size="sm" disabled>
                <Check className="h-4 w-4 mr-2" />
                Installed
              </Button>
              
              <Button 
                variant="destructive" 
                size="sm"
                onClick={handleUninstall}
                disabled={loading.installation}
              >
                {loading.installation ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Uninstalling...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Uninstall
                  </>
                )}
              </Button>
            </>
          ) : (
            <Button 
              variant="default" 
              size="sm"
              onClick={handleInstall}
              disabled={installing}
              className="ml-auto"
            >
              {installing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Installing...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Install
                </>
              )}
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}
