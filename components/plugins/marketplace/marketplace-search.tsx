"use client"

import React, { useState, useEffect } from 'react'
import { useMarketplace } from './marketplace-provider'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  Search, 
  Filter, 
  SortAsc, 
  SortDesc, 
  Loader2 
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MarketplaceSearchProps {
  className?: string
  onSearch?: () => void
}

/**
 * MarketplaceSearch component
 * 
 * Renders a search form for the marketplace
 */
export function MarketplaceSearch({ className, onSearch }: MarketplaceSearchProps) {
  const { categories, searchPlugins, loading } = useMarketplace()
  
  const [query, setQuery] = useState('')
  const [category, setCategory] = useState<string>('')
  const [sort, setSort] = useState<'popular' | 'recent' | 'rating'>('popular')

  // Handle search
  const handleSearch = () => {
    searchPlugins({
      query,
      category: category || undefined,
      sort,
    })
    
    if (onSearch) {
      onSearch()
    }
  }

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search plugins..."
            className="pl-8"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyPress}
          />
        </div>
        
        <Button 
          onClick={handleSearch}
          disabled={loading.search}
        >
          {loading.search ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            'Search'
          )}
        </Button>
      </div>
      
      <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
        <div className="flex-1">
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger>
              <div className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="All Categories" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Categories</SelectItem>
              {categories.map((cat) => (
                <SelectItem key={cat.id} value={cat.id}>
                  {cat.icon && <span className="mr-2">{cat.icon}</span>}
                  {cat.name}
                  {cat.count !== undefined && (
                    <span className="ml-2 text-muted-foreground">({cat.count})</span>
                  )}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="w-full sm:w-48">
          <Select value={sort} onValueChange={(value: 'popular' | 'recent' | 'rating') => setSort(value)}>
            <SelectTrigger>
              <div className="flex items-center">
                <SortAsc className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Sort by" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="popular">Most Popular</SelectItem>
              <SelectItem value="recent">Recently Updated</SelectItem>
              <SelectItem value="rating">Highest Rated</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}
