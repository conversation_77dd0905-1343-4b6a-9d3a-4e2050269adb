"use client"

import React, { useEffect, useState } from 'react'
import { PluginTab as PluginTabType } from '@/lib/plugins'
import { PluginTab } from './plugin-tab'
import { getPluginRegistry } from '@/lib/plugins'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'

interface PluginTabRendererProps {
  location: 'main' | 'left' | 'right' | 'bottom'
  tabId: string
  className?: string
}

/**
 * PluginTabRenderer component
 * 
 * Renders a plugin tab by its ID and location
 */
export function PluginTabRenderer({
  location,
  tabId,
  className
}: PluginTabRendererProps) {
  const [tab, setTab] = useState<PluginTabType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Find the tab in the registry
    const registry = getPluginRegistry()
    const tabs = registry.getTabsByLocation(location)
    const foundTab = tabs.find(t => `${t.id}` === tabId)

    if (foundTab) {
      setTab(foundTab)
      setError(null)
    } else {
      setTab(null)
      setError(`Plugin tab not found: ${tabId}`)
    }

    setLoading(false)
  }, [location, tabId])

  if (loading) {
    return (
      <div className={cn("flex items-center justify-center h-full", className)}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (error || !tab) {
    return (
      <div className={cn("p-4 text-muted-foreground", className)}>
        {error || `Plugin tab not found: ${tabId}`}
      </div>
    )
  }

  return <PluginTab tab={tab} className={className} />
}
