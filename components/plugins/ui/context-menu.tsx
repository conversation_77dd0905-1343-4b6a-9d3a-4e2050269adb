"use client"

import React, { useEffect, useState } from 'react'
import {
  ContextMenu as UIContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import { cn } from '@/lib/utils'
import { ContextMenuExtensionPoint } from '@/lib/plugins/extension-points'
import { getExtensionPointRegistry, ExtensionPointRegistryEvent } from '@/lib/plugins'

interface ContextMenuProps {
  context: string
  contextData?: any
  children: React.ReactNode
  className?: string
}

/**
 * ContextMenu component
 * 
 * Renders a context menu with items from plugins
 */
export function ContextMenu({ context, contextData, children, className }: ContextMenuProps) {
  const [menuItems, setMenuItems] = useState<ContextMenuExtensionPoint[]>([])

  useEffect(() => {
    // Get extension point registry
    const registry = getExtensionPointRegistry()
    
    // Get context menu items for this context
    const items = registry.getExtensionPointsByContext<ContextMenuExtensionPoint>('contextMenu', context)
    setMenuItems(items)
    
    // Listen for changes
    const handleExtensionPointRegistered = (data: any) => {
      if (data.type === 'contextMenu' && (data.data as ContextMenuExtensionPoint).context === context) {
        setMenuItems(registry.getExtensionPointsByContext<ContextMenuExtensionPoint>('contextMenu', context))
      }
    }
    
    const handleExtensionPointUnregistered = () => {
      setMenuItems(registry.getExtensionPointsByContext<ContextMenuExtensionPoint>('contextMenu', context))
    }
    
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    
    return () => {
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    }
  }, [context])

  // Filter items by condition
  const filteredItems = menuItems.filter(item => {
    if (!item.condition) {
      return true
    }
    
    try {
      return item.condition(contextData)
    } catch (error) {
      console.error(`Error in context menu condition for ${item.id}:`, error)
      return false
    }
  })

  // Sort items by order
  const sortedItems = [...filteredItems].sort((a, b) => (a.order || 0) - (b.order || 0))

  // Group items by plugin
  const groupedItems: Record<string, ContextMenuExtensionPoint[]> = {}
  
  for (const item of sortedItems) {
    if (!groupedItems[item.pluginId]) {
      groupedItems[item.pluginId] = []
    }
    
    groupedItems[item.pluginId].push(item)
  }

  // If no items, just render children
  if (Object.keys(groupedItems).length === 0) {
    return <>{children}</>
  }

  return (
    <UIContextMenu>
      <ContextMenuTrigger className={className}>
        {children}
      </ContextMenuTrigger>
      
      <ContextMenuContent className="w-64">
        {Object.entries(groupedItems).map(([pluginId, items], index, array) => (
          <React.Fragment key={pluginId}>
            {items.map((item) => (
              <ContextMenuItem
                key={`${item.pluginId}:${item.id}`}
                onClick={() => item.onClick(contextData)}
                disabled={item.disabled}
              >
                {item.icon && <span className="mr-2">{item.icon}</span>}
                {item.title}
                {item.shortcut && (
                  <ContextMenuShortcut>{item.shortcut}</ContextMenuShortcut>
                )}
              </ContextMenuItem>
            ))}
            
            {index < array.length - 1 && <ContextMenuSeparator />}
          </React.Fragment>
        ))}
      </ContextMenuContent>
    </UIContextMenu>
  )
}
