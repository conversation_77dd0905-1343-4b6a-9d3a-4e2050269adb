"use client"

import React, { useEffect, useState } from 'react'
import { ClassErrorBoundary } from '@/components/class-error-boundary'
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { StatusBarExtensionPoint } from '@/lib/plugins/extension-points'
import { getExtensionPointRegistry, ExtensionPointRegistryEvent } from '@/lib/plugins'

interface StatusBarProps {
  className?: string
}

/**
 * StatusBar component
 * 
 * Renders status bar items from plugins
 */
export function StatusBar({ className }: StatusBarProps) {
  const [leftItems, setLeftItems] = useState<StatusBarExtensionPoint[]>([])
  const [rightItems, setRightItems] = useState<StatusBarExtensionPoint[]>([])

  useEffect(() => {
    // Get extension point registry
    const registry = getExtensionPointRegistry()
    
    // Get status bar items
    const updateItems = () => {
      setLeftItems(registry.getExtensionPointsByLocation<StatusBarExtensionPoint>('statusBar', 'left'))
      setRightItems(registry.getExtensionPointsByLocation<StatusBarExtensionPoint>('statusBar', 'right'))
    }
    
    updateItems()
    
    // Listen for changes
    const handleExtensionPointRegistered = (data: any) => {
      if (data.type === 'statusBar') {
        updateItems()
      }
    }
    
    const handleExtensionPointUnregistered = () => {
      updateItems()
    }
    
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    
    return () => {
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    }
  }, [])

  // Sort items by order
  const sortedLeftItems = [...leftItems].sort((a, b) => (a.order || 0) - (b.order || 0))
  const sortedRightItems = [...rightItems].sort((a, b) => (a.order || 0) - (b.order || 0))

  if (sortedLeftItems.length === 0 && sortedRightItems.length === 0) {
    return null
  }

  return (
    <div className={cn("flex items-center justify-between h-6 px-2 text-xs border-t bg-muted/50", className)}>
      <div className="flex items-center gap-2">
        {sortedLeftItems.map((item) => (
          <StatusBarItem key={`${item.pluginId}:${item.id}`} item={item} />
        ))}
      </div>
      
      <div className="flex items-center gap-2">
        {sortedRightItems.map((item) => (
          <StatusBarItem key={`${item.pluginId}:${item.id}`} item={item} />
        ))}
      </div>
    </div>
  )
}

interface StatusBarItemProps {
  item: StatusBarExtensionPoint
}

/**
 * StatusBarItem component
 * 
 * Renders a single status bar item
 */
function StatusBarItem({ item }: StatusBarItemProps) {
  const content = (
    <div 
      className={cn(
        "flex items-center gap-1 px-1 py-0.5 rounded hover:bg-muted/80 cursor-pointer",
        !item.onClick && "cursor-default"
      )}
      onClick={item.onClick}
    >
      {item.icon && <span className="h-3.5 w-3.5">{item.icon}</span>}
      {item.text && <span>{item.text}</span>}
      {item.component && (
        <ClassErrorBoundary fallback={<span className="text-destructive">Error</span>}>
          <item.component {...(item.props || {})} />
        </ClassErrorBoundary>
      )}
    </div>
  )

  if (item.tooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {content}
          </TooltipTrigger>
          <TooltipContent>
            <p>{item.tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return content
}
