"use client"

import React, { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { ToolbarExtensionPoint } from '@/lib/plugins/extension-points'
import { getExtensionPointRegistry, ExtensionPointRegistryEvent } from '@/lib/plugins'

interface ToolbarProps {
  location: string
  className?: string
}

/**
 * Toolbar component
 * 
 * Renders toolbar items from plugins
 */
export function Toolbar({ location, className }: ToolbarProps) {
  const [toolbarItems, setToolbarItems] = useState<ToolbarExtensionPoint[]>([])

  useEffect(() => {
    // Get extension point registry
    const registry = getExtensionPointRegistry()
    
    // Get toolbar items for this location
    const items = registry.getExtensionPointsByLocation<ToolbarExtensionPoint>('toolbar', location)
    setToolbarItems(items)
    
    // Listen for changes
    const handleExtensionPointRegistered = (data: any) => {
      if (data.type === 'toolbar' && (data.data as ToolbarExtensionPoint).location === location) {
        setToolbarItems(registry.getExtensionPointsByLocation<ToolbarExtensionPoint>('toolbar', location))
      }
    }
    
    const handleExtensionPointUnregistered = () => {
      setToolbarItems(registry.getExtensionPointsByLocation<ToolbarExtensionPoint>('toolbar', location))
    }
    
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    
    return () => {
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    }
  }, [location])

  // Sort toolbar items by order
  const sortedItems = [...toolbarItems].sort((a, b) => (a.order || 0) - (b.order || 0))

  if (sortedItems.length === 0) {
    return null
  }

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {sortedItems.map((item) => (
        <TooltipProvider key={`${item.pluginId}:${item.id}`}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={item.variant || "outline"}
                size={item.size || "icon"}
                onClick={item.onClick}
                disabled={item.disabled}
                className="h-8 w-8"
              >
                {item.icon}
              </Button>
            </TooltipTrigger>
            {item.tooltip && (
              <TooltipContent>
                <p>{item.tooltip}</p>
                {item.shortcut && (
                  <p className="text-xs text-muted-foreground">{item.shortcut}</p>
                )}
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      ))}
    </div>
  )
}
