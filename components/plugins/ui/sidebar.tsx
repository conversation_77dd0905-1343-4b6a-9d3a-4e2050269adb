"use client"

import React, { useEffect, useState } from 'react'
import { ClassErrorBoundary } from '@/components/class-error-boundary'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { SidebarExtensionPoint } from '@/lib/plugins/extension-points'
import { getExtensionPointRegistry, ExtensionPointRegistryEvent } from '@/lib/plugins'

interface SidebarProps {
  location: 'left' | 'right'
  className?: string
}

/**
 * Sidebar component
 * 
 * Renders sidebar items from plugins
 */
export function Sidebar({ location, className }: SidebarProps) {
  const [sidebarItems, setSidebarItems] = useState<SidebarExtensionPoint[]>([])

  useEffect(() => {
    // Get extension point registry
    const registry = getExtensionPointRegistry()
    
    // Get sidebar items for this location
    const items = registry.getExtensionPointsByLocation<SidebarExtensionPoint>('sidebar', location)
    setSidebarItems(items)
    
    // Listen for changes
    const handleExtensionPointRegistered = (data: any) => {
      if (data.type === 'sidebar' && (data.data as SidebarExtensionPoint).location === location) {
        setSidebarItems(registry.getExtensionPointsByLocation<SidebarExtensionPoint>('sidebar', location))
      }
    }
    
    const handleExtensionPointUnregistered = () => {
      setSidebarItems(registry.getExtensionPointsByLocation<SidebarExtensionPoint>('sidebar', location))
    }
    
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    
    return () => {
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    }
  }, [location])

  // Sort sidebar items by order
  const sortedItems = [...sidebarItems].sort((a, b) => (a.order || 0) - (b.order || 0))

  if (sortedItems.length === 0) {
    return null
  }

  return (
    <div className={cn("flex flex-col gap-4", className)}>
      {sortedItems.map((item) => (
        <SidebarItem key={`${item.pluginId}:${item.id}`} item={item} />
      ))}
    </div>
  )
}

interface SidebarItemProps {
  item: SidebarExtensionPoint
}

/**
 * SidebarItem component
 * 
 * Renders a single sidebar item
 */
function SidebarItem({ item }: SidebarItemProps) {
  const Component = item.component

  return (
    <div className="flex flex-col">
      <div className="flex items-center gap-2 px-2 py-1 text-sm font-medium">
        {item.icon}
        <span>{item.title}</span>
      </div>
      
      <ClassErrorBoundary
        fallback={
          <Alert variant="destructive" className="m-2">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              An error occurred while rendering the sidebar item: {item.id}
            </AlertDescription>
          </Alert>
        }
      >
        <div className="mt-1">
          <Component {...(item.props || {})} />
        </div>
      </ClassErrorBoundary>
    </div>
  )
}
