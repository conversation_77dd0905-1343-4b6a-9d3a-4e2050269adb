"use client"

import React, { useEffect, useState } from 'react'
import { Command as CommandPrimitive } from 'cmdk'
import { CommandPaletteExtensionPoint } from '@/lib/plugins/extension-points'
import { getExtensionPointRegistry, ExtensionPointRegistryEvent } from '@/lib/plugins'
import { cn } from '@/lib/utils'

interface CommandPaletteProps {
  onSelect?: (command: CommandPaletteExtensionPoint) => void
  className?: string
}

/**
 * CommandPalette component
 * 
 * Renders command palette items from plugins
 */
export function CommandPalette({ onSelect, className }: CommandPaletteProps) {
  const [commands, setCommands] = useState<CommandPaletteExtensionPoint[]>([])
  const [categories, setCategories] = useState<string[]>([])

  useEffect(() => {
    // Get extension point registry
    const registry = getExtensionPointRegistry()
    
    // Get command palette items
    const updateCommands = () => {
      const items = registry.getExtensionPoints<CommandPaletteExtensionPoint>('commandPalette')
      setCommands(items)
      
      // Extract unique categories
      const uniqueCategories = Array.from(new Set(items.map(item => item.category)))
      setCategories(uniqueCategories)
    }
    
    updateCommands()
    
    // Listen for changes
    const handleExtensionPointRegistered = (data: any) => {
      if (data.type === 'commandPalette') {
        updateCommands()
      }
    }
    
    const handleExtensionPointUnregistered = () => {
      updateCommands()
    }
    
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    
    return () => {
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered)
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered)
    }
  }, [])

  // Sort commands by category and title
  const sortedCommands = [...commands].sort((a, b) => {
    if (a.category !== b.category) {
      return a.category.localeCompare(b.category)
    }
    return a.title.localeCompare(b.title)
  })

  // Group commands by category
  const commandsByCategory: Record<string, CommandPaletteExtensionPoint[]> = {}
  
  for (const command of sortedCommands) {
    if (!commandsByCategory[command.category]) {
      commandsByCategory[command.category] = []
    }
    
    commandsByCategory[command.category].push(command)
  }

  const handleSelect = (command: CommandPaletteExtensionPoint) => {
    if (onSelect) {
      onSelect(command)
    } else {
      // Execute the command
      try {
        command.execute()
      } catch (error) {
        console.error(`Error executing command ${command.id}:`, error)
      }
    }
  }

  return (
    <div className={cn("w-full", className)}>
      <CommandPrimitive.List className="max-h-[300px] overflow-y-auto overflow-x-hidden">
        {categories.length === 0 && commands.length === 0 ? (
          <CommandPrimitive.Empty className="py-6 text-center text-sm">
            No plugin commands available.
          </CommandPrimitive.Empty>
        ) : (
          categories.map((category) => (
            <CommandPrimitive.Group key={category} heading={category}>
              {commandsByCategory[category]?.map((command) => (
                <CommandPrimitive.Item
                  key={`${command.pluginId}:${command.id}`}
                  onSelect={() => handleSelect(command)}
                  className="flex cursor-pointer items-center"
                >
                  {command.icon && <span className="mr-2">{command.icon}</span>}
                  <span>{command.title}</span>
                  {command.shortcut && (
                    <span className="ml-auto text-xs text-muted-foreground">
                      {command.shortcut}
                    </span>
                  )}
                </CommandPrimitive.Item>
              ))}
            </CommandPrimitive.Group>
          ))
        )}
      </CommandPrimitive.List>
    </div>
  )
}
