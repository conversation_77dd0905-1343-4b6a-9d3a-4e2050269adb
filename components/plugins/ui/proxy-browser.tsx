"use client"

import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  RefreshCw, 
  ArrowLeft, 
  ArrowRight, 
  Home, 
  X, 
  ExternalLink,
  Shield,
  AlertTriangle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'

interface ProxyBrowserProps {
  initialUrl?: string
  height?: string | number
  width?: string | number
  className?: string
  sandbox?: string
  allowFullscreen?: boolean
  allowScripts?: boolean
  allowForms?: boolean
  allowPopups?: boolean
  allowSameOrigin?: boolean
  onNavigate?: (url: string) => void
  onError?: (error: Error) => void
}

/**
 * ProxyBrowser component
 * 
 * A browser-like component for displaying web content in a sandboxed iframe
 */
export function ProxyBrowser({
  initialUrl = 'about:blank',
  height = '600px',
  width = '100%',
  className,
  sandbox = 'allow-same-origin allow-scripts allow-forms',
  allowFullscreen = true,
  allowScripts = true,
  allowForms = true,
  allowPopups = false,
  allowSameOrigin = true,
  onNavigate,
  onError
}: ProxyBrowserProps) {
  const [url, setUrl] = useState(initialUrl)
  const [inputUrl, setInputUrl] = useState(initialUrl)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [history, setHistory] = useState<string[]>([initialUrl])
  const [historyIndex, setHistoryIndex] = useState(0)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // Build sandbox attribute
  const buildSandboxAttr = () => {
    const attrs = []
    if (allowSameOrigin) attrs.push('allow-same-origin')
    if (allowScripts) attrs.push('allow-scripts')
    if (allowForms) attrs.push('allow-forms')
    if (allowPopups) attrs.push('allow-popups')
    return attrs.join(' ')
  }

  // Handle URL navigation
  const navigateTo = (newUrl: string) => {
    try {
      // Basic URL validation
      if (!newUrl.startsWith('http://') && !newUrl.startsWith('https://') && newUrl !== 'about:blank') {
        newUrl = `https://${newUrl}`
      }

      // Update state
      setUrl(newUrl)
      setInputUrl(newUrl)
      setLoading(true)
      setError(null)

      // Update history
      const newHistory = [...history.slice(0, historyIndex + 1), newUrl]
      setHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)

      // Notify parent
      if (onNavigate) {
        onNavigate(newUrl)
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Invalid URL'
      setError(errorMsg)
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMsg))
      }
    }
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    navigateTo(inputUrl)
  }

  // Handle iframe load event
  const handleIframeLoad = () => {
    setLoading(false)
  }

  // Handle iframe error event
  const handleIframeError = () => {
    setLoading(false)
    setError('Failed to load page')
  }

  // Handle back button
  const handleBack = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setUrl(history[historyIndex - 1])
      setInputUrl(history[historyIndex - 1])
    }
  }

  // Handle forward button
  const handleForward = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setUrl(history[historyIndex + 1])
      setInputUrl(history[historyIndex + 1])
    }
  }

  // Handle refresh button
  const handleRefresh = () => {
    setLoading(true)
    if (iframeRef.current) {
      iframeRef.current.src = url
    }
  }

  // Handle home button
  const handleHome = () => {
    navigateTo(initialUrl)
  }

  // Handle open in new tab button
  const handleOpenInNewTab = () => {
    window.open(url, '_blank')
  }

  return (
    <div className={cn("flex flex-col border border-border rounded-md overflow-hidden", className)}>
      {/* Browser toolbar */}
      <div className="flex items-center gap-2 p-2 bg-muted border-b border-border">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleBack}
          disabled={historyIndex <= 0}
          title="Back"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={handleForward}
          disabled={historyIndex >= history.length - 1}
          title="Forward"
        >
          <ArrowRight className="h-4 w-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={handleRefresh}
          title="Refresh"
        >
          <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
        </Button>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={handleHome}
          title="Home"
        >
          <Home className="h-4 w-4" />
        </Button>
        
        <form onSubmit={handleSubmit} className="flex-1 flex items-center">
          <div className="relative flex-1">
            <Input
              type="text"
              value={inputUrl}
              onChange={(e) => setInputUrl(e.target.value)}
              className="pr-8"
              placeholder="Enter URL"
            />
            {inputUrl && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full"
                onClick={() => setInputUrl('')}
                title="Clear"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </form>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={handleOpenInNewTab}
          title="Open in new tab"
        >
          <ExternalLink className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Error message */}
      {error && (
        <Alert variant="destructive" className="m-2">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {/* Security warning for non-HTTPS */}
      {url !== 'about:blank' && !url.startsWith('https://') && (
        <Alert variant="warning" className="m-2">
          <Shield className="h-4 w-4" />
          <AlertTitle>Insecure Connection</AlertTitle>
          <AlertDescription>
            This page is not using a secure connection (HTTPS).
          </AlertDescription>
        </Alert>
      )}
      
      {/* Content iframe */}
      <div 
        className="flex-1 relative bg-background"
        style={{ height, width }}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
        
        <iframe
          ref={iframeRef}
          src={url}
          className="w-full h-full border-0"
          sandbox={buildSandboxAttr()}
          allowFullScreen={allowFullscreen}
          onLoad={handleIframeLoad}
          onError={handleIframeError}
        />
      </div>
    </div>
  )
}
