"use client"

import React, { useRef, useEffect } from 'react'
import { Editor, type Monaco } from '@monaco-editor/react'
import { editor as monacoEditor } from 'monaco-editor'
import { useEditorDecorations } from '@/lib/plugins/editor-decorations'
import { cn } from '@/lib/utils'

interface DecoratedEditorProps {
  value: string
  language?: string
  onChange?: (value: string | undefined) => void
  options?: monacoEditor.IStandaloneEditorConstructionOptions
  height?: string | number
  width?: string | number
  className?: string
  path?: string
  onMount?: (editor: monacoEditor.IStandaloneCodeEditor, monaco: Monaco) => void
}

/**
 * DecoratedEditor component
 *
 * A Monaco editor with support for plugin decorations
 */
export function DecoratedEditor({
  value,
  language,
  onChange,
  options,
  height = '100%',
  width = '100%',
  className,
  path,
  onMount,
}: DecoratedEditorProps) {
  const editorRef = useRef<monacoEditor.IStandaloneCodeEditor | null>(null)
  const monacoRef = useRef<Monaco | null>(null)
  const { setEditor } = useEditorDecorations()

  // Handle editor mount
  const handleEditorDidMount = (editor: monacoEditor.IStandaloneCodeEditor, monaco: Monaco) => {
    editorRef.current = editor
    monacoRef.current = monaco

    // Set the editor in the decoration service
    setEditor(editor, monaco.editor)

    // Set the model path if provided
    if (path && editor.getModel()) {
      const uri = monaco.Uri.parse(`file://${path}`)
      const model = monaco.editor.getModel(uri) || monaco.editor.createModel(value, language, uri)
      editor.setModel(model)
    }

    // Call onMount callback if provided
    if (onMount) {
      onMount(editor, monaco)
    }
  }

  // Clean up on unmount
  useEffect(() => {
    return () => {
      // Dispose the editor
      if (editorRef.current) {
        editorRef.current.dispose()
      }
    }
  }, [])

  return (
    <div className={cn("relative", className)}>
      <Editor
        height={height}
        width={width}
        language={language}
        value={value}
        onChange={onChange}
        options={{
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          fontSize: 14,
          tabSize: 2,
          wordWrap: "on",
          automaticLayout: true,
          ...options,
        }}
        onMount={handleEditorDidMount}
      />
    </div>
  )
}
