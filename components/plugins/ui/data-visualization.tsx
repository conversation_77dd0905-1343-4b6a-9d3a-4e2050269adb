"use client"

import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Line, 
  PieChart, 
  Pie, 
  AreaChart, 
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell
} from 'recharts'
import { cn } from '@/lib/utils'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// Chart types
type ChartType = 'bar' | 'line' | 'pie' | 'area'

// Color schemes
const COLOR_SCHEMES = {
  default: ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe', '#00C49F', '#FFBB28', '#FF8042'],
  blue: ['#0088FE', '#1E88E5', '#3F51B5', '#7986CB', '#9FA8DA', '#C5CAE9', '#E8EAF6', '#8C9EFF'],
  green: ['#00C49F', '#4CAF50', '#8BC34A', '#CDDC39', '#AFB42B', '#C0CA33', '#D4E157', '#DCE775'],
  warm: ['#FF8042', '#FF5722', '#F44336', '#E91E63', '#D81B60', '#C2185B', '#AD1457', '#880E4F'],
  rainbow: ['#FF0000', '#FF7F00', '#FFFF00', '#00FF00', '#0000FF', '#4B0082', '#9400D3', '#FF00FF']
}

type ColorScheme = keyof typeof COLOR_SCHEMES

interface DataVisualizationProps {
  data: any[]
  type?: ChartType
  xKey?: string
  yKeys?: string[]
  width?: string | number
  height?: string | number
  title?: string
  colorScheme?: ColorScheme
  allowTypeChange?: boolean
  className?: string
}

/**
 * DataVisualization component
 * 
 * A flexible data visualization component that supports multiple chart types
 */
export function DataVisualization({
  data,
  type: initialType = 'bar',
  xKey = 'name',
  yKeys,
  width = '100%',
  height = 400,
  title = 'Data Visualization',
  colorScheme = 'default',
  allowTypeChange = true,
  className
}: DataVisualizationProps) {
  const [chartType, setChartType] = useState<ChartType>(initialType)
  const [processedData, setProcessedData] = useState<any[]>([])
  const [dataKeys, setDataKeys] = useState<string[]>([])
  const colors = COLOR_SCHEMES[colorScheme] || COLOR_SCHEMES.default

  // Process data and extract keys
  useEffect(() => {
    if (!data || data.length === 0) {
      setProcessedData([])
      setDataKeys([])
      return
    }

    // Use provided yKeys or extract all keys except xKey
    const keys = yKeys || Object.keys(data[0]).filter(key => key !== xKey)
    setDataKeys(keys)
    
    // For pie charts, transform data if needed
    if (chartType === 'pie' && keys.length > 0) {
      // If multiple yKeys, use the first one for pie chart
      const pieKey = keys[0]
      
      // Create pie data
      const pieData = data.map(item => ({
        name: item[xKey],
        value: Number(item[pieKey]) || 0
      }))
      
      setProcessedData(pieData)
    } else {
      setProcessedData(data)
    }
  }, [data, xKey, yKeys, chartType])

  // Render the appropriate chart based on type
  const renderChart = () => {
    if (!processedData || processedData.length === 0 || dataKeys.length === 0) {
      return (
        <div className="flex items-center justify-center h-full text-muted-foreground">
          No data available
        </div>
      )
    }

    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={processedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xKey} />
              <YAxis />
              <Tooltip />
              <Legend />
              {dataKeys.map((key, index) => (
                <Bar 
                  key={key} 
                  dataKey={key} 
                  fill={colors[index % colors.length]} 
                  name={key}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        )
      
      case 'line':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={processedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xKey} />
              <YAxis />
              <Tooltip />
              <Legend />
              {dataKeys.map((key, index) => (
                <Line 
                  key={key} 
                  type="monotone" 
                  dataKey={key} 
                  stroke={colors[index % colors.length]} 
                  name={key}
                  activeDot={{ r: 8 }}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        )
      
      case 'area':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={processedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xKey} />
              <YAxis />
              <Tooltip />
              <Legend />
              {dataKeys.map((key, index) => (
                <Area 
                  key={key} 
                  type="monotone" 
                  dataKey={key} 
                  fill={colors[index % colors.length]} 
                  stroke={colors[index % colors.length]} 
                  name={key}
                  fillOpacity={0.3}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        )
      
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={processedData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={80}
                label
              >
                {processedData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        )
      
      default:
        return null
    }
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium">
          {title}
        </CardTitle>
        
        {allowTypeChange && (
          <Select
            value={chartType}
            onValueChange={(value) => setChartType(value as ChartType)}
          >
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Chart Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="bar">Bar Chart</SelectItem>
              <SelectItem value="line">Line Chart</SelectItem>
              <SelectItem value="area">Area Chart</SelectItem>
              <SelectItem value="pie">Pie Chart</SelectItem>
            </SelectContent>
          </Select>
        )}
      </CardHeader>
      
      <CardContent>
        <div style={{ width, height }}>
          {renderChart()}
        </div>
      </CardContent>
    </Card>
  )
}
