"use client"

import React, { useEffect, useState } from 'react'
import {
  Plugin,
  PluginState,
  getPluginRegistry,
  PluginRegistryEvent
} from '@/lib/plugins'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Settings,
  Info,
  Package,
  Shield,
  AlertCircle,
  CheckCircle2,
  XCircle,
  ArrowLeft
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { PluginSettings } from './plugin-settings'
import { MarketplaceProvider, Marketplace } from './marketplace'

interface PluginManagerProps {
  className?: string
}

/**
 * PluginManager component
 *
 * Displays a list of installed plugins and allows managing them
 */
export function PluginManager({ className }: PluginManagerProps) {
  const [plugins, setPlugins] = useState<Plugin[]>([])
  const [activeTab, setActiveTab] = useState('installed')
  const [selectedPlugin, setSelectedPlugin] = useState<Plugin | null>(null)
  const [showSettings, setShowSettings] = useState(false)

  useEffect(() => {
    const registry = getPluginRegistry()

    // Load initial plugins
    setPlugins(registry.getAllPlugins())

    // Listen for plugin events
    const handlePluginUpdate = () => {
      setPlugins(registry.getAllPlugins())
    }

    registry.on(PluginRegistryEvent.PLUGIN_REGISTERED, handlePluginUpdate)
    registry.on(PluginRegistryEvent.PLUGIN_LOADED, handlePluginUpdate)
    registry.on(PluginRegistryEvent.PLUGIN_ENABLED, handlePluginUpdate)
    registry.on(PluginRegistryEvent.PLUGIN_DISABLED, handlePluginUpdate)
    registry.on(PluginRegistryEvent.PLUGIN_UNLOADED, handlePluginUpdate)
    registry.on(PluginRegistryEvent.PLUGIN_ERROR, handlePluginUpdate)

    return () => {
      registry.off(PluginRegistryEvent.PLUGIN_REGISTERED, handlePluginUpdate)
      registry.off(PluginRegistryEvent.PLUGIN_LOADED, handlePluginUpdate)
      registry.off(PluginRegistryEvent.PLUGIN_ENABLED, handlePluginUpdate)
      registry.off(PluginRegistryEvent.PLUGIN_DISABLED, handlePluginUpdate)
      registry.off(PluginRegistryEvent.PLUGIN_UNLOADED, handlePluginUpdate)
      registry.off(PluginRegistryEvent.PLUGIN_ERROR, handlePluginUpdate)
    }
  }, [])

  const handleTogglePlugin = async (plugin: Plugin) => {
    const registry = getPluginRegistry()

    try {
      if (plugin.enabled) {
        await registry.disablePlugin(plugin.manifest.metadata.id)
      } else {
        await registry.enablePlugin(plugin.manifest.metadata.id)
      }
    } catch (error) {
      console.error('Failed to toggle plugin:', error)
    }
  }

  const handleOpenSettings = (plugin: Plugin) => {
    setSelectedPlugin(plugin)
    setShowSettings(true)
  }

  const handleCloseSettings = () => {
    setShowSettings(false)
    setSelectedPlugin(null)
  }

  return (
    <div className={cn("p-4", className)}>
      {showSettings && selectedPlugin ? (
        <div>
          <div className="flex items-center mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCloseSettings}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h1 className="text-2xl font-bold">Plugin Settings</h1>
          </div>

          <PluginSettings plugin={selectedPlugin} onCancel={handleCloseSettings} />
        </div>
      ) : (
        <>
          <h1 className="text-2xl font-bold mb-4">Plugin Manager</h1>

          <Tabs defaultValue="installed" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="installed">
                <Package className="h-4 w-4 mr-2" />
                Installed
              </TabsTrigger>
              <TabsTrigger value="marketplace">
                <Package className="h-4 w-4 mr-2" />
                Marketplace
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="installed">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {plugins.length === 0 ? (
                  <div className="col-span-full text-center p-8 text-muted-foreground">
                    No plugins installed
                  </div>
                ) : (
                  plugins.map((plugin) => (
                    <PluginCard
                      key={plugin.manifest.metadata.id}
                      plugin={plugin}
                      onToggle={() => handleTogglePlugin(plugin)}
                      onOpenSettings={() => handleOpenSettings(plugin)}
                    />
                  ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="marketplace" className="p-0">
              <MarketplaceProvider>
                <Marketplace />
              </MarketplaceProvider>
            </TabsContent>

            <TabsContent value="settings">
              <div className="text-center p-8 text-muted-foreground">
                Global plugin settings coming soon
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}

interface PluginCardProps {
  plugin: Plugin
  onToggle: () => void
  onOpenSettings: () => void
}

function PluginCard({ plugin, onToggle, onOpenSettings }: PluginCardProps) {
  const { metadata } = plugin.manifest

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="flex items-center">
              {metadata.icon && <span className="mr-2">{metadata.icon}</span>}
              {metadata.name}
            </CardTitle>
            <CardDescription>{metadata.description}</CardDescription>
          </div>
          <Switch
            checked={plugin.enabled}
            onCheckedChange={onToggle}
            aria-label={`Toggle ${metadata.name}`}
          />
        </div>
      </CardHeader>

      <CardContent>
        <div className="flex flex-wrap gap-2 mb-2">
          <Badge variant="outline">v{metadata.version}</Badge>
          {metadata.tags?.map((tag) => (
            <Badge key={tag} variant="secondary">{tag}</Badge>
          ))}
        </div>

        <div className="text-sm text-muted-foreground">
          By {metadata.author}
        </div>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline" size="sm">
          <Info className="h-4 w-4 mr-2" />
          Details
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={onOpenSettings}
        >
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>
      </CardFooter>
    </Card>
  )
}
