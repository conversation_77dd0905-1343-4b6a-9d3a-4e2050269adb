"use client"

import React from 'react'
import { PluginTab as PluginTabType } from '@/lib/plugins'
import { ClassErrorBoundary } from '@/components/class-error-boundary'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'

interface PluginTabProps {
  tab: PluginTabType
  className?: string
}

/**
 * PluginTab component
 *
 * Renders a plugin tab with error handling
 */
export function PluginTab({ tab, className }: PluginTabProps) {
  const Component = tab.component

  return (
    <ClassErrorBoundary
      fallback={
        <Alert variant="destructive" className="m-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            An error occurred while rendering the plugin tab: {tab.id}
          </AlertDescription>
        </Alert>
      }
    >
      <div className={className}>
        <Component {...(tab.props || {})} />
      </div>
    </ClassErrorBoundary>
  )
}
