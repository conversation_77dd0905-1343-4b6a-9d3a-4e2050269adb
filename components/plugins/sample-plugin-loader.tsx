"use client"

import React, { useEffect } from 'react'
import { getPluginLoader } from '@/lib/plugins'
import { manifest as helloWorldManifest, lifecycle as helloWorldLifecycle } from '@/lib/plugins/samples/hello-world-plugin'
import { manifest as enhancedManifest, lifecycle as enhancedLifecycle } from '@/lib/plugins/samples/enhanced-plugin'
import { manifest as editorManifest, lifecycle as editorLifecycle } from '@/lib/plugins/samples/editor-plugin'

/**
 * SamplePluginLoader component
 *
 * Loads sample plugins for demonstration purposes
 */
export function SamplePluginLoader() {
  useEffect(() => {
    const loadSamplePlugins = async () => {
      try {
        const loader = getPluginLoader()

        // Load Hello World plugin
        await loader.loadPluginFromCode(
          helloWorldManifest,
          `return ${JSON.stringify(helloWorldLifecycle)}`
        )

        // Load Enhanced plugin
        await loader.loadPluginFromCode(
          enhancedManifest,
          `return ${JSON.stringify(enhancedLifecycle)}`
        )

        // Load Editor plugin
        await loader.loadPluginFromCode(
          editorManifest,
          `return ${JSON.stringify(editorLifecycle)}`
        )

        console.log('Sample plugins loaded')
      } catch (error) {
        console.error('Failed to load sample plugins:', error)
      }
    }

    loadSamplePlugins()
  }, [])

  return null
}
