"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { SamplePluginLoader } from './sample-plugin-loader'
import {
  Plugin,
  PluginTab,
  initializePluginSystem,
  getPluginRegistry,
  PluginRegistryEvent
} from '@/lib/plugins'

interface PluginContextType {
  plugins: Plugin[]
  tabs: {
    main: PluginTab[]
    left: PluginTab[]
    right: PluginTab[]
    bottom: PluginTab[]
  }
  initialized: boolean
  loading: boolean
  error: string | null
}

const PluginContext = createContext<PluginContextType>({
  plugins: [],
  tabs: {
    main: [],
    left: [],
    right: [],
    bottom: []
  },
  initialized: false,
  loading: true,
  error: null
})

/**
 * Use plugin context hook
 */
export const usePlugins = () => useContext(PluginContext)

interface PluginProviderProps {
  children: React.ReactNode
}

/**
 * PluginProvider component
 *
 * Provides plugin context to the application
 */
export function PluginProvider({ children }: PluginProviderProps) {
  const [plugins, setPlugins] = useState<Plugin[]>([])
  const [tabs, setTabs] = useState<PluginContextType['tabs']>({
    main: [],
    left: [],
    right: [],
    bottom: []
  })
  const [initialized, setInitialized] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const initPlugins = async () => {
      try {
        // Initialize plugin system
        await initializePluginSystem()

        // Get registry
        const registry = getPluginRegistry()

        // Update state
        updatePluginsAndTabs(registry)

        // Listen for plugin events
        const handlePluginUpdate = () => {
          updatePluginsAndTabs(registry)
        }

        registry.on(PluginRegistryEvent.PLUGIN_REGISTERED, handlePluginUpdate)
        registry.on(PluginRegistryEvent.PLUGIN_LOADED, handlePluginUpdate)
        registry.on(PluginRegistryEvent.PLUGIN_ENABLED, handlePluginUpdate)
        registry.on(PluginRegistryEvent.PLUGIN_DISABLED, handlePluginUpdate)
        registry.on(PluginRegistryEvent.PLUGIN_UNLOADED, handlePluginUpdate)

        setInitialized(true)
        setLoading(false)

        return () => {
          registry.off(PluginRegistryEvent.PLUGIN_REGISTERED, handlePluginUpdate)
          registry.off(PluginRegistryEvent.PLUGIN_LOADED, handlePluginUpdate)
          registry.off(PluginRegistryEvent.PLUGIN_ENABLED, handlePluginUpdate)
          registry.off(PluginRegistryEvent.PLUGIN_DISABLED, handlePluginUpdate)
          registry.off(PluginRegistryEvent.PLUGIN_UNLOADED, handlePluginUpdate)
        }
      } catch (error) {
        console.error('Failed to initialize plugin system:', error)
        setError(error instanceof Error ? error.message : 'Failed to initialize plugin system')
        setLoading(false)
      }
    }

    initPlugins()
  }, [])

  const updatePluginsAndTabs = (registry: any) => {
    // Update plugins
    setPlugins(registry.getAllPlugins())

    // Update tabs
    setTabs({
      main: registry.getTabsByLocation('main'),
      left: registry.getTabsByLocation('left'),
      right: registry.getTabsByLocation('right'),
      bottom: registry.getTabsByLocation('bottom')
    })
  }

  const value = {
    plugins,
    tabs,
    initialized,
    loading,
    error
  }

  return (
    <PluginContext.Provider value={value}>
      <SamplePluginLoader />
      {children}
    </PluginContext.Provider>
  )
}
