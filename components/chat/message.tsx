"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Message } from "ai";
import { Avatar } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import ReactMarkdown from "react-markdown";
import { <PERSON>rk<PERSON>, User, Loader2, Check, X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface MessageProps {
  message: Message;
  className?: string;
  onToolResult?: (toolCallId: string, result: string) => void;
}

export function BotMessageUi({ message, className, onToolResult }: MessageProps) {
  return (
    <div className={cn("flex items-start gap-3", className)}>
      <Avatar className="h-8 w-8 bg-primary/10 text-primary-foreground flex items-center justify-center">
        <Sparkles className="h-4 w-4" />
      </Avatar>
      <Card className="flex-1 p-3 bg-muted/50">
        <div className="prose prose-sm dark:prose-invert max-w-none">
          {message.parts ? (
            <div className="flex flex-col gap-3">
              {message.parts.map((part, index) => {
                if (part.type === "text") {
                  return <ReactMarkdown key={index}>{part.text}</ReactMarkdown>;
                }
                
                if (part.type === "tool-invocation") {
                  const { toolInvocation } = part;
                  const callId = toolInvocation.toolCallId;
                  
                  return (
                    <div key={index} className="border border-border rounded-md p-3 bg-background">
                      <div className="text-xs font-medium text-muted-foreground mb-1">
                        Tool: {toolInvocation.toolName}
                      </div>
                      
                      {toolInvocation.state === "partial-call" && (
                        <div className="flex items-center gap-2 text-sm">
                          <Loader2 className="h-3 w-3 animate-spin" />
                          <span>Preparing tool call...</span>
                        </div>
                      )}
                      
                      {toolInvocation.state === "call" && (
                        <div>
                          <pre className="text-xs bg-muted p-2 rounded-sm overflow-x-auto">
                            {JSON.stringify(toolInvocation.args, null, 2)}
                          </pre>
                          
                          {toolInvocation.toolName === "askForConfirmation" && onToolResult && (
                            <div className="flex gap-2 mt-2">
                              <Button
                                size="sm"
                                variant="default"
                                onClick={() => onToolResult(callId, "Yes, confirmed.")}
                                className="h-7"
                              >
                                <Check className="h-3 w-3 mr-1" /> Confirm
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onToolResult(callId, "No, denied.")}
                                className="h-7"
                              >
                                <X className="h-3 w-3 mr-1" /> Deny
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                      
                      {toolInvocation.state === "result" && (
                        <div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
                            <span>Args:</span>
                            <pre className="text-xs bg-muted p-1 rounded-sm overflow-x-auto">
                              {JSON.stringify(toolInvocation.args, null, 2)}
                            </pre>
                          </div>
                          <div className="mt-1 border-t pt-1">
                            <div className="text-xs font-medium mb-1">Result:</div>
                            <pre className="text-xs bg-muted/50 p-2 rounded-sm overflow-x-auto whitespace-pre-wrap">
                              {typeof toolInvocation.result === 'object' 
                                ? JSON.stringify(toolInvocation.result, null, 2) 
                                : toolInvocation.result}
                            </pre>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                }
                
                if (part.type === "step-start" && index > 0) {
                  return <hr key={index} className="my-2 border-gray-200" />;
                }
                
                return null;
              })}
            </div>
          ) : (
            <ReactMarkdown>{message.content}</ReactMarkdown>
          )}
        </div>
      </Card>
    </div>
  );
}

export function UserMessageUi({ message, className }: MessageProps) {
  return (
    <div className={cn("flex items-start gap-3 flex-row-reverse", className)}>
      <Avatar className="h-8 w-8 bg-muted flex items-center justify-center">
        <User className="h-4 w-4" />
      </Avatar>
      <Card className="flex-1 p-3 bg-primary text-primary-foreground">
        <div className="prose prose-sm dark:prose-invert prose-p:text-primary-foreground max-w-none">
          <ReactMarkdown>{message.content}</ReactMarkdown>
        </div>
      </Card>
    </div>
  );
} 