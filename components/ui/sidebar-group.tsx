"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface SidebarGroupProps
  extends React.HTMLAttributes<HTMLDivElement> {}

const SidebarGroup = React.forwardRef<HTMLDivElement, SidebarGroupProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        className={cn("space-y-1", className)}
        ref={ref}
        {...props}
      />
    )
  }
)
SidebarGroup.displayName = "SidebarGroup"

export interface SidebarGroupLabelProps
  extends React.HTMLAttributes<HTMLDivElement> {}

const SidebarGroupLabel = React.forwardRef<HTMLDivElement, SidebarGroupLabelProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        className={cn("text-xs font-medium", className)}
        ref={ref}
        {...props}
      />
    )
  }
)
SidebarGroupLabel.displayName = "SidebarGroupLabel"

export interface SidebarGroupContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

const SidebarGroupContent = React.forwardRef<HTMLDivElement, SidebarGroupContentProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        className={cn("space-y-1", className)}
        ref={ref}
        {...props}
      />
    )
  }
)
SidebarGroupContent.displayName = "SidebarGroupContent"

export { SidebarGroup, SidebarGroupLabel, SidebarGroupContent }
