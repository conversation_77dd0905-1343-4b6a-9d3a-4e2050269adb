"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface SidebarSeparatorProps
  extends React.HTMLAttributes<HTMLDivElement> {}

const SidebarSeparator = React.forwardRef<HTMLDivElement, SidebarSeparatorProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        className={cn("mx-2 h-px bg-border", className)}
        ref={ref}
        {...props}
      />
    )
  }
)
SidebarSeparator.displayName = "SidebarSeparator"

export { SidebarSeparator }
