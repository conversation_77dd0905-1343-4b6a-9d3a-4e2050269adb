"use client";

import * as React from "react";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { TabsLayout, TabGroup } from "@/components/ui/tabs-layout";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable";
import { SidePanel } from "@/components/ui/side-panel";
import {
  VSCodeActivityBar,
  DEV_ACTIVITY_BAR_ITEMS,
  ActivityBarItem,
} from "@/components/ui/vscode-activity-bar";
import {
  Code2,
  Play,
  Pause,
  RefreshCw,
  Download,
  Monitor,
  Files,
  Terminal,
  Settings,
  Search,
  Sparkles,
  Boxes,
  Bug,
  Package,
  Server,
  Database,
  FileCode,
  PlusCircle,
  FolderTree,
  Cpu,
  CircleSlash,
  Loader2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useWorkspaceStore } from "@/lib/stores/workspace-store";
import { useAgentStore } from "@/lib/stores/agent-store";
import { useActivityBarStore } from "@/lib/stores/activity-bar-store";
import { useTabContent } from "@/lib/hooks/use-tab-content";
import VKLogo from "@/components/icons/VkLogoGradient";
import { UserMenu } from "./ui/user-menu";
import { ContainerStatusIndicator } from "@/components/ui/container-status-indicator";
import { TranscriptProvider } from "./dev-bot/TranscriptContext";
import { ToolsProvider } from "./dev-bot/ToolsContext";
import { ContainerProvider } from "@/lib/contexts/container-context";
import { toast } from "@/components/ui/use-toast";
import { MicroVmDashboard } from './microvm/microvm-dashboard';
import { MicroVmTerminal } from './microvm-terminal';
import { MicroVmDevBot } from "./dev-bot/MicroVmDevBot";
import { MicroVmIframePreview } from "./microvm-iframe-preview";
import { MicroVmFileExplorer } from "@/components/microvm-file-explorer";
import { EphemeralVmCreator } from "@/components/ephemeral-vm-creator";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { DirectVmFileExplorer } from "@/components/direct-vm-file-explorer";

// Define MicroVM-specific activity bar items
const MICROVM_ACTIVITY_BAR_ITEMS: ActivityBarItem[] = [
  {
    id: "explorer",
    icon: <Files className="h-5 w-5" />,
    label: "Explorer",
    panelId: "left",
    tabId: "explorer",
  },
  {
    id: "search",
    icon: <Search className="h-5 w-5" />,
    label: "Search",
    panelId: "left",
    tabId: "search",
  },
  {
    id: "ai-agent",
    icon: <Sparkles className="h-5 w-5" />,
    label: "AI Agent",
    tabId: "ai-agent",
  },
  {
    id: "app-preview",
    icon: <Monitor className="h-5 w-5" />,
    label: "App Preview",
    tabId: "app-preview",
  },
  {
    id: "terminal",
    icon: <Terminal className="h-5 w-5" />,
    label: "Terminal",
    panelId: "bottom",
    tabId: "terminal",
  },
  {
    id: "microvm-dashboard",
    icon: <Boxes className="h-5 w-5" />,
    label: "MicroVM Dashboard",
    tabId: "microvm-dashboard",
  },
  {
    id: "problems",
    icon: <Bug className="h-5 w-5" />,
    label: "Problems",
    panelId: "bottom",
    tabId: "problems",
  },
];

// Add a configuration setting at the top of the file
const VM_CONFIG = {
  useDirectVmByDefault: true,  // Set to true to make Direct VM the default
  autoCreateDirectVm: true,   // Set to true to automatically create a Direct VM on component mount
};

// Define the VM type interface
interface VMTypeConfig {
  name: string;
  description: string;
  rootfsPath: string;
  icon: React.ReactNode;
}

// Add VM type configuration with proper typing
const VM_TYPES: Record<string, VMTypeConfig> = {
  nodejs: {
    name: "Node.js",
    description: "Ubuntu 18.04 with Node.js 16 pre-installed",
    rootfsPath: "/home/<USER>/Documents/Webdev/app-gen/resources/lxc-images/nodejs.rootfs.ext4",
    icon: <div className="text-green-500">
      <svg viewBox="0 0 256 282" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMinYMin meet" fill="currentColor" className="h-5 w-5">
        <path d="M116.504 3.58c6.962-3.985 16.03-4.003 22.986 0 34.995 19.774 70.001 39.517 104.99 59.303 6.581 3.707 10.983 11.031 10.916 18.614v118.968c.049 7.897-4.788 15.396-11.731 19.019-34.88 19.665-69.742 39.354-104.616 59.019-7.106 4.063-16.356 3.75-23.24-.646-10.457-6.062-20.932-12.094-31.39-18.15-2.137-1.274-4.546-2.288-6.055-4.36 1.334-1.798 3.719-2.022 5.657-2.807 4.365-1.388 8.374-3.616 12.384-5.778 1.014-.694 2.252-.428 3.224.193 8.942 5.127 17.805 10.403 26.777 15.481 1.914 1.105 3.852-.362 5.488-1.274 34.228-19.345 68.498-38.617 102.72-57.968 1.268-.61 1.969-1.956 1.866-3.345.024-39.245.006-78.497.012-117.742.145-1.576-.767-3.025-2.192-3.67-34.759-19.575-69.5-39.18-104.253-58.76a3.621 3.621 0 0 0-4.094-.006C91.2 39.257 56.465 58.88 21.712 78.454c-1.42.646-2.373 2.071-2.204 3.653.006 39.245 0 78.497 0 117.748a3.329 3.329 0 0 0 1.89 3.303c9.274 5.259 18.56 10.481 27.84 15.722 5.228 2.814 11.647 4.486 17.407 2.33 5.083-1.823 8.646-7.01 8.549-12.407.048-39.016-.024-78.038.036-117.048-.127-1.732 1.516-3.163 3.2-3 4.456-.03 8.918-.06 13.374.012 1.86-.042 3.14 1.823 2.91 3.568-.018 39.263.048 78.527-.03 117.79.012 10.464-4.287 21.85-13.966 26.97-11.924 6.177-26.662 4.867-38.442-1.056-10.198-5.09-19.93-11.097-29.947-16.55C5.368 215.886.555 208.357.604 200.466V81.497c-.073-7.74 4.504-15.197 11.29-18.85C46.768 42.966 81.636 23.27 116.504 3.58z"/>
        <path d="M146.928 85.99c15.21-.979 31.493-.58 45.18 6.913 10.597 5.742 16.472 17.793 16.659 29.566-.296 1.588-1.956 2.464-3.472 2.355-4.413-.006-8.827.06-13.24-.03-1.872.072-2.96-1.654-3.195-3.309-1.268-5.633-4.34-11.212-9.642-13.929-8.139-4.075-17.576-3.87-26.451-3.785-6.479.344-13.446.905-18.935 4.715-4.214 2.886-5.494 8.712-3.99 13.404 1.418 3.369 5.307 4.456 8.489 5.458 18.33 4.794 37.754 4.317 55.734 10.626 7.444 2.572 14.726 7.572 17.274 15.366 3.333 10.446 1.872 22.932-5.56 31.318-6.027 6.901-14.805 10.657-23.56 12.697-11.647 2.597-23.734 2.663-35.562 1.51-11.122-1.268-22.696-4.19-31.282-11.768-7.342-6.375-10.928-16.308-10.572-25.895.085-1.619 1.697-2.748 3.248-2.615 4.444-.036 8.888-.048 13.332.006 1.775-.127 3.091 1.407 3.182 3.08.82 5.367 2.837 11 7.517 14.182 9.032 5.827 20.365 5.428 30.707 5.591 8.568-.38 18.186-.495 25.178-6.158 3.689-3.23 4.782-8.634 3.785-13.283-1.08-3.925-5.186-5.754-8.712-6.95-18.095-5.724-37.736-3.647-55.656-10.12-7.275-2.571-14.31-7.432-17.105-14.906-3.9-10.578-2.113-23.662 6.098-31.765 8.006-8.06 19.563-11.164 30.551-12.275z"/>
      </svg>
    </div>
  },
  python: {
    name: "Python",
    description: "Ubuntu 18.04 with Python 3.8 pre-installed",
    rootfsPath: "/home/<USER>/Documents/Webdev/app-gen/resources/lxc-images/python.rootfs.ext4",
    icon: <div className="text-blue-500">
      <svg viewBox="0 0 256 255" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMinYMin meet" fill="currentColor" className="h-5 w-5">
        <path d="M126.916.072c-64.832 0-60.784 28.115-60.784 28.115l.072 29.128h61.868v8.745H41.631S.145 61.355.145 126.77c0 65.417 36.21 63.097 36.21 63.097h21.61v-30.356s-1.165-36.21 35.632-36.21h61.362s34.475.557 34.475-33.319V33.97S194.67.072 126.916.072zM92.802 19.66a11.12 11.12 0 0 1 11.13 11.13 11.12 11.12 0 0 1-11.13 11.13 11.12 11.12 0 0 1-11.13-11.13 11.12 11.12 0 0 1 11.13-11.13z"/>
        <path d="M128.757 254.126c64.832 0 60.784-28.115 60.784-28.115l-.072-29.127H127.6v-8.745h86.441s41.486 4.705 41.486-60.712c0-65.416-36.21-63.096-36.21-63.096h-21.61v30.355s1.165 36.21-35.632 36.21h-61.362s-34.475-.557-34.475 33.32v56.013s-5.235 33.897 62.518 33.897zm34.114-19.586a11.12 11.12 0 0 1-11.13-11.13 11.12 11.12 0 0 1 11.13-11.131 11.12 11.12 0 0 1 11.13 11.13 11.12 11.12 0 0 1-11.13 11.13z"/>
      </svg>
    </div>
  },
  default: {
    name: "Ubuntu",
    description: "Basic Ubuntu 18.04 Linux",
    rootfsPath: "/home/<USER>/Documents/Webdev/app-gen/resources/lxc-images/bionic.rootfs.ext4",
    icon: <div className="text-orange-500">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" fill="currentColor" className="h-5 w-5">
        <path d="M255.637 127.683c0 70.514-57.165 127.679-127.683 127.679C57.434 255.362.27 198.197.27 127.683.27 57.165 57.434 0 127.954 0c70.519 0 127.683 57.165 127.683 127.683zm-11.299 0c0-64.195-52.193-116.38-116.384-116.38S11.57 63.488 11.57 127.683s52.193 116.38 116.384 116.38 116.384-52.185 116.384-116.38zm-96.056-27.716c0 10.219-8.287 18.51-18.51 18.51-10.219 0-18.51-8.291-18.51-18.51s8.291-18.51 18.51-18.51c10.223 0 18.51 8.291 18.51 18.51zm-74.043 27.716c0 10.219-8.291 18.51-18.51 18.51s-18.51-8.291-18.51-18.51 8.291-18.51 18.51-18.51 18.51 8.291 18.51 18.51zm99.381 41.903a54.56 54.56 0 0 0 3.642-4.262 53.907 53.907 0 0 0 7.699-13.772 54.386 54.386 0 0 0 1.686-4.803 53.519 53.519 0 0 0 2.33-12.358 54.665 54.665 0 0 0-1.686-17.049 54.574 54.574 0 0 0-2.862-8.648 54.376 54.376 0 0 0-3.642-7.01 54.593 54.593 0 0 0-4.587-6.378 53.736 53.736 0 0 0-5.534-5.55 54.483 54.483 0 0 0-6.378-4.587 54.724 54.724 0 0 0-7.01-3.634 54.26 54.26 0 0 0-8.652-2.862 54.013 54.013 0 0 0-17.049-1.694c-3.154.108-6.262.494-9.342 1.132a54.264 54.264 0 0 0-6.586 1.686l.463-12.59a18.51 18.51 0 1 0-11.074.123l.926 24.823a54.486 54.486 0 0 0-7.241 3.792 54.063 54.063 0 0 0-11.965 10.137 54.362 54.362 0 0 0-4.587 6.378 54.553 54.553 0 0 0-6.504 15.658 53.407 53.407 0 0 0-1.686 17.049 52.819 52.819 0 0 0 2.33 12.358 54.282 54.282 0 0 0 9.385 18.575 54.38 54.38 0 0 0 4.587 6.378 54.113 54.113 0 0 0 28.626 15.536 54.329 54.329 0 0 0 9.342 1.132 54.059 54.059 0 0 0 17.049-1.686 54.267 54.267 0 0 0 8.652-2.862 54.83 54.83 0 0 0 15.598-10.629 37.09 37.09 0 0 1-10.926 9.104 37.42 37.42 0 0 1-6.034 2.606 37.048 37.048 0 0 1-18.371 1.132 37.428 37.428 0 0 1-5.959-1.548 37.03 37.03 0 0 1-10.137-5.534 37.226 37.226 0 0 1-3.171-2.862 37.064 37.064 0 0 1-8.405-12.821 36.888 36.888 0 0 1-1.694-5.1 36.319 36.319 0 0 1-1.163-8.468c0-1.686.154-3.34.371-4.973.232-1.686.617-3.325 1.102-4.927a37.194 37.194 0 0 1 1.686-5.1 36.965 36.965 0 0 1 5.827-10.183 37.2 37.2 0 0 1 3.171-2.862 36.866 36.866 0 0 1 10.137-5.534 37.546 37.546 0 0 1 5.959-1.548 37.05 37.05 0 0 1 18.371 1.132 37.33 37.33 0 0 1 6.034 2.606 37.122 37.122 0 0 1 10.926 9.104 55.007 55.007 0 0 0-1.979-9.957 54.106 54.106 0 0 0-2.862-8.652 37.146 37.146 0 0 1-8.745-6.878 54.246 54.246 0 0 0-6.955 5.919zm37.021-14.188c0 10.219-8.291 18.51-18.51 18.51s-18.51-8.291-18.51-18.51 8.291-18.51 18.51-18.51 18.51 8.291 18.51 18.51z"/>
      </svg>
    </div>
  }
};

interface MicroVmAgentWorkspaceProps {
  className?: string;
  projectId?: string;
  vmId?: string;
}

export function MicroVmAgentWorkspace({
  className,
  projectId,
  vmId,
}: MicroVmAgentWorkspaceProps) {
  // Get state and actions from the workspace store
  const {
    // Container state
    containerStatus,
    containerError,
    containerType,
    containerId,

    // Tab management
    tabs,
    activeTabId,
    setActiveTabId,
    removeTab,
    addTab,

    // Panel tabs
    leftPanelTabs,
    rightPanelTabs,
    bottomPanelTabs,
    activeLeftPanelTab,
    activeRightPanelTab,
    activeBottomPanelTab,
    setActiveLeftPanelTab,
    setActiveRightPanelTab,
    setActiveBottomPanelTab,

    // Container operations
    startContainer,
    stopContainer,
    restartContainer,
    setContainerStatus,
    setContainerId,
    setContainerType,
  } = useWorkspaceStore();

  // Get state and actions from the activity bar store
  const {
    activeItemId,
    setActiveItemId,
    isLeftPanelVisible,
    isRightPanelVisible,
    isBottomPanelVisible,
    toggleLeftPanel,
    toggleRightPanel,
    toggleBottomPanel,
  } = useActivityBarStore();

  // Get tab content rendering functions
  const { renderTabContent, renderPanelContent } = useTabContent();

  // Get agent store
  const { activeAgent, setActiveAgent } = useAgentStore() as unknown as {
    activeAgent: string;
    setActiveAgent: (agent: string) => void;
  };

  // Set up state for MicroVM specific functionality
  const [vmStatus, setVmStatus] = useState<string>(containerStatus || "unknown");
  const [vmResourceUsage, setVmResourceUsage] = useState({
    cpu: 0,
    memory: 0,
    disk: 0,
  });
  const [fileExplorerPath, setFileExplorerPath] = useState("/");
  const [isCreatingVm, setIsCreatingVm] = useState(false);
  const [activeVmId, setActiveVmId] = useState<string | undefined>(vmId);
  const [vmCreationError, setVmCreationError] = useState<string | null>(null);

  // Handle tab close
  const handleTabClose = (tabId: string) => {
    removeTab(tabId);
  };

  // Handle activity bar item click
  const handleActivityBarItemClick = (itemId: string) => {
    setActiveItemId(itemId);

    // Find the clicked item in the activity bar items
    const clickedItem = MICROVM_ACTIVITY_BAR_ITEMS.find((item) => item.id === itemId);

    if (clickedItem) {
      // If the item has a panelId, ensure the panel is visible and set the active tab
      if (clickedItem.panelId) {
        // Make sure the panel is visible
        switch (clickedItem.panelId) {
          case "left":
            if (!isLeftPanelVisible) toggleLeftPanel();
            if (clickedItem.tabId) setActiveLeftPanelTab(clickedItem.tabId);
            break;
          case "right":
            if (!isRightPanelVisible) toggleRightPanel();
            if (clickedItem.tabId) setActiveRightPanelTab(clickedItem.tabId);
            break;
          case "bottom":
            if (!isBottomPanelVisible) toggleBottomPanel();
            if (clickedItem.tabId) setActiveBottomPanelTab(clickedItem.tabId);
            break;
        }
      }
      // If the item has a tabId but no panelId, set it as the active main tab
      else if (clickedItem.tabId && !clickedItem.panelId) {
        setActiveTabId(clickedItem.tabId);
      }
    }
  };

  // Create a new MicroVM using direct Firecracker interaction
  const createDirectVm = async (vmType = 'nodejs') => {
    setIsCreatingVm(true);
    setVmCreationError(null);
    
    try {
      // Generate a VM ID with a clearer prefix to identify direct VMs
      const vmId = `vm-direct-${vmType}-${Date.now().toString().slice(-6)}`;
      console.log(`Creating direct VM (${vmType}) with ID: ${vmId}`);
      
      const socketDir = `/tmp/microvms/${vmId}`;
      const socketPath = `${socketDir}/firecracker.sock`;
      
      // Get rootfs path based on VM type
      const vmTypeConfig = VM_TYPES[vmType] || VM_TYPES.default;
      const rootfsPath = vmTypeConfig.rootfsPath;
      
      // Create a direct VM using the API
      const response = await fetch(`/api/containerization/microvm/create-direct`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: vmId,
          id: vmId,
          socketPath: socketPath,
          kernelPath: "/home/<USER>/Documents/Webdev/app-gen/resources/kernels/vmlinux.bin",
          rootfsPath: rootfsPath,
          memSizeMib: 2048,
          vcpuCount: 2,
          bootArgs: "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp",
        }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`Failed to create direct MicroVM: ${response.status} - ${errorText}`);
      }
      
      const data = await response.json();
      
      if (data && data.success) {
        console.log(`VM created successfully. Setting active VM ID to: ${vmId}`);
        setActiveVmId(vmId);
        setContainerId(vmId);
        setContainerType("microvm");
        setVmStatus("running");
        setContainerStatus("running");
        
        // Add default terminal tab
        if (!tabs.some(tab => tab.id === "microvm-terminal")) {
          addTab({
            id: "microvm-terminal",
            title: "MicroVM Terminal",
            icon: <Terminal className="h-4 w-4" />,
            content: null,
            isActive: true,
          });
        }
        
        toast({
          title: "MicroVM Created Directly",
          description: `New MicroVM created with ID: ${vmId}`,
        });
        
        // Start polling for status
        fetchVmStatus();
        return true;
      } else {
        throw new Error("Failed to create direct VM");
      }
    } catch (error) {
      console.error("Error creating direct MicroVM:", error);
      setVmCreationError(error instanceof Error ? error.message : "Unknown error occurred");
      toast({
        title: "Direct VM Creation Failed",
        description: "Could not create VM directly. See console for details.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsCreatingVm(false);
    }
  };

  // Create a new MicroVM
  const createNewVm = async () => {
    setIsCreatingVm(true);
    setVmCreationError(null);
    
    try {
      // Try the dedicated create endpoint first
      const response = await fetch(`/api/microvms/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `vm-${Date.now().toString().slice(-6)}`,
          projectId: projectId || 'default-project',
          template: 'nextjs', // Default template
          memSizeMib: 2048,
          vcpuCount: 2,
          networkEnabled: true,
        }),
      });
      
      if (!response.ok) {
        // Fall back to the regular microvms endpoint
        const fallbackResponse = await fetch(`/api/microvms`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: `vm-${Date.now().toString().slice(-6)}`,
            projectId: projectId || 'default-project',
            template: 'nextjs', // Default template
            memSizeMib: 2048,
            vcpuCount: 2,
            networkEnabled: true,
          }),
        });
        
        if (!fallbackResponse.ok) {
          // If standard API methods fail, try direct VM creation approach
          console.log("Standard MicroVM creation failed. Trying direct approach...");
          const directSuccess = await createDirectVm();
          
          if (!directSuccess) {
            throw new Error("All VM creation methods failed");
          }
          return;
        }
        
        const data = await fallbackResponse.json();
        
        if (data && data.id) {
          setActiveVmId(data.id);
          setContainerId(data.id);
          setContainerType("microvm");
          setVmStatus("running");
          setContainerStatus("running");
          
          // Add default terminal tab
          if (!tabs.some(tab => tab.id === "microvm-terminal")) {
            addTab({
              id: "microvm-terminal",
              title: "MicroVM Terminal",
              icon: <Terminal className="h-4 w-4" />,
              content: null,
              isActive: true,
            });
          }
          
          toast({
            title: "MicroVM Created",
            description: `New MicroVM created with ID: ${data.id}`,
          });
          
          // Start polling for status
          fetchVmStatus();
        } else {
          throw new Error("No VM ID returned from creation API");
        }
      } else {
        const data = await response.json();
        
        // Process response from the dedicated create endpoint
        const vmId = data.id || data.vmId;
        
        if (vmId) {
          setActiveVmId(vmId);
          setContainerId(vmId);
          setContainerType("microvm");
          setVmStatus("running");
          setContainerStatus("running");
          
          // Add default terminal tab
          if (!tabs.some(tab => tab.id === "microvm-terminal")) {
            addTab({
              id: "microvm-terminal",
              title: "MicroVM Terminal",
              icon: <Terminal className="h-4 w-4" />,
              content: null,
              isActive: true,
            });
          }
          
          toast({
            title: "MicroVM Created",
            description: `New MicroVM created with ID: ${vmId}`,
          });
          
          // Start polling for status
          fetchVmStatus();
        } else {
          throw new Error("No VM ID returned from creation API");
        }
      }
    } catch (error) {
      console.error("Error creating MicroVM:", error);
      setVmCreationError(error instanceof Error ? error.message : "Unknown error occurred");
      toast({
        title: "Error",
        description: "Failed to create MicroVM. Check console for details.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingVm(false);
    }
  };

  // Remove the current MicroVM
  const removeCurrentVm = async () => {
    if (!activeVmId) return;
    
    if (!confirm(`Are you sure you want to remove the MicroVM with ID: ${activeVmId}?`)) {
      return;
    }
    
    try {
      // Use the remove endpoint
      const response = await fetch(`/api/microvms/remove`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          vmId: activeVmId,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to remove MicroVM: ${response.status}`);
      }
      
      toast({
        title: "MicroVM Removed",
        description: `MicroVM with ID ${activeVmId} has been removed.`,
      });
      
      // Reset state
      setActiveVmId(undefined);
      setContainerId("");
      
    } catch (error) {
      console.error("Error removing MicroVM:", error);
      toast({
        title: "Error",
        description: "Failed to remove MicroVM. Check console for details.",
        variant: "destructive",
      });
    }
  };

  // Effect to initialize the component with the MicroVM ID and fetch status
  useEffect(() => {
    if (activeVmId) {
      // Set container ID for the workspace
      setContainerId(activeVmId);
      setContainerType("microvm");

      // Add default terminal tab
      if (!tabs.some(tab => tab.id === "microvm-terminal")) {
        addTab({
          id: "microvm-terminal",
          title: "MicroVM Terminal",
          icon: <Terminal className="h-4 w-4" />,
          content: null,
          isActive: true,
        });
      }

      // Fetch MicroVM status
      fetchVmStatus();

      // Set up interval to refresh VM status
      const statusInterval = setInterval(fetchVmStatus, 10000);
      return () => clearInterval(statusInterval);
    }
  }, [activeVmId]);

  // Fetch VM status from the API
  const fetchVmStatus = async () => {
    if (!activeVmId) return;

    try {
      // First try the Firecracker API
      const response = await fetch(`/api/containerization/firecracker/${activeVmId}`);
      
      if (response.ok) {
        const data = await response.json();
        setVmStatus(data.status || "running");
        setContainerStatus(data.status || "running");
        
        // Update resource usage if available
        if (data.resources) {
          setVmResourceUsage({
            cpu: data.resources.cpu || 0,
            memory: data.resources.memory || 0,
            disk: data.resources.disk || 0,
          });
        }
        return;
      }
      
      // Fall back to the original MicroVM API
      const fallbackResponse = await fetch(`/api/containerization/microvm/${activeVmId}`);
      
      if (fallbackResponse.ok) {
        const data = await fallbackResponse.json();
        setVmStatus(data.status || "running");
        setContainerStatus(data.status || "running");
        
        // Update resource usage if available
        if (data.resources) {
          setVmResourceUsage({
            cpu: data.resources.cpu || 0,
            memory: data.resources.memory || 0,
            disk: data.resources.disk || 0,
          });
        }
      } else {
        setVmStatus("error");
        setContainerStatus("error");
      }
    } catch (error) {
      console.error("Error fetching VM status:", error);
      setVmStatus("error");
      setContainerStatus("error");
    }
  };

  // Start the MicroVM
  const handleStartVm = async () => {
    if (!activeVmId) return;
    
    try {
      // Try Firecracker API first
      let response = await fetch(`/api/containerization/firecracker/${activeVmId}/start`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        // Fall back to original MicroVM API
        response = await fetch(`/api/containerization/microvm/${activeVmId}/start`, {
          method: 'POST',
        });
      }
      
      if (response.ok) {
        setVmStatus("running");
        setContainerStatus("running");
        toast({
          title: "MicroVM Started",
          description: "The MicroVM has been started successfully.",
        });
      } else {
        throw new Error(`Failed to start MicroVM: ${response.status}`);
      }
    } catch (error) {
      console.error("Error starting MicroVM:", error);
      toast({
        title: "Error",
        description: "Failed to start MicroVM. Check console for details.",
        variant: "destructive",
      });
    }
  };

  // Stop the MicroVM
  const handleStopVm = async () => {
    if (!activeVmId) return;
    
    try {
      // Try Firecracker API first
      let response = await fetch(`/api/containerization/firecracker/${activeVmId}/stop`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        // Fall back to original MicroVM API
        response = await fetch(`/api/containerization/microvm/${activeVmId}/stop`, {
          method: 'POST',
        });
      }
      
      if (response.ok) {
        setVmStatus("stopped");
        setContainerStatus("stopped");
        toast({
          title: "MicroVM Stopped",
          description: "The MicroVM has been stopped successfully.",
        });
      } else {
        throw new Error(`Failed to stop MicroVM: ${response.status}`);
      }
    } catch (error) {
      console.error("Error stopping MicroVM:", error);
      toast({
        title: "Error",
        description: "Failed to stop MicroVM. Check console for details.",
        variant: "destructive",
      });
    }
  };

  // Restart the MicroVM
  const handleRestartVm = async () => {
    if (!activeVmId) return;
    
    try {
      // Try Firecracker API first
      let response = await fetch(`/api/containerization/firecracker/${activeVmId}/restart`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        // Fall back to original MicroVM API
        response = await fetch(`/api/containerization/microvm/${activeVmId}/restart`, {
          method: 'POST',
        });
      }
      
      if (response.ok) {
        setVmStatus("running");
        setContainerStatus("running");
        toast({
          title: "MicroVM Restarted",
          description: "The MicroVM has been restarted successfully.",
        });
      } else {
        throw new Error(`Failed to restart MicroVM: ${response.status}`);
      }
    } catch (error) {
      console.error("Error restarting MicroVM:", error);
      toast({
        title: "Error",
        description: "Failed to restart MicroVM. Check console for details.",
        variant: "destructive",
      });
    }
  };

  // Open file explorer at a specific path
  const openFileExplorer = (path: string) => {
    setFileExplorerPath(path);
    if (!isLeftPanelVisible) toggleLeftPanel();
    setActiveLeftPanelTab("explorer");
  };

  // Open a new terminal tab
  const openNewTerminal = () => {
    const terminalId = `terminal-${Date.now()}`;
    addTab({
      id: terminalId,
      title: "Terminal",
      icon: <Terminal className="h-4 w-4" />,
      content: null,
      isActive: true,
    });
    setActiveTabId(terminalId);
  };

  // Custom tab rendering for specific tabs
  const renderCustomTabContent = (tabId: string) => {
    if (tabId === "ai-agent") {
      return <MicroVmDevBot vmId={activeVmId} />;
    }
    
    if (tabId === "app-preview") {
      return <MicroVmIframePreview vmId={activeVmId} />;
    }
    
    if (tabId === "explorer") {
      // Check if this is a direct VM by looking at its structure or metadata
      const isDirectVm = containerType === "microvm" && 
        (activeVmId?.startsWith("vm-direct-") || activeVmId?.startsWith("vm-"));
      
      console.log(`Explorer tab: containerType=${containerType}, vmStatus=${vmStatus}, activeVmId=${activeVmId}, isDirectVm=${isDirectVm}`);
      
      if (isDirectVm) {
        return <DirectVmFileExplorer vmId={activeVmId} rootPath="/" />;
      } else {
        return <MicroVmFileExplorer vmId={activeVmId} rootPath="/app" />;
      }
    }
    
    if (tabId === "microvm-terminal" || tabId.startsWith("terminal")) {
      return <MicroVmTerminal vmId={activeVmId} className="h-full" />;
    }
    
    if (tabId === "microvm-dashboard") {
      return <MicroVmDashboard 
        projectId={projectId} 
        onOpenTerminal={(vmId) => {
          setActiveVmId(vmId);
          addTab({
            id: "microvm-terminal",
            title: "MicroVM Terminal",
            icon: <Terminal className="h-4 w-4" />,
            content: null,
            isActive: true,
          });
          setActiveTabId("microvm-terminal");
        }} 
      />;
    }
    
    // Otherwise use the default tab content rendering
    return renderTabContent(tabId);
  };

  // Update renderPanelContent for panels
  const renderCustomPanelContent = (panelId: "left" | "right" | "bottom", tabId: string) => {
    if (panelId === "left" && tabId === "explorer") {
      // Check if this is a direct VM by looking at its structure or metadata
      const isDirectVm = containerType === "microvm" && 
        (activeVmId?.startsWith("vm-direct-") || activeVmId?.startsWith("vm-"));
      
      console.log(`Explorer panel: containerType=${containerType}, vmStatus=${vmStatus}, activeVmId=${activeVmId}, isDirectVm=${isDirectVm}`);
      
      if (isDirectVm) {
        return <DirectVmFileExplorer vmId={activeVmId} rootPath="/" />;
      } else {
        return <MicroVmFileExplorer vmId={activeVmId} rootPath="/app" />;
      }
    }
    
    if (panelId === "bottom" && tabId === "terminal") {
      return <MicroVmTerminal vmId={activeVmId} className="h-full" />;
    }
    
    // Use the default panel content rendering
    return renderPanelContent(panelId, tabId);
  };

  // Render the VM creation UI when no VM is available
  const renderVmCreationUI = () => {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-card">
        <Tabs defaultValue="direct" className="w-full max-w-3xl">
          <TabsList className="grid w-full grid-cols-3 mb-6">
            <TabsTrigger value="standard">Standard VM</TabsTrigger>
            <TabsTrigger value="ephemeral">Ephemeral VM</TabsTrigger>
            <TabsTrigger value="direct" className="relative">
              Direct VM
              <span className="absolute -top-2 -right-2 px-1.5 py-0.5 rounded-full text-[0.6rem] font-medium bg-green-500 text-white">Recommended</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="standard">
            <div className="max-w-md w-full p-8 border rounded-lg shadow-sm mx-auto">
              <div className="flex items-center justify-center mb-6">
                <Boxes className="h-16 w-16 text-primary mb-2" />
              </div>
              
              <h2 className="text-2xl font-semibold text-center mb-2">Create a MicroVM</h2>
              <p className="text-muted-foreground text-center mb-6">
                No MicroVM is connected. Create a new one to start working on your project.
              </p>
              
              <div className="bg-amber-100 border border-amber-400 text-amber-800 p-4 rounded mb-4 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-800">
                <h3 className="text-sm font-bold mb-1">Backend Setup Required</h3>
                <p className="text-xs">
                  MicroVM functionality requires proper backend configuration. The current errors indicate that the MicroVM 
                  API endpoints may not be properly configured. Please check your server configuration.
                </p>
                <ul className="list-disc list-inside text-xs mt-2">
                  <li>Ensure the MicroVM manager is properly configured</li>
                  <li>Check that kernel and rootfs images are available</li>
                  <li>Verify that the Firecracker binary is installed and accessible</li>
                </ul>
              </div>
              
              {vmCreationError && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800">
                  <p className="text-sm">{vmCreationError}</p>
                </div>
              )}
              
              <Button
                className="w-full flex items-center justify-center gap-2"
                size="lg"
                onClick={createNewVm}
                disabled={isCreatingVm}
              >
                {isCreatingVm ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    Creating MicroVM...
                  </>
                ) : (
                  <>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Create New MicroVM
                  </>
                )}
              </Button>
              
              <p className="text-xs text-muted-foreground mt-4 text-center">
                This will create a new isolated environment with Next.js pre-installed.
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="ephemeral">
            <EphemeralVmCreator 
              projectId={projectId} 
              onVmCreated={(vmId) => {
                setActiveVmId(vmId);
                fetchVmStatus();
                
                // Add default terminal tab
                if (!tabs.some(tab => tab.id === "microvm-terminal")) {
                  addTab({
                    id: "microvm-terminal",
                    title: "MicroVM Terminal",
                    icon: <Terminal className="h-4 w-4" />,
                    content: null,
                    isActive: true,
                  });
                }
              }}
              onError={(error) => setVmCreationError(error.message)}
            />
          </TabsContent>
          
          <TabsContent value="direct">
            <div className="max-w-md w-full p-8 border rounded-lg shadow-sm mx-auto">
              <div className="flex items-center justify-center mb-6">
                <Server className="h-16 w-16 text-green-500 mb-2" />
              </div>
              
              <h2 className="text-2xl font-semibold text-center mb-2">Create Direct VM</h2>
              <p className="text-muted-foreground text-center mb-6">
                Create a VM by directly interacting with Firecracker. This bypasses the MicroVM Manager
                and has fewer permission requirements.
              </p>
              
              <div className="bg-green-100 border border-green-400 text-green-800 p-4 rounded mb-4 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800">
                <h3 className="text-sm font-bold mb-1">Recommended Approach</h3>
                <p className="text-xs">
                  This method directly creates a VM without using the MicroVM Manager, avoiding common permission errors.
                </p>
              </div>
              
              <div className="mb-6">
                <h3 className="text-sm font-semibold mb-2">Choose VM Type:</h3>
                <div className="grid grid-cols-1 gap-3">
                  {Object.entries(VM_TYPES).map(([key, config]) => (
                    <button 
                      key={key}
                      className={`flex items-center p-3 border rounded-lg hover:bg-accent ${
                        key === 'nodejs' ? 'border-green-500 bg-green-50 dark:bg-green-900/20' : ''
                      }`}
                      onClick={() => createDirectVm(key)}
                      disabled={isCreatingVm}
                    >
                      <div className="mr-3">
                        {config.icon}
                      </div>
                      <div className="text-left">
                        <div className="font-medium">{config.name}</div>
                        <div className="text-xs text-muted-foreground">{config.description}</div>
                      </div>
                      {key === 'nodejs' && (
                        <span className="ml-auto text-xs px-2 py-1 rounded bg-green-200 text-green-800 dark:bg-green-900 dark:text-green-300">
                          Recommended
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              </div>
              
              {vmCreationError && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800">
                  <p className="text-sm">{vmCreationError}</p>
                </div>
              )}
              
              <p className="text-xs text-muted-foreground mt-4 text-center">
                All VMs include basic development tools. The Node.js option also includes npm and common packages.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    );
  };

  // Add auto-creation of Direct VM on component mount
  useEffect(() => {
    if (VM_CONFIG.autoCreateDirectVm && !activeVmId && !isCreatingVm) {
      console.log("Auto-creating Direct VM on component mount...");
      
      // Set creating state to prevent multiple creation attempts
      setIsCreatingVm(true);
      
      // Show a toast notification
      toast({
        title: "Creating Node.js VM",
        description: "Automatically creating a Node.js VM for you...",
      });
      
      // Use setTimeout to ensure the component is fully mounted
      const timer = setTimeout(() => {
        createDirectVm('nodejs').then(success => {
          if (!success) {
            setIsCreatingVm(false);
            toast({
              title: "Direct VM Creation Failed",
              description: "Automatic creation failed. You can try again manually.",
              variant: "destructive",
            });
          }
        });
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [activeVmId, isCreatingVm]);

  // Add a loading overlay component for auto-creation
  const AutoCreationLoadingOverlay = () => (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex flex-col items-center justify-center">
      <div className="flex flex-col items-center gap-4 p-6 bg-card rounded-lg shadow-lg max-w-md text-center">
        <Server className="h-12 w-12 text-green-500 mb-2 animate-pulse" />
        <h2 className="text-xl font-semibold">Creating Direct VM</h2>
        <p className="text-muted-foreground mb-4">
          Setting up a Firecracker MicroVM for you. This will only take a moment...
        </p>
        <div className="flex flex-col items-center gap-2">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin text-primary" />
            <span>Initializing VM resources</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <TranscriptProvider>
      <ToolsProvider>
        <ContainerProvider>
          {VM_CONFIG.autoCreateDirectVm && isCreatingVm && !activeVmId && (
            <AutoCreationLoadingOverlay />
          )}
          {!activeVmId ? (
            renderVmCreationUI()
          ) : (
            <div className={cn("flex h-full w-full flex-col", className)}>
              <div className="flex items-center justify-between border-b border-border px-4 py-2 bg-card text-card-foreground">
                <div className="flex items-center gap-3">
                  <VKLogo
                    width={"1em"}
                    height={"1em"}
                    animate={true}
                    animationDuration={2}
                    animationDelay={0}
                    animationLoop={true}
                    animationType={"draw"}
                    strokeColor={"currentColor"}
                    strokeWidth={1}
                    animationTrigger={"onMount"}
                  />
                  <div className="h-4 w-[1px] bg-border"></div>
                  <h2 className="text-sm font-medium">MicroVM Environment</h2>
                  <span className="text-xs px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                    {projectId || "AI Workspace"}
                  </span>
                  
                  <div className="h-4 w-[1px] bg-border ml-2"></div>
                  <div className="ml-2">
                    <span className="text-xs font-medium">VM ID:</span>
                    <span className="ml-1 text-xs font-medium text-muted-foreground">
                      {activeVmId || "Not connected"}
                    </span>
                  </div>
                  
                  <div className="h-4 w-[1px] bg-border ml-2"></div>
                  <div className="ml-2">
                    <span className={cn(
                      "text-xs px-1.5 py-0.5 rounded-full",
                      vmStatus === "running" ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" :
                      vmStatus === "stopped" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400" :
                      vmStatus === "error" ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400" :
                      "bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-400"
                    )}>
                      {vmStatus === "running" ? "Running" :
                      vmStatus === "stopped" ? "Stopped" :
                      vmStatus === "error" ? "Error" :
                      "Unknown"}
                    </span>
                  </div>
                  
                  <div className="h-4 w-[1px] bg-border ml-2"></div>
                  <div className="ml-2 flex items-center gap-2">
                    <div className="flex items-center">
                      <Cpu className="h-3 w-3 text-muted-foreground" />
                      <span className="ml-1 text-xs">{vmResourceUsage.cpu}%</span>
                    </div>
                    <div className="flex items-center">
                      <Database className="h-3 w-3 text-muted-foreground" />
                      <span className="ml-1 text-xs">{vmResourceUsage.memory}MB</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <div className="flex items-center mr-2 border-r border-border pr-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "px-2 py-1",
                        isLeftPanelVisible ? "bg-muted" : "bg-transparent"
                      )}
                      onClick={toggleLeftPanel}
                      title={isLeftPanelVisible ? "Hide Explorer" : "Show Explorer"}
                    >
                      <Files className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "px-2 py-1",
                        isBottomPanelVisible ? "bg-muted" : "bg-transparent"
                      )}
                      onClick={toggleBottomPanel}
                      title={
                        isBottomPanelVisible ? "Hide Terminal" : "Show Terminal"
                      }
                    >
                      <Terminal className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "px-2 py-1",
                        isRightPanelVisible ? "bg-muted" : "bg-transparent"
                      )}
                      onClick={toggleRightPanel}
                      title={
                        isRightPanelVisible ? "Hide Properties" : "Show Properties"
                      }
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-green-500 text-green-600 hover:bg-green-500/10 hover:text-green-700 dark:border-green-600 dark:text-green-500 dark:hover:text-green-400"
                    onClick={handleStartVm}
                    disabled={vmStatus === "running"}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Start
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-yellow-500 text-yellow-600 hover:bg-yellow-500/10 hover:text-yellow-700 dark:border-yellow-600 dark:text-yellow-500 dark:hover:text-yellow-400"
                    onClick={handleStopVm}
                    disabled={vmStatus !== "running"}
                  >
                    <Pause className="h-4 w-4 mr-2" />
                    Stop
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500 text-blue-600 hover:bg-blue-500/10 hover:text-blue-700 dark:border-blue-600 dark:text-blue-500 dark:hover:text-blue-400"
                    onClick={handleRestartVm}
                    disabled={vmStatus !== "running"}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Restart
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-border bg-card text-card-foreground hover:bg-muted"
                    onClick={openNewTerminal}
                    disabled={vmStatus !== "running"}
                  >
                    <Terminal className="h-4 w-4 mr-2" />
                    New Terminal
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-purple-500 text-purple-600 hover:bg-purple-500/10 hover:text-purple-700 dark:border-purple-600 dark:text-purple-500 dark:hover:text-purple-400"
                    onClick={() => { 
                      setActiveVmId(undefined); 
                      setContainerId("");
                    }}
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    New VM
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-500 text-red-600 hover:bg-red-500/10 hover:text-red-700 dark:border-red-600 dark:text-red-500 dark:hover:text-red-400"
                    onClick={removeCurrentVm}
                  >
                    <CircleSlash className="h-4 w-4 mr-2" />
                    Remove VM
                  </Button>
                  
                  <UserMenu />
                </div>
              </div>

              <div className="flex-1 overflow-hidden flex">
                {/* VSCode Activity Bar */}
                <VSCodeActivityBar
                  items={MICROVM_ACTIVITY_BAR_ITEMS}
                  activeItemId={activeItemId}
                  onItemClick={handleActivityBarItemClick}
                  checkTabExists={(tabId) => tabs.some((tab) => tab.id === tabId)}
                  createTabIfNotExists={(tabId, title, _icon) => {
                    // Use the addTab function from the workspace store
                    const { addTab } = useWorkspaceStore.getState();

                    // Map icons based on tab ID
                    let tabIcon;
                    switch (tabId) {
                      case "explorer":
                        tabIcon = <Files className="h-4 w-4" />;
                        break;
                      case "search":
                        tabIcon = <Search className="h-4 w-4" />;
                        break;
                      case "ai-agent":
                        tabIcon = <Sparkles className="h-4 w-4" />;
                        break;
                      case "terminal":
                        tabIcon = <Terminal className="h-4 w-4" />;
                        break;
                      case "microvm-dashboard":
                        tabIcon = <Boxes className="h-4 w-4" />;
                        break;
                      case "problems":
                        tabIcon = <Bug className="h-4 w-4" />;
                        break;
                      default:
                        tabIcon = <FileCode className="h-4 w-4" />;
                    }

                    addTab({
                      id: tabId,
                      title: title,
                      icon: tabIcon,
                      content: null,
                      isActive: true,
                    });
                  }}
                  checkPanelTabExists={(panelId, tabId) => {
                    // Get the appropriate panel tabs array
                    const getPanelTabs = () => {
                      switch (panelId) {
                        case "left":
                          return leftPanelTabs;
                        case "right":
                          return rightPanelTabs;
                        case "bottom":
                          return bottomPanelTabs;
                        default:
                          return [];
                      }
                    };

                    // Check if the tab exists in the panel
                    return getPanelTabs().some((tab) => tab.id === tabId);
                  }}
                  createPanelTabIfNotExists={(panelId, tabId, title, _icon) => {
                    // Use the addPanelTab function from the workspace store
                    const { addPanelTab } = useWorkspaceStore.getState();

                    // Map icons based on tab ID
                    let tabIcon;
                    switch (tabId) {
                      case "explorer":
                        tabIcon = <Files className="h-4 w-4" />;
                        break;
                      case "search":
                        tabIcon = <Search className="h-4 w-4" />;
                        break;
                      case "terminal":
                        tabIcon = <Terminal className="h-4 w-4" />;
                        break;
                      case "problems":
                        tabIcon = <Bug className="h-4 w-4" />;
                        break;
                      default:
                        tabIcon = <FileCode className="h-4 w-4" />;
                    }

                    addPanelTab(panelId, {
                      id: tabId,
                      title: title,
                      icon: tabIcon,
                      content: null,
                      isActive: true,
                    });
                  }}
                />

                <ResizablePanelGroup
                  direction="horizontal"
                  className="h-full flex-1"
                >
                  {/* Left Panel */}
                  {isLeftPanelVisible && (
                    <>
                      <SidePanel
                        position="left"
                        isVisible={isLeftPanelVisible}
                        onToggle={toggleLeftPanel}
                        tabs={leftPanelTabs}
                        activeTabId={activeLeftPanelTab}
                        onTabChange={setActiveLeftPanelTab}
                        defaultSize={30}
                        minSize={15}
                        maxSize={30}
                        className="bg-card"
                      >
                        {/* Render active left panel tab content */}
                        {renderCustomPanelContent("left", activeLeftPanelTab)}
                      </SidePanel>

                      <ResizableHandle withHandle />
                    </>
                  )}

                  {/* Main Content */}
                  <ResizablePanel
                    defaultSize={
                      isLeftPanelVisible && isRightPanelVisible
                        ? 60
                        : isLeftPanelVisible || isRightPanelVisible
                        ? 80
                        : 100
                    }
                  >
                    <ResizablePanelGroup direction="vertical" className="h-full">
                      <ResizablePanel defaultSize={isBottomPanelVisible ? 70 : 100}>
                        <TabsLayout className="border-none h-full">
                          <TabGroup
                            tabs={tabs}
                            activeTabId={activeTabId}
                            onTabChange={setActiveTabId}
                            onTabClose={handleTabClose}
                            groupId="main-tabs"
                          />
                          <div className="flex-1 overflow-auto">
                            {vmStatus === "running" ? (
                              renderCustomTabContent(activeTabId)
                            ) : (
                              <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                                <CircleSlash className="h-12 w-12 mb-4" />
                                <p className="text-lg">MicroVM is not running</p>
                                <p className="text-sm mb-4">Start the MicroVM to interact with it</p>
                                <Button 
                                  onClick={handleStartVm}
                                  className="bg-primary text-primary-foreground hover:bg-primary/90"
                                >
                                  <Play className="h-4 w-4 mr-2" />
                                  Start MicroVM
                                </Button>
                              </div>
                            )}
                          </div>
                        </TabsLayout>
                      </ResizablePanel>

                      {/* Bottom Panel */}
                      {isBottomPanelVisible && (
                        <>
                          <ResizableHandle withHandle />
                          <SidePanel
                            position="bottom"
                            isVisible={isBottomPanelVisible}
                            onToggle={toggleBottomPanel}
                            tabs={bottomPanelTabs}
                            activeTabId={activeBottomPanelTab}
                            onTabChange={setActiveBottomPanelTab}
                            defaultSize={30}
                            minSize={15}
                            maxSize={50}
                            className="bg-card"
                          >
                            {/* Render active bottom panel tab content */}
                            {renderCustomPanelContent("bottom", activeBottomPanelTab)}
                          </SidePanel>
                        </>
                      )}
                    </ResizablePanelGroup>
                  </ResizablePanel>

                  {/* Right Panel */}
                  {isRightPanelVisible && (
                    <>
                      <ResizableHandle withHandle />
                      <SidePanel
                        position="right"
                        isVisible={isRightPanelVisible}
                        onToggle={toggleRightPanel}
                        tabs={rightPanelTabs}
                        activeTabId={activeRightPanelTab}
                        onTabChange={setActiveRightPanelTab}
                        defaultSize={20}
                        minSize={15}
                        maxSize={30}
                        className="bg-card"
                      >
                        {/* Render active right panel tab content */}
                        {renderCustomPanelContent("right", activeRightPanelTab)}
                      </SidePanel>
                    </>
                  )}
                </ResizablePanelGroup>
              </div>
            </div>
          )}
        </ContainerProvider>
      </ToolsProvider>
    </TranscriptProvider>
  );
} 