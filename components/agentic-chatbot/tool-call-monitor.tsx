'use client';

import { useState, useEffect, useMemo } from 'react';
import {
  useActiveToolCalls,
  useIsExecuting,
  useToolCallMetrics,
  useToolCallOperations,
  ToolCall
} from './stores/tool-call-store';
import {
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  Pause,
  Download,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface ToolCallMonitorProps {
  className?: string;
  showMetrics?: boolean;
  showHistory?: boolean;
  compact?: boolean;
}

const TOOL_ICONS = {
  generateComponent: '🧩',
  analyzeCode: '🔍',
  generateTests: '🧪',
  explainCode: '📖',
  refactorCode: '🔧',
  default: '⚡',
};

const STATE_COLORS = {
  pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  executing: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  cancelled: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
};

const STATE_ICONS = {
  pending: Clock,
  executing: Loader2,
  completed: CheckCircle,
  error: XCircle,
  cancelled: Pause,
};

export function ToolCallMonitor({
  className = "",
  showMetrics = true,
  showHistory = true,
  compact = false,
}: ToolCallMonitorProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [selectedCall, setSelectedCall] = useState<ToolCall | null>(null);
  const [currentTime, setCurrentTime] = useState(Date.now());

  const activeCalls = useActiveToolCalls();
  const isExecuting = useIsExecuting();
  const metrics = useToolCallMetrics();
  const { clearToolCalls, exportToolCalls, cancelToolCall } = useToolCallOperations();

  // Update current time every second for duration calculations, but only when there are active calls
  useEffect(() => {
    if (activeCalls.length === 0) {
      return;
    }

    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, [activeCalls.length]);

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  // Memoize active calls with current durations to prevent unnecessary re-renders
  const activeCallsWithDurations = useMemo(() => {
    return activeCalls.map(call => ({
      ...call,
      currentDuration: currentTime - call.startTime
    }));
  }, [activeCalls, currentTime]);

  const handleExport = () => {
    const data = exportToolCalls();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tool-calls-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!isVisible && compact) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(true)}
        className={`fixed bottom-4 right-4 z-50 ${className}`}
      >
        <Eye size={16} />
        Tool Monitor
        {isExecuting && (
          <Badge variant="secondary" className="ml-2">
            {activeCalls.length}
          </Badge>
        )}
      </Button>
    );
  }

  return (
    <Card className={`${className} ${compact ? 'fixed bottom-4 right-4 w-96 z-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Activity size={16} />
            Tool Call Monitor
            {isExecuting && (
              <Badge variant="secondary" className="ml-2">
                {activeCalls.length} active
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExport}
              className="h-6 w-6 p-0"
            >
              <Download size={12} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearToolCalls}
              className="h-6 w-6 p-0"
            >
              <Trash2 size={12} />
            </Button>
            {compact && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
                className="h-6 w-6 p-0"
              >
                <EyeOff size={12} />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <Tabs defaultValue="active" className="w-full">
          <TabsList className="grid w-full grid-cols-3 h-8">
            <TabsTrigger value="active" className="text-xs">
              Active ({activeCalls.length})
            </TabsTrigger>
            {showMetrics && (
              <TabsTrigger value="metrics" className="text-xs">
                Metrics
              </TabsTrigger>
            )}
            {showHistory && (
              <TabsTrigger value="history" className="text-xs">
                History
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="active" className="mt-3">
            <ScrollArea className="h-48">
              {activeCalls.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400 text-sm">
                  No active tool calls
                </div>
              ) : (
                <div className="space-y-2">
                  {activeCallsWithDurations.map((call) => {
                    const StateIcon = STATE_ICONS[call.state];
                    const toolIcon = TOOL_ICONS[call.toolName as keyof typeof TOOL_ICONS] || TOOL_ICONS.default;

                    return (
                      <div
                        key={call.id}
                        className="flex items-center gap-2 p-2 border border-gray-200 dark:border-gray-700 rounded-lg text-sm"
                      >
                        <span className="text-lg">{toolIcon}</span>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{call.toolName}</span>
                            <Badge variant="secondary" className={`text-xs ${STATE_COLORS[call.state]}`}>
                              <StateIcon size={10} className={call.state === 'executing' ? 'animate-spin' : ''} />
                              <span className="ml-1">{call.state}</span>
                            </Badge>
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDuration(call.currentDuration)}
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => setSelectedCall(call)}
                              >
                                <Eye size={10} />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Tool Call Details</DialogTitle>
                              </DialogHeader>
                              {selectedCall && (
                                <div className="space-y-4">
                                  <div>
                                    <strong>Tool:</strong> {selectedCall.toolName}
                                  </div>
                                  <div>
                                    <strong>State:</strong> {selectedCall.state}
                                  </div>
                                  <div>
                                    <strong>Arguments:</strong>
                                    <pre className="mt-1 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">
                                      {JSON.stringify(selectedCall.args, null, 2)}
                                    </pre>
                                  </div>
                                  {selectedCall.result && (
                                    <div>
                                      <strong>Result:</strong>
                                      <pre className="mt-1 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">
                                        {JSON.stringify(selectedCall.result, null, 2)}
                                      </pre>
                                    </div>
                                  )}
                                  {selectedCall.error && (
                                    <div>
                                      <strong>Error:</strong>
                                      <div className="mt-1 p-2 bg-red-50 dark:bg-red-900/20 rounded text-xs text-red-700 dark:text-red-300">
                                        {selectedCall.error}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                          {call.state === 'executing' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => cancelToolCall(call.id)}
                            >
                              <Pause size={10} />
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          {showMetrics && (
            <TabsContent value="metrics" className="mt-3">
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div className="text-lg font-bold">{metrics.totalCalls}</div>
                    <div className="text-xs text-gray-500">Total Calls</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div className="text-lg font-bold">{formatDuration(metrics.averageDuration)}</div>
                    <div className="text-xs text-gray-500">Avg Duration</div>
                  </div>
                </div>
                
                <div>
                  <div className="text-sm font-medium mb-2">Success Rate</div>
                  <Progress 
                    value={metrics.totalCalls > 0 ? (metrics.completedCalls / metrics.totalCalls) * 100 : 0} 
                    className="h-2"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {metrics.completedCalls}/{metrics.totalCalls} completed
                  </div>
                </div>

                <div>
                  <div className="text-sm font-medium mb-2">Tool Usage</div>
                  <div className="space-y-1">
                    {Object.entries(metrics.toolUsageCount).map(([tool, count]) => (
                      <div key={tool} className="flex justify-between text-xs">
                        <span>{tool}</span>
                        <span>{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
          )}

          {showHistory && (
            <TabsContent value="history" className="mt-3">
              <ScrollArea className="h-48">
                {metrics.recentCalls.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400 text-sm">
                    No call history
                  </div>
                ) : (
                  <div className="space-y-2">
                    {metrics.recentCalls.map((call) => {
                      const StateIcon = STATE_ICONS[call.state];
                      const toolIcon = TOOL_ICONS[call.toolName as keyof typeof TOOL_ICONS] || TOOL_ICONS.default;
                      
                      return (
                        <div
                          key={call.id}
                          className="flex items-center gap-2 p-2 border border-gray-200 dark:border-gray-700 rounded-lg text-sm"
                        >
                          <span className="text-lg">{toolIcon}</span>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium truncate">{call.toolName}</span>
                              <Badge variant="secondary" className={`text-xs ${STATE_COLORS[call.state]}`}>
                                <StateIcon size={10} />
                                <span className="ml-1">{call.state}</span>
                              </Badge>
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {call.duration ? formatDuration(call.duration) : 'N/A'}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}
