'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { 
  Send, 
  Paperclip, 
  Code, 
  FileText, 
  Zap, 
  Settings, 
  X, 
  Plus,
  ChevronDown,
  Sparkles,
  Terminal,
  Database,
  Globe
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

export interface ContextItem {
  id: string;
  type: 'file' | 'code' | 'documentation' | 'api' | 'database' | 'custom';
  title: string;
  content: string;
  metadata?: Record<string, any>;
}

export interface EnhancedTextareaProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (message: string, context: ContextItem[]) => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  onStop?: () => void;
  maxLength?: number;
  minRows?: number;
  maxRows?: number;
  showCharacterCount?: boolean;
  enableContextSelection?: boolean;
  enableTemplates?: boolean;
  enableFileUpload?: boolean;
  className?: string;
}

const CONTEXT_TYPES = [
  { type: 'file', icon: FileText, label: 'File', color: 'bg-blue-100 text-blue-800' },
  { type: 'code', icon: Code, label: 'Code', color: 'bg-green-100 text-green-800' },
  { type: 'documentation', icon: Globe, label: 'Docs', color: 'bg-purple-100 text-purple-800' },
  { type: 'api', icon: Zap, label: 'API', color: 'bg-orange-100 text-orange-800' },
  { type: 'database', icon: Database, label: 'Database', color: 'bg-indigo-100 text-indigo-800' },
  { type: 'custom', icon: Terminal, label: 'Custom', color: 'bg-gray-100 text-gray-800' },
] as const;

const QUICK_TEMPLATES = [
  { label: 'Generate Component', template: 'Create a React component that ' },
  { label: 'Fix Bug', template: 'Help me fix this bug: ' },
  { label: 'Optimize Code', template: 'Optimize this code for better performance: ' },
  { label: 'Add Tests', template: 'Write unit tests for: ' },
  { label: 'Explain Code', template: 'Explain how this code works: ' },
  { label: 'Refactor', template: 'Refactor this code to be more maintainable: ' },
];

export function EnhancedTextarea({
  value,
  onChange,
  onSubmit,
  placeholder = "Describe what you want to build...",
  disabled = false,
  isLoading = false,
  onStop,
  maxLength = 4000,
  minRows = 3,
  maxRows = 12,
  showCharacterCount = true,
  enableContextSelection = true,
  enableTemplates = true,
  enableFileUpload = true,
  className = "",
}: EnhancedTextareaProps) {
  const [attachedContext, setAttachedContext] = useState<ContextItem[]>([]);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    textarea.style.height = 'auto';
    const scrollHeight = textarea.scrollHeight;
    const lineHeight = 24; // Approximate line height
    const minHeight = lineHeight * minRows;
    const maxHeight = lineHeight * maxRows;
    
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
    textarea.style.height = `${newHeight}px`;
  }, [minRows, maxRows]);

  useEffect(() => {
    adjustTextareaHeight();
  }, [value, adjustTextareaHeight]);

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
    
    if (e.key === 'Escape') {
      setShowContextMenu(false);
      setShowTemplates(false);
      setShowSettings(false);
    }

    // Quick template shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 't':
          e.preventDefault();
          setShowTemplates(!showTemplates);
          break;
        case 'k':
          e.preventDefault();
          setShowContextMenu(!showContextMenu);
          break;
      }
    }
  };

  const handleSubmit = () => {
    if (!value.trim() || disabled || isLoading) return;
    onSubmit(value, attachedContext);
  };

  const handleTemplateSelect = (template: string) => {
    onChange(template);
    setShowTemplates(false);
    textareaRef.current?.focus();
  };

  const addContext = (context: ContextItem) => {
    setAttachedContext(prev => [...prev, context]);
    setShowContextMenu(false);
  };

  const removeContext = (id: string) => {
    setAttachedContext(prev => prev.filter(item => item.id !== id));
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    try {
      const file = files[0];
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const content = event.target?.result as string;
        const contextItem: ContextItem = {
          id: `file-${Date.now()}`,
          type: 'file',
          title: file.name,
          content,
          metadata: {
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
          }
        };
        addContext(contextItem);
      };
      
      reader.readAsText(file);
    } catch (error) {
      console.error('Error reading file:', error);
    }
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const characterCount = value.length;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;

  return (
    <TooltipProvider>
      <div className={`relative bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
        {/* Context Items Display */}
        {attachedContext.length > 0 && (
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex flex-wrap gap-2">
              {attachedContext.map((item) => {
                const contextType = CONTEXT_TYPES.find(t => t.type === item.type);
                const Icon = contextType?.icon || FileText;
                
                return (
                  <Badge
                    key={item.id}
                    variant="secondary"
                    className={`${contextType?.color} flex items-center gap-1 pr-1`}
                  >
                    <Icon size={12} />
                    <span className="text-xs truncate max-w-[120px]">{item.title}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-red-100"
                      onClick={() => removeContext(item.id)}
                    >
                      <X size={10} />
                    </Button>
                  </Badge>
                );
              })}
            </div>
          </div>
        )}

        {/* Main Input Area */}
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            maxLength={maxLength}
            className="w-full resize-none bg-transparent border-0 p-4 pr-16 focus:outline-none focus:ring-0 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
            style={{ minHeight: `${minRows * 24}px` }}
          />

          {/* Inline Action Buttons */}
          <div className="absolute bottom-3 right-3 flex items-center gap-1">
            {enableTemplates && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => setShowTemplates(!showTemplates)}
                  >
                    <Sparkles size={16} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Templates (Ctrl+T)</TooltipContent>
              </Tooltip>
            )}

            {enableContextSelection && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => setShowContextMenu(!showContextMenu)}
                  >
                    <Plus size={16} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Add Context (Ctrl+K)</TooltipContent>
              </Tooltip>
            )}

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setShowSettings(!showSettings)}
                >
                  <Settings size={16} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Settings</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={isLoading ? "destructive" : "default"}
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={isLoading ? onStop : handleSubmit}
                  disabled={(!value.trim() && !isLoading) || isOverLimit}
                >
                  {isLoading ? <X size={16} /> : <Send size={16} />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isLoading ? "Stop" : "Send (Enter)"}
              </TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Footer */}
        <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-4">
            <span>Enter to send • Shift+Enter for new line</span>
            {enableTemplates && <span>Ctrl+T for templates</span>}
            {enableContextSelection && <span>Ctrl+K for context</span>}
          </div>
          
          {showCharacterCount && (
            <span className={isNearLimit ? (isOverLimit ? 'text-red-500' : 'text-yellow-500') : ''}>
              {characterCount}/{maxLength}
            </span>
          )}
        </div>

        {/* Templates Dropdown */}
        {showTemplates && (
          <div className="absolute bottom-full left-0 mb-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Quick Templates</h3>
            </div>
            <div className="p-2 max-h-60 overflow-y-auto">
              {QUICK_TEMPLATES.map((template, index) => (
                <button
                  key={index}
                  onClick={() => handleTemplateSelect(template.template)}
                  className="w-full text-left p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {template.label}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {template.template}
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Context Menu Dropdown */}
        {showContextMenu && (
          <div className="absolute bottom-full left-0 mb-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Add Context</h3>
            </div>
            <div className="p-2">
              {CONTEXT_TYPES.map((contextType) => {
                const Icon = contextType.icon;
                return (
                  <button
                    key={contextType.type}
                    onClick={() => {
                      if (contextType.type === 'file') {
                        fileInputRef.current?.click();
                      } else {
                        // Handle other context types
                        const content = prompt(`Enter ${contextType.label.toLowerCase()} content:`);
                        if (content) {
                          const contextItem: ContextItem = {
                            id: `${contextType.type}-${Date.now()}`,
                            type: contextType.type as any,
                            title: `${contextType.label} Context`,
                            content,
                          };
                          addContext(contextItem);
                        }
                      }
                    }}
                    className="w-full flex items-center gap-3 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className={`p-1.5 rounded-md ${contextType.color}`}>
                      <Icon size={14} />
                    </div>
                    <div className="text-left">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {contextType.label}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Add {contextType.label.toLowerCase()} context
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Settings Dropdown */}
        {showSettings && (
          <div className="absolute bottom-full right-0 mb-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Settings</h3>
            </div>
            <div className="p-2">
              <div className="space-y-2">
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Show character count</span>
                  <input type="checkbox" defaultChecked={showCharacterCount} className="rounded" />
                </label>
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Enable templates</span>
                  <input type="checkbox" defaultChecked={enableTemplates} className="rounded" />
                </label>
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Enable file upload</span>
                  <input type="checkbox" defaultChecked={enableFileUpload} className="rounded" />
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileUpload}
          className="hidden"
          accept=".txt,.js,.ts,.jsx,.tsx,.html,.css,.json,.md,.py,.java,.cpp,.c,.php,.rb,.go,.rs,.swift,.kt"
        />
      </div>
    </TooltipProvider>
  );
}
