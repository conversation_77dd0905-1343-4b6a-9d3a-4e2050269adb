'use client';

import { useState } from 'react';
import { 
  AgenticChatInterface, 
  ToolCallMonitor, 
  useToolCallOperations,
  useActiveToolCalls,
  useIsExecuting,
  useToolCallMetrics,
  createClientTools
} from '../index';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Play, 
  Square, 
  BarChart3, 
  Download,
  Trash2,
  Code,
  TestTube,
  Zap
} from 'lucide-react';

export function ToolCallDemo() {
  const [demoMode, setDemoMode] = useState<'chat' | 'monitor' | 'manual'>('chat');
  
  // Tool call store hooks
  const { 
    addToolCall, 
    completeToolCall, 
    errorToolCall, 
    clearToolCalls,
    exportToolCalls 
  } = useToolCallOperations();
  
  const activeCalls = useActiveToolCalls();
  const isExecuting = useIsExecuting();
  const metrics = useToolCallMetrics();

  // Client tools for demonstration
  const clientTools = createClientTools();

  // Manual tool call simulation
  const simulateToolCall = async (toolName: string, args: any) => {
    const callId = addToolCall({
      toolName,
      args,
      messageId: `demo-${Date.now()}`,
    });

    // Simulate execution delay
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

    // Randomly succeed or fail for demo purposes
    if (Math.random() > 0.2) {
      completeToolCall(callId, {
        success: true,
        result: `Mock result for ${toolName}`,
        timestamp: new Date().toISOString(),
      });
    } else {
      errorToolCall(callId, `Simulated error for ${toolName}`);
    }
  };

  const handleExportData = () => {
    const data = exportToolCalls();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tool-calls-demo-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Tool Call Store Demo
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Comprehensive demonstration of the tool call tracking system with real-time monitoring,
            metrics, and client-side tool execution.
          </p>
        </div>

        {/* Status Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity size={20} />
              System Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {activeCalls.length}
                </div>
                <div className="text-sm text-gray-500">Active Calls</div>
              </div>
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {metrics.completedCalls}
                </div>
                <div className="text-sm text-gray-500">Completed</div>
              </div>
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {metrics.errorCalls}
                </div>
                <div className="text-sm text-gray-500">Errors</div>
              </div>
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round(metrics.averageDuration)}ms
                </div>
                <div className="text-sm text-gray-500">Avg Duration</div>
              </div>
            </div>
            
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center gap-2">
                <Badge variant={isExecuting ? "default" : "secondary"}>
                  {isExecuting ? "Executing" : "Idle"}
                </Badge>
                <span className="text-sm text-gray-500">
                  {metrics.totalCalls} total calls
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportData}
                  disabled={metrics.totalCalls === 0}
                >
                  <Download size={16} />
                  Export
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearToolCalls}
                  disabled={metrics.totalCalls === 0}
                >
                  <Trash2 size={16} />
                  Clear
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Demo Modes */}
        <Tabs value={demoMode} onValueChange={(value) => setDemoMode(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chat">AI Chat Demo</TabsTrigger>
            <TabsTrigger value="monitor">Tool Monitor</TabsTrigger>
            <TabsTrigger value="manual">Manual Testing</TabsTrigger>
          </TabsList>

          <TabsContent value="chat" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>AI Chat with Tool Call Tracking</CardTitle>
                <CardDescription>
                  Chat with the AI assistant and watch tool calls being tracked in real-time.
                  Try asking for code generation, analysis, or testing.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[600px]">
                  <AgenticChatInterface
                    apiEndpoint="/api/agentic-chat"
                    systemPrompt="You are an expert software developer. Use tools when appropriate to help with code generation, analysis, testing, and documentation."
                    maxSteps={5}
                    enableTools={true}
                    enableContextManager={true}
                    className="h-full rounded-lg border"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="monitor" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Tool Call Monitor</CardTitle>
                <CardDescription>
                  Real-time monitoring of tool calls with metrics and history
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[500px]">
                  <ToolCallMonitor
                    className="h-full"
                    showMetrics={true}
                    showHistory={true}
                    compact={false}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manual" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Manual Tool Testing</CardTitle>
                <CardDescription>
                  Manually trigger tool calls to test the tracking system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={() => simulateToolCall('generateComponent', {
                      componentName: 'TestComponent',
                      props: ['title', 'description'],
                      typescript: true,
                    })}
                    disabled={isExecuting}
                    className="h-20 flex flex-col items-center gap-2"
                  >
                    <Code size={24} />
                    Generate Component
                  </Button>
                  
                  <Button
                    onClick={() => simulateToolCall('analyzeCode', {
                      code: 'function example() { return "test"; }',
                      language: 'javascript',
                      analysisType: 'all',
                    })}
                    disabled={isExecuting}
                    className="h-20 flex flex-col items-center gap-2"
                  >
                    <Zap size={24} />
                    Analyze Code
                  </Button>
                  
                  <Button
                    onClick={() => simulateToolCall('generateTests', {
                      code: 'function add(a, b) { return a + b; }',
                      testFramework: 'jest',
                      testType: 'unit',
                    })}
                    disabled={isExecuting}
                    className="h-20 flex flex-col items-center gap-2"
                  >
                    <TestTube size={24} />
                    Generate Tests
                  </Button>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4">Client-Side Tools</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button
                      onClick={() => clientTools.copyToClipboard({ text: 'Hello from tool call demo!' })}
                      variant="outline"
                      className="justify-start"
                    >
                      Copy to Clipboard
                    </Button>
                    
                    <Button
                      onClick={() => clientTools.openUrl({ url: 'https://github.com' })}
                      variant="outline"
                      className="justify-start"
                    >
                      Open URL
                    </Button>
                    
                    <Button
                      onClick={() => clientTools.saveToLocalStorage({ 
                        key: 'demo-data', 
                        value: { timestamp: Date.now(), demo: true } 
                      })}
                      variant="outline"
                      className="justify-start"
                    >
                      Save to LocalStorage
                    </Button>
                    
                    <Button
                      onClick={() => clientTools.loadFromLocalStorage({ key: 'demo-data' })}
                      variant="outline"
                      className="justify-start"
                    >
                      Load from LocalStorage
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Live Monitor */}
            <Card>
              <CardHeader>
                <CardTitle>Live Tool Call Monitor</CardTitle>
              </CardHeader>
              <CardContent>
                <ToolCallMonitor
                  className="h-64"
                  showMetrics={false}
                  showHistory={false}
                  compact={true}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Usage Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Use the Tool Call Store</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">For Developers:</h4>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• Import hooks from the agentic-chatbot package</li>
                  <li>• Use useAgenticChat instead of useChat for automatic tracking</li>
                  <li>• Access tool call state with useActiveToolCalls, useIsExecuting</li>
                  <li>• Get metrics with useToolCallMetrics</li>
                  <li>• Create client tools with createClientTools</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Features:</h4>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• Real-time tool call tracking and monitoring</li>
                  <li>• Automatic state management (pending, executing, completed, error)</li>
                  <li>• Performance metrics and analytics</li>
                  <li>• Export/import functionality</li>
                  <li>• Client-side tool execution with tracking</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
