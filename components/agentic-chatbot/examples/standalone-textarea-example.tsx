'use client';

import { useState } from 'react';
import { EnhancedTextarea, ContextItem } from '../enhanced-textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export function StandaloneTextareaExample() {
  const [value, setValue] = useState('');
  const [submittedData, setSubmittedData] = useState<{
    message: string;
    contexts: ContextItem[];
    timestamp: string;
  } | null>(null);

  const handleSubmit = (message: string, contexts: ContextItem[]) => {
    setSubmittedData({
      message,
      contexts,
      timestamp: new Date().toLocaleString(),
    });
    setValue(''); // Clear the input after submission
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Enhanced Textarea Example
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Standalone usage of the enhanced textarea component with context management
        </p>
      </div>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Input</CardTitle>
          <CardDescription>
            Try the enhanced textarea with templates, context attachment, and file upload
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <EnhancedTextarea
              value={value}
              onChange={setValue}
              onSubmit={handleSubmit}
              placeholder="Try typing, using templates (Ctrl+T), or adding context (Ctrl+K)..."
              enableContextSelection={true}
              enableTemplates={true}
              enableFileUpload={true}
              showCharacterCount={true}
              className="h-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* Output Section */}
      {submittedData && (
        <Card>
          <CardHeader>
            <CardTitle>Submitted Data</CardTitle>
            <CardDescription>
              Last submission at {submittedData.timestamp}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Message */}
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Message:</h3>
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <pre className="whitespace-pre-wrap text-sm text-gray-700 dark:text-gray-300">
                  {submittedData.message}
                </pre>
              </div>
            </div>

            {/* Contexts */}
            {submittedData.contexts.length > 0 && (
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Attached Contexts ({submittedData.contexts.length}):
                </h3>
                <div className="space-y-3">
                  {submittedData.contexts.map((context) => (
                    <div
                      key={context.id}
                      className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="secondary">{context.type}</Badge>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {context.title}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        <strong>Content:</strong>
                        <div className="mt-1 p-2 bg-gray-100 dark:bg-gray-900 rounded text-xs max-h-32 overflow-y-auto">
                          <pre className="whitespace-pre-wrap">
                            {context.content.substring(0, 500)}
                            {context.content.length > 500 && '...'}
                          </pre>
                        </div>
                      </div>
                      {context.metadata && (
                        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                          <strong>Metadata:</strong> {JSON.stringify(context.metadata)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Features List */}
      <Card>
        <CardHeader>
          <CardTitle>Features Demonstrated</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Input Features:</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Auto-resizing textarea</li>
                <li>• Character count with limits</li>
                <li>• Keyboard shortcuts (Ctrl+T, Ctrl+K)</li>
                <li>• Enter to submit, Shift+Enter for new line</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Context Features:</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Multiple context types</li>
                <li>• File upload support</li>
                <li>• Quick templates</li>
                <li>• Visual context management</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Use</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <div>
              <strong>Templates:</strong> Press Ctrl+T or click the sparkles icon to access quick templates
            </div>
            <div>
              <strong>Context:</strong> Press Ctrl+K or click the plus icon to add context (files, code, etc.)
            </div>
            <div>
              <strong>Settings:</strong> Click the settings icon to customize the textarea behavior
            </div>
            <div>
              <strong>Submit:</strong> Press Enter to submit or click the send button
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
