'use client';

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export interface ToolCall {
  id: string;
  toolName: string;
  args: Record<string, any>;
  state: 'pending' | 'executing' | 'completed' | 'error' | 'cancelled';
  result?: any;
  error?: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  messageId?: string;
  stepIndex?: number;
}

export interface ToolCallMetrics {
  totalCalls: number;
  completedCalls: number;
  errorCalls: number;
  averageDuration: number;
  toolUsageCount: Record<string, number>;
  recentCalls: ToolCall[];
}

interface ToolCallStore {
  // State
  toolCalls: ToolCall[];
  activeCalls: ToolCall[];
  callHistory: ToolCall[];
  isExecuting: boolean;
  
  // Actions
  addToolCall: (toolCall: Omit<ToolCall, 'id' | 'startTime' | 'state'>) => string;
  updateToolCall: (id: string, updates: Partial<ToolCall>) => void;
  completeToolCall: (id: string, result: any) => void;
  errorToolCall: (id: string, error: string) => void;
  cancelToolCall: (id: string) => void;
  clearToolCalls: () => void;
  clearHistory: () => void;
  
  // Getters
  getToolCall: (id: string) => ToolCall | undefined;
  getToolCallsByMessage: (messageId: string) => ToolCall[];
  getToolCallsByName: (toolName: string) => ToolCall[];
  getActiveToolCalls: () => ToolCall[];
  getMetrics: () => ToolCallMetrics;
  
  // Utilities
  exportToolCalls: () => string;
  importToolCalls: (data: string) => void;
}

export const useToolCallStore = create<ToolCallStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    toolCalls: [],
    activeCalls: [],
    callHistory: [],
    isExecuting: false,

    // Actions
    addToolCall: (toolCall) => {
      const id = `tool-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const newToolCall: ToolCall = {
        ...toolCall,
        id,
        state: 'pending',
        startTime: Date.now(),
      };

      set((state) => ({
        toolCalls: [...state.toolCalls, newToolCall],
        activeCalls: [...state.activeCalls, newToolCall],
        isExecuting: true,
      }));

      return id;
    },

    updateToolCall: (id, updates) => {
      set((state) => {
        const updatedToolCalls = state.toolCalls.map((call) =>
          call.id === id ? { ...call, ...updates } : call
        );
        
        const updatedActiveCalls = state.activeCalls.map((call) =>
          call.id === id ? { ...call, ...updates } : call
        );

        return {
          toolCalls: updatedToolCalls,
          activeCalls: updatedActiveCalls,
        };
      });
    },

    completeToolCall: (id, result) => {
      const endTime = Date.now();
      
      set((state) => {
        const toolCall = state.toolCalls.find((call) => call.id === id);
        if (!toolCall) return state;

        const duration = endTime - toolCall.startTime;
        const completedCall: ToolCall = {
          ...toolCall,
          state: 'completed',
          result,
          endTime,
          duration,
        };

        const updatedToolCalls = state.toolCalls.map((call) =>
          call.id === id ? completedCall : call
        );

        const updatedActiveCalls = state.activeCalls.filter((call) => call.id !== id);
        const updatedHistory = [...state.callHistory, completedCall];

        return {
          toolCalls: updatedToolCalls,
          activeCalls: updatedActiveCalls,
          callHistory: updatedHistory,
          isExecuting: updatedActiveCalls.length > 0,
        };
      });
    },

    errorToolCall: (id, error) => {
      const endTime = Date.now();
      
      set((state) => {
        const toolCall = state.toolCalls.find((call) => call.id === id);
        if (!toolCall) return state;

        const duration = endTime - toolCall.startTime;
        const errorCall: ToolCall = {
          ...toolCall,
          state: 'error',
          error,
          endTime,
          duration,
        };

        const updatedToolCalls = state.toolCalls.map((call) =>
          call.id === id ? errorCall : call
        );

        const updatedActiveCalls = state.activeCalls.filter((call) => call.id !== id);
        const updatedHistory = [...state.callHistory, errorCall];

        return {
          toolCalls: updatedToolCalls,
          activeCalls: updatedActiveCalls,
          callHistory: updatedHistory,
          isExecuting: updatedActiveCalls.length > 0,
        };
      });
    },

    cancelToolCall: (id) => {
      set((state) => {
        const toolCall = state.toolCalls.find((call) => call.id === id);
        if (!toolCall) return state;

        const cancelledCall: ToolCall = {
          ...toolCall,
          state: 'cancelled',
          endTime: Date.now(),
          duration: Date.now() - toolCall.startTime,
        };

        const updatedToolCalls = state.toolCalls.map((call) =>
          call.id === id ? cancelledCall : call
        );

        const updatedActiveCalls = state.activeCalls.filter((call) => call.id !== id);

        return {
          toolCalls: updatedToolCalls,
          activeCalls: updatedActiveCalls,
          isExecuting: updatedActiveCalls.length > 0,
        };
      });
    },

    clearToolCalls: () => {
      set({
        toolCalls: [],
        activeCalls: [],
        isExecuting: false,
      });
    },

    clearHistory: () => {
      set({
        callHistory: [],
      });
    },

    // Getters
    getToolCall: (id) => {
      return get().toolCalls.find((call) => call.id === id);
    },

    getToolCallsByMessage: (messageId) => {
      return get().toolCalls.filter((call) => call.messageId === messageId);
    },

    getToolCallsByName: (toolName) => {
      return get().toolCalls.filter((call) => call.toolName === toolName);
    },

    getActiveToolCalls: () => {
      return get().activeCalls;
    },

    getMetrics: () => {
      const { toolCalls, callHistory } = get();
      const allCalls = [...toolCalls, ...callHistory];
      
      const totalCalls = allCalls.length;
      const completedCalls = allCalls.filter((call) => call.state === 'completed').length;
      const errorCalls = allCalls.filter((call) => call.state === 'error').length;
      
      const completedCallsWithDuration = allCalls.filter(
        (call) => call.state === 'completed' && call.duration
      );
      const averageDuration = completedCallsWithDuration.length > 0
        ? completedCallsWithDuration.reduce((sum, call) => sum + (call.duration || 0), 0) / completedCallsWithDuration.length
        : 0;

      const toolUsageCount = allCalls.reduce((acc, call) => {
        acc[call.toolName] = (acc[call.toolName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const recentCalls = allCalls
        .sort((a, b) => b.startTime - a.startTime)
        .slice(0, 10);

      return {
        totalCalls,
        completedCalls,
        errorCalls,
        averageDuration,
        toolUsageCount,
        recentCalls,
      };
    },

    // Utilities
    exportToolCalls: () => {
      const { toolCalls, callHistory } = get();
      const exportData = {
        toolCalls,
        callHistory,
        exportedAt: new Date().toISOString(),
      };
      return JSON.stringify(exportData, null, 2);
    },

    importToolCalls: (data) => {
      try {
        const importData = JSON.parse(data);
        set({
          toolCalls: importData.toolCalls || [],
          callHistory: importData.callHistory || [],
          activeCalls: [],
          isExecuting: false,
        });
      } catch (error) {
        console.error('Failed to import tool calls:', error);
      }
    },
  }))
);

// Selectors for optimized re-renders
export const useActiveToolCalls = () => useToolCallStore((state) => state.activeCalls);
export const useIsExecuting = () => useToolCallStore((state) => state.isExecuting);

// Stable metrics selector to prevent unnecessary recalculations
const metricsSelector = (state: any) => state.getMetrics();

// Memoized metrics selector to prevent unnecessary recalculations
export const useToolCallMetrics = () => {
  return useToolCallStore(metricsSelector);
};

export const useToolCallsByMessage = (messageId: string) =>
  useToolCallStore((state) => state.toolCalls.filter((call) => call.messageId === messageId));

// Stable operations selector
const operationsSelector = (state: any) => ({
  addToolCall: state.addToolCall,
  updateToolCall: state.updateToolCall,
  completeToolCall: state.completeToolCall,
  errorToolCall: state.errorToolCall,
  cancelToolCall: state.cancelToolCall,
  clearToolCalls: state.clearToolCalls,
  clearHistory: state.clearHistory,
  exportToolCalls: state.exportToolCalls,
  importToolCalls: state.importToolCalls,
});

// Hook for tool call operations
export const useToolCallOperations = () => {
  return useToolCallStore(operationsSelector);
};
