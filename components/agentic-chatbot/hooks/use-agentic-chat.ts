'use client';

import { useChat, UseChatOptions } from '@ai-sdk/react';
import { useCallback } from 'react';
import { useToolCallStore } from '../stores/tool-call-store';

interface AgenticChatOptions extends UseChatOptions {
  enableToolCallTracking?: boolean;
  onToolCallStart?: (toolName: string, args: any) => void;
  onToolCallComplete?: (toolName: string, result: any) => void;
  onToolCallError?: (toolName: string, error: string) => void;
}

export function useAgenticChat(options: AgenticChatOptions = {}) {
  const {
    enableToolCallTracking = true,
    onToolCallStart,
    onToolCallComplete,
    onToolCallError,
    ...chatOptions
  } = options;

  const {
    addToolCall,
    updateToolCall,
    completeToolCall,
    errorToolCall,
    clearToolCalls,
  } = useToolCallStore();

  // Enhanced onToolCall handler that integrates with the store
  const handleToolCall = useCallback(async ({ toolCall }: { toolCall: any }) => {
    if (!enableToolCallTracking) {
      return options.onToolCall?.({ toolCall });
    }

    // Add tool call to store
    const callId = addToolCall({
      toolName: toolCall.toolName,
      args: toolCall.args,
      messageId: toolCall.messageId,
      stepIndex: toolCall.stepIndex,
    });

    // Update state to executing
    updateToolCall(callId, { state: 'executing' });

    // Call custom handler if provided
    onToolCallStart?.(toolCall.toolName, toolCall.args);

    try {
      // Execute the original tool call handler if provided
      let result;
      if (options.onToolCall) {
        result = await options.onToolCall({ toolCall });
      }

      // Complete the tool call in store
      completeToolCall(callId, result);
      onToolCallComplete?.(toolCall.toolName, result);

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Mark as error in store
      errorToolCall(callId, errorMessage);
      onToolCallError?.(toolCall.toolName, errorMessage);

      throw error;
    }
  }, [
    enableToolCallTracking,
    addToolCall,
    updateToolCall,
    completeToolCall,
    errorToolCall,
    onToolCallStart,
    onToolCallComplete,
    onToolCallError,
    options.onToolCall,
  ]);

  // Use the enhanced chat hook
  const chat = useChat({
    ...chatOptions,
    onToolCall: handleToolCall,
    onFinish: (message) => {
      // Call original onFinish if provided
      options.onFinish?.(message);
    },
    onError: (error) => {
      // Call original onError if provided
      options.onError?.(error);
    },
  });

  // Clear tool calls when starting a new conversation
  const clearChat = useCallback(() => {
    if (enableToolCallTracking) {
      clearToolCalls();
    }
    // Note: useChat doesn't provide a direct clear method
    // You might need to implement this based on your needs
    window.location.reload();
  }, [enableToolCallTracking, clearToolCalls]);

  // Enhanced submit that tracks message context
  const enhancedSubmit = useCallback((
    event: React.FormEvent<HTMLFormElement>,
    _options?: {
      body?: Record<string, any>;
      data?: FormData | Record<string, any>;
    }
  ) => {
    // You can add additional tracking here if needed
    return chat.handleSubmit(event);
  }, [chat.handleSubmit]);

  return {
    ...chat,
    clearChat,
    enhancedSubmit,
    // Expose tool call tracking state
    toolCallTracking: {
      enabled: enableToolCallTracking,
      clearCalls: clearToolCalls,
    },
  };
}

// Hook for client-side tool execution with automatic tracking
export function useClientToolExecution() {
  const {
    addToolCall,
    updateToolCall,
    completeToolCall,
    errorToolCall,
  } = useToolCallStore();

  const executeClientTool = useCallback(async (
    toolName: string,
    args: Record<string, any>,
    executor: (args: Record<string, any>) => Promise<any>,
    messageId?: string
  ) => {
    // Add tool call to store
    const callId = addToolCall({
      toolName,
      args,
      messageId,
    });

    // Update state to executing
    updateToolCall(callId, { state: 'executing' });

    try {
      // Execute the tool
      const result = await executor(args);

      // Complete the tool call in store
      completeToolCall(callId, result);

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Mark as error in store
      errorToolCall(callId, errorMessage);

      throw error;
    }
  }, [addToolCall, updateToolCall, completeToolCall, errorToolCall]);

  return {
    executeClientTool,
  };
}

// Predefined client-side tools that can be tracked
export const createClientTools = () => {
  const { executeClientTool } = useClientToolExecution();

  return {
    // File system operations
    readFile: async (args: { path: string }) => {
      return executeClientTool('readFile', args, async ({ path: _path }) => {
        // Implement file reading logic
        throw new Error('File reading not implemented in browser');
      });
    },

    // Code formatting
    formatCode: async (args: { code: string; language: string }) => {
      return executeClientTool('formatCode', args, async ({ code, language }) => {
        // Implement code formatting logic
        // This could use a client-side formatter like Prettier
        return { formattedCode: code, language };
      });
    },

    // Code validation
    validateCode: async (args: { code: string; language: string }) => {
      return executeClientTool('validateCode', args, async ({ code: _code, language: _language }) => {
        // Implement code validation logic
        return {
          isValid: true,
          errors: [],
          warnings: [],
        };
      });
    },

    // Browser operations
    openUrl: async (args: { url: string }) => {
      return executeClientTool('openUrl', args, async ({ url }) => {
        window.open(url, '_blank');
        return { success: true, url };
      });
    },

    // Clipboard operations
    copyToClipboard: async (args: { text: string }) => {
      return executeClientTool('copyToClipboard', args, async ({ text }) => {
        await navigator.clipboard.writeText(text);
        return { success: true, length: text.length };
      });
    },

    // Local storage operations
    saveToLocalStorage: async (args: { key: string; value: any }) => {
      return executeClientTool('saveToLocalStorage', args, async ({ key, value }) => {
        localStorage.setItem(key, JSON.stringify(value));
        return { success: true, key };
      });
    },

    loadFromLocalStorage: async (args: { key: string }) => {
      return executeClientTool('loadFromLocalStorage', args, async ({ key }) => {
        const value = localStorage.getItem(key);
        return {
          success: value !== null,
          value: value ? JSON.parse(value) : null,
          key,
        };
      });
    },
  };
};
