'use client';

import { useState, useRef, useEffect } from 'react';
import { Message } from '@ai-sdk/ui-utils';
import { useAgenticChat } from './hooks/use-agentic-chat';
import { nodeboxToolExecutors } from '@/lib/agents/nodebox-tools';
import { useNodeboxIntegration } from '@/lib/stores/nodebox-store';
import { useNodeboxAIIntegration, integrationService } from '@/lib/nodebox-ai';
import { 
  Bot, 
  User, 
  Loader2, 
  AlertCircle, 
  RefreshCw,
  Settings,
  Download,
  Share,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { EnhancedTextarea, ContextItem } from './enhanced-textarea';
import { ContextManager } from './context-manager';
import { Tool<PERSON>allMonitor } from './tool-call-monitor';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

interface AgenticChatInterfaceProps {
  apiEndpoint?: string;
  systemPrompt?: string;
  maxSteps?: number;
  enableTools?: boolean;
  enableContextManager?: boolean;
  className?: string;
  projectId?: string; // Add projectId for Nodebox integration
}

interface ChatMessage extends Message {
  context?: ContextItem[];
  toolCalls?: any[];
  toolResults?: any[];
}

export function AgenticChatInterface({
  apiEndpoint = '/api/nodebox-ai',
  systemPrompt = 'You are a helpful AI assistant specialized in code generation and development tasks.',
  maxSteps = 5,
  enableTools = true,
  enableContextManager = true,
  className = "",
  projectId,
}: AgenticChatInterfaceProps) {
  const [contexts, setContexts] = useState<ContextItem[]>([]);
  const [activeTab, setActiveTab] = useState('chat');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Nodebox integration - only initialize if projectId is provided
  const nodeboxIntegration = projectId ? useNodeboxIntegration(projectId) : null;

  // AI Integration service for enhanced context and coordination
  const aiIntegration = projectId ? useNodeboxAIIntegration(projectId) : null;

  // Enhanced project context for better AI assistance
  const getProjectContext = () => {
    if (!projectId) return '';

    // Use integration service for enhanced context
    const enhancedContext = aiIntegration?.getEnhancedContext();
    if (enhancedContext) {
      return `
Project Context:
- Project ID: ${enhancedContext.projectId}
- Instance ID: ${enhancedContext.instanceId || 'None'}
- Active Instance: ${enhancedContext.projectContext?.activeInstance || 'None'}
- Template: ${enhancedContext.projectContext?.template || 'Unknown'}
- Status: ${enhancedContext.projectContext?.status || 'Unknown'}
- Integration Status: ${aiIntegration?.isProjectConnected ? 'Connected' : 'Disconnected'}
- Last Sync: ${aiIntegration?.lastSync?.toISOString() || 'Never'}
${aiIntegration?.syncErrors?.length ? `- Sync Errors: ${aiIntegration.syncErrors.join(', ')}` : ''}
`;
    }

    // Fallback to basic context
    if (nodeboxIntegration) {
      const { activeInstance, isLoading, error } = nodeboxIntegration;
      return `
Project Context:
- Project ID: ${projectId}
- Active Instance: ${activeInstance?.config.name || 'None'}
- Instance ID: ${activeInstance?.id || 'None'}
- Template: ${activeInstance?.config.template || 'Unknown'}
- Runtime Status: ${error ? 'Error' : isLoading ? 'Loading' : activeInstance ? 'Connected' : 'Disconnected'}
- Instance Status: ${activeInstance?.status || 'Unknown'}
${error ? `- Error: ${error.message}` : ''}
`;
    }

    return '';
  };

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    stop,
    reload,
    clearChat: clearChatFromHook,
  } = useAgenticChat({
    api: apiEndpoint,
    maxSteps,
    enableToolCallTracking: true,
    onError: (error: Error) => {
      console.error('Chat error:', error);
    },
    onFinish: (message: any) => {
      console.log('Message finished:', message);
    },
    onToolCall: async ({ toolCall }: { toolCall: any }) => {
      try {
        // Handle Nodebox tools if projectId is provided and tool is Nodebox-related
        if (projectId && (toolCall.toolName.endsWith('_nodebox') || toolCall.toolName.includes('nodebox'))) {
          const toolName = toolCall.toolName;
          const args = toolCall.args || {};

          console.log(`Executing Nodebox tool: ${toolName}`, args);

          // Use integration service for enhanced context
          const enhancedContext = aiIntegration?.getEnhancedContext();
          const enhancedArgs = integrationService.enhanceToolExecution({
            projectId: projectId!,
            instanceId: enhancedContext?.instanceId,
            activeInstance: enhancedContext?.activeInstance,
            projectContext: enhancedContext?.projectContext,
            toolName,
            args,
            messageId: toolCall.toolCallId,
            stepIndex: messages.length
          });

          // Route to appropriate Nodebox tool executor for basic tools
          switch (toolName) {
            case 'read_file_nodebox':
              return await nodeboxToolExecutors.readFile(enhancedArgs);

            case 'write_file_nodebox':
              return await nodeboxToolExecutors.writeFile(enhancedArgs);

            case 'create_file_nodebox':
              return await nodeboxToolExecutors.createFile(enhancedArgs);

            case 'create_directory_nodebox':
              return await nodeboxToolExecutors.createDirectory(enhancedArgs);

            case 'delete_file_nodebox':
              return await nodeboxToolExecutors.deleteFile(enhancedArgs);

            case 'list_files_nodebox':
              return await nodeboxToolExecutors.listFiles(enhancedArgs);

            case 'run_command_nodebox':
              return await nodeboxToolExecutors.runCommand(enhancedArgs);

            case 'create_project_nodebox':
              return await nodeboxToolExecutors.createProject(enhancedArgs);

            case 'get_project_info_nodebox':
              return await nodeboxToolExecutors.getProjectInfo(enhancedArgs);

            // Enhanced AI tools and intelligent runtime tools - handled server-side
            case 'analyze_codebase_nodebox':
            case 'generate_contextual_code_nodebox':
            case 'generate_project_template_nodebox':
            case 'optimize_project_structure_nodebox':
            case 'update_file_nodebox':
            case 'append_to_file_nodebox':
            case 'smart_merge_file_nodebox':
            case 'force_write_file_nodebox':
            case 'get_file_history_nodebox':
            case 'restore_from_backup_nodebox':
            case 'compare_files_nodebox':
            case 'create_project_from_template':
            case 'install_packages_intelligent':
            case 'run_dev_server_with_preview':
            case 'get_available_templates':
            case 'manage_shell_lifecycle':
            case 'analyze_task_complexity':
            case 'orchestrate_complex_task':
            case 'evaluate_generated_code':
            case 'scrape_webpage':
            case 'search_web':
            case 'interact_with_webpage':
            case 'take_screenshot':
              // These tools are handled server-side only
              console.log(`Advanced tool ${toolName} will be handled server-side with enhanced context`);
              return null;

            default:
              console.warn(`Unknown Nodebox tool: ${toolName}`);
              return {
                error: `Unknown Nodebox tool: ${toolName}`,
                success: false,
                availableTools: [
                  'Basic: read_file_nodebox, write_file_nodebox, create_file_nodebox, list_files_nodebox',
                  'Runtime: create_project_from_template, install_packages_intelligent, run_dev_server_with_preview',
                  'Web: scrape_webpage, search_web, interact_with_webpage, take_screenshot',
                  'Agentic: analyze_task_complexity, orchestrate_complex_task, evaluate_generated_code'
                ]
              };
          }
        }

        // If no tool matches, return null to let the server handle it
        return null;

      } catch (error) {
        console.error(`Tool call error for ${toolCall.toolName}:`, error);

        // Return structured error response
        return {
          error: error instanceof Error ? error.message : String(error),
          success: false,
          toolName: toolCall.toolName,
          timestamp: new Date().toISOString()
        };
      }
    },
    onToolCallStart: (toolName: string, args: any) => {
      console.log('Tool call started:', toolName, args);
    },
    onToolCallComplete: (toolName: string, result: any) => {
      console.log('Tool call completed:', toolName, result);
    },
    onToolCallError: (toolName: string, error: string) => {
      console.error('Tool call error:', toolName, error);
    },
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleEnhancedSubmit = (message: string, messageContexts: ContextItem[]) => {
    // Prepare the message with context
    let enhancedMessage = message;

    // Add project context if available
    const projectContext = getProjectContext();
    if (projectContext.trim()) {
      enhancedMessage = `${message}\n\n--- Project Context ---\n${projectContext}`;
    }

    // Add user-provided contexts
    if (messageContexts.length > 0) {
      const contextString = messageContexts
        .map(ctx => `[${ctx.type.toUpperCase()}: ${ctx.title}]\n${ctx.content}`)
        .join('\n\n');

      enhancedMessage = `${enhancedMessage}\n\n--- Additional Context ---\n${contextString}`;
    }

    // Create a synthetic event for the useChat hook
    const syntheticEvent = {
      preventDefault: () => {},
      currentTarget: {
        elements: {
          prompt: { value: enhancedMessage }
        }
      }
    } as any;

    // Update input and submit
    handleInputChange({ target: { value: enhancedMessage } } as any);
    handleSubmit(syntheticEvent);
  };



  const handleAddContext = (context: ContextItem) => {
    setContexts(prev => [...prev, context]);
  };

  const handleRemoveContext = (id: string) => {
    setContexts(prev => prev.filter(ctx => ctx.id !== id));
  };

  const handleUpdateContext = (id: string, updates: Partial<ContextItem>) => {
    setContexts(prev => prev.map(ctx => 
      ctx.id === id ? { ...ctx, ...updates } : ctx
    ));
  };

  const clearChat = () => {
    // Use the enhanced clear chat that also clears tool calls
    clearChatFromHook();
  };

  const exportChat = () => {
    const chatData = {
      messages,
      contexts,
      timestamp: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(chatData, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-export-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderMessage = (message: ChatMessage, index: number) => {
    const isUser = message.role === 'user';
    const isSystem = message.role === 'system';

    return (
      <div
        key={message.id || index}
        className={`flex gap-4 p-4 rounded-lg border transition-all duration-200 hover:shadow-sm ${
          isUser
            ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
            : isSystem
            ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
            : 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700'
        }`}
      >
        {/* Avatar */}
        <div className="flex-shrink-0">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center shadow-sm ${
            isUser
              ? 'bg-gradient-to-br from-blue-500 to-blue-600'
              : isSystem
              ? 'bg-gradient-to-br from-yellow-500 to-orange-500'
              : 'bg-gradient-to-br from-purple-500 to-blue-600'
          }`}>
            {isUser ? (
              <User size={16} className="text-white" />
            ) : isSystem ? (
              <Settings size={16} className="text-white" />
            ) : (
              <Bot size={16} className="text-white" />
            )}
          </div>
        </div>

        {/* Message Content */}
        <div className="flex-1 min-w-0 space-y-2">
          {/* Header */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
              {isUser ? 'You' : isSystem ? 'System' : 'Assistant'}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
              {new Date().toLocaleTimeString()}
            </span>
          </div>

          {/* Message Body */}
          <div className="prose prose-sm max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-gray-100 prose-p:text-gray-700 dark:prose-p:text-gray-300">
            {message.content && (
              <div className="whitespace-pre-wrap text-gray-900 dark:text-gray-100 leading-relaxed">
                {message.content}
              </div>
            )}
          </div>

          {/* Tool Calls - Enhanced display with better flexbox layout */}
          {message.parts && message.parts.some(part => part.type === 'tool-invocation') && (
            <div className="mt-4 space-y-3">
              {message.parts
                .filter(part => part.type === 'tool-invocation')
                .map((part, toolIndex) => {
                  if (part.type !== 'tool-invocation') return null;
                  const tool = part.toolInvocation;

                  return (
                    <div
                      key={toolIndex}
                      className="p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm"
                    >
                      {/* Tool Header */}
                      <div className="flex flex-wrap items-center gap-2 mb-3">
                        <Badge
                          variant="outline"
                          className={`text-xs font-medium ${
                            tool.toolName.endsWith('_nodebox')
                              ? 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800'
                              : 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-700'
                          }`}
                        >
                          {tool.toolName.endsWith('_nodebox') ? '📦 ' : '🔧 '}
                          {tool.toolName.replace('_nodebox', '').replace('_', ' ')}
                        </Badge>
                        <Badge
                          variant={tool.state === 'result' ? 'default' : 'secondary'}
                          className={`text-xs ${
                            tool.state === 'result'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
                          }`}
                        >
                          {tool.state === 'result' ? '✅ Complete' : '⏳ Running'}
                        </Badge>
                        {tool.toolName.endsWith('_nodebox') && (
                          <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300">
                            Nodebox
                          </Badge>
                        )}
                      </div>

                      {/* Tool Arguments */}
                      {tool.args && (
                        <div className="mb-3">
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            📋 Arguments:
                          </div>
                          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3 border">
                            <pre className="text-xs text-gray-700 dark:text-gray-300 overflow-x-auto whitespace-pre-wrap">
                              {JSON.stringify(tool.args, null, 2)}
                            </pre>
                          </div>
                        </div>
                      )}

                      {/* Tool Results */}
                      {tool.state === 'result' && 'result' in tool && (
                        <div>
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            📊 Result:
                          </div>
                          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3 border">
                            {/* Enhanced display for Nodebox tool results */}
                            {tool.toolName.endsWith('_nodebox') && tool.result && typeof tool.result === 'object' ? (
                              <div className="space-y-2">
                                {tool.result.success !== undefined && (
                                  <div className={`font-medium ${tool.result.success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                    {tool.result.success ? '✅ Success' : '❌ Failed'}
                                  </div>
                                )}

                                {/* Enhanced tool-specific displays */}
                                {tool.toolName === 'analyze_codebase_nodebox' && tool.result.projectType && (
                                  <div className="space-y-1">
                                    <div><strong>🏗️ Project Type:</strong> {tool.result.projectType}</div>
                                    <div><strong>⚡ Framework:</strong> {tool.result.framework}</div>
                                    <div><strong>📁 Total Files:</strong> {tool.result.totalFiles}</div>
                                    {tool.result.patterns && (
                                      <div>
                                        <strong>🎨 Patterns:</strong>
                                        <div className="ml-4 text-xs space-y-1">
                                          <div>Components: {tool.result.patterns.componentStructure}</div>
                                          <div>Styling: {tool.result.patterns.styling}</div>
                                          <div>State: {tool.result.patterns.stateManagement}</div>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                )}

                                {tool.toolName === 'generate_contextual_code_nodebox' && (
                                  <div className="space-y-1">
                                    <div><strong>🎯 Generated:</strong> {tool.result.name} ({tool.result.type})</div>
                                    <div><strong>📍 Path:</strong> <code className="text-xs bg-gray-200 dark:bg-gray-800 px-1 rounded">{tool.result.generatedPath}</code></div>
                                    <div><strong>📏 Size:</strong> {tool.result.codeLength} characters</div>
                                    {tool.result.pathChanged && (
                                      <div className="text-yellow-600 dark:text-yellow-400">
                                        <strong>⚠️ Path Modified:</strong> Conflict resolved by renaming
                                      </div>
                                    )}
                                    {tool.result.features && tool.result.features.length > 0 && (
                                      <div><strong>✨ Features:</strong> {tool.result.features.join(', ')}</div>
                                    )}
                                  </div>
                                )}

                                {tool.toolName === 'generate_project_template_nodebox' && (
                                  <div className="space-y-1">
                                    <div><strong>📦 Template:</strong> {tool.result.templateName}</div>
                                    <div><strong>📁 Files Created:</strong> {tool.result.successfulFiles}/{tool.result.totalFiles}</div>
                                    {tool.result.errors && tool.result.errors.length > 0 && (
                                      <div className="text-red-600 dark:text-red-400">
                                        <strong>❌ Errors:</strong> {tool.result.errors.length} files failed
                                      </div>
                                    )}
                                    {tool.result.dependencies && Object.keys(tool.result.dependencies).length > 0 && (
                                      <div><strong>📚 Dependencies:</strong> {Object.keys(tool.result.dependencies).length} packages</div>
                                    )}
                                  </div>
                                )}

                                {tool.toolName === 'optimize_project_structure_nodebox' && (
                                  <div className="space-y-1">
                                    <div><strong>📊 Overall Score:</strong> {tool.result.overallScore}/100</div>
                                    {tool.result.analysis && (
                                      <div className="space-y-1">
                                        {Object.entries(tool.result.analysis).map(([key, value]: [string, any]) => (
                                          <div key={key} className="text-xs">
                                            <strong>{key.charAt(0).toUpperCase() + key.slice(1)}:</strong> {value.score}/100
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                )}

                                {/* Standard file operation displays */}
                                {tool.result.path && !['analyze_codebase_nodebox', 'generate_contextual_code_nodebox', 'generate_project_template_nodebox', 'optimize_project_structure_nodebox'].includes(tool.toolName) && (
                                  <div><strong>📍 Path:</strong> {tool.result.path}</div>
                                )}

                                {tool.result.content && tool.toolName === 'read_file_nodebox' && (
                                  <div>
                                    <strong>📄 Content:</strong>
                                    <pre className="mt-1 text-xs bg-gray-200 dark:bg-gray-800 p-2 rounded overflow-x-auto max-h-32">
                                      {tool.result.content.substring(0, 500)}{tool.result.content.length > 500 ? '...' : ''}
                                    </pre>
                                  </div>
                                )}

                                {tool.result.bytesWritten && (
                                  <div><strong>💾 Bytes Written:</strong> {tool.result.bytesWritten}</div>
                                )}

                                {tool.result.editType && (
                                  <div><strong>✏️ Edit Type:</strong> {tool.result.editType}</div>
                                )}

                                {tool.result.backedUp && (
                                  <div className="text-blue-600 dark:text-blue-400">
                                    <strong>🛡️ Backup Created:</strong> {tool.result.backupPath}
                                  </div>
                                )}

                                {tool.result.merged && (
                                  <div className="text-green-600 dark:text-green-400">
                                    <strong>🔀 Content Merged:</strong> Intelligently combined
                                  </div>
                                )}

                                {tool.result.files && Array.isArray(tool.result.files) && (
                                  <div>
                                    <strong>📁 Files ({tool.result.files.length}):</strong>
                                    <ul className="mt-1 space-y-1">
                                      {tool.result.files.slice(0, 5).map((file: any, idx: number) => (
                                        <li key={idx} className="text-xs">
                                          {file.type === 'directory' ? '📁' : '📄'} {file.name}
                                        </li>
                                      ))}
                                      {tool.result.files.length > 5 && (
                                        <li className="text-xs text-gray-500">... and {tool.result.files.length - 5} more</li>
                                      )}
                                    </ul>
                                  </div>
                                )}

                                {tool.result.command && (
                                  <div><strong>⚡ Command:</strong> {tool.result.command} {tool.result.args?.join(' ')}</div>
                                )}

                                {tool.result.processId && (
                                  <div><strong>🔧 Process ID:</strong> {tool.result.processId}</div>
                                )}

                                {tool.result.instanceId && (
                                  <div><strong>🏃 Instance:</strong> {tool.result.instanceId}</div>
                                )}

                                {tool.result.template && (
                                  <div><strong>📋 Template:</strong> {tool.result.template}</div>
                                )}

                                {tool.result.message && (
                                  <div className="text-blue-600 dark:text-blue-400">
                                    <strong>💬 Message:</strong> {tool.result.message}
                                  </div>
                                )}

                                {tool.result.error && (
                                  <div className="text-red-600 dark:text-red-400">
                                    <strong>❌ Error:</strong> {tool.result.error}
                                  </div>
                                )}
                              </div>
                            ) : (
                              // Default display for non-Nodebox tools or simple results
                              typeof tool.result === 'string' ? tool.result : JSON.stringify(tool.result, null, 2)
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`flex flex-col h-full max-h-full bg-white dark:bg-gray-900 ${className}`}>
      {/* Tab Navigation - Fixed at top */}
      <div className="flex-shrink-0 border-b border-gray-200 dark:border-gray-700">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={`grid w-full ${enableContextManager ? 'grid-cols-3' : 'grid-cols-2'} bg-gray-50 dark:bg-gray-800`}>
            <TabsTrigger value="chat" className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-900">
              Chat
            </TabsTrigger>
            {enableContextManager && (
              <TabsTrigger value="context" className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-900">
                Context ({contexts.length})
              </TabsTrigger>
            )}
            <TabsTrigger value="tools" className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-900">
              Tools
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Tab Content Area - Takes remaining space */}
      <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col min-h-0">
          {/* Chat Tab Content */}
          <TabsContent value="chat" className="flex-1 flex flex-col min-h-0 mt-0 data-[state=inactive]:hidden">
            {/* Messages Container - Flexible, scrollable area with max height */}
            <div className="flex-1 min-h-0 overflow-hidden">
              <ScrollArea className="h-full">
                <div className="p-4 space-y-4">
                  {messages.length === 0 ? (
                    <div className="flex items-center justify-center min-h-[400px] text-gray-500 dark:text-gray-400">
                      <div className="text-center max-w-md">
                        <Bot size={64} className="mx-auto mb-6 opacity-50" />
                        <h3 className="text-xl font-semibold mb-3">Ready to help you code!</h3>
                        <p className="text-sm leading-relaxed">
                          Ask me to generate components, fix bugs, explain code, or help with any development task.
                        </p>
                        <div className="mt-6 flex flex-wrap gap-2 justify-center">
                          <span className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-xs">
                            File Operations
                          </span>
                          <span className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-xs">
                            Code Generation
                          </span>
                          <span className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-xs">
                            Project Setup
                          </span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <>
                      {messages.map((message, index) => renderMessage(message as ChatMessage, index))}

                      {/* Loading indicator */}
                      {isLoading && (
                        <div className="flex gap-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <Bot size={16} className="text-white" />
                          </div>
                          <div className="flex items-center gap-2">
                            <Loader2 size={16} className="animate-spin text-blue-600" />
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              Assistant is thinking...
                            </span>
                          </div>
                        </div>
                      )}
                    </>
                  )}

                  {/* Scroll anchor */}
                  <div ref={messagesEndRef} className="h-1" />
                </div>
              </ScrollArea>
            </div>

            {/* Error Display - Conditional, fixed height when present */}
            {error && (
              <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700">
                <Alert className="border-red-200 bg-red-50 dark:bg-red-900/20">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800 dark:text-red-200">
                    <div className="flex items-center justify-between">
                      <span>{error.message}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => reload()}
                        className="ml-2 h-8"
                      >
                        <RefreshCw size={14} className="mr-1" />
                        Retry
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {/* Input Area - Fixed at bottom, always visible */}
            <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
              <div className="p-4">
                <EnhancedTextarea
                  value={input}
                  onChange={(value) => handleInputChange({ target: { value } } as any)}
                  onSubmit={handleEnhancedSubmit}
                  isLoading={isLoading}
                  onStop={stop}
                  placeholder="Describe what you want to build, fix, or understand..."
                  className="border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </TabsContent>

          {/* Context Tab Content */}
          {enableContextManager && (
            <TabsContent value="context" className="flex-1 flex flex-col min-h-0 mt-0 data-[state=inactive]:hidden">
              <div className="flex-1 min-h-0 overflow-hidden">
                <ContextManager
                  contexts={contexts}
                  onAddContext={handleAddContext}
                  onRemoveContext={handleRemoveContext}
                  onUpdateContext={handleUpdateContext}
                  className="h-full border-0"
                />
              </div>
            </TabsContent>
          )}

          {/* Tools Tab Content */}
          <TabsContent value="tools" className="flex-1 flex flex-col min-h-0 mt-0 data-[state=inactive]:hidden">
            <div className="flex-1 min-h-0 overflow-hidden p-4">
              <ToolCallMonitor
                className="h-full"
                showMetrics={true}
                showHistory={true}
                compact={false}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
