'use client';

import { useState, useCallback } from 'react';
import { 
  Search, 
  Filter, 
  X, 
  FileText, 
  Code, 
  Database, 
  Globe, 
  Zap, 
  Terminal,
  Plus,
  Trash2,
  Edit3,
  <PERSON><PERSON>,
  Eye
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ContextItem } from './enhanced-textarea';
import { ScrollArea } from '../ui/scroll-area';

interface ContextManagerProps {
  contexts: ContextItem[];
  onAddContext: (context: ContextItem) => void;
  onRemoveContext: (id: string) => void;
  onUpdateContext: (id: string, updates: Partial<ContextItem>) => void;
  className?: string;
}

const CONTEXT_ICONS = {
  file: FileText,
  code: Code,
  documentation: Globe,
  api: Zap,
  database: Database,
  custom: Terminal,
};

const CONTEXT_COLORS = {
  file: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  code: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  documentation: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  api: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
  database: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
  custom: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
};

export function ContextManager({
  contexts,
  onAddContext,
  onRemoveContext,
  onUpdateContext,
  className = "",
}: ContextManagerProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [selectedContext, setSelectedContext] = useState<ContextItem | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  // Filter contexts based on search and type
  const filteredContexts = contexts.filter(context => {
    const matchesSearch = context.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         context.content.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = filterType === 'all' || context.type === filterType;
    return matchesSearch && matchesType;
  });

  const handleCreateContext = (formData: FormData) => {
    const title = formData.get('title') as string;
    const content = formData.get('content') as string;
    const type = formData.get('type') as ContextItem['type'];
    
    if (!title || !content || !type) return;

    const newContext: ContextItem = {
      id: `context-${Date.now()}`,
      type,
      title,
      content,
      metadata: {
        createdAt: new Date().toISOString(),
        wordCount: content.split(/\s+/).length,
        charCount: content.length,
      }
    };

    onAddContext(newContext);
    setIsCreateDialogOpen(false);
  };

  const handleDuplicateContext = (context: ContextItem) => {
    const duplicated: ContextItem = {
      ...context,
      id: `context-${Date.now()}`,
      title: `${context.title} (Copy)`,
      metadata: {
        ...context.metadata,
        createdAt: new Date().toISOString(),
      }
    };
    onAddContext(duplicated);
  };

  const getContextStats = () => {
    const totalContexts = contexts.length;
    const totalChars = contexts.reduce((sum, ctx) => sum + ctx.content.length, 0);
    const typeDistribution = contexts.reduce((acc, ctx) => {
      acc[ctx.type] = (acc[ctx.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return { totalContexts, totalChars, typeDistribution };
  };

  const stats = getContextStats();

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
      {/* Header - Fixed height */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Context Manager
          </h2>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="flex items-center gap-2">
                <Plus size={16} />
                Add Context
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Context</DialogTitle>
              </DialogHeader>
              <form action={handleCreateContext} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      name="title"
                      placeholder="Context title..."
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="type">Type</Label>
                    <Select name="type" required>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="file">File</SelectItem>
                        <SelectItem value="code">Code</SelectItem>
                        <SelectItem value="documentation">Documentation</SelectItem>
                        <SelectItem value="api">API</SelectItem>
                        <SelectItem value="database">Database</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    name="content"
                    placeholder="Enter context content..."
                    rows={8}
                    required
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Create Context</Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search contexts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-32">
              <Filter size={16} />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="file">Files</SelectItem>
              <SelectItem value="code">Code</SelectItem>
              <SelectItem value="documentation">Docs</SelectItem>
              <SelectItem value="api">API</SelectItem>
              <SelectItem value="database">Database</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Stats - Fixed height */}
      <div className="flex-shrink-0 p-4 bg-gray-50 dark:bg-gray-800/50">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {stats.totalContexts}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Total Contexts</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {Math.round(stats.totalChars / 1000)}K
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Characters</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {Object.keys(stats.typeDistribution).length}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Types Used</div>
          </div>
        </div>
      </div>

      {/* Context List - Scrollable */}
      <div className="flex-1 min-h-0 relative">
        <ScrollArea className="absolute inset-0">
          <div className="p-4">
        {filteredContexts.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {searchQuery || filterType !== 'all' ? 'No contexts match your filters' : 'No contexts added yet'}
          </div>
        ) : (
          <div className="space-y-3">
            {filteredContexts.map((context) => {
              const Icon = CONTEXT_ICONS[context.type];
              const colorClass = CONTEXT_COLORS[context.type];
              
              return (
                <div
                  key={context.id}
                  className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                >
                  <div className={`p-2 rounded-md ${colorClass}`}>
                    <Icon size={16} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                        {context.title}
                      </h3>
                      <Badge variant="secondary" className="text-xs">
                        {context.type}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                      {context.content.substring(0, 100)}...
                    </p>
                    <div className="flex items-center gap-4 mt-1 text-xs text-gray-400">
                      <span>{context.content.length} chars</span>
                      {context.metadata?.createdAt && (
                        <span>
                          {new Date(context.metadata.createdAt).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => {
                        setSelectedContext(context);
                        setIsViewDialogOpen(true);
                      }}
                    >
                      <Eye size={14} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => handleDuplicateContext(context)}
                    >
                      <Copy size={14} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                      onClick={() => onRemoveContext(context.id)}
                    >
                      <Trash2 size={14} />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
          </div>
        </ScrollArea>
      </div>

      {/* View Context Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedContext && (
                <>
                  {(() => {
                    const Icon = CONTEXT_ICONS[selectedContext.type];
                    return <Icon size={20} />;
                  })()}
                  {selectedContext.title}
                  <Badge variant="secondary">{selectedContext.type}</Badge>
                </>
              )}
            </DialogTitle>
          </DialogHeader>
          {selectedContext && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Characters:</span>
                  <span className="ml-2 font-medium">{selectedContext.content.length}</span>
                </div>
                <div>
                  <span className="text-gray-500">Words:</span>
                  <span className="ml-2 font-medium">
                    {selectedContext.content.split(/\s+/).length}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Type:</span>
                  <span className="ml-2 font-medium capitalize">{selectedContext.type}</span>
                </div>
              </div>
              <div>
                <Label>Content</Label>
                <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg max-h-96 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-sm text-gray-900 dark:text-gray-100">
                    {selectedContext.content}
                  </pre>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
