// Export all agentic chatbot components
export { EnhancedTextarea } from './enhanced-textarea';
export { ContextManager } from './context-manager';
export { AgenticChatInterface } from './agentic-chat-interface';
export { ToolCallMonitor } from './tool-call-monitor';

// Export hooks
export { useAgenticChat, useClientToolExecution, createClientTools } from './hooks/use-agentic-chat';

// Export stores
export {
  useToolCallStore,
  useActiveToolCalls,
  useIsExecuting,
  useToolCallMetrics,
  useToolCallsByMessage,
  useToolCallOperations
} from './stores/tool-call-store';

// Export types
export type { ContextItem, EnhancedTextareaProps } from './enhanced-textarea';
export type { ToolCall, ToolCallMetrics } from './stores/tool-call-store';

// Re-export commonly used types from AI SDK
export type { Message } from '@ai-sdk/ui-utils';
