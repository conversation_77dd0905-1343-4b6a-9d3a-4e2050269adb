# Agentic Chatbot Components

A comprehensive set of modular React components for building AI-powered code generation chatbots using the Vercel AI SDK.

## Overview

This component library provides everything you need to create sophisticated AI chatbots with advanced features like context management, tool integration, and enhanced user interfaces. Built with modern React patterns, TypeScript, and Tailwind CSS.

## Components

### 1. Tool Call Store

A comprehensive Zustand-based store for tracking and managing client-side tool calls that the AI is making.

**Features:**
- Real-time tool call tracking with state management
- Performance metrics and analytics
- Export/import functionality
- Client-side tool execution with automatic tracking
- Integration with AI SDK's useChat hook

**Usage:**
```tsx
import {
  useToolCallStore,
  useActiveToolCalls,
  useIsExecuting,
  useToolCallMetrics,
  useAgenticChat
} from '@/components/agentic-chatbot';

function MyComponent() {
  // Use enhanced chat hook with automatic tool call tracking
  const chat = useAgenticChat({
    api: '/api/chat',
    enableToolCallTracking: true,
    onToolCallStart: (toolName, args) => console.log('Tool started:', toolName),
    onToolCallComplete: (toolName, result) => console.log('Tool completed:', toolName),
  });

  // Access tool call state
  const activeCalls = useActiveToolCalls();
  const isExecuting = useIsExecuting();
  const metrics = useToolCallMetrics();

  return (
    <div>
      <p>Active calls: {activeCalls.length}</p>
      <p>Is executing: {isExecuting ? 'Yes' : 'No'}</p>
      <p>Total calls: {metrics.totalCalls}</p>
    </div>
  );
}
```

### 2. ToolCallMonitor

A visual component for monitoring tool calls in real-time with metrics and history.

**Features:**
- Real-time visualization of active tool calls
- Performance metrics dashboard
- Tool call history with filtering
- Export functionality
- Compact and full-screen modes

**Usage:**
```tsx
import { ToolCallMonitor } from '@/components/agentic-chatbot';

function MyComponent() {
  return (
    <ToolCallMonitor
      showMetrics={true}
      showHistory={true}
      compact={false}
      className="h-96"
    />
  );
}
```

### 3. EnhancedTextarea

A powerful textarea component with inline buttons, context selection, and advanced features.

**Features:**
- Auto-resizing based on content
- Quick template insertion
- Context attachment system
- Keyboard shortcuts (Ctrl+T for templates, Ctrl+K for context)
- Character count with visual indicators
- File upload support
- Customizable settings

**Usage:**
```tsx
import { EnhancedTextarea } from '@/components/agentic-chatbot';

function MyComponent() {
  const [value, setValue] = useState('');
  
  const handleSubmit = (message: string, contexts: ContextItem[]) => {
    // Handle message submission with attached contexts
  };

  return (
    <EnhancedTextarea
      value={value}
      onChange={setValue}
      onSubmit={handleSubmit}
      placeholder="Describe what you want to build..."
      enableContextSelection={true}
      enableTemplates={true}
      enableFileUpload={true}
    />
  );
}
```

### 2. ContextManager

A comprehensive context management system for organizing and managing AI conversation context.

**Features:**
- Multiple context types (file, code, documentation, API, database, custom)
- Search and filter functionality
- Context creation, editing, and deletion
- Import/export capabilities
- Visual organization with icons and colors
- Statistics and analytics

**Usage:**
```tsx
import { ContextManager } from '@/components/agentic-chatbot';

function MyComponent() {
  const [contexts, setContexts] = useState<ContextItem[]>([]);

  return (
    <ContextManager
      contexts={contexts}
      onAddContext={(context) => setContexts(prev => [...prev, context])}
      onRemoveContext={(id) => setContexts(prev => prev.filter(c => c.id !== id))}
      onUpdateContext={(id, updates) => setContexts(prev => 
        prev.map(c => c.id === id ? { ...c, ...updates } : c)
      )}
    />
  );
}
```

### 3. AgenticChatInterface

The main chat interface that combines all components with AI SDK integration.

**Features:**
- Full AI SDK integration with streaming
- Tool call support and visualization
- Context-aware conversations
- Message export/import
- Error handling and retry mechanisms
- Tabbed interface for chat and context management

**Usage:**
```tsx
import { AgenticChatInterface } from '@/components/agentic-chatbot';

function MyApp() {
  return (
    <AgenticChatInterface
      apiEndpoint="/api/agentic-chat"
      systemPrompt="You are a helpful coding assistant..."
      maxSteps={5}
      enableTools={true}
      enableContextManager={true}
    />
  );
}
```

## API Integration

### Setting up the API Route

Create an API route that supports the agentic chatbot:

```typescript
// app/api/agentic-chat/route.ts
import { openai } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';

export async function POST(req: Request) {
  const { messages, systemPrompt, enableTools } = await req.json();

  const result = streamText({
    model: openai('gpt-4o'),
    system: systemPrompt,
    messages,
    tools: enableTools ? {
      // Define your tools here
      generateComponent: tool({
        description: 'Generate a React component',
        parameters: z.object({
          componentName: z.string(),
          // ... other parameters
        }),
        execute: async (params) => {
          // Tool implementation
        },
      }),
    } : {},
    maxSteps: 5,
  });

  return result.toDataStreamResponse();
}
```

## Context Types

The system supports multiple context types:

- **File**: Text files, code files, documentation
- **Code**: Code snippets, functions, components
- **Documentation**: API docs, guides, references
- **API**: API specifications, endpoints, schemas
- **Database**: Schema definitions, queries, models
- **Custom**: Any other type of context

## Tool Integration

The chatbot supports various development tools:

### Code Generation Tools
- `generateComponent`: Create React components
- `generateTests`: Create unit/integration tests
- `generateAPI`: Create API endpoints

### Analysis Tools
- `analyzeCode`: Code quality and security analysis
- `explainCode`: Detailed code explanations
- `refactorCode`: Code improvement suggestions

### Utility Tools
- `formatCode`: Code formatting and linting
- `generateDocs`: Documentation generation
- `optimizePerformance`: Performance optimization

## Keyboard Shortcuts

- `Enter`: Send message
- `Shift + Enter`: New line
- `Ctrl/Cmd + T`: Open templates
- `Ctrl/Cmd + K`: Open context menu
- `Escape`: Close dropdowns

## Customization

### Styling

All components use Tailwind CSS classes and support dark mode. You can customize the appearance by:

1. Overriding CSS classes
2. Using the `className` prop
3. Modifying the Tailwind configuration

### Templates

Customize quick templates by modifying the `QUICK_TEMPLATES` array:

```typescript
const QUICK_TEMPLATES = [
  { label: 'Custom Template', template: 'Your template text...' },
  // Add more templates
];
```

### Context Types

Add custom context types by extending the `CONTEXT_TYPES` array:

```typescript
const CONTEXT_TYPES = [
  { type: 'custom', icon: YourIcon, label: 'Custom', color: 'bg-custom-color' },
  // Add more types
];
```

## Best Practices

1. **Context Management**: Keep contexts focused and relevant
2. **Tool Usage**: Use appropriate tools for specific tasks
3. **Error Handling**: Implement proper error boundaries
4. **Performance**: Use React.memo for expensive components
5. **Accessibility**: Ensure keyboard navigation and screen reader support

## Dependencies

- React 18+
- @ai-sdk/react
- @ai-sdk/openai (or other providers)
- Tailwind CSS
- Lucide React (for icons)
- Radix UI components

## Examples

See the demo page at `/agentic-chatbot-demo` for live examples and usage patterns.

## Contributing

1. Follow the existing code style
2. Add TypeScript types for all props
3. Include proper documentation
4. Test with different screen sizes
5. Ensure accessibility compliance

## License

MIT License - see LICENSE file for details.
