'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp, Code, Loader2, CheckCircle, XCircle, Terminal, FileText, FolderTree } from 'lucide-react';
import { ExecuteCommandTool, ReadFileTool, WriteFileTool, ListFilesTool } from './tools/container-tools';

// Define the structure of a tool call based on the AI SDK
interface ToolCallProps {
  id: string;
  function: {
    name: string;
    arguments: string;
  };
}

interface ContainerToolCallRendererProps {
  toolCall: ToolCallProps;
  onResult: (toolCallId: string, result: unknown) => void;
}

export function ContainerToolCallRenderer({ toolCall, onResult }: ContainerToolCallRendererProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [result, setResult] = useState<string | null>(null);

  const toggleExpand = () => setIsExpanded(!isExpanded);

  // Parse the function arguments safely
  const parseArguments = () => {
    try {
      const args = toolCall.function?.arguments;
      if (!args) return {};
      return typeof args === 'string' ? JSON.parse(args) : args;
    } catch (e) {
      console.error('Failed to parse tool arguments:', e);
      return { error: 'Failed to parse arguments' };
    }
  };

  const args = parseArguments();

  // Handle tool result
  const handleToolResult = (result: string) => {
    setResult(result);
    onResult(toolCall.id, result);
  };

  // Render the appropriate tool component based on the function name
  const renderToolComponent = () => {
    const functionName = toolCall.function?.name;

    switch (functionName) {
      case 'executeCommand':
        return (
          <ExecuteCommandTool
            command={args.command || ''}
            onResult={handleToolResult}
          />
        );
      case 'readFile':
        return (
          <ReadFileTool
            path={args.path || ''}
            onResult={handleToolResult}
          />
        );
      case 'writeFile':
        return (
          <WriteFileTool
            path={args.path || ''}
            content={args.content || ''}
            onResult={handleToolResult}
          />
        );
      case 'listFiles':
        return (
          <ListFilesTool
            path={args.path || '/'}
            onResult={handleToolResult}
          />
        );
      default:
        return (
          <div className="p-3 text-sm border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
            <div className="mb-3">
              <h4 className="font-medium text-xs text-gray-500 dark:text-gray-400 uppercase mb-1">Arguments</h4>
              <pre className="text-xs p-2 bg-gray-50 dark:bg-gray-800 rounded overflow-auto max-h-40">
                {JSON.stringify(args, null, 2)}
              </pre>
            </div>
            <div className="text-yellow-500 text-sm">
              No specific renderer available for this tool type.
            </div>
          </div>
        );
    }
  };

  // Get the icon for the tool
  const getToolIcon = () => {
    const functionName = toolCall.function?.name;

    switch (functionName) {
      case 'executeCommand':
        return <Terminal className="h-4 w-4 text-blue-500" />;
      case 'readFile':
        return <FileText className="h-4 w-4 text-green-500" />;
      case 'writeFile':
        return <FileText className="h-4 w-4 text-orange-500" />;
      case 'listFiles':
        return <FolderTree className="h-4 w-4 text-purple-500" />;
      default:
        return <Code className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <div className="border rounded-lg overflow-hidden my-2 shadow-sm hover:shadow transition-shadow duration-200">
      <div 
        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer"
        onClick={toggleExpand}
      >
        <div className="flex items-center space-x-2">
          {getToolIcon()}
          <span className="font-mono text-sm font-medium">
            {toolCall.function?.name || 'Tool Call'}
          </span>
          {result && (
            <span className="ml-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
            </span>
          )}
        </div>
        <div>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </div>
      </div>
      
      {isExpanded && renderToolComponent()}
    </div>
  );
}
