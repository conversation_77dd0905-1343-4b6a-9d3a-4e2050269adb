"use client";

import React, { useState, useRef, useEffect } from "react";
import { Message } from "ai";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { BotMessageUi } from "@/components/chat/message";
import { UserMessageUi } from "@/components/chat/message";
import { ChatInput } from "@/components/chat/chat-input";
import { Sparkles, RefreshCw } from "lucide-react";
import { createMicroVmAgentStream } from "@/lib/agents/microvm-agent";
import { useToast } from "@/components/ui/use-toast";

interface MicroVmDevBotProps {
  vmId?: string;
  className?: string;
}

export function MicroVmDevBot({ vmId, className }: MicroVmDevBotProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  // Handle adding a tool result for client-side tools
  const handleToolResult = async (toolCallId: string, result: string) => {
    if (!vmId || !toolCallId) return;
    
    // Add the tool result to the last assistant message
    setMessages((prev) => {
      const lastMessage = prev[prev.length - 1];
      if (lastMessage.role !== "assistant" || !lastMessage.parts) {
        return prev;
      }
      
      // Find the tool invocation part and update its state and result
      const updatedParts = lastMessage.parts.map(part => {
        if (
          part.type === "tool-invocation" && 
          part.toolInvocation.toolCallId === toolCallId &&
          part.toolInvocation.state === "call"
        ) {
          return {
            ...part,
            toolInvocation: {
              ...part.toolInvocation,
              state: "result",
              result
            }
          };
        }
        return part;
      });
      
      return prev.map((msg, i) => 
        i === prev.length - 1 ? { ...msg, parts: updatedParts } : msg
      );
    });
    
    // Continue the conversation with updated tool results
    await continueConversationWithToolResults();
  };

  // Continue conversation after tool results are added
  const continueConversationWithToolResults = async () => {
    if (!vmId || isLoading) return;
    
    setIsLoading(true);
    
    try {
      // Get messages with tool results for the agent
      const agentMessages = messages.map(msg => {
        if (!msg.parts) {
          return { role: msg.role, content: msg.content };
        }
        
        // Extract text content and tool results
        const textContent = msg.parts
          .filter(part => part.type === "text")
          .map(part => part.text)
          .join("");
        
        const toolResults = msg.parts
          .filter(part => 
            part.type === "tool-invocation" && 
            part.toolInvocation.state === "result"
          )
          .map(part => ({
            tool_call_id: part.toolInvocation.toolCallId,
            name: part.toolInvocation.toolName,
            content: typeof part.toolInvocation.result === 'string' 
              ? part.toolInvocation.result 
              : JSON.stringify(part.toolInvocation.result)
          }));
        
        return { 
          role: msg.role, 
          content: textContent,
          tool_results: toolResults.length > 0 ? toolResults : undefined
        };
      });
      
      // Create a placeholder for the new assistant response
      const assistantMessageId = Date.now().toString();
      setMessages((prev) => [
        ...prev,
        {
          id: assistantMessageId,
          role: "assistant",
          content: "",
          parts: [{ type: "text", text: "" }]
        },
      ]);
      
      // Get the response stream with updated messages
      const stream = await createMicroVmAgentStream(vmId, agentMessages);
      
      // Process the stream
      await processStream(stream, assistantMessageId);
    } catch (error) {
      handleStreamError(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Process the AI stream and update message parts
  const processStream = async (stream: AsyncIterable<any>, messageId: string) => {
    for await (const chunk of stream) {
      setMessages((prev) => {
        const messageIndex = prev.findIndex(msg => msg.id === messageId);
        if (messageIndex === -1) return prev;
        
        const message = prev[messageIndex];
        const parts = message.parts || [{ type: "text" as const, text: "" }];
        let updatedParts = [...parts];
        
        // Handle text chunk
        if (chunk.type === 'text') {
          // Update the last text part or add a new one
          const lastTextPartIndex = updatedParts.findIndex(part => part.type === "text");
          if (lastTextPartIndex >= 0) {
            updatedParts[lastTextPartIndex] = {
              ...updatedParts[lastTextPartIndex],
              text: updatedParts[lastTextPartIndex].text + chunk.value
            };
          } else {
            updatedParts.push({ type: "text" as const, text: chunk.value });
          }
        }
        
        // Handle tool call
        else if (chunk.type === 'tool-call') {
          updatedParts.push({
            type: "tool-invocation" as const,
            toolInvocation: {
              toolCallId: chunk.id,
              toolName: chunk.name,
              args: chunk.args || {},
              state: "call" as const
            }
          });
        }
        
        // Handle partial tool call
        else if (chunk.type === 'tool-call-partial') {
          updatedParts.push({
            type: "tool-invocation" as const,
            toolInvocation: {
              toolCallId: chunk.id,
              toolName: chunk.name,
              args: chunk.args || {},
              state: "partial-call" as const
            }
          });
        }
        
        // Handle tool result
        else if (chunk.type === 'tool-result') {
          // Find and update the matching tool call
          updatedParts = updatedParts.map(part => {
            if (
              part.type === "tool-invocation" && 
              part.toolInvocation.toolCallId === chunk.id
            ) {
              return {
                ...part,
                toolInvocation: {
                  ...part.toolInvocation,
                  state: "result" as const,
                  result: chunk.result
                }
              };
            }
            return part;
          });
        }
        
        // Handle step start marker
        else if (chunk.type === 'step-start') {
          updatedParts.push({ type: "step-start" as const });
        }
        
        // Create updated messages array
        const updatedMessages = [...prev];
        updatedMessages[messageIndex] = {
          ...message,
          parts: updatedParts,
          content: updatedParts
            .filter(part => part.type === "text")
            .map(part => part.text)
            .join("")
        };
        
        return updatedMessages;
      });
    }
  };

  // Handle errors from the stream
  const handleStreamError = (error: unknown) => {
    console.error("Error getting AI response:", error);
    
    // Update the last message to show the error
    setMessages((prev) => {
      const lastMessage = prev[prev.length - 1];
      if (lastMessage.role !== "assistant") return prev;
      
      const errorMessage = {
        ...lastMessage,
        parts: [{ 
          type: "text" as const, 
          text: "Sorry, there was an error processing your request. Please try again." 
        }],
        content: "Sorry, there was an error processing your request. Please try again."
      };
      
      return [...prev.slice(0, -1), errorMessage];
    });
    
    toast({
      title: "Error",
      description: error instanceof Error ? error.message : "Failed to get AI response",
      variant: "destructive",
    });
  };

  // Handle submit of a new message
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim() || isLoading) return;
    if (!vmId) {
      toast({
        title: "Error",
        description: "No VM ID provided. Cannot send message.",
        variant: "destructive",
      });
      return;
    }
    
    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
    };
    
    // Append user message
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);
    
    // Create a placeholder for the assistant's response
    const assistantMessageId = (Date.now() + 1).toString();
    setMessages((prev) => [
      ...prev,
      {
        id: assistantMessageId,
        role: "assistant",
        content: "",
        parts: [{ type: "text", text: "" }]
      },
    ]);
    
    try {
      // Get messages for the agent
      const agentMessages = messages.map(({ role, content }) => ({ role, content }));
      
      // Append the user's current input
      agentMessages.push({ role: "user", content: input });
      
      // Get the response stream
      const stream = await createMicroVmAgentStream(vmId, agentMessages);
      
      // Process the stream
      await processStream(stream, assistantMessageId);
    } catch (error) {
      handleStreamError(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear chat history
  const handleClearChat = () => {
    setMessages([]);
  };

  return (
    <Card className={cn("flex h-full flex-col", className)}>
      <CardHeader className="p-4 flex-row items-center justify-between">
        <CardTitle className="text-sm font-medium flex items-center">
          <Sparkles className="h-4 w-4 mr-2 text-primary" />
          MicroVM DevBot
          {vmId && <span className="ml-2 text-xs text-muted-foreground">({vmId})</span>}
        </CardTitle>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleClearChat}
          className="h-8 w-8"
          disabled={isLoading}
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden p-0">
        <ScrollArea className="h-full">
          <div className="flex flex-col gap-4 p-4">
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-8 text-center">
                <Sparkles className="h-8 w-8 mb-4 text-primary opacity-50" />
                <h3 className="text-lg font-medium mb-2">
                  How can I help with your Next.js project?
                </h3>
                <p className="text-sm text-muted-foreground">
                  I can create new projects, help debug issues, and deploy your application inside this MicroVM environment.
                </p>
                <div className="mt-4 flex flex-col gap-2 w-full max-w-md">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setInput("Create a new Next.js app with Tailwind CSS");
                    }}
                    className="justify-start text-left text-sm"
                  >
                    Create a new Next.js app with Tailwind CSS
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setInput("Help me set up a simple blog with Next.js");
                    }}
                    className="justify-start text-left text-sm"
                  >
                    Help me set up a simple blog with Next.js
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setInput("Create an e-commerce product page with Next.js");
                    }}
                    className="justify-start text-left text-sm"
                  >
                    Create an e-commerce product page with Next.js
                  </Button>
                </div>
              </div>
            ) : (
              messages.map((message) => {
                if (message.role === "user") {
                  return (
                    <UserMessageUi
                      key={message.id}
                      message={message}
                    />
                  );
                }
                return (
                  <BotMessageUi
                    key={message.id}
                    message={message}
                    onToolResult={handleToolResult}
                  />
                );
              })
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>
      <CardFooter className="p-4 pt-2">
        <form onSubmit={handleSubmit} className="w-full">
          <ChatInput
            placeholder="Ask me to create a Next.js app..."
            value={input}
            onChange={handleInputChange}
            onSubmit={handleSubmit}
            isLoading={isLoading}
          />
        </form>
      </CardFooter>
    </Card>
  );
} 