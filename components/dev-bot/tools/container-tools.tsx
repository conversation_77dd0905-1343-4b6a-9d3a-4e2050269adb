"use client";

import React, { useState } from "react";
import { useContainer } from "@/lib/contexts/container-context";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Terminal, FileText, FolderTree } from "lucide-react";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";

/**
 * Execute Command Tool
 * 
 * This tool allows DevBot to execute commands in the container.
 */
export function ExecuteCommandTool({ 
  command, 
  onResult 
}: { 
  command: string; 
  onResult: (result: string) => void;
}) {
  const [isExecuting, setIsExecuting] = useState(false);
  const [output, setOutput] = useState<string | null>(null);
  const { executeCommand, containerStatus } = useContainer();

  const handleExecute = async () => {
    if (containerStatus !== "ready") {
      toast.error("Container is not ready. Please start the container first.");
      onResult("Error: Container is not ready. Please start the container first.");
      return;
    }

    setIsExecuting(true);
    try {
      const result = await executeCommand(command);
      const output = result.stdout || result.stderr || `Command executed with exit code: ${result.exitCode}`;
      setOutput(output);
      onResult(output);
    } catch (error: any) {
      console.error("Error executing command:", error);
      const errorMessage = `Error: ${error.message}`;
      setOutput(errorMessage);
      onResult(errorMessage);
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <Terminal className="h-4 w-4 mr-2" />
          Execute Command
        </CardTitle>
        <CardDescription className="text-xs">
          {command}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        {output && (
          <ScrollArea className="h-[200px] w-full rounded-md border p-2 font-mono text-xs">
            <pre className="whitespace-pre-wrap">{output}</pre>
          </ScrollArea>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          size="sm" 
          onClick={handleExecute} 
          disabled={isExecuting}
          className="w-full"
        >
          {isExecuting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Executing...
            </>
          ) : (
            <>
              <Terminal className="h-4 w-4 mr-2" />
              Execute
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

/**
 * Read File Tool
 * 
 * This tool allows DevBot to read files from the container.
 */
export function ReadFileTool({ 
  path, 
  onResult 
}: { 
  path: string; 
  onResult: (result: string) => void;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [content, setContent] = useState<string | null>(null);
  const { readFile, containerStatus } = useContainer();

  const handleRead = async () => {
    if (containerStatus !== "ready") {
      toast.error("Container is not ready. Please start the container first.");
      onResult("Error: Container is not ready. Please start the container first.");
      return;
    }

    setIsLoading(true);
    try {
      const fileContent = await readFile(path);
      setContent(fileContent);
      onResult(fileContent);
    } catch (error: any) {
      console.error("Error reading file:", error);
      const errorMessage = `Error: ${error.message}`;
      setContent(errorMessage);
      onResult(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <FileText className="h-4 w-4 mr-2" />
          Read File
        </CardTitle>
        <CardDescription className="text-xs">
          {path}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        {content && (
          <ScrollArea className="h-[200px] w-full rounded-md border p-2 font-mono text-xs">
            <pre className="whitespace-pre-wrap">{content}</pre>
          </ScrollArea>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          size="sm" 
          onClick={handleRead} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Reading...
            </>
          ) : (
            <>
              <FileText className="h-4 w-4 mr-2" />
              Read File
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

/**
 * Write File Tool
 * 
 * This tool allows DevBot to write files to the container.
 */
export function WriteFileTool({ 
  path, 
  content,
  onResult 
}: { 
  path: string;
  content: string;
  onResult: (result: string) => void;
}) {
  const [isWriting, setIsWriting] = useState(false);
  const { writeFile, containerStatus } = useContainer();

  const handleWrite = async () => {
    if (containerStatus !== "ready") {
      toast.error("Container is not ready. Please start the container first.");
      onResult("Error: Container is not ready. Please start the container first.");
      return;
    }

    setIsWriting(true);
    try {
      await writeFile(path, content);
      const successMessage = `File ${path} written successfully`;
      toast.success(successMessage);
      onResult(successMessage);
    } catch (error: any) {
      console.error("Error writing file:", error);
      const errorMessage = `Error: ${error.message}`;
      toast.error(errorMessage);
      onResult(errorMessage);
    } finally {
      setIsWriting(false);
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <FileText className="h-4 w-4 mr-2" />
          Write File
        </CardTitle>
        <CardDescription className="text-xs">
          {path}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <ScrollArea className="h-[200px] w-full rounded-md border p-2 font-mono text-xs">
          <pre className="whitespace-pre-wrap">{content}</pre>
        </ScrollArea>
      </CardContent>
      <CardFooter>
        <Button 
          size="sm" 
          onClick={handleWrite} 
          disabled={isWriting}
          className="w-full"
        >
          {isWriting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Writing...
            </>
          ) : (
            <>
              <FileText className="h-4 w-4 mr-2" />
              Write File
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

/**
 * List Files Tool
 * 
 * This tool allows DevBot to list files in a directory in the container.
 */
export function ListFilesTool({ 
  path, 
  onResult 
}: { 
  path: string; 
  onResult: (result: string) => void;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [files, setFiles] = useState<any[] | null>(null);
  const { listFiles, containerStatus } = useContainer();

  const handleList = async () => {
    if (containerStatus !== "ready") {
      toast.error("Container is not ready. Please start the container first.");
      onResult("Error: Container is not ready. Please start the container first.");
      return;
    }

    setIsLoading(true);
    try {
      const fileList = await listFiles(path);
      setFiles(fileList);
      
      // Format the result
      const formattedResult = fileList.map(file => 
        `${file.type === 'directory' ? 'd' : '-'} ${file.name}${file.type === 'directory' ? '/' : ''}`
      ).join('\n');
      
      onResult(formattedResult);
    } catch (error: any) {
      console.error("Error listing files:", error);
      const errorMessage = `Error: ${error.message}`;
      setFiles(null);
      onResult(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <FolderTree className="h-4 w-4 mr-2" />
          List Files
        </CardTitle>
        <CardDescription className="text-xs">
          {path}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        {files && (
          <ScrollArea className="h-[200px] w-full rounded-md border p-2 font-mono text-xs">
            <div className="space-y-1">
              {files.map((file, index) => (
                <div key={index} className="flex items-center">
                  {file.type === 'directory' ? (
                    <FolderTree className="h-3 w-3 mr-2 text-blue-500" />
                  ) : (
                    <FileText className="h-3 w-3 mr-2 text-gray-500" />
                  )}
                  <span>{file.name}{file.type === 'directory' ? '/' : ''}</span>
                </div>
              ))}
              {files.length === 0 && (
                <div className="text-muted-foreground">Directory is empty</div>
              )}
            </div>
          </ScrollArea>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          size="sm" 
          onClick={handleList} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Loading...
            </>
          ) : (
            <>
              <FolderTree className="h-4 w-4 mr-2" />
              List Files
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
