/**
 * Next.js project helper utilities for MicroVM AI Dev Bot
 */

import { exec } from "child_process";
import { promisify } from "util";
import path from "path";
import fs from "fs/promises";

const execAsync = promisify(exec);
const WORKSPACE_ROOT = "/app"; // Default workspace root in the VM

/**
 * Create a new Next.js project with the specified options
 */
export async function createNextJsProject(options: {
  projectName: string;
  template?: string;
  typescript?: boolean;
  tailwind?: boolean;
  eslint?: boolean;
  appDir?: boolean;
  vmId?: string;
}) {
  const { 
    projectName, 
    template = "app", 
    typescript = true, 
    tailwind = true, 
    eslint = true, 
    appDir = true, 
    vmId 
  } = options;
  
  // Sanitize project name
  const sanitizedName = projectName
    .toLowerCase()
    .replace(/[^a-z0-9-]/g, "-")
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "");
  
  if (!sanitizedName) {
    throw new Error("Invalid project name");
  }

  const projectPath = path.join(WORKSPACE_ROOT, sanitizedName);
  
  // Check if project already exists
  try {
    const checkCmd = `test -d "${projectPath}" && echo "exists" || echo "not exists"`;
    const { stdout } = await executeInVM(checkCmd, vmId);
    
    if (stdout.trim() === "exists") {
      throw new Error(`Project ${sanitizedName} already exists`);
    }
  } catch (error) {
    // If the error is our own throw, rethrow it
    if (error instanceof Error && error.message.includes("already exists")) {
      throw error;
    }
    // Otherwise continue - this means the directory check failed, which is fine
  }
  
  // Build create-next-app command with options
  let createCommand = `npx --yes create-next-app@latest ${sanitizedName}`;
  
  // Add flags
  createCommand += ` --js ${typescript ? 'false' : 'true'}`;
  createCommand += ` --eslint ${eslint ? 'true' : 'false'}`;
  createCommand += ` --tailwind ${tailwind ? 'true' : 'false'}`;
  createCommand += ` --app ${appDir ? 'true' : 'false'}`;
  createCommand += ` --src-dir true`;
  createCommand += ` --import-alias "@/*"`;

  // If a specific template is specified
  if (template && template !== "app" && template !== "default") {
    createCommand += ` --example "${template}"`;
  }
  
  // Create the project
  try {
    // Make sure we're in the workspace root
    await executeInVM(`cd ${WORKSPACE_ROOT}`, vmId);
    
    // Create the Next.js project
    const { stdout, stderr } = await executeInVM(createCommand, vmId);
    
    if (stderr && stderr.includes("ERR!")) {
      throw new Error(`Error creating Next.js project: ${stderr}`);
    }
    
    return {
      success: true,
      projectName: sanitizedName,
      projectPath,
      output: stdout
    };
  } catch (error) {
    console.error("Error creating Next.js project:", error);
    throw error;
  }
}

/**
 * Start the Next.js development server
 */
export async function startNextJsDevServer(options: {
  projectName: string;
  port?: number;
  vmId?: string;
}) {
  const { projectName, port = 3000, vmId } = options;
  
  // Sanitize project name
  const sanitizedName = projectName
    .toLowerCase()
    .replace(/[^a-z0-9-]/g, "-")
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "");
    
  if (!sanitizedName) {
    throw new Error("Invalid project name");
  }

  const projectPath = path.join(WORKSPACE_ROOT, sanitizedName);
  
  try {
    // Check if project exists
    const checkCmd = `test -d "${projectPath}" && echo "exists" || echo "not exists"`;
    const { stdout } = await executeInVM(checkCmd, vmId);
    
    if (stdout.trim() !== "exists") {
      throw new Error(`Project ${sanitizedName} does not exist`);
    }
    
    // Check if development server is already running
    const checkProcess = `pgrep -f "next dev" || echo "not running"`;
    const { stdout: processCheck } = await executeInVM(checkProcess, vmId);
    
    if (processCheck.trim() !== "not running") {
      // Kill existing server
      await executeInVM(`pkill -f "next dev" || true`, vmId);
      // Wait a moment to ensure the port is released
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Start the development server in the background
    const startCmd = `cd "${projectPath}" && (npm run dev -- -p ${port} > /tmp/nextjs-dev.log 2>&1 &) && echo "started"`;
    await executeInVM(startCmd, vmId);
    
    // Give the server a moment to start
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check if server started successfully
    const checkServerCmd = `curl -s -o /dev/null -w "%{http_code}" http://localhost:${port} -m 1 || echo "failed"`;
    const { stdout: serverCheck } = await executeInVM(checkServerCmd, vmId);
    
    const isRunning = serverCheck.trim() !== "failed" && serverCheck.trim() !== "0";
    
    return {
      success: isRunning,
      projectName: sanitizedName,
      port,
      url: isRunning ? `http://localhost:${port}` : null,
      message: isRunning 
        ? `Next.js development server started at http://localhost:${port}` 
        : "Development server may still be starting up. Try checking again in a moment."
    };
  } catch (error) {
    console.error("Error starting Next.js development server:", error);
    throw error;
  }
}

/**
 * Execute a command in the VM
 */
async function executeInVM(command: string, vmId?: string) {
  if (!vmId) {
    throw new Error("VM ID is required");
  }
  
  // Execute the command using the API
  const response = await fetch("/api/microvm/command", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ vmId, command }),
  });
  
  if (!response.ok) {
    throw new Error(`Failed to execute command: ${response.status}`);
  }
  
  const result = await response.json();
  
  if (result.error) {
    throw new Error(`Command execution error: ${result.error}`);
  }
  
  return { stdout: result.result || "", stderr: result.error || "" };
} 