"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";
import { ContainerFileBrowser } from "@/components/file-explorer/container-file-browser";
import { FileNode } from "@/components/file-explorer/file-tree";
import { useContainer } from "@/lib/contexts/container-context";
import { toast } from "sonner";
import { Loader2, Save } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getLanguageFromFileName } from "@/lib/utils/file-utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";

// Import Monaco Editor dynamically
import dynamic from "next/dynamic";
const MonacoEditor = dynamic(() => import("@monaco-editor/react"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full">
      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
    </div>
  ),
});

interface ContainerCodeEditorProps {
  className?: string;
  onFileChange?: (file: FileNode) => void;
}

interface OpenFile extends FileNode {
  content: string;
  language: string;
  isDirty: boolean;
}

export function ContainerCodeEditor({ className, onFileChange }: ContainerCodeEditorProps) {
  const [openFiles, setOpenFiles] = useState<OpenFile[]>([]);
  const [activeFile, setActiveFile] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [editorValue, setEditorValue] = useState<string>("");

  // Get container context
  const { containerStatus, readFile, writeFile } = useContainer();

  // Handle file selection from file browser
  const handleFileSelect = async (file: FileNode) => {
    if (file.type !== "file") return;

    // Check if file is already open
    const isOpen = openFiles.some((f) => f.path === file.path);
    if (isOpen) {
      // Set as active file
      setActiveFile(file.path);
      return;
    }

    // Load file content
    setIsLoading(true);
    try {
      const content = await readFile(file.path);
      const language = getLanguageFromFileName(file.name);

      // Add to open files
      const newOpenFile: OpenFile = {
        ...file,
        content,
        language,
        isDirty: false,
      };

      setOpenFiles((prev) => [...prev, newOpenFile]);
      setActiveFile(file.path);

      // Set editor value
      setEditorValue(content);

      // Notify parent
      if (onFileChange) {
        onFileChange(file);
      }
    } catch (error: any) {
      console.error("Error loading file:", error);
      toast.error(`Failed to load file: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle editor value change
  const handleEditorChange = (value: string | undefined) => {
    if (value === undefined || !activeFile) return;

    setEditorValue(value);

    // Mark file as dirty
    setOpenFiles((prev) =>
      prev.map((file) =>
        file.path === activeFile
          ? { ...file, content: value, isDirty: file.content !== value }
          : file
      )
    );
  };

  // Handle file save
  const handleSaveFile = async () => {
    if (!activeFile) return;

    const file = openFiles.find((f) => f.path === activeFile);
    if (!file) return;

    setIsLoading(true);
    try {
      await writeFile(file.path, editorValue);

      // Mark file as clean
      setOpenFiles((prev) =>
        prev.map((f) =>
          f.path === activeFile
            ? { ...f, content: editorValue, isDirty: false }
            : f
        )
      );

      toast.success(`File ${file.name} saved`);
    } catch (error: any) {
      console.error("Error saving file:", error);
      toast.error(`Failed to save file: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle file tab close
  const handleCloseFile = (path: string) => {
    // Check if file is dirty
    const file = openFiles.find((f) => f.path === path);
    if (file?.isDirty) {
      const confirm = window.confirm(
        `File ${file.name} has unsaved changes. Close anyway?`
      );
      if (!confirm) return;
    }

    // Remove from open files
    setOpenFiles((prev) => prev.filter((f) => f.path !== path));

    // If active file is closed, set active file to the first open file
    if (activeFile === path) {
      const remainingFiles = openFiles.filter((f) => f.path !== path);
      setActiveFile(remainingFiles.length > 0 ? remainingFiles[0].path : null);
    }
  };

  // Update editor value when active file changes
  useEffect(() => {
    if (activeFile) {
      const file = openFiles.find((f) => f.path === activeFile);
      if (file) {
        setEditorValue(file.content);
      }
    } else {
      setEditorValue("");
    }
  }, [activeFile, openFiles]);

  // Get active file object
  const activeFileObj = activeFile
    ? openFiles.find((f) => f.path === activeFile)
    : null;

  return (
    <div className={cn("h-full", className)}>
      <ResizablePanelGroup direction="horizontal">
        {/* File Browser */}
        <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
          <ContainerFileBrowser onFileSelect={handleFileSelect} className="h-full" />
        </ResizablePanel>

        <ResizableHandle withHandle />

        {/* Editor */}
        <ResizablePanel defaultSize={80}>
          <div className="flex flex-col h-full">
            {/* Tabs */}
            {openFiles.length > 0 ? (
              <Tabs
                value={activeFile || ""}
                onValueChange={setActiveFile}
                className="w-full"
              >
                <div className="flex items-center border-b">
                  <ScrollArea className="w-full" orientation="horizontal">
                    <TabsList className="h-10 bg-transparent p-0">
                      {openFiles.map((file) => (
                        <TabsTrigger
                          key={file.path}
                          value={file.path}
                          className={cn(
                            "data-[state=active]:bg-background relative h-10 rounded-none border-r px-4 font-normal",
                            file.isDirty && "after:content-['*'] after:ml-1 after:text-blue-500"
                          )}
                        >
                          {file.name}
                          <button
                            className="ml-2 rounded-full p-1 hover:bg-muted"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCloseFile(file.path);
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="12"
                              height="12"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M18 6 6 18" />
                              <path d="m6 6 12 12" />
                            </svg>
                          </button>
                        </TabsTrigger>
                      ))}
                    </TabsList>
                  </ScrollArea>
                  
                  {/* Save button */}
                  {activeFileObj?.isDirty && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-10 w-10 rounded-none border-l"
                      onClick={handleSaveFile}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                    </Button>
                  )}
                </div>

                {/* Editor content */}
                {openFiles.map((file) => (
                  <TabsContent
                    key={file.path}
                    value={file.path}
                    className="border-none p-0 h-full"
                  >
                    <MonacoEditor
                      height="100%"
                      language={file.language}
                      value={file.path === activeFile ? editorValue : file.content}
                      onChange={handleEditorChange}
                      options={{
                        minimap: { enabled: false },
                        scrollBeyondLastLine: false,
                        automaticLayout: true,
                      }}
                    />
                  </TabsContent>
                ))}
              </Tabs>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mb-4 opacity-50"
                >
                  <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                  <polyline points="14 2 14 8 20 8" />
                </svg>
                <p className="mb-2">No file open</p>
                <p className="text-sm">Select a file from the file browser to edit</p>
              </div>
            )}
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}
