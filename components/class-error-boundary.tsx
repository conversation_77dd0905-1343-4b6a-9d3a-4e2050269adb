"use client"

import React, { Component, ErrorInfo, ReactNode } from 'react'

interface ClassErrorBoundaryProps {
  children: ReactNode
  fallback: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface ClassErrorBoundaryState {
  hasError: boolean
  error: Error | null
}

/**
 * ClassErrorBoundary component
 * 
 * Catches JavaScript errors anywhere in its child component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 */
export class ClassErrorBoundary extends Component<ClassErrorBoundaryProps, ClassErrorBoundaryState> {
  constructor(props: ClassErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): ClassErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    console.error('Error caught by ClassErrorBoundary:', error, errorInfo)
    
    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // Render the fallback UI
      return this.props.fallback
    }

    // Render the children normally
    return this.props.children
  }
}
