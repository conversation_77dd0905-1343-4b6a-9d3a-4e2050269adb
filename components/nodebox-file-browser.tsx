/**
 * Nodebox File Browser
 * 
 * Enhanced file browser that integrates with Nodebox store
 * and provides AI agent-compatible file operations
 */

"use client";

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/ui/context-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  FolderTree,
  FileText,
  FilePlus,
  FolderPlus,
  Trash2,
  Search,
  RefreshCw,
  Download,
  Upload,
  Copy,
  Edit,
  Eye,
  Code,
  Image,
  FileCode,
  Loader2
} from 'lucide-react';
import {
  useNodeboxIntegration,
  useActiveNodeboxInstance,
  useNodeboxFileSystem,
  useNodeboxActions
} from '@/lib/stores/nodebox-store';
import { FileEntry } from '@/lib/nodebox-runtime/api/nodebox-types';

interface NodeboxFileBrowserProps {
  className?: string;
  projectId?: string;
  onFileSelect?: (file: FileEntry) => void;
  showActions?: boolean;
}

export function NodeboxFileBrowser({
  className,
  projectId,
  onFileSelect,
  showActions = true
}: NodeboxFileBrowserProps) {
  // Nodebox integration
  const {
    activeInstance,
    isLoading,
    error,
    quickReadFile,
    quickWriteFile,
    createFile,
    createDirectory,
    deleteFile,
    loadFileSystem
  } = useNodeboxIntegration(projectId);

  const files = useNodeboxFileSystem(activeInstance?.id);
  const actions = useNodeboxActions();

  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFile, setSelectedFile] = useState<FileEntry | null>(null);
  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set(['/']));
  const [isCreatingFile, setIsCreatingFile] = useState(false);
  const [isCreatingDir, setIsCreatingDir] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [newDirName, setNewDirName] = useState('');
  const [currentPath, setCurrentPath] = useState('/');

  // Load file system when instance changes
  useEffect(() => {
    if (activeInstance) {
      loadFileSystem(activeInstance.id);
    }
  }, [activeInstance, loadFileSystem]);

  // Filter files based on search query
  const filteredFiles = React.useMemo(() => {
    if (!searchQuery) return files;
    
    return files.filter(file =>
      file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      file.path.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [files, searchQuery]);

  // Build file tree structure
  const fileTree = React.useMemo(() => {
    const tree: Record<string, FileEntry[]> = {};
    
    filteredFiles.forEach(file => {
      const parentPath = file.path.substring(0, file.path.lastIndexOf('/')) || '/';
      if (!tree[parentPath]) {
        tree[parentPath] = [];
      }
      tree[parentPath].push(file);
    });

    // Sort files: directories first, then files, both alphabetically
    Object.keys(tree).forEach(path => {
      tree[path].sort((a, b) => {
        if (a.type === 'directory' && b.type === 'file') return -1;
        if (a.type === 'file' && b.type === 'directory') return 1;
        return a.name.localeCompare(b.name);
      });
    });

    return tree;
  }, [filteredFiles]);

  // Handle file selection
  const handleFileSelect = (file: FileEntry) => {
    setSelectedFile(file);
    if (onFileSelect) {
      onFileSelect(file);
    }
  };

  // Handle directory expansion
  const toggleDirectory = (path: string) => {
    const newExpanded = new Set(expandedDirs);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedDirs(newExpanded);
  };

  // Handle file creation
  const handleCreateFile = async () => {
    if (!newFileName.trim() || !activeInstance) return;

    const filePath = currentPath === '/' ? `/${newFileName}` : `${currentPath}/${newFileName}`;
    
    try {
      await createFile(activeInstance.id, filePath, '');
      setNewFileName('');
      setIsCreatingFile(false);
      await loadFileSystem(activeInstance.id);
    } catch (error) {
      console.error('Error creating file:', error);
    }
  };

  // Handle directory creation
  const handleCreateDirectory = async () => {
    if (!newDirName.trim() || !activeInstance) return;

    const dirPath = currentPath === '/' ? `/${newDirName}` : `${currentPath}/${newDirName}`;
    
    try {
      await createDirectory(activeInstance.id, dirPath);
      setNewDirName('');
      setIsCreatingDir(false);
      await loadFileSystem(activeInstance.id);
    } catch (error) {
      console.error('Error creating directory:', error);
    }
  };

  // Handle file deletion
  const handleDeleteFile = async (file: FileEntry) => {
    if (!activeInstance) return;

    try {
      await deleteFile(activeInstance.id, file.path);
      await loadFileSystem(activeInstance.id);
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  };

  // Get file icon
  const getFileIcon = (file: FileEntry) => {
    if (file.type === 'directory') {
      return <FolderTree className="h-4 w-4 text-blue-500" />;
    }

    const extension = file.name.split('.').pop()?.toLowerCase() || '';
    const iconMap: Record<string, React.ReactNode> = {
      'js': <FileCode className="h-4 w-4 text-yellow-500" />,
      'jsx': <FileCode className="h-4 w-4 text-blue-500" />,
      'ts': <FileCode className="h-4 w-4 text-blue-600" />,
      'tsx': <FileCode className="h-4 w-4 text-blue-600" />,
      'json': <Code className="h-4 w-4 text-green-500" />,
      'html': <FileCode className="h-4 w-4 text-orange-500" />,
      'css': <FileCode className="h-4 w-4 text-blue-400" />,
      'md': <FileText className="h-4 w-4 text-gray-500" />,
      'png': <Image className="h-4 w-4 text-purple-500" />,
      'jpg': <Image className="h-4 w-4 text-purple-500" />,
      'jpeg': <Image className="h-4 w-4 text-purple-500" />,
      'gif': <Image className="h-4 w-4 text-purple-500" />,
      'svg': <Image className="h-4 w-4 text-purple-500" />,
    };

    return iconMap[extension] || <FileText className="h-4 w-4 text-gray-500" />;
  };

  // Render file tree recursively
  const renderFileTree = (path: string, level = 0) => {
    const filesInPath = fileTree[path] || [];
    
    return filesInPath.map(file => (
      <div key={file.path}>
        <ContextMenu>
          <ContextMenuTrigger>
            <div
              className={cn(
                "flex items-center gap-2 p-1 rounded cursor-pointer hover:bg-muted/50 transition-colors",
                selectedFile?.path === file.path && "bg-muted",
                "group"
              )}
              style={{ paddingLeft: `${level * 16 + 8}px` }}
              onClick={() => {
                if (file.type === 'directory') {
                  toggleDirectory(file.path);
                } else {
                  handleFileSelect(file);
                }
              }}
            >
              {getFileIcon(file)}
              <span className="text-sm flex-1 truncate">{file.name}</span>
              {file.type === 'file' && file.size && (
                <Badge variant="outline" className="text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                  {(file.size / 1024).toFixed(1)}KB
                </Badge>
              )}
            </div>
          </ContextMenuTrigger>
          
          <ContextMenuContent>
            {file.type === 'file' && (
              <>
                <ContextMenuItem onClick={() => handleFileSelect(file)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Open
                </ContextMenuItem>
                <ContextMenuItem>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Path
                </ContextMenuItem>
                <ContextMenuItem>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </ContextMenuItem>
              </>
            )}
            {file.type === 'directory' && (
              <ContextMenuItem onClick={() => setCurrentPath(file.path)}>
                <FolderTree className="h-4 w-4 mr-2" />
                Open in Explorer
              </ContextMenuItem>
            )}
            <ContextMenuItem 
              className="text-destructive"
              onClick={() => handleDeleteFile(file)}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </ContextMenuItem>
          </ContextMenuContent>
        </ContextMenu>

        {/* Render children if directory is expanded */}
        {file.type === 'directory' && expandedDirs.has(file.path) && (
          <div>
            {renderFileTree(file.path, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center gap-2">
          <FolderTree className="h-4 w-4" />
          File Browser
          {activeInstance && (
            <Badge variant="outline" className="ml-auto">
              {files.length} items
            </Badge>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="p-2 space-y-2">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            className="pl-8 h-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Actions */}
        {showActions && activeInstance && (
          <div className="flex gap-1">
            <Dialog open={isCreatingFile} onOpenChange={setIsCreatingFile}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="flex-1">
                  <FilePlus className="h-3 w-3 mr-1" />
                  File
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New File</DialogTitle>
                  <DialogDescription>
                    Create a new file in {currentPath}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-2">
                  <Label htmlFor="filename">File Name</Label>
                  <Input
                    id="filename"
                    placeholder="example.tsx"
                    value={newFileName}
                    onChange={(e) => setNewFileName(e.target.value)}
                  />
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreatingFile(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateFile}>Create</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isCreatingDir} onOpenChange={setIsCreatingDir}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="flex-1">
                  <FolderPlus className="h-3 w-3 mr-1" />
                  Folder
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Folder</DialogTitle>
                  <DialogDescription>
                    Create a new folder in {currentPath}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-2">
                  <Label htmlFor="dirname">Folder Name</Label>
                  <Input
                    id="dirname"
                    placeholder="components"
                    value={newDirName}
                    onChange={(e) => setNewDirName(e.target.value)}
                  />
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreatingDir(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateDirectory}>Create</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Button
              variant="outline"
              size="sm"
              onClick={() => activeInstance && loadFileSystem(activeInstance.id)}
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        )}

        {/* File Tree */}
        <ScrollArea className="h-[calc(100vh-200px)]">
          {activeInstance ? (
            files.length > 0 ? (
              <div className="space-y-1">
                {renderFileTree('/')}
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                <FileText className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm">No files found</p>
                {showActions && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => setIsCreatingFile(true)}
                  >
                    <FilePlus className="h-3 w-3 mr-1" />
                    Create First File
                  </Button>
                )}
              </div>
            )
          ) : (
            <div className="text-center text-muted-foreground py-8">
              <Code className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">No active instance</p>
            </div>
          )}
        </ScrollArea>

        {/* Status */}
        {error && (
          <div className="text-xs text-destructive p-2 bg-destructive/10 rounded">
            Error: {error.message}
          </div>
        )}
        
        {isLoading && (
          <div className="flex items-center gap-2 text-xs text-muted-foreground p-2">
            <Loader2 className="h-3 w-3 animate-spin" />
            Loading files...
          </div>
        )}
      </CardContent>
    </Card>
  );
}
