/**
 * File Sync Dashboard Component
 * Demonstrates usage of file sync hooks
 */

'use client'

import React, { useState } from 'react'
import { useFileSync, useConflictResolution, useSyncProgress } from '@/hooks'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  RefreshCw, 
  Upload, 
  Download, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  FileText,
  Settings
} from 'lucide-react'

interface FileSyncDashboardProps {
  projectId: string
}

export function FileSyncDashboard({ projectId }: FileSyncDashboardProps) {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])

  // File sync hook
  const {
    stats,
    isLoading: syncLoading,
    error: syncError,
    lastSync,
    config,
    isConnected,
    fullSync,
    incrementalSync,
    syncFiles,
    refreshStats,
    updateConfig,
    startWatching,
    stopWatching,
    clearError: clearSyncError
  } = useFileSync(projectId)

  // Conflict resolution hook
  const {
    conflicts,
    isLoading: conflictLoading,
    error: conflictError,
    resolvingFiles,
    resolveConflict,
    autoResolveConflicts,
    getConflictSummary,
    clearError: clearConflictError,
    hasConflicts,
    conflictCount,
    autoResolvableCount
  } = useConflictResolution(projectId)

  // Sync progress hook
  const {
    currentSession,
    progress,
    recentSessions,
    events,
    isLoading: progressLoading,
    error: progressError,
    hasActiveSync,
    progressPercentage,
    getProgressText,
    getTimeRemainingText,
    statistics
  } = useSyncProgress(projectId)

  const handleFullSync = async () => {
    try {
      await fullSync({ direction: 'bidirectional' })
    } catch (error) {
      console.error('Full sync failed:', error)
    }
  }

  const handleIncrementalSync = async () => {
    try {
      await incrementalSync({ direction: 'bidirectional' })
    } catch (error) {
      console.error('Incremental sync failed:', error)
    }
  }

  const handleSyncFiles = async () => {
    if (selectedFiles.length === 0) return
    
    try {
      await syncFiles(selectedFiles, { direction: 'bidirectional' })
      setSelectedFiles([])
    } catch (error) {
      console.error('File sync failed:', error)
    }
  }

  const handleAutoResolve = async () => {
    try {
      const result = await autoResolveConflicts()
      console.log(`Auto-resolved ${result.resolved} conflicts`)
    } catch (error) {
      console.error('Auto-resolve failed:', error)
    }
  }

  const handleResolveConflict = async (filePath: string, strategy: string) => {
    try {
      await resolveConflict(filePath, { strategy: strategy as any })
    } catch (error) {
      console.error('Conflict resolution failed:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">File Synchronization</h2>
          <p className="text-muted-foreground">
            Project: {projectId} • 
            Status: <Badge variant={isConnected ? 'default' : 'destructive'}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={refreshStats} disabled={syncLoading}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alerts */}
      {(syncError || conflictError || progressError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {syncError || conflictError || progressError}
            <Button 
              variant="ghost" 
              size="sm" 
              className="ml-2"
              onClick={() => {
                clearSyncError()
                clearConflictError()
              }}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Sync Progress */}
      {hasActiveSync && progress && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="w-4 h-4 animate-spin" />
              Sync in Progress
            </CardTitle>
            <CardDescription>{getProgressText()}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Progress value={progressPercentage} className="w-full" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{progressPercentage}% complete</span>
                {getTimeRemainingText() && <span>{getTimeRemainingText()}</span>}
              </div>
              {progress.currentFile && (
                <p className="text-sm">Processing: {progress.currentFile}</p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="conflicts">
            Conflicts {hasConflicts && <Badge className="ml-1">{conflictCount}</Badge>}
          </TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Sync Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  File Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                {stats ? (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total Files:</span>
                      <span>{stats.totalFiles}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Synced:</span>
                      <span className="text-green-600">{stats.syncedFiles}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Conflicts:</span>
                      <span className="text-red-600">{stats.conflictedFiles}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Pending:</span>
                      <span className="text-yellow-600">{stats.pendingFiles}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Size:</span>
                      <span>{(stats.totalSize / 1024 / 1024).toFixed(2)} MB</span>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Loading...</p>
                )}
              </CardContent>
            </Card>

            {/* Sync Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Sync Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button 
                  onClick={handleFullSync} 
                  disabled={syncLoading || hasActiveSync}
                  className="w-full"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Full Sync
                </Button>
                <Button 
                  onClick={handleIncrementalSync} 
                  disabled={syncLoading || hasActiveSync}
                  variant="outline"
                  className="w-full"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Incremental Sync
                </Button>
                {selectedFiles.length > 0 && (
                  <Button 
                    onClick={handleSyncFiles} 
                    disabled={syncLoading || hasActiveSync}
                    variant="secondary"
                    className="w-full"
                  >
                    Sync Selected ({selectedFiles.length})
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Last Sync */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Last Sync
                </CardTitle>
              </CardHeader>
              <CardContent>
                {lastSync ? (
                  <div className="space-y-2">
                    <p className="text-sm">{lastSync.toLocaleString()}</p>
                    <Badge variant="outline">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Completed
                    </Badge>
                  </div>
                ) : (
                  <p className="text-muted-foreground">No sync performed yet</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Conflicts Tab */}
        <TabsContent value="conflicts" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">File Conflicts</h3>
            {autoResolvableCount > 0 && (
              <Button onClick={handleAutoResolve} disabled={conflictLoading}>
                Auto-resolve ({autoResolvableCount})
              </Button>
            )}
          </div>

          {hasConflicts ? (
            <div className="space-y-4">
              {conflicts.map((conflict) => {
                const summary = getConflictSummary(conflict)
                const isResolving = resolvingFiles.has(conflict.path)
                
                return (
                  <Card key={conflict.id}>
                    <CardHeader>
                      <CardTitle className="text-base">{conflict.path}</CardTitle>
                      <CardDescription>
                        {summary.type} conflict • {summary.nodeboxSize} vs {summary.databaseSize} bytes
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleResolveConflict(conflict.path, 'nodebox_wins')}
                          disabled={isResolving}
                        >
                          Use Nodebox
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleResolveConflict(conflict.path, 'database_wins')}
                          disabled={isResolving}
                        >
                          Use Database
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleResolveConflict(conflict.path, 'merge_content')}
                          disabled={isResolving}
                        >
                          Merge
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <CheckCircle className="w-12 h-12 mx-auto text-green-500 mb-4" />
                <p className="text-lg font-semibold">No conflicts found</p>
                <p className="text-muted-foreground">All files are in sync</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-4">
          <h3 className="text-lg font-semibold">Recent Sync Sessions</h3>
          
          {recentSessions.length > 0 ? (
            <div className="space-y-2">
              {recentSessions.map((session) => (
                <Card key={session.id}>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{session.sessionType.replace('_', ' ')}</p>
                        <p className="text-sm text-muted-foreground">
                          {session.startedAt.toLocaleString()}
                        </p>
                      </div>
                      <Badge variant={session.status === 'completed' ? 'default' : 'destructive'}>
                        {session.status}
                      </Badge>
                    </div>
                    <div className="mt-2 text-sm text-muted-foreground">
                      {session.statistics.filesCreated} created • 
                      {session.statistics.filesUpdated} updated • 
                      {session.statistics.filesDeleted} deleted
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No sync history available</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          <h3 className="text-lg font-semibold">Sync Configuration</h3>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                File Watching
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button onClick={startWatching} variant="outline">
                  Start Watching
                </Button>
                <Button onClick={stopWatching} variant="outline">
                  Stop Watching
                </Button>
              </div>
              
              {config && (
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Auto Sync:</span>
                    <Badge variant={config.autoSync ? 'default' : 'secondary'}>
                      {config.autoSync ? 'Enabled' : 'Disabled'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Sync Interval:</span>
                    <span>{config.syncInterval}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Conflict Resolution:</span>
                    <span>{config.conflictResolution}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
