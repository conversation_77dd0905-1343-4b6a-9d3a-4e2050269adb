"use client";

import { useState, useEffect, useCallback } from "react";
import { Loader2, RefreshCw, ExternalLink, PlayCircle, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/components/ui/use-toast";

interface MicroVmIframePreviewProps {
  vmId?: string;
  projectPath?: string;
  port?: number;
}

export function MicroVmIframePreview({
  vmId,
  projectPath = "/app",
  port = 3000
}: MicroVmIframePreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isAvailable, setIsAvailable] = useState(false);
  const [url, setUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [progress, setProgress] = useState(0);
  const [startingServer, setStartingServer] = useState(false);

  const getAppUrl = useCallback(async (silent = false) => {
    if (!vmId) {
      setError("No VM ID provided");
      return;
    }

    if (!silent) {
      setIsLoading(true);
      setError(null);
    }

    try {
      // Request the app URL from the server
      const response = await fetch(`/api/microvm/app-url?vmId=${encodeURIComponent(vmId)}&port=${port}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          // App might not be started yet, show specific message
          setError("No application is running on port " + port);
          setIsAvailable(false);
        } else {
          throw new Error(`Failed to get app URL: ${response.status}`);
        }
        return;
      }
      
      const data = await response.json();
      
      if (data.url) {
        setUrl(data.url);
        setIsAvailable(true);
        setError(null);
        setRetryCount(0);
        setProgress(100);
        if (!silent && !isAvailable) {
          toast({
            title: "App is running",
            description: "The application is now available for preview.",
            variant: "default"
          });
        }
      } else {
        setError(data.error || "Failed to get app URL");
        setIsAvailable(false);
      }
    } catch (err) {
      console.error("Error getting app URL:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
      setIsAvailable(false);
    } finally {
      setIsLoading(false);
    }
  }, [vmId, port, isAvailable]);

  const startServerManually = async () => {
    if (!vmId) return;
    
    setStartingServer(true);
    setProgress(10);
    
    try {
      // Execute command to check for Next.js projects in workspace root
      const response = await fetch("/api/microvm/command", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          vmId, 
          command: "find /app -maxdepth 2 -name 'package.json' | xargs grep -l 'next' 2>/dev/null || echo 'No Next.js projects found'" 
        }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to check for Next.js projects");
      }
      
      const result = await response.json();
      const output = result.result || '';
      setProgress(30);
      
      if (output.includes('No Next.js projects found')) {
        setError("No Next.js projects found in workspace");
        return;
      }
      
      // Get first project path
      const projectPaths = output
        .split('\n')
        .filter((line: string) => line.trim() && line.includes('/app/'));
      
      if (projectPaths.length === 0) {
        setError("No valid Next.js project paths found");
        return;
      }
      
      // Extract project directory from package.json path
      const projectDir = projectPaths[0].replace(/\/package\.json$/, '');
      setProgress(50);
      
      // Start development server
      const startResponse = await fetch("/api/microvm/command", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          vmId, 
          command: `cd ${projectDir} && (npm run dev -- -p ${port} > /tmp/nextjs-dev.log 2>&1 &) && echo "Started server"` 
        }),
      });
      
      if (!startResponse.ok) {
        throw new Error("Failed to start development server");
      }
      
      setProgress(70);
      toast({
        title: "Starting server",
        description: "The Next.js development server is starting. This may take a moment...",
      });
      
      // Wait a few seconds for server to start
      await new Promise(resolve => setTimeout(resolve, 5000));
      setProgress(90);
      
      // Check if server is running
      await getAppUrl();
      
    } catch (error) {
      console.error("Error starting server manually:", error);
      setError(error instanceof Error ? error.message : "Failed to start server");
    } finally {
      setStartingServer(false);
      setProgress(100);
    }
  };

  // Check if the app is available when component mounts
  useEffect(() => {
    getAppUrl();

    // Poll the app URL every 5 seconds if not available
    const interval = setInterval(() => {
      if (!isAvailable && !startingServer) {
        getAppUrl(true);
        setRetryCount(prev => prev + 1);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [vmId, port, getAppUrl, isAvailable, startingServer]);

  const handleRefresh = () => {
    getAppUrl();
  };

  const handleOpenInNewTab = () => {
    if (url) {
      window.open(url, "_blank");
    }
  };

  return (
    <Card className="flex flex-col h-full">
      <CardHeader className="flex flex-row items-center justify-between py-2 px-4">
        <CardTitle className="text-sm font-medium">App Preview</CardTitle>
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={handleRefresh} 
            title="Refresh"
            disabled={startingServer}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          {url && (
            <Button variant="ghost" size="icon" onClick={handleOpenInNewTab} title="Open in new tab">
              <ExternalLink className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="p-0 flex-1 relative border-t">
        {startingServer && (
          <div className="absolute inset-0 z-50 flex flex-col items-center justify-center bg-background/80">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
            <p className="text-sm font-medium mb-4">Starting Next.js development server...</p>
            <Progress value={progress} className="w-64 h-2 mb-2" />
          </div>
        )}
        
        {isLoading && !startingServer && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/50">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Loading preview...</span>
          </div>
        )}

        {error && !isLoading && !startingServer && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-background p-4">
            <AlertCircle className="h-10 w-10 text-amber-500 mb-2" />
            <p className="text-sm text-muted-foreground text-center mb-4">
              {error}
            </p>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Button variant="default" size="sm" onClick={startServerManually}>
                <PlayCircle className="h-4 w-4 mr-2" />
                Start Server
              </Button>
            </div>
          </div>
        )}

        {url && !isLoading && (
          <iframe
            src={url}
            className="w-full h-full border-0"
            title="Next.js App Preview"
            sandbox="allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-popups allow-popups-to-escape-sandbox allow-presentation allow-same-origin allow-scripts"
          />
        )}

        {!url && !isLoading && !error && !startingServer && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-background p-4">
            <p className="text-sm text-muted-foreground text-center mb-4">
              No running app detected on port {port}.
            </p>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Check Again
              </Button>
              <Button variant="default" size="sm" onClick={startServerManually}>
                <PlayCircle className="h-4 w-4 mr-2" />
                Start Server
              </Button>
            </div>
          </div>
        )}
      </CardContent>
      
      {retryCount > 3 && !isAvailable && !startingServer && !url && (
        <CardFooter className="p-2 border-t bg-muted/20">
          <Alert className="w-full">
            <AlertDescription className="text-xs">
              If you've already asked the AI to create and start a Next.js app, try switching to the AI Agent tab and asking it to "start the development server" again.
            </AlertDescription>
          </Alert>
        </CardFooter>
      )}
    </Card>
  );
} 