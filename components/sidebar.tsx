import { FileCode, Server } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface SidebarProps {
  showFileExplorer: boolean;
  onFileExplorerToggle: () => void;
  showMicroVmDashboard?: boolean;
  onMicroVmDashboardToggle?: () => void;
}

export function Sidebar({
  showFileExplorer,
  onFileExplorerToggle,
  showMicroVmDashboard = false,
  onMicroVmDashboardToggle
}: SidebarProps) {
  return (
    <div className="group fixed left-0 top-0 z-30 flex h-screen w-12 flex-col items-center border-r bg-background px-2 py-2">
      <div className="flex flex-col items-center gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={onFileExplorerToggle}
              className={cn(
                "h-9 w-9",
                showFileExplorer && "bg-accent"
              )}
            >
              <FileCode className="h-4 w-4" />
              <span className="sr-only">File Explorer</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right">File Explorer</TooltipContent>
        </Tooltip>
        
        {onMicroVmDashboardToggle && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={onMicroVmDashboardToggle}
                className={cn(
                  "h-9 w-9",
                  showMicroVmDashboard && "bg-accent"
                )}
              >
                <Server className="h-4 w-4" />
                <span className="sr-only">MicroVM Dashboard</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">MicroVM Dashboard</TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  );
} 