"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Server, 
  Globe, 
  Database, 
  Brain, 
  Box, 
  Cpu, 
  Search,
  Memory,
  HardDrive,
  Wifi
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface VMTemplate {
  id: string
  name: string
  description: string
  icon: string
  specs: {
    cpu: number
    memory: number
    storage: number
    bandwidth: string
  }
  price: number
  popular: boolean
  category: string
}

interface VMTemplateGridProps {
  templates: VMTemplate[]
  onSelect: (template: VMTemplate) => void
}

export function VMTemplateGrid({ templates, onSelect }: VMTemplateGridProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  
  // Get icon component based on template icon name
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case "server":
        return <Server className="h-10 w-10 text-primary" />
      case "globe":
        return <Globe className="h-10 w-10 text-blue-500" />
      case "database":
        return <Database className="h-10 w-10 text-purple-500" />
      case "brain":
        return <Brain className="h-10 w-10 text-pink-500" />
      case "box":
        return <Box className="h-10 w-10 text-green-500" />
      case "cpu":
        return <Cpu className="h-10 w-10 text-orange-500" />
      default:
        return <Server className="h-10 w-10 text-primary" />
    }
  }
  
  // Filter templates based on search term and category
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCategory = categoryFilter === "all" || template.category === categoryFilter
    
    return matchesSearch && matchesCategory
  })
  
  // Get unique categories for filter
  const categories = Array.from(new Set(templates.map(t => t.category)))
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {filteredTemplates.length === 0 ? (
        <div className="text-center py-10 border rounded-md">
          <Server className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
          <h3 className="text-lg font-medium">No templates found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search or filter criteria
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <Card 
              key={template.id}
              className={cn(
                "flex flex-col",
                template.popular && "border-primary shadow-md"
              )}
            >
              <CardHeader>
                <div className="flex justify-between">
                  <div className="flex items-center">
                    {getIconComponent(template.icon)}
                    <div className="ml-4">
                      <CardTitle>{template.name}</CardTitle>
                      <CardDescription className="mt-1">
                        {template.description}
                      </CardDescription>
                    </div>
                  </div>
                  {template.popular && (
                    <Badge className="bg-primary ml-2 flex-shrink-0">Popular</Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-1">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <Cpu className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{template.specs.cpu} CPU Cores</span>
                  </div>
                  <div className="flex items-center">
                    <Memory className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{template.specs.memory} GB RAM</span>
                  </div>
                  <div className="flex items-center">
                    <HardDrive className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{template.specs.storage} GB SSD</span>
                  </div>
                  <div className="flex items-center">
                    <Wifi className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{template.specs.bandwidth}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between items-center">
                <div className="font-bold text-lg">
                  ${template.price}<span className="text-sm font-normal text-muted-foreground">/mo</span>
                </div>
                <Button onClick={() => onSelect(template)}>
                  Select
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
