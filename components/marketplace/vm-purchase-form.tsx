"use client"

import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Loader2,
  Server,
  Globe,
  Database,
  Brain,
  Box,
  Cpu,
  Memory,
  HardDrive,
  Wifi,
  CreditCard
} from "lucide-react"

// Form schema
const formSchema = z.object({
  vmName: z.string().min(3, "VM name must be at least 3 characters"),
  region: z.string({
    required_error: "Please select a region",
  }),
  cpu: z.number().min(1, "CPU cores must be at least 1"),
  memory: z.number().min(1, "Memory must be at least 1 GB"),
  storage: z.number().min(10, "Storage must be at least 10 GB"),
  operatingSystem: z.string({
    required_error: "Please select an operating system",
  }),
  backups: z.boolean().default(false),
  autoStart: z.boolean().default(true),
  paymentMethod: z.enum(["credit_card", "paypal", "crypto"]),
  agreeTerms: z.boolean().refine(val => val === true, {
    message: "You must agree to the terms and conditions",
  }),
});

interface VMPurchaseFormProps {
  template: any
  onSubmit: (data: any) => void
  onCancel: () => void
  isLoading?: boolean
}

export function VMPurchaseForm({ template, onSubmit, onCancel, isLoading = false }: VMPurchaseFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [totalPrice, setTotalPrice] = useState(template.price)

  // Default values based on template
  const defaultValues = {
    vmName: `${template.name.toLowerCase().replace(/\s+/g, '-')}-${Math.floor(Math.random() * 1000)}`,
    region: "us-east",
    cpu: template.specs.cpu,
    memory: template.specs.memory,
    storage: template.specs.storage,
    operatingSystem: "ubuntu-20.04",
    backups: false,
    autoStart: true,
    paymentMethod: "credit_card",
    agreeTerms: false,
  }

  // Form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues,
  })

  // Watch form values to calculate price
  const cpu = form.watch("cpu")
  const memory = form.watch("memory")
  const storage = form.watch("storage")
  const backups = form.watch("backups")

  // Calculate price based on resources
  const calculatePrice = () => {
    // Base price from template
    let price = template.price

    // Adjust for CPU changes
    if (cpu > template.specs.cpu) {
      price += (cpu - template.specs.cpu) * 5
    }

    // Adjust for memory changes
    if (memory > template.specs.memory) {
      price += (memory - template.specs.memory) * 2
    }

    // Adjust for storage changes
    if (storage > template.specs.storage) {
      price += (storage - template.specs.storage) * 0.1
    }

    // Add backup cost
    if (backups) {
      price += Math.ceil(price * 0.2) // 20% extra for backups
    }

    return Math.round(price * 100) / 100
  }

  // Update price when form values change
  useState(() => {
    setTotalPrice(calculatePrice())
  })

  // Handle form submission
  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsSubmitting(true)

      // Calculate final price
      const finalPrice = calculatePrice()

      // Submit form data with price
      await onSubmit({
        ...values,
        price: finalPrice,
        templateId: template.id,
        templateName: template.name
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium">VM Configuration</h3>
              <p className="text-sm text-muted-foreground">
                Configure your virtual machine specifications
              </p>
            </div>

            <FormField
              control={form.control}
              name="vmName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>VM Name</FormLabel>
                  <FormControl>
                    <Input placeholder="my-awesome-vm" {...field} />
                  </FormControl>
                  <FormDescription>
                    A unique name for your virtual machine
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="region"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Region</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a region" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="us-east">US East (N. Virginia)</SelectItem>
                      <SelectItem value="us-west">US West (Oregon)</SelectItem>
                      <SelectItem value="eu-central">EU Central (Frankfurt)</SelectItem>
                      <SelectItem value="ap-southeast">Asia Pacific (Singapore)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The geographic location where your VM will be hosted
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="operatingSystem"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Operating System</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an OS" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ubuntu-20.04">Ubuntu 20.04 LTS</SelectItem>
                      <SelectItem value="ubuntu-22.04">Ubuntu 22.04 LTS</SelectItem>
                      <SelectItem value="debian-11">Debian 11</SelectItem>
                      <SelectItem value="centos-8">CentOS 8</SelectItem>
                      <SelectItem value="windows-server-2022">Windows Server 2022</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The operating system for your virtual machine
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            <div>
              <h3 className="text-lg font-medium">Resources</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Adjust the resources for your virtual machine
              </p>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="cpu"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between">
                        <FormLabel>CPU Cores</FormLabel>
                        <span className="text-muted-foreground">{field.value} cores</span>
                      </div>
                      <FormControl>
                        <Slider
                          min={1}
                          max={16}
                          step={1}
                          defaultValue={[field.value]}
                          onValueChange={(vals) => {
                            field.onChange(vals[0])
                            setTotalPrice(calculatePrice())
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="memory"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between">
                        <FormLabel>Memory (RAM)</FormLabel>
                        <span className="text-muted-foreground">{field.value} GB</span>
                      </div>
                      <FormControl>
                        <Slider
                          min={1}
                          max={64}
                          step={1}
                          defaultValue={[field.value]}
                          onValueChange={(vals) => {
                            field.onChange(vals[0])
                            setTotalPrice(calculatePrice())
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="storage"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between">
                        <FormLabel>Storage</FormLabel>
                        <span className="text-muted-foreground">{field.value} GB</span>
                      </div>
                      <FormControl>
                        <Slider
                          min={10}
                          max={1000}
                          step={10}
                          defaultValue={[field.value]}
                          onValueChange={(vals) => {
                            field.onChange(vals[0])
                            setTotalPrice(calculatePrice())
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium">Additional Options</h3>
              <p className="text-sm text-muted-foreground">
                Configure additional settings for your VM
              </p>
            </div>

            <FormField
              control={form.control}
              name="backups"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={(checked) => {
                        field.onChange(checked)
                        setTotalPrice(calculatePrice())
                      }}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Daily Backups</FormLabel>
                    <FormDescription>
                      Enable automated daily backups for your VM (adds 20% to monthly cost)
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="autoStart"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Auto-start VM</FormLabel>
                    <FormDescription>
                      Automatically start the VM after creation and on host reboot
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <Separator />

            <div>
              <h3 className="text-lg font-medium">Payment Information</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Select your payment method
              </p>

              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="credit_card" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            <CreditCard className="h-4 w-4 inline mr-2" />
                            Credit Card
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="paypal" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            <svg className="h-4 w-4 inline mr-2" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.59 3.025-2.566 4.643-5.783 4.643h-2.189c-.262 0-.524.143-.589.382l-1.188 7.46h3.213c.44 0 .816-.318.884-.752l.036-.199.732-4.644.047-.254a.895.895 0 0 1 .884-.752h.556c3.594 0 6.414-1.46 7.235-5.687.34-1.778.164-3.26-.19-4.32a2.222 2.222 0 0 0-.262-.59h-.008z" />
                            </svg>
                            PayPal
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="crypto" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            <svg className="h-4 w-4 inline mr-2" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M23.638 14.904c-1.602 6.43-8.113 10.34-14.542 8.736C2.67 22.05-1.244 15.525.362 9.105 1.962 2.67 8.475-1.243 14.9.358c6.43 1.605 10.342 8.115 8.738 14.548v-.002zm-6.35-4.613c.24-1.59-.974-2.45-2.64-3.03l.54-2.153-1.315-.33-.525 2.107c-.345-.087-.705-.167-1.064-.25l.526-2.127-1.32-.33-.54 2.165c-.285-.067-.565-.132-.84-.2l-1.815-.45-.35 1.407s.975.225.955.236c.535.136.63.486.615.766l-1.477 5.92c-.075.166-.24.406-.614.314.015.02-.96-.24-.96-.24l-.66 1.51 1.71.426.93.242-.54 2.19 1.32.327.54-2.17c.36.1.705.19 1.05.273l-.51 2.154 1.32.33.545-2.19c2.24.427 3.93.257 4.64-1.774.57-1.637-.03-2.58-1.217-3.196.854-.193 1.5-.76 1.68-1.93h.01zm-3.01 4.22c-.404 1.64-3.157.75-4.05.53l.72-2.9c.896.23 3.757.67 3.33 2.37zm.41-4.24c-.37 1.49-2.662.735-3.405.55l.654-2.64c.744.18 3.137.524 2.75 2.084v.006z" />
                            </svg>
                            Cryptocurrency
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="rounded-md border p-4 bg-muted/50">
              <div className="text-lg font-medium mb-2">Order Summary</div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>Base VM ({template.name})</span>
                  <span>${template.price.toFixed(2)}/mo</span>
                </div>
                {cpu !== template.specs.cpu && (
                  <div className="flex justify-between">
                    <span>CPU Adjustment ({cpu - template.specs.cpu} cores)</span>
                    <span>${((cpu - template.specs.cpu) * 5).toFixed(2)}/mo</span>
                  </div>
                )}
                {memory !== template.specs.memory && (
                  <div className="flex justify-between">
                    <span>Memory Adjustment ({memory - template.specs.memory} GB)</span>
                    <span>${((memory - template.specs.memory) * 2).toFixed(2)}/mo</span>
                  </div>
                )}
                {storage !== template.specs.storage && (
                  <div className="flex justify-between">
                    <span>Storage Adjustment ({storage - template.specs.storage} GB)</span>
                    <span>${((storage - template.specs.storage) * 0.1).toFixed(2)}/mo</span>
                  </div>
                )}
                {backups && (
                  <div className="flex justify-between">
                    <span>Daily Backups</span>
                    <span>${(totalPrice * 0.2).toFixed(2)}/mo</span>
                  </div>
                )}
                <Separator className="my-2" />
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>${totalPrice.toFixed(2)}/mo</span>
                </div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="agreeTerms"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>I agree to the terms and conditions</FormLabel>
                    <FormDescription>
                      By checking this box, you agree to our <a href="#" className="text-primary underline">Terms of Service</a> and <a href="#" className="text-primary underline">Privacy Policy</a>
                    </FormDescription>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button variant="outline" onClick={onCancel} type="button" disabled={isSubmitting || isLoading}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting || isLoading}>
            {isSubmitting || isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>Purchase VM</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
