"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check } from "lucide-react"
import { cn } from "@/lib/utils"

interface PricingPlan {
  id: string
  name: string
  description: string
  price: number
  features: string[]
  popular: boolean
}

interface VMPricingPlansProps {
  plans: PricingPlan[]
  onSelect: (plan: any) => void
}

export function VMPricingPlans({ plans, onSelect }: VMPricingPlansProps) {
  // Convert pricing plan to template format for consistency
  const convertPlanToTemplate = (plan: PricingPlan) => {
    // Parse features to extract specs
    const cpuFeature = plan.features.find(f => f.includes("CPU"));
    const ramFeature = plan.features.find(f => f.includes("RAM"));
    const storageFeature = plan.features.find(f => f.includes("Storage"));
    const bandwidthFeature = plan.features.find(f => f.includes("Bandwidth"));
    
    // Extract CPU cores
    const cpuCores = cpuFeature ? parseInt(cpuFeature.match(/\d+/)?.[0] || "2") : 2;
    
    // Extract RAM
    const ramGB = ramFeature ? parseInt(ramFeature.match(/\d+/)?.[0] || "4") : 4;
    
    // Extract storage
    const storageGB = storageFeature ? parseInt(storageFeature.match(/\d+/)?.[0] || "50") : 50;
    
    // Extract bandwidth
    const bandwidth = bandwidthFeature || "1 TB";
    
    return {
      id: plan.id,
      name: `${plan.name} VM`,
      description: plan.description,
      icon: "server",
      specs: {
        cpu: cpuCores,
        memory: ramGB,
        storage: storageGB,
        bandwidth: bandwidth.replace(/.*?(\d+\s*TB).*/, "$1")
      },
      price: plan.price,
      popular: plan.popular,
      category: "plan"
    };
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {plans.map((plan) => (
        <Card 
          key={plan.id}
          className={cn(
            "flex flex-col",
            plan.popular && "border-primary shadow-md"
          )}
        >
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>{plan.name}</CardTitle>
                <CardDescription className="mt-1.5">{plan.description}</CardDescription>
              </div>
              {plan.popular && (
                <Badge className="bg-primary">Popular</Badge>
              )}
            </div>
          </CardHeader>
          <CardContent className="flex-1">
            <div className="mb-4">
              <span className="text-3xl font-bold">${plan.price}</span>
              <span className="text-muted-foreground ml-1">/month</span>
            </div>
            
            <ul className="space-y-2">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button 
              className="w-full" 
              variant={plan.popular ? "default" : "outline"}
              onClick={() => onSelect(convertPlanToTemplate(plan))}
            >
              Select Plan
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
