/**
 * Enhanced Project Dashboard
 * 
 * Displays project analytics, AI capabilities, and real-time collaboration features
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { EnhancedWorkspaceIntegration } from './enhanced-workspace-integration';
import { EnhancedFileBrowser } from './enhanced-file-browser';
import { 
  Brain, 
  Code, 
  FileText, 
  GitBranch, 
  Users, 
  Zap, 
  BarChart3, 
  Shield, 
  Rocket,
  Bot,
  Sparkles,
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';

interface ProjectStats {
  totalFiles: number;
  totalLines: number;
  languages: Record<string, number>;
  lastModified: Date;
  aiGeneratedFiles: number;
  collaborators: number;
}

interface AICapabilities {
  contextAnalysis: boolean;
  smartGeneration: boolean;
  conflictResolution: boolean;
  backupManagement: boolean;
  realTimeCollab: boolean;
}

interface ProjectHealth {
  overall: number;
  structure: number;
  dependencies: number;
  performance: number;
  security: number;
}

export function EnhancedProjectDashboard({ projectId }: { projectId: string }) {
  const [stats, setStats] = useState<ProjectStats>({
    totalFiles: 0,
    totalLines: 0,
    languages: {},
    lastModified: new Date(),
    aiGeneratedFiles: 0,
    collaborators: 1
  });

  const [capabilities] = useState<AICapabilities>({
    contextAnalysis: true,
    smartGeneration: true,
    conflictResolution: true,
    backupManagement: true,
    realTimeCollab: true
  });

  const [health] = useState<ProjectHealth>({
    overall: 87,
    structure: 85,
    dependencies: 90,
    performance: 78,
    security: 95
  });

  const [recentActivity, setRecentActivity] = useState([
    {
      id: '1',
      type: 'ai_generation',
      description: 'Generated Login component with TypeScript',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      user: 'AI Assistant',
      icon: Bot
    },
    {
      id: '2',
      type: 'file_edit',
      description: 'Updated package.json dependencies',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      user: 'You',
      icon: FileText
    },
    {
      id: '3',
      type: 'analysis',
      description: 'Analyzed codebase patterns and conventions',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      user: 'AI Assistant',
      icon: Brain
    }
  ]);

  useEffect(() => {
    // Simulate loading project stats
    const loadStats = async () => {
      // In real implementation, this would fetch from the enhanced filesystem
      setStats({
        totalFiles: 24,
        totalLines: 1847,
        languages: {
          'TypeScript': 65,
          'JavaScript': 20,
          'CSS': 10,
          'JSON': 5
        },
        lastModified: new Date(),
        aiGeneratedFiles: 8,
        collaborators: 1
      });
    };

    loadStats();
  }, [projectId]);

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthBgColor = (score: number) => {
    if (score >= 90) return 'bg-green-100';
    if (score >= 70) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Project Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Enhanced AI-powered development environment
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            <Sparkles className="w-3 h-3 mr-1" />
            AI Enhanced
          </Badge>
          <Badge variant="outline">
            <Users className="w-3 h-3 mr-1" />
            {stats.collaborators} Active
          </Badge>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Files</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.totalFiles}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Lines of Code</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.totalLines.toLocaleString()}</p>
              </div>
              <Code className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">AI Generated</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.aiGeneratedFiles}</p>
              </div>
              <Bot className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Health Score</p>
                <p className={`text-2xl font-bold ${getHealthColor(health.overall)}`}>{health.overall}%</p>
              </div>
              <Target className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="files">Files</TabsTrigger>
          <TabsTrigger value="ai-capabilities">AI Capabilities</TabsTrigger>
          <TabsTrigger value="health">Project Health</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
          <TabsTrigger value="integration">Integration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Language Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Language Distribution
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(stats.languages).map(([lang, percentage]) => (
                  <div key={lang} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="font-medium">{lang}</span>
                      <span className="text-gray-600">{percentage}%</span>
                    </div>
                    <Progress value={percentage} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <Brain className="w-4 h-4 mr-2" />
                  Analyze Codebase
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Code className="w-4 h-4 mr-2" />
                  Generate Component
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Rocket className="w-4 h-4 mr-2" />
                  Optimize Structure
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <GitBranch className="w-4 h-4 mr-2" />
                  Create Template
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="files" className="space-y-4">
          <div className="h-[600px]">
            <EnhancedFileBrowser
              projectId={projectId}
              onFileSelect={(file) => {
                console.log('Selected file:', file);
                // Handle file selection - could open in editor, show preview, etc.
              }}
              showActions={true}
            />
          </div>
        </TabsContent>

        <TabsContent value="ai-capabilities" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(capabilities).map(([key, enabled]) => {
              const capabilityInfo = {
                contextAnalysis: {
                  title: 'Context Analysis',
                  description: 'Analyzes existing code patterns and conventions',
                  icon: Brain
                },
                smartGeneration: {
                  title: 'Smart Generation',
                  description: 'Generates contextually appropriate code',
                  icon: Sparkles
                },
                conflictResolution: {
                  title: 'Conflict Resolution',
                  description: 'Automatically resolves file conflicts',
                  icon: Shield
                },
                backupManagement: {
                  title: 'Backup Management',
                  description: 'Creates backups before destructive operations',
                  icon: Clock
                },
                realTimeCollab: {
                  title: 'Real-time Collaboration',
                  description: 'Multi-user collaboration with AI agents',
                  icon: Users
                }
              }[key];

              if (!capabilityInfo) return null;

              const Icon = capabilityInfo.icon;

              return (
                <Card key={key} className={enabled ? 'border-green-200' : 'border-gray-200'}>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-lg ${enabled ? 'bg-green-100' : 'bg-gray-100'}`}>
                        <Icon className={`w-5 h-5 ${enabled ? 'text-green-600' : 'text-gray-400'}`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-sm">{capabilityInfo.title}</h3>
                          {enabled && <CheckCircle className="w-4 h-4 text-green-600" />}
                        </div>
                        <p className="text-xs text-gray-600 mt-1">{capabilityInfo.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Health Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(health).filter(([key]) => key !== 'overall').map(([key, score]) => (
                  <div key={key} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                      <span className={`font-bold ${getHealthColor(score)}`}>{score}%</span>
                    </div>
                    <Progress value={score} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="w-5 h-5" />
                  Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                  <span className="text-sm">Implement proper error boundaries</span>
                </div>
                <div className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
                  <span className="text-sm">Add comprehensive testing coverage</span>
                </div>
                <div className="flex items-start gap-2">
                  <Info className="w-4 h-4 text-blue-600 mt-0.5" />
                  <span className="text-sm">Set up proper CI/CD pipeline</span>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                  <span className="text-sm">Configure environment variables</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Latest actions and AI-generated content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => {
                  const Icon = activity.icon;
                  return (
                    <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                      <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900">
                        <Icon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.description}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-gray-600 dark:text-gray-400">
                            by {activity.user}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatTimeAgo(activity.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integration" className="space-y-4">
          <EnhancedWorkspaceIntegration
            projectId={projectId}
            onToolAction={(action, data) => {
              console.log('Tool action:', action, data);
              // Handle tool actions from the integration component
            }}
            onFileChange={(path, content) => {
              console.log('File change:', path, content);
              // Handle file changes from the integration component
            }}
            onCollaborationEvent={(event) => {
              console.log('Collaboration event:', event);
              // Handle collaboration events from the integration component
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
