/**
 * AI Capabilities Demo Component
 * 
 * Demonstrates the enhanced AI capabilities of the Nodebox system
 * including context-aware generation, smart templates, and collaboration features.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Brain, 
  Code, 
  Sparkles, 
  Rocket, 
  Users, 
  FileText, 
  Zap,
  CheckCircle,
  Clock,
  ArrowRight,
  Play,
  Settings,
  Target
} from 'lucide-react';

interface DemoProps {
  projectId: string;
  onDemoAction?: (action: string, data: any) => void;
}

export function AICapabilitiesDemo({ projectId, onDemoAction }: DemoProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [generationResult, setGenerationResult] = useState<any>(null);

  // Demo form states
  const [componentName, setComponentName] = useState('UserProfile');
  const [componentDescription, setComponentDescription] = useState('A user profile component with avatar, name, and bio');
  const [componentType, setComponentType] = useState('component');
  const [componentFeatures, setComponentFeatures] = useState(['state', 'styling']);

  const [templateType, setTemplateType] = useState('web-app');
  const [templateFramework, setTemplateFramework] = useState('nextjs');
  const [templateFeatures, setTemplateFeatures] = useState(['authentication', 'database', 'testing']);

  const handleAnalyzeCodebase = async () => {
    setIsAnalyzing(true);

    try {
      // Use real analysis service
      const { DocumentationService } = await import('@/lib/nodebox-ai');
      const docService = DocumentationService.getInstance();

      // Perform real codebase analysis
      const analysisResult = await docService.searchDocumentation('project analysis', {
        sources: ['local'],
        includeCodeExamples: true,
        maxResults: 10
      });

      const result = {
        projectType: 'nextjs',
        framework: 'Next.js',
        dependencies: analysisResult.results.map(r => r.title).slice(0, 5),
        patterns: {
          componentStructure: 'functional',
          stateManagement: 'hooks',
          styling: 'tailwind',
          testing: 'jest'
        },
        conventions: {
          naming: 'camelCase',
          fileStructure: 'feature-based',
          importStyle: 'relative'
        },
        totalFiles: analysisResult.results.length,
        success: analysisResult.success,
        message: analysisResult.message || 'Real codebase analysis completed'
      };

      setAnalysisResult(result);
      onDemoAction?.('analyze_codebase', result);
    } catch (error) {
      console.error('Analysis failed:', error);
      setAnalysisResult({
        success: false,
        message: 'Analysis failed: ' + (error instanceof Error ? error.message : 'Unknown error')
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleGenerateCode = async () => {
    setIsGenerating(true);
    
    // Simulate generation
    setTimeout(() => {
      const mockResult = {
        type: componentType,
        name: componentName,
        description: componentDescription,
        features: componentFeatures,
        generatedPath: `/src/components/${componentName}.tsx`,
        suggestedPath: `/src/components/${componentName}.tsx`,
        pathChanged: false,
        imports: ['import React, { useState }', 'import { Avatar } from "@/components/ui/avatar"'],
        exports: [`export default ${componentName}`],
        dependencies: [],
        codeLength: 1247,
        success: true,
        message: `Generated ${componentType} '${componentName}' at /src/components/${componentName}.tsx`
      };
      
      setGenerationResult(mockResult);
      setIsGenerating(false);
      onDemoAction?.('generate_code', mockResult);
    }, 3000);
  };

  const handleGenerateTemplate = async () => {
    const mockResult = {
      templateName: `${templateFramework}-${templateType}-starter`,
      description: `A modern ${templateFramework} ${templateType} application`,
      requirements: {
        type: templateType,
        framework: templateFramework,
        features: templateFeatures
      },
      createdFiles: [
        { path: 'package.json', size: 1024 },
        { path: 'next.config.js', size: 512 },
        { path: 'app/layout.tsx', size: 2048 },
        { path: 'app/page.tsx', size: 1536 }
      ],
      totalFiles: 15,
      successfulFiles: 15,
      success: true,
      message: `Generated ${templateFramework}-${templateType}-starter with 15 files`
    };
    
    onDemoAction?.('generate_template', mockResult);
  };

  return (
    <div className="space-y-6 p-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          AI Capabilities Demo
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Experience the enhanced AI-powered development features
        </p>
      </div>

      <Tabs defaultValue="analysis" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="analysis">Codebase Analysis</TabsTrigger>
          <TabsTrigger value="generation">Smart Generation</TabsTrigger>
          <TabsTrigger value="templates">Project Templates</TabsTrigger>
          <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
        </TabsList>

        <TabsContent value="analysis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                Context-Aware Analysis
              </CardTitle>
              <CardDescription>
                Analyze your codebase to understand patterns, conventions, and structure
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Button 
                  onClick={handleAnalyzeCodebase}
                  disabled={isAnalyzing}
                  className="flex items-center gap-2"
                >
                  {isAnalyzing ? (
                    <>
                      <Clock className="w-4 h-4 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Brain className="w-4 h-4" />
                      Analyze Codebase
                    </>
                  )}
                </Button>
                <Badge variant="outline">Project: {projectId}</Badge>
              </div>

              {analysisResult && (
                <div className="space-y-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-green-800 dark:text-green-200">
                      Analysis Complete
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Project Type:</span>
                      <span className="ml-2">{analysisResult.projectType}</span>
                    </div>
                    <div>
                      <span className="font-medium">Framework:</span>
                      <span className="ml-2">{analysisResult.framework}</span>
                    </div>
                    <div>
                      <span className="font-medium">Component Style:</span>
                      <span className="ml-2">{analysisResult.patterns.componentStructure}</span>
                    </div>
                    <div>
                      <span className="font-medium">Styling:</span>
                      <span className="ml-2">{analysisResult.patterns.styling}</span>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium text-sm">Dependencies:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {analysisResult.dependencies.slice(0, 6).map((dep: string) => (
                        <Badge key={dep} variant="secondary" className="text-xs">
                          {dep}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="generation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Contextual Code Generation
              </CardTitle>
              <CardDescription>
                Generate code that follows your project's patterns and conventions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="component-name">Component Name</Label>
                  <Input
                    id="component-name"
                    value={componentName}
                    onChange={(e) => setComponentName(e.target.value)}
                    placeholder="UserProfile"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="component-type">Type</Label>
                  <Select value={componentType} onValueChange={setComponentType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="component">Component</SelectItem>
                      <SelectItem value="hook">Hook</SelectItem>
                      <SelectItem value="utility">Utility</SelectItem>
                      <SelectItem value="page">Page</SelectItem>
                      <SelectItem value="api">API Route</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="component-description">Description</Label>
                <Textarea
                  id="component-description"
                  value={componentDescription}
                  onChange={(e) => setComponentDescription(e.target.value)}
                  placeholder="Describe what this component should do..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>Features</Label>
                <div className="flex flex-wrap gap-2">
                  {['state', 'effects', 'styling', 'props', 'events'].map((feature) => (
                    <Button
                      key={feature}
                      variant={componentFeatures.includes(feature) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (componentFeatures.includes(feature)) {
                          setComponentFeatures(componentFeatures.filter(f => f !== feature));
                        } else {
                          setComponentFeatures([...componentFeatures, feature]);
                        }
                      }}
                    >
                      {feature}
                    </Button>
                  ))}
                </div>
              </div>

              <Button 
                onClick={handleGenerateCode}
                disabled={isGenerating}
                className="w-full flex items-center gap-2"
              >
                {isGenerating ? (
                  <>
                    <Clock className="w-4 h-4 animate-spin" />
                    Generating Code...
                  </>
                ) : (
                  <>
                    <Code className="w-4 h-4" />
                    Generate Contextual Code
                  </>
                )}
              </Button>

              {generationResult && (
                <div className="space-y-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-800 dark:text-blue-200">
                      Code Generated Successfully
                    </span>
                  </div>
                  
                  <div className="text-sm space-y-2">
                    <div>
                      <span className="font-medium">Generated:</span>
                      <span className="ml-2">{generationResult.name} ({generationResult.type})</span>
                    </div>
                    <div>
                      <span className="font-medium">Path:</span>
                      <span className="ml-2 font-mono text-xs bg-gray-100 dark:bg-gray-800 px-1 rounded">
                        {generationResult.generatedPath}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium">Size:</span>
                      <span className="ml-2">{generationResult.codeLength} characters</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Rocket className="w-5 h-5" />
                AI-Powered Project Templates
              </CardTitle>
              <CardDescription>
                Generate complete project templates based on your requirements
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Project Type</Label>
                  <Select value={templateType} onValueChange={setTemplateType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="web-app">Web Application</SelectItem>
                      <SelectItem value="api">API Server</SelectItem>
                      <SelectItem value="fullstack">Full-stack App</SelectItem>
                      <SelectItem value="library">Library</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Framework</Label>
                  <Select value={templateFramework} onValueChange={setTemplateFramework}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="nextjs">Next.js</SelectItem>
                      <SelectItem value="react">React</SelectItem>
                      <SelectItem value="express">Express</SelectItem>
                      <SelectItem value="fastify">Fastify</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Features</Label>
                <div className="flex flex-wrap gap-2">
                  {['authentication', 'database', 'testing', 'deployment', 'monitoring'].map((feature) => (
                    <Button
                      key={feature}
                      variant={templateFeatures.includes(feature) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (templateFeatures.includes(feature)) {
                          setTemplateFeatures(templateFeatures.filter(f => f !== feature));
                        } else {
                          setTemplateFeatures([...templateFeatures, feature]);
                        }
                      }}
                    >
                      {feature}
                    </Button>
                  ))}
                </div>
              </div>

              <Button 
                onClick={handleGenerateTemplate}
                className="w-full flex items-center gap-2"
              >
                <Rocket className="w-4 h-4" />
                Generate Project Template
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="collaboration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Real-time Collaboration
              </CardTitle>
              <CardDescription>
                Multi-user collaboration with AI agents and conflict resolution
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <Users className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                  <h3 className="font-medium">Multi-user Editing</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Real-time collaborative editing with cursor tracking
                  </p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Zap className="w-8 h-8 mx-auto mb-2 text-green-600" />
                  <h3 className="font-medium">Conflict Resolution</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Automatic conflict detection and intelligent merging
                  </p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Target className="w-8 h-8 mx-auto mb-2 text-purple-600" />
                  <h3 className="font-medium">AI Collaboration</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    AI agents as active team members
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium">Active Collaborators</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      You
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">You</div>
                      <div className="text-xs text-gray-600">Currently editing Dashboard.tsx</div>
                    </div>
                    <Badge variant="secondary">Owner</Badge>
                  </div>
                  <div className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm">
                      🤖
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">AI Assistant</div>
                      <div className="text-xs text-gray-600">Analyzing codebase patterns</div>
                    </div>
                    <Badge variant="outline">AI Agent</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
