/**
 * Enhanced File Browser
 * 
 * File browser component that reads from the enhanced nodebox filesystem
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  Folder, 
  File, 
  FolderOpen,
  FileText,
  FileCode,
  FileJson,
  FileImage,
  FileCog,
  RefreshCw,
  Search,
  Plus,
  FolderPlus,
  FilePlus,
  Trash2,
  Download,
  Eye,
  Edit,
  Copy,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { FileSystemEntry } from '@/lib/api/enhanced-nodebox-filesystem';

interface EnhancedFileBrowserProps {
  projectId: string;
  className?: string;
  onFileSelect?: (file: FileSystemEntry) => void;
  showActions?: boolean;
}

export function EnhancedFileBrowser({
  projectId,
  className,
  onFileSelect,
  showActions = true
}: EnhancedFileBrowserProps) {
  const [files, setFiles] = useState<FileSystemEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFile, setSelectedFile] = useState<FileSystemEntry | null>(null);
  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set(['/']));

  // Load files from enhanced filesystem
  const loadFiles = useCallback(async () => {
    if (!projectId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Call the enhanced filesystem API directly
      const response = await fetch('/api/nodebox-ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get_project_info',
          projectId
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // The enhanced filesystem should return file entries
      if (data.files && Array.isArray(data.files)) {
        setFiles(data.files);
      } else {
        // Fallback: try to get files from filesystem stats
        const statsResponse = await fetch('/api/nodebox-ai', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'get_filesystem_stats',
            projectId
          }),
        });

        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          if (statsData.entries && Array.isArray(statsData.entries)) {
            setFiles(statsData.entries);
          } else {
            setFiles([]);
          }
        } else {
          setFiles([]);
        }
      }
    } catch (err) {
      console.error('Error loading files:', err);
      setError(err instanceof Error ? err.message : 'Failed to load files');
      setFiles([]);
    } finally {
      setIsLoading(false);
    }
  }, [projectId]);

  // Load files on mount and when projectId changes
  useEffect(() => {
    loadFiles();
  }, [loadFiles]);

  // Filter files based on search query
  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    file.path.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get file icon based on type and extension
  const getFileIcon = (file: FileSystemEntry) => {
    if (file.type === 'directory') {
      return expandedDirs.has(file.path) ? 
        <FolderOpen className="h-4 w-4 text-blue-500" /> : 
        <Folder className="h-4 w-4 text-blue-500" />;
    }

    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <FileCode className="h-4 w-4 text-yellow-500" />;
      case 'json':
        return <FileJson className="h-4 w-4 text-green-500" />;
      case 'md':
      case 'txt':
        return <FileText className="h-4 w-4 text-gray-500" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <FileImage className="h-4 w-4 text-purple-500" />;
      case 'config':
      case 'conf':
        return <FileCog className="h-4 w-4 text-orange-500" />;
      default:
        return <File className="h-4 w-4 text-gray-400" />;
    }
  };

  // Toggle directory expansion
  const toggleDirectory = (path: string) => {
    setExpandedDirs(prev => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  // Handle file selection
  const handleFileSelect = (file: FileSystemEntry) => {
    setSelectedFile(file);
    onFileSelect?.(file);
  };

  // Build file tree structure
  const buildFileTree = (files: FileSystemEntry[]) => {
    const tree: { [key: string]: FileSystemEntry[] } = {};
    
    files.forEach(file => {
      const parentPath = file.parentPath || '/';
      if (!tree[parentPath]) {
        tree[parentPath] = [];
      }
      tree[parentPath].push(file);
    });

    return tree;
  };

  // Render file tree recursively
  const renderFileTree = (files: FileSystemEntry[], level = 0) => {
    const tree = buildFileTree(files);
    
    return files
      .filter(file => level === 0 ? file.parentPath === '/' : true)
      .sort((a, b) => {
        // Directories first, then files
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
      })
      .map(file => (
        <div key={file.path}>
          <div
            className={cn(
              "flex items-center gap-2 p-1 rounded cursor-pointer hover:bg-muted/50 transition-colors",
              selectedFile?.path === file.path && "bg-muted",
              "group"
            )}
            style={{ paddingLeft: `${level * 16 + 8}px` }}
            onClick={() => {
              if (file.type === 'directory') {
                toggleDirectory(file.path);
              } else {
                handleFileSelect(file);
              }
            }}
          >
            {file.type === 'directory' && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDirectory(file.path);
                }}
              >
                {expandedDirs.has(file.path) ? 
                  <ChevronDown className="h-3 w-3" /> : 
                  <ChevronRight className="h-3 w-3" />
                }
              </Button>
            )}
            {getFileIcon(file)}
            <span className="text-sm flex-1 truncate">{file.name}</span>
            {file.type === 'file' && file.size && (
              <Badge variant="outline" className="text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                {(file.size / 1024).toFixed(1)}KB
              </Badge>
            )}
          </div>
          
          {/* Render children if directory is expanded */}
          {file.type === 'directory' && expandedDirs.has(file.path) && tree[file.path] && (
            <div>
              {renderFileTree(tree[file.path], level + 1)}
            </div>
          )}
        </div>
      ));
  };

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Project Files</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={loadFiles}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-full">
          <div className="p-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8 text-muted-foreground">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                Loading files...
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="text-red-500 mb-2">Error loading files</div>
                <div className="text-sm text-muted-foreground mb-4">{error}</div>
                <Button variant="outline" size="sm" onClick={loadFiles}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            ) : filteredFiles.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
                <File className="h-12 w-12 mb-2 opacity-50" />
                <p>No files found</p>
                <p className="text-sm">
                  {searchQuery ? 'Try a different search term' : 'Create some files to get started'}
                </p>
              </div>
            ) : (
              <div className="space-y-1">
                {renderFileTree(filteredFiles)}
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
