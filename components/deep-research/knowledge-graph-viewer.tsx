/**
 * Knowledge Graph Viewer Component
 * 
 * Interactive visualization of research entities and their relationships
 */

"use client"

import * as React from "react"
import { useState, useEffect, useRef } from "react"
import { cn } from "@/lib/utils"
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Download,
  Settings,
  Filter,
  Search,
  Users,
  Building,
  MapPin,
  Lightbulb,
  Link,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

import { useResearchInsights } from "@/hooks/deep-research"

interface KnowledgeGraphViewerProps {
  projectId: string
  className?: string
}

interface GraphNode {
  id: string
  name: string
  type: 'person' | 'organization' | 'location' | 'concept'
  confidence: number
  count: number
  x?: number
  y?: number
}

interface GraphEdge {
  id: string
  source: string
  target: string
  type: 'related' | 'mentions' | 'located_in' | 'works_for'
  strength: number
}

interface GraphData {
  nodes: GraphNode[]
  edges: GraphEdge[]
}

export function KnowledgeGraphViewer({
  projectId,
  className
}: KnowledgeGraphViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], edges: [] })
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [filterType, setFilterType] = useState<string>("all")
  const [minConfidence, setMinConfidence] = useState([0.5])
  const [showLabels, setShowLabels] = useState(true)
  const [showEdges, setShowEdges] = useState(true)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [zoom, setZoom] = useState(1)
  const [pan, setPan] = useState({ x: 0, y: 0 })

  const { getTopEntities } = useResearchInsights(projectId)

  // Convert research entities to graph data
  useEffect(() => {
    const entities = getTopEntities(50)
    
    const nodes: GraphNode[] = entities.map((entity, index) => ({
      id: entity.name.toLowerCase().replace(/\s+/g, '_'),
      name: entity.name,
      type: entity.type as any,
      confidence: entity.confidence,
      count: entity.count || 1,
      x: Math.random() * 800,
      y: Math.random() * 600
    }))

    // Generate synthetic relationships for demo
    const edges: GraphEdge[] = []
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < Math.min(nodes.length, i + 3); j++) {
        if (Math.random() > 0.7) {
          edges.push({
            id: `${nodes[i].id}-${nodes[j].id}`,
            source: nodes[i].id,
            target: nodes[j].id,
            type: 'related',
            strength: Math.random() * 0.8 + 0.2
          })
        }
      }
    }

    setGraphData({ nodes, edges })
  }, [getTopEntities])

  // Filter nodes based on search and filters
  const filteredNodes = React.useMemo(() => {
    return graphData.nodes.filter(node => {
      const matchesSearch = node.name.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesType = filterType === "all" || node.type === filterType
      const matchesConfidence = node.confidence >= minConfidence[0]
      return matchesSearch && matchesType && matchesConfidence
    })
  }, [graphData.nodes, searchQuery, filterType, minConfidence])

  // Filter edges based on visible nodes
  const filteredEdges = React.useMemo(() => {
    const visibleNodeIds = new Set(filteredNodes.map(n => n.id))
    return graphData.edges.filter(edge => 
      visibleNodeIds.has(edge.source) && visibleNodeIds.has(edge.target)
    )
  }, [graphData.edges, filteredNodes])

  // Canvas drawing
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const container = containerRef.current
    if (container) {
      canvas.width = container.clientWidth
      canvas.height = container.clientHeight
    }

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    ctx.save()

    // Apply zoom and pan
    ctx.translate(pan.x, pan.y)
    ctx.scale(zoom, zoom)

    // Draw edges
    if (showEdges) {
      filteredEdges.forEach(edge => {
        const sourceNode = filteredNodes.find(n => n.id === edge.source)
        const targetNode = filteredNodes.find(n => n.id === edge.target)
        
        if (sourceNode && targetNode) {
          ctx.beginPath()
          ctx.moveTo(sourceNode.x!, sourceNode.y!)
          ctx.lineTo(targetNode.x!, targetNode.y!)
          ctx.strokeStyle = `rgba(100, 100, 100, ${edge.strength * 0.5})`
          ctx.lineWidth = edge.strength * 2
          ctx.stroke()
        }
      })
    }

    // Draw nodes
    filteredNodes.forEach(node => {
      const radius = Math.max(5, node.count * 2 + node.confidence * 10)
      
      // Node circle
      ctx.beginPath()
      ctx.arc(node.x!, node.y!, radius, 0, 2 * Math.PI)
      
      // Color based on type
      let color = '#6b7280'
      switch (node.type) {
        case 'person':
          color = '#3b82f6'
          break
        case 'organization':
          color = '#10b981'
          break
        case 'location':
          color = '#f59e0b'
          break
        case 'concept':
          color = '#8b5cf6'
          break
      }
      
      ctx.fillStyle = color
      ctx.fill()
      
      // Highlight selected node
      if (selectedNode?.id === node.id) {
        ctx.strokeStyle = '#ffffff'
        ctx.lineWidth = 3
        ctx.stroke()
      }

      // Draw labels
      if (showLabels && zoom > 0.5) {
        ctx.fillStyle = '#ffffff'
        ctx.font = `${Math.max(10, 12 * zoom)}px sans-serif`
        ctx.textAlign = 'center'
        ctx.fillText(node.name, node.x!, node.y! + radius + 15)
      }
    })

    ctx.restore()
  }, [filteredNodes, filteredEdges, selectedNode, showLabels, showEdges, zoom, pan])

  // Handle canvas click
  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = (event.clientX - rect.left - pan.x) / zoom
    const y = (event.clientY - rect.top - pan.y) / zoom

    // Find clicked node
    const clickedNode = filteredNodes.find(node => {
      const radius = Math.max(5, node.count * 2 + node.confidence * 10)
      const distance = Math.sqrt((x - node.x!) ** 2 + (y - node.y!) ** 2)
      return distance <= radius
    })

    setSelectedNode(clickedNode || null)
  }

  const getNodeTypeIcon = (type: string) => {
    switch (type) {
      case 'person':
        return <Users className="h-4 w-4" />
      case 'organization':
        return <Building className="h-4 w-4" />
      case 'location':
        return <MapPin className="h-4 w-4" />
      case 'concept':
        return <Lightbulb className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const handleZoomIn = () => setZoom(prev => Math.min(prev * 1.2, 3))
  const handleZoomOut = () => setZoom(prev => Math.max(prev / 1.2, 0.3))
  const handleReset = () => {
    setZoom(1)
    setPan({ x: 0, y: 0 })
    setSelectedNode(null)
  }

  const exportGraph = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const link = document.createElement('a')
    link.download = `knowledge-graph-${projectId}.png`
    link.href = canvas.toDataURL()
    link.click()
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search entities..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="person">People</SelectItem>
              <SelectItem value="organization">Organizations</SelectItem>
              <SelectItem value="location">Locations</SelectItem>
              <SelectItem value="concept">Concepts</SelectItem>
            </SelectContent>
          </Select>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                <Settings className="h-4 w-4" />
                Settings
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Minimum Confidence: {minConfidence[0]}</Label>
                  <Slider
                    value={minConfidence}
                    onValueChange={setMinConfidence}
                    max={1}
                    min={0}
                    step={0.1}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-labels">Show Labels</Label>
                  <Switch
                    id="show-labels"
                    checked={showLabels}
                    onCheckedChange={setShowLabels}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-edges">Show Connections</Label>
                  <Switch
                    id="show-edges"
                    checked={showEdges}
                    onCheckedChange={setShowEdges}
                  />
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleZoomIn}>
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={handleZoomOut}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={handleReset}>
            <RotateCcw className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={exportGraph}>
            <Download className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Graph Container */}
      <div className="flex gap-4">
        <div className="flex-1">
          <Card className={cn(isFullscreen && "fixed inset-4 z-50")}>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Knowledge Graph</CardTitle>
              <CardDescription>
                Interactive visualization of entities and relationships ({filteredNodes.length} entities, {filteredEdges.length} connections)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div
                ref={containerRef}
                className={cn(
                  "relative border rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-900",
                  isFullscreen ? "h-[calc(100vh-200px)]" : "h-96"
                )}
              >
                <canvas
                  ref={canvasRef}
                  onClick={handleCanvasClick}
                  className="cursor-pointer"
                />
                
                {/* Zoom indicator */}
                <div className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm rounded px-2 py-1 text-xs">
                  {Math.round(zoom * 100)}%
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Node Details Panel */}
        {selectedNode && (
          <Card className="w-80">
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                {getNodeTypeIcon(selectedNode.type)}
                Entity Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium">{selectedNode.name}</h3>
                <Badge variant="outline" className="mt-1 capitalize">
                  {selectedNode.type}
                </Badge>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Confidence:</span>
                  <span className="font-medium">
                    {Math.round(selectedNode.confidence * 100)}%
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Mentions:</span>
                  <span className="font-medium">{selectedNode.count}</span>
                </div>
              </div>

              {/* Connected entities */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Connected To:</h4>
                <div className="space-y-1">
                  {filteredEdges
                    .filter(edge => edge.source === selectedNode.id || edge.target === selectedNode.id)
                    .slice(0, 5)
                    .map(edge => {
                      const connectedNodeId = edge.source === selectedNode.id ? edge.target : edge.source
                      const connectedNode = filteredNodes.find(n => n.id === connectedNodeId)
                      return connectedNode ? (
                        <div key={edge.id} className="flex items-center justify-between text-sm">
                          <span className="truncate">{connectedNode.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {Math.round(edge.strength * 100)}%
                          </Badge>
                        </div>
                      ) : null
                    })}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Legend */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-blue-500" />
              <span className="text-sm">People</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-green-500" />
              <span className="text-sm">Organizations</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-yellow-500" />
              <span className="text-sm">Locations</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 rounded-full bg-purple-500" />
              <span className="text-sm">Concepts</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
