# Deep Research UI Components

A comprehensive suite of AI-powered React components for research management and analysis.

## Components Overview

### 🏠 **ResearchDashboard**
Main dashboard for managing research projects with real-time monitoring.

**Features:**
- Project overview with statistics
- Real-time session monitoring
- Search and filtering
- Project creation wizard integration
- Responsive grid layout

**Usage:**
```tsx
import { ResearchDashboard } from '@/components/deep-research'

function App() {
  return <ResearchDashboard />
}
```

### 📋 **ResearchProjectCard**
Individual project card component with status, metrics, and actions.

**Features:**
- Project status indicators
- Progress tracking for active projects
- Source and analysis statistics
- Action menu with CRUD operations
- Confirmation dialogs for destructive actions

**Props:**
```tsx
interface ResearchProjectCardProps {
  project: ResearchProject
  onDelete?: () => void
  onArchive?: () => void
  onActivate?: () => void
  className?: string
}
```

### 🧙‍♂️ **ResearchSessionWizard**
Multi-step wizard for creating research sessions with AI-powered suggestions.

**Features:**
- 5-step guided setup process
- AI-powered template recommendations
- Source configuration with sliders
- Analysis settings customization
- Real-time validation

**Steps:**
1. **Query Definition** - AI-enhanced query builder
2. **Template Selection** - Smart recommendations
3. **Source Configuration** - Customize data sources
4. **Analysis Settings** - Configure AI analysis
5. **Review & Launch** - Final review and execution

### 🤖 **AIQueryBuilder**
Intelligent query builder with AI-powered suggestions and enhancements.

**Features:**
- Real-time AI suggestions
- Keyword extraction
- Query enhancement with AI
- Alternative query recommendations
- Interactive suggestion application

**AI Integration:**
- Uses Vercel AI SDK for real-time enhancements
- Tool calls for query optimization
- Intelligent keyword extraction
- Context-aware suggestions

### 📊 **ResearchProgressMonitor**
Real-time monitoring of research session progress with WebSocket integration.

**Features:**
- Live progress tracking
- Phase-based progress indicators
- Performance metrics
- Error and warning display
- Estimated time remaining
- Compact and full view modes

**Props:**
```tsx
interface ResearchProgressMonitorProps {
  sessionId: string
  className?: string
  compact?: boolean
}
```

### 📖 **ResearchResultsViewer**
Comprehensive viewer for research sources and AI-generated analyses.

**Features:**
- Tabbed interface (Sources, Analyses, Insights)
- Advanced filtering and sorting
- Source credibility indicators
- Analysis confidence scores
- Export and sharing capabilities
- Detailed source information

**Tabs:**
- **Sources** - Collected research sources with metadata
- **Analyses** - AI-generated analysis results
- **Insights** - Processed insights and summaries

### 🕸️ **KnowledgeGraphViewer**
Interactive visualization of research entities and their relationships.

**Features:**
- Canvas-based graph rendering
- Interactive node selection
- Zoom and pan controls
- Entity filtering by type and confidence
- Relationship visualization
- Export capabilities
- Fullscreen mode

**Entity Types:**
- People (blue nodes)
- Organizations (green nodes)
- Locations (yellow nodes)
- Concepts (purple nodes)

### 🧠 **ResearchInsightsDashboard**
AI-powered insights dashboard with summaries, trends, and recommendations.

**Features:**
- AI-generated insights with confidence scores
- Executive summary generation
- Quality analysis metrics
- Custom AI analysis queries
- Interactive insight exploration
- Export and sharing

**Insight Types:**
- **Summary** - Research overviews
- **Trend** - Emerging patterns
- **Recommendation** - Actionable suggestions
- **Warning** - Quality alerts

## AI-Powered Features

### 🔧 **Tool Calls Integration**
Components integrate with Vercel AI SDK for intelligent features:

```tsx
// Query enhancement
const { messages, handleSubmit } = useChat({
  api: '/api/deep-research/enhance-query',
  onFinish: (message) => {
    const response = JSON.parse(message.content)
    // Apply enhancements
  }
})

// Custom insights generation
const { messages, input, handleInputChange } = useChat({
  api: '/api/deep-research/generate-insights'
})
```

### 🎯 **Smart Recommendations**
- Template recommendations based on query analysis
- Source filtering suggestions
- Analysis type recommendations
- Quality improvement suggestions

### 📈 **Real-time Analysis**
- Live progress monitoring via WebSocket
- Streaming AI responses
- Dynamic insight generation
- Automatic quality assessment

## API Integration

### **Query Enhancement**
```typescript
POST /api/deep-research/enhance-query
{
  "query": "machine learning in healthcare"
}
```

### **Suggestions Generation**
```typescript
POST /api/deep-research/query-suggestions
{
  "query": "quantum computing applications"
}
```

### **Keyword Extraction**
```typescript
POST /api/deep-research/extract-keywords
{
  "query": "artificial intelligence ethics"
}
```

### **Custom Insights**
```typescript
POST /api/deep-research/generate-insights
{
  "messages": [
    {
      "role": "user",
      "content": "What are the main challenges in quantum computing?"
    }
  ]
}
```

## Styling and Theming

Components use **shadcn/ui** design system with:
- Consistent color schemes
- Responsive layouts
- Dark/light mode support
- Accessible design patterns
- Smooth animations and transitions

### **Color Coding**
- **Blue** - Active/Running states
- **Green** - Completed/Success states
- **Yellow** - Warning/Pending states
- **Red** - Error/Failed states
- **Purple** - AI/Insights features

## Usage Examples

### **Complete Research Workflow**
```tsx
import {
  ResearchDashboard,
  ResearchSessionWizard,
  ResearchProgressMonitor,
  ResearchResultsViewer,
  ResearchInsightsDashboard
} from '@/components/deep-research'

function ResearchApp() {
  const [currentProject, setCurrentProject] = useState(null)
  const [activeSession, setActiveSession] = useState(null)

  return (
    <div className="space-y-6">
      {/* Main dashboard */}
      <ResearchDashboard />
      
      {/* Active session monitoring */}
      {activeSession && (
        <ResearchProgressMonitor 
          sessionId={activeSession.id}
          compact={false}
        />
      )}
      
      {/* Results viewing */}
      {currentProject && (
        <ResearchResultsViewer projectId={currentProject.id} />
      )}
      
      {/* AI insights */}
      {currentProject && (
        <ResearchInsightsDashboard projectId={currentProject.id} />
      )}
    </div>
  )
}
```

### **Embedded Components**
```tsx
// Compact progress monitor in a sidebar
<ResearchProgressMonitor 
  sessionId={sessionId} 
  compact={true}
  className="w-80"
/>

// Knowledge graph in a modal
<Dialog>
  <DialogContent className="max-w-6xl">
    <KnowledgeGraphViewer projectId={projectId} />
  </DialogContent>
</Dialog>
```

## Performance Considerations

- **Lazy Loading** - Components load data on demand
- **Memoization** - Expensive calculations are memoized
- **Virtual Scrolling** - Large lists use virtual scrolling
- **Debounced Search** - Search inputs are debounced
- **Optimistic Updates** - UI updates optimistically

## Accessibility

- **Keyboard Navigation** - Full keyboard support
- **Screen Reader Support** - ARIA labels and descriptions
- **Color Contrast** - WCAG AA compliant colors
- **Focus Management** - Proper focus handling
- **Semantic HTML** - Meaningful HTML structure

## Browser Support

- **Modern Browsers** - Chrome, Firefox, Safari, Edge
- **Mobile Responsive** - Touch-friendly interfaces
- **Progressive Enhancement** - Graceful degradation
- **WebSocket Fallback** - Polling fallback for real-time features
