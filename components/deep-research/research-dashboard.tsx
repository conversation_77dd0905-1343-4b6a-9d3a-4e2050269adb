/**
 * Research Dashboard Component
 * 
 * Main dashboard for managing research projects with real-time monitoring
 */

"use client"

import * as React from "react"
import { useState } from "react"
import { cn } from "@/lib/utils"
import {
  Search,
  Plus,
  Filter,
  MoreHorizontal,
  Clock,
  Users,
  TrendingUp,
  BookOpen,
  Brain,
  Zap,
  AlertCircle,
  CheckCircle2,
  Loader2
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import { useResearchProjects, useResearchSession } from "@/hooks/deep-research"
import { ResearchProjectCard } from "./research-project-card"
import { ResearchSessionWizard } from "./research-session-wizard"
import { ResearchProgressMonitor } from "./research-progress-monitor"

interface ResearchDashboardProps {
  className?: string
}

export function ResearchDashboard({ className }: ResearchDashboardProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [showCreateWizard, setShowCreateWizard] = useState(false)

  const {
    projects,
    isLoading: projectsLoading,
    error: projectsError,
    createProject,
    deleteProject
  } = useResearchProjects()

  const {
    currentSession,
    isStarting,
    startResearch
  } = useResearchSession()

  // Filter projects based on search and status
  const filteredProjects = React.useMemo(() => {
    return projects.filter(project => {
      const matchesSearch = project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           project.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = selectedStatus === "all" || project.status === selectedStatus
      return matchesSearch && matchesStatus
    })
  }, [projects, searchQuery, selectedStatus])

  // Calculate dashboard statistics
  const stats = React.useMemo(() => {
    const totalProjects = projects.length
    const activeProjects = projects.filter(p => p.status === 'active').length
    const completedProjects = projects.filter(p => p.status === 'completed').length
    const totalSources = projects.reduce((sum, p) => sum + p.metadata.totalSources, 0)
    const totalAnalyses = projects.reduce((sum, p) => sum + p.metadata.totalAnalyses, 0)

    return {
      totalProjects,
      activeProjects,
      completedProjects,
      totalSources,
      totalAnalyses
    }
  }, [projects])

  const handleCreateProject = async (title: string, description: string) => {
    try {
      await createProject(title, description, {
        autoAnalysis: true,
        sourceTypes: ['web', 'academic', 'news'],
        analysisTypes: ['summary', 'insights', 'credibility'],
        maxSources: 30,
        qualityThreshold: 0.7
      })
      setShowCreateWizard(false)
    } catch (error) {
      console.error('Failed to create project:', error)
    }
  }

  const handleDeleteProject = async (projectId: string) => {
    try {
      await deleteProject(projectId)
    } catch (error) {
      console.error('Failed to delete project:', error)
    }
  }

  if (projectsError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Failed to load research projects</p>
          <Button variant="outline" size="sm" className="mt-2">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Research Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your AI-powered research projects and insights
          </p>
        </div>
        <Button onClick={() => setShowCreateWizard(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          New Research
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProjects}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeProjects} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sources Collected</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSources}</div>
            <p className="text-xs text-muted-foreground">
              Across all projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Analyses</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalAnalyses}</div>
            <p className="text-xs text-muted-foreground">
              Generated insights
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedProjects}</div>
            <p className="text-xs text-muted-foreground">
              Research projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.totalProjects > 0 
                ? Math.round((stats.completedProjects / stats.totalProjects) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Project completion
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Active Research Session Monitor */}
      {currentSession && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Active Research Session
            </CardTitle>
            <CardDescription>
              Monitoring progress for: {currentSession.query}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResearchProgressMonitor sessionId={currentSession.id} />
          </CardContent>
        </Card>
      )}

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search research projects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              Status: {selectedStatus === "all" ? "All" : selectedStatus}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setSelectedStatus("all")}>
              All Projects
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSelectedStatus("draft")}>
              Draft
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSelectedStatus("active")}>
              Active
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSelectedStatus("completed")}>
              Completed
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSelectedStatus("archived")}>
              Archived
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Projects Grid */}
      <div className="space-y-4">
        {projectsLoading ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredProjects.length === 0 ? (
          <div className="text-center py-12">
            <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No research projects found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || selectedStatus !== "all" 
                ? "Try adjusting your search or filters"
                : "Get started by creating your first research project"
              }
            </p>
            {!searchQuery && selectedStatus === "all" && (
              <Button onClick={() => setShowCreateWizard(true)} className="gap-2">
                <Plus className="h-4 w-4" />
                Create Research Project
              </Button>
            )}
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredProjects.map((project) => (
              <ResearchProjectCard
                key={project.id}
                project={project}
                onDelete={() => handleDeleteProject(project.id)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Research Session Wizard */}
      {showCreateWizard && (
        <ResearchSessionWizard
          open={showCreateWizard}
          onClose={() => setShowCreateWizard(false)}
          onCreateProject={handleCreateProject}
          onStartResearch={startResearch}
        />
      )}
    </div>
  )
}
