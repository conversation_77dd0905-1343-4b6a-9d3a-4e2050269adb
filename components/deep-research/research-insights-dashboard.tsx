/**
 * Research Insights Dashboard Component
 * 
 * AI-powered insights dashboard with summaries, trends, and recommendations
 */

"use client"

import * as React from "react"
import { useState } from "react"
import { cn } from "@/lib/utils"
import {
  Brain,
  TrendingUp,
  AlertTriangle,
  CheckCircle2,
  Lightbulb,
  Target,
  BarChart3,
  PieChart,
  Download,
  Share,
  RefreshCw,
  Sparkles,
  Eye,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Loader2
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  Dialog<PERSON>ontent,
  DialogDescription,
  <PERSON>alog<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

import { useResearchInsights, useResearchStats } from "@/hooks/deep-research"
import { useChat } from "@ai-sdk/react"

interface ResearchInsightsDashboardProps {
  projectId: string
  className?: string
}

interface AIInsight {
  id: string
  type: 'summary' | 'trend' | 'recommendation' | 'warning'
  title: string
  content: string
  confidence: number
  sources: string[]
  timestamp: string
}

export function ResearchInsightsDashboard({
  projectId,
  className
}: ResearchInsightsDashboardProps) {
  const [selectedInsight, setSelectedInsight] = useState<AIInsight | null>(null)
  const [isGeneratingInsights, setIsGeneratingInsights] = useState(false)
  const [customQuery, setCustomQuery] = useState("")

  const {
    summaries,
    insights,
    biasAnalyses,
    credibilityAnalyses,
    getOverallBiasScore,
    getOverallCredibilityScore,
    getTopEntities,
    getKeyInsights
  } = useResearchInsights(projectId)

  const { sourceStats, analysisStats } = useResearchStats(projectId)

  // AI chat for generating custom insights
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isGeneratingCustomInsight
  } = useChat({
    api: '/api/deep-research/generate-insights',
    onFinish: (message) => {
      // Handle the AI response
      console.log('Generated insight:', message.content)
    }
  })

  // Mock AI insights for demonstration
  const aiInsights: AIInsight[] = [
    {
      id: '1',
      type: 'summary',
      title: 'Research Overview',
      content: 'Based on analysis of 45 sources, the research reveals significant developments in quantum computing with a focus on error correction and scalability. Key players include IBM, Google, and emerging startups.',
      confidence: 0.92,
      sources: ['source1', 'source2', 'source3'],
      timestamp: new Date().toISOString()
    },
    {
      id: '2',
      type: 'trend',
      title: 'Emerging Trend: Quantum Error Correction',
      content: 'There is a growing emphasis on quantum error correction methods, with 78% of recent papers focusing on this area. This represents a 45% increase from the previous year.',
      confidence: 0.87,
      sources: ['source4', 'source5'],
      timestamp: new Date().toISOString()
    },
    {
      id: '3',
      type: 'recommendation',
      title: 'Research Gap Identified',
      content: 'Limited coverage of quantum computing applications in healthcare. Consider expanding research to include medical imaging and drug discovery applications.',
      confidence: 0.75,
      sources: ['source6'],
      timestamp: new Date().toISOString()
    },
    {
      id: '4',
      type: 'warning',
      title: 'Source Bias Alert',
      content: 'Detected potential bias in 23% of sources toward commercial quantum computing solutions. Consider including more academic and neutral perspectives.',
      confidence: 0.81,
      sources: ['source7', 'source8'],
      timestamp: new Date().toISOString()
    }
  ]

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'summary':
        return <BarChart3 className="h-4 w-4" />
      case 'trend':
        return <TrendingUp className="h-4 w-4" />
      case 'recommendation':
        return <Lightbulb className="h-4 w-4" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Brain className="h-4 w-4" />
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'summary':
        return 'text-blue-500'
      case 'trend':
        return 'text-green-500'
      case 'recommendation':
        return 'text-purple-500'
      case 'warning':
        return 'text-yellow-500'
      default:
        return 'text-gray-500'
    }
  }

  const generateCustomInsight = async () => {
    if (!customQuery.trim()) return

    setIsGeneratingInsights(true)
    try {
      // This would call the AI API to generate insights
      await new Promise(resolve => setTimeout(resolve, 2000)) // Mock delay
      console.log('Generating insight for:', customQuery)
    } catch (error) {
      console.error('Failed to generate insight:', error)
    } finally {
      setIsGeneratingInsights(false)
    }
  }

  const exportInsights = () => {
    const data = {
      projectId,
      insights: aiInsights,
      stats: { sourceStats, analysisStats },
      timestamp: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `research-insights-${projectId}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  return (
    <TooltipProvider>
      <div className={cn("space-y-6", className)}>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Research Insights</h2>
            <p className="text-muted-foreground">
              AI-powered analysis and recommendations based on your research data
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={exportInsights} className="gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="gap-2">
              <Share className="h-4 w-4" />
              Share
            </Button>
            <Button variant="outline" size="sm" className="gap-2">
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{aiInsights.length}</div>
              <div className="text-sm text-muted-foreground">AI Insights</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className={cn("text-2xl font-bold", getOverallCredibilityScore() && getOverallCredibilityScore()! > 0.8 ? "text-green-500" : "text-yellow-500")}>
                {getOverallCredibilityScore() ? Math.round(getOverallCredibilityScore()! * 100) : 0}%
              </div>
              <div className="text-sm text-muted-foreground">Credibility</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{getTopEntities(10).length}</div>
              <div className="text-sm text-muted-foreground">Key Entities</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{sourceStats.total}</div>
              <div className="text-sm text-muted-foreground">Sources</div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="insights" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="insights">AI Insights</TabsTrigger>
            <TabsTrigger value="summary">Executive Summary</TabsTrigger>
            <TabsTrigger value="quality">Quality Analysis</TabsTrigger>
            <TabsTrigger value="custom">Custom Analysis</TabsTrigger>
          </TabsList>

          {/* AI Insights Tab */}
          <TabsContent value="insights" className="space-y-4">
            <div className="grid gap-4">
              {aiInsights.map((insight) => (
                <Card key={insight.id} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <div className={cn("p-2 rounded-lg bg-muted", getInsightColor(insight.type))}>
                          {getInsightIcon(insight.type)}
                        </div>
                        <div>
                          <CardTitle className="text-base">{insight.title}</CardTitle>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="capitalize">
                              {insight.type}
                            </Badge>
                            <Badge variant="outline">
                              {Math.round(insight.confidence * 100)}% confidence
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                              {getInsightIcon(insight.type)}
                              {insight.title}
                            </DialogTitle>
                            <DialogDescription>
                              Generated with {Math.round(insight.confidence * 100)}% confidence
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="text-sm">{insight.content}</div>
                            <div className="space-y-2">
                              <h4 className="font-medium">Supporting Sources</h4>
                              <div className="flex flex-wrap gap-1">
                                {insight.sources.map((source, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    Source {index + 1}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                            <div className="flex items-center gap-2 pt-2 border-t">
                              <Button variant="outline" size="sm" className="gap-2">
                                <ThumbsUp className="h-3 w-3" />
                                Helpful
                              </Button>
                              <Button variant="outline" size="sm" className="gap-2">
                                <ThumbsDown className="h-3 w-3" />
                                Not Helpful
                              </Button>
                              <Button variant="outline" size="sm" className="gap-2">
                                <MessageSquare className="h-3 w-3" />
                                Feedback
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {insight.content}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Executive Summary Tab */}
          <TabsContent value="summary" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Executive Summary
                </CardTitle>
                <CardDescription>
                  AI-generated overview of your research findings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="prose prose-sm max-w-none">
                  <p>
                    This research project has analyzed <strong>{sourceStats.total} sources</strong> across 
                    multiple domains, generating <strong>{analysisStats.total} AI analyses</strong>. 
                    The overall credibility score of <strong>{getOverallCredibilityScore() ? Math.round(getOverallCredibilityScore()! * 100) : 0}%</strong> indicates 
                    high-quality source material.
                  </p>
                  
                  <h3>Key Findings</h3>
                  <ul>
                    {getKeyInsights().slice(0, 5).map((insight, index) => (
                      <li key={index}>{insight}</li>
                    ))}
                  </ul>

                  <h3>Top Entities</h3>
                  <div className="flex flex-wrap gap-2 not-prose">
                    {getTopEntities(10).map((entity, index) => (
                      <Badge key={index} variant="outline">
                        {entity.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Quality Analysis Tab */}
          <TabsContent value="quality" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Source Quality</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Overall Credibility</span>
                      <span>{getOverallCredibilityScore() ? Math.round(getOverallCredibilityScore()! * 100) : 0}%</span>
                    </div>
                    <Progress value={(getOverallCredibilityScore() || 0) * 100} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Bias Score</span>
                      <span>{getOverallBiasScore() ? Math.round(getOverallBiasScore()! * 100) : 0}%</span>
                    </div>
                    <Progress value={(getOverallBiasScore() || 0) * 100} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Source Diversity</span>
                      <span>{Object.keys(sourceStats.byType).length} types</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Analysis Performance</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Average Confidence</span>
                      <span>{Math.round(analysisStats.averageConfidence * 100)}%</span>
                    </div>
                    <Progress value={analysisStats.averageConfidence * 100} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Processing Time</span>
                      <span>{Math.round(analysisStats.totalProcessingTime / 1000)}s</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Tokens Used</span>
                      <span>{analysisStats.totalTokensUsed.toLocaleString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Custom Analysis Tab */}
          <TabsContent value="custom" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  Custom AI Analysis
                </CardTitle>
                <CardDescription>
                  Ask specific questions about your research data
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Textarea
                    placeholder="Ask a question about your research (e.g., 'What are the main challenges mentioned in the sources?')"
                    value={customQuery}
                    onChange={(e) => setCustomQuery(e.target.value)}
                    rows={3}
                  />
                  <Button 
                    onClick={generateCustomInsight}
                    disabled={!customQuery.trim() || isGeneratingInsights}
                    className="gap-2"
                  >
                    {isGeneratingInsights ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Brain className="h-4 w-4" />
                    )}
                    Generate Insight
                  </Button>
                </div>

                {/* Custom insight results would appear here */}
                {messages.length > 0 && (
                  <div className="space-y-2 pt-4 border-t">
                    <h4 className="font-medium">AI Response</h4>
                    {messages
                      .filter(m => m.role === 'assistant')
                      .slice(-1)
                      .map((message) => (
                        <div key={message.id} className="p-3 rounded-lg bg-muted text-sm">
                          {message.content}
                        </div>
                      ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </TooltipProvider>
  )
}
