/**
 * AI Query Builder Component
 * 
 * Intelligent query builder with AI-powered suggestions and enhancements
 */

"use client"

import * as React from "react"
import { useState, useCallback } from "react"
import { cn } from "@/lib/utils"
import {
  Sparkles,
  Lightbulb,
  RefreshCw,
  Plus,
  X,
  Wand2,
  Search,
  Brain,
  Target,
  Loader2
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useChat } from "@ai-sdk/react"

interface AIQueryBuilderProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
}

interface QuerySuggestion {
  id: string
  text: string
  type: 'enhancement' | 'alternative' | 'refinement'
  reasoning: string
}

export function AIQueryBuilder({
  value,
  onChange,
  placeholder = "Enter your research query...",
  className
}: AIQueryBuilderProps) {
  const [suggestions, setSuggestions] = useState<QuerySuggestion[]>([])
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState(false)
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([])

  // AI chat for query enhancement
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isEnhancing
  } = useChat({
    api: '/api/deep-research/enhance-query',
    onFinish: (message) => {
      try {
        const response = JSON.parse(message.content)
        if (response.suggestions) {
          setSuggestions(response.suggestions)
        }
        if (response.enhancedQuery) {
          onChange(response.enhancedQuery)
        }
      } catch (error) {
        console.error('Failed to parse AI response:', error)
      }
    }
  })

  // Generate AI suggestions for the current query
  const generateSuggestions = useCallback(async () => {
    if (!value.trim()) return

    setIsGeneratingSuggestions(true)
    try {
      const response = await fetch('/api/deep-research/query-suggestions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: value })
      })

      if (response.ok) {
        const data = await response.json()
        setSuggestions(data.suggestions || [])
      }
    } catch (error) {
      console.error('Failed to generate suggestions:', error)
    } finally {
      setIsGeneratingSuggestions(false)
    }
  }, [value])

  // Extract keywords from the query
  const extractKeywords = useCallback(async () => {
    if (!value.trim()) return

    try {
      const response = await fetch('/api/deep-research/extract-keywords', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: value })
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedKeywords(data.keywords || [])
      }
    } catch (error) {
      console.error('Failed to extract keywords:', error)
    }
  }, [value])

  // Enhance query with AI
  const enhanceQuery = useCallback(() => {
    if (!value.trim()) return

    handleInputChange({
      target: { value: `Enhance this research query for better results: "${value}"` }
    } as any)
    handleSubmit()
  }, [value, handleInputChange, handleSubmit])

  // Apply a suggestion
  const applySuggestion = (suggestion: QuerySuggestion) => {
    onChange(suggestion.text)
    setSuggestions(suggestions.filter(s => s.id !== suggestion.id))
  }

  // Remove a keyword
  const removeKeyword = (keyword: string) => {
    setSelectedKeywords(prev => prev.filter(k => k !== keyword))
  }

  // Add a keyword
  const addKeyword = (keyword: string) => {
    if (!selectedKeywords.includes(keyword)) {
      setSelectedKeywords(prev => [...prev, keyword])
    }
  }

  React.useEffect(() => {
    if (value.trim()) {
      extractKeywords()
    }
  }, [value, extractKeywords])

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'enhancement':
        return <Wand2 className="h-3 w-3" />
      case 'alternative':
        return <RefreshCw className="h-3 w-3" />
      case 'refinement':
        return <Target className="h-3 w-3" />
      default:
        return <Lightbulb className="h-3 w-3" />
    }
  }

  const getSuggestionColor = (type: string) => {
    switch (type) {
      case 'enhancement':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20'
      case 'alternative':
        return 'bg-green-500/10 text-green-500 border-green-500/20'
      case 'refinement':
        return 'bg-purple-500/10 text-purple-500 border-purple-500/20'
      default:
        return 'bg-gray-500/10 text-gray-500 border-gray-500/20'
    }
  }

  return (
    <TooltipProvider>
      <div className={cn("space-y-4", className)}>
        {/* Main Query Input */}
        <div className="space-y-2">
          <Label htmlFor="query-input">Research Query</Label>
          <div className="relative">
            <Textarea
              id="query-input"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder={placeholder}
              rows={3}
              className="pr-12"
            />
            <div className="absolute right-2 top-2 flex flex-col gap-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={generateSuggestions}
                    disabled={!value.trim() || isGeneratingSuggestions}
                    className="h-8 w-8 p-0"
                  >
                    {isGeneratingSuggestions ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Lightbulb className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Generate AI suggestions</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={enhanceQuery}
                    disabled={!value.trim() || isEnhancing}
                    className="h-8 w-8 p-0"
                  >
                    {isEnhancing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Sparkles className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Enhance with AI</TooltipContent>
              </Tooltip>
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            Describe your research question in detail. Use AI suggestions to improve your query.
          </p>
        </div>

        {/* Keywords */}
        {selectedKeywords.length > 0 && (
          <div className="space-y-2">
            <Label>Extracted Keywords</Label>
            <div className="flex flex-wrap gap-2">
              {selectedKeywords.map((keyword) => (
                <Badge
                  key={keyword}
                  variant="secondary"
                  className="gap-1 cursor-pointer hover:bg-secondary/80"
                >
                  {keyword}
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeKeyword(keyword)}
                    className="h-3 w-3 p-0 hover:bg-transparent"
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* AI Suggestions */}
        {suggestions.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Brain className="h-4 w-4" />
                AI Suggestions
              </CardTitle>
              <CardDescription>
                Click on a suggestion to apply it to your query
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {suggestions.map((suggestion) => (
                <div
                  key={suggestion.id}
                  className="group cursor-pointer rounded-lg border p-3 hover:bg-accent transition-colors"
                  onClick={() => applySuggestion(suggestion)}
                >
                  <div className="flex items-start gap-3">
                    <div className={cn(
                      "flex h-6 w-6 items-center justify-center rounded-full border",
                      getSuggestionColor(suggestion.type)
                    )}>
                      {getSuggestionIcon(suggestion.type)}
                    </div>
                    <div className="flex-1 space-y-1">
                      <div className="font-medium text-sm group-hover:text-primary transition-colors">
                        {suggestion.text}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {suggestion.reasoning}
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs capitalize">
                      {suggestion.type}
                    </Badge>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={generateSuggestions}
            disabled={!value.trim() || isGeneratingSuggestions}
            className="gap-2"
          >
            <Search className="h-3 w-3" />
            Get Suggestions
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={enhanceQuery}
            disabled={!value.trim() || isEnhancing}
            className="gap-2"
          >
            <Sparkles className="h-3 w-3" />
            Enhance Query
          </Button>
        </div>

        {/* AI Enhancement Messages */}
        {messages.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">AI Enhancement</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {messages
                  .filter(m => m.role === 'assistant')
                  .slice(-1)
                  .map((message) => (
                    <div key={message.id} className="text-sm">
                      {message.content}
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </TooltipProvider>
  )
}
