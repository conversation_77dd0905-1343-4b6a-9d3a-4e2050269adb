/**
 * Research Results Viewer Component
 * 
 * Displays research sources and AI-generated analyses with filtering and sorting
 */

"use client"

import * as React from "react"
import { useState, useMemo } from "react"
import { cn } from "@/lib/utils"
import {
  Filter,
  Search,
  SortAsc,
  SortDesc,
  ExternalLink,
  Calendar,
  Star,
  TrendingUp,
  AlertTriangle,
  CheckCircle2,
  Eye,
  Download,
  Share,
  MoreHorizontal,
  Globe,
  GraduationCap,
  Newspaper,
  Users,
  Brain,
  Lightbulb,
  Target,
  BarChart3
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

import { useResearchSources, useResearchAnalyses, useResearchInsights } from "@/hooks/deep-research"

interface ResearchResultsViewerProps {
  projectId: string
  className?: string
}

export function ResearchResultsViewer({
  projectId,
  className
}: ResearchResultsViewerProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedSourceType, setSelectedSourceType] = useState<string>("all")
  const [selectedAnalysisType, setSelectedAnalysisType] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("relevance")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  const {
    sources,
    getSourcesByType,
    getSourcesByCredibility,
    getSourcesByRelevance,
    isLoading: sourcesLoading
  } = useResearchSources(projectId)

  const {
    analyses,
    getAnalysesByType,
    getLatestAnalyses,
    getHighConfidenceAnalyses,
    isLoading: analysesLoading
  } = useResearchAnalyses(projectId)

  const {
    getOverallBiasScore,
    getOverallCredibilityScore,
    getTopEntities,
    getKeyInsights
  } = useResearchInsights(projectId)

  // Filter and sort sources
  const filteredSources = useMemo(() => {
    let filtered = sources.filter(source => {
      const matchesSearch = source.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           source.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesType = selectedSourceType === "all" || source.type === selectedSourceType
      return matchesSearch && matchesType
    })

    // Sort sources
    filtered.sort((a, b) => {
      let comparison = 0
      switch (sortBy) {
        case 'relevance':
          comparison = a.relevanceScore - b.relevanceScore
          break
        case 'credibility':
          comparison = a.credibilityScore - b.credibilityScore
          break
        case 'date':
          comparison = new Date(a.publishedAt || a.accessedAt).getTime() - 
                      new Date(b.publishedAt || b.accessedAt).getTime()
          break
        case 'title':
          comparison = a.title.localeCompare(b.title)
          break
        default:
          comparison = 0
      }
      return sortOrder === 'desc' ? -comparison : comparison
    })

    return filtered
  }, [sources, searchQuery, selectedSourceType, sortBy, sortOrder])

  // Filter analyses
  const filteredAnalyses = useMemo(() => {
    return selectedAnalysisType === "all" 
      ? analyses 
      : getAnalysesByType(selectedAnalysisType)
  }, [analyses, selectedAnalysisType, getAnalysesByType])

  const getSourceIcon = (type: string) => {
    switch (type) {
      case 'web':
        return <Globe className="h-4 w-4" />
      case 'academic':
        return <GraduationCap className="h-4 w-4" />
      case 'news':
        return <Newspaper className="h-4 w-4" />
      case 'social':
        return <Users className="h-4 w-4" />
      default:
        return <Globe className="h-4 w-4" />
    }
  }

  const getAnalysisIcon = (type: string) => {
    switch (type) {
      case 'summary':
        return <BarChart3 className="h-4 w-4" />
      case 'insights':
        return <Lightbulb className="h-4 w-4" />
      case 'bias':
        return <AlertTriangle className="h-4 w-4" />
      case 'credibility':
        return <CheckCircle2 className="h-4 w-4" />
      case 'entities':
        return <Target className="h-4 w-4" />
      default:
        return <Brain className="h-4 w-4" />
    }
  }

  const getCredibilityColor = (score: number) => {
    if (score >= 0.8) return "text-green-500"
    if (score >= 0.6) return "text-yellow-500"
    return "text-red-500"
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with Overview Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{sources.length}</div>
            <div className="text-sm text-muted-foreground">Total Sources</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{analyses.length}</div>
            <div className="text-sm text-muted-foreground">AI Analyses</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className={cn("text-2xl font-bold", getCredibilityColor(getOverallCredibilityScore() || 0))}>
              {getOverallCredibilityScore() ? Math.round(getOverallCredibilityScore()! * 100) : 0}%
            </div>
            <div className="text-sm text-muted-foreground">Avg Credibility</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{getTopEntities(10).length}</div>
            <div className="text-sm text-muted-foreground">Key Entities</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search sources and analyses..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Relevance</SelectItem>
              <SelectItem value="credibility">Credibility</SelectItem>
              <SelectItem value="date">Date</SelectItem>
              <SelectItem value="title">Title</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
          >
            {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="sources" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sources">Sources ({sources.length})</TabsTrigger>
          <TabsTrigger value="analyses">Analyses ({analyses.length})</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        {/* Sources Tab */}
        <TabsContent value="sources" className="space-y-4">
          <div className="flex gap-2">
            <Select value={selectedSourceType} onValueChange={setSelectedSourceType}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="web">Web</SelectItem>
                <SelectItem value="academic">Academic</SelectItem>
                <SelectItem value="news">News</SelectItem>
                <SelectItem value="social">Social</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-4">
            {filteredSources.map((source) => (
              <Card key={source.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-2">
                        {getSourceIcon(source.type)}
                        <CardTitle className="text-base line-clamp-1">
                          <a 
                            href={source.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="hover:text-primary transition-colors"
                          >
                            {source.title}
                          </a>
                        </CardTitle>
                        <ExternalLink className="h-3 w-3 text-muted-foreground" />
                      </div>
                      <CardDescription className="line-clamp-2">
                        {source.description}
                      </CardDescription>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Share className="h-4 w-4 mr-2" />
                          Share
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-4 text-sm">
                    <Badge variant="outline" className="capitalize">
                      {source.type}
                    </Badge>
                    {source.author && (
                      <span className="text-muted-foreground">by {source.author}</span>
                    )}
                    {source.publishedAt && (
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {formatDate(source.publishedAt)}
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="space-y-1">
                      <div className={cn("text-sm font-medium", getCredibilityColor(source.credibilityScore))}>
                        {Math.round(source.credibilityScore * 100)}%
                      </div>
                      <div className="text-xs text-muted-foreground">Credibility</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {Math.round(source.relevanceScore * 100)}%
                      </div>
                      <div className="text-xs text-muted-foreground">Relevance</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {Math.round(source.biasScore * 100)}%
                      </div>
                      <div className="text-xs text-muted-foreground">Bias</div>
                    </div>
                  </div>

                  {source.content.summary && (
                    <div className="pt-2 border-t">
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {source.content.summary}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Analyses Tab */}
        <TabsContent value="analyses" className="space-y-4">
          <div className="flex gap-2">
            <Select value={selectedAnalysisType} onValueChange={setSelectedAnalysisType}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="summary">Summary</SelectItem>
                <SelectItem value="insights">Insights</SelectItem>
                <SelectItem value="bias">Bias</SelectItem>
                <SelectItem value="credibility">Credibility</SelectItem>
                <SelectItem value="entities">Entities</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-4">
            {filteredAnalyses.map((analysis) => (
              <Card key={analysis.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getAnalysisIcon(analysis.type)}
                      <CardTitle className="text-base capitalize">
                        {analysis.type} Analysis
                      </CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {Math.round(analysis.confidence * 100)}% confidence
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {formatDate(analysis.createdAt)}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Analysis Result */}
                    <div className="text-sm">
                      {typeof analysis.result === 'string' ? (
                        <p>{analysis.result}</p>
                      ) : (
                        <pre className="whitespace-pre-wrap">
                          {JSON.stringify(analysis.result, null, 2)}
                        </pre>
                      )}
                    </div>

                    {/* Metadata */}
                    <div className="flex items-center gap-4 text-xs text-muted-foreground pt-2 border-t">
                      <span>Model: {analysis.model}</span>
                      <span>Processing: {analysis.metadata.processingTime}ms</span>
                      <span>Tokens: {analysis.metadata.tokensUsed}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Key Insights */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Lightbulb className="h-4 w-4" />
                  Key Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {getKeyInsights().slice(0, 5).map((insight, index) => (
                    <div key={index} className="text-sm p-2 rounded bg-muted">
                      {insight}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Entities */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Top Entities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {getTopEntities(10).map((entity, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span>{entity.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {entity.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quality Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Quality Assessment</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Overall Credibility</span>
                  <span className={getCredibilityColor(getOverallCredibilityScore() || 0)}>
                    {getOverallCredibilityScore() ? Math.round(getOverallCredibilityScore()! * 100) : 0}%
                  </span>
                </div>
                <Progress value={(getOverallCredibilityScore() || 0) * 100} />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Bias Score</span>
                  <span>{getOverallBiasScore() ? Math.round(getOverallBiasScore()! * 100) : 0}%</span>
                </div>
                <Progress value={(getOverallBiasScore() || 0) * 100} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
