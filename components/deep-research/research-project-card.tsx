/**
 * Research Project Card Component
 * 
 * Individual project card with status, metrics, and actions
 */

"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import {
  MoreHorizontal,
  Calendar,
  FileText,
  BarChart3,
  Play,
  Pause,
  Archive,
  Trash2,
  ExternalLink,
  Clock,
  TrendingUp,
  Users,
  CheckCircle2,
  AlertCircle,
  Loader2
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

import { useResearchProject } from "@/hooks/deep-research"

interface ResearchProject {
  id: string
  title: string
  description: string
  status: 'draft' | 'active' | 'completed' | 'archived'
  tags: string[]
  createdAt: string
  updatedAt: string
  userId: string
  settings: {
    autoAnalysis: boolean
    sourceTypes: string[]
    analysisTypes: string[]
    maxSources: number
    qualityThreshold: number
    biasDetection: boolean
    factChecking: boolean
    realTimeUpdates: boolean
  }
  metadata: {
    totalSources: number
    totalAnalyses: number
    lastActivity: string
  }
}

interface ResearchProjectCardProps {
  project: ResearchProject
  onDelete?: () => void
  onArchive?: () => void
  onActivate?: () => void
  className?: string
}

export function ResearchProjectCard({
  project,
  onDelete,
  onArchive,
  onActivate,
  className
}: ResearchProjectCardProps) {
  const { stats, isLoading: statsLoading } = useResearchProject(project.id)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
      case 'completed':
        return <CheckCircle2 className="h-3 w-3 text-green-500" />
      case 'archived':
        return <Archive className="h-3 w-3 text-gray-500" />
      case 'draft':
      default:
        return <Clock className="h-3 w-3 text-yellow-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20'
      case 'completed':
        return 'bg-green-500/10 text-green-500 border-green-500/20'
      case 'archived':
        return 'bg-gray-500/10 text-gray-500 border-gray-500/20'
      case 'draft':
      default:
        return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getProgressPercentage = () => {
    if (!stats) return 0
    const { sourcesCollected = 0, sourcesProcessed = 0, analysesCompleted = 0 } = stats
    if (sourcesCollected === 0) return 0
    
    // Weight different phases: collection (40%), processing (40%), analysis (20%)
    const collectionProgress = Math.min(sourcesCollected / project.settings.maxSources, 1) * 40
    const processingProgress = sourcesCollected > 0 ? (sourcesProcessed / sourcesCollected) * 40 : 0
    const analysisProgress = sourcesProcessed > 0 ? (analysesCompleted / sourcesProcessed) * 20 : 0
    
    return Math.round(collectionProgress + processingProgress + analysisProgress)
  }

  return (
    <Card className={cn("group hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <CardTitle className="text-lg line-clamp-1">{project.title}</CardTitle>
            <CardDescription className="line-clamp-2">
              {project.description}
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <ExternalLink className="h-4 w-4 mr-2" />
                Open Project
              </DropdownMenuItem>
              {project.status === 'draft' && onActivate && (
                <DropdownMenuItem onClick={onActivate}>
                  <Play className="h-4 w-4 mr-2" />
                  Activate
                </DropdownMenuItem>
              )}
              {project.status === 'active' && (
                <DropdownMenuItem>
                  <Pause className="h-4 w-4 mr-2" />
                  Pause
                </DropdownMenuItem>
              )}
              {project.status !== 'archived' && onArchive && (
                <DropdownMenuItem onClick={onArchive}>
                  <Archive className="h-4 w-4 mr-2" />
                  Archive
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onDelete && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onSelect={(e) => e.preventDefault()}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Research Project</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete "{project.title}"? This action cannot be undone.
                        All research data, sources, and analyses will be permanently removed.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={onDelete}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        Delete Project
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant="outline" className={cn("text-xs", getStatusColor(project.status))}>
            {getStatusIcon(project.status)}
            <span className="ml-1 capitalize">{project.status}</span>
          </Badge>
          {project.tags.slice(0, 2).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {project.tags.length > 2 && (
            <Badge variant="secondary" className="text-xs">
              +{project.tags.length - 2}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Bar for Active Projects */}
        {project.status === 'active' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Progress</span>
              <span className="font-medium">{getProgressPercentage()}%</span>
            </div>
            <Progress value={getProgressPercentage()} className="h-2" />
          </div>
        )}

        {/* Statistics */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="space-y-1">
            <div className="text-lg font-semibold">
              {statsLoading ? '...' : project.metadata.totalSources}
            </div>
            <div className="text-xs text-muted-foreground flex items-center justify-center gap-1">
              <FileText className="h-3 w-3" />
              Sources
            </div>
          </div>
          <div className="space-y-1">
            <div className="text-lg font-semibold">
              {statsLoading ? '...' : project.metadata.totalAnalyses}
            </div>
            <div className="text-xs text-muted-foreground flex items-center justify-center gap-1">
              <BarChart3 className="h-3 w-3" />
              Analyses
            </div>
          </div>
          <div className="space-y-1">
            <div className="text-lg font-semibold">
              {statsLoading ? '...' : stats?.qualityScore ? `${Math.round(stats.qualityScore * 100)}%` : 'N/A'}
            </div>
            <div className="text-xs text-muted-foreground flex items-center justify-center gap-1">
              <TrendingUp className="h-3 w-3" />
              Quality
            </div>
          </div>
        </div>

        {/* Source Types */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Source Types</div>
          <div className="flex flex-wrap gap-1">
            {project.settings.sourceTypes.map((type) => (
              <Badge key={type} variant="outline" className="text-xs capitalize">
                {type}
              </Badge>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            Created {formatDate(project.createdAt)}
          </div>
          {project.metadata.lastActivity && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {formatDate(project.metadata.lastActivity)}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
