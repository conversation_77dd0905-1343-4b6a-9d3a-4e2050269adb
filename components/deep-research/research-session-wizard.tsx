/**
 * Research Session Wizard Component
 *
 * Multi-step wizard for creating research sessions with AI-powered suggestions
 */

"use client"

import * as React from "react"
import { useState } from "react"
import { cn } from "@/lib/utils"
import {
  ArrowLeft,
  ArrowRight,
  Sparkles,
  Search,
  Settings,
  Play,
  BookOpen,
  Globe,
  GraduationCap,
  Newspaper,
  Users,
  Zap,
  Brain,
  CheckCircle2,
  Loader2
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  <PERSON>,
  Select<PERSON>ontent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { useResearchTemplates, useTemplateRecommendations } from "@/hooks/deep-research"
import { AIQueryBuilder } from "./ai-query-builder"

interface ResearchSessionWizardProps {
  open: boolean
  onClose: () => void
  onCreateProject: (title: string, description: string) => Promise<void>
  onStartResearch: (query: string, options: any, projectId?: string) => Promise<any>
  existingProjectId?: string
}

interface WizardStep {
  id: string
  title: string
  description: string
  icon: React.ReactNode
}

const steps: WizardStep[] = [
  {
    id: 'query',
    title: 'Research Query',
    description: 'Define what you want to research',
    icon: <Search className="h-5 w-5" />
  },
  {
    id: 'template',
    title: 'Choose Template',
    description: 'Select a research template',
    icon: <BookOpen className="h-5 w-5" />
  },
  {
    id: 'sources',
    title: 'Configure Sources',
    description: 'Customize data sources',
    icon: <Globe className="h-5 w-5" />
  },
  {
    id: 'analysis',
    title: 'Analysis Settings',
    description: 'Configure AI analysis',
    icon: <Brain className="h-5 w-5" />
  },
  {
    id: 'review',
    title: 'Review & Launch',
    description: 'Review and start research',
    icon: <Play className="h-5 w-5" />
  }
]

export function ResearchSessionWizard({
  open,
  onClose,
  onCreateProject,
  onStartResearch,
  existingProjectId
}: ResearchSessionWizardProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isStarting, setIsStarting] = useState(false)

  // Form state
  const [query, setQuery] = useState("")
  const [projectTitle, setProjectTitle] = useState("")
  const [projectDescription, setProjectDescription] = useState("")
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [sourceConfig, setSourceConfig] = useState({
    web: { enabled: true, maxResults: 15, domains: [], excludeDomains: [] },
    academic: { enabled: true, maxResults: 10, databases: ['arxiv', 'pubmed'] },
    news: { enabled: true, maxResults: 10, sources: [], languages: ['en'] },
    social: { enabled: false, platforms: [], maxResults: 5 }
  })
  const [analysisConfig, setAnalysisConfig] = useState({
    summarize: true,
    extractEntities: true,
    detectBias: true,
    checkCredibility: true,
    findRelationships: true
  })

  const { templates, getTemplateById } = useResearchTemplates()
  const { recommendations } = useTemplateRecommendations(query)

  const canProceed = () => {
    switch (currentStep) {
      case 0: return query.trim().length > 0
      case 1: return selectedTemplate !== null
      case 2: return Object.values(sourceConfig).some(source => source.enabled)
      case 3: return Object.values(analysisConfig).some(analysis => analysis)
      case 4: return true
      default: return false
    }
  }

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId)
    const template = getTemplateById(templateId)
    if (template) {
      // Auto-configure sources and analysis based on template
      setSourceConfig(template.options.sources as any)
      setAnalysisConfig(template.options.analysis || analysisConfig)

      // Auto-generate project title if not set
      if (!projectTitle) {
        setProjectTitle(`${template.name}: ${query}`)
      }
    }
  }

  const handleStartResearch = async () => {
    setIsStarting(true)
    try {
      let projectId = existingProjectId

      // Create project if needed
      if (!projectId) {
        await onCreateProject(
          projectTitle || `Research: ${query}`,
          projectDescription || `Research session for: ${query}`
        )
      }

      // Start research session
      const options = {
        sources: sourceConfig,
        analysis: analysisConfig
      }

      await onStartResearch(query, options, projectId)
      onClose()
    } catch (error) {
      console.error('Failed to start research:', error)
    } finally {
      setIsStarting(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <AIQueryBuilder
              value={query}
              onChange={setQuery}
              placeholder="What would you like to research? (e.g., 'Latest developments in quantum computing')"
            />

            {!existingProjectId && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="project-title">Project Title (Optional)</Label>
                  <Input
                    id="project-title"
                    value={projectTitle}
                    onChange={(e) => setProjectTitle(e.target.value)}
                    placeholder="Enter a title for your research project"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="project-description">Project Description (Optional)</Label>
                  <Textarea
                    id="project-description"
                    value={projectDescription}
                    onChange={(e) => setProjectDescription(e.target.value)}
                    placeholder="Describe the goals and scope of your research"
                    rows={3}
                  />
                </div>
              </div>
            )}
          </div>
        )

      case 1:
        return (
          <div className="space-y-6">
            {recommendations.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-yellow-500" />
                  <h3 className="font-medium">AI Recommendations</h3>
                </div>
                <div className="grid gap-3">
                  {recommendations.map((template) => (
                    <Card
                      key={template.id}
                      className={cn(
                        "cursor-pointer transition-colors hover:bg-accent",
                        selectedTemplate === template.id && "ring-2 ring-primary"
                      )}
                      onClick={() => handleTemplateSelect(template.id)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">{template.icon}</div>
                          <div>
                            <CardTitle className="text-base">{template.name}</CardTitle>
                            <CardDescription className="text-sm">
                              {template.description}
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            <div className="space-y-3">
              <h3 className="font-medium">All Templates</h3>
              <div className="grid gap-3">
                {templates.map((template) => (
                  <Card
                    key={template.id}
                    className={cn(
                      "cursor-pointer transition-colors hover:bg-accent",
                      selectedTemplate === template.id && "ring-2 ring-primary"
                    )}
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{template.icon}</div>
                        <div className="flex-1">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                          <CardDescription className="text-sm">
                            {template.description}
                          </CardDescription>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {template.category}
                        </Badge>
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              {/* Web Sources */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      <CardTitle className="text-base">Web Sources</CardTitle>
                    </div>
                    <Switch
                      checked={sourceConfig.web.enabled}
                      onCheckedChange={(enabled) =>
                        setSourceConfig(prev => ({
                          ...prev,
                          web: { ...prev.web, enabled }
                        }))
                      }
                    />
                  </div>
                </CardHeader>
                {sourceConfig.web.enabled && (
                  <CardContent className="space-y-3">
                    <div className="space-y-2">
                      <Label>Max Results: {sourceConfig.web.maxResults}</Label>
                      <Slider
                        value={[sourceConfig.web.maxResults]}
                        onValueChange={([value]) =>
                          setSourceConfig(prev => ({
                            ...prev,
                            web: { ...prev.web, maxResults: value }
                          }))
                        }
                        max={50}
                        min={5}
                        step={5}
                      />
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* Academic Sources */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4" />
                      <CardTitle className="text-base">Academic Sources</CardTitle>
                    </div>
                    <Switch
                      checked={sourceConfig.academic.enabled}
                      onCheckedChange={(enabled) =>
                        setSourceConfig(prev => ({
                          ...prev,
                          academic: { ...prev.academic, enabled }
                        }))
                      }
                    />
                  </div>
                </CardHeader>
                {sourceConfig.academic.enabled && (
                  <CardContent className="space-y-3">
                    <div className="space-y-2">
                      <Label>Max Results: {sourceConfig.academic.maxResults}</Label>
                      <Slider
                        value={[sourceConfig.academic.maxResults]}
                        onValueChange={([value]) =>
                          setSourceConfig(prev => ({
                            ...prev,
                            academic: { ...prev.academic, maxResults: value }
                          }))
                        }
                        max={30}
                        min={5}
                        step={5}
                      />
                    </div>
                  </CardContent>
                )}
              </Card>

              {/* News Sources */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Newspaper className="h-4 w-4" />
                      <CardTitle className="text-base">News Sources</CardTitle>
                    </div>
                    <Switch
                      checked={sourceConfig.news.enabled}
                      onCheckedChange={(enabled) =>
                        setSourceConfig(prev => ({
                          ...prev,
                          news: { ...prev.news, enabled }
                        }))
                      }
                    />
                  </div>
                </CardHeader>
                {sourceConfig.news.enabled && (
                  <CardContent className="space-y-3">
                    <div className="space-y-2">
                      <Label>Max Results: {sourceConfig.news.maxResults}</Label>
                      <Slider
                        value={[sourceConfig.news.maxResults]}
                        onValueChange={([value]) =>
                          setSourceConfig(prev => ({
                            ...prev,
                            news: { ...prev.news, maxResults: value }
                          }))
                        }
                        max={30}
                        min={5}
                        step={5}
                      />
                    </div>
                  </CardContent>
                )}
              </Card>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="grid gap-4">
              {Object.entries(analysisConfig).map(([key, enabled]) => (
                <div key={key} className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {getAnalysisDescription(key)}
                    </p>
                  </div>
                  <Switch
                    checked={enabled}
                    onCheckedChange={(checked) =>
                      setAnalysisConfig(prev => ({ ...prev, [key]: checked }))
                    }
                  />
                </div>
              ))}
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <div className="rounded-lg border p-4 space-y-3">
                <h3 className="font-medium">Research Configuration</h3>
                <div className="space-y-2 text-sm">
                  <div><strong>Query:</strong> {query}</div>
                  <div><strong>Template:</strong> {selectedTemplate ? getTemplateById(selectedTemplate)?.name : 'None'}</div>
                  <div><strong>Sources:</strong> {Object.entries(sourceConfig).filter(([_, config]) => config.enabled).map(([key]) => key).join(', ')}</div>
                  <div><strong>Analysis:</strong> {Object.entries(analysisConfig).filter(([_, enabled]) => enabled).map(([key]) => key).join(', ')}</div>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  const getAnalysisDescription = (key: string) => {
    const descriptions: Record<string, string> = {
      summarize: 'Generate AI-powered summaries of collected sources',
      extractEntities: 'Identify people, organizations, concepts, and locations',
      detectBias: 'Analyze potential bias in sources and content',
      checkCredibility: 'Assess the credibility and reliability of sources',
      findRelationships: 'Discover connections between entities and concepts'
    }
    return descriptions[key] || ''
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Research Session</DialogTitle>
          <DialogDescription>
            Configure your AI-powered research session with intelligent templates and customizable settings
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={cn(
                  "flex items-center gap-2 text-sm",
                  index <= currentStep ? "text-primary" : "text-muted-foreground"
                )}
              >
                <div
                  className={cn(
                    "flex h-8 w-8 items-center justify-center rounded-full border-2",
                    index < currentStep
                      ? "bg-primary border-primary text-primary-foreground"
                      : index === currentStep
                      ? "border-primary text-primary"
                      : "border-muted-foreground"
                  )}
                >
                  {index < currentStep ? (
                    <CheckCircle2 className="h-4 w-4" />
                  ) : (
                    step.icon
                  )}
                </div>
                <span className="hidden sm:inline">{step.title}</span>
              </div>
            ))}
          </div>

          {/* Step Content */}
          <div className="min-h-[400px]">
            {renderStepContent()}
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Previous
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button
                onClick={handleStartResearch}
                disabled={!canProceed() || isStarting}
                className="gap-2"
              >
                {isStarting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
                Start Research
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                disabled={!canProceed()}
                className="gap-2"
              >
                Next
                <ArrowRight className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
