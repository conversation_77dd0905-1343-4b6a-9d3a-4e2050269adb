/**
 * Research Progress Monitor Component
 * 
 * Real-time monitoring of research session progress with WebSocket integration
 */

"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import {
  Activity,
  Clock,
  CheckCircle2,
  AlertCircle,
  Loader2,
  Pause,
  Play,
  Square,
  TrendingUp,
  FileText,
  BarChart3,
  Users,
  Zap,
  Brain,
  Globe,
  Search
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

import { useResearchMonitoring } from "@/hooks/deep-research"

interface ResearchProgressMonitorProps {
  sessionId: string
  className?: string
  compact?: boolean
}

export function ResearchProgressMonitor({
  sessionId,
  className,
  compact = false
}: ResearchProgressMonitorProps) {
  const {
    progress,
    events,
    isConnected,
    isMonitoring,
    getProgressPercentage,
    getCurrentPhaseDescription,
    getEstimatedTimeRemaining,
    stopMonitoring,
    reconnectAttempts
  } = useResearchMonitoring(sessionId)

  if (!progress) {
    return (
      <div className={cn("flex items-center justify-center p-8", className)}>
        <div className="text-center space-y-2">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
          <p className="text-sm text-muted-foreground">Loading research progress...</p>
        </div>
      </div>
    )
  }

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'collection':
        return <Search className="h-4 w-4" />
      case 'analysis':
        return <Brain className="h-4 w-4" />
      case 'synthesis':
        return <Zap className="h-4 w-4" />
      case 'completed':
        return <CheckCircle2 className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-blue-500'
      case 'completed':
        return 'text-green-500'
      case 'failed':
        return 'text-red-500'
      case 'cancelled':
        return 'text-gray-500'
      default:
        return 'text-muted-foreground'
    }
  }

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  if (compact) {
    return (
      <TooltipProvider>
        <div className={cn("space-y-3", className)}>
          {/* Compact Progress Bar */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <div className={cn("flex items-center gap-1", getStatusColor(progress.status))}>
                  {getPhaseIcon(progress.progress.currentPhase)}
                  <span className="font-medium capitalize">{progress.progress.currentPhase}</span>
                </div>
                {!isConnected && (
                  <Tooltip>
                    <TooltipTrigger>
                      <AlertCircle className="h-3 w-3 text-yellow-500" />
                    </TooltipTrigger>
                    <TooltipContent>Connection lost - attempting to reconnect</TooltipContent>
                  </Tooltip>
                )}
              </div>
              <span className="font-medium">{getProgressPercentage()}%</span>
            </div>
            <Progress value={getProgressPercentage()} className="h-2" />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{getCurrentPhaseDescription()}</span>
              {getEstimatedTimeRemaining() && (
                <span>ETA: {getEstimatedTimeRemaining()}</span>
              )}
            </div>
          </div>

          {/* Compact Stats */}
          <div className="grid grid-cols-3 gap-2 text-center">
            <div className="space-y-1">
              <div className="text-lg font-semibold">{progress.progress.sourcesFound}</div>
              <div className="text-xs text-muted-foreground">Sources</div>
            </div>
            <div className="space-y-1">
              <div className="text-lg font-semibold">{progress.progress.analysesCompleted}</div>
              <div className="text-xs text-muted-foreground">Analyses</div>
            </div>
            <div className="space-y-1">
              <div className="text-lg font-semibold">{progress.progress.entitiesExtracted}</div>
              <div className="text-xs text-muted-foreground">Entities</div>
            </div>
          </div>
        </div>
      </TooltipProvider>
    )
  }

  return (
    <TooltipProvider>
      <div className={cn("space-y-6", className)}>
        {/* Header with Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn("flex items-center gap-2", getStatusColor(progress.status))}>
              {getPhaseIcon(progress.progress.currentPhase)}
              <h3 className="font-semibold capitalize">
                {progress.progress.currentPhase} Phase
              </h3>
            </div>
            <Badge variant="outline" className={cn("capitalize", getStatusColor(progress.status))}>
              {progress.status}
            </Badge>
            {!isConnected && (
              <Tooltip>
                <TooltipTrigger>
                  <Badge variant="outline" className="text-yellow-500 border-yellow-500/20">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Reconnecting ({reconnectAttempts}/5)
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  Connection lost - attempting to reconnect automatically
                </TooltipContent>
              </Tooltip>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {progress.status === 'running' && (
              <Button
                variant="outline"
                size="sm"
                onClick={stopMonitoring}
                className="gap-2"
              >
                <Square className="h-3 w-3" />
                Stop
              </Button>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-sm font-medium">{getProgressPercentage()}%</span>
          </div>
          <Progress value={getProgressPercentage()} className="h-3" />
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>{getCurrentPhaseDescription()}</span>
            {getEstimatedTimeRemaining() && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>ETA: {getEstimatedTimeRemaining()}</span>
              </div>
            )}
          </div>
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Globe className="h-4 w-4 text-blue-500" />
              </div>
              <div className="text-2xl font-bold">{progress.progress.sourcesFound}</div>
              <div className="text-xs text-muted-foreground">Sources Found</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <FileText className="h-4 w-4 text-green-500" />
              </div>
              <div className="text-2xl font-bold">{progress.progress.sourcesProcessed}</div>
              <div className="text-xs text-muted-foreground">Processed</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <BarChart3 className="h-4 w-4 text-purple-500" />
              </div>
              <div className="text-2xl font-bold">{progress.progress.analysesCompleted}</div>
              <div className="text-xs text-muted-foreground">Analyses</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center mb-2">
                <Users className="h-4 w-4 text-orange-500" />
              </div>
              <div className="text-2xl font-bold">{progress.progress.entitiesExtracted}</div>
              <div className="text-xs text-muted-foreground">Entities</div>
            </CardContent>
          </Card>
        </div>

        {/* Metrics */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Elapsed Time:</span>
                <span className="font-medium">
                  {formatDuration(progress.metrics.elapsedTime)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Avg Source Time:</span>
                <span className="font-medium">
                  {formatDuration(progress.metrics.averageSourceProcessingTime)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Avg Analysis Time:</span>
                <span className="font-medium">
                  {formatDuration(progress.metrics.averageAnalysisTime)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Relationships:</span>
                <span className="font-medium">{progress.progress.relationshipsFound}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Errors and Warnings */}
        {(progress.errors.length > 0 || progress.warnings.length > 0) && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Issues ({progress.errors.length + progress.warnings.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {progress.errors.map((error, index) => (
                <div key={index} className="flex items-start gap-2 p-2 rounded bg-red-500/10 border border-red-500/20">
                  <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 space-y-1">
                    <div className="text-sm font-medium text-red-500">Error</div>
                    <div className="text-sm">{error.message}</div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(error.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}

              {progress.warnings.map((warning, index) => (
                <div key={index} className="flex items-start gap-2 p-2 rounded bg-yellow-500/10 border border-yellow-500/20">
                  <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 space-y-1">
                    <div className="text-sm font-medium text-yellow-500">Warning</div>
                    <div className="text-sm">{warning.message}</div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(warning.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Recent Events */}
        {events.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {events.slice(-5).reverse().map((event, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 rounded-full bg-blue-500 flex-shrink-0" />
                    <span className="text-muted-foreground">
                      {new Date(event.timestamp).toLocaleTimeString()}
                    </span>
                    <span className="capitalize">{event.type.replace('_', ' ')}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </TooltipProvider>
  )
}
