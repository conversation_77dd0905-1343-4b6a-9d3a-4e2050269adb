import React, { useEffect, useRef, useState } from 'react';
import { Button } from './ui/button';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import '@xterm/xterm/css/xterm.css';

interface MicroVmTerminalProps {
  vmId?: string;
  className?: string;
}

export function MicroVmTerminal({ vmId, className }: MicroVmTerminalProps) {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<Terminal | null>(null);
  const socketRef = useRef<WebSocket | null>(null);
  const fitAddonRef = useRef<FitAddon | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isDirectVm, setIsDirectVm] = useState(false);
  const [commandBuffer, setCommandBuffer] = useState('');
  const [wsConnectionAttempted, setWsConnectionAttempted] = useState(false);
  
  // Check if VM is a direct VM by checking metadata API
  useEffect(() => {
    if (!vmId) return;
    
    const checkVmType = async () => {
      try {
        const response = await fetch(`/api/containerization/firecracker/${vmId}`);
        if (response.ok) {
          setIsDirectVm(true);
        }
      } catch (error) {
        console.error('Error checking VM type:', error);
      }
    };
    
    checkVmType();
  }, [vmId]);
  
  // Setup terminal
  useEffect(() => {
    if (!terminalRef.current || !vmId) return;
    
    // Clean up previous terminal instance if it exists
    if (xtermRef.current) {
      xtermRef.current.dispose();
    }
    
    // Initialize xterm.js
    const term = new Terminal({
      cursorBlink: true,
      fontFamily: 'monospace',
      fontSize: 14,
      theme: {
        background: '#1a1b26',
        foreground: '#c0caf5',
        cursor: '#c0caf5',
      }
    });
    
    xtermRef.current = term;
    
    // Initialize fit addon
    const fitAddon = new FitAddon();
    term.loadAddon(fitAddon);
    fitAddonRef.current = fitAddon;
    
    // Open terminal
    term.open(terminalRef.current);
    
    // Fit terminal to container
    try {
      fitAddon.fit();
    } catch (e) {
      console.warn('Could not fit terminal', e);
    }
    
    // For direct VMs, we'll use the command execution API instead of WebSockets
    if (isDirectVm) {
      term.writeln('\r\nConnected to direct MicroVM terminal.');
      term.writeln('\r\nType commands and press Enter to execute them.\r\n');
      
      // Set a custom prompt
      term.write('\r\n$ ');
      
      // Handle user input for direct VM
      term.onData((data: string) => {
        // If user presses Enter, execute the command
        if (data === '\r' || data === '\n') {
          const command = commandBuffer.trim();
          if (command) {
            // Send command to API
            executeCommand(command, term);
            setCommandBuffer('');
          } else {
            // Just print a new prompt
            term.write('\r\n$ ');
          }
        }
        // If user presses backspace
        else if (data === '\b' || data === '\x7f') {
          if (commandBuffer.length > 0) {
            setCommandBuffer(commandBuffer.slice(0, -1));
            // Move cursor back and erase character
            term.write('\b \b');
          }
        }
        // Otherwise add to command buffer
        else {
          setCommandBuffer(commandBuffer + data);
          term.write(data);
        }
      });
      
      setIsConnected(true);
    }
    // For regular VMs, try WebSockets first, then fall back to command API
    else if (!isDirectVm && vmId) {
      const tryWebSocket = () => {
        try {
          setWsConnectionAttempted(true);
          term.writeln('Attempting to connect to terminal via WebSocket...');
          
          // Try to connect via WebSocket for regular VMs
          const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
          const socket = new WebSocket(`${protocol}//${window.location.host}/api/microvms/${vmId}/terminal`);
          
          // Set a connection timeout
          const connectionTimeout = setTimeout(() => {
            if (socket.readyState !== WebSocket.OPEN) {
              socket.close();
              term.writeln('\r\nWebSocket connection timeout. Falling back to command-based terminal.');
              setupCommandBasedTerminal(term);
            }
          }, 3000);
          
          socket.onopen = () => {
            clearTimeout(connectionTimeout);
            term.writeln('Connected to MicroVM terminal');
            setIsConnected(true);
          };
          
          socket.onclose = () => {
            term.writeln('\r\nWebSocket connection closed');
            setIsConnected(false);
            // Fall back to command-based terminal if connection was closed unexpectedly
            if (socketRef.current === socket) {
              setupCommandBasedTerminal(term);
            }
          };
          
          socket.onerror = () => {
            clearTimeout(connectionTimeout);
            term.writeln('\r\nError connecting to terminal via WebSocket');
            term.writeln('\r\nFalling back to command-based terminal...');
            setIsConnected(false);
            // Fall back to command-based terminal
            setupCommandBasedTerminal(term);
          };
          
          socket.onmessage = (event) => {
            term.write(event.data);
          };
          
          term.onData((data: string) => {
            if (socket.readyState === WebSocket.OPEN) {
              socket.send(data);
            }
          });
          
          socketRef.current = socket;
        } catch (error) {
          console.error('Failed to connect to terminal via WebSocket:', error);
          term.writeln('\r\nFailed to connect to terminal via WebSocket');
          term.writeln('\r\nFalling back to command-based terminal...');
          // Fall back to command-based terminal
          setupCommandBasedTerminal(term);
        }
      };
      
      // Setup command-based terminal as fallback
      const setupCommandBasedTerminal = (term: Terminal) => {
        term.writeln('\r\nUsing command-based terminal.');
        term.writeln('\r\nType commands and press Enter to execute them.\r\n');
        
        // Set a custom prompt
        term.write('\r\n$ ');
        
        // Clear any existing handlers
        if (socketRef.current) {
          socketRef.current.close();
          socketRef.current = null;
        }
        
        // Handle user input
        term.onData((data: string) => {
          // If user presses Enter, execute the command
          if (data === '\r' || data === '\n') {
            const command = commandBuffer.trim();
            if (command) {
              // Send command to API
              executeCommand(command, term);
              setCommandBuffer('');
            } else {
              // Just print a new prompt
              term.write('\r\n$ ');
            }
          }
          // If user presses backspace
          else if (data === '\b' || data === '\x7f') {
            if (commandBuffer.length > 0) {
              setCommandBuffer(commandBuffer.slice(0, -1));
              // Move cursor back and erase character
              term.write('\b \b');
            }
          }
          // Otherwise add to command buffer
          else {
            setCommandBuffer(commandBuffer + data);
            term.write(data);
          }
        });
        
        setIsConnected(true);
      };
      
      // Start with WebSocket approach
      tryWebSocket();
    }
    
    // Handle resize
    const handleResize = () => {
      if (fitAddonRef.current) {
        try {
          fitAddonRef.current.fit();
        } catch (e) {
          console.warn('Could not fit terminal on resize', e);
        }
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      
      if (socketRef.current) {
        socketRef.current.close();
      }
      
      if (xtermRef.current) {
        xtermRef.current.dispose();
      }
    };
  }, [vmId, isDirectVm]);
  
  // Execute command for direct VM
  const executeCommand = async (command: string, term: Terminal) => {
    if (!vmId) return;
    
    term.writeln(`\r\n${command}`);
    term.writeln('\r\nExecuting...');
    
    try {
      const response = await fetch(`/api/microvms/${vmId}/terminal`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ command }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Output stdout if available
        if (data.stdout) {
          term.writeln('\r\n' + data.stdout.replace(/\n/g, '\r\n'));
        }
        
        // Output stderr if available
        if (data.stderr) {
          term.writeln('\r\n' + data.stderr.replace(/\n/g, '\r\n'));
        }
      } else {
        term.writeln('\r\nError: ' + (data.error || 'Command execution failed'));
        
        if (data.stdout) {
          term.writeln('\r\n' + data.stdout.replace(/\n/g, '\r\n'));
        }
        
        if (data.stderr) {
          term.writeln('\r\n' + data.stderr.replace(/\n/g, '\r\n'));
        }
      }
    } catch (error) {
      term.writeln('\r\nFailed to execute command: ' + (error instanceof Error ? error.message : String(error)));
    }
    
    // Print new prompt
    term.write('\r\n$ ');
  };
  
  // Reconnect function
  const handleReconnect = () => {
    if (xtermRef.current) {
      xtermRef.current.dispose();
      xtermRef.current = null;
    }
    
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }
    
    setIsConnected(false);
    setCommandBuffer('');
    setWsConnectionAttempted(false);
    
    // Force re-mount of terminal component
    setTimeout(() => {
      if (terminalRef.current && vmId) {
        // The useEffect will handle the reconnection
      }
    }, 500);
  };
  
  if (!vmId) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="flex flex-col items-center">
          <span className="text-muted-foreground">No MicroVM connected</span>
          <span className="text-xs text-muted-foreground mt-1">Select or create a MicroVM to use the terminal</span>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Terminal toolbar */}
      <div className="flex justify-between items-center p-2 bg-card border-b">
        <div className="text-sm">
          <span className="text-muted-foreground mr-2">VM:</span>
          <span className="font-mono">{vmId}</span>
          {isDirectVm && <span className="ml-2 text-xs text-green-500">(Direct VM)</span>}
          <span className={`ml-2 inline-block w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleReconnect}
          disabled={!vmId}
        >
          Reconnect
        </Button>
      </div>
      
      {/* Terminal container */}
      <div 
        ref={terminalRef} 
        className="flex-grow bg-black overflow-hidden"
        style={{ padding: '4px' }}
      />
    </div>
  );
} 