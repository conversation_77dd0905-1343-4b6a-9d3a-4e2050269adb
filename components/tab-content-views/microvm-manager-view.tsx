/**
 * MicroVM Manager View
 * 
 * This component is used as a tab content view for managing MicroVMs.
 */

import { useEffect, useState } from "react";
import { useWorkspaceStore } from "@/lib/stores/workspace-store";
import { MicroVmManager } from "@/components/microvm/manager";
import { toast } from "@/components/ui/use-toast";

/**
 * MicroVM Manager View
 * 
 * This component is used as a tab content view for managing MicroVMs.
 */
export function MicroVmManagerView() {
  const { 
    projectId, 
    setContainerId, 
    setContainerType, 
    setContainerStatus, 
    addTab 
  } = useWorkspaceStore();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  // Handle selecting a MicroVM
  const handleSelectVM = (vmId: string) => {
    // Set container ID and type
    setContainerId(vmId);
    setContainerType("microvm");
    setContainerStatus("running");
    
    // Add terminal tab
    addTab({
      id: "microvm-terminal",
      title: "MicroVM Terminal",
      icon: null, // Will be set by the tab renderer
      content: null,
      isActive: true,
    });
    
    toast({
      title: "MicroVM Selected",
      description: `MicroVM ${vmId} is now active`,
    });
  };

  return (
    <div className="h-full overflow-auto p-4">
      <MicroVmManager 
        projectId={projectId || undefined} 
        onSelectVM={handleSelectVM} 
      />
    </div>
  );
}
