"use client"

import React from 'react'
import { PluginManager } from '@/components/plugins'
import { cn } from '@/lib/utils'

interface PluginManagerViewProps {
  className?: string
}

/**
 * PluginManagerView component
 * 
 * Renders the plugin manager in a tab content view
 */
export function PluginManagerView({ className }: PluginManagerViewProps) {
  return (
    <div className={cn("h-full w-full overflow-auto", className)}>
      <PluginManager />
    </div>
  )
}
