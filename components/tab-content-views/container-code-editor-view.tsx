"use client";

import { useState } from "react";
import { ContainerCodeEditor } from "@/components/code-editor/container-code-editor";
import { FileNode } from "@/components/file-explorer/file-tree";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useContainer } from "@/lib/contexts/container-context";

interface ContainerCodeEditorViewProps {
  className?: string;
}

/**
 * Container Code Editor View
 * 
 * This component is used as a tab content view for the container code editor.
 * It provides a code editor that interacts with the container filesystem.
 */
export function ContainerCodeEditorView({ className }: ContainerCodeEditorViewProps) {
  const [currentFile, setCurrentFile] = useState<FileNode | null>(null);
  const { containerStatus } = useContainer();

  const handleFileChange = (file: FileNode) => {
    setCurrentFile(file);
  };

  if (containerStatus !== "ready") {
    return (
      <div className="flex items-center justify-center h-full">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Container not ready</AlertTitle>
          <AlertDescription>
            The container is not running. Start the container to access the code editor.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="h-full">
      <ContainerCodeEditor 
        className={className} 
        onFileChange={handleFileChange} 
      />
    </div>
  );
}
