/**
 * MicroVM Terminal View
 * 
 * This component is used as a tab content view for MicroVM terminals.
 */

import { useEffect, useState } from "react";
import { useWorkspaceStore } from "@/lib/stores/workspace-store";
import { MicroVmTerminal } from "@/components/microvm/terminal";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";

/**
 * MicroVM Terminal View
 * 
 * This component is used as a tab content view for MicroVM terminals.
 */
export function MicroVmTerminalView() {
  const { containerId, containerType } = useWorkspaceStore();
  const [mounted, setMounted] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  if (!containerId || containerType !== "microvm") {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-muted-foreground">No MicroVM is currently active</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-4"
            onClick={() => router.push("/microvm")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go to MicroVMs
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto p-4">
      <MicroVmTerminal 
        vmId={containerId} 
        height="100%" 
        autoConnect={true} 
      />
    </div>
  );
}
