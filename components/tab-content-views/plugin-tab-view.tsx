"use client"

import React from 'react'
import { PluginTabRenderer } from '@/components/plugins/plugin-tab-renderer'
import { cn } from '@/lib/utils'

interface PluginTabViewProps {
  tabId: string
  location?: 'main' | 'left' | 'right' | 'bottom'
  className?: string
}

/**
 * PluginTabView component
 * 
 * Renders a plugin tab in the tab content view system
 */
export function PluginTabView({
  tabId,
  location = 'main',
  className
}: PluginTabViewProps) {
  return (
    <div className={cn("h-full w-full", className)}>
      <PluginTabRenderer location={location} tabId={tabId} />
    </div>
  )
}
