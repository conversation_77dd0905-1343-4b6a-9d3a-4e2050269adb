/**
 * Proxmox type definitions
 */

/**
 * Proxmox authentication ticket
 */
export interface ProxmoxTicket {
  /**
   * Authentication ticket
   */
  ticket: string;
  
  /**
   * CSRF prevention token
   */
  csrfToken?: string;
  
  /**
   * Username
   */
  username?: string;
}

/**
 * VM status enum
 */
export enum VMStatus {
  RUNNING = 'running',
  STOPPED = 'stopped',
  PAUSED = 'paused',
  SUSPENDED = 'suspended',
  UNKNOWN = 'unknown'
}