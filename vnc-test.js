/**
 * VNC Test Script
 * 
 * This script tests VNC connectivity to the LXD container.
 */

const net = require('net');

// Configuration
const config = {
  host: '*************',
  port: 5901
};

// Create a socket connection to the VNC server
const socket = new net.Socket();

// Connect to the VNC server
socket.connect(config.port, config.host, () => {
  console.log(`Connected to VNC server at ${config.host}:${config.port}`);
  
  // Wait for the RFB handshake
  socket.once('data', (data) => {
    const version = data.toString('utf8', 0, 12);
    console.log(`VNC Server Version: ${version}`);
    
    // Close the connection
    socket.end();
    console.log('Connection closed');
  });
});

// Handle connection errors
socket.on('error', (err) => {
  console.error('VNC connection error:', err.message);
});

// Handle connection close
socket.on('close', () => {
  console.log('Connection closed');
});
