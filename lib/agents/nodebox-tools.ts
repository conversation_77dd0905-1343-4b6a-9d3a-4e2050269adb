/**
 * Nodebox Agent Tools
 * 
 * Provides AI agents with tools to interact with Nodebox instances
 * for file reading, creating, editing, and project management
 */

import { useNodeboxStore } from '@/lib/stores/nodebox-store';
import { ProjectTemplate } from '@/lib/nodebox-runtime/api/nodebox-types';

// Tool definitions for AI agents
export interface NodeboxTool {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required: string[];
  };
  execute: (params: any) => Promise<any>;
}

// Get the active instance ID helper
const getActiveInstanceId = (): string | null => {
  const store = useNodeboxStore.getState();
  return store.activeInstanceId;
};

// File reading tool
export const readFileNodeboxTool: NodeboxTool = {
  name: 'read_file_nodebox',
  description: 'Read the contents of a file from the active Nodebox instance',
  parameters: {
    type: 'object',
    properties: {
      path: {
        type: 'string',
        description: 'The file path to read (e.g., "/src/App.tsx", "/package.json")'
      }
    },
    required: ['path']
  },
  execute: async (params: { path: string }) => {
    const instanceId = getActiveInstanceId();
    if (!instanceId) {
      throw new Error('No active Nodebox instance found');
    }

    const store = useNodeboxStore.getState();
    const content = await store.readFile(instanceId, params.path);
    
    if (content === null) {
      throw new Error(`File not found: ${params.path}`);
    }

    return {
      path: params.path,
      content,
      success: true
    };
  }
};

// File writing tool
export const writeFileNodeboxTool: NodeboxTool = {
  name: 'write_file_nodebox',
  description: 'Write content to a file in the active Nodebox instance',
  parameters: {
    type: 'object',
    properties: {
      path: {
        type: 'string',
        description: 'The file path to write to (e.g., "/src/App.tsx", "/README.md")'
      },
      content: {
        type: 'string',
        description: 'The content to write to the file'
      }
    },
    required: ['path', 'content']
  },
  execute: async (params: { path: string; content: string }) => {
    const instanceId = getActiveInstanceId();
    if (!instanceId) {
      throw new Error('No active Nodebox instance found');
    }

    const store = useNodeboxStore.getState();
    const success = await store.writeFile(instanceId, params.path, params.content);
    
    if (!success) {
      throw new Error(`Failed to write file: ${params.path}`);
    }

    return {
      path: params.path,
      bytesWritten: params.content.length,
      success: true
    };
  }
};

// File creation tool
export const createFileNodeboxTool: NodeboxTool = {
  name: 'create_file_nodebox',
  description: 'Create a new file in the active Nodebox instance',
  parameters: {
    type: 'object',
    properties: {
      path: {
        type: 'string',
        description: 'The file path to create (e.g., "/src/components/Button.tsx")'
      },
      content: {
        type: 'string',
        description: 'The initial content for the file',
        default: ''
      }
    },
    required: ['path']
  },
  execute: async (params: { path: string; content?: string }) => {
    const instanceId = getActiveInstanceId();
    if (!instanceId) {
      throw new Error('No active Nodebox instance found');
    }

    const store = useNodeboxStore.getState();
    const success = await store.createFile(instanceId, params.path, params.content || '');
    
    if (!success) {
      throw new Error(`Failed to create file: ${params.path}`);
    }

    return {
      path: params.path,
      created: true,
      success: true
    };
  }
};

// Directory creation tool
export const createDirectoryNodeboxTool: NodeboxTool = {
  name: 'create_directory_nodebox',
  description: 'Create a new directory in the active Nodebox instance',
  parameters: {
    type: 'object',
    properties: {
      path: {
        type: 'string',
        description: 'The directory path to create (e.g., "/src/components", "/docs")'
      }
    },
    required: ['path']
  },
  execute: async (params: { path: string }) => {
    const instanceId = getActiveInstanceId();
    if (!instanceId) {
      throw new Error('No active Nodebox instance found');
    }

    const store = useNodeboxStore.getState();
    const success = await store.createDirectory(instanceId, params.path);
    
    if (!success) {
      throw new Error(`Failed to create directory: ${params.path}`);
    }

    return {
      path: params.path,
      created: true,
      success: true
    };
  }
};

// File deletion tool
export const deleteFileNodeboxTool: NodeboxTool = {
  name: 'delete_file_nodebox',
  description: 'Delete a file from the active Nodebox instance',
  parameters: {
    type: 'object',
    properties: {
      path: {
        type: 'string',
        description: 'The file path to delete (e.g., "/src/unused.js")'
      }
    },
    required: ['path']
  },
  execute: async (params: { path: string }) => {
    const instanceId = getActiveInstanceId();
    if (!instanceId) {
      throw new Error('No active Nodebox instance found');
    }

    const store = useNodeboxStore.getState();
    const success = await store.deleteFile(instanceId, params.path);
    
    if (!success) {
      throw new Error(`Failed to delete file: ${params.path}`);
    }

    return {
      path: params.path,
      deleted: true,
      success: true
    };
  }
};

// List files tool
export const listFilesNodeboxTool: NodeboxTool = {
  name: 'list_files_nodebox',
  description: 'List all files and directories in the active Nodebox instance',
  parameters: {
    type: 'object',
    properties: {
      path: {
        type: 'string',
        description: 'The directory path to list (defaults to root "/")',
        default: '/'
      }
    },
    required: []
  },
  execute: async (params: { path?: string }) => {
    const instanceId = getActiveInstanceId();
    if (!instanceId) {
      throw new Error('No active Nodebox instance found');
    }

    const store = useNodeboxStore.getState();
    const files = store.fileSystem[instanceId] || [];
    
    const targetPath = params.path || '/';
    const filteredFiles = files.filter(file => {
      if (targetPath === '/') {
        return !file.path.includes('/', 1); // Root level files only
      }
      return file.path.startsWith(targetPath) && file.path !== targetPath;
    });

    return {
      path: targetPath,
      files: filteredFiles.map(file => ({
        name: file.name,
        path: file.path,
        type: file.type,
        size: file.size,
        lastModified: file.lastModified
      })),
      count: filteredFiles.length,
      success: true
    };
  }
};

// Run command tool
export const runCommandNodeboxTool: NodeboxTool = {
  name: 'run_command_nodebox',
  description: 'Run a command in the active Nodebox instance terminal',
  parameters: {
    type: 'object',
    properties: {
      command: {
        type: 'string',
        description: 'The command to run (e.g., "npm", "node", "tsc")'
      },
      args: {
        type: 'array',
        items: { type: 'string' },
        description: 'Command arguments (e.g., ["install", "lodash"] for npm install lodash)',
        default: []
      }
    },
    required: ['command']
  },
  execute: async (params: { command: string; args?: string[] }) => {
    const instanceId = getActiveInstanceId();
    if (!instanceId) {
      throw new Error('No active Nodebox instance found');
    }

    const store = useNodeboxStore.getState();
    const process = await store.runCommand(instanceId, params.command, params.args || []);
    
    if (!process) {
      throw new Error(`Failed to run command: ${params.command}`);
    }

    return {
      command: params.command,
      args: params.args || [],
      processId: process.id,
      status: process.status,
      success: true
    };
  }
};

// Create project tool
export const createProjectNodeboxTool: NodeboxTool = {
  name: 'create_project_nodebox',
  description: 'Create a new Nodebox project from a template',
  parameters: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: 'The name for the new project'
      },
      template: {
        type: 'string',
        enum: ['react', 'nextjs', 'express', 'vanilla-js', 'typescript', 'research-dashboard', 'data-analysis', 'custom'],
        description: 'The project template to use'
      },
      projectId: {
        type: 'string',
        description: 'Optional project ID for organization',
        default: undefined
      }
    },
    required: ['name', 'template']
  },
  execute: async (params: { name: string; template: ProjectTemplate; projectId?: string }) => {
    const store = useNodeboxStore.getState();
    const instance = await store.createFromTemplate(params.template, params.name, params.projectId);
    
    if (!instance) {
      throw new Error(`Failed to create project: ${params.name}`);
    }

    // Load the file system for the new instance
    await store.loadFileSystem(instance.id);

    return {
      name: params.name,
      template: params.template,
      instanceId: instance.id,
      projectId: params.projectId,
      created: true,
      success: true
    };
  }
};

// Get project info tool
export const getProjectInfoNodeboxTool: NodeboxTool = {
  name: 'get_project_info_nodebox',
  description: 'Get information about the active Nodebox project',
  parameters: {
    type: 'object',
    properties: {},
    required: []
  },
  execute: async () => {
    const store = useNodeboxStore.getState();
    const activeInstance = store.getActiveInstance();
    
    if (!activeInstance) {
      throw new Error('No active Nodebox instance found');
    }

    const files = store.fileSystem[activeInstance.id] || [];
    const processes = store.processes[activeInstance.id] || [];
    const previews = store.previews[activeInstance.id] || [];

    return {
      instance: {
        id: activeInstance.id,
        name: activeInstance.config.name,
        template: activeInstance.config.template,
        status: activeInstance.status,
        createdAt: activeInstance.createdAt
      },
      fileCount: files.length,
      processCount: processes.length,
      previewCount: previews.length,
      success: true
    };
  }
};

// Export all tools as an array for easy registration
export const nodeboxTools: NodeboxTool[] = [
  readFileNodeboxTool,
  writeFileNodeboxTool,
  createFileNodeboxTool,
  createDirectoryNodeboxTool,
  deleteFileNodeboxTool,
  listFilesNodeboxTool,
  runCommandNodeboxTool,
  createProjectNodeboxTool,
  getProjectInfoNodeboxTool
];

// Helper function to convert tools to AI SDK format
export const convertToAISDKTools = (tools: NodeboxTool[]) => {
  return tools.reduce((acc, tool) => {
    acc[tool.name] = {
      description: tool.description,
      parameters: tool.parameters,
      execute: tool.execute
    };
    return acc;
  }, {} as Record<string, any>);
};

// Export individual tool executors for direct use
export const nodeboxToolExecutors = {
  readFile: readFileNodeboxTool.execute,
  writeFile: writeFileNodeboxTool.execute,
  createFile: createFileNodeboxTool.execute,
  createDirectory: createDirectoryNodeboxTool.execute,
  deleteFile: deleteFileNodeboxTool.execute,
  listFiles: listFilesNodeboxTool.execute,
  runCommand: runCommandNodeboxTool.execute,
  createProject: createProjectNodeboxTool.execute,
  getProjectInfo: getProjectInfoNodeboxTool.execute
};
