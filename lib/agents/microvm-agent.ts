/**
 * MicroVM Agent Implementation
 * Based on AI SDK agentic patterns (https://ai-sdk.dev/docs/foundations/agents)
 */

import { streamText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { type ExecOptions } from 'child_process';
import { Message } from 'ai';

// Define schema for filesystem operations
const filesystemSchema = z.object({
  operation: z.enum(["read", "write", "list", "delete", "exists"]),
  path: z.string().describe("The file path"),
  content: z.string().optional().describe("Content for write operations"),
});

// Define schema for command execution
const commandSchema = z.object({
  command: z.string().describe("The command to execute in the VM"),
});

// Define schema for Next.js operations
const nextjsSchema = z.object({
  operation: z.enum(["create", "start", "analyze"]),
  projectName: z.string().optional().describe("Name of the Next.js project"),
  template: z.string().optional().describe("Template to use (e.g., 'app', 'default')"),
  port: z.number().optional().describe("Port to run the server on"),
  path: z.string().optional().describe("Path to analyze or operate on"),
});

// Define schema for user confirmation
const askForConfirmationSchema = z.object({
  message: z.string().describe("The message to ask for confirmation"),
});

// Define schema for structured answer to enforce consistent responses
const answerSchema = z.object({
  explanation: z.string().describe("Explanation of what was done"),
  nextSteps: z.array(z.string()).describe("Suggested next steps"),
  success: z.boolean().describe("Whether the operation was successful"),
  errorMessage: z.string().optional().describe("Error message if operation failed"),
});

/**
 * Create a MicroVM agent stream for handling complex tasks
 */
export async function createMicroVmAgentStream(
  vmId: string,
  messages: Array<Message | Omit<Message, 'id'>>
) {
  // Define the system prompt with instructions on task handling
  const systemPrompt = `You are an AI assistant running in a MicroVM environment with ID: ${vmId}.
You have access to tools that can interact with the filesystem, run commands, and manage Next.js applications.
Your goal is to help users build, deploy, and manage web applications in this isolated environment.

When solving complex tasks:
1. Break down problems into smaller steps
2. Use appropriate tools for each step
3. Explain what you're doing and why
4. Provide clear results and next steps

The filesystem is isolated within the VM and the default workspace directory is /app.`;

  // Create a stream using AI SDK's streamText function with multi-step tool usage pattern
  return streamText({
    model: openai('gpt-4o'),
    system: systemPrompt,
    messages,
    maxSteps: 15, // Allow up to 15 steps for complex tasks
    temperature: 0.7,
    toolCallStreaming: true, // Enable streaming of tool calls
    tools: {
      // File system operations tool
      filesystem: tool({
        description: "Perform filesystem operations in the MicroVM",
        parameters: filesystemSchema,
        execute: async ({ operation, path, content }) => {
          try {
            const response = await fetch("/api/microvm/filesystem", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ vmId, operation, path, content }),
            });
            
            if (!response.ok) {
              throw new Error(`Failed to perform filesystem operation: ${response.status}`);
            }
            
            return await response.json();
          } catch (error) {
            console.error("Error performing filesystem operation:", error);
            return { error: error instanceof Error ? error.message : "Unknown error" };
          }
        }
      }),
      
      // Command execution tool
      execute: tool({
        description: "Execute a command in the MicroVM environment",
        parameters: commandSchema,
        execute: async ({ command }) => {
          try {
            const response = await fetch("/api/microvm/command", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ vmId, command }),
            });
            
            if (!response.ok) {
              throw new Error(`Failed to execute command: ${response.status}`);
            }
            
            const result = await response.json();
            return {
              result: result.result,
              error: result.error
            };
          } catch (error) {
            console.error("Error executing command:", error);
            return { error: error instanceof Error ? error.message : "Unknown error" };
          }
        }
      }),
      
      // Next.js operations tool
      nextjs: tool({
        description: "Manage Next.js applications in the MicroVM",
        parameters: nextjsSchema,
        execute: async ({ operation, projectName, template, port, path }) => {
          try {
            if (operation === "create") {
              // Create a new Next.js project
              const response = await fetch("/api/microvm/command", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ 
                  vmId, 
                  command: `cd /app && npx --yes create-next-app ${projectName} --js ${template ? `--example ${template}` : ''} --tailwind --eslint --app --src-dir --import-alias "@/*"` 
                }),
              });
              
              if (!response.ok) {
                throw new Error(`Failed to create Next.js project: ${response.status}`);
              }
              
              return await response.json();
            } else if (operation === "start") {
              // Start a Next.js development server
              const projectPath = projectName ? `/app/${projectName}` : path || '/app';
              const serverPort = port || 3000;
              
              const response = await fetch("/api/microvm/command", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ 
                  vmId, 
                  command: `cd ${projectPath} && (npm run dev -- -p ${serverPort} > /tmp/nextjs-dev.log 2>&1 &) && echo "Started server"` 
                }),
              });
              
              if (!response.ok) {
                throw new Error(`Failed to start Next.js server: ${response.status}`);
              }
              
              return {
                ...(await response.json()),
                port: serverPort,
                url: `http://localhost:${serverPort}`
              };
            } else if (operation === "analyze") {
              // Analyze a Next.js project structure
              const projectPath = projectName ? `/app/${projectName}` : path || '/app';
              
              const response = await fetch("/api/microvm/command", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ 
                  vmId, 
                  command: `find ${projectPath} -type f -not -path "*/node_modules/*" -not -path "*/.git/*" | sort` 
                }),
              });
              
              if (!response.ok) {
                throw new Error(`Failed to analyze Next.js project: ${response.status}`);
              }
              
              return await response.json();
            }
            
            return { error: "Invalid operation" };
          } catch (error) {
            console.error("Error in Next.js operation:", error);
            return { error: error instanceof Error ? error.message : "Unknown error" };
          }
        }
      }),
      
      // User confirmation tool (client-side)
      askForConfirmation: tool({
        description: "Ask the user for confirmation before proceeding with an action",
        parameters: askForConfirmationSchema,
        // No execute function - this will be handled on the client side
      }),
      
      // Structured answer tool to ensure consistent responses
      answer: tool({
        description: "Provide a structured answer with explanation and next steps",
        parameters: answerSchema,
        // No execute function - invoking it will terminate the agent
      })
    },
    // Optional callback to track each step
    onStepFinish({ text, toolCalls, toolResults, finishReason, usage }) {
      console.log(`Step completed: ${toolCalls ? toolCalls.length : 0} tools called`);
    },
  });
}

/**
 * Sequential Processing Pattern - Execute a series of steps in order
 * Useful for workflows with well-defined sequences
 */
export async function sequentialProcess(vmId: string, steps: Array<(result?: any) => Promise<any>>) {
  let result: any = null;
  
  for (const step of steps) {
    result = await step(result);
  }
  
  return result;
}

/**
 * Evaluator-Optimizer Pattern - Iteratively improve results
 * Useful for tasks that require quality checks and refinement
 */
export async function evaluateAndOptimize(
  vmId: string,
  generate: (input: any) => Promise<any>,
  evaluate: (result: any) => Promise<{ score: number; feedback: string }>,
  maxIterations: number = 3,
  threshold: number = 0.8
) {
  let result = await generate(null);
  let iterations = 0;
  
  while (iterations < maxIterations) {
    const evaluation = await evaluate(result);
    
    if (evaluation.score >= threshold) {
      return { result, evaluation, iterations };
    }
    
    result = await generate({ previous: result, feedback: evaluation.feedback });
    iterations++;
  }
  
  return { result, iterations };
} 