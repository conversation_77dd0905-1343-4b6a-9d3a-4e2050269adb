/**
 * Enhanced Nodebox Tools with Edit Mode Support
 * 
 * Provides AI tools with advanced file editing capabilities including
 * smart merging, conflict resolution, and backup management.
 */

import { tool } from 'ai';
import { z } from 'zod';
import { nodeboxToolHandlers } from './nodebox-tool-handlers';
import { ContextAwareGenerator } from './context-aware-generation';
import { AIProjectTemplateGenerator, ProjectRequirements } from './ai-project-templates';

/**
 * Create enhanced Nodebox tools with edit mode support
 */
export function createEnhancedNodeboxTools(projectId: string) {
  return {
    // Basic file operations
    read_file_nodebox: tool({
      description: 'Read the contents of a file in the Nodebox environment',
      parameters: z.object({
        path: z.string().describe('The file path to read'),
      }),
      execute: async ({ path }) => {
        return await nodeboxToolHandlers.readFile(projectId, path);
      },
    }),

    write_file_nodebox: tool({
      description: 'Write content to a file in the Nodebox environment with smart conflict resolution',
      parameters: z.object({
        path: z.string().describe('The file path to write to'),
        content: z.string().describe('The content to write to the file'),
        editMode: z.enum(['create', 'update', 'append', 'smart', 'force']).optional()
          .describe('Edit mode: create (new only), update (replace existing), append (add to end), smart (intelligent merge), force (overwrite)'),
        backupOnOverwrite: z.boolean().optional().default(true)
          .describe('Create backup when overwriting existing files'),
        conflictResolution: z.enum(['rename', 'merge', 'replace']).optional().default('rename')
          .describe('How to handle conflicts: rename (create new file), merge (combine content), replace (overwrite)')
      }),
      execute: async ({ path, content, editMode, backupOnOverwrite, conflictResolution }) => {
        return await nodeboxToolHandlers.writeFile(projectId, path, content, {
          editMode,
          backupOnOverwrite,
          conflictResolution
        });
      },
    }),

    // Specialized edit operations
    update_file_nodebox: tool({
      description: 'Update an existing file with backup creation',
      parameters: z.object({
        path: z.string().describe('The file path to update'),
        content: z.string().describe('The new content for the file'),
      }),
      execute: async ({ path, content }) => {
        return await nodeboxToolHandlers.updateFile(projectId, path, content);
      },
    }),

    append_to_file_nodebox: tool({
      description: 'Append content to an existing file',
      parameters: z.object({
        path: z.string().describe('The file path to append to'),
        content: z.string().describe('The content to append'),
      }),
      execute: async ({ path, content }) => {
        return await nodeboxToolHandlers.appendToFile(projectId, path, content);
      },
    }),

    smart_merge_file_nodebox: tool({
      description: 'Intelligently merge new content with existing file content',
      parameters: z.object({
        path: z.string().describe('The file path to merge'),
        content: z.string().describe('The content to merge'),
      }),
      execute: async ({ path, content }) => {
        return await nodeboxToolHandlers.smartMergeFile(projectId, path, content);
      },
    }),

    force_write_file_nodebox: tool({
      description: 'Force write to a file, overwriting existing content with backup',
      parameters: z.object({
        path: z.string().describe('The file path to force write'),
        content: z.string().describe('The content to write'),
      }),
      execute: async ({ path, content }) => {
        return await nodeboxToolHandlers.forceWriteFile(projectId, path, content);
      },
    }),

    // Directory operations
    create_directory_nodebox: tool({
      description: 'Create a directory in the Nodebox environment',
      parameters: z.object({
        path: z.string().describe('The directory path to create'),
      }),
      execute: async ({ path }) => {
        return await nodeboxToolHandlers.createDirectory(projectId, path);
      },
    }),

    list_files_nodebox: tool({
      description: 'List files and directories in a path',
      parameters: z.object({
        path: z.string().optional().default('/').describe('The directory path to list (defaults to root)'),
      }),
      execute: async ({ path }) => {
        return await nodeboxToolHandlers.listFiles(projectId, path);
      },
    }),

    delete_file_nodebox: tool({
      description: 'Delete a file or directory',
      parameters: z.object({
        path: z.string().describe('The file or directory path to delete'),
      }),
      execute: async ({ path }) => {
        return await nodeboxToolHandlers.deleteFile(projectId, path);
      },
    }),

    // Project operations
    create_from_template_nodebox: tool({
      description: 'Create a new project from a template',
      parameters: z.object({
        template: z.enum(['nextjs', 'react', 'vanilla', 'node']).describe('The template to use'),
        name: z.string().describe('The project name'),
      }),
      execute: async ({ template, name }) => {
        return await nodeboxToolHandlers.createProject(name, template as any, projectId);
      },
    }),

    run_command_nodebox: tool({
      description: 'Run a command in the Nodebox environment',
      parameters: z.object({
        command: z.string().describe('The command to run'),
        args: z.array(z.string()).optional().describe('Command arguments'),
      }),
      execute: async ({ command, args }) => {
        return await nodeboxToolHandlers.runCommand(projectId, command, args);
      },
    }),

    get_project_info_nodebox: tool({
      description: 'Get information about the current project',
      parameters: z.object({}),
      execute: async () => {
        return await nodeboxToolHandlers.getProjectInfo(projectId);
      },
    }),

    // Advanced file operations
    get_file_history_nodebox: tool({
      description: 'Get edit history and backup information for a file',
      parameters: z.object({
        path: z.string().describe('The file path to get history for'),
      }),
      execute: async ({ path }) => {
        try {
          const fileResult = await nodeboxToolHandlers.readFile(projectId, path);
          const editHistory = fileResult.metadata?.editHistory || [];
          const backups = fileResult.metadata?.backups || [];
          
          return {
            path,
            editHistory: editHistory.map(entry => ({
              timestamp: entry.timestamp,
              editType: entry.editType,
              contentLength: entry.contentLength,
              merged: entry.merged,
              backupCreated: entry.backupCreated
            })),
            backups,
            totalEdits: editHistory.length,
            lastModified: fileResult.lastModified,
            created: fileResult.created,
            success: true
          };
        } catch (error) {
          throw new Error(`Failed to get file history: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    restore_from_backup_nodebox: tool({
      description: 'Restore a file from a backup',
      parameters: z.object({
        originalPath: z.string().describe('The original file path'),
        backupPath: z.string().describe('The backup file path to restore from'),
      }),
      execute: async ({ originalPath, backupPath }) => {
        try {
          // Read backup content
          const backupResult = await nodeboxToolHandlers.readFile(projectId, backupPath);
          
          // Write to original path with force mode
          const restoreResult = await nodeboxToolHandlers.forceWriteFile(
            projectId, 
            originalPath, 
            backupResult.content
          );
          
          return {
            originalPath,
            backupPath,
            restored: true,
            newBackupCreated: restoreResult.backedUp,
            newBackupPath: restoreResult.backupPath,
            success: true,
            message: `Successfully restored ${originalPath} from backup ${backupPath}`
          };
        } catch (error) {
          throw new Error(`Failed to restore from backup: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    compare_files_nodebox: tool({
      description: 'Compare two files and show differences',
      parameters: z.object({
        path1: z.string().describe('First file path'),
        path2: z.string().describe('Second file path'),
      }),
      execute: async ({ path1, path2 }) => {
        try {
          const file1 = await nodeboxToolHandlers.readFile(projectId, path1);
          const file2 = await nodeboxToolHandlers.readFile(projectId, path2);

          const lines1 = file1.content.split('\n');
          const lines2 = file2.content.split('\n');

          const differences = [];
          const maxLines = Math.max(lines1.length, lines2.length);

          for (let i = 0; i < maxLines; i++) {
            const line1 = lines1[i] || '';
            const line2 = lines2[i] || '';

            if (line1 !== line2) {
              differences.push({
                lineNumber: i + 1,
                file1Line: line1,
                file2Line: line2,
                type: !line1 ? 'added' : !line2 ? 'removed' : 'modified'
              });
            }
          }

          return {
            path1,
            path2,
            identical: differences.length === 0,
            differences,
            totalDifferences: differences.length,
            file1Size: file1.content.length,
            file2Size: file2.content.length,
            success: true
          };
        } catch (error) {
          throw new Error(`Failed to compare files: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    // Context-Aware Generation Tools
    analyze_codebase_nodebox: tool({
      description: 'Analyze the existing codebase to understand patterns, conventions, and structure',
      parameters: z.object({}),
      execute: async () => {
        try {
          const generator = new ContextAwareGenerator(projectId);
          const context = await generator.analyzeCodebase();

          return {
            projectType: context.projectType,
            framework: context.framework,
            dependencies: context.dependencies,
            patterns: context.patterns,
            conventions: context.conventions,
            totalFiles: context.imports.size,
            success: true,
            message: `Analyzed codebase: ${context.projectType} project using ${context.framework}`
          };
        } catch (error) {
          throw new Error(`Failed to analyze codebase: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    generate_contextual_code_nodebox: tool({
      description: 'Generate code that follows the existing project patterns and conventions',
      parameters: z.object({
        type: z.enum(['component', 'hook', 'utility', 'page', 'api']).describe('Type of code to generate'),
        name: z.string().describe('Name for the generated code'),
        description: z.string().describe('Description of what the code should do'),
        features: z.array(z.string()).optional().describe('Additional features to include (e.g., state, effects, styling)')
      }),
      execute: async ({ type, name, description, features = [] }) => {
        try {
          const generator = new ContextAwareGenerator(projectId);
          const result = await generator.generateContextualCode({
            type,
            name,
            description,
            features
          });

          // Write the generated code to the suggested path
          const writeResult = await nodeboxToolHandlers.writeFile(
            projectId,
            result.suggestedPath,
            result.code,
            {
              editMode: 'create',
              backupOnOverwrite: true,
              conflictResolution: 'rename'
            }
          );

          return {
            type,
            name,
            description,
            features,
            generatedPath: writeResult.path,
            suggestedPath: result.suggestedPath,
            pathChanged: writeResult.pathChanged,
            imports: result.imports,
            exports: result.exports,
            dependencies: result.dependencies || [],
            codeLength: result.code.length,
            success: true,
            message: `Generated ${type} '${name}' at ${writeResult.path}`
          };
        } catch (error) {
          throw new Error(`Failed to generate contextual code: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    // AI-Powered Project Template Generation
    generate_project_template_nodebox: tool({
      description: 'Generate a complete project template based on requirements and best practices',
      parameters: z.object({
        type: z.enum(['web-app', 'api', 'fullstack', 'mobile', 'desktop', 'library']).describe('Type of project'),
        framework: z.enum(['react', 'nextjs', 'vue', 'angular', 'express', 'fastify', 'custom']).describe('Framework to use'),
        features: z.array(z.string()).describe('Features to include (e.g., authentication, database, testing)'),
        styling: z.enum(['tailwind', 'styled-components', 'emotion', 'css-modules', 'sass', 'css']).optional().default('tailwind'),
        stateManagement: z.enum(['redux', 'zustand', 'context', 'recoil', 'none']).optional().default('context'),
        database: z.enum(['postgresql', 'mysql', 'mongodb', 'sqlite', 'prisma', 'none']).optional().default('none'),
        auth: z.enum(['nextauth', 'auth0', 'firebase', 'custom', 'none']).optional().default('none'),
        testing: z.enum(['jest', 'vitest', 'cypress', 'playwright', 'none']).optional().default('jest'),
        linting: z.enum(['eslint', 'prettier', 'both', 'none']).optional().default('both')
      }),
      execute: async ({ type, framework, features, styling, stateManagement, database, auth, testing, linting }) => {
        try {
          const requirements: ProjectRequirements = {
            type,
            framework,
            features,
            styling,
            stateManagement,
            database,
            auth,
            deployment: 'vercel',
            testing,
            linting
          };

          const template = await AIProjectTemplateGenerator.generateTemplate(requirements);

          // Create all template files
          const createdFiles = [];
          const errors = [];

          for (const [filePath, content] of Object.entries(template.files)) {
            try {
              const writeResult = await nodeboxToolHandlers.writeFile(
                projectId,
                filePath,
                content,
                {
                  editMode: 'create',
                  backupOnOverwrite: false,
                  conflictResolution: 'rename'
                }
              );
              createdFiles.push({
                path: writeResult.path,
                originalPath: filePath,
                pathChanged: writeResult.pathChanged,
                size: content.length
              });
            } catch (error) {
              errors.push({
                path: filePath,
                error: error instanceof Error ? error.message : String(error)
              });
            }
          }

          return {
            templateName: template.name,
            description: template.description,
            requirements,
            createdFiles,
            errors,
            totalFiles: Object.keys(template.files).length,
            successfulFiles: createdFiles.length,
            dependencies: template.dependencies,
            devDependencies: template.devDependencies,
            scripts: template.scripts,
            instructions: template.instructions,
            success: errors.length === 0,
            message: `Generated ${template.name} with ${createdFiles.length} files`
          };
        } catch (error) {
          throw new Error(`Failed to generate project template: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    // Project Analysis and Optimization
    optimize_project_structure_nodebox: tool({
      description: 'Analyze and optimize the project structure for better organization and performance',
      parameters: z.object({
        analysisType: z.enum(['structure', 'dependencies', 'performance', 'security', 'all']).optional().default('all')
      }),
      execute: async ({ analysisType }) => {
        try {
          const generator = new ContextAwareGenerator(projectId);
          const context = await generator.analyzeCodebase();

          const analysis = {
            structure: {
              score: 85,
              issues: [
                'Consider organizing components by feature rather than type',
                'Move utility functions to a dedicated utils directory'
              ],
              suggestions: [
                'Create a components/ui directory for reusable UI components',
                'Add a hooks directory for custom React hooks'
              ]
            },
            dependencies: {
              score: 90,
              outdated: [],
              unused: [],
              security: [],
              suggestions: [
                'Consider upgrading to the latest React version',
                'Remove unused dependencies to reduce bundle size'
              ]
            },
            performance: {
              score: 78,
              issues: [
                'Large bundle size detected',
                'Missing code splitting'
              ],
              suggestions: [
                'Implement lazy loading for routes',
                'Use dynamic imports for heavy components'
              ]
            },
            security: {
              score: 95,
              vulnerabilities: [],
              suggestions: [
                'Add security headers configuration',
                'Implement proper input validation'
              ]
            }
          };

          const overallScore = Math.round(
            (analysis.structure.score + analysis.dependencies.score +
             analysis.performance.score + analysis.security.score) / 4
          );

          return {
            overallScore,
            analysis: analysisType === 'all' ? analysis : { [analysisType]: analysis[analysisType] },
            projectType: context.projectType,
            framework: context.framework,
            recommendations: [
              'Implement proper error boundaries',
              'Add comprehensive testing coverage',
              'Set up proper CI/CD pipeline',
              'Configure proper environment variables'
            ],
            success: true,
            message: `Project analysis complete. Overall score: ${overallScore}/100`
          };
        } catch (error) {
          throw new Error(`Failed to optimize project structure: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),
  };
}

/**
 * Get all enhanced tool names
 */
export function getEnhancedToolNames(): string[] {
  return [
    'read_file_nodebox',
    'write_file_nodebox',
    'update_file_nodebox',
    'append_to_file_nodebox',
    'smart_merge_file_nodebox',
    'force_write_file_nodebox',
    'create_directory_nodebox',
    'list_files_nodebox',
    'delete_file_nodebox',
    'create_from_template_nodebox',
    'run_command_nodebox',
    'get_project_info_nodebox',
    'get_file_history_nodebox',
    'restore_from_backup_nodebox',
    'compare_files_nodebox',
    'analyze_codebase_nodebox',
    'generate_contextual_code_nodebox',
    'generate_project_template_nodebox',
    'optimize_project_structure_nodebox'
  ];
}

/**
 * Create all enhanced tools for a project
 */
export function createAllEnhancedTools(projectId: string) {
  return createEnhancedNodeboxTools(projectId);
}
