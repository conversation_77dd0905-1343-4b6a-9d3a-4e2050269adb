/**
 * Enhanced Server Nodebox Store
 * 
 * Uses the enhanced filesystem to prevent duplicates and manage conflicts
 */

import { EnhancedNodeboxFilesystem, FileSystemEntry } from './enhanced-nodebox-filesystem';

export interface NodeboxInstance {
  id: string;
  projectId: string;
  config: {
    name: string;
    template: string;
    description?: string;
  };
  status: 'ready' | 'running' | 'stopped' | 'error';
  createdAt: Date;
  lastAccessed: Date;
  filesystem: EnhancedNodeboxFilesystem;
}

export interface NodeboxProcess {
  id: string;
  instanceId: string;
  command: string;
  args: string[];
  status: 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  exitCode?: number;
  output: string[];
}

/**
 * Enhanced Server Nodebox Store with proper filesystem management
 */
export class EnhancedServerNodeboxStore {
  private static instances: Map<string, NodeboxInstance> = new Map();
  private static processes: Map<string, NodeboxProcess[]> = new Map();
  private static globalFilesystems: Map<string, EnhancedNodeboxFilesystem> = new Map();

  /**
   * Get active instance for a project
   */
  static getActiveInstance(projectId?: string): NodeboxInstance | null {
    if (!projectId) return null;
    
    const instance = this.instances.get(projectId);
    if (instance) {
      // Update last accessed time
      instance.lastAccessed = new Date();
    }
    
    return instance || null;
  }

  /**
   * Create a new instance with enhanced filesystem
   */
  static createInstance(projectId: string, config: any): NodeboxInstance {
    console.log(`[Enhanced Store] Creating instance for project: ${projectId}`);
    
    // Create enhanced filesystem
    const filesystem = new EnhancedNodeboxFilesystem(projectId);
    
    const instance: NodeboxInstance = {
      id: `instance_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      projectId,
      config: {
        name: config.name || `Project ${projectId}`,
        template: config.template || 'custom',
        description: config.description
      },
      status: 'ready',
      createdAt: new Date(),
      lastAccessed: new Date(),
      filesystem
    };
    
    // Store instance and filesystem
    this.instances.set(projectId, instance);
    this.globalFilesystems.set(projectId, filesystem);
    this.processes.set(projectId, []);
    
    console.log(`[Enhanced Store] Created instance: ${instance.id} for project: ${projectId}`);
    
    return instance;
  }

  /**
   * Get filesystem for a project
   */
  static getFilesystem(projectId: string): EnhancedNodeboxFilesystem | null {
    return this.globalFilesystems.get(projectId) || null;
  }

  /**
   * Get file system entries (legacy compatibility)
   */
  static getFileSystem(projectId: string): FileSystemEntry[] {
    const filesystem = this.getFilesystem(projectId);
    if (!filesystem) {
      console.warn(`[Enhanced Store] No filesystem found for project: ${projectId}`);
      return [];
    }
    
    try {
      const stats = filesystem.getStats();
      return stats.entries;
    } catch (error) {
      console.error(`[Enhanced Store] Error getting filesystem entries:`, error);
      return [];
    }
  }

  /**
   * Set file system (legacy compatibility - converts to enhanced filesystem)
   */
  static async setFileSystem(projectId: string, files: any[]): Promise<void> {
    console.log(`[Enhanced Store] Setting filesystem for project: ${projectId} with ${files.length} files`);

    let filesystem = this.getFilesystem(projectId);
    if (!filesystem) {
      // Create new filesystem if it doesn't exist
      filesystem = new EnhancedNodeboxFilesystem(projectId);
      this.globalFilesystems.set(projectId, filesystem);
    }

    // Convert legacy files to enhanced filesystem
    await this.convertLegacyFiles(filesystem, files);
  }

  /**
   * Convert legacy file format to enhanced filesystem
   */
  private static async convertLegacyFiles(filesystem: EnhancedNodeboxFilesystem, files: any[]): Promise<void> {
    try {
      // Sort files to create directories first
      const sortedFiles = files.sort((a, b) => {
        if (a.type === 'directory' && b.type === 'file') return -1;
        if (a.type === 'file' && b.type === 'directory') return 1;
        return a.path.localeCompare(b.path);
      });

      for (const file of sortedFiles) {
        try {
          if (file.type === 'directory') {
            await filesystem.createDirectory(file.path, { recursive: true });
          } else if (file.type === 'file') {
            const result = await filesystem.writeFile(file.path, file.content || '', {
              overwrite: true,
              createDirectories: true
            });
            
            if (result.path !== file.path) {
              console.log(`[Enhanced Store] File path changed due to conflict: ${file.path} -> ${result.path}`);
            }
          }
        } catch (error) {
          console.error(`[Enhanced Store] Error converting file ${file.path}:`, error);
        }
      }
      
      console.log(`[Enhanced Store] Successfully converted ${files.length} legacy files`);
    } catch (error) {
      console.error(`[Enhanced Store] Error converting legacy files:`, error);
    }
  }

  /**
   * Add a process to an instance
   */
  static addProcess(projectId: string, process: Omit<NodeboxProcess, 'id'>): NodeboxProcess {
    const fullProcess: NodeboxProcess = {
      id: `proc_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      ...process
    };
    
    const processes = this.processes.get(projectId) || [];
    processes.push(fullProcess);
    this.processes.set(projectId, processes);
    
    console.log(`[Enhanced Store] Added process: ${fullProcess.id} to project: ${projectId}`);
    
    return fullProcess;
  }

  /**
   * Get processes for a project
   */
  static getProcesses(projectId: string): NodeboxProcess[] {
    return this.processes.get(projectId) || [];
  }

  /**
   * Get all instances
   */
  static getAllInstances(): NodeboxInstance[] {
    return Array.from(this.instances.values());
  }

  /**
   * Delete an instance and its filesystem
   */
  static deleteInstance(projectId: string): boolean {
    const instance = this.instances.get(projectId);
    if (!instance) {
      return false;
    }
    
    // Clean up
    this.instances.delete(projectId);
    this.globalFilesystems.delete(projectId);
    this.processes.delete(projectId);
    
    console.log(`[Enhanced Store] Deleted instance for project: ${projectId}`);
    
    return true;
  }

  /**
   * Get filesystem statistics for all projects
   */
  static getGlobalStats(): {
    totalProjects: number;
    totalInstances: number;
    totalFiles: number;
    totalDirectories: number;
    totalSize: number;
    projects: Array<{
      projectId: string;
      instanceId: string;
      stats: ReturnType<EnhancedNodeboxFilesystem['getStats']>;
    }>;
  } {
    const projects: Array<{
      projectId: string;
      instanceId: string;
      stats: ReturnType<EnhancedNodeboxFilesystem['getStats']>;
    }> = [];
    
    let totalFiles = 0;
    let totalDirectories = 0;
    let totalSize = 0;
    
    for (const [projectId, instance] of this.instances) {
      const stats = instance.filesystem.getStats();
      projects.push({
        projectId,
        instanceId: instance.id,
        stats
      });
      
      totalFiles += stats.totalFiles;
      totalDirectories += stats.totalDirectories;
      totalSize += stats.totalSize;
    }
    
    return {
      totalProjects: this.instances.size,
      totalInstances: this.instances.size,
      totalFiles,
      totalDirectories,
      totalSize,
      projects
    };
  }

  /**
   * Export all data for backup/persistence
   */
  static exportAllData(): any {
    const data = {
      instances: Array.from(this.instances.entries()).map(([projectId, instance]) => ({
        projectId,
        instance: {
          ...instance,
          filesystem: instance.filesystem.exportState()
        }
      })),
      processes: Array.from(this.processes.entries()),
      exportedAt: new Date().toISOString(),
      version: '1.0.0'
    };
    
    console.log(`[Enhanced Store] Exported data for ${data.instances.length} projects`);
    
    return data;
  }

  /**
   * Import data from backup/persistence
   */
  static importAllData(data: any): void {
    try {
      // Clear existing data
      this.instances.clear();
      this.globalFilesystems.clear();
      this.processes.clear();
      
      // Import instances and filesystems
      for (const { projectId, instance } of data.instances) {
        const filesystem = new EnhancedNodeboxFilesystem(projectId);
        filesystem.importState(instance.filesystem);
        
        const restoredInstance: NodeboxInstance = {
          ...instance,
          filesystem,
          createdAt: new Date(instance.createdAt),
          lastAccessed: new Date(instance.lastAccessed)
        };
        
        this.instances.set(projectId, restoredInstance);
        this.globalFilesystems.set(projectId, filesystem);
      }
      
      // Import processes
      this.processes = new Map(data.processes);
      
      console.log(`[Enhanced Store] Imported data for ${data.instances.length} projects`);
    } catch (error) {
      console.error(`[Enhanced Store] Error importing data:`, error);
      throw error;
    }
  }

  /**
   * Clean up old instances (for memory management)
   */
  static cleanupOldInstances(maxAge: number = 24 * 60 * 60 * 1000): number {
    const now = new Date();
    const cutoff = new Date(now.getTime() - maxAge);
    let cleaned = 0;
    
    for (const [projectId, instance] of this.instances) {
      if (instance.lastAccessed < cutoff) {
        this.deleteInstance(projectId);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      console.log(`[Enhanced Store] Cleaned up ${cleaned} old instances`);
    }
    
    return cleaned;
  }

  /**
   * Get instance by ID
   */
  static getInstanceById(instanceId: string): NodeboxInstance | null {
    for (const instance of this.instances.values()) {
      if (instance.id === instanceId) {
        return instance;
      }
    }
    return null;
  }

  /**
   * Update instance status
   */
  static updateInstanceStatus(projectId: string, status: NodeboxInstance['status']): boolean {
    const instance = this.instances.get(projectId);
    if (!instance) {
      return false;
    }
    
    instance.status = status;
    instance.lastAccessed = new Date();
    
    console.log(`[Enhanced Store] Updated instance status: ${projectId} -> ${status}`);
    
    return true;
  }
}
