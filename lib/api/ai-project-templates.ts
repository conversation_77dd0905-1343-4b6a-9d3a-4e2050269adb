/**
 * AI-Powered Project Templates
 * 
 * Dynamic template generation based on user requirements and best practices.
 * Creates complete project structures with proper configurations.
 */

export interface ProjectRequirements {
  type: 'web-app' | 'api' | 'fullstack' | 'mobile' | 'desktop' | 'library';
  framework: 'react' | 'nextjs' | 'vue' | 'angular' | 'express' | 'fastify' | 'custom';
  features: string[];
  styling: 'tailwind' | 'styled-components' | 'emotion' | 'css-modules' | 'sass' | 'css';
  stateManagement: 'redux' | 'zustand' | 'context' | 'recoil' | 'none';
  database: 'postgresql' | 'mysql' | 'mongodb' | 'sqlite' | 'prisma' | 'none';
  auth: 'nextauth' | 'auth0' | 'firebase' | 'custom' | 'none';
  deployment: 'vercel' | 'netlify' | 'aws' | 'docker' | 'none';
  testing: 'jest' | 'vitest' | 'cypress' | 'playwright' | 'none';
  linting: 'eslint' | 'prettier' | 'both' | 'none';
}

export interface GeneratedTemplate {
  name: string;
  description: string;
  files: Record<string, string>;
  dependencies: Record<string, string>;
  devDependencies: Record<string, string>;
  scripts: Record<string, string>;
  configuration: {
    eslint?: any;
    prettier?: any;
    tailwind?: any;
    typescript?: any;
    next?: any;
  };
  instructions: string[];
}

export class AIProjectTemplateGenerator {
  /**
   * Generate a complete project template based on requirements
   */
  static async generateTemplate(requirements: ProjectRequirements): Promise<GeneratedTemplate> {
    const template: GeneratedTemplate = {
      name: this.generateProjectName(requirements),
      description: this.generateDescription(requirements),
      files: {},
      dependencies: {},
      devDependencies: {},
      scripts: {},
      configuration: {},
      instructions: []
    };

    // Generate base structure
    await this.generateBaseStructure(template, requirements);
    
    // Add framework-specific files
    await this.generateFrameworkFiles(template, requirements);
    
    // Add feature-specific files
    await this.generateFeatureFiles(template, requirements);
    
    // Add configuration files
    await this.generateConfigurationFiles(template, requirements);
    
    // Generate package.json
    await this.generatePackageJson(template, requirements);
    
    // Generate setup instructions
    template.instructions = this.generateInstructions(requirements);

    return template;
  }

  private static generateProjectName(requirements: ProjectRequirements): string {
    const { type, framework } = requirements;
    return `${framework}-${type}-starter`;
  }

  private static generateDescription(requirements: ProjectRequirements): string {
    const { type, framework, features } = requirements;
    const featureList = features.length > 0 ? ` with ${features.join(', ')}` : '';
    return `A modern ${framework} ${type} application${featureList}`;
  }

  private static async generateBaseStructure(template: GeneratedTemplate, requirements: ProjectRequirements) {
    const { framework, type } = requirements;

    // README.md
    template.files['README.md'] = `# ${template.name}

${template.description}

## Features

${requirements.features.map(feature => `- ${feature}`).join('\n')}

## Getting Started

\`\`\`bash
npm install
npm run dev
\`\`\`

## Tech Stack

- **Framework**: ${framework}
- **Styling**: ${requirements.styling}
- **State Management**: ${requirements.stateManagement}
- **Database**: ${requirements.database}
- **Authentication**: ${requirements.auth}
- **Testing**: ${requirements.testing}

## Project Structure

\`\`\`
src/
├── components/     # Reusable UI components
├── pages/         # Application pages
├── hooks/         # Custom React hooks
├── utils/         # Utility functions
├── types/         # TypeScript type definitions
├── styles/        # Global styles
└── lib/           # External library configurations
\`\`\`
`;

    // .gitignore
    template.files['.gitignore'] = `# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/

# Next.js
.next/
out/

# Production
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
`;

    // Environment template
    template.files['.env.example'] = `# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database"

# Authentication
NEXTAUTH_SECRET="your-secret-here"
NEXTAUTH_URL="http://localhost:3000"

# API Keys
API_KEY="your-api-key-here"
`;
  }

  private static async generateFrameworkFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    const { framework, styling } = requirements;

    switch (framework) {
      case 'nextjs':
        await this.generateNextJSFiles(template, requirements);
        break;
      case 'react':
        await this.generateReactFiles(template, requirements);
        break;
      case 'express':
        await this.generateExpressFiles(template, requirements);
        break;
    }
  }

  private static async generateNextJSFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    // next.config.js
    template.files['next.config.js'] = `/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['localhost'],
  },
}

module.exports = nextConfig
`;

    // app/layout.tsx
    template.files['app/layout.tsx'] = `import './globals.css'
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: '${template.name}',
  description: '${template.description}',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
`;

    // app/page.tsx
    template.files['app/page.tsx'] = `export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm">
        <h1 className="text-4xl font-bold text-center">
          Welcome to ${template.name}
        </h1>
        <p className="text-center mt-4">
          ${template.description}
        </p>
      </div>
    </main>
  )
}
`;

    // Global styles
    if (requirements.styling === 'tailwind') {
      template.files['app/globals.css'] = `@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}
`;
    }

    // Add dependencies
    template.dependencies['next'] = '^14.0.0';
    template.dependencies['react'] = '^18.0.0';
    template.dependencies['react-dom'] = '^18.0.0';
    template.devDependencies['@types/node'] = '^20.0.0';
    template.devDependencies['@types/react'] = '^18.0.0';
    template.devDependencies['@types/react-dom'] = '^18.0.0';
    template.devDependencies['typescript'] = '^5.0.0';

    // Add scripts
    template.scripts['dev'] = 'next dev';
    template.scripts['build'] = 'next build';
    template.scripts['start'] = 'next start';
    template.scripts['lint'] = 'next lint';
  }

  private static async generateReactFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    // src/App.tsx
    template.files['src/App.tsx'] = `import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to ${template.name}</h1>
        <p>${template.description}</p>
      </header>
    </div>
  );
}

export default App;
`;

    // src/index.tsx
    template.files['src/index.tsx'] = `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
`;

    // public/index.html
    template.files['public/index.html'] = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="${template.description}" />
    <title>${template.name}</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
`;

    // Add dependencies
    template.dependencies['react'] = '^18.0.0';
    template.dependencies['react-dom'] = '^18.0.0';
    template.devDependencies['@types/react'] = '^18.0.0';
    template.devDependencies['@types/react-dom'] = '^18.0.0';
    template.devDependencies['typescript'] = '^5.0.0';
    template.devDependencies['@vitejs/plugin-react'] = '^4.0.0';
    template.devDependencies['vite'] = '^5.0.0';

    // Add scripts
    template.scripts['dev'] = 'vite';
    template.scripts['build'] = 'vite build';
    template.scripts['preview'] = 'vite preview';
  }

  private static async generateExpressFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    // src/index.ts
    template.files['src/index.ts'] = `import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to ${template.name}',
    description: '${template.description}',
    version: '1.0.0'
  });
});

app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

app.listen(PORT, () => {
  console.log(\`Server is running on port \${PORT}\`);
});
`;

    // Add dependencies
    template.dependencies['express'] = '^4.18.0';
    template.dependencies['cors'] = '^2.8.5';
    template.dependencies['helmet'] = '^7.0.0';
    template.dependencies['dotenv'] = '^16.0.0';
    template.devDependencies['@types/express'] = '^4.17.0';
    template.devDependencies['@types/cors'] = '^2.8.0';
    template.devDependencies['typescript'] = '^5.0.0';
    template.devDependencies['ts-node'] = '^10.9.0';
    template.devDependencies['nodemon'] = '^3.0.0';

    // Add scripts
    template.scripts['dev'] = 'nodemon src/index.ts';
    template.scripts['build'] = 'tsc';
    template.scripts['start'] = 'node dist/index.js';
  }

  private static async generateFeatureFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    const { features, framework } = requirements;

    for (const feature of features) {
      switch (feature) {
        case 'authentication':
          await this.generateAuthFiles(template, requirements);
          break;
        case 'database':
          await this.generateDatabaseFiles(template, requirements);
          break;
        case 'api':
          await this.generateApiFiles(template, requirements);
          break;
        case 'testing':
          await this.generateTestFiles(template, requirements);
          break;
      }
    }
  }

  private static async generateAuthFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    if (requirements.auth === 'nextauth' && requirements.framework === 'nextjs') {
      // pages/api/auth/[...nextauth].ts
      template.files['pages/api/auth/[...nextauth].ts'] = `import NextAuth from 'next-auth'
import GithubProvider from 'next-auth/providers/github'

export default NextAuth({
  providers: [
    GithubProvider({
      clientId: process.env.GITHUB_ID!,
      clientSecret: process.env.GITHUB_SECRET!,
    }),
  ],
  callbacks: {
    async jwt({ token, account }) {
      if (account) {
        token.accessToken = account.access_token
      }
      return token
    },
    async session({ session, token }) {
      session.accessToken = token.accessToken
      return session
    },
  },
})
`;

      template.dependencies['next-auth'] = '^4.24.0';
    }
  }

  private static async generateDatabaseFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    if (requirements.database === 'prisma') {
      // prisma/schema.prisma
      template.files['prisma/schema.prisma'] = `generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
`;

      template.dependencies['prisma'] = '^5.0.0';
      template.dependencies['@prisma/client'] = '^5.0.0';
      template.scripts['db:generate'] = 'prisma generate';
      template.scripts['db:push'] = 'prisma db push';
      template.scripts['db:migrate'] = 'prisma migrate dev';
    }
  }

  private static async generateApiFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    if (requirements.framework === 'nextjs') {
      // pages/api/users.ts
      template.files['pages/api/users.ts'] = `import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === 'GET') {
    // Get users
    res.status(200).json({ users: [] })
  } else if (req.method === 'POST') {
    // Create user
    res.status(201).json({ message: 'User created' })
  } else {
    res.setHeader('Allow', ['GET', 'POST'])
    res.status(405).end(\`Method \${req.method} Not Allowed\`)
  }
}
`;
    }
  }

  private static async generateTestFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    if (requirements.testing === 'jest') {
      // jest.config.js
      template.files['jest.config.js'] = `module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
}
`;

      // src/setupTests.ts
      template.files['src/setupTests.ts'] = `import '@testing-library/jest-dom'
`;

      template.devDependencies['jest'] = '^29.0.0';
      template.devDependencies['@testing-library/react'] = '^13.0.0';
      template.devDependencies['@testing-library/jest-dom'] = '^6.0.0';
      template.scripts['test'] = 'jest';
      template.scripts['test:watch'] = 'jest --watch';
    }
  }

  private static async generateConfigurationFiles(template: GeneratedTemplate, requirements: ProjectRequirements) {
    // TypeScript config
    template.files['tsconfig.json'] = `{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": [
    "src",
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx"
  ],
  "exclude": [
    "node_modules"
  ]
}
`;

    // Tailwind config
    if (requirements.styling === 'tailwind') {
      template.files['tailwind.config.js'] = `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
}
`;

      template.devDependencies['tailwindcss'] = '^3.3.0';
      template.devDependencies['autoprefixer'] = '^10.4.0';
      template.devDependencies['postcss'] = '^8.4.0';
    }

    // ESLint config
    if (requirements.linting === 'eslint' || requirements.linting === 'both') {
      template.files['.eslintrc.json'] = `{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn"
  }
}
`;

      template.devDependencies['eslint'] = '^8.0.0';
      template.devDependencies['@typescript-eslint/eslint-plugin'] = '^6.0.0';
      template.devDependencies['@typescript-eslint/parser'] = '^6.0.0';
    }

    // Prettier config
    if (requirements.linting === 'prettier' || requirements.linting === 'both') {
      template.files['.prettierrc'] = `{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
`;

      template.devDependencies['prettier'] = '^3.0.0';
      template.scripts['format'] = 'prettier --write .';
    }
  }

  private static async generatePackageJson(template: GeneratedTemplate, requirements: ProjectRequirements) {
    template.files['package.json'] = JSON.stringify({
      name: template.name,
      version: '0.1.0',
      description: template.description,
      private: true,
      scripts: template.scripts,
      dependencies: template.dependencies,
      devDependencies: template.devDependencies,
      keywords: [
        requirements.framework,
        requirements.type,
        ...requirements.features
      ],
      author: '',
      license: 'MIT'
    }, null, 2);
  }

  private static generateInstructions(requirements: ProjectRequirements): string[] {
    const instructions = [
      '1. Install dependencies: `npm install`',
      '2. Copy `.env.example` to `.env` and fill in your environment variables',
    ];

    if (requirements.database === 'prisma') {
      instructions.push('3. Set up the database: `npm run db:push`');
      instructions.push('4. Generate Prisma client: `npm run db:generate`');
    }

    if (requirements.framework === 'nextjs') {
      instructions.push('5. Start the development server: `npm run dev`');
      instructions.push('6. Open http://localhost:3000 in your browser');
    } else if (requirements.framework === 'react') {
      instructions.push('5. Start the development server: `npm run dev`');
      instructions.push('6. Open http://localhost:5173 in your browser');
    } else if (requirements.framework === 'express') {
      instructions.push('5. Start the development server: `npm run dev`');
      instructions.push('6. API will be available at http://localhost:3000');
    }

    if (requirements.testing !== 'none') {
      instructions.push('7. Run tests: `npm test`');
    }

    return instructions;
  }
}
