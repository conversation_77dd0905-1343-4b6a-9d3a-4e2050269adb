/**
 * Nodebox Tool Handlers
 * 
 * Server-side implementations of Nodebox tools for the AI chat API
 * These handlers interface with the Nodebox runtime and file system
 */

import { ProjectTemplate } from '@/lib/nodebox-runtime/api/nodebox-types';
import { EnhancedServerNodeboxStore } from './enhanced-server-nodebox-store';

// Helper function to generate edit messages
function generateEditMessage(result: any, _originalPath?: string): string {
  const { edit_type, path, merged, backed_up, conflict_resolved } = result;

  let message = '';

  switch (edit_type) {
    case 'create':
      message = conflict_resolved
        ? `File created with modified path due to conflict: ${path}`
        : `File created successfully: ${path}`;
      break;

    case 'update':
      message = backed_up
        ? `File updated successfully (backup created): ${path}`
        : `File updated successfully: ${path}`;
      break;

    case 'append':
      message = `Content appended to existing file: ${path}`;
      break;

    case 'merge':
      message = backed_up
        ? `Files merged intelligently (backup created): ${path}`
        : `Files merged intelligently: ${path}`;
      break;

    case 'replace':
      message = backed_up
        ? `File replaced (backup created): ${path}`
        : `File replaced: ${path}`;
      break;

    default:
      message = `File operation completed: ${path}`;
  }

  if (merged) {
    message += ' (content merged)';
  }

  return message;
}

// Legacy compatibility wrapper for enhanced filesystem
class ServerNodeboxStore {
  static getActiveInstance(projectId?: string) {
    return EnhancedServerNodeboxStore.getActiveInstance(projectId);
  }

  static createInstance(projectId: string, config: any) {
    return EnhancedServerNodeboxStore.createInstance(projectId, config);
  }

  static getFileSystem(projectId: string) {
    return EnhancedServerNodeboxStore.getFileSystem(projectId);
  }

  static async setFileSystem(projectId: string, files: any[]): Promise<void> {
    return await EnhancedServerNodeboxStore.setFileSystem(projectId, files);
  }

  static addProcess(projectId: string, process: any) {
    return EnhancedServerNodeboxStore.addProcess(projectId, process);
  }
}

// Template configurations
const templates: Record<ProjectTemplate, any> = {
  'react': {
    name: 'React App',
    files: {
      'package.json': JSON.stringify({
        name: 'react-app',
        version: '1.0.0',
        dependencies: {
          'react': '^18.2.0',
          'react-dom': '^18.2.0',
          'typescript': '^5.0.0',
          '@types/react': '^18.2.0',
          '@types/react-dom': '^18.2.0'
        },
        scripts: {
          'start': 'react-scripts start',
          'build': 'react-scripts build',
          'dev': 'react-scripts start'
        }
      }, null, 2),
      'src/App.tsx': `import React from 'react';

function App() {
  return (
    <div className="App">
      <header>
        <h1>Hello React!</h1>
        <p>Your React application is running.</p>
      </header>
    </div>
  );
}

export default App;`,
      'src/index.tsx': `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(<App />);`,
      'public/index.html': `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>React App</title>
</head>
<body>
  <div id="root"></div>
</body>
</html>`
    }
  },
  'nextjs': {
    name: 'Next.js App',
    files: {
      'package.json': JSON.stringify({
        name: 'nextjs-app',
        version: '1.0.0',
        dependencies: {
          'next': '^14.0.0',
          'react': '^18.2.0',
          'react-dom': '^18.2.0',
          'typescript': '^5.0.0'
        },
        scripts: {
          'dev': 'next dev',
          'build': 'next build',
          'start': 'next start'
        }
      }, null, 2),
      'app/page.tsx': `export default function Home() {
  return (
    <main>
      <h1>Welcome to Next.js!</h1>
      <p>Your Next.js application is running.</p>
    </main>
  );
}`,
      'app/layout.tsx': `import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Next.js App',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  );
}`
    }
  },
  'express': {
    name: 'Express Server',
    files: {
      'package.json': JSON.stringify({
        name: 'express-server',
        version: '1.0.0',
        dependencies: {
          'express': '^4.18.0',
          'typescript': '^5.0.0',
          '@types/express': '^4.17.0'
        },
        scripts: {
          'start': 'node dist/index.js',
          'dev': 'ts-node src/index.ts',
          'build': 'tsc'
        }
      }, null, 2),
      'src/index.ts': `import express from 'express';

const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Hello from Express!' });
});

app.listen(port, () => {
  console.log(\`Server running on port \${port}\`);
});`
    }
  },
  'typescript': {
    name: 'TypeScript Project',
    files: {
      'package.json': JSON.stringify({
        name: 'typescript-project',
        version: '1.0.0',
        dependencies: {
          'typescript': '^5.0.0',
          '@types/node': '^20.0.0'
        },
        scripts: {
          'build': 'tsc',
          'dev': 'ts-node src/index.ts'
        }
      }, null, 2),
      'src/index.ts': `console.log('Hello TypeScript!');

interface User {
  id: number;
  name: string;
  email: string;
}

const user: User = {
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>'
};

console.log('User:', user);`
    }
  },
  'vanilla-js': {
    name: 'Vanilla JavaScript',
    files: {
      'index.html': `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vanilla JS App</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div id="app">
    <h1>Hello Vanilla JavaScript!</h1>
    <button id="clickBtn">Click me!</button>
  </div>
  <script src="script.js"></script>
</body>
</html>`,
      'script.js': `document.addEventListener('DOMContentLoaded', function() {
  const button = document.getElementById('clickBtn');
  let clickCount = 0;
  
  button.addEventListener('click', function() {
    clickCount++;
    button.textContent = \`Clicked \${clickCount} times!\`;
  });
});`,
      'style.css': `body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
}

#app {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}`
    }
  },
  'research-dashboard': {
    name: 'Research Dashboard',
    files: {
      'package.json': JSON.stringify({
        name: 'research-dashboard',
        version: '1.0.0',
        dependencies: {
          'react': '^18.2.0',
          'react-dom': '^18.2.0',
          'recharts': '^2.8.0'
        }
      }, null, 2),
      'src/App.tsx': `import React from 'react';

function App() {
  return (
    <div>
      <h1>Research Dashboard</h1>
      <p>Data visualization dashboard for research.</p>
    </div>
  );
}

export default App;`
    }
  },
  'data-analysis': {
    name: 'Data Analysis',
    files: {
      'package.json': JSON.stringify({
        name: 'data-analysis',
        version: '1.0.0',
        dependencies: {
          'typescript': '^5.0.0',
          '@types/node': '^20.0.0'
        }
      }, null, 2),
      'src/index.ts': `console.log('Data Analysis Project');

// Add your data analysis code here`
    }
  },
  'custom': {
    name: 'Custom Project',
    files: {
      'package.json': JSON.stringify({
        name: 'custom-project',
        version: '1.0.0'
      }, null, 2),
      'index.js': `console.log('Hello from custom project!');`
    }
  }
};

// Tool handler implementations
export const nodeboxToolHandlers = {
  async readFile(projectId: string, path: string) {
    try {
      console.log(`[Enhanced Nodebox] Reading file: ${path} for project: ${projectId}`);

      // Use enhanced filesystem
      const filesystem = EnhancedServerNodeboxStore.getFilesystem(projectId);
      if (!filesystem) {
        throw new Error(`Failed to get filesystem for project: ${projectId}`);
      }

      // Read file with enhanced filesystem
      const result = await filesystem.readFile(path);

      console.log(`[Enhanced Nodebox] Successfully read file: ${path} (${result.content.length} bytes)`);

      return {
        path,
        content: result.content,
        size: result.content.length,
        lastModified: result.entry.lastModified,
        created: result.entry.created,
        metadata: result.entry.metadata,
        success: true,
        message: `Successfully read file: ${path}`
      };
    } catch (error) {
      console.error(`[Enhanced Nodebox] Failed to read file ${path}:`, error);
      throw new Error(`Failed to read file: ${error instanceof Error ? error.message : String(error)}`);
    }
  },

  async writeFile(projectId: string, path: string, content: string, options?: {
    editMode?: 'create' | 'update' | 'append' | 'smart' | 'force';
    backupOnOverwrite?: boolean;
    conflictResolution?: 'rename' | 'merge' | 'replace' | 'prompt';
  }) {
    try {
      console.log(`[Enhanced Nodebox] Writing file: ${path} for project: ${projectId}`);

      // Ensure project exists, create if it doesn't
      let instance = ServerNodeboxStore.getActiveInstance(projectId);
      if (!instance) {
        console.log(`[Enhanced Nodebox] Project ${projectId} doesn't exist, creating it...`);
        instance = ServerNodeboxStore.createInstance(projectId, {
          name: `Project ${projectId}`,
          template: 'custom',
          description: 'Auto-created project for file operations'
        });
      }

      // Use enhanced filesystem
      const filesystem = EnhancedServerNodeboxStore.getFilesystem(projectId);
      if (!filesystem) {
        throw new Error(`Failed to get filesystem for project: ${projectId}`);
      }

      // Write file with enhanced options
      const writeOptions = {
        editMode: options?.editMode || 'smart',
        backupOnOverwrite: options?.backupOnOverwrite ?? true,
        conflictResolution: options?.conflictResolution || 'rename',
        createDirectories: true,
        overwrite: options?.editMode === 'force' || options?.editMode === 'update'
      };

      const result = await filesystem.writeFile(path, content, writeOptions);

      console.log(`[Enhanced Nodebox] Successfully wrote ${content.length} bytes to ${result.path}`);

      // Sync with main Nodebox store for UI updates
      await this.syncFilesystemToStore(projectId);

      // Return enhanced result with edit information
      return {
        path: result.path,
        originalPath: path,
        bytesWritten: content.length,
        created: result.created,
        overwritten: result.overwritten,
        pathChanged: result.path !== path,
        editType: result.edit_type,
        merged: result.merged || false,
        backedUp: result.backed_up || false,
        backupPath: result.backup_path,
        conflictResolved: result.conflict_resolved || false,
        success: true,
        message: generateEditMessage(result, path)
      };
    } catch (error) {
      console.error(`[Enhanced Nodebox] Failed to write file ${path}:`, error);
      throw new Error(`Failed to write file: ${error instanceof Error ? error.message : String(error)}`);
    }
  },

  async createFile(projectId: string, path: string, content = '') {
    return this.writeFile(projectId, path, content);
  },

  // Enhanced edit operations
  async updateFile(projectId: string, path: string, content: string) {
    return this.writeFile(projectId, path, content, {
      editMode: 'update',
      backupOnOverwrite: true,
      conflictResolution: 'replace'
    });
  },

  async appendToFile(projectId: string, path: string, content: string) {
    return this.writeFile(projectId, path, content, {
      editMode: 'append',
      backupOnOverwrite: false,
      conflictResolution: 'merge'
    });
  },

  async forceWriteFile(projectId: string, path: string, content: string) {
    return this.writeFile(projectId, path, content, {
      editMode: 'force',
      backupOnOverwrite: true,
      conflictResolution: 'replace'
    });
  },

  async smartMergeFile(projectId: string, path: string, content: string) {
    return this.writeFile(projectId, path, content, {
      editMode: 'smart',
      backupOnOverwrite: true,
      conflictResolution: 'merge'
    });
  },

  async createDirectory(projectId: string, path: string) {
    try {
      console.log(`[Enhanced Nodebox] Creating directory: ${path} for project: ${projectId}`);

      // Ensure project exists
      let instance = ServerNodeboxStore.getActiveInstance(projectId);
      if (!instance) {
        instance = ServerNodeboxStore.createInstance(projectId, {
          name: `Project ${projectId}`,
          template: 'custom',
          description: 'Auto-created project for directory operations'
        });
      }

      // Use enhanced filesystem
      const filesystem = EnhancedServerNodeboxStore.getFilesystem(projectId);
      if (!filesystem) {
        throw new Error(`Failed to get filesystem for project: ${projectId}`);
      }

      // Create directory with conflict resolution
      const result = await filesystem.createDirectory(path, {
        recursive: true
      });

      console.log(`[Enhanced Nodebox] Successfully created directory: ${result.path}`);

      // Sync with main Nodebox store for UI updates
      await this.syncFilesystemToStore(projectId);

      return {
        path: result.path,
        originalPath: path,
        created: result.created,
        pathChanged: result.path !== path,
        success: true,
        message: result.created
          ? 'Directory created successfully'
          : 'Directory already exists'
      };
    } catch (error) {
      console.error(`[Enhanced Nodebox] Failed to create directory ${path}:`, error);
      throw new Error(`Failed to create directory: ${error instanceof Error ? error.message : String(error)}`);
    }
  },

  async deleteFile(projectId: string, path: string) {
    try {
      console.log(`[Enhanced Nodebox] Deleting file: ${path} for project: ${projectId}`);

      // Use enhanced filesystem
      const filesystem = EnhancedServerNodeboxStore.getFilesystem(projectId);
      if (!filesystem) {
        throw new Error(`Failed to get filesystem for project: ${projectId}`);
      }

      // Delete with enhanced filesystem
      const result = await filesystem.delete(path, { recursive: false });

      console.log(`[Enhanced Nodebox] Successfully deleted: ${path}`);

      // Sync with main Nodebox store for UI updates
      await this.syncFilesystemToStore(projectId);

      return {
        path,
        deleted: result.deleted,
        deletedPaths: result.deleted,
        success: true,
        message: `Successfully deleted: ${path}`
      };
    } catch (error) {
      console.error(`[Enhanced Nodebox] Failed to delete file ${path}:`, error);
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : String(error)}`);
    }
  },

  async listFiles(projectId: string, path = '/') {
    try {
      console.log(`[Enhanced Nodebox] Listing files in: ${path} for project: ${projectId}`);

      // Use enhanced filesystem
      const filesystem = EnhancedServerNodeboxStore.getFilesystem(projectId);
      if (!filesystem) {
        // Return empty list if no filesystem exists yet
        return {
          path,
          files: [],
          count: 0,
          success: true,
          message: 'No filesystem found - project may not be initialized'
        };
      }

      // List directory with enhanced filesystem
      const entries = await filesystem.listDirectory(path);

      console.log(`[Enhanced Nodebox] Found ${entries.length} entries in: ${path}`);

      return {
        path,
        files: entries.map((entry) => ({
          id: entry.id,
          name: entry.name,
          path: entry.path,
          type: entry.type,
          size: entry.size,
          lastModified: entry.lastModified,
          created: entry.created,
          parentPath: entry.parentPath,
          metadata: entry.metadata
        })),
        count: entries.length,
        success: true,
        message: `Found ${entries.length} entries`
      };
    } catch (error) {
      console.error(`[Enhanced Nodebox] Failed to list files in ${path}:`, error);
      throw new Error(`Failed to list files: ${error instanceof Error ? error.message : String(error)}`);
    }
  },

  async runCommand(projectId: string, command: string, args: string[] = []) {
    try {
      const processId = `proc_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      const process = {
        id: processId,
        command,
        args,
        status: 'completed',
        startTime: new Date(),
        endTime: new Date(),
        exitCode: 0,
        output: [`$ ${command} ${args.join(' ')}`, 'Command executed successfully']
      };

      ServerNodeboxStore.addProcess(projectId, process);

      return {
        command,
        args,
        processId,
        status: 'completed',
        success: true
      };
    } catch (error) {
      throw new Error(`Failed to run command: ${error instanceof Error ? error.message : String(error)}`);
    }
  },

  async createProject(name: string, template: ProjectTemplate, projectId?: string) {
    try {
      const finalProjectId = projectId || `project_${Date.now()}`;
      const templateConfig = templates[template];
      
      if (!templateConfig) {
        throw new Error(`Template not found: ${template}`);
      }

      const instance = ServerNodeboxStore.createInstance(finalProjectId, {
        name,
        template,
        description: `Project created from ${template} template`
      });

      // Initialize file system with template files
      const files = Object.entries(templateConfig.files).map(([filePath, content]) => ({
        name: filePath.split('/').pop(),
        path: filePath.startsWith('/') ? filePath : `/${filePath}`,
        type: 'file',
        content: String(content),
        size: String(content).length,
        lastModified: new Date()
      }));

      await ServerNodeboxStore.setFileSystem(finalProjectId, files);

      return {
        name,
        template,
        instanceId: instance.id,
        projectId: finalProjectId,
        created: true,
        success: true
      };
    } catch (error) {
      throw new Error(`Failed to create project: ${error instanceof Error ? error.message : String(error)}`);
    }
  },

  async getProjectInfo(projectId: string) {
    try {
      const instance = ServerNodeboxStore.getActiveInstance(projectId);
      
      if (!instance) {
        throw new Error('No active project found');
      }

      const files = ServerNodeboxStore.getFileSystem(projectId);

      return {
        instance: {
          id: instance.id,
          name: instance.config.name,
          template: instance.config.template,
          status: instance.status,
          createdAt: instance.createdAt
        },
        fileCount: files.length,
        processCount: 0,
        previewCount: 0,
        success: true
      };
    } catch (error) {
      throw new Error(`Failed to get project info: ${error instanceof Error ? error.message : String(error)}`);
    }
  },

  /**
   * Sync enhanced filesystem state with main Nodebox store for UI updates
   */
  async syncFilesystemToStore(projectId: string) {
    try {
      console.log(`[Enhanced Nodebox] Syncing filesystem to store for project: ${projectId}`);

      // Get enhanced filesystem and instance
      const filesystem = EnhancedServerNodeboxStore.getFilesystem(projectId);
      const instance = EnhancedServerNodeboxStore.getActiveInstance(projectId);

      if (!filesystem) {
        console.warn(`[Enhanced Nodebox] No filesystem found for project: ${projectId}`);
        return;
      }

      if (!instance) {
        console.warn(`[Enhanced Nodebox] No instance found for project: ${projectId}`);
        return;
      }

      // Get all entries from enhanced filesystem
      const stats = filesystem.getStats();
      const entries = stats.entries;

      console.log(`[Enhanced Nodebox] Found ${entries.length} entries in enhanced filesystem`);

      // Convert enhanced filesystem entries to legacy format for main store
      const legacyFiles = entries.map(entry => ({
        id: entry.id,
        name: entry.name,
        path: entry.path,
        type: entry.type,
        content: entry.content || '',
        size: entry.size || 0,
        lastModified: entry.lastModified,
        created: entry.created,
        parentPath: entry.parentPath,
        metadata: entry.metadata
      }));

      // Update main Nodebox store using the instance ID
      await ServerNodeboxStore.setFileSystem(instance.id, legacyFiles);

      console.log(`[Enhanced Nodebox] Successfully synced ${legacyFiles.length} files to main store with instanceId: ${instance.id}`);
    } catch (error) {
      console.error(`[Enhanced Nodebox] Failed to sync filesystem to store:`, error);
      // Don't throw error to avoid breaking the main operation
    }
  }
};
