/**
 * Real-time Collaboration System
 * 
 * Enables multiple users and AI agents to collaborate on the same project
 * with real-time synchronization, conflict resolution, and change tracking.
 */

import { WebSocket } from 'ws';
import { EnhancedServerNodeboxStore } from './enhanced-server-nodebox-store';

export interface CollaborationEvent {
  id: string;
  type: 'file_change' | 'cursor_move' | 'selection_change' | 'ai_action' | 'user_join' | 'user_leave';
  projectId: string;
  userId: string;
  timestamp: Date;
  data: any;
}

export interface Collaborator {
  id: string;
  name: string;
  type: 'human' | 'ai';
  avatar?: string;
  cursor?: {
    file: string;
    line: number;
    column: number;
  };
  selection?: {
    file: string;
    start: { line: number; column: number };
    end: { line: number; column: number };
  };
  lastSeen: Date;
  permissions: {
    read: boolean;
    write: boolean;
    admin: boolean;
  };
}

export interface FileChange {
  id: string;
  file: string;
  type: 'create' | 'update' | 'delete' | 'rename';
  content?: string;
  oldContent?: string;
  author: string;
  timestamp: Date;
  conflictResolution?: 'auto' | 'manual' | 'ai_assisted';
}

export class RealtimeCollaborationManager {
  private static instance: RealtimeCollaborationManager;
  private connections: Map<string, WebSocket> = new Map();
  private projectCollaborators: Map<string, Map<string, Collaborator>> = new Map();
  private pendingChanges: Map<string, FileChange[]> = new Map();
  private changeHistory: Map<string, FileChange[]> = new Map();

  static getInstance(): RealtimeCollaborationManager {
    if (!this.instance) {
      this.instance = new RealtimeCollaborationManager();
    }
    return this.instance;
  }

  /**
   * Add a collaborator to a project
   */
  addCollaborator(projectId: string, collaborator: Collaborator, ws?: WebSocket): void {
    if (!this.projectCollaborators.has(projectId)) {
      this.projectCollaborators.set(projectId, new Map());
    }

    const projectCollabs = this.projectCollaborators.get(projectId)!;
    projectCollabs.set(collaborator.id, collaborator);

    if (ws) {
      this.connections.set(collaborator.id, ws);
      this.setupWebSocketHandlers(collaborator.id, projectId, ws);
    }

    // Notify other collaborators
    this.broadcastEvent(projectId, {
      id: this.generateId(),
      type: 'user_join',
      projectId,
      userId: collaborator.id,
      timestamp: new Date(),
      data: { collaborator }
    }, collaborator.id);

    console.log(`[Collaboration] ${collaborator.name} joined project ${projectId}`);
  }

  /**
   * Remove a collaborator from a project
   */
  removeCollaborator(projectId: string, collaboratorId: string): void {
    const projectCollabs = this.projectCollaborators.get(projectId);
    if (projectCollabs) {
      const collaborator = projectCollabs.get(collaboratorId);
      projectCollabs.delete(collaboratorId);

      if (collaborator) {
        // Notify other collaborators
        this.broadcastEvent(projectId, {
          id: this.generateId(),
          type: 'user_leave',
          projectId,
          userId: collaboratorId,
          timestamp: new Date(),
          data: { collaborator }
        }, collaboratorId);
      }
    }

    this.connections.delete(collaboratorId);
    console.log(`[Collaboration] Collaborator ${collaboratorId} left project ${projectId}`);
  }

  /**
   * Handle file changes with conflict resolution
   */
  async handleFileChange(projectId: string, change: FileChange): Promise<{
    success: boolean;
    conflicts?: FileChange[];
    resolvedChange?: FileChange;
  }> {
    const filesystem = EnhancedServerNodeboxStore.getFilesystem(projectId);
    if (!filesystem) {
      throw new Error('Project filesystem not found');
    }

    // Check for conflicts
    const conflicts = await this.detectConflicts(projectId, change);
    
    if (conflicts.length > 0) {
      // Handle conflicts
      const resolution = await this.resolveConflicts(projectId, change, conflicts);
      
      if (resolution.success) {
        // Apply resolved change
        await this.applyFileChange(projectId, resolution.resolvedChange!);
        
        // Broadcast the resolved change
        this.broadcastEvent(projectId, {
          id: this.generateId(),
          type: 'file_change',
          projectId,
          userId: change.author,
          timestamp: new Date(),
          data: { change: resolution.resolvedChange, conflicts, resolved: true }
        });

        return resolution;
      } else {
        // Manual resolution required
        return { success: false, conflicts };
      }
    } else {
      // No conflicts, apply change directly
      await this.applyFileChange(projectId, change);
      
      // Broadcast the change
      this.broadcastEvent(projectId, {
        id: this.generateId(),
        type: 'file_change',
        projectId,
        userId: change.author,
        timestamp: new Date(),
        data: { change }
      });

      return { success: true };
    }
  }

  /**
   * Detect conflicts between changes
   */
  private async detectConflicts(projectId: string, newChange: FileChange): Promise<FileChange[]> {
    const pendingChanges = this.pendingChanges.get(projectId) || [];
    const conflicts: FileChange[] = [];

    for (const pendingChange of pendingChanges) {
      if (pendingChange.file === newChange.file && 
          pendingChange.author !== newChange.author &&
          Math.abs(pendingChange.timestamp.getTime() - newChange.timestamp.getTime()) < 5000) {
        conflicts.push(pendingChange);
      }
    }

    return conflicts;
  }

  /**
   * Resolve conflicts using AI assistance
   */
  private async resolveConflicts(
    projectId: string, 
    newChange: FileChange, 
    conflicts: FileChange[]
  ): Promise<{
    success: boolean;
    resolvedChange?: FileChange;
    strategy?: string;
  }> {
    // Simple conflict resolution strategies
    const strategies = [
      'merge_changes',
      'timestamp_priority',
      'user_priority',
      'ai_mediated'
    ];

    // For now, use timestamp priority (latest wins)
    const allChanges = [newChange, ...conflicts];
    const latestChange = allChanges.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

    // If the new change is the latest, apply it
    if (latestChange.id === newChange.id) {
      return {
        success: true,
        resolvedChange: newChange,
        strategy: 'timestamp_priority'
      };
    }

    // Otherwise, try to merge changes
    try {
      const mergedContent = await this.mergeChanges(allChanges);
      const resolvedChange: FileChange = {
        ...newChange,
        id: this.generateId(),
        content: mergedContent,
        conflictResolution: 'ai_assisted'
      };

      return {
        success: true,
        resolvedChange,
        strategy: 'ai_mediated'
      };
    } catch (error) {
      console.error('Failed to resolve conflicts:', error);
      return { success: false };
    }
  }

  /**
   * Merge multiple changes using AI
   */
  private async mergeChanges(changes: FileChange[]): Promise<string> {
    // Sort changes by timestamp
    const sortedChanges = changes.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    
    // Start with the oldest content
    let mergedContent = sortedChanges[0].oldContent || '';
    
    // Apply changes sequentially
    for (const change of sortedChanges) {
      if (change.content) {
        // Simple merge: use the latest content
        // In a more sophisticated implementation, this would use diff/patch algorithms
        mergedContent = change.content;
      }
    }

    return mergedContent;
  }

  /**
   * Apply a file change to the filesystem
   */
  private async applyFileChange(projectId: string, change: FileChange): Promise<void> {
    const filesystem = EnhancedServerNodeboxStore.getFilesystem(projectId);
    if (!filesystem) {
      throw new Error('Project filesystem not found');
    }

    switch (change.type) {
      case 'create':
      case 'update':
        if (change.content !== undefined) {
          await filesystem.writeFile(change.file, change.content, {
            editMode: 'smart',
            backupOnOverwrite: true
          });
        }
        break;
        
      case 'delete':
        await filesystem.delete(change.file);
        break;
        
      case 'rename':
        // Handle rename operation
        if (change.oldContent && change.content) {
          await filesystem.delete(change.oldContent); // old path
          await filesystem.writeFile(change.file, change.content, {
            editMode: 'create'
          });
        }
        break;
    }

    // Add to change history
    if (!this.changeHistory.has(projectId)) {
      this.changeHistory.set(projectId, []);
    }
    this.changeHistory.get(projectId)!.push(change);

    // Remove from pending changes
    const pending = this.pendingChanges.get(projectId) || [];
    const filteredPending = pending.filter(p => p.id !== change.id);
    this.pendingChanges.set(projectId, filteredPending);
  }

  /**
   * Broadcast an event to all collaborators in a project
   */
  private broadcastEvent(projectId: string, event: CollaborationEvent, excludeUserId?: string): void {
    const collaborators = this.projectCollaborators.get(projectId);
    if (!collaborators) return;

    for (const [userId, collaborator] of collaborators) {
      if (excludeUserId && userId === excludeUserId) continue;

      const ws = this.connections.get(userId);
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(event));
      }
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(userId: string, projectId: string, ws: WebSocket): void {
    ws.on('message', async (data) => {
      try {
        const event: CollaborationEvent = JSON.parse(data.toString());
        await this.handleWebSocketEvent(userId, projectId, event);
      } catch (error) {
        console.error('Failed to handle WebSocket message:', error);
      }
    });

    ws.on('close', () => {
      this.removeCollaborator(projectId, userId);
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.removeCollaborator(projectId, userId);
    });
  }

  /**
   * Handle incoming WebSocket events
   */
  private async handleWebSocketEvent(userId: string, projectId: string, event: CollaborationEvent): Promise<void> {
    switch (event.type) {
      case 'file_change':
        const change: FileChange = event.data.change;
        await this.handleFileChange(projectId, change);
        break;

      case 'cursor_move':
        await this.updateCollaboratorCursor(projectId, userId, event.data.cursor);
        break;

      case 'selection_change':
        await this.updateCollaboratorSelection(projectId, userId, event.data.selection);
        break;

      default:
        console.warn('Unknown event type:', event.type);
    }
  }

  /**
   * Update collaborator cursor position
   */
  private async updateCollaboratorCursor(projectId: string, userId: string, cursor: any): Promise<void> {
    const collaborators = this.projectCollaborators.get(projectId);
    if (!collaborators) return;

    const collaborator = collaborators.get(userId);
    if (collaborator) {
      collaborator.cursor = cursor;
      collaborator.lastSeen = new Date();

      // Broadcast cursor update
      this.broadcastEvent(projectId, {
        id: this.generateId(),
        type: 'cursor_move',
        projectId,
        userId,
        timestamp: new Date(),
        data: { cursor }
      }, userId);
    }
  }

  /**
   * Update collaborator selection
   */
  private async updateCollaboratorSelection(projectId: string, userId: string, selection: any): Promise<void> {
    const collaborators = this.projectCollaborators.get(projectId);
    if (!collaborators) return;

    const collaborator = collaborators.get(userId);
    if (collaborator) {
      collaborator.selection = selection;
      collaborator.lastSeen = new Date();

      // Broadcast selection update
      this.broadcastEvent(projectId, {
        id: this.generateId(),
        type: 'selection_change',
        projectId,
        userId,
        timestamp: new Date(),
        data: { selection }
      }, userId);
    }
  }

  /**
   * Get all collaborators for a project
   */
  getCollaborators(projectId: string): Collaborator[] {
    const collaborators = this.projectCollaborators.get(projectId);
    return collaborators ? Array.from(collaborators.values()) : [];
  }

  /**
   * Get change history for a project
   */
  getChangeHistory(projectId: string, limit?: number): FileChange[] {
    const history = this.changeHistory.get(projectId) || [];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Add AI agent as collaborator
   */
  addAIAgent(projectId: string, agentName: string = 'AI Assistant'): void {
    const aiAgent: Collaborator = {
      id: `ai_${this.generateId()}`,
      name: agentName,
      type: 'ai',
      avatar: '🤖',
      lastSeen: new Date(),
      permissions: {
        read: true,
        write: true,
        admin: false
      }
    };

    this.addCollaborator(projectId, aiAgent);
  }

  /**
   * Track AI action for collaboration
   */
  async trackAIAction(projectId: string, action: {
    type: string;
    description: string;
    files?: string[];
    result?: any;
  }): Promise<void> {
    this.broadcastEvent(projectId, {
      id: this.generateId(),
      type: 'ai_action',
      projectId,
      userId: 'ai_assistant',
      timestamp: new Date(),
      data: action
    });
  }
}
