/**
 * Enhanced Nodebox Filesystem
 * 
 * Provides a robust base filesystem that prevents duplicates,
 * manages file conflicts, and ensures data integrity.
 */

export interface FileSystemEntry {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  content?: string;
  size?: number;
  lastModified: Date;
  created: Date;
  parentPath: string;
  children?: string[]; // For directories, list of child IDs
  metadata?: {
    encoding?: string;
    mimeType?: string;
    permissions?: string;
    isHidden?: boolean;
    isReadOnly?: boolean;
    editHistory?: Array<{
      timestamp: Date;
      editType: 'create' | 'update' | 'append' | 'merge' | 'replace';
      contentLength: number;
      merged: boolean;
      backupCreated: boolean;
    }>;
    backups?: string[]; // List of backup file paths
  };
}

export interface FileSystemState {
  entries: Map<string, FileSystemEntry>; // path -> entry
  pathIndex: Map<string, string>; // normalized path -> actual path
  directoryContents: Map<string, Set<string>>; // directory path -> set of child paths
  rootPath: string;
}

export class EnhancedNodeboxFilesystem {
  private state: FileSystemState;
  private projectId: string;

  constructor(projectId: string) {
    this.projectId = projectId;
    this.state = {
      entries: new Map(),
      pathIndex: new Map(),
      directoryContents: new Map(),
      rootPath: '/'
    };
    
    // Initialize root directory
    this.initializeRoot();
  }

  private initializeRoot(): void {
    const rootEntry: FileSystemEntry = {
      id: 'root',
      name: '',
      path: '/',
      type: 'directory',
      lastModified: new Date(),
      created: new Date(),
      parentPath: '',
      children: [],
      metadata: {
        permissions: 'rwxr-xr-x',
        isHidden: false,
        isReadOnly: false
      }
    };

    this.state.entries.set('/', rootEntry);
    this.state.pathIndex.set('/', '/');
    this.state.directoryContents.set('/', new Set());
  }

  /**
   * Normalize path to prevent conflicts
   */
  private normalizePath(path: string): string {
    // Ensure path starts with /
    if (!path.startsWith('/')) {
      path = '/' + path;
    }

    // Remove leading/trailing slashes, resolve relative paths
    const normalized = path
      .replace(/\/+/g, '/') // Replace multiple slashes with single
      .replace(/\/\./g, '/') // Remove /./ patterns
      .replace(/\/[^\/]+\/\.\./g, '/') // Resolve /../ patterns
      .replace(/\/$/, '') // Remove trailing slash
      || '/';

    return normalized === '' ? '/' : normalized;
  }

  /**
   * Check if path exists (case-insensitive check for conflicts)
   */
  private pathExists(path: string): boolean {
    const normalized = this.normalizePath(path);
    return this.state.entries.has(normalized);
  }

  /**
   * Find conflicting paths (case-insensitive)
   */
  private findConflictingPath(path: string): string | null {
    const normalized = this.normalizePath(path).toLowerCase();
    
    for (const [existingPath] of this.state.entries) {
      if (existingPath.toLowerCase() === normalized && existingPath !== this.normalizePath(path)) {
        return existingPath;
      }
    }
    
    return null;
  }

  /**
   * Generate unique path if conflicts exist
   */
  private generateUniquePath(basePath: string, type: 'file' | 'directory'): string {
    const normalized = this.normalizePath(basePath);
    
    if (!this.pathExists(normalized) && !this.findConflictingPath(normalized)) {
      return normalized;
    }

    const pathParts = normalized.split('/');
    const fileName = pathParts.pop() || '';
    const directory = pathParts.join('/') || '/';
    
    let counter = 1;
    let newPath: string;
    
    do {
      if (type === 'file') {
        const dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0) {
          const name = fileName.substring(0, dotIndex);
          const extension = fileName.substring(dotIndex);
          newPath = `${directory}/${name}_${counter}${extension}`;
        } else {
          newPath = `${directory}/${fileName}_${counter}`;
        }
      } else {
        newPath = `${directory}/${fileName}_${counter}`;
      }
      
      counter++;
    } while (this.pathExists(newPath) || this.findConflictingPath(newPath));
    
    return newPath;
  }

  /**
   * Create or update a file with advanced edit handling
   */
  async writeFile(path: string, content: string, options?: {
    overwrite?: boolean;
    createDirectories?: boolean;
    editMode?: 'create' | 'update' | 'append' | 'smart' | 'force';
    backupOnOverwrite?: boolean;
    conflictResolution?: 'rename' | 'merge' | 'replace' | 'prompt';
    mergeStrategy?: 'append' | 'prepend' | 'smart-merge';
  }): Promise<{
    path: string;
    created: boolean;
    overwritten: boolean;
    backed_up?: boolean;
    backup_path?: string;
    merged?: boolean;
    conflict_resolved?: boolean;
    original_content?: string;
    edit_type: 'create' | 'update' | 'append' | 'merge' | 'replace';
  }> {
    const opts = {
      overwrite: true,
      createDirectories: true,
      editMode: 'smart',
      backupOnOverwrite: true,
      conflictResolution: 'rename',
      mergeStrategy: 'smart-merge',
      ...options
    };

    const normalizedPath = this.normalizePath(path);
    const existingEntry = this.state.entries.get(normalizedPath);

    let finalPath = normalizedPath;
    let finalContent = content;
    let editType: 'create' | 'update' | 'append' | 'merge' | 'replace' = 'create';
    let backupPath: string | undefined;
    let originalContent: string | undefined;
    let merged = false;
    let conflictResolved = false;

    // Handle different edit modes
    if (existingEntry) {
      originalContent = existingEntry.content || '';

      switch (opts.editMode) {
        case 'create':
          // Only create if doesn't exist, otherwise rename
          if (opts.conflictResolution === 'rename') {
            finalPath = this.generateUniquePath(normalizedPath, 'file');
            editType = 'create';
            conflictResolved = true;
          } else {
            throw new Error(`File already exists: ${normalizedPath}`);
          }
          break;

        case 'update':
        case 'force':
          // Update existing file
          if (opts.backupOnOverwrite) {
            backupPath = await this.createBackup(normalizedPath, originalContent);
          }
          editType = 'update';
          break;

        case 'append':
          // Append to existing content
          finalContent = this.mergeContent(originalContent, content, 'append');
          editType = 'append';
          merged = true;
          break;

        case 'smart':
          // Smart merge based on content analysis
          const mergeResult = this.smartMerge(originalContent, content);
          finalContent = mergeResult.content;
          editType = mergeResult.type;
          merged = mergeResult.merged;

          if (merged && opts.backupOnOverwrite) {
            backupPath = await this.createBackup(normalizedPath, originalContent);
          }
          break;
      }
    } else {
      // File doesn't exist, create it
      editType = 'create';
    }

    // Create parent directories if needed
    if (opts.createDirectories) {
      await this.createDirectoriesRecursive(finalPath);
    }

    const parentPath = finalPath.substring(0, finalPath.lastIndexOf('/')) || '/';
    const fileName = finalPath.substring(finalPath.lastIndexOf('/') + 1);

    // Check if parent directory exists
    if (!this.state.entries.has(parentPath)) {
      throw new Error(`Parent directory does not exist: ${parentPath}`);
    }

    const now = new Date();
    const entry: FileSystemEntry = {
      id: existingEntry?.id || `file_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      name: fileName,
      path: finalPath,
      type: 'file',
      content: finalContent,
      size: finalContent.length,
      lastModified: now,
      created: existingEntry?.created || now,
      parentPath,
      metadata: {
        encoding: 'utf-8',
        mimeType: this.getMimeType(fileName),
        permissions: 'rw-r--r--',
        isHidden: fileName.startsWith('.'),
        isReadOnly: false,
        editHistory: [
          ...(existingEntry?.metadata?.editHistory || []),
          {
            timestamp: now,
            editType,
            contentLength: finalContent.length,
            merged,
            backupCreated: !!backupPath
          }
        ],
        ...existingEntry?.metadata
      }
    };

    // Update filesystem state
    this.state.entries.set(finalPath, entry);
    this.state.pathIndex.set(finalPath.toLowerCase(), finalPath);

    // Update parent directory
    const parentContents = this.state.directoryContents.get(parentPath) || new Set();
    parentContents.add(finalPath);
    this.state.directoryContents.set(parentPath, parentContents);

    console.log(`[Enhanced FS] ${editType.toUpperCase()}: ${finalPath} (${finalContent.length} bytes)`);

    return {
      path: finalPath,
      created: !existingEntry,
      overwritten: !!existingEntry && editType === 'update',
      backed_up: !!backupPath,
      backup_path: backupPath,
      merged,
      conflict_resolved: conflictResolved,
      original_content: originalContent,
      edit_type: editType
    };
  }

  /**
   * Create directory recursively
   */
  private async createDirectoriesRecursive(filePath: string): Promise<void> {
    const pathParts = filePath.split('/').filter(Boolean);
    let currentPath = '/';

    // Create each directory in the path (excluding the filename)
    for (const part of pathParts.slice(0, -1)) { // Exclude filename
      currentPath = currentPath === '/' ? `/${part}` : `${currentPath}/${part}`;

      if (!this.state.entries.has(currentPath)) {
        // Create directory without recursion to avoid infinite loops
        await this.createDirectoryInternal(currentPath);
      }
    }
  }

  /**
   * Internal directory creation without recursive parent creation
   */
  private async createDirectoryInternal(path: string): Promise<{ path: string; created: boolean }> {
    const normalizedPath = this.normalizePath(path);

    if (this.state.entries.has(normalizedPath)) {
      const existing = this.state.entries.get(normalizedPath)!;
      if (existing.type === 'directory') {
        return { path: normalizedPath, created: false };
      } else {
        throw new Error(`Cannot create directory: ${normalizedPath} already exists as a file`);
      }
    }

    const parentPath = normalizedPath.substring(0, normalizedPath.lastIndexOf('/')) || '/';
    const dirName = normalizedPath.substring(normalizedPath.lastIndexOf('/') + 1);

    // Parent must exist (should be created by recursive call)
    if (!this.state.entries.has(parentPath)) {
      throw new Error(`Parent directory does not exist: ${parentPath}`);
    }

    const now = new Date();
    const entry: FileSystemEntry = {
      id: `dir_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      name: dirName,
      path: normalizedPath,
      type: 'directory',
      lastModified: now,
      created: now,
      parentPath,
      children: [],
      metadata: {
        permissions: 'rwxr-xr-x',
        isHidden: dirName.startsWith('.'),
        isReadOnly: false
      }
    };

    // Update filesystem state
    this.state.entries.set(normalizedPath, entry);
    this.state.pathIndex.set(normalizedPath.toLowerCase(), normalizedPath);
    this.state.directoryContents.set(normalizedPath, new Set());

    // Update parent directory
    const parentContents = this.state.directoryContents.get(parentPath) || new Set();
    parentContents.add(normalizedPath);
    this.state.directoryContents.set(parentPath, parentContents);

    console.log(`[Enhanced FS] Created directory: ${normalizedPath}`);

    return { path: normalizedPath, created: true };
  }

  /**
   * Create a directory
   */
  async createDirectory(path: string, options?: {
    recursive?: boolean;
  }): Promise<{ path: string; created: boolean }> {
    const opts = { recursive: true, ...options };
    const normalizedPath = this.normalizePath(path);

    if (this.state.entries.has(normalizedPath)) {
      const existing = this.state.entries.get(normalizedPath)!;
      if (existing.type === 'directory') {
        return { path: normalizedPath, created: false };
      } else {
        throw new Error(`Cannot create directory: ${normalizedPath} already exists as a file`);
      }
    }

    // Check for conflicts
    const conflictingPath = this.findConflictingPath(normalizedPath);
    if (conflictingPath) {
      throw new Error(`Directory conflict: ${conflictingPath} already exists (case-insensitive)`);
    }

    const parentPath = normalizedPath.substring(0, normalizedPath.lastIndexOf('/')) || '/';

    // Create parent directories if needed
    if (opts.recursive && parentPath !== '/' && !this.state.entries.has(parentPath)) {
      await this.createDirectoriesRecursive(normalizedPath);
      // After creating parent directories, the current directory should be created too
      if (this.state.entries.has(normalizedPath)) {
        return { path: normalizedPath, created: true };
      }
    }

    // Use internal method to create the directory
    return await this.createDirectoryInternal(normalizedPath);
  }

  /**
   * Create a backup of existing file content
   */
  private async createBackup(originalPath: string, content: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const pathParts = originalPath.split('/');
    const fileName = pathParts.pop() || 'unknown';
    const directory = pathParts.join('/') || '/';

    // Create backup filename
    const dotIndex = fileName.lastIndexOf('.');
    let backupName: string;
    if (dotIndex > 0) {
      const name = fileName.substring(0, dotIndex);
      const extension = fileName.substring(dotIndex);
      backupName = `${name}.backup.${timestamp}${extension}`;
    } else {
      backupName = `${fileName}.backup.${timestamp}`;
    }

    const backupPath = `${directory}/.backups/${backupName}`;

    // Ensure .backups directory exists
    await this.createDirectory(`${directory}/.backups`, { recursive: true });

    // Create backup file
    const backupEntry: FileSystemEntry = {
      id: `backup_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      name: backupName,
      path: backupPath,
      type: 'file',
      content,
      size: content.length,
      lastModified: new Date(),
      created: new Date(),
      parentPath: `${directory}/.backups`,
      metadata: {
        encoding: 'utf-8',
        mimeType: 'application/backup',
        permissions: 'r--r--r--',
        isHidden: true,
        isReadOnly: true
      }
    };

    this.state.entries.set(backupPath, backupEntry);
    this.state.pathIndex.set(backupPath.toLowerCase(), backupPath);

    // Update parent directory
    const parentContents = this.state.directoryContents.get(`${directory}/.backups`) || new Set();
    parentContents.add(backupPath);
    this.state.directoryContents.set(`${directory}/.backups`, parentContents);

    console.log(`[Enhanced FS] Created backup: ${backupPath}`);
    return backupPath;
  }

  /**
   * Merge content using different strategies
   */
  private mergeContent(originalContent: string, newContent: string, strategy: 'append' | 'prepend' | 'smart-merge'): string {
    switch (strategy) {
      case 'append':
        return originalContent + '\n\n' + newContent;

      case 'prepend':
        return newContent + '\n\n' + originalContent;

      case 'smart-merge':
        return this.performSmartMerge(originalContent, newContent);

      default:
        return newContent;
    }
  }

  /**
   * Smart merge that analyzes content and merges intelligently
   */
  private smartMerge(originalContent: string, newContent: string): {
    content: string;
    type: 'create' | 'update' | 'append' | 'merge' | 'replace';
    merged: boolean;
  } {
    // If original is empty, just create
    if (!originalContent.trim()) {
      return {
        content: newContent,
        type: 'create',
        merged: false
      };
    }

    // If new content is identical, no change needed
    if (originalContent === newContent) {
      return {
        content: originalContent,
        type: 'update',
        merged: false
      };
    }

    // Check if new content is a subset (likely an update to part of the file)
    if (originalContent.includes(newContent)) {
      return {
        content: originalContent,
        type: 'update',
        merged: false
      };
    }

    // Check if new content contains the original (likely an expansion)
    if (newContent.includes(originalContent)) {
      return {
        content: newContent,
        type: 'update',
        merged: true
      };
    }

    // Perform intelligent merge based on file type
    const mergedContent = this.performSmartMerge(originalContent, newContent);

    return {
      content: mergedContent,
      type: 'merge',
      merged: true
    };
  }

  /**
   * Perform smart merge based on content analysis
   */
  private performSmartMerge(originalContent: string, newContent: string): string {
    const originalLines = originalContent.split('\n');
    const newLines = newContent.split('\n');

    // Simple line-based merge for now
    // In a more sophisticated implementation, this could use diff algorithms

    // Check for common patterns
    const originalImports = originalLines.filter(line => line.trim().startsWith('import '));
    const newImports = newLines.filter(line => line.trim().startsWith('import '));

    // Merge imports
    const allImports = [...new Set([...originalImports, ...newImports])];

    // Get non-import content
    const originalNonImports = originalLines.filter(line => !line.trim().startsWith('import '));
    const newNonImports = newLines.filter(line => !line.trim().startsWith('import '));

    // For now, append new non-import content
    const mergedNonImports = [...originalNonImports];

    // Add new content that doesn't already exist
    for (const line of newNonImports) {
      if (line.trim() && !originalNonImports.some(orig => orig.trim() === line.trim())) {
        mergedNonImports.push(line);
      }
    }

    // Combine imports and content
    const result = [...allImports, '', ...mergedNonImports].join('\n');

    return result;
  }

  /**
   * Get MIME type based on file extension
   */
  private getMimeType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      'js': 'application/javascript',
      'ts': 'application/typescript',
      'jsx': 'application/javascript',
      'tsx': 'application/typescript',
      'json': 'application/json',
      'html': 'text/html',
      'css': 'text/css',
      'md': 'text/markdown',
      'txt': 'text/plain',
      'xml': 'application/xml',
      'svg': 'image/svg+xml',
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'gif': 'image/gif'
    };
    
    return mimeTypes[extension || ''] || 'application/octet-stream';
  }

  /**
   * Read a file
   */
  async readFile(path: string): Promise<{ content: string; entry: FileSystemEntry }> {
    const normalizedPath = this.normalizePath(path);
    const entry = this.state.entries.get(normalizedPath);
    
    if (!entry) {
      throw new Error(`File not found: ${path}`);
    }
    
    if (entry.type !== 'file') {
      throw new Error(`Path is not a file: ${path}`);
    }
    
    return {
      content: entry.content || '',
      entry: { ...entry }
    };
  }

  /**
   * List directory contents
   */
  async listDirectory(path: string = '/'): Promise<FileSystemEntry[]> {
    const normalizedPath = this.normalizePath(path);
    const entry = this.state.entries.get(normalizedPath);
    
    if (!entry) {
      throw new Error(`Directory not found: ${path}`);
    }
    
    if (entry.type !== 'directory') {
      throw new Error(`Path is not a directory: ${path}`);
    }
    
    const contents = this.state.directoryContents.get(normalizedPath) || new Set();
    const entries: FileSystemEntry[] = [];
    
    for (const childPath of contents) {
      const childEntry = this.state.entries.get(childPath);
      if (childEntry) {
        entries.push({ ...childEntry });
      }
    }
    
    // Sort: directories first, then files, both alphabetically
    return entries.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });
  }

  /**
   * Delete a file or directory
   */
  async delete(path: string, options?: { recursive?: boolean }): Promise<{ deleted: string[] }> {
    const opts = { recursive: false, ...options };
    const normalizedPath = this.normalizePath(path);
    const entry = this.state.entries.get(normalizedPath);
    
    if (!entry) {
      throw new Error(`Path not found: ${path}`);
    }
    
    const deletedPaths: string[] = [];
    
    if (entry.type === 'directory') {
      const contents = this.state.directoryContents.get(normalizedPath) || new Set();
      
      if (contents.size > 0 && !opts.recursive) {
        throw new Error(`Directory not empty: ${path}. Use recursive option to delete.`);
      }
      
      // Delete contents recursively
      for (const childPath of contents) {
        const result = await this.delete(childPath, { recursive: true });
        deletedPaths.push(...result.deleted);
      }
      
      // Delete directory itself
      this.state.directoryContents.delete(normalizedPath);
    }
    
    // Remove from parent directory
    const parentContents = this.state.directoryContents.get(entry.parentPath);
    if (parentContents) {
      parentContents.delete(normalizedPath);
    }
    
    // Remove from filesystem
    this.state.entries.delete(normalizedPath);
    this.state.pathIndex.delete(normalizedPath.toLowerCase());
    deletedPaths.push(normalizedPath);
    
    console.log(`[Enhanced FS] Deleted: ${normalizedPath}`);
    
    return { deleted: deletedPaths };
  }

  /**
   * Check if path exists
   */
  exists(path: string): boolean {
    const normalizedPath = this.normalizePath(path);
    return this.state.entries.has(normalizedPath);
  }

  /**
   * Get filesystem statistics
   */
  getStats(): {
    totalFiles: number;
    totalDirectories: number;
    totalSize: number;
    entries: FileSystemEntry[];
  } {
    const entries = Array.from(this.state.entries.values());
    const files = entries.filter(e => e.type === 'file');
    const directories = entries.filter(e => e.type === 'directory');
    const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);
    
    return {
      totalFiles: files.length,
      totalDirectories: directories.length,
      totalSize,
      entries: entries.map(e => ({ ...e }))
    };
  }

  /**
   * Export filesystem state for persistence
   */
  exportState(): any {
    return {
      projectId: this.projectId,
      entries: Array.from(this.state.entries.entries()),
      pathIndex: Array.from(this.state.pathIndex.entries()),
      directoryContents: Array.from(this.state.directoryContents.entries()).map(([path, contents]) => [
        path,
        Array.from(contents)
      ]),
      rootPath: this.state.rootPath,
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Import filesystem state from persistence
   */
  importState(state: any): void {
    this.projectId = state.projectId;
    this.state.entries = new Map(state.entries);
    this.state.pathIndex = new Map(state.pathIndex);
    this.state.directoryContents = new Map(
      state.directoryContents.map(([path, contents]: [string, string[]]) => [
        path,
        new Set(contents)
      ])
    );
    this.state.rootPath = state.rootPath;
    
    console.log(`[Enhanced FS] Imported state for project: ${this.projectId}`);
  }
}
