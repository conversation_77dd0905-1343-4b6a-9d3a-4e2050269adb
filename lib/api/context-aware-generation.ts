/**
 * Context-Aware Code Generation System
 * 
 * Analyzes existing codebase to generate contextually appropriate code
 * that follows project patterns, imports, and conventions.
 */

import { EnhancedServerNodeboxStore } from './enhanced-server-nodebox-store';

export interface CodeContext {
  projectType: 'react' | 'nextjs' | 'node' | 'vanilla' | 'typescript';
  framework: string;
  dependencies: string[];
  imports: Map<string, string[]>; // file -> imports
  exports: Map<string, string[]>; // file -> exports
  patterns: {
    componentStructure: 'functional' | 'class' | 'mixed';
    stateManagement: 'useState' | 'redux' | 'zustand' | 'context' | 'none';
    styling: 'css' | 'scss' | 'tailwind' | 'styled-components' | 'emotion';
    testing: 'jest' | 'vitest' | 'cypress' | 'playwright' | 'none';
  };
  conventions: {
    naming: 'camelCase' | 'PascalCase' | 'kebab-case' | 'snake_case';
    fileStructure: 'flat' | 'feature-based' | 'domain-driven';
    importStyle: 'relative' | 'absolute' | 'mixed';
  };
}

export class ContextAwareGenerator {
  private projectId: string;
  private context: CodeContext | null = null;

  constructor(projectId: string) {
    this.projectId = projectId;
  }

  /**
   * Analyze existing codebase to understand patterns and conventions
   */
  async analyzeCodebase(): Promise<CodeContext> {
    const filesystem = EnhancedServerNodeboxStore.getFilesystem(this.projectId);
    if (!filesystem) {
      throw new Error('No filesystem found for project');
    }

    const stats = filesystem.getStats();
    const files = stats.entries.filter(entry => entry.type === 'file');
    
    // Analyze package.json for dependencies and project type
    const packageJson = files.find(f => f.name === 'package.json');
    let dependencies: string[] = [];
    let projectType: CodeContext['projectType'] = 'vanilla';
    
    if (packageJson && packageJson.content) {
      try {
        const pkg = JSON.parse(packageJson.content);
        dependencies = [
          ...Object.keys(pkg.dependencies || {}),
          ...Object.keys(pkg.devDependencies || {})
        ];
        
        // Detect project type
        if (dependencies.includes('next')) projectType = 'nextjs';
        else if (dependencies.includes('react')) projectType = 'react';
        else if (dependencies.includes('typescript')) projectType = 'typescript';
        else if (dependencies.includes('express')) projectType = 'node';
      } catch (error) {
        console.warn('Failed to parse package.json:', error);
      }
    }

    // Analyze code files for patterns
    const codeFiles = files.filter(f => 
      f.name?.endsWith('.ts') || 
      f.name?.endsWith('.tsx') || 
      f.name?.endsWith('.js') || 
      f.name?.endsWith('.jsx')
    );

    const imports = new Map<string, string[]>();
    const exports = new Map<string, string[]>();
    let functionalComponents = 0;
    let classComponents = 0;
    let useStateUsage = 0;
    let styledComponentsUsage = 0;
    let tailwindUsage = 0;

    for (const file of codeFiles) {
      if (!file.content) continue;

      const content = file.content;
      const lines = content.split('\n');
      
      // Extract imports
      const fileImports = lines
        .filter(line => line.trim().startsWith('import '))
        .map(line => line.trim());
      imports.set(file.path, fileImports);

      // Extract exports
      const fileExports = lines
        .filter(line => line.trim().startsWith('export '))
        .map(line => line.trim());
      exports.set(file.path, fileExports);

      // Analyze patterns
      if (content.includes('const ') && content.includes('= () =>')) {
        functionalComponents++;
      }
      if (content.includes('class ') && content.includes('extends React.Component')) {
        classComponents++;
      }
      if (content.includes('useState')) {
        useStateUsage++;
      }
      if (content.includes('styled.') || content.includes('styled(')) {
        styledComponentsUsage++;
      }
      if (content.includes('className=') && content.includes('bg-') || content.includes('text-')) {
        tailwindUsage++;
      }
    }

    // Determine patterns
    const componentStructure = functionalComponents > classComponents ? 'functional' : 
                              classComponents > 0 ? 'class' : 'mixed';
    
    const styling = tailwindUsage > 0 ? 'tailwind' :
                   styledComponentsUsage > 0 ? 'styled-components' : 'css';

    this.context = {
      projectType,
      framework: projectType === 'nextjs' ? 'Next.js' : 
                projectType === 'react' ? 'React' : 
                projectType === 'node' ? 'Node.js' : 'JavaScript',
      dependencies,
      imports,
      exports,
      patterns: {
        componentStructure,
        stateManagement: useStateUsage > 0 ? 'useState' : 'none',
        styling,
        testing: dependencies.includes('jest') ? 'jest' : 
                dependencies.includes('vitest') ? 'vitest' : 'none'
      },
      conventions: {
        naming: 'camelCase', // Could be analyzed from existing files
        fileStructure: 'feature-based', // Could be analyzed from directory structure
        importStyle: 'relative' // Could be analyzed from import patterns
      }
    };

    return this.context;
  }

  /**
   * Generate contextually appropriate code based on analysis
   */
  async generateContextualCode(request: {
    type: 'component' | 'hook' | 'utility' | 'page' | 'api';
    name: string;
    description: string;
    features?: string[];
  }): Promise<{
    code: string;
    imports: string[];
    exports: string[];
    suggestedPath: string;
    dependencies?: string[];
  }> {
    if (!this.context) {
      await this.analyzeCodebase();
    }

    const { type, name, description, features = [] } = request;
    const context = this.context!;

    switch (type) {
      case 'component':
        return this.generateComponent(name, description, features, context);
      case 'hook':
        return this.generateHook(name, description, features, context);
      case 'utility':
        return this.generateUtility(name, description, features, context);
      case 'page':
        return this.generatePage(name, description, features, context);
      case 'api':
        return this.generateApiRoute(name, description, features, context);
      default:
        throw new Error(`Unsupported generation type: ${type}`);
    }
  }

  private generateComponent(name: string, description: string, features: string[], context: CodeContext) {
    const isTypeScript = context.projectType === 'typescript' || context.projectType === 'nextjs';
    const usesTailwind = context.patterns.styling === 'tailwind';
    const usesStyledComponents = context.patterns.styling === 'styled-components';
    const isFunctional = context.patterns.componentStructure === 'functional';

    let imports = ['import React'];
    let code = '';
    let exports = [`export default ${name}`];

    // Add state management imports if needed
    if (features.includes('state') && context.patterns.stateManagement === 'useState') {
      imports[0] = 'import React, { useState }';
    }

    // Add effect imports if needed
    if (features.includes('effects')) {
      imports[0] = imports[0].includes('useState') 
        ? 'import React, { useState, useEffect }'
        : 'import React, { useEffect }';
    }

    // Add styling imports
    if (usesStyledComponents) {
      imports.push('import styled from \'styled-components\'');
    }

    // Generate component code
    if (isFunctional) {
      code = `${imports.join(';\n')};\n\n`;
      
      if (isTypeScript) {
        code += `interface ${name}Props {\n  // Add props here\n}\n\n`;
        code += `const ${name}: React.FC<${name}Props> = () => {\n`;
      } else {
        code += `const ${name} = () => {\n`;
      }

      if (features.includes('state')) {
        code += `  const [state, setState] = useState(null);\n\n`;
      }

      if (features.includes('effects')) {
        code += `  useEffect(() => {\n    // ${description}\n  }, []);\n\n`;
      }

      code += `  return (\n    <div`;
      
      if (usesTailwind) {
        code += ` className="p-4"`;
      }
      
      code += `>\n      {/* ${description} */}\n      <h1>${name} Component</h1>\n    </div>\n  );\n};\n\n`;
      code += `export default ${name};`;
    }

    const suggestedPath = context.projectType === 'nextjs' 
      ? `/components/${name}.${isTypeScript ? 'tsx' : 'jsx'}`
      : `/src/components/${name}.${isTypeScript ? 'tsx' : 'jsx'}`;

    return {
      code,
      imports,
      exports,
      suggestedPath,
      dependencies: usesStyledComponents ? ['styled-components'] : []
    };
  }

  private generateHook(name: string, description: string, features: string[], context: CodeContext) {
    const isTypeScript = context.projectType === 'typescript' || context.projectType === 'nextjs';
    const hookName = name.startsWith('use') ? name : `use${name}`;

    let imports = ['import { useState, useEffect }'];
    let code = `${imports.join(';\n')};\n\n`;

    if (isTypeScript) {
      code += `interface ${hookName.charAt(3).toUpperCase() + hookName.slice(4)}Options {\n  // Add options here\n}\n\n`;
      code += `export const ${hookName} = (options?: ${hookName.charAt(3).toUpperCase() + hookName.slice(4)}Options) => {\n`;
    } else {
      code += `export const ${hookName} = (options = {}) => {\n`;
    }

    code += `  const [data, setData] = useState(null);\n`;
    code += `  const [loading, setLoading] = useState(false);\n`;
    code += `  const [error, setError] = useState(null);\n\n`;
    
    code += `  useEffect(() => {\n    // ${description}\n    setLoading(true);\n    // Implementation here\n    setLoading(false);\n  }, []);\n\n`;
    
    code += `  return { data, loading, error };\n};\n`;

    const suggestedPath = `/src/hooks/${hookName}.${isTypeScript ? 'ts' : 'js'}`;

    return {
      code,
      imports,
      exports: [`export { ${hookName} }`],
      suggestedPath
    };
  }

  private generateUtility(name: string, description: string, features: string[], context: CodeContext) {
    const isTypeScript = context.projectType === 'typescript' || context.projectType === 'nextjs';
    
    let code = `/**\n * ${description}\n */\n\n`;
    
    if (isTypeScript) {
      code += `export const ${name} = (input: any): any => {\n`;
    } else {
      code += `export const ${name} = (input) => {\n`;
    }
    
    code += `  // ${description}\n  return input;\n};\n`;

    const suggestedPath = `/src/utils/${name}.${isTypeScript ? 'ts' : 'js'}`;

    return {
      code,
      imports: [],
      exports: [`export { ${name} }`],
      suggestedPath
    };
  }

  private generatePage(name: string, description: string, features: string[], context: CodeContext) {
    if (context.projectType === 'nextjs') {
      return this.generateNextJSPage(name, description, features, context);
    } else {
      return this.generateReactPage(name, description, features, context);
    }
  }

  private generateNextJSPage(name: string, description: string, features: string[], context: CodeContext) {
    const isTypeScript = true; // Next.js typically uses TypeScript
    
    let code = `import React from 'react';\n`;
    if (features.includes('ssg')) {
      code += `import { GetStaticProps } from 'next';\n`;
    }
    if (features.includes('ssr')) {
      code += `import { GetServerSideProps } from 'next';\n`;
    }
    
    code += `\n`;
    
    if (isTypeScript) {
      code += `interface ${name}PageProps {\n  // Add props here\n}\n\n`;
      code += `const ${name}Page: React.FC<${name}PageProps> = () => {\n`;
    } else {
      code += `const ${name}Page = () => {\n`;
    }
    
    code += `  return (\n    <div>\n      <h1>${name} Page</h1>\n      <p>${description}</p>\n    </div>\n  );\n};\n\n`;
    
    if (features.includes('ssg')) {
      code += `export const getStaticProps: GetStaticProps = async () => {\n  return {\n    props: {},\n  };\n};\n\n`;
    }
    
    if (features.includes('ssr')) {
      code += `export const getServerSideProps: GetServerSideProps = async () => {\n  return {\n    props: {},\n  };\n};\n\n`;
    }
    
    code += `export default ${name}Page;`;

    const suggestedPath = `/pages/${name.toLowerCase()}.tsx`;

    return {
      code,
      imports: ['import React'],
      exports: [`export default ${name}Page`],
      suggestedPath
    };
  }

  private generateReactPage(name: string, description: string, features: string[], context: CodeContext) {
    const isTypeScript = context.projectType === 'typescript';
    
    let code = `import React from 'react';\n\n`;
    
    if (isTypeScript) {
      code += `const ${name}Page: React.FC = () => {\n`;
    } else {
      code += `const ${name}Page = () => {\n`;
    }
    
    code += `  return (\n    <div>\n      <h1>${name} Page</h1>\n      <p>${description}</p>\n    </div>\n  );\n};\n\n`;
    code += `export default ${name}Page;`;

    const suggestedPath = `/src/pages/${name}Page.${isTypeScript ? 'tsx' : 'jsx'}`;

    return {
      code,
      imports: ['import React'],
      exports: [`export default ${name}Page`],
      suggestedPath
    };
  }

  private generateApiRoute(name: string, description: string, features: string[], context: CodeContext) {
    if (context.projectType === 'nextjs') {
      return this.generateNextJSApiRoute(name, description, features);
    } else {
      return this.generateExpressRoute(name, description, features);
    }
  }

  private generateNextJSApiRoute(name: string, description: string, features: string[]) {
    let code = `import { NextApiRequest, NextApiResponse } from 'next';\n\n`;
    
    code += `/**\n * ${description}\n */\n`;
    code += `export default async function handler(\n  req: NextApiRequest,\n  res: NextApiResponse\n) {\n`;
    
    if (features.includes('cors')) {
      code += `  // Enable CORS\n  res.setHeader('Access-Control-Allow-Origin', '*');\n  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');\n  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n\n`;
    }
    
    code += `  if (req.method === 'GET') {\n    // Handle GET request\n    res.status(200).json({ message: '${description}' });\n  } else {\n    res.setHeader('Allow', ['GET']);\n    res.status(405).end(\`Method \${req.method} Not Allowed\`);\n  }\n}`;

    const suggestedPath = `/pages/api/${name.toLowerCase()}.ts`;

    return {
      code,
      imports: ['import { NextApiRequest, NextApiResponse }'],
      exports: ['export default handler'],
      suggestedPath
    };
  }

  private generateExpressRoute(name: string, description: string, features: string[]) {
    let code = `import express from 'express';\n\n`;
    code += `const router = express.Router();\n\n`;
    code += `/**\n * ${description}\n */\n`;
    code += `router.get('/${name.toLowerCase()}', async (req, res) => {\n`;
    code += `  try {\n    // ${description}\n    res.json({ message: '${description}' });\n  } catch (error) {\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n`;
    code += `export default router;`;

    const suggestedPath = `/src/routes/${name.toLowerCase()}.ts`;

    return {
      code,
      imports: ['import express'],
      exports: ['export default router'],
      suggestedPath
    };
  }
}
