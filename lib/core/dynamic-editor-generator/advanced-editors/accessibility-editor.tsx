'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Eye, 
  Users, 
  Volume2, 
  Keyboard, 
  MousePointer,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react'

interface AccessibilityEditorProps {
  block: any
  onUpdate: (updates: any) => void
}

export function AccessibilityEditor({ block, onUpdate }: AccessibilityEditorProps) {
  const accessibility = block.accessibility || {}

  const updateAccessibility = (updates: any) => {
    onUpdate({
      accessibility: { ...accessibility, ...updates }
    })
  }

  const getAccessibilityScore = () => {
    let score = 0
    let total = 0

    // Check for ARIA label
    total++
    if (accessibility.ariaLabel) score++

    // Check for role
    total++
    if (accessibility.role) score++

    // Check for description
    total++
    if (accessibility.ariaDescription) score++

    // Check for focusable
    total++
    if (accessibility.focusable !== undefined) score++

    return { score, total, percentage: Math.round((score / total) * 100) }
  }

  const accessibilityScore = getAccessibilityScore()

  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600'
    if (percentage >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreIcon = (percentage: number) => {
    if (percentage >= 80) return <CheckCircle className="h-4 w-4 text-green-600" />
    if (percentage >= 60) return <AlertTriangle className="h-4 w-4 text-yellow-600" />
    return <AlertTriangle className="h-4 w-4 text-red-600" />
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Accessibility
            </CardTitle>
            <CardDescription className="text-xs">
              Improve accessibility for screen readers and assistive technologies
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {getScoreIcon(accessibilityScore.percentage)}
            <Badge 
              variant="outline" 
              className={`text-xs ${getScoreColor(accessibilityScore.percentage)}`}
            >
              {accessibilityScore.percentage}%
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Accessibility Score */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium">Accessibility Score</span>
            <span className={`text-xs font-bold ${getScoreColor(accessibilityScore.percentage)}`}>
              {accessibilityScore.score}/{accessibilityScore.total}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                accessibilityScore.percentage >= 80 ? 'bg-green-600' :
                accessibilityScore.percentage >= 60 ? 'bg-yellow-600' : 'bg-red-600'
              }`}
              style={{ width: `${accessibilityScore.percentage}%` }}
            />
          </div>
        </div>

        {/* ARIA Label */}
        <div>
          <div className="flex items-center space-x-2 mb-1">
            <Label className="text-xs">ARIA Label</Label>
            {accessibility.ariaLabel && <CheckCircle className="h-3 w-3 text-green-600" />}
          </div>
          <Input
            type="text"
            value={accessibility.ariaLabel || ''}
            onChange={(e) => updateAccessibility({ ariaLabel: e.target.value })}
            placeholder="Descriptive label for screen readers"
            className="text-xs"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Provides an accessible name for the element
          </p>
        </div>

        {/* ARIA Description */}
        <div>
          <div className="flex items-center space-x-2 mb-1">
            <Label className="text-xs">ARIA Description</Label>
            {accessibility.ariaDescription && <CheckCircle className="h-3 w-3 text-green-600" />}
          </div>
          <Textarea
            value={accessibility.ariaDescription || ''}
            onChange={(e) => updateAccessibility({ ariaDescription: e.target.value })}
            placeholder="Detailed description of the block content and functionality"
            rows={3}
            className="text-xs"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Provides additional context and description
          </p>
        </div>

        {/* ARIA Role */}
        <div>
          <div className="flex items-center space-x-2 mb-1">
            <Label className="text-xs">ARIA Role</Label>
            {accessibility.role && <CheckCircle className="h-3 w-3 text-green-600" />}
          </div>
          <select
            value={accessibility.role || ''}
            onChange={(e) => updateAccessibility({ role: e.target.value })}
            className="w-full px-2 py-1 text-xs border rounded"
          >
            <option value="">Default (no role)</option>
            <option value="banner">Banner</option>
            <option value="main">Main</option>
            <option value="navigation">Navigation</option>
            <option value="complementary">Complementary</option>
            <option value="contentinfo">Content Info</option>
            <option value="region">Region</option>
            <option value="article">Article</option>
            <option value="section">Section</option>
            <option value="button">Button</option>
            <option value="link">Link</option>
            <option value="heading">Heading</option>
            <option value="list">List</option>
            <option value="listitem">List Item</option>
            <option value="img">Image</option>
            <option value="figure">Figure</option>
          </select>
          <p className="text-xs text-muted-foreground mt-1">
            Defines the element's purpose and behavior
          </p>
        </div>

        {/* Keyboard Navigation */}
        <div className="space-y-3 pt-3 border-t">
          <Label className="text-xs font-medium flex items-center">
            <Keyboard className="h-3 w-3 mr-1" />
            Keyboard Navigation
          </Label>
          
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs">Make Focusable</Label>
              <p className="text-xs text-muted-foreground">Allow keyboard navigation to this element</p>
            </div>
            <Switch
              checked={accessibility.focusable || false}
              onCheckedChange={(checked) => updateAccessibility({ focusable: checked })}
            />
          </div>

          {accessibility.focusable && (
            <div>
              <Label className="text-xs">Tab Index</Label>
              <Input
                type="number"
                value={accessibility.tabIndex || 0}
                onChange={(e) => updateAccessibility({ tabIndex: parseInt(e.target.value) })}
                placeholder="0"
                className="text-xs mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                0 = natural tab order, -1 = programmatically focusable only
              </p>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs">Skip to Content Link</Label>
              <p className="text-xs text-muted-foreground">Add a skip link for keyboard users</p>
            </div>
            <Switch
              checked={accessibility.skipToContent || false}
              onCheckedChange={(checked) => updateAccessibility({ skipToContent: checked })}
            />
          </div>
        </div>

        {/* Screen Reader */}
        <div className="space-y-3 pt-3 border-t">
          <Label className="text-xs font-medium flex items-center">
            <Volume2 className="h-3 w-3 mr-1" />
            Screen Reader
          </Label>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs">Hide from Screen Readers</Label>
              <p className="text-xs text-muted-foreground">Decorative elements only</p>
            </div>
            <Switch
              checked={accessibility.ariaHidden || false}
              onCheckedChange={(checked) => updateAccessibility({ ariaHidden: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs">Live Region</Label>
              <p className="text-xs text-muted-foreground">Announce dynamic content changes</p>
            </div>
            <Switch
              checked={accessibility.liveRegion || false}
              onCheckedChange={(checked) => updateAccessibility({ liveRegion: checked })}
            />
          </div>

          {accessibility.liveRegion && (
            <div>
              <Label className="text-xs">Live Region Type</Label>
              <select
                value={accessibility.ariaLive || 'polite'}
                onChange={(e) => updateAccessibility({ ariaLive: e.target.value })}
                className="w-full mt-1 px-2 py-1 text-xs border rounded"
              >
                <option value="off">Off</option>
                <option value="polite">Polite</option>
                <option value="assertive">Assertive</option>
              </select>
            </div>
          )}
        </div>

        {/* Color and Contrast */}
        <div className="space-y-3 pt-3 border-t">
          <Label className="text-xs font-medium flex items-center">
            <Eye className="h-3 w-3 mr-1" />
            Visual Accessibility
          </Label>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs">High Contrast Mode</Label>
              <p className="text-xs text-muted-foreground">Optimize for high contrast displays</p>
            </div>
            <Switch
              checked={accessibility.highContrast || false}
              onCheckedChange={(checked) => updateAccessibility({ highContrast: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-xs">Reduce Motion</Label>
              <p className="text-xs text-muted-foreground">Respect user's motion preferences</p>
            </div>
            <Switch
              checked={accessibility.respectMotionPreference || true}
              onCheckedChange={(checked) => updateAccessibility({ respectMotionPreference: checked })}
            />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex items-center space-x-2 pt-3 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              updateAccessibility({
                ariaLabel: `${block.type} block`,
                role: block.type === 'hero' ? 'banner' : 'region',
                focusable: true,
                respectMotionPreference: true
              })
            }}
            className="text-xs"
          >
            <CheckCircle className="h-3 w-3 mr-1" />
            Auto-configure
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => updateAccessibility({})}
            className="text-xs"
          >
            Reset
          </Button>
        </div>

        {/* Accessibility Tips */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <Info className="h-4 w-4 text-blue-600 mt-0.5" />
            <div>
              <p className="text-xs font-medium text-blue-800 mb-1">Accessibility Tips</p>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>• Use descriptive labels for all interactive elements</li>
                <li>• Ensure sufficient color contrast (4.5:1 minimum)</li>
                <li>• Test with keyboard navigation only</li>
                <li>• Provide alternative text for images</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
