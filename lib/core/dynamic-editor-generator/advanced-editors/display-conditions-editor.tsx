'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Eye, 
  Users, 
  Clock, 
  Calendar,
  Globe,
  Smartphone,
  Settings,
  Plus,
  X
} from 'lucide-react'

interface DisplayConditionsEditorProps {
  conditions: any
  onUpdate: (updates: any) => void
}

export function DisplayConditionsEditor({ conditions, onUpdate }: DisplayConditionsEditorProps) {
  const getActiveConditionsCount = () => {
    let count = 0
    if (conditions.userRoles?.length > 0) count++
    if (conditions.timeRange?.enabled) count++
    if (conditions.location?.enabled) count++
    if (conditions.device?.enabled) count++
    if (conditions.customRules?.length > 0) count++
    return count
  }

  const activeConditions = getActiveConditionsCount()

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm flex items-center">
              <Eye className="h-4 w-4 mr-2" />
              Display Conditions
            </CardTitle>
            <CardDescription className="text-xs">
              Control when this block should be displayed
            </CardDescription>
          </div>
          {activeConditions > 0 && (
            <Badge variant="secondary" className="text-xs">
              {activeConditions} condition{activeConditions > 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* User Role Conditions */}
        <div className="space-y-3">
          <Label className="text-xs font-medium flex items-center">
            <Users className="h-3 w-3 mr-1" />
            User Roles
          </Label>
          <div className="space-y-2">
            {['guest', 'customer', 'admin', 'editor', 'subscriber'].map((role) => (
              <div key={role} className="flex items-center justify-between p-2 border rounded">
                <div>
                  <Label className="text-xs capitalize font-medium">{role}</Label>
                  <p className="text-xs text-muted-foreground">
                    {role === 'guest' && 'Non-logged in users'}
                    {role === 'customer' && 'Registered customers'}
                    {role === 'admin' && 'Site administrators'}
                    {role === 'editor' && 'Content editors'}
                    {role === 'subscriber' && 'Newsletter subscribers'}
                  </p>
                </div>
                <Switch
                  checked={conditions.userRoles?.includes(role) || false}
                  onCheckedChange={(checked) => {
                    const currentRoles = conditions.userRoles || []
                    const newRoles = checked
                      ? [...currentRoles, role]
                      : currentRoles.filter((r: string) => r !== role)
                    onUpdate({ userRoles: newRoles })
                  }}
                />
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Time-based Conditions */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              Time-based Display
            </Label>
            <Switch
              checked={conditions.timeRange?.enabled || false}
              onCheckedChange={(checked) => onUpdate({
                timeRange: { ...conditions.timeRange, enabled: checked }
              })}
            />
          </div>

          {conditions.timeRange?.enabled && (
            <div className="space-y-3 ml-4 pl-4 border-l-2 border-muted">
              <div>
                <Label className="text-xs">Start Date & Time</Label>
                <Input
                  type="datetime-local"
                  value={conditions.timeRange?.start || ''}
                  onChange={(e) => onUpdate({
                    timeRange: { ...conditions.timeRange, start: e.target.value }
                  })}
                  className="text-xs mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">End Date & Time</Label>
                <Input
                  type="datetime-local"
                  value={conditions.timeRange?.end || ''}
                  onChange={(e) => onUpdate({
                    timeRange: { ...conditions.timeRange, end: e.target.value }
                  })}
                  className="text-xs mt-1"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={conditions.timeRange?.recurring || false}
                  onCheckedChange={(checked) => onUpdate({
                    timeRange: { ...conditions.timeRange, recurring: checked }
                  })}
                />
                <Label className="text-xs">Recurring (daily)</Label>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Location-based Conditions */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium flex items-center">
              <Globe className="h-3 w-3 mr-1" />
              Location-based Display
            </Label>
            <Switch
              checked={conditions.location?.enabled || false}
              onCheckedChange={(checked) => onUpdate({
                location: { ...conditions.location, enabled: checked }
              })}
            />
          </div>

          {conditions.location?.enabled && (
            <div className="space-y-3 ml-4 pl-4 border-l-2 border-muted">
              <div>
                <Label className="text-xs">Countries (comma-separated)</Label>
                <Input
                  type="text"
                  value={conditions.location?.countries || ''}
                  onChange={(e) => onUpdate({
                    location: { ...conditions.location, countries: e.target.value }
                  })}
                  placeholder="US, CA, GB, AU"
                  className="text-xs mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">Cities (comma-separated)</Label>
                <Input
                  type="text"
                  value={conditions.location?.cities || ''}
                  onChange={(e) => onUpdate({
                    location: { ...conditions.location, cities: e.target.value }
                  })}
                  placeholder="New York, London, Sydney"
                  className="text-xs mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">Exclude Mode</Label>
                <div className="flex items-center space-x-2 mt-1">
                  <Switch
                    checked={conditions.location?.exclude || false}
                    onCheckedChange={(checked) => onUpdate({
                      location: { ...conditions.location, exclude: checked }
                    })}
                  />
                  <span className="text-xs text-muted-foreground">
                    Hide in specified locations instead of showing
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Device-based Conditions */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium flex items-center">
              <Smartphone className="h-3 w-3 mr-1" />
              Device-based Display
            </Label>
            <Switch
              checked={conditions.device?.enabled || false}
              onCheckedChange={(checked) => onUpdate({
                device: { ...conditions.device, enabled: checked }
              })}
            />
          </div>

          {conditions.device?.enabled && (
            <div className="space-y-3 ml-4 pl-4 border-l-2 border-muted">
              <div>
                <Label className="text-xs">Operating Systems</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {['Windows', 'macOS', 'iOS', 'Android', 'Linux'].map((os) => (
                    <div key={os} className="flex items-center space-x-2">
                      <Switch
                        checked={conditions.device?.operatingSystems?.includes(os) || false}
                        onCheckedChange={(checked) => {
                          const currentOS = conditions.device?.operatingSystems || []
                          const newOS = checked
                            ? [...currentOS, os]
                            : currentOS.filter((o: string) => o !== os)
                          onUpdate({
                            device: { ...conditions.device, operatingSystems: newOS }
                          })
                        }}
                      />
                      <Label className="text-xs">{os}</Label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <Label className="text-xs">Browsers</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera'].map((browser) => (
                    <div key={browser} className="flex items-center space-x-2">
                      <Switch
                        checked={conditions.device?.browsers?.includes(browser) || false}
                        onCheckedChange={(checked) => {
                          const currentBrowsers = conditions.device?.browsers || []
                          const newBrowsers = checked
                            ? [...currentBrowsers, browser]
                            : currentBrowsers.filter((b: string) => b !== browser)
                          onUpdate({
                            device: { ...conditions.device, browsers: newBrowsers }
                          })
                        }}
                      />
                      <Label className="text-xs">{browser}</Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Custom Rules */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium flex items-center">
              <Settings className="h-3 w-3 mr-1" />
              Custom Rules
            </Label>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const currentRules = conditions.customRules || []
                onUpdate({
                  customRules: [...currentRules, { field: '', operator: 'equals', value: '' }]
                })
              }}
              className="text-xs h-6"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Rule
            </Button>
          </div>

          {conditions.customRules?.map((rule: any, index: number) => (
            <div key={index} className="p-3 border rounded-lg space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Rule {index + 1}</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const newRules = conditions.customRules.filter((_: any, i: number) => i !== index)
                    onUpdate({ customRules: newRules })
                  }}
                  className="text-xs h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <Label className="text-xs">Field</Label>
                  <select
                    value={rule.field}
                    onChange={(e) => {
                      const newRules = [...conditions.customRules]
                      newRules[index] = { ...rule, field: e.target.value }
                      onUpdate({ customRules: newRules })
                    }}
                    className="w-full mt-1 px-2 py-1 text-xs border rounded"
                  >
                    <option value="">Select field</option>
                    <option value="url">Current URL</option>
                    <option value="referrer">Referrer</option>
                    <option value="userAgent">User Agent</option>
                    <option value="cookie">Cookie Value</option>
                    <option value="localStorage">Local Storage</option>
                    <option value="sessionStorage">Session Storage</option>
                  </select>
                </div>
                
                <div>
                  <Label className="text-xs">Operator</Label>
                  <select
                    value={rule.operator}
                    onChange={(e) => {
                      const newRules = [...conditions.customRules]
                      newRules[index] = { ...rule, operator: e.target.value }
                      onUpdate({ customRules: newRules })
                    }}
                    className="w-full mt-1 px-2 py-1 text-xs border rounded"
                  >
                    <option value="equals">Equals</option>
                    <option value="contains">Contains</option>
                    <option value="startsWith">Starts with</option>
                    <option value="endsWith">Ends with</option>
                    <option value="regex">Regex match</option>
                  </select>
                </div>
                
                <div>
                  <Label className="text-xs">Value</Label>
                  <Input
                    type="text"
                    value={rule.value}
                    onChange={(e) => {
                      const newRules = [...conditions.customRules]
                      newRules[index] = { ...rule, value: e.target.value }
                      onUpdate({ customRules: newRules })
                    }}
                    placeholder="Enter value"
                    className="text-xs mt-1"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        {activeConditions > 0 && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-xs font-medium text-blue-800 mb-1">
              Active Conditions: {activeConditions}
            </p>
            <p className="text-xs text-blue-700">
              This block will only be displayed when all active conditions are met.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
