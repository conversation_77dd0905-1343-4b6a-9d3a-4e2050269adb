'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Code,
  Copy,
  Download,
  Upload,
  CheckCircle,
  AlertTriangle,
  Wand2,
  RotateCcw
} from 'lucide-react'

interface CustomCSSEditorProps {
  block: any
  onUpdate: (updates: any) => void
}

export function CustomCSSEditor({ block, onUpdate }: CustomCSSEditorProps) {
  const [cssValue, setCssValue] = useState(block.styling?.customCss || '')
  const [cssError, setCssError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('editor')

  const handleCssChange = (value: string) => {
    setCssValue(value)
    
    // Basic CSS validation
    try {
      // Simple validation - check for balanced braces
      const openBraces = (value.match(/{/g) || []).length
      const closeBraces = (value.match(/}/g) || []).length
      
      if (openBraces !== closeBraces) {
        setCssError('Unbalanced braces in CSS')
      } else {
        setCssError(null)
        onUpdate({
          styling: { ...block.styling, customCss: value }
        })
      }
    } catch (error) {
      setCssError('Invalid CSS syntax')
    }
  }

  const formatCss = () => {
    // Basic CSS formatting
    let formatted = cssValue
      .replace(/\s*{\s*/g, ' {\n  ')
      .replace(/;\s*/g, ';\n  ')
      .replace(/\s*}\s*/g, '\n}\n\n')
      .replace(/,\s*/g, ',\n')
      .trim()
    
    setCssValue(formatted)
    handleCssChange(formatted)
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(cssValue)
  }

  const exportCss = () => {
    const blob = new Blob([cssValue], { type: 'text/css' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${block.type}-block-styles.css`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const importCss = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        setCssValue(content)
        handleCssChange(content)
      }
      reader.readAsText(file)
    }
  }

  const insertTemplate = (template: string) => {
    const templates = {
      hover: `/* Hover effects */
.block-${block.id}:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}`,
      gradient: `/* Gradient background */
.block-${block.id} {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}`,
      animation: `/* Custom animation */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.block-${block.id} {
  animation: slideIn 0.6s ease-out;
}`,
      responsive: `/* Responsive styles */
.block-${block.id} {
  padding: 2rem;
}

@media (max-width: 768px) {
  .block-${block.id} {
    padding: 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .block-${block.id} {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
}`,
      glassmorphism: `/* Glassmorphism effect */
.block-${block.id} {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}`
    }
    
    const templateCss = templates[template as keyof typeof templates]
    const newValue = cssValue ? `${cssValue}\n\n${templateCss}` : templateCss
    setCssValue(newValue)
    handleCssChange(newValue)
  }

  const getCssStats = () => {
    const lines = cssValue.split('\n').length
    const rules = (cssValue.match(/{[^}]*}/g) || []).length
    const selectors = (cssValue.match(/[^{}]+(?=\s*{)/g) || []).length
    return { lines, rules, selectors }
  }

  const stats = getCssStats()

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm flex items-center">
              <Code className="h-4 w-4 mr-2" />
              Custom CSS
            </CardTitle>
            <CardDescription className="text-xs">
              Add custom CSS styles for this block
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {cssError ? (
              <Badge variant="destructive" className="text-xs">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Error
              </Badge>
            ) : cssValue ? (
              <Badge variant="secondary" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                {stats.lines} lines
              </Badge>
            ) : null}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="editor" className="text-xs">Editor</TabsTrigger>
            <TabsTrigger value="templates" className="text-xs">Templates</TabsTrigger>
            <TabsTrigger value="preview" className="text-xs">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="editor" className="space-y-4">
            {/* CSS Editor */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-xs font-medium">CSS Code</label>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={formatCss}
                    title="Format CSS"
                    className="h-6 px-2 text-xs"
                  >
                    Format
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={copyToClipboard}
                    title="Copy to clipboard"
                    className="h-6 px-2 text-xs"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              
              <Textarea
                value={cssValue}
                onChange={(e) => handleCssChange(e.target.value)}
                rows={12}
                className="font-mono text-xs"
                placeholder={`/* Custom CSS for ${block.type} block */
.block-${block.id} {
  /* Your styles here */
}

/* Example hover effect */
.block-${block.id}:hover {
  transform: scale(1.02);
  transition: transform 0.3s ease;
}`}
              />
              
              {cssError && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    CSS Error: {cssError}
                  </AlertDescription>
                </Alert>
              )}

              {/* CSS Stats */}
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center space-x-4">
                  <span>{stats.lines} lines</span>
                  <span>{stats.rules} rules</span>
                  <span>{stats.selectors} selectors</span>
                </div>
                <div className="text-xs">
                  Target: .block-{block.id}
                </div>
              </div>
            </div>

            {/* Import/Export */}
            <div className="flex items-center space-x-2 pt-2 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={exportCss}
                className="text-xs"
                disabled={!cssValue}
              >
                <Download className="h-3 w-3 mr-1" />
                Export
              </Button>
              
              <div className="relative">
                <input
                  type="file"
                  accept=".css"
                  onChange={importCss}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                <Button variant="outline" size="sm" className="text-xs">
                  <Upload className="h-3 w-3 mr-1" />
                  Import
                </Button>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setCssValue('')
                  handleCssChange('')
                }}
                className="text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Clear
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <div className="space-y-3">
              <label className="text-xs font-medium">CSS Templates</label>
              
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => insertTemplate('hover')}
                  className="text-xs justify-start"
                >
                  <Wand2 className="h-3 w-3 mr-2" />
                  Hover Effects
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => insertTemplate('gradient')}
                  className="text-xs justify-start"
                >
                  <Wand2 className="h-3 w-3 mr-2" />
                  Gradient Background
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => insertTemplate('animation')}
                  className="text-xs justify-start"
                >
                  <Wand2 className="h-3 w-3 mr-2" />
                  Custom Animation
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => insertTemplate('responsive')}
                  className="text-xs justify-start"
                >
                  <Wand2 className="h-3 w-3 mr-2" />
                  Responsive Styles
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => insertTemplate('glassmorphism')}
                  className="text-xs justify-start"
                >
                  <Wand2 className="h-3 w-3 mr-2" />
                  Glassmorphism Effect
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <div className="space-y-2">
              <label className="text-xs font-medium">CSS Preview</label>
              <div className="bg-muted/50 rounded-md p-3 max-h-64 overflow-auto">
                <pre className="text-xs text-muted-foreground whitespace-pre-wrap">
                  {cssValue || '/* No custom CSS added yet */'}
                </pre>
              </div>
            </div>
            
            {cssValue && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-xs font-medium text-blue-800 mb-1">
                  CSS Applied
                </p>
                <p className="text-xs text-blue-700">
                  This CSS will be applied to the block with class .block-{block.id}
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
