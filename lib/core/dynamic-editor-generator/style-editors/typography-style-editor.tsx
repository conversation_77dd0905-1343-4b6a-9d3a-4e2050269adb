'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface TypographyStyleEditorProps {
  styling: any
  onUpdate: (updates: any) => void
}

export function TypographyStyleEditor({ styling, onUpdate }: TypographyStyleEditorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm">Typography</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label className="text-xs">Text Color</Label>
          <div className="flex gap-2 mt-1">
            <Input
              type="color"
              value={styling.textColor || '#000000'}
              onChange={(e) => onUpdate({ textColor: e.target.value })}
              className="h-8 w-16"
            />
            <Input
              type="text"
              value={styling.textColor || '#000000'}
              on<PERSON><PERSON><PERSON>={(e) => onUpdate({ textColor: e.target.value })}
              placeholder="#000000"
              className="text-xs flex-1"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs">Font Family</Label>
            <select
              value={styling.fontFamily || 'inherit'}
              onChange={(e) => onUpdate({ fontFamily: e.target.value })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="inherit">Inherit</option>
              <option value="Inter">Inter</option>
              <option value="P22 Underground">P22 Underground</option>
              <option value="serif">Serif</option>
              <option value="monospace">Monospace</option>
            </select>
          </div>
          <div>
            <Label className="text-xs">Font Size</Label>
            <Input
              type="text"
              value={styling.fontSize || ''}
              onChange={(e) => onUpdate({ fontSize: e.target.value })}
              placeholder="16px, 1rem"
              className="text-xs mt-1"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs">Font Weight</Label>
            <select
              value={styling.fontWeight || 'normal'}
              onChange={(e) => onUpdate({ fontWeight: e.target.value })}
              className="w-full mt-1 px-2 py-1 text-xs border rounded"
            >
              <option value="normal">Normal</option>
              <option value="light">Light</option>
              <option value="medium">Medium</option>
              <option value="semibold">Semibold</option>
              <option value="bold">Bold</option>
            </select>
          </div>
          <div>
            <Label className="text-xs">Line Height</Label>
            <Input
              type="text"
              value={styling.lineHeight || ''}
              onChange={(e) => onUpdate({ lineHeight: e.target.value })}
              placeholder="1.5, 24px"
              className="text-xs mt-1"
            />
          </div>
        </div>

        <div>
          <Label className="text-xs">Text Align</Label>
          <select
            value={styling.textAlign || 'left'}
            onChange={(e) => onUpdate({ textAlign: e.target.value })}
            className="w-full mt-1 px-2 py-1 text-xs border rounded"
          >
            <option value="left">Left</option>
            <option value="center">Center</option>
            <option value="right">Right</option>
            <option value="justify">Justify</option>
          </select>
        </div>

        <div>
          <Label className="text-xs">Letter Spacing</Label>
          <Input
            type="text"
            value={styling.letterSpacing || ''}
            onChange={(e) => onUpdate({ letterSpacing: e.target.value })}
            placeholder="0.1em, 1px"
            className="text-xs mt-1"
          />
        </div>

        <div>
          <Label className="text-xs">Text Transform</Label>
          <select
            value={styling.textTransform || 'none'}
            onChange={(e) => onUpdate({ textTransform: e.target.value })}
            className="w-full mt-1 px-2 py-1 text-xs border rounded"
          >
            <option value="none">None</option>
            <option value="uppercase">Uppercase</option>
            <option value="lowercase">Lowercase</option>
            <option value="capitalize">Capitalize</option>
          </select>
        </div>

        <div>
          <Label className="text-xs">Text Decoration</Label>
          <select
            value={styling.textDecoration || 'none'}
            onChange={(e) => onUpdate({ textDecoration: e.target.value })}
            className="w-full mt-1 px-2 py-1 text-xs border rounded"
          >
            <option value="none">None</option>
            <option value="underline">Underline</option>
            <option value="overline">Overline</option>
            <option value="line-through">Line Through</option>
          </select>
        </div>

        <div>
          <Label className="text-xs">Font Style</Label>
          <select
            value={styling.fontStyle || 'normal'}
            onChange={(e) => onUpdate({ fontStyle: e.target.value })}
            className="w-full mt-1 px-2 py-1 text-xs border rounded"
          >
            <option value="normal">Normal</option>
            <option value="italic">Italic</option>
            <option value="oblique">Oblique</option>
          </select>
        </div>
      </CardContent>
    </Card>
  )
}
