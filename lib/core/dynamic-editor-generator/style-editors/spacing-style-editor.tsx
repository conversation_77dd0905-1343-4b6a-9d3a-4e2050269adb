'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface SpacingStyleEditorProps {
  styling: any
  onUpdate: (updates: any) => void
}

export function SpacingStyleEditor({ styling, onUpdate }: SpacingStyleEditorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm">Spacing</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Padding */}
        <div>
          <Label className="text-xs mb-2 block">Padding</Label>
          <div className="grid grid-cols-2 gap-2">
            <Input
              placeholder="Top"
              value={styling.padding?.top || ''}
              onChange={(e) => onUpdate({
                padding: { ...styling.padding, top: e.target.value }
              })}
              className="text-xs"
            />
            <Input
              placeholder="Right"
              value={styling.padding?.right || ''}
              onChange={(e) => onUpdate({
                padding: { ...styling.padding, right: e.target.value }
              })}
              className="text-xs"
            />
            <Input
              placeholder="Bottom"
              value={styling.padding?.bottom || ''}
              onChange={(e) => onUpdate({
                padding: { ...styling.padding, bottom: e.target.value }
              })}
              className="text-xs"
            />
            <Input
              placeholder="Left"
              value={styling.padding?.left || ''}
              onChange={(e) => onUpdate({
                padding: { ...styling.padding, left: e.target.value }
              })}
              className="text-xs"
            />
          </div>
        </div>

        {/* Margin */}
        <div>
          <Label className="text-xs mb-2 block">Margin</Label>
          <div className="grid grid-cols-2 gap-2">
            <Input
              placeholder="Top"
              value={styling.margin?.top || ''}
              onChange={(e) => onUpdate({
                margin: { ...styling.margin, top: e.target.value }
              })}
              className="text-xs"
            />
            <Input
              placeholder="Right"
              value={styling.margin?.right || ''}
              onChange={(e) => onUpdate({
                margin: { ...styling.margin, right: e.target.value }
              })}
              className="text-xs"
            />
            <Input
              placeholder="Bottom"
              value={styling.margin?.bottom || ''}
              onChange={(e) => onUpdate({
                margin: { ...styling.margin, bottom: e.target.value }
              })}
              className="text-xs"
            />
            <Input
              placeholder="Left"
              value={styling.margin?.left || ''}
              onChange={(e) => onUpdate({
                margin: { ...styling.margin, left: e.target.value }
              })}
              className="text-xs"
            />
          </div>
        </div>

        {/* Width & Height */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs">Width</Label>
            <Input
              type="text"
              value={styling.width || ''}
              onChange={(e) => onUpdate({ width: e.target.value })}
              placeholder="auto, 100%, 300px"
              className="text-xs mt-1"
            />
          </div>
          <div>
            <Label className="text-xs">Height</Label>
            <Input
              type="text"
              value={styling.height || ''}
              onChange={(e) => onUpdate({ height: e.target.value })}
              placeholder="auto, 100vh, 200px"
              className="text-xs mt-1"
            />
          </div>
        </div>

        {/* Max Width & Max Height */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs">Max Width</Label>
            <Input
              type="text"
              value={styling.maxWidth || ''}
              onChange={(e) => onUpdate({ maxWidth: e.target.value })}
              placeholder="none, 1200px"
              className="text-xs mt-1"
            />
          </div>
          <div>
            <Label className="text-xs">Max Height</Label>
            <Input
              type="text"
              value={styling.maxHeight || ''}
              onChange={(e) => onUpdate({ maxHeight: e.target.value })}
              placeholder="none, 500px"
              className="text-xs mt-1"
            />
          </div>
        </div>

        {/* Min Width & Min Height */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs">Min Width</Label>
            <Input
              type="text"
              value={styling.minWidth || ''}
              onChange={(e) => onUpdate({ minWidth: e.target.value })}
              placeholder="0, 200px"
              className="text-xs mt-1"
            />
          </div>
          <div>
            <Label className="text-xs">Min Height</Label>
            <Input
              type="text"
              value={styling.minHeight || ''}
              onChange={(e) => onUpdate({ minHeight: e.target.value })}
              placeholder="0, 100px"
              className="text-xs mt-1"
            />
          </div>
        </div>

        {/* Position */}
        <div>
          <Label className="text-xs">Position</Label>
          <select
            value={styling.position || 'static'}
            onChange={(e) => onUpdate({ position: e.target.value })}
            className="w-full mt-1 px-2 py-1 text-xs border rounded"
          >
            <option value="static">Static</option>
            <option value="relative">Relative</option>
            <option value="absolute">Absolute</option>
            <option value="fixed">Fixed</option>
            <option value="sticky">Sticky</option>
          </select>
        </div>

        {/* Position Values (for non-static positions) */}
        {styling.position && styling.position !== 'static' && (
          <div>
            <Label className="text-xs mb-2 block">Position Values</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Top"
                value={styling.top || ''}
                onChange={(e) => onUpdate({ top: e.target.value })}
                className="text-xs"
              />
              <Input
                placeholder="Right"
                value={styling.right || ''}
                onChange={(e) => onUpdate({ right: e.target.value })}
                className="text-xs"
              />
              <Input
                placeholder="Bottom"
                value={styling.bottom || ''}
                onChange={(e) => onUpdate({ bottom: e.target.value })}
                className="text-xs"
              />
              <Input
                placeholder="Left"
                value={styling.left || ''}
                onChange={(e) => onUpdate({ left: e.target.value })}
                className="text-xs"
              />
            </div>
          </div>
        )}

        {/* Z-Index */}
        <div>
          <Label className="text-xs">Z-Index</Label>
          <Input
            type="number"
            value={styling.zIndex || ''}
            onChange={(e) => onUpdate({ zIndex: e.target.value })}
            placeholder="auto, 10, 999"
            className="text-xs mt-1"
          />
        </div>
      </CardContent>
    </Card>
  )
}
