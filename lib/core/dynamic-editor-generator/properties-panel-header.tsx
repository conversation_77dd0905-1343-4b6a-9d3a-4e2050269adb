'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { 
  <PERSON>, 
  <PERSON>Off, 
  <PERSON><PERSON>, 
  Trash2,
  MoreHorizontal,
  Move
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface PropertiesPanelHeaderProps {
  block: any
  blockType: any
  onVisibilityToggle: () => void
  onDuplicate: () => void
  onDelete: () => void
  onMoveUp?: () => void
  onMoveDown?: () => void
}

export function PropertiesPanelHeader({
  block,
  blockType,
  onVisibilityToggle,
  onDuplicate,
  onDelete,
  onMoveUp,
  onMoveDown
}: PropertiesPanelHeaderProps) {
  return (
    <div className="p-4 border-b bg-background/50">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{blockType.icon || '📦'}</span>
          <div>
            <h2 className="font-semibold text-sm">{blockType.displayName}</h2>
            <p className="text-xs text-muted-foreground">
              Position: {block.position + 1}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-1">
          {/* Quick Actions */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onVisibilityToggle}
            title={block.isVisible ? 'Hide block' : 'Show block'}
            className="h-8 w-8 p-0"
          >
            {block.isVisible ? (
              <Eye className="h-4 w-4" />
            ) : (
              <EyeOff className="h-4 w-4 text-muted-foreground" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onDuplicate}
            title="Duplicate block"
            className="h-8 w-8 p-0"
          >
            <Copy className="h-4 w-4" />
          </Button>

          {/* More Actions Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                title="More actions"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {onMoveUp && (
                <DropdownMenuItem onClick={onMoveUp}>
                  <Move className="h-4 w-4 mr-2" />
                  Move Up
                </DropdownMenuItem>
              )}
              {onMoveDown && (
                <DropdownMenuItem onClick={onMoveDown}>
                  <Move className="h-4 w-4 mr-2" />
                  Move Down
                </DropdownMenuItem>
              )}
              {(onMoveUp || onMoveDown) && <DropdownMenuSeparator />}
              <DropdownMenuItem onClick={onDuplicate}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Block
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={onDelete}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Block
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      {blockType.description && (
        <p className="text-xs text-muted-foreground leading-relaxed">
          {blockType.description}
        </p>
      )}

      {/* Block Status Indicators */}
      <div className="flex items-center space-x-2 mt-3">
        {!block.isVisible && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600">
            <EyeOff className="h-3 w-3 mr-1" />
            Hidden
          </span>
        )}
        
        {block.responsive?.hideOnMobile && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-600">
            Hidden on Mobile
          </span>
        )}
        
        {block.responsive?.hideOnTablet && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-600">
            Hidden on Tablet
          </span>
        )}
        
        {block.responsive?.hideOnDesktop && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-600">
            Hidden on Desktop
          </span>
        )}

        {block.animation?.enabled && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-600">
            Animated
          </span>
        )}

        {block.conditions && Object.keys(block.conditions).length > 0 && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-600">
            Conditional
          </span>
        )}
      </div>
    </div>
  )
}
