'use client'

import React from 'react'
import { FieldConfig, FieldValue, CustomFieldProps } from './types'

// Import all field components
import { TextField } from './fields/text-field'
import { NumberField } from './fields/number-field'
import { BooleanField } from './fields/boolean-field'
import { SelectField } from './fields/select-field'
import { MultiSelectField } from './fields/multi-select-field'
import { ColorField } from './fields/color-field'
import { RangeField } from './fields/range-field'
import { TextareaField } from './fields/textarea-field'
import { RichTextField } from './fields/rich-text-field'
import { CodeField } from './fields/code-field'
import { ImageField } from './fields/image-field'
import { FileField } from './fields/file-field'
import { DateField } from './fields/date-field'
import { TimeField } from './fields/time-field'
import { DateTimeField } from './fields/date-time-field'
import { IconField } from './fields/icon-field'
import { FontField } from './fields/font-field'
import { SpacingField } from './fields/spacing-field'
import { BorderField } from './fields/border-field'
import { ShadowField } from './fields/shadow-field'
import { GradientField } from './fields/gradient-field'
import { AnimationField } from './fields/animation-field'
import { LinkField } from './fields/link-field'
import { MediaField } from './fields/media-field'
import { RepeaterField } from './fields/repeater-field'
import { ObjectField } from './fields/object-field'
import { ConditionalField } from './fields/conditional-field'
import { TabsField } from './fields/tabs-field'
import { AccordionField } from './fields/accordion-field'

// Import plugin system integration
import { getCustomFieldsRegistry } from '@/lib/plugins/custom-fields/registry'

interface CustomFieldRendererProps {
  config: FieldConfig
  value: FieldValue
  onChange: (value: FieldValue) => void
  onValidate?: (isValid: boolean, message?: string) => void
  errors?: string[]
  disabled?: boolean
  className?: string
}

export function CustomFieldRenderer({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldRendererProps) {
  // Check if field should be hidden based on conditions
  if (config.hidden) {
    return null
  }

  // Common props for all field components
  const fieldProps: CustomFieldProps = {
    config,
    value,
    onChange,
    onValidate,
    errors,
    disabled: disabled || config.disabled,
    className
  }

  // First, try to get field component from plugin registry
  try {
    const registry = getCustomFieldsRegistry();
    const pluginField = registry.getField(config.type);

    if (pluginField) {
      const PluginFieldComponent = pluginField.component;
      return <PluginFieldComponent {...fieldProps} />
    }
  } catch (error) {
    console.warn(`Failed to load plugin field for type ${config.type}:`, error);
  }

  // Fallback to built-in field components
  switch (config.type) {
    case 'text':
      return <TextField {...fieldProps} />

    case 'number':
      return <NumberField {...fieldProps} />

    case 'boolean':
      return <BooleanField {...fieldProps} />

    case 'select':
      return <SelectField {...fieldProps} />

    case 'multi-select':
      return <MultiSelectField {...fieldProps} />

    case 'color':
      return <ColorField {...fieldProps} />

    case 'range':
      return <RangeField {...fieldProps} />

    case 'textarea':
      return <TextareaField {...fieldProps} />

    case 'richtext':
      return <RichTextField {...fieldProps} />

    case 'code':
      return <CodeField {...fieldProps} />

    case 'image':
      return <ImageField {...fieldProps} />

    case 'file':
      return <FileField {...fieldProps} />

    case 'date':
      return <DateField {...fieldProps} />

    case 'time':
      return <TimeField {...fieldProps} />

    case 'datetime':
      return <DateTimeField {...fieldProps} />

    case 'icon':
      return <IconField {...fieldProps} />

    case 'font':
      return <FontField {...fieldProps} />

    case 'spacing':
      return <SpacingField {...fieldProps} />

    case 'border':
      return <BorderField {...fieldProps} />

    case 'shadow':
      return <ShadowField {...fieldProps} />

    case 'gradient':
      return <GradientField {...fieldProps} />

    case 'animation':
      return <AnimationField {...fieldProps} />

    case 'link':
      return <LinkField {...fieldProps} />

    case 'media':
      return <MediaField {...fieldProps} />

    case 'repeater':
      return <RepeaterField {...fieldProps} />

    case 'object':
      return <ObjectField {...fieldProps} />

    case 'conditional':
      return <ConditionalField {...fieldProps} />

    case 'tabs':
      return <TabsField {...fieldProps} />

    case 'accordion':
      return <AccordionField {...fieldProps} />

    default:
      // Fallback to text field for unknown types
      console.warn(`Unknown field type: ${config.type}. Falling back to text field.`)
      return <TextField {...fieldProps} />
  }
}

// Helper function to render multiple fields
export function renderFields(
  configs: FieldConfig[],
  values: Record<string, FieldValue>,
  onChange: (fieldId: string, value: FieldValue) => void,
  options?: {
    onValidate?: (fieldId: string, isValid: boolean, message?: string) => void
    errors?: Record<string, string[]>
    disabled?: boolean
    className?: string
  }
) {
  return configs.map((config) => (
    <CustomFieldRenderer
      key={config.id}
      config={config}
      value={values[config.id]}
      onChange={(value) => onChange(config.id, value)}
      onValidate={options?.onValidate ? (isValid, message) => options.onValidate!(config.id, isValid, message) : undefined}
      errors={options?.errors?.[config.id]}
      disabled={options?.disabled}
      className={options?.className}
    />
  ))
}

// Helper function to check field visibility based on conditions
export function isFieldVisible(
  config: FieldConfig,
  values: Record<string, FieldValue>
): boolean {
  if (config.hidden) {
    return false
  }

  if (!config.showWhen) {
    return true
  }

  const { field, operator, value: conditionValue } = config.showWhen
  const fieldValue = values[field]

  switch (operator) {
    case 'equals':
      return fieldValue === conditionValue
    
    case 'not-equals':
      return fieldValue !== conditionValue
    
    case 'contains':
      if (Array.isArray(fieldValue)) {
        return fieldValue.includes(conditionValue)
      }
      if (typeof fieldValue === 'string') {
        return fieldValue.includes(String(conditionValue))
      }
      return false
    
    case 'not-contains':
      if (Array.isArray(fieldValue)) {
        return !fieldValue.includes(conditionValue)
      }
      if (typeof fieldValue === 'string') {
        return !fieldValue.includes(String(conditionValue))
      }
      return true
    
    case 'greater':
      return Number(fieldValue) > Number(conditionValue)
    
    case 'less':
      return Number(fieldValue) < Number(conditionValue)
    
    default:
      return true
  }
}

// Helper function to filter visible fields
export function getVisibleFields(
  configs: FieldConfig[],
  values: Record<string, FieldValue>
): FieldConfig[] {
  return configs.filter(config => isFieldVisible(config, values))
}
