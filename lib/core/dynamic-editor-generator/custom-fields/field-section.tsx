'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'

import { FieldSectionConfig, FieldGroupConfig, FieldConfig, FieldValue } from './types'
import { FieldGroup } from './field-group'

interface FieldSectionProps {
  config: FieldSectionConfig
  groups: FieldGroupConfig[]
  fields: FieldConfig[]
  values: Record<string, FieldValue>
  onChange: (fieldId: string, value: FieldValue) => void
  onValidate?: (fieldId: string, isValid: boolean, message?: string) => void
  errors?: Record<string, string[]>
  disabled?: boolean
  className?: string
}

export function FieldSection({
  config,
  groups,
  fields,
  values,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: FieldSectionProps) {
  // Safety checks
  if (!config) {
    console.warn('FieldSection: config is undefined')
    return null
  }

  if (!groups || !Array.isArray(groups)) {
    console.warn('FieldSection: groups is not an array')
    return null
  }

  if (!config.groups || !Array.isArray(config.groups)) {
    console.warn('FieldSection: config.groups is not an array')
    return null
  }

  const [isOpen, setIsOpen] = useState(!(config.collapsed ?? false))

  // Filter groups that belong to this section
  const sectionGroups = groups.filter(group =>
    group && group.id && config.groups.includes(group.id)
  )

  if (sectionGroups.length === 0) {
    return null
  }

  const content = (
    <div className="space-y-4">
      {sectionGroups.map((group, index) => (
        <React.Fragment key={group.id}>
          <FieldGroup
            config={group}
            fields={fields}
            values={values}
            onChange={onChange}
            onValidate={onValidate}
            errors={errors}
            disabled={disabled}
          />
          {index < sectionGroups.length - 1 && <Separator />}
        </React.Fragment>
      ))}
    </div>
  )

  if (!(config.collapsible ?? true)) {
    return (
      <div className={cn('space-y-4', className, config.className)}>
        <div className="flex items-center gap-2 px-1">
          {config.icon}
          <div>
            <h3 className="text-sm font-semibold">{config.label || 'Section'}</h3>
            {config.description && (
              <p className="text-xs text-muted-foreground">{config.description}</p>
            )}
          </div>
        </div>
        <Separator />
        {content}
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className, config.className)}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-start p-1 h-auto hover:bg-muted/50"
          >
            <div className="flex items-center gap-2 w-full">
              {isOpen ? (
                <ChevronDown className="h-3 w-3 shrink-0" />
              ) : (
                <ChevronRight className="h-3 w-3 shrink-0" />
              )}
              {config.icon}
              <div className="text-left">
                <h3 className="text-sm font-semibold">{config.label || 'Section'}</h3>
                {config.description && (
                  <p className="text-xs text-muted-foreground">{config.description}</p>
                )}
              </div>
            </div>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="pl-5 pt-2">
            <Separator className="mb-4" />
            {content}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}

// Helper component for rendering multiple field sections
interface FieldSectionsRendererProps {
  sections: FieldSectionConfig[]
  groups: FieldGroupConfig[]
  fields: FieldConfig[]
  values: Record<string, FieldValue>
  onChange: (fieldId: string, value: FieldValue) => void
  onValidate?: (fieldId: string, isValid: boolean, message?: string) => void
  errors?: Record<string, string[]>
  disabled?: boolean
  className?: string
}

export function FieldSectionsRenderer({
  sections,
  groups,
  fields,
  values,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: FieldSectionsRendererProps) {
  return (
    <div className={cn('space-y-6', className)}>
      {sections.map((section, index) => (
        <React.Fragment key={section.id}>
          <FieldSection
            config={section}
            groups={groups}
            fields={fields}
            values={values}
            onChange={onChange}
            onValidate={onValidate}
            errors={errors}
            disabled={disabled}
          />
          {index < sections.length - 1 && <Separator className="my-6" />}
        </React.Fragment>
      ))}
    </div>
  )
}

// Helper function to create a field section
export function createFieldSection(
  id: string,
  label: string,
  groupIds: string[],
  options?: {
    description?: string
    icon?: React.ReactNode
    collapsible?: boolean
    collapsed?: boolean
    className?: string
  }
): FieldSectionConfig {
  return {
    id,
    label,
    description: options?.description,
    icon: options?.icon,
    groups: groupIds,
    collapsible: options?.collapsible ?? true,
    collapsed: options?.collapsed ?? false,
    className: options?.className
  }
}

// Predefined field sections for common use cases
export const commonFieldSections = {
  // Content section
  content: createFieldSection(
    'content',
    'Content',
    ['text', 'media', 'links'],
    {
      description: 'Content and media settings',
      collapsible: true
    }
  ),

  // Design section
  design: createFieldSection(
    'design',
    'Design',
    ['typography', 'colors', 'spacing', 'effects'],
    {
      description: 'Visual design and styling',
      collapsible: true
    }
  ),

  // Layout section
  layout: createFieldSection(
    'layout',
    'Layout',
    ['layout', 'responsive'],
    {
      description: 'Layout and responsive settings',
      collapsible: true
    }
  ),

  // Advanced section
  advanced: createFieldSection(
    'advanced',
    'Advanced',
    ['animation', 'accessibility', 'seo', 'custom'],
    {
      description: 'Advanced configuration options',
      collapsible: true,
      collapsed: true
    }
  )
}
