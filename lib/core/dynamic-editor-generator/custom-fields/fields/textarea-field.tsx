'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { RotateCcw, AlertTriangle, Copy, FileText } from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

export function TextareaField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState(value as string || '')
  const [validationError, setValidationError] = useState<string | null>(null)

  useEffect(() => {
    setLocalValue(value as string || '')
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    setLocalValue(newValue)
    onChange(newValue)
  }

  const handleReset = () => {
    const defaultValue = config.defaultValue as string || ''
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <FileText className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        
        <div className="flex items-center gap-1">
          {localValue && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigator.clipboard.writeText(localValue)}
              className="h-5 w-5 p-0"
              title="Copy text"
            >
              <Copy className="h-3 w-3" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      <div className="relative">
        <Textarea
          id={config.id}
          value={localValue}
          onChange={handleChange}
          placeholder={config.placeholder}
          disabled={disabled || config.disabled}
          rows={config.rows || 4}
          className={cn(
            'text-xs resize-none',
            hasError && 'border-destructive focus-visible:ring-destructive',
            config.className
          )}
          maxLength={config.validation?.maxLength}
        />
        
        {config.validation?.maxLength && (
          <div className="absolute bottom-2 right-2">
            <Badge
              variant={localValue.length > config.validation.maxLength * 0.8 ? 'destructive' : 'secondary'}
              className="text-xs h-4 px-1"
            >
              {localValue.length}/{config.validation.maxLength}
            </Badge>
          </div>
        )}
      </div>

      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
