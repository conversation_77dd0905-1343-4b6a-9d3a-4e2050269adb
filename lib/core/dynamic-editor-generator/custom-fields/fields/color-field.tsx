'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Slider } from '@/components/ui/slider'
import { 
  <PERSON>lette, 
  Pipette, 
  RotateCcw, 
  Copy, 
  AlertTriangle,
  Eye,
  Gradient
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

export function ColorField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState(value as string || '#000000')
  const [isOpen, setIsOpen] = useState(false)
  const [format, setFormat] = useState(config.format || 'hex')
  const [hsla, setHsla] = useState({ h: 0, s: 0, l: 0, a: 1 })
  const [validationError, setValidationError] = useState<string | null>(null)

  // Update local value when prop value changes
  useEffect(() => {
    const colorValue = value as string || '#000000'
    setLocalValue(colorValue)
    setHsla(hexToHsla(colorValue))
  }, [value])

  // Validate field on value change
  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const handleColorChange = (newColor: string) => {
    setLocalValue(newColor)
    onChange(newColor)
    setHsla(hexToHsla(newColor))
  }

  const handleHslaChange = (newHsla: typeof hsla) => {
    setHsla(newHsla)
    const hexColor = hslaToHex(newHsla)
    setLocalValue(hexColor)
    onChange(hexColor)
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(localValue)
    } catch (error) {
      console.error('Failed to copy color:', error)
    }
  }

  const handleReset = () => {
    const defaultValue = config.defaultValue as string || '#000000'
    handleColorChange(defaultValue)
  }

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])

  // Predefined color presets
  const presets = config.presets || [
    '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff',
    '#ffff00', '#ff00ff', '#00ffff', '#808080', '#ffa500',
    '#800080', '#008000', '#000080', '#800000', '#008080'
  ]

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium">
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>
        
        {/* Field actions */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-5 w-5 p-0"
            title="Copy color value"
          >
            <Copy className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Color input */}
      <div className="flex items-center gap-2">
        {/* Color preview and picker */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-12 h-8 p-0 border-2',
                hasError && 'border-destructive'
              )}
              style={{ backgroundColor: localValue }}
              disabled={disabled || config.disabled}
            >
              <Palette className="h-3 w-3 text-white mix-blend-difference" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-4" align="start">
            <Tabs value={format} onValueChange={setFormat} className="w-full">
              <TabsList className="grid w-full grid-cols-3 mb-4">
                <TabsTrigger value="hex" className="text-xs">Hex</TabsTrigger>
                <TabsTrigger value="rgb" className="text-xs">RGB</TabsTrigger>
                <TabsTrigger value="hsl" className="text-xs">HSL</TabsTrigger>
              </TabsList>

              {/* Color preview */}
              <div 
                className="w-full h-16 rounded-lg border mb-4"
                style={{ backgroundColor: localValue }}
              />

              <TabsContent value="hex" className="space-y-4">
                <Input
                  type="color"
                  value={localValue}
                  onChange={(e) => handleColorChange(e.target.value)}
                  className="w-full h-8"
                />
              </TabsContent>

              <TabsContent value="rgb" className="space-y-4">
                <div className="space-y-2">
                  <div>
                    <Label className="text-xs">Red</Label>
                    <Slider
                      value={[Math.round(hsla.h * 255 / 360)]}
                      onValueChange={([r]) => {
                        const newHsla = { ...hsla, h: (r * 360) / 255 }
                        handleHslaChange(newHsla)
                      }}
                      max={255}
                      step={1}
                      className="mt-1"
                    />
                  </div>
                  {/* Similar for Green and Blue */}
                </div>
              </TabsContent>

              <TabsContent value="hsl" className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <Label className="text-xs">Hue: {Math.round(hsla.h)}°</Label>
                    <Slider
                      value={[hsla.h]}
                      onValueChange={([h]) => handleHslaChange({ ...hsla, h })}
                      max={360}
                      step={1}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Saturation: {Math.round(hsla.s * 100)}%</Label>
                    <Slider
                      value={[hsla.s * 100]}
                      onValueChange={([s]) => handleHslaChange({ ...hsla, s: s / 100 })}
                      max={100}
                      step={1}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Lightness: {Math.round(hsla.l * 100)}%</Label>
                    <Slider
                      value={[hsla.l * 100]}
                      onValueChange={([l]) => handleHslaChange({ ...hsla, l: l / 100 })}
                      max={100}
                      step={1}
                      className="mt-1"
                    />
                  </div>
                  {config.alpha && (
                    <div>
                      <Label className="text-xs">Alpha: {Math.round(hsla.a * 100)}%</Label>
                      <Slider
                        value={[hsla.a * 100]}
                        onValueChange={([a]) => handleHslaChange({ ...hsla, a: a / 100 })}
                        max={100}
                        step={1}
                        className="mt-1"
                      />
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Color presets */}
              <div className="space-y-2">
                <Label className="text-xs">Presets</Label>
                <div className="grid grid-cols-5 gap-2">
                  {presets.map((preset) => (
                    <Button
                      key={preset}
                      variant="outline"
                      className="w-8 h-8 p-0 border-2"
                      style={{ backgroundColor: preset }}
                      onClick={() => handleColorChange(preset)}
                      title={preset}
                    />
                  ))}
                </div>
              </div>
            </Tabs>
          </PopoverContent>
        </Popover>

        {/* Text input */}
        <Input
          type="text"
          value={localValue}
          onChange={(e) => handleColorChange(e.target.value)}
          placeholder={config.placeholder || '#000000'}
          disabled={disabled || config.disabled}
          className={cn(
            'text-xs flex-1',
            hasError && 'border-destructive focus-visible:ring-destructive',
            config.className
          )}
        />

        {/* Eye dropper (if supported) */}
        {typeof window !== 'undefined' && 'EyeDropper' in window && (
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              try {
                // @ts-ignore - EyeDropper is not in TypeScript types yet
                const eyeDropper = new EyeDropper()
                const result = await eyeDropper.open()
                handleColorChange(result.sRGBHex)
              } catch (error) {
                console.error('Eye dropper failed:', error)
              }
            }}
            className="h-8 w-8 p-0"
            title="Pick color from screen"
          >
            <Pipette className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}

// Helper functions for color conversion
function hexToHsla(hex: string): { h: number; s: number; l: number; a: number } {
  // Remove # if present
  hex = hex.replace('#', '')
  
  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16) / 255
  const g = parseInt(hex.substr(2, 2), 16) / 255
  const b = parseInt(hex.substr(4, 2), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0
  let s = 0
  const l = (max + min) / 2

  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }

  return {
    h: h * 360,
    s,
    l,
    a: 1
  }
}

function hslaToHex({ h, s, l, a }: { h: number; s: number; l: number; a: number }): string {
  h = h / 360
  
  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1
    if (t > 1) t -= 1
    if (t < 1/6) return p + (q - p) * 6 * t
    if (t < 1/2) return q
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
    return p
  }

  let r, g, b

  if (s === 0) {
    r = g = b = l // achromatic
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s
    const p = 2 * l - q
    r = hue2rgb(p, q, h + 1/3)
    g = hue2rgb(p, q, h)
    b = hue2rgb(p, q, h - 1/3)
  }

  const toHex = (c: number) => {
    const hex = Math.round(c * 255).toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}
