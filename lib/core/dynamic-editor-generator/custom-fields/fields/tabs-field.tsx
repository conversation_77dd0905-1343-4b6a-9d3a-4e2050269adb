'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  FolderOpen as TabsIcon,
  RotateCcw,
  AlertTriangle,
  Plus,
  Trash2,
  GripVertical,
  Copy
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface TabItem {
  id: string
  label: string
  icon?: string
  fields: any[]
  disabled?: boolean
}

interface TabsValue {
  tabs: TabItem[]
  activeTab: string
  orientation: 'horizontal' | 'vertical'
  variant: 'default' | 'pills' | 'underline'
}

export function TabsField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<TabsValue>({
    ...(value as TabsValue || {}),
    tabs: (value as TabsValue)?.tabs || [],
    activeTab: (value as TabsValue)?.activeTab || '',
    orientation: (value as TabsValue)?.orientation || 'horizontal',
    variant: (value as TabsValue)?.variant || 'default'
  })
  const [validationError, setValidationError] = useState<string | null>(null)
  const [draggedTab, setDraggedTab] = useState<string | null>(null)

  useEffect(() => {
    setLocalValue({
      ...(value as TabsValue || {}),
      tabs: (value as TabsValue)?.tabs || [],
      activeTab: (value as TabsValue)?.activeTab || '',
      orientation: (value as TabsValue)?.orientation || 'horizontal',
      variant: (value as TabsValue)?.variant || 'default'
    })
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateTabs = (updates: Partial<TabsValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: TabsValue = {
      ...(config.defaultValue as TabsValue || {}),
      tabs: (config.defaultValue as TabsValue)?.tabs || [],
      activeTab: (config.defaultValue as TabsValue)?.activeTab || '',
      orientation: (config.defaultValue as TabsValue)?.orientation || 'horizontal',
      variant: (config.defaultValue as TabsValue)?.variant || 'default'
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const addTab = () => {
    const newTab: TabItem = {
      id: `tab_${Date.now()}`,
      label: `Tab ${localValue.tabs.length + 1}`,
      fields: [],
      disabled: false
    }
    const newTabs = [...localValue.tabs, newTab]
    updateTabs({
      tabs: newTabs,
      activeTab: localValue.activeTab || newTab.id
    })
  }

  const removeTab = (tabId: string) => {
    const newTabs = localValue.tabs.filter(tab => tab.id !== tabId)
    const newActiveTab = localValue.activeTab === tabId
      ? (newTabs.length > 0 ? newTabs[0].id : '')
      : localValue.activeTab

    updateTabs({
      tabs: newTabs,
      activeTab: newActiveTab
    })
  }

  const updateTab = (tabId: string, updates: Partial<TabItem>) => {
    const newTabs = localValue.tabs.map(tab =>
      tab.id === tabId ? { ...tab, ...updates } : tab
    )
    updateTabs({ tabs: newTabs })
  }

  const duplicateTab = (tabId: string) => {
    const tab = localValue.tabs.find(t => t.id === tabId)
    if (!tab) return

    const newTab: TabItem = {
      ...tab,
      id: `tab_${Date.now()}`,
      label: `${tab.label} Copy`,
      fields: [...tab.fields]
    }

    const tabIndex = localValue.tabs.findIndex(t => t.id === tabId)
    const newTabs = [...localValue.tabs]
    newTabs.splice(tabIndex + 1, 0, newTab)

    updateTabs({ tabs: newTabs })
  }

  const moveTab = (fromIndex: number, toIndex: number) => {
    const newTabs = [...localValue.tabs]
    const [movedTab] = newTabs.splice(fromIndex, 1)
    newTabs.splice(toIndex, 0, movedTab)
    updateTabs({ tabs: newTabs })
  }

  const addFieldToTab = (tabId: string) => {
    const newField = {
      id: `field_${Date.now()}`,
      type: 'text',
      label: 'New Field',
      value: ''
    }

    updateTab(tabId, {
      fields: [...(localValue.tabs.find(t => t.id === tabId)?.fields || []), newField]
    })
  }

  const removeFieldFromTab = (tabId: string, fieldIndex: number) => {
    const tab = localValue.tabs.find(t => t.id === tabId)
    if (!tab) return

    const newFields = tab.fields.filter((_, i) => i !== fieldIndex)
    updateTab(tabId, { fields: newFields })
  }

  const updateFieldInTab = (tabId: string, fieldIndex: number, updates: any) => {
    const tab = localValue.tabs.find(t => t.id === tabId)
    if (!tab) return

    const newFields = [...tab.fields]
    newFields[fieldIndex] = { ...newFields[fieldIndex], ...updates }
    updateTab(tabId, { fields: newFields })
  }

  const handleDragStart = (e: React.DragEvent, tabId: string) => {
    setDraggedTab(tabId)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, dropTabId: string) => {
    e.preventDefault()
    if (!draggedTab || draggedTab === dropTabId) return

    const fromIndex = localValue.tabs.findIndex(t => t.id === draggedTab)
    const toIndex = localValue.tabs.findIndex(t => t.id === dropTabId)

    if (fromIndex !== -1 && toIndex !== -1) {
      moveTab(fromIndex, toIndex)
    }

    setDraggedTab(null)
  }

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])
  const tabCount = localValue.tabs.length

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <TabsIcon className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs h-4 px-1">
            {tabCount} tabs
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Tabs configuration */}
      <div className="space-y-4">
        {/* Settings */}
        <div className="border rounded-lg p-3 bg-muted/20">
          <div className="flex items-center justify-between mb-3">
            <Label className="text-xs font-medium">Tab Settings</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={addTab}
              disabled={disabled}
              className="text-xs h-6"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Tab
            </Button>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label className="text-xs">Orientation</Label>
              <Select
                value={localValue.orientation}
                onValueChange={(value) => updateTabs({ orientation: value as any })}
                disabled={disabled}
              >
                <SelectTrigger className="text-xs h-6">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="horizontal" className="text-xs">Horizontal</SelectItem>
                  <SelectItem value="vertical" className="text-xs">Vertical</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-xs">Variant</Label>
              <Select
                value={localValue.variant}
                onValueChange={(value) => updateTabs({ variant: value as any })}
                disabled={disabled}
              >
                <SelectTrigger className="text-xs h-6">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default" className="text-xs">Default</SelectItem>
                  <SelectItem value="pills" className="text-xs">Pills</SelectItem>
                  <SelectItem value="underline" className="text-xs">Underline</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Tab management */}
        <div className="border rounded-lg p-3 bg-background">
          <Label className="text-xs font-medium mb-3 block">Tab Management</Label>

          {localValue.tabs.length === 0 ? (
            <div className="text-center py-6 border-2 border-dashed rounded-lg">
              <TabsIcon className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm font-medium mb-1">No tabs created</p>
              <p className="text-xs text-muted-foreground mb-3">
                Create tabs to organize your fields
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={addTab}
                disabled={disabled}
                className="text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Create First Tab
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              {localValue.tabs.map((tab) => (
                <div
                  key={tab.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, tab.id)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, tab.id)}
                  className={cn(
                    'border rounded-lg p-3 bg-background transition-all',
                    draggedTab === tab.id && 'opacity-50',
                    'hover:shadow-sm'
                  )}
                >
                  {/* Tab header */}
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2 flex-1">
                      <GripVertical className="h-3 w-3 text-muted-foreground cursor-grab" />

                      <Input
                        value={tab.label}
                        onChange={(e) => updateTab(tab.id, { label: e.target.value })}
                        placeholder="Tab label"
                        className="text-xs h-6 flex-1"
                        disabled={disabled}
                      />

                      <Badge variant={tab.disabled ? 'secondary' : 'default'} className="text-xs h-4 px-1">
                        {tab.fields.length} fields
                      </Badge>
                    </div>

                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => duplicateTab(tab.id)}
                        disabled={disabled}
                        className="h-6 w-6 p-0"
                        title="Duplicate tab"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeTab(tab.id)}
                        disabled={disabled || localValue.tabs.length <= 1}
                        className="h-6 w-6 p-0"
                        title="Remove tab"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Tab options */}
                  <div className="flex items-center gap-2 mb-3">
                    <label className="flex items-center space-x-2 text-xs">
                      <input
                        type="checkbox"
                        checked={tab.disabled || false}
                        onChange={(e) => updateTab(tab.id, { disabled: e.target.checked })}
                        disabled={disabled}
                        className="rounded"
                      />
                      <span>Disabled</span>
                    </label>
                  </div>

                  {/* Tab fields */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs">Fields in this tab</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addFieldToTab(tab.id)}
                        disabled={disabled}
                        className="text-xs h-5"
                      >
                        <Plus className="h-2 w-2 mr-1" />
                        Add Field
                      </Button>
                    </div>

                    {tab.fields.length === 0 ? (
                      <div className="text-center py-3 border border-dashed rounded">
                        <p className="text-xs text-muted-foreground">No fields in this tab</p>
                      </div>
                    ) : (
                      <div className="space-y-1">
                        {tab.fields.map((field, fieldIndex) => (
                          <div key={field.id || fieldIndex} className="flex items-center gap-2 p-2 border rounded bg-muted/20">
                            <Input
                              value={field.label || ''}
                              onChange={(e) => updateFieldInTab(tab.id, fieldIndex, { label: e.target.value })}
                              placeholder="Field label"
                              className="text-xs h-5 flex-1"
                              disabled={disabled}
                            />

                            <Select
                              value={field.type || 'text'}
                              onValueChange={(value) => updateFieldInTab(tab.id, fieldIndex, { type: value })}
                              disabled={disabled}
                            >
                              <SelectTrigger className="w-20 h-5 text-xs">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="text" className="text-xs">Text</SelectItem>
                                <SelectItem value="number" className="text-xs">Number</SelectItem>
                                <SelectItem value="email" className="text-xs">Email</SelectItem>
                                <SelectItem value="select" className="text-xs">Select</SelectItem>
                                <SelectItem value="checkbox" className="text-xs">Checkbox</SelectItem>
                              </SelectContent>
                            </Select>

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFieldFromTab(tab.id, fieldIndex)}
                              disabled={disabled}
                              className="h-5 w-5 p-0"
                              title="Remove field"
                            >
                              <Trash2 className="h-2 w-2" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Preview */}
        {localValue.tabs.length > 0 && (
          <div className="border rounded-lg p-3 bg-background">
            <Label className="text-xs font-medium mb-3 block">Preview</Label>

            <Tabs
              value={localValue.activeTab}
              onValueChange={(value) => updateTabs({ activeTab: value })}
              orientation={localValue.orientation}
              className="w-full"
            >
              <TabsList className={cn(
                localValue.orientation === 'vertical' ? 'flex-col h-auto' : 'grid',
                localValue.orientation === 'horizontal' && `grid-cols-${Math.min(localValue.tabs.length, 4)}`
              )}>
                {localValue.tabs.map((tab) => (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    disabled={tab.disabled}
                    className="text-xs"
                  >
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>

              {localValue.tabs.map((tab) => (
                <TabsContent key={tab.id} value={tab.id} className="space-y-2">
                  {tab.fields.length === 0 ? (
                    <div className="text-center py-4 text-xs text-muted-foreground">
                      No fields in this tab
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {tab.fields.map((field, index) => (
                        <div key={field.id || index} className="space-y-1">
                          <Label className="text-xs">{field.label || 'Field Label'}</Label>
                          {field.type === 'text' && (
                            <Input placeholder="Text input" className="text-xs h-6" disabled />
                          )}
                          {field.type === 'number' && (
                            <Input type="number" placeholder="Number input" className="text-xs h-6" disabled />
                          )}
                          {field.type === 'email' && (
                            <Input type="email" placeholder="Email input" className="text-xs h-6" disabled />
                          )}
                          {field.type === 'select' && (
                            <Select disabled>
                              <SelectTrigger className="h-6 text-xs">
                                <SelectValue placeholder="Select option" />
                              </SelectTrigger>
                            </Select>
                          )}
                          {field.type === 'checkbox' && (
                            <div className="flex items-center space-x-2">
                              <input type="checkbox" disabled className="rounded" />
                              <span className="text-xs">Checkbox option</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </TabsContent>
              ))}
            </Tabs>
          </div>
        )}
      </div>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
