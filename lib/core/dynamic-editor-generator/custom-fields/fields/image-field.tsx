'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Image,
  RotateCcw,
  AlertTriangle,
  Upload,
  X,
  Eye,
  Download,
  Link,
  Settings
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface ImageValue {
  url: string
  alt: string
  width?: number
  height?: number
  size?: number
  format?: string
  name?: string
}

export function ImageField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<ImageValue>({
    url: '',
    alt: '',
    ...(value as ImageValue || {}),
    url: (value as ImageValue)?.url || (value as string) || ''
  })
  const [isDragging, setIsDragging] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const dropZoneRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    setLocalValue({
      url: '',
      alt: '',
      ...(value as ImageValue || {}),
      url: (value as ImageValue)?.url || (value as string) || ''
    })
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue.url)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateImage = (updates: Partial<ImageValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: ImageValue = {
      url: '',
      alt: '',
      ...(config.defaultValue as ImageValue || {})
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const handleFileSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      setValidationError('Please select a valid image file')
      return
    }

    setIsLoading(true)
    const reader = new FileReader()

    reader.onload = (e) => {
      const url = e.target?.result as string
      const img = new window.Image()

      img.onload = () => {
        updateImage({
          url,
          alt: localValue.alt || file.name.replace(/\.[^/.]+$/, ''),
          width: img.width,
          height: img.height,
          size: file.size,
          format: file.type,
          name: file.name
        })
        setIsLoading(false)
      }

      img.onerror = () => {
        setValidationError('Failed to load image')
        setIsLoading(false)
      }

      img.src = url
    }

    reader.onerror = () => {
      setValidationError('Failed to read file')
      setIsLoading(false)
    }

    reader.readAsDataURL(file)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    if (!dropZoneRef.current?.contains(e.relatedTarget as Node)) {
      setIsDragging(false)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])
  const hasImage = localValue.url && localValue.url.length > 0

  // Generate image preview with SVG placeholder
  const generateImagePreview = () => {
    if (hasImage) {
      return (
        <div className="relative group">
          <img
            src={localValue.url}
            alt={localValue.alt || 'Preview'}
            className="w-full h-32 object-cover rounded border"
            onError={() => setValidationError('Failed to load image')}
          />

          {/* Image overlay with actions */}
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center gap-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={() => window.open(localValue.url, '_blank')}
              className="h-6 px-2 text-xs"
            >
              <Eye className="h-3 w-3 mr-1" />
              View
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => updateImage({ url: '', alt: '', width: undefined, height: undefined })}
              className="h-6 px-2 text-xs"
            >
              <X className="h-3 w-3 mr-1" />
              Remove
            </Button>
          </div>
        </div>
      )
    }

    return (
      <svg width="100%" height="128" className="border rounded bg-muted/20">
        <defs>
          <pattern id={`image-pattern-${config.id}`} width="20" height="20" patternUnits="userSpaceOnUse">
            <rect width="20" height="20" fill="#f8fafc" />
            <circle cx="10" cy="10" r="1" fill="#e2e8f0" />
          </pattern>
        </defs>

        <rect width="100%" height="100%" fill={`url(#image-pattern-${config.id})`} />

        {/* Image placeholder icon */}
        <g transform="translate(50%, 50%)">
          <rect x="-20" y="-15" width="40" height="30" fill="#e2e8f0" rx="4" />
          <circle cx="-8" cy="-5" r="3" fill="#94a3b8" />
          <polygon points="-15,5 -5,-5 5,5 15,15 -15,15" fill="#94a3b8" />
        </g>

        <text x="50%" y="75%" textAnchor="middle" className="text-xs fill-muted-foreground">
          No image selected
        </text>
      </svg>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Image className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <div className="flex items-center gap-1">
          {hasImage && localValue.size && (
            <Badge variant="outline" className="text-xs h-4 px-1">
              {formatFileSize(localValue.size)}
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Image preview */}
      <div className="space-y-2">
        {generateImagePreview()}

        {/* Image info */}
        {hasImage && (
          <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
            {localValue.width && localValue.height && (
              <div>Dimensions: {localValue.width} × {localValue.height}</div>
            )}
            {localValue.format && (
              <div>Format: {localValue.format.split('/')[1]?.toUpperCase()}</div>
            )}
          </div>
        )}
      </div>

      {/* Image controls */}
      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="upload" className="text-xs">
            <Upload className="h-3 w-3 mr-1" />
            Upload
          </TabsTrigger>
          <TabsTrigger value="url" className="text-xs">
            <Link className="h-3 w-3 mr-1" />
            URL
          </TabsTrigger>
          <TabsTrigger value="settings" className="text-xs">
            <Settings className="h-3 w-3 mr-1" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-3">
          {/* File upload area */}
          <div
            ref={dropZoneRef}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            className={cn(
              'border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer',
              isDragging ? 'border-primary bg-primary/5' : 'border-muted-foreground/25',
              hasError && 'border-destructive',
              disabled && 'opacity-50 cursor-not-allowed'
            )}
            onClick={() => !disabled && fileInputRef.current?.click()}
          >
            <Upload className={cn(
              'h-8 w-8 mx-auto mb-2',
              isDragging ? 'text-primary' : 'text-muted-foreground'
            )} />
            <p className="text-sm font-medium mb-1">
              {isDragging ? 'Drop image here' : 'Click to upload or drag and drop'}
            </p>
            <p className="text-xs text-muted-foreground">
              PNG, JPG, GIF up to 10MB
            </p>

            {isLoading && (
              <div className="mt-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mx-auto"></div>
              </div>
            )}
          </div>

          <Input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => {
              const file = e.target.files?.[0]
              if (file) handleFileSelect(file)
            }}
            className="hidden"
            disabled={disabled}
          />
        </TabsContent>

        <TabsContent value="url" className="space-y-3">
          {/* URL input */}
          <div className="space-y-2">
            <Label className="text-xs">Image URL</Label>
            <Input
              type="url"
              value={localValue.url}
              onChange={(e) => updateImage({ url: e.target.value })}
              placeholder="https://example.com/image.jpg"
              className={cn(
                'text-xs',
                hasError && 'border-destructive'
              )}
              disabled={disabled}
            />
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-3">
          {/* Alt text */}
          <div className="space-y-2">
            <Label className="text-xs">Alt Text</Label>
            <Input
              type="text"
              value={localValue.alt}
              onChange={(e) => updateImage({ alt: e.target.value })}
              placeholder="Describe the image for accessibility"
              className="text-xs"
              disabled={disabled}
            />
          </div>

          {/* Image actions */}
          {hasImage && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(localValue.url, '_blank')}
                className="text-xs h-6"
                disabled={disabled}
              >
                <Eye className="h-3 w-3 mr-1" />
                Preview
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const a = document.createElement('a')
                  a.href = localValue.url
                  a.download = localValue.name || 'image'
                  a.click()
                }}
                className="text-xs h-6"
                disabled={disabled}
              >
                <Download className="h-3 w-3 mr-1" />
                Download
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
