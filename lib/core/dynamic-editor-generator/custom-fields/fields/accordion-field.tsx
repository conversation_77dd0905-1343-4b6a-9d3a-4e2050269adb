'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import {
  ChevronDown,
  RotateCcw,
  AlertTriangle,
  Plus,
  Trash2,
  GripVertical,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface AccordionItem {
  id: string
  title: string
  content: string
  disabled?: boolean
  defaultOpen?: boolean
  fields?: any[]
}

interface AccordionValue {
  items: AccordionItem[]
  type: 'single' | 'multiple'
  collapsible: boolean
  variant: 'default' | 'ghost' | 'outline'
  openItems: string[]
}

export function AccordionField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<AccordionValue>({
    ...(value as AccordionValue || {}),
    items: (value as AccordionValue)?.items || [],
    type: (value as AccordionValue)?.type || 'single',
    collapsible: (value as AccordionValue)?.collapsible ?? true,
    variant: (value as AccordionValue)?.variant || 'default',
    openItems: (value as AccordionValue)?.openItems || []
  })
  const [validationError, setValidationError] = useState<string | null>(null)
  const [draggedItem, setDraggedItem] = useState<string | null>(null)
  const [previewMode, setPreviewMode] = useState(false)

  useEffect(() => {
    setLocalValue({
      ...(value as AccordionValue || {}),
      items: (value as AccordionValue)?.items || [],
      type: (value as AccordionValue)?.type || 'single',
      collapsible: (value as AccordionValue)?.collapsible ?? true,
      variant: (value as AccordionValue)?.variant || 'default',
      openItems: (value as AccordionValue)?.openItems || []
    })
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  const updateAccordion = (updates: Partial<AccordionValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(updated)
  }

  const handleReset = () => {
    const defaultValue: AccordionValue = {
      ...(config.defaultValue as AccordionValue || {}),
      items: (config.defaultValue as AccordionValue)?.items || [],
      type: (config.defaultValue as AccordionValue)?.type || 'single',
      collapsible: (config.defaultValue as AccordionValue)?.collapsible ?? true,
      variant: (config.defaultValue as AccordionValue)?.variant || 'default',
      openItems: (config.defaultValue as AccordionValue)?.openItems || []
    }
    setLocalValue(defaultValue)
    onChange(defaultValue)
  }

  const addItem = () => {
    const newItem: AccordionItem = {
      id: `item_${Date.now()}`,
      title: `Accordion Item ${localValue.items.length + 1}`,
      content: 'Content for this accordion item...',
      disabled: false,
      defaultOpen: false,
      fields: []
    }
    updateAccordion({
      items: [...localValue.items, newItem]
    })
  }

  const removeItem = (itemId: string) => {
    const newItems = localValue.items.filter(item => item.id !== itemId)
    const newOpenItems = localValue.openItems.filter(id => id !== itemId)
    updateAccordion({
      items: newItems,
      openItems: newOpenItems
    })
  }

  const updateItem = (itemId: string, updates: Partial<AccordionItem>) => {
    const newItems = localValue.items.map(item =>
      item.id === itemId ? { ...item, ...updates } : item
    )
    updateAccordion({ items: newItems })
  }

  const duplicateItem = (itemId: string) => {
    const item = localValue.items.find(i => i.id === itemId)
    if (!item) return

    const newItem: AccordionItem = {
      ...item,
      id: `item_${Date.now()}`,
      title: `${item.title} Copy`,
      fields: [...(item.fields || [])]
    }

    const itemIndex = localValue.items.findIndex(i => i.id === itemId)
    const newItems = [...localValue.items]
    newItems.splice(itemIndex + 1, 0, newItem)

    updateAccordion({ items: newItems })
  }

  const moveItem = (fromIndex: number, toIndex: number) => {
    const newItems = [...localValue.items]
    const [movedItem] = newItems.splice(fromIndex, 1)
    newItems.splice(toIndex, 0, movedItem)
    updateAccordion({ items: newItems })
  }

  const addFieldToItem = (itemId: string) => {
    const newField = {
      id: `field_${Date.now()}`,
      type: 'text',
      label: 'New Field',
      value: ''
    }

    updateItem(itemId, {
      fields: [...(localValue.items.find(i => i.id === itemId)?.fields || []), newField]
    })
  }

  const removeFieldFromItem = (itemId: string, fieldIndex: number) => {
    const item = localValue.items.find(i => i.id === itemId)
    if (!item) return

    const newFields = item.fields?.filter((_, i) => i !== fieldIndex) || []
    updateItem(itemId, { fields: newFields })
  }

  const handleDragStart = (e: React.DragEvent, itemId: string) => {
    setDraggedItem(itemId)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, dropItemId: string) => {
    e.preventDefault()
    if (!draggedItem || draggedItem === dropItemId) return

    const fromIndex = localValue.items.findIndex(i => i.id === draggedItem)
    const toIndex = localValue.items.findIndex(i => i.id === dropItemId)

    if (fromIndex !== -1 && toIndex !== -1) {
      moveItem(fromIndex, toIndex)
    }

    setDraggedItem(null)
  }

  const hasError = validationError || (errors && errors.length > 0)
  const errorMessage = validationError || (errors && errors[0])
  const itemCount = localValue.items.length

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <ChevronDown className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs h-4 px-1">
            {itemCount} items
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setPreviewMode(!previewMode)}
            className="h-5 w-5 p-0"
            title={previewMode ? 'Exit preview' : 'Preview accordion'}
          >
            {previewMode ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Accordion configuration */}
      <div className="space-y-4">
        {/* Settings */}
        <div className="border rounded-lg p-3 bg-muted/20">
          <div className="flex items-center justify-between mb-3">
            <Label className="text-xs font-medium">Accordion Settings</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={addItem}
              disabled={disabled}
              className="text-xs h-6"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Item
            </Button>
          </div>

          <div className="grid grid-cols-3 gap-3">
            <div className="space-y-2">
              <Label className="text-xs">Type</Label>
              <Select
                value={localValue.type}
                onValueChange={(value) => updateAccordion({ type: value as any })}
                disabled={disabled}
              >
                <SelectTrigger className="text-xs h-6">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="single" className="text-xs">Single</SelectItem>
                  <SelectItem value="multiple" className="text-xs">Multiple</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-xs">Variant</Label>
              <Select
                value={localValue.variant}
                onValueChange={(value) => updateAccordion({ variant: value as any })}
                disabled={disabled}
              >
                <SelectTrigger className="text-xs h-6">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default" className="text-xs">Default</SelectItem>
                  <SelectItem value="ghost" className="text-xs">Ghost</SelectItem>
                  <SelectItem value="outline" className="text-xs">Outline</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-xs">Options</Label>
              <label className="flex items-center space-x-2 text-xs">
                <input
                  type="checkbox"
                  checked={localValue.collapsible}
                  onChange={(e) => updateAccordion({ collapsible: e.target.checked })}
                  disabled={disabled}
                  className="rounded"
                />
                <span>Collapsible</span>
              </label>
            </div>
          </div>
        </div>

        {/* Item management */}
        <div className="border rounded-lg p-3 bg-background">
          <Label className="text-xs font-medium mb-3 block">Accordion Items</Label>

          {localValue.items.length === 0 ? (
            <div className="text-center py-6 border-2 border-dashed rounded-lg">
              <ChevronDown className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm font-medium mb-1">No accordion items</p>
              <p className="text-xs text-muted-foreground mb-3">
                Create collapsible sections for your content
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={addItem}
                disabled={disabled}
                className="text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Create First Item
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              {localValue.items.map((item) => (
                <div
                  key={item.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, item.id)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, item.id)}
                  className={cn(
                    'border rounded-lg p-3 bg-background transition-all',
                    draggedItem === item.id && 'opacity-50',
                    'hover:shadow-sm'
                  )}
                >
                  {/* Item header */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2 flex-1">
                      <GripVertical className="h-3 w-3 text-muted-foreground cursor-grab" />

                      <Input
                        value={item.title}
                        onChange={(e) => updateItem(item.id, { title: e.target.value })}
                        placeholder="Accordion item title"
                        className="text-xs h-6 flex-1"
                        disabled={disabled}
                      />

                      <Badge variant={item.disabled ? 'secondary' : 'default'} className="text-xs h-4 px-1">
                        {item.fields?.length || 0} fields
                      </Badge>
                    </div>

                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => duplicateItem(item.id)}
                        disabled={disabled}
                        className="h-6 w-6 p-0"
                        title="Duplicate item"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(item.id)}
                        disabled={disabled}
                        className="h-6 w-6 p-0"
                        title="Remove item"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Item content */}
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-xs">Content</Label>
                      <Textarea
                        value={item.content}
                        onChange={(e) => updateItem(item.id, { content: e.target.value })}
                        placeholder="Accordion item content..."
                        className="text-xs min-h-[60px] resize-none"
                        disabled={disabled}
                      />
                    </div>

                    {/* Item options */}
                    <div className="flex items-center gap-4">
                      <label className="flex items-center space-x-2 text-xs">
                        <input
                          type="checkbox"
                          checked={item.disabled || false}
                          onChange={(e) => updateItem(item.id, { disabled: e.target.checked })}
                          disabled={disabled}
                          className="rounded"
                        />
                        <span>Disabled</span>
                      </label>
                      <label className="flex items-center space-x-2 text-xs">
                        <input
                          type="checkbox"
                          checked={item.defaultOpen || false}
                          onChange={(e) => updateItem(item.id, { defaultOpen: e.target.checked })}
                          disabled={disabled}
                          className="rounded"
                        />
                        <span>Default Open</span>
                      </label>
                    </div>

                    {/* Item fields */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Fields in this item</Label>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addFieldToItem(item.id)}
                          disabled={disabled}
                          className="text-xs h-5"
                        >
                          <Plus className="h-2 w-2 mr-1" />
                          Add Field
                        </Button>
                      </div>

                      {(item.fields?.length || 0) === 0 ? (
                        <div className="text-center py-3 border border-dashed rounded">
                          <p className="text-xs text-muted-foreground">No fields in this item</p>
                        </div>
                      ) : (
                        <div className="space-y-1">
                          {item.fields?.map((field, fieldIndex) => (
                            <div key={field.id || fieldIndex} className="flex items-center gap-2 p-2 border rounded bg-muted/20">
                              <Input
                                value={field.label || ''}
                                onChange={(e) => {
                                  const newFields = [...(item.fields || [])]
                                  newFields[fieldIndex] = { ...newFields[fieldIndex], label: e.target.value }
                                  updateItem(item.id, { fields: newFields })
                                }}
                                placeholder="Field label"
                                className="text-xs h-5 flex-1"
                                disabled={disabled}
                              />

                              <Select
                                value={field.type || 'text'}
                                onValueChange={(value) => {
                                  const newFields = [...(item.fields || [])]
                                  newFields[fieldIndex] = { ...newFields[fieldIndex], type: value }
                                  updateItem(item.id, { fields: newFields })
                                }}
                                disabled={disabled}
                              >
                                <SelectTrigger className="w-20 h-5 text-xs">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="text" className="text-xs">Text</SelectItem>
                                  <SelectItem value="number" className="text-xs">Number</SelectItem>
                                  <SelectItem value="email" className="text-xs">Email</SelectItem>
                                  <SelectItem value="select" className="text-xs">Select</SelectItem>
                                  <SelectItem value="checkbox" className="text-xs">Checkbox</SelectItem>
                                </SelectContent>
                              </Select>

                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFieldFromItem(item.id, fieldIndex)}
                                disabled={disabled}
                                className="h-5 w-5 p-0"
                                title="Remove field"
                              >
                                <Trash2 className="h-2 w-2" />
                              </Button>
                            </div>
                          )) || []}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Preview */}
        {previewMode && localValue.items.length > 0 && (
          <div className="border rounded-lg p-3 bg-background">
            <Label className="text-xs font-medium mb-3 block">Preview</Label>

            <Accordion
              type={localValue.type}
              collapsible={localValue.collapsible}
              className="w-full"
            >
              {localValue.items.map((item) => (
                <AccordionItem
                  key={item.id}
                  value={item.id}
                  disabled={item.disabled}
                >
                  <AccordionTrigger className="text-sm">
                    {item.title}
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3">
                      <p className="text-sm">{item.content}</p>

                      {item.fields && item.fields.length > 0 && (
                        <div className="space-y-2 pt-2 border-t">
                          {item.fields.map((field, index) => (
                            <div key={field.id || index} className="space-y-1">
                              <Label className="text-xs">{field.label || 'Field Label'}</Label>
                              {field.type === 'text' && (
                                <Input placeholder="Text input" className="text-xs h-6" disabled />
                              )}
                              {field.type === 'number' && (
                                <Input type="number" placeholder="Number input" className="text-xs h-6" disabled />
                              )}
                              {field.type === 'email' && (
                                <Input type="email" placeholder="Email input" className="text-xs h-6" disabled />
                              )}
                              {field.type === 'select' && (
                                <Select disabled>
                                  <SelectTrigger className="h-6 text-xs">
                                    <SelectValue placeholder="Select option" />
                                  </SelectTrigger>
                                </Select>
                              )}
                              {field.type === 'checkbox' && (
                                <div className="flex items-center space-x-2">
                                  <input type="checkbox" disabled className="rounded" />
                                  <span className="text-xs">Checkbox option</span>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        )}
      </div>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
