'use client'

import React from 'react'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { RotateCcw, Calendar } from 'lucide-react'
import { cn } from '@/lib/utils'
import { CustomFieldProps } from '../types'

export function DateField({ config, value, onChange, className }: CustomFieldProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between">
        <Label className="text-xs font-medium flex items-center gap-1">
          <Calendar className="h-3 w-3" />
          {config.label}
        </Label>
        <Button variant="ghost" size="sm" className="h-5 w-5 p-0">
          <RotateCcw className="h-3 w-3" />
        </Button>
      </div>
      <Input
        type="date"
        value={value as string || ''}
        onChange={(e) => onChange(e.target.value)}
        className="text-xs"
      />
    </div>
  )
}
