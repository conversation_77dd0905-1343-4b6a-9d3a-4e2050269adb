'use client'

import React, { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Package,
  RotateCcw,
  AlertTriangle,
  Plus,
  Trash2,
  Edit3,
  Eye,
  ChevronDown,
  ChevronRight,
  Key,
  Type,
  Hash,
  ToggleLeft,
  Braces
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { CustomFieldProps } from '../types'
import { validateField } from '../validation'

interface ObjectProperty {
  key: string
  value: any
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'null'
  collapsed?: boolean
}

interface ObjectValue {
  properties: ObjectProperty[]
  mode: 'visual' | 'json'
}

export function ObjectField({
  config,
  value,
  onChange,
  onValidate,
  errors,
  disabled,
  className
}: CustomFieldProps) {
  const [localValue, setLocalValue] = useState<ObjectValue>({
    properties: [],
    mode: 'visual',
    ...(value as ObjectValue || {}),
    properties: parseObjectToProperties(value)
  })
  const [jsonString, setJsonString] = useState('')
  const [jsonError, setJsonError] = useState<string | null>(null)
  const [validationError, setValidationError] = useState<string | null>(null)

  useEffect(() => {
    setLocalValue({
      properties: [],
      mode: 'visual',
      ...(value as ObjectValue || {}),
      properties: parseObjectToProperties(value)
    })
    setJsonString(JSON.stringify(propertiesToObject(parseObjectToProperties(value)), null, 2))
  }, [value])

  useEffect(() => {
    const result = validateField(config, localValue)
    setValidationError(result.isValid ? null : result.message || null)
    onValidate?.(result.isValid, result.message)
  }, [localValue, config, onValidate])

  function parseObjectToProperties(obj: any): ObjectProperty[] {
    if (!obj || typeof obj !== 'object') return []

    return Object.entries(obj).map(([key, val]) => ({
      key,
      value: val,
      type: getValueType(val),
      collapsed: typeof val === 'object' && val !== null
    }))
  }

  function propertiesToObject(properties: ObjectProperty[]): Record<string, any> {
    const obj: Record<string, any> = {}
    properties.forEach(prop => {
      obj[prop.key] = prop.value
    })
    return obj
  }

  function getValueType(value: any): ObjectProperty['type'] {
    if (value === null) return 'null'
    if (Array.isArray(value)) return 'array'
    if (typeof value === 'object') return 'object'
    if (typeof value === 'boolean') return 'boolean'
    if (typeof value === 'number') return 'number'
    return 'string'
  }

  const updateObject = (updates: Partial<ObjectValue>) => {
    const updated = { ...localValue, ...updates }
    setLocalValue(updated)
    onChange(propertiesToObject(updated.properties))
  }

  const handleReset = () => {
    const defaultValue: ObjectValue = {
      properties: [],
      mode: 'visual',
      ...(config.defaultValue as ObjectValue || {})
    }
    setLocalValue(defaultValue)
    onChange(propertiesToObject(defaultValue.properties))
  }

  const addProperty = () => {
    const newProperty: ObjectProperty = {
      key: `property${localValue.properties.length + 1}`,
      value: '',
      type: 'string',
      collapsed: false
    }
    updateObject({
      properties: [...localValue.properties, newProperty]
    })
  }

  const removeProperty = (index: number) => {
    const newProperties = localValue.properties.filter((_, i) => i !== index)
    updateObject({ properties: newProperties })
  }

  const updateProperty = (index: number, updates: Partial<ObjectProperty>) => {
    const newProperties = [...localValue.properties]
    newProperties[index] = { ...newProperties[index], ...updates }
    updateObject({ properties: newProperties })
  }

  const togglePropertyCollapse = (index: number) => {
    updateProperty(index, { collapsed: !localValue.properties[index].collapsed })
  }

  const handleJsonChange = (newJson: string) => {
    setJsonString(newJson)
    setJsonError(null)

    try {
      const parsed = JSON.parse(newJson)
      const properties = parseObjectToProperties(parsed)
      updateObject({ properties })
    } catch (error) {
      setJsonError('Invalid JSON syntax')
    }
  }

  const getTypeIcon = (type: ObjectProperty['type']) => {
    switch (type) {
      case 'string': return <Type className="h-3 w-3" />
      case 'number': return <Hash className="h-3 w-3" />
      case 'boolean': return <ToggleLeft className="h-3 w-3" />
      case 'object': return <Braces className="h-3 w-3" />
      case 'array': return <Package className="h-3 w-3" />
      default: return <Key className="h-3 w-3" />
    }
  }

  const renderPropertyValue = (property: ObjectProperty, index: number) => {
    if (property.type === 'object' && property.collapsed) {
      return (
        <div className="text-xs text-muted-foreground">
          {`{${Object.keys(property.value || {}).length} properties}`}
        </div>
      )
    }

    if (property.type === 'array' && property.collapsed) {
      return (
        <div className="text-xs text-muted-foreground">
          {`[${(property.value || []).length} items]`}
        </div>
      )
    }

    switch (property.type) {
      case 'string':
        return (
          <Input
            value={property.value || ''}
            onChange={(e) => updateProperty(index, { value: e.target.value })}
            placeholder="Enter string value"
            className="text-xs h-6"
            disabled={disabled}
          />
        )
      case 'number':
        return (
          <Input
            type="number"
            value={property.value || 0}
            onChange={(e) => updateProperty(index, { value: parseFloat(e.target.value) || 0 })}
            placeholder="Enter number"
            className="text-xs h-6"
            disabled={disabled}
          />
        )
      case 'boolean':
        return (
          <select
            value={property.value ? 'true' : 'false'}
            onChange={(e) => updateProperty(index, { value: e.target.value === 'true' })}
            className="text-xs h-6 px-2 border rounded"
            disabled={disabled}
          >
            <option value="true">true</option>
            <option value="false">false</option>
          </select>
        )
      case 'object':
        return (
          <div className="text-xs text-muted-foreground">
            Nested object editing (expand to edit)
          </div>
        )
      case 'array':
        return (
          <div className="text-xs text-muted-foreground">
            Array editing (expand to edit)
          </div>
        )
      default:
        return (
          <Input
            value={String(property.value || '')}
            onChange={(e) => updateProperty(index, { value: e.target.value })}
            placeholder="Enter value"
            className="text-xs h-6"
            disabled={disabled}
          />
        )
    }
  }

  const hasError = validationError || (errors && errors.length > 0) || jsonError
  const errorMessage = validationError || (errors && errors[0]) || jsonError
  const propertyCount = localValue.properties.length

  return (
    <div className={cn('space-y-3', className)}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={config.id} className="text-xs font-medium flex items-center gap-1">
          <Package className="h-3 w-3" />
          {config.label}
          {config.validation?.required && (
            <span className="text-destructive ml-1">*</span>
          )}
        </Label>

        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs h-4 px-1">
            {propertyCount} properties
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-5 w-5 p-0"
            title="Reset to default"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Description */}
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}

      {/* Object editor */}
      <Tabs value={localValue.mode} onValueChange={(mode) => updateObject({ mode: mode as any })} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="visual" className="text-xs">
            <Edit3 className="h-3 w-3 mr-1" />
            Visual Editor
          </TabsTrigger>
          <TabsTrigger value="json" className="text-xs">
            <Eye className="h-3 w-3 mr-1" />
            JSON Editor
          </TabsTrigger>
        </TabsList>

        <TabsContent value="visual" className="space-y-3">
          {/* Add property button */}
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={addProperty}
              disabled={disabled}
              className="text-xs h-6"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Property
            </Button>
          </div>

          {/* Properties list */}
          <div className="space-y-2">
            {localValue.properties.length === 0 ? (
              <div className="border-2 border-dashed rounded-lg p-6 text-center">
                <Package className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm font-medium mb-1">No properties</p>
                <p className="text-xs text-muted-foreground mb-3">
                  Add properties to build your object
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addProperty}
                  disabled={disabled}
                  className="text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add First Property
                </Button>
              </div>
            ) : (
              localValue.properties.map((property, index) => (
                <div
                  key={index}
                  className="border rounded-lg p-3 bg-background space-y-2"
                >
                  {/* Property header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 flex-1">
                      {(property.type === 'object' || property.type === 'array') && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => togglePropertyCollapse(index)}
                          className="h-4 w-4 p-0"
                        >
                          {property.collapsed ? (
                            <ChevronRight className="h-3 w-3" />
                          ) : (
                            <ChevronDown className="h-3 w-3" />
                          )}
                        </Button>
                      )}

                      <div className="flex items-center gap-1">
                        {getTypeIcon(property.type)}
                        <Badge variant="secondary" className="text-xs h-4 px-1">
                          {property.type}
                        </Badge>
                      </div>

                      {/* Property key */}
                      <Input
                        value={property.key}
                        onChange={(e) => updateProperty(index, { key: e.target.value })}
                        placeholder="Property name"
                        className="text-xs h-6 flex-1"
                        disabled={disabled}
                      />
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeProperty(index)}
                      disabled={disabled}
                      className="h-6 w-6 p-0"
                      title="Remove property"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>

                  {/* Property value */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground w-12">Value:</span>
                      <div className="flex-1">
                        {renderPropertyValue(property, index)}
                      </div>
                    </div>

                    {/* Type selector */}
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground w-12">Type:</span>
                      <select
                        value={property.type}
                        onChange={(e) => {
                          const newType = e.target.value as ObjectProperty['type']
                          let newValue = property.value

                          // Convert value based on new type
                          switch (newType) {
                            case 'string':
                              newValue = String(property.value || '')
                              break
                            case 'number':
                              newValue = parseFloat(String(property.value)) || 0
                              break
                            case 'boolean':
                              newValue = Boolean(property.value)
                              break
                            case 'object':
                              newValue = typeof property.value === 'object' ? property.value : {}
                              break
                            case 'array':
                              newValue = Array.isArray(property.value) ? property.value : []
                              break
                            case 'null':
                              newValue = null
                              break
                          }

                          updateProperty(index, { type: newType, value: newValue })
                        }}
                        className="text-xs h-6 px-2 border rounded"
                        disabled={disabled}
                      >
                        <option value="string">String</option>
                        <option value="number">Number</option>
                        <option value="boolean">Boolean</option>
                        <option value="object">Object</option>
                        <option value="array">Array</option>
                        <option value="null">Null</option>
                      </select>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="json" className="space-y-3">
          {/* JSON editor */}
          <div className="space-y-2">
            <Label className="text-xs">JSON Editor</Label>
            <textarea
              value={jsonString}
              onChange={(e) => handleJsonChange(e.target.value)}
              placeholder="Enter valid JSON..."
              className={cn(
                'w-full min-h-[200px] p-3 text-xs font-mono border rounded-lg resize-none',
                jsonError && 'border-destructive',
                disabled && 'opacity-50 cursor-not-allowed'
              )}
              disabled={disabled}
            />

            {jsonError && (
              <p className="text-xs text-destructive">{jsonError}</p>
            )}
          </div>

          {/* JSON preview */}
          <div className="space-y-2">
            <Label className="text-xs">Preview</Label>
            <div className="border rounded-lg p-3 bg-muted/20 min-h-[100px]">
              <pre className="text-xs overflow-auto">
                {JSON.stringify(propertiesToObject(localValue.properties), null, 2)}
              </pre>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Validation error */}
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">
            {errorMessage}
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
