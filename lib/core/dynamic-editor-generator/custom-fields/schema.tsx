'use client'

import { FieldConfig, FieldSchema, FieldGroupConfig, FieldSectionConfig } from './types'
import { createFieldConfig } from './defaults'
import { commonFieldGroups } from './field-group'
import { commonFieldSections } from './field-section'

// Create a field schema from configuration
export function createFieldSchema(config: {
  fields: FieldConfig[]
  groups?: FieldGroupConfig[]
  sections?: FieldSectionConfig[]
}): FieldSchema {
  return {
    fields: config.fields,
    groups: config.groups || [],
    sections: config.sections || []
  }
}

// Predefined field schemas for common use cases
export const commonFieldSchemas = {
  // Typography schema
  typography: createFieldSchema({
    fields: [
      createFieldConfig('fontFamily', 'font', 'Font Family'),
      createFieldConfig('fontSize', 'text', 'Font Size', { placeholder: '16px, 1rem' }),
      createFieldConfig('fontWeight', 'select', 'Font Weight', {
        options: [
          { label: 'Light', value: '300' },
          { label: 'Normal', value: '400' },
          { label: 'Medium', value: '500' },
          { label: 'Semibold', value: '600' },
          { label: 'Bold', value: '700' }
        ]
      }),
      createFieldConfig('lineHeight', 'text', 'Line Height', { placeholder: '1.5, 24px' }),
      createFieldConfig('letterSpacing', 'text', 'Letter Spacing', { placeholder: '0.1em, 1px' }),
      createFieldConfig('textAlign', 'select', 'Text Align', {
        options: [
          { label: 'Left', value: 'left' },
          { label: 'Center', value: 'center' },
          { label: 'Right', value: 'right' },
          { label: 'Justify', value: 'justify' }
        ]
      }),
      createFieldConfig('textTransform', 'select', 'Text Transform', {
        options: [
          { label: 'None', value: 'none' },
          { label: 'Uppercase', value: 'uppercase' },
          { label: 'Lowercase', value: 'lowercase' },
          { label: 'Capitalize', value: 'capitalize' }
        ]
      }),
      createFieldConfig('textColor', 'color', 'Text Color')
    ],
    groups: [commonFieldGroups.typography]
  }),

  // Colors schema
  colors: createFieldSchema({
    fields: [
      createFieldConfig('textColor', 'color', 'Text Color'),
      createFieldConfig('backgroundColor', 'color', 'Background Color'),
      createFieldConfig('borderColor', 'color', 'Border Color'),
      createFieldConfig('accentColor', 'color', 'Accent Color')
    ],
    groups: [commonFieldGroups.colors]
  }),

  // Spacing schema
  spacing: createFieldSchema({
    fields: [
      createFieldConfig('margin', 'spacing', 'Margin'),
      createFieldConfig('padding', 'spacing', 'Padding'),
      createFieldConfig('gap', 'number', 'Gap', { min: 0, max: 100, unit: 'px' })
    ],
    groups: [commonFieldGroups.spacing]
  }),

  // Layout schema
  layout: createFieldSchema({
    fields: [
      createFieldConfig('display', 'select', 'Display', {
        options: [
          { label: 'Block', value: 'block' },
          { label: 'Inline', value: 'inline' },
          { label: 'Inline Block', value: 'inline-block' },
          { label: 'Flex', value: 'flex' },
          { label: 'Grid', value: 'grid' },
          { label: 'None', value: 'none' }
        ]
      }),
      createFieldConfig('position', 'select', 'Position', {
        options: [
          { label: 'Static', value: 'static' },
          { label: 'Relative', value: 'relative' },
          { label: 'Absolute', value: 'absolute' },
          { label: 'Fixed', value: 'fixed' },
          { label: 'Sticky', value: 'sticky' }
        ]
      }),
      createFieldConfig('width', 'text', 'Width', { placeholder: '100%, 300px, auto' }),
      createFieldConfig('height', 'text', 'Height', { placeholder: '100%, 300px, auto' }),
      createFieldConfig('flexDirection', 'select', 'Flex Direction', {
        options: [
          { label: 'Row', value: 'row' },
          { label: 'Column', value: 'column' },
          { label: 'Row Reverse', value: 'row-reverse' },
          { label: 'Column Reverse', value: 'column-reverse' }
        ],
        showWhen: { field: 'display', operator: 'equals', value: 'flex' }
      }),
      createFieldConfig('justifyContent', 'select', 'Justify Content', {
        options: [
          { label: 'Start', value: 'flex-start' },
          { label: 'Center', value: 'center' },
          { label: 'End', value: 'flex-end' },
          { label: 'Space Between', value: 'space-between' },
          { label: 'Space Around', value: 'space-around' },
          { label: 'Space Evenly', value: 'space-evenly' }
        ],
        showWhen: { field: 'display', operator: 'equals', value: 'flex' }
      }),
      createFieldConfig('alignItems', 'select', 'Align Items', {
        options: [
          { label: 'Start', value: 'flex-start' },
          { label: 'Center', value: 'center' },
          { label: 'End', value: 'flex-end' },
          { label: 'Stretch', value: 'stretch' },
          { label: 'Baseline', value: 'baseline' }
        ],
        showWhen: { field: 'display', operator: 'equals', value: 'flex' }
      })
    ],
    groups: [commonFieldGroups.layout]
  }),

  // Effects schema
  effects: createFieldSchema({
    fields: [
      createFieldConfig('boxShadow', 'shadow', 'Box Shadow'),
      createFieldConfig('borderRadius', 'text', 'Border Radius', { placeholder: '8px, 50%' }),
      createFieldConfig('opacity', 'range', 'Opacity', { min: 0, max: 1, step: 0.1 }),
      createFieldConfig('transform', 'text', 'Transform', { placeholder: 'rotate(45deg) scale(1.1)' }),
      createFieldConfig('filter', 'text', 'Filter', { placeholder: 'blur(5px) brightness(1.2)' })
    ],
    groups: [commonFieldGroups.effects]
  }),

  // Animation schema
  animation: createFieldSchema({
    fields: [
      createFieldConfig('animationType', 'select', 'Animation Type', {
        options: [
          { label: 'None', value: 'none' },
          { label: 'Fade In', value: 'fadeIn' },
          { label: 'Slide Up', value: 'slideUp' },
          { label: 'Slide Down', value: 'slideDown' },
          { label: 'Slide Left', value: 'slideLeft' },
          { label: 'Slide Right', value: 'slideRight' },
          { label: 'Scale', value: 'scale' },
          { label: 'Bounce', value: 'bounce' },
          { label: 'Pulse', value: 'pulse' }
        ]
      }),
      createFieldConfig('animationDuration', 'number', 'Duration (ms)', { min: 0, max: 5000, defaultValue: 300 }),
      createFieldConfig('animationDelay', 'number', 'Delay (ms)', { min: 0, max: 5000, defaultValue: 0 }),
      createFieldConfig('animationEasing', 'select', 'Easing', {
        options: [
          { label: 'Ease', value: 'ease' },
          { label: 'Ease In', value: 'ease-in' },
          { label: 'Ease Out', value: 'ease-out' },
          { label: 'Ease In Out', value: 'ease-in-out' },
          { label: 'Linear', value: 'linear' }
        ]
      })
    ],
    groups: [commonFieldGroups.animation]
  }),

  // Content schema
  content: createFieldSchema({
    fields: [
      createFieldConfig('title', 'text', 'Title'),
      createFieldConfig('subtitle', 'text', 'Subtitle'),
      createFieldConfig('description', 'textarea', 'Description'),
      createFieldConfig('content', 'richtext', 'Content'),
      createFieldConfig('image', 'image', 'Image'),
      createFieldConfig('link', 'link', 'Link')
    ]
  }),

  // SEO schema
  seo: createFieldSchema({
    fields: [
      createFieldConfig('metaTitle', 'text', 'Meta Title', { maxLength: 60 }),
      createFieldConfig('metaDescription', 'textarea', 'Meta Description', { maxLength: 160 }),
      createFieldConfig('keywords', 'multi-select', 'Keywords', { creatable: true }),
      createFieldConfig('canonical', 'url', 'Canonical URL'),
      createFieldConfig('noindex', 'boolean', 'No Index'),
      createFieldConfig('nofollow', 'boolean', 'No Follow')
    ],
    groups: [commonFieldGroups.seo]
  }),

  // Accessibility schema
  accessibility: createFieldSchema({
    fields: [
      createFieldConfig('ariaLabel', 'text', 'ARIA Label'),
      createFieldConfig('ariaDescription', 'text', 'ARIA Description'),
      createFieldConfig('role', 'select', 'Role', {
        options: [
          { label: 'Button', value: 'button' },
          { label: 'Link', value: 'link' },
          { label: 'Heading', value: 'heading' },
          { label: 'Banner', value: 'banner' },
          { label: 'Navigation', value: 'navigation' },
          { label: 'Main', value: 'main' },
          { label: 'Complementary', value: 'complementary' },
          { label: 'Contentinfo', value: 'contentinfo' }
        ]
      }),
      createFieldConfig('tabIndex', 'number', 'Tab Index', { min: -1, max: 999 }),
      createFieldConfig('altText', 'text', 'Alt Text')
    ],
    groups: [commonFieldGroups.accessibility]
  })
}

// Helper function to merge multiple schemas
export function mergeFieldSchemas(...schemas: FieldSchema[]): FieldSchema {
  const mergedFields: FieldConfig[] = []
  const mergedGroups: FieldGroupConfig[] = []
  const mergedSections: FieldSectionConfig[] = []

  schemas.forEach(schema => {
    mergedFields.push(...schema.fields)
    if (schema.groups) {
      mergedGroups.push(...schema.groups)
    }
    if (schema.sections) {
      mergedSections.push(...schema.sections)
    }
  })

  return {
    fields: mergedFields,
    groups: mergedGroups,
    sections: mergedSections
  }
}

// Helper function to create a complete block schema
export function createBlockSchema(
  blockType: string,
  customFields: FieldConfig[] = []
): FieldSchema {
  // Base schemas that most blocks need
  const baseSchemas = [
    commonFieldSchemas.content,
    commonFieldSchemas.typography,
    commonFieldSchemas.colors,
    commonFieldSchemas.spacing,
    commonFieldSchemas.layout,
    commonFieldSchemas.effects
  ]

  // Add custom fields
  const customSchema = createFieldSchema({ fields: customFields })

  // Merge all schemas
  const mergedSchema = mergeFieldSchemas(...baseSchemas, customSchema)

  // Add sections for organization
  mergedSchema.sections = [
    commonFieldSections.content,
    commonFieldSections.design,
    commonFieldSections.layout,
    commonFieldSections.advanced
  ]

  return mergedSchema
}
