'use client'

import React, { useState } from 'react'
import { usePageBuilder } from '../../context'
import { blockRegistry } from '../../blocks/registry'

import { <PERSON><PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Settings,
  Palette,
  Smartphone,
  Move
} from 'lucide-react'

import { PropertiesPanelHeader } from './properties-panel-header'
import { BlockContentEditor } from './block-content-editor'
import { BlockStyleEditor } from './block-style-editor'
import { BlockResponsiveEditor } from './block-responsive-editor'
import { BlockAdvancedEditor } from './block-advanced-editor'

interface PropertiesPanelProps {
  blockId: string
}

export function PropertiesPanel({ blockId }: PropertiesPanelProps) {
  const { state, updateBlock, deleteBlock, duplicateBlock } = usePageBuilder()
  const [activeTab, setActiveTab] = useState('content')

  const block = state.page.blocks.find(b => b.id === blockId)
  
  if (!block) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <Settings className="h-8 w-8 mx-auto mb-2" />
        <p>Block not found</p>
      </div>
    )
  }

  const blockType = blockRegistry.getBlockType(block.type)

  if (!blockType) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <Settings className="h-8 w-8 mx-auto mb-2" />
        <p>Unknown block type: {block.type}</p>
      </div>
    )
  }

  // Handle block configuration updates
  const handleConfigUpdate = (updates: any) => {
    updateBlock(block.id, {
      configuration: { ...block.configuration, ...updates }
    })
  }

  // Handle block styling updates
  const handleStylingUpdate = (updates: any) => {
    updateBlock(block.id, {
      styling: { ...block.styling, ...updates }
    })
  }

  // Handle block responsive updates
  const handleResponsiveUpdate = (updates: any) => {
    updateBlock(block.id, {
      responsive: { ...block.responsive, ...updates }
    })
  }

  // Handle block visibility toggle
  const handleVisibilityToggle = () => {
    updateBlock(block.id, { isVisible: !block.isVisible })
  }

  // Handle block deletion
  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this block?')) {
      deleteBlock(block.id)
    }
  }

  // Handle block duplication
  const handleDuplicate = () => {
    duplicateBlock(block.id)
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <PropertiesPanelHeader
        block={block}
        blockType={blockType}
        onVisibilityToggle={handleVisibilityToggle}
        onDuplicate={handleDuplicate}
        onDelete={handleDelete}
      />

      {/* Tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="px-4 pt-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="content" className="text-xs">
                <Settings className="h-3 w-3 mr-1" />
                Content
              </TabsTrigger>
              <TabsTrigger value="style" className="text-xs">
                <Palette className="h-3 w-3 mr-1" />
                Style
              </TabsTrigger>
              <TabsTrigger value="responsive" className="text-xs">
                <Smartphone className="h-3 w-3 mr-1" />
                Mobile
              </TabsTrigger>
              <TabsTrigger value="advanced" className="text-xs">
                <Move className="h-3 w-3 mr-1" />
                Advanced
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-hidden">
            {/* Content Tab */}
            <TabsContent value="content" className="h-full mt-0">
              <ScrollArea className="h-full px-4 pb-4">
                <div className="pt-4">
                  <BlockContentEditor
                    block={block}
                    blockType={blockType}
                    onUpdate={handleConfigUpdate}
                  />
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Style Tab */}
            <TabsContent value="style" className="h-full mt-0">
              <ScrollArea className="h-full px-4 pb-4">
                <div className="pt-4">
                  <BlockStyleEditor
                    block={block}
                    onUpdate={handleStylingUpdate}
                  />
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Responsive Tab */}
            <TabsContent value="responsive" className="h-full mt-0">
              <ScrollArea className="h-full px-4 pb-4">
                <div className="pt-4">
                  <BlockResponsiveEditor
                    block={block}
                    onUpdate={handleResponsiveUpdate}
                  />
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Advanced Tab */}
            <TabsContent value="advanced" className="h-full mt-0">
              <ScrollArea className="h-full px-4 pb-4">
                <div className="pt-4">
                  <BlockAdvancedEditor
                    block={block}
                    onUpdate={(updates) => updateBlock(block.id, updates)}
                  />
                </div>
              </ScrollArea>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  )
}

// Re-export all components for easy access
export { PropertiesPanelHeader } from './properties-panel-header'
export { BlockContentEditor } from './block-content-editor'
export { BlockStyleEditor } from './block-style-editor'
export { BlockResponsiveEditor } from './block-responsive-editor'
export { BlockAdvancedEditor } from './block-advanced-editor'

// Re-export style sub-components
export { BackgroundStyleEditor } from './style-editors/background-style-editor'
export { TypographyStyleEditor } from './style-editors/typography-style-editor'
export { SpacingStyleEditor } from './style-editors/spacing-style-editor'
export { EffectsStyleEditor } from './style-editors/effects-style-editor'

// Re-export responsive sub-components
export { ResponsiveVisibilityControls } from './responsive-editors/responsive-visibility-controls'
export { ResponsiveBreakpointEditor } from './responsive-editors/responsive-breakpoint-editor'

// Re-export advanced sub-components
export { AnimationEditor } from './advanced-editors/animation-editor'
export { AccessibilityEditor } from './advanced-editors/accessibility-editor'
export { DisplayConditionsEditor } from './advanced-editors/display-conditions-editor'
export { CustomCSSEditor } from './advanced-editors/custom-css-editor'
export { CustomAttributesEditor } from './advanced-editors/custom-attributes-editor'

// Re-export utility components
export { GenericBlockEditor } from './generic-block-editor'

// Re-export custom field components
export * from './custom-fields'
export { CustomFieldsDemo } from './custom-fields/demo'
