'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  Code, 
  Eye, 
  AlertTriangle,
  CheckCircle,
  Copy,
  Download,
  Upload
} from 'lucide-react'

interface GenericBlockEditorProps {
  config: any
  onChange: (updates: any) => void
}

export function GenericBlockEditor({ config, onChange }: GenericBlockEditorProps) {
  const [jsonValue, setJsonValue] = useState(JSON.stringify(config, null, 2))
  const [jsonError, setJsonError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('editor')

  const handleJsonChange = (value: string) => {
    setJsonValue(value)
    
    try {
      const parsed = JSON.parse(value)
      setJsonError(null)
      onChange(parsed)
    } catch (error) {
      setJsonError(error instanceof Error ? error.message : 'Invalid JSON')
    }
  }

  const formatJson = () => {
    try {
      const parsed = JSON.parse(jsonValue)
      const formatted = JSON.stringify(parsed, null, 2)
      setJsonValue(formatted)
      setJsonError(null)
    } catch (error) {
      setJsonError(error instanceof Error ? error.message : 'Invalid JSON')
    }
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonValue)
  }

  const exportConfig = () => {
    const blob = new Blob([jsonValue], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'block-config.json'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const importConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        try {
          const parsed = JSON.parse(content)
          setJsonValue(JSON.stringify(parsed, null, 2))
          onChange(parsed)
          setJsonError(null)
        } catch (error) {
          setJsonError('Invalid JSON file')
        }
      }
      reader.readAsText(file)
    }
  }

  const getConfigStats = () => {
    const keys = Object.keys(config)
    const totalProperties = keys.length
    const nestedObjects = keys.filter(key => 
      typeof config[key] === 'object' && config[key] !== null && !Array.isArray(config[key])
    ).length
    const arrays = keys.filter(key => Array.isArray(config[key])).length
    
    return { totalProperties, nestedObjects, arrays }
  }

  const stats = getConfigStats()

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-sm flex items-center">
                <Code className="h-4 w-4 mr-2" />
                Block Configuration
              </CardTitle>
              <CardDescription className="text-xs">
                Edit the block configuration directly using JSON
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              {jsonError ? (
                <Badge variant="destructive" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Invalid
                </Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Valid
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="editor" className="text-xs">Editor</TabsTrigger>
              <TabsTrigger value="preview" className="text-xs">Preview</TabsTrigger>
              <TabsTrigger value="stats" className="text-xs">Stats</TabsTrigger>
            </TabsList>

            <TabsContent value="editor" className="space-y-4">
              {/* JSON Editor */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-xs font-medium">Configuration JSON</label>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={formatJson}
                      title="Format JSON"
                      className="h-6 px-2 text-xs"
                    >
                      Format
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={copyToClipboard}
                      title="Copy to clipboard"
                      className="h-6 px-2 text-xs"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                
                <Textarea
                  value={jsonValue}
                  onChange={(e) => handleJsonChange(e.target.value)}
                  rows={12}
                  className="font-mono text-xs"
                  placeholder="Enter JSON configuration..."
                />
                
                {jsonError && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription className="text-xs">
                      JSON Error: {jsonError}
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              {/* Import/Export */}
              <div className="flex items-center space-x-2 pt-2 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportConfig}
                  className="text-xs"
                >
                  <Download className="h-3 w-3 mr-1" />
                  Export
                </Button>
                
                <div className="relative">
                  <input
                    type="file"
                    accept=".json"
                    onChange={importConfig}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                  <Button variant="outline" size="sm" className="text-xs">
                    <Upload className="h-3 w-3 mr-1" />
                    Import
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              <div className="space-y-2">
                <label className="text-xs font-medium">Configuration Preview</label>
                <div className="bg-muted/50 rounded-md p-3 max-h-64 overflow-auto">
                  <pre className="text-xs text-muted-foreground whitespace-pre-wrap">
                    {JSON.stringify(config, null, 2)}
                  </pre>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="stats" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-xs font-medium">Configuration Statistics</label>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>Total Properties:</span>
                      <Badge variant="secondary">{stats.totalProperties}</Badge>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Nested Objects:</span>
                      <Badge variant="secondary">{stats.nestedObjects}</Badge>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Arrays:</span>
                      <Badge variant="secondary">{stats.arrays}</Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium">Property Types</label>
                  <div className="space-y-1">
                    {Object.entries(config).slice(0, 5).map(([key, value]) => (
                      <div key={key} className="flex justify-between text-xs">
                        <span className="truncate max-w-[80px]" title={key}>
                          {key}:
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {Array.isArray(value) ? 'array' : typeof value}
                        </Badge>
                      </div>
                    ))}
                    {Object.keys(config).length > 5 && (
                      <div className="text-xs text-muted-foreground">
                        +{Object.keys(config).length - 5} more...
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onChange({})}
            className="w-full text-xs"
          >
            Reset Configuration
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const template = {
                title: 'Sample Title',
                description: 'Sample description',
                enabled: true,
                settings: {
                  color: '#000000',
                  size: 'medium'
                }
              }
              setJsonValue(JSON.stringify(template, null, 2))
              onChange(template)
            }}
            className="w-full text-xs"
          >
            Load Template
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
