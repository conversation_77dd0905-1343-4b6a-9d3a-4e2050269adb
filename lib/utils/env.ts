/**
 * Environment variable utilities
 */

/**
 * Gets an environment variable, with optional default value
 * If required is true and the variable doesn't exist, throws an error
 */
export function getEnv(key: string, defaultValue: string = "", required: boolean = false): string {
  const value = process.env[key] || defaultValue;
  
  if (required && !value) {
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  
  return value;
} 