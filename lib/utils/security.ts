/**
 * Security utilities for MicroVM commands
 */

// List of disallowed commands or patterns
const DISALLOWED_COMMANDS = [
  /rm\s+(-rf|--force)\s+\//i,  // rm -rf /
  /rm\s+(-rf|--force)\s+\*/i,  // rm -rf *
  /mkfs/i,                     // mkfs commands
  /dd\s+if=/i,                 // dd if=
  /:(){ :|: & };:/,            // Fork bomb
  /curl -s .* \| bash/i,       // Piping curl to bash
  /wget .* \| bash/i,          // Piping wget to bash
  /shutdown/i,                 // shutdown
  /halt/i,                     // halt
  /poweroff/i,                 // poweroff
  /reboot/i,                   // reboot
  /init 0/i,                   // init 0
  /init 6/i,                   // init 6
];

// List of allowed commands (whitelist approach is safer)
const ALLOWED_COMMAND_PREFIXES = [
  "ls", "cd", "pwd", "mkdir", "touch", "cat", "echo", "grep",
  "find", "ps", "top", "free", "df", "du", "cp", "mv",
  "apt-get install", "apt-get update", "apt-get upgrade", 
  "npm", "node", "python", "python3", "pip", "pip3",
  "git clone", "git pull", "git checkout", "git status",
  "gcc", "make", "cmake",
];

/**
 * Validates a command for safety and sanitizes it
 * 
 * @param command - The command to validate
 * @returns The sanitized command if valid, null if invalid
 */
export function validateAndSanitizeCommand(command: string): string | null {
  // Trim whitespace
  const trimmedCommand = command.trim();
  
  // Basic validation - reject empty commands
  if (!trimmedCommand) {
    return null;
  }
  
  // Check against disallowed patterns
  for (const pattern of DISALLOWED_COMMANDS) {
    if (pattern.test(trimmedCommand)) {
      console.warn(`Command rejected (matched disallowed pattern): ${trimmedCommand}`);
      return null;
    }
  }
  
  // Validate command against allowed prefixes (more restrictive approach)
  const isAllowed = ALLOWED_COMMAND_PREFIXES.some(prefix => 
    trimmedCommand.startsWith(prefix + " ") || trimmedCommand === prefix
  );
  
  if (!isAllowed) {
    console.warn(`Command rejected (not in whitelist): ${trimmedCommand}`);
    // We could return null here for stricter security,
    // but for flexibility we'll let it pass with a warning
  }
  
  // Sanitize command - escape shell special characters
  // This is a basic implementation and may need to be enhanced
  const sanitized = trimmedCommand
    .replace(/"/g, '\\"')     // Escape double quotes
    .replace(/`/g, '\\`')     // Escape backticks
    .replace(/\$/g, '\\$')    // Escape $ signs
    .replace(/\r/g, '')       // Remove carriage returns
    .replace(/\n/g, ' ');     // Replace newlines with spaces
  
  return sanitized;
} 