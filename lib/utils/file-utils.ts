/**
 * File Utilities
 * 
 * This module provides utility functions for working with files.
 */

/**
 * Get the language for syntax highlighting based on file name
 * @param fileName The name of the file
 * @returns The language identifier for syntax highlighting
 */
export function getLanguageFromFileName(fileName: string): string {
  if (!fileName) return 'plaintext';
  
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  
  // Map file extensions to languages
  switch (extension) {
    // JavaScript and TypeScript
    case 'js':
      return 'javascript';
    case 'jsx':
      return 'javascriptreact';
    case 'ts':
      return 'typescript';
    case 'tsx':
      return 'typescriptreact';
    
    // Web
    case 'html':
    case 'htm':
      return 'html';
    case 'css':
      return 'css';
    case 'scss':
    case 'sass':
      return 'scss';
    case 'less':
      return 'less';
    
    // Data formats
    case 'json':
      return 'json';
    case 'xml':
      return 'xml';
    case 'yaml':
    case 'yml':
      return 'yaml';
    case 'toml':
      return 'toml';
    
    // Markdown and documentation
    case 'md':
    case 'markdown':
      return 'markdown';
    case 'txt':
      return 'plaintext';
    
    // Programming languages
    case 'py':
      return 'python';
    case 'java':
      return 'java';
    case 'c':
      return 'c';
    case 'cpp':
    case 'cc':
    case 'cxx':
      return 'cpp';
    case 'cs':
      return 'csharp';
    case 'go':
      return 'go';
    case 'rs':
      return 'rust';
    case 'rb':
      return 'ruby';
    case 'php':
      return 'php';
    case 'swift':
      return 'swift';
    case 'kt':
    case 'kts':
      return 'kotlin';
    
    // Shell scripts
    case 'sh':
    case 'bash':
      return 'shellscript';
    case 'bat':
    case 'cmd':
      return 'bat';
    case 'ps1':
      return 'powershell';
    
    // Configuration files
    case 'ini':
      return 'ini';
    case 'env':
      return 'dotenv';
    case 'dockerfile':
      return 'dockerfile';
    
    // Other
    case 'sql':
      return 'sql';
    case 'graphql':
    case 'gql':
      return 'graphql';
    
    // Default
    default:
      return 'plaintext';
  }
}

/**
 * Get a file icon based on file extension
 * @param fileName The name of the file
 * @returns The icon component name to use
 */
export function getFileIconByName(fileName: string): string {
  if (!fileName) return 'FileText';
  
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  
  // Map file extensions to icon names
  switch (extension) {
    // Code files
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
    case 'py':
    case 'java':
    case 'c':
    case 'cpp':
    case 'cs':
    case 'go':
    case 'rs':
    case 'rb':
    case 'php':
    case 'swift':
    case 'kt':
      return 'FileCode';
    
    // Data files
    case 'json':
    case 'xml':
    case 'yaml':
    case 'yml':
    case 'toml':
      return 'FileJson';
    
    // Configuration files
    case 'ini':
    case 'env':
    case 'config':
    case 'conf':
    case 'cfg':
      return 'FileCog';
    
    // Image files
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'svg':
    case 'webp':
    case 'bmp':
    case 'ico':
      return 'FileImage';
    
    // Archive files
    case 'zip':
    case 'tar':
    case 'gz':
    case 'rar':
    case '7z':
      return 'FileArchive';
    
    // Spreadsheet files
    case 'csv':
    case 'xls':
    case 'xlsx':
    case 'ods':
      return 'FileSpreadsheet';
    
    // Text files
    case 'txt':
    case 'md':
    case 'markdown':
      return 'FileText';
    
    // Default
    default:
      return 'FileText';
  }
}

/**
 * Check if a file is a binary file based on its extension
 * @param fileName The name of the file
 * @returns True if the file is likely binary, false otherwise
 */
export function isBinaryFile(fileName: string): boolean {
  if (!fileName) return false;
  
  const extension = fileName.split('.').pop()?.toLowerCase() || '';
  
  // List of common binary file extensions
  const binaryExtensions = [
    // Images
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'ico', 'webp', 'tiff', 'svg',
    
    // Audio
    'mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a',
    
    // Video
    'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm',
    
    // Archives
    'zip', 'tar', 'gz', 'rar', '7z', 'bz2', 'xz',
    
    // Documents
    'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
    
    // Executables
    'exe', 'dll', 'so', 'dylib', 'bin',
    
    // Other
    'class', 'jar', 'war', 'pyc', 'o', 'a', 'lib'
  ];
  
  return binaryExtensions.includes(extension);
}
