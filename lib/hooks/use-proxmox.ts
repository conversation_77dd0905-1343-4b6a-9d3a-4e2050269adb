import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useToast } from '@/components/ui/use-toast';
import { NodeInfo, VMInfo, VMStatus, StorageInfo, TaskInfo } from '@/lib/proxmox/types';

// Base hook for Proxmox API
export function useProxmox() {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Generic fetch function
  const fetchData = useCallback(async <T>(
    resource: string,
    params: Record<string, string> = {}
  ): Promise<T | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams({ resource, ...params }).toString();
      const response = await axios.get(`/api/proxmox?${queryParams}`);
      return response.data.data as T;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'An error occurred';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Generic action function
  const performAction = useCallback(async <T>(
    action: string,
    params: Record<string, any> = {}
  ): Promise<T | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post('/api/proxmox', {
        action,
        ...params,
      });
      return response.data.data as T;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'An error occurred';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    loading,
    error,
    fetchData,
    performAction,
  };
}

// Hook for Proxmox nodes
export function useProxmoxNodes() {
  const [nodes, setNodes] = useState<NodeInfo[]>([]);
  const { loading, error, fetchData } = useProxmox();

  const getNodes = useCallback(async () => {
    const data = await fetchData<NodeInfo[]>('nodes');
    if (data) {
      setNodes(data);
    }
  }, [fetchData]);

  useEffect(() => {
    getNodes();
  }, [getNodes]);

  return {
    nodes,
    loading,
    error,
    refreshNodes: getNodes,
  };
}

// Hook for Proxmox VMs
export function useProxmoxVMs(node: string) {
  const [vms, setVMs] = useState<VMInfo[]>([]);
  const { loading, error, fetchData, performAction } = useProxmox();

  const getVMs = useCallback(async () => {
    if (!node) return;
    
    const data = await fetchData<VMInfo[]>('vms', { node });
    if (data) {
      setVMs(data);
    }
  }, [fetchData, node]);

  useEffect(() => {
    if (node) {
      getVMs();
    }
  }, [getVMs, node]);

  const getVM = useCallback(async (vmid: string) => {
    return await fetchData<VMInfo>('vm', { node, vmid });
  }, [fetchData, node]);

  const createVM = useCallback(async (params: {
    vmid: number | string;
    name: string;
    cores?: number;
    memory?: number;
    disks?: Array<{ storage: string; size: number; format?: string }>;
    networks?: Array<{ model: string; bridge?: string; vlan?: number }>;
    onBoot?: boolean;
    start?: boolean;
  }) => {
    return await performAction<VMInfo>('createVM', {
      node,
      ...params,
    });
  }, [performAction, node]);

  const startVM = useCallback(async (vmid: string) => {
    return await performAction('startVM', { node, vmid });
  }, [performAction, node]);

  const stopVM = useCallback(async (vmid: string) => {
    return await performAction('stopVM', { node, vmid });
  }, [performAction, node]);

  const restartVM = useCallback(async (vmid: string) => {
    return await performAction('restartVM', { node, vmid });
  }, [performAction, node]);

  const deleteVM = useCallback(async (vmid: string) => {
    return await performAction('deleteVM', { node, vmid });
  }, [performAction, node]);

  const createTemplate = useCallback(async (params: {
    vmid: number | string;
    name: string;
    description?: string;
    cores?: number;
    memory?: number;
    disks?: Array<{ storage: string; size: number; format?: string }>;
    networks?: Array<{ model: string; bridge?: string; vlan?: number }>;
  }) => {
    return await performAction<VMInfo>('createTemplate', {
      node,
      ...params,
    });
  }, [performAction, node]);

  const cloneVM = useCallback(async (params: {
    vmid: string;
    newVmId: number | string;
    name: string;
    description?: string;
    start?: boolean;
    full?: boolean;
  }) => {
    return await performAction<VMInfo>('cloneVM', {
      node,
      ...params,
    });
  }, [performAction, node]);

  return {
    vms,
    loading,
    error,
    refreshVMs: getVMs,
    getVM,
    createVM,
    startVM,
    stopVM,
    restartVM,
    deleteVM,
    createTemplate,
    cloneVM,
  };
}

// Hook for Proxmox storage
export function useProxmoxStorage() {
  const [storage, setStorage] = useState<StorageInfo[]>([]);
  const { loading, error, fetchData } = useProxmox();

  const getStorage = useCallback(async () => {
    const data = await fetchData<StorageInfo[]>('storage');
    if (data) {
      setStorage(data);
    }
  }, [fetchData]);

  useEffect(() => {
    getStorage();
  }, [getStorage]);

  const getVolumes = useCallback(async (node: string, storageId: string) => {
    return await fetchData('volumes', { node, storage: storageId });
  }, [fetchData]);

  return {
    storage,
    loading,
    error,
    refreshStorage: getStorage,
    getVolumes,
  };
}

// Hook for Proxmox backups and snapshots
export function useProxmoxBackup(node: string, vmid: string) {
  const [snapshots, setSnapshots] = useState<any[]>([]);
  const [backups, setBackups] = useState<any[]>([]);
  const { loading, error, fetchData, performAction } = useProxmox();

  const getSnapshots = useCallback(async () => {
    if (!node || !vmid) return;
    
    const data = await fetchData<any[]>('backup', { node, vmid, type: 'snapshots' });
    if (data) {
      setSnapshots(data);
    }
  }, [fetchData, node, vmid]);

  const getBackups = useCallback(async () => {
    if (!node || !vmid) return;
    
    const data = await fetchData<any[]>('backup', { node, vmid, type: 'backups' });
    if (data) {
      setBackups(data);
    }
  }, [fetchData, node, vmid]);

  useEffect(() => {
    if (node && vmid) {
      getSnapshots();
      getBackups();
    }
  }, [getSnapshots, getBackups, node, vmid]);

  const createSnapshot = useCallback(async (params: {
    name: string;
    description?: string;
    vmstate?: boolean;
  }) => {
    return await performAction('createSnapshot', {
      node,
      vmid,
      ...params,
    });
  }, [performAction, node, vmid]);

  const createBackup = useCallback(async (params: {
    storage: string;
    mode?: 'snapshot' | 'suspend' | 'stop';
    compress?: 'gzip' | 'lzo' | 'zstd' | 0;
    notes?: string;
  }) => {
    return await performAction('createBackup', {
      node,
      vmid,
      ...params,
    });
  }, [performAction, node, vmid]);

  const rollbackSnapshot = useCallback(async (snapshot: string) => {
    return await performAction('rollbackSnapshot', {
      node,
      vmid,
      snapshot,
    });
  }, [performAction, node, vmid]);

  const deleteSnapshot = useCallback(async (snapshot: string) => {
    return await performAction('deleteSnapshot', {
      node,
      vmid,
      snapshot,
    });
  }, [performAction, node, vmid]);

  const deleteBackup = useCallback(async (backupId: string) => {
    return await performAction('deleteBackup', {
      node,
      vmid,
      backupId,
    });
  }, [performAction, node, vmid]);

  return {
    snapshots,
    backups,
    loading,
    error,
    refreshSnapshots: getSnapshots,
    refreshBackups: getBackups,
    createSnapshot,
    createBackup,
    rollbackSnapshot,
    deleteSnapshot,
    deleteBackup,
  };
}

// Hook for Proxmox tasks
export function useProxmoxTasks(node: string) {
  const [tasks, setTasks] = useState<TaskInfo[]>([]);
  const { loading, error, fetchData } = useProxmox();

  const getTasks = useCallback(async () => {
    if (!node) return;
    
    const data = await fetchData<TaskInfo[]>('tasks', { node });
    if (data) {
      setTasks(data);
    }
  }, [fetchData, node]);

  useEffect(() => {
    if (node) {
      getTasks();
      
      // Set up polling for tasks
      const interval = setInterval(() => {
        getTasks();
      }, 5000); // Poll every 5 seconds
      
      return () => clearInterval(interval);
    }
  }, [getTasks, node]);

  return {
    tasks,
    loading,
    error,
    refreshTasks: getTasks,
  };
}
