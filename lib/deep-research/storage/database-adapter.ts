/**
 * Database Adapter for Deep Research
 *
 * Integrates with existing Prisma database schema for persistent storage
 * of research projects, sources, analyses, and knowledge graphs.
 */

import { PrismaClient } from '@prisma/client';
import { deepResearchConfig } from '../config';
import {
  ResearchProject,
  ResearchSource,
  AnalysisResult,
  KnowledgeEntity,
  KnowledgeRelationship,
  ResearchProjectStatus
} from '../types';

// Initialize Prisma client
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: deepResearchConfig.database.url
    }
  }
});

/**
 * Database Adapter
 *
 * Handles all database operations for the deep research system
 */
export class DatabaseAdapter {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = prisma;
  }

  /**
   * Save research project
   */
  async saveProject(project: ResearchProject): Promise<void> {
    await this.prisma.project.upsert({
      where: { id: project.id },
      update: {
        name: project.title,
        description: project.description,
        status: project.status,
        updatedAt: project.updatedAt,
        // Store deep research specific data in a JSON field
        technologies: JSON.stringify({
          deepResearch: true,
          settings: project.settings,
          metadata: project.metadata,
          tags: project.tags
        })
      },
      create: {
        id: project.id,
        name: project.title,
        description: project.description,
        appType: 'deep-research',
        status: project.status,
        userId: project.userId || 'system',
        createdAt: project.createdAt,
        updatedAt: project.updatedAt,
        technologies: JSON.stringify({
          deepResearch: true,
          settings: project.settings,
          metadata: project.metadata,
          tags: project.tags
        })
      }
    });
  }

  /**
   * Get research project by ID
   */
  async getProject(projectId: string): Promise<ResearchProject | null> {
    const project = await this.prisma.project.findUnique({
      where: { id: projectId }
    });

    if (!project) return null;

    // Parse deep research data from technologies field
    let deepResearchData: any = {};
    try {
      const tech = JSON.parse(project.technologies || '{}');
      if (tech.deepResearch) {
        deepResearchData = tech;
      }
    } catch (error) {
      console.error('Error parsing project technologies:', error);
    }

    return {
      id: project.id,
      title: project.name,
      description: project.description,
      status: project.status as ResearchProjectStatus,
      tags: deepResearchData.tags || [],
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      userId: project.userId,
      settings: deepResearchData.settings || {
        autoAnalysis: true,
        sourceTypes: ['web'],
        analysisTypes: ['summary', 'insights'],
        maxSources: 50,
        qualityThreshold: 0.7,
        biasDetection: true,
        factChecking: true,
        realTimeUpdates: false
      },
      metadata: deepResearchData.metadata || {
        totalSources: 0,
        totalAnalyses: 0,
        lastActivity: new Date()
      }
    };
  }

  /**
   * List research projects
   */
  async listProjects(userId?: string): Promise<ResearchProject[]> {
    const where = userId ? { userId, appType: 'deep-research' } : { appType: 'deep-research' };

    const projects = await this.prisma.project.findMany({
      where,
      orderBy: { updatedAt: 'desc' }
    });

    return projects.map(project => {
      let deepResearchData: any = {};
      try {
        const tech = JSON.parse(project.technologies || '{}');
        if (tech.deepResearch) {
          deepResearchData = tech;
        }
      } catch (error) {
        console.error('Error parsing project technologies:', error);
      }

      return {
        id: project.id,
        title: project.name,
        description: project.description,
        status: project.status as ResearchProjectStatus,
        tags: deepResearchData.tags || [],
        createdAt: project.createdAt,
        updatedAt: project.updatedAt,
        userId: project.userId,
        settings: deepResearchData.settings || {
          autoAnalysis: true,
          sourceTypes: ['web'],
          analysisTypes: ['summary', 'insights'],
          maxSources: 50,
          qualityThreshold: 0.7,
          biasDetection: true,
          factChecking: true,
          realTimeUpdates: false
        },
        metadata: deepResearchData.metadata || {
          totalSources: 0,
          totalAnalyses: 0,
          lastActivity: new Date()
        }
      };
    });
  }

  /**
   * Delete research project
   */
  async deleteProject(projectId: string): Promise<void> {
    await this.prisma.project.delete({
      where: { id: projectId }
    });
  }

  /**
   * Delete all project data (sources, analyses, etc.)
   */
  async deleteProjectData(projectId: string): Promise<void> {
    // Delete documents associated with the project
    await this.prisma.document.deleteMany({
      where: { projectId }
    });

    // Delete generated files
    await this.prisma.generatedFile.deleteMany({
      where: { projectId }
    });

    // Note: We'll store research-specific data in documents table
    // with metadata indicating the type (source, analysis, entity, etc.)
  }

  /**
   * Save research source
   */
  async saveSource(source: ResearchSource): Promise<void> {
    await this.prisma.document.upsert({
      where: { id: source.id },
      update: {
        content: source.content.raw,
        metadata: {
          type: 'research-source',
          sourceType: source.type,
          url: source.url,
          title: source.title,
          description: source.description,
          author: source.author,
          publishedAt: source.publishedAt,
          accessedAt: source.accessedAt,
          credibilityScore: source.credibilityScore,
          biasScore: source.biasScore,
          relevanceScore: source.relevanceScore,
          status: source.status,
          content: source.content,
          sourceMetadata: source.metadata
        },
        updatedAt: new Date()
      },
      create: {
        id: source.id,
        content: source.content.raw,
        projectId: source.projectId,
        metadata: {
          type: 'research-source',
          sourceType: source.type,
          url: source.url,
          title: source.title,
          description: source.description,
          author: source.author,
          publishedAt: source.publishedAt,
          accessedAt: source.accessedAt,
          credibilityScore: source.credibilityScore,
          biasScore: source.biasScore,
          relevanceScore: source.relevanceScore,
          status: source.status,
          content: source.content,
          sourceMetadata: source.metadata
        }
      }
    });
  }

  /**
   * Save analysis result
   */
  async saveAnalysis(analysis: AnalysisResult): Promise<void> {
    await this.prisma.document.create({
      data: {
        id: analysis.id,
        content: JSON.stringify(analysis.result),
        projectId: analysis.projectId,
        metadata: {
          type: 'analysis-result',
          sourceId: analysis.sourceId,
          analysisType: analysis.type,
          model: analysis.model,
          confidence: analysis.confidence,
          createdAt: analysis.createdAt,
          analysisMetadata: analysis.metadata
        }
      }
    });
  }

  /**
   * Save knowledge entity
   */
  async saveEntity(entity: KnowledgeEntity): Promise<void> {
    await this.prisma.document.upsert({
      where: { id: entity.id },
      update: {
        content: entity.description || entity.name,
        metadata: {
          type: 'knowledge-entity',
          entityType: entity.type,
          name: entity.name,
          description: entity.description,
          aliases: entity.aliases,
          confidence: entity.confidence,
          sources: entity.sources,
          entityMetadata: entity.metadata
        },
        updatedAt: entity.updatedAt
      },
      create: {
        id: entity.id,
        content: entity.description || entity.name,
        metadata: {
          type: 'knowledge-entity',
          entityType: entity.type,
          name: entity.name,
          description: entity.description,
          aliases: entity.aliases,
          confidence: entity.confidence,
          sources: entity.sources,
          entityMetadata: entity.metadata
        }
      }
    });
  }

  /**
   * Save knowledge relationship
   */
  async saveRelationship(relationship: KnowledgeRelationship): Promise<void> {
    await this.prisma.document.create({
      data: {
        id: relationship.id,
        content: relationship.description || `${relationship.fromEntityId} -> ${relationship.toEntityId}`,
        metadata: {
          type: 'knowledge-relationship',
          fromEntityId: relationship.fromEntityId,
          toEntityId: relationship.toEntityId,
          relationshipType: relationship.type,
          description: relationship.description,
          confidence: relationship.confidence,
          sources: relationship.sources,
          relationshipMetadata: relationship.metadata
        }
      }
    });
  }

  /**
   * Get project statistics
   */
  async getProjectStats(projectId: string): Promise<{
    totalSources: number;
    totalAnalyses: number;
    sourcesByType: Record<string, number>;
    analysesByType: Record<string, number>;
    entitiesCount: number;
    relationshipsCount: number;
  }> {
    const documents = await this.prisma.document.findMany({
      where: { projectId },
      select: { metadata: true }
    });

    const stats = {
      totalSources: 0,
      totalAnalyses: 0,
      sourcesByType: {} as Record<string, number>,
      analysesByType: {} as Record<string, number>,
      entitiesCount: 0,
      relationshipsCount: 0
    };

    documents.forEach(doc => {
      const metadata = doc.metadata as any;
      const type = metadata?.type;

      switch (type) {
        case 'research-source':
          stats.totalSources++;
          const sourceType = metadata.sourceType || 'unknown';
          stats.sourcesByType[sourceType] = (stats.sourcesByType[sourceType] || 0) + 1;
          break;
        case 'analysis-result':
          stats.totalAnalyses++;
          const analysisType = metadata.analysisType || 'unknown';
          stats.analysesByType[analysisType] = (stats.analysesByType[analysisType] || 0) + 1;
          break;
        case 'knowledge-entity':
          stats.entitiesCount++;
          break;
        case 'knowledge-relationship':
          stats.relationshipsCount++;
          break;
      }
    });

    return stats;
  }

  /**
   * Search projects
   */
  async searchProjects(query: string, userId?: string): Promise<ResearchProject[]> {
    const where = {
      AND: [
        { appType: 'deep-research' },
        userId ? { userId } : {},
        {
          OR: [
            { name: { contains: query, mode: 'insensitive' as const } },
            { description: { contains: query, mode: 'insensitive' as const } }
          ]
        }
      ]
    };

    const projects = await this.prisma.project.findMany({
      where,
      orderBy: { updatedAt: 'desc' }
    });

    return this.convertProjectsToResearchProjects(projects);
  }

  /**
   * Get projects by status
   */
  async getProjectsByStatus(status: ResearchProjectStatus, userId?: string): Promise<ResearchProject[]> {
    const where = userId
      ? { userId, appType: 'deep-research', status }
      : { appType: 'deep-research', status };

    const projects = await this.prisma.project.findMany({
      where,
      orderBy: { updatedAt: 'desc' }
    });

    return this.convertProjectsToResearchProjects(projects);
  }

  /**
   * Delete old projects
   */
  async deleteOldProjects(cutoffDate: Date): Promise<number> {
    const result = await this.prisma.project.deleteMany({
      where: {
        appType: 'deep-research',
        status: 'archived',
        updatedAt: { lt: cutoffDate }
      }
    });

    return result.count;
  }

  /**
   * Convert Prisma projects to ResearchProjects
   */
  private convertProjectsToResearchProjects(projects: any[]): ResearchProject[] {
    return projects.map(project => {
      let deepResearchData: any = {};
      try {
        const tech = JSON.parse(project.technologies || '{}');
        if (tech.deepResearch) {
          deepResearchData = tech;
        }
      } catch (error) {
        console.error('Error parsing project technologies:', error);
      }

      return {
        id: project.id,
        title: project.name,
        description: project.description,
        status: project.status as ResearchProjectStatus,
        tags: deepResearchData.tags || [],
        createdAt: project.createdAt,
        updatedAt: project.updatedAt,
        userId: project.userId,
        settings: deepResearchData.settings || {
          autoAnalysis: true,
          sourceTypes: ['web'],
          analysisTypes: ['summary', 'insights'],
          maxSources: 50,
          qualityThreshold: 0.7,
          biasDetection: true,
          factChecking: true,
          realTimeUpdates: false
        },
        metadata: deepResearchData.metadata || {
          totalSources: 0,
          totalAnalyses: 0,
          lastActivity: new Date()
        }
      };
    });
  }

  /**
   * Get all sources for a project
   */
  async getProjectSources(projectId: string): Promise<ResearchSource[]> {
    const documents = await this.prisma.document.findMany({
      where: {
        projectId,
        metadata: {
          path: ['type'],
          equals: 'research-source'
        }
      }
    });

    return documents.map(doc => this.convertDocumentToSource(doc));
  }

  /**
   * Get all analyses for a project
   */
  async getProjectAnalyses(projectId: string): Promise<AnalysisResult[]> {
    const documents = await this.prisma.document.findMany({
      where: {
        projectId,
        metadata: {
          path: ['type'],
          equals: 'analysis-result'
        }
      }
    });

    return documents.map(doc => this.convertDocumentToAnalysis(doc));
  }

  /**
   * Convert document to ResearchSource
   */
  private convertDocumentToSource(doc: any): ResearchSource {
    const metadata = doc.metadata as any;
    return {
      id: doc.id,
      projectId: doc.projectId || '',
      type: metadata.sourceType || 'web',
      url: metadata.url || '',
      title: metadata.title || '',
      description: metadata.description || '',
      author: metadata.author,
      publishedAt: metadata.publishedAt ? new Date(metadata.publishedAt) : undefined,
      accessedAt: new Date(metadata.accessedAt || doc.createdAt),
      credibilityScore: metadata.credibilityScore || 0.5,
      biasScore: metadata.biasScore || 0.5,
      relevanceScore: metadata.relevanceScore || 0.5,
      status: metadata.status || 'completed',
      metadata: metadata.sourceMetadata || {},
      content: metadata.content || {
        raw: doc.content,
        processed: doc.content
      }
    };
  }

  /**
   * Convert document to AnalysisResult
   */
  private convertDocumentToAnalysis(doc: any): AnalysisResult {
    const metadata = doc.metadata as any;
    return {
      id: doc.id,
      sourceId: metadata.sourceId || '',
      projectId: doc.projectId || '',
      type: metadata.analysisType || 'summary',
      model: metadata.model || 'unknown',
      result: JSON.parse(doc.content),
      confidence: metadata.confidence || 0.8,
      createdAt: new Date(metadata.createdAt || doc.createdAt),
      metadata: metadata.analysisMetadata || {
        processingTime: 0,
        tokensUsed: 0
      }
    };
  }

  /**
   * Close database connection
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
