/**
 * File Manager
 * 
 * Manages file storage for research data.
 */

import fs from 'fs/promises';
import path from 'path';
import { deepResearchConfig } from '../config';

export class FileManager {
  private baseDir: string;

  constructor() {
    this.baseDir = deepResearchConfig.baseDir;
  }

  async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      console.error('Error creating directory:', error);
    }
  }

  async saveFile(filePath: string, content: string): Promise<void> {
    const fullPath = path.join(this.baseDir, filePath);
    const dir = path.dirname(fullPath);
    
    await this.ensureDirectory(dir);
    await fs.writeFile(fullPath, content, 'utf8');
  }

  async readFile(filePath: string): Promise<string> {
    const fullPath = path.join(this.baseDir, filePath);
    return await fs.readFile(fullPath, 'utf8');
  }

  async deleteFile(filePath: string): Promise<void> {
    const fullPath = path.join(this.baseDir, filePath);
    await fs.unlink(fullPath);
  }
}
