/**
 * News Source Collector
 *
 * Collects news articles and current events from multiple news sources
 * including NewsAPI and RSS feeds.
 */

import { EventEmitter } from 'events';
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { browserlessService } from '@/lib/browserless';
import { deepResearchConfig } from '../config';
import { RateLimiter } from '../utils/rate-limiter';
import { TextProcessor } from '../utils/text-processing';
import {
  ResearchSource,
  ResearchSourceType,
  MultiSourceResearchOptions
} from '../types';

/**
 * News Collector
 *
 * Collects and processes news sources for research
 */
export class NewsCollector extends EventEmitter {
  private rateLimiter: RateLimiter;
  private textProcessor: TextProcessor;

  constructor(rateLimiter: RateLimiter) {
    super();
    this.rateLimiter = rateLimiter;
    this.textProcessor = new TextProcessor();
  }

  /**
   * Collect news sources for a research query
   */
  async collect(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['news']>
  ): Promise<ResearchSource[]> {
    const sources: ResearchSource[] = [];

    try {
      const collectors = [];

      // Collect from NewsAPI if available
      if (deepResearchConfig.sources.news.newsApi.apiKey) {
        collectors.push(this.collectFromNewsAPI(query, options));
      }

      // Collect from RSS feeds
      if (deepResearchConfig.sources.news.rss.enabled) {
        collectors.push(this.collectFromRSSFeeds(query, options));
      }

      // Execute collectors in parallel
      const results = await Promise.allSettled(collectors);

      results.forEach(result => {
        if (result.status === 'fulfilled') {
          sources.push(...result.value);
        } else {
          console.error('News collection error:', result.reason);
        }
      });

      // Sort by relevance and recency
      sources.sort((a, b) => {
        const relevanceDiff = b.relevanceScore - a.relevanceScore;
        if (Math.abs(relevanceDiff) > 0.1) return relevanceDiff;

        // If relevance is similar, prefer more recent articles
        const aTime = a.publishedAt?.getTime() || 0;
        const bTime = b.publishedAt?.getTime() || 0;
        return bTime - aTime;
      });

      const limitedSources = sources.slice(0, options.maxResults);

      // Emit events for collected sources
      limitedSources.forEach(source => {
        this.emit('sourceCollected', source);
      });

      return limitedSources;
    } catch (error) {
      console.error('Error in news collection:', error);
      throw error;
    }
  }

  /**
   * Collect articles from NewsAPI
   */
  private async collectFromNewsAPI(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['news']>
  ): Promise<ResearchSource[]> {
    const sources: ResearchSource[] = [];

    try {
      await this.rateLimiter.waitForServiceSlot('newsapi');

      const apiKey = deepResearchConfig.sources.news.newsApi.apiKey;
      const baseUrl = deepResearchConfig.sources.news.newsApi.baseUrl;
      const maxResults = Math.min(options.maxResults, deepResearchConfig.sources.news.newsApi.maxResults);

      // Build NewsAPI query parameters
      const params = new URLSearchParams({
        q: query,
        apiKey,
        pageSize: maxResults.toString(),
        sortBy: 'relevancy',
        language: options.languages?.[0] || 'en'
      });

      // Add source restrictions if specified
      if (options.sources && options.sources.length > 0) {
        params.append('sources', options.sources.join(','));
      }

      const url = `${baseUrl}/everything?${params.toString()}`;

      const response = await fetch(url, {
        timeout: deepResearchConfig.sources.news.rss.timeout
      });

      if (!response.ok) {
        throw new Error(`NewsAPI error: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.status !== 'ok') {
        throw new Error(`NewsAPI error: ${data.message}`);
      }

      // Process each article
      for (const article of data.articles || []) {
        try {
          const source = await this.processNewsArticle(article, query, 'newsapi');
          if (source) {
            sources.push(source);
          }
        } catch (error) {
          console.error('Error processing news article:', error);
          continue;
        }
      }

      return sources;
    } catch (error) {
      console.error('Error collecting from NewsAPI:', error);
      return [];
    }
  }

  /**
   * Collect articles from RSS feeds
   */
  private async collectFromRSSFeeds(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['news']>
  ): Promise<ResearchSource[]> {
    const sources: ResearchSource[] = [];

    try {
      // Popular news RSS feeds
      const rssFeeds = [
        'https://rss.cnn.com/rss/edition.rss',
        'https://feeds.bbci.co.uk/news/rss.xml',
        'https://www.reuters.com/rssFeed/topNews',
        'https://rss.nytimes.com/services/xml/rss/nyt/HomePage.xml',
        'https://feeds.npr.org/1001/rss.xml'
      ];

      const maxFeeds = Math.min(rssFeeds.length, deepResearchConfig.sources.news.rss.maxFeeds);
      const feedsToProcess = rssFeeds.slice(0, maxFeeds);

      for (const feedUrl of feedsToProcess) {
        try {
          await this.rateLimiter.waitForServiceSlot('rss');
          const feedSources = await this.processRSSFeed(feedUrl, query, options);
          sources.push(...feedSources);
        } catch (error) {
          console.error(`Error processing RSS feed ${feedUrl}:`, error);
          continue;
        }
      }

      return sources;
    } catch (error) {
      console.error('Error collecting from RSS feeds:', error);
      return [];
    }
  }

  /**
   * Process RSS feed
   */
  private async processRSSFeed(
    feedUrl: string,
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['news']>
  ): Promise<ResearchSource[]> {
    const sources: ResearchSource[] = [];

    try {
      // Fetch RSS feed content
      const response = await fetch(feedUrl, {
        timeout: deepResearchConfig.sources.news.rss.timeout
      });

      if (!response.ok) {
        throw new Error(`RSS feed error: ${response.statusText}`);
      }

      const xmlContent = await response.text();
      const articles = this.parseRSSFeed(xmlContent);

      // Filter articles by relevance to query
      const relevantArticles = await this.filterRelevantArticles(articles, query);

      // Process relevant articles
      for (const article of relevantArticles.slice(0, Math.ceil(options.maxResults / 3))) {
        try {
          const source = await this.processNewsArticle(article, query, 'rss');
          if (source) {
            sources.push(source);
          }
        } catch (error) {
          console.error('Error processing RSS article:', error);
          continue;
        }
      }

      return sources;
    } catch (error) {
      console.error(`Error processing RSS feed ${feedUrl}:`, error);
      return [];
    }
  }

  /**
   * Parse RSS feed XML
   */
  private parseRSSFeed(xmlContent: string): any[] {
    const articles: any[] = [];

    try {
      // Simple RSS parsing - extract items
      const itemRegex = /<item[^>]*>(.*?)<\/item>/gs;
      let match;

      while ((match = itemRegex.exec(xmlContent)) !== null) {
        const itemXml = match[1];

        const article = {
          title: this.extractXmlValue(itemXml, 'title'),
          description: this.extractXmlValue(itemXml, 'description'),
          link: this.extractXmlValue(itemXml, 'link'),
          pubDate: this.extractXmlValue(itemXml, 'pubDate'),
          author: this.extractXmlValue(itemXml, 'author') || this.extractXmlValue(itemXml, 'dc:creator'),
          category: this.extractXmlValue(itemXml, 'category')
        };

        if (article.title && article.link) {
          articles.push(article);
        }
      }
    } catch (error) {
      console.error('Error parsing RSS feed:', error);
    }

    return articles;
  }

  /**
   * Extract value from XML
   */
  private extractXmlValue(xml: string, tag: string): string {
    const regex = new RegExp(`<${tag}[^>]*><!\\[CDATA\\[(.*?)\\]\\]><\\/${tag}>|<${tag}[^>]*>(.*?)<\\/${tag}>`, 's');
    const match = xml.match(regex);
    return match ? (match[1] || match[2] || '').trim() : '';
  }

  /**
   * Filter articles by relevance to query
   */
  private async filterRelevantArticles(articles: any[], query: string): Promise<any[]> {
    const relevantArticles: any[] = [];

    for (const article of articles) {
      try {
        const content = `${article.title} ${article.description}`;
        const relevanceScore = await this.calculateRelevanceScore(content, query);

        if (relevanceScore > 0.3) {
          article.relevanceScore = relevanceScore;
          relevantArticles.push(article);
        }
      } catch (error) {
        // Skip articles that can't be processed
        continue;
      }
    }

    return relevantArticles.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Process news article into ResearchSource
   */
  private async processNewsArticle(
    article: any,
    query: string,
    sourceType: string
  ): Promise<ResearchSource | null> {
    try {
      const title = this.textProcessor.cleanText(article.title || '');
      const description = this.textProcessor.cleanText(article.description || article.content || '');
      const url = article.url || article.link || '';

      if (!title || !url) {
        return null;
      }

      // Try to get full article content if URL is available
      let fullContent = description;
      try {
        if (url && url.startsWith('http')) {
          await this.rateLimiter.waitForServiceSlot('browserless');

          const result = await browserlessService.getHtml({
            url,
            timeout: 15000,
            stealth: true
          });

          if (result.success && result.data) {
            const extractedText = this.textProcessor.extractTextFromHtml(result.data);
            if (extractedText.length > description.length) {
              fullContent = extractedText;
            }
          }
        }
      } catch (error) {
        // Use description if full content extraction fails
        console.warn(`Could not extract full content from ${url}:`, error);
      }

      // Calculate relevance and credibility scores
      const relevanceScore = article.relevanceScore || await this.calculateRelevanceScore(fullContent, query);
      const credibilityScore = await this.calculateCredibilityScore(url, fullContent);

      // Parse publish date
      let publishedAt: Date | undefined;
      if (article.publishedAt || article.pubDate) {
        publishedAt = new Date(article.publishedAt || article.pubDate);
      }

      const source: ResearchSource = {
        id: `news_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        projectId: '',
        type: 'news' as ResearchSourceType,
        url,
        title,
        description: description.substring(0, 200) + '...',
        author: article.author || article.source?.name || 'Unknown',
        publishedAt,
        accessedAt: new Date(),
        credibilityScore,
        biasScore: 0.4, // News articles may have moderate bias
        relevanceScore,
        status: 'completed',
        metadata: {
          sourceType,
          source: article.source?.name || 'RSS Feed',
          category: article.category,
          language: this.textProcessor.detectLanguage(fullContent)
        },
        content: {
          raw: fullContent,
          processed: this.textProcessor.cleanText(fullContent),
          summary: await this.generateSummary(fullContent),
          keyPoints: await this.extractKeyPoints(fullContent)
        }
      };

      return source;
    } catch (error) {
      console.error('Error processing news article:', error);
      return null;
    }
  }

  /**
   * Calculate relevance score using AI
   */
  private async calculateRelevanceScore(content: string, query: string): Promise<number> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.insights),
        temperature: 0.1,
        prompt: `Rate how relevant this news content is to the research query on a scale of 0.0 to 1.0.

Query: "${query}"
Content: "${content.substring(0, 1500)}..."

Consider:
- Direct relevance to the query topic
- Timeliness and currency of information
- Factual content vs opinion

Return only the numeric score (e.g., 0.7).`,
      });

      const score = parseFloat(text.trim());
      return isNaN(score) ? 0.5 : Math.max(0, Math.min(1, score));
    } catch (error) {
      console.error('Error calculating relevance score:', error);
      return 0.5;
    }
  }

  /**
   * Calculate credibility score for news source
   */
  private async calculateCredibilityScore(url: string, content: string): Promise<number> {
    try {
      const domain = new URL(url).hostname.toLowerCase();

      // Base credibility scores for known news sources
      const trustedSources = {
        'reuters.com': 0.95,
        'bbc.com': 0.9,
        'npr.org': 0.9,
        'pbs.org': 0.9,
        'apnews.com': 0.95,
        'cnn.com': 0.8,
        'nytimes.com': 0.85,
        'washingtonpost.com': 0.85,
        'wsj.com': 0.85,
        'theguardian.com': 0.8
      };

      let baseScore = 0.6; // Default for unknown sources

      for (const [trustedDomain, score] of Object.entries(trustedSources)) {
        if (domain.includes(trustedDomain)) {
          baseScore = score;
          break;
        }
      }

      // Adjust based on content quality
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.credibility),
        temperature: 0.1,
        prompt: `Assess the credibility of this news content based on:
- Factual reporting vs opinion
- Use of sources and citations
- Balanced presentation
- Professional writing quality

Content: "${content.substring(0, 1000)}..."

Rate from 0.0 to 1.0. Return only the numeric score.`,
      });

      const contentScore = parseFloat(text.trim()) || 0.6;

      // Combine base and content scores
      return (baseScore * 0.6 + contentScore * 0.4);
    } catch (error) {
      console.error('Error calculating credibility score:', error);
      return 0.6;
    }
  }

  /**
   * Generate summary using AI
   */
  private async generateSummary(content: string): Promise<string> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.summary),
        temperature: 0.3,
        prompt: `Summarize this news article in 2-3 sentences focusing on the key facts and developments:

${content.substring(0, 2000)}

Summary:`,
      });

      return text.trim();
    } catch (error) {
      console.error('Error generating summary:', error);
      return content.substring(0, 200) + '...';
    }
  }

  /**
   * Extract key points using AI
   */
  private async extractKeyPoints(content: string): Promise<string[]> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.insights),
        temperature: 0.3,
        prompt: `Extract 3-5 key facts or developments from this news article:

${content.substring(0, 2000)}

Return as a simple list, one point per line:`,
      });

      return text
        .split('\n')
        .map(point => point.replace(/^[-•*]\s*/, '').trim())
        .filter(point => point.length > 0)
        .slice(0, 5);
    } catch (error) {
      console.error('Error extracting key points:', error);
      return [];
    }
  }
}
