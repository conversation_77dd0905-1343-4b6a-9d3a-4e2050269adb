/**
 * Web Source Collector
 *
 * Collects web sources using Browserless API and existing web research services.
 * Implements intelligent web scraping with content extraction and quality assessment.
 */

import { EventEmitter } from 'events';
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { browserlessService } from '@/lib/browserless';
import { webResearchService } from '@/lib/app-dev/web-research';
import { generateEmbedding } from '@/lib/ai-vector/embeddings';
import { deepResearchConfig } from '../config';
import { RateLimiter } from '../utils/rate-limiter';
import { TextProcessor } from '../utils/text-processing';
import {
  ResearchSource,
  ResearchSourceType,
  MultiSourceResearchOptions
} from '../types';

/**
 * Web Collector
 *
 * Collects and processes web sources for research
 */
export class WebCollector extends EventEmitter {
  private rateLimiter: RateLimiter;
  private textProcessor: TextProcessor;

  constructor(rateLimiter: RateLimiter) {
    super();
    this.rateLimiter = rateLimiter;
    this.textProcessor = new TextProcessor();
  }

  /**
   * Collect web sources for a research query
   */
  async collect(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['web']>
  ): Promise<ResearchSource[]> {
    const sources: ResearchSource[] = [];

    try {
      // Step 1: Perform web search to find relevant URLs
      const searchResults = await this.performWebSearch(query, options);

      // Step 2: Process each URL found
      for (const url of searchResults) {
        try {
          await this.rateLimiter.waitForSlot();

          const source = await this.processWebPage(url, query, options);
          if (source) {
            sources.push(source);
            this.emit('sourceCollected', source);
          }
        } catch (error) {
          console.error(`Error processing URL ${url}:`, error);
          continue;
        }
      }

      return sources;
    } catch (error) {
      console.error('Error in web collection:', error);
      throw error;
    }
  }

  /**
   * Perform web search to find relevant URLs
   */
  private async performWebSearch(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['web']>
  ): Promise<string[]> {
    try {
      // Use AI to generate search queries
      const searchQueries = await this.generateSearchQueries(query);
      const allUrls: string[] = [];

      for (const searchQuery of searchQueries) {
        // Use Browserless to perform Google search
        const searchResults = await this.performGoogleSearch(searchQuery, options);
        allUrls.push(...searchResults);
      }

      // Remove duplicates and filter by domain restrictions
      const uniqueUrls = Array.from(new Set(allUrls));
      return this.filterUrlsByDomains(uniqueUrls, options);
    } catch (error) {
      console.error('Error performing web search:', error);
      return [];
    }
  }

  /**
   * Generate multiple search queries using AI
   */
  private async generateSearchQueries(originalQuery: string): Promise<string[]> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.insights),
        temperature: deepResearchConfig.ai.temperature.insights,
        prompt: `Generate 3-5 diverse search queries to comprehensively research the topic: "${originalQuery}"

Requirements:
- Each query should explore different aspects or angles
- Include both broad and specific queries
- Consider synonyms and related terms
- Keep queries concise and search-engine friendly

Return only the queries, one per line, without numbering or formatting.`,
      });

      return text
        .split('\n')
        .map(q => q.trim())
        .filter(q => q.length > 0)
        .slice(0, 5); // Limit to 5 queries
    } catch (error) {
      console.error('Error generating search queries:', error);
      return [originalQuery]; // Fallback to original query
    }
  }

  /**
   * Perform Google search using Browserless
   */
  private async performGoogleSearch(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['web']>
  ): Promise<string[]> {
    try {
      const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}&num=${Math.min(options.maxResults, 20)}`;

      const result = await browserlessService.getHtml({
        url: searchUrl,
        waitForSelector: '#search',
        timeout: deepResearchConfig.sources.web.timeout,
        userAgent: deepResearchConfig.sources.web.userAgent,
        stealth: true
      });

      if (!result.success || !result.data) {
        throw new Error('Failed to get search results');
      }

      // Extract URLs from search results
      return this.extractUrlsFromSearchResults(result.data);
    } catch (error) {
      console.error(`Error performing Google search for "${query}":`, error);
      return [];
    }
  }

  /**
   * Extract URLs from Google search results HTML
   */
  private extractUrlsFromSearchResults(html: string): string[] {
    const urls: string[] = [];

    // Regex to match Google search result URLs
    const urlRegex = /<a[^>]+href="\/url\?q=([^&"]+)[^"]*"[^>]*>/g;
    let match;

    while ((match = urlRegex.exec(html)) !== null) {
      try {
        const url = decodeURIComponent(match[1]);

        // Filter out Google's own URLs and invalid URLs
        if (this.isValidUrl(url) && !this.isGoogleUrl(url)) {
          urls.push(url);
        }
      } catch (error) {
        // Skip invalid URLs
        continue;
      }
    }

    return urls;
  }

  /**
   * Check if URL is valid
   */
  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * Check if URL is a Google URL
   */
  private isGoogleUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.includes('google.com') ||
             urlObj.hostname.includes('youtube.com') ||
             urlObj.hostname.includes('googlevideo.com');
    } catch {
      return false;
    }
  }

  /**
   * Filter URLs by domain restrictions
   */
  private filterUrlsByDomains(
    urls: string[],
    options: NonNullable<MultiSourceResearchOptions['sources']['web']>
  ): string[] {
    return urls.filter(url => {
      try {
        const urlObj = new URL(url);
        const domain = urlObj.hostname;

        // Check excluded domains
        if (options.excludeDomains?.some(excluded => domain.includes(excluded))) {
          return false;
        }

        // Check included domains (if specified)
        if (options.domains && options.domains.length > 0) {
          return options.domains.some(included => domain.includes(included));
        }

        return true;
      } catch {
        return false;
      }
    }).slice(0, options.maxResults);
  }

  /**
   * Process a single web page
   */
  private async processWebPage(
    url: string,
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['web']>
  ): Promise<ResearchSource | null> {
    try {
      // Use existing web research service to get page content
      const webResearch = await webResearchService.performResearch({
        url,
        resultType: 'html',
        timeout: deepResearchConfig.sources.web.timeout,
        javascript: true,
        stealth: true
      });

      if (!webResearch.content) {
        return null;
      }

      // Extract and process text content
      const textContent = this.textProcessor.extractTextFromHtml(webResearch.content);
      const cleanedText = this.textProcessor.cleanText(textContent);

      if (cleanedText.length < 100) {
        return null; // Skip pages with too little content
      }

      // Calculate relevance score
      const relevanceScore = await this.calculateRelevanceScore(cleanedText, query);

      if (relevanceScore < deepResearchConfig.quality.credibilityWeights.contentQuality) {
        return null; // Skip irrelevant content
      }

      // Generate summary and key points
      const summary = await this.generateSummary(cleanedText);
      const keyPoints = await this.extractKeyPoints(cleanedText);

      // Create research source
      const source: ResearchSource = {
        id: `web_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        projectId: '', // Will be set by the calling code
        type: 'web' as ResearchSourceType,
        url,
        title: webResearch.metadata?.title || this.textProcessor.extractTitle(webResearch.content) || url,
        description: summary.substring(0, 200) + '...',
        accessedAt: new Date(),
        credibilityScore: await this.calculateCredibilityScore(url, cleanedText),
        biasScore: 0.5, // Will be calculated by bias detector
        relevanceScore,
        status: 'completed',
        metadata: {
          contentLength: cleanedText.length,
          extractedAt: new Date(),
          userAgent: deepResearchConfig.sources.web.userAgent
        },
        content: {
          raw: webResearch.content,
          processed: cleanedText,
          summary,
          keyPoints
        }
      };

      return source;
    } catch (error) {
      console.error(`Error processing web page ${url}:`, error);
      return null;
    }
  }

  /**
   * Calculate relevance score using AI
   */
  private async calculateRelevanceScore(content: string, query: string): Promise<number> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.insights),
        temperature: 0.1,
        prompt: `Analyze how relevant this content is to the research query.

Query: "${query}"

Content: "${content.substring(0, 2000)}..."

Rate the relevance on a scale of 0.0 to 1.0 where:
- 1.0 = Highly relevant, directly addresses the query
- 0.7 = Moderately relevant, contains useful information
- 0.5 = Somewhat relevant, tangentially related
- 0.3 = Low relevance, minimal connection
- 0.0 = Not relevant at all

Return only the numeric score (e.g., 0.8).`,
      });

      const score = parseFloat(text.trim());
      return isNaN(score) ? 0.5 : Math.max(0, Math.min(1, score));
    } catch (error) {
      console.error('Error calculating relevance score:', error);
      return 0.5; // Default score
    }
  }

  /**
   * Calculate credibility score
   */
  private async calculateCredibilityScore(url: string, content: string): Promise<number> {
    try {
      const domain = new URL(url).hostname;

      // Basic domain authority scoring
      let domainScore = 0.5;
      const trustedDomains = [
        'wikipedia.org', 'edu', 'gov', 'nature.com', 'science.org',
        'reuters.com', 'bbc.com', 'npr.org', 'pbs.org'
      ];

      if (trustedDomains.some(trusted => domain.includes(trusted))) {
        domainScore = 0.9;
      } else if (domain.includes('.edu') || domain.includes('.gov')) {
        domainScore = 0.8;
      }

      // Content quality assessment using AI
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.credibility),
        temperature: 0.1,
        prompt: `Assess the credibility of this content based on:
- Writing quality and professionalism
- Presence of citations or references
- Factual tone vs opinion
- Logical structure and coherence

Content: "${content.substring(0, 1500)}..."

Rate credibility from 0.0 to 1.0. Return only the numeric score.`,
      });

      const contentScore = parseFloat(text.trim()) || 0.5;

      // Combine domain and content scores
      return (domainScore * 0.4 + contentScore * 0.6);
    } catch (error) {
      console.error('Error calculating credibility score:', error);
      return 0.5;
    }
  }

  /**
   * Generate summary using AI
   */
  private async generateSummary(content: string): Promise<string> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.summary),
        temperature: deepResearchConfig.ai.temperature.summary,
        prompt: `Summarize this content in 2-3 concise sentences that capture the main points:

${content.substring(0, 3000)}

Summary:`,
      });

      return text.trim();
    } catch (error) {
      console.error('Error generating summary:', error);
      return content.substring(0, 200) + '...';
    }
  }

  /**
   * Extract key points using AI
   */
  private async extractKeyPoints(content: string): Promise<string[]> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.insights),
        temperature: deepResearchConfig.ai.temperature.insights,
        prompt: `Extract 3-5 key points from this content. Each point should be a concise, standalone insight.

${content.substring(0, 3000)}

Return the key points as a simple list, one per line:`,
      });

      return text
        .split('\n')
        .map(point => point.replace(/^[-•*]\s*/, '').trim())
        .filter(point => point.length > 0)
        .slice(0, 5);
    } catch (error) {
      console.error('Error extracting key points:', error);
      return [];
    }
  }
}
