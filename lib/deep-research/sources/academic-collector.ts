/**
 * Academic Source Collector
 * 
 * Collects academic papers and scholarly content from multiple databases
 * including arXiv, PubMed, and Google Scholar.
 */

import { EventEmitter } from 'events';
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { browserlessService } from '@/lib/browserless';
import { deepResearchConfig } from '../config';
import { RateLimiter } from '../utils/rate-limiter';
import { TextProcessor } from '../utils/text-processing';
import {
  ResearchSource,
  ResearchSourceType,
  MultiSourceResearchOptions
} from '../types';

/**
 * Academic Collector
 * 
 * Collects and processes academic sources for research
 */
export class AcademicCollector extends EventEmitter {
  private rateLimiter: RateLimiter;
  private textProcessor: TextProcessor;

  constructor(rateLimiter: RateLimiter) {
    super();
    this.rateLimiter = rateLimiter;
    this.textProcessor = new TextProcessor();
  }

  /**
   * Collect academic sources for a research query
   */
  async collect(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['academic']>
  ): Promise<ResearchSource[]> {
    const sources: ResearchSource[] = [];

    try {
      // Collect from each enabled database
      const collectors = [];

      if (options.databases.includes('arxiv')) {
        collectors.push(this.collectFromArxiv(query, options));
      }

      if (options.databases.includes('pubmed')) {
        collectors.push(this.collectFromPubmed(query, options));
      }

      if (options.databases.includes('scholar')) {
        collectors.push(this.collectFromScholar(query, options));
      }

      // Execute all collectors in parallel
      const results = await Promise.allSettled(collectors);
      
      results.forEach(result => {
        if (result.status === 'fulfilled') {
          sources.push(...result.value);
        } else {
          console.error('Academic collection error:', result.reason);
        }
      });

      // Sort by relevance and limit results
      sources.sort((a, b) => b.relevanceScore - a.relevanceScore);
      const limitedSources = sources.slice(0, options.maxResults);

      // Emit events for collected sources
      limitedSources.forEach(source => {
        this.emit('sourceCollected', source);
      });

      return limitedSources;
    } catch (error) {
      console.error('Error in academic collection:', error);
      throw error;
    }
  }

  /**
   * Collect papers from arXiv
   */
  private async collectFromArxiv(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['academic']>
  ): Promise<ResearchSource[]> {
    const sources: ResearchSource[] = [];

    try {
      await this.rateLimiter.waitForServiceSlot('arxiv');

      // Build arXiv API query
      const searchQuery = this.buildArxivQuery(query);
      const maxResults = Math.min(options.maxResults, deepResearchConfig.sources.academic.arxiv.maxResults);
      
      const apiUrl = `${deepResearchConfig.sources.academic.arxiv.baseUrl}?search_query=${encodeURIComponent(searchQuery)}&start=0&max_results=${maxResults}&sortBy=relevance&sortOrder=descending`;

      // Fetch from arXiv API
      const response = await fetch(apiUrl, {
        timeout: deepResearchConfig.sources.academic.arxiv.timeout
      });

      if (!response.ok) {
        throw new Error(`arXiv API error: ${response.statusText}`);
      }

      const xmlData = await response.text();
      const papers = this.parseArxivResponse(xmlData);

      // Process each paper
      for (const paper of papers) {
        try {
          const source = await this.processArxivPaper(paper, query);
          if (source) {
            sources.push(source);
          }
        } catch (error) {
          console.error('Error processing arXiv paper:', error);
          continue;
        }
      }

      return sources;
    } catch (error) {
      console.error('Error collecting from arXiv:', error);
      return [];
    }
  }

  /**
   * Build arXiv search query
   */
  private buildArxivQuery(query: string): string {
    // Convert natural language query to arXiv search format
    const keywords = query.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);

    // Search in title, abstract, and keywords
    return keywords.map(keyword => `all:${keyword}`).join(' AND ');
  }

  /**
   * Parse arXiv XML response
   */
  private parseArxivResponse(xmlData: string): any[] {
    const papers: any[] = [];

    try {
      // Simple XML parsing for arXiv entries
      const entryRegex = /<entry>(.*?)<\/entry>/gs;
      let match;

      while ((match = entryRegex.exec(xmlData)) !== null) {
        const entryXml = match[1];
        
        const paper = {
          id: this.extractXmlValue(entryXml, 'id'),
          title: this.extractXmlValue(entryXml, 'title'),
          summary: this.extractXmlValue(entryXml, 'summary'),
          authors: this.extractArxivAuthors(entryXml),
          published: this.extractXmlValue(entryXml, 'published'),
          updated: this.extractXmlValue(entryXml, 'updated'),
          categories: this.extractArxivCategories(entryXml),
          pdfUrl: this.extractArxivPdfUrl(entryXml)
        };

        if (paper.id && paper.title) {
          papers.push(paper);
        }
      }
    } catch (error) {
      console.error('Error parsing arXiv response:', error);
    }

    return papers;
  }

  /**
   * Extract value from XML
   */
  private extractXmlValue(xml: string, tag: string): string {
    const regex = new RegExp(`<${tag}[^>]*>(.*?)<\/${tag}>`, 's');
    const match = xml.match(regex);
    return match ? match[1].trim() : '';
  }

  /**
   * Extract authors from arXiv entry
   */
  private extractArxivAuthors(entryXml: string): string[] {
    const authors: string[] = [];
    const authorRegex = /<author[^>]*>.*?<name>(.*?)<\/name>.*?<\/author>/gs;
    let match;

    while ((match = authorRegex.exec(entryXml)) !== null) {
      authors.push(match[1].trim());
    }

    return authors;
  }

  /**
   * Extract categories from arXiv entry
   */
  private extractArxivCategories(entryXml: string): string[] {
    const categories: string[] = [];
    const categoryRegex = /<category[^>]*term="([^"]+)"/g;
    let match;

    while ((match = categoryRegex.exec(entryXml)) !== null) {
      categories.push(match[1]);
    }

    return categories;
  }

  /**
   * Extract PDF URL from arXiv entry
   */
  private extractArxivPdfUrl(entryXml: string): string {
    const linkRegex = /<link[^>]*href="([^"]+)"[^>]*type="application\/pdf"/;
    const match = entryXml.match(linkRegex);
    return match ? match[1] : '';
  }

  /**
   * Process arXiv paper into ResearchSource
   */
  private async processArxivPaper(paper: any, query: string): Promise<ResearchSource | null> {
    try {
      // Calculate relevance score
      const relevanceScore = await this.calculateRelevanceScore(
        `${paper.title} ${paper.summary}`,
        query
      );

      if (relevanceScore < 0.3) {
        return null; // Skip irrelevant papers
      }

      // Clean and process content
      const cleanTitle = this.textProcessor.cleanText(paper.title);
      const cleanSummary = this.textProcessor.cleanText(paper.summary);

      const source: ResearchSource = {
        id: `arxiv_${paper.id.split('/').pop()}`,
        projectId: '', // Will be set by calling code
        type: 'academic' as ResearchSourceType,
        url: paper.id,
        title: cleanTitle,
        description: cleanSummary.substring(0, 200) + '...',
        author: paper.authors.join(', '),
        publishedAt: new Date(paper.published),
        accessedAt: new Date(),
        credibilityScore: 0.85, // arXiv papers generally have high credibility
        biasScore: 0.2, // Academic papers typically have low bias
        relevanceScore,
        status: 'completed',
        metadata: {
          database: 'arxiv',
          categories: paper.categories,
          pdfUrl: paper.pdfUrl,
          updated: paper.updated,
          authors: paper.authors
        },
        content: {
          raw: `${paper.title}\n\n${paper.summary}`,
          processed: `${cleanTitle}\n\n${cleanSummary}`,
          summary: cleanSummary,
          keyPoints: await this.extractKeyPoints(cleanSummary)
        }
      };

      return source;
    } catch (error) {
      console.error('Error processing arXiv paper:', error);
      return null;
    }
  }

  /**
   * Collect papers from PubMed
   */
  private async collectFromPubmed(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['academic']>
  ): Promise<ResearchSource[]> {
    const sources: ResearchSource[] = [];

    try {
      if (!deepResearchConfig.sources.academic.pubmed.apiKey) {
        console.warn('PubMed API key not configured, skipping PubMed collection');
        return [];
      }

      await this.rateLimiter.waitForServiceSlot('pubmed');

      // Build PubMed search query
      const searchQuery = this.buildPubmedQuery(query, options);
      const maxResults = Math.min(options.maxResults, deepResearchConfig.sources.academic.pubmed.maxResults);

      // Search PubMed
      const searchUrl = `${deepResearchConfig.sources.academic.pubmed.baseUrl}/esearch.fcgi?db=pubmed&term=${encodeURIComponent(searchQuery)}&retmax=${maxResults}&retmode=json&api_key=${deepResearchConfig.sources.academic.pubmed.apiKey}`;

      const searchResponse = await fetch(searchUrl, {
        timeout: deepResearchConfig.sources.academic.pubmed.timeout
      });

      if (!searchResponse.ok) {
        throw new Error(`PubMed search error: ${searchResponse.statusText}`);
      }

      const searchData = await searchResponse.json();
      const pmids = searchData.esearchresult?.idlist || [];

      if (pmids.length === 0) {
        return [];
      }

      // Fetch details for found papers
      const detailsUrl = `${deepResearchConfig.sources.academic.pubmed.baseUrl}/esummary.fcgi?db=pubmed&id=${pmids.join(',')}&retmode=json&api_key=${deepResearchConfig.sources.academic.pubmed.apiKey}`;

      const detailsResponse = await fetch(detailsUrl, {
        timeout: deepResearchConfig.sources.academic.pubmed.timeout
      });

      if (!detailsResponse.ok) {
        throw new Error(`PubMed details error: ${detailsResponse.statusText}`);
      }

      const detailsData = await detailsResponse.json();
      const papers = Object.values(detailsData.result || {}).filter((paper: any) => paper.uid);

      // Process each paper
      for (const paper of papers as any[]) {
        try {
          const source = await this.processPubmedPaper(paper, query);
          if (source) {
            sources.push(source);
          }
        } catch (error) {
          console.error('Error processing PubMed paper:', error);
          continue;
        }
      }

      return sources;
    } catch (error) {
      console.error('Error collecting from PubMed:', error);
      return [];
    }
  }

  /**
   * Build PubMed search query
   */
  private buildPubmedQuery(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['academic']>
  ): string {
    let searchQuery = query;

    // Add date range if specified
    if (options.dateRange) {
      const fromDate = options.dateRange.from.toISOString().split('T')[0].replace(/-/g, '/');
      const toDate = options.dateRange.to.toISOString().split('T')[0].replace(/-/g, '/');
      searchQuery += ` AND ("${fromDate}"[Date - Publication] : "${toDate}"[Date - Publication])`;
    }

    return searchQuery;
  }

  /**
   * Process PubMed paper into ResearchSource
   */
  private async processPubmedPaper(paper: any, query: string): Promise<ResearchSource | null> {
    try {
      const title = paper.title || '';
      const authors = paper.authors?.map((author: any) => author.name).join(', ') || '';
      const abstract = paper.abstract || '';
      const pmid = paper.uid;

      // Calculate relevance score
      const relevanceScore = await this.calculateRelevanceScore(
        `${title} ${abstract}`,
        query
      );

      if (relevanceScore < 0.3) {
        return null;
      }

      const source: ResearchSource = {
        id: `pubmed_${pmid}`,
        projectId: '',
        type: 'academic' as ResearchSourceType,
        url: `https://pubmed.ncbi.nlm.nih.gov/${pmid}/`,
        title: this.textProcessor.cleanText(title),
        description: this.textProcessor.cleanText(abstract).substring(0, 200) + '...',
        author: authors,
        publishedAt: new Date(paper.pubdate || Date.now()),
        accessedAt: new Date(),
        credibilityScore: 0.9, // PubMed papers have very high credibility
        biasScore: 0.15, // Medical papers typically have low bias
        relevanceScore,
        status: 'completed',
        metadata: {
          database: 'pubmed',
          pmid,
          journal: paper.source,
          doi: paper.elocationid
        },
        content: {
          raw: `${title}\n\n${abstract}`,
          processed: `${this.textProcessor.cleanText(title)}\n\n${this.textProcessor.cleanText(abstract)}`,
          summary: this.textProcessor.cleanText(abstract),
          keyPoints: await this.extractKeyPoints(abstract)
        }
      };

      return source;
    } catch (error) {
      console.error('Error processing PubMed paper:', error);
      return null;
    }
  }

  /**
   * Collect papers from Google Scholar (limited due to restrictions)
   */
  private async collectFromScholar(
    query: string,
    options: NonNullable<MultiSourceResearchOptions['sources']['academic']>
  ): Promise<ResearchSource[]> {
    // Google Scholar has strict anti-scraping measures
    // This is a placeholder for potential future implementation
    console.warn('Google Scholar collection not implemented due to access restrictions');
    return [];
  }

  /**
   * Calculate relevance score using AI
   */
  private async calculateRelevanceScore(content: string, query: string): Promise<number> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.insights),
        temperature: 0.1,
        prompt: `Rate the relevance of this academic content to the research query on a scale of 0.0 to 1.0.

Query: "${query}"
Content: "${content.substring(0, 1000)}..."

Consider:
- Direct relevance to the query topic
- Quality and depth of information
- Academic rigor and methodology

Return only the numeric score (e.g., 0.8).`,
      });

      const score = parseFloat(text.trim());
      return isNaN(score) ? 0.5 : Math.max(0, Math.min(1, score));
    } catch (error) {
      console.error('Error calculating relevance score:', error);
      return 0.5;
    }
  }

  /**
   * Extract key points using AI
   */
  private async extractKeyPoints(content: string): Promise<string[]> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.insights),
        temperature: 0.3,
        prompt: `Extract 3-5 key research findings or insights from this academic content:

${content.substring(0, 2000)}

Return as a simple list, one point per line:`,
      });

      return text
        .split('\n')
        .map(point => point.replace(/^[-•*]\s*/, '').trim())
        .filter(point => point.length > 0)
        .slice(0, 5);
    } catch (error) {
      console.error('Error extracting key points:', error);
      return [];
    }
  }
}
