/**
 * Synthesis Engine
 * 
 * Synthesizes insights from multiple research sources.
 */

import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { AIAnalyzer } from '../analysis/ai-analyzer';
import { deepResearchConfig } from '../config';
import { ResearchSource, AnalysisResult } from '../types';

export class SynthesisEngine {
  private aiAnalyzer: AIAnalyzer;

  constructor(aiAnalyzer: AIAnalyzer) {
    this.aiAnalyzer = aiAnalyzer;
  }

  async synthesizeFindings(
    sources: ResearchSource[],
    analyses: AnalysisResult[]
  ): Promise<any> {
    try {
      const summaries = sources.map(s => s.content.summary).join('\n\n');
      
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.insights),
        temperature: 0.7,
        prompt: `Synthesize insights from these research sources:

${summaries.substring(0, 4000)}

Provide:
1. Key themes and patterns
2. Contradictions or disagreements
3. Research gaps
4. Overall conclusions
5. Future research directions

Format as structured JSON.`,
      });

      return JSON.parse(text);
    } catch (error) {
      console.error('Error synthesizing findings:', error);
      return { synthesis: 'Error generating synthesis' };
    }
  }
}
