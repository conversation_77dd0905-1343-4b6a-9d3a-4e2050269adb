/**
 * Knowledge Graph
 * 
 * Manages the construction and querying of knowledge graphs from research data.
 */

import { EventEmitter } from 'events';
import { DatabaseAdapter } from '../storage/database-adapter';
import { KnowledgeEntity, KnowledgeRelationship } from '../types';

export class KnowledgeGraph extends EventEmitter {
  private databaseAdapter: DatabaseAdapter;

  constructor(databaseAdapter: DatabaseAdapter) {
    super();
    this.databaseAdapter = databaseAdapter;
  }

  async addEntity(entity: KnowledgeEntity): Promise<void> {
    await this.databaseAdapter.saveEntity(entity);
    this.emit('entityAdded', entity);
  }

  async addRelationship(relationship: KnowledgeRelationship): Promise<void> {
    await this.databaseAdapter.saveRelationship(relationship);
    this.emit('relationshipAdded', relationship);
  }
}
