/**
 * Deep Research Library Configuration
 * 
 * This file contains configuration settings for the deep research library.
 */

import path from 'path';
import os from 'os';

/**
 * Base configuration for the deep research library
 */
export const deepResearchConfig = {
  /**
   * Base directory for storing deep research data
   */
  baseDir: process.env.DEEP_RESEARCH_BASE_DIR || path.join(os.tmpdir(), 'deep-research'),

  /**
   * Database configuration
   */
  database: {
    // Use existing Prisma connection
    provider: 'postgresql',
    url: process.env.DATABASE_URL,
  },

  /**
   * AI Analysis configuration
   */
  ai: {
    // Default models for different analysis types
    models: {
      summary: process.env.SUMMARY_MODEL || 'gpt-4o-mini',
      insights: process.env.INSIGHTS_MODEL || 'gpt-4o',
      sentiment: process.env.SENTIMENT_MODEL || 'gpt-4o-mini',
      bias: process.env.BIAS_MODEL || 'gpt-4o',
      credibility: process.env.CREDIBILITY_MODEL || 'gpt-4o',
      entities: process.env.ENTITIES_MODEL || 'gpt-4o-mini',
      relationships: process.env.RELATIONSHIPS_MODEL || 'gpt-4o',
    },
    
    // Token limits for different operations
    tokenLimits: {
      summary: 4000,
      insights: 8000,
      sentiment: 2000,
      bias: 6000,
      credibility: 6000,
      entities: 4000,
      relationships: 8000,
    },

    // Temperature settings for different analysis types
    temperature: {
      summary: 0.3,
      insights: 0.7,
      sentiment: 0.1,
      bias: 0.2,
      credibility: 0.1,
      entities: 0.1,
      relationships: 0.5,
    },

    // Concurrent analysis limit
    maxConcurrentAnalyses: parseInt(process.env.MAX_CONCURRENT_ANALYSES || '5'),
    
    // Analysis timeout (in milliseconds)
    analysisTimeout: parseInt(process.env.ANALYSIS_TIMEOUT || '120000'), // 2 minutes
  },

  /**
   * Research sources configuration
   */
  sources: {
    // Web research
    web: {
      enabled: true,
      maxResults: 50,
      timeout: 30000,
      retries: 3,
      userAgent: 'DeepResearchBot/1.0',
    },

    // Academic research
    academic: {
      enabled: true,
      arxiv: {
        baseUrl: 'http://export.arxiv.org/api/query',
        maxResults: 20,
        timeout: 15000,
      },
      pubmed: {
        baseUrl: 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils',
        apiKey: process.env.PUBMED_API_KEY,
        maxResults: 20,
        timeout: 15000,
      },
      scholar: {
        enabled: process.env.GOOGLE_SCHOLAR_ENABLED === 'true',
        maxResults: 10,
        timeout: 30000,
      },
    },

    // News research
    news: {
      enabled: true,
      newsApi: {
        apiKey: process.env.NEWS_API_KEY,
        baseUrl: 'https://newsapi.org/v2',
        maxResults: 30,
      },
      rss: {
        enabled: true,
        maxFeeds: 10,
        timeout: 15000,
      },
    },

    // Social media research
    social: {
      enabled: false, // Disabled by default due to API restrictions
      twitter: {
        apiKey: process.env.TWITTER_API_KEY,
        apiSecret: process.env.TWITTER_API_SECRET,
        bearerToken: process.env.TWITTER_BEARER_TOKEN,
        maxResults: 20,
      },
      reddit: {
        clientId: process.env.REDDIT_CLIENT_ID,
        clientSecret: process.env.REDDIT_CLIENT_SECRET,
        userAgent: 'DeepResearchBot/1.0',
        maxResults: 20,
      },
    },

    // Documentation research
    documentation: {
      enabled: true,
      github: {
        token: process.env.GITHUB_TOKEN,
        maxRepos: 10,
        maxFiles: 50,
      },
      maxDepth: 3,
      timeout: 30000,
    },
  },

  /**
   * Knowledge graph configuration
   */
  knowledgeGraph: {
    // Entity extraction settings
    entityExtraction: {
      minConfidence: 0.7,
      maxEntitiesPerSource: 50,
      deduplicationThreshold: 0.8,
    },

    // Relationship extraction settings
    relationshipExtraction: {
      minConfidence: 0.6,
      maxRelationshipsPerSource: 100,
      maxDistance: 3,
    },

    // Graph storage settings
    storage: {
      maxNodes: 10000,
      maxEdges: 50000,
      pruneThreshold: 0.5,
    },
  },

  /**
   * Quality and credibility settings
   */
  quality: {
    // Credibility scoring weights
    credibilityWeights: {
      domainAuthority: 0.3,
      authorCredibility: 0.2,
      contentQuality: 0.2,
      sourceReliability: 0.15,
      factualAccuracy: 0.15,
    },

    // Bias detection settings
    biasDetection: {
      enabled: true,
      threshold: 0.7,
      categories: [
        'political',
        'commercial',
        'cultural',
        'confirmation',
        'selection',
      ],
    },

    // Fact checking settings
    factChecking: {
      enabled: true,
      crossReferenceThreshold: 3,
      reliabilityThreshold: 0.8,
    },
  },

  /**
   * Performance and caching settings
   */
  performance: {
    // Cache settings
    cache: {
      enabled: true,
      ttl: 3600000, // 1 hour
      maxSize: 1000,
    },

    // Rate limiting
    rateLimit: {
      requestsPerMinute: 60,
      burstLimit: 10,
    },

    // Batch processing
    batchSize: {
      sources: 10,
      analyses: 5,
      entities: 100,
    },

    // Timeouts
    timeouts: {
      sourceProcessing: 60000, // 1 minute
      analysisProcessing: 120000, // 2 minutes
      workflowExecution: 600000, // 10 minutes
    },
  },

  /**
   * Storage and file management
   */
  storage: {
    // File storage paths
    paths: {
      projects: 'projects',
      sources: 'sources',
      analyses: 'analyses',
      knowledge: 'knowledge',
      cache: 'cache',
      temp: 'temp',
    },

    // File size limits (in bytes)
    limits: {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      maxProjectSize: 1024 * 1024 * 1024, // 1GB
      maxCacheSize: 500 * 1024 * 1024, // 500MB
    },

    // Cleanup settings
    cleanup: {
      tempFileAge: 24 * 60 * 60 * 1000, // 24 hours
      cacheAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      archivedProjectAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    },
  },

  /**
   * Security settings
   */
  security: {
    // Content filtering
    contentFilter: {
      enabled: true,
      blockedDomains: [
        'malware-site.com',
        'spam-site.com',
      ],
      allowedFileTypes: [
        'text/html',
        'text/plain',
        'application/pdf',
        'application/json',
        'text/xml',
        'application/xml',
      ],
    },

    // API security
    api: {
      requireAuth: true,
      rateLimitByUser: true,
      maxRequestSize: 10 * 1024 * 1024, // 10MB
    },

    // Data privacy
    privacy: {
      anonymizeData: false,
      retentionPeriod: 90 * 24 * 60 * 60 * 1000, // 90 days
      encryptSensitiveData: true,
    },
  },

  /**
   * Logging and monitoring
   */
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableMetrics: true,
    enableTracing: false,
    
    // Log retention
    retention: {
      errorLogs: 30 * 24 * 60 * 60 * 1000, // 30 days
      accessLogs: 7 * 24 * 60 * 60 * 1000, // 7 days
      debugLogs: 24 * 60 * 60 * 1000, // 24 hours
    },
  },

  /**
   * Feature flags
   */
  features: {
    realTimeResearch: process.env.ENABLE_REALTIME_RESEARCH === 'true',
    multiLanguageSupport: process.env.ENABLE_MULTILANG === 'true',
    advancedAnalytics: process.env.ENABLE_ADVANCED_ANALYTICS === 'true',
    collaborativeResearch: process.env.ENABLE_COLLABORATION === 'true',
    exportFeatures: process.env.ENABLE_EXPORT === 'true',
    apiAccess: process.env.ENABLE_API_ACCESS === 'true',
  },
};

/**
 * Environment-specific configuration overrides
 */
const environment = process.env.NODE_ENV || 'development';

if (environment === 'development') {
  // Development overrides
  deepResearchConfig.ai.maxConcurrentAnalyses = 2;
  deepResearchConfig.sources.web.maxResults = 10;
  deepResearchConfig.sources.academic.arxiv.maxResults = 5;
  deepResearchConfig.logging.level = 'debug';
}

if (environment === 'test') {
  // Test overrides
  deepResearchConfig.ai.maxConcurrentAnalyses = 1;
  deepResearchConfig.sources.web.maxResults = 5;
  deepResearchConfig.performance.cache.enabled = false;
  deepResearchConfig.logging.level = 'error';
}

if (environment === 'production') {
  // Production overrides
  deepResearchConfig.ai.maxConcurrentAnalyses = 10;
  deepResearchConfig.performance.rateLimit.requestsPerMinute = 120;
  deepResearchConfig.security.api.requireAuth = true;
  deepResearchConfig.logging.enableMetrics = true;
}

export default deepResearchConfig;
