/**
 * Deep Research Library Types
 *
 * This file contains TypeScript interfaces and types for the deep research library.
 */

import { z } from 'zod';

/**
 * Research project status
 */
export type ResearchProjectStatus = 'draft' | 'active' | 'completed' | 'archived';

/**
 * Research source types
 */
export type ResearchSourceType =
  | 'web'
  | 'academic'
  | 'news'
  | 'social'
  | 'documentation'
  | 'multimedia'
  | 'database';

/**
 * Research analysis types
 */
export type AnalysisType =
  | 'summary'
  | 'insights'
  | 'sentiment'
  | 'bias'
  | 'credibility'
  | 'entities'
  | 'relationships';

/**
 * Research project metadata
 */
export interface ResearchProject {
  id: string;
  title: string;
  description: string;
  status: ResearchProjectStatus;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
  settings: ResearchProjectSettings;
  metadata: {
    totalSources: number;
    totalAnalyses: number;
    lastActivity: Date;
    estimatedCompletion?: Date;
  };
}

/**
 * Research project settings
 */
export interface ResearchProjectSettings {
  autoAnalysis: boolean;
  sourceTypes: ResearchSourceType[];
  analysisTypes: AnalysisType[];
  maxSources: number;
  qualityThreshold: number;
  biasDetection: boolean;
  factChecking: boolean;
  realTimeUpdates: boolean;
}

/**
 * Research source metadata
 */
export interface ResearchSource {
  id: string;
  projectId: string;
  type: ResearchSourceType;
  url: string;
  title: string;
  description?: string;
  author?: string;
  publishedAt?: Date;
  accessedAt: Date;
  credibilityScore: number;
  biasScore: number;
  relevanceScore: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  metadata: Record<string, any>;
  content: {
    raw: string;
    processed: string;
    summary?: string;
    keyPoints?: string[];
  };
}

/**
 * AI analysis result
 */
export interface AnalysisResult {
  id: string;
  sourceId: string;
  projectId: string;
  type: AnalysisType;
  model: string;
  result: Record<string, any>;
  confidence: number;
  createdAt: Date;
  metadata: {
    processingTime: number;
    tokensUsed: number;
    cost?: number;
    [key: string]: any;
  };
}

/**
 * Knowledge entity
 */
export interface KnowledgeEntity {
  id: string;
  name: string;
  type: 'person' | 'organization' | 'concept' | 'location' | 'event' | 'product';
  description?: string;
  aliases: string[];
  confidence: number;
  sources: string[]; // Source IDs
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Knowledge relationship
 */
export interface KnowledgeRelationship {
  id: string;
  fromEntityId: string;
  toEntityId: string;
  type: string;
  description?: string;
  confidence: number;
  sources: string[]; // Source IDs
  metadata: Record<string, any>;
  createdAt: Date;
}

/**
 * Research session
 */
export interface ResearchSession {
  id: string;
  projectId: string;
  query: string;
  status: 'running' | 'completed' | 'failed';
  startedAt: Date;
  completedAt?: Date;
  results: {
    sourcesFound: number;
    sourcesProcessed: number;
    analysesCompleted: number;
    entitiesExtracted: number;
    relationshipsFound: number;
  };
  metadata: Record<string, any>;
}

/**
 * Research query options
 */
export interface ResearchQuery {
  query: string;
  projectId?: string;
  sourceTypes?: ResearchSourceType[];
  maxSources?: number;
  analysisTypes?: AnalysisType[];
  realTime?: boolean;
  deepAnalysis?: boolean;
}

/**
 * Multi-source research options
 */
export interface MultiSourceResearchOptions {
  query: string;
  sources: {
    web?: {
      enabled: boolean;
      maxResults: number;
      domains?: string[];
      excludeDomains?: string[];
    };
    academic?: {
      enabled: boolean;
      maxResults: number;
      databases: ('arxiv' | 'pubmed' | 'scholar')[];
      dateRange?: {
        from: Date;
        to: Date;
      };
    };
    news?: {
      enabled: boolean;
      maxResults: number;
      sources?: string[];
      languages?: string[];
    };
    social?: {
      enabled: boolean;
      platforms: ('twitter' | 'reddit' | 'linkedin')[];
      maxResults: number;
    };
  };
  analysis: {
    summarize: boolean;
    extractEntities: boolean;
    detectBias: boolean;
    checkCredibility: boolean;
    findRelationships: boolean;
  };
}

/**
 * Research workflow step
 */
export interface ResearchWorkflowStep {
  id: string;
  name: string;
  type: 'search' | 'analyze' | 'synthesize' | 'validate';
  config: Record<string, any>;
  dependencies: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: any;
}

/**
 * Research workflow
 */
export interface ResearchWorkflow {
  id: string;
  name: string;
  description: string;
  steps: ResearchWorkflowStep[];
  status: 'draft' | 'running' | 'completed' | 'failed';
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Zod schemas for validation
 */
export const researchProjectSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(1000),
  tags: z.array(z.string()).default([]),
  settings: z.object({
    autoAnalysis: z.boolean().default(true),
    sourceTypes: z.array(z.enum(['web', 'academic', 'news', 'social', 'documentation', 'multimedia', 'database'])).default(['web']),
    analysisTypes: z.array(z.enum(['summary', 'insights', 'sentiment', 'bias', 'credibility', 'entities', 'relationships'])).default(['summary', 'insights']),
    maxSources: z.number().min(1).max(1000).default(50),
    qualityThreshold: z.number().min(0).max(1).default(0.7),
    biasDetection: z.boolean().default(true),
    factChecking: z.boolean().default(true),
    realTimeUpdates: z.boolean().default(false)
  })
});

export const researchQuerySchema = z.object({
  query: z.string().min(1).max(500),
  projectId: z.string().optional(),
  sourceTypes: z.array(z.enum(['web', 'academic', 'news', 'social', 'documentation', 'multimedia', 'database'])).optional(),
  maxSources: z.number().min(1).max(100).default(20),
  analysisTypes: z.array(z.enum(['summary', 'insights', 'sentiment', 'bias', 'credibility', 'entities', 'relationships'])).optional(),
  realTime: z.boolean().default(false),
  deepAnalysis: z.boolean().default(false)
});

export const multiSourceResearchSchema = z.object({
  query: z.string().min(1).max(500),
  sources: z.object({
    web: z.object({
      enabled: z.boolean(),
      maxResults: z.number().min(1).max(50),
      domains: z.array(z.string()).optional(),
      excludeDomains: z.array(z.string()).optional()
    }).optional(),
    academic: z.object({
      enabled: z.boolean(),
      maxResults: z.number().min(1).max(20),
      databases: z.array(z.enum(['arxiv', 'pubmed', 'scholar'])),
      dateRange: z.object({
        from: z.date(),
        to: z.date()
      }).optional()
    }).optional(),
    news: z.object({
      enabled: z.boolean(),
      maxResults: z.number().min(1).max(30),
      sources: z.array(z.string()).optional(),
      languages: z.array(z.string()).optional()
    }).optional(),
    social: z.object({
      enabled: z.boolean(),
      platforms: z.array(z.enum(['twitter', 'reddit', 'linkedin'])),
      maxResults: z.number().min(1).max(20)
    }).optional()
  }),
  analysis: z.object({
    summarize: z.boolean().default(true),
    extractEntities: z.boolean().default(true),
    detectBias: z.boolean().default(true),
    checkCredibility: z.boolean().default(true),
    findRelationships: z.boolean().default(false)
  })
});
