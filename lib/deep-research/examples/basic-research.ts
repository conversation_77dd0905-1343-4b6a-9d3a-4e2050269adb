/**
 * Basic Deep Research Example
 * 
 * Demonstrates how to use the deep research library for comprehensive research.
 */

import { deepResearchService } from '../index';
import { MultiSourceResearchOptions } from '../types';

/**
 * Example: Comprehensive AI Research
 */
export async function performAIResearch() {
  try {
    console.log('🔬 Starting comprehensive AI research...');

    // Step 1: Create a research project
    const project = await deepResearchService.createProject(
      'AI and Machine Learning Trends 2024',
      'Comprehensive research on current AI trends, breakthroughs, and future directions in machine learning.',
      {
        autoAnalysis: true,
        sourceTypes: ['web', 'academic', 'news'],
        analysisTypes: ['summary', 'insights', 'credibility', 'bias', 'entities'],
        maxSources: 30,
        qualityThreshold: 0.7,
        biasDetection: true,
        factChecking: true,
        realTimeUpdates: false
      }
    );

    console.log(`📁 Created project: ${project.title} (${project.id})`);

    // Step 2: Define research options
    const researchOptions: MultiSourceResearchOptions = {
      query: 'artificial intelligence machine learning trends 2024 breakthroughs',
      sources: {
        web: {
          enabled: true,
          maxResults: 15,
          domains: ['arxiv.org', 'nature.com', 'science.org', 'mit.edu', 'stanford.edu'],
          excludeDomains: ['spam-site.com', 'low-quality-blog.com']
        },
        academic: {
          enabled: true,
          maxResults: 10,
          databases: ['arxiv', 'pubmed'],
          dateRange: {
            from: new Date('2024-01-01'),
            to: new Date()
          }
        },
        news: {
          enabled: true,
          maxResults: 8,
          sources: ['reuters.com', 'bbc.com', 'nature.com'],
          languages: ['en']
        },
        social: {
          enabled: false, // Disabled due to API restrictions
          platforms: ['twitter', 'reddit'],
          maxResults: 5
        }
      },
      analysis: {
        summarize: true,
        extractEntities: true,
        detectBias: true,
        checkCredibility: true,
        findRelationships: true
      }
    };

    // Step 3: Start research session
    const session = await deepResearchService.startResearchSession(
      {
        query: researchOptions.query,
        projectId: project.id,
        sourceTypes: ['web', 'academic', 'news'],
        maxSources: 30,
        analysisTypes: ['summary', 'insights', 'credibility', 'bias', 'entities'],
        realTime: false,
        deepAnalysis: true
      },
      researchOptions
    );

    console.log(`🚀 Started research session: ${session.id}`);

    // Step 4: Monitor progress
    const monitorProgress = () => {
      const status = deepResearchService.getSessionStatus(session.id);
      if (status) {
        console.log(`📊 Progress: ${status.results.sourcesProcessed}/${status.results.sourcesFound} sources processed`);
        console.log(`🔍 Analyses completed: ${status.results.analysesCompleted}`);
        console.log(`🧠 Entities extracted: ${status.results.entitiesExtracted}`);
        console.log(`🔗 Relationships found: ${status.results.relationshipsFound}`);
        
        if (status.status === 'completed') {
          console.log('✅ Research session completed!');
          return true;
        } else if (status.status === 'failed') {
          console.log('❌ Research session failed');
          return true;
        }
      }
      return false;
    };

    // Poll for completion (in a real app, you'd use event listeners)
    while (!monitorProgress()) {
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
    }

    // Step 5: Get final results
    const finalProject = await deepResearchService.getProject(project.id);
    if (finalProject) {
      console.log('\n📋 Final Results:');
      console.log(`Total Sources: ${finalProject.metadata.totalSources}`);
      console.log(`Total Analyses: ${finalProject.metadata.totalAnalyses}`);
      console.log(`Last Activity: ${finalProject.metadata.lastActivity}`);
    }

    return {
      project: finalProject,
      session
    };

  } catch (error) {
    console.error('❌ Research failed:', error);
    throw error;
  }
}

/**
 * Example: Quick Web Research
 */
export async function performQuickWebResearch(query: string) {
  try {
    console.log(`🔍 Starting quick web research for: "${query}"`);

    // Create a simple project
    const project = await deepResearchService.createProject(
      `Quick Research: ${query}`,
      `Quick web research on: ${query}`,
      {
        autoAnalysis: true,
        sourceTypes: ['web'],
        analysisTypes: ['summary', 'insights'],
        maxSources: 10,
        qualityThreshold: 0.6
      }
    );

    // Simple web-only research
    const researchOptions: MultiSourceResearchOptions = {
      query,
      sources: {
        web: {
          enabled: true,
          maxResults: 10
        }
      },
      analysis: {
        summarize: true,
        extractEntities: true,
        detectBias: false,
        checkCredibility: true,
        findRelationships: false
      }
    };

    const session = await deepResearchService.startResearchSession(
      {
        query,
        projectId: project.id,
        sourceTypes: ['web'],
        maxSources: 10,
        analysisTypes: ['summary', 'insights']
      },
      researchOptions
    );

    console.log(`🚀 Quick research session started: ${session.id}`);
    return { project, session };

  } catch (error) {
    console.error('❌ Quick research failed:', error);
    throw error;
  }
}

/**
 * Example: Academic Research
 */
export async function performAcademicResearch(topic: string) {
  try {
    console.log(`🎓 Starting academic research on: "${topic}"`);

    const project = await deepResearchService.createProject(
      `Academic Research: ${topic}`,
      `Academic literature review on: ${topic}`,
      {
        autoAnalysis: true,
        sourceTypes: ['academic'],
        analysisTypes: ['summary', 'insights', 'entities', 'relationships'],
        maxSources: 20,
        qualityThreshold: 0.8
      }
    );

    const researchOptions: MultiSourceResearchOptions = {
      query: topic,
      sources: {
        academic: {
          enabled: true,
          maxResults: 20,
          databases: ['arxiv', 'pubmed'],
          dateRange: {
            from: new Date('2020-01-01'),
            to: new Date()
          }
        }
      },
      analysis: {
        summarize: true,
        extractEntities: true,
        detectBias: true,
        checkCredibility: true,
        findRelationships: true
      }
    };

    const session = await deepResearchService.startResearchSession(
      {
        query: topic,
        projectId: project.id,
        sourceTypes: ['academic'],
        maxSources: 20,
        analysisTypes: ['summary', 'insights', 'entities', 'relationships']
      },
      researchOptions
    );

    console.log(`🎓 Academic research session started: ${session.id}`);
    return { project, session };

  } catch (error) {
    console.error('❌ Academic research failed:', error);
    throw error;
  }
}

/**
 * Example: Multi-source Comparative Research
 */
export async function performComparativeResearch(topic1: string, topic2: string) {
  try {
    console.log(`⚖️ Starting comparative research: "${topic1}" vs "${topic2}"`);

    // Create projects for both topics
    const project1 = await deepResearchService.createProject(
      `Research: ${topic1}`,
      `Research on: ${topic1}`,
      { maxSources: 15 }
    );

    const project2 = await deepResearchService.createProject(
      `Research: ${topic2}`,
      `Research on: ${topic2}`,
      { maxSources: 15 }
    );

    // Research both topics
    const researchOptions = {
      sources: {
        web: { enabled: true, maxResults: 10 },
        academic: { enabled: true, maxResults: 5, databases: ['arxiv'] as const }
      },
      analysis: {
        summarize: true,
        extractEntities: true,
        detectBias: true,
        checkCredibility: true,
        findRelationships: true
      }
    };

    const session1 = await deepResearchService.startResearchSession(
      { query: topic1, projectId: project1.id, sourceTypes: ['web', 'academic'], maxSources: 15 },
      { ...researchOptions, query: topic1 }
    );

    const session2 = await deepResearchService.startResearchSession(
      { query: topic2, projectId: project2.id, sourceTypes: ['web', 'academic'], maxSources: 15 },
      { ...researchOptions, query: topic2 }
    );

    console.log(`⚖️ Comparative research sessions started: ${session1.id} vs ${session2.id}`);
    return { project1, project2, session1, session2 };

  } catch (error) {
    console.error('❌ Comparative research failed:', error);
    throw error;
  }
}

// Export all examples
export const examples = {
  performAIResearch,
  performQuickWebResearch,
  performAcademicResearch,
  performComparativeResearch
};
