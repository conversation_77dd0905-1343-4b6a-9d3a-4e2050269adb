# Deep Research Library

A production-grade, AI-powered research library that provides comprehensive research capabilities with multi-source data collection, intelligent analysis, and knowledge synthesis.

## 🚀 Features

### Core Capabilities
- **Multi-Source Data Collection**: Web scraping, academic papers (arXiv, PubMed), news articles, and documentation
- **AI-Powered Analysis**: Content summarization, insight extraction, sentiment analysis, bias detection, and credibility assessment
- **Knowledge Graph Construction**: Entity extraction, relationship mapping, and knowledge synthesis
- **Real-time Research Sessions**: Live progress tracking and event-driven architecture
- **Production-Ready**: Robust error handling, rate limiting, caching, and comprehensive logging

### Unique Value Proposition
- **Comprehensive Analysis**: Goes beyond simple search to provide deep insights and synthesis
- **Multi-Model AI**: Uses different AI models optimized for specific analysis types
- **Quality Assessment**: Built-in credibility scoring and bias detection
- **Knowledge Integration**: Constructs knowledge graphs from research findings
- **Enterprise-Ready**: Designed for production use with proper error handling and monitoring

## 🏗️ Architecture

### Core Components

#### Research Engine (`core/research-engine.ts`)
- Main orchestration engine for research operations
- Coordinates source collection, analysis, and synthesis
- Event-driven architecture with real-time progress tracking

#### Source Collectors (`sources/`)
- **WebCollector**: Intelligent web scraping using Browserless API
- **AcademicCollector**: Academic paper collection from arXiv and PubMed
- **NewsCollector**: News article collection from NewsAPI and RSS feeds
- **SocialCollector**: Social media content (currently disabled due to API restrictions)
- **DocumentationCollector**: Technical documentation and API docs

#### Analysis Engines (`analysis/`)
- **AIAnalyzer**: Multi-type content analysis using Vercel AI SDK
- **CredibilityScorer**: Source credibility assessment
- **BiasDetector**: Bias detection and analysis
- **EntityExtractor**: Named entity recognition and extraction

#### Knowledge Management (`knowledge/`)
- **KnowledgeGraph**: Knowledge graph construction and querying
- **EntityManager**: Entity relationship management
- **SynthesisEngine**: Intelligent synthesis of research findings

#### Storage & Persistence (`storage/`)
- **DatabaseAdapter**: Prisma-based database operations
- **FileManager**: File storage management
- **CacheManager**: Intelligent caching layer

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Browserless API access
- OpenAI API key
- NewsAPI key (optional)
- PubMed API key (optional)

### Configuration

Update your `lib/deep-research/config.ts`:

```typescript
export const deepResearchConfig = {
  ai: {
    models: {
      summary: 'gpt-4-turbo-preview',
      insights: 'gpt-4-turbo-preview',
      credibility: 'gpt-4-turbo-preview',
      bias: 'gpt-4-turbo-preview',
      entities: 'gpt-4-turbo-preview',
      relationships: 'gpt-4-turbo-preview',
      sentiment: 'gpt-3.5-turbo'
    }
  },
  sources: {
    news: {
      newsApi: {
        apiKey: process.env.NEWS_API_KEY
      }
    },
    academic: {
      pubmed: {
        apiKey: process.env.PUBMED_API_KEY
      }
    }
  }
};
```

## 📖 Usage

### Basic Research

```typescript
import { deepResearchService } from '@/lib/deep-research';

// Create a research project
const project = await deepResearchService.createProject(
  'AI Trends 2024',
  'Research on current AI and ML trends',
  {
    autoAnalysis: true,
    sourceTypes: ['web', 'academic', 'news'],
    maxSources: 30,
    qualityThreshold: 0.7
  }
);

// Define research options
const options = {
  query: 'artificial intelligence machine learning trends 2024',
  sources: {
    web: {
      enabled: true,
      maxResults: 15,
      domains: ['arxiv.org', 'nature.com', 'mit.edu']
    },
    academic: {
      enabled: true,
      maxResults: 10,
      databases: ['arxiv', 'pubmed']
    },
    news: {
      enabled: true,
      maxResults: 8,
      sources: ['reuters.com', 'bbc.com']
    }
  },
  analysis: {
    summarize: true,
    extractEntities: true,
    detectBias: true,
    checkCredibility: true,
    findRelationships: true
  }
};

// Start research session
const session = await deepResearchService.startResearchSession(
  {
    query: options.query,
    projectId: project.id,
    sourceTypes: ['web', 'academic', 'news'],
    maxSources: 30
  },
  options
);

// Monitor progress
deepResearchService.on('sessionCompleted', (session) => {
  console.log('Research completed!', session.results);
});
```

### Quick Web Research

```typescript
import { examples } from '@/lib/deep-research/examples/basic-research';

const result = await examples.performQuickWebResearch(
  'quantum computing breakthroughs 2024'
);
```

### Academic Literature Review

```typescript
const result = await examples.performAcademicResearch(
  'CRISPR gene editing applications'
);
```

## 🔧 API Reference

### DeepResearchEngine

#### Project Management
- `createProject(title, description, settings)` - Create a new research project
- `getProject(projectId)` - Get project by ID
- `listProjects(userId?)` - List all projects
- `updateProject(projectId, updates)` - Update project
- `deleteProject(projectId)` - Delete project

#### Research Sessions
- `startResearchSession(query, options)` - Start a comprehensive research session
- `getSessionStatus(sessionId)` - Get session status and progress

#### Events
- `sessionStarted` - Research session started
- `sessionCompleted` - Research session completed
- `sourceCollected` - New source collected
- `analysisComplete` - Analysis completed
- `synthesisComplete` - Synthesis completed

## 🎯 Advanced Features

### Custom Analysis Types
```typescript
// Register custom analysis
aiAnalyzer.registerAnalysisType('custom', async (source) => {
  // Custom analysis logic
  return { customInsights: '...' };
});
```

### Workflow Orchestration
```typescript
const workflow = workflowEngine.createWorkflow(
  'Custom Research Workflow',
  'Multi-step research process',
  [
    { name: 'Collect Sources', type: 'search' },
    { name: 'Analyze Content', type: 'analyze', dependencies: ['step_1'] },
    { name: 'Synthesize Results', type: 'synthesize', dependencies: ['step_2'] }
  ]
);

await workflowEngine.executeWorkflow(workflow);
```

### Knowledge Graph Queries
```typescript
// Add entities and relationships
await knowledgeGraph.addEntity({
  name: 'OpenAI',
  type: 'organization',
  confidence: 0.95
});

await knowledgeGraph.addRelationship({
  fromEntityId: 'openai',
  toEntityId: 'gpt-4',
  type: 'develops',
  confidence: 0.9
});
```

## 🔒 Security & Privacy

- All API keys are securely managed through environment variables
- Rate limiting prevents API abuse
- Content is processed locally with configurable data retention
- No sensitive data is logged or stored unnecessarily

## 🚀 Performance

- Intelligent caching reduces redundant API calls
- Batch processing for efficient analysis
- Configurable rate limiting for API compliance
- Optimized database queries with proper indexing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For issues and questions:
- Check the examples in `examples/basic-research.ts`
- Review the configuration in `config.ts`
- Ensure all required API keys are configured
- Check the console for detailed error messages
