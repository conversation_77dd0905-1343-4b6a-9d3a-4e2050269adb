/**
 * Research Project Manager
 * 
 * Manages research projects, their lifecycle, settings, and metadata.
 */

import { EventEmitter } from 'events';
import { DatabaseAdapter } from '../storage/database-adapter';
import { deepResearchConfig } from '../config';
import {
  ResearchProject,
  ResearchProjectStatus,
  ResearchProjectSettings,
  researchProjectSchema
} from '../types';

/**
 * Project Manager
 * 
 * Handles CRUD operations and lifecycle management for research projects
 */
export class ProjectManager extends EventEmitter {
  private databaseAdapter: DatabaseAdapter;

  constructor(databaseAdapter: DatabaseAdapter) {
    super();
    this.databaseAdapter = databaseAdapter;
  }

  /**
   * Create a new research project
   */
  async createProject(
    title: string,
    description: string,
    settings: Partial<ResearchProjectSettings> = {},
    userId?: string
  ): Promise<ResearchProject> {
    // Validate input
    const validatedData = researchProjectSchema.parse({
      title,
      description,
      settings: {
        ...this.getDefaultSettings(),
        ...settings
      }
    });

    // Generate project ID
    const projectId = `proj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create project object
    const project: ResearchProject = {
      id: projectId,
      title: validatedData.title,
      description: validatedData.description,
      status: 'draft',
      tags: validatedData.tags || [],
      createdAt: new Date(),
      updatedAt: new Date(),
      userId,
      settings: validatedData.settings,
      metadata: {
        totalSources: 0,
        totalAnalyses: 0,
        lastActivity: new Date()
      }
    };

    // Save to database
    await this.databaseAdapter.saveProject(project);

    this.emit('projectCreated', project);
    return project;
  }

  /**
   * Get a research project by ID
   */
  async getProject(projectId: string): Promise<ResearchProject | null> {
    try {
      const project = await this.databaseAdapter.getProject(projectId);
      return project;
    } catch (error) {
      console.error(`Error getting project ${projectId}:`, error);
      return null;
    }
  }

  /**
   * List all research projects
   */
  async listProjects(userId?: string): Promise<ResearchProject[]> {
    try {
      const projects = await this.databaseAdapter.listProjects(userId);
      return projects;
    } catch (error) {
      console.error('Error listing projects:', error);
      return [];
    }
  }

  /**
   * Update a research project
   */
  async updateProject(
    projectId: string,
    updates: Partial<ResearchProject>
  ): Promise<ResearchProject> {
    const existingProject = await this.getProject(projectId);
    if (!existingProject) {
      throw new Error(`Project ${projectId} not found`);
    }

    // Merge updates
    const updatedProject: ResearchProject = {
      ...existingProject,
      ...updates,
      id: projectId, // Ensure ID cannot be changed
      updatedAt: new Date(),
      metadata: {
        ...existingProject.metadata,
        ...updates.metadata,
        lastActivity: new Date()
      }
    };

    // Validate updated project
    if (updates.title || updates.description || updates.settings) {
      researchProjectSchema.parse({
        title: updatedProject.title,
        description: updatedProject.description,
        settings: updatedProject.settings
      });
    }

    // Save to database
    await this.databaseAdapter.saveProject(updatedProject);

    this.emit('projectUpdated', updatedProject, existingProject);
    return updatedProject;
  }

  /**
   * Delete a research project
   */
  async deleteProject(projectId: string): Promise<boolean> {
    try {
      const project = await this.getProject(projectId);
      if (!project) {
        return false;
      }

      // Delete all related data
      await this.databaseAdapter.deleteProjectData(projectId);
      
      // Delete the project itself
      await this.databaseAdapter.deleteProject(projectId);

      this.emit('projectDeleted', project);
      return true;
    } catch (error) {
      console.error(`Error deleting project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Archive a research project
   */
  async archiveProject(projectId: string): Promise<ResearchProject> {
    return this.updateProject(projectId, { 
      status: 'archived',
      metadata: {
        archivedAt: new Date()
      }
    });
  }

  /**
   * Activate a research project
   */
  async activateProject(projectId: string): Promise<ResearchProject> {
    return this.updateProject(projectId, { 
      status: 'active',
      metadata: {
        activatedAt: new Date()
      }
    });
  }

  /**
   * Complete a research project
   */
  async completeProject(projectId: string): Promise<ResearchProject> {
    return this.updateProject(projectId, { 
      status: 'completed',
      metadata: {
        completedAt: new Date()
      }
    });
  }

  /**
   * Add tags to a project
   */
  async addTags(projectId: string, tags: string[]): Promise<ResearchProject> {
    const project = await this.getProject(projectId);
    if (!project) {
      throw new Error(`Project ${projectId} not found`);
    }

    const uniqueTags = Array.from(new Set([...project.tags, ...tags]));
    return this.updateProject(projectId, { tags: uniqueTags });
  }

  /**
   * Remove tags from a project
   */
  async removeTags(projectId: string, tags: string[]): Promise<ResearchProject> {
    const project = await this.getProject(projectId);
    if (!project) {
      throw new Error(`Project ${projectId} not found`);
    }

    const filteredTags = project.tags.filter(tag => !tags.includes(tag));
    return this.updateProject(projectId, { tags: filteredTags });
  }

  /**
   * Update project settings
   */
  async updateSettings(
    projectId: string,
    settings: Partial<ResearchProjectSettings>
  ): Promise<ResearchProject> {
    const project = await this.getProject(projectId);
    if (!project) {
      throw new Error(`Project ${projectId} not found`);
    }

    const updatedSettings = {
      ...project.settings,
      ...settings
    };

    return this.updateProject(projectId, { settings: updatedSettings });
  }

  /**
   * Get project statistics
   */
  async getProjectStats(projectId: string): Promise<{
    totalSources: number;
    totalAnalyses: number;
    sourcesByType: Record<string, number>;
    analysesByType: Record<string, number>;
    entitiesCount: number;
    relationshipsCount: number;
  }> {
    try {
      return await this.databaseAdapter.getProjectStats(projectId);
    } catch (error) {
      console.error(`Error getting stats for project ${projectId}:`, error);
      return {
        totalSources: 0,
        totalAnalyses: 0,
        sourcesByType: {},
        analysesByType: {},
        entitiesCount: 0,
        relationshipsCount: 0
      };
    }
  }

  /**
   * Search projects by title, description, or tags
   */
  async searchProjects(
    query: string,
    userId?: string
  ): Promise<ResearchProject[]> {
    try {
      return await this.databaseAdapter.searchProjects(query, userId);
    } catch (error) {
      console.error('Error searching projects:', error);
      return [];
    }
  }

  /**
   * Get projects by status
   */
  async getProjectsByStatus(
    status: ResearchProjectStatus,
    userId?: string
  ): Promise<ResearchProject[]> {
    try {
      return await this.databaseAdapter.getProjectsByStatus(status, userId);
    } catch (error) {
      console.error(`Error getting projects by status ${status}:`, error);
      return [];
    }
  }

  /**
   * Get default project settings
   */
  private getDefaultSettings(): ResearchProjectSettings {
    return {
      autoAnalysis: true,
      sourceTypes: ['web'],
      analysisTypes: ['summary', 'insights'],
      maxSources: 50,
      qualityThreshold: 0.7,
      biasDetection: true,
      factChecking: true,
      realTimeUpdates: false
    };
  }

  /**
   * Validate project access for user
   */
  async validateProjectAccess(
    projectId: string,
    userId?: string
  ): Promise<boolean> {
    const project = await this.getProject(projectId);
    if (!project) {
      return false;
    }

    // If no userId provided, allow access (for system operations)
    if (!userId) {
      return true;
    }

    // Check if user owns the project
    return project.userId === userId;
  }

  /**
   * Cleanup old projects based on configuration
   */
  async cleanupOldProjects(): Promise<number> {
    try {
      const cutoffDate = new Date(
        Date.now() - deepResearchConfig.storage.cleanup.archivedProjectAge
      );

      const deletedCount = await this.databaseAdapter.deleteOldProjects(cutoffDate);
      
      this.emit('projectsCleanedUp', deletedCount);
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up old projects:', error);
      return 0;
    }
  }
}
