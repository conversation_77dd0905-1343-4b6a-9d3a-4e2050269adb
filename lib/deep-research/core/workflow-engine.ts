/**
 * Research Workflow Engine
 * 
 * Orchestrates complex research workflows with dependency management,
 * parallel execution, and error handling.
 */

import { EventEmitter } from 'events';
import { deepResearchConfig } from '../config';
import {
  ResearchWorkflow,
  ResearchWorkflowStep,
  ResearchSession
} from '../types';

/**
 * Workflow Engine
 * 
 * Manages execution of research workflows with steps, dependencies, and parallel processing
 */
export class WorkflowEngine extends EventEmitter {
  private activeWorkflows: Map<string, ResearchWorkflow> = new Map();
  private stepExecutors: Map<string, (step: ResearchWorkflowStep, context: any) => Promise<any>> = new Map();

  constructor() {
    super();
    this.registerDefaultStepExecutors();
  }

  /**
   * Register default step executors
   */
  private registerDefaultStepExecutors(): void {
    this.stepExecutors.set('search', this.executeSearchStep.bind(this));
    this.stepExecutors.set('analyze', this.executeAnalyzeStep.bind(this));
    this.stepExecutors.set('synthesize', this.executeSynthesizeStep.bind(this));
    this.stepExecutors.set('validate', this.executeValidateStep.bind(this));
  }

  /**
   * Create a new workflow
   */
  createWorkflow(
    name: string,
    description: string,
    steps: Omit<ResearchWorkflowStep, 'id' | 'status' | 'result'>[]
  ): ResearchWorkflow {
    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const workflow: ResearchWorkflow = {
      id: workflowId,
      name,
      description,
      steps: steps.map((step, index) => ({
        ...step,
        id: `step_${index + 1}`,
        status: 'pending',
        result: undefined
      })),
      status: 'draft',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return workflow;
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(
    workflow: ResearchWorkflow,
    context: any = {}
  ): Promise<ResearchWorkflow> {
    this.activeWorkflows.set(workflow.id, workflow);
    
    try {
      workflow.status = 'running';
      workflow.updatedAt = new Date();
      this.emit('workflowStarted', workflow);

      // Build dependency graph
      const dependencyGraph = this.buildDependencyGraph(workflow.steps);
      
      // Execute steps in dependency order
      await this.executeStepsInOrder(workflow, dependencyGraph, context);

      workflow.status = 'completed';
      workflow.updatedAt = new Date();
      this.emit('workflowCompleted', workflow);

    } catch (error) {
      workflow.status = 'failed';
      workflow.updatedAt = new Date();
      this.emit('workflowFailed', workflow, error);
      throw error;
    } finally {
      this.activeWorkflows.delete(workflow.id);
    }

    return workflow;
  }

  /**
   * Build dependency graph for workflow steps
   */
  private buildDependencyGraph(steps: ResearchWorkflowStep[]): Map<string, string[]> {
    const graph = new Map<string, string[]>();
    
    steps.forEach(step => {
      graph.set(step.id, step.dependencies || []);
    });

    return graph;
  }

  /**
   * Execute steps in dependency order
   */
  private async executeStepsInOrder(
    workflow: ResearchWorkflow,
    dependencyGraph: Map<string, string[]>,
    context: any
  ): Promise<void> {
    const completed = new Set<string>();
    const inProgress = new Set<string>();
    const stepMap = new Map(workflow.steps.map(step => [step.id, step]));

    // Find steps with no dependencies to start with
    const readySteps = workflow.steps.filter(step => 
      (step.dependencies || []).length === 0
    );

    // Execute ready steps
    const executeStep = async (step: ResearchWorkflowStep): Promise<void> => {
      if (completed.has(step.id) || inProgress.has(step.id)) {
        return;
      }

      // Check if all dependencies are completed
      const dependencies = step.dependencies || [];
      const allDependenciesCompleted = dependencies.every(depId => completed.has(depId));
      
      if (!allDependenciesCompleted) {
        return;
      }

      inProgress.add(step.id);
      step.status = 'running';
      this.emit('stepStarted', workflow, step);

      try {
        // Execute the step
        const executor = this.stepExecutors.get(step.type);
        if (!executor) {
          throw new Error(`No executor found for step type: ${step.type}`);
        }

        // Prepare step context with results from dependencies
        const stepContext = {
          ...context,
          dependencyResults: dependencies.reduce((acc, depId) => {
            const depStep = stepMap.get(depId);
            if (depStep && depStep.result) {
              acc[depId] = depStep.result;
            }
            return acc;
          }, {} as Record<string, any>)
        };

        const result = await executor(step, stepContext);
        
        step.result = result;
        step.status = 'completed';
        completed.add(step.id);
        inProgress.delete(step.id);
        
        this.emit('stepCompleted', workflow, step, result);

        // Find and execute next ready steps
        const nextReadySteps = workflow.steps.filter(nextStep => {
          if (completed.has(nextStep.id) || inProgress.has(nextStep.id)) {
            return false;
          }
          
          const nextDependencies = nextStep.dependencies || [];
          return nextDependencies.every(depId => completed.has(depId));
        });

        // Execute next steps in parallel
        await Promise.all(nextReadySteps.map(executeStep));

      } catch (error) {
        step.status = 'failed';
        step.result = { error: (error as Error).message };
        inProgress.delete(step.id);
        
        this.emit('stepFailed', workflow, step, error);
        throw error;
      }
    };

    // Start execution with initial ready steps
    await Promise.all(readySteps.map(executeStep));

    // Verify all steps completed
    const incompleteSteps = workflow.steps.filter(step => step.status !== 'completed');
    if (incompleteSteps.length > 0) {
      throw new Error(`Workflow incomplete. Failed steps: ${incompleteSteps.map(s => s.id).join(', ')}`);
    }
  }

  /**
   * Execute search step
   */
  private async executeSearchStep(
    step: ResearchWorkflowStep,
    context: any
  ): Promise<any> {
    const { query, sourceTypes, maxResults } = step.config;
    
    // This would integrate with source collectors
    // For now, return mock data
    return {
      query,
      sourceTypes,
      sourcesFound: maxResults || 10,
      timestamp: new Date()
    };
  }

  /**
   * Execute analyze step
   */
  private async executeAnalyzeStep(
    step: ResearchWorkflowStep,
    context: any
  ): Promise<any> {
    const { analysisTypes, sources } = step.config;
    const { dependencyResults } = context;
    
    // This would integrate with analysis engines
    // For now, return mock data
    return {
      analysisTypes,
      sourcesAnalyzed: sources?.length || 0,
      analyses: analysisTypes?.map((type: string) => ({
        type,
        confidence: 0.8,
        result: `Analysis result for ${type}`
      })) || [],
      timestamp: new Date()
    };
  }

  /**
   * Execute synthesize step
   */
  private async executeSynthesizeStep(
    step: ResearchWorkflowStep,
    context: any
  ): Promise<any> {
    const { synthesisType, inputs } = step.config;
    const { dependencyResults } = context;
    
    // This would integrate with synthesis engine
    // For now, return mock data
    return {
      synthesisType,
      inputCount: Object.keys(dependencyResults).length,
      synthesis: `Synthesized insights from ${Object.keys(dependencyResults).length} inputs`,
      confidence: 0.85,
      timestamp: new Date()
    };
  }

  /**
   * Execute validate step
   */
  private async executeValidateStep(
    step: ResearchWorkflowStep,
    context: any
  ): Promise<any> {
    const { validationType, criteria } = step.config;
    const { dependencyResults } = context;
    
    // This would integrate with validation engines
    // For now, return mock data
    return {
      validationType,
      criteria,
      validationResults: criteria?.map((criterion: string) => ({
        criterion,
        passed: Math.random() > 0.3,
        confidence: Math.random()
      })) || [],
      overallValid: Math.random() > 0.2,
      timestamp: new Date()
    };
  }

  /**
   * Register custom step executor
   */
  registerStepExecutor(
    stepType: string,
    executor: (step: ResearchWorkflowStep, context: any) => Promise<any>
  ): void {
    this.stepExecutors.set(stepType, executor);
  }

  /**
   * Get workflow status
   */
  getWorkflowStatus(workflowId: string): ResearchWorkflow | null {
    return this.activeWorkflows.get(workflowId) || null;
  }

  /**
   * Cancel workflow execution
   */
  async cancelWorkflow(workflowId: string): Promise<boolean> {
    const workflow = this.activeWorkflows.get(workflowId);
    if (!workflow) {
      return false;
    }

    workflow.status = 'failed';
    workflow.updatedAt = new Date();
    
    // Mark running steps as failed
    workflow.steps.forEach(step => {
      if (step.status === 'running') {
        step.status = 'failed';
        step.result = { error: 'Workflow cancelled' };
      }
    });

    this.activeWorkflows.delete(workflowId);
    this.emit('workflowCancelled', workflow);
    
    return true;
  }

  /**
   * Create standard research workflow
   */
  createStandardResearchWorkflow(
    query: string,
    sourceTypes: string[] = ['web'],
    analysisTypes: string[] = ['summary', 'insights']
  ): ResearchWorkflow {
    const steps: Omit<ResearchWorkflowStep, 'id' | 'status' | 'result'>[] = [
      {
        name: 'Collect Sources',
        type: 'search',
        config: {
          query,
          sourceTypes,
          maxResults: 20
        },
        dependencies: []
      },
      {
        name: 'Analyze Content',
        type: 'analyze',
        config: {
          analysisTypes,
          sources: 'from_previous_step'
        },
        dependencies: ['step_1']
      },
      {
        name: 'Synthesize Insights',
        type: 'synthesize',
        config: {
          synthesisType: 'comprehensive',
          inputs: 'from_previous_steps'
        },
        dependencies: ['step_2']
      },
      {
        name: 'Validate Results',
        type: 'validate',
        config: {
          validationType: 'quality_check',
          criteria: ['credibility', 'relevance', 'completeness']
        },
        dependencies: ['step_3']
      }
    ];

    return this.createWorkflow(
      `Research: ${query}`,
      `Standard research workflow for query: ${query}`,
      steps
    );
  }
}
