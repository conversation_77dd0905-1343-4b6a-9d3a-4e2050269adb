/**
 * Deep Research Engine
 *
 * Production-grade research orchestration engine that integrates with real APIs
 * including Vercel AI SDK, Browserless, and multiple data sources.
 */

import { EventEmitter } from 'events';
import { deepResearchConfig } from '../config';
import { ProjectManager } from './project-manager';
import { WorkflowEngine } from './workflow-engine';
import { WebCollector } from '../sources/web-collector';
import { AcademicCollector } from '../sources/academic-collector';
import { NewsCollector } from '../sources/news-collector';
import { SocialCollector } from '../sources/social-collector';
import { DocumentationCollector } from '../sources/documentation-collector';
import { AIAnalyzer } from '../analysis/ai-analyzer';
import { CredibilityScorer } from '../analysis/credibility-scorer';
import { BiasDetector } from '../analysis/bias-detector';
import { EntityExtractor } from '../analysis/entity-extractor';
import { KnowledgeGraph } from '../knowledge/knowledge-graph';
import { EntityManager } from '../knowledge/entity-manager';
import { SynthesisEngine } from '../knowledge/synthesis-engine';
import { DatabaseAdapter } from '../storage/database-adapter';
import { FileManager } from '../storage/file-manager';
import { CacheManager } from '../storage/cache-manager';
import { RateLimiter } from '../utils/rate-limiter';
import {
  ResearchProject,
  ResearchQuery,
  ResearchSession,
  ResearchSource,
  AnalysisResult,
  MultiSourceResearchOptions
} from '../types';

/**
 * Deep Research Engine
 *
 * Main class that orchestrates all research operations
 */
export class DeepResearchEngine extends EventEmitter {
  private projectManager!: ProjectManager;
  private workflowEngine!: WorkflowEngine;
  private webCollector!: WebCollector;
  private academicCollector!: AcademicCollector;
  private newsCollector!: NewsCollector;
  private socialCollector!: SocialCollector;
  private documentationCollector!: DocumentationCollector;
  private aiAnalyzer!: AIAnalyzer;
  private credibilityScorer!: CredibilityScorer;
  private biasDetector!: BiasDetector;
  private entityExtractor!: EntityExtractor;
  private knowledgeGraph!: KnowledgeGraph;
  private entityManager!: EntityManager;
  private synthesisEngine!: SynthesisEngine;
  private databaseAdapter!: DatabaseAdapter;
  private fileManager!: FileManager;
  private cacheManager!: CacheManager;
  private rateLimiter!: RateLimiter;
  private activeSessions: Map<string, ResearchSession> = new Map();

  constructor() {
    super();
    this.initializeComponents();
  }

  /**
   * Initialize all components
   */
  private initializeComponents(): void {
    // Storage components
    this.databaseAdapter = new DatabaseAdapter();
    this.fileManager = new FileManager();
    this.cacheManager = new CacheManager();

    // Core components
    this.projectManager = new ProjectManager(this.databaseAdapter);
    this.workflowEngine = new WorkflowEngine();
    this.rateLimiter = new RateLimiter();

    // Source collectors
    this.webCollector = new WebCollector(this.rateLimiter);
    this.academicCollector = new AcademicCollector(this.rateLimiter);
    this.newsCollector = new NewsCollector(this.rateLimiter);
    this.socialCollector = new SocialCollector(this.rateLimiter);
    this.documentationCollector = new DocumentationCollector(this.rateLimiter);

    // Analysis engines
    this.aiAnalyzer = new AIAnalyzer();
    this.credibilityScorer = new CredibilityScorer();
    this.biasDetector = new BiasDetector();
    this.entityExtractor = new EntityExtractor();

    // Knowledge management
    this.knowledgeGraph = new KnowledgeGraph(this.databaseAdapter);
    this.entityManager = new EntityManager(this.databaseAdapter);
    this.synthesisEngine = new SynthesisEngine(this.aiAnalyzer);

    this.setupEventListeners();
  }

  /**
   * Setup event listeners for component communication
   */
  private setupEventListeners(): void {
    // Source collection events
    this.webCollector.on('sourceCollected', this.handleSourceCollected.bind(this));
    this.academicCollector.on('sourceCollected', this.handleSourceCollected.bind(this));
    this.newsCollector.on('sourceCollected', this.handleSourceCollected.bind(this));
    this.socialCollector.on('sourceCollected', this.handleSourceCollected.bind(this));
    this.documentationCollector.on('sourceCollected', this.handleSourceCollected.bind(this));

    // Analysis events
    this.aiAnalyzer.on('analysisComplete', this.handleAnalysisComplete.bind(this));
    this.entityExtractor.on('entitiesExtracted', this.handleEntitiesExtracted.bind(this));

    // Knowledge graph events
    this.knowledgeGraph.on('entityAdded', this.handleEntityAdded.bind(this));
    this.knowledgeGraph.on('relationshipAdded', this.handleRelationshipAdded.bind(this));
  }

  /**
   * Create a new research project
   */
  async createProject(
    title: string,
    description: string,
    settings: Partial<ResearchProject['settings']> = {}
  ): Promise<ResearchProject> {
    return this.projectManager.createProject(title, description, settings);
  }

  /**
   * Get a research project by ID
   */
  async getProject(projectId: string): Promise<ResearchProject | null> {
    return this.projectManager.getProject(projectId);
  }

  /**
   * List all research projects
   */
  async listProjects(userId?: string): Promise<ResearchProject[]> {
    return this.projectManager.listProjects(userId);
  }

  /**
   * Update a research project
   */
  async updateProject(
    projectId: string,
    updates: Partial<ResearchProject>
  ): Promise<ResearchProject> {
    return this.projectManager.updateProject(projectId, updates);
  }

  /**
   * Delete a research project
   */
  async deleteProject(projectId: string): Promise<boolean> {
    return this.projectManager.deleteProject(projectId);
  }

  /**
   * Start a comprehensive research session
   */
  async startResearchSession(
    query: ResearchQuery,
    options: MultiSourceResearchOptions
  ): Promise<ResearchSession> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const session: ResearchSession = {
      id: sessionId,
      projectId: query.projectId || 'default',
      query: query.query,
      status: 'running',
      startedAt: new Date(),
      results: {
        sourcesFound: 0,
        sourcesProcessed: 0,
        analysesCompleted: 0,
        entitiesExtracted: 0,
        relationshipsFound: 0
      },
      metadata: { options }
    };

    this.activeSessions.set(sessionId, session);
    this.emit('sessionStarted', session);

    // Execute research workflow
    this.executeResearchWorkflow(session, options).catch(error => {
      session.status = 'failed';
      session.completedAt = new Date();
      session.metadata.error = error.message;
      this.emit('sessionFailed', session, error);
    });

    return session;
  }

  /**
   * Get research session status
   */
  getSessionStatus(sessionId: string): ResearchSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * Execute the main research workflow
   */
  private async executeResearchWorkflow(
    session: ResearchSession,
    options: MultiSourceResearchOptions
  ): Promise<void> {
    try {
      // Phase 1: Source Collection
      await this.collectSources(session, options);

      // Phase 2: Content Analysis
      await this.analyzeCollectedSources(session);

      // Phase 3: Knowledge Extraction
      await this.extractKnowledge(session);

      // Phase 4: Synthesis
      await this.synthesizeFindings(session);

      // Complete session
      session.status = 'completed';
      session.completedAt = new Date();
      this.emit('sessionCompleted', session);

    } catch (error) {
      session.status = 'failed';
      session.completedAt = new Date();
      session.metadata.error = (error as Error).message;
      throw error;
    } finally {
      this.activeSessions.delete(session.id);
    }
  }

  /**
   * Collect sources from multiple channels
   */
  private async collectSources(
    session: ResearchSession,
    options: MultiSourceResearchOptions
  ): Promise<void> {
    const collectors: Promise<ResearchSource[]>[] = [];

    // Web sources
    if (options.sources.web?.enabled) {
      collectors.push(
        this.webCollector.collect(session.query, options.sources.web)
      );
    }

    // Academic sources
    if (options.sources.academic?.enabled) {
      collectors.push(
        this.academicCollector.collect(session.query, options.sources.academic)
      );
    }

    // News sources
    if (options.sources.news?.enabled) {
      collectors.push(
        this.newsCollector.collect(session.query, options.sources.news)
      );
    }

    // Social sources
    if (options.sources.social?.enabled) {
      collectors.push(
        this.socialCollector.collect(session.query, options.sources.social)
      );
    }

    // Execute all collectors in parallel
    const results = await Promise.allSettled(collectors);

    let totalSources = 0;
    results.forEach(result => {
      if (result.status === 'fulfilled') {
        totalSources += result.value.length;
      }
    });

    session.results.sourcesFound = totalSources;
    this.emit('sourcesCollected', session);
  }

  /**
   * Handle source collected event
   */
  private async handleSourceCollected(source: ResearchSource): Promise<void> {
    // Store source in database
    await this.databaseAdapter.saveSource(source);

    // Update session if active
    const session = Array.from(this.activeSessions.values())
      .find(s => s.projectId === source.projectId);

    if (session) {
      session.results.sourcesProcessed++;
      this.emit('sourceProcessed', session, source);
    }
  }

  /**
   * Analyze collected sources
   */
  private async analyzeCollectedSources(session: ResearchSession): Promise<void> {
    try {
      // Get all sources for this session's project
      const sources = await this.databaseAdapter.getProjectSources(session.projectId);

      // Analyze sources in batches
      const analysisTypes = ['summary', 'insights', 'credibility', 'bias', 'entities'];
      const analyses = await this.aiAnalyzer.batchAnalyze(sources, analysisTypes as any[]);

      // Save analyses to database
      for (const analysis of analyses) {
        await this.databaseAdapter.saveAnalysis(analysis);
      }

      session.results.analysesCompleted = analyses.length;
      this.emit('analysisPhaseComplete', session);
    } catch (error) {
      console.error('Error analyzing sources:', error);
      throw error;
    }
  }

  /**
   * Extract knowledge from analyzed sources
   */
  private async extractKnowledge(session: ResearchSession): Promise<void> {
    try {
      // Get all sources for this session's project
      const sources = await this.databaseAdapter.getProjectSources(session.projectId);

      // Extract entities from each source
      for (const source of sources) {
        const entities = await this.entityExtractor.extractEntities(source);

        for (const entity of entities) {
          await this.knowledgeGraph.addEntity(entity);
        }

        session.results.entitiesExtracted += entities.length;
      }

      this.emit('knowledgeExtractionComplete', session);
    } catch (error) {
      console.error('Error extracting knowledge:', error);
      throw error;
    }
  }

  /**
   * Synthesize findings into coherent insights
   */
  private async synthesizeFindings(session: ResearchSession): Promise<void> {
    try {
      // Get all sources and analyses for this project
      const sources = await this.databaseAdapter.getProjectSources(session.projectId);
      const analyses = await this.databaseAdapter.getProjectAnalyses(session.projectId);

      // Generate synthesis
      const synthesis = await this.synthesisEngine.synthesizeFindings(sources, analyses);

      // Save synthesis as a special analysis result
      const synthesisResult: AnalysisResult = {
        id: `synthesis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        sourceId: 'multiple',
        projectId: session.projectId,
        type: 'insights' as any,
        model: 'synthesis-engine',
        result: synthesis,
        confidence: 0.8,
        createdAt: new Date(),
        metadata: {
          processingTime: 0,
          tokensUsed: 0,
          cost: 0,
          type: 'synthesis'
        }
      };

      await this.databaseAdapter.saveAnalysis(synthesisResult);
      this.emit('synthesisComplete', session, synthesis);
    } catch (error) {
      console.error('Error synthesizing findings:', error);
      throw error;
    }
  }

  /**
   * Handle analysis complete event
   */
  private async handleAnalysisComplete(analysis: AnalysisResult): Promise<void> {
    try {
      // Update project metadata
      const project = await this.projectManager.getProject(analysis.projectId);
      if (project) {
        await this.projectManager.updateProject(analysis.projectId, {
          metadata: {
            ...project.metadata,
            totalAnalyses: project.metadata.totalAnalyses + 1,
            lastActivity: new Date()
          }
        });
      }

      this.emit('analysisProcessed', analysis);
    } catch (error) {
      console.error('Error handling analysis complete:', error);
    }
  }

  /**
   * Handle entities extracted event
   */
  private async handleEntitiesExtracted(entities: any[]): Promise<void> {
    try {
      // Process and merge entities
      const mergedEntities = await this.entityManager.mergeEntities(entities);

      // Add to knowledge graph
      for (const entity of mergedEntities) {
        await this.knowledgeGraph.addEntity(entity);
      }

      this.emit('entitiesProcessed', mergedEntities);
    } catch (error) {
      console.error('Error handling entities extracted:', error);
    }
  }

  /**
   * Handle entity added event
   */
  private async handleEntityAdded(entity: any): Promise<void> {
    try {
      // Log entity addition
      console.log(`Entity added: ${entity.name} (${entity.type})`);
      this.emit('entityIndexed', entity);
    } catch (error) {
      console.error('Error handling entity added:', error);
    }
  }

  /**
   * Handle relationship added event
   */
  private async handleRelationshipAdded(relationship: any): Promise<void> {
    try {
      // Log relationship addition
      console.log(`Relationship added: ${relationship.fromEntityId} -> ${relationship.toEntityId}`);
      this.emit('relationshipIndexed', relationship);
    } catch (error) {
      console.error('Error handling relationship added:', error);
    }
  }
}

// Export singleton instance
export const deepResearchService = new DeepResearchEngine();
