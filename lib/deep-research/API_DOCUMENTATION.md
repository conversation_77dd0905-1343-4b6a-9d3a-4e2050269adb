# Deep Research API Documentation

## API Routes

### Projects API

#### `GET /api/deep-research/projects`
List all research projects for the authenticated user.

**Query Parameters:**
- `status` (optional): Filter by project status (`draft`, `active`, `completed`, `archived`)
- `search` (optional): Search projects by title or description

**Response:**
```json
{
  "projects": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "status": "draft|active|completed|archived",
      "tags": ["string"],
      "createdAt": "string",
      "updatedAt": "string",
      "userId": "string",
      "settings": { ... },
      "metadata": { ... }
    }
  ]
}
```

#### `POST /api/deep-research/projects`
Create a new research project.

**Request Body:**
```json
{
  "title": "string",
  "description": "string",
  "settings": {
    "autoAnalysis": "boolean",
    "sourceTypes": ["string"],
    "analysisTypes": ["string"],
    "maxSources": "number",
    "qualityThreshold": "number",
    "biasDetection": "boolean",
    "factChecking": "boolean",
    "realTimeUpdates": "boolean"
  }
}
```

#### `GET /api/deep-research/projects/[projectId]`
Get a specific research project with statistics.

#### `PUT /api/deep-research/projects/[projectId]`
Update a research project.

#### `DELETE /api/deep-research/projects/[projectId]`
Delete a research project.

### Research Sessions API

#### `POST /api/deep-research/research`
Start a new research session.

**Request Body:**
```json
{
  "query": "string",
  "projectId": "string (optional)",
  "options": {
    "sources": {
      "web": {
        "enabled": "boolean",
        "maxResults": "number",
        "domains": ["string"],
        "excludeDomains": ["string"]
      },
      "academic": {
        "enabled": "boolean",
        "maxResults": "number",
        "databases": ["arxiv", "pubmed", "scholar"],
        "dateRange": {
          "from": "string (ISO date)",
          "to": "string (ISO date)"
        }
      },
      "news": {
        "enabled": "boolean",
        "maxResults": "number",
        "sources": ["string"],
        "languages": ["string"]
      }
    },
    "analysis": {
      "summarize": "boolean",
      "extractEntities": "boolean",
      "detectBias": "boolean",
      "checkCredibility": "boolean",
      "findRelationships": "boolean"
    }
  }
}
```

#### `GET /api/deep-research/research/[sessionId]`
Get research session status and progress.

#### `DELETE /api/deep-research/research/[sessionId]`
Cancel a running research session.

### Sources and Analyses API

#### `GET /api/deep-research/projects/[projectId]/sources`
Get all sources for a research project.

**Query Parameters:**
- `type` (optional): Filter by source type
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset (default: 0)

#### `GET /api/deep-research/projects/[projectId]/analyses`
Get all analyses for a research project.

**Query Parameters:**
- `type` (optional): Filter by analysis type
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset (default: 0)

### Templates API

#### `GET /api/deep-research/templates`
Get all available research templates.

**Query Parameters:**
- `category` (optional): Filter by template category

**Response:**
```json
{
  "templates": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "icon": "string",
      "category": "string",
      "options": { ... },
      "settings": { ... }
    }
  ],
  "categories": ["string"]
}
```

## React Hooks

### Core Research Hooks

#### `useResearchProjects()`
Manages research projects with CRUD operations.

```typescript
const {
  projects,
  isLoading,
  error,
  createProject,
  updateProject,
  deleteProject,
  refresh
} = useResearchProjects();
```

#### `useResearchProject(projectId)`
Manages a specific research project.

```typescript
const {
  project,
  stats,
  isLoading,
  error,
  refresh
} = useResearchProject(projectId);
```

#### `useResearchSession()`
Manages research sessions.

```typescript
const {
  currentSession,
  isStarting,
  error,
  startResearch,
  getSessionStatus,
  cancelSession,
  clearError
} = useResearchSession();
```

#### `useResearchSessionPolling(sessionId, interval)`
Polls research session status for real-time updates.

```typescript
const {
  session,
  isPolling
} = useResearchSessionPolling(sessionId, 5000);
```

### Source and Analysis Hooks

#### `useResearchSources(projectId)`
Manages research sources for a project.

```typescript
const {
  sources,
  total,
  isLoading,
  error,
  getSourcesByType,
  getSourcesByCredibility,
  getSourcesByRelevance,
  refresh
} = useResearchSources(projectId);
```

#### `useResearchAnalyses(projectId)`
Manages research analyses for a project.

```typescript
const {
  analyses,
  total,
  isLoading,
  error,
  getAnalysesByType,
  getLatestAnalyses,
  getHighConfidenceAnalyses,
  refresh
} = useResearchAnalyses(projectId);
```

#### `useResearchInsights(projectId)`
Provides processed insights and summaries.

```typescript
const {
  summaries,
  insights,
  biasAnalyses,
  credibilityAnalyses,
  entityAnalyses,
  getOverallBiasScore,
  getOverallCredibilityScore,
  getTopEntities,
  getKeyInsights
} = useResearchInsights(projectId);
```

#### `useResearchStats(projectId)`
Provides research statistics and metrics.

```typescript
const {
  sourceStats,
  analysisStats
} = useResearchStats(projectId);
```

### Template Hooks

#### `useResearchTemplates(category?)`
Manages research templates.

```typescript
const {
  templates,
  categories,
  isLoading,
  error,
  getTemplateById,
  getTemplatesByCategory,
  createProjectFromTemplate,
  startResearchFromTemplate
} = useResearchTemplates();
```

#### `useTemplateRecommendations(query)`
Provides template recommendations based on query.

```typescript
const {
  recommendations,
  getRecommendations
} = useTemplateRecommendations(query);
```

#### `useTemplateCustomization()`
Utilities for customizing templates.

```typescript
const {
  customizeTemplate,
  validateTemplate
} = useTemplateCustomization();
```

### Monitoring Hooks

#### `useResearchMonitoring(sessionId)`
Provides real-time monitoring of research sessions.

```typescript
const {
  progress,
  events,
  isConnected,
  isMonitoring,
  getProgressPercentage,
  getCurrentPhaseDescription,
  getEstimatedTimeRemaining,
  startMonitoring,
  stopMonitoring,
  reconnectAttempts
} = useResearchMonitoring(sessionId);
```

## Usage Examples

### Starting a Research Session

```typescript
import { useResearchSession, useResearchTemplates } from '@/hooks/deep-research';

function ResearchComponent() {
  const { startResearch, currentSession, isStarting } = useResearchSession();
  const { getTemplateById } = useResearchTemplates();

  const handleStartResearch = async () => {
    const template = getTemplateById('academic-literature-review');
    
    await startResearch(
      'machine learning in healthcare',
      template.options
    );
  };

  return (
    <div>
      <button onClick={handleStartResearch} disabled={isStarting}>
        {isStarting ? 'Starting...' : 'Start Research'}
      </button>
      
      {currentSession && (
        <div>Session ID: {currentSession.id}</div>
      )}
    </div>
  );
}
```

### Monitoring Research Progress

```typescript
import { useResearchMonitoring } from '@/hooks/deep-research';

function ProgressMonitor({ sessionId }) {
  const {
    progress,
    getProgressPercentage,
    getCurrentPhaseDescription,
    getEstimatedTimeRemaining
  } = useResearchMonitoring(sessionId);

  if (!progress) return <div>Loading...</div>;

  return (
    <div>
      <div>Progress: {getProgressPercentage()}%</div>
      <div>Phase: {getCurrentPhaseDescription()}</div>
      <div>ETA: {getEstimatedTimeRemaining()}</div>
    </div>
  );
}
```

### Displaying Research Results

```typescript
import { useResearchInsights, useResearchSources } from '@/hooks/deep-research';

function ResearchResults({ projectId }) {
  const { getTopEntities, getKeyInsights } = useResearchInsights(projectId);
  const { getSourcesByCredibility } = useResearchSources(projectId);

  const topEntities = getTopEntities(10);
  const keyInsights = getKeyInsights();
  const credibleSources = getSourcesByCredibility(0.8);

  return (
    <div>
      <h3>Key Entities</h3>
      {topEntities.map(entity => (
        <div key={entity.name}>{entity.name} ({entity.type})</div>
      ))}

      <h3>Key Insights</h3>
      {keyInsights.map((insight, index) => (
        <div key={index}>{insight}</div>
      ))}

      <h3>Credible Sources ({credibleSources.length})</h3>
      {credibleSources.map(source => (
        <div key={source.id}>
          <a href={source.url}>{source.title}</a>
          <span> (Credibility: {source.credibilityScore})</span>
        </div>
      ))}
    </div>
  );
}
```
