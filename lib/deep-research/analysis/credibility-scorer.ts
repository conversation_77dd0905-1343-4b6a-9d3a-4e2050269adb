/**
 * Credibility Scorer
 * 
 * Assesses the credibility of research sources using multiple factors.
 */

import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { deepResearchConfig } from '../config';
import { ResearchSource } from '../types';

export class CredibilityScorer {
  async scoreSource(source: ResearchSource): Promise<number> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.credibility),
        temperature: 0.1,
        prompt: `Assess the credibility of this source on a scale of 0.0 to 1.0:

Title: ${source.title}
URL: ${source.url}
Author: ${source.author || 'Unknown'}
Content: ${source.content.processed?.substring(0, 1000)}

Consider:
- Source authority and reputation
- Author expertise
- Evidence quality
- Fact-checking potential
- Transparency

Return only the numeric score.`,
      });

      const score = parseFloat(text.trim());
      return isNaN(score) ? 0.5 : Math.max(0, Math.min(1, score));
    } catch (error) {
      console.error('Error scoring credibility:', error);
      return 0.5;
    }
  }
}
