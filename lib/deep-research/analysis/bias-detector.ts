/**
 * Bias Detector
 * 
 * Detects various types of bias in research content.
 */

import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { deepResearchConfig } from '../config';
import { ResearchSource } from '../types';

export class BiasDetector {
  async detectBias(source: ResearchSource): Promise<any> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.bias),
        temperature: 0.1,
        prompt: `Analyze this content for bias and return a JSON object:

Content: ${source.content.processed?.substring(0, 2000)}

Detect:
- Political bias (left/right/center)
- Commercial bias
- Confirmation bias
- Selection bias
- Cultural bias

Format:
{
  "politicalBias": {"direction": "center", "strength": 0.2},
  "commercialBias": {"present": false, "strength": 0.0},
  "overallBiasScore": 0.2
}`,
      });

      return JSON.parse(text);
    } catch (error) {
      console.error('Error detecting bias:', error);
      return { overallBiasScore: 0.5 };
    }
  }
}
