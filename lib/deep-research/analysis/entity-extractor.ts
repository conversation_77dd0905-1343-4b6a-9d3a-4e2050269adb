/**
 * Entity Extractor
 *
 * Extracts entities (people, organizations, concepts) from research content.
 */

import { EventEmitter } from 'events';
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { deepResearchConfig } from '../config';
import { ResearchSource, KnowledgeEntity } from '../types';

export class EntityExtractor extends EventEmitter {
  async extractEntities(source: ResearchSource): Promise<KnowledgeEntity[]> {
    try {
      const { text } = await generateText({
        model: openai(deepResearchConfig.ai.models.entities),
        temperature: 0.1,
        prompt: `Extract entities from this content and return as JSON:

Content: ${source.content.processed?.substring(0, 2000)}

Extract:
- People (names, roles)
- Organizations (companies, institutions)
- Concepts (technologies, methodologies)
- Locations (places, regions)

Format:
{
  "entities": [
    {"name": "John Doe", "type": "person", "confidence": 0.9},
    {"name": "MIT", "type": "organization", "confidence": 0.95}
  ]
}`,
      });

      const result = JSON.parse(text);
      const entities: KnowledgeEntity[] = [];

      for (const entity of result.entities || []) {
        entities.push({
          id: `entity_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          name: entity.name,
          type: entity.type,
          confidence: entity.confidence || 0.8,
          aliases: [],
          sources: [source.id],
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }

      return entities;
    } catch (error) {
      console.error('Error extracting entities:', error);
      return [];
    }
  }
}
