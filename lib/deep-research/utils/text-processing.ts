/**
 * Text Processing Utilities
 *
 * Advanced text processing utilities for content extraction, cleaning,
 * and analysis in the deep research system.
 */

// Note: JSDOM would be ideal but we'll use a simpler approach for now
// import { JSDOM } from 'jsdom';

/**
 * Text Processor
 *
 * Handles text extraction, cleaning, and processing operations
 */
export class TextProcessor {
  private stopWords: Set<string>;
  private htmlTagRegex: RegExp;
  private whitespaceRegex: RegExp;
  private urlRegex: RegExp;
  private emailRegex: RegExp;

  constructor() {
    this.stopWords = new Set([
      'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
      'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
      'to', 'was', 'will', 'with', 'would', 'could', 'should', 'may',
      'might', 'can', 'must', 'shall', 'this', 'these', 'those', 'they',
      'them', 'their', 'there', 'where', 'when', 'why', 'how', 'what',
      'who', 'which', 'whom', 'whose', 'if', 'then', 'else', 'do', 'did',
      'does', 'have', 'had', 'having', 'been', 'being', 'get', 'got',
      'getting', 'go', 'going', 'went', 'gone', 'come', 'came', 'coming',
      'see', 'saw', 'seen', 'seeing', 'know', 'knew', 'known', 'knowing'
    ]);

    this.htmlTagRegex = /<[^>]*>/g;
    this.whitespaceRegex = /\s+/g;
    this.urlRegex = /https?:\/\/[^\s]+/g;
    this.emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
  }

  /**
   * Extract text content from HTML
   */
  extractTextFromHtml(html: string): string {
    try {
      // Simple HTML text extraction without JSDOM
      let text = html
        // Remove script and style content
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, ' ')
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, ' ')
        .replace(/<nav[^>]*>[\s\S]*?<\/nav>/gi, ' ')
        .replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi, ' ')
        .replace(/<header[^>]*>[\s\S]*?<\/header>/gi, ' ')
        .replace(/<aside[^>]*>[\s\S]*?<\/aside>/gi, ' ')
        // Remove all HTML tags
        .replace(this.htmlTagRegex, ' ')
        // Decode common HTML entities
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");

      // Clean up the text
      return this.cleanText(text);
    } catch (error) {
      console.error('Error extracting text from HTML:', error);
      // Fallback: simple HTML tag removal
      return this.cleanText(html.replace(this.htmlTagRegex, ' '));
    }
  }

  /**
   * Clean and normalize text
   */
  cleanText(text: string): string {
    return text
      // Remove URLs
      .replace(this.urlRegex, ' ')
      // Remove email addresses
      .replace(this.emailRegex, ' ')
      // Remove extra whitespace
      .replace(this.whitespaceRegex, ' ')
      // Remove non-printable characters
      .replace(/[\x00-\x1F\x7F-\x9F]/g, ' ')
      // Trim and normalize
      .trim();
  }

  /**
   * Extract title from HTML content
   */
  extractTitle(html: string): string | null {
    try {
      // Simple title extraction without JSDOM
      const titlePatterns = [
        /<title[^>]*>([^<]+)<\/title>/i,
        /<h1[^>]*>([^<]+)<\/h1>/i,
        /<meta[^>]*property="og:title"[^>]*content="([^"]+)"/i,
        /<meta[^>]*name="twitter:title"[^>]*content="([^"]+)"/i,
        /<h2[^>]*>([^<]+)<\/h2>/i
      ];

      for (const pattern of titlePatterns) {
        const match = html.match(pattern);
        if (match && match[1] && match[1].trim().length > 0) {
          return this.cleanText(match[1]).substring(0, 200);
        }
      }

      return null;
    } catch (error) {
      console.error('Error extracting title:', error);
      return null;
    }
  }

  /**
   * Extract metadata from HTML
   */
  extractMetadata(html: string): {
    title?: string;
    description?: string;
    author?: string;
    publishDate?: string;
    keywords?: string[];
    language?: string;
  } {
    try {
      const metadata: any = {};

      // Title
      metadata.title = this.extractTitle(html);

      // Description
      const descriptionPatterns = [
        /<meta[^>]*name="description"[^>]*content="([^"]+)"/i,
        /<meta[^>]*property="og:description"[^>]*content="([^"]+)"/i,
        /<meta[^>]*name="twitter:description"[^>]*content="([^"]+)"/i
      ];

      for (const pattern of descriptionPatterns) {
        const match = html.match(pattern);
        if (match && match[1] && match[1].trim().length > 0) {
          metadata.description = this.cleanText(match[1]).substring(0, 500);
          break;
        }
      }

      // Author
      const authorPatterns = [
        /<meta[^>]*name="author"[^>]*content="([^"]+)"/i,
        /<meta[^>]*property="article:author"[^>]*content="([^"]+)"/i,
        /<[^>]*class="[^"]*author[^"]*"[^>]*>([^<]+)</i,
        /<[^>]*class="[^"]*byline[^"]*"[^>]*>([^<]+)</i
      ];

      for (const pattern of authorPatterns) {
        const match = html.match(pattern);
        if (match && match[1] && match[1].trim().length > 0) {
          metadata.author = this.cleanText(match[1]).substring(0, 100);
          break;
        }
      }

      // Publish date
      const datePatterns = [
        /<meta[^>]*property="article:published_time"[^>]*content="([^"]+)"/i,
        /<meta[^>]*name="publish_date"[^>]*content="([^"]+)"/i,
        /<time[^>]*datetime="([^"]+)"/i,
        /<[^>]*class="[^"]*publish-date[^"]*"[^>]*>([^<]+)</i,
        /<[^>]*class="[^"]*date[^"]*"[^>]*>([^<]+)</i
      ];

      for (const pattern of datePatterns) {
        const match = html.match(pattern);
        if (match && match[1] && match[1].trim().length > 0) {
          metadata.publishDate = match[1].trim();
          break;
        }
      }

      // Keywords
      const keywordsMatch = html.match(/<meta[^>]*name="keywords"[^>]*content="([^"]+)"/i);
      if (keywordsMatch && keywordsMatch[1]) {
        metadata.keywords = keywordsMatch[1].split(',').map(k => k.trim()).filter(k => k.length > 0);
      }

      // Language
      const langPatterns = [
        /<html[^>]*lang="([^"]+)"/i,
        /<meta[^>]*property="og:locale"[^>]*content="([^"]+)"/i,
        /<meta[^>]*http-equiv="content-language"[^>]*content="([^"]+)"/i
      ];

      for (const pattern of langPatterns) {
        const match = html.match(pattern);
        if (match && match[1] && match[1].trim().length > 0) {
          metadata.language = match[1].trim().toLowerCase();
          break;
        }
      }

      return metadata;
    } catch (error) {
      console.error('Error extracting metadata:', error);
      return {};
    }
  }

  /**
   * Split text into sentences
   */
  splitIntoSentences(text: string): string[] {
    // Simple sentence splitting - can be enhanced with more sophisticated NLP
    return text
      .split(/[.!?]+/)
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 10); // Filter out very short fragments
  }

  /**
   * Split text into paragraphs
   */
  splitIntoParagraphs(text: string): string[] {
    return text
      .split(/\n\s*\n/)
      .map(paragraph => paragraph.trim())
      .filter(paragraph => paragraph.length > 20); // Filter out very short paragraphs
  }

  /**
   * Extract keywords from text
   */
  extractKeywords(text: string, maxKeywords: number = 20): string[] {
    const words = text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word =>
        word.length > 3 &&
        !this.stopWords.has(word) &&
        !/^\d+$/.test(word) // Exclude pure numbers
      );

    // Count word frequency
    const wordCount = new Map<string, number>();
    words.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });

    // Sort by frequency and return top keywords
    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxKeywords)
      .map(([word]) => word);
  }

  /**
   * Calculate reading time estimate
   */
  calculateReadingTime(text: string, wordsPerMinute: number = 200): number {
    const wordCount = text.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  }

  /**
   * Calculate text complexity score
   */
  calculateComplexityScore(text: string): number {
    const sentences = this.splitIntoSentences(text);
    const words = text.split(/\s+/);

    if (sentences.length === 0 || words.length === 0) {
      return 0;
    }

    // Average sentence length
    const avgSentenceLength = words.length / sentences.length;

    // Average word length
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;

    // Syllable count estimation (rough)
    const avgSyllables = words.reduce((sum, word) => {
      const syllables = word.toLowerCase().replace(/[^aeiou]/g, '').length || 1;
      return sum + syllables;
    }, 0) / words.length;

    // Flesch Reading Ease approximation
    const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllables);

    // Normalize to 0-1 scale (higher = more complex)
    return Math.max(0, Math.min(1, (100 - score) / 100));
  }

  /**
   * Extract quoted text
   */
  extractQuotes(text: string): string[] {
    const quotes: string[] = [];

    // Match text in quotes
    const quotePatterns = [
      /"([^"]+)"/g,  // Double quotes
      /'([^']+)'/g,  // Single quotes
      /[""]([^""]+)["]/g,  // Smart quotes
      /['']([^'']+)[']/g   // Smart single quotes
    ];

    quotePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const quote = match[1].trim();
        if (quote.length > 10) { // Only include substantial quotes
          quotes.push(quote);
        }
      }
    });

    return quotes;
  }

  /**
   * Truncate text to specified length with word boundaries
   */
  truncateText(text: string, maxLength: number, suffix: string = '...'): string {
    if (text.length <= maxLength) {
      return text;
    }

    const truncated = text.substring(0, maxLength - suffix.length);
    const lastSpace = truncated.lastIndexOf(' ');

    if (lastSpace > 0) {
      return truncated.substring(0, lastSpace) + suffix;
    }

    return truncated + suffix;
  }

  /**
   * Remove stop words from text
   */
  removeStopWords(text: string): string {
    return text
      .split(/\s+/)
      .filter(word => !this.stopWords.has(word.toLowerCase()))
      .join(' ');
  }

  /**
   * Detect language of text (basic implementation)
   */
  detectLanguage(text: string): string {
    // Very basic language detection based on common words
    const sample = text.toLowerCase().substring(0, 1000);

    const languagePatterns = {
      'en': /\b(the|and|or|but|in|on|at|to|for|of|with|by)\b/g,
      'es': /\b(el|la|los|las|y|o|pero|en|de|con|por|para)\b/g,
      'fr': /\b(le|la|les|et|ou|mais|dans|de|avec|par|pour)\b/g,
      'de': /\b(der|die|das|und|oder|aber|in|von|mit|für)\b/g,
      'it': /\b(il|la|i|le|e|o|ma|in|di|con|per)\b/g
    };

    let maxMatches = 0;
    let detectedLanguage = 'en'; // Default to English

    for (const [lang, pattern] of Object.entries(languagePatterns)) {
      const matches = (sample.match(pattern) || []).length;
      if (matches > maxMatches) {
        maxMatches = matches;
        detectedLanguage = lang;
      }
    }

    return detectedLanguage;
  }
}
