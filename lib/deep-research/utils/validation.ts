/**
 * Validation Utilities
 * 
 * Provides validation functions for deep research data.
 */

import { z } from 'zod';
import {
  researchProjectSchema,
  researchQuerySchema,
  multiSourceResearchSchema
} from '../types';

export class ValidationUtils {
  static validateProject(data: any) {
    return researchProjectSchema.parse(data);
  }

  static validateQuery(data: any) {
    return researchQuerySchema.parse(data);
  }

  static validateMultiSourceOptions(data: any) {
    return multiSourceResearchSchema.parse(data);
  }

  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static sanitizeText(text: string): string {
    return text.replace(/[<>]/g, '').trim();
  }
}
