/**
 * Rate Limiter Utility
 * 
 * Implements sophisticated rate limiting for API calls and resource management
 * with support for different rate limits per service and adaptive throttling.
 */

import { deepResearchConfig } from '../config';

interface RateLimitConfig {
  requestsPerMinute: number;
  burstLimit: number;
  backoffMultiplier: number;
  maxBackoffTime: number;
}

interface ServiceLimits {
  [serviceName: string]: RateLimitConfig;
}

/**
 * Rate Limiter
 * 
 * Manages rate limiting across multiple services with adaptive throttling
 */
export class RateLimiter {
  private serviceLimits: ServiceLimits;
  private requestCounts: Map<string, number[]> = new Map();
  private backoffTimes: Map<string, number> = new Map();
  private lastRequestTimes: Map<string, number> = new Map();

  constructor() {
    this.serviceLimits = {
      default: {
        requestsPerMinute: deepResearchConfig.performance.rateLimit.requestsPerMinute,
        burstLimit: deepResearchConfig.performance.rateLimit.burstLimit,
        backoffMultiplier: 1.5,
        maxBackoffTime: 60000 // 1 minute
      },
      browserless: {
        requestsPerMinute: 30,
        burstLimit: 5,
        backoffMultiplier: 2.0,
        maxBackoffTime: 120000 // 2 minutes
      },
      openai: {
        requestsPerMinute: 60,
        burstLimit: 10,
        backoffMultiplier: 1.2,
        maxBackoffTime: 30000 // 30 seconds
      },
      google: {
        requestsPerMinute: 20,
        burstLimit: 3,
        backoffMultiplier: 3.0,
        maxBackoffTime: 300000 // 5 minutes
      },
      newsapi: {
        requestsPerMinute: 15,
        burstLimit: 2,
        backoffMultiplier: 2.5,
        maxBackoffTime: 180000 // 3 minutes
      }
    };
  }

  /**
   * Wait for an available slot (default service)
   */
  async waitForSlot(): Promise<void> {
    return this.waitForServiceSlot('default');
  }

  /**
   * Wait for an available slot for a specific service
   */
  async waitForServiceSlot(serviceName: string): Promise<void> {
    const config = this.serviceLimits[serviceName] || this.serviceLimits.default;
    const now = Date.now();
    
    // Check if we're in a backoff period
    const backoffTime = this.backoffTimes.get(serviceName) || 0;
    if (backoffTime > now) {
      const waitTime = backoffTime - now;
      console.log(`Rate limiter: Waiting ${waitTime}ms for ${serviceName} backoff`);
      await this.sleep(waitTime);
    }

    // Clean old request timestamps (older than 1 minute)
    this.cleanOldRequests(serviceName);

    // Get current request count
    const requests = this.requestCounts.get(serviceName) || [];
    
    // Check if we're within rate limits
    if (requests.length >= config.requestsPerMinute) {
      // Calculate wait time until oldest request expires
      const oldestRequest = requests[0];
      const waitTime = 60000 - (now - oldestRequest); // 60 seconds
      
      if (waitTime > 0) {
        console.log(`Rate limiter: Waiting ${waitTime}ms for ${serviceName} rate limit`);
        await this.sleep(waitTime);
        return this.waitForServiceSlot(serviceName); // Recursive call after waiting
      }
    }

    // Check burst limit
    const recentRequests = requests.filter(timestamp => now - timestamp < 10000); // Last 10 seconds
    if (recentRequests.length >= config.burstLimit) {
      const waitTime = 10000; // Wait 10 seconds
      console.log(`Rate limiter: Waiting ${waitTime}ms for ${serviceName} burst limit`);
      await this.sleep(waitTime);
      return this.waitForServiceSlot(serviceName); // Recursive call after waiting
    }

    // Record this request
    this.recordRequest(serviceName);
  }

  /**
   * Record a successful request
   */
  recordRequest(serviceName: string): void {
    const now = Date.now();
    const requests = this.requestCounts.get(serviceName) || [];
    requests.push(now);
    this.requestCounts.set(serviceName, requests);
    this.lastRequestTimes.set(serviceName, now);
    
    // Reset backoff time on successful request
    this.backoffTimes.delete(serviceName);
  }

  /**
   * Record a failed request (triggers backoff)
   */
  recordFailure(serviceName: string, error?: Error): void {
    const config = this.serviceLimits[serviceName] || this.serviceLimits.default;
    const currentBackoff = this.backoffTimes.get(serviceName) || 1000; // Start with 1 second
    
    // Calculate new backoff time
    const newBackoff = Math.min(
      currentBackoff * config.backoffMultiplier,
      config.maxBackoffTime
    );
    
    const backoffUntil = Date.now() + newBackoff;
    this.backoffTimes.set(serviceName, backoffUntil);
    
    console.log(`Rate limiter: ${serviceName} failed, backing off for ${newBackoff}ms`);
    
    // Log error details if provided
    if (error) {
      console.error(`Rate limiter: ${serviceName} error:`, error.message);
    }
  }

  /**
   * Check if a service is currently available (not in backoff)
   */
  isServiceAvailable(serviceName: string): boolean {
    const backoffTime = this.backoffTimes.get(serviceName) || 0;
    return backoffTime <= Date.now();
  }

  /**
   * Get current rate limit status for a service
   */
  getServiceStatus(serviceName: string): {
    available: boolean;
    requestsInLastMinute: number;
    requestsInLastTenSeconds: number;
    backoffUntil?: number;
    nextAvailableSlot: number;
  } {
    const config = this.serviceLimits[serviceName] || this.serviceLimits.default;
    const now = Date.now();
    
    this.cleanOldRequests(serviceName);
    const requests = this.requestCounts.get(serviceName) || [];
    const recentRequests = requests.filter(timestamp => now - timestamp < 10000);
    
    const backoffUntil = this.backoffTimes.get(serviceName);
    const available = !backoffUntil || backoffUntil <= now;
    
    let nextAvailableSlot = now;
    
    if (backoffUntil && backoffUntil > now) {
      nextAvailableSlot = backoffUntil;
    } else if (requests.length >= config.requestsPerMinute) {
      nextAvailableSlot = requests[0] + 60000; // When oldest request expires
    } else if (recentRequests.length >= config.burstLimit) {
      nextAvailableSlot = now + 10000; // Wait for burst window
    }

    return {
      available,
      requestsInLastMinute: requests.length,
      requestsInLastTenSeconds: recentRequests.length,
      backoffUntil,
      nextAvailableSlot
    };
  }

  /**
   * Clean old request timestamps
   */
  private cleanOldRequests(serviceName: string): void {
    const now = Date.now();
    const requests = this.requestCounts.get(serviceName) || [];
    const recentRequests = requests.filter(timestamp => now - timestamp < 60000);
    this.requestCounts.set(serviceName, recentRequests);
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Reset rate limits for a service (useful for testing)
   */
  resetService(serviceName: string): void {
    this.requestCounts.delete(serviceName);
    this.backoffTimes.delete(serviceName);
    this.lastRequestTimes.delete(serviceName);
  }

  /**
   * Reset all rate limits
   */
  resetAll(): void {
    this.requestCounts.clear();
    this.backoffTimes.clear();
    this.lastRequestTimes.clear();
  }

  /**
   * Update rate limits for a service
   */
  updateServiceLimits(serviceName: string, limits: Partial<RateLimitConfig>): void {
    const currentLimits = this.serviceLimits[serviceName] || this.serviceLimits.default;
    this.serviceLimits[serviceName] = { ...currentLimits, ...limits };
  }

  /**
   * Get statistics for all services
   */
  getGlobalStats(): {
    totalRequests: number;
    activeServices: number;
    servicesInBackoff: number;
    averageRequestsPerMinute: number;
  } {
    const now = Date.now();
    let totalRequests = 0;
    let servicesInBackoff = 0;
    
    for (const [serviceName, requests] of this.requestCounts.entries()) {
      totalRequests += requests.length;
      
      const backoffTime = this.backoffTimes.get(serviceName);
      if (backoffTime && backoffTime > now) {
        servicesInBackoff++;
      }
    }

    return {
      totalRequests,
      activeServices: this.requestCounts.size,
      servicesInBackoff,
      averageRequestsPerMinute: this.requestCounts.size > 0 ? totalRequests / this.requestCounts.size : 0
    };
  }

  /**
   * Execute a function with rate limiting for a specific service
   */
  async executeWithRateLimit<T>(
    serviceName: string,
    fn: () => Promise<T>,
    retries: number = 3
  ): Promise<T> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        await this.waitForServiceSlot(serviceName);
        const result = await fn();
        this.recordRequest(serviceName);
        return result;
      } catch (error) {
        this.recordFailure(serviceName, error as Error);
        
        if (attempt === retries) {
          throw error;
        }
        
        // Wait before retry
        const backoffTime = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        await this.sleep(backoffTime);
      }
    }
    
    throw new Error(`Failed after ${retries} attempts`);
  }
}
