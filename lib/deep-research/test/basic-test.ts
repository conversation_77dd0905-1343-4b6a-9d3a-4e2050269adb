/**
 * Basic Tests for Deep Research Library
 * 
 * Simple tests to verify the library functionality.
 */

import { deepResearchService } from '../index';
import { TextProcessor } from '../utils/text-processing';
import { RateLimiter } from '../utils/rate-limiter';

/**
 * Test project creation and management
 */
export async function testProjectManagement() {
  console.log('🧪 Testing project management...');

  try {
    // Create a test project
    const project = await deepResearchService.createProject(
      'Test Project',
      'A test project for the deep research library',
      {
        autoAnalysis: true,
        sourceTypes: ['web'],
        maxSources: 5
      }
    );

    console.log('✅ Project created:', project.id);

    // Get the project
    const retrievedProject = await deepResearchService.getProject(project.id);
    console.log('✅ Project retrieved:', retrievedProject?.title);

    // List projects
    const projects = await deepResearchService.listProjects();
    console.log('✅ Projects listed:', projects.length);

    // Update project
    const updatedProject = await deepResearchService.updateProject(project.id, {
      title: 'Updated Test Project'
    });
    console.log('✅ Project updated:', updatedProject.title);

    // Delete project
    const deleted = await deepResearchService.deleteProject(project.id);
    console.log('✅ Project deleted:', deleted);

    return true;
  } catch (error) {
    console.error('❌ Project management test failed:', error);
    return false;
  }
}

/**
 * Test text processing utilities
 */
export function testTextProcessing() {
  console.log('🧪 Testing text processing...');

  try {
    const processor = new TextProcessor();

    // Test HTML text extraction
    const html = `
      <html>
        <head><title>Test Page</title></head>
        <body>
          <h1>Main Heading</h1>
          <p>This is a test paragraph with <strong>bold text</strong>.</p>
          <script>console.log('script');</script>
          <style>body { color: red; }</style>
        </body>
      </html>
    `;

    const extractedText = processor.extractTextFromHtml(html);
    console.log('✅ Text extracted:', extractedText.substring(0, 50) + '...');

    // Test title extraction
    const title = processor.extractTitle(html);
    console.log('✅ Title extracted:', title);

    // Test text cleaning
    const dirtyText = '  This   is   dirty    text   with   extra   spaces  ';
    const cleanText = processor.cleanText(dirtyText);
    console.log('✅ Text cleaned:', cleanText);

    // Test keyword extraction
    const content = 'Artificial intelligence and machine learning are transforming technology. AI systems use neural networks and deep learning algorithms.';
    const keywords = processor.extractKeywords(content, 5);
    console.log('✅ Keywords extracted:', keywords);

    // Test sentence splitting
    const sentences = processor.splitIntoSentences(content);
    console.log('✅ Sentences split:', sentences.length);

    return true;
  } catch (error) {
    console.error('❌ Text processing test failed:', error);
    return false;
  }
}

/**
 * Test rate limiting
 */
export async function testRateLimiting() {
  console.log('🧪 Testing rate limiting...');

  try {
    const rateLimiter = new RateLimiter();

    // Test basic rate limiting
    console.log('Testing basic rate limiting...');
    await rateLimiter.waitForSlot();
    console.log('✅ First request allowed');

    await rateLimiter.waitForSlot();
    console.log('✅ Second request allowed');

    // Test service-specific rate limiting
    console.log('Testing service-specific rate limiting...');
    await rateLimiter.waitForServiceSlot('openai');
    console.log('✅ OpenAI service request allowed');

    // Test rate limit status
    const status = rateLimiter.getServiceStatus('default');
    console.log('✅ Rate limit status:', status);

    // Test failure recording
    rateLimiter.recordFailure('test-service');
    const testStatus = rateLimiter.getServiceStatus('test-service');
    console.log('✅ Failure recorded, service available:', testStatus.available);

    return true;
  } catch (error) {
    console.error('❌ Rate limiting test failed:', error);
    return false;
  }
}

/**
 * Test configuration loading
 */
export function testConfiguration() {
  console.log('🧪 Testing configuration...');

  try {
    const config = require('../config').deepResearchConfig;
    
    console.log('✅ Config loaded');
    console.log('AI models configured:', Object.keys(config.ai.models).length);
    console.log('Source types configured:', Object.keys(config.sources).length);
    console.log('Performance settings:', config.performance ? 'configured' : 'missing');

    return true;
  } catch (error) {
    console.error('❌ Configuration test failed:', error);
    return false;
  }
}

/**
 * Test library imports
 */
export function testImports() {
  console.log('🧪 Testing library imports...');

  try {
    // Test main exports
    const { deepResearchService } = require('../index');
    console.log('✅ Main service imported');

    // Test component imports
    const { WebCollector } = require('../sources/web-collector');
    const { AIAnalyzer } = require('../analysis/ai-analyzer');
    const { DatabaseAdapter } = require('../storage/database-adapter');
    console.log('✅ Components imported');

    // Test utility imports
    const { TextProcessor } = require('../utils/text-processing');
    const { RateLimiter } = require('../utils/rate-limiter');
    console.log('✅ Utilities imported');

    return true;
  } catch (error) {
    console.error('❌ Import test failed:', error);
    return false;
  }
}

/**
 * Run all tests
 */
export async function runAllTests() {
  console.log('🚀 Running Deep Research Library Tests\n');

  const tests = [
    { name: 'Imports', test: testImports },
    { name: 'Configuration', test: testConfiguration },
    { name: 'Text Processing', test: testTextProcessing },
    { name: 'Rate Limiting', test: testRateLimiting },
    { name: 'Project Management', test: testProjectManagement }
  ];

  const results = [];

  for (const { name, test } of tests) {
    console.log(`\n--- Testing ${name} ---`);
    try {
      const result = await test();
      results.push({ name, passed: result });
      console.log(`${result ? '✅' : '❌'} ${name} test ${result ? 'passed' : 'failed'}`);
    } catch (error) {
      results.push({ name, passed: false, error });
      console.log(`❌ ${name} test failed with error:`, error);
    }
  }

  console.log('\n📊 Test Results Summary:');
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  console.log(`Passed: ${passed}/${total}`);
  
  results.forEach(result => {
    console.log(`${result.passed ? '✅' : '❌'} ${result.name}`);
  });

  if (passed === total) {
    console.log('\n🎉 All tests passed! The Deep Research Library is ready to use.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the configuration and dependencies.');
  }

  return { passed, total, results };
}

// Export test functions
export const tests = {
  testProjectManagement,
  testTextProcessing,
  testRateLimiting,
  testConfiguration,
  testImports,
  runAllTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
