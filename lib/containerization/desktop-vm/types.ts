/**
 * Desktop VM Types
 * 
 * Type definitions for desktop virtual machine management and AI automation.
 */

export type DesktopVmStatus = 'stopped' | 'starting' | 'running' | 'stopping' | 'error';

export type DesktopEnvironment = 'xfce' | 'gnome' | 'kde' | 'lxde';

export interface DesktopVmConfig {
  name: string;
  desktopEnvironment: DesktopEnvironment;
  resolution: string;
  enableAudio: boolean;
  enableClipboard: boolean;
  enableFileSharing: boolean;
  aiAutomationEnabled: boolean;
  customPackages?: string[];
  environmentVariables?: Record<string, string>;
}

export interface DesktopVmResources {
  cpu: string;
  memory: string;
  disk: string;
  network: string;
}

export interface DesktopVm {
  id: string;
  config: DesktopVmConfig;
  status: DesktopVmStatus;
  microVmId: string;
  createdAt: string;
  lastActivity: string;
  resources?: DesktopVmResources;
  error?: string;
}

// AI Automation Types

export type AutomationActionType = 
  | 'mouse_click'
  | 'mouse_double_click'
  | 'mouse_right_click'
  | 'mouse_move'
  | 'keyboard_type'
  | 'keyboard_key'
  | 'screenshot'
  | 'wait'
  | 'scroll'
  | 'drag_drop';

export type AutomationActionStatus = 'pending' | 'executing' | 'completed' | 'failed';

export interface AutomationAction {
  id: string;
  type: AutomationActionType;
  status: AutomationActionStatus;
  timestamp: string;
  description?: string;
  parameters?: Record<string, any>;
  result?: any;
  error?: string;
  duration?: number;
}

export interface AutomationSession {
  id: string;
  vmId: string;
  startTime: string;
  endTime?: string;
  status: 'active' | 'completed' | 'failed';
  actions: AutomationAction[];
  metadata?: Record<string, any>;
}

export interface ScreenshotData {
  id: string;
  vmId: string;
  timestamp: string;
  width: number;
  height: number;
  format: 'png' | 'jpeg';
  dataUrl: string;
  size: number;
  metadata?: Record<string, any>;
}

export interface MouseAction {
  type: 'click' | 'double-click' | 'right-click' | 'move';
  x: number;
  y: number;
  button?: 'left' | 'right' | 'middle';
  modifiers?: string[];
}

export interface KeyboardAction {
  type: 'type' | 'key' | 'combination';
  text?: string;
  keys?: string[];
  modifiers?: string[];
}

export interface AutomationPrompt {
  id: string;
  text: string;
  timestamp: string;
  vmId: string;
  sessionId?: string;
  actions?: AutomationAction[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
}

// Hook Types

export interface UseDesktopVmManagerResult {
  vmId?: string;
  vmStatus: DesktopVmStatus;
  vmConfig?: DesktopVmConfig;
  vms: DesktopVm[];
  isLoading: boolean;
  error?: string;
  createDesktopVm: (config: DesktopVmConfig) => Promise<DesktopVm>;
  startVm: (vmId?: string) => Promise<void>;
  stopVm: (vmId?: string) => Promise<void>;
  restartVm: (vmId?: string) => Promise<void>;
  deleteVm: (vmId?: string) => Promise<void>;
  updateVmConfig: (updates: Partial<DesktopVmConfig>, vmId?: string) => Promise<void>;
  getConnectionToken: (vmId?: string) => Promise<string>;
  refreshVms: () => Promise<void>;
}

export interface UseAiDesktopAutomationResult {
  isAutomationActive: boolean;
  currentSession?: AutomationSession;
  automationError?: string;
  startAutomation: () => Promise<AutomationSession>;
  stopAutomation: () => Promise<AutomationSession>;
  executeAutomationPrompt: (prompt: string) => Promise<AutomationAction[]>;
  takeScreenshot: () => Promise<ScreenshotData>;
  executeMouseAction: (action: string, x: number, y: number) => Promise<AutomationAction>;
  executeKeyboardAction: (text: string, keys?: string[]) => Promise<AutomationAction>;
  startSessionRecording: () => Promise<void>;
  stopSessionRecording: () => Promise<void>;
  getSessionHistory: () => Promise<AutomationAction[]>;
  exportSession: (sessionId: string) => Promise<any>;
  importSession: (sessionData: any) => Promise<AutomationSession>;
}

// API Types

export interface CreateDesktopVmRequest {
  config: DesktopVmConfig;
}

export interface CreateDesktopVmResponse {
  vm: DesktopVm;
}

export interface StartVmRequest {
  vmId: string;
}

export interface StartVmResponse {
  success: boolean;
  message?: string;
}

export interface StopVmRequest {
  vmId: string;
}

export interface StopVmResponse {
  success: boolean;
  message?: string;
}

export interface UpdateVmConfigRequest {
  vmId: string;
  updates: Partial<DesktopVmConfig>;
}

export interface UpdateVmConfigResponse {
  success: boolean;
  vm: DesktopVm;
}

export interface GetConnectionTokenRequest {
  vmId: string;
}

export interface GetConnectionTokenResponse {
  token: string;
  wsUrl: string;
  expiresAt: string;
}

export interface ExecuteAutomationRequest {
  vmId: string;
  prompt: string;
  sessionId?: string;
}

export interface ExecuteAutomationResponse {
  actions: AutomationAction[];
  sessionId: string;
}

export interface TakeScreenshotRequest {
  vmId: string;
  format?: 'png' | 'jpeg';
  quality?: number;
}

export interface TakeScreenshotResponse {
  screenshot: ScreenshotData;
}

export interface ExecuteMouseActionRequest {
  vmId: string;
  action: MouseAction;
  sessionId?: string;
}

export interface ExecuteMouseActionResponse {
  action: AutomationAction;
}

export interface ExecuteKeyboardActionRequest {
  vmId: string;
  action: KeyboardAction;
  sessionId?: string;
}

export interface ExecuteKeyboardActionResponse {
  action: AutomationAction;
}

export interface StartAutomationSessionRequest {
  vmId: string;
  metadata?: Record<string, any>;
}

export interface StartAutomationSessionResponse {
  session: AutomationSession;
}

export interface StopAutomationSessionRequest {
  vmId: string;
  sessionId: string;
}

export interface StopAutomationSessionResponse {
  session: AutomationSession;
}

export interface GetSessionHistoryRequest {
  vmId: string;
  sessionId?: string;
  limit?: number;
  offset?: number;
}

export interface GetSessionHistoryResponse {
  actions: AutomationAction[];
  total: number;
}

export interface ExportSessionRequest {
  sessionId: string;
  format?: 'json' | 'csv';
}

export interface ExportSessionResponse {
  data: any;
  format: string;
  filename: string;
}

export interface ImportSessionRequest {
  vmId: string;
  sessionData: any;
  format?: 'json' | 'csv';
}

export interface ImportSessionResponse {
  session: AutomationSession;
}

// Event Types

export interface VmCreatedEvent {
  type: 'vmCreated';
  vm: DesktopVm;
}

export interface VmStartedEvent {
  type: 'vmStarted';
  vm: DesktopVm;
}

export interface VmStoppedEvent {
  type: 'vmStopped';
  vm: DesktopVm;
}

export interface VmDeletedEvent {
  type: 'vmDeleted';
  vm: DesktopVm;
}

export interface VmStatusChangedEvent {
  type: 'vmStatusChanged';
  vm: DesktopVm;
}

export interface VmConfigUpdatedEvent {
  type: 'vmConfigUpdated';
  vm: DesktopVm;
}

export interface VmResourcesUpdatedEvent {
  type: 'vmResourcesUpdated';
  vm: DesktopVm;
}

export interface VmErrorEvent {
  type: 'vmError';
  vm: DesktopVm;
  error: Error;
}

export interface AutomationSessionStartedEvent {
  type: 'automationSessionStarted';
  session: AutomationSession;
}

export interface AutomationSessionEndedEvent {
  type: 'automationSessionEnded';
  session: AutomationSession;
}

export interface AutomationActionExecutedEvent {
  type: 'automationActionExecuted';
  action: AutomationAction;
}

export interface ScreenshotCapturedEvent {
  type: 'screenshotCaptured';
  screenshot: ScreenshotData;
}

export type DesktopVmEvent = 
  | VmCreatedEvent
  | VmStartedEvent
  | VmStoppedEvent
  | VmDeletedEvent
  | VmStatusChangedEvent
  | VmConfigUpdatedEvent
  | VmResourcesUpdatedEvent
  | VmErrorEvent
  | AutomationSessionStartedEvent
  | AutomationSessionEndedEvent
  | AutomationActionExecutedEvent
  | ScreenshotCapturedEvent;
