/**
 * Desktop VM Manager Hook
 * 
 * React hook for managing desktop virtual machines with lifecycle operations,
 * monitoring, and configuration management.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  DesktopVm, 
  DesktopVmConfig, 
  DesktopVmStatus, 
  UseDesktopVmManagerResult,
  CreateDesktopVmRequest,
  CreateDesktopVmResponse,
  StartVmRequest,
  StartVmResponse,
  StopVmRequest,
  StopVmResponse,
  UpdateVmConfigRequest,
  UpdateVmConfigResponse,
  GetConnectionTokenRequest,
  GetConnectionTokenResponse
} from '../types';

export function useDesktopVmManager(initialVmId?: string): UseDesktopVmManagerResult {
  // State
  const [vmId, setVmId] = useState<string | undefined>(initialVmId);
  const [vmStatus, setVmStatus] = useState<DesktopVmStatus>('stopped');
  const [vmConfig, setVmConfig] = useState<DesktopVmConfig | undefined>();
  const [vms, setVms] = useState<DesktopVm[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();

  // Refs
  const pollingInterval = useRef<NodeJS.Timeout>();
  const abortController = useRef<AbortController>();

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current);
      }
      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, []);

  // Start polling for VM status updates
  const startPolling = useCallback(() => {
    if (pollingInterval.current) {
      clearInterval(pollingInterval.current);
    }

    pollingInterval.current = setInterval(() => {
      refreshVms();
    }, 5000); // Poll every 5 seconds
  }, []);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingInterval.current) {
      clearInterval(pollingInterval.current);
      pollingInterval.current = undefined;
    }
  }, []);

  // Create a new desktop VM
  const createDesktopVm = useCallback(async (config: DesktopVmConfig): Promise<DesktopVm> => {
    setIsLoading(true);
    setError(undefined);

    try {
      const request: CreateDesktopVmRequest = { config };
      
      const response = await fetch('/api/desktop-vm/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create desktop VM');
      }

      const data: CreateDesktopVmResponse = await response.json();
      
      // Update state
      setVmId(data.vm.id);
      setVmStatus(data.vm.status);
      setVmConfig(data.vm.config);
      setVms(prev => [...prev, data.vm]);

      // Start polling for updates
      startPolling();

      return data.vm;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setError(error.message);
        throw error;
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [startPolling]);

  // Start a VM
  const startVm = useCallback(async (targetVmId?: string): Promise<void> => {
    const id = targetVmId || vmId;
    if (!id) {
      throw new Error('No VM ID provided');
    }

    setIsLoading(true);
    setError(undefined);

    try {
      const request: StartVmRequest = { vmId: id };
      
      const response = await fetch('/api/desktop-vm/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start desktop VM');
      }

      const data: StartVmResponse = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to start desktop VM');
      }

      // Update status immediately
      if (id === vmId) {
        setVmStatus('starting');
      }

      // Start polling for updates
      startPolling();
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setError(error.message);
        throw error;
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [vmId, startPolling]);

  // Stop a VM
  const stopVm = useCallback(async (targetVmId?: string): Promise<void> => {
    const id = targetVmId || vmId;
    if (!id) {
      throw new Error('No VM ID provided');
    }

    setIsLoading(true);
    setError(undefined);

    try {
      const request: StopVmRequest = { vmId: id };
      
      const response = await fetch('/api/desktop-vm/stop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to stop desktop VM');
      }

      const data: StopVmResponse = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to stop desktop VM');
      }

      // Update status immediately
      if (id === vmId) {
        setVmStatus('stopping');
      }

      // Stop polling when VM is stopped
      stopPolling();
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setError(error.message);
        throw error;
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [vmId, stopPolling]);

  // Restart a VM
  const restartVm = useCallback(async (targetVmId?: string): Promise<void> => {
    await stopVm(targetVmId);
    await startVm(targetVmId);
  }, [stopVm, startVm]);

  // Delete a VM
  const deleteVm = useCallback(async (targetVmId?: string): Promise<void> => {
    const id = targetVmId || vmId;
    if (!id) {
      throw new Error('No VM ID provided');
    }

    setIsLoading(true);
    setError(undefined);

    try {
      const response = await fetch(`/api/desktop-vm/${id}`, {
        method: 'DELETE',
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete desktop VM');
      }

      // Update state
      setVms(prev => prev.filter(vm => vm.id !== id));
      
      if (id === vmId) {
        setVmId(undefined);
        setVmStatus('stopped');
        setVmConfig(undefined);
        stopPolling();
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setError(error.message);
        throw error;
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [vmId, stopPolling]);

  // Update VM configuration
  const updateVmConfig = useCallback(async (
    updates: Partial<DesktopVmConfig>, 
    targetVmId?: string
  ): Promise<void> => {
    const id = targetVmId || vmId;
    if (!id) {
      throw new Error('No VM ID provided');
    }

    setIsLoading(true);
    setError(undefined);

    try {
      const request: UpdateVmConfigRequest = { vmId: id, updates };
      
      const response = await fetch('/api/desktop-vm/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update VM configuration');
      }

      const data: UpdateVmConfigResponse = await response.json();
      
      if (!data.success) {
        throw new Error('Failed to update VM configuration');
      }

      // Update state
      if (id === vmId) {
        setVmConfig(data.vm.config);
      }
      
      setVms(prev => prev.map(vm => vm.id === id ? data.vm : vm));
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setError(error.message);
        throw error;
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [vmId]);

  // Get connection token for Guacamole
  const getConnectionToken = useCallback(async (targetVmId?: string): Promise<string> => {
    const id = targetVmId || vmId;
    if (!id) {
      throw new Error('No VM ID provided');
    }

    try {
      const request: GetConnectionTokenRequest = { vmId: id };
      
      const response = await fetch('/api/desktop-vm/connection-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get connection token');
      }

      const data: GetConnectionTokenResponse = await response.json();
      return data.token;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId]);

  // Refresh VMs list
  const refreshVms = useCallback(async (): Promise<void> => {
    try {
      const response = await fetch('/api/desktop-vm', {
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch desktop VMs');
      }

      const data: { vms: DesktopVm[] } = await response.json();
      setVms(data.vms);

      // Update current VM status if we have one
      if (vmId) {
        const currentVm = data.vms.find(vm => vm.id === vmId);
        if (currentVm) {
          setVmStatus(currentVm.status);
          setVmConfig(currentVm.config);
        }
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Failed to refresh VMs:', error);
        // Don't set error state for refresh failures to avoid disrupting UI
      }
    }
  }, [vmId]);

  // Initialize abort controller
  useEffect(() => {
    abortController.current = new AbortController();
    
    return () => {
      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, []);

  // Load initial data
  useEffect(() => {
    refreshVms();
  }, [refreshVms]);

  // Start polling if we have a running VM
  useEffect(() => {
    if (vmStatus === 'running' || vmStatus === 'starting') {
      startPolling();
    } else {
      stopPolling();
    }
  }, [vmStatus, startPolling, stopPolling]);

  return {
    vmId,
    vmStatus,
    vmConfig,
    vms,
    isLoading,
    error,
    createDesktopVm,
    startVm,
    stopVm,
    restartVm,
    deleteVm,
    updateVmConfig,
    getConnectionToken,
    refreshVms,
  };
}
