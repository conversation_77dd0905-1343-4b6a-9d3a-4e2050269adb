/**
 * Storage Service
 *
 * Provides functionality for managing LXC storage pools and volumes.
 */

import { LxcApi } from '../api/lxc-api';
import { logger } from '@/lib/logger';
import { ContainerizationError, NotFoundError, AlreadyExistsError } from '../../shared/error-handling';
import { retry } from '../../shared/retry';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Storage pool configuration
 */
export interface StoragePoolConfig {
  name: string;
  driver: 'dir' | 'lvm' | 'btrfs' | 'zfs';
  source?: string;
  size?: string;
  options?: Record<string, string>;
}

/**
 * Storage pool information
 */
export interface StoragePoolInfo {
  name: string;
  driver: string;
  source?: string;
  used?: string;
  total?: string;
  state?: 'active' | 'inactive';
}

/**
 * Storage volume configuration
 */
export interface StorageVolumeConfig {
  size?: string;
  filesystem?: string;
  options?: Record<string, string>;
}

/**
 * Storage volume information
 */
export interface StorageVolumeInfo {
  name: string;
  pool: string;
  type: string;
  size?: string;
  used?: string;
  path?: string;
}

/**
 * Storage service class
 */
export class StorageService {
  private readonly api: LxcApi;

  constructor(api: LxcApi) {
    this.api = api;
  }

  /**
   * Create a storage pool
   */
  async createStoragePool(config: StoragePoolConfig): Promise<void> {
    try {
      logger.info(`Creating storage pool ${config.name}`);

      // Check if pool already exists
      const pools = await this.listStoragePools();
      if (pools.includes(config.name)) {
        throw new AlreadyExistsError(`Storage pool ${config.name} already exists`);
      }

      // Create storage pool using lxc storage
      const cmd = [
        'lxc', 'storage', 'create',
        config.name,
        config.driver
      ];

      if (config.source) {
        cmd.push('source=' + config.source);
      }

      if (config.size) {
        cmd.push('size=' + config.size);
      }

      if (config.options) {
        for (const [key, value] of Object.entries(config.options)) {
          cmd.push(`${key}=${value}`);
        }
      }

      // Execute command with retry
      await retry(
        async () => {
          await execAsync(cmd.join(' '));
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Create storage pool ${config.name}`
        }
      );

      logger.info(`Storage pool ${config.name} created successfully`);
    } catch (error) {
      logger.error(`Failed to create storage pool ${config.name}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to create storage pool ${config.name}`, {
        cause: error,
        details: { config }
      });
    }
  }

  /**
   * Delete a storage pool
   */
  async deleteStoragePool(name: string): Promise<void> {
    try {
      logger.info(`Deleting storage pool ${name}`);

      // Check if pool exists
      const pools = await this.listStoragePools();
      if (!pools.includes(name)) {
        throw new NotFoundError(`Storage pool ${name} not found`);
      }

      // Delete storage pool using lxc storage
      await retry(
        async () => {
          await execAsync(`lxc storage delete ${name}`);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Delete storage pool ${name}`
        }
      );

      logger.info(`Storage pool ${name} deleted successfully`);
    } catch (error) {
      logger.error(`Failed to delete storage pool ${name}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to delete storage pool ${name}`, {
        cause: error,
        details: { name }
      });
    }
  }

  /**
   * List storage pools
   */
  async listStoragePools(): Promise<string[]> {
    try {
      logger.info('Listing storage pools');

      // List storage pools using lxc storage
      const { stdout } = await retry(
        async () => {
          return await execAsync('lxc storage list --format csv');
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: 'List storage pools'
        }
      );

      // Parse output
      const pools = stdout.trim().split('\n')
        .filter(line => line.trim().length > 0)
        .map(line => line.split(',')[0]);

      logger.info(`Found ${pools.length} storage pools`);
      return pools;
    } catch (error) {
      logger.error('Failed to list storage pools', error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError('Failed to list storage pools', {
        cause: error
      });
    }
  }

  /**
   * Get storage pool information
   */
  async getStoragePool(name: string): Promise<StoragePoolInfo> {
    try {
      logger.info(`Getting storage pool ${name}`);

      // Check if pool exists
      const pools = await this.listStoragePools();
      if (!pools.includes(name)) {
        throw new NotFoundError(`Storage pool ${name} not found`);
      }

      // Get storage pool info using lxc storage
      const { stdout } = await retry(
        async () => {
          return await execAsync(`lxc storage show ${name} --format json`);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Get storage pool ${name}`
        }
      );

      // Parse output
      const data = JSON.parse(stdout);

      const info: StoragePoolInfo = {
        name: data.name,
        driver: data.driver,
        source: data.config?.source,
        used: data.used,
        total: data.total,
        state: data.status === 'Created' ? 'active' : 'inactive'
      };

      logger.info(`Successfully retrieved storage pool ${name}`);
      return info;
    } catch (error) {
      logger.error(`Failed to get storage pool ${name}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to get storage pool ${name}`, {
        cause: error,
        details: { name }
      });
    }
  }

  /**
   * Create a storage volume
   */
  async createStorageVolume(poolName: string, volumeName: string, config: StorageVolumeConfig = {}): Promise<void> {
    try {
      logger.info(`Creating storage volume ${volumeName} in pool ${poolName}`);

      // Check if pool exists
      const pools = await this.listStoragePools();
      if (!pools.includes(poolName)) {
        throw new NotFoundError(`Storage pool ${poolName} not found`);
      }

      // Check if volume already exists
      const volumes = await this.listStorageVolumes(poolName);
      if (volumes.includes(volumeName)) {
        throw new AlreadyExistsError(`Storage volume ${volumeName} already exists in pool ${poolName}`);
      }

      // Create storage volume using lxc storage
      const cmd = [
        'lxc', 'storage', 'volume', 'create',
        poolName,
        volumeName
      ];

      if (config.size) {
        cmd.push('size=' + config.size);
      }

      if (config.filesystem) {
        cmd.push('filesystem=' + config.filesystem);
      }

      if (config.options) {
        for (const [key, value] of Object.entries(config.options)) {
          cmd.push(`${key}=${value}`);
        }
      }

      // Execute command with retry
      await retry(
        async () => {
          await execAsync(cmd.join(' '));
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Create storage volume ${volumeName} in pool ${poolName}`
        }
      );

      logger.info(`Storage volume ${volumeName} created successfully in pool ${poolName}`);
    } catch (error) {
      logger.error(`Failed to create storage volume ${volumeName} in pool ${poolName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to create storage volume ${volumeName} in pool ${poolName}`, {
        cause: error,
        details: { poolName, volumeName, config }
      });
    }
  }

  /**
   * Delete a storage volume
   */
  async deleteStorageVolume(poolName: string, volumeName: string): Promise<void> {
    try {
      logger.info(`Deleting storage volume ${volumeName} from pool ${poolName}`);

      // Check if pool exists
      const pools = await this.listStoragePools();
      if (!pools.includes(poolName)) {
        throw new NotFoundError(`Storage pool ${poolName} not found`);
      }

      // Check if volume exists
      const volumes = await this.listStorageVolumes(poolName);
      if (!volumes.includes(volumeName)) {
        throw new NotFoundError(`Storage volume ${volumeName} not found in pool ${poolName}`);
      }

      // Delete storage volume using lxc storage
      await retry(
        async () => {
          await execAsync(`lxc storage volume delete ${poolName} ${volumeName}`);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Delete storage volume ${volumeName} from pool ${poolName}`
        }
      );

      logger.info(`Storage volume ${volumeName} deleted successfully from pool ${poolName}`);
    } catch (error) {
      logger.error(`Failed to delete storage volume ${volumeName} from pool ${poolName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to delete storage volume ${volumeName} from pool ${poolName}`, {
        cause: error,
        details: { poolName, volumeName }
      });
    }
  }

  /**
   * List storage volumes in a pool
   */
  async listStorageVolumes(poolName: string): Promise<string[]> {
    try {
      logger.info(`Listing storage volumes in pool ${poolName}`);

      // Check if pool exists
      const pools = await this.listStoragePools();
      if (!pools.includes(poolName)) {
        throw new NotFoundError(`Storage pool ${poolName} not found`);
      }

      // List storage volumes using lxc storage
      const { stdout } = await retry(
        async () => {
          return await execAsync(`lxc storage volume list ${poolName} --format csv`);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `List storage volumes in pool ${poolName}`
        }
      );

      // Parse output
      const volumes = stdout.trim().split('\n')
        .filter(line => line.trim().length > 0)
        .map(line => line.split(',')[0]);

      logger.info(`Found ${volumes.length} storage volumes in pool ${poolName}`);
      return volumes;
    } catch (error) {
      logger.error(`Failed to list storage volumes in pool ${poolName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to list storage volumes in pool ${poolName}`, {
        cause: error,
        details: { poolName }
      });
    }
  }

  /**
   * Get storage volume information
   */
  async getStorageVolume(poolName: string, volumeName: string): Promise<StorageVolumeInfo> {
    try {
      logger.info(`Getting storage volume ${volumeName} from pool ${poolName}`);

      // Check if pool exists
      const pools = await this.listStoragePools();
      if (!pools.includes(poolName)) {
        throw new NotFoundError(`Storage pool ${poolName} not found`);
      }

      // Check if volume exists
      const volumes = await this.listStorageVolumes(poolName);
      if (!volumes.includes(volumeName)) {
        throw new NotFoundError(`Storage volume ${volumeName} not found in pool ${poolName}`);
      }

      // Get storage volume info using lxc storage
      const { stdout } = await retry(
        async () => {
          return await execAsync(`lxc storage volume show ${poolName} ${volumeName} --format json`);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Get storage volume ${volumeName} from pool ${poolName}`
        }
      );

      // Parse output
      const data = JSON.parse(stdout);

      const info: StorageVolumeInfo = {
        name: data.name,
        pool: poolName,
        type: data.type || 'custom',
        size: data.config?.size,
        used: data.used,
        path: data.location
      };

      logger.info(`Successfully retrieved storage volume ${volumeName} from pool ${poolName}`);
      return info;
    } catch (error) {
      logger.error(`Failed to get storage volume ${volumeName} from pool ${poolName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to get storage volume ${volumeName} from pool ${poolName}`, {
        cause: error,
        details: { poolName, volumeName }
      });
    }
  }

  /**
   * Attach a storage volume to a container
   */
  async attachStorageVolume(containerName: string, poolName: string, volumeName: string, mountPath: string, options: {
    readonly?: boolean;
    shift?: boolean;
    propagation?: 'private' | 'shared' | 'slave' | 'unbindable' | 'bidirectional';
  } = {}): Promise<void> {
    try {
      logger.info(`Attaching storage volume ${volumeName} from pool ${poolName} to container ${containerName} at ${mountPath}`);

      // Check if container exists
      try {
        await this.api.getContainerInfo(containerName);
      } catch (error) {
        throw new NotFoundError(`Container ${containerName} not found`);
      }

      // Check if pool exists
      const pools = await this.listStoragePools();
      if (!pools.includes(poolName)) {
        throw new NotFoundError(`Storage pool ${poolName} not found`);
      }

      // Check if volume exists
      const volumes = await this.listStorageVolumes(poolName);
      if (!volumes.includes(volumeName)) {
        throw new NotFoundError(`Storage volume ${volumeName} not found in pool ${poolName}`);
      }

      // Attach storage volume using lxc storage
      const cmd = [
        'lxc', 'storage', 'volume', 'attach',
        poolName,
        volumeName,
        containerName,
        mountPath
      ];

      if (options.readonly) {
        cmd.push('readonly=true');
      }

      if (options.shift) {
        cmd.push('shift=true');
      }

      if (options.propagation) {
        cmd.push(`propagation=${options.propagation}`);
      }

      // Execute command with retry
      await retry(
        async () => {
          await execAsync(cmd.join(' '));
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Attach storage volume ${volumeName} to container ${containerName}`
        }
      );

      logger.info(`Storage volume ${volumeName} attached successfully to container ${containerName} at ${mountPath}`);
    } catch (error) {
      logger.error(`Failed to attach storage volume ${volumeName} to container ${containerName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to attach storage volume ${volumeName} to container ${containerName}`, {
        cause: error,
        details: { containerName, poolName, volumeName, mountPath, options }
      });
    }
  }

  /**
   * Detach a storage volume from a container
   */
  async detachStorageVolume(containerName: string, mountPath: string): Promise<void> {
    try {
      logger.info(`Detaching storage volume at ${mountPath} from container ${containerName}`);

      // Check if container exists
      try {
        await this.api.getContainerInfo(containerName);
      } catch (error) {
        throw new NotFoundError(`Container ${containerName} not found`);
      }

      // Detach storage volume using lxc config
      await retry(
        async () => {
          await execAsync(`lxc config device remove ${containerName} ${mountPath}`);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Detach storage volume from container ${containerName}`
        }
      );

      logger.info(`Storage volume detached successfully from container ${containerName} at ${mountPath}`);
    } catch (error) {
      logger.error(`Failed to detach storage volume from container ${containerName} at ${mountPath}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to detach storage volume from container ${containerName} at ${mountPath}`, {
        cause: error,
        details: { containerName, mountPath }
      });
    }
  }

  /**
   * List attached storage volumes for a container
   */
  async listAttachedStorageVolumes(containerName: string): Promise<{ device: string; mountPath: string; pool?: string; volume?: string }[]> {
    try {
      logger.info(`Listing attached storage volumes for container ${containerName}`);

      // Check if container exists
      try {
        await this.api.getContainerInfo(containerName);
      } catch (error) {
        throw new NotFoundError(`Container ${containerName} not found`);
      }

      // List devices using lxc config
      const { stdout } = await retry(
        async () => {
          return await execAsync(`lxc config device list ${containerName} --format json`);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `List attached storage volumes for container ${containerName}`
        }
      );

      // Parse output
      const devices = JSON.parse(stdout) as Record<string, any>;
      const volumes = [];

      for (const [device, config] of Object.entries(devices)) {
        if (config && typeof config === 'object' && config.type === 'disk') {
          volumes.push({
            device,
            mountPath: config.path,
            pool: config.pool,
            volume: config.source
          });
        }
      }

      logger.info(`Found ${volumes.length} attached storage volumes for container ${containerName}`);
      return volumes;
    } catch (error) {
      logger.error(`Failed to list attached storage volumes for container ${containerName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to list attached storage volumes for container ${containerName}`, {
        cause: error,
        details: { containerName }
      });
    }
  }
}
