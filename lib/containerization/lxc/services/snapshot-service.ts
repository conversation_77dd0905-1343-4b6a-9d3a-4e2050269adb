/**
 * Snapshot Service
 *
 * Provides functionality for managing LXC container snapshots.
 */

import { LxcApi } from '../api/lxc-api';
import { logger } from '@/lib/logger';
import { ContainerizationError, NotFoundError, AlreadyExistsError } from '../../shared/error-handling';
import { retry } from '../../shared/retry';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Snapshot service information
 */
export interface SnapshotServiceInfo {
  name: string;
  createdAt: Date;
  stateful: boolean;
  size?: string;
}

/**
 * Snapshot service class
 */
export class SnapshotService {
  private readonly api: LxcApi;

  constructor(api: LxcApi) {
    this.api = api;
  }

  /**
   * Create a snapshot
   */
  async createSnapshot(containerName: string, snapshotName: string, options: {
    stateful?: boolean;
  } = {}): Promise<void> {
    try {
      logger.info(`Creating snapshot ${snapshotName} for container ${containerName}`);

      // Check if container exists
      try {
        await this.api.getContainerInfo(containerName);
      } catch (error) {
        throw new NotFoundError(`Container ${containerName} not found`);
      }

      // Check if snapshot already exists
      const snapshots = await this.listSnapshots(containerName);
      if (snapshots.some(s => s.name === snapshotName)) {
        throw new AlreadyExistsError(`Snapshot ${snapshotName} already exists for container ${containerName}`);
      }

      // Create snapshot using lxc snapshot
      const cmd = [
        'lxc', 'snapshot',
        containerName,
        snapshotName
      ];

      if (options.stateful) {
        cmd.push('--stateful');
      }

      // Execute command with retry
      await retry(
        async () => {
          await execAsync(cmd.join(' '));
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Create snapshot ${snapshotName} for container ${containerName}`
        }
      );

      logger.info(`Snapshot ${snapshotName} created successfully for container ${containerName}`);
    } catch (error) {
      logger.error(`Failed to create snapshot ${snapshotName} for container ${containerName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to create snapshot ${snapshotName} for container ${containerName}`, {
        cause: error,
        details: { containerName, snapshotName, options }
      });
    }
  }

  /**
   * Delete a snapshot
   */
  async deleteSnapshot(containerName: string, snapshotName: string): Promise<void> {
    try {
      logger.info(`Deleting snapshot ${snapshotName} for container ${containerName}`);

      // Check if container exists
      try {
        await this.api.getContainerInfo(containerName);
      } catch (error) {
        throw new NotFoundError(`Container ${containerName} not found`);
      }

      // Check if snapshot exists
      const snapshots = await this.listSnapshots(containerName);
      if (!snapshots.some(s => s.name === snapshotName)) {
        throw new NotFoundError(`Snapshot ${snapshotName} not found for container ${containerName}`);
      }

      // Delete snapshot using lxc delete
      await retry(
        async () => {
          await execAsync(`lxc delete ${containerName}/${snapshotName}`);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Delete snapshot ${snapshotName} for container ${containerName}`
        }
      );

      logger.info(`Snapshot ${snapshotName} deleted successfully for container ${containerName}`);
    } catch (error) {
      logger.error(`Failed to delete snapshot ${snapshotName} for container ${containerName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to delete snapshot ${snapshotName} for container ${containerName}`, {
        cause: error,
        details: { containerName, snapshotName }
      });
    }
  }

  /**
   * List snapshots for a container
   */
  async listSnapshots(containerName: string): Promise<SnapshotServiceInfo[]> {
    try {
      logger.info(`Listing snapshots for container ${containerName}`);

      // Check if container exists
      try {
        await this.api.getContainerInfo(containerName);
      } catch (error) {
        throw new NotFoundError(`Container ${containerName} not found`);
      }

      // List snapshots using lxc info
      const { stdout } = await retry(
        async () => {
          return await execAsync(`lxc info ${containerName} --show-snapshots --format json`);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `List snapshots for container ${containerName}`
        }
      );

      // Parse output
      const data = JSON.parse(stdout);
      const snapshots: SnapshotServiceInfo[] = [];

      if (data.snapshots && Array.isArray(data.snapshots)) {
        for (const snapshot of data.snapshots) {
          snapshots.push({
            name: snapshot.name,
            createdAt: new Date(snapshot.created_at),
            stateful: snapshot.stateful || false,
            size: snapshot.size
          });
        }
      }

      logger.info(`Found ${snapshots.length} snapshots for container ${containerName}`);
      return snapshots;
    } catch (error) {
      logger.error(`Failed to list snapshots for container ${containerName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to list snapshots for container ${containerName}`, {
        cause: error,
        details: { containerName }
      });
    }
  }

  /**
   * Restore a snapshot
   */
  async restoreSnapshot(containerName: string, snapshotName: string, options: {
    stateful?: boolean;
  } = {}): Promise<void> {
    try {
      logger.info(`Restoring snapshot ${snapshotName} for container ${containerName}`);

      // Check if container exists
      try {
        await this.api.getContainerInfo(containerName);
      } catch (error) {
        throw new NotFoundError(`Container ${containerName} not found`);
      }

      // Check if snapshot exists
      const snapshots = await this.listSnapshots(containerName);
      if (!snapshots.some(s => s.name === snapshotName)) {
        throw new NotFoundError(`Snapshot ${snapshotName} not found for container ${containerName}`);
      }

      // Restore snapshot using lxc restore
      const cmd = [
        'lxc', 'restore',
        containerName,
        snapshotName
      ];

      if (options.stateful) {
        cmd.push('--stateful');
      }

      // Execute command with retry
      await retry(
        async () => {
          await execAsync(cmd.join(' '));
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Restore snapshot ${snapshotName} for container ${containerName}`
        }
      );

      logger.info(`Snapshot ${snapshotName} restored successfully for container ${containerName}`);
    } catch (error) {
      logger.error(`Failed to restore snapshot ${snapshotName} for container ${containerName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to restore snapshot ${snapshotName} for container ${containerName}`, {
        cause: error,
        details: { containerName, snapshotName, options }
      });
    }
  }

  /**
   * Publish a snapshot as a template
   */
  async publishSnapshot(containerName: string, snapshotName: string, options: {
    alias?: string;
    public?: boolean;
    description?: string;
  } = {}): Promise<string> {
    try {
      logger.info(`Publishing snapshot ${snapshotName} for container ${containerName}`);

      // Check if container exists
      try {
        await this.api.getContainerInfo(containerName);
      } catch (error) {
        throw new NotFoundError(`Container ${containerName} not found`);
      }

      // Check if snapshot exists
      const snapshots = await this.listSnapshots(containerName);
      if (!snapshots.some(s => s.name === snapshotName)) {
        throw new NotFoundError(`Snapshot ${snapshotName} not found for container ${containerName}`);
      }

      // Publish snapshot using lxc publish
      const cmd = [
        'lxc', 'publish',
        `${containerName}/${snapshotName}`,
        '--alias', options.alias || `${containerName}-${snapshotName}`
      ];

      if (options.public) {
        cmd.push('--public');
      }

      if (options.description) {
        cmd.push('--description', `"${options.description}"`);
      }

      // Execute command with retry
      const { stdout } = await retry(
        async () => {
          return await execAsync(cmd.join(' '));
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Publish snapshot ${snapshotName} for container ${containerName}`
        }
      );

      // Extract fingerprint from output
      const match = stdout.match(/Fingerprint: ([a-f0-9]+)/i);
      const fingerprint = match ? match[1] : '';

      logger.info(`Snapshot ${snapshotName} published successfully for container ${containerName} with fingerprint ${fingerprint}`);
      return fingerprint;
    } catch (error) {
      logger.error(`Failed to publish snapshot ${snapshotName} for container ${containerName}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to publish snapshot ${snapshotName} for container ${containerName}`, {
        cause: error,
        details: { containerName, snapshotName, options }
      });
    }
  }
}
