/**
 * Template Service
 *
 * Provides functionality for managing LXC templates.
 */

import { LxcApi } from '../api/lxc-api';
import { Template, TemplateConfig, TemplateInfo, TemplateType } from '../models/template';
import { logger } from '@/lib/logger';
import { ContainerizationError, NotFoundError, AlreadyExistsError } from '../../shared/error-handling';
import { retry } from '../../shared/retry';
import { generateId } from '../utils/id-generator';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { exec } from 'child_process';
import { getLxcPath, ensureDirectoryExists } from '../utils/paths';
import * as os from 'os';

const execAsync = promisify(exec);
const fsPromises = fs.promises;

/**
 * Template service options
 */
export interface TemplateServiceOptions {
  templatesPath?: string;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

/**
 * Template download options
 */
export interface TemplateDownloadOptions {
  distribution: string;
  release: string;
  arch: string;
  variant?: string;
  server?: string;
  keyserver?: string;
}

/**
 * Template service class
 */
export class TemplateService {
  private readonly api: LxcApi;
  private templatesPath: string; // Not readonly to allow dynamic updates
  private readonly options: TemplateServiceOptions;

  constructor(api: LxcApi, options: TemplateServiceOptions = {}) {
    this.api = api;
    this.options = options;

    // Use the provided path or determine a suitable path
    if (options.templatesPath) {
      this.templatesPath = options.templatesPath;
    } else {
      // Initialize with a temporary path that will be updated asynchronously
      this.templatesPath = path.join(os.tmpdir(), 'lxc-templates');

      // Asynchronously determine a better path
      this.initializeTemplatesPath();
    }

    // Create templates directory if it doesn't exist (synchronously for constructor)
    try {
      if (!fs.existsSync(this.templatesPath)) {
        fs.mkdirSync(this.templatesPath, { recursive: true });
      }
    } catch (error) {
      logger.warn(`Could not create templates directory at ${this.templatesPath}. Will try alternative locations.`, error);
      // We'll handle this in initializeTemplatesPath
    }
  }

  /**
   * Initialize the templates path asynchronously
   */
  private async initializeTemplatesPath(): Promise<void> {
    try {
      // Get the LXC path
      const lxcPath = await getLxcPath();

      // Create a templates directory within the LXC path
      const templatesPath = path.join(lxcPath, 'templates');

      // Ensure the directory exists
      await ensureDirectoryExists(templatesPath);

      // Update the templates path
      this.templatesPath = templatesPath;

      logger.info(`Templates path initialized to: ${this.templatesPath}`);
    } catch (error) {
      // If we can't use the LXC path, try the user's home directory
      try {
        const homePath = os.homedir();
        const templatesPath = path.join(homePath, '.lxc', 'templates');

        // Ensure the directory exists
        await ensureDirectoryExists(templatesPath);

        // Update the templates path
        this.templatesPath = templatesPath;

        logger.info(`Templates path initialized to: ${this.templatesPath}`);
      } catch (homeError) {
        // If we can't use the home directory, use a temporary directory
        const tempPath = path.join(os.tmpdir(), `lxc-templates-${generateId()}`);

        // Ensure the directory exists
        await ensureDirectoryExists(tempPath);

        // Update the templates path
        this.templatesPath = tempPath;

        logger.warn(`Using temporary directory for templates: ${this.templatesPath}`);
      }
    }
  }

  /**
   * List available templates
   */
  async listTemplates(): Promise<TemplateInfo[]> {
    try {
      logger.info('Listing available templates');

      // Get templates from LXC
      const templateNames = await retry(
        async () => {
          return await this.api.listTemplates();
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: 'List templates'
        }
      );

      // Get template info for each template
      const templates: TemplateInfo[] = [];

      for (const name of templateNames) {
        try {
          const info = await this.api.getTemplateInfo(name);
          templates.push(info);
        } catch (error) {
          logger.warn(`Failed to get info for template ${name}`, error);
        }
      }

      logger.info(`Found ${templates.length} templates`);
      return templates;
    } catch (error) {
      logger.error('Failed to list templates', error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError('Failed to list templates', {
        cause: error
      });
    }
  }

  /**
   * Get template info
   */
  async getTemplateInfo(name: string): Promise<TemplateInfo> {
    try {
      logger.info(`Getting info for template ${name}`);

      // Get template info from LXC
      const info = await retry(
        async () => {
          return await this.api.getTemplateInfo(name);
        },
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          context: `Get template info for ${name}`
        }
      );

      logger.info(`Successfully retrieved info for template ${name}`);
      return info;
    } catch (error) {
      logger.error(`Failed to get info for template ${name}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to get info for template ${name}`, {
        cause: error,
        details: { name }
      });
    }
  }

  /**
   * Download a template
   */
  async downloadTemplate(options: TemplateDownloadOptions): Promise<TemplateInfo> {
    try {
      logger.info(`Downloading template: ${options.distribution} ${options.release} ${options.arch} ${options.variant || ''}`);

      // Create a unique name for the template
      const name = `${options.distribution}-${options.release}-${options.arch}${options.variant ? `-${options.variant}` : ''}`;

      // Check if template already exists
      const templates = await this.listTemplates();
      if (templates.some(t => t.name === name)) {
        throw new AlreadyExistsError(`Template ${name} already exists`);
      }

      // Download template using lxc-create with download template
      const cmd = [
        'lxc-create',
        '-t', 'download',
        '-n', `temp-${generateId()}`,
        '--',
        '-d', options.distribution,
        '-r', options.release,
        '-a', options.arch
      ];

      if (options.variant) {
        cmd.push('-v', options.variant);
      }

      if (options.server) {
        cmd.push('--server', options.server);
      }

      if (options.keyserver) {
        cmd.push('--keyserver', options.keyserver);
      }

      // Execute command
      await execAsync(cmd.join(' '));

      // Get template info
      const info = await this.getTemplateInfo(name);

      logger.info(`Template ${name} downloaded successfully`);
      return info;
    } catch (error) {
      logger.error('Failed to download template', error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError('Failed to download template', {
        cause: error,
        details: { options }
      });
    }
  }

  /**
   * Delete a template
   */
  async deleteTemplate(name: string): Promise<void> {
    try {
      logger.info(`Deleting template ${name}`);

      // Check if template exists
      try {
        await this.getTemplateInfo(name);
      } catch (error) {
        throw new NotFoundError(`Template ${name} not found`);
      }

      // Delete template using lxc-image
      await execAsync(`lxc-image delete ${name}`);

      logger.info(`Template ${name} deleted successfully`);
    } catch (error) {
      logger.error(`Failed to delete template ${name}`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to delete template ${name}`, {
        cause: error,
        details: { name }
      });
    }
  }

  /**
   * Get the templates path
   */
  getTemplatesPath(): string {
    return this.templatesPath;
  }

  /**
   * Set the templates path
   */
  async setTemplatesPath(templatesPath: string): Promise<void> {
    try {
      // Ensure the directory exists
      await ensureDirectoryExists(templatesPath);

      // Update the templates path
      this.templatesPath = templatesPath;

      logger.info(`Templates path updated to: ${this.templatesPath}`);
    } catch (error) {
      logger.error(`Failed to set templates path to ${templatesPath}`, error);

      throw new ContainerizationError(`Failed to set templates path to ${templatesPath}`, {
        cause: error,
        details: { templatesPath }
      });
    }
  }
}
