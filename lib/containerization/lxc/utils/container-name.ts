/**
 * Utility functions for handling container names
 */

import { logger } from '@/lib/logger';

/**
 * Extract the actual container name from a potentially CSV-formatted string
 * 
 * @param name The container name or CSV-formatted string
 * @returns The extracted container name
 */
export function extractContainerName(name: string): string {
  if (!name) {
    return '';
  }
  
  // If the name contains commas, it might be in CSV format
  if (name.includes(',')) {
    // The container name is the first part before the comma
    const containerName = name.split(',')[0];
    logger.debug(`Extracted container name from CSV format: ${containerName}`);
    return containerName;
  }
  
  return name;
}

/**
 * URL encode a container name for use in API requests
 * 
 * @param name The container name
 * @returns The URL-encoded container name
 */
export function encodeContainerName(name: string): string {
  return encodeURIComponent(extractContainerName(name));
}
