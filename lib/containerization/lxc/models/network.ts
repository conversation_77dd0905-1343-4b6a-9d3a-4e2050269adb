/**
 * Network Models
 * 
 * Defines models for LXC network interfaces and bridge networks.
 */

import { NetworkType } from './container';

/**
 * Network interface configuration
 */
export interface NetworkInterfaceConfig {
  /** Name of the network interface */
  name: string;
  
  /** Type of network interface */
  type: NetworkType;
  
  /** Bridge to connect to (for bridged interfaces) */
  bridge?: string;
  
  /** Physical interface to connect to (for macvlan interfaces) */
  link?: string;
  
  /** Flags for the interface (e.g., 'up') */
  flags?: string;
  
  /** MAC address for the interface */
  hwaddr?: string;
  
  /** MTU for the interface */
  mtu?: number;
  
  /** IPv4 configuration */
  ipv4?: {
    /** IPv4 address with CIDR notation (e.g., '***********00/24') */
    address?: string;
    
    /** IPv4 gateway */
    gateway?: string;
    
    /** IPv4 netmask */
    netmask?: string;
  };
  
  /** IPv6 configuration */
  ipv6?: {
    /** IPv6 address with CIDR notation (e.g., 'fd00::1/64') */
    address?: string;
    
    /** IPv6 gateway */
    gateway?: string;
    
    /** IPv6 prefix length */
    prefix?: number;
  };
}

/**
 * Network interface information
 */
export interface NetworkInterface extends NetworkInterfaceConfig {
  /** State of the interface (up/down) */
  state?: 'up' | 'down';
  
  /** Statistics for the interface */
  stats?: {
    /** Bytes received */
    bytesReceived?: number;
    
    /** Bytes sent */
    bytesSent?: number;
    
    /** Packets received */
    packetsReceived?: number;
    
    /** Packets sent */
    packetsSent?: number;
    
    /** Errors received */
    errorsReceived?: number;
    
    /** Errors sent */
    errorsSent?: number;
  };
}

/**
 * Bridge network configuration
 */
export interface BridgeNetworkConfig {
  /** Name of the bridge network */
  name: string;
  
  /** IPv4 address with CIDR notation (e.g., '***********/24') */
  ipv4?: string;
  
  /** IPv4 gateway */
  ipv4Gateway?: string;
  
  /** IPv6 address with CIDR notation (e.g., 'fd00::1/64') */
  ipv6?: string;
  
  /** IPv6 gateway */
  ipv6Gateway?: string;
  
  /** MTU for the bridge */
  mtu?: number;
  
  /** Domain name for DHCP */
  domain?: string;
  
  /** DHCP configuration */
  dhcp?: boolean;
  
  /** NAT configuration */
  nat?: boolean;
}

/**
 * Bridge network information
 */
export interface BridgeNetwork extends BridgeNetworkConfig {
  /** State of the bridge (up/down) */
  state?: 'up' | 'down';
  
  /** List of connected interfaces */
  interfaces?: string[];
}
