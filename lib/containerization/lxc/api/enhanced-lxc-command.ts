/**
 * Enhanced LXC Command
 *
 * Provides an enhanced version of the LXC command executor with robust error handling,
 * retry mechanisms, and fallback options.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { logger } from '@/lib/logger';
import { getLxcPath, getLxcConfigPath, isPathWritable } from '../utils/paths';
import { LxcApiOptions } from './lxc-api';
import {
  ContainerizationError,
  ConnectionError,
  PermissionError,
  TimeoutError,
  logError
} from '../../shared/error-handling';
import { retry, RetryOptions } from '../../shared/retry';
import {
  ResourceCleanupRegistry,
  globalCleanupRegistry
} from '../../shared/resource-management';
import {
  withFallback,
  createFallbackChain
} from '../../shared/fallback';
import {
  performHealthCheck,
  createCommandExecutionCheck
} from '../../shared/health-check';

const execAsync = promisify(exec);

/**
 * LXC command options
 */
export interface LxcCommandOptions {
  /** Environment variables */
  env?: Record<string, string>;
  /** Command timeout in milliseconds */
  timeout?: number;
  /** Working directory */
  cwd?: string;
  /** Whether to retry on failure */
  retry?: boolean;
  /** Retry options */
  retryOptions?: Partial<RetryOptions>;
  /** Whether to use fallback paths if primary paths fail */
  useFallbackPaths?: boolean;
  /** Resource cleanup registry */
  cleanupRegistry?: ResourceCleanupRegistry;
}

/**
 * Default LXC command options
 */
export const DEFAULT_LXC_COMMAND_OPTIONS: LxcCommandOptions = {
  timeout: 60000, // 60 seconds
  retry: true,
  retryOptions: {
    maxAttempts: 3,
    initialDelayMs: 1000,
    maxDelayMs: 10000,
    backoffFactor: 2,
    jitter: true,
  },
  useFallbackPaths: true,
  cleanupRegistry: globalCleanupRegistry
};

/**
 * Enhanced LXC command class
 */
export class EnhancedLxcCommand {
  private readonly options: LxcApiOptions;
  private primaryLxcPath: string;
  private primaryConfigPath: string;
  private fallbackLxcPath: string | null = null;
  private fallbackConfigPath: string | null = null;
  private readonly logLevel: string;
  private initialized: boolean = false;
  private initPromise: Promise<void> | null = null;
  private healthCheckPromise: Promise<boolean> | null = null;
  private readonly cleanupRegistry: ResourceCleanupRegistry;

  constructor(options: LxcApiOptions = {}) {
    this.options = options;
    this.primaryLxcPath = options.lxcPath || '';
    this.primaryConfigPath = options.configPath || '';
    this.logLevel = options.logLevel || 'info';
    this.cleanupRegistry = options.cleanupRegistry || globalCleanupRegistry;

    // Initialize the LXC path asynchronously
    this.initialize();
  }

  /**
   * Initialize the LXC command
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._initialize();
    await this.initPromise;
    this.initialized = true;
  }

  /**
   * Internal initialization
   */
  private async _initialize(): Promise<void> {
    try {
      logger.info('Initializing LXC command');

      // Get primary paths
      if (!this.primaryLxcPath) {
        this.primaryLxcPath = await getLxcPath();
      }

      if (!this.primaryConfigPath) {
        this.primaryConfigPath = await getLxcConfigPath();
      }

      // Verify primary paths are writable
      const isPrimaryLxcPathWritable = await isPathWritable(this.primaryLxcPath);
      const isPrimaryConfigPathWritable = await isPathWritable(this.primaryConfigPath);

      // Set up fallback paths if needed
      if (!isPrimaryLxcPathWritable || !isPrimaryConfigPathWritable) {
        logger.warn('Primary LXC paths are not writable, setting up fallback paths');

        // Use home directory for fallback
        const homePath = os.homedir();
        this.fallbackLxcPath = path.join(homePath, '.lxc');
        this.fallbackConfigPath = path.join(homePath, '.lxc', 'config');

        // Create fallback directories
        await fs.mkdir(this.fallbackLxcPath, { recursive: true });
        await fs.mkdir(this.fallbackConfigPath, { recursive: true });

        logger.info(`Fallback paths set up: LXC_PATH=${this.fallbackLxcPath}, LXC_CONF_PATH=${this.fallbackConfigPath}`);
      }

      // Perform health check
      const isHealthy = await this.checkHealth();

      if (!isHealthy) {
        logger.warn('LXC health check failed during initialization');
      }

      logger.info('LXC command initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize LXC command', error);
      throw new ContainerizationError('Failed to initialize LXC command', { cause: error });
    }
  }

  /**
   * Check if LXC is healthy
   */
  async checkHealth(): Promise<boolean> {
    if (this.healthCheckPromise) {
      return this.healthCheckPromise;
    }

    this.healthCheckPromise = (async () => {
      try {
        const healthCheck = createCommandExecutionCheck(
          async () => {
            // Try a simple command to check if LXC is working
            await this.execute(['version'], { retry: false });
            return true;
          },
          'LXC'
        );

        const result = await performHealthCheck(healthCheck);
        return result.healthy;
      } catch (error) {
        logger.error('LXC health check failed', error);
        return false;
      } finally {
        // Clear the promise after a delay to allow for caching
        setTimeout(() => {
          this.healthCheckPromise = null;
        }, 30000); // Cache health check for 30 seconds
      }
    })();

    return this.healthCheckPromise;
  }

  /**
   * Execute an LXC command with robust error handling and retry
   */
  async execute(
    args: string[],
    options: LxcCommandOptions = {}
  ): Promise<{ stdout: string, stderr: string }> {
    // Ensure initialized
    await this.initialize();

    const opts = { ...DEFAULT_LXC_COMMAND_OPTIONS, ...options };
    const context = `LXC command: ${args.join(' ')}`;

    // Create the execution function
    const executeFn = async () => {
      // Try with primary paths first
      const primaryExecution = async () => {
        return this.executeWithPaths(args, this.primaryLxcPath, this.primaryConfigPath, opts);
      };

      // If fallback paths are available and enabled, use them as fallback
      if (this.fallbackLxcPath && this.fallbackConfigPath && opts.useFallbackPaths) {
        const fallbackExecution = async () => {
          logger.info(`Using fallback paths for LXC command: ${args.join(' ')}`);
          return this.executeWithPaths(args, this.fallbackLxcPath || '', this.fallbackConfigPath || '', opts);
        };

        return withFallback(primaryExecution, fallbackExecution, {
          context,
          retryBeforeFallback: opts.retry,
          retryOptions: opts.retryOptions
        });
      } else {
        // No fallback paths, just use primary with retry if enabled
        if (opts.retry) {
          return retry(primaryExecution, {
            ...opts.retryOptions,
            context
          });
        } else {
          return primaryExecution();
        }
      }
    };

    try {
      return await executeFn();
    } catch (error: any) {
      // Convert to appropriate error type
      if (error.code === 'EACCES') {
        throw new PermissionError(`Permission denied executing LXC command: ${args.join(' ')}`, {
          cause: error,
          details: { args, code: error.code }
        });
      } else if (error.code === 'ENOENT') {
        throw new ContainerizationError(`LXC command not found: ${args.join(' ')}`, {
          cause: error,
          details: { args, code: error.code }
        });
      } else if (error.code === 'ETIMEDOUT') {
        throw new TimeoutError(`LXC command timed out: ${args.join(' ')}`, {
          cause: error,
          details: { args, code: error.code, timeout: opts.timeout }
        });
      } else {
        throw new ContainerizationError(`Error executing LXC command: ${args.join(' ')}`, {
          cause: error,
          details: { args, stdout: error.stdout, stderr: error.stderr }
        });
      }
    }
  }

  /**
   * Execute an LXC command with specific paths
   */
  private async executeWithPaths(
    args: string[],
    lxcPath: string,
    configPath: string,
    options: LxcCommandOptions
  ): Promise<{ stdout: string, stderr: string }> {
    try {
      // Build command
      const command = this.buildCommand(args);

      // Set environment variables
      const env = {
        ...process.env,
        LXC_PATH: lxcPath,
        LXC_CONF_PATH: configPath,
        LXC_LOG_LEVEL: this.logLevel,
        ...options.env
      };

      logger.debug(`Executing LXC command: ${command}`);
      logger.debug(`Using LXC_PATH: ${lxcPath}`);
      logger.debug(`Using LXC_CONF_PATH: ${configPath}`);

      // Execute command
      const { stdout, stderr } = await execAsync(command, {
        env,
        timeout: options.timeout || DEFAULT_LXC_COMMAND_OPTIONS.timeout,
        cwd: options.cwd
      });

      return { stdout, stderr };
    } catch (error: any) {
      logger.error(`Error executing LXC command with paths: ${error.message}`, {
        command: args.join(' '),
        lxcPath,
        configPath,
        error
      });

      // Include stdout and stderr in the error if available
      if (error.stdout || error.stderr) {
        error.stdout = error.stdout || '';
        error.stderr = error.stderr || '';
      }

      throw error;
    }
  }

  /**
   * Build an LXC command
   */
  private buildCommand(args: string[]): string {
    if (args.length === 0) {
      return '';
    }

    // Escape arguments
    const escapedArgs = args.map(arg => {
      if (arg.includes(' ') || arg.includes('"') || arg.includes("'")) {
        return `"${arg.replace(/"/g, '\\"')}"`;
      }
      return arg;
    });

    // For LXC commands (not LXD), we need to use the full command name
    // The first argument should already include the command name (e.g., lxc-create, lxc-start)
    return escapedArgs.join(' ');
  }
}
