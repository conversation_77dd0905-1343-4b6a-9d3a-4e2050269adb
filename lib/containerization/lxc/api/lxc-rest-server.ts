/**
 * LXC REST API Server
 *
 * Provides a REST API server for interacting with LXC containers.
 */

import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { getLxcPath, getLxcConfigPath, isPathWritable } from '../utils/paths';
import { logger } from '@/lib/logger';
import { EnhancedLxcCommand } from './enhanced-lxc-command';
import {
  ContainerizationError,
  ConnectionError,
  NotFoundError,
  PermissionError,
  TimeoutError,
  logError
} from '../../shared/error-handling';
import { retry } from '../../shared/retry';
import {
  ResourceCleanupRegistry,
  globalCleanupRegistry
} from '../../shared/resource-management';
import {
  performHealthCheck,
  createCommandExecutionCheck
} from '../../shared/health-check';
import { findAvailablePort } from '../../shared/port-finder';

const execAsync = promisify(exec);

/**
 * LXC REST API server options
 */
export interface LxcRestServerOptions {
  port?: number;
  host?: string;
  lxcPath?: string;
  configPath?: string;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  cleanupRegistry?: ResourceCleanupRegistry;
}

/**
 * LXC REST API server
 */
export class LxcRestServer {
  private app: express.Application;
  private server: any;
  private readonly options: LxcRestServerOptions;
  private lxcPath: string = '';
  private configPath: string = '';
  private lxcCommand: EnhancedLxcCommand;
  private cleanupRegistry: ResourceCleanupRegistry;

  constructor(options: LxcRestServerOptions = {}) {
    this.options = {
      port: options.port || 8080,
      host: options.host || 'localhost',
      lxcPath: options.lxcPath,
      configPath: options.configPath,
      logLevel: options.logLevel || 'info',
      cleanupRegistry: options.cleanupRegistry
    };

    // Create Express app
    this.app = express();

    // Configure middleware
    this.app.use(cors());
    this.app.use(bodyParser.json());
    this.app.use(bodyParser.text({ type: 'application/octet-stream' }));

    // Initialize resource cleanup registry
    this.cleanupRegistry = this.options.cleanupRegistry || globalCleanupRegistry;

    // Initialize enhanced LXC command
    this.lxcCommand = new EnhancedLxcCommand(this.options);

    // Configure routes
    this.configureRoutes();
  }

  /**
   * Configure API routes
   */
  private configureRoutes(): void {
    // API version
    const apiVersion = 'v1';
    const apiPrefix = `/api/${apiVersion}`;

    // Health check
    this.app.get(`${apiPrefix}/ping`, (_req: Request, res: Response) => {
      res.status(200).json({ status: 'ok' });
    });

    // List containers
    this.app.get(`${apiPrefix}/containers`, this.asyncHandler(this.listContainers.bind(this)));

    // Get container info
    this.app.get(`${apiPrefix}/containers/:name`, this.asyncHandler(this.getContainerInfo.bind(this)));

    // Create container
    this.app.post(`${apiPrefix}/containers`, this.asyncHandler(this.createContainer.bind(this)));

    // Update container state
    this.app.put(`${apiPrefix}/containers/:name/state`, this.asyncHandler(this.updateContainerState.bind(this)));

    // Update container config
    this.app.put(`${apiPrefix}/containers/:name/config`, this.asyncHandler(this.updateContainerConfig.bind(this)));

    // Execute command in container
    this.app.post(`${apiPrefix}/containers/:name/exec`, this.asyncHandler(this.executeCommand.bind(this)));

    // List container files
    this.app.get(`${apiPrefix}/containers/:name/files`, this.asyncHandler(this.listContainerFiles.bind(this)));

    // Get container file content
    this.app.get(`${apiPrefix}/containers/:name/files/content`, this.asyncHandler(this.getContainerFileContent.bind(this)));

    // Put container file content
    this.app.put(`${apiPrefix}/containers/:name/files/content`, this.asyncHandler(this.putContainerFileContent.bind(this)));

    // List images
    this.app.get(`${apiPrefix}/images`, this.asyncHandler(this.listImages.bind(this)));

    // Get image info
    this.app.get(`${apiPrefix}/images/:fingerprint`, this.asyncHandler(this.getImageInfo.bind(this)));

    // Error handler
    this.app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
      logger.error('API Error:', err);
      res.status(err.status || 500).json({
        error: err.message || 'Internal Server Error',
        details: err.details || {}
      });
    });
  }

  /**
   * Async handler wrapper
   */
  private asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) {
    return (req: Request, res: Response, next: NextFunction) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Start the server
   */
  async start(): Promise<void> {
    try {
      // Initialize the LXC command
      await this.lxcCommand.initialize();

      // Perform health check
      const isHealthy = await this.lxcCommand.checkHealth();
      if (!isHealthy) {
        logger.warn('LXC health check failed during server startup');
      }

      // Get the LXC paths from the command
      this.lxcPath = this.options.lxcPath || await getLxcPath();
      this.configPath = this.options.configPath || await getLxcConfigPath();

      // Set environment variables for LXC
      process.env.LXC_PATH = this.lxcPath;
      process.env.LXC_CONF_PATH = this.configPath;

      // Find an available port if the specified port is in use
      let port = this.options.port || 8080;
      try {
        port = await findAvailablePort(port, this.options.host as string);
        if (port !== this.options.port) {
          logger.info(`Port ${this.options.port} is in use, using port ${port} instead`);
          // Update the options with the new port
          this.options.port = port;
        }
      } catch (portError) {
        logger.error('Failed to find an available port', portError);
        throw new ContainerizationError('Failed to find an available port', {
          cause: portError
        });
      }

      // Start the server
      return new Promise((resolve, reject) => {
        this.server = this.app.listen(port, this.options.host as string, () => {
          logger.info(`LXC REST API server listening on http://${this.options.host}:${port}`);
          logger.info(`Using LXC_PATH: ${this.lxcPath}`);
          logger.info(`Using LXC_CONF_PATH: ${this.configPath}`);
          resolve();
        });

        this.server.on('error', (error: any) => {
          logger.error('Failed to start LXC REST API server', error);
          reject(error);
        });
      });
    } catch (error) {
      logger.error('Failed to initialize LXC REST API server', error);

      // Convert to appropriate error type if not already
      if (!(error instanceof ContainerizationError)) {
        throw new ContainerizationError('Failed to initialize LXC REST API server', {
          cause: error,
          details: { error }
        });
      }

      throw error;
    }
  }

  /**
   * Stop the server and clean up resources
   */
  async stop(): Promise<void> {
    try {
      // Stop the server
      await new Promise<void>((resolve, reject) => {
        if (this.server) {
          this.server.close((error: any) => {
            if (error) {
              logger.error('Failed to stop LXC REST API server', error);
              reject(error);
            } else {
              logger.info('LXC REST API server stopped');
              resolve();
            }
          });
        } else {
          resolve();
        }
      });

      // Clean up resources
      try {
        logger.info('Cleaning up LXC REST API server resources');
        await this.cleanupRegistry.cleanupAll();
        logger.info('LXC REST API server resources cleaned up');
      } catch (cleanupError) {
        logger.error('Error cleaning up LXC REST API server resources', cleanupError);
        // Don't throw the error, just log it
      }
    } catch (error) {
      logger.error('Error stopping LXC REST API server', error);
      throw new ContainerizationError('Failed to stop LXC REST API server', {
        cause: error
      });
    }
  }

  /**
   * Execute an LXC command with robust error handling and retry
   */
  private async executeLxcCommand(args: string[]): Promise<{ stdout: string, stderr: string }> {
    try {
      // Use the enhanced LXC command with retry and fallback
      return await this.lxcCommand.execute(args, {
        env: {
          LXC_PATH: this.lxcPath,
          LXC_CONF_PATH: this.configPath
        },
        retry: true,
        retryOptions: {
          maxAttempts: 3,
          initialDelayMs: 1000,
          maxDelayMs: 10000,
          backoffFactor: 2,
          jitter: true,
          context: `LXC command: ${args.join(' ')}`
        },
        useFallbackPaths: true,
        cleanupRegistry: this.cleanupRegistry
      });
    } catch (error: any) {
      // Log the error with detailed information
      logError(error, `Error executing LXC command: ${args.join(' ')}`);

      // Convert to appropriate error type if not already
      if (!(error instanceof ContainerizationError)) {
        if (error.code === 'ENOENT') {
          throw new NotFoundError(`LXC command not found: ${args.join(' ')}`, {
            cause: error,
            details: { args }
          });
        } else if (error.code === 'EACCES') {
          throw new PermissionError(`Permission denied executing LXC command: ${args.join(' ')}`, {
            cause: error,
            details: { args }
          });
        } else {
          throw new ContainerizationError(`Error executing LXC command: ${args.join(' ')}`, {
            cause: error,
            details: { args, stdout: error.stdout, stderr: error.stderr }
          });
        }
      }

      // If the error has details with stdout/stderr, return them
      if (error.details && (error.details.stdout || error.details.stderr)) {
        return {
          stdout: error.details.stdout || '',
          stderr: error.details.stderr || ''
        };
      }

      // For non-ContainerizationError types that might have stdout/stderr directly
      if (typeof error === 'object' && error !== null && ('stdout' in error || 'stderr' in error)) {
        return {
          stdout: (error as any).stdout || '',
          stderr: (error as any).stderr || ''
        };
      }

      throw error;
    }
  }

  /**
   * List containers
   */
  private async listContainers(_req: Request, res: Response): Promise<void> {
    try {
      const { stdout } = await this.executeLxcCommand(['lxc-ls']);
      const containers = stdout.trim().split('\n').filter(Boolean);
      res.json({ containers });
    } catch (error: any) {
      throw { status: 500, message: 'Failed to list containers', details: error.message };
    }
  }

  /**
   * Get container info
   */
  private async getContainerInfo(req: Request, res: Response): Promise<void> {
    // Get container name from params and decode it
    const containerName = decodeURIComponent(req.params.name);

    // Log the request for debugging
    logger.debug(`Getting container info for: ${containerName}`);

    try {
      // Check if the container exists first
      try {
        const { stdout: listOutput } = await this.executeLxcCommand(['ls', '--format', 'csv']);
        const containers = listOutput.trim().split('\n').filter(Boolean);

        // Extract just the container names from the CSV output
        const containerNames = containers.map(container => container.split(',')[0]);

        if (!containerNames.includes(containerName)) {
          throw {
            status: 404,
            message: `Container not found: ${containerName}`,
            details: `Available containers: ${containerNames.join(', ')}`
          };
        }
      } catch (error: any) {
        if (error.status) throw error;
        logger.warn(`Error checking if container exists: ${containerName}`, error);
        // Continue even if this check fails
      }

      // Get container state - use 'lxc info' and parse the state from output
      const { stdout: infoOutput } = await this.executeLxcCommand(['info', containerName]);
      // Parse state from info output
      const stateMatch = infoOutput.match(/Status:\s+(\w+)/);
      const state = stateMatch ? stateMatch[1].toUpperCase() : 'UNKNOWN';

      // Get container config
      const { stdout: configOutput } = await this.executeLxcCommand(['config', 'show', containerName]);
      const config = configOutput.trim();

      // Get container IP addresses if running
      let ip: string[] = [];
      if (state === 'RUNNING') {
        try {
          // Parse IP addresses from the info output we already have
          const ipMatches = infoOutput.match(/eth\d+:\s+inet\s+(\d+\.\d+\.\d+\.\d+)/g);
          if (ipMatches) {
            ip = ipMatches.map(match => {
              const ipMatch = match.match(/(\d+\.\d+\.\d+\.\d+)/);
              return ipMatch ? ipMatch[1] : '';
            }).filter(Boolean);
          }
        } catch (error) {
          logger.warn(`Failed to get IP addresses for container ${containerName}`, error);
        }
      }

      // Parse the config as a structured object instead of trying to parse as JSON
      let parsedConfig: Record<string, any> = { raw: config };

      try {
        // LXC config is typically in a key=value format, not JSON
        // Parse it into a structured object
        const configLines = config.split('\n');
        const configObj: Record<string, any> = {};

        for (const line of configLines) {
          const trimmedLine = line.trim();
          if (!trimmedLine || trimmedLine.startsWith('#')) {
            continue;
          }

          // Try to split by the first equals sign
          const equalsIndex = trimmedLine.indexOf('=');
          if (equalsIndex > 0) {
            const key = trimmedLine.substring(0, equalsIndex).trim();
            const value = trimmedLine.substring(equalsIndex + 1).trim();

            // Handle nested properties using dot notation
            const keyParts = key.split('.');
            let current = configObj;

            for (let i = 0; i < keyParts.length - 1; i++) {
              const part = keyParts[i];
              if (!current[part]) {
                current[part] = {};
              }
              current = current[part];
            }

            current[keyParts[keyParts.length - 1]] = value;
          }
        }

        parsedConfig = configObj;
      } catch (error) {
        logger.warn(`Failed to parse container config for ${containerName}`, error);
        // Keep using the raw config string if parsing fails
      }

      res.json({
        container: {
          id: containerName,
          name: containerName,
          state,
          ip,
          config: parsedConfig
        }
      });
    } catch (error: any) {
      if (error.status) throw error;
      throw {
        status: 500,
        message: `Failed to get container info: ${containerName}`,
        details: error.message
      };
    }
  }

  /**
   * Create container
   */
  private async createContainer(req: Request, res: Response): Promise<void> {
    const { name, config } = req.body;

    if (!name) {
      throw { status: 400, message: 'Container name is required' };
    }

    try {
      // Check if container already exists
      const { stdout } = await this.executeLxcCommand(['lxc-ls']);
      const containers = stdout.trim().split('\n').filter(Boolean);

      if (containers.includes(name)) {
        throw { status: 409, message: `Container ${name} already exists` };
      }

      // Create container - use the correct LXC command format
      if (config && config.template) {
        // For LXC (not LXD), the command format is typically:
        // lxc-create -t <template> -n <name> [options]
        await this.executeLxcCommand(['lxc-create', '-t', config.template.name, '-n', name]);
      } else {
        // Fallback to a default template if none is specified
        await this.executeLxcCommand(['lxc-create', '-t', 'download', '-n', name, '--', '--dist', 'ubuntu', '--release', 'focal', '--arch', 'amd64']);
      }

      res.status(201).json({ message: `Container ${name} created successfully` });
    } catch (error: any) {
      if (error.status) {
        throw error;
      }

      // Log detailed error information
      logger.error(`Failed to create container: ${name}`, {
        error,
        command: error.details?.args?.join(' ') || 'unknown command',
        stdout: error.details?.stdout || '',
        stderr: error.details?.stderr || ''
      });

      // Provide more detailed error message
      const errorDetails = error.details?.stderr || error.message;
      throw {
        status: 500,
        message: `Failed to create container: ${name}`,
        details: errorDetails
      };
    }
  }

  /**
   * Update container state
   */
  private async updateContainerState(req: Request, res: Response): Promise<void> {
    const { name } = req.params;
    const { action, force } = req.body;

    if (!action) {
      throw { status: 400, message: 'Action is required' };
    }

    try {
      switch (action) {
        case 'start':
          await this.executeLxcCommand(['lxc-start', '-n', name]);
          break;
        case 'stop':
          await this.executeLxcCommand(force ? ['lxc-stop', '-n', name, '-k'] : ['lxc-stop', '-n', name]);
          break;
        case 'restart':
          // For LXC, restart is a stop followed by a start
          await this.executeLxcCommand(['lxc-stop', '-n', name]);
          await this.executeLxcCommand(['lxc-start', '-n', name]);
          break;
        default:
          throw { status: 400, message: `Invalid action: ${action}` };
      }

      res.json({ message: `Container ${name} ${action}ed successfully` });
    } catch (error: any) {
      throw { status: 500, message: `Failed to ${action} container: ${name}`, details: error.message };
    }
  }

  /**
   * Update container config
   */
  private async updateContainerConfig(req: Request, res: Response): Promise<void> {
    const { name } = req.params;
    const { config } = req.body;

    if (!config) {
      throw { status: 400, message: 'Config is required' };
    }

    try {
      // Get current config
      const { stdout } = await this.executeLxcCommand(['config', 'show', name]);
      const currentConfig = JSON.parse(stdout.trim());

      // Merge configs
      const newConfig = {
        ...currentConfig,
        ...config
      };

      // Write config to temporary file
      const tempFile = `/tmp/lxc-config-${Date.now()}.json`;
      await fs.writeFile(tempFile, JSON.stringify(newConfig, null, 2));

      // Update container config
      await this.executeLxcCommand(['config', 'edit', name, tempFile]);

      // Delete temporary file
      await fs.unlink(tempFile);

      res.json({ message: `Container ${name} config updated successfully` });
    } catch (error: any) {
      throw { status: 500, message: `Failed to update container config: ${name}`, details: error.message };
    }
  }

  /**
   * Execute command in container
   */
  private async executeCommand(req: Request, res: Response): Promise<void> {
    const { name } = req.params;
    const { command, environment, workingDirectory, user } = req.body;

    if (!command || !Array.isArray(command)) {
      throw { status: 400, message: 'Command is required and must be an array' };
    }

    try {
      // For LXC, we need to use lxc-attach to execute commands in a container
      let args = ['lxc-attach', '-n', name];

      // Add environment variables
      if (environment) {
        for (const [key, value] of Object.entries(environment)) {
          args.push('-v', `${key}=${value}`);
        }
      }

      // Add working directory option
      if (workingDirectory) {
        args.push('--', 'sh', '-c', `cd ${workingDirectory} && ${command.join(' ')}`);
      } else if (user) {
        // Add user option
        args.push('--', 'su', '-', user, '-c', command.join(' '));
      } else {
        // Just add the command
        args.push('--', ...command);
      }

      // Execute command
      const { stdout, stderr } = await this.executeLxcCommand(args);

      res.json({
        stdout,
        stderr,
        exitCode: 0 // TODO: Get actual exit code
      });
    } catch (error: any) {
      res.json({
        stdout: '',
        stderr: error.message,
        exitCode: 1
      });
    }
  }

  /**
   * List container files
   */
  private async listContainerFiles(req: Request, res: Response): Promise<void> {
    const { name } = req.params;
    const { path: filePath } = req.query;

    if (!filePath || typeof filePath !== 'string') {
      throw { status: 400, message: 'Path is required' };
    }

    try {
      // For LXC, we need to use lxc-attach to list files
      const { stdout } = await this.executeLxcCommand(['lxc-attach', '-n', name, '--', 'ls', '-la', filePath]);

      // Parse output
      const files = stdout.trim().split('\n').filter(Boolean).map(line => {
        const parts = line.split(' ');
        const type = parts[0].startsWith('d') ? 'directory' : 'file';
        const name = parts[parts.length - 1];

        return {
          name,
          type,
          size: 0, // TODO: Parse size
          lastModified: new Date() // TODO: Parse last modified date
        };
      });

      res.json({ files });
    } catch (error: any) {
      throw { status: 500, message: `Failed to list container files: ${name} ${filePath}`, details: error.message };
    }
  }

  /**
   * Get container file content
   */
  private async getContainerFileContent(req: Request, res: Response): Promise<void> {
    const { name } = req.params;
    const { path: filePath } = req.query;

    if (!filePath || typeof filePath !== 'string') {
      throw { status: 400, message: 'Path is required' };
    }

    try {
      // Create a temporary file to store the content
      const tempFile = path.join('/tmp', `lxc-file-${Date.now()}`);

      // For LXC, we need to use a different approach to pull files
      // First, create a temporary directory in the container
      const containerTempDir = '/tmp/lxc-file-transfer';
      await this.executeLxcCommand(['lxc-attach', '-n', name, '--', 'mkdir', '-p', containerTempDir]);

      // Copy the file to the temporary directory
      await this.executeLxcCommand(['lxc-attach', '-n', name, '--', 'cp', filePath, containerTempDir]);

      // Get the filename
      const fileName = path.basename(filePath);

      // Use lxc-usernsexec to copy the file from the container's filesystem
      await this.executeLxcCommand([
        'lxc-usernsexec',
        '-m', 'b:0:100000:65536',
        '--',
        'cp',
        `/var/lib/lxc/${name}/rootfs${containerTempDir}/${fileName}`,
        tempFile
      ]);

      // Read the file content
      const content = await fs.readFile(tempFile, 'utf8');

      // Delete the temporary file
      await fs.unlink(tempFile);

      res.send(content);
    } catch (error: any) {
      throw { status: 500, message: `Failed to get container file content: ${name} ${filePath}`, details: error.message };
    }
  }

  /**
   * Put container file content
   */
  private async putContainerFileContent(req: Request, res: Response): Promise<void> {
    const { name } = req.params;
    const { path: filePath } = req.query;
    const content = req.body;

    if (!filePath || typeof filePath !== 'string') {
      throw { status: 400, message: 'Path is required' };
    }

    try {
      // Create a temporary file to store the content
      const tempFile = path.join('/tmp', `lxc-file-${Date.now()}`);

      // Write the content to the temporary file
      await fs.writeFile(tempFile, content);

      // For LXC, we need to use a different approach to push files
      // First, create a temporary directory in the container
      const containerTempDir = '/tmp/lxc-file-transfer';
      await this.executeLxcCommand(['lxc-attach', '-n', name, '--', 'mkdir', '-p', containerTempDir]);

      // Get the directory path in the container's filesystem
      const containerRootfsPath = `/var/lib/lxc/${name}/rootfs`;
      const containerTempFilePath = `${containerTempDir}/${path.basename(tempFile)}`;

      // Use lxc-usernsexec to copy the file to the container's filesystem
      await this.executeLxcCommand([
        'lxc-usernsexec',
        '-m', 'b:0:100000:65536',
        '--',
        'cp',
        tempFile,
        `${containerRootfsPath}${containerTempFilePath}`
      ]);

      // Move the file to the desired location in the container
      await this.executeLxcCommand([
        'lxc-attach',
        '-n',
        name,
        '--',
        'mv',
        containerTempFilePath,
        filePath
      ]);

      // Delete the temporary file
      await fs.unlink(tempFile);

      res.status(204).end();
    } catch (error: any) {
      throw { status: 500, message: `Failed to put container file content: ${name} ${filePath}`, details: error.message };
    }
  }

  /**
   * List images
   */
  private async listImages(_req: Request, res: Response): Promise<void> {
    try {
      // For LXC, we need to list templates instead of images
      const { stdout } = await this.executeLxcCommand(['lxc-create', '--help']);

      // Parse the output to extract available templates
      const templateMatch = stdout.match(/Available templates:\s+(.*?)(?:\n\n|\n[^\n])/s);
      const templateList = templateMatch ? templateMatch[1].trim().split(/\s+/) : [];

      res.json({ templates: templateList });
    } catch (error: any) {
      throw { status: 500, message: 'Failed to list templates', details: error.message };
    }
  }

  /**
   * Get image info
   */
  private async getImageInfo(req: Request, res: Response): Promise<void> {
    const { fingerprint } = req.params;

    try {
      // For LXC, we don't have image fingerprints, but we can get template info
      const { stdout } = await this.executeLxcCommand(['lxc-create', '-t', fingerprint, '--help']);

      // Parse output to extract template information
      const template: any = {
        id: fingerprint,
        name: fingerprint,
        description: stdout.split('\n')[0]?.trim() || ''
      };

      // Extract options
      const optionsMatch = stdout.match(/Options for the (.*?) template:(.*?)(?:\n\n|\n[^\s]|$)/s);
      if (optionsMatch) {
        template.options = optionsMatch[2].trim();
      }

      res.json({ template });
    } catch (error: any) {
      throw { status: 500, message: `Failed to get template info: ${fingerprint}`, details: error.message };
    }
  }
}
