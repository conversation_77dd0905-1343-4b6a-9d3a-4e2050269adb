/**
 * LXC REST API Client
 *
 * Provides a client for interacting with the LXC REST API.
 */

import axios, { AxiosInstance } from 'axios';
import { ContainerInfo } from '../models/container';
import { TemplateInfo } from '../models/template';
import { BridgeNetwork, BridgeNetworkConfig } from '../models/network';
import { getLxcPath, getLxcConfigPath } from '../utils/paths';
import { extractContainerName, encodeContainerName } from '../utils/container-name';
import { logger } from '@/lib/logger';
import { ContainerizationError, NotFoundError, ConnectionError } from '../../shared/error-handling';
import { retry } from '../../shared/retry';

/**
 * LXC REST API client options
 */
export interface LxcRestClientOptions {
  baseUrl?: string;
  apiVersion?: string;
  timeout?: number;
  headers?: Record<string, string>;
  auth?: {
    username: string;
    password: string;
  };
  lxcPath?: string;
  configPath?: string;
}

/**
 * LXC REST API client
 */
export class LxcRestClient {
  private client: AxiosInstance;
  private readonly options: LxcRestClientOptions;
  private initialized: boolean = false;
  private lxcPath: string = '';
  private configPath: string = '';
  private useDirectCommands: boolean = false;

  constructor(options: LxcRestClientOptions = {}) {
    this.options = {
      baseUrl: options.baseUrl || 'http://localhost:8080',
      apiVersion: options.apiVersion || 'v1',
      timeout: options.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      },
      auth: options.auth,
      lxcPath: options.lxcPath,
      configPath: options.configPath
    };

    // Create axios instance
    this.client = axios.create({
      baseURL: `${this.options.baseUrl}/api/${this.options.apiVersion}`,
      timeout: this.options.timeout,
      headers: this.options.headers,
      auth: this.options.auth
    });

    // Add request interceptor to log requests
    this.client.interceptors.request.use((config) => {
      logger.debug(`LXC REST API Request: ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    });

    // Add response interceptor to log responses
    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`LXC REST API Response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        if (error.response) {
          logger.error(`LXC REST API Error: ${error.response.status} ${error.response.statusText}`);
          logger.error(`Error details: ${JSON.stringify(error.response.data)}`);
        } else if (error.request) {
          logger.error('LXC REST API Error: No response received');
        } else {
          logger.error(`LXC REST API Error: ${error.message}`);
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Initialize the client
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Get LXC paths
      this.lxcPath = this.options.lxcPath || await getLxcPath();
      this.configPath = this.options.configPath || await getLxcConfigPath();

      // Set environment variables for LXC
      process.env.LXC_PATH = this.lxcPath;
      process.env.LXC_CONF_PATH = this.configPath;

      // Check if the API is available with retry
      try {
        await retry(
          async () => {
            await this.ping();
          },
          {
            maxAttempts: 5,
            initialDelayMs: 1000,
            maxDelayMs: 5000,
            backoffFactor: 1.5,
            jitter: true,
            context: 'LXC REST API ping',
            onRetry: (error, attempt, delay) => {
              logger.warn(`Retrying LXC REST API ping (attempt ${attempt}/5) after ${delay}ms due to error: ${error.message}`);
            }
          }
        );
      } catch (pingError) {
        logger.warn('Failed to connect to LXC REST API server after multiple attempts. Using direct LXC commands instead.');
        // We'll continue initialization but mark the client as using direct commands
        this.useDirectCommands = true;
      }

      this.initialized = true;
      logger.info('LXC REST API client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize LXC REST API client', error);
      throw error;
    }
  }

  /**
   * Ensure the client is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * Ping the API to check if it's available
   */
  async ping(): Promise<boolean> {
    try {
      const response = await this.client.get('/ping');
      return response.status === 200;
    } catch (error) {
      logger.error('Failed to ping LXC REST API', error);
      return false;
    }
  }

  /**
   * List containers
   */
  async listContainers(): Promise<string[]> {
    await this.ensureInitialized();

    // If using direct commands, execute lxc-ls directly
    if (this.useDirectCommands) {
      try {
        const { promisify } = require('util');
        const { exec } = require('child_process');
        const execAsync = promisify(exec);

        logger.debug('Using direct LXC command: lxc-ls');

        const { stdout } = await execAsync('lxc-ls', {
          env: {
            ...process.env,
            LXC_PATH: this.lxcPath,
            LXC_CONF_PATH: this.configPath
          }
        });

        return stdout.trim().split('\n').filter(Boolean);
      } catch (error) {
        logger.error('Failed to list containers using direct command', error);
        throw new ContainerizationError('Failed to list containers', {
          cause: error,
          details: { command: 'lxc-ls' }
        });
      }
    }

    // Otherwise use the REST API
    try {
      const response = await this.client.get('/containers');
      return response.data.containers || [];
    } catch (error) {
      logger.error('Failed to list containers', error);

      // If the API request fails, try using direct command as fallback
      try {
        logger.info('Falling back to direct LXC command for listing containers');
        this.useDirectCommands = true;
        return await this.listContainers();
      } catch (fallbackError) {
        logger.error('Fallback to direct command also failed', fallbackError);
        throw error; // Throw the original error
      }
    }
  }

  /**
   * Get container information
   */
  async getContainerInfo(name: string): Promise<ContainerInfo> {
    await this.ensureInitialized();

    try {
      // Extract the container name and encode it
      const containerName = extractContainerName(name);
      const encodedName = encodeContainerName(containerName);

      const response = await this.client.get(`/containers/${encodedName}`);
      return response.data.container;
    } catch (error) {
      logger.error(`Failed to get container info: ${name}`, error);
      throw error;
    }
  }

  /**
   * Update container configuration
   */
  async updateContainerConfig(name: string, config: any): Promise<void> {
    await this.ensureInitialized();

    try {
      // Extract the container name and encode it
      const containerName = extractContainerName(name);
      const encodedName = encodeContainerName(containerName);

      await this.client.put(`/containers/${encodedName}/config`, {
        config
      });
    } catch (error) {
      logger.error(`Failed to update container config: ${name}`, error);
      throw error;
    }
  }

  /**
   * Create a container
   */
  async createContainer(name: string, config: any): Promise<void> {
    await this.ensureInitialized();

    // If using direct commands, execute lxc-create directly
    if (this.useDirectCommands) {
      try {
        const { promisify } = require('util');
        const { exec } = require('child_process');
        const execAsync = promisify(exec);

        logger.debug(`Using direct LXC command: lxc-create for container ${name}`);

        let command = `lxc-create -n ${name}`;

        // Add template if specified
        if (config && config.template) {
          command += ` -t ${config.template.name}`;

          // Add template options if specified
          if (config.template.release) {
            command += ` -- --release ${config.template.release}`;
          }
        } else {
          // Use a default template if none is specified
          command += ` -t download -- --dist ubuntu --release focal --arch amd64`;
        }

        await execAsync(command, {
          env: {
            ...process.env,
            LXC_PATH: this.lxcPath,
            LXC_CONF_PATH: this.configPath
          }
        });

        logger.info(`Container ${name} created successfully using direct command`);
      } catch (error) {
        logger.error(`Failed to create container ${name} using direct command`, error);
        throw new ContainerizationError(`Failed to create container: ${name}`, {
          cause: error,
          details: { name, config }
        });
      }
      return;
    }

    // Otherwise use the REST API
    try {
      await this.client.post('/containers', {
        name,
        config
      });
    } catch (error) {
      logger.error(`Failed to create container: ${name}`, error);

      // If the API request fails, try using direct command as fallback
      try {
        logger.info(`Falling back to direct LXC command for creating container ${name}`);
        this.useDirectCommands = true;
        await this.createContainer(name, config);
      } catch (fallbackError) {
        logger.error(`Fallback to direct command also failed for container ${name}`, fallbackError);
        throw error; // Throw the original error
      }
    }
  }

  /**
   * Start a container
   */
  async startContainer(name: string): Promise<void> {
    await this.ensureInitialized();

    // If using direct commands, execute lxc-start directly
    if (this.useDirectCommands) {
      try {
        const { promisify } = require('util');
        const { exec } = require('child_process');
        const execAsync = promisify(exec);

        logger.debug(`Using direct LXC command: lxc-start for container ${name}`);

        // Extract the container name
        const containerName = extractContainerName(name);

        await execAsync(`lxc-start -n ${containerName}`, {
          env: {
            ...process.env,
            LXC_PATH: this.lxcPath,
            LXC_CONF_PATH: this.configPath
          }
        });

        logger.info(`Container ${name} started successfully using direct command`);
      } catch (error) {
        logger.error(`Failed to start container ${name} using direct command`, error);
        throw new ContainerizationError(`Failed to start container: ${name}`, {
          cause: error,
          details: { name }
        });
      }
      return;
    }

    // Otherwise use the REST API
    try {
      // Extract the container name and encode it
      const containerName = extractContainerName(name);
      const encodedName = encodeContainerName(containerName);

      await this.client.put(`/containers/${encodedName}/state`, {
        action: 'start'
      });
    } catch (error) {
      logger.error(`Failed to start container: ${name}`, error);

      // If the API request fails, try using direct command as fallback
      try {
        logger.info(`Falling back to direct LXC command for starting container ${name}`);
        this.useDirectCommands = true;
        await this.startContainer(name);
      } catch (fallbackError) {
        logger.error(`Fallback to direct command also failed for starting container ${name}`, fallbackError);
        throw error; // Throw the original error
      }
    }
  }

  /**
   * Stop a container
   */
  async stopContainer(name: string, force: boolean = false): Promise<void> {
    await this.ensureInitialized();

    // If using direct commands, execute lxc-stop directly
    if (this.useDirectCommands) {
      try {
        const { promisify } = require('util');
        const { exec } = require('child_process');
        const execAsync = promisify(exec);

        logger.debug(`Using direct LXC command: lxc-stop for container ${name}`);

        // Extract the container name
        const containerName = extractContainerName(name);

        // Add force option if specified
        const forceOption = force ? ' -k' : '';

        await execAsync(`lxc-stop -n ${containerName}${forceOption}`, {
          env: {
            ...process.env,
            LXC_PATH: this.lxcPath,
            LXC_CONF_PATH: this.configPath
          }
        });

        logger.info(`Container ${name} stopped successfully using direct command`);
      } catch (error) {
        logger.error(`Failed to stop container ${name} using direct command`, error);
        throw new ContainerizationError(`Failed to stop container: ${name}`, {
          cause: error,
          details: { name, force }
        });
      }
      return;
    }

    // Otherwise use the REST API
    try {
      // Extract the container name and encode it
      const containerName = extractContainerName(name);
      const encodedName = encodeContainerName(containerName);

      await this.client.put(`/containers/${encodedName}/state`, {
        action: 'stop',
        force
      });
    } catch (error) {
      logger.error(`Failed to stop container: ${name}`, error);

      // If the API request fails, try using direct command as fallback
      try {
        logger.info(`Falling back to direct LXC command for stopping container ${name}`);
        this.useDirectCommands = true;
        await this.stopContainer(name, force);
      } catch (fallbackError) {
        logger.error(`Fallback to direct command also failed for stopping container ${name}`, fallbackError);
        throw error; // Throw the original error
      }
    }
  }

  /**
   * Delete a container
   */
  async deleteContainer(name: string): Promise<void> {
    await this.ensureInitialized();

    // If using direct commands, execute lxc-destroy directly
    if (this.useDirectCommands) {
      try {
        const { promisify } = require('util');
        const { exec } = require('child_process');
        const execAsync = promisify(exec);

        logger.debug(`Using direct LXC command: lxc-destroy for container ${name}`);

        // Extract the container name
        const containerName = extractContainerName(name);

        // First try to stop the container if it's running
        try {
          await execAsync(`lxc-stop -n ${containerName} -k`, {
            env: {
              ...process.env,
              LXC_PATH: this.lxcPath,
              LXC_CONF_PATH: this.configPath
            }
          });
        } catch (stopError) {
          // Ignore stop errors, the container might not be running
          logger.debug(`Container ${name} might not be running, continuing with destroy`);
        }

        // Now destroy the container
        await execAsync(`lxc-destroy -n ${containerName}`, {
          env: {
            ...process.env,
            LXC_PATH: this.lxcPath,
            LXC_CONF_PATH: this.configPath
          }
        });

        logger.info(`Container ${name} deleted successfully using direct command`);
      } catch (error) {
        logger.error(`Failed to delete container ${name} using direct command`, error);
        throw new ContainerizationError(`Failed to delete container: ${name}`, {
          cause: error,
          details: { name }
        });
      }
      return;
    }

    // Otherwise use the REST API
    try {
      // Extract the container name and encode it
      const containerName = extractContainerName(name);
      const encodedName = encodeContainerName(containerName);

      await this.client.delete(`/containers/${encodedName}`);
    } catch (error) {
      logger.error(`Failed to delete container: ${name}`, error);

      // If the API request fails, try using direct command as fallback
      try {
        logger.info(`Falling back to direct LXC command for deleting container ${name}`);
        this.useDirectCommands = true;
        await this.deleteContainer(name);
      } catch (fallbackError) {
        logger.error(`Fallback to direct command also failed for deleting container ${name}`, fallbackError);
        throw error; // Throw the original error
      }
    }
  }

  /**
   * Update container state
   */
  async updateContainerState(name: string, action: string): Promise<void> {
    await this.ensureInitialized();

    try {
      // Extract the container name and encode it
      const containerName = extractContainerName(name);
      const encodedName = encodeContainerName(containerName);

      await this.client.put(`/containers/${encodedName}/state`, {
        action
      });
    } catch (error) {
      logger.error(`Failed to update container state (${action}): ${name}`, error);
      throw error;
    }
  }

  /**
   * Execute a command in a container
   */
  async executeCommand(
    name: string,
    command: string[],
    options: {
      env?: Record<string, string>;
      cwd?: string;
      user?: string;
      timeout?: number;
    } = {}
  ): Promise<{ stdout: string; stderr: string; exitCode: number }> {
    await this.ensureInitialized();

    // If using direct commands, execute lxc-attach directly
    if (this.useDirectCommands) {
      try {
        const { promisify } = require('util');
        const { exec } = require('child_process');
        const execAsync = promisify(exec);

        logger.debug(`Using direct LXC command: lxc-attach for container ${name}`);

        // Extract the container name
        const containerName = extractContainerName(name);

        // Build the command
        let lxcCommand = `lxc-attach -n ${containerName}`;

        // Add environment variables
        if (options.env) {
          for (const [key, value] of Object.entries(options.env)) {
            lxcCommand += ` -v ${key}=${value}`;
          }
        }

        // Add working directory and command
        if (options.cwd) {
          lxcCommand += ` -- sh -c "cd ${options.cwd} && ${command.join(' ')}"`;
        } else if (options.user) {
          // Add user
          lxcCommand += ` -- su - ${options.user} -c "${command.join(' ')}"`;
        } else {
          // Just add the command
          lxcCommand += ` -- ${command.join(' ')}`;
        }

        // Execute the command with timeout
        const timeout = options.timeout || 30000; // Default to 30 seconds

        const { stdout, stderr } = await execAsync(lxcCommand, {
          env: {
            ...process.env,
            LXC_PATH: this.lxcPath,
            LXC_CONF_PATH: this.configPath
          },
          timeout
        });

        return {
          stdout: stdout || '',
          stderr: stderr || '',
          exitCode: 0
        };
      } catch (error: any) {
        logger.error(`Failed to execute command in container ${name} using direct command`, error);

        // Check if it's a timeout error
        const isTimeout = error.signal === 'SIGTERM' && error.killed;

        return {
          stdout: '',
          stderr: isTimeout ? 'Command timed out' : (error.message || 'Unknown error'),
          exitCode: isTimeout ? 124 : 1
        };
      }
    }

    // Otherwise use the REST API
    try {
      // Extract the container name and encode it
      const containerName = extractContainerName(name);
      const encodedName = encodeContainerName(containerName);

      const response = await this.client.post(`/containers/${encodedName}/exec`, {
        command,
        environment: options.env,
        workingDirectory: options.cwd,
        user: options.user,
        timeout: options.timeout
      });

      return {
        stdout: response.data.stdout || '',
        stderr: response.data.stderr || '',
        exitCode: response.data.exitCode || 0
      };
    } catch (error: any) {
      logger.error(`Failed to execute command in container: ${name}`, error);

      // If the API request fails, try using direct command as fallback
      try {
        logger.info(`Falling back to direct LXC command for executing command in container ${name}`);
        this.useDirectCommands = true;
        return await this.executeCommand(name, command, options);
      } catch (fallbackError) {
        logger.error(`Fallback to direct command also failed for executing command in container ${name}`, fallbackError);

        // Return error information in a structured format
        return {
          stdout: '',
          stderr: error.message || 'Unknown error',
          exitCode: 1
        };
      }
    }
  }

  /**
   * List images/templates
   */
  async listImages(): Promise<string[]> {
    await this.ensureInitialized();

    try {
      const response = await this.client.get('/images');
      return response.data.images || [];
    } catch (error) {
      logger.error('Failed to list images', error);
      throw error;
    }
  }

  /**
   * Get image/template information
   */
  async getImageInfo(fingerprint: string): Promise<TemplateInfo> {
    await this.ensureInitialized();

    try {
      const response = await this.client.get(`/images/${fingerprint}`);
      return response.data.image;
    } catch (error) {
      logger.error(`Failed to get image info: ${fingerprint}`, error);
      throw error;
    }
  }

  /**
   * Update client options
   */
  updateOptions(options: Partial<LxcRestClientOptions>): void {
    // Update the options
    Object.assign(this.options, options);

    // Update paths
    if (options.lxcPath) {
      this.lxcPath = options.lxcPath;
    }

    if (options.configPath) {
      this.configPath = options.configPath;
    }

    // Update baseURL if baseUrl changed
    if (options.baseUrl) {
      this.client.defaults.baseURL = `${options.baseUrl}/api/${this.options.apiVersion}`;
      logger.info(`Updated LXC REST client baseURL to ${this.client.defaults.baseURL}`);
    }

    // Reinitialize if needed
    if (this.initialized && (options.lxcPath || options.configPath || options.baseUrl)) {
      this.initialized = false;
      this.ensureInitialized().catch(error => {
        logger.error('Failed to reinitialize LXC REST client after options update', error);
      });
    }
  }

  /**
   * Get container files
   */
  async getContainerFiles(name: string, path: string): Promise<any[]> {
    await this.ensureInitialized();

    try {
      const response = await this.client.get(`/containers/${name}/files`, {
        params: { path }
      });
      return response.data.files || [];
    } catch (error) {
      logger.error(`Failed to get container files: ${name} ${path}`, error);
      throw error;
    }
  }

  /**
   * Get container file content
   */
  async getContainerFileContent(name: string, path: string): Promise<string> {
    await this.ensureInitialized();

    try {
      const response = await this.client.get(`/containers/${name}/files/content`, {
        params: { path },
        responseType: 'text'
      });
      return response.data;
    } catch (error) {
      logger.error(`Failed to get container file content: ${name} ${path}`, error);
      throw error;
    }
  }

  /**
   * Put container file content
   */
  async putContainerFileContent(name: string, path: string, content: string): Promise<void> {
    await this.ensureInitialized();

    try {
      await this.client.put(`/containers/${name}/files/content`, content, {
        params: { path },
        headers: {
          'Content-Type': 'application/octet-stream'
        }
      });
    } catch (error) {
      logger.error(`Failed to put container file content: ${name} ${path}`, error);
      throw error;
    }
  }

  /**
   * Create a bridge network
   */
  async createBridgeNetwork(name: string, config: Partial<BridgeNetworkConfig> = {}): Promise<void> {
    await this.ensureInitialized();

    try {
      await this.client.post('/networks', {
        name,
        type: 'bridge',
        config
      });
      logger.info(`Bridge network ${name} created successfully`);
    } catch (error) {
      logger.error(`Failed to create bridge network: ${name}`, error);

      if (error instanceof Error) {
        throw new ContainerizationError(`Failed to create bridge network: ${name}`, {
          cause: error,
          details: { name, config }
        });
      }

      throw error;
    }
  }

  /**
   * Delete a bridge network
   */
  async deleteBridgeNetwork(name: string): Promise<void> {
    await this.ensureInitialized();

    try {
      await this.client.delete(`/networks/${name}`);
      logger.info(`Bridge network ${name} deleted successfully`);
    } catch (error) {
      logger.error(`Failed to delete bridge network: ${name}`, error);

      if (error instanceof Error) {
        throw new ContainerizationError(`Failed to delete bridge network: ${name}`, {
          cause: error,
          details: { name }
        });
      }

      throw error;
    }
  }

  /**
   * List bridge networks
   */
  async listBridgeNetworks(): Promise<string[]> {
    await this.ensureInitialized();

    try {
      const response = await this.client.get('/networks');
      return response.data.networks || [];
    } catch (error) {
      logger.error('Failed to list bridge networks', error);

      if (error instanceof Error) {
        throw new ContainerizationError('Failed to list bridge networks', {
          cause: error
        });
      }

      throw error;
    }
  }

  /**
   * Get bridge network information
   */
  async getBridgeNetwork(name: string): Promise<BridgeNetwork> {
    await this.ensureInitialized();

    try {
      const response = await this.client.get(`/networks/${name}`);

      if (!response.data.network) {
        throw new NotFoundError(`Bridge network ${name} not found`);
      }

      return response.data.network;
    } catch (error) {
      logger.error(`Failed to get bridge network: ${name}`, error);

      if (error instanceof NotFoundError) {
        throw error;
      }

      if (error instanceof Error) {
        throw new ContainerizationError(`Failed to get bridge network: ${name}`, {
          cause: error,
          details: { name }
        });
      }

      throw error;
    }
  }
}
