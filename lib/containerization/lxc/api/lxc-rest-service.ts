/**
 * LXC REST API Service
 *
 * Provides a service for managing the LXC REST API server.
 */

import { LxcRestServer, LxcRestServerOptions } from './lxc-rest-server';
import { logger } from '@/lib/logger';
import { ContainerizationError } from '../../shared/error-handling';
import { ResourceCleanupRegistry, globalCleanupRegistry } from '../../shared/resource-management';
import { retry } from '../../shared/retry';

/**
 * LXC REST API service options
 */
export interface LxcRestServiceOptions extends LxcRestServerOptions {
  autoStart?: boolean;
  cleanupRegistry?: ResourceCleanupRegistry;
  retryStartup?: boolean;
  maxStartupAttempts?: number;
}

/**
 * LXC REST API service
 */
export class LxcRestService {
  private static instance: LxcRestService;
  private server: LxcRestServer;
  private readonly options: LxcRestServiceOptions;
  private isRunning: boolean = false;

  private constructor(options: LxcRestServiceOptions = {}) {
    this.options = {
      port: options.port || 8080,
      host: options.host || 'localhost',
      lxcPath: options.lxcPath,
      configPath: options.configPath,
      logLevel: options.logLevel || 'info',
      autoStart: options.autoStart !== undefined ? options.autoStart : true,
      cleanupRegistry: options.cleanupRegistry || globalCleanupRegistry,
      retryStartup: options.retryStartup !== undefined ? options.retryStartup : true,
      maxStartupAttempts: options.maxStartupAttempts || 3
    };

    // Create server with enhanced error handling
    this.server = new LxcRestServer({
      ...this.options,
      cleanupRegistry: this.options.cleanupRegistry
    });

    // Auto-start server if enabled
    if (this.options.autoStart) {
      this.start().catch(error => {
        logger.error('Failed to auto-start LXC REST API server', error);
      });
    }
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(options?: LxcRestServiceOptions): LxcRestService {
    if (!LxcRestService.instance) {
      LxcRestService.instance = new LxcRestService(options);
    }
    return LxcRestService.instance;
  }

  /**
   * Start the server with retry capability
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.info('LXC REST API server is already running');
      return;
    }

    try {
      if (this.options.retryStartup) {
        // Use retry with exponential backoff
        await retry(
          async () => {
            await this.server.start();
            this.isRunning = true;

            // Get the actual port that was used (might be different from the requested port)
            const actualPort = (this.server as any).options.port;
            if (actualPort !== this.options.port) {
              logger.info(`LXC REST API server using port ${actualPort} instead of requested port ${this.options.port}`);
              this.options.port = actualPort;

              // Notify any listeners about the port change
              this.notifyPortChange(actualPort);
            }

            logger.info('LXC REST API server started successfully');
          },
          {
            maxAttempts: this.options.maxStartupAttempts || 3,
            initialDelayMs: 1000,
            maxDelayMs: 10000,
            backoffFactor: 2,
            jitter: true,
            context: 'LXC REST API server startup',
            onRetry: (error, attempt, delay) => {
              logger.warn(`Retrying LXC REST API server startup (attempt ${attempt}/${this.options.maxStartupAttempts}) after ${delay}ms due to error: ${error.message}`);
            }
          }
        );
      } else {
        // Start without retry
        await this.server.start();
        this.isRunning = true;

        // Get the actual port that was used (might be different from the requested port)
        const actualPort = (this.server as any).options.port;
        if (actualPort !== this.options.port) {
          logger.info(`LXC REST API server using port ${actualPort} instead of requested port ${this.options.port}`);
          this.options.port = actualPort;

          // Notify any listeners about the port change
          this.notifyPortChange(actualPort);
        }

        logger.info('LXC REST API server started');
      }
    } catch (error) {
      logger.error('Failed to start LXC REST API server after all retry attempts', error);

      // Convert to ContainerizationError if not already
      if (!(error instanceof ContainerizationError)) {
        throw new ContainerizationError('Failed to start LXC REST API server', {
          cause: error,
          details: { options: this.options }
        });
      }

      throw error;
    }
  }

  /**
   * Notify listeners about port changes
   */
  private notifyPortChange(port: number): void {
    // Update any clients that might be using this service
    try {
      // Get the LXC API instance from the container manager
      const lxcApi = require('../core/container-manager').ContainerManager.getInstance().api;

      if (lxcApi && typeof lxcApi.updateClientUrl === 'function') {
        const newUrl = `http://${this.options.host}:${port}`;
        lxcApi.updateClientUrl(newUrl);
        logger.info(`Updated LXC API client URL to ${newUrl}`);
      }
    } catch (error) {
      // Don't throw an error if this fails, just log it
      logger.warn('Failed to notify LXC API client about port change:', error);
    }

    // Log the port change
    logger.info(`LXC REST API server port changed to ${port}`);
  }

  /**
   * Stop the server with enhanced error handling
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      logger.info('LXC REST API server is not running');
      return;
    }

    try {
      // Use retry for stopping the server
      await retry(
        async () => {
          await this.server.stop();
          this.isRunning = false;
          logger.info('LXC REST API server stopped successfully');
        },
        {
          maxAttempts: 2,
          initialDelayMs: 500,
          maxDelayMs: 2000,
          backoffFactor: 2,
          jitter: true,
          context: 'LXC REST API server shutdown',
          onRetry: (error, attempt, delay) => {
            logger.warn(`Retrying LXC REST API server shutdown (attempt ${attempt}/2) after ${delay}ms due to error: ${error.message}`);
          }
        }
      );
    } catch (error) {
      logger.error('Failed to stop LXC REST API server after all retry attempts', error);

      // Convert to ContainerizationError if not already
      if (!(error instanceof ContainerizationError)) {
        throw new ContainerizationError('Failed to stop LXC REST API server', {
          cause: error,
          details: { options: this.options }
        });
      }

      throw error;
    }
  }

  /**
   * Check if the server is running
   */
  public isServerRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Get the server options
   */
  public getOptions(): LxcRestServiceOptions {
    return { ...this.options };
  }

  /**
   * Get the server URL
   */
  public getUrl(): string {
    // Get the actual port from the server if it's running
    const port = this.server && this.isRunning ?
      (this.server as any).address()?.port || this.options.port :
      this.options.port;

    return `http://${this.options.host}:${port}`;
  }

  /**
   * Get the API URL
   */
  public getApiUrl(): string {
    return `${this.getUrl()}/api/v1`;
  }

  /**
   * Update service options with enhanced error handling
   */
  public async updateOptions(options: Partial<LxcRestServiceOptions>): Promise<void> {
    // Update the options
    Object.assign(this.options, options);

    // Restart the server if needed
    if (this.isRunning && (options.port || options.host || options.lxcPath || options.configPath)) {
      try {
        logger.info('Restarting LXC REST API server due to configuration changes');

        // Stop the server
        await this.stop();

        // Start the server with retry
        await this.start();

        logger.info('LXC REST API server restarted successfully after configuration changes');
      } catch (error) {
        logger.error('Failed to restart LXC REST API server after options update', error);

        // Try to ensure the server is in a known state
        if (this.isRunning) {
          logger.warn('Attempting to stop LXC REST API server after failed restart');
          try {
            await this.stop();
          } catch (stopError) {
            logger.error('Failed to stop LXC REST API server after failed restart', stopError);
          }
        }

        // Convert to ContainerizationError if not already
        if (!(error instanceof ContainerizationError)) {
          throw new ContainerizationError('Failed to restart LXC REST API server', {
            cause: error,
            details: { options: this.options }
          });
        }

        throw error;
      }
    }
  }
}
