/**
 * LXC Command
 * 
 * This file re-exports the EnhancedLxcCommand as LxcCommand for backward compatibility.
 */

import { EnhancedLxcCommand, LxcCommandOptions } from './enhanced-lxc-command';

/**
 * LXC command class
 * 
 * @deprecated Use EnhancedLxcCommand instead
 */
export class LxcCommand extends EnhancedLxcCommand {
  constructor(options: any = {}) {
    super(options);
    console.warn('LxcCommand is deprecated. Use EnhancedLxcCommand instead.');
  }
}

// Re-export types
export { LxcCommandOptions };
