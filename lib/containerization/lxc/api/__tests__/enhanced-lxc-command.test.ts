/**
 * Unit tests for the EnhancedLxcCommand class
 */

import { EnhancedLxcCommand } from '../enhanced-lxc-command';
import { ResourceCleanupRegistry } from '../../../shared/resource-management';
import { MockCommandExecutor } from '../../../shared/test-utils';
import { ContainerizationError, PermissionError, TimeoutError } from '../../../shared/error-handling';

// Mock dependencies
jest.mock('child_process', () => ({
  exec: jest.fn()
}));

jest.mock('fs/promises', () => ({
  mkdir: jest.fn().mockResolvedValue(undefined),
  access: jest.fn().mockResolvedValue(undefined),
  writeFile: jest.fn().mockResolvedValue(undefined),
  unlink: jest.fn().mockResolvedValue(undefined)
}));

jest.mock('@/lib/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

describe('EnhancedLxcCommand', () => {
  let command: EnhancedLxcCommand;
  let cleanupRegistry: ResourceCleanupRegistry;
  let mockExecutor: MockCommandExecutor;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create a new cleanup registry for each test
    cleanupRegistry = new ResourceCleanupRegistry();
    
    // Create a new mock executor
    mockExecutor = new MockCommandExecutor();
    
    // Create a new command instance
    command = new EnhancedLxcCommand({
      lxcPath: '/test/lxc',
      configPath: '/test/lxc/config',
      cleanupRegistry
    });
    
    // Mock the exec function
    const { exec } = require('child_process');
    exec.mockImplementation((cmd: string, options: any, callback: any) => {
      if (typeof options === 'function') {
        callback = options;
        options = {};
      }
      
      mockExecutor.execute('lxc', cmd.split(' ').slice(1))
        .then(({ stdout, stderr, exitCode }) => {
          if (exitCode === 0) {
            callback(null, { stdout, stderr });
          } else {
            const error = new Error(`Command failed: ${cmd}\n${stderr}`);
            (error as any).stdout = stdout;
            (error as any).stderr = stderr;
            (error as any).code = 'COMMAND_FAILED';
            callback(error);
          }
        })
        .catch(error => {
          callback(error);
        });
      
      return {
        on: jest.fn()
      };
    });
  });
  
  describe('initialize', () => {
    it('should initialize successfully', async () => {
      // Mock successful version command
      mockExecutor.setCommandResponse('version', {
        stdout: 'lxc version: 4.0.0',
        stderr: ''
      });
      
      await command.initialize();
      
      expect(mockExecutor.wasCommandExecuted('version')).toBe(true);
    });
    
    it('should handle initialization failure', async () => {
      // Mock failed version command
      mockExecutor.setCommandResponse('version', {
        stdout: '',
        stderr: 'Command not found',
        exitCode: 1
      });
      
      await expect(command.initialize()).rejects.toThrow(ContainerizationError);
    });
  });
  
  describe('checkHealth', () => {
    it('should return true when LXC is healthy', async () => {
      // Mock successful version command
      mockExecutor.setCommandResponse('version', {
        stdout: 'lxc version: 4.0.0',
        stderr: ''
      });
      
      const result = await command.checkHealth();
      
      expect(result).toBe(true);
      expect(mockExecutor.wasCommandExecuted('version')).toBe(true);
    });
    
    it('should return false when LXC is not healthy', async () => {
      // Mock failed version command
      mockExecutor.setCommandResponse('version', {
        stdout: '',
        stderr: 'Command not found',
        exitCode: 1
      });
      
      const result = await command.checkHealth();
      
      expect(result).toBe(false);
      expect(mockExecutor.wasCommandExecuted('version')).toBe(true);
    });
  });
  
  describe('execute', () => {
    it('should execute a command successfully', async () => {
      // Mock successful version command
      mockExecutor.setCommandResponse('version', {
        stdout: 'lxc version: 4.0.0',
        stderr: ''
      });
      
      // Mock successful ls command
      mockExecutor.setCommandResponse('ls', {
        stdout: 'container1,RUNNING\ncontainer2,STOPPED',
        stderr: ''
      });
      
      const result = await command.execute(['ls']);
      
      expect(result).toEqual({
        stdout: 'container1,RUNNING\ncontainer2,STOPPED',
        stderr: ''
      });
      expect(mockExecutor.wasCommandExecuted('ls')).toBe(true);
    });
    
    it('should retry on transient failures', async () => {
      // Mock successful version command
      mockExecutor.setCommandResponse('version', {
        stdout: 'lxc version: 4.0.0',
        stderr: ''
      });
      
      // Mock a command that fails once then succeeds
      let attempts = 0;
      mockExecutor.setCommandResponse('ls', {
        get stdout() {
          attempts++;
          if (attempts === 1) {
            throw new Error('Connection reset');
          }
          return 'container1,RUNNING\ncontainer2,STOPPED';
        },
        get stderr() {
          return attempts === 1 ? 'Connection reset' : '';
        },
        get exitCode() {
          return attempts === 1 ? 1 : 0;
        }
      });
      
      const result = await command.execute(['ls'], {
        retry: true,
        retryOptions: {
          maxAttempts: 2,
          initialDelayMs: 10
        }
      });
      
      expect(result).toEqual({
        stdout: 'container1,RUNNING\ncontainer2,STOPPED',
        stderr: ''
      });
      expect(mockExecutor.getCommandExecutionCount('ls')).toBe(2);
    });
    
    it('should throw appropriate error types', async () => {
      // Mock successful version command
      mockExecutor.setCommandResponse('version', {
        stdout: 'lxc version: 4.0.0',
        stderr: ''
      });
      
      // Mock permission denied error
      const execMock = require('child_process').exec;
      execMock.mockImplementationOnce((cmd: string, options: any, callback: any) => {
        const error = new Error('Permission denied');
        (error as any).code = 'EACCES';
        callback(error);
        return { on: jest.fn() };
      });
      
      await expect(command.execute(['ls'], { retry: false }))
        .rejects
        .toThrow(PermissionError);
      
      // Mock timeout error
      execMock.mockImplementationOnce((cmd: string, options: any, callback: any) => {
        const error = new Error('Timeout');
        (error as any).code = 'ETIMEDOUT';
        callback(error);
        return { on: jest.fn() };
      });
      
      await expect(command.execute(['ls'], { retry: false }))
        .rejects
        .toThrow(TimeoutError);
    });
  });
});
