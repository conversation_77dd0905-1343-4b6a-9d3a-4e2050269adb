/**
 * LXC API
 *
 * Provides a wrapper for the LXC REST API.
 */

import {
  ContainerConfig,
  ContainerInfo,
  ContainerState,
  NetworkType
} from '../models/container';
import { TemplateInfo } from '../models/template';
import {
  NetworkInterface,
  NetworkInterfaceConfig,
  BridgeNetwork,
  BridgeNetworkConfig
} from '../models/network';
import { LxcRestClient } from './lxc-rest-client';
import { LxcRestService } from './lxc-rest-service';
import { extractContainerName } from '../utils/container-name';
import { logger } from '@/lib/logger';
import { ResourceCleanupRegistry } from '../../shared/resource-management';
import { ContainerizationError, NotFoundError } from '../../shared/error-handling';

/**
 * LXC API options
 */
export interface LxcApiOptions {
  lxcPath?: string;
  configPath?: string;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  baseUrl?: string;
  apiVersion?: string;
  useRestApi?: boolean;
  cleanupRegistry?: ResourceCleanupRegistry;
}

/**
 * LXC API class
 */
export class LxcApi {
  private readonly client: LxcRestClient;
  private readonly options: LxcApiOptions;
  private readonly restService: LxcRestService;

  constructor(options: LxcApiOptions = {}) {
    this.options = {
      ...options,
      useRestApi: options.useRestApi !== undefined ? options.useRestApi : true
    };

    // Initialize REST API service
    this.restService = LxcRestService.getInstance({
      port: 8080,
      host: 'localhost',
      lxcPath: options.lxcPath,
      configPath: options.configPath,
      logLevel: options.logLevel || 'info',
      autoStart: this.options.useRestApi
    });

    // Initialize REST API client
    this.client = new LxcRestClient({
      baseUrl: options.baseUrl || this.restService.getUrl(),
      apiVersion: options.apiVersion || 'v1',
      lxcPath: options.lxcPath,
      configPath: options.configPath
    });
  }

  /**
   * Update the client URL
   *
   * This method is called when the REST API server port changes
   */
  updateClientUrl(url: string): void {
    logger.info(`Updating LXC API client URL to ${url}`);

    // Update the client options
    this.client.updateOptions({
      baseUrl: url
    });
  }

  /**
   * List containers
   */
  async listContainers(): Promise<string[]> {
    try {
      return await this.client.listContainers();
    } catch (error) {
      logger.error('Error listing containers:', error);
      throw error;
    }
  }

  /**
   * Get container information
   */
  async getContainerInfo(name: string): Promise<ContainerInfo> {
    try {
      const containerName = extractContainerName(name);
      return await this.client.getContainerInfo(containerName);
    } catch (error) {
      logger.error(`Error getting container info for ${name}:`, error);
      throw error;
    }
  }

  /**
   * Create a container
   */
  async createContainer(name: string, config: ContainerConfig): Promise<void> {
    try {
      await this.client.createContainer(name, config);
    } catch (error) {
      logger.error(`Error creating container ${name}:`, error);
      throw error;
    }
  }

  /**
   * Start a container
   */
  async startContainer(name: string): Promise<void> {
    try {
      await this.client.startContainer(name);
    } catch (error) {
      logger.error(`Error starting container ${name}:`, error);
      throw error;
    }
  }

  /**
   * Stop a container
   */
  async stopContainer(name: string, force: boolean = false): Promise<void> {
    try {
      await this.client.stopContainer(name, force);
    } catch (error) {
      logger.error(`Error stopping container ${name}:`, error);
      throw error;
    }
  }

  /**
   * Restart a container
   */
  async restartContainer(name: string): Promise<void> {
    try {
      // Use the client to restart the container
      // First stop, then start
      await this.client.stopContainer(name);
      await this.client.startContainer(name);
      logger.info(`Container ${name} restarted successfully`);
    } catch (error) {
      logger.error(`Error restarting container ${name}:`, error);
      throw error;
    }
  }

  /**
   * Delete a container
   */
  async deleteContainer(name: string): Promise<void> {
    try {
      // First stop the container if it's running
      try {
        const info = await this.client.getContainerInfo(name);
        if (info.state === ContainerState.RUNNING) {
          await this.client.stopContainer(name, true);
        }
      } catch (stopError) {
        logger.warn(`Error stopping container ${name} before deletion:`, stopError);
      }

      // Delete the container
      await this.client.deleteContainer(name);
      logger.info(`Container ${name} deleted successfully`);
    } catch (error) {
      logger.error(`Error deleting container ${name}:`, error);
      throw error;
    }
  }

  /**
   * Freeze a container
   */
  async freezeContainer(name: string): Promise<void> {
    try {
      // Use the client to freeze the container
      // This is a specialized form of stopping
      await this.client.updateContainerState(name, 'freeze');
      logger.info(`Container ${name} frozen successfully`);
    } catch (error) {
      logger.error(`Error freezing container ${name}:`, error);
      throw error;
    }
  }

  /**
   * Unfreeze a container
   */
  async unfreezeContainer(name: string): Promise<void> {
    try {
      // Use the client to unfreeze the container
      // This is a specialized form of starting
      await this.client.updateContainerState(name, 'unfreeze');
      logger.info(`Container ${name} unfrozen successfully`);
    } catch (error) {
      logger.error(`Error unfreezing container ${name}:`, error);
      throw error;
    }
  }

  /**
   * Execute a command in a container
   */
  async executeCommand(name: string, command: string[], options: {
    env?: Record<string, string>,
    cwd?: string,
    user?: string,
    timeout?: number
  } = {}): Promise<{ stdout: string, stderr: string, exitCode: number }> {
    try {
      return await this.client.executeCommand(name, command, options);
    } catch (error: any) {
      logger.error(`Error executing command in container ${name}:`, error);
      return {
        stdout: '',
        stderr: error.message || 'Unknown error',
        exitCode: 1
      };
    }
  }

  /**
   * Get container state
   */
  async getContainerState(name: string): Promise<ContainerState> {
    try {
      const containerName = extractContainerName(name);
      const containerInfo = await this.client.getContainerInfo(containerName);
      return containerInfo.state;
    } catch (error) {
      logger.error(`Error getting container state for ${name}:`, error);
      throw error;
    }
  }

  /**
   * Get container config
   */
  async getContainerConfig(name: string): Promise<ContainerConfig> {
    try {
      const containerName = extractContainerName(name);
      const containerInfo = await this.client.getContainerInfo(containerName);
      return containerInfo.config || {};
    } catch (error) {
      logger.error(`Error getting container config for ${name}:`, error);
      throw error;
    }
  }

  /**
   * Update container config
   */
  async updateContainerConfig(name: string, config: Partial<ContainerConfig>): Promise<void> {
    try {
      const containerName = extractContainerName(name);

      // Get current config
      const currentConfig = await this.getContainerConfig(containerName);

      // Merge configs
      const newConfig = {
        ...currentConfig,
        ...config
      };

      // Update container config via REST API
      await this.client.updateContainerConfig(containerName, newConfig);
    } catch (error) {
      logger.error(`Error updating container config for ${name}:`, error);
      throw error;
    }
  }

  /**
   * List templates
   */
  async listTemplates(): Promise<string[]> {
    try {
      return await this.client.listImages();
    } catch (error) {
      logger.error('Error listing templates:', error);
      throw error;
    }
  }

  /**
   * Get template info
   */
  async getTemplateInfo(name: string): Promise<TemplateInfo> {
    try {
      // The image show command expects a fingerprint or alias, not a CSV line
      // First, parse the name to extract the fingerprint if it's in CSV format
      let imageId = name;

      // Check if the name is in CSV format (contains commas)
      if (name.includes(',')) {
        // Try to extract the fingerprint from the CSV format
        const parts = name.split(',');
        if (parts.length > 1) {
          // The fingerprint is typically the second field in the CSV
          imageId = parts[1].trim();
        }
      }

      return await this.client.getImageInfo(imageId);
    } catch (error) {
      logger.error(`Error getting template info for ${name}:`, error);
      throw error;
    }
  }

  /**
   * Create a network interface for a container
   */
  async createNetworkInterface(containerName: string, config: NetworkInterfaceConfig): Promise<void> {
    try {
      const name = extractContainerName(containerName);

      // Check if container exists
      try {
        await this.getContainerInfo(name);
      } catch (error) {
        throw new NotFoundError(`Container ${name} not found`, { cause: error });
      }

      // Get current container config
      const containerInfo = await this.getContainerInfo(name);
      const containerConfig = containerInfo.config || {};

      // Add network interface to container config
      const devices = containerConfig.devices || {};
      devices[config.name] = {
        type: 'nic',
        nictype: this.mapNetworkType(config.type),
        parent: config.bridge || config.link,
        hwaddr: config.hwaddr,
        mtu: config.mtu,
        ipv4: config.ipv4?.address,
        'ipv4.gateway': config.ipv4?.gateway,
        ipv6: config.ipv6?.address,
        'ipv6.gateway': config.ipv6?.gateway,
      };

      // Update container config
      await this.updateContainerConfig(name, {
        ...containerConfig,
        devices
      });

      logger.info(`Network interface ${config.name} created for container ${name}`);
    } catch (error) {
      logger.error(`Error creating network interface for container ${containerName}:`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to create network interface for container ${containerName}`, {
        cause: error,
        details: { containerName, config }
      });
    }
  }

  /**
   * Delete a network interface from a container
   */
  async deleteNetworkInterface(containerName: string, interfaceName: string): Promise<void> {
    try {
      const name = extractContainerName(containerName);

      // Check if container exists
      try {
        await this.getContainerInfo(name);
      } catch (error) {
        throw new NotFoundError(`Container ${name} not found`, { cause: error });
      }

      // Get current container config
      const containerInfo = await this.getContainerInfo(name);
      const containerConfig = containerInfo.config || {};

      // Remove network interface from container config
      const devices = containerConfig.devices || {};
      if (!devices[interfaceName]) {
        throw new NotFoundError(`Network interface ${interfaceName} not found in container ${name}`);
      }

      delete devices[interfaceName];

      // Update container config
      await this.updateContainerConfig(name, {
        ...containerConfig,
        devices
      });

      logger.info(`Network interface ${interfaceName} deleted from container ${name}`);
    } catch (error) {
      logger.error(`Error deleting network interface ${interfaceName} from container ${containerName}:`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to delete network interface ${interfaceName} from container ${containerName}`, {
        cause: error,
        details: { containerName, interfaceName }
      });
    }
  }

  /**
   * List network interfaces for a container
   */
  async listNetworkInterfaces(containerName: string): Promise<NetworkInterface[]> {
    try {
      const name = extractContainerName(containerName);

      // Check if container exists
      try {
        await this.getContainerInfo(name);
      } catch (error) {
        throw new NotFoundError(`Container ${name} not found`, { cause: error });
      }

      // Get container config
      const containerInfo = await this.getContainerInfo(name);
      const devices = containerInfo.config?.devices || {};

      // Extract network interfaces
      const interfaces: NetworkInterface[] = [];

      for (const [deviceName, device] of Object.entries(devices)) {
        if (device.type === 'nic') {
          interfaces.push({
            name: deviceName,
            type: this.mapNicTypeToNetworkType(device.nictype),
            bridge: device.parent,
            link: device.parent,
            hwaddr: device.hwaddr,
            mtu: device.mtu ? parseInt(device.mtu) : undefined,
            ipv4: device.ipv4 ? { address: device.ipv4, gateway: device['ipv4.gateway'] } : undefined,
            ipv6: device.ipv6 ? { address: device.ipv6, gateway: device['ipv6.gateway'] } : undefined,
            state: 'up' // Assume up if the device exists
          });
        }
      }

      return interfaces;
    } catch (error) {
      logger.error(`Error listing network interfaces for container ${containerName}:`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to list network interfaces for container ${containerName}`, {
        cause: error,
        details: { containerName }
      });
    }
  }

  /**
   * Get network interface information
   */
  async getNetworkInterface(containerName: string, interfaceName: string): Promise<NetworkInterface> {
    try {
      const name = extractContainerName(containerName);

      // Check if container exists
      try {
        await this.getContainerInfo(name);
      } catch (error) {
        throw new NotFoundError(`Container ${name} not found`, { cause: error });
      }

      // Get container config
      const containerInfo = await this.getContainerInfo(name);
      const devices = containerInfo.config?.devices || {};

      // Get network interface
      const device = devices[interfaceName];
      if (!device || device.type !== 'nic') {
        throw new NotFoundError(`Network interface ${interfaceName} not found in container ${name}`);
      }

      return {
        name: interfaceName,
        type: this.mapNicTypeToNetworkType(device.nictype),
        bridge: device.parent,
        link: device.parent,
        hwaddr: device.hwaddr,
        mtu: device.mtu ? parseInt(device.mtu) : undefined,
        ipv4: device.ipv4 ? { address: device.ipv4, gateway: device['ipv4.gateway'] } : undefined,
        ipv6: device.ipv6 ? { address: device.ipv6, gateway: device['ipv6.gateway'] } : undefined,
        state: 'up' // Assume up if the device exists
      };
    } catch (error) {
      logger.error(`Error getting network interface ${interfaceName} for container ${containerName}:`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to get network interface ${interfaceName} for container ${containerName}`, {
        cause: error,
        details: { containerName, interfaceName }
      });
    }
  }

  /**
   * Update network interface configuration
   */
  async updateNetworkInterface(containerName: string, interfaceName: string, config: Partial<NetworkInterfaceConfig>): Promise<void> {
    try {
      const name = extractContainerName(containerName);

      // Check if container exists
      try {
        await this.getContainerInfo(name);
      } catch (error) {
        throw new NotFoundError(`Container ${name} not found`, { cause: error });
      }

      // Get current container config
      const containerInfo = await this.getContainerInfo(name);
      const containerConfig = containerInfo.config || {};
      const devices = containerConfig.devices || {};

      // Check if network interface exists
      if (!devices[interfaceName] || devices[interfaceName].type !== 'nic') {
        throw new NotFoundError(`Network interface ${interfaceName} not found in container ${name}`);
      }

      // Update network interface
      const device = devices[interfaceName];

      if (config.type) {
        device.nictype = this.mapNetworkType(config.type);
      }

      if (config.bridge || config.link) {
        device.parent = config.bridge || config.link;
      }

      if (config.hwaddr) {
        device.hwaddr = config.hwaddr;
      }

      if (config.mtu) {
        device.mtu = config.mtu.toString();
      }

      if (config.ipv4) {
        if (config.ipv4.address) {
          device.ipv4 = config.ipv4.address;
        }

        if (config.ipv4.gateway) {
          device['ipv4.gateway'] = config.ipv4.gateway;
        }
      }

      if (config.ipv6) {
        if (config.ipv6.address) {
          device.ipv6 = config.ipv6.address;
        }

        if (config.ipv6.gateway) {
          device['ipv6.gateway'] = config.ipv6.gateway;
        }
      }

      // Update container config
      await this.updateContainerConfig(name, {
        ...containerConfig,
        devices
      });

      logger.info(`Network interface ${interfaceName} updated for container ${name}`);
    } catch (error) {
      logger.error(`Error updating network interface ${interfaceName} for container ${containerName}:`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to update network interface ${interfaceName} for container ${containerName}`, {
        cause: error,
        details: { containerName, interfaceName, config }
      });
    }
  }

  /**
   * Create a bridge network
   */
  async createBridgeNetwork(name: string, config: Partial<BridgeNetworkConfig> = {}): Promise<void> {
    try {
      // Execute LXC command to create bridge
      await this.client.createBridgeNetwork(name, config);

      logger.info(`Bridge network ${name} created successfully`);
    } catch (error) {
      logger.error(`Error creating bridge network ${name}:`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to create bridge network ${name}`, {
        cause: error,
        details: { name, config }
      });
    }
  }

  /**
   * Delete a bridge network
   */
  async deleteBridgeNetwork(name: string): Promise<void> {
    try {
      // Execute LXC command to delete bridge
      await this.client.deleteBridgeNetwork(name);

      logger.info(`Bridge network ${name} deleted successfully`);
    } catch (error) {
      logger.error(`Error deleting bridge network ${name}:`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to delete bridge network ${name}`, {
        cause: error,
        details: { name }
      });
    }
  }

  /**
   * List bridge networks
   */
  async listBridgeNetworks(): Promise<string[]> {
    try {
      // Execute LXC command to list bridges
      return await this.client.listBridgeNetworks();
    } catch (error) {
      logger.error('Error listing bridge networks:', error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError('Failed to list bridge networks', {
        cause: error
      });
    }
  }

  /**
   * Get bridge network information
   */
  async getBridgeNetwork(name: string): Promise<BridgeNetwork> {
    try {
      // Execute LXC command to get bridge info
      return await this.client.getBridgeNetwork(name);
    } catch (error) {
      logger.error(`Error getting bridge network ${name}:`, error);

      if (error instanceof ContainerizationError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to get bridge network ${name}`, {
        cause: error,
        details: { name }
      });
    }
  }

  /**
   * Map network type to LXC nic type
   */
  private mapNetworkType(type: NetworkType): string {
    switch (type) {
      case NetworkType.PHYSICAL:
        return 'physical';
      case NetworkType.BRIDGED:
        return 'bridged';
      case NetworkType.MACVLAN:
        return 'macvlan';
      case NetworkType.IPVLAN:
        return 'ipvlan';
      case NetworkType.P2P:
        return 'p2p';
      case NetworkType.SRIOV:
        return 'sriov';
      default:
        return 'bridged';
    }
  }

  /**
   * Map LXC nic type to network type
   */
  private mapNicTypeToNetworkType(nicType: string): NetworkType {
    switch (nicType) {
      case 'physical':
        return NetworkType.PHYSICAL;
      case 'bridged':
        return NetworkType.BRIDGED;
      case 'macvlan':
        return NetworkType.MACVLAN;
      case 'ipvlan':
        return NetworkType.IPVLAN;
      case 'p2p':
        return NetworkType.P2P;
      case 'sriov':
        return NetworkType.SRIOV;
      default:
        return NetworkType.BRIDGED;
    }
  }
}

