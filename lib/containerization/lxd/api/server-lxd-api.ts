/**
 * Server-side LXD API
 *
 * This file contains server-side only code for LXD API operations.
 * It should only be imported in server components or API routes.
 */

import * as fs from 'fs';
import * as https from 'https';
import * as path from 'path';
import setupLxdApi from '../utils/setup-lxd-api';

/**
 * Read certificate and key files for LXD API
 */
export function readCertificateFiles(clientCertPath: string, clientKeyPath: string) {
  const cert = fs.existsSync(clientCertPath) ? fs.readFileSync(clientCertPath) : undefined;
  const key = fs.existsSync(clientKeyPath) ? fs.readFileSync(clientKeyPath) : undefined;
  return { cert, key };
}

/**
 * Create HTTPS agent with client certificate
 */
export function createHttpsAgent(verifySSL: boolean, clientCertPath: string, clientKeyPath: string) {
  const { cert, key } = readCertificateFiles(clientCertPath, clientKeyPath);

  return new https.Agent({
    rejectUnauthorized: verifySSL,
    cert,
    key,
  });
}

// Export other server-side only functions that might be needed