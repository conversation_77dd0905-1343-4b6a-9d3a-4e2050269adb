/**
 * LXD Command
 * 
 * This file re-exports the EnhancedLxdCommand class for backward compatibility.
 */

import { EnhancedLxdCommand, LxdCommandOptions } from './enhanced-lxd-command';
import { LxdConfig } from './lxd-config';

/**
 * LXD command class
 * 
 * @deprecated Use EnhancedLxdCommand instead
 */
export class LxdCommand extends EnhancedLxdCommand {
  constructor(config: LxdConfig) {
    super(config);
    console.warn('LxdCommand is deprecated. Use EnhancedLxdCommand instead.');
  }
}

// Re-export types
export { LxdCommandOptions };
