/**
 * Enhanced LXD Command
 * 
 * Provides an enhanced version of the LXD command executor with robust error handling,
 * retry mechanisms, and fallback options.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { logger } from '@/lib/logger';
import { LxdConfig } from './lxd-config';
import { 
  ContainerizationError, 
  ConnectionError,
  PermissionError,
  TimeoutError,
  logError
} from '../../shared/error-handling';
import { retry, RetryOptions } from '../../shared/retry';
import { 
  ResourceCleanupRegistry, 
  globalCleanupRegistry 
} from '../../shared/resource-management';
import { 
  withFallback, 
  createFallbackChain 
} from '../../shared/fallback';
import {
  performHealthCheck,
  createCommandExecutionCheck
} from '../../shared/health-check';

const execAsync = promisify(exec);

/**
 * LXD command options
 */
export interface LxdCommandOptions {
  /** Environment variables */
  env?: Record<string, string>;
  /** Command timeout in milliseconds */
  timeout?: number;
  /** Working directory */
  cwd?: string;
  /** Whether to retry on failure */
  retry?: boolean;
  /** Retry options */
  retryOptions?: Partial<RetryOptions>;
  /** Whether to use socket fallback if REST API fails */
  useSocketFallback?: boolean;
  /** Resource cleanup registry */
  cleanupRegistry?: ResourceCleanupRegistry;
}

/**
 * Default LXD command options
 */
export const DEFAULT_LXD_COMMAND_OPTIONS: LxdCommandOptions = {
  timeout: 60000, // 60 seconds
  retry: true,
  retryOptions: {
    maxAttempts: 3,
    initialDelayMs: 1000,
    maxDelayMs: 10000,
    backoffFactor: 2,
    jitter: true,
  },
  useSocketFallback: true,
  cleanupRegistry: globalCleanupRegistry
};

/**
 * Enhanced LXD command class
 */
export class EnhancedLxdCommand {
  private readonly config: LxdConfig;
  private initialized: boolean = false;
  private initPromise: Promise<void> | null = null;
  private healthCheckPromise: Promise<boolean> | null = null;
  private readonly cleanupRegistry: ResourceCleanupRegistry;
  private readonly socketPath: string = '/var/lib/lxd/unix.socket';
  private readonly restApiUrl: string = 'https://localhost:8443';
  private useRestApi: boolean = true;

  constructor(config: LxdConfig) {
    this.config = config;
    this.cleanupRegistry = globalCleanupRegistry;

    // Initialize the LXD command asynchronously
    this.initialize();
  }

  /**
   * Initialize the LXD command
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._initialize();
    await this.initPromise;
    this.initialized = true;
  }

  /**
   * Internal initialization
   */
  private async _initialize(): Promise<void> {
    try {
      logger.info('Initializing LXD command');

      // Check if REST API is available
      try {
        await this.checkRestApiAvailability();
        this.useRestApi = true;
        logger.info('LXD REST API is available');
      } catch (error) {
        logger.warn('LXD REST API is not available, falling back to socket', error);
        this.useRestApi = false;
        
        // Check if socket is available
        try {
          await this.checkSocketAvailability();
          logger.info('LXD socket is available');
        } catch (socketError) {
          logger.error('LXD socket is not available', socketError);
          throw new ConnectionError('Neither LXD REST API nor socket are available', {
            cause: error,
            details: { socketError }
          });
        }
      }

      // Perform health check
      const isHealthy = await this.checkHealth();
      
      if (!isHealthy) {
        logger.warn('LXD health check failed during initialization');
      }

      logger.info('LXD command initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize LXD command', error);
      throw new ContainerizationError('Failed to initialize LXD command', { cause: error });
    }
  }

  /**
   * Check if LXD REST API is available
   */
  private async checkRestApiAvailability(): Promise<void> {
    try {
      // Try a simple curl command to check if the REST API is available
      await execAsync(`curl -k -s ${this.restApiUrl}/1.0`, { timeout: 5000 });
    } catch (error) {
      throw new ConnectionError('LXD REST API is not available', { cause: error });
    }
  }

  /**
   * Check if LXD socket is available
   */
  private async checkSocketAvailability(): Promise<void> {
    try {
      // Check if socket file exists
      await fs.access(this.socketPath);
    } catch (error) {
      throw new ConnectionError('LXD socket is not available', { cause: error });
    }
  }

  /**
   * Check if LXD is healthy
   */
  async checkHealth(): Promise<boolean> {
    if (this.healthCheckPromise) {
      return this.healthCheckPromise;
    }

    this.healthCheckPromise = (async () => {
      try {
        const healthCheck = createCommandExecutionCheck(
          async () => {
            // Try a simple command to check if LXD is working
            await this.execute(['version'], { retry: false });
            return true;
          },
          'LXD'
        );

        const result = await performHealthCheck(healthCheck);
        return result.healthy;
      } catch (error) {
        logger.error('LXD health check failed', error);
        return false;
      } finally {
        // Clear the promise after a delay to allow for caching
        setTimeout(() => {
          this.healthCheckPromise = null;
        }, 30000); // Cache health check for 30 seconds
      }
    })();

    return this.healthCheckPromise;
  }

  /**
   * Execute an LXD command with robust error handling and retry
   */
  async execute(
    args: string[],
    options: LxdCommandOptions = {}
  ): Promise<{ stdout: string, stderr: string }> {
    // Ensure initialized
    await this.initialize();
    
    const opts = { ...DEFAULT_LXD_COMMAND_OPTIONS, ...options };
    const context = `LXD command: ${args.join(' ')}`;
    
    // Create the execution function
    const executeFn = async () => {
      // Try with REST API first if available
      const restApiExecution = async () => {
        return this.executeWithRestApi(args, opts);
      };
      
      // Use socket as fallback if enabled
      if (!this.useRestApi || opts.useSocketFallback) {
        const socketExecution = async () => {
          logger.info(`Using socket for LXD command: ${args.join(' ')}`);
          return this.executeWithSocket(args, opts);
        };
        
        if (this.useRestApi) {
          // REST API is primary, socket is fallback
          return withFallback(restApiExecution, socketExecution, {
            context,
            retryBeforeFallback: opts.retry,
            retryOptions: opts.retryOptions
          });
        } else {
          // Socket is primary (REST API not available)
          if (opts.retry) {
            return retry(socketExecution, {
              ...opts.retryOptions,
              context
            });
          } else {
            return socketExecution();
          }
        }
      } else {
        // No fallback, just use REST API with retry if enabled
        if (opts.retry) {
          return retry(restApiExecution, {
            ...opts.retryOptions,
            context
          });
        } else {
          return restApiExecution();
        }
      }
    };
    
    try {
      return await executeFn();
    } catch (error) {
      // Convert to appropriate error type
      if (error.code === 'EACCES') {
        throw new PermissionError(`Permission denied executing LXD command: ${args.join(' ')}`, {
          cause: error,
          details: { args, code: error.code }
        });
      } else if (error.code === 'ENOENT') {
        throw new ContainerizationError(`LXD command not found: ${args.join(' ')}`, {
          cause: error,
          details: { args, code: error.code }
        });
      } else if (error.code === 'ETIMEDOUT') {
        throw new TimeoutError(`LXD command timed out: ${args.join(' ')}`, {
          cause: error,
          details: { args, code: error.code, timeout: opts.timeout }
        });
      } else {
        throw new ContainerizationError(`Error executing LXD command: ${args.join(' ')}`, {
          cause: error,
          details: { args, stdout: error.stdout, stderr: error.stderr }
        });
      }
    }
  }

  /**
   * Execute an LXD command using the REST API
   */
  private async executeWithRestApi(
    args: string[],
    options: LxdCommandOptions
  ): Promise<{ stdout: string, stderr: string }> {
    try {
      // Build command with REST API flags
      const apiArgs = [...args, '--url', this.restApiUrl];
      
      // Build command
      const command = this.buildCommand(apiArgs);
      
      // Set environment variables
      const env = {
        ...process.env,
        ...options.env
      };
      
      logger.debug(`Executing LXD command via REST API: ${command}`);
      
      // Execute command
      const { stdout, stderr } = await execAsync(command, {
        env,
        timeout: options.timeout || this.config.getTimeout(),
        cwd: options.cwd
      });
      
      return { stdout, stderr };
    } catch (error: any) {
      logger.error(`Error executing LXD command via REST API: ${error.message}`, {
        command: args.join(' '),
        error
      });
      
      // Include stdout and stderr in the error if available
      if (error.stdout || error.stderr) {
        error.stdout = error.stdout || '';
        error.stderr = error.stderr || '';
      }
      
      throw error;
    }
  }

  /**
   * Execute an LXD command using the socket
   */
  private async executeWithSocket(
    args: string[],
    options: LxdCommandOptions
  ): Promise<{ stdout: string, stderr: string }> {
    try {
      // Build command with socket flags
      const socketArgs = [...args, '--socket', this.socketPath];
      
      // Build command
      const command = this.buildCommand(socketArgs);
      
      // Set environment variables
      const env = {
        ...process.env,
        ...options.env
      };
      
      logger.debug(`Executing LXD command via socket: ${command}`);
      
      // Execute command
      const { stdout, stderr } = await execAsync(command, {
        env,
        timeout: options.timeout || this.config.getTimeout(),
        cwd: options.cwd
      });
      
      return { stdout, stderr };
    } catch (error: any) {
      logger.error(`Error executing LXD command via socket: ${error.message}`, {
        command: args.join(' '),
        error
      });
      
      // Include stdout and stderr in the error if available
      if (error.stdout || error.stderr) {
        error.stdout = error.stdout || '';
        error.stderr = error.stderr || '';
      }
      
      throw error;
    }
  }

  /**
   * Build an LXD command
   */
  private buildCommand(args: string[]): string {
    return `lxc ${args.join(' ')}`;
  }
}
