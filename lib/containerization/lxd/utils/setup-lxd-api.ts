/**
 * Setup LXD API
 *
 * Utility to set up the LXD REST API connection.
 */

import * as fs from 'fs';
import axios, { AxiosError } from 'axios';
import * as https from 'https';
import * as path from 'path';
import { execSync } from 'child_process';
import logger from './logger';
import { generateClientCert } from './generate-client-cert';

/**
 * Setup LXD API options
 */
export interface SetupLxdApiOptions {
  apiUrl: string;
  clientCertPath: string;
  clientKeyPath: string;
  trustPassword?: string;
  verifySSL?: boolean;
  timeout?: number;
  addCertToServer?: boolean;
}

/**
 * Setup LXD API
 */
export async function setupLxdApi(options: SetupLxdApiOptions): Promise<boolean> {
  try {
    const {
      apiUrl,
      clientCertPath,
      clientKeyPath,
      trustPassword,
      verifySSL = false,
      timeout = 30000,
      addCertToServer = false
    } = options;

    // Generate client certificate if it doesn't exist
    const certGenerated = await generateClientCert({
      certPath: clientCertPath,
      keyPath: clientKeyPath,
      force: false
    });

    if (certGenerated) {
      logger.info(`Generated new client certificate at ${clientCertPath}`);
    }

    // Create HTTPS agent with client certificate
    const httpsAgent = new https.Agent({
      rejectUnauthorized: verifySSL,
      cert: fs.readFileSync(clientCertPath),
      key: fs.readFileSync(clientKeyPath),
    });

    // Create axios instance
    const client = axios.create({
      baseURL: apiUrl,
      httpsAgent,
      timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Check if we're already authenticated
    try {
      const response = await client.get('/1.0');
      const auth = response.data.metadata.auth;

      if (auth === 'trusted') {
        logger.info('Already authenticated with LXD API');
        return true;
      } else {
        logger.info('Not authenticated with LXD API, attempting authentication');
      }
    } catch (error) {
      const axiosError = error as AxiosError;

      if (axiosError.response?.status === 403) {
        logger.warn('Authentication required for LXD API');
      } else {
        logger.warn('Failed to check authentication status', error);
      }
    }

    // If addCertToServer is true, try to add the certificate directly to the server
    if (addCertToServer) {
      try {
        logger.info('Adding client certificate directly to LXD server trust store');

        // Extract hostname from API URL
        const url = new URL(apiUrl);
        const hostname = url.hostname;

        // Copy certificate to a temporary file on the server
        const tempCertPath = path.join('/tmp', `lxd-client-${Date.now()}.crt`);
        fs.copyFileSync(clientCertPath, tempCertPath);

        // Add certificate to server trust store
        execSync(`ssh ${hostname} "sudo lxc config trust add ${tempCertPath}"`);

        // Remove temporary file
        fs.unlinkSync(tempCertPath);

        logger.info('Successfully added client certificate to LXD server trust store');
        return true;
      } catch (error) {
        logger.error('Failed to add client certificate to LXD server trust store', error);
      }
    }

    // If trust password is provided, try to authenticate
    if (trustPassword) {
      try {
        logger.info('Authenticating with LXD API using trust password');

        const response = await client.post('/1.0/certificates', {
          type: 'client',
          password: trustPassword
        });

        if (response.status === 200) {
          logger.info('Successfully authenticated with LXD API using trust password');
          return true;
        }
      } catch (error) {
        const axiosError = error as AxiosError;

        if (axiosError.response?.status === 403) {
          logger.error('Authentication failed: Invalid trust password');
        } else {
          logger.error('Failed to authenticate with LXD API', error);
        }

        throw error;
      }
    }

    logger.warn('No authentication method succeeded. The client certificate must be added to the server trust store.');
    logger.warn('You can:');
    logger.warn('1. Set a trust password on the server with: lxc config set core.trust_password YOUR_PASSWORD');
    logger.warn(`2. Add the client certificate directly with: lxc config trust add ${clientCertPath}`);

    return false;
  } catch (error) {
    logger.error('Failed to setup LXD API', error);
    throw error;
  }
}

export default setupLxdApi;
