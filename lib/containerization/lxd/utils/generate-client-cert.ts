/**
 * Generate Client Certificate
 * 
 * Utility to generate client certificates for LXD API authentication.
 */

import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';
import logger from './logger';

/**
 * Generate client certificate options
 */
export interface GenerateClientCertOptions {
  certPath: string;
  keyPath: string;
  commonName?: string;
  organization?: string;
  validDays?: number;
  force?: boolean;
}

/**
 * Generate client certificate
 */
export async function generateClientCert(options: GenerateClientCertOptions): Promise<boolean> {
  try {
    const {
      certPath,
      keyPath,
      commonName = 'LXD Client',
      organization = 'LXD',
      validDays = 3650, // 10 years
      force = false
    } = options;

    // Check if certificate and key already exist
    const certExists = fs.existsSync(certPath);
    const keyExists = fs.existsSync(keyPath);

    if (certExists && keyExists && !force) {
      logger.info('Client certificate and key already exist');
      return false;
    }

    // Create directories if they don't exist
    const certDir = path.dirname(certPath);
    const keyDir = path.dirname(keyPath);

    if (!fs.existsSync(certDir)) {
      fs.mkdirSync(certDir, { recursive: true });
    }

    if (!fs.existsSync(keyDir)) {
      fs.mkdirSync(keyDir, { recursive: true });
    }

    // Generate private key
    logger.info('Generating client private key');
    execSync(`openssl genrsa -out "${keyPath}" 2048`);

    // Generate certificate signing request
    logger.info('Generating certificate signing request');
    const csrPath = `${certPath}.csr`;
    execSync(
      `openssl req -new -key "${keyPath}" -out "${csrPath}" -subj "/CN=${commonName}/O=${organization}" -nodes`
    );

    // Generate self-signed certificate
    logger.info('Generating self-signed certificate');
    execSync(
      `openssl x509 -req -in "${csrPath}" -signkey "${keyPath}" -out "${certPath}" -days ${validDays}`
    );

    // Remove CSR file
    fs.unlinkSync(csrPath);

    // Set permissions
    fs.chmodSync(keyPath, 0o600); // rw-------
    fs.chmodSync(certPath, 0o644); // rw-r--r--

    logger.info(`Client certificate generated: ${certPath}`);
    logger.info(`Client key generated: ${keyPath}`);

    return true;
  } catch (error) {
    logger.error('Failed to generate client certificate', error);
    throw error;
  }
}

export default generateClientCert;
