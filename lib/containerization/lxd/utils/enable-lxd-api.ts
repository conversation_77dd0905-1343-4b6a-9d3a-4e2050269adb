/**
 * Enable LXD API
 * 
 * Utility to enable the LXD REST API if it's not already enabled.
 */

import { execSync } from 'child_process';
import logger from './logger';

/**
 * Enable LXD API options
 */
export interface EnableLxdApiOptions {
  address?: string;
  port?: number;
  trustPassword?: string;
}

/**
 * Enable LXD API
 */
export async function enableLxdApi(options: EnableLxdApiOptions = {}): Promise<boolean> {
  try {
    const {
      address = '[::]',
      port = 8443,
      trustPassword
    } = options;

    // Check if LXD is installed
    try {
      execSync('lxd --version', { stdio: 'ignore' });
    } catch (error) {
      logger.error('LXD is not installed');
      return false;
    }

    // Check if LXD API is already enabled
    try {
      const output = execSync('lxc config get core.https_address').toString().trim();
      
      if (output) {
        logger.info(`LXD API is already enabled at ${output}`);
        return true;
      }
    } catch (error) {
      logger.info('LXD API is not enabled');
    }

    // Enable LXD API
    try {
      const httpsAddress = `${address}:${port}`;
      logger.info(`Enabling LXD API at ${httpsAddress}`);
      
      execSync(`lxc config set core.https_address "${httpsAddress}"`);
      
      // Set trust password if provided
      if (trustPassword) {
        logger.info('Setting LXD trust password');
        execSync(`lxc config set core.trust_password "${trustPassword}"`);
      }
      
      logger.info('LXD API enabled successfully');
      return true;
    } catch (error) {
      logger.error('Failed to enable LXD API', error);
      return false;
    }
  } catch (error) {
    logger.error('Failed to enable LXD API', error);
    return false;
  }
}

export default enableLxdApi;
