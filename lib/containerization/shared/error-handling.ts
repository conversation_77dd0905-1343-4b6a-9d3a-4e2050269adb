/**
 * Shared Error Handling Utilities
 *
 * This module provides shared error handling utilities for containerization libraries.
 */

import { logger } from '@/lib/logger';

/**
 * Base containerization error
 */
export class ContainerizationError extends Error {
  /**
   * Error code
   */
  code?: string;

  /**
   * HTTP status code (if applicable)
   */
  status?: number;

  /**
   * Original error
   */
  cause?: Error;

  /**
   * Additional error details
   */
  details?: Record<string, any>;

  /**
   * Whether this error is retryable
   */
  retryable: boolean;

  /**
   * Create a new ContainerizationError
   */
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
      retryable?: boolean;
    }
  ) {
    super(message);
    this.name = 'ContainerizationError';

    if (options) {
      this.code = options.code;
      this.status = options.status;
      this.details = options.details;
      this.retryable = options.retryable ?? false;

      if (options.cause instanceof Error) {
        this.cause = options.cause;
      } else if (options.cause) {
        this.cause = new Error(String(options.cause));
      }
    } else {
      this.retryable = false;
    }
  }
}

/**
 * Connection error
 */
export class ConnectionError extends ContainerizationError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
      retryable?: boolean;
    }
  ) {
    super(message, { ...options, retryable: options?.retryable ?? true });
    this.name = 'ConnectionError';
  }
}

/**
 * Authentication error
 */
export class AuthenticationError extends ContainerizationError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, { ...options, retryable: false });
    this.name = 'AuthenticationError';
  }
}

/**
 * Resource not found error
 */
export class NotFoundError extends ContainerizationError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, { ...options, retryable: false });
    this.name = 'NotFoundError';
  }
}

/**
 * Permission error
 */
export class PermissionError extends ContainerizationError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, { ...options, retryable: false });
    this.name = 'PermissionError';
  }
}

/**
 * Timeout error
 */
export class TimeoutError extends ContainerizationError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, { ...options, retryable: true });
    this.name = 'TimeoutError';
  }
}

/**
 * Resource constraint error
 */
export class ResourceConstraintError extends ContainerizationError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, { ...options, retryable: false });
    this.name = 'ResourceConstraintError';
  }
}

/**
 * Resource already exists error
 */
export class AlreadyExistsError extends ContainerizationError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, { ...options, retryable: false, status: options?.status || 409 });
    this.name = 'AlreadyExistsError';
  }
}

/**
 * Invalid state error - when a resource is in an invalid state for the requested operation
 */
export class InvalidStateError extends ContainerizationError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, { ...options, retryable: false, status: options?.status || 409 });
    this.name = 'InvalidStateError';
  }
}

/**
 * Resource exhaustion error
 */
export class ResourceExhaustionError extends ContainerizationError {
  constructor(message: string) {
    super(message);
    this.name = 'ResourceExhaustionError';
  }
}

/**
 * Network error
 */
export class NetworkError extends ContainerizationError {
  constructor(message: string, public readonly cause?: Error) {
    super(message, cause);
    this.name = 'NetworkError';
  }
}

/**
 * Determine if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  // If it's our custom error with retryable property
  if (error instanceof ContainerizationError) {
    return error.retryable;
  }

  // Network errors are generally retryable
  if (
    error.code === 'ECONNRESET' ||
    error.code === 'ECONNABORTED' ||
    error.code === 'ETIMEDOUT' ||
    error.code === 'ENOTFOUND' ||
    error.code === 'ENETUNREACH' ||
    error.code === 'EHOSTUNREACH'
  ) {
    return true;
  }

  // HTTP 5xx errors are generally retryable
  if (error.response && error.response.status >= 500) {
    return true;
  }

  // HTTP 429 (Too Many Requests) is retryable
  if (error.response && error.response.status === 429) {
    return true;
  }

  return false;
}

/**
 * Format error for logging
 */
export function formatErrorForLogging(error: any): Record<string, any> {
  const result: Record<string, any> = {
    message: error.message || 'Unknown error',
  };

  if (error.code) {
    result.code = error.code;
  }

  if (error.status) {
    result.status = error.status;
  }

  if (error.details) {
    result.details = error.details;
  }

  if (error.stack) {
    result.stack = error.stack;
  }

  if (error.cause) {
    result.cause = formatErrorForLogging(error.cause);
  }

  return result;
}

/**
 * Log an error with appropriate level and details
 */
export function logError(error: any, context: string): void {
  const formattedError = formatErrorForLogging(error);

  if (error instanceof ConnectionError || isRetryableError(error)) {
    logger.warn(`${context}: ${error.message}`, formattedError);
  } else {
    logger.error(`${context}: ${error.message}`, formattedError);
  }
}
