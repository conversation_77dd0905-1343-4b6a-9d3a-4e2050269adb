/**
 * Retry Utilities
 * 
 * This module provides utilities for retrying operations with exponential backoff.
 */

import { logger } from '@/lib/logger';
import { isRetryableError, logError } from './error-handling';

export interface RetryOptions {
  /** Maximum number of retry attempts */
  maxAttempts: number;
  /** Initial delay in milliseconds */
  initialDelayMs: number;
  /** Maximum delay in milliseconds */
  maxDelayMs: number;
  /** Factor to multiply the delay by after each attempt */
  backoffFactor: number;
  /** Whether to add jitter to the delay */
  jitter: boolean;
  /** Function to determine if an error is retryable */
  isRetryable?: (error: any) => boolean;
  /** Function to call before each retry attempt */
  onRetry?: (error: any, attempt: number, delay: number) => void;
  /** Context for logging */
  context?: string;
}

export const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxAttempts: 3,
  initialDelayMs: 1000,
  maxDelayMs: 10000,
  backoffFactor: 2,
  jitter: true,
  isRetryable: isRetryableError,
  onRetry: (error, attempt, delay, context = 'Operation') => {
    logger.warn(`Retrying ${context} (attempt ${attempt}/${DEFAULT_RETRY_OPTIONS.maxAttempts}) after ${delay}ms due to error: ${error.message}`);
  },
  context: 'Operation'
};

/**
 * Retry a function with exponential backoff
 * @param fn The function to retry
 * @param options Retry options
 * @returns The result of the function
 * @throws The last error encountered if all retries fail
 */
export async function retry<T>(
  fn: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const opts: RetryOptions = { ...DEFAULT_RETRY_OPTIONS, ...options };
  let lastError: any;
  
  for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Check if the error is retryable
      if (opts.isRetryable && !opts.isRetryable(error)) {
        logError(error, opts.context || 'Operation');
        throw error;
      }
      
      // If this was the last attempt, throw the error
      if (attempt === opts.maxAttempts) {
        logError(error, `${opts.context || 'Operation'} failed after ${opts.maxAttempts} attempts`);
        throw error;
      }
      
      // Calculate the delay with exponential backoff
      let delay = Math.min(
        opts.initialDelayMs * Math.pow(opts.backoffFactor, attempt - 1),
        opts.maxDelayMs
      );
      
      // Add jitter if enabled (±20%)
      if (opts.jitter) {
        const jitterFactor = 0.8 + Math.random() * 0.4; // 0.8 to 1.2
        delay = Math.floor(delay * jitterFactor);
      }
      
      // Call the onRetry callback if provided
      if (opts.onRetry) {
        opts.onRetry(error, attempt, delay);
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // This should never happen, but TypeScript requires a return statement
  throw lastError;
}

/**
 * Decorator for adding retry logic to a class method
 * @param options Retry options
 */
export function withRetry(options: Partial<RetryOptions> = {}) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args: any[]) {
      const context = options.context || `${target.constructor.name}.${propertyKey}`;
      const retryOptions = { ...options, context };
      
      return retry(
        () => originalMethod.apply(this, args),
        retryOptions
      );
    };
    
    return descriptor;
  };
}

/**
 * Create a retryable version of a function
 * @param fn The function to make retryable
 * @param options Retry options
 * @returns A retryable version of the function
 */
export function createRetryableFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: Partial<RetryOptions> = {}
): T {
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    return retry(
      () => fn(...args),
      options
    );
  }) as T;
}
