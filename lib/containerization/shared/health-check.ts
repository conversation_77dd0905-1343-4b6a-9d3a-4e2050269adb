/**
 * Health Check Utilities
 * 
 * This module provides utilities for performing health checks on containerization services.
 */

import { logger } from '@/lib/logger';
import { retry, RetryOptions } from './retry';
import { ConnectionError, TimeoutError } from './error-handling';

export interface HealthCheckResult {
  /** Whether the service is healthy */
  healthy: boolean;
  /** Status message */
  message: string;
  /** Detailed information about the health check */
  details?: Record<string, any>;
  /** Timestamp when the health check was performed */
  timestamp: Date;
}

export interface HealthCheckOptions {
  /** Timeout in milliseconds */
  timeoutMs?: number;
  /** Retry options */
  retry?: Partial<RetryOptions>;
}

/**
 * Default health check options
 */
export const DEFAULT_HEALTH_CHECK_OPTIONS: HealthCheckOptions = {
  timeoutMs: 5000,
  retry: {
    maxAttempts: 2,
    initialDelayMs: 500,
    maxDelayMs: 2000,
    backoffFactor: 2,
    jitter: true,
  }
};

/**
 * Perform a health check with timeout and retry
 * @param checkFn The function to perform the health check
 * @param options Health check options
 * @returns Health check result
 */
export async function performHealthCheck(
  checkFn: () => Promise<HealthCheckResult>,
  options: HealthCheckOptions = {}
): Promise<HealthCheckResult> {
  const opts = { ...DEFAULT_HEALTH_CHECK_OPTIONS, ...options };
  
  try {
    // Create a promise that rejects after the timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new TimeoutError(`Health check timed out after ${opts.timeoutMs}ms`));
      }, opts.timeoutMs);
    });
    
    // Create a promise for the health check with retry
    const checkPromise = retry(
      checkFn,
      {
        ...DEFAULT_HEALTH_CHECK_OPTIONS.retry,
        ...opts.retry,
        context: 'Health check'
      }
    );
    
    // Race the promises
    return await Promise.race([checkPromise, timeoutPromise]);
  } catch (error) {
    logger.error('Health check failed', error);
    
    return {
      healthy: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? { error: error.toString() } : { error },
      timestamp: new Date()
    };
  }
}

/**
 * Create a health check function that verifies connectivity to a service
 * @param connectFn Function that attempts to connect to the service
 * @param serviceName Name of the service for logging
 * @returns Health check function
 */
export function createConnectivityCheck(
  connectFn: () => Promise<any>,
  serviceName: string
): () => Promise<HealthCheckResult> {
  return async (): Promise<HealthCheckResult> => {
    try {
      await connectFn();
      
      return {
        healthy: true,
        message: `Successfully connected to ${serviceName}`,
        timestamp: new Date()
      };
    } catch (error) {
      throw new ConnectionError(
        `Failed to connect to ${serviceName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { cause: error, retryable: true }
      );
    }
  };
}

/**
 * Create a health check function that verifies a command can be executed
 * @param executeFn Function that attempts to execute a command
 * @param serviceName Name of the service for logging
 * @returns Health check function
 */
export function createCommandExecutionCheck(
  executeFn: () => Promise<any>,
  serviceName: string
): () => Promise<HealthCheckResult> {
  return async (): Promise<HealthCheckResult> => {
    try {
      const result = await executeFn();
      
      return {
        healthy: true,
        message: `Successfully executed command on ${serviceName}`,
        details: { result },
        timestamp: new Date()
      };
    } catch (error) {
      throw new ConnectionError(
        `Failed to execute command on ${serviceName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { cause: error, retryable: true }
      );
    }
  };
}

/**
 * Create a composite health check that runs multiple checks
 * @param checks Array of health check functions
 * @param serviceName Name of the service for logging
 * @returns Health check function
 */
export function createCompositeCheck(
  checks: Array<() => Promise<HealthCheckResult>>,
  serviceName: string
): () => Promise<HealthCheckResult> {
  return async (): Promise<HealthCheckResult> => {
    const results: HealthCheckResult[] = [];
    let allHealthy = true;
    
    for (const check of checks) {
      try {
        const result = await check();
        results.push(result);
        
        if (!result.healthy) {
          allHealthy = false;
        }
      } catch (error) {
        allHealthy = false;
        results.push({
          healthy: false,
          message: error instanceof Error ? error.message : 'Unknown error',
          details: { error },
          timestamp: new Date()
        });
      }
    }
    
    return {
      healthy: allHealthy,
      message: allHealthy 
        ? `All ${serviceName} health checks passed` 
        : `One or more ${serviceName} health checks failed`,
      details: { results },
      timestamp: new Date()
    };
  };
}
