/**
 * Test Utilities for Containerization Libraries
 * 
 * This module provides utilities for testing containerization libraries.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/lib/logger';
import { ResourceCleanupRegistry } from './resource-management';

const execAsync = promisify(exec);

/**
 * Test container options
 */
export interface TestContainerOptions {
  /** Container name prefix */
  namePrefix?: string;
  /** Container image */
  image?: string;
  /** Whether to clean up the container after the test */
  cleanup?: boolean;
  /** Timeout for container operations in milliseconds */
  timeout?: number;
}

/**
 * Default test container options
 */
export const DEFAULT_TEST_CONTAINER_OPTIONS: TestContainerOptions = {
  namePrefix: 'test-container',
  image: 'alpine:latest',
  cleanup: true,
  timeout: 60000 // 60 seconds
};

/**
 * Create a test container
 * @param options Test container options
 * @param cleanupRegistry Resource cleanup registry
 * @returns Container name
 */
export async function createTestContainer(
  options: TestContainerOptions = {},
  cleanupRegistry: ResourceCleanupRegistry
): Promise<string> {
  const opts = { ...DEFAULT_TEST_CONTAINER_OPTIONS, ...options };
  const containerName = `${opts.namePrefix}-${uuidv4().substring(0, 8)}`;
  
  try {
    logger.info(`Creating test container: ${containerName}`);
    
    // Create the container
    await execAsync(`lxc launch ${opts.image} ${containerName}`, {
      timeout: opts.timeout
    });
    
    // Register cleanup function if cleanup is enabled
    if (opts.cleanup) {
      cleanupRegistry.register(`container-${containerName}`, async () => {
        try {
          logger.info(`Cleaning up test container: ${containerName}`);
          
          // Stop the container if it's running
          try {
            await execAsync(`lxc stop ${containerName} --force`, {
              timeout: 30000
            });
          } catch (stopError) {
            logger.warn(`Error stopping test container ${containerName}:`, stopError);
          }
          
          // Delete the container
          await execAsync(`lxc delete ${containerName}`, {
            timeout: 30000
          });
          
          logger.info(`Test container cleaned up: ${containerName}`);
        } catch (error) {
          logger.error(`Error cleaning up test container ${containerName}:`, error);
        }
      });
    }
    
    logger.info(`Test container created: ${containerName}`);
    return containerName;
  } catch (error) {
    logger.error(`Error creating test container ${containerName}:`, error);
    throw error;
  }
}

/**
 * Wait for a container to be in a specific state
 * @param containerName Container name
 * @param state Expected state
 * @param timeout Timeout in milliseconds
 * @returns Whether the container reached the expected state
 */
export async function waitForContainerState(
  containerName: string,
  state: string,
  timeout: number = 60000
): Promise<boolean> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    try {
      const { stdout } = await execAsync(`lxc list ${containerName} -c s --format csv`);
      const currentState = stdout.trim();
      
      if (currentState.toLowerCase() === state.toLowerCase()) {
        return true;
      }
      
      // Wait before checking again
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      logger.warn(`Error checking container state for ${containerName}:`, error);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return false;
}

/**
 * Create a temporary directory for testing
 * @param prefix Directory name prefix
 * @param cleanupRegistry Resource cleanup registry
 * @returns Path to the temporary directory
 */
export async function createTempDirectory(
  prefix: string = 'containerization-test',
  cleanupRegistry: ResourceCleanupRegistry
): Promise<string> {
  try {
    // Create a temporary directory
    const tempDir = path.join(os.tmpdir(), `${prefix}-${uuidv4().substring(0, 8)}`);
    await fs.mkdir(tempDir, { recursive: true });
    
    // Register cleanup function
    cleanupRegistry.register(`temp-dir-${tempDir}`, async () => {
      try {
        await fs.rm(tempDir, { recursive: true, force: true });
        logger.debug(`Temporary directory cleaned up: ${tempDir}`);
      } catch (error) {
        logger.warn(`Error cleaning up temporary directory ${tempDir}:`, error);
      }
    });
    
    logger.debug(`Temporary directory created: ${tempDir}`);
    return tempDir;
  } catch (error) {
    logger.error('Error creating temporary directory:', error);
    throw error;
  }
}

/**
 * Mock command executor for testing
 */
export class MockCommandExecutor {
  private readonly commandResponses: Map<string, { stdout: string, stderr: string, exitCode: number }> = new Map();
  private readonly commandHistory: Array<{ command: string, args: string[] }> = [];
  private defaultResponse: { stdout: string, stderr: string, exitCode: number } = {
    stdout: '',
    stderr: 'Command not mocked',
    exitCode: 1
  };
  
  /**
   * Set a response for a command
   * @param command Command to match
   * @param response Response to return
   */
  setCommandResponse(
    command: string,
    response: { stdout: string, stderr: string, exitCode?: number }
  ): void {
    this.commandResponses.set(command, {
      ...response,
      exitCode: response.exitCode ?? 0
    });
  }
  
  /**
   * Set a default response for commands that don't have a specific response
   * @param response Default response
   */
  setDefaultResponse(
    response: { stdout: string, stderr: string, exitCode?: number }
  ): void {
    this.defaultResponse = {
      ...response,
      exitCode: response.exitCode ?? 0
    };
  }
  
  /**
   * Execute a command
   * @param command Command to execute
   * @param args Command arguments
   * @returns Command response
   */
  async execute(
    command: string,
    args: string[] = []
  ): Promise<{ stdout: string, stderr: string, exitCode: number }> {
    // Record the command
    this.commandHistory.push({ command, args });
    
    // Build the full command
    const fullCommand = `${command} ${args.join(' ')}`;
    
    // Find a matching command response
    for (const [pattern, response] of this.commandResponses.entries()) {
      if (fullCommand.includes(pattern)) {
        return response;
      }
    }
    
    // Return the default response
    return this.defaultResponse;
  }
  
  /**
   * Get the command history
   */
  getCommandHistory(): Array<{ command: string, args: string[] }> {
    return [...this.commandHistory];
  }
  
  /**
   * Clear the command history
   */
  clearCommandHistory(): void {
    this.commandHistory.length = 0;
  }
  
  /**
   * Check if a command was executed
   * @param command Command to check
   */
  wasCommandExecuted(command: string): boolean {
    return this.commandHistory.some(entry => 
      entry.command === command || 
      `${entry.command} ${entry.args.join(' ')}`.includes(command)
    );
  }
  
  /**
   * Get the number of times a command was executed
   * @param command Command to check
   */
  getCommandExecutionCount(command: string): number {
    return this.commandHistory.filter(entry => 
      entry.command === command || 
      `${entry.command} ${entry.args.join(' ')}`.includes(command)
    ).length;
  }
}
