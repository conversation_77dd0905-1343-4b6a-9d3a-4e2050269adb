/**
 * Resource Management Utilities
 * 
 * This module provides utilities for managing and cleaning up resources.
 */

import { logger } from '@/lib/logger';

/**
 * Resource cleanup function
 */
export type CleanupFunction = () => Promise<void>;

/**
 * Resource cleanup registry
 */
export class ResourceCleanupRegistry {
  private cleanupFunctions: Map<string, CleanupFunction> = new Map();
  private cleanupPromises: Map<string, Promise<void>> = new Map();
  
  /**
   * Register a cleanup function for a resource
   * @param resourceId Unique identifier for the resource
   * @param cleanupFn Function to clean up the resource
   */
  register(resourceId: string, cleanupFn: CleanupFunction): void {
    this.cleanupFunctions.set(resourceId, cleanupFn);
    logger.debug(`Registered cleanup function for resource: ${resourceId}`);
  }
  
  /**
   * Unregister a cleanup function for a resource
   * @param resourceId Unique identifier for the resource
   */
  unregister(resourceId: string): void {
    this.cleanupFunctions.delete(resourceId);
    this.cleanupPromises.delete(resourceId);
    logger.debug(`Unregistered cleanup function for resource: ${resourceId}`);
  }
  
  /**
   * Clean up a specific resource
   * @param resourceId Unique identifier for the resource
   * @returns Promise that resolves when cleanup is complete
   */
  async cleanup(resourceId: string): Promise<void> {
    const cleanupFn = this.cleanupFunctions.get(resourceId);
    
    if (!cleanupFn) {
      logger.warn(`No cleanup function registered for resource: ${resourceId}`);
      return;
    }
    
    // Check if cleanup is already in progress
    if (this.cleanupPromises.has(resourceId)) {
      logger.debug(`Cleanup already in progress for resource: ${resourceId}`);
      return this.cleanupPromises.get(resourceId);
    }
    
    // Execute cleanup
    logger.debug(`Cleaning up resource: ${resourceId}`);
    
    const cleanupPromise = (async () => {
      try {
        await cleanupFn();
        logger.debug(`Successfully cleaned up resource: ${resourceId}`);
      } catch (error) {
        logger.error(`Error cleaning up resource ${resourceId}:`, error);
      } finally {
        this.cleanupFunctions.delete(resourceId);
        this.cleanupPromises.delete(resourceId);
      }
    })();
    
    this.cleanupPromises.set(resourceId, cleanupPromise);
    return cleanupPromise;
  }
  
  /**
   * Clean up all registered resources
   * @returns Promise that resolves when all cleanups are complete
   */
  async cleanupAll(): Promise<void> {
    const resourceIds = Array.from(this.cleanupFunctions.keys());
    
    if (resourceIds.length === 0) {
      logger.debug('No resources to clean up');
      return;
    }
    
    logger.debug(`Cleaning up ${resourceIds.length} resources`);
    
    // Clean up resources in parallel
    await Promise.allSettled(
      resourceIds.map(resourceId => this.cleanup(resourceId))
    );
    
    logger.debug('Finished cleaning up all resources');
  }
  
  /**
   * Get the number of registered resources
   */
  get size(): number {
    return this.cleanupFunctions.size;
  }
  
  /**
   * Check if a resource is registered
   * @param resourceId Unique identifier for the resource
   */
  has(resourceId: string): boolean {
    return this.cleanupFunctions.has(resourceId);
  }
  
  /**
   * Get all registered resource IDs
   */
  getResourceIds(): string[] {
    return Array.from(this.cleanupFunctions.keys());
  }
}

/**
 * Create a resource with automatic cleanup
 * @param createFn Function to create the resource
 * @param cleanupFn Function to clean up the resource
 * @param registry Resource cleanup registry
 * @param resourceId Unique identifier for the resource
 * @returns The created resource
 */
export async function withCleanup<T>(
  createFn: () => Promise<T>,
  cleanupFn: (resource: T) => Promise<void>,
  registry: ResourceCleanupRegistry,
  resourceId: string
): Promise<T> {
  const resource = await createFn();
  
  // Register cleanup function
  registry.register(resourceId, async () => {
    await cleanupFn(resource);
  });
  
  return resource;
}

/**
 * Execute a function with a resource that is automatically cleaned up
 * @param createFn Function to create the resource
 * @param useFn Function that uses the resource
 * @param cleanupFn Function to clean up the resource
 * @returns The result of useFn
 */
export async function useResource<T, R>(
  createFn: () => Promise<T>,
  useFn: (resource: T) => Promise<R>,
  cleanupFn: (resource: T) => Promise<void>
): Promise<R> {
  let resource: T | undefined;
  
  try {
    // Create the resource
    resource = await createFn();
    
    // Use the resource
    return await useFn(resource);
  } finally {
    // Clean up the resource if it was created
    if (resource !== undefined) {
      try {
        await cleanupFn(resource);
      } catch (error) {
        logger.error('Error cleaning up resource:', error);
      }
    }
  }
}

// Create a global resource cleanup registry
export const globalCleanupRegistry = new ResourceCleanupRegistry();

// Register process exit handlers to clean up resources
if (typeof process !== 'undefined') {
  process.on('exit', () => {
    // Synchronous cleanup on exit
    logger.info('Process exiting, cleaning up resources');
  });
  
  process.on('SIGINT', async () => {
    logger.info('Received SIGINT, cleaning up resources');
    await globalCleanupRegistry.cleanupAll();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    logger.info('Received SIGTERM, cleaning up resources');
    await globalCleanupRegistry.cleanupAll();
    process.exit(0);
  });
  
  process.on('uncaughtException', async (error) => {
    logger.error('Uncaught exception:', error);
    await globalCleanupRegistry.cleanupAll();
    process.exit(1);
  });
}
