/**
 * Port Finder Utility
 * 
 * Provides functionality for finding available network ports.
 */

import * as net from 'net';
import { logger } from '@/lib/logger';

/**
 * Check if a port is available
 */
export function isPortAvailable(port: number, host: string = 'localhost'): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.once('error', (err: any) => {
      if (err.code === 'EADDRINUSE' || err.code === 'EACCES') {
        resolve(false);
      } else {
        // For other errors, assume the port is not available
        logger.warn(`Error checking port ${port} availability:`, err);
        resolve(false);
      }
    });
    
    server.once('listening', () => {
      // Close the server and resolve with true (port is available)
      server.close(() => {
        resolve(true);
      });
    });
    
    server.listen(port, host);
  });
}

/**
 * Find an available port starting from the given port
 */
export async function findAvailablePort(startPort: number, host: string = 'localhost', maxAttempts: number = 100): Promise<number> {
  let port = startPort;
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    const available = await isPortAvailable(port, host);
    
    if (available) {
      return port;
    }
    
    // Try the next port
    port++;
    attempts++;
  }
  
  // If we've tried maxAttempts ports and none are available, throw an error
  throw new Error(`Could not find an available port after ${maxAttempts} attempts starting from ${startPort}`);
}

/**
 * Find an available port in a range
 */
export async function findAvailablePortInRange(minPort: number, maxPort: number, host: string = 'localhost'): Promise<number> {
  const maxAttempts = maxPort - minPort + 1;
  return findAvailablePort(minPort, host, maxAttempts);
}
