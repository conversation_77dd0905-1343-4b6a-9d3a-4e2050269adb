/**
 * Fallback Mechanisms
 * 
 * This module provides utilities for implementing fallback mechanisms.
 */

import { logger } from '@/lib/logger';
import { retry, RetryOptions } from './retry';
import { logError } from './error-handling';

/**
 * Fallback options
 */
export interface FallbackOptions {
  /** Context for logging */
  context?: string;
  /** Whether to retry the primary function before falling back */
  retryBeforeFallback?: boolean;
  /** Retry options for the primary function */
  retryOptions?: Partial<RetryOptions>;
  /** Whether to retry the fallback function */
  retryFallback?: boolean;
  /** Retry options for the fallback function */
  fallbackRetryOptions?: Partial<RetryOptions>;
}

/**
 * Default fallback options
 */
export const DEFAULT_FALLBACK_OPTIONS: FallbackOptions = {
  retryBeforeFallback: true,
  retryOptions: {
    maxAttempts: 2,
    initialDelayMs: 500,
    maxDelayMs: 2000,
    backoffFactor: 2,
    jitter: true,
  },
  retryFallback: true,
  fallbackRetryOptions: {
    maxAttempts: 2,
    initialDelayMs: 500,
    maxDelayMs: 2000,
    backoffFactor: 2,
    jitter: true,
  }
};

/**
 * Execute a function with fallback
 * @param primaryFn Primary function to execute
 * @param fallbackFn Fallback function to execute if primary fails
 * @param options Fallback options
 * @returns Result of either the primary or fallback function
 */
export async function withFallback<T>(
  primaryFn: () => Promise<T>,
  fallbackFn: () => Promise<T>,
  options: FallbackOptions = {}
): Promise<T> {
  const opts = { ...DEFAULT_FALLBACK_OPTIONS, ...options };
  const context = opts.context || 'Operation';
  
  try {
    // Try the primary function with retry if enabled
    if (opts.retryBeforeFallback) {
      return await retry(
        primaryFn,
        {
          ...DEFAULT_FALLBACK_OPTIONS.retryOptions,
          ...opts.retryOptions,
          context: `${context} (primary)`
        }
      );
    } else {
      return await primaryFn();
    }
  } catch (primaryError) {
    // Log the primary error
    logError(primaryError, `${context} (primary) failed, falling back`);
    
    try {
      // Try the fallback function with retry if enabled
      if (opts.retryFallback) {
        return await retry(
          fallbackFn,
          {
            ...DEFAULT_FALLBACK_OPTIONS.fallbackRetryOptions,
            ...opts.fallbackRetryOptions,
            context: `${context} (fallback)`
          }
        );
      } else {
        return await fallbackFn();
      }
    } catch (fallbackError) {
      // Log the fallback error
      logError(fallbackError, `${context} (fallback) failed`);
      
      // Throw an error with both primary and fallback errors
      const error = new Error(`${context} failed: primary and fallback both failed`);
      (error as any).primaryError = primaryError;
      (error as any).fallbackError = fallbackError;
      throw error;
    }
  }
}

/**
 * Create a function that tries multiple implementations in sequence
 * @param fns Array of functions to try
 * @param options Fallback options
 * @returns Function that tries each implementation until one succeeds
 */
export function createFallbackChain<T>(
  fns: Array<() => Promise<T>>,
  options: FallbackOptions = {}
): () => Promise<T> {
  return async (): Promise<T> => {
    const opts = { ...DEFAULT_FALLBACK_OPTIONS, ...options };
    const context = opts.context || 'Operation';
    
    const errors: any[] = [];
    
    for (let i = 0; i < fns.length; i++) {
      const fn = fns[i];
      const isLast = i === fns.length - 1;
      const fnContext = `${context} (implementation ${i + 1}/${fns.length})`;
      
      try {
        // Try the function with retry if enabled
        if (i === 0 && opts.retryBeforeFallback) {
          // Primary function with primary retry options
          return await retry(
            fn,
            {
              ...DEFAULT_FALLBACK_OPTIONS.retryOptions,
              ...opts.retryOptions,
              context: fnContext
            }
          );
        } else if (i > 0 && opts.retryFallback) {
          // Fallback function with fallback retry options
          return await retry(
            fn,
            {
              ...DEFAULT_FALLBACK_OPTIONS.fallbackRetryOptions,
              ...opts.fallbackRetryOptions,
              context: fnContext
            }
          );
        } else {
          // No retry
          return await fn();
        }
      } catch (error) {
        // Log the error
        logError(error, `${fnContext} failed${isLast ? '' : ', trying next implementation'}`);
        
        // Store the error
        errors.push(error);
        
        // If this is the last function, throw an error with all errors
        if (isLast) {
          const finalError = new Error(`${context} failed: all implementations failed`);
          (finalError as any).errors = errors;
          throw finalError;
        }
      }
    }
    
    // This should never happen, but TypeScript requires a return statement
    throw new Error(`${context} failed: no implementations available`);
  };
}

/**
 * Create a circuit breaker
 * @param fn Function to execute
 * @param options Circuit breaker options
 * @returns Function with circuit breaker
 */
export function createCircuitBreaker<T>(
  fn: () => Promise<T>,
  options: {
    maxFailures: number;
    resetTimeoutMs: number;
    fallbackFn?: () => Promise<T>;
    context?: string;
  }
): () => Promise<T> {
  const opts = {
    ...options,
    maxFailures: 5,
    resetTimeoutMs: 30000,
    context: 'Operation',
  };
  
  let failures = 0;
  let lastFailureTime = 0;
  let circuitOpen = false;
  
  return async (): Promise<T> => {
    const now = Date.now();
    
    // Check if circuit should be reset
    if (circuitOpen && now - lastFailureTime > opts.resetTimeoutMs) {
      logger.info(`Circuit breaker for ${opts.context} reset after ${opts.resetTimeoutMs}ms`);
      circuitOpen = false;
      failures = 0;
    }
    
    // If circuit is open, use fallback or throw error
    if (circuitOpen) {
      logger.warn(`Circuit breaker for ${opts.context} is open, skipping primary function`);
      
      if (opts.fallbackFn) {
        return opts.fallbackFn();
      } else {
        throw new Error(`Circuit breaker for ${opts.context} is open`);
      }
    }
    
    try {
      // Try the function
      const result = await fn();
      
      // Reset failures on success
      failures = 0;
      
      return result;
    } catch (error) {
      // Increment failures
      failures++;
      lastFailureTime = now;
      
      // Check if circuit should be opened
      if (failures >= opts.maxFailures) {
        circuitOpen = true;
        logger.error(`Circuit breaker for ${opts.context} opened after ${failures} failures`);
      }
      
      // Use fallback or throw error
      if (opts.fallbackFn) {
        logger.warn(`Using fallback for ${opts.context} after error`);
        return opts.fallbackFn();
      } else {
        throw error;
      }
    }
  };
}
