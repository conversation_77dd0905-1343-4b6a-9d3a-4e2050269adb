/**
 * Overlay Manager
 *
 * This module provides functionality for creating and managing overlay filesystems
 * for MicroVMs.
 */

import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';
import { ContainerizationError } from '../../shared/error-handling';

// Simple console-based logger implementation
const logger = {
  info: (...args: any[]) => console.log('[INFO]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
  debug: (...args: any[]) => console.log('[DEBUG]', ...args)
};

/**
 * Options for creating an overlay disk
 */
export interface OverlayDiskOptions {
  /**
   * Path to the base image
   */
  baseImagePath: string;

  /**
   * Path to the overlay directory
   */
  overlayDir: string;

  /**
   * Path to the merged directory
   */
  mergedDir: string;

  /**
   * Path to the work directory
   */
  workDir: string;

  /**
   * Path to the output disk
   */
  outputPath: string;
}

  /**
 * Result of creating an overlay disk
 */
export interface OverlayDiskResult {
  /**
   * Path to the output disk
   */
  outputPath: string;

  /**
   * Whether the disk is read-only
   */
  readOnly: boolean;

  /**
   * Method used to create the disk
   */
  method: 'overlay' | 'copy' | 'direct';
}

/**
 * Overlay manager
 */
export class OverlayManager {
  private readonly baseDir: string;

  /**
   * Create a new overlay manager
   * @param options Options
   */
  constructor(options: { baseDir: string }) {
    this.baseDir = options.baseDir;

    // Create the base directory if it doesn't exist
    fs.mkdirSync(this.baseDir, { recursive: true });
  }

  /**
   * Create an overlay disk
   * @param options Options
   * @returns Result
   */
  async createOverlayDisk(options: OverlayDiskOptions): Promise<OverlayDiskResult> {
    // Create the overlay directory structure if needed
    const directoriesToCreate = [
      options.overlayDir,
      options.mergedDir,
      options.workDir,
      path.dirname(options.outputPath),
    ];

    for (const dir of directoriesToCreate) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }

    try {
      // Try the overlay approach first
      return await this.createOverlayDiskWithOverlayFS(options);
    } catch (error) {
      logger.warn(`Failed to create overlay disk with OverlayFS: ${error}`);
      
      // If overlay fails, try copy approach
      try {
        logger.info(`Falling back to copy-based overlay for ${options.baseImagePath}`);
        return await this.createOverlayDiskWithCopy(options);
      } catch (copyError) {
        logger.warn(`Failed to create overlay disk with copy: ${copyError}`);
        
        // If all else fails, just use the base image directly
        logger.info(`Falling back to direct base image use for ${options.baseImagePath}`);
        return {
          outputPath: options.baseImagePath,
          readOnly: true,
          method: 'direct'
        };
      }
    }
  }

  /**
   * Create an overlay disk using OverlayFS
   * @param options Options
   * @returns Result
   */
  private async createOverlayDiskWithOverlayFS(options: OverlayDiskOptions): Promise<OverlayDiskResult> {
    // Check if we're running as root or have sudo privileges
    const hasRootOrSudo = this.checkForRootOrSudo();
    
    if (!hasRootOrSudo) {
      throw new ContainerizationError('Creating OverlayFS requires root or sudo privileges');
    }

    // Create a mount point for the base image
    const baseDir = path.join(options.mergedDir, 'base');
    if (!fs.existsSync(baseDir)) {
      fs.mkdirSync(baseDir, { recursive: true });
    }

    try {
      // Mount the base image
      logger.debug(`Mounting base image ${options.baseImagePath} to ${baseDir}`);
      
      try {
        execSync(`mount -o loop,ro ${options.baseImagePath} ${baseDir}`, {
          stdio: 'pipe',
        });
      } catch (error) {
        logger.error(`Failed to setup OverlayFS: ${error}`);
        throw new ContainerizationError(`Failed to setup OverlayFS: ${error}`);
      }

      // Create the overlay
      logger.debug(`Creating overlay with merged dir ${options.mergedDir}`);
      
      try {
        execSync(
          `mount -t overlay overlay -o lowerdir=${baseDir},upperdir=${options.overlayDir},workdir=${options.workDir} ${options.mergedDir}`,
          { stdio: 'pipe' }
        );
      } catch (error) {
        // Unmount the base image if we fail to create the overlay
        try {
          execSync(`umount ${baseDir}`, { stdio: 'pipe' });
        } catch (unmountError) {
          logger.warn(`Failed to unmount base image: ${unmountError}`);
        }
        
        logger.error(`Failed to create overlay: ${error}`);
        throw new ContainerizationError(`Failed to create overlay: ${error}`);
      }

      // Create a disk image from the overlay
      logger.debug(`Creating disk image from overlay at ${options.outputPath}`);
      
      try {
        // Use a filesystem-agnostic approach
        const sizeMB = 2048; // 2GB disk image
        execSync(`dd if=/dev/zero of=${options.outputPath} bs=1M count=${sizeMB}`, { 
          stdio: 'pipe' 
        });
        
        execSync(`mkfs.ext4 -F ${options.outputPath}`, { 
          stdio: 'pipe' 
        });
      
        // Create a temporary mount point for the new disk
        const tempMountPoint = path.join(path.dirname(options.outputPath), 'temp_mount');
        if (!fs.existsSync(tempMountPoint)) {
          fs.mkdirSync(tempMountPoint, { recursive: true });
        }
        
        // Mount the new disk
        execSync(`mount -o loop ${options.outputPath} ${tempMountPoint}`, {
          stdio: 'pipe',
        });
        
        // Copy the overlay to the new disk
        execSync(`cp -a ${options.mergedDir}/. ${tempMountPoint}/`, {
          stdio: 'pipe',
        });
        
        // Unmount the new disk
        execSync(`umount ${tempMountPoint}`, {
          stdio: 'pipe',
        });
        
        // Clean up the temporary mount point
        fs.rmdirSync(tempMountPoint);
      } catch (error) {
        logger.error(`Failed to create disk image from overlay: ${error}`);
        throw new ContainerizationError(`Failed to create disk image from overlay: ${error}`);
      } finally {
        // Unmount the overlay and base image
        try {
          execSync(`umount ${options.mergedDir}`, { stdio: 'pipe' });
        } catch (unmountError) {
          logger.warn(`Failed to unmount overlay: ${unmountError}`);
        }
        
        try {
          execSync(`umount ${baseDir}`, { stdio: 'pipe' });
        } catch (unmountError) {
          logger.warn(`Failed to unmount base image: ${unmountError}`);
        }
      }

      return {
        outputPath: options.outputPath,
        readOnly: false,
        method: 'overlay'
      };
    } catch (error) {
      // Clean up on error
      throw error;
    }
  }

  /**
   * Create an overlay disk using file copy approach (no mount privileges required)
   * @param options Options
   * @returns Result
   */
  private async createOverlayDiskWithCopy(options: OverlayDiskOptions): Promise<OverlayDiskResult> {
    try {
      // Simply copy the base image to the output path
      logger.debug(`Copying base image ${options.baseImagePath} to ${options.outputPath}`);
      
      // Use native fs methods instead of execSync to avoid permission issues
      fs.copyFileSync(options.baseImagePath, options.outputPath);
      
      // Ensure the copied file is writable
      fs.chmodSync(options.outputPath, 0o644);
      
      return {
        outputPath: options.outputPath,
        readOnly: false,
        method: 'copy'
      };
    } catch (error) {
      logger.error(`Failed to copy base image: ${error}`);
      throw new ContainerizationError(`Failed to copy base image: ${error}`);
    }
  }

  /**
   * Check if the current process has root or sudo privileges
   */
  private checkForRootOrSudo(): boolean {
    try {
      // Check if we're running as root
      if (process.getuid && process.getuid() === 0) {
        return true;
      }
      
      // Try to run a simple sudo command
      execSync('sudo -n true', { stdio: 'ignore' });
      return true;
    } catch (error) {
      return false;
    }
  }
}
