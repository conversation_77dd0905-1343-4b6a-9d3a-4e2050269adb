/**
 * OverlayManager Tests
 * 
 * This file contains tests for the OverlayManager class.
 */

import { OverlayManager } from '../overlay-manager';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { ContainerizationError } from '../../../shared/error-handling';

const execAsync = promisify(exec);

// Mock dependencies
jest.mock('fs');
jest.mock('child_process');
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('OverlayManager', () => {
  let overlayManager: OverlayManager;
  const testBaseDir = '/tmp/test-overlays';
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock fs.mkdirSync
    (fs.mkdirSync as jest.Mock).mockImplementation(() => {});
    
    // Mock fs.existsSync
    (fs.existsSync as jest.Mock).mockImplementation((path: string) => {
      // Return true for base image path
      if (path.includes('base-image.ext4')) {
        return true;
      }
      return false;
    });
    
    // Mock fs.statSync
    (fs.statSync as jest.Mock).mockImplementation(() => ({
      size: 1024 * 1024 * 1024, // 1GB
    }));
    
    // Mock execAsync
    (execAsync as jest.Mock).mockResolvedValue({ stdout: '1024', stderr: '' });
    
    // Create overlay manager
    overlayManager = new OverlayManager({
      baseDir: testBaseDir,
    });
  });
  
  describe('createOverlayDisk', () => {
    it('should create an overlay disk successfully', async () => {
      // Arrange
      const config = {
        baseImagePath: '/path/to/base-image.ext4',
        overlayDir: path.join(testBaseDir, 'test-overlay'),
        mergedDir: path.join(testBaseDir, 'test-merged'),
        workDir: path.join(testBaseDir, 'test-work'),
        outputPath: path.join(testBaseDir, 'test-disk.ext4'),
      };
      
      // Act
      const result = await overlayManager.createOverlayDisk(config);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.baseImagePath).toBe(config.baseImagePath);
      expect(result.overlayDir).toBe(config.overlayDir);
      expect(result.mergedDir).toBe(config.mergedDir);
      expect(result.workDir).toBe(config.workDir);
      expect(result.outputPath).toBe(config.outputPath);
      expect(result.fsType).toBe('ext4');
      expect(result.sizeBytes).toBe(1024 * 1024 * 1024);
      expect(result.createdAt).toBeInstanceOf(Date);
      
      // Verify mkdir calls
      expect(fs.mkdirSync).toHaveBeenCalledWith(config.overlayDir, { recursive: true });
      expect(fs.mkdirSync).toHaveBeenCalledWith(config.mergedDir, { recursive: true });
      expect(fs.mkdirSync).toHaveBeenCalledWith(config.workDir, { recursive: true });
      
      // Verify exec calls
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('mount -o loop,ro'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('mount -t overlay'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('dd if=/dev/zero'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('mkfs.ext4'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('mount -o loop'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('cp -a'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('umount'));
    });
    
    it('should throw an error if base image does not exist', async () => {
      // Arrange
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      
      const config = {
        baseImagePath: '/path/to/nonexistent-image.ext4',
        overlayDir: path.join(testBaseDir, 'test-overlay'),
        mergedDir: path.join(testBaseDir, 'test-merged'),
        workDir: path.join(testBaseDir, 'test-work'),
        outputPath: path.join(testBaseDir, 'test-disk.ext4'),
      };
      
      // Act & Assert
      await expect(overlayManager.createOverlayDisk(config)).rejects.toThrow(ContainerizationError);
      await expect(overlayManager.createOverlayDisk(config)).rejects.toThrow('Base image not found');
    });
    
    it('should throw an error if overlay setup fails', async () => {
      // Arrange
      (execAsync as jest.Mock).mockRejectedValueOnce(new Error('Mount failed'));
      
      const config = {
        baseImagePath: '/path/to/base-image.ext4',
        overlayDir: path.join(testBaseDir, 'test-overlay'),
        mergedDir: path.join(testBaseDir, 'test-merged'),
        workDir: path.join(testBaseDir, 'test-work'),
        outputPath: path.join(testBaseDir, 'test-disk.ext4'),
      };
      
      // Act & Assert
      await expect(overlayManager.createOverlayDisk(config)).rejects.toThrow(ContainerizationError);
      await expect(overlayManager.createOverlayDisk(config)).rejects.toThrow('Failed to setup OverlayFS');
    });
    
    it('should throw an error if disk creation fails', async () => {
      // Arrange
      (execAsync as jest.Mock)
        .mockResolvedValueOnce({ stdout: '', stderr: '' }) // First call (mount base)
        .mockResolvedValueOnce({ stdout: '', stderr: '' }) // Second call (mount overlay)
        .mockRejectedValueOnce(new Error('Disk creation failed')); // Third call (dd)
      
      const config = {
        baseImagePath: '/path/to/base-image.ext4',
        overlayDir: path.join(testBaseDir, 'test-overlay'),
        mergedDir: path.join(testBaseDir, 'test-merged'),
        workDir: path.join(testBaseDir, 'test-work'),
        outputPath: path.join(testBaseDir, 'test-disk.ext4'),
      };
      
      // Act & Assert
      await expect(overlayManager.createOverlayDisk(config)).rejects.toThrow(ContainerizationError);
      await expect(overlayManager.createOverlayDisk(config)).rejects.toThrow('Failed to create disk image');
    });
  });
  
  describe('calculateDirectorySize', () => {
    it('should calculate directory size correctly', async () => {
      // Arrange
      (execAsync as jest.Mock).mockResolvedValue({ stdout: '1048576', stderr: '' });
      
      // Act
      const result = await (overlayManager as any).calculateDirectorySize('/path/to/dir');
      
      // Assert
      expect(result).toBe(1048576);
      expect(execAsync).toHaveBeenCalledWith('du -sb /path/to/dir | cut -f1');
    });
    
    it('should throw an error if calculation fails', async () => {
      // Arrange
      (execAsync as jest.Mock).mockRejectedValue(new Error('Command failed'));
      
      // Act & Assert
      await expect((overlayManager as any).calculateDirectorySize('/path/to/dir')).rejects.toThrow(ContainerizationError);
      await expect((overlayManager as any).calculateDirectorySize('/path/to/dir')).rejects.toThrow('Failed to calculate directory size');
    });
  });
});
