/**
 * MicroVM Tests
 * 
 * This module contains tests for the MicroVM class.
 */

import { MicroVm } from '../core/microvm';
import { FirecrackerClient } from '../core/firecracker-client';
import { MicroVmState } from '../models';
import { ContainerizationError, ResourceExhaustionError } from '../../shared/error-handling';
import { jest } from '@jest/globals';

describe('MicroVM', () => {
  let microvm: MicroVm;
  let mockClient: jest.Mocked<FirecrackerClient>;

  beforeEach(() => {
    mockClient = {
      configureVm: jest.fn(),
      configureBootSource: jest.fn(),
      addDrive: jest.fn(),
      addNetworkInterface: jest.fn(),
      updateMetadata: jest.fn(),
      startVm: jest.fn(),
      stopVm: jest.fn(),
      getVmInfo: jest.fn(),
      getSystemInfo: jest.fn(),
    } as any;

    microvm = new MicroVm({
      id: 'test-vm',
      name: 'Test VM',
      vmDir: '/tmp/test-vm',
      config: {
        resources: {
          vcpuCount: 2,
          memSizeMib: 512,
        },
        kernel: {
          path: '/path/to/kernel',
        },
        drives: [
          {
            id: 'rootfs',
            path: '/path/to/rootfs',
            isRootfs: true,
            size: 1024 * 1024 * 1024, // 1GB
          },
        ],
        metadata: {},
      },
      client: mockClient,
      command: {
        stop: jest.fn(),
      },
    });
  });

  describe('configure', () => {
    it('should configure the VM successfully', async () => {
      await microvm.configure();

      expect(mockClient.configureVm).toHaveBeenCalledWith({
        vcpu_count: 2,
        mem_size_mib: 512,
      });

      expect(mockClient.configureBootSource).toHaveBeenCalledWith(
        '/path/to/kernel',
        undefined
      );

      expect(mockClient.addDrive).toHaveBeenCalledWith('rootfs', {
        drive_id: 'rootfs',
        path_on_host: '/path/to/rootfs',
        is_root_device: true,
        is_read_only: false,
      });

      expect(mockClient.updateMetadata).toHaveBeenCalledWith({
        id: 'test-vm',
        name: 'Test VM',
        labels: undefined,
        annotations: undefined,
      });
    });

    it('should throw an error if configuration fails', async () => {
      mockClient.configureVm.mockRejectedValueOnce(new Error('Configuration failed'));

      await expect(microvm.configure()).rejects.toThrow(ContainerizationError);
    });
  });

  describe('start', () => {
    beforeEach(() => {
      mockClient.getSystemInfo.mockResolvedValue({
        available_cpus: 4,
        available_memory: 2048,
      });
    });

    it('should start the VM successfully', async () => {
      await microvm.start();

      expect(mockClient.startVm).toHaveBeenCalled();
      expect(microvm.getState()).toBe(MicroVmState.RUNNING);
    });

    it('should throw ResourceExhaustionError if resources are insufficient', async () => {
      mockClient.getSystemInfo.mockResolvedValue({
        available_cpus: 1,
        available_memory: 256,
      });

      await expect(microvm.start()).rejects.toThrow(ResourceExhaustionError);
    });

    it('should throw an error if start fails', async () => {
      mockClient.startVm.mockRejectedValueOnce(new Error('Start failed'));

      await expect(microvm.start()).rejects.toThrow(ContainerizationError);
    });
  });

  describe('stop', () => {
    beforeEach(async () => {
      mockClient.getSystemInfo.mockResolvedValue({
        available_cpus: 4,
        available_memory: 2048,
      });
      await microvm.start();
    });

    it('should stop the VM successfully', async () => {
      await microvm.stop();

      expect(mockClient.stopVm).toHaveBeenCalled();
      expect(microvm.getState()).toBe(MicroVmState.STOPPED);
    });

    it('should throw an error if stop fails', async () => {
      mockClient.stopVm.mockRejectedValueOnce(new Error('Stop failed'));

      await expect(microvm.stop()).rejects.toThrow(ContainerizationError);
    });
  });

  describe('metrics', () => {
    beforeEach(async () => {
      mockClient.getSystemInfo.mockResolvedValue({
        available_cpus: 4,
        available_memory: 2048,
      });
      await microvm.start();
    });

    it('should collect metrics successfully', async () => {
      mockClient.getVmInfo.mockResolvedValue({
        id: 'test-vm',
        name: 'Test VM',
        state: 'running',
        uptime: 10,
        cpu_usage: 50,
        memory_usage: 256 * 1024 * 1024,
        network_rx: 1024,
        network_tx: 2048,
        disk_read: 4096,
        disk_write: 8192,
      });

      const metrics = microvm.getMetrics();
      expect(metrics).toEqual({
        cpuUsage: 50,
        memoryUsage: 256 * 1024 * 1024,
        networkRx: 1024,
        networkTx: 2048,
        diskRead: 4096,
        diskWrite: 8192,
        uptime: 10,
      });
    });
  });
}); 