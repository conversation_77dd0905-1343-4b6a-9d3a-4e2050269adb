/**
 * MicroVM Image Loader
 * 
 * This module provides utilities for loading kernel and rootfs images for MicroVMs
 * from the resources directory or other configured locations.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
// Remove dependency on external logger
// import { logger } from '@/lib/logger';

// Simple console logging wrapper
const logger = {
  info: (...args: any[]) => console.log('[INFO]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
  debug: (...args: any[]) => console.log('[DEBUG]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args)
};

export interface VMImage {
  name: string;
  description: string;
  type: 'kernel' | 'rootfs';
  path: string;
  size: number;
  lastModified: Date;
}

export interface LXCImage {
  name: string; 
  path: string;
  description: string;
  size: number;
  type: 'alpine' | 'ubuntu' | 'other';
  format: 'tar.gz' | 'img' | 'qcow2' | 'other';
}

export interface AvailableImages {
  kernelImages: VMImage[];
  rootfsImages: VMImage[];
  lxcImages: LXCImage[];
}

/**
 * Default paths to check for VM images
 */
const DEFAULT_IMAGE_PATHS = [
  // Project resources directory
  path.join(process.cwd(), 'resources', 'lxc-images'),
  // Project kernels directory
  path.join(process.cwd(), 'resources', 'kernels'),
  // Standard system directories
  '/opt/firecracker',
  '/var/lib/firecracker',
  // User's home directory
  path.join(os.homedir(), '.firecracker'),
];

/**
 * Get all available VM images from all configured paths
 */
export async function getAllAvailableImages(): Promise<AvailableImages> {
  const kernelImages: VMImage[] = [];
  const rootfsImages: VMImage[] = [];
  const lxcImages: LXCImage[] = [];
  
  // Add custom paths from environment variables
  const customPaths: string[] = [];
  if (process.env.MICROVM_IMAGE_PATHS) {
    customPaths.push(...process.env.MICROVM_IMAGE_PATHS.split(','));
  }
  
  // Combine default and custom paths
  const searchPaths = [...DEFAULT_IMAGE_PATHS, ...customPaths];
  
  logger.info(`Searching for VM images in: ${searchPaths.join(', ')}`);
  
  // Search all directories
  for (const dirPath of searchPaths) {
    try {
      if (!fs.existsSync(dirPath)) {
        logger.debug(`Directory does not exist: ${dirPath}`);
        continue;
      }
      
      const files = fs.readdirSync(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (!stats.isFile()) continue;
        
        // Skip index files and non-image files
        if (file === 'index.ts' || file === 'index.js') continue;
        
        // Kernel image detection
        if (file.includes('vmlinux') || file.includes('kernel') || file.endsWith('.bin')) {
          kernelImages.push({
            name: file,
            description: `Kernel image found in ${dirPath}`,
            type: 'kernel',
            path: filePath,
            size: stats.size,
            lastModified: stats.mtime
          });
        }
        
        // Rootfs image detection
        if (file.endsWith('.ext4') || file.endsWith('.img') || file.includes('rootfs')) {
          rootfsImages.push({
            name: file,
            description: `Root filesystem image found in ${dirPath}`,
            type: 'rootfs',
            path: filePath,
            size: stats.size,
            lastModified: stats.mtime
          });
        }
        
        // LXC image detection (in the lxc-images directory)
        if (dirPath.includes('lxc-images')) {
          // Determine image type
          let type: 'alpine' | 'ubuntu' | 'other' = 'other';
          if (file.toLowerCase().includes('alpine')) type = 'alpine';
          if (file.toLowerCase().includes('ubuntu') || file.toLowerCase().includes('jammy')) type = 'ubuntu';
          
          // Determine format
          let format: 'tar.gz' | 'img' | 'qcow2' | 'other' = 'other';
          if (file.endsWith('.tar.gz')) format = 'tar.gz';
          if (file.endsWith('.img')) format = 'img';
          if (file.endsWith('.qcow2')) format = 'qcow2';
          
          lxcImages.push({
            name: file,
            path: filePath,
            description: `LXC ${type} image (${(stats.size / 1024 / 1024).toFixed(2)} MB)`,
            size: stats.size,
            type,
            format
          });
        }
      }
    } catch (error) {
      logger.error(`Error reading directory ${dirPath}:`, error);
    }
  }
  
  logger.info(`Found ${kernelImages.length} kernel images, ${rootfsImages.length} rootfs images, and ${lxcImages.length} LXC images`);
  
  return { kernelImages, rootfsImages, lxcImages };
}

/**
 * Find the best kernel image to use
 */
export function findBestKernelImage(images: VMImage[]): VMImage | null {
  if (images.length === 0) {
    return null;
  }
  
  // Prioritize based on name (prefer newer versions if identifiable)
  const sortedImages = [...images].sort((a, b) => {
    // Prefer images with "vmlinux" in the name
    if (a.name.includes('vmlinux') && !b.name.includes('vmlinux')) return -1;
    if (!a.name.includes('vmlinux') && b.name.includes('vmlinux')) return 1;
    
    // Then sort by last modified date (newer first)
    return b.lastModified.getTime() - a.lastModified.getTime();
  });
  
  return sortedImages[0];
}

/**
 * Find the best rootfs image to use
 */
export function findBestRootfsImage(images: VMImage[], preferredType?: string): VMImage | null {
  if (images.length === 0) {
    return null;
  }
  
  // Prioritize based on name and preferred type if specified
  const sortedImages = [...images].sort((a, b) => {
    // If a preferred type is specified, prioritize images with that type in the name
    if (preferredType) {
      if (a.name.toLowerCase().includes(preferredType.toLowerCase()) && 
          !b.name.toLowerCase().includes(preferredType.toLowerCase())) return -1;
      if (!a.name.toLowerCase().includes(preferredType.toLowerCase()) && 
          b.name.toLowerCase().includes(preferredType.toLowerCase())) return 1;
    }
    
    // Then sort by last modified date (newer first)
    return b.lastModified.getTime() - a.lastModified.getTime();
  });
  
  return sortedImages[0];
}

/**
 * Find the best LXC image to use based on type
 */
export function findBestLxcImage(images: LXCImage[], preferredType: 'alpine' | 'ubuntu' | 'other' = 'ubuntu'): LXCImage | null {
  if (images.length === 0) {
    return null;
  }
  
  // Filter by preferred type first
  const typeFiltered = images.filter(img => img.type === preferredType);
  
  // If no matches, return the first image
  if (typeFiltered.length === 0) {
    return images[0];
  }
  
  // Sort by size (smaller first)
  return typeFiltered.sort((a, b) => a.size - b.size)[0];
}

/**
 * Load images for a MicroVM
 */
export async function loadImagesForMicroVM(preferredRootfsType?: string): Promise<{
  kernelPath: string;
  rootfsPath: string;
}> {
  // Get all available images
  const availableImages = await getAllAvailableImages();
  
  // Find best images
  const kernelImage = findBestKernelImage(availableImages.kernelImages);
  const rootfsImage = findBestRootfsImage(availableImages.rootfsImages, preferredRootfsType);
  
  if (!kernelImage) {
    throw new Error('No suitable kernel image found');
  }
  
  if (!rootfsImage) {
    throw new Error('No suitable rootfs image found');
  }
  
  logger.info(`Selected kernel image: ${kernelImage.name} (${kernelImage.path})`);
  logger.info(`Selected rootfs image: ${rootfsImage.name} (${rootfsImage.path})`);
  
  return {
    kernelPath: kernelImage.path,
    rootfsPath: rootfsImage.path
  };
}

/**
 * Get an image by path
 */
export async function getImageByPath(imagePath: string): Promise<VMImage | null> {
  try {
    if (!fs.existsSync(imagePath)) {
      logger.error(`Image not found: ${imagePath}`);
      return null;
    }
    
    const stats = fs.statSync(imagePath);
    const fileName = path.basename(imagePath);
    
    let type: 'kernel' | 'rootfs' = 'rootfs';
    if (fileName.includes('vmlinux') || fileName.includes('kernel') || fileName.endsWith('.bin')) {
      type = 'kernel';
    }
    
    return {
      name: fileName,
      description: `Image loaded from ${imagePath}`,
      type,
      path: imagePath,
      size: stats.size,
      lastModified: stats.mtime
    };
  } catch (error) {
    logger.error(`Error loading image from ${imagePath}:`, error);
    return null;
  }
}

/**
 * Get LXC images from the resources directory
 */
export async function getLxcImages(): Promise<LXCImage[]> {
  const resourcesPath = path.join(process.cwd(), 'resources', 'lxc-images');
  const results: LXCImage[] = [];
  
  try {
    if (!fs.existsSync(resourcesPath)) {
      logger.warn(`LXC images directory does not exist: ${resourcesPath}`);
      return [];
    }
    
    const files = fs.readdirSync(resourcesPath);
    
    for (const file of files) {
      const filePath = path.join(resourcesPath, file);
      const stats = fs.statSync(filePath);
      
      if (!stats.isFile()) continue;
      
      // Skip index files and non-image files
      if (file === 'index.ts' || file === 'index.js') continue;
      
      // Determine image type
      let type: 'alpine' | 'ubuntu' | 'other' = 'other';
      if (file.toLowerCase().includes('alpine')) type = 'alpine';
      if (file.toLowerCase().includes('ubuntu') || file.toLowerCase().includes('jammy')) type = 'ubuntu';
      
      // Determine format
      let format: 'tar.gz' | 'img' | 'qcow2' | 'other' = 'other';
      if (file.endsWith('.tar.gz')) format = 'tar.gz';
      if (file.endsWith('.img')) format = 'img';
      if (file.endsWith('.qcow2')) format = 'qcow2';
      
      // Add image to results
      results.push({
        name: file,
        path: filePath,
        description: `LXC ${type} image (${(stats.size / 1024 / 1024).toFixed(2)} MB)`,
        size: stats.size,
        type,
        format
      });
    }
    
    return results;
  } catch (error) {
    logger.error(`Error reading LXC images directory:`, error);
    return [];
  }
} 