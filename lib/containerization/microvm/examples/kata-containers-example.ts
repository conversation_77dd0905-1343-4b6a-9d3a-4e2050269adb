/**
 * Kata Containers MicroVM Example
 * 
 * This example demonstrates how to use Kata Containers with the microVM library.
 * Note: This is a conceptual example and would require additional implementation
 * to work with actual Kata Containers.
 */

import { MicroVmManager } from '../core';
import { findKernelImage, findRootfsImage } from '../utils';

async function main() {
  try {
    console.log('Initializing MicroVM Manager for Kata Containers...');
    const manager = new MicroVmManager({
      // Kata Containers specific options would go here
    });
    
    // Find kernel and rootfs images
    // In a real implementation, these would be Kata Containers specific images
    const kernelPath = findKernelImage() || '/usr/share/kata-containers/vmlinux.container';
    const rootfsPath = findRootfsImage() || '/usr/share/kata-containers/kata-containers.img';
    
    console.log(`Using kernel: ${kernelPath}`);
    console.log(`Using rootfs: ${rootfsPath}`);
    
    // Create a new microVM using Kata Containers
    console.log('Creating Kata Containers microVM...');
    const microvm = await manager.createMicroVm({
      name: 'kata-example',
      memSizeMib: 2048,
      vcpuCount: 2,
      kernel: {
        path: kernelPath,
        bootArgs: 'console=ttyS0 reboot=k panic=1 pci=off',
      },
      rootfs: {
        path: rootfsPath,
      },
      // Additional Kata Containers specific options
      annotations: {
        'io.katacontainers.config.hypervisor.firecracker': 'true',
      },
    });
    
    // Get microVM info
    const info = await microvm.getInfo();
    console.log('Kata Containers microVM created:');
    console.log(JSON.stringify(info, null, 2));
    
    // Start the microVM
    console.log('Starting Kata Containers microVM...');
    await microvm.start();
    
    console.log('Kata Containers microVM started successfully!');
    console.log('Current state:', microvm.getState());
    
    // In a real implementation, we would run a container inside the Kata Containers microVM
    console.log('\nRunning a container inside the Kata Containers microVM...');
    // This is a conceptual example - actual implementation would depend on Kata Containers API
    try {
      const result = await microvm.executeCommand('docker run --runtime=kata-fc -it --rm alpine echo "Hello from Kata Containers!"');
      console.log('Container output:', result);
    } catch (error) {
      console.log('Note: Command execution is not implemented in this example');
    }
    
    // Wait for user input
    console.log('\nPress Enter to stop and delete the Kata Containers microVM...');
    await new Promise(resolve => process.stdin.once('data', resolve));
    
    // Stop the microVM
    console.log('Stopping Kata Containers microVM...');
    await microvm.stop();
    console.log('Kata Containers microVM stopped successfully!');
    
    // Delete the microVM
    console.log('Deleting Kata Containers microVM...');
    await manager.deleteMicroVm(microvm.getId());
    console.log('Kata Containers microVM deleted successfully!');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}
