/**
 * Basic MicroVM Usage Example
 * 
 * This example demonstrates how to create, start, and manage a microVM.
 */

import { MicroVmManager } from '../core';
import { findKernelImage, findRootfsImage } from '../utils';

async function main() {
  // Create a microVM manager
  const manager = new MicroVmManager();
  
  // Find kernel and rootfs images
  const kernelPath = findKernelImage();
  const rootfsPath = findRootfsImage();
  
  if (!kernelPath) {
    console.error('Kernel image not found. Please specify the path manually.');
    process.exit(1);
  }
  
  if (!rootfsPath) {
    console.error('Root filesystem image not found. Please specify the path manually.');
    process.exit(1);
  }
  
  try {
    // Create a new microVM
    console.log('Creating microVM...');
    const microvm = await manager.createMicroVm({
      name: 'example-microvm',
      memSizeMib: 1024,
      vcpuCount: 2,
      kernel: {
        path: kernelPath,
        bootArgs: 'console=ttyS0 reboot=k panic=1 pci=off',
      },
      rootfs: {
        path: rootfsPath,
      },
    });
    
    // Start the microVM
    console.log('Starting microVM...');
    await microvm.start();
    
    // Get microVM info
    const info = await microvm.getInfo();
    console.log('MicroVM info:', JSON.stringify(info, null, 2));
    
    // Wait for user input
    console.log('MicroVM is running. Press Enter to stop and delete it...');
    await new Promise(resolve => process.stdin.once('data', resolve));
    
    // Stop the microVM
    console.log('Stopping microVM...');
    await microvm.stop();
    
    // Delete the microVM
    console.log('Deleting microVM...');
    await manager.deleteMicroVm(microvm.getId());
    
    console.log('Done!');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}
