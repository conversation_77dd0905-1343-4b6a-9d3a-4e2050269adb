/**
 * Firecracker MicroVM Example
 * 
 * This example demonstrates how to create and manage a Firecracker microVM.
 */

import { MicroVmManager } from '../core';
import { findKernelImage, findRootfsImage } from '../utils';

async function main() {
  try {
    console.log('Initializing MicroVM Manager...');
    const manager = new MicroVmManager();
    
    // Find kernel and rootfs images
    const kernelPath = findKernelImage() || '/path/to/vmlinux';
    const rootfsPath = findRootfsImage() || '/path/to/rootfs.ext4';
    
    console.log(`Using kernel: ${kernelPath}`);
    console.log(`Using rootfs: ${rootfsPath}`);
    
    // Create a new microVM
    console.log('Creating microVM...');
    const microvm = await manager.createMicroVm({
      name: 'firecracker-example',
      memSizeMib: 1024,
      vcpuCount: 2,
      kernel: {
        path: kernelPath,
        bootArgs: 'console=ttyS0 reboot=k panic=1 pci=off',
      },
      rootfs: {
        path: rootfsPath,
      },
      networkInterfaces: [
        {
          id: 'eth0',
          hostDevName: 'tap0',
        },
      ],
    });
    
    // Get microVM info
    const info = await microvm.getInfo();
    console.log('MicroVM created:');
    console.log(JSON.stringify(info, null, 2));
    
    // Start the microVM
    console.log('Starting microVM...');
    await microvm.start();
    
    console.log('MicroVM started successfully!');
    console.log('Current state:', microvm.getState());
    
    // Wait for user input
    console.log('\nPress Enter to stop and delete the microVM...');
    await new Promise(resolve => process.stdin.once('data', resolve));
    
    // Stop the microVM
    console.log('Stopping microVM...');
    await microvm.stop();
    console.log('MicroVM stopped successfully!');
    
    // Delete the microVM
    console.log('Deleting microVM...');
    await manager.deleteMicroVm(microvm.getId());
    console.log('MicroVM deleted successfully!');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}
