/**
 * Network Manager
 * 
 * This module provides a manager for creating and managing network interfaces
 * for MicroVMs.
 */

import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';
import { ContainerizationError } from '../../shared/error-handling';
import { v4 as uuidv4 } from 'uuid';

// Simple console-based logger implementation
const logger = {
  info: (...args: any[]) => console.log('[INFO]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
  debug: (...args: any[]) => console.log('[DEBUG]', ...args)
};

/**
 * Network interface creation options
 */
export interface NetworkInterfaceOptions {
  /**
   * Interface ID
   */
  id?: string;

  /**
   * Host device name
   */
  hostDevName?: string;

  /**
   * Guest MAC address
   */
  guestMac?: string;

  /**
   * Network type
   */
  networkType?: 'bridge' | 'tap' | 'user';

  /**
   * Bridge name (for bridge networks)
   */
  bridgeName?: string;
}

/**
 * Network interface information
 */
export interface NetworkInterfaceInfo {
  /**
   * Interface ID
   */
  id: string;

  /**
   * Host device name
   */
  hostDevName: string;

  /**
   * Guest MAC address
   */
  guestMac: string;

  /**
   * Network type
   */
  networkType: 'bridge' | 'tap' | 'user';

  /**
   * Bridge name (for bridge networks)
   */
  bridgeName?: string;

  /**
   * Created at timestamp
   */
  createdAt: Date;
}

/**
 * Network manager options
 */
export interface NetworkManagerOptions {
  /**
   * Base directory for network configuration files
   */
  baseDir?: string;
}

/**
 * Network manager
 */
export class NetworkManager {
  private readonly baseDir: string;
  private readonly interfaces: Map<string, NetworkInterfaceInfo> = new Map();

  /**
   * Create a new network manager
   * @param options Network manager options
   */
  constructor(options: NetworkManagerOptions = {}) {
    this.baseDir = options.baseDir || path.join(process.cwd(), 'data', 'networks');

    // Create base directory if it doesn't exist
    fs.mkdirSync(this.baseDir, { recursive: true });
  }

  /**
   * Create a network interface
   * @param options Network interface options
   * @returns Network interface information
   */
  async createNetworkInterface(options: NetworkInterfaceOptions): Promise<NetworkInterfaceInfo> {
    // Check if we have root privileges for network operations
    const hasPrivileges = this.checkForNetworkPrivileges();

    // If we're trying to create an actual network device and don't have privileges, use user networking instead
    if (!hasPrivileges && (options.networkType === 'bridge' || options.networkType === 'tap')) {
      logger.warn('Insufficient privileges for creating network devices. Using user networking instead.');
      options.networkType = 'user';
    }

    // Generate ID if not provided
    const id = options.id || `net-${uuidv4().substring(0, 8)}`;

    // Generate MAC if not provided
    const guestMac = options.guestMac || this.generateRandomMac();

    let hostDevName = options.hostDevName;
    let networkType = options.networkType || 'user'; // Default to user networking which requires no privileges
    let bridgeName = options.bridgeName;

    try {
      if (networkType === 'bridge') {
        // Try to use an existing bridge or create one
        hostDevName = await this.setupBridgeInterface(bridgeName || 'virbr0', id);
      } else if (networkType === 'tap') {
        // Create a tap interface
        hostDevName = await this.setupTapInterface(options.hostDevName || `tap${id.slice(-4)}`, id);
      } else {
        // For user networking, we don't need to create any actual device
        hostDevName = 'user0'; // Just a placeholder name
      }
    } catch (error) {
      logger.error(`Failed to create network interface: ${error}`);
      
      // Fall back to user networking if anything fails
      networkType = 'user';
      hostDevName = 'user0';
      
      logger.info('Falling back to user networking (no custom device)');
    }

    // Create interface info
    const interfaceInfo: NetworkInterfaceInfo = {
      id,
      hostDevName,
      guestMac,
      networkType,
      bridgeName: networkType === 'bridge' ? bridgeName : undefined,
      createdAt: new Date(),
    };

    // Store interface info
    this.interfaces.set(id, interfaceInfo);

    // Save interface info to disk
    const configPath = path.join(this.baseDir, `${id}.json`);
    fs.writeFileSync(configPath, JSON.stringify(interfaceInfo, null, 2));

    logger.info(`Created network interface: ${id} (${hostDevName})`);
    return interfaceInfo;
  }

  /**
   * Get a network interface by ID
   * @param id Interface ID
   * @returns Network interface information, or undefined if not found
   */
  getNetworkInterface(id: string): NetworkInterfaceInfo | undefined {
    return this.interfaces.get(id);
  }

  /**
   * Delete a network interface
   * @param id Interface ID
   */
  async deleteNetworkInterface(id: string): Promise<void> {
    const interfaceInfo = this.interfaces.get(id);
    if (!interfaceInfo) {
      logger.warn(`Interface not found: ${id}`);
      return;
    }

    try {
      if (interfaceInfo.networkType === 'tap') {
        // Only attempt to delete if we have privileges
        if (this.checkForNetworkPrivileges()) {
          await this.deleteTapInterface(interfaceInfo.hostDevName);
        }
      }
      // We don't delete bridge interfaces as they might be shared

      // Remove from map
      this.interfaces.delete(id);

      // Remove config file
      const configPath = path.join(this.baseDir, `${id}.json`);
      if (fs.existsSync(configPath)) {
        fs.unlinkSync(configPath);
      }

      logger.info(`Deleted network interface: ${id} (${interfaceInfo.hostDevName})`);
    } catch (error) {
      logger.warn(`Failed to delete network interface: ${error}`);
      // Don't throw, just log warning
    }
  }

  /**
   * Setup a bridge interface
   * @param bridgeName Bridge name
   * @param id Interface ID (for logging)
   * @returns Host device name
   */
  private async setupBridgeInterface(bridgeName: string, id: string): Promise<string> {
    try {
      // Check if bridge already exists
      try {
        const output = execSync(`ip link show ${bridgeName}`, { stdio: 'pipe' }).toString();
        logger.info(`Bridge ${bridgeName} already exists. Reusing.`);
        return bridgeName;
      } catch (error) {
        // Bridge doesn't exist, create it
        logger.info(`Creating bridge ${bridgeName}`);
        
        // Create bridge
        execSync(`ip link add name ${bridgeName} type bridge`, { stdio: 'pipe' });
        
        // Enable bridge
        execSync(`ip link set ${bridgeName} up`, { stdio: 'pipe' });
        
        return bridgeName;
      }
    } catch (error) {
      logger.error(`Failed to create bridge interface: ${error}`);
      throw new ContainerizationError(`Failed to create bridge interface: ${error}`);
    }
  }

  /**
   * Setup a tap interface
   * @param tapName Tap name
   * @param id Interface ID (for logging)
   * @returns Host device name
   */
  private async setupTapInterface(tapName: string, id: string): Promise<string> {
    try {
      // Create tap interface
      execSync(`ip tuntap add dev ${tapName} mode tap`, { stdio: 'pipe' });
      
      // Enable the interface
      execSync(`ip link set ${tapName} up`, { stdio: 'pipe' });
      
      return tapName;
    } catch (error) {
      logger.error(`Failed to create tap interface: ${error}`);
      throw new ContainerizationError(`Failed to create tap interface: ${error}`);
    }
  }

  /**
   * Delete a tap interface
   * @param tapName Tap name
   */
  private async deleteTapInterface(tapName: string): Promise<void> {
    try {
      // Check if tap interface exists
      try {
        execSync(`ip link show ${tapName}`, { stdio: 'pipe' });
      } catch (error) {
        // Interface doesn't exist, nothing to do
        return;
      }
      
      // Delete the interface
      execSync(`ip tuntap del dev ${tapName} mode tap`, { stdio: 'pipe' });
    } catch (error) {
      logger.warn(`Failed to delete tap interface: ${error}`);
      // Don't throw, just log warning
    }
  }

  /**
   * Generate a random MAC address
   * @returns Random MAC address
   */
  private generateRandomMac(): string {
    // Generate a random MAC address with the locally administered bit set
    const macParts: string[] = [];
    
    // First byte with locally administered bit set
    macParts.push('02');
    
    // Generate 5 random bytes
    for (let i = 0; i < 5; i++) {
      const byte = Math.floor(Math.random() * 256);
      macParts.push(byte.toString(16).padStart(2, '0'));
    }
    
    return macParts.join(':');
  }

  /**
   * Check if we have privileges to perform network operations
   * @returns Whether we have network privileges
   */
  private checkForNetworkPrivileges(): boolean {
    try {
      // Check if we're running as root
      if (process.getuid && process.getuid() === 0) {
        return true;
      }
      
      // Try to run a simple network command that requires privileges
      // This will fail if we don't have permissions
      execSync('ip tuntap list', { stdio: 'ignore' });
      return true;
    } catch (error) {
      return false;
    }
  }
}
