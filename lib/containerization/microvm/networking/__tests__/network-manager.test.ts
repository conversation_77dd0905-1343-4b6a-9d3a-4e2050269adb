/**
 * NetworkManager Tests
 * 
 * This file contains tests for the NetworkManager class.
 */

import { NetworkManager } from '../network-manager';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { ContainerizationError } from '../../../shared/error-handling';

const execAsync = promisify(exec);

// Mock dependencies
jest.mock('fs');
jest.mock('child_process');
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('NetworkManager', () => {
  let networkManager: NetworkManager;
  const testBaseDir = '/tmp/test-networks';
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock fs.mkdirSync
    (fs.mkdirSync as jest.Mock).mockImplementation(() => {});
    
    // Mock execAsync
    (execAsync as jest.Mock).mockResolvedValue({ stdout: '', stderr: '' });
    
    // Create network manager
    networkManager = new NetworkManager({
      baseDir: testBaseDir,
      defaultBridgeName: 'test-bridge',
    });
  });
  
  describe('createNetworkInterface', () => {
    it('should create a bridge network interface successfully', async () => {
      // Arrange
      const config = {
        id: 'test-interface',
        networkType: 'bridge' as const,
        bridgeName: 'test-bridge',
      };
      
      // Mock bridge existence check
      (execAsync as jest.Mock)
        .mockRejectedValueOnce(new Error('Bridge not found')) // First call (check bridge)
        .mockResolvedValue({ stdout: '', stderr: '' }); // Subsequent calls
      
      // Act
      const result = await networkManager.createNetworkInterface(config);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe(config.id);
      expect(result.networkType).toBe(config.networkType);
      expect(result.bridgeName).toBe(config.bridgeName);
      expect(result.hostDevName).toContain('tap-');
      expect(result.guestMac).toMatch(/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/);
      expect(result.createdAt).toBeInstanceOf(Date);
      
      // Verify exec calls
      expect(execAsync).toHaveBeenCalledWith(`ip link show ${config.bridgeName}`);
      expect(execAsync).toHaveBeenCalledWith(`ip link add name ${config.bridgeName} type bridge`);
      expect(execAsync).toHaveBeenCalledWith(`ip link set ${config.bridgeName} up`);
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('ip tuntap add dev'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('ip link set'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('ip link set'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('ip link set'));
    });
    
    it('should use existing bridge if it already exists', async () => {
      // Arrange
      const config = {
        id: 'test-interface',
        networkType: 'bridge' as const,
        bridgeName: 'test-bridge',
      };
      
      // Mock bridge existence check
      (execAsync as jest.Mock).mockResolvedValue({ stdout: '', stderr: '' });
      
      // Act
      const result = await networkManager.createNetworkInterface(config);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.bridgeName).toBe(config.bridgeName);
      
      // Verify exec calls - should not create bridge
      expect(execAsync).toHaveBeenCalledWith(`ip link show ${config.bridgeName}`);
      expect(execAsync).not.toHaveBeenCalledWith(`ip link add name ${config.bridgeName} type bridge`);
      expect(execAsync).not.toHaveBeenCalledWith(`ip link set ${config.bridgeName} up`);
    });
    
    it('should create a macvtap network interface successfully', async () => {
      // Arrange
      const config = {
        id: 'test-interface',
        networkType: 'macvtap' as const,
        hostDevName: 'eth0',
      };
      
      // Act
      const result = await networkManager.createNetworkInterface(config);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe(config.id);
      expect(result.networkType).toBe(config.networkType);
      expect(result.hostDevName).toContain('macvtap-');
      
      // Verify exec calls
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('ip link add link eth0'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('ip link set'));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('ip link set'));
    });
    
    it('should create a user network interface successfully', async () => {
      // Arrange
      const config = {
        id: 'test-interface',
        networkType: 'user' as const,
      };
      
      // Act
      const result = await networkManager.createNetworkInterface(config);
      
      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe(config.id);
      expect(result.networkType).toBe(config.networkType);
      expect(result.hostDevName).toContain('user-');
      
      // Verify no exec calls for user mode
      expect(execAsync).not.toHaveBeenCalled();
    });
    
    it('should throw an error for unsupported network type', async () => {
      // Arrange
      const config = {
        id: 'test-interface',
        networkType: 'unsupported' as any,
      };
      
      // Act & Assert
      await expect(networkManager.createNetworkInterface(config)).rejects.toThrow(ContainerizationError);
      await expect(networkManager.createNetworkInterface(config)).rejects.toThrow('Unsupported network type');
    });
    
    it('should throw an error if bridge creation fails', async () => {
      // Arrange
      const config = {
        id: 'test-interface',
        networkType: 'bridge' as const,
        bridgeName: 'test-bridge',
      };
      
      // Mock bridge existence check and creation failure
      (execAsync as jest.Mock)
        .mockRejectedValueOnce(new Error('Bridge not found')) // First call (check bridge)
        .mockRejectedValueOnce(new Error('Failed to create bridge')); // Second call (create bridge)
      
      // Act & Assert
      await expect(networkManager.createNetworkInterface(config)).rejects.toThrow(ContainerizationError);
      await expect(networkManager.createNetworkInterface(config)).rejects.toThrow('Failed to create bridge interface');
    });
  });
  
  describe('deleteNetworkInterface', () => {
    it('should delete a bridge network interface successfully', async () => {
      // Arrange
      const interfaceId = 'test-interface';
      const hostDevName = 'tap-test';
      
      // Create a test interface first
      (networkManager as any).interfaces.set(interfaceId, {
        id: interfaceId,
        hostDevName,
        guestMac: '52:54:00:00:00:00',
        networkType: 'bridge',
        bridgeName: 'test-bridge',
        createdAt: new Date(),
      });
      
      // Act
      await networkManager.deleteNetworkInterface(interfaceId);
      
      // Assert
      expect((networkManager as any).interfaces.has(interfaceId)).toBe(false);
      
      // Verify exec calls
      expect(execAsync).toHaveBeenCalledWith(`ip link delete ${hostDevName}`);
    });
    
    it('should delete a macvtap network interface successfully', async () => {
      // Arrange
      const interfaceId = 'test-interface';
      const hostDevName = 'macvtap-test';
      
      // Create a test interface first
      (networkManager as any).interfaces.set(interfaceId, {
        id: interfaceId,
        hostDevName,
        guestMac: '52:54:00:00:00:00',
        networkType: 'macvtap',
        createdAt: new Date(),
      });
      
      // Act
      await networkManager.deleteNetworkInterface(interfaceId);
      
      // Assert
      expect((networkManager as any).interfaces.has(interfaceId)).toBe(false);
      
      // Verify exec calls
      expect(execAsync).toHaveBeenCalledWith(`ip link delete ${hostDevName}`);
    });
    
    it('should handle non-existent interface gracefully', async () => {
      // Act
      await networkManager.deleteNetworkInterface('non-existent');
      
      // Assert - should not throw and not call exec
      expect(execAsync).not.toHaveBeenCalled();
    });
    
    it('should handle deletion errors gracefully', async () => {
      // Arrange
      const interfaceId = 'test-interface';
      const hostDevName = 'tap-test';
      
      // Create a test interface first
      (networkManager as any).interfaces.set(interfaceId, {
        id: interfaceId,
        hostDevName,
        guestMac: '52:54:00:00:00:00',
        networkType: 'bridge',
        bridgeName: 'test-bridge',
        createdAt: new Date(),
      });
      
      // Mock deletion failure
      (execAsync as jest.Mock).mockRejectedValue(new Error('Failed to delete interface'));
      
      // Act
      await networkManager.deleteNetworkInterface(interfaceId);
      
      // Assert - should not throw but should remove from map
      expect((networkManager as any).interfaces.has(interfaceId)).toBe(false);
    });
  });
});
