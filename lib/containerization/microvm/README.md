# MicroVM Containerization Library

This library provides a TypeScript implementation for managing microVMs (micro virtual machines) using technologies like Firecracker and Kata Containers. MicroVMs offer the security benefits of virtual machines with the resource efficiency and speed closer to containers.

## Features

- Create, manage, and monitor microVMs
- Support for Firecracker microVMs
- Network and storage management
- Integration with container ecosystems
- Comprehensive error handling and fallback mechanisms
- Health monitoring and resource cleanup

## Usage

```typescript
import { MicroVmManager } from '@/lib/containerization/microvm/core';

// Create a microVM manager
const manager = new MicroVmManager();

// Create a new microVM
const microvm = await manager.createMicroVm({
  name: 'test-microvm',
  memSizeMib: 1024,
  vcpuCount: 2,
  rootfs: {
    path: '/path/to/rootfs.ext4',
  },
  kernel: {
    path: '/path/to/vmlinux',
  },
});

// Start the microVM
await microvm.start();

// Execute a command in the microVM
const result = await microvm.executeCommand('ls -la');
console.log(result);

// Stop the microVM
await microvm.stop();

// Clean up resources
await microvm.delete();
```

## Architecture

The library is organized into several modules:

- **api**: Low-level API clients for interacting with microVM technologies
- **core**: High-level management interfaces for microVMs
- **models**: Data models and interfaces
- **hooks**: Integration hooks for file system, networking, etc.
- **utils**: Utility functions and helpers

## Supported Technologies

- [Firecracker](https://firecracker-microvm.github.io/): A lightweight VMM developed by AWS
- [Kata Containers](https://katacontainers.io/): Secure container runtime with lightweight VMs

## Requirements

- Linux host with KVM support
- Firecracker binary (for Firecracker support)
- Kata Containers runtime (for Kata Containers support)
