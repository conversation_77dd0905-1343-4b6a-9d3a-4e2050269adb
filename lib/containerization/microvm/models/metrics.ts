/**
 * MicroVM Metrics
 * 
 * This module defines the metrics interface for MicroVMs.
 */

export interface MicroVmMetrics {
  /**
   * CPU usage percentage
   */
  cpuUsage: number;

  /**
   * Memory usage in bytes
   */
  memoryUsage: number;

  /**
   * Network received bytes
   */
  networkRx: number;

  /**
   * Network transmitted bytes
   */
  networkTx: number;

  /**
   * Disk read bytes
   */
  diskRead: number;

  /**
   * Disk write bytes
   */
  diskWrite: number;

  /**
   * VM uptime in seconds
   */
  uptime: number;
} 