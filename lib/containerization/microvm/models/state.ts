/**
 * MicroVM State
 * 
 * This module defines the possible states of a MicroVM.
 */

export enum MicroVmState {
  /**
   * VM has been created but not started
   */
  CREATED = 'created',

  /**
   * VM is starting up
   */
  STARTING = 'starting',

  /**
   * VM is running
   */
  RUNNING = 'running',

  /**
   * VM is stopping
   */
  STOPPING = 'stopping',

  /**
   * VM has been stopped
   */
  STOPPED = 'stopped',

  /**
   * VM has failed
   */
  FAILED = 'failed',

  /**
   * VM has been deleted
   */
  DELETED = 'deleted'
} 