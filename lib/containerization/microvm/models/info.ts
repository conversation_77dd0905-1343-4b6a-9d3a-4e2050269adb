/**
 * MicroVM Info
 * 
 * This module defines the information interface for MicroVMs.
 */

export interface MicroVmInfo {
  /**
   * VM ID
   */
  id: string;

  /**
   * VM name
   */
  name: string;

  /**
   * VM state
   */
  state: string;

  /**
   * VM uptime in seconds
   */
  uptime: number;

  /**
   * CPU usage percentage
   */
  cpu_usage?: number;

  /**
   * Memory usage in bytes
   */
  memory_usage?: number;

  /**
   * Network received bytes
   */
  network_rx?: number;

  /**
   * Network transmitted bytes
   */
  network_tx?: number;

  /**
   * Disk read bytes
   */
  disk_read?: number;

  /**
   * Disk write bytes
   */
  disk_write?: number;
} 