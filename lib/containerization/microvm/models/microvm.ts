/**
 * MicroVM Models
 *
 * This module defines the data models for microVMs.
 */

/**
 * MicroVM state
 */
export enum MicroVmState {
  CREATED = 'created',
  STARTING = 'starting',
  RUNNING = 'running',
  PAUSED = 'paused',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  FAILED = 'failed',
  DELETED = 'deleted',
}

/**
 * MicroVM network interface configuration
 */
export interface MicroVmNetworkInterface {
  /**
   * Interface ID
   */
  id: string;

  /**
   * Host device name
   */
  hostDevName?: string;

  /**
   * Guest MAC address
   */
  guestMac?: string;

  /**
   * Whether to allow multiple interfaces
   */
  allowMultipleInterfaces?: boolean;

  /**
   * Interface type (e.g., 'tap', 'macvtap')
   */
  ifType?: string;

  /**
   * Static IP configuration
   */
  staticIpConfig?: {
    ipAddr: string;
    prefix: number;
    gateway?: string;
    nameservers?: string[];
  };
}

/**
 * MicroVM drive configuration
 */
export interface MicroVmDrive {
  /**
   * Drive ID
   */
  id: string;

  /**
   * Path to the drive file
   */
  path: string;

  /**
   * Whether the drive is read-only
   */
  readOnly?: boolean;

  /**
   * Drive type (e.g., 'block', 'file')
   */
  type?: 'block' | 'file';

  /**
   * Whether this is the root filesystem
   */
  isRootfs?: boolean;
}

/**
 * MicroVM kernel configuration
 */
export interface MicroVmKernel {
  /**
   * Path to the kernel image
   */
  path: string;

  /**
   * Kernel boot arguments
   */
  bootArgs?: string;
}

/**
 * MicroVM resource constraints
 */
export interface MicroVmResources {
  /**
   * Memory size in MiB
   */
  memSizeMib: number;

  /**
   * Number of vCPUs
   */
  vcpuCount: number;

  /**
   * CPU template (e.g., 'T2', 'C3')
   */
  cpuTemplate?: string;

  /**
   * Whether to enable hyperthreading
   */
  hyperthreading?: boolean;

  /**
   * CPU affinity
   */
  cpuAffinity?: number[];
}

/**
 * MicroVM metadata
 */
export interface MicroVmMetadata {
  /**
   * MicroVM name
   */
  name: string;

  /**
   * MicroVM ID
   */
  id: string;

  /**
   * Creation timestamp
   */
  createdAt: Date;

  /**
   * Last updated timestamp
   */
  updatedAt: Date;

  /**
   * Custom labels
   */
  labels?: Record<string, string>;

  /**
   * Custom annotations
   */
  annotations?: Record<string, string>;
}

/**
 * MicroVM configuration
 */
export interface MicroVmConfig {
  /**
   * MicroVM metadata
   */
  metadata: MicroVmMetadata;

  /**
   * MicroVM resources
   */
  resources: MicroVmResources;

  /**
   * MicroVM kernel
   */
  kernel: MicroVmKernel;

  /**
   * MicroVM drives
   */
  drives: MicroVmDrive[];

  /**
   * MicroVM network interfaces
   */
  networkInterfaces: MicroVmNetworkInterface[];

  /**
   * Whether to enable seccomp filtering
   */
  seccompEnabled?: boolean;

  /**
   * Whether to enable jailer
   */
  jailerEnabled?: boolean;

  /**
   * Jailer configuration
   */
  jailerConfig?: {
    gid?: number;
    uid?: number;
    id?: string;
    netns?: string;
    daemonize?: boolean;
  };
}

/**
 * MicroVM creation options
 */
export interface MicroVmCreationOptions {
  /**
   * MicroVM name
   */
  name: string;

  /**
   * Memory size in MiB
   */
  memSizeMib: number;

  /**
   * Number of vCPUs
   */
  vcpuCount: number;

  /**
   * Disk size in GB
   */
  diskSizeGb?: number;

  /**
   * Root filesystem
   */
  rootfs: {
    path: string;
    readOnly?: boolean;
  };

  /**
   * Kernel configuration
   */
  kernel: {
    path: string;
    bootArgs?: string;
  };

  /**
   * Additional drives
   */
  additionalDrives?: Array<{
    id: string;
    path: string;
    readOnly?: boolean;
  }>;

  /**
   * Network interfaces
   */
  networkInterfaces?: MicroVmNetworkInterface[];

  /**
   * Custom labels
   */
  labels?: Record<string, string>;

  /**
   * Custom annotations
   */
  annotations?: Record<string, string>;

  /**
   * Whether to enable seccomp filtering
   */
  seccompEnabled?: boolean;

  /**
   * Whether to enable jailer
   */
  jailerEnabled?: boolean;

  /**
   * Jailer configuration
   */
  jailerConfig?: {
    gid?: number;
    uid?: number;
    id?: string;
    netns?: string;
    daemonize?: boolean;
  };
}

/**
 * MicroVM information
 */
export interface MicroVmInfo {
  /**
   * MicroVM ID
   */
  id: string;

  /**
   * MicroVM name
   */
  name: string;

  /**
   * MicroVM state
   */
  state: MicroVmState;

  /**
   * Creation timestamp
   */
  createdAt: Date;

  /**
   * Last updated timestamp
   */
  updatedAt: Date;

  /**
   * Memory size in MiB
   */
  memSizeMib: number;

  /**
   * Number of vCPUs
   */
  vcpuCount: number;

  /**
   * Network interfaces
   */
  networkInterfaces: MicroVmNetworkInterface[];

  /**
   * Drives
   */
  drives: MicroVmDrive[];

  /**
   * Custom labels
   */
  labels?: Record<string, string>;

  /**
   * Custom annotations
   */
  annotations?: Record<string, string>;
}
