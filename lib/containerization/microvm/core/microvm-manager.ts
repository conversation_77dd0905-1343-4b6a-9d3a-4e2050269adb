/**
 * MicroVM Manager
 *
 * This module provides a manager for creating and managing microVMs.
 */

// Replace logger import with simple console logging
// import { logger } from '@/lib/logger';

// Simple console-based logger implementation
const logger = {
  info: (...args: any[]) => console.log('[INFO]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
  debug: (...args: any[]) => console.log('[DEBUG]', ...args)
};

import {
  MicroVmConfig,
  MicroVmCreationOptions,
  MicroVmInfo
} from '../models';
import {
  FirecrackerClient,
  EnhancedFirecrackerCommand
} from '../api';
import {
  NotFoundError,
  AlreadyExistsError,
  ContainerizationError
} from '../../shared/error-handling';
import { ResourceCleanupRegistry, globalCleanupRegistry } from '../../shared/resource-management';
import { IMicroVm } from './interfaces';
import { MicroVm } from './microvm-impl';
import { OverlayManager } from '../storage';
import { NetworkManager } from '../networking';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { v4 as uuidv4 } from 'uuid';

/**
 * MicroVM manager options
 */
export interface MicroVmManagerOptions {
  /**
   * Base directory for microVM data
   */
  baseDir?: string;

  /**
   * Resource cleanup registry
   */
  cleanupRegistry?: ResourceCleanupRegistry;

  /**
   * Path to the Firecracker binary
   */
  firecrackerBinaryPath?: string;

  /**
   * Default kernel image path
   */
  defaultKernelPath?: string;

  /**
   * Default root filesystem path
   */
  defaultRootfsPath?: string;
}

/**
 * MicroVM manager
 */
export class MicroVmManager {
  private readonly options: MicroVmManagerOptions;
  private readonly microvms: Map<string, IMicroVm> = new Map();
  private readonly overlayManager: OverlayManager;
  private readonly networkManager: NetworkManager;

  /**
   * Create a new microVM manager
   * @param options Manager options
   */
  constructor(options: MicroVmManagerOptions = {}) {
    this.options = {
      baseDir: path.join(os.tmpdir(), 'microvms'),
      cleanupRegistry: globalCleanupRegistry,
      ...options,
    };

    // Create the base directory if it doesn't exist
    fs.mkdirSync(this.options.baseDir!, { recursive: true });

    // Create the overlay manager
    this.overlayManager = new OverlayManager({
      baseDir: path.join(this.options.baseDir!, 'overlays'),
    });

    // Create the network manager
    this.networkManager = new NetworkManager({
      baseDir: path.join(this.options.baseDir!, 'networks'),
    });

    // Register cleanup function
    if (this.options.cleanupRegistry) {
      this.options.cleanupRegistry.register('microvm-manager', async () => {
        await this.cleanup();
      });
    }
  }

  /**
   * Create a new microVM
   * @param options MicroVM creation options
   * @returns The created microVM
   */
  async createMicroVm(options: MicroVmCreationOptions | any): Promise<IMicroVm> {
    const id = uuidv4();
    const name = options.name || `microvm-${id.substring(0, 8)}`;

    // Check if a microVM with the same name already exists
    if (this.getMicroVmByName(name)) {
      throw new AlreadyExistsError(`MicroVM with name '${name}' already exists`);
    }

    logger.info(`Creating microVM '${name}' (${id})`);

    // Create the microVM directory
    const vmDir = path.join(this.options.baseDir!, id);
    fs.mkdirSync(vmDir, { recursive: true });

    // Generate socket path
    const socketPath = path.join(vmDir, 'firecracker.sock');

    // Create the Firecracker command
    const command = new EnhancedFirecrackerCommand({
      socketPath,
      binaryPath: this.options.firecrackerBinaryPath,
      // Remove log file references to avoid firecracker logger initialization errors
      // logPath: path.join(vmDir, 'firecracker.log'),
      // metricsPath: path.join(vmDir, 'metrics.json'),
      cleanupRegistry: this.options.cleanupRegistry,
    });

    // Start the Firecracker process
    const commandResult = await command.start();

    // Create the Firecracker client
    const client = new FirecrackerClient({
      socketPath: commandResult.socketPath,
    });

    // Handle template-based creation (simplified for ephemeral VMs)
    if (options.template && !options.rootfs) {
      // For template-based creation, set default paths
      options.rootfs = {
        path: this.options.defaultRootfsPath || 
              path.join(process.cwd(), 'resources', 'lxc-images', 'bionic.rootfs.ext4'),
        readOnly: false
      };
      
      options.kernel = {
        path: this.options.defaultKernelPath || 
              path.join(process.cwd(), 'resources', 'kernels', 'vmlinux.bin'),
        bootArgs: "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp"
      };
    }

    // Prepare rootfs
    let rootfsPath = options.rootfs?.path;
    let isOverlayRootfs = false;

    // If rootfs is not read-only, create an overlay
    if (options.rootfs && !options.rootfs.readOnly) {
      try {
        logger.info(`Creating overlay rootfs for microVM '${name}' (${id})`);

        // Create overlay disk
        const overlayDisk = await this.overlayManager.createOverlayDisk({
          baseImagePath: options.rootfs.path,
          overlayDir: path.join(vmDir, 'overlay'),
          mergedDir: path.join(vmDir, 'merged'),
          workDir: path.join(vmDir, 'work'),
          outputPath: path.join(vmDir, 'rootfs.ext4'),
        });

        // Use the overlay disk as rootfs
        rootfsPath = overlayDisk.outputPath;
        isOverlayRootfs = true;

        logger.info(`Created overlay rootfs for microVM '${name}' (${id}): ${rootfsPath}`);
      } catch (error) {
        logger.warn(`Failed to create overlay rootfs for microVM '${name}' (${id}): ${error}`);
        logger.warn(`Falling back to direct rootfs for microVM '${name}' (${id})`);

        // Fallback to direct rootfs
        rootfsPath = options.rootfs.path;
        isOverlayRootfs = false;
      }
    }

    // Create the microVM configuration
    const config: MicroVmConfig = {
      metadata: {
        id,
        name,
        createdAt: new Date(),
        updatedAt: new Date(),
        labels: options.labels || {},
        annotations: {
          ...options.annotations || {},
          isOverlayRootfs: String(isOverlayRootfs),
          originalRootfsPath: options.rootfs?.path || '',
        },
      },
      resources: {
        memSizeMib: options.memSizeMib || 2048,
        vcpuCount: options.vcpuCount || 2,
      },
      kernel: {
        path: options.kernel?.path || '',
        bootArgs: options.kernel?.bootArgs || "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp",
      },
      drives: [
        {
          id: 'rootfs',
          path: rootfsPath || '',
          readOnly: false, // Always false for the overlay
          isRootfs: true,
        },
      ],
      networkInterfaces: [], // Initialize as empty array (not optional)
      seccompEnabled: options.seccompEnabled || false,
      jailerEnabled: options.jailerEnabled || false,
      jailerConfig: options.jailerConfig,
    };

    // Setup network interfaces
    if (options.networkEnabled !== false) {
      try {
        logger.info(`Creating default network interface for microVM '${name}' (${id})`);

        // Create network interface
        const interfaceInfo = await this.networkManager.createNetworkInterface({
          id: `net-${id.substring(0, 8)}`,
          networkType: 'bridge',
          bridgeName: 'virbr0',
        });

        // Add network interface to config
        config.networkInterfaces.push({
          id: interfaceInfo.id,
          hostDevName: interfaceInfo.hostDevName,
          guestMac: interfaceInfo.guestMac,
        });

        logger.info(`Created default network interface for microVM '${name}' (${id}): ${interfaceInfo.id} (${interfaceInfo.hostDevName})`);
      } catch (error) {
        logger.warn(`Failed to create default network interface for microVM '${name}' (${id}): ${error}`);
      }
    }

    // Add additional drives
    if (options.additionalDrives) {
      for (const drive of options.additionalDrives) {
        config.drives.push({
          id: drive.id,
          path: drive.path,
          readOnly: drive.readOnly || false,
        });
      }
    }

    // Create the microVM
    const microvm = new MicroVm({
      id,
      name,
      config,
      client,
      command,
      vmDir,
      cleanupRegistry: this.options.cleanupRegistry,
    });

    // Configure the microVM
    await microvm.configure();

    // Type assertion to ensure compatibility with the interface
    const typedMicrovm = microvm as unknown as IMicroVm;

    // Store the microVM
    this.microvms.set(id, typedMicrovm);

    return typedMicrovm;
  }

  /**
   * Get a microVM by ID
   * @param id MicroVM ID
   * @returns The microVM, or undefined if not found
   */
  getMicroVm(id: string): IMicroVm | undefined {
    return this.microvms.get(id);
  }

  /**
   * Get a microVM by name
   * @param name MicroVM name
   * @returns The microVM, or undefined if not found
   */
  getMicroVmByName(name: string): IMicroVm | undefined {
    for (const microvm of this.microvms.values()) {
      if (microvm.getName() === name) {
        return microvm;
      }
    }

    return undefined;
  }

  /**
   * List all microVMs
   * @returns Array of microVM information
   */
  async listMicroVms(): Promise<MicroVmInfo[]> {
    const result: MicroVmInfo[] = [];

    for (const microvm of this.microvms.values()) {
      result.push(await microvm.getInfo());
    }

    return result;
  }

  /**
   * Delete a microVM
   * @param id MicroVM ID
   */
  async deleteMicroVm(id: string): Promise<void> {
    const microvm = this.getMicroVm(id);

    if (!microvm) {
      throw new NotFoundError(`MicroVM with ID '${id}' not found`);
    }

    logger.info(`Deleting microVM '${microvm.getName()}' (${id})`);

    // Get microVM info before deleting
    const info = await microvm.getInfo();

    // Stop and delete the microVM
    await microvm.delete();

    // Clean up network interfaces
    if (info.networkInterfaces.length > 0) {
      for (const networkInterface of info.networkInterfaces) {
        try {
          logger.info(`Deleting network interface for microVM '${info.name}' (${id}): ${networkInterface.id}`);
          await this.networkManager.deleteNetworkInterface(networkInterface.id);
          logger.info(`Deleted network interface for microVM '${info.name}' (${id}): ${networkInterface.id}`);
        } catch (error) {
          logger.warn(`Failed to delete network interface for microVM '${info.name}' (${id}): ${error}`);
        }
      }
    }

    // Remove from the map
    this.microvms.delete(id);
  }

  /**
   * Clean up all resources
   */
  async cleanup(): Promise<void> {
    logger.info(`Cleaning up ${this.microvms.size} microVMs`);

    // Stop all microVMs
    const promises: Promise<void>[] = [];

    for (const [id, microvm] of this.microvms.entries()) {
      promises.push(
        microvm.delete().catch((error: Error) => {
          logger.error(`Failed to delete microVM ${id}:`, error);
        })
      );
    }

    // Wait for all promises to settle
    await Promise.allSettled(promises);

    // Clear the map
    this.microvms.clear();
  }
}
