/**
 * MicroVM
 *
 * This module provides a class for managing a single microVM.
 */

import { logger } from '@/lib/logger';
import {
  MicroVmState,
  MicroVmInfo,
  MicroVmMetrics
} from '../models';
import {
  ContainerizationError,
  InvalidStateError,
  ResourceExhaustionError,
  NetworkError
} from '../../shared/error-handling';
import { globalCleanupRegistry } from '../../shared/resource-management';
import { IMicroVm, MicroVmOptions } from './interfaces';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { EventEmitter } from 'events';
import { promisify } from 'util';

const execAsync = promisify(exec);

export class MicroVm extends EventEmitter implements IMicroVm {
  private readonly options: MicroVmOptions;
  private state: MicroVmState = MicroVmState.CREATED;
  private metrics: MicroVmMetrics = {
    cpuUsage: 0,
    memoryUsage: 0,
    networkRx: 0,
    networkTx: 0,
    diskRead: 0,
    diskWrite: 0,
    uptime: 0
  };
  private lastMetricsUpdate: number = Date.now();
  private readonly metricsUpdateInterval: number = 5000; // 5 seconds
  private metricsTimer?: NodeJS.Timeout;

  /**
   * Create a new microVM
   * @param options MicroVM options
   */
  constructor(options: MicroVmOptions) {
    super();
    this.options = {
      cleanupRegistry: globalCleanupRegistry,
      ...options,
    };

    // Register cleanup function
    if (this.options.cleanupRegistry) {
      this.options.cleanupRegistry.register(`microvm-${this.options.id}`, async () => {
        await this.stop().catch(error => {
          logger.error(`Failed to stop microVM ${this.options.id}:`, error);
        });
      });
    }

    // Start metrics collection if VM is running
    if (this.state === MicroVmState.RUNNING) {
      this.startMetricsCollection();
    }
  }

  /**
   * Start metrics collection
   */
  private startMetricsCollection(): void {
    if (this.metricsTimer) {
      clearInterval(this.metricsTimer);
    }

    this.metricsTimer = setInterval(async () => {
      try {
        await this.updateMetrics();
      } catch (error) {
        logger.error(`Failed to update metrics for microVM ${this.options.id}:`, error);
      }
    }, this.metricsUpdateInterval);
  }

  /**
   * Stop metrics collection
   */
  private stopMetricsCollection(): void {
    if (this.metricsTimer) {
      clearInterval(this.metricsTimer);
      this.metricsTimer = undefined;
    }
  }

  /**
   * Update VM metrics
   */
  private async updateMetrics(): Promise<void> {
    if (this.state !== MicroVmState.RUNNING) {
      return;
    }

    try {
      const vmInfo = await this.options.client.getVmInfo();
      const now = Date.now();
      const timeDelta = now - this.lastMetricsUpdate;

      // Update metrics
      this.metrics = {
        cpuUsage: vmInfo.cpu_usage || 0,
        memoryUsage: vmInfo.memory_usage || 0,
        networkRx: vmInfo.network_rx || 0,
        networkTx: vmInfo.network_tx || 0,
        diskRead: vmInfo.disk_read || 0,
        diskWrite: vmInfo.disk_write || 0,
        uptime: vmInfo.uptime || 0
      };

      this.lastMetricsUpdate = now;
      this.emit('metrics', this.metrics);
    } catch (error) {
      logger.error(`Failed to update metrics for microVM ${this.options.id}:`, error);
      throw new ContainerizationError(`Failed to update metrics: ${error}`, { cause: error });
    }
  }

  /**
   * Configure the microVM
   */
  async configure(): Promise<void> {
    logger.debug(`Configuring microVM '${this.options.name}' (${this.options.id})`);

    // Configure the machine
    await this.options.client.configureVm({
      vcpu_count: this.options.config.resources.vcpuCount,
      mem_size_mib: this.options.config.resources.memSizeMib,
      cpu_template: this.options.config.resources.cpuTemplate,
      ht_enabled: this.options.config.resources.hyperthreading,
    });

    // Configure the boot source
    await this.options.client.configureBootSource(
      this.options.config.kernel.path,
      this.options.config.kernel.bootArgs
    );

    // Configure drives
    for (const drive of this.options.config.drives) {
      await this.options.client.addDrive(drive.id, {
        drive_id: drive.id,
        path_on_host: drive.path,
        is_root_device: drive.isRootfs || false,
        is_read_only: drive.readOnly || false,
      });
    }

    // Configure network interfaces
    if (this.options.config.networkInterfaces) {
      for (const iface of this.options.config.networkInterfaces) {
        await this.options.client.addNetworkInterface(iface.id, {
          iface_id: iface.id,
          host_dev_name: iface.hostDevName,
          guest_mac: iface.guestMac,
        });
      }
    }

    // Update metadata
    await this.options.client.updateMetadata({
      id: this.options.id,
      name: this.options.name,
      labels: this.options.config.metadata.labels,
      annotations: this.options.config.metadata.annotations,
    });
  }

  /**
   * Start the microVM
   */
  async start(): Promise<void> {
    if (this.state === MicroVmState.RUNNING) {
      logger.debug(`MicroVM '${this.options.name}' is already running`);
      return;
    }

    if (this.state === MicroVmState.DELETED) {
      throw new InvalidStateError(`MicroVM '${this.options.name}' has been deleted`);
    }

    logger.info(`Starting microVM '${this.options.name}' (${this.options.id})`);

    this.state = MicroVmState.STARTING;
    this.emit('stateChange', this.state);

    try {
      // Check resource availability
      await this.checkResourceAvailability();

      // Start the VM
      await this.options.client.startVm();

      // Wait for the VM to be ready
      await this.waitForState(MicroVmState.RUNNING);

      // Start metrics collection
      this.startMetricsCollection();

      logger.info(`MicroVM '${this.options.name}' started successfully`);
    } catch (error) {
      this.state = MicroVmState.FAILED;
      this.emit('stateChange', this.state);
      this.emit('error', error);

      if (error instanceof ResourceExhaustionError) {
        throw error;
      }

      throw new ContainerizationError(`Failed to start microVM '${this.options.name}': ${error}`, {
        cause: error,
      });
    }
  }

  /**
   * Check resource availability
   */
  private async checkResourceAvailability(): Promise<void> {
    try {
      const systemInfo = await this.options.client.getSystemInfo();
      
      // Check CPU availability
      if (systemInfo.available_cpus < this.options.config.resources.vcpuCount) {
        throw new ResourceExhaustionError('Insufficient CPU resources available');
      }

      // Check memory availability
      if (systemInfo.available_memory < this.options.config.resources.memSizeMib) {
        throw new ResourceExhaustionError('Insufficient memory resources available');
      }

      // Check disk space
      const diskSpace = await this.checkDiskSpace();
      if (!diskSpace) {
        throw new ResourceExhaustionError('Insufficient disk space available');
      }
    } catch (error) {
      if (error instanceof ResourceExhaustionError) {
        throw error;
      }
      throw new ContainerizationError(`Failed to check resource availability: ${error}`, {
        cause: error,
      });
    }
  }

  /**
   * Check disk space availability
   */
  private async checkDiskSpace(): Promise<boolean> {
    try {
      const { stdout } = await execAsync('df -k .');
      const lines = stdout.split('\n');
      const diskInfo = lines[1].split(/\s+/);
      const availableSpace = parseInt(diskInfo[3], 10) * 1024; // Convert to bytes

      // Check if we have enough space for the VM
      const requiredSpace = this.options.config.drives.reduce((total, drive) => {
        return total + (drive.size || 0);
      }, 0);

      return availableSpace >= requiredSpace;
    } catch (error) {
      logger.error('Failed to check disk space:', error);
      return false;
    }
  }

  /**
   * Stop the microVM
   */
  async stop(): Promise<void> {
    if (this.state === MicroVmState.STOPPED || this.state === MicroVmState.DELETED) {
      logger.debug(`MicroVM '${this.options.name}' is already stopped or deleted`);
      return;
    }

    logger.info(`Stopping microVM '${this.options.name}' (${this.options.id})`);

    this.state = MicroVmState.STOPPING;
    this.emit('stateChange', this.state);

    try {
      // Stop metrics collection
      this.stopMetricsCollection();

      // Stop the VM
      await this.options.client.stopVm();

      // Wait for the VM to stop
      await this.waitForState(MicroVmState.STOPPED);

      logger.info(`MicroVM '${this.options.name}' stopped successfully`);
    } catch (error) {
      logger.error(`Failed to stop microVM '${this.options.name}':`, error);
      this.emit('error', error);

      // Force stop the Firecracker process
      try {
        await this.options.command.stop();
        this.state = MicroVmState.STOPPED;
        this.emit('stateChange', this.state);
      } catch (stopError) {
        logger.error(`Failed to force stop microVM '${this.options.name}':`, stopError);
        this.state = MicroVmState.FAILED;
        this.emit('stateChange', this.state);
        throw new ContainerizationError(`Failed to stop microVM '${this.options.name}': ${error}`, {
          cause: error,
        });
      }
    }
  }

  /**
   * Delete the microVM
   */
  async delete(): Promise<void> {
    if (this.state === MicroVmState.DELETED) {
      logger.debug(`MicroVM '${this.options.name}' is already deleted`);
      return;
    }

    logger.info(`Deleting microVM '${this.options.name}' (${this.options.id})`);

    // Stop the VM if it's running
    if (this.state !== MicroVmState.STOPPED) {
      await this.stop();
    }

    // Stop the Firecracker process
    await this.options.command.stop();

    // Clean up the VM directory
    try {
      await fs.promises.rm(this.options.vmDir, { recursive: true, force: true });
    } catch (error) {
      logger.warn(`Failed to remove VM directory: ${error}`);
    }

    // Unregister from cleanup registry
    if (this.options.cleanupRegistry) {
      this.options.cleanupRegistry.unregister(`microvm-${this.options.id}`);
    }

    this.state = MicroVmState.DELETED;
    logger.info(`MicroVM '${this.options.name}' deleted successfully`);
  }

  /**
   * Execute a command in the microVM
   * @param command Command to execute
   * @param options Command execution options
   * @returns Command output
   */
  async executeCommand(
    command: string,
    options: {
      timeout?: number;
      cwd?: string;
      env?: Record<string, string>;
    } = {}
  ): Promise<string> {
    if (this.state !== MicroVmState.RUNNING) {
      throw new InvalidStateError(`MicroVM '${this.options.name}' is not running`);
    }

    logger.debug(`Executing command in microVM '${this.options.name}': ${command}`);

    try {
      // Prepare environment variables if provided
      const envVars = options.env
        ? Object.entries(options.env)
            .map(([key, value]) => `${key}="${value}"`)
            .join(' ') + ' '
        : '';

      // Prepare working directory if provided
      const cdCommand = options.cwd ? `cd "${options.cwd}" && ` : '';

      // Construct the full command with environment variables and working directory
      const fullCommand = `${cdCommand}${envVars}${command}`;

      // Execute the command via vsock
      // We'll use the Firecracker API to execute the command via vsock
      const result = await this.executeViaVsock(fullCommand, options.timeout);

      logger.debug(`Command execution result: ${result.substring(0, 100)}${result.length > 100 ? '...' : ''}`);

      return result;
    } catch (error) {
      logger.error(`Failed to execute command in microVM '${this.options.name}': ${error}`);
      throw new ContainerizationError(`Failed to execute command in microVM '${this.options.name}': ${error}`, {
        cause: error,
      });
    }
  }

  /**
   * Execute a command via vsock
   * @param command Command to execute
   * @param timeout Timeout in milliseconds
   * @returns Command output
   * @private
   */
  private async executeViaVsock(command: string, timeout?: number): Promise<string> {
    // Get the vsock CID (context ID) for the VM
    // In Firecracker, the host is always CID 2, and the guest is CID 3
    // We only need the guest CID for the connection
    const guestCID = 3;

    // The port to use for command execution
    // We'll use port 52000 for command execution
    const port = 52000;

    // Create a temporary file to store the command output
    const outputFile = path.join(this.options.vmDir, `cmd-output-${Date.now()}.txt`);

    try {
      // Encode the command to base64 to avoid issues with special characters
      const encodedCommand = Buffer.from(command).toString('base64');

      // Create the vsock-proxy command to send the command to the VM
      // This assumes vsock-proxy is installed on the host
      const vsockCommand = `echo "${encodedCommand}" | base64 -d | socat - VSOCK-CONNECT:${guestCID}:${port} > ${outputFile}`;

      // Execute the vsock command with timeout
      await new Promise<void>((resolve, reject) => {
        exec(vsockCommand, { timeout: timeout || 30000 }, (error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        });
      });

      // Read the command output from the temporary file
      const output = await fs.promises.readFile(outputFile, 'utf-8');

      // Clean up the temporary file
      await fs.promises.unlink(outputFile).catch(error => {
        logger.warn(`Failed to remove temporary file ${outputFile}: ${error}`);
      });

      return output;
    } catch (error) {
      // Clean up the temporary file if it exists
      try {
        await fs.promises.access(outputFile);
        await fs.promises.unlink(outputFile);
      } catch {
        // Ignore errors if the file doesn't exist
      }

      throw error;
    }
  }

  /**
   * Get the microVM information
   * @returns MicroVM information
   */
  async getInfo(): Promise<MicroVmInfo> {
    return {
      id: this.options.id,
      name: this.options.name,
      state: this.state,
      createdAt: this.options.config.metadata.createdAt,
      updatedAt: this.options.config.metadata.updatedAt,
      memSizeMib: this.options.config.resources.memSizeMib,
      vcpuCount: this.options.config.resources.vcpuCount,
      networkInterfaces: this.options.config.networkInterfaces || [],
      drives: this.options.config.drives,
      labels: this.options.config.metadata.labels,
      annotations: this.options.config.metadata.annotations,
    };
  }

  /**
   * Get the microVM state
   * @returns MicroVM state
   */
  getState(): MicroVmState {
    return this.state;
  }

  /**
   * Get the microVM ID
   * @returns MicroVM ID
   */
  getId(): string {
    return this.options.id;
  }

  /**
   * Get the microVM name
   * @returns MicroVM name
   */
  getName(): string {
    return this.options.name;
  }

  /**
   * Wait for the microVM to reach a specific state
   * @param targetState Target state
   * @param timeoutMs Timeout in milliseconds
   */
  private async waitForState(targetState: MicroVmState, timeoutMs: number = 30000): Promise<void> {
    const startTime = Date.now();
    const checkInterval = 500;

    while (this.state !== targetState) {
      // Check timeout
      if (Date.now() - startTime > timeoutMs) {
        throw new ContainerizationError(`Timeout waiting for microVM to reach state ${targetState}`);
      }

      // Update state based on Firecracker process status
      this.updateState();

      // Wait before checking again
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }
  }

  /**
   * Update the microVM state based on the Firecracker process status
   */
  private updateState(): void {
    if (this.state === MicroVmState.DELETED) {
      return;
    }

    if (!this.options.command.isRunning()) {
      this.state = MicroVmState.STOPPED;
      return;
    }

    if (this.state === MicroVmState.STARTING) {
      this.state = MicroVmState.RUNNING;
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): MicroVmMetrics {
    return { ...this.metrics };
  }
}
