/**
 * MicroVM Core Interfaces
 * 
 * This module defines interfaces for the microVM core components.
 */

import { 
  MicroVmState, 
  MicroVmConfig, 
  MicroVmInfo, 
  MicroVmMetrics 
} from '../models';
import { 
  FirecrackerClient, 
  EnhancedFirecrackerCommand 
} from '../api';
import { ResourceCleanupRegistry } from '../../shared/resource-management';

/**
 * MicroVM options
 */
export interface MicroVmOptions {
  /**
   * MicroVM ID
   */
  id: string;
  
  /**
   * MicroVM name
   */
  name: string;
  
  /**
   * MicroVM configuration
   */
  config: MicroVmConfig;
  
  /**
   * Firecracker client
   */
  client: FirecrackerClient;
  
  /**
   * Firecracker command
   */
  command: EnhancedFirecrackerCommand;
  
  /**
   * MicroVM directory
   */
  vmDir: string;
  
  /**
   * Resource cleanup registry
   */
  cleanupRegistry?: ResourceCleanupRegistry;
}

/**
 * MicroVM interface
 */
export interface IMicroVm {
  /**
   * Configure the microVM
   */
  configure(): Promise<void>;
  
  /**
   * Start the microVM
   */
  start(): Promise<void>;
  
  /**
   * Stop the microVM
   */
  stop(): Promise<void>;
  
  /**
   * Delete the microVM
   */
  delete(): Promise<void>;
  
  /**
   * Execute a command in the microVM
   * @param command Command to execute
   * @returns Command output
   */
  executeCommand(command: string): Promise<string>;
  
  /**
   * Get the microVM information
   * @returns MicroVM information
   */
  getInfo(): Promise<MicroVmInfo>;
  
  /**
   * Get the microVM state
   * @returns MicroVM state
   */
  getState(): MicroVmState;
  
  /**
   * Get the microVM ID
   * @returns MicroVM ID
   */
  getId(): string;
  
  /**
   * Get the microVM name
   * @returns MicroVM name
   */
  getName(): string;
  
  /**
   * Get metrics for the MicroVM
   * @returns MicroVM metrics
   * @throws InvalidStateError if the MicroVM is not running
   */
  getMetrics(): Promise<MicroVmMetrics>;
}

export interface MicroVmDrive {
  id: string;
  path: string;
  isRootfs?: boolean;
  readOnly?: boolean;
  size: number; // Size in bytes
}
