/**
 * SnapshotManager Tests
 * 
 * This file contains tests for the SnapshotManager class.
 */

import { SnapshotManager } from '../snapshot-manager';
import { MicroVmManager } from '../../core/microvm-manager';
import { IMicroVm } from '../../core/interfaces';
import { MicroVmState } from '../../models';
import { ContainerizationError } from '../../../shared/error-handling';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Mock dependencies
jest.mock('fs');
jest.mock('child_process');
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock MicroVmManager
jest.mock('../../core/microvm-manager');

describe('SnapshotManager', () => {
  let snapshotManager: SnapshotManager;
  let mockManager: jest.Mocked<MicroVmManager>;
  let mockMicroVm: jest.Mocked<IMicroVm>;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock fs functions
    (fs.mkdirSync as jest.Mock).mockImplementation(() => {});
    (fs.existsSync as jest.Mock).mockReturnValue(true);
    (fs.writeFileSync as jest.Mock).mockImplementation(() => {});
    (fs.rmSync as jest.Mock).mockImplementation(() => {});
    
    // Mock execAsync
    (execAsync as jest.Mock).mockResolvedValue({ stdout: '', stderr: '' });
    
    // Create mock MicroVm
    mockMicroVm = {
      getId: jest.fn().mockReturnValue('test-vm-id'),
      getName: jest.fn().mockReturnValue('test-vm'),
      getState: jest.fn().mockReturnValue(MicroVmState.RUNNING),
      configure: jest.fn().mockResolvedValue(undefined),
      start: jest.fn().mockResolvedValue(undefined),
      stop: jest.fn().mockResolvedValue(undefined),
      delete: jest.fn().mockResolvedValue(undefined),
      executeCommand: jest.fn().mockResolvedValue('command output'),
      getInfo: jest.fn().mockResolvedValue({
        id: 'test-vm-id',
        name: 'test-vm',
        state: MicroVmState.RUNNING,
        createdAt: new Date(),
        updatedAt: new Date(),
        drives: [
          {
            id: 'rootfs',
            path: '/path/to/rootfs.ext4',
            readOnly: false,
            isRootfs: true,
          },
        ],
      }),
    };
    
    // Create mock MicroVmManager
    mockManager = MicroVmManager as jest.Mocked<typeof MicroVmManager>;
    
    // Create snapshot manager
    snapshotManager = new SnapshotManager({
      baseDir: '/tmp/snapshots',
      manager: new MicroVmManager(),
    });
  });
  
  describe('createSnapshot', () => {
    it('should create a snapshot successfully', async () => {
      // Arrange
      const options = {
        name: 'test-snapshot',
        includeMemory: true,
        metadata: { test: 'metadata' },
      };
      
      // Act
      const snapshot = await snapshotManager.createSnapshot(mockMicroVm, options);
      
      // Assert
      expect(snapshot).toBeDefined();
      expect(snapshot.name).toBe(options.name);
      expect(snapshot.microVmId).toBe('test-vm-id');
      expect(snapshot.metadata).toEqual(options.metadata);
      expect(fs.mkdirSync).toHaveBeenCalled();
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('cp'));
      expect(fs.writeFileSync).toHaveBeenCalled(); // For memory snapshot
    });
    
    it('should create a snapshot without memory if not requested', async () => {
      // Arrange
      const options = {
        name: 'test-snapshot',
        includeMemory: false,
      };
      
      // Act
      const snapshot = await snapshotManager.createSnapshot(mockMicroVm, options);
      
      // Assert
      expect(snapshot).toBeDefined();
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('cp'));
      expect(fs.writeFileSync).not.toHaveBeenCalled(); // No memory snapshot
    });
    
    it('should throw an error if rootfs drive is not found', async () => {
      // Arrange
      mockMicroVm.getInfo.mockResolvedValue({
        id: 'test-vm-id',
        name: 'test-vm',
        state: MicroVmState.RUNNING,
        createdAt: new Date(),
        updatedAt: new Date(),
        drives: [], // No rootfs drive
      });
      
      const options = {
        name: 'test-snapshot',
      };
      
      // Act & Assert
      await expect(snapshotManager.createSnapshot(mockMicroVm, options)).rejects.toThrow(ContainerizationError);
      await expect(snapshotManager.createSnapshot(mockMicroVm, options)).rejects.toThrow('Rootfs drive not found');
    });
    
    it('should clean up on error', async () => {
      // Arrange
      (execAsync as jest.Mock).mockRejectedValue(new Error('Command failed'));
      
      const options = {
        name: 'test-snapshot',
      };
      
      // Act & Assert
      await expect(snapshotManager.createSnapshot(mockMicroVm, options)).rejects.toThrow(ContainerizationError);
      expect(fs.rmSync).toHaveBeenCalled();
    });
  });
  
  describe('restoreSnapshot', () => {
    it('should restore a snapshot successfully', async () => {
      // Arrange
      const snapshot = await snapshotManager.createSnapshot(mockMicroVm, { name: 'test-snapshot' });
      
      // Act
      const restoredVm = await snapshotManager.restoreSnapshot(mockMicroVm, snapshot.id);
      
      // Assert
      expect(restoredVm).toBe(mockMicroVm);
      expect(mockMicroVm.stop).toHaveBeenCalled();
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('cp'));
      expect(mockMicroVm.start).toHaveBeenCalled();
    });
    
    it('should throw an error if snapshot is not found', async () => {
      // Act & Assert
      await expect(snapshotManager.restoreSnapshot(mockMicroVm, 'non-existent')).rejects.toThrow(ContainerizationError);
      await expect(snapshotManager.restoreSnapshot(mockMicroVm, 'non-existent')).rejects.toThrow('Snapshot with ID');
    });
    
    it('should not stop the VM if it is already stopped', async () => {
      // Arrange
      mockMicroVm.getState.mockReturnValue(MicroVmState.STOPPED);
      const snapshot = await snapshotManager.createSnapshot(mockMicroVm, { name: 'test-snapshot' });
      
      // Act
      await snapshotManager.restoreSnapshot(mockMicroVm, snapshot.id);
      
      // Assert
      expect(mockMicroVm.stop).not.toHaveBeenCalled();
    });
    
    it('should restore memory snapshot if available', async () => {
      // Arrange
      const snapshot = await snapshotManager.createSnapshot(mockMicroVm, { 
        name: 'test-snapshot',
        includeMemory: true,
      });
      
      // Act
      await snapshotManager.restoreSnapshot(mockMicroVm, snapshot.id);
      
      // Assert
      expect(mockMicroVm.start).toHaveBeenCalled();
    });
  });
  
  describe('deleteSnapshot', () => {
    it('should delete a snapshot successfully', async () => {
      // Arrange
      const snapshot = await snapshotManager.createSnapshot(mockMicroVm, { name: 'test-snapshot' });
      
      // Act
      await snapshotManager.deleteSnapshot(snapshot.id);
      
      // Assert
      expect(fs.rmSync).toHaveBeenCalled();
      expect(snapshotManager.getSnapshot(snapshot.id)).toBeUndefined();
    });
    
    it('should handle non-existent snapshot gracefully', async () => {
      // Act
      await snapshotManager.deleteSnapshot('non-existent');
      
      // Assert - should not throw
      expect(fs.rmSync).not.toHaveBeenCalled();
    });
  });
  
  describe('listSnapshots', () => {
    it('should list all snapshots', async () => {
      // Arrange
      const snapshot1 = await snapshotManager.createSnapshot(mockMicroVm, { name: 'snapshot-1' });
      const snapshot2 = await snapshotManager.createSnapshot(mockMicroVm, { name: 'snapshot-2' });
      
      // Act
      const snapshots = snapshotManager.listSnapshots();
      
      // Assert
      expect(snapshots).toHaveLength(2);
      expect(snapshots).toContainEqual(expect.objectContaining({ id: snapshot1.id }));
      expect(snapshots).toContainEqual(expect.objectContaining({ id: snapshot2.id }));
    });
    
    it('should filter snapshots by MicroVM ID', async () => {
      // Arrange
      const snapshot1 = await snapshotManager.createSnapshot(mockMicroVm, { name: 'snapshot-1' });
      
      // Mock a different MicroVM
      const otherMicroVm = { ...mockMicroVm, getId: jest.fn().mockReturnValue('other-vm-id') };
      const snapshot2 = await snapshotManager.createSnapshot(otherMicroVm, { name: 'snapshot-2' });
      
      // Act
      const snapshots = snapshotManager.listSnapshots('test-vm-id');
      
      // Assert
      expect(snapshots).toHaveLength(1);
      expect(snapshots).toContainEqual(expect.objectContaining({ id: snapshot1.id }));
      expect(snapshots).not.toContainEqual(expect.objectContaining({ id: snapshot2.id }));
    });
  });
  
  describe('cleanup', () => {
    it('should clean up all snapshots', async () => {
      // Arrange
      await snapshotManager.createSnapshot(mockMicroVm, { name: 'snapshot-1' });
      await snapshotManager.createSnapshot(mockMicroVm, { name: 'snapshot-2' });
      
      // Act
      await snapshotManager.cleanup();
      
      // Assert
      expect(fs.rmSync).toHaveBeenCalledTimes(2);
      expect(snapshotManager.listSnapshots()).toHaveLength(0);
    });
    
    it('should handle cleanup errors gracefully', async () => {
      // Arrange
      await snapshotManager.createSnapshot(mockMicroVm, { name: 'snapshot-1' });
      
      // Mock rmSync to throw an error
      (fs.rmSync as jest.Mock).mockImplementation(() => {
        throw new Error('Delete failed');
      });
      
      // Act
      await snapshotManager.cleanup();
      
      // Assert - should not throw
      expect(snapshotManager.listSnapshots()).toHaveLength(0);
    });
  });
});
