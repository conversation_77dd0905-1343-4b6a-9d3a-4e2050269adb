/**
 * MicroVmWarmPool Tests
 * 
 * This file contains tests for the MicroVmWarmPool class.
 */

import { MicroVmWarmPool } from '../warm-pool';
import { MicroVmManager } from '../../core/microvm-manager';
import { IMicroVm } from '../../core/interfaces';
import { MicroVmCreationOptions, MicroVmState } from '../../models';
import { ContainerizationError } from '../../../shared/error-handling';

// Mock dependencies
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock MicroVmManager
jest.mock('../../core/microvm-manager');

describe('MicroVmWarmPool', () => {
  let warmPool: MicroVmWarmPool;
  let mockManager: jest.Mocked<MicroVmManager>;
  let mockMicroVm: jest.Mocked<IMicroVm>;
  
  const mockTemplate: MicroVmCreationOptions = {
    name: 'test-template',
    memSizeMib: 1024,
    vcpuCount: 2,
    rootfs: {
      path: '/path/to/rootfs.ext4',
      readOnly: true,
    },
    kernel: {
      path: '/path/to/vmlinux',
      bootArgs: 'console=ttyS0 reboot=k panic=1 pci=off',
    },
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mock MicroVm
    mockMicroVm = {
      getId: jest.fn().mockReturnValue('test-vm-id'),
      getName: jest.fn().mockReturnValue('test-vm'),
      getState: jest.fn().mockReturnValue(MicroVmState.CREATED),
      configure: jest.fn().mockResolvedValue(undefined),
      start: jest.fn().mockResolvedValue(undefined),
      stop: jest.fn().mockResolvedValue(undefined),
      delete: jest.fn().mockResolvedValue(undefined),
      executeCommand: jest.fn().mockResolvedValue('command output'),
      getInfo: jest.fn().mockResolvedValue({
        id: 'test-vm-id',
        name: 'test-vm',
        state: MicroVmState.CREATED,
        createdAt: new Date(),
        updatedAt: new Date(),
      }),
    };
    
    // Create mock MicroVmManager
    mockManager = MicroVmManager as jest.Mocked<typeof MicroVmManager>;
    mockManager.prototype.createMicroVm = jest.fn().mockResolvedValue(mockMicroVm);
    mockManager.prototype.deleteMicroVm = jest.fn().mockResolvedValue(undefined);
    
    // Create warm pool
    warmPool = new MicroVmWarmPool({
      minPoolSize: 2,
      maxPoolSize: 5,
      initialPoolSize: 3,
      template: mockTemplate,
      manager: new MicroVmManager(),
      preStart: false,
      autoReplenish: true,
    });
  });
  
  describe('initialize', () => {
    it('should initialize the warm pool with the specified number of VMs', async () => {
      // Act
      await warmPool.initialize();
      
      // Assert
      expect(mockManager.prototype.createMicroVm).toHaveBeenCalledTimes(3);
      expect(warmPool.getPoolSize()).toBe(3);
    });
    
    it('should pre-start VMs if preStart is enabled', async () => {
      // Arrange
      warmPool = new MicroVmWarmPool({
        initialPoolSize: 2,
        template: mockTemplate,
        manager: new MicroVmManager(),
        preStart: true,
      });
      
      // Act
      await warmPool.initialize();
      
      // Assert
      expect(mockMicroVm.start).toHaveBeenCalledTimes(2);
    });
    
    it('should throw an error if template is not provided', async () => {
      // Arrange
      warmPool = new MicroVmWarmPool({
        manager: new MicroVmManager(),
      });
      
      // Act & Assert
      await expect(warmPool.initialize()).rejects.toThrow(ContainerizationError);
      await expect(warmPool.initialize()).rejects.toThrow('MicroVM template is required');
    });
    
    it('should handle initialization errors gracefully', async () => {
      // Arrange
      mockManager.prototype.createMicroVm = jest.fn()
        .mockRejectedValueOnce(new Error('Creation failed'))
        .mockResolvedValue(mockMicroVm);
      
      // Act
      await warmPool.initialize();
      
      // Assert
      expect(mockManager.prototype.createMicroVm).toHaveBeenCalledTimes(3);
      expect(warmPool.getPoolSize()).toBe(2); // One failed, two succeeded
    });
  });
  
  describe('acquire', () => {
    it('should acquire a VM from the pool', async () => {
      // Arrange
      await warmPool.initialize();
      
      // Act
      const microvm = await warmPool.acquire();
      
      // Assert
      expect(microvm).toBe(mockMicroVm);
      expect(warmPool.getPoolSize()).toBe(2); // 3 initial - 1 acquired
    });
    
    it('should throw an error if the pool is not initialized', async () => {
      // Act & Assert
      await expect(warmPool.acquire()).rejects.toThrow(ContainerizationError);
      await expect(warmPool.acquire()).rejects.toThrow('not initialized');
    });
    
    it('should create a new VM if the pool is empty', async () => {
      // Arrange
      warmPool = new MicroVmWarmPool({
        initialPoolSize: 0,
        template: mockTemplate,
        manager: new MicroVmManager(),
      });
      await warmPool.initialize();
      
      // Reset mock to verify new calls
      mockManager.prototype.createMicroVm.mockClear();
      
      // Act
      const microvm = await warmPool.acquire();
      
      // Assert
      expect(microvm).toBe(mockMicroVm);
      expect(mockManager.prototype.createMicroVm).toHaveBeenCalledTimes(1);
    });
    
    it('should replenish the pool if autoReplenish is enabled', async () => {
      // Arrange
      warmPool = new MicroVmWarmPool({
        minPoolSize: 3,
        initialPoolSize: 3,
        template: mockTemplate,
        manager: new MicroVmManager(),
        autoReplenish: true,
      });
      await warmPool.initialize();
      
      // Reset mock to verify new calls
      mockManager.prototype.createMicroVm.mockClear();
      
      // Act
      await warmPool.acquire(); // Pool size becomes 2, below minPoolSize of 3
      
      // Assert
      expect(mockManager.prototype.createMicroVm).toHaveBeenCalledTimes(1); // One VM created to replenish
    });
  });
  
  describe('release', () => {
    it('should release a VM back to the pool', async () => {
      // Arrange
      await warmPool.initialize();
      const microvm = await warmPool.acquire();
      
      // Act
      await warmPool.release(microvm);
      
      // Assert
      expect(warmPool.getPoolSize()).toBe(3); // Back to initial size
    });
    
    it('should reset the VM before releasing it back to the pool', async () => {
      // Arrange
      await warmPool.initialize();
      const microvm = await warmPool.acquire();
      
      // Mock VM as running
      mockMicroVm.getState.mockReturnValue(MicroVmState.RUNNING);
      
      // Act
      await warmPool.release(microvm);
      
      // Assert
      expect(mockMicroVm.stop).toHaveBeenCalled();
    });
    
    it('should delete the VM if the pool is full', async () => {
      // Arrange
      warmPool = new MicroVmWarmPool({
        maxPoolSize: 1,
        initialPoolSize: 1,
        template: mockTemplate,
        manager: new MicroVmManager(),
      });
      await warmPool.initialize();
      
      // Create an extra VM to release
      const extraVm = { ...mockMicroVm, getId: jest.fn().mockReturnValue('extra-vm') };
      
      // Act
      await warmPool.release(extraVm);
      
      // Assert
      expect(mockManager.prototype.deleteMicroVm).toHaveBeenCalledWith('extra-vm');
    });
    
    it('should throw an error if the pool is not initialized', async () => {
      // Act & Assert
      await expect(warmPool.release(mockMicroVm)).rejects.toThrow(ContainerizationError);
      await expect(warmPool.release(mockMicroVm)).rejects.toThrow('not initialized');
    });
  });
  
  describe('shutdown', () => {
    it('should delete all VMs in the pool', async () => {
      // Arrange
      await warmPool.initialize();
      
      // Act
      await warmPool.shutdown();
      
      // Assert
      expect(mockManager.prototype.deleteMicroVm).toHaveBeenCalledTimes(3);
      expect(warmPool.getPoolSize()).toBe(0);
    });
    
    it('should handle shutdown errors gracefully', async () => {
      // Arrange
      await warmPool.initialize();
      
      // Mock deleteMicroVm to throw an error
      mockManager.prototype.deleteMicroVm.mockRejectedValue(new Error('Delete failed'));
      
      // Act
      await warmPool.shutdown();
      
      // Assert
      expect(mockManager.prototype.deleteMicroVm).toHaveBeenCalledTimes(3);
      expect(warmPool.getPoolSize()).toBe(0);
    });
  });
  
  describe('getStatus', () => {
    it('should return the current status of the pool', async () => {
      // Arrange
      await warmPool.initialize();
      
      // Act
      const status = warmPool.getStatus();
      
      // Assert
      expect(status).toEqual({
        initialized: true,
        shuttingDown: false,
        poolSize: 3,
        minPoolSize: 2,
        maxPoolSize: 5,
      });
    });
  });
});
