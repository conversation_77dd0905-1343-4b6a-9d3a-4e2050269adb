/**
 * MicroVM Snapshot Manager
 *
 * This module provides functionality for creating and managing snapshots of MicroVMs.
 * It allows for quick state preservation and restoration.
 */

import { logger } from '@/lib/logger';
import { MicroVmManager } from '../core/microvm-manager';
import { IMicroVm } from '../core/interfaces';
import { ContainerizationError } from '../../shared/error-handling';
import { ResourceCleanupRegistry, globalCleanupRegistry } from '../../shared/resource-management';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Snapshot information
 */
export interface SnapshotInfo {
  /**
   * Snapshot ID
   */
  id: string;

  /**
   * Snapshot name
   */
  name: string;

  /**
   * MicroVM ID
   */
  microVmId: string;

  /**
   * Memory snapshot path
   */
  memoryPath: string;

  /**
   * Disk snapshot path
   */
  diskPath: string;

  /**
   * Creation timestamp
   */
  createdAt: Date;

  /**
   * Snapshot metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Snapshot creation options
 */
export interface SnapshotOptions {
  /**
   * Snapshot name
   */
  name: string;

  /**
   * Whether to include memory state
   */
  includeMemory?: boolean;

  /**
   * Snapshot metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Snapshot manager options
 */
export interface SnapshotManagerOptions {
  /**
   * Base directory for snapshots
   */
  baseDir?: string;

  /**
   * MicroVM manager
   */
  manager?: MicroVmManager;

  /**
   * Resource cleanup registry
   */
  cleanupRegistry?: ResourceCleanupRegistry;
}

/**
 * MicroVM snapshot manager
 */
export class SnapshotManager {
  private readonly options: SnapshotManagerOptions;
  private readonly manager: MicroVmManager;
  private readonly snapshots: Map<string, SnapshotInfo> = new Map();

  /**
   * Create a new snapshot manager
   * @param options Snapshot manager options
   */
  constructor(options: SnapshotManagerOptions = {}) {
    this.options = {
      baseDir: path.join(process.cwd(), 'data', 'snapshots'),
      cleanupRegistry: globalCleanupRegistry,
      ...options,
    };

    // Create or use provided manager
    this.manager = this.options.manager || new MicroVmManager();

    // Create the base directory if it doesn't exist
    fs.mkdirSync(this.options.baseDir!, { recursive: true });

    // Register cleanup function
    if (this.options.cleanupRegistry) {
      this.options.cleanupRegistry.register('microvm-snapshot-manager', async () => {
        await this.cleanup();
      });
    }
  }

  /**
   * Create a snapshot of a MicroVM
   * @param microVm MicroVM to snapshot
   * @param options Snapshot options
   * @returns Snapshot information
   */
  async createSnapshot(microVm: IMicroVm, options: SnapshotOptions): Promise<SnapshotInfo> {
    const microVmId = microVm.getId();
    const microVmInfo = await microVm.getInfo();
    
    // Generate snapshot ID
    const snapshotId = uuidv4();
    
    // Create snapshot directory
    const snapshotDir = path.join(this.options.baseDir!, snapshotId);
    fs.mkdirSync(snapshotDir, { recursive: true });
    
    // Paths for memory and disk snapshots
    const memoryPath = path.join(snapshotDir, 'memory.bin');
    const diskPath = path.join(snapshotDir, 'disk.ext4');
    
    try {
      logger.info(`Creating snapshot '${options.name}' for MicroVM '${microVmInfo.name}' (${microVmId})`);
      
      // Create disk snapshot
      await this.createDiskSnapshot(microVm, diskPath);
      
      // Create memory snapshot if requested
      if (options.includeMemory) {
        await this.createMemorySnapshot(microVm, memoryPath);
      }
      
      // Create snapshot info
      const snapshotInfo: SnapshotInfo = {
        id: snapshotId,
        name: options.name,
        microVmId,
        memoryPath,
        diskPath,
        createdAt: new Date(),
        metadata: options.metadata,
      };
      
      // Store snapshot info
      this.snapshots.set(snapshotId, snapshotInfo);
      
      logger.info(`Created snapshot '${options.name}' (${snapshotId}) for MicroVM '${microVmInfo.name}' (${microVmId})`);
      
      return snapshotInfo;
    } catch (error) {
      // Clean up on error
      try {
        if (fs.existsSync(snapshotDir)) {
          fs.rmSync(snapshotDir, { recursive: true, force: true });
        }
      } catch (cleanupError) {
        logger.error(`Failed to clean up snapshot directory: ${cleanupError}`);
      }
      
      throw new ContainerizationError(`Failed to create snapshot: ${error}`, {
        cause: error,
      });
    }
  }

  /**
   * Restore a MicroVM from a snapshot
   * @param microVm MicroVM to restore
   * @param snapshotId Snapshot ID
   * @returns Restored MicroVM
   */
  async restoreSnapshot(microVm: IMicroVm, snapshotId: string): Promise<IMicroVm> {
    const snapshot = this.snapshots.get(snapshotId);
    
    if (!snapshot) {
      throw new ContainerizationError(`Snapshot with ID '${snapshotId}' not found`);
    }
    
    const microVmId = microVm.getId();
    const microVmInfo = await microVm.getInfo();
    
    try {
      logger.info(`Restoring MicroVM '${microVmInfo.name}' (${microVmId}) from snapshot '${snapshot.name}' (${snapshotId})`);
      
      // Stop the MicroVM if it's running
      if (microVm.getState() === 'RUNNING') {
        await microVm.stop();
      }
      
      // Restore disk snapshot
      await this.restoreDiskSnapshot(microVm, snapshot.diskPath);
      
      // Restore memory snapshot if available
      if (fs.existsSync(snapshot.memoryPath)) {
        await this.restoreMemorySnapshot(microVm, snapshot.memoryPath);
      } else {
        // Start the MicroVM if memory snapshot is not available
        await microVm.start();
      }
      
      logger.info(`Restored MicroVM '${microVmInfo.name}' (${microVmId}) from snapshot '${snapshot.name}' (${snapshotId})`);
      
      return microVm;
    } catch (error) {
      throw new ContainerizationError(`Failed to restore snapshot: ${error}`, {
        cause: error,
      });
    }
  }

  /**
   * Delete a snapshot
   * @param snapshotId Snapshot ID
   */
  async deleteSnapshot(snapshotId: string): Promise<void> {
    const snapshot = this.snapshots.get(snapshotId);
    
    if (!snapshot) {
      logger.warn(`Snapshot with ID '${snapshotId}' not found`);
      return;
    }
    
    try {
      logger.info(`Deleting snapshot '${snapshot.name}' (${snapshotId})`);
      
      // Delete snapshot directory
      const snapshotDir = path.dirname(snapshot.diskPath);
      if (fs.existsSync(snapshotDir)) {
        fs.rmSync(snapshotDir, { recursive: true, force: true });
      }
      
      // Remove from map
      this.snapshots.delete(snapshotId);
      
      logger.info(`Deleted snapshot '${snapshot.name}' (${snapshotId})`);
    } catch (error) {
      throw new ContainerizationError(`Failed to delete snapshot: ${error}`, {
        cause: error,
      });
    }
  }

  /**
   * Get snapshot information
   * @param snapshotId Snapshot ID
   * @returns Snapshot information
   */
  getSnapshot(snapshotId: string): SnapshotInfo | undefined {
    return this.snapshots.get(snapshotId);
  }

  /**
   * List all snapshots
   * @param microVmId Optional MicroVM ID to filter snapshots
   * @returns List of snapshots
   */
  listSnapshots(microVmId?: string): SnapshotInfo[] {
    if (microVmId) {
      return Array.from(this.snapshots.values()).filter(snapshot => snapshot.microVmId === microVmId);
    }
    return Array.from(this.snapshots.values());
  }

  /**
   * Clean up all snapshots
   */
  async cleanup(): Promise<void> {
    logger.info('Cleaning up all snapshots');
    
    const deletePromises = Array.from(this.snapshots.keys()).map(snapshotId => {
      return this.deleteSnapshot(snapshotId).catch(error => {
        logger.error(`Failed to delete snapshot '${snapshotId}' during cleanup: ${error}`);
      });
    });
    
    await Promise.all(deletePromises);
    
    logger.info('Snapshot cleanup complete');
  }

  /**
   * Create a disk snapshot
   * @param microVm MicroVM to snapshot
   * @param diskPath Path to save the disk snapshot
   * @private
   */
  private async createDiskSnapshot(microVm: IMicroVm, diskPath: string): Promise<void> {
    const microVmInfo = await microVm.getInfo();
    
    // Get the rootfs path
    const rootfsDrive = microVmInfo.drives.find(drive => drive.isRootfs);
    
    if (!rootfsDrive) {
      throw new ContainerizationError('Rootfs drive not found');
    }
    
    // Copy the rootfs to the snapshot path
    await execAsync(`cp ${rootfsDrive.path} ${diskPath}`);
  }

  /**
   * Create a memory snapshot
   * @param microVm MicroVM to snapshot
   * @param memoryPath Path to save the memory snapshot
   * @private
   */
  private async createMemorySnapshot(microVm: IMicroVm, memoryPath: string): Promise<void> {
    // Note: This is a placeholder for the actual memory snapshot implementation
    // In a real implementation, this would use the Firecracker API to create a memory snapshot
    
    // For now, we'll just create an empty file
    fs.writeFileSync(memoryPath, '');
    
    logger.warn('Memory snapshot is not fully implemented yet');
  }

  /**
   * Restore a disk snapshot
   * @param microVm MicroVM to restore
   * @param diskPath Path to the disk snapshot
   * @private
   */
  private async restoreDiskSnapshot(microVm: IMicroVm, diskPath: string): Promise<void> {
    const microVmInfo = await microVm.getInfo();
    
    // Get the rootfs path
    const rootfsDrive = microVmInfo.drives.find(drive => drive.isRootfs);
    
    if (!rootfsDrive) {
      throw new ContainerizationError('Rootfs drive not found');
    }
    
    // Copy the snapshot to the rootfs path
    await execAsync(`cp ${diskPath} ${rootfsDrive.path}`);
  }

  /**
   * Restore a memory snapshot
   * @param microVm MicroVM to restore
   * @param memoryPath Path to the memory snapshot
   * @private
   */
  private async restoreMemorySnapshot(microVm: IMicroVm, memoryPath: string): Promise<void> {
    // Note: This is a placeholder for the actual memory snapshot implementation
    // In a real implementation, this would use the Firecracker API to restore a memory snapshot
    
    // For now, we'll just start the VM
    await microVm.start();
    
    logger.warn('Memory snapshot restoration is not fully implemented yet');
  }
}
