/**
 * MicroVM Warm Pool
 *
 * This module provides functionality for managing a pool of pre-created MicroVMs
 * to reduce startup time for new VMs.
 */

import { logger } from '@/lib/logger';
import { MicroVmManager } from '../core/microvm-manager';
import { MicroVmCreationOptions } from '../models';
import { IMicroVm } from '../core/interfaces';
import { ContainerizationError } from '../../shared/error-handling';
import { ResourceCleanupRegistry, globalCleanupRegistry } from '../../shared/resource-management';

/**
 * MicroVM warm pool options
 */
export interface MicroVmWarmPoolOptions {
  /**
   * Minimum pool size
   */
  minPoolSize?: number;

  /**
   * Maximum pool size
   */
  maxPoolSize?: number;

  /**
   * Initial pool size
   */
  initialPoolSize?: number;

  /**
   * MicroVM creation options template
   */
  template?: MicroVmCreationOptions;

  /**
   * MicroVM manager
   */
  manager?: MicroVmManager;

  /**
   * Resource cleanup registry
   */
  cleanupRegistry?: ResourceCleanupRegistry;

  /**
   * Whether to pre-start VMs
   */
  preStart?: boolean;

  /**
   * Whether to auto-replenish the pool when VMs are acquired
   */
  autoReplenish?: boolean;
}

/**
 * MicroVM warm pool
 */
export class MicroVmWarmPool {
  private readonly options: MicroVmWarmPoolOptions;
  private readonly pool: IMicroVm[] = [];
  private readonly manager: MicroVmManager;
  private isInitialized: boolean = false;
  private isShuttingDown: boolean = false;

  /**
   * Create a new MicroVM warm pool
   * @param options Warm pool options
   */
  constructor(options: MicroVmWarmPoolOptions = {}) {
    this.options = {
      minPoolSize: 1,
      maxPoolSize: 10,
      initialPoolSize: 3,
      preStart: false,
      autoReplenish: true,
      cleanupRegistry: globalCleanupRegistry,
      ...options,
    };

    // Create or use provided manager
    this.manager = this.options.manager || new MicroVmManager();

    // Register cleanup function
    if (this.options.cleanupRegistry) {
      this.options.cleanupRegistry.register('microvm-warm-pool', async () => {
        await this.shutdown();
      });
    }
  }

  /**
   * Initialize the warm pool
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('MicroVM warm pool is already initialized');
      return;
    }

    if (!this.options.template) {
      throw new ContainerizationError('MicroVM template is required for initialization');
    }

    logger.info(`Initializing MicroVM warm pool with ${this.options.initialPoolSize} VMs`);

    // Create initial VMs
    const initialSize = this.options.initialPoolSize!;
    const createPromises: Promise<void>[] = [];

    for (let i = 0; i < initialSize; i++) {
      createPromises.push(this.createAndAddToPool());
    }

    await Promise.all(createPromises);
    this.isInitialized = true;

    logger.info(`MicroVM warm pool initialized with ${this.pool.length} VMs`);
  }

  /**
   * Create a new MicroVM and add it to the pool
   * @private
   */
  private async createAndAddToPool(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    try {
      // Create a new MicroVM from the template
      const microvm = await this.manager.createMicroVm({
        ...this.options.template!,
        name: `warm-pool-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      });

      // Pre-start the VM if enabled
      if (this.options.preStart) {
        await microvm.start();
      }

      // Add to pool
      this.pool.push(microvm);

      logger.debug(`Added new MicroVM to warm pool: ${microvm.getId()}`);
    } catch (error) {
      logger.error(`Failed to create MicroVM for warm pool: ${error}`);
    }
  }

  /**
   * Acquire a MicroVM from the pool
   * @returns A MicroVM from the pool
   */
  async acquire(): Promise<IMicroVm> {
    if (!this.isInitialized) {
      throw new ContainerizationError('MicroVM warm pool is not initialized');
    }

    if (this.isShuttingDown) {
      throw new ContainerizationError('MicroVM warm pool is shutting down');
    }

    if (this.pool.length === 0) {
      logger.warn('MicroVM warm pool is empty, creating a new VM');
      await this.createAndAddToPool();

      if (this.pool.length === 0) {
        throw new ContainerizationError('Failed to create a new MicroVM for the pool');
      }
    }

    // Get a VM from the pool
    const microvm = this.pool.pop()!;

    logger.info(`Acquired MicroVM from warm pool: ${microvm.getId()}`);

    // Replenish the pool if needed
    if (this.options.autoReplenish && this.pool.length < this.options.minPoolSize!) {
      this.replenishPool().catch(error => {
        logger.error(`Failed to replenish warm pool: ${error}`);
      });
    }

    return microvm;
  }

  /**
   * Release a MicroVM back to the pool
   * @param microvm MicroVM to release
   */
  async release(microvm: IMicroVm): Promise<void> {
    if (!this.isInitialized) {
      throw new ContainerizationError('MicroVM warm pool is not initialized');
    }

    if (this.isShuttingDown) {
      // If shutting down, just delete the VM
      await this.manager.deleteMicroVm(microvm.getId());
      return;
    }

    if (this.pool.length >= this.options.maxPoolSize!) {
      logger.info(`Warm pool is full, deleting MicroVM: ${microvm.getId()}`);
      await this.manager.deleteMicroVm(microvm.getId());
      return;
    }

    try {
      // Reset the VM to a clean state
      await this.resetMicroVm(microvm);

      // Add back to the pool
      this.pool.push(microvm);

      logger.info(`Released MicroVM back to warm pool: ${microvm.getId()}`);
    } catch (error) {
      logger.error(`Failed to release MicroVM to warm pool: ${error}`);
      
      // If reset fails, delete the VM
      try {
        await this.manager.deleteMicroVm(microvm.getId());
      } catch (deleteError) {
        logger.error(`Failed to delete MicroVM: ${deleteError}`);
      }
    }
  }

  /**
   * Reset a MicroVM to a clean state
   * @param microvm MicroVM to reset
   * @private
   */
  private async resetMicroVm(microvm: IMicroVm): Promise<void> {
    // If the VM is running, stop it
    if (microvm.getState() === 'RUNNING') {
      await microvm.stop();
    }

    // If pre-start is enabled, start the VM
    if (this.options.preStart) {
      await microvm.start();
    }
  }

  /**
   * Replenish the pool to the minimum size
   * @private
   */
  private async replenishPool(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    const currentSize = this.pool.length;
    const minSize = this.options.minPoolSize!;

    if (currentSize >= minSize) {
      return;
    }

    logger.info(`Replenishing warm pool from ${currentSize} to ${minSize} VMs`);

    const createPromises: Promise<void>[] = [];
    for (let i = currentSize; i < minSize; i++) {
      createPromises.push(this.createAndAddToPool());
    }

    await Promise.all(createPromises);
  }

  /**
   * Shutdown the warm pool
   */
  async shutdown(): Promise<void> {
    if (!this.isInitialized || this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    logger.info(`Shutting down MicroVM warm pool with ${this.pool.length} VMs`);

    // Delete all VMs in the pool
    const deletePromises = this.pool.map(microvm => {
      return this.manager.deleteMicroVm(microvm.getId()).catch(error => {
        logger.error(`Failed to delete MicroVM during shutdown: ${error}`);
      });
    });

    await Promise.all(deletePromises);
    this.pool.length = 0;
    this.isInitialized = false;
    this.isShuttingDown = false;

    logger.info('MicroVM warm pool shutdown complete');
  }

  /**
   * Get the current pool size
   * @returns Current pool size
   */
  getPoolSize(): number {
    return this.pool.length;
  }

  /**
   * Get the pool status
   * @returns Pool status
   */
  getStatus(): {
    initialized: boolean;
    shuttingDown: boolean;
    poolSize: number;
    minPoolSize: number;
    maxPoolSize: number;
  } {
    return {
      initialized: this.isInitialized,
      shuttingDown: this.isShuttingDown,
      poolSize: this.pool.length,
      minPoolSize: this.options.minPoolSize!,
      maxPoolSize: this.options.maxPoolSize!,
    };
  }
}
