/**
 * Enhanced Firecracker Command
 * 
 * This module provides a wrapper around the Firecracker binary to start and manage
 * Firecracker processes.
 */

import * as child_process from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { ResourceCleanupRegistry } from '../../shared/resource-management';
import { ContainerizationError } from '../../shared/error-handling';

// Simple console-based logger implementation
const logger = {
  info: (...args: any[]) => console.log('[INFO]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
  debug: (...args: any[]) => console.log('[DEBUG]', ...args)
};

/** 
 * FirecrackerCommandOptions defines options for starting a Firecracker process
 */
export interface FirecrackerCommandOptions {
  /**
   * Path to the Firecracker binary
   * If not specified, will try to find it in common locations
   */
  binaryPath?: string;

  /**
   * Path to the socket file for the API server
   * If not specified, a temporary file will be created
   */
  socketPath?: string;

  /**
   * Path to the log file
   * If not specified, logs will be directed to stdout/stderr
   */
  logPath?: string;

  /**
   * Path to the metrics file
   */
  metricsPath?: string;

  /**
   * Log level (error, warning, info, debug)
   */
  logLevel?: string;

  /**
   * Resource cleanup registry
   */
  cleanupRegistry?: ResourceCleanupRegistry;

  /**
   * Additional command-line arguments to pass to Firecracker
   */
  extraArgs?: string[];
}

/**
 * FirecrackerCommandResult defines the result of starting a Firecracker process
 */
export interface FirecrackerCommandResult {
  /**
   * Process ID of the Firecracker process
   */
  pid: number;

  /**
   * Path to the socket file for the API server
   */
  socketPath: string;

  /**
   * Path to the log file
   */
  logPath?: string;

  /**
   * Path to the metrics file
   */
  metricsPath?: string;
}

/**
 * Enhanced Firecracker command
 */
export class EnhancedFirecrackerCommand {
  private readonly options: FirecrackerCommandOptions;
  private process: child_process.ChildProcess | undefined;
  private socketPath: string | undefined;
  private tempDir: string | undefined;
  private resourceCleanupHandlerRegistered = false;

  /**
   * Create a new Firecracker command
   * @param options Command options
   */
  constructor(options: FirecrackerCommandOptions = {}) {
    this.options = options;
  }

  /**
   * Start the Firecracker process
   * @returns Process information
   */
  async start(): Promise<FirecrackerCommandResult> {
    // Find the Firecracker binary
    const binaryPath = this.findFirecrackerBinary();

    // Create socket path if not provided
    if (!this.options.socketPath) {
      this.tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'fc-'));
      this.socketPath = path.join(this.tempDir, 'firecracker.sock');
    } else {
      this.socketPath = this.options.socketPath;
    }

    // Ensure socket does not exist yet
    if (fs.existsSync(this.socketPath)) {
      fs.unlinkSync(this.socketPath);
    }

    // Ensure parent directory exists
    const socketDir = path.dirname(this.socketPath);
    if (!fs.existsSync(socketDir)) {
      fs.mkdirSync(socketDir, { recursive: true });
    }

    // Prepare command arguments
    const args = ['--api-sock', this.socketPath];

    // Add log file argument if provided
    if (this.options.logPath) {
      const logDir = path.dirname(this.options.logPath);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }

      args.push('--log-path', this.options.logPath);
    }

    // Add log level if provided
    if (this.options.logLevel) {
      args.push('--level', this.options.logLevel);
    }

    // Add metrics file argument if provided
    if (this.options.metricsPath) {
      const metricsDir = path.dirname(this.options.metricsPath);
      if (!fs.existsSync(metricsDir)) {
        fs.mkdirSync(metricsDir, { recursive: true });
      }

      args.push('--metrics-path', this.options.metricsPath);
    }

    // Add extra arguments if provided
    if (this.options.extraArgs) {
      args.push(...this.options.extraArgs);
    }

    // Start the Firecracker process
    logger.info(`Starting Firecracker process: ${binaryPath} ${args.join(' ')}`);

    const process = child_process.spawn(binaryPath, args, {
      detached: false,
      stdio: ['ignore', 'pipe', 'pipe'],
    });

    // Store the process
    this.process = process;

    // Handle process output
    if (process.stdout) {
      process.stdout.on('data', (data) => {
        logger.info(`Firecracker stdout: ${data.toString().trim()}`);
      });
    }

    if (process.stderr) {
      process.stderr.on('data', (data) => {
        logger.warn(`Firecracker stderr: ${data.toString().trim()}`);
      });
    }

    // Handle process exit
    process.on('exit', (code, signal) => {
      if (code !== 0) {
        logger.warn(`Firecracker process exited with code ${code}, signal ${signal}`);
      } else {
        logger.info(`Firecracker process exited with code ${code}, signal ${signal}`);
      }

      this.process = undefined;
    });

    // Handle process error
    process.on('error', (error) => {
      logger.error(`Firecracker process error: ${error.message}`);
      this.process = undefined;
    });

    // Register cleanup handler
    if (this.options.cleanupRegistry && !this.resourceCleanupHandlerRegistered) {
      this.options.cleanupRegistry.register(`firecracker-command-${this.socketPath}`, async () => {
        await this.cleanup();
      });

      this.resourceCleanupHandlerRegistered = true;
    }

    // Wait for the socket file to be created
    const socketCreationTimeout = 5000; // 5 seconds
    const socketCreationInterval = 100; // 100 milliseconds

    const socketCreationStartTime = Date.now();
    let socketExists = false;

    while (Date.now() - socketCreationStartTime < socketCreationTimeout) {
      if (fs.existsSync(this.socketPath)) {
        socketExists = true;
        break;
      }

      await new Promise((resolve) => setTimeout(resolve, socketCreationInterval));
    }

    if (!socketExists) {
      throw new ContainerizationError(`Socket file ${this.socketPath} was not created within the timeout period`);
    }

    // Make sure the process is still running
    if (!this.process || this.process.exitCode !== null) {
      throw new ContainerizationError('Firecracker process exited unexpectedly');
    }

    // Return the process information
    const result: FirecrackerCommandResult = {
      pid: this.process.pid!,
      socketPath: this.socketPath,
      logPath: this.options.logPath,
      metricsPath: this.options.metricsPath,
    };

    return result;
  }

  /**
   * Stop the Firecracker process
   */
  async stop(): Promise<void> {
    if (this.process && this.process.exitCode === null) {
      logger.info(`Stopping Firecracker process: ${this.process.pid}`);

      // Try to terminate gracefully
      this.process.kill('SIGTERM');

      // Wait for the process to exit
      await new Promise<void>((resolve) => {
        if (!this.process || this.process.exitCode !== null) {
          resolve();
          return;
        }

        const timeout = setTimeout(() => {
          if (this.process && this.process.exitCode === null) {
            logger.warn(`Firecracker process ${this.process.pid} did not exit, killing with SIGKILL`);
            this.process.kill('SIGKILL');
          }
          resolve();
        }, 5000);

        this.process.on('exit', () => {
          clearTimeout(timeout);
          resolve();
        });
      });
    }

    this.process = undefined;
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    // Stop the process
    await this.stop();

    // Clean up socket file
    if (this.socketPath && fs.existsSync(this.socketPath)) {
      try {
        fs.unlinkSync(this.socketPath);
      } catch (error) {
        logger.warn(`Failed to delete socket file ${this.socketPath}: ${error}`);
      }
    }

    // Clean up temporary directory
    if (this.tempDir && fs.existsSync(this.tempDir)) {
      try {
        fs.rmdirSync(this.tempDir, { recursive: true });
      } catch (error) {
        logger.warn(`Failed to delete temporary directory ${this.tempDir}: ${error}`);
      }
    }
  }

  /**
   * Find the Firecracker binary
   */
  private findFirecrackerBinary(): string {
    // Use the provided binary path
    if (this.options.binaryPath) {
      if (!fs.existsSync(this.options.binaryPath)) {
        throw new ContainerizationError(`Firecracker binary not found at ${this.options.binaryPath}`);
      }

      return this.options.binaryPath;
    }

    // Try to find the binary in common locations
    const commonLocations = [
      '/usr/bin/firecracker',
      '/usr/local/bin/firecracker',
      '/opt/firecracker/firecracker',
      path.join(process.cwd(), 'bin', 'firecracker'),
      path.join(os.homedir(), '.local', 'bin', 'firecracker'),
    ];

    for (const location of commonLocations) {
      if (fs.existsSync(location)) {
        return location;
      }
    }

    throw new ContainerizationError('Firecracker binary not found');
  }
}
