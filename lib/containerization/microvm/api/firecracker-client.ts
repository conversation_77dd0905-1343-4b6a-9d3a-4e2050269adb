/**
 * Firecracker API Client
 *
 * This module provides a client for interacting with the Firecracker API.
 */

import * as http from 'http';
import * as net from 'net';
import { ContainerizationError } from '../../shared/error-handling';

// Simple console-based logger implementation
const logger = {
  info: (...args: any[]) => console.log('[INFO]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
  debug: (...args: any[]) => console.log('[DEBUG]', ...args)
};

/**
 * Firecracker API client options
 */
export interface FirecrackerClientOptions {
  /**
   * Path to the Firecracker API socket
   */
  socketPath: string;

  /**
   * Request timeout in milliseconds
   */
  timeout?: number;
}

  /**
 * Extended Error interface with code property
   */
interface NodeError extends Error {
  code?: string;
}

/**
 * Firecracker API client
 */
export class FirecrackerClient {
  private readonly options: FirecrackerClientOptions;

  /**
   * Create a new Firecracker API client
   * @param options Client options
   */
  constructor(options: FirecrackerClientOptions) {
    this.options = {
      timeout: 5000,
      ...options,
    };
  }

  /**
   * Send a GET request to the Firecracker API
   * @param path API path
   * @returns Response data
   */
  async get<T = any>(path: string): Promise<T> {
    logger.debug(`Firecracker API request: GET ${path}`);
    return this.request<T>('GET', path);
  }

  /**
   * Send a PUT request to the Firecracker API
   * @param path API path
   * @param data Request data
   * @returns Response data
   */
  async put<T = any>(path: string, data: any): Promise<T> {
    logger.debug(`Firecracker API request: PUT ${path}`);
    return this.request<T>('PUT', path, data);
  }

  /**
   * Send a PATCH request to the Firecracker API
   * @param path API path
   * @param data Request data
   * @returns Response data
   */
  async patch<T = any>(path: string, data: any): Promise<T> {
    logger.debug(`Firecracker API request: PATCH ${path}`);
    return this.request<T>('PATCH', path, data);
  }

  /**
   * Send a DELETE request to the Firecracker API
   * @param path API path
   * @returns Response data
   */
  async delete<T = any>(path: string): Promise<T> {
    logger.debug(`Firecracker API request: DELETE ${path}`);
    return this.request<T>('DELETE', path);
  }

  /**
   * Send a request to the Firecracker API
   * @param method HTTP method
   * @param path API path
   * @param data Request data
   * @returns Response data
   */
  private async request<T = any>(method: string, path: string, data?: any): Promise<T> {
    // Ensure path starts with a forward slash
    if (path && !path.startsWith('/')) {
      path = '/' + path;
    }

    return new Promise<T>((resolve, reject) => {
      // Serialize request data
      const requestData = data ? JSON.stringify(data) : '';

      // Prepare request options
      const options: http.RequestOptions = {
        socketPath: this.options.socketPath,
        method,
        path,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Content-Length': Buffer.byteLength(requestData),
        },
        timeout: this.options.timeout,
      };

      // Create request
      const req = http.request(options, (res) => {
        // Collect response data
        const chunks: Buffer[] = [];
        res.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
        res.on('end', () => {
          const body = Buffer.concat(chunks).toString();

          // Check if the response is successful
          if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
            // Parse and return response data
            if (body && body.length > 0) {
              try {
                resolve(JSON.parse(body) as T);
              } catch (error) {
                // If response is not valid JSON, return the raw body
                resolve(body as unknown as T);
              }
            } else {
              // Return empty object for empty responses
              resolve({} as T);
            }
          } else {
            // Handle error responses
            logger.error(`Firecracker API error: ${res.statusCode} ${res.statusMessage}`);
            logger.error(`Response body: ${body}`);
            reject(new ContainerizationError(`Firecracker API error: ${res.statusCode} ${res.statusMessage}`));
          }
        });
      });

      // Handle request errors
      req.on('error', (error: NodeError) => {
        if (error.code === 'ECONNREFUSED') {
          logger.error(`Firecracker API connection refused: ${this.options.socketPath}`);
          reject(new ContainerizationError(`Firecracker API connection refused: ${this.options.socketPath}`));
        } else if (error.code === 'ENOENT') {
          logger.error(`Firecracker API socket not found: ${this.options.socketPath}`);
          reject(new ContainerizationError(`Firecracker API socket not found: ${this.options.socketPath}`));
        } else {
          logger.error(`Firecracker API error: ${error.message}`);
          reject(new ContainerizationError(`Firecracker API error: ${error.message}`));
        }
      });

      // Handle request timeout
      req.on('timeout', () => {
        req.destroy();
        logger.error(`Firecracker API request timed out: ${method} ${path}`);
        reject(new ContainerizationError(`Firecracker API request timed out: ${method} ${path}`));
      });

      // Send request data
      if (requestData) {
        req.write(requestData);
      }

      // End request
      req.end();
    });
  }

  /**
   * Check if the Firecracker API is available
   * @returns Whether the API is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      await this.get('/');
      return true;
    } catch (error) {
      return false;
    }
  }
}
