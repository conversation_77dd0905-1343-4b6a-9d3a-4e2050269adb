/**
 * Enhanced Firecracker Command
 *
 * This module provides an enhanced wrapper for executing Firecracker commands
 * with improved error handling, retries, and fallbacks.
 */

import { logger } from '@/lib/logger';
import {
  FirecrackerCommand,
  FirecrackerCommandOptions,
  FirecrackerCommandResult
} from './firecracker-command';
import { retry, RetryOptions } from '../../shared/retry';
import { withFallback, FallbackOptions } from '../../shared/fallback';
import {
  ContainerizationError,
  logError
} from '../../shared/error-handling';
import { globalCleanupRegistry } from '../../shared/resource-management';
import { performHealthCheck, createConnectivityCheck } from '../../shared/health-check';
import { Jailer, JailerConfig } from '../security/jailer';
import { SeccompFilterManager, SeccompLevel } from '../security/seccomp';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * Enhanced Firecracker command options
 */
export interface EnhancedFirecrackerCommandOptions extends FirecrackerCommandOptions {
  /**
   * Retry options
   */
  retry?: Partial<RetryOptions>;

  /**
   * Fallback options
   */
  fallback?: Partial<FallbackOptions>;

  /**
   * Alternative binary paths to try if the primary binary is not available
   */
  alternativeBinaryPaths?: string[];

  /**
   * Alternative socket paths to try if the primary socket path is not available
   */
  alternativeSocketPaths?: string[];

  /**
   * Whether to perform health checks after starting
   */
  performHealthCheck?: boolean;

  /**
   * Health check timeout in milliseconds
   */
  healthCheckTimeoutMs?: number;

  /**
   * Whether to use the jailer
   */
  useJailer?: boolean;

  /**
   * Jailer configuration
   */
  jailerConfig?: Partial<JailerConfig>;

  /**
   * Whether to use seccomp filtering
   */
  useSeccomp?: boolean;

  /**
   * Seccomp filter level
   */
  seccompLevel?: SeccompLevel;

  /**
   * Custom seccomp filter JSON
   */
  customSeccompFilter?: string;
}

/**
 * Enhanced Firecracker command
 */
export class EnhancedFirecrackerCommand {
  private readonly options: EnhancedFirecrackerCommandOptions;
  private command: FirecrackerCommand | null = null;
  private result: FirecrackerCommandResult | null = null;
  private jailer: Jailer | null = null;
  private seccompFilterManager: SeccompFilterManager | null = null;

  /**
   * Create a new enhanced Firecracker command
   * @param options Command options
   */
  constructor(options: EnhancedFirecrackerCommandOptions) {
    this.options = {
      ...options,
      binaryPath: this.findFirecrackerBinary(),
      socketPath: this.generateSocketPath(),
      cleanupRegistry: globalCleanupRegistry,
      performHealthCheck: true,
      healthCheckTimeoutMs: 5000,
      useJailer: options.useJailer || false,
      useSeccomp: options.useSeccomp || false,
      seccompLevel: options.seccompLevel || SeccompLevel.BASIC,
    };

    // Initialize seccomp filter manager if needed
    if (this.options.useSeccomp) {
      this.seccompFilterManager = new SeccompFilterManager();
    }

    // Initialize jailer if needed
    if (this.options.useJailer) {
      this.jailer = new Jailer(this.options.jailerConfig);
    }
  }

  /**
   * Find the Firecracker binary
   * @returns Path to the Firecracker binary
   */
  private findFirecrackerBinary(): string {
    // Common locations for the Firecracker binary
    const commonPaths = [
      '/usr/bin/firecracker',
      '/usr/local/bin/firecracker',
      '/opt/firecracker/firecracker',
      path.join(os.homedir(), 'firecracker'),
    ];

    return commonPaths[0]; // Default to the first path
  }

  /**
   * Generate a unique socket path
   * @returns Socket path
   */
  private generateSocketPath(): string {
    const tmpDir = os.tmpdir();
    const socketDir = path.join(tmpDir, 'firecracker-sockets');
    const socketName = `firecracker-${Date.now()}-${Math.floor(Math.random() * 1000)}.sock`;
    return path.join(socketDir, socketName);
  }

  /**
   * Start the Firecracker process with retries and fallbacks
   * @returns Command result
   */
  async start(): Promise<FirecrackerCommandResult> {
    // Create a function to start with the primary configuration
    const startWithPrimaryConfig = async (): Promise<FirecrackerCommandResult> => {
      // Create seccomp filter if needed
      let seccompFilterPath: string | undefined;
      if (this.options.useSeccomp && this.seccompFilterManager) {
        try {
          logger.info('Creating seccomp filter');
          const filter = await this.seccompFilterManager.createFilter({
            level: this.options.seccompLevel,
            customFilter: this.options.customSeccompFilter,
          });
          seccompFilterPath = filter.filePath;
          logger.info(`Created seccomp filter: ${filter.id} (${filter.level}) at ${filter.filePath}`);
        } catch (error) {
          logger.warn(`Failed to create seccomp filter: ${error}`);
        }
      }

      // Start with jailer if enabled
      if (this.options.useJailer && this.jailer) {
        try {
          logger.info('Starting Firecracker with jailer');

          // Start jailer
          const jailerResult = await this.jailer.start();

          // Create Firecracker command with jailer socket path
          this.command = new FirecrackerCommand({
            binaryPath: this.options.binaryPath,
            socketPath: jailerResult.socketPath,
            logPath: this.options.logPath,
            logLevel: this.options.logLevel,
            metricsPath: this.options.metricsPath,
            daemonize: false, // Jailer already daemonizes
            cleanupRegistry: this.options.cleanupRegistry,
          });

          // Return jailer result as command result
          return {
            pid: jailerResult.pid,
            socketPath: jailerResult.socketPath,
            process: {} as any, // Jailer doesn't provide a process object
          };
        } catch (error) {
          logger.warn(`Failed to start with jailer: ${error}`);
          logger.warn('Falling back to standard Firecracker');
        }
      }

      // Start with standard Firecracker
      const commandOptions: FirecrackerCommandOptions = {
        binaryPath: this.options.binaryPath,
        socketPath: this.options.socketPath,
        logPath: this.options.logPath,
        logLevel: this.options.logLevel,
        metricsPath: this.options.metricsPath,
        daemonize: this.options.daemonize,
        cleanupRegistry: this.options.cleanupRegistry,
      };

      // Add seccomp filter if available
      if (seccompFilterPath) {
        commandOptions.additionalArgs = [
          '--seccomp-filter', seccompFilterPath
        ];
      }

      this.command = new FirecrackerCommand(commandOptions);

      return retry(
        () => this.command!.start(),
        {
          maxAttempts: 3,
          initialDelayMs: 1000,
          maxDelayMs: 5000,
          backoffFactor: 2,
          jitter: true,
          context: 'Firecracker start (primary)',
          ...this.options.retry,
        }
      );
    };

    // Create a function to start with alternative configurations
    const startWithAlternativeConfig = async (): Promise<FirecrackerCommandResult> => {
      // Try alternative binary paths
      if (this.options.alternativeBinaryPaths && this.options.alternativeBinaryPaths.length > 0) {
        for (const binaryPath of this.options.alternativeBinaryPaths) {
          try {
            logger.info(`Trying alternative Firecracker binary: ${binaryPath}`);

            this.command = new FirecrackerCommand({
              ...this.options,
              binaryPath,
            });

            return await this.command.start();
          } catch (error) {
            logger.warn(`Failed to start with alternative binary ${binaryPath}: ${error}`);
          }
        }
      }

      // Try alternative socket paths
      if (this.options.alternativeSocketPaths && this.options.alternativeSocketPaths.length > 0) {
        for (const socketPath of this.options.alternativeSocketPaths) {
          try {
            logger.info(`Trying alternative socket path: ${socketPath}`);

            this.command = new FirecrackerCommand({
              ...this.options,
              socketPath,
            });

            return await this.command.start();
          } catch (error) {
            logger.warn(`Failed to start with alternative socket path ${socketPath}: ${error}`);
          }
        }
      }

      // If all alternatives failed, throw an error
      throw new ContainerizationError('Failed to start Firecracker with all alternative configurations');
    };

    // Start with fallback
    this.result = await withFallback(
      startWithPrimaryConfig,
      startWithAlternativeConfig,
      {
        context: 'Firecracker start',
        retryBeforeFallback: true,
        retryFallback: true,
        ...this.options.fallback,
      }
    );

    // Perform health check if enabled
    if (this.options.performHealthCheck) {
      await this.checkHealth();
    }

    return this.result;
  }

  /**
   * Stop the Firecracker process
   */
  async stop(): Promise<void> {
    // Stop the Firecracker process
    if (this.command) {
      try {
        await this.command.stop();
        this.result = null;
      } catch (error) {
        logError(error, 'Failed to stop Firecracker');

        // Force kill if needed
        if (this.command.isRunning()) {
          try {
            logger.warn('Forcing Firecracker process termination');
            process.kill(this.command.getPid()!, 'SIGKILL');
          } catch (killError) {
            logger.error('Failed to force kill Firecracker process', killError);
          }
        }
      }
    } else {
      logger.debug('Firecracker command is not initialized');
    }

    // Stop the jailer if it was used
    if (this.options.useJailer && this.jailer && this.result) {
      try {
        logger.info('Stopping jailer');
        await this.jailer.stop(this.result.pid);

        // Clean up jailer resources
        if (this.options.jailerConfig?.id) {
          await this.jailer.cleanup(this.options.jailerConfig.id);
        }

        logger.info('Jailer stopped and cleaned up');
      } catch (error) {
        logError(error, 'Failed to stop jailer');
      }
    }

    // Clean up seccomp filter if it was used
    if (this.options.useSeccomp && this.seccompFilterManager) {
      try {
        // The seccomp filter is automatically cleaned up when the process exits
        logger.debug('Seccomp filter will be cleaned up automatically');
      } catch (error) {
        logError(error, 'Failed to clean up seccomp filter');
      }
    }
  }

  /**
   * Check the health of the Firecracker process
   */
  private async checkHealth(): Promise<void> {
    if (!this.result) {
      throw new ContainerizationError('Cannot check health: Firecracker process not started');
    }

    // Create a connectivity check
    const connectivityCheck = createConnectivityCheck(
      async () => {
        // Try to access the socket file
        await fs.promises.access(this.result!.socketPath, fs.constants.F_OK);

        // TODO: Add a simple API call to verify the API is responsive
      },
      'Firecracker API'
    );

    // Perform the health check
    const healthResult = await performHealthCheck(
      connectivityCheck,
      {
        timeoutMs: this.options.healthCheckTimeoutMs,
      }
    );

    if (!healthResult.healthy) {
      throw new ContainerizationError(`Firecracker health check failed: ${healthResult.message}`, {
        details: healthResult.details,
      });
    }

    logger.debug('Firecracker health check passed');
  }

  /**
   * Get the command result
   * @returns Command result
   */
  getResult(): FirecrackerCommandResult | null {
    return this.result;
  }

  /**
   * Check if the Firecracker process is running
   * @returns Whether the process is running
   */
  isRunning(): boolean {
    return this.command !== null && this.command.isRunning();
  }
}
