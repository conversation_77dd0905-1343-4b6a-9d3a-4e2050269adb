/**
 * MicroVM API Handlers
 * 
 * This module provides API handlers for microVM operations.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { MicroVmManager } from '../core/microvm-manager';
import { MicroVmCreationOptions, MicroVmState } from '../models';
import { 
  ContainerizationError, 
  NotFoundError, 
  AlreadyExistsError, 
  InvalidStateError 
} from '../../shared/error-handling';
import { findKernelImage, findRootfsImage } from '../utils';

// Initialize the microVM manager
const microVmManager = new MicroVmManager();

/**
 * Handle API errors
 * @param res Response object
 * @param error Error to handle
 */
export function handleApiError(res: NextApiResponse, error: any): void {
  logger.error('MicroVM API error:', error);
  
  if (error instanceof NotFoundError) {
    res.status(404).json({ error: error.message });
  } else if (error instanceof AlreadyExistsError) {
    res.status(409).json({ error: error.message });
  } else if (error instanceof InvalidStateError) {
    res.status(400).json({ error: error.message });
  } else if (error instanceof ContainerizationError) {
    res.status(error.status || 500).json({ error: error.message });
  } else {
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * List all microVMs
 * @param req Request object
 * @param res Response object
 */
export async function listMicroVms(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const microVms = await microVmManager.listMicroVms();
    res.status(200).json(microVms);
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Get a microVM by ID
 * @param req Request object
 * @param res Response object
 */
export async function getMicroVm(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    const info = await microVm.getInfo();
    res.status(200).json(info);
  } catch (error) {
    handleApiError(res, error);
  }
}

// Schema for microVM creation
const createMicroVmSchema = z.object({
  name: z.string().min(1).max(64).regex(/^[a-zA-Z0-9][a-zA-Z0-9-]*$/, {
    message: 'Name must contain only alphanumeric characters and hyphens, and must start with an alphanumeric character',
  }),
  memSizeMib: z.number().int().min(128).default(1024),
  vcpuCount: z.number().int().min(1).default(2),
  kernel: z.object({
    path: z.string().optional(),
    bootArgs: z.string().optional(),
  }).optional(),
  rootfs: z.object({
    path: z.string().optional(),
    readOnly: z.boolean().optional(),
  }).optional(),
  additionalDrives: z.array(z.object({
    id: z.string(),
    path: z.string(),
    readOnly: z.boolean().optional(),
  })).optional(),
  networkInterfaces: z.array(z.object({
    id: z.string(),
    hostDevName: z.string().optional(),
    guestMac: z.string().optional(),
  })).optional(),
  labels: z.record(z.string()).optional(),
  annotations: z.record(z.string()).optional(),
  seccompEnabled: z.boolean().optional(),
  jailerEnabled: z.boolean().optional(),
});

/**
 * Create a new microVM
 * @param req Request object
 * @param res Response object
 */
export async function createMicroVm(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Validate the request body
    const validationResult = createMicroVmSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      res.status(400).json({ error: 'Invalid request body', details: validationResult.error.format() });
      return;
    }
    
    const options = validationResult.data;
    
    // Find kernel and rootfs images if not provided
    if (!options.kernel?.path) {
      const kernelPath = findKernelImage();
      
      if (!kernelPath) {
        res.status(400).json({ error: 'Kernel image not found. Please specify the path manually.' });
        return;
      }
      
      options.kernel = {
        ...options.kernel,
        path: kernelPath,
      };
    }
    
    if (!options.rootfs?.path) {
      const rootfsPath = findRootfsImage();
      
      if (!rootfsPath) {
        res.status(400).json({ error: 'Root filesystem image not found. Please specify the path manually.' });
        return;
      }
      
      options.rootfs = {
        ...options.rootfs,
        path: rootfsPath,
      };
    }
    
    // Create the microVM
    const microVm = await microVmManager.createMicroVm(options as MicroVmCreationOptions);
    
    // Get the microVM info
    const info = await microVm.getInfo();
    
    res.status(201).json(info);
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Delete a microVM
 * @param req Request object
 * @param res Response object
 */
export async function deleteMicroVm(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    await microVmManager.deleteMicroVm(id);
    
    res.status(204).end();
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Start a microVM
 * @param req Request object
 * @param res Response object
 */
export async function startMicroVm(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    await microVm.start();
    
    const info = await microVm.getInfo();
    res.status(200).json(info);
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Stop a microVM
 * @param req Request object
 * @param res Response object
 */
export async function stopMicroVm(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    await microVm.stop();
    
    const info = await microVm.getInfo();
    res.status(200).json(info);
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Execute a command in a microVM
 * @param req Request object
 * @param res Response object
 */
export async function executeCommand(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const { command } = req.body;
    
    if (!command || typeof command !== 'string') {
      res.status(400).json({ error: 'Missing or invalid command' });
      return;
    }
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    if (microVm.getState() !== MicroVmState.RUNNING) {
      res.status(400).json({ error: `MicroVM is not running (current state: ${microVm.getState()})` });
      return;
    }
    
    const output = await microVm.executeCommand(command);
    
    res.status(200).json({ output });
  } catch (error) {
    handleApiError(res, error);
  }
}
