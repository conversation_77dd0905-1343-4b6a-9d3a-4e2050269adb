/**
 * Seccomp Filter for Firecracker
 *
 * This module provides functionality for creating and managing seccomp filters for Firecracker.
 * Seccomp filters restrict the system calls that Firecracker can make, enhancing security.
 */

import { logger } from '@/lib/logger';
import { ContainerizationError } from '../../shared/error-handling';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * Seccomp filter level
 */
export enum SeccompLevel {
  /**
   * No seccomp filtering
   */
  NONE = 'none',

  /**
   * Basic seccomp filtering (recommended)
   */
  BASIC = 'basic',

  /**
   * Advanced seccomp filtering (more restrictive)
   */
  ADVANCED = 'advanced',
}

/**
 * Seccomp filter configuration
 */
export interface SeccompFilterConfig {
  /**
   * Filter level
   */
  level?: SeccompLevel;

  /**
   * Output file path
   */
  outputPath?: string;

  /**
   * Custom filter JSON
   */
  customFilter?: string;
}

/**
 * Seccomp filter result
 */
export interface SeccompFilterResult {
  /**
   * Filter ID
   */
  id: string;

  /**
   * Filter level
   */
  level: SeccompLevel;

  /**
   * Filter file path
   */
  filePath: string;

  /**
   * Creation timestamp
   */
  createdAt: Date;
}

/**
 * Seccomp filter manager
 */
export class SeccompFilterManager {
  private readonly filters: Map<string, SeccompFilterResult> = new Map();

  /**
   * Create a new seccomp filter
   * @param config Filter configuration
   * @returns Filter result
   */
  async createFilter(config: SeccompFilterConfig = {}): Promise<SeccompFilterResult> {
    try {
      const id = uuidv4();
      const level = config.level || SeccompLevel.BASIC;
      const outputPath = config.outputPath || path.join(process.cwd(), 'data', 'seccomp', `${id}.json`);

      // Create directory if it doesn't exist
      fs.mkdirSync(path.dirname(outputPath), { recursive: true });

      // Get filter content
      let filterContent: string;

      if (config.customFilter) {
        // Use custom filter
        filterContent = config.customFilter;
      } else {
        // Use predefined filter
        filterContent = this.getPredefinedFilter(level);
      }

      // Write filter to file
      fs.writeFileSync(outputPath, filterContent);

      // Create filter result
      const result: SeccompFilterResult = {
        id,
        level,
        filePath: outputPath,
        createdAt: new Date(),
      };

      // Store filter
      this.filters.set(id, result);

      logger.info(`Created seccomp filter: ${id} (${level}) at ${outputPath}`);

      return result;
    } catch (error) {
      logger.error(`Failed to create seccomp filter: ${error}`);
      throw new ContainerizationError(`Failed to create seccomp filter: ${error}`, {
        cause: error,
      });
    }
  }

  /**
   * Get a predefined seccomp filter
   * @param level Filter level
   * @returns Filter content
   * @private
   */
  private getPredefinedFilter(level: SeccompLevel): string {
    switch (level) {
      case SeccompLevel.NONE:
        return JSON.stringify({
          defaultAction: "SCMP_ACT_ALLOW",
          architectures: ["SCMP_ARCH_X86_64"],
          syscalls: [],
        }, null, 2);

      case SeccompLevel.BASIC:
        return JSON.stringify({
          defaultAction: "SCMP_ACT_ERRNO",
          architectures: ["SCMP_ARCH_X86_64"],
          syscalls: [
            {
              names: [
                "accept4",
                "brk",
                "clock_gettime",
                "close",
                "dup",
                "epoll_create1",
                "epoll_ctl",
                "epoll_pwait",
                "exit",
                "exit_group",
                "fcntl",
                "fstat",
                "fsync",
                "ftruncate",
                "futex",
                "getpid",
                "gettid",
                "gettimeofday",
                "ioctl",
                "lseek",
                "madvise",
                "mmap",
                "mprotect",
                "munmap",
                "openat",
                "pipe2",
                "pread64",
                "prlimit64",
                "pwrite64",
                "read",
                "readv",
                "recvfrom",
                "recvmsg",
                "rt_sigaction",
                "rt_sigprocmask",
                "rt_sigreturn",
                "sched_yield",
                "sendmsg",
                "set_robust_list",
                "sigaltstack",
                "socket",
                "stat",
                "statx",
                "tgkill",
                "timerfd_create",
                "timerfd_settime",
                "write",
                "writev"
              ],
              action: "SCMP_ACT_ALLOW"
            }
          ]
        }, null, 2);

      case SeccompLevel.ADVANCED:
        return JSON.stringify({
          defaultAction: "SCMP_ACT_KILL_PROCESS",
          architectures: ["SCMP_ARCH_X86_64"],
          syscalls: [
            {
              names: [
                "accept4",
                "brk",
                "clock_gettime",
                "close",
                "dup",
                "epoll_create1",
                "epoll_ctl",
                "epoll_pwait",
                "exit",
                "exit_group",
                "fcntl",
                "fstat",
                "fsync",
                "ftruncate",
                "futex",
                "getpid",
                "gettid",
                "gettimeofday",
                "ioctl",
                "lseek",
                "madvise",
                "mmap",
                "mprotect",
                "munmap",
                "openat",
                "pipe2",
                "pread64",
                "prlimit64",
                "pwrite64",
                "read",
                "readv",
                "recvfrom",
                "recvmsg",
                "rt_sigaction",
                "rt_sigprocmask",
                "rt_sigreturn",
                "sched_yield",
                "sendmsg",
                "set_robust_list",
                "sigaltstack",
                "socket",
                "stat",
                "statx",
                "tgkill",
                "timerfd_create",
                "timerfd_settime",
                "write",
                "writev"
              ],
              action: "SCMP_ACT_ALLOW"
            }
          ]
        }, null, 2);

      default:
        throw new ContainerizationError(`Unknown seccomp filter level: ${level}`);
    }
  }

  /**
   * Delete a seccomp filter
   * @param id Filter ID
   */
  async deleteFilter(id: string): Promise<void> {
    try {
      const filter = this.filters.get(id);

      if (!filter) {
        return;
      }

      // Delete filter file
      if (fs.existsSync(filter.filePath)) {
        fs.unlinkSync(filter.filePath);
      }

      // Remove from map
      this.filters.delete(id);

      logger.info(`Deleted seccomp filter: ${id}`);
    } catch (error) {
      logger.warn(`Failed to delete seccomp filter: ${error}`);
    }
  }

  /**
   * Get a seccomp filter
   * @param id Filter ID
   * @returns Filter result
   */
  getFilter(id: string): SeccompFilterResult | undefined {
    return this.filters.get(id);
  }

  /**
   * List all seccomp filters
   * @returns Filter results
   */
  listFilters(): SeccompFilterResult[] {
    return Array.from(this.filters.values());
  }
}
