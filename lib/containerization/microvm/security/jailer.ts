/**
 * Firecracker Jailer
 *
 * This module provides functionality for running Firecracker in a jailed environment.
 * It uses the Firecracker jailer to provide additional security isolation.
 */

import { logger } from '@/lib/logger';
import { ContainerizationError } from '../../shared/error-handling';
import * as fs from 'fs';
import * as path from 'path';
import { exec, execSync } from 'child_process';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';

const execAsync = promisify(exec);

/**
 * Jailer configuration
 */
export interface JailerConfig {
  /**
   * Jailer binary path
   */
  jailerBinaryPath?: string;

  /**
   * Chroot directory
   */
  chrootDir?: string;

  /**
   * UID to run Firecracker as
   */
  uid?: number;

  /**
   * GID to run Firecracker as
   */
  gid?: number;

  /**
   * Number of network interfaces
   */
  numNetworkInterfaces?: number;

  /**
   * ID for the jailed Firecracker process
   */
  id?: string;

  /**
   * Exec file
   */
  execFile?: string;

  /**
   * Kernel image path
   */
  kernelImagePath?: string;

  /**
   * Additional arguments to pass to the jailer
   */
  additionalArgs?: string[];
}

/**
 * Jailer result
 */
export interface JailerResult {
  /**
   * Process ID of the jailed Firecracker process
   */
  pid: number;

  /**
   * Socket path for the jailed Firecracker process
   */
  socketPath: string;

  /**
   * Chroot directory
   */
  chrootDir: string;

  /**
   * Jailer ID
   */
  id: string;
}

/**
 * Firecracker jailer
 */
export class Jailer {
  private readonly config: JailerConfig;

  /**
   * Create a new jailer
   * @param config Jailer configuration
   */
  constructor(config: JailerConfig = {}) {
    this.config = {
      jailerBinaryPath: '/usr/local/bin/jailer',
      chrootDir: '/srv/jailer',
      uid: 1000,
      gid: 1000,
      numNetworkInterfaces: 1,
      id: uuidv4(),
      execFile: '/usr/local/bin/firecracker',
      ...config,
    };
  }

  /**
   * Start the jailer
   * @returns Jailer result
   */
  async start(): Promise<JailerResult> {
    try {
      // Check if jailer binary exists
      if (!fs.existsSync(this.config.jailerBinaryPath!)) {
        throw new ContainerizationError(`Jailer binary not found: ${this.config.jailerBinaryPath}`);
      }

      // Check if exec file exists
      if (!fs.existsSync(this.config.execFile!)) {
        throw new ContainerizationError(`Exec file not found: ${this.config.execFile}`);
      }

      // Create chroot directory if it doesn't exist
      fs.mkdirSync(this.config.chrootDir!, { recursive: true });

      // Build jailer command
      const args = [
        '--id', this.config.id!,
        '--uid', String(this.config.uid!),
        '--gid', String(this.config.gid!),
        '--chroot-base-dir', this.config.chrootDir!,
        '--exec-file', this.config.execFile!,
        '--daemonize',
      ];

      // Add kernel image path if provided
      if (this.config.kernelImagePath) {
        args.push('--kernel', this.config.kernelImagePath);
      }

      // Add network interfaces
      if (this.config.numNetworkInterfaces) {
        args.push('--net-iface-count', String(this.config.numNetworkInterfaces));
      }

      // Add additional arguments
      if (this.config.additionalArgs) {
        args.push(...this.config.additionalArgs);
      }

      // Build command string
      const command = `${this.config.jailerBinaryPath} ${args.join(' ')}`;

      logger.info(`Starting jailer: ${command}`);

      // Execute jailer command
      const { stdout } = await execAsync(command);

      // Parse PID from stdout
      const pidMatch = stdout.match(/PID: (\d+)/);
      const pid = pidMatch ? parseInt(pidMatch[1], 10) : -1;

      if (pid <= 0) {
        throw new ContainerizationError(`Failed to get PID from jailer output: ${stdout}`);
      }

      // Calculate socket path
      const socketPath = path.join(
        this.config.chrootDir!,
        'firecracker',
        this.config.id!,
        'root',
        'run',
        'firecracker.socket'
      );

      logger.info(`Jailer started with PID ${pid}, socket path: ${socketPath}`);

      return {
        pid,
        socketPath,
        chrootDir: this.config.chrootDir!,
        id: this.config.id!,
      };
    } catch (error) {
      logger.error(`Failed to start jailer: ${error}`);
      throw new ContainerizationError(`Failed to start jailer: ${error}`, {
        cause: error,
      });
    }
  }

  /**
   * Stop the jailer
   * @param pid Process ID of the jailed Firecracker process
   */
  async stop(pid: number): Promise<void> {
    try {
      logger.info(`Stopping jailer process: ${pid}`);

      // Send SIGTERM to the process
      process.kill(pid, 'SIGTERM');

      // Wait for the process to exit
      await new Promise<void>((resolve) => {
        const checkInterval = setInterval(() => {
          try {
            // Check if process exists
            process.kill(pid, 0);
          } catch (error) {
            // Process doesn't exist anymore
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);
      });

      logger.info(`Jailer process stopped: ${pid}`);
    } catch (error) {
      logger.warn(`Failed to stop jailer process: ${error}`);
    }
  }

  /**
   * Clean up jailer resources
   * @param id Jailer ID
   */
  async cleanup(id: string): Promise<void> {
    try {
      logger.info(`Cleaning up jailer resources for ID: ${id}`);

      // Calculate chroot directory
      const chrootDir = path.join(
        this.config.chrootDir!,
        'firecracker',
        id
      );

      // Check if directory exists
      if (fs.existsSync(chrootDir)) {
        // Remove directory
        await execAsync(`rm -rf ${chrootDir}`);
      }

      logger.info(`Jailer resources cleaned up for ID: ${id}`);
    } catch (error) {
      logger.warn(`Failed to clean up jailer resources: ${error}`);
    }
  }
}
