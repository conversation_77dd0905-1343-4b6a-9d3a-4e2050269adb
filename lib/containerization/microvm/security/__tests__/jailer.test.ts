/**
 * Jailer Tests
 * 
 * This file contains tests for the Jailer class.
 */

import { Jai<PERSON>, JailerConfig } from '../jailer';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { ContainerizationError } from '../../../shared/error-handling';

const execAsync = promisify(exec);

// Mock dependencies
jest.mock('fs');
jest.mock('child_process');
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('Jailer', () => {
  let jailer: Jailer;
  const testConfig: JailerConfig = {
    jailerBinaryPath: '/usr/local/bin/jailer',
    chrootDir: '/srv/jailer',
    uid: 1000,
    gid: 1000,
    id: 'test-jailer',
    execFile: '/usr/local/bin/firecracker',
  };
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock fs.existsSync
    (fs.existsSync as jest.Mock).mockImplementation((path: string) => {
      // Return true for binary paths
      if (path === testConfig.jailerBinaryPath || path === testConfig.execFile) {
        return true;
      }
      return false;
    });
    
    // Mock fs.mkdirSync
    (fs.mkdirSync as jest.Mock).mockImplementation(() => {});
    
    // Mock execAsync
    (execAsync as jest.Mock).mockResolvedValue({ stdout: 'PID: 12345', stderr: '' });
    
    // Create jailer
    jailer = new Jailer(testConfig);
  });
  
  describe('start', () => {
    it('should start the jailer successfully', async () => {
      // Act
      const result = await jailer.start();
      
      // Assert
      expect(result).toBeDefined();
      expect(result.pid).toBe(12345);
      expect(result.socketPath).toContain(testConfig.chrootDir);
      expect(result.socketPath).toContain(testConfig.id);
      expect(result.socketPath).toContain('firecracker.socket');
      expect(result.chrootDir).toBe(testConfig.chrootDir);
      expect(result.id).toBe(testConfig.id);
      
      // Verify mkdir call
      expect(fs.mkdirSync).toHaveBeenCalledWith(testConfig.chrootDir, { recursive: true });
      
      // Verify exec call
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining(testConfig.jailerBinaryPath));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining(`--id ${testConfig.id}`));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining(`--uid ${testConfig.uid}`));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining(`--gid ${testConfig.gid}`));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining(`--chroot-base-dir ${testConfig.chrootDir}`));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining(`--exec-file ${testConfig.execFile}`));
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('--daemonize'));
    });
    
    it('should throw an error if jailer binary does not exist', async () => {
      // Arrange
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      
      // Act & Assert
      await expect(jailer.start()).rejects.toThrow(ContainerizationError);
      await expect(jailer.start()).rejects.toThrow('Jailer binary not found');
    });
    
    it('should throw an error if exec file does not exist', async () => {
      // Arrange
      (fs.existsSync as jest.Mock).mockImplementation((path: string) => {
        // Return true only for jailer binary
        return path === testConfig.jailerBinaryPath;
      });
      
      // Act & Assert
      await expect(jailer.start()).rejects.toThrow(ContainerizationError);
      await expect(jailer.start()).rejects.toThrow('Exec file not found');
    });
    
    it('should throw an error if jailer command fails', async () => {
      // Arrange
      (execAsync as jest.Mock).mockRejectedValue(new Error('Command failed'));
      
      // Act & Assert
      await expect(jailer.start()).rejects.toThrow(ContainerizationError);
      await expect(jailer.start()).rejects.toThrow('Failed to start jailer');
    });
    
    it('should throw an error if PID cannot be parsed', async () => {
      // Arrange
      (execAsync as jest.Mock).mockResolvedValue({ stdout: 'No PID here', stderr: '' });
      
      // Act & Assert
      await expect(jailer.start()).rejects.toThrow(ContainerizationError);
      await expect(jailer.start()).rejects.toThrow('Failed to get PID from jailer output');
    });
    
    it('should include kernel image path if provided', async () => {
      // Arrange
      const configWithKernel = {
        ...testConfig,
        kernelImagePath: '/path/to/kernel',
      };
      
      jailer = new Jailer(configWithKernel);
      
      // Act
      await jailer.start();
      
      // Assert
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('--kernel /path/to/kernel'));
    });
    
    it('should include network interfaces if provided', async () => {
      // Arrange
      const configWithNetworks = {
        ...testConfig,
        numNetworkInterfaces: 2,
      };
      
      jailer = new Jailer(configWithNetworks);
      
      // Act
      await jailer.start();
      
      // Assert
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('--net-iface-count 2'));
    });
    
    it('should include additional arguments if provided', async () => {
      // Arrange
      const configWithArgs = {
        ...testConfig,
        additionalArgs: ['--arg1', 'value1', '--arg2', 'value2'],
      };
      
      jailer = new Jailer(configWithArgs);
      
      // Act
      await jailer.start();
      
      // Assert
      expect(execAsync).toHaveBeenCalledWith(expect.stringContaining('--arg1 value1 --arg2 value2'));
    });
  });
  
  describe('stop', () => {
    it('should stop the jailer process successfully', async () => {
      // Arrange
      const pid = 12345;
      const processKillSpy = jest.spyOn(process, 'kill').mockImplementation(() => true);
      
      // Mock setInterval to call callback immediately
      jest.useFakeTimers();
      const setIntervalSpy = jest.spyOn(global, 'setInterval').mockImplementation((callback: any) => {
        callback();
        // Simulate process not existing on second check
        processKillSpy.mockImplementationOnce(() => {
          throw new Error('Process does not exist');
        });
        return 1 as any;
      });
      
      // Act
      await jailer.stop(pid);
      
      // Assert
      expect(processKillSpy).toHaveBeenCalledWith(pid, 'SIGTERM');
      expect(setIntervalSpy).toHaveBeenCalled();
      
      // Restore mocks
      processKillSpy.mockRestore();
      setIntervalSpy.mockRestore();
      jest.useRealTimers();
    });
    
    it('should handle errors gracefully', async () => {
      // Arrange
      const pid = 12345;
      const processKillSpy = jest.spyOn(process, 'kill').mockImplementation(() => {
        throw new Error('Failed to kill process');
      });
      
      // Act
      await jailer.stop(pid);
      
      // Assert - should not throw
      expect(processKillSpy).toHaveBeenCalledWith(pid, 'SIGTERM');
      
      // Restore mock
      processKillSpy.mockRestore();
    });
  });
  
  describe('cleanup', () => {
    it('should clean up jailer resources successfully', async () => {
      // Arrange
      const id = 'test-jailer';
      const chrootDir = path.join(testConfig.chrootDir, 'firecracker', id);
      
      // Mock fs.existsSync for chroot directory
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      
      // Act
      await jailer.cleanup(id);
      
      // Assert
      expect(execAsync).toHaveBeenCalledWith(`rm -rf ${chrootDir}`);
    });
    
    it('should skip cleanup if directory does not exist', async () => {
      // Arrange
      const id = 'test-jailer';
      
      // Mock fs.existsSync for chroot directory
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      
      // Act
      await jailer.cleanup(id);
      
      // Assert
      expect(execAsync).not.toHaveBeenCalled();
    });
    
    it('should handle cleanup errors gracefully', async () => {
      // Arrange
      const id = 'test-jailer';
      
      // Mock fs.existsSync for chroot directory
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      
      // Mock execAsync to throw error
      (execAsync as jest.Mock).mockRejectedValue(new Error('Cleanup failed'));
      
      // Act
      await jailer.cleanup(id);
      
      // Assert - should not throw
      expect(execAsync).toHaveBeenCalled();
    });
  });
});
