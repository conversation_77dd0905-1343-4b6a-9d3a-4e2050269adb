/**
 * SeccompFilterManager Tests
 * 
 * This file contains tests for the SeccompFilterManager class.
 */

import { SeccompFilterManager, SeccompLevel } from '../seccomp';
import * as fs from 'fs';
import * as path from 'path';
import { ContainerizationError } from '../../../shared/error-handling';

// Mock dependencies
jest.mock('fs');
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('SeccompFilterManager', () => {
  let seccompFilterManager: SeccompFilterManager;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock fs.mkdirSync
    (fs.mkdirSync as jest.Mock).mockImplementation(() => {});
    
    // Mock fs.writeFileSync
    (fs.writeFileSync as jest.Mock).mockImplementation(() => {});
    
    // Mock fs.existsSync
    (fs.existsSync as jest.Mock).mockReturnValue(true);
    
    // Mock fs.unlinkSync
    (fs.unlinkSync as jest.Mock).mockImplementation(() => {});
    
    // Create seccomp filter manager
    seccompFilterManager = new SeccompFilterManager();
  });
  
  describe('createFilter', () => {
    it('should create a basic seccomp filter successfully', async () => {
      // Act
      const result = await seccompFilterManager.createFilter({
        level: SeccompLevel.BASIC,
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.level).toBe(SeccompLevel.BASIC);
      expect(result.filePath).toContain('seccomp');
      expect(result.filePath).toContain('.json');
      expect(result.createdAt).toBeInstanceOf(Date);
      
      // Verify mkdir call
      expect(fs.mkdirSync).toHaveBeenCalledWith(expect.any(String), { recursive: true });
      
      // Verify writeFileSync call
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        result.filePath,
        expect.stringContaining('"defaultAction": "SCMP_ACT_ERRNO"')
      );
    });
    
    it('should create a none seccomp filter successfully', async () => {
      // Act
      const result = await seccompFilterManager.createFilter({
        level: SeccompLevel.NONE,
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.level).toBe(SeccompLevel.NONE);
      
      // Verify writeFileSync call
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        result.filePath,
        expect.stringContaining('"defaultAction": "SCMP_ACT_ALLOW"')
      );
    });
    
    it('should create an advanced seccomp filter successfully', async () => {
      // Act
      const result = await seccompFilterManager.createFilter({
        level: SeccompLevel.ADVANCED,
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.level).toBe(SeccompLevel.ADVANCED);
      
      // Verify writeFileSync call
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        result.filePath,
        expect.stringContaining('"defaultAction": "SCMP_ACT_KILL_PROCESS"')
      );
    });
    
    it('should create a filter with custom content', async () => {
      // Arrange
      const customFilter = '{"custom": "filter"}';
      
      // Act
      const result = await seccompFilterManager.createFilter({
        customFilter,
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.level).toBe(SeccompLevel.BASIC); // Default level
      
      // Verify writeFileSync call
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        result.filePath,
        customFilter
      );
    });
    
    it('should use custom output path if provided', async () => {
      // Arrange
      const outputPath = '/custom/path/filter.json';
      
      // Act
      const result = await seccompFilterManager.createFilter({
        outputPath,
      });
      
      // Assert
      expect(result).toBeDefined();
      expect(result.filePath).toBe(outputPath);
      
      // Verify mkdir call
      expect(fs.mkdirSync).toHaveBeenCalledWith('/custom/path', { recursive: true });
    });
    
    it('should throw an error for unknown filter level', async () => {
      // Act & Assert
      await expect(seccompFilterManager.createFilter({
        level: 'unknown' as any,
      })).rejects.toThrow(ContainerizationError);
      await expect(seccompFilterManager.createFilter({
        level: 'unknown' as any,
      })).rejects.toThrow('Unknown seccomp filter level');
    });
    
    it('should throw an error if file creation fails', async () => {
      // Arrange
      (fs.writeFileSync as jest.Mock).mockImplementation(() => {
        throw new Error('Failed to write file');
      });
      
      // Act & Assert
      await expect(seccompFilterManager.createFilter()).rejects.toThrow(ContainerizationError);
      await expect(seccompFilterManager.createFilter()).rejects.toThrow('Failed to create seccomp filter');
    });
  });
  
  describe('deleteFilter', () => {
    it('should delete a filter successfully', async () => {
      // Arrange
      const filter = await seccompFilterManager.createFilter();
      
      // Act
      await seccompFilterManager.deleteFilter(filter.id);
      
      // Assert
      expect(fs.unlinkSync).toHaveBeenCalledWith(filter.filePath);
      expect((seccompFilterManager as any).filters.has(filter.id)).toBe(false);
    });
    
    it('should do nothing if filter does not exist', async () => {
      // Act
      await seccompFilterManager.deleteFilter('non-existent');
      
      // Assert
      expect(fs.unlinkSync).not.toHaveBeenCalled();
    });
    
    it('should handle deletion errors gracefully', async () => {
      // Arrange
      const filter = await seccompFilterManager.createFilter();
      
      // Mock unlinkSync to throw error
      (fs.unlinkSync as jest.Mock).mockImplementation(() => {
        throw new Error('Failed to delete file');
      });
      
      // Act
      await seccompFilterManager.deleteFilter(filter.id);
      
      // Assert - should not throw but should remove from map
      expect((seccompFilterManager as any).filters.has(filter.id)).toBe(false);
    });
  });
  
  describe('getFilter', () => {
    it('should get a filter by ID', async () => {
      // Arrange
      const filter = await seccompFilterManager.createFilter();
      
      // Act
      const result = seccompFilterManager.getFilter(filter.id);
      
      // Assert
      expect(result).toBeDefined();
      expect(result!.id).toBe(filter.id);
    });
    
    it('should return undefined for non-existent filter', () => {
      // Act
      const result = seccompFilterManager.getFilter('non-existent');
      
      // Assert
      expect(result).toBeUndefined();
    });
  });
  
  describe('listFilters', () => {
    it('should list all filters', async () => {
      // Arrange
      const filter1 = await seccompFilterManager.createFilter({ level: SeccompLevel.BASIC });
      const filter2 = await seccompFilterManager.createFilter({ level: SeccompLevel.ADVANCED });
      
      // Act
      const result = seccompFilterManager.listFilters();
      
      // Assert
      expect(result).toHaveLength(2);
      expect(result).toContainEqual(expect.objectContaining({ id: filter1.id }));
      expect(result).toContainEqual(expect.objectContaining({ id: filter2.id }));
    });
    
    it('should return empty array if no filters exist', () => {
      // Act
      const result = seccompFilterManager.listFilters();
      
      // Assert
      expect(result).toHaveLength(0);
    });
  });
});
