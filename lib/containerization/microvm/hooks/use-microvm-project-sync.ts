/**
 * MicroVM Project Synchronization Hook
 * 
 * This hook provides functionality for synchronizing project files between the host and microVMs.
 */

import { useState, useEffect, useCallback } from 'react';
import { logger } from '@/lib/logger';
import { IMicroVm } from '../core/interfaces';
import { MicroVmState } from '../models';
import { ContainerizationError } from '../../shared/error-handling';
import { retry } from '../../shared/retry';
import { useToast } from '@/components/ui/use-toast';
import { useMicroVmFiles } from './use-microvm-files';

export interface SyncOptions {
  /**
   * Source directory on the host
   */
  sourceDir: string;
  
  /**
   * Destination directory in the microVM
   */
  destDir: string;
  
  /**
   * Files or directories to exclude from synchronization
   */
  exclude?: string[];
  
  /**
   * Whether to delete files in the destination that don't exist in the source
   */
  delete?: boolean;
  
  /**
   * Whether to synchronize recursively
   */
  recursive?: boolean;
  
  /**
   * Whether to preserve permissions
   */
  preservePermissions?: boolean;
  
  /**
   * Whether to preserve timestamps
   */
  preserveTimestamps?: boolean;
  
  /**
   * Whether to compress files during transfer
   */
  compress?: boolean;
  
  /**
   * Maximum number of retry attempts
   */
  maxRetryAttempts?: number;
  
  /**
   * Whether to automatically synchronize on changes
   */
  autoSync?: boolean;
  
  /**
   * Auto-sync interval in milliseconds
   */
  autoSyncInterval?: number;
}

export interface SyncResult {
  /**
   * Whether the synchronization was successful
   */
  success: boolean;
  
  /**
   * Error that occurred during synchronization
   */
  error?: Error;
  
  /**
   * Number of files transferred
   */
  filesTransferred?: number;
  
  /**
   * Number of bytes transferred
   */
  bytesTransferred?: number;
  
  /**
   * Time taken for synchronization in milliseconds
   */
  timeTaken?: number;
}

export interface UseMicroVmProjectSyncResult {
  /**
   * Whether synchronization is in progress
   */
  syncing: boolean;
  
  /**
   * Last synchronization result
   */
  lastSyncResult: SyncResult | null;
  
  /**
   * Error that occurred during synchronization
   */
  error: Error | null;
  
  /**
   * Synchronize files from host to microVM
   * @param options Synchronization options
   * @returns Synchronization result
   */
  syncToMicroVm: (options?: Partial<SyncOptions>) => Promise<SyncResult>;
  
  /**
   * Synchronize files from microVM to host
   * @param options Synchronization options
   * @returns Synchronization result
   */
  syncFromMicroVm: (options?: Partial<SyncOptions>) => Promise<SyncResult>;
  
  /**
   * Start automatic synchronization
   * @param options Synchronization options
   */
  startAutoSync: (options?: Partial<SyncOptions>) => void;
  
  /**
   * Stop automatic synchronization
   */
  stopAutoSync: () => void;
}

/**
 * Hook for synchronizing project files between the host and microVMs
 * @param microvm MicroVM instance
 * @param defaultOptions Default synchronization options
 * @returns Synchronization functions and state
 */
export function useMicroVmProjectSync(
  microvm: IMicroVm | null,
  defaultOptions: Partial<SyncOptions> = {}
): UseMicroVmProjectSyncResult {
  const { toast } = useToast();
  const [syncing, setSyncing] = useState<boolean>(false);
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [autoSyncEnabled, setAutoSyncEnabled] = useState<boolean>(false);
  const [autoSyncOptions, setAutoSyncOptions] = useState<Partial<SyncOptions>>({});
  
  // Use the files hook for file operations
  const { writeFile, readFile } = useMicroVmFiles(microvm, '/', { loadOnMount: false });
  
  const defaultSyncOptions: SyncOptions = {
    sourceDir: '.',
    destDir: '/app',
    exclude: ['node_modules', '.git', 'dist', 'build'],
    delete: false,
    recursive: true,
    preservePermissions: true,
    preserveTimestamps: true,
    compress: true,
    maxRetryAttempts: 3,
    autoSync: false,
    autoSyncInterval: 5000,
    ...defaultOptions,
  };
  
  /**
   * Execute a command in the microVM and handle errors
   * @param command Command to execute
   * @returns Command output
   */
  const executeCommand = useCallback(async (command: string): Promise<string> => {
    if (!microvm) {
      throw new Error('MicroVM is not available');
    }
    
    if (microvm.getState() !== MicroVmState.RUNNING) {
      throw new Error(`MicroVM is not running (current state: ${microvm.getState()})`);
    }
    
    try {
      return await retry(
        () => microvm.executeCommand(command),
        {
          maxAttempts: defaultSyncOptions.maxRetryAttempts || 3,
          context: 'MicroVM command execution',
        }
      );
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to execute command in microVM: ${command}`, error);
      setError(error);
      throw error;
    }
  }, [microvm, defaultSyncOptions.maxRetryAttempts]);
  
  /**
   * Synchronize files from host to microVM
   * @param options Synchronization options
   * @returns Synchronization result
   */
  const syncToMicroVm = useCallback(async (options: Partial<SyncOptions> = {}): Promise<SyncResult> => {
    const mergedOptions = { ...defaultSyncOptions, ...options };
    const startTime = Date.now();
    
    try {
      setSyncing(true);
      setError(null);
      
      if (!microvm) {
        throw new Error('MicroVM is not available');
      }
      
      if (microvm.getState() !== MicroVmState.RUNNING) {
        throw new Error(`MicroVM is not running (current state: ${microvm.getState()})`);
      }
      
      // Ensure the destination directory exists
      await executeCommand(`mkdir -p "${mergedOptions.destDir}"`);
      
      // Build rsync-like command for synchronization
      // In a real implementation, this would use a proper file transfer mechanism
      // For this example, we'll simulate it with a simple command
      const excludeArgs = (mergedOptions.exclude || [])
        .map(pattern => `--exclude="${pattern}"`)
        .join(' ');
      
      const deleteArg = mergedOptions.delete ? '--delete' : '';
      const recursiveArg = mergedOptions.recursive ? '-r' : '';
      const preserveArg = mergedOptions.preservePermissions ? '-p' : '';
      const timestampArg = mergedOptions.preserveTimestamps ? '-t' : '';
      const compressArg = mergedOptions.compress ? '-z' : '';
      
      // In a real implementation, this would be a proper rsync or similar command
      // For this example, we'll simulate it with a simple command
      const syncCommand = `echo "Simulating sync from ${mergedOptions.sourceDir} to ${mergedOptions.destDir} with options: ${excludeArgs} ${deleteArg} ${recursiveArg} ${preserveArg} ${timestampArg} ${compressArg}"`;
      
      const output = await executeCommand(syncCommand);
      logger.info(`Sync to microVM output: ${output}`);
      
      // In a real implementation, parse the output to get statistics
      const filesTransferred = 10; // Simulated value
      const bytesTransferred = 1024 * 1024; // Simulated value
      
      const result: SyncResult = {
        success: true,
        filesTransferred,
        bytesTransferred,
        timeTaken: Date.now() - startTime,
      };
      
      setLastSyncResult(result);
      
      toast({
        title: "Sync Completed",
        description: `Synchronized ${filesTransferred} files (${Math.round(bytesTransferred / 1024)} KB) in ${result.timeTaken}ms`,
      });
      
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error('Failed to sync to microVM:', error);
      setError(error);
      
      const result: SyncResult = {
        success: false,
        error,
        timeTaken: Date.now() - startTime,
      };
      
      setLastSyncResult(result);
      
      toast({
        title: "Sync Failed",
        description: `Failed to sync to microVM: ${error.message}`,
        variant: "destructive",
      });
      
      return result;
    } finally {
      setSyncing(false);
    }
  }, [microvm, defaultSyncOptions, executeCommand, toast]);
  
  /**
   * Synchronize files from microVM to host
   * @param options Synchronization options
   * @returns Synchronization result
   */
  const syncFromMicroVm = useCallback(async (options: Partial<SyncOptions> = {}): Promise<SyncResult> => {
    const mergedOptions = { ...defaultSyncOptions, ...options };
    const startTime = Date.now();
    
    try {
      setSyncing(true);
      setError(null);
      
      if (!microvm) {
        throw new Error('MicroVM is not available');
      }
      
      if (microvm.getState() !== MicroVmState.RUNNING) {
        throw new Error(`MicroVM is not running (current state: ${microvm.getState()})`);
      }
      
      // In a real implementation, this would use a proper file transfer mechanism
      // For this example, we'll simulate it with a simple command
      const syncCommand = `echo "Simulating sync from ${mergedOptions.destDir} to ${mergedOptions.sourceDir}"`;
      
      const output = await executeCommand(syncCommand);
      logger.info(`Sync from microVM output: ${output}`);
      
      // In a real implementation, parse the output to get statistics
      const filesTransferred = 5; // Simulated value
      const bytesTransferred = 512 * 1024; // Simulated value
      
      const result: SyncResult = {
        success: true,
        filesTransferred,
        bytesTransferred,
        timeTaken: Date.now() - startTime,
      };
      
      setLastSyncResult(result);
      
      toast({
        title: "Sync Completed",
        description: `Synchronized ${filesTransferred} files (${Math.round(bytesTransferred / 1024)} KB) in ${result.timeTaken}ms`,
      });
      
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error('Failed to sync from microVM:', error);
      setError(error);
      
      const result: SyncResult = {
        success: false,
        error,
        timeTaken: Date.now() - startTime,
      };
      
      setLastSyncResult(result);
      
      toast({
        title: "Sync Failed",
        description: `Failed to sync from microVM: ${error.message}`,
        variant: "destructive",
      });
      
      return result;
    } finally {
      setSyncing(false);
    }
  }, [microvm, defaultSyncOptions, executeCommand, toast]);
  
  /**
   * Start automatic synchronization
   * @param options Synchronization options
   */
  const startAutoSync = useCallback((options: Partial<SyncOptions> = {}) => {
    setAutoSyncOptions({ ...defaultSyncOptions, ...options });
    setAutoSyncEnabled(true);
    
    toast({
      title: "Auto-Sync Enabled",
      description: `Auto-sync will run every ${(options.autoSyncInterval || defaultSyncOptions.autoSyncInterval) / 1000} seconds`,
    });
  }, [defaultSyncOptions, toast]);
  
  /**
   * Stop automatic synchronization
   */
  const stopAutoSync = useCallback(() => {
    setAutoSyncEnabled(false);
    
    toast({
      title: "Auto-Sync Disabled",
      description: "Automatic synchronization has been disabled",
    });
  }, [toast]);
  
  // Set up auto-sync
  useEffect(() => {
    if (autoSyncEnabled && microvm && microvm.getState() === MicroVmState.RUNNING) {
      const interval = autoSyncOptions.autoSyncInterval || defaultSyncOptions.autoSyncInterval;
      
      const intervalId = setInterval(() => {
        if (!syncing) {
          syncToMicroVm(autoSyncOptions);
        }
      }, interval);
      
      return () => clearInterval(intervalId);
    }
  }, [autoSyncEnabled, autoSyncOptions, defaultSyncOptions.autoSyncInterval, microvm, syncing, syncToMicroVm]);
  
  return {
    syncing,
    lastSyncResult,
    error,
    syncToMicroVm,
    syncFromMicroVm,
    startAutoSync,
    stopAutoSync,
  };
}
