/**
 * MicroVM Files Hook
 * 
 * This hook provides functionality for managing files in microVMs.
 */

import { useState, useEffect, useCallback } from 'react';
import { logger } from '@/lib/logger';
import { IMicroVm } from '../core/interfaces';
import { MicroVmState } from '../models';
import { ContainerizationError, NotFoundError } from '../../shared/error-handling';
import { retry } from '../../shared/retry';
import { useToast } from '@/components/ui/use-toast';

export interface MicroVmFile {
  path: string;
  name: string;
  isDirectory: boolean;
  size?: number;
  modifiedTime?: Date;
  permissions?: string;
  owner?: string;
  group?: string;
}

export interface MicroVmFileOperationResult {
  success: boolean;
  error?: Error;
  path?: string;
}

export interface UseMicroVmFilesOptions {
  /**
   * Auto-refresh interval in milliseconds
   */
  refreshInterval?: number;
  
  /**
   * Whether to load files on mount
   */
  loadOnMount?: boolean;
  
  /**
   * Maximum number of retry attempts for file operations
   */
  maxRetryAttempts?: number;
}

export interface UseMicroVmFilesResult {
  /**
   * List of files in the current directory
   */
  files: MicroVmFile[];
  
  /**
   * Current directory path
   */
  currentPath: string;
  
  /**
   * Whether files are currently being loaded
   */
  loading: boolean;
  
  /**
   * Error that occurred during file operations
   */
  error: Error | null;
  
  /**
   * Change the current directory
   * @param path Directory path
   */
  changeDirectory: (path: string) => Promise<void>;
  
  /**
   * Read a file's content
   * @param path File path
   * @returns File content as string
   */
  readFile: (path: string) => Promise<string>;
  
  /**
   * Write content to a file
   * @param path File path
   * @param content File content
   * @returns Operation result
   */
  writeFile: (path: string, content: string) => Promise<MicroVmFileOperationResult>;
  
  /**
   * Create a new directory
   * @param path Directory path
   * @returns Operation result
   */
  createDirectory: (path: string) => Promise<MicroVmFileOperationResult>;
  
  /**
   * Delete a file or directory
   * @param path File or directory path
   * @param recursive Whether to recursively delete directories
   * @returns Operation result
   */
  deleteFileOrDirectory: (path: string, recursive?: boolean) => Promise<MicroVmFileOperationResult>;
  
  /**
   * Refresh the file list
   */
  refresh: () => Promise<void>;
  
  /**
   * Upload a file to the microVM
   * @param path Destination path
   * @param file File to upload
   * @returns Operation result
   */
  uploadFile: (path: string, file: File) => Promise<MicroVmFileOperationResult>;
  
  /**
   * Download a file from the microVM
   * @param path File path
   * @returns File content as Blob
   */
  downloadFile: (path: string) => Promise<Blob>;
}

/**
 * Hook for managing files in a microVM
 * @param microvm MicroVM instance
 * @param initialPath Initial directory path
 * @param options Hook options
 * @returns File management functions and state
 */
export function useMicroVmFiles(
  microvm: IMicroVm | null,
  initialPath: string = '/',
  options: UseMicroVmFilesOptions = {}
): UseMicroVmFilesResult {
  const { toast } = useToast();
  const [files, setFiles] = useState<MicroVmFile[]>([]);
  const [currentPath, setCurrentPath] = useState<string>(initialPath);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  const defaultOptions: UseMicroVmFilesOptions = {
    refreshInterval: 0, // No auto-refresh by default
    loadOnMount: true,
    maxRetryAttempts: 3,
  };
  
  const mergedOptions = { ...defaultOptions, ...options };
  
  /**
   * Execute a command in the microVM and handle errors
   * @param command Command to execute
   * @returns Command output
   */
  const executeCommand = useCallback(async (command: string): Promise<string> => {
    if (!microvm) {
      throw new Error('MicroVM is not available');
    }
    
    if (microvm.getState() !== MicroVmState.RUNNING) {
      throw new Error(`MicroVM is not running (current state: ${microvm.getState()})`);
    }
    
    try {
      return await retry(
        () => microvm.executeCommand(command),
        {
          maxAttempts: mergedOptions.maxRetryAttempts || 3,
          context: 'MicroVM command execution',
        }
      );
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to execute command in microVM: ${command}`, error);
      setError(error);
      throw error;
    }
  }, [microvm, mergedOptions.maxRetryAttempts]);
  
  /**
   * List files in the current directory
   */
  const listFiles = useCallback(async (): Promise<MicroVmFile[]> => {
    try {
      setLoading(true);
      setError(null);
      
      // Execute ls command with detailed output
      const output = await executeCommand(`ls -la --time-style=long-iso "${currentPath}"`);
      
      // Parse the output
      const lines = output.trim().split('\n');
      
      // Skip the first line (total) and the second line (. directory)
      const fileLines = lines.slice(1);
      
      // Parse each line
      const parsedFiles: MicroVmFile[] = fileLines.map(line => {
        // Example line: drwxr-xr-x 2 <USER> <GROUP> 4096 2023-01-01 12:00 directory_name
        const parts = line.trim().split(/\s+/);
        
        // Need at least 8 parts for a valid line
        if (parts.length < 8) {
          return null;
        }
        
        const permissions = parts[0];
        const isDirectory = permissions.startsWith('d');
        const owner = parts[2];
        const group = parts[3];
        const size = parseInt(parts[4], 10);
        
        // Date and time are parts[5] and parts[6]
        const dateStr = `${parts[5]} ${parts[6]}`;
        const modifiedTime = new Date(dateStr);
        
        // The name is everything after the date and time
        const name = parts.slice(7).join(' ');
        
        // Skip . and .. entries
        if (name === '.' || name === '..') {
          return null;
        }
        
        return {
          path: `${currentPath}${currentPath.endsWith('/') ? '' : '/'}${name}`,
          name,
          isDirectory,
          size,
          modifiedTime,
          permissions,
          owner,
          group,
        };
      }).filter(Boolean) as MicroVmFile[];
      
      return parsedFiles;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to list files in microVM at path: ${currentPath}`, error);
      setError(error);
      return [];
    } finally {
      setLoading(false);
    }
  }, [currentPath, executeCommand]);
  
  /**
   * Refresh the file list
   */
  const refresh = useCallback(async (): Promise<void> => {
    try {
      const files = await listFiles();
      setFiles(files);
    } catch (err) {
      // Error is already set in listFiles
      setFiles([]);
    }
  }, [listFiles]);
  
  /**
   * Change the current directory
   * @param path Directory path
   */
  const changeDirectory = useCallback(async (path: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      // Check if the directory exists
      await executeCommand(`test -d "${path}" && echo "Directory exists"`);
      
      // Update the current path
      setCurrentPath(path);
      
      // Refresh the file list
      await refresh();
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to change directory to: ${path}`, error);
      setError(error);
      toast({
        title: "Error",
        description: `Failed to change directory: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [executeCommand, refresh, toast]);
  
  /**
   * Read a file's content
   * @param path File path
   * @returns File content as string
   */
  const readFile = useCallback(async (path: string): Promise<string> => {
    try {
      setLoading(true);
      setError(null);
      
      // Check if the file exists
      const checkResult = await executeCommand(`test -f "${path}" && echo "File exists"`);
      
      if (!checkResult.includes('File exists')) {
        throw new NotFoundError(`File not found: ${path}`);
      }
      
      // Read the file content
      const content = await executeCommand(`cat "${path}"`);
      
      return content;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to read file: ${path}`, error);
      setError(error);
      toast({
        title: "Error",
        description: `Failed to read file: ${error.message}`,
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [executeCommand, toast]);
  
  /**
   * Write content to a file
   * @param path File path
   * @param content File content
   * @returns Operation result
   */
  const writeFile = useCallback(async (path: string, content: string): Promise<MicroVmFileOperationResult> => {
    try {
      setLoading(true);
      setError(null);
      
      // Create a temporary file with the content
      const tempFile = `/tmp/microvm-file-${Date.now()}`;
      await executeCommand(`cat > "${tempFile}" << 'EOF'\n${content}\nEOF`);
      
      // Move the temporary file to the destination
      await executeCommand(`mv "${tempFile}" "${path}"`);
      
      // Refresh the file list if the file is in the current directory
      if (path.startsWith(currentPath)) {
        await refresh();
      }
      
      toast({
        title: "Success",
        description: `File saved: ${path}`,
      });
      
      return { success: true, path };
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to write file: ${path}`, error);
      setError(error);
      toast({
        title: "Error",
        description: `Failed to save file: ${error.message}`,
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  }, [currentPath, executeCommand, refresh, toast]);
  
  /**
   * Create a new directory
   * @param path Directory path
   * @returns Operation result
   */
  const createDirectory = useCallback(async (path: string): Promise<MicroVmFileOperationResult> => {
    try {
      setLoading(true);
      setError(null);
      
      // Create the directory
      await executeCommand(`mkdir -p "${path}"`);
      
      // Refresh the file list if the directory is in the current directory
      if (path.startsWith(currentPath)) {
        await refresh();
      }
      
      toast({
        title: "Success",
        description: `Directory created: ${path}`,
      });
      
      return { success: true, path };
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to create directory: ${path}`, error);
      setError(error);
      toast({
        title: "Error",
        description: `Failed to create directory: ${error.message}`,
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  }, [currentPath, executeCommand, refresh, toast]);
  
  /**
   * Delete a file or directory
   * @param path File or directory path
   * @param recursive Whether to recursively delete directories
   * @returns Operation result
   */
  const deleteFileOrDirectory = useCallback(async (path: string, recursive: boolean = false): Promise<MicroVmFileOperationResult> => {
    try {
      setLoading(true);
      setError(null);
      
      // Check if the path exists
      const checkResult = await executeCommand(`test -e "${path}" && echo "Path exists"`);
      
      if (!checkResult.includes('Path exists')) {
        throw new NotFoundError(`Path not found: ${path}`);
      }
      
      // Check if it's a directory
      const isDirResult = await executeCommand(`test -d "${path}" && echo "Is directory"`);
      const isDirectory = isDirResult.includes('Is directory');
      
      // Delete the file or directory
      if (isDirectory) {
        if (recursive) {
          await executeCommand(`rm -rf "${path}"`);
        } else {
          await executeCommand(`rmdir "${path}"`);
        }
      } else {
        await executeCommand(`rm "${path}"`);
      }
      
      // Refresh the file list if the path is in the current directory
      if (path.startsWith(currentPath)) {
        await refresh();
      }
      
      toast({
        title: "Success",
        description: `Deleted: ${path}`,
      });
      
      return { success: true, path };
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to delete: ${path}`, error);
      setError(error);
      toast({
        title: "Error",
        description: `Failed to delete: ${error.message}`,
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  }, [currentPath, executeCommand, refresh, toast]);
  
  /**
   * Upload a file to the microVM
   * @param path Destination path
   * @param file File to upload
   * @returns Operation result
   */
  const uploadFile = useCallback(async (path: string, file: File): Promise<MicroVmFileOperationResult> => {
    try {
      setLoading(true);
      setError(null);
      
      // Read the file content
      const content = await file.text();
      
      // Write the file to the microVM
      const result = await writeFile(path, content);
      
      if (result.success) {
        toast({
          title: "Success",
          description: `File uploaded: ${path}`,
        });
      }
      
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to upload file: ${path}`, error);
      setError(error);
      toast({
        title: "Error",
        description: `Failed to upload file: ${error.message}`,
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  }, [writeFile, toast]);
  
  /**
   * Download a file from the microVM
   * @param path File path
   * @returns File content as Blob
   */
  const downloadFile = useCallback(async (path: string): Promise<Blob> => {
    try {
      setLoading(true);
      setError(null);
      
      // Read the file content
      const content = await readFile(path);
      
      // Create a blob from the content
      const blob = new Blob([content], { type: 'application/octet-stream' });
      
      return blob;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to download file: ${path}`, error);
      setError(error);
      toast({
        title: "Error",
        description: `Failed to download file: ${error.message}`,
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [readFile, toast]);
  
  // Load files on mount
  useEffect(() => {
    if (mergedOptions.loadOnMount && microvm) {
      refresh();
    }
  }, [microvm, mergedOptions.loadOnMount, refresh]);
  
  // Set up auto-refresh
  useEffect(() => {
    if (mergedOptions.refreshInterval && mergedOptions.refreshInterval > 0) {
      const intervalId = setInterval(() => {
        refresh();
      }, mergedOptions.refreshInterval);
      
      return () => clearInterval(intervalId);
    }
  }, [mergedOptions.refreshInterval, refresh]);
  
  return {
    files,
    currentPath,
    loading,
    error,
    changeDirectory,
    readFile,
    writeFile,
    createDirectory,
    deleteFileOrDirectory,
    refresh,
    uploadFile,
    downloadFile,
  };
}
