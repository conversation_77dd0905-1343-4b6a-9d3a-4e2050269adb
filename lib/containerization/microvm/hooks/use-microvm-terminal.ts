/**
 * MicroVM Terminal Hook
 * 
 * This hook provides functionality for interacting with terminals in microVMs.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { logger } from '@/lib/logger';
import { IMicroVm } from '../core/interfaces';
import { MicroVmState } from '../models';
import { ContainerizationError } from '../../shared/error-handling';
import { retry } from '../../shared/retry';
import { useToast } from '@/components/ui/use-toast';

export interface TerminalOptions {
  /**
   * Shell to use (e.g., 'bash', 'sh')
   */
  shell?: string;
  
  /**
   * Initial working directory
   */
  cwd?: string;
  
  /**
   * Environment variables
   */
  env?: Record<string, string>;
  
  /**
   * Terminal dimensions
   */
  dimensions?: {
    rows: number;
    cols: number;
  };
  
  /**
   * Maximum number of retry attempts
   */
  maxRetryAttempts?: number;
  
  /**
   * Whether to automatically reconnect on disconnect
   */
  autoReconnect?: boolean;
  
  /**
   * Maximum number of reconnect attempts
   */
  maxReconnectAttempts?: number;
}

export interface TerminalData {
  /**
   * Terminal ID
   */
  id: string;
  
  /**
   * Terminal process ID
   */
  pid?: number;
  
  /**
   * Shell being used
   */
  shell: string;
  
  /**
   * Current working directory
   */
  cwd: string;
  
  /**
   * Terminal dimensions
   */
  dimensions: {
    rows: number;
    cols: number;
  };
  
  /**
   * Whether the terminal is connected
   */
  connected: boolean;
  
  /**
   * Creation timestamp
   */
  createdAt: Date;
}

export interface UseMicroVmTerminalResult {
  /**
   * Terminal data
   */
  terminal: TerminalData | null;
  
  /**
   * Terminal output
   */
  output: string;
  
  /**
   * Whether the terminal is connecting
   */
  connecting: boolean;
  
  /**
   * Error that occurred during terminal operations
   */
  error: Error | null;
  
  /**
   * Connect to the terminal
   * @param options Terminal options
   */
  connect: (options?: TerminalOptions) => Promise<void>;
  
  /**
   * Disconnect from the terminal
   */
  disconnect: () => Promise<void>;
  
  /**
   * Send input to the terminal
   * @param input Input to send
   */
  sendInput: (input: string) => Promise<void>;
  
  /**
   * Resize the terminal
   * @param rows Number of rows
   * @param cols Number of columns
   */
  resize: (rows: number, cols: number) => Promise<void>;
  
  /**
   * Clear the terminal output
   */
  clearOutput: () => void;
}

/**
 * Hook for interacting with terminals in microVMs
 * @param microvm MicroVM instance
 * @param options Terminal options
 * @returns Terminal functions and state
 */
export function useMicroVmTerminal(
  microvm: IMicroVm | null,
  options: TerminalOptions = {}
): UseMicroVmTerminalResult {
  const { toast } = useToast();
  const [terminal, setTerminal] = useState<TerminalData | null>(null);
  const [output, setOutput] = useState<string>('');
  const [connecting, setConnecting] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Use refs for values that shouldn't trigger re-renders
  const outputRef = useRef<string>('');
  const reconnectAttemptsRef = useRef<number>(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const defaultOptions: TerminalOptions = {
    shell: 'bash',
    cwd: '/',
    env: {
      TERM: 'xterm-256color',
      COLORTERM: 'truecolor',
    },
    dimensions: {
      rows: 24,
      cols: 80,
    },
    maxRetryAttempts: 3,
    autoReconnect: true,
    maxReconnectAttempts: 5,
  };
  
  const mergedOptions = { ...defaultOptions, ...options };
  
  /**
   * Execute a command in the microVM and handle errors
   * @param command Command to execute
   * @returns Command output
   */
  const executeCommand = useCallback(async (command: string): Promise<string> => {
    if (!microvm) {
      throw new Error('MicroVM is not available');
    }
    
    if (microvm.getState() !== MicroVmState.RUNNING) {
      throw new Error(`MicroVM is not running (current state: ${microvm.getState()})`);
    }
    
    try {
      return await retry(
        () => microvm.executeCommand(command),
        {
          maxAttempts: mergedOptions.maxRetryAttempts || 3,
          context: 'MicroVM command execution',
        }
      );
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error(`Failed to execute command in microVM: ${command}`, error);
      setError(error);
      throw error;
    }
  }, [microvm, mergedOptions.maxRetryAttempts]);
  
  /**
   * Connect to the terminal
   * @param connectOptions Terminal options
   */
  const connect = useCallback(async (connectOptions: TerminalOptions = {}): Promise<void> => {
    try {
      setConnecting(true);
      setError(null);
      
      if (!microvm) {
        throw new Error('MicroVM is not available');
      }
      
      if (microvm.getState() !== MicroVmState.RUNNING) {
        throw new Error(`MicroVM is not running (current state: ${microvm.getState()})`);
      }
      
      // Merge options
      const termOptions = { ...mergedOptions, ...connectOptions };
      
      // Generate a unique terminal ID
      const terminalId = `term-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      
      // Set environment variables
      const envVars = Object.entries(termOptions.env || {})
        .map(([key, value]) => `${key}="${value}"`)
        .join(' ');
      
      // In a real implementation, this would use a proper terminal emulation
      // For this example, we'll simulate it with a simple command
      const startCommand = `cd "${termOptions.cwd}" && ${envVars} TERM=xterm-256color LINES=${termOptions.dimensions?.rows} COLUMNS=${termOptions.dimensions?.cols} ${termOptions.shell}`;
      
      // Execute the command to start the shell
      // In a real implementation, this would use a proper PTY
      await executeCommand(startCommand);
      
      // Create terminal data
      const newTerminal: TerminalData = {
        id: terminalId,
        shell: termOptions.shell || 'bash',
        cwd: termOptions.cwd || '/',
        dimensions: termOptions.dimensions || { rows: 24, cols: 80 },
        connected: true,
        createdAt: new Date(),
      };
      
      setTerminal(newTerminal);
      
      // Add initial output
      const initialOutput = `Connected to ${microvm.getName()} (${microvm.getId()}) using ${newTerminal.shell}\r\n`;
      appendOutput(initialOutput);
      
      // Reset reconnect attempts
      reconnectAttemptsRef.current = 0;
      
      toast({
        title: "Terminal Connected",
        description: `Connected to ${microvm.getName()} using ${newTerminal.shell}`,
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error('Failed to connect to terminal:', error);
      setError(error);
      
      toast({
        title: "Connection Failed",
        description: `Failed to connect to terminal: ${error.message}`,
        variant: "destructive",
      });
      
      // Try to reconnect if auto-reconnect is enabled
      if (mergedOptions.autoReconnect) {
        scheduleReconnect();
      }
    } finally {
      setConnecting(false);
    }
  }, [microvm, mergedOptions, executeCommand, toast]);
  
  /**
   * Disconnect from the terminal
   */
  const disconnect = useCallback(async (): Promise<void> => {
    try {
      if (!terminal) {
        return;
      }
      
      // In a real implementation, this would terminate the PTY
      // For this example, we'll just update the state
      
      // Cancel any pending reconnect
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      
      setTerminal(prev => prev ? { ...prev, connected: false } : null);
      
      appendOutput('\r\nDisconnected from terminal.\r\n');
      
      toast({
        title: "Terminal Disconnected",
        description: "Disconnected from terminal",
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error('Failed to disconnect from terminal:', error);
      setError(error);
      
      toast({
        title: "Disconnect Failed",
        description: `Failed to disconnect from terminal: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [terminal, toast]);
  
  /**
   * Send input to the terminal
   * @param input Input to send
   */
  const sendInput = useCallback(async (input: string): Promise<void> => {
    try {
      if (!terminal || !terminal.connected) {
        throw new Error('Terminal is not connected');
      }
      
      if (!microvm) {
        throw new Error('MicroVM is not available');
      }
      
      // In a real implementation, this would send input to the PTY
      // For this example, we'll simulate it by executing the command
      
      // Echo the input to the output
      appendOutput(input);
      
      // If the input ends with a newline, execute the command
      if (input.endsWith('\n') || input.endsWith('\r')) {
        const command = input.trim();
        
        if (command) {
          // Execute the command
          try {
            const result = await executeCommand(command);
            appendOutput(`${result}\r\n`);
          } catch (err) {
            appendOutput(`Error: ${err}\r\n`);
          }
        } else {
          // Just add a new prompt line for empty commands
          appendOutput('\r\n');
        }
        
        // Add a new prompt
        appendOutput(`${terminal.cwd}$ `);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error('Failed to send input to terminal:', error);
      setError(error);
      
      toast({
        title: "Input Failed",
        description: `Failed to send input to terminal: ${error.message}`,
        variant: "destructive",
      });
      
      // Try to reconnect if the terminal is disconnected
      if (terminal && !terminal.connected && mergedOptions.autoReconnect) {
        scheduleReconnect();
      }
    }
  }, [terminal, microvm, executeCommand, mergedOptions.autoReconnect, toast]);
  
  /**
   * Resize the terminal
   * @param rows Number of rows
   * @param cols Number of columns
   */
  const resize = useCallback(async (rows: number, cols: number): Promise<void> => {
    try {
      if (!terminal) {
        return;
      }
      
      // In a real implementation, this would resize the PTY
      // For this example, we'll just update the state
      
      setTerminal(prev => prev ? {
        ...prev,
        dimensions: { rows, cols },
      } : null);
      
      // Set environment variables for the new dimensions
      if (terminal.connected) {
        await executeCommand(`export LINES=${rows} COLUMNS=${cols}`);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.error('Failed to resize terminal:', error);
      setError(error);
    }
  }, [terminal, executeCommand]);
  
  /**
   * Clear the terminal output
   */
  const clearOutput = useCallback((): void => {
    setOutput('');
    outputRef.current = '';
  }, []);
  
  /**
   * Append output to the terminal
   * @param text Text to append
   */
  const appendOutput = useCallback((text: string): void => {
    outputRef.current += text;
    setOutput(outputRef.current);
  }, []);
  
  /**
   * Schedule a reconnect attempt
   */
  const scheduleReconnect = useCallback((): void => {
    // Cancel any pending reconnect
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    // Check if we've reached the maximum number of reconnect attempts
    if (reconnectAttemptsRef.current >= (mergedOptions.maxReconnectAttempts || 5)) {
      logger.warn('Maximum reconnect attempts reached');
      appendOutput('\r\nMaximum reconnect attempts reached. Please reconnect manually.\r\n');
      return;
    }
    
    // Increment the reconnect attempts
    reconnectAttemptsRef.current++;
    
    // Calculate the delay with exponential backoff
    const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current - 1), 30000);
    
    appendOutput(`\r\nAttempting to reconnect in ${delay / 1000} seconds... (${reconnectAttemptsRef.current}/${mergedOptions.maxReconnectAttempts})\r\n`);
    
    // Schedule the reconnect
    reconnectTimeoutRef.current = setTimeout(() => {
      if (microvm && microvm.getState() === MicroVmState.RUNNING) {
        connect();
      }
    }, delay);
  }, [microvm, mergedOptions.maxReconnectAttempts, connect, appendOutput]);
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      // Cancel any pending reconnect
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      
      // Disconnect the terminal
      if (terminal && terminal.connected) {
        disconnect();
      }
    };
  }, [terminal, disconnect]);
  
  return {
    terminal,
    output,
    connecting,
    error,
    connect,
    disconnect,
    sendInput,
    resize,
    clearOutput,
  };
}
