/**
 * Validators
 * 
 * This module provides validation utilities for microVMs.
 */

import * as fs from 'fs';
import * as path from 'path';
import { ContainerizationError } from '../../shared/error-handling';
import { MicroVmCreationOptions } from '../models';

/**
 * Validate microVM creation options
 * @param options MicroVM creation options
 * @throws ContainerizationError if options are invalid
 */
export function validateMicroVmCreationOptions(options: MicroVmCreationOptions): void {
  // Validate name
  if (!options.name || options.name.trim() === '') {
    throw new ContainerizationError('MicroVM name is required');
  }
  
  if (!/^[a-zA-Z0-9][a-zA-Z0-9-]*$/.test(options.name)) {
    throw new ContainerizationError('MicroVM name must contain only alphanumeric characters and hyphens, and must start with an alphanumeric character');
  }
  
  // Validate resources
  if (!options.memSizeMib || options.memSizeMib < 128) {
    throw new ContainerizationError('Memory size must be at least 128 MiB');
  }
  
  if (!options.vcpuCount || options.vcpuCount < 1) {
    throw new ContainerizationError('vCPU count must be at least 1');
  }
  
  // Validate kernel
  if (!options.kernel || !options.kernel.path) {
    throw new ContainerizationError('Kernel path is required');
  }
  
  // Validate rootfs
  if (!options.rootfs || !options.rootfs.path) {
    throw new ContainerizationError('Root filesystem path is required');
  }
}

/**
 * Validate that a file exists and is accessible
 * @param filePath Path to the file
 * @param description Description of the file
 * @throws ContainerizationError if the file does not exist or is not accessible
 */
export async function validateFileExists(filePath: string, description: string): Promise<void> {
  try {
    await fs.promises.access(filePath, fs.constants.R_OK);
  } catch (error) {
    throw new ContainerizationError(`${description} file does not exist or is not readable: ${filePath}`, {
      cause: error,
    });
  }
}

/**
 * Validate that a directory exists and is accessible
 * @param dirPath Path to the directory
 * @param description Description of the directory
 * @throws ContainerizationError if the directory does not exist or is not accessible
 */
export async function validateDirectoryExists(dirPath: string, description: string): Promise<void> {
  try {
    const stats = await fs.promises.stat(dirPath);
    
    if (!stats.isDirectory()) {
      throw new ContainerizationError(`${description} is not a directory: ${dirPath}`);
    }
  } catch (error) {
    throw new ContainerizationError(`${description} directory does not exist or is not accessible: ${dirPath}`, {
      cause: error,
    });
  }
}

/**
 * Validate that a command is available in the PATH
 * @param command Command name
 * @throws ContainerizationError if the command is not available
 */
export async function validateCommandExists(command: string): Promise<void> {
  const { exec } = require('child_process');
  const { promisify } = require('util');
  const execAsync = promisify(exec);
  
  try {
    await execAsync(`which ${command}`);
  } catch (error) {
    throw new ContainerizationError(`Command '${command}' is not available in the PATH`, {
      cause: error,
    });
  }
}
