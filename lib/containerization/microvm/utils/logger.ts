/**
 * Logger
 * 
 * This module provides logging utilities for microVMs.
 */

import { logger } from '@/lib/logger';

/**
 * Create a logger with a prefix
 * @param prefix Logger prefix
 * @returns Logger with prefix
 */
export function createPrefixedLogger(prefix: string) {
  return {
    debug: (message: string, ...args: any[]) => {
      logger.debug(`[${prefix}] ${message}`, ...args);
    },
    info: (message: string, ...args: any[]) => {
      logger.info(`[${prefix}] ${message}`, ...args);
    },
    warn: (message: string, ...args: any[]) => {
      logger.warn(`[${prefix}] ${message}`, ...args);
    },
    error: (message: string, ...args: any[]) => {
      logger.error(`[${prefix}] ${message}`, ...args);
    },
  };
}

/**
 * Create a logger for a specific microVM
 * @param id MicroVM ID
 * @param name MicroVM name
 * @returns Logger for the microVM
 */
export function createMicroVmLogger(id: string, name: string) {
  return createPrefixedLogger(`MicroVM ${name} (${id})`);
}

/**
 * Parse a Firecracker log file
 * @param logContent Log file content
 * @returns Parsed log entries
 */
export function parseFirecrackerLog(logContent: string): any[] {
  const logEntries: any[] = [];
  
  const lines = logContent.split('\n');
  
  for (const line of lines) {
    if (!line.trim()) {
      continue;
    }
    
    try {
      const entry = JSON.parse(line);
      logEntries.push(entry);
    } catch (error) {
      logger.warn(`Failed to parse Firecracker log line: ${line}`);
    }
  }
  
  return logEntries;
}

/**
 * Extract errors from a Firecracker log
 * @param logEntries Parsed log entries
 * @returns Error log entries
 */
export function extractFirecrackerErrors(logEntries: any[]): any[] {
  return logEntries.filter(entry => entry.level === 'Error' || entry.level === 'Warning');
}

/**
 * Format a Firecracker log entry for display
 * @param entry Log entry
 * @returns Formatted log entry
 */
export function formatFirecrackerLogEntry(entry: any): string {
  const timestamp = entry.time || '';
  const level = entry.level || 'INFO';
  const message = entry.message || '';
  
  return `${timestamp} [${level}] ${message}`;
}
