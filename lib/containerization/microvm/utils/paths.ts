/**
 * Path Utilities
 * 
 * This module provides utilities for working with paths.
 */

import * as path from 'path';
import * as os from 'os';
import * as fs from 'fs';

/**
 * Get the default base directory for microVM data
 * @returns Base directory path
 */
export function getDefaultBaseDir(): string {
  return path.join(os.tmpdir(), 'microvms');
}

/**
 * Get the directory for a specific microVM
 * @param baseDir Base directory
 * @param id MicroVM ID
 * @returns MicroVM directory path
 */
export function getMicroVmDir(baseDir: string, id: string): string {
  return path.join(baseDir, id);
}

/**
 * Get the socket path for a specific microVM
 * @param vmDir MicroVM directory
 * @returns Socket path
 */
export function getSocketPath(vmDir: string): string {
  return path.join(vmDir, 'firecracker.sock');
}

/**
 * Get the log path for a specific microVM
 * @param vmDir MicroVM directory
 * @returns Log path
 */
export function getLogPath(vmDir: string): string {
  return path.join(vmDir, 'firecracker.log');
}

/**
 * Get the metrics path for a specific microVM
 * @param vmDir MicroVM directory
 * @returns Metrics path
 */
export function getMetricsPath(vmDir: string): string {
  return path.join(vmDir, 'metrics.json');
}

/**
 * Find the Firecracker binary
 * @returns Path to the Firecracker binary, or null if not found
 */
export function findFirecrackerBinary(): string | null {
  // Common locations for the Firecracker binary
  const commonPaths = [
    '/usr/bin/firecracker',
    '/usr/local/bin/firecracker',
    '/opt/firecracker/firecracker',
    path.join(os.homedir(), 'firecracker'),
  ];
  
  for (const binPath of commonPaths) {
    try {
      fs.accessSync(binPath, fs.constants.X_OK);
      return binPath;
    } catch (error) {
      // Ignore errors
    }
  }
  
  return null;
}

/**
 * Find the jailer binary
 * @returns Path to the jailer binary, or null if not found
 */
export function findJailerBinary(): string | null {
  // Common locations for the jailer binary
  const commonPaths = [
    '/usr/bin/jailer',
    '/usr/local/bin/jailer',
    '/opt/firecracker/jailer',
    path.join(os.homedir(), 'jailer'),
  ];
  
  for (const binPath of commonPaths) {
    try {
      fs.accessSync(binPath, fs.constants.X_OK);
      return binPath;
    } catch (error) {
      // Ignore errors
    }
  }
  
  return null;
}

/**
 * Find a kernel image
 * @returns Path to the kernel image, or null if not found
 */
export function findKernelImage(): string | null {
  // Common locations for kernel images
  const commonPaths = [
    '/opt/firecracker/vmlinux',
    '/var/lib/firecracker/vmlinux',
    path.join(os.homedir(), 'vmlinux'),
  ];
  
  for (const kernelPath of commonPaths) {
    try {
      fs.accessSync(kernelPath, fs.constants.R_OK);
      return kernelPath;
    } catch (error) {
      // Ignore errors
    }
  }
  
  return null;
}

/**
 * Find a root filesystem image
 * @returns Path to the root filesystem image, or null if not found
 */
export function findRootfsImage(): string | null {
  // Common locations for rootfs images
  const commonPaths = [
    '/opt/firecracker/rootfs.ext4',
    '/var/lib/firecracker/rootfs.ext4',
    path.join(os.homedir(), 'rootfs.ext4'),
  ];
  
  for (const rootfsPath of commonPaths) {
    try {
      fs.accessSync(rootfsPath, fs.constants.R_OK);
      return rootfsPath;
    } catch (error) {
      // Ignore errors
    }
  }
  
  return null;
}
