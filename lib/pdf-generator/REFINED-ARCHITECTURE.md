# Refined Block-Based Editor Architecture

## Overview

This document outlines the refined block-based editor architecture that leverages modern React patterns and integrates seamlessly with our existing PDF generator system. The architecture implements four key innovations:

1. **Remark JSX Integration** - Seamless markdown-to-JSX conversion
2. **Four-State Block Lifecycle** - Preview, Edit, Render, and Print states
3. **Modal-Based Editors** - Complex editing scenarios with dedicated modals
4. **Dynamic Data Support** - Real-time updates and live content

## 🏗️ Architecture Components

### 1. Remark JSX Integration (`remark-integration.ts`)

**Purpose**: Provides bidirectional conversion between markdown and React components while maintaining block structure.

**Key Features**:
- Seamless markdown ↔ blocks conversion
- JSX component generation
- Block structure preservation
- Support for extended markdown syntax

**Usage**:
```typescript
import { remarkJSXProcessor } from '@/lib/pdf-generator';

// Convert blocks to markdown
const markdown = remarkJSXProcessor.blocksToMarkdown(blocks);

// Convert markdown to blocks
const blocks = await remarkJSXProcessor.markdownToBlocks(markdown);
```

**Integration Points**:
- Works with existing `blocksToMarkdown` and `markdownToBlocks` utilities
- Maintains compatibility with PDF generation pipeline
- Supports template system integration

### 2. Four-State Block Lifecycle (`block-state-manager.ts`)

**Purpose**: Manages block states for different interaction modes and rendering contexts.

**States**:
- **Preview**: Read-only display with formatted content
- **Edit**: Inline editing with block-specific controls
- **Render**: Optimized for PDF generation and export
- **Print**: Special formatting for print/export layouts

**Key Features**:
- State transition validation
- History tracking
- Batch state operations
- Callback system for state changes

**Usage**:
```typescript
import { blockStateManager, transitionBlockState } from '@/lib/pdf-generator';

// Initialize block state
blockStateManager.initializeBlock(blockId, 'preview');

// Transition to edit mode
await transitionBlockState(blockId, 'edit');

// Batch transition all blocks to render mode
await blockStateManager.batchTransition(blockIds, 'render');
```

**State-Specific Behaviors**:
- **Preview**: Interactive hover, click to edit, toolbar hidden
- **Edit**: Focused editing, toolbar visible, auto-save enabled
- **Render**: Non-interactive, optimized for export, metadata included
- **Print**: Page-break aware, print-optimized styling

### 3. Modal-Based Editors (`modal-editors/`)

**Purpose**: Provides advanced editing interfaces for complex block types.

**Available Editors**:
- **Table Editor**: Dynamic rows/columns, sorting, filtering
- **Code Editor**: Syntax highlighting, live execution, snippets
- **Image Editor**: Upload, URL input, caption, alignment
- **Math Editor**: LaTeX formula editing with live preview
- **Embed Editor**: External content integration

**Key Features**:
- Tabbed interface (Content, Settings, Preview)
- Real-time validation
- Auto-save functionality
- Keyboard shortcuts
- Extensible architecture

**Usage**:
```typescript
import { BlockModalEditor } from '@/lib/pdf-generator';

<BlockModalEditor
  block={block}
  isOpen={showModal}
  onSave={(updatedBlock) => handleSave(updatedBlock)}
  onCancel={() => setShowModal(false)}
/>
```

### 4. Dynamic Data Support (`dynamic-data-manager.ts`)

**Purpose**: Enables blocks to handle dynamic content and real-time updates.

**Features**:
- Auto-updating lists with add/remove functionality
- Data-driven tables with sorting/filtering
- Live code execution for code blocks
- Real-time math formula rendering
- API data binding
- Reactive updates

**Usage**:
```typescript
import { 
  createDynamicList, 
  createDataTable, 
  setupLiveExecution 
} from '@/lib/pdf-generator';

// Create dynamic list
const dynamicList = createDynamicList(blockId, initialItems);
dynamicList.addItem(newItem);

// Create data table
const dataTable = createDataTable(blockId, data, columns);
dataTable.sortBy('column', 'asc');

// Setup live code execution
setupLiveExecution({
  blockId,
  language: 'javascript',
  code: 'console.log("Hello, World!");',
  environment: 'browser'
});
```

## 🔧 Enhanced Components

### EnhancedBlockContainer

**Purpose**: Wrapper component that implements the four-state lifecycle with modal editor support.

**Features**:
- Automatic state transitions based on user interaction
- Visual state indicators
- Modal editor integration
- Drag and drop support
- Validation feedback

### EnhancedBlockEditor

**Purpose**: Main editor component that orchestrates all enhanced features.

**Features**:
- Mode switching (Blocks, Markdown, JSX)
- Global state management
- Dynamic data toggles
- Live execution controls
- Enhanced keyboard shortcuts

## 🚀 Integration with Existing System

### PDF Generation Pipeline

The refined architecture maintains full compatibility with the existing PDF generation system:

1. **Template System**: All block types work with existing templates
2. **Validation**: Enhanced validation integrates with existing validation pipeline
3. **Export**: Render and Print states optimize content for PDF generation
4. **Metadata**: Block metadata is preserved and included in exports

### Backward Compatibility

- Existing block components continue to work
- Original `BlockEditor` remains functional
- All existing APIs are preserved
- Migration path is non-breaking

## 📊 Performance Considerations

### State Management

- **Lazy Loading**: Modal editors load only when needed
- **Memoization**: Block components use React.memo for optimization
- **Debouncing**: Auto-save and validation use debouncing
- **Virtual Scrolling**: Large documents use virtual scrolling

### Memory Management

- **Cleanup**: Automatic cleanup of state managers and data bindings
- **Weak References**: Event listeners use weak references where possible
- **Resource Disposal**: Modal editors dispose of resources on close

## 🔒 Security Considerations

### Live Code Execution

- **Sandboxing**: Code execution runs in isolated environment
- **Timeout Protection**: Execution has configurable timeouts
- **Network Restrictions**: Network access can be disabled
- **Input Validation**: All code input is validated and sanitized

### Dynamic Data

- **API Security**: Data sources require authentication
- **Input Sanitization**: All dynamic content is sanitized
- **XSS Protection**: HTML content is properly escaped
- **Rate Limiting**: API calls are rate-limited

## 🎯 Usage Examples

### Basic Enhanced Editor

```typescript
import { EnhancedBlockEditor } from '@/lib/pdf-generator';

function MyEditor() {
  return (
    <EnhancedBlockEditor
      enableRemarkJSX={true}
      enableDynamicData={true}
      enableLiveExecution={true}
      defaultBlockState="preview"
      onSave={(blocks, markdown, jsx) => {
        console.log('Saved:', { blocks, markdown, jsx });
      }}
    />
  );
}
```

### Custom State Management

```typescript
import { 
  useBlockEditor, 
  blockStateManager,
  EnhancedBlockContainer 
} from '@/lib/pdf-generator';

function CustomEditor() {
  const { blocks, updateBlock } = useBlockEditor();
  
  const handleStateChange = (blockId: string, state: BlockState) => {
    console.log(`Block ${blockId} transitioned to ${state}`);
  };

  return (
    <div>
      {blocks.map(block => (
        <EnhancedBlockContainer
          key={block.id}
          block={block}
          initialState="preview"
          onUpdate={(updates) => updateBlock(block.id, updates)}
          onStateChange={handleStateChange}
        />
      ))}
    </div>
  );
}
```

### Dynamic Data Integration

```typescript
import { 
  createDynamicList,
  bindBlockToData,
  setupMathRendering 
} from '@/lib/pdf-generator';

// Setup dynamic list
const dynamicList = createDynamicList('list-block-id', [
  { id: '1', content: 'Item 1' },
  { id: '2', content: 'Item 2' }
]);

// Bind block to API data
bindBlockToData({
  blockId: 'table-block-id',
  blockType: 'table',
  dataSourceId: 'api-source',
  fieldMappings: { headers: 'data.headers', rows: 'data.rows' },
  autoUpdate: true,
  updateTriggers: ['data-change']
});

// Setup math rendering
setupMathRendering({
  blockId: 'math-block-id',
  formula: 'E = mc^2',
  engine: 'katex',
  displayMode: 'block',
  autoRender: true
});
```

## 🔄 Migration Guide

### From Basic Block Editor

1. **Import Enhanced Components**:
   ```typescript
   // Before
   import { BlockEditor } from '@/lib/pdf-generator';
   
   // After
   import { EnhancedBlockEditor } from '@/lib/pdf-generator';
   ```

2. **Update Props**:
   ```typescript
   // Before
   <BlockEditor onSave={(blocks, markdown) => save(blocks, markdown)} />
   
   // After
   <EnhancedBlockEditor 
     onSave={(blocks, markdown, jsx) => save(blocks, markdown, jsx)}
     enableRemarkJSX={true}
     enableDynamicData={true}
   />
   ```

3. **Add State Management** (Optional):
   ```typescript
   const handleStateChange = (blockId: string, state: BlockState) => {
     // Handle state changes
   };
   ```

### Enabling Advanced Features

1. **Enable Remark JSX**:
   ```typescript
   <EnhancedBlockEditor enableRemarkJSX={true} />
   ```

2. **Enable Dynamic Data**:
   ```typescript
   <EnhancedBlockEditor enableDynamicData={true} />
   ```

3. **Enable Live Execution**:
   ```typescript
   <EnhancedBlockEditor enableLiveExecution={true} />
   ```

## 🎨 Customization

### Custom Block States

```typescript
// Add custom state
blockStateManager.initializeBlock(blockId, 'custom-state', {
  allowedTransitions: ['preview', 'edit', 'custom-state'],
  autoSave: true,
  validateOnTransition: true
});
```

### Custom Modal Editors

```typescript
// Create custom modal editor
function CustomModalEditor({ block, onChange, onSave }) {
  return (
    <div className="p-4">
      {/* Custom editor UI */}
    </div>
  );
}
```

### Custom Dynamic Data Sources

```typescript
// Register custom data source
dynamicDataManager.registerDataSource({
  id: 'custom-api',
  type: 'api',
  url: 'https://api.example.com/data',
  refreshInterval: 30000
});
```

## 🔮 Future Enhancements

1. **Collaborative Editing**: Real-time collaboration with operational transforms
2. **Plugin System**: Extensible plugin architecture for custom blocks
3. **AI Integration**: AI-powered content generation and suggestions
4. **Advanced Templates**: Template marketplace and custom template builder
5. **Performance Monitoring**: Built-in performance monitoring and optimization
6. **Accessibility**: Enhanced accessibility features and ARIA support

## 📚 API Reference

For detailed API documentation, see:
- [Block State Manager API](./docs/block-state-manager-api.md)
- [Dynamic Data Manager API](./docs/dynamic-data-manager-api.md)
- [Remark JSX Integration API](./docs/remark-jsx-api.md)
- [Modal Editors API](./docs/modal-editors-api.md)

## 🤝 Contributing

When contributing to the refined architecture:

1. Follow the four-state lifecycle pattern
2. Ensure backward compatibility
3. Add comprehensive tests
4. Update documentation
5. Consider performance implications
6. Maintain security best practices
