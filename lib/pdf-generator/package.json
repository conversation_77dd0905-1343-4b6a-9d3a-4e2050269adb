{"name": "pdf-generator", "version": "1.0.0", "description": "A comprehensive PDF document generation library with markdown support, real-time editing capabilities, and seamless integration with existing tools", "main": "index.ts", "types": "index.ts", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build": "tsc", "build:watch": "tsc --watch", "dev": "tsc --watch", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "example": "ts-node examples/basic-usage.ts", "example:all": "ts-node examples/basic-usage.ts && ts-node examples/advanced-usage.ts", "clean": "rm -rf dist", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["pdf", "generation", "markdown", "document", "template", "export", "puppeteer", "react-pdf", "ai-tools", "nextjs", "typescript"], "author": "PDF Generator Library", "license": "MIT", "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0", "next": "^14.0.0 || ^15.0.0", "ai": "^4.0.0", "zod": "^3.22.0"}, "dependencies": {"@react-pdf/renderer": "^4.1.7", "jspdf": "^2.5.2", "marked": "^15.0.4", "html2canvas": "^1.4.1", "puppeteer-core": "^24.8.2", "unified": "^11.0.4", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-frontmatter": "^5.0.0", "katex": "^0.16.8", "prismjs": "^1.29.0", "lodash": "^4.17.21", "uuid": "^9.0.1", "date-fns": "^2.30.0", "dompurify": "^3.0.3", "file-saver": "^2.0.5"}, "devDependencies": {"@types/marked": "^6.0.0", "@types/node": "^22.0.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/dompurify": "^3.0.5", "@types/file-saver": "^2.0.7", "@types/katex": "^0.16.7", "@types/prismjs": "^1.26.3", "@types/jest": "^29.5.8", "typescript": "^5.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "ts-node": "^10.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1"}, "files": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.d.ts", "README.md", "package.json"], "exports": {".": {"import": "./index.ts", "require": "./index.ts", "types": "./index.ts"}, "./components": {"import": "./components/index.ts", "require": "./components/index.ts", "types": "./components/index.ts"}, "./api": {"import": "./api/index.ts", "require": "./api/index.ts", "types": "./api/index.ts"}, "./types": {"import": "./types.ts", "require": "./types.ts", "types": "./types.ts"}, "./config": {"import": "./config.ts", "require": "./config.ts", "types": "./config.ts"}}, "repository": {"type": "git", "url": "git+https://github.com/Sa9eDesigns/app-gen.git", "directory": "lib/pdf-generator"}, "bugs": {"url": "https://github.com/Sa9eDesigns/app-gen/issues"}, "homepage": "https://github.com/Sa9eDesigns/app-gen/tree/main/lib/pdf-generator#readme", "engines": {"node": ">=18.0.0"}, "publishConfig": {"access": "public"}}