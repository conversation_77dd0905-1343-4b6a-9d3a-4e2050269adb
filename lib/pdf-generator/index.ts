/**
 * PDF Generator Library
 * 
 * A comprehensive PDF document generation library with markdown support,
 * real-time editing capabilities, and seamless integration with existing tools.
 */

// Core exports
export * from './core/pdf-generator-service';

// Parsers
export * from './parsers/markdown-parser';

// Generators
export * from './generators/pdf-generator';

// Templates
export * from './templates/template-renderer';

// Utilities
export * from './utils/style-generator';
export * from './utils/validation-service';

// Components and Hooks
export * from './components';
export * from './hooks';

// Block System
export * from './blocks';

// Types and configuration
export * from './types';
export * from './config';

// Main service instance
export { pdfGeneratorService } from './core/pdf-generator-service';

// Re-export commonly used types
export type {
  PDFDocument,
  DocumentTemplate,
  DocumentStyling,
  PDFGenerationOptions,
  ExportOptions,
  MarkdownParsingOptions,
  ParsedMarkdown,
  ValidationError,
  ValidationWarning,
  BatchGenerationOptions,
  ResearchIntegrationOptions,
} from './types';

// Re-export configuration
export {
  config,
  defaultStyling,
  defaultMarkdownOptions,
  defaultPDFOptions,
  builtInTemplates,
  colorSchemes,
  getConfig,
  getTemplate,
  getColorScheme,
} from './config';

/**
 * Quick start functions for common use cases
 */

/**
 * Generate PDF from markdown content
 */
export async function generatePDFFromMarkdown(
  title: string,
  markdown: string,
  options?: {
    template?: string;
    author?: string;
    metadata?: Record<string, any>;
    styling?: Partial<DocumentStyling>;
    pdfOptions?: Partial<PDFGenerationOptions>;
  }
): Promise<Buffer> {
  const { pdfGeneratorService } = await import('./core/pdf-generator-service');
  
  const document = await pdfGeneratorService.createDocument(
    title,
    markdown,
    options?.template || 'research-report',
    {
      author: options?.author || 'Unknown Author',
      ...options?.metadata,
    }
  );

  if (options?.styling) {
    document.styling = { ...document.styling, ...options.styling };
  }

  return pdfGeneratorService.generatePDF(document, options?.pdfOptions);
}

/**
 * Create document from template
 */
export async function createDocumentFromTemplate(
  templateId: string,
  content: {
    title: string;
    markdown: string;
    metadata?: Record<string, any>;
  }
): Promise<PDFDocument> {
  const { pdfGeneratorService } = await import('./core/pdf-generator-service');
  
  return pdfGeneratorService.createDocument(
    content.title,
    content.markdown,
    templateId,
    content.metadata
  );
}

/**
 * Preview document as HTML
 */
export async function previewDocumentHTML(document: PDFDocument): Promise<string> {
  const { pdfGeneratorService } = await import('./core/pdf-generator-service');
  return pdfGeneratorService.previewHTML(document);
}

/**
 * Validate markdown content
 */
export async function validateMarkdown(markdown: string): Promise<{
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}> {
  const { MarkdownParser } = await import('./parsers/markdown-parser');
  const { ValidationService } = await import('./utils/validation-service');
  const { defaultMarkdownOptions } = await import('./config');
  
  const parser = new MarkdownParser(defaultMarkdownOptions);
  const validator = new ValidationService();
  
  try {
    const parsed = await parser.parse(markdown);
    const errors = await validator.validateContent(markdown);
    
    // Create a temporary document for warning generation
    const tempDoc: PDFDocument = {
      id: 'temp',
      title: 'Validation',
      content: markdown,
      metadata: { title: 'Validation', author: 'System', creationDate: new Date(), modificationDate: new Date(), creator: 'System', producer: 'System' },
      template: { id: 'temp', name: 'Temp', type: 'custom', layout: { size: 'A4', orientation: 'portrait', margins: { top: 0, right: 0, bottom: 0, left: 0 } }, sections: [], styling: {} },
      styling: { fonts: {} as any, colors: {} as any, spacing: {} as any, layout: {} as any, theme: 'default' },
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1,
    };
    
    const warnings = validator.generateWarnings(tempDoc, parsed);
    
    return {
      isValid: !errors.some(e => e.severity === 'error'),
      errors,
      warnings,
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [{
        id: 'parse-error',
        type: 'syntax',
        message: error.message,
        severity: 'error',
      }],
      warnings: [],
    };
  }
}

/**
 * Get available templates
 */
export async function getAvailableTemplates(): Promise<Array<{ id: string; name: string; type: string }>> {
  const { pdfGeneratorService } = await import('./core/pdf-generator-service');
  return pdfGeneratorService.getAvailableTemplates();
}

/**
 * Export document in different formats
 */
export async function exportDocument(
  document: PDFDocument,
  format: 'pdf' | 'html' | 'markdown' | 'docx',
  options?: Partial<ExportOptions>
): Promise<Buffer | string> {
  const { pdfGeneratorService } = await import('./core/pdf-generator-service');
  
  const exportOptions: ExportOptions = {
    format,
    destination: 'download',
    includeAssets: true,
    bundleAssets: false,
    ...options,
  };
  
  return pdfGeneratorService.exportDocument(document, exportOptions);
}

/**
 * Batch generate PDFs
 */
export async function batchGeneratePDFs(
  documents: PDFDocument[],
  options?: {
    parallelProcessing?: boolean;
    progressCallback?: (progress: any) => void;
  }
): Promise<Buffer[]> {
  const { pdfGeneratorService } = await import('./core/pdf-generator-service');

  const batchOptions: BatchGenerationOptions = {
    documents: documents.map(d => d.id),
    outputFormat: 'pdf',
    mergeDocuments: false,
    parallelProcessing: options?.parallelProcessing || false,
    progressCallback: options?.progressCallback,
  };

  return pdfGeneratorService.batchGenerate(documents, batchOptions);
}

/**
 * Integration with Deep Research library
 */
export async function generateResearchReport(
  projectId: string,
  options?: {
    template?: 'research-report' | 'academic-paper' | 'executive-summary';
    includeFindings?: boolean;
    includeSources?: boolean;
    includeAnalysis?: boolean;
    autoGenerateReferences?: boolean;
  }
): Promise<PDFDocument> {
  const { pdfGeneratorService } = await import('./core/pdf-generator-service');
  
  const integrationOptions: ResearchIntegrationOptions = {
    projectId,
    template: options?.template || 'research-report',
    includeFindings: options?.includeFindings !== false,
    includeSources: options?.includeSources !== false,
    includeAnalysis: options?.includeAnalysis !== false,
    autoGenerateReferences: options?.autoGenerateReferences !== false,
  };
  
  return pdfGeneratorService.generateFromResearch(projectId, integrationOptions);
}

/**
 * Utility functions
 */

/**
 * Parse markdown and extract structure
 */
export async function parseMarkdownStructure(markdown: string): Promise<{
  toc: any[];
  images: any[];
  links: any[];
  codeBlocks: any[];
  tables: any[];
  metadata: Record<string, any>;
}> {
  const { MarkdownParser } = await import('./parsers/markdown-parser');
  const { defaultMarkdownOptions } = await import('./config');
  
  const parser = new MarkdownParser(defaultMarkdownOptions);
  const parsed = await parser.parse(markdown);
  
  return {
    toc: parsed.toc,
    images: parsed.images,
    links: parsed.links,
    codeBlocks: parsed.codeBlocks,
    tables: parsed.tables,
    metadata: parsed.metadata,
  };
}

/**
 * Generate CSS from styling configuration
 */
export async function generateStyleCSS(styling: DocumentStyling): Promise<string> {
  const { StyleGenerator } = await import('./utils/style-generator');
  const generator = new StyleGenerator();
  return generator.generateCSS(styling);
}

/**
 * Get library status
 */
export async function getLibraryStatus(): Promise<{
  ready: boolean;
  version: string;
  activeJobs: number;
  generatorStatus: any;
}> {
  const { pdfGeneratorService } = await import('./core/pdf-generator-service');
  const status = pdfGeneratorService.getStatus();
  
  return {
    ...status,
    version: '1.0.0',
  };
}

/**
 * Cleanup library resources
 */
export async function cleanup(): Promise<void> {
  const { pdfGeneratorService } = await import('./core/pdf-generator-service');
  await pdfGeneratorService.cleanup();
}

// Enhanced API exports
export * from './api/routes';
export * from './api/ai-tools';

// API route handlers
export {
  routeHandlers,
  handleGeneratePDF,
  handleGenerateFromBlocks,
  handleBatchGenerate,
  handleValidateContent,
  handleGetTemplates,
  handlePreviewHTML,
  handleExportDocument,
  handleGenerateResearchReport,
  handleParseMarkdown,
  handleBlockOperation,
  handleDynamicData,
  handleGetStatus
} from './api/routes';

// Enhanced AI tools
export {
  pdfGeneratorTools,
  toolDescriptions,
  toolCategories,
  toolRegistry,
  getToolsByCategory,
  getAllToolNames,
  getToolByName,
  generatePDFTool,
  generateFromBlocksTool,
  batchGeneratePDFsTool,
  validateMarkdownTool,
  blockOperationTool,
  createDynamicDataTool,
  getTemplatesTool,
  generateResearchReportTool,
  parseMarkdownTool,
  createDocumentTool,
  previewHTMLTool,
  exportDocumentTool
} from './api/ai-tools';

// Default export for convenience
export default {
  generatePDFFromMarkdown,
  createDocumentFromTemplate,
  previewDocumentHTML,
  validateMarkdown,
  getAvailableTemplates,
  exportDocument,
  batchGeneratePDFs,
  generateResearchReport,
  parseMarkdownStructure,
  generateStyleCSS,
  getLibraryStatus,
  cleanup,
  service: pdfGeneratorService,
  // Enhanced API access
  api: {
    routes: routeHandlers,
    tools: pdfGeneratorTools,
  },
};
