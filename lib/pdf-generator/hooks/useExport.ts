/**
 * Export Hook
 * 
 * Hook for handling document exports in multiple formats
 * with progress tracking and download management.
 */

'use client';

import { useState, useCallback, useRef } from 'react';
import { PDFDocument, ExportOptions } from '../types';
import { exportDocument } from '../index';

export interface UseExportOptions {
  onExportStart?: (format: string) => void;
  onExportComplete?: (format: string, result: Buffer | string) => void;
  onExportError?: (format: string, error: Error) => void;
  onProgress?: (progress: number) => void;
}

export interface ExportJob {
  id: string;
  format: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  startTime: Date;
  endTime?: Date;
  error?: Error;
  result?: Buffer | string;
}

export interface UseExportReturn {
  // State
  isExporting: boolean;
  currentJob: ExportJob | null;
  exportHistory: ExportJob[];
  progress: number;
  
  // Actions
  exportToPDF: (document: PDFDocument, options?: Partial<ExportOptions>) => Promise<Buffer>;
  exportToHTML: (document: PDFDocument, options?: Partial<ExportOptions>) => Promise<string>;
  exportToMarkdown: (document: PDFDocument, options?: Partial<ExportOptions>) => Promise<string>;
  exportToFormat: (document: PDFDocument, format: string, options?: Partial<ExportOptions>) => Promise<Buffer | string>;
  downloadFile: (data: Buffer | string, filename: string, mimeType: string) => void;
  clearHistory: () => void;
  
  // Utilities
  getExportStats: () => {
    total: number;
    successful: number;
    failed: number;
    formats: Record<string, number>;
  };
}

export function useExport(options: UseExportOptions = {}): UseExportReturn {
  const {
    onExportStart,
    onExportComplete,
    onExportError,
    onProgress
  } = options;

  // State
  const [isExporting, setIsExporting] = useState(false);
  const [currentJob, setCurrentJob] = useState<ExportJob | null>(null);
  const [exportHistory, setExportHistory] = useState<ExportJob[]>([]);
  const [progress, setProgress] = useState(0);

  // Refs
  const jobIdCounter = useRef(0);

  // Generate job ID
  const generateJobId = useCallback((): string => {
    return `export_${Date.now()}_${++jobIdCounter.current}`;
  }, []);

  // Create export job
  const createJob = useCallback((format: string): ExportJob => {
    return {
      id: generateJobId(),
      format,
      status: 'pending',
      progress: 0,
      startTime: new Date(),
    };
  }, [generateJobId]);

  // Update job progress
  const updateJobProgress = useCallback((job: ExportJob, progress: number) => {
    job.progress = progress;
    setProgress(progress);
    onProgress?.(progress);
  }, [onProgress]);

  // Complete job
  const completeJob = useCallback((job: ExportJob, result: Buffer | string) => {
    job.status = 'completed';
    job.progress = 100;
    job.endTime = new Date();
    job.result = result;
    
    setCurrentJob(null);
    setIsExporting(false);
    setProgress(0);
    
    setExportHistory(prev => [...prev, job]);
    onExportComplete?.(job.format, result);
  }, [onExportComplete]);

  // Fail job
  const failJob = useCallback((job: ExportJob, error: Error) => {
    job.status = 'failed';
    job.endTime = new Date();
    job.error = error;
    
    setCurrentJob(null);
    setIsExporting(false);
    setProgress(0);
    
    setExportHistory(prev => [...prev, job]);
    onExportError?.(job.format, error);
  }, [onExportError]);

  // Generic export function
  const performExport = useCallback(async (
    document: PDFDocument,
    format: string,
    options?: Partial<ExportOptions>
  ): Promise<Buffer | string> => {
    if (isExporting) {
      throw new Error('Export already in progress');
    }

    const job = createJob(format);
    setCurrentJob(job);
    setIsExporting(true);
    
    job.status = 'running';
    onExportStart?.(format);

    try {
      updateJobProgress(job, 10);

      const exportOptions: ExportOptions = {
        format: format as any,
        destination: 'download',
        includeAssets: true,
        bundleAssets: false,
        ...options,
      };

      updateJobProgress(job, 30);

      const result = await exportDocument(document, exportOptions);
      
      updateJobProgress(job, 90);

      completeJob(job, result);
      return result;
    } catch (error) {
      failJob(job, error as Error);
      throw error;
    }
  }, [isExporting, createJob, onExportStart, updateJobProgress, completeJob, failJob]);

  // Export to PDF
  const exportToPDF = useCallback(async (
    document: PDFDocument,
    options?: Partial<ExportOptions>
  ): Promise<Buffer> => {
    const result = await performExport(document, 'pdf', options);
    return result as Buffer;
  }, [performExport]);

  // Export to HTML
  const exportToHTML = useCallback(async (
    document: PDFDocument,
    options?: Partial<ExportOptions>
  ): Promise<string> => {
    const result = await performExport(document, 'html', options);
    return result as string;
  }, [performExport]);

  // Export to Markdown
  const exportToMarkdown = useCallback(async (
    document: PDFDocument,
    options?: Partial<ExportOptions>
  ): Promise<string> => {
    const result = await performExport(document, 'markdown', options);
    return result as string;
  }, [performExport]);

  // Export to any format
  const exportToFormat = useCallback(async (
    document: PDFDocument,
    format: string,
    options?: Partial<ExportOptions>
  ): Promise<Buffer | string> => {
    return performExport(document, format, options);
  }, [performExport]);

  // Download file
  const downloadFile = useCallback((
    data: Buffer | string,
    filename: string,
    mimeType: string
  ): void => {
    try {
      let blob: Blob;
      
      if (typeof data === 'string') {
        blob = new Blob([data], { type: mimeType });
      } else {
        blob = new Blob([data], { type: mimeType });
      }

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      throw new Error(`Download failed: ${error.message}`);
    }
  }, []);

  // Clear export history
  const clearHistory = useCallback(() => {
    setExportHistory([]);
  }, []);

  // Get export statistics
  const getExportStats = useCallback(() => {
    const total = exportHistory.length;
    const successful = exportHistory.filter(job => job.status === 'completed').length;
    const failed = exportHistory.filter(job => job.status === 'failed').length;
    
    const formats = exportHistory.reduce((acc, job) => {
      acc[job.format] = (acc[job.format] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      successful,
      failed,
      formats,
    };
  }, [exportHistory]);

  return {
    // State
    isExporting,
    currentJob,
    exportHistory,
    progress,
    
    // Actions
    exportToPDF,
    exportToHTML,
    exportToMarkdown,
    exportToFormat,
    downloadFile,
    clearHistory,
    
    // Utilities
    getExportStats,
  };
}
