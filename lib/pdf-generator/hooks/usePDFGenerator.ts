/**
 * PDF Generator Hook
 * 
 * Main hook for PDF generation functionality with state management,
 * error handling, and progress tracking.
 */

'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  PDFDocument,
  PDFGenerationOptions,
  ExportOptions,
  ValidationError,
  ValidationWarning
} from '../types';
import { 
  pdfGeneratorService,
  generatePDFFromMarkdown,
  validateMarkdown,
  previewDocumentHTML
} from '../index';

export interface UsePDFGeneratorOptions {
  autoValidate?: boolean;
  autoPreview?: boolean;
  debounceMs?: number;
  onError?: (error: Error) => void;
  onSuccess?: (result: any) => void;
}

export interface UsePDFGeneratorReturn {
  // State
  document: PDFDocument | null;
  isGenerating: boolean;
  isValidating: boolean;
  isPreviewLoading: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  previewHtml: string;
  progress: number;
  
  // Actions
  createDocument: (title: string, content: string, templateId?: string, metadata?: Record<string, any>) => Promise<PDFDocument>;
  updateDocument: (updates: Partial<PDFDocument>) => Promise<void>;
  generatePDF: (options?: PDFGenerationOptions) => Promise<Buffer>;
  exportDocument: (format: string, options?: Partial<ExportOptions>) => Promise<Buffer | string>;
  validateContent: (content?: string) => Promise<void>;
  updatePreview: (content?: string) => Promise<void>;
  reset: () => void;
  
  // Utilities
  isDirty: boolean;
  canGenerate: boolean;
  lastSaved: Date | null;
}

export function usePDFGenerator(options: UsePDFGeneratorOptions = {}): UsePDFGeneratorReturn {
  const {
    autoValidate = true,
    autoPreview = true,
    debounceMs = 500,
    onError,
    onSuccess
  } = options;

  // State
  const [document, setDocument] = useState<PDFDocument | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [warnings, setWarnings] = useState<ValidationWarning[]>([]);
  const [previewHtml, setPreviewHtml] = useState('');
  const [progress, setProgress] = useState(0);
  const [isDirty, setIsDirty] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Refs for debouncing
  const validationTimeoutRef = useRef<NodeJS.Timeout>(null);
  const previewTimeoutRef = useRef<NodeJS.Timeout>(null);
  const originalDocumentRef = useRef<PDFDocument | null>(null);

  // Create document
  const createDocument = useCallback(async (
    title: string,
    content: string,
    templateId: string = 'research-report',
    metadata: Record<string, any> = {}
  ): Promise<PDFDocument> => {
    try {
      setProgress(10);
      const newDocument = await pdfGeneratorService.createDocument(title, content, templateId, metadata);
      setDocument(newDocument);
      originalDocumentRef.current = { ...newDocument };
      setIsDirty(false);
      setLastSaved(new Date());
      setProgress(100);
      
      // Auto-validate and preview
      if (autoValidate) {
        await validateContent(content);
      }
      if (autoPreview) {
        await updatePreview(content);
      }
      
      onSuccess?.(newDocument);
      return newDocument;
    } catch (error) {
      setProgress(0);
      onError?.(error as Error);
      throw error;
    }
  }, [autoValidate, autoPreview, onError, onSuccess]);

  // Update document
  const updateDocument = useCallback(async (updates: Partial<PDFDocument>): Promise<void> => {
    if (!document) return;

    try {
      const updatedDocument = { ...document, ...updates, updatedAt: new Date() };
      setDocument(updatedDocument);
      setIsDirty(true);

      // Auto-validate if content changed
      if (updates.content && autoValidate) {
        if (validationTimeoutRef.current) {
          clearTimeout(validationTimeoutRef.current);
        }
        validationTimeoutRef.current = setTimeout(() => {
          validateContent(updates.content);
        }, debounceMs);
      }

      // Auto-preview if content changed
      if (updates.content && autoPreview) {
        if (previewTimeoutRef.current) {
          clearTimeout(previewTimeoutRef.current);
        }
        previewTimeoutRef.current = setTimeout(() => {
          updatePreview(updates.content);
        }, debounceMs);
      }
    } catch (error) {
      onError?.(error as Error);
    }
  }, [document, autoValidate, autoPreview, debounceMs, onError]);

  // Generate PDF
  const generatePDF = useCallback(async (options?: PDFGenerationOptions): Promise<Buffer> => {
    if (!document) throw new Error('No document to generate');

    setIsGenerating(true);
    setProgress(0);

    try {
      // Validate first
      const validation = await validateMarkdown(document.content);
      if (!validation.isValid) {
        throw new Error(`Document validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
      }

      setProgress(25);
      const pdfBuffer = await generatePDFFromMarkdown(
        document.title,
        document.content,
        {
          template: document.template.id,
          author: document.metadata.author,
          metadata: document.metadata,
          ...options,
        }
      );

      setProgress(100);
      onSuccess?.(pdfBuffer);
      return pdfBuffer;
    } catch (error) {
      setProgress(0);
      onError?.(error as Error);
      throw error;
    } finally {
      setIsGenerating(false);
    }
  }, [document, onError, onSuccess]);

  // Export document
  const exportDocument = useCallback(async (
    format: string,
    options?: Partial<ExportOptions>
  ): Promise<Buffer | string> => {
    if (!document) throw new Error('No document to export');

    setIsGenerating(true);
    setProgress(0);

    try {
      setProgress(25);
      const result = await pdfGeneratorService.exportDocument(document, {
        format: format as any,
        destination: 'download',
        includeAssets: true,
        bundleAssets: false,
        ...options,
      });

      setProgress(100);
      onSuccess?.(result);
      return result;
    } catch (error) {
      setProgress(0);
      onError?.(error as Error);
      throw error;
    } finally {
      setIsGenerating(false);
    }
  }, [document, onError, onSuccess]);

  // Validate content
  const validateContent = useCallback(async (content?: string): Promise<void> => {
    const contentToValidate = content || document?.content;
    if (!contentToValidate) return;

    setIsValidating(true);
    try {
      const validation = await validateMarkdown(contentToValidate);
      setErrors(validation.errors);
      setWarnings(validation.warnings);
    } catch (error) {
      onError?.(error as Error);
    } finally {
      setIsValidating(false);
    }
  }, [document, onError]);

  // Update preview
  const updatePreview = useCallback(async (content?: string): Promise<void> => {
    if (!document) return;

    const contentToPreview = content || document.content;
    setIsPreviewLoading(true);

    try {
      const tempDocument = { ...document, content: contentToPreview };
      const html = await previewDocumentHTML(tempDocument);
      setPreviewHtml(html);
    } catch (error) {
      onError?.(error as Error);
    } finally {
      setIsPreviewLoading(false);
    }
  }, [document, onError]);

  // Reset state
  const reset = useCallback(() => {
    setDocument(null);
    setIsGenerating(false);
    setIsValidating(false);
    setIsPreviewLoading(false);
    setErrors([]);
    setWarnings([]);
    setPreviewHtml('');
    setProgress(0);
    setIsDirty(false);
    setLastSaved(null);
    originalDocumentRef.current = null;

    // Clear timeouts
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }
    if (previewTimeoutRef.current) {
      clearTimeout(previewTimeoutRef.current);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
      if (previewTimeoutRef.current) {
        clearTimeout(previewTimeoutRef.current);
      }
    };
  }, []);

  // Computed values
  const canGenerate = document !== null && errors.length === 0 && !isGenerating;

  return {
    // State
    document,
    isGenerating,
    isValidating,
    isPreviewLoading,
    errors,
    warnings,
    previewHtml,
    progress,
    
    // Actions
    createDocument,
    updateDocument,
    generatePDF,
    exportDocument,
    validateContent,
    updatePreview,
    reset,
    
    // Utilities
    isDirty,
    canGenerate,
    lastSaved,
  };
}
