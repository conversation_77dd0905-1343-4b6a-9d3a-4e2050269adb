/**
 * Validation Hook
 * 
 * Hook for real-time content validation with debouncing,
 * error tracking, and validation statistics.
 */

'use client';

import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { ValidationError, ValidationWarning } from '../types';
import { validateMarkdown } from '../index';

export interface UseValidationOptions {
  debounceMs?: number;
  autoValidate?: boolean;
  validateOnMount?: boolean;
  onValidationComplete?: (isValid: boolean, errors: ValidationError[], warnings: ValidationWarning[]) => void;
  onError?: (error: Error) => void;
}

export interface UseValidationReturn {
  // State
  isValidating: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  isValid: boolean;
  lastValidated: Date | null;
  validationCount: number;
  
  // Actions
  validate: (content: string) => Promise<void>;
  clearValidation: () => void;
  
  // Computed
  errorCount: number;
  warningCount: number;
  errorsByType: Record<string, ValidationError[]>;
  warningsByType: Record<string, ValidationWarning[]>;
  validationSummary: {
    total: number;
    errors: number;
    warnings: number;
    isValid: boolean;
    lastValidated: Date | null;
  };
}

export function useValidation(options: UseValidationOptions = {}): UseValidationReturn {
  const {
    debounceMs = 500,
    autoValidate = true,
    validateOnMount = false,
    onValidationComplete,
    onError
  } = options;

  // State
  const [isValidating, setIsValidating] = useState(false);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [warnings, setWarnings] = useState<ValidationWarning[]>([]);
  const [lastValidated, setLastValidated] = useState<Date | null>(null);
  const [validationCount, setValidationCount] = useState(0);

  // Refs
  const validationTimeoutRef = useRef<NodeJS.Timeout>();
  const currentContentRef = useRef<string>('');

  // Validate content
  const validate = useCallback(async (content: string): Promise<void> => {
    // Clear existing timeout
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }

    currentContentRef.current = content;

    // Debounce validation
    if (autoValidate && debounceMs > 0) {
      validationTimeoutRef.current = setTimeout(async () => {
        await performValidation(content);
      }, debounceMs);
    } else {
      await performValidation(content);
    }
  }, [autoValidate, debounceMs]);

  // Perform actual validation
  const performValidation = useCallback(async (content: string): Promise<void> => {
    if (!content.trim()) {
      setErrors([]);
      setWarnings([]);
      setLastValidated(new Date());
      setValidationCount(prev => prev + 1);
      onValidationComplete?.(true, [], []);
      return;
    }

    setIsValidating(true);

    try {
      const validation = await validateMarkdown(content);
      
      setErrors(validation.errors);
      setWarnings(validation.warnings);
      setLastValidated(new Date());
      setValidationCount(prev => prev + 1);
      
      onValidationComplete?.(validation.isValid, validation.errors, validation.warnings);
    } catch (error) {
      const err = error as Error;
      setErrors([{
        id: 'validation-error',
        type: 'syntax',
        message: `Validation failed: ${err.message}`,
        severity: 'error',
      }]);
      setWarnings([]);
      onError?.(err);
    } finally {
      setIsValidating(false);
    }
  }, [onValidationComplete, onError]);

  // Clear validation
  const clearValidation = useCallback(() => {
    setErrors([]);
    setWarnings([]);
    setLastValidated(null);
    setValidationCount(0);
    
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, []);

  // Validate on mount if requested
  useEffect(() => {
    if (validateOnMount && currentContentRef.current) {
      validate(currentContentRef.current);
    }
  }, [validateOnMount, validate]);

  // Computed values
  const isValid = useMemo(() => {
    return errors.length === 0;
  }, [errors]);

  const errorCount = useMemo(() => errors.length, [errors]);
  const warningCount = useMemo(() => warnings.length, [warnings]);

  const errorsByType = useMemo(() => {
    return errors.reduce((acc, error) => {
      if (!acc[error.type]) {
        acc[error.type] = [];
      }
      acc[error.type].push(error);
      return acc;
    }, {} as Record<string, ValidationError[]>);
  }, [errors]);

  const warningsByType = useMemo(() => {
    return warnings.reduce((acc, warning) => {
      const type = 'warning'; // Warnings don't have a type field in the current interface
      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(warning);
      return acc;
    }, {} as Record<string, ValidationWarning[]>);
  }, [warnings]);

  const validationSummary = useMemo(() => ({
    total: validationCount,
    errors: errorCount,
    warnings: warningCount,
    isValid,
    lastValidated,
  }), [validationCount, errorCount, warningCount, isValid, lastValidated]);

  return {
    // State
    isValidating,
    errors,
    warnings,
    isValid,
    lastValidated,
    validationCount,
    
    // Actions
    validate,
    clearValidation,
    
    // Computed
    errorCount,
    warningCount,
    errorsByType,
    warningsByType,
    validationSummary,
  };
}
