/**
 * Templates Hook
 * 
 * Hook for managing document templates, including loading, caching,
 * and template-specific operations.
 */

'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { DocumentTemplate } from '../types';
import { getAvailableTemplates } from '../index';
import { TemplateRenderer } from '../templates/template-renderer';

export interface UseTemplatesOptions {
  autoLoad?: boolean;
  cacheTemplates?: boolean;
  onError?: (error: Error) => void;
}

export interface UseTemplatesReturn {
  // State
  templates: Array<{ id: string; name: string; type: string }>;
  selectedTemplate: DocumentTemplate | null;
  isLoading: boolean;
  error: Error | null;
  
  // Actions
  loadTemplates: () => Promise<void>;
  selectTemplate: (templateId: string) => Promise<DocumentTemplate | null>;
  getTemplate: (templateId: string) => DocumentTemplate | null;
  validateTemplate: (template: DocumentTemplate) => string[];
  
  // Computed
  templatesByType: Record<string, Array<{ id: string; name: string; type: string }>>;
  templateOptions: Array<{ value: string; label: string; type: string }>;
}

// Template cache
const templateCache = new Map<string, DocumentTemplate>();

export function useTemplates(options: UseTemplatesOptions = {}): UseTemplatesReturn {
  const {
    autoLoad = true,
    cacheTemplates = true,
    onError
  } = options;

  // State
  const [templates, setTemplates] = useState<Array<{ id: string; name: string; type: string }>>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Template renderer instance
  const templateRenderer = useMemo(() => new TemplateRenderer(), []);

  // Load templates
  const loadTemplates = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      const availableTemplates = await getAvailableTemplates();
      setTemplates(availableTemplates);
    } catch (err) {
      const error = err as Error;
      setError(error);
      onError?.(error);
    } finally {
      setIsLoading(false);
    }
  }, [onError]);

  // Select template
  const selectTemplate = useCallback(async (templateId: string): Promise<DocumentTemplate | null> => {
    try {
      // Check cache first
      if (cacheTemplates && templateCache.has(templateId)) {
        const cachedTemplate = templateCache.get(templateId)!;
        setSelectedTemplate(cachedTemplate);
        return cachedTemplate;
      }

      // Load template
      const template = templateRenderer.getTemplate(templateId);
      if (!template) {
        throw new Error(`Template not found: ${templateId}`);
      }

      // Cache template
      if (cacheTemplates) {
        templateCache.set(templateId, template);
      }

      setSelectedTemplate(template);
      return template;
    } catch (err) {
      const error = err as Error;
      setError(error);
      onError?.(error);
      return null;
    }
  }, [templateRenderer, cacheTemplates, onError]);

  // Get template without selecting
  const getTemplate = useCallback((templateId: string): DocumentTemplate | null => {
    // Check cache first
    if (cacheTemplates && templateCache.has(templateId)) {
      return templateCache.get(templateId)!;
    }

    // Load template
    const template = templateRenderer.getTemplate(templateId);
    if (template && cacheTemplates) {
      templateCache.set(templateId, template);
    }

    return template;
  }, [templateRenderer, cacheTemplates]);

  // Validate template
  const validateTemplate = useCallback((template: DocumentTemplate): string[] => {
    return templateRenderer.validateTemplate(template);
  }, [templateRenderer]);

  // Auto-load templates on mount
  useEffect(() => {
    if (autoLoad) {
      loadTemplates();
    }
  }, [autoLoad, loadTemplates]);

  // Computed values
  const templatesByType = useMemo(() => {
    return templates.reduce((acc, template) => {
      if (!acc[template.type]) {
        acc[template.type] = [];
      }
      acc[template.type].push(template);
      return acc;
    }, {} as Record<string, Array<{ id: string; name: string; type: string }>>);
  }, [templates]);

  const templateOptions = useMemo(() => {
    return templates.map(template => ({
      value: template.id,
      label: template.name,
      type: template.type,
    }));
  }, [templates]);

  return {
    // State
    templates,
    selectedTemplate,
    isLoading,
    error,
    
    // Actions
    loadTemplates,
    selectTemplate,
    getTemplate,
    validateTemplate,
    
    // Computed
    templatesByType,
    templateOptions,
  };
}
