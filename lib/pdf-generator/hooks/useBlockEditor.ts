/**
 * Block Editor Hook
 * 
 * Main hook for managing block-based editor state and operations
 */

'use client';

import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { 
  Block, 
  BlockType, 
  BlockEditorState, 
  BlockSelection, 
  BlockOperation,
  BlockValidationError,
  BlockEditorConfig,
  BlockEditorCallbacks
} from '../blocks/types';
import { 
  createBlock, 
  duplicateBlock, 
  convertBlock, 
  validateBlock,
  blocksToMarkdown,
  markdownToBlocks,
  generateBlockId
} from '../blocks/utils';

export interface UseBlockEditorOptions extends Partial<BlockEditorConfig>, BlockEditorCallbacks {
  initialBlocks?: Block[];
  initialMarkdown?: string;
}

export interface UseBlockEditorReturn {
  // State
  blocks: Block[];
  selection: BlockSelection | null;
  focusedBlockId: string | null;
  isEditing: boolean;
  draggedBlockId: string | null;
  validationErrors: BlockValidationError[];
  
  // Actions
  addBlock: (type: BlockType, position?: number, data?: any) => Block;
  deleteBlock: (blockId: string) => void;
  updateBlock: (blockId: string, updates: Partial<Block>) => void;
  moveBlock: (blockId: string, newPosition: number) => void;
  duplicateBlockById: (blockId: string) => Block;
  convertBlockType: (blockId: string, newType: BlockType) => boolean;
  
  // Selection and focus
  selectBlock: (blockId: string) => void;
  selectRange: (startBlockId: string, endBlockId: string) => void;
  clearSelection: () => void;
  focusBlock: (blockId: string) => void;
  focusNextBlock: (currentBlockId: string) => void;
  focusPreviousBlock: (currentBlockId: string) => void;
  
  // Drag and drop
  startDrag: (blockId: string) => void;
  endDrag: () => void;
  
  // Clipboard operations
  copyBlock: (blockId: string) => void;
  cutBlock: (blockId: string) => void;
  pasteBlock: (position?: number) => void;
  
  // Bulk operations
  deleteSelectedBlocks: () => void;
  duplicateSelectedBlocks: () => void;
  
  // Content conversion
  toMarkdown: () => string;
  fromMarkdown: (markdown: string) => void;
  
  // History
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  
  // Validation
  validateAllBlocks: () => BlockValidationError[];
  validateBlockById: (blockId: string) => BlockValidationError[];
  
  // Utilities
  getBlockIndex: (blockId: string) => number;
  getBlockById: (blockId: string) => Block | undefined;
  isEmpty: boolean;
  blockCount: number;
}

const defaultConfig: BlockEditorConfig = {
  allowedBlockTypes: ['paragraph', 'heading', 'code', 'quote', 'list', 'table', 'image', 'divider', 'embed', 'math', 'callout'],
  enableDragDrop: true,
  enableKeyboardShortcuts: true,
  autoSave: false,
  autoSaveInterval: 5000,
  placeholder: 'Start writing...',
  theme: 'light',
};

export function useBlockEditor(options: UseBlockEditorOptions = {}): UseBlockEditorReturn {
  const config = { ...defaultConfig, ...options };
  
  // Initialize blocks
  const initialBlocks = useMemo(() => {
    if (options.initialBlocks) {
      return options.initialBlocks;
    }
    if (options.initialMarkdown) {
      return markdownToBlocks(options.initialMarkdown);
    }
    return [createBlock('paragraph')];
  }, [options.initialBlocks, options.initialMarkdown]);

  // State
  const [editorState, setEditorState] = useState<BlockEditorState>({
    blocks: initialBlocks,
    selection: null,
    focusedBlockId: null,
    isEditing: false,
    draggedBlockId: null,
    clipboard: null,
    history: {
      past: [],
      present: initialBlocks,
      future: [],
    },
  });

  const [validationErrors, setValidationErrors] = useState<BlockValidationError[]>([]);
  
  // Refs
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  // Update history when blocks change
  const updateHistory = useCallback((newBlocks: Block[]) => {
    setEditorState(prev => ({
      ...prev,
      blocks: newBlocks,
      history: {
        past: [...prev.history.past, prev.history.present],
        present: newBlocks,
        future: [],
      },
    }));
  }, []);

  // Add block
  const addBlock = useCallback((type: BlockType, position?: number, data?: any): Block => {
    if (!config.allowedBlockTypes.includes(type)) {
      throw new Error(`Block type ${type} is not allowed`);
    }

    const newBlock = createBlock(type, data);
    const insertPosition = position ?? editorState.blocks.length;
    
    const newBlocks = [...editorState.blocks];
    newBlocks.splice(insertPosition, 0, newBlock);
    
    updateHistory(newBlocks);
    options.onBlockAdd?.(newBlock, insertPosition);
    options.onChange?.(newBlocks);
    
    return newBlock;
  }, [config.allowedBlockTypes, editorState.blocks, updateHistory, options]);

  // Delete block
  const deleteBlock = useCallback((blockId: string) => {
    const newBlocks = editorState.blocks.filter(block => block.id !== blockId);
    
    // If no blocks remain, add a default paragraph
    if (newBlocks.length === 0) {
      newBlocks.push(createBlock('paragraph'));
    }
    
    updateHistory(newBlocks);
    options.onBlockDelete?.(blockId);
    options.onChange?.(newBlocks);
  }, [editorState.blocks, updateHistory, options]);

  // Update block
  const updateBlock = useCallback((blockId: string, updates: Partial<Block>) => {
    const newBlocks = editorState.blocks.map(block => 
      block.id === blockId 
        ? { ...block, ...updates, updatedAt: new Date() }
        : block
    );
    
    updateHistory(newBlocks);
    
    const updatedBlock = newBlocks.find(b => b.id === blockId);
    if (updatedBlock) {
      options.onBlockUpdate?.(updatedBlock);
    }
    options.onChange?.(newBlocks);
  }, [editorState.blocks, updateHistory, options]);

  // Move block
  const moveBlock = useCallback((blockId: string, newPosition: number) => {
    const currentIndex = editorState.blocks.findIndex(block => block.id === blockId);
    if (currentIndex === -1) return;

    const newBlocks = [...editorState.blocks];
    const [movedBlock] = newBlocks.splice(currentIndex, 1);
    newBlocks.splice(newPosition, 0, movedBlock);
    
    updateHistory(newBlocks);
    options.onBlockMove?.(blockId, currentIndex, newPosition);
    options.onChange?.(newBlocks);
  }, [editorState.blocks, updateHistory, options]);

  // Duplicate block
  const duplicateBlockById = useCallback((blockId: string): Block => {
    const block = editorState.blocks.find(b => b.id === blockId);
    if (!block) throw new Error('Block not found');

    const duplicated = duplicateBlock(block);
    const position = editorState.blocks.findIndex(b => b.id === blockId) + 1;
    
    const newBlocks = [...editorState.blocks];
    newBlocks.splice(position, 0, duplicated);
    
    updateHistory(newBlocks);
    options.onChange?.(newBlocks);
    
    return duplicated;
  }, [editorState.blocks, updateHistory, options]);

  // Convert block type
  const convertBlockType = useCallback((blockId: string, newType: BlockType): boolean => {
    const block = editorState.blocks.find(b => b.id === blockId);
    if (!block) return false;

    const converted = convertBlock(block, newType);
    if (!converted) return false;

    const newBlocks = editorState.blocks.map(b => 
      b.id === blockId ? { ...converted, id: blockId } : b
    );
    
    updateHistory(newBlocks);
    options.onBlockConvert?.(blockId, block.type, newType);
    options.onChange?.(newBlocks);
    
    return true;
  }, [editorState.blocks, updateHistory, options]);

  // Selection management
  const selectBlock = useCallback((blockId: string) => {
    const selection: BlockSelection = {
      start: { blockId, index: 0 },
      isRange: false,
    };
    
    setEditorState(prev => ({ ...prev, selection }));
    options.onSelectionChange?.(selection);
  }, [options]);

  const selectRange = useCallback((startBlockId: string, endBlockId: string) => {
    const startIndex = editorState.blocks.findIndex(b => b.id === startBlockId);
    const endIndex = editorState.blocks.findIndex(b => b.id === endBlockId);
    
    if (startIndex === -1 || endIndex === -1) return;

    const selection: BlockSelection = {
      start: { blockId: startBlockId, index: startIndex },
      end: { blockId: endBlockId, index: endIndex },
      isRange: true,
    };
    
    setEditorState(prev => ({ ...prev, selection }));
    options.onSelectionChange?.(selection);
  }, [editorState.blocks, options]);

  const clearSelection = useCallback(() => {
    setEditorState(prev => ({ ...prev, selection: null }));
    options.onSelectionChange?.(null);
  }, [options]);

  // Focus management
  const focusBlock = useCallback((blockId: string) => {
    setEditorState(prev => ({ ...prev, focusedBlockId: blockId }));
    options.onFocusChange?.(blockId);
  }, [options]);

  const focusNextBlock = useCallback((currentBlockId: string) => {
    const currentIndex = editorState.blocks.findIndex(b => b.id === currentBlockId);
    if (currentIndex < editorState.blocks.length - 1) {
      const nextBlock = editorState.blocks[currentIndex + 1];
      focusBlock(nextBlock.id);
    }
  }, [editorState.blocks, focusBlock]);

  const focusPreviousBlock = useCallback((currentBlockId: string) => {
    const currentIndex = editorState.blocks.findIndex(b => b.id === currentBlockId);
    if (currentIndex > 0) {
      const prevBlock = editorState.blocks[currentIndex - 1];
      focusBlock(prevBlock.id);
    }
  }, [editorState.blocks, focusBlock]);

  // Drag and drop
  const startDrag = useCallback((blockId: string) => {
    setEditorState(prev => ({ ...prev, draggedBlockId: blockId }));
  }, []);

  const endDrag = useCallback(() => {
    setEditorState(prev => ({ ...prev, draggedBlockId: null }));
  }, []);

  // Clipboard operations
  const copyBlock = useCallback((blockId: string) => {
    const block = editorState.blocks.find(b => b.id === blockId);
    if (block) {
      setEditorState(prev => ({ ...prev, clipboard: block }));
    }
  }, [editorState.blocks]);

  const cutBlock = useCallback((blockId: string) => {
    copyBlock(blockId);
    deleteBlock(blockId);
  }, [copyBlock, deleteBlock]);

  const pasteBlock = useCallback((position?: number) => {
    if (editorState.clipboard) {
      const duplicated = duplicateBlock(editorState.clipboard);
      const insertPosition = position ?? editorState.blocks.length;
      
      const newBlocks = [...editorState.blocks];
      newBlocks.splice(insertPosition, 0, duplicated);
      
      updateHistory(newBlocks);
      options.onChange?.(newBlocks);
    }
  }, [editorState.clipboard, editorState.blocks, updateHistory, options]);

  // Bulk operations
  const deleteSelectedBlocks = useCallback(() => {
    if (!editorState.selection) return;

    if (editorState.selection.isRange && editorState.selection.end) {
      const startIndex = Math.min(editorState.selection.start.index, editorState.selection.end.index);
      const endIndex = Math.max(editorState.selection.start.index, editorState.selection.end.index);
      
      const newBlocks = editorState.blocks.filter((_, index) => 
        index < startIndex || index > endIndex
      );
      
      if (newBlocks.length === 0) {
        newBlocks.push(createBlock('paragraph'));
      }
      
      updateHistory(newBlocks);
      options.onChange?.(newBlocks);
    } else {
      deleteBlock(editorState.selection.start.blockId);
    }
    
    clearSelection();
  }, [editorState.selection, editorState.blocks, updateHistory, deleteBlock, clearSelection, options]);

  const duplicateSelectedBlocks = useCallback(() => {
    if (!editorState.selection) return;

    if (editorState.selection.isRange && editorState.selection.end) {
      const startIndex = Math.min(editorState.selection.start.index, editorState.selection.end.index);
      const endIndex = Math.max(editorState.selection.start.index, editorState.selection.end.index);
      
      const selectedBlocks = editorState.blocks.slice(startIndex, endIndex + 1);
      const duplicatedBlocks = selectedBlocks.map(duplicateBlock);
      
      const newBlocks = [...editorState.blocks];
      newBlocks.splice(endIndex + 1, 0, ...duplicatedBlocks);
      
      updateHistory(newBlocks);
      options.onChange?.(newBlocks);
    } else {
      duplicateBlockById(editorState.selection.start.blockId);
    }
  }, [editorState.selection, editorState.blocks, updateHistory, duplicateBlockById, options]);

  // Content conversion
  const toMarkdown = useCallback((): string => {
    return blocksToMarkdown(editorState.blocks);
  }, [editorState.blocks]);

  const fromMarkdown = useCallback((markdown: string) => {
    const newBlocks = markdownToBlocks(markdown);
    updateHistory(newBlocks);
    options.onChange?.(newBlocks);
  }, [updateHistory, options]);

  // History operations
  const undo = useCallback(() => {
    if (editorState.history.past.length === 0) return;

    const previous = editorState.history.past[editorState.history.past.length - 1];
    const newPast = editorState.history.past.slice(0, -1);

    setEditorState(prev => ({
      ...prev,
      blocks: previous,
      history: {
        past: newPast,
        present: previous,
        future: [prev.history.present, ...prev.history.future],
      },
    }));

    options.onChange?.(previous);
  }, [editorState.history, options]);

  const redo = useCallback(() => {
    if (editorState.history.future.length === 0) return;

    const next = editorState.history.future[0];
    const newFuture = editorState.history.future.slice(1);

    setEditorState(prev => ({
      ...prev,
      blocks: next,
      history: {
        past: [...prev.history.past, prev.history.present],
        present: next,
        future: newFuture,
      },
    }));

    options.onChange?.(next);
  }, [editorState.history, options]);

  // Validation
  const validateAllBlocks = useCallback((): BlockValidationError[] => {
    const errors = editorState.blocks.flatMap(validateBlock);
    setValidationErrors(errors);
    options.onValidationChange?.(errors);
    return errors;
  }, [editorState.blocks, options]);

  const validateBlockById = useCallback((blockId: string): BlockValidationError[] => {
    const block = editorState.blocks.find(b => b.id === blockId);
    return block ? validateBlock(block) : [];
  }, [editorState.blocks]);

  // Utility functions
  const getBlockIndex = useCallback((blockId: string): number => {
    return editorState.blocks.findIndex(block => block.id === blockId);
  }, [editorState.blocks]);

  const getBlockById = useCallback((blockId: string): Block | undefined => {
    return editorState.blocks.find(block => block.id === blockId);
  }, [editorState.blocks]);

  // Auto-save
  useEffect(() => {
    if (config.autoSave) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      
      autoSaveTimeoutRef.current = setTimeout(() => {
        // Auto-save logic would go here
        console.log('Auto-saving blocks...');
      }, config.autoSaveInterval);
    }

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [editorState.blocks, config.autoSave, config.autoSaveInterval]);

  // Validate blocks when they change
  useEffect(() => {
    validateAllBlocks();
  }, [validateAllBlocks]);

  // Computed values
  const canUndo = editorState.history.past.length > 0;
  const canRedo = editorState.history.future.length > 0;
  const isEmpty = editorState.blocks.length === 1 && 
                  editorState.blocks[0].type === 'paragraph' && 
                  !editorState.blocks[0].content.trim();
  const blockCount = editorState.blocks.length;

  return {
    // State
    blocks: editorState.blocks,
    selection: editorState.selection,
    focusedBlockId: editorState.focusedBlockId,
    isEditing: editorState.isEditing,
    draggedBlockId: editorState.draggedBlockId,
    validationErrors,
    
    // Actions
    addBlock,
    deleteBlock,
    updateBlock,
    moveBlock,
    duplicateBlockById,
    convertBlockType,
    
    // Selection and focus
    selectBlock,
    selectRange,
    clearSelection,
    focusBlock,
    focusNextBlock,
    focusPreviousBlock,
    
    // Drag and drop
    startDrag,
    endDrag,
    
    // Clipboard operations
    copyBlock,
    cutBlock,
    pasteBlock,
    
    // Bulk operations
    deleteSelectedBlocks,
    duplicateSelectedBlocks,
    
    // Content conversion
    toMarkdown,
    fromMarkdown,
    
    // History
    undo,
    redo,
    canUndo,
    canRedo,
    
    // Validation
    validateAllBlocks,
    validateBlockById,
    
    // Utilities
    getBlockIndex,
    getBlockById,
    isEmpty,
    blockCount,
  };
}
