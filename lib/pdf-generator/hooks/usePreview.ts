/**
 * Preview Hook
 * 
 * Hook for managing document preview with different modes,
 * real-time updates, and caching.
 */

'use client';

import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { PDFDocument, PreviewMode } from '../types';
import { previewDocumentHTML, parseMarkdownStructure } from '../index';

export interface UsePreviewOptions {
  debounceMs?: number;
  autoUpdate?: boolean;
  cachePreview?: boolean;
  defaultMode?: PreviewMode;
  onError?: (error: Error) => void;
  onPreviewUpdate?: (html: string, mode: PreviewMode) => void;
}

export interface UsePreviewReturn {
  // State
  previewHtml: string;
  previewMode: PreviewMode;
  isLoading: boolean;
  error: Error | null;
  lastUpdated: Date | null;
  
  // Actions
  updatePreview: (document: PDFDocument) => Promise<void>;
  setPreviewMode: (mode: PreviewMode) => void;
  clearPreview: () => void;
  refreshPreview: () => Promise<void>;
  
  // Computed
  hasPreview: boolean;
  previewStats: {
    wordCount: number;
    characterCount: number;
    headingCount: number;
    imageCount: number;
    linkCount: number;
  };
}

// Preview cache
const previewCache = new Map<string, { html: string; timestamp: Date }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

export function usePreview(options: UsePreviewOptions = {}): UsePreviewReturn {
  const {
    debounceMs = 300,
    autoUpdate = true,
    cachePreview = true,
    defaultMode = 'split',
    onError,
    onPreviewUpdate
  } = options;

  // State
  const [previewHtml, setPreviewHtml] = useState('');
  const [previewMode, setPreviewModeState] = useState<PreviewMode>(defaultMode);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Refs
  const updateTimeoutRef = useRef<NodeJS.Timeout>();
  const currentDocumentRef = useRef<PDFDocument | null>(null);
  const previewStatsRef = useRef({
    wordCount: 0,
    characterCount: 0,
    headingCount: 0,
    imageCount: 0,
    linkCount: 0,
  });

  // Generate cache key
  const generateCacheKey = useCallback((document: PDFDocument): string => {
    return `${document.id}-${document.version}-${document.content.length}`;
  }, []);

  // Check if cache is valid
  const isCacheValid = useCallback((timestamp: Date): boolean => {
    return Date.now() - timestamp.getTime() < CACHE_TTL;
  }, []);

  // Update preview
  const updatePreview = useCallback(async (document: PDFDocument): Promise<void> => {
    // Clear existing timeout
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    currentDocumentRef.current = document;

    // Debounce updates
    if (autoUpdate && debounceMs > 0) {
      updateTimeoutRef.current = setTimeout(async () => {
        await performPreviewUpdate(document);
      }, debounceMs);
    } else {
      await performPreviewUpdate(document);
    }
  }, [autoUpdate, debounceMs]);

  // Perform actual preview update
  const performPreviewUpdate = useCallback(async (document: PDFDocument): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      const cacheKey = generateCacheKey(document);
      
      // Check cache first
      if (cachePreview && previewCache.has(cacheKey)) {
        const cached = previewCache.get(cacheKey)!;
        if (isCacheValid(cached.timestamp)) {
          setPreviewHtml(cached.html);
          setLastUpdated(cached.timestamp);
          onPreviewUpdate?.(cached.html, previewMode);
          return;
        } else {
          previewCache.delete(cacheKey);
        }
      }

      // Generate new preview
      const html = await previewDocumentHTML(document);
      const now = new Date();
      
      // Cache the result
      if (cachePreview) {
        previewCache.set(cacheKey, { html, timestamp: now });
      }

      // Update stats
      await updatePreviewStats(document.content);

      setPreviewHtml(html);
      setLastUpdated(now);
      onPreviewUpdate?.(html, previewMode);
    } catch (err) {
      const error = err as Error;
      setError(error);
      onError?.(error);
    } finally {
      setIsLoading(false);
    }
  }, [generateCacheKey, cachePreview, isCacheValid, previewMode, onPreviewUpdate, onError]);

  // Update preview statistics
  const updatePreviewStats = useCallback(async (content: string): Promise<void> => {
    try {
      const structure = await parseMarkdownStructure(content);
      
      // Calculate word count
      const wordCount = content
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 0).length;

      previewStatsRef.current = {
        wordCount,
        characterCount: content.length,
        headingCount: structure.toc.length,
        imageCount: structure.images.length,
        linkCount: structure.links.length,
      };
    } catch (error) {
      // Fallback to basic stats
      const wordCount = content
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 0).length;

      previewStatsRef.current = {
        wordCount,
        characterCount: content.length,
        headingCount: 0,
        imageCount: 0,
        linkCount: 0,
      };
    }
  }, []);

  // Set preview mode
  const setPreviewMode = useCallback((mode: PreviewMode) => {
    setPreviewModeState(mode);
    
    // Re-generate preview if document exists and mode affects rendering
    if (currentDocumentRef.current && (mode === 'pdf' || mode === 'html')) {
      updatePreview(currentDocumentRef.current);
    }
  }, [updatePreview]);

  // Clear preview
  const clearPreview = useCallback(() => {
    setPreviewHtml('');
    setError(null);
    setLastUpdated(null);
    currentDocumentRef.current = null;
    previewStatsRef.current = {
      wordCount: 0,
      characterCount: 0,
      headingCount: 0,
      imageCount: 0,
      linkCount: 0,
    };

    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
  }, []);

  // Refresh preview
  const refreshPreview = useCallback(async (): Promise<void> => {
    if (!currentDocumentRef.current) return;

    // Clear cache for current document
    if (cachePreview) {
      const cacheKey = generateCacheKey(currentDocumentRef.current);
      previewCache.delete(cacheKey);
    }

    await performPreviewUpdate(currentDocumentRef.current);
  }, [cachePreview, generateCacheKey, performPreviewUpdate]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // Clean up old cache entries periodically
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = Date.now();
      for (const [key, value] of previewCache.entries()) {
        if (now - value.timestamp.getTime() > CACHE_TTL) {
          previewCache.delete(key);
        }
      }
    }, CACHE_TTL);

    return () => clearInterval(cleanupInterval);
  }, []);

  // Computed values
  const hasPreview = useMemo(() => previewHtml.length > 0, [previewHtml]);

  const previewStats = useMemo(() => previewStatsRef.current, [lastUpdated]);

  return {
    // State
    previewHtml,
    previewMode,
    isLoading,
    error,
    lastUpdated,
    
    // Actions
    updatePreview,
    setPreviewMode,
    clearPreview,
    refreshPreview,
    
    // Computed
    hasPreview,
    previewStats,
  };
}
