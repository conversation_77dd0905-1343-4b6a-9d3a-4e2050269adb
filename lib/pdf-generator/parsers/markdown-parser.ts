/**
 * Markdown Parser
 * 
 * Advanced markdown parsing with support for GitHub Flavored Markdown,
 * math equations, code highlighting, and custom extensions.
 */

// @ts-ignore - marked types may not be available during build
import { marked } from 'marked';
import { 
  MarkdownParsingOptions, 
  ParsedMarkdown, 
  TableOfContentsEntry,
  ImageReference,
  LinkReference,
  CodeBlockReference,
  TableReference
} from '../types';

export class MarkdownParser {
  private options: MarkdownParsingOptions;
  private renderer: any;
  private toc: TableOfContentsEntry[] = [];
  private images: ImageReference[] = [];
  private links: LinkReference[] = [];
  private codeBlocks: CodeBlockReference[] = [];
  private tables: TableReference[] = [];
  private position = 0;

  constructor(options: MarkdownParsingOptions) {
    this.options = options;
    this.renderer = new (marked as any).Renderer();
    this.setupRenderer();
    this.configureMarked();
  }

  /**
   * Parse markdown content into structured data
   */
  async parse(markdown: string): Promise<ParsedMarkdown> {
    // Reset state
    this.resetState();

    try {
      // Extract frontmatter metadata
      const { content, metadata } = this.extractFrontmatter(markdown);

      // Parse markdown to HTML
      const html = await marked.parse(content);

      return {
        html,
        toc: this.toc,
        metadata,
        images: this.images,
        links: this.links,
        codeBlocks: this.codeBlocks,
        tables: this.tables,
      };
    } catch (error: any) {
      throw new Error(`Markdown parsing failed: ${error.message}`);
    }
  }

  /**
   * Setup custom renderer for tracking elements
   */
  private setupRenderer(): void {
    // Track headings for TOC
    this.renderer.heading = (text: string, level: number, _raw: string) => {
      const id = this.generateId(text);
      const anchor = `#${id}`;
      
      this.toc.push({
        id,
        level,
        title: text,
        anchor,
        children: [],
      });

      return `<h${level} id="${id}">${text}</h${level}>`;
    };

    // Track images
    this.renderer.image = (href: string, title: string | null, text: string) => {
      this.images.push({
        src: href,
        alt: text,
        title: title || undefined,
        position: this.position++,
      });

      const titleAttr = title ? ` title="${title}"` : '';
      return `<img src="${href}" alt="${text}"${titleAttr} />`;
    };

    // Track links
    this.renderer.link = (href: string, title: string | null, text: string) => {
      this.links.push({
        href,
        text,
        title: title || undefined,
        position: this.position++,
      });

      const titleAttr = title ? ` title="${title}"` : '';
      return `<a href="${href}"${titleAttr}>${text}</a>`;
    };

    // Track code blocks
    this.renderer.code = (code: string, language: string | undefined) => {
      const highlighted = this.options.highlight ? 
        this.highlightCode(code, language) : code;

      this.codeBlocks.push({
        language: language || 'text',
        code,
        position: this.position++,
        highlighted,
      });

      const langClass = language ? ` class="language-${language}"` : '';
      return `<pre><code${langClass}>${highlighted}</code></pre>`;
    };

    // Track tables
    this.renderer.table = (header: string, body: string) => {
      // Parse table structure (simplified)
      const headerCells = this.extractTableCells(header);
      const bodyRows = this.extractTableRows(body);

      this.tables.push({
        headers: headerCells,
        rows: bodyRows,
        position: this.position++,
      });

      return `<table><thead>${header}</thead><tbody>${body}</tbody></table>`;
    };
  }

  /**
   * Configure marked with options
   */
  private configureMarked(): void {
    (marked as any).setOptions({
      renderer: this.renderer,
      gfm: this.options.gfm,
      breaks: this.options.breaks,
      pedantic: false,
    });

    // Add extensions
    if (this.options.math) {
      this.addMathExtension();
    }

    if (this.options.mermaid) {
      this.addMermaidExtension();
    }

    if (this.options.customRenderers) {
      this.addCustomRenderers();
    }
  }

  /**
   * Extract frontmatter metadata
   */
  private extractFrontmatter(markdown: string): { content: string; metadata: Record<string, any> } {
    const frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n/;
    const match = markdown.match(frontmatterRegex);

    if (!match) {
      return { content: markdown, metadata: {} };
    }

    const frontmatter = match[1];
    const content = markdown.slice(match[0].length);
    
    try {
      // Simple YAML parsing (for basic key-value pairs)
      const metadata = this.parseSimpleYaml(frontmatter);
      return { content, metadata };
    } catch (error) {
      console.warn('Failed to parse frontmatter:', error);
      return { content: markdown, metadata: {} };
    }
  }

  /**
   * Simple YAML parser for frontmatter
   */
  private parseSimpleYaml(yaml: string): Record<string, any> {
    const metadata: Record<string, any> = {};
    const lines = yaml.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) continue;

      const colonIndex = trimmed.indexOf(':');
      if (colonIndex === -1) continue;

      const key = trimmed.slice(0, colonIndex).trim();
      const value = trimmed.slice(colonIndex + 1).trim();

      // Handle different value types
      if (value.startsWith('[') && value.endsWith(']')) {
        // Array
        metadata[key] = value.slice(1, -1).split(',').map(v => v.trim().replace(/['"]/g, ''));
      } else if (value === 'true' || value === 'false') {
        // Boolean
        metadata[key] = value === 'true';
      } else if (!isNaN(Number(value))) {
        // Number
        metadata[key] = Number(value);
      } else {
        // String
        metadata[key] = value.replace(/['"]/g, '');
      }
    }

    return metadata;
  }

  /**
   * Generate ID from text
   */
  private generateId(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  /**
   * Highlight code using basic syntax highlighting
   */
  private highlightCode(code: string, language?: string): string {
    if (!language) return code;

    // Basic syntax highlighting (can be extended with Prism.js or similar)
    switch (language.toLowerCase()) {
      case 'javascript':
      case 'js':
        return this.highlightJavaScript(code);
      case 'typescript':
      case 'ts':
        return this.highlightTypeScript(code);
      case 'python':
        return this.highlightPython(code);
      case 'json':
        return this.highlightJSON(code);
      default:
        return code;
    }
  }

  /**
   * Basic JavaScript syntax highlighting
   */
  private highlightJavaScript(code: string): string {
    return code
      .replace(/\b(const|let|var|function|class|if|else|for|while|return|import|export|default)\b/g, 
        '<span class="keyword">$1</span>')
      .replace(/\b(true|false|null|undefined)\b/g, 
        '<span class="literal">$1</span>')
      .replace(/"([^"\\]|\\.)*"/g, 
        '<span class="string">$&</span>')
      .replace(/'([^'\\]|\\.)*'/g, 
        '<span class="string">$&</span>')
      .replace(/\/\/.*$/gm, 
        '<span class="comment">$&</span>');
  }

  /**
   * Basic TypeScript syntax highlighting
   */
  private highlightTypeScript(code: string): string {
    return this.highlightJavaScript(code)
      .replace(/\b(interface|type|enum|namespace|declare|abstract|readonly)\b/g, 
        '<span class="keyword">$1</span>');
  }

  /**
   * Basic Python syntax highlighting
   */
  private highlightPython(code: string): string {
    return code
      .replace(/\b(def|class|if|elif|else|for|while|return|import|from|as|try|except|finally|with|lambda|yield)\b/g, 
        '<span class="keyword">$1</span>')
      .replace(/\b(True|False|None)\b/g, 
        '<span class="literal">$1</span>')
      .replace(/"([^"\\]|\\.)*"/g, 
        '<span class="string">$&</span>')
      .replace(/'([^'\\]|\\.)*'/g, 
        '<span class="string">$&</span>')
      .replace(/#.*$/gm, 
        '<span class="comment">$&</span>');
  }

  /**
   * Basic JSON syntax highlighting
   */
  private highlightJSON(code: string): string {
    return code
      .replace(/"([^"\\]|\\.)*":/g, 
        '<span class="property">$&</span>')
      .replace(/:\s*"([^"\\]|\\.)*"/g, 
        ': <span class="string">$1</span>')
      .replace(/:\s*(true|false|null)/g, 
        ': <span class="literal">$1</span>')
      .replace(/:\s*(-?\d+\.?\d*)/g, 
        ': <span class="number">$1</span>');
  }

  /**
   * Extract table cells from header HTML
   */
  private extractTableCells(header: string): string[] {
    const cellRegex = /<th[^>]*>(.*?)<\/th>/g;
    const cells: string[] = [];
    let match;

    while ((match = cellRegex.exec(header)) !== null) {
      cells.push(match[1].trim());
    }

    return cells;
  }

  /**
   * Extract table rows from body HTML
   */
  private extractTableRows(body: string): string[][] {
    const rowRegex = /<tr[^>]*>(.*?)<\/tr>/g;
    const cellRegex = /<td[^>]*>(.*?)<\/td>/g;
    const rows: string[][] = [];
    let rowMatch;

    while ((rowMatch = rowRegex.exec(body)) !== null) {
      const rowHtml = rowMatch[1];
      const cells: string[] = [];
      let cellMatch;

      while ((cellMatch = cellRegex.exec(rowHtml)) !== null) {
        cells.push(cellMatch[1].trim());
      }

      if (cells.length > 0) {
        rows.push(cells);
      }
    }

    return rows;
  }

  /**
   * Add math extension for LaTeX equations
   */
  private addMathExtension(): void {
    // Add support for inline math ($...$) and block math ($$...$$)
    try {
      (marked as any).use({
        extensions: [{
          name: 'math',
          level: 'inline',
          start: (src: string) => src.indexOf('$'),
          tokenizer: (src: string) => {
            const match = src.match(/^\$([^$]+)\$/);
            if (match) {
              return {
                type: 'math',
                raw: match[0],
                text: match[1],
                inline: true,
              };
            }
          },
          renderer: (token: any) => {
            return `<span class="math-inline">${token.text}</span>`;
          },
        }],
      });
    } catch (error) {
      console.warn('Math extension not supported in this version of marked');
    }
  }

  /**
   * Add Mermaid diagram extension
   */
  private addMermaidExtension(): void {
    try {
      (marked as any).use({
        extensions: [{
          name: 'mermaid',
          level: 'block',
          tokenizer: (src: string) => {
            const match = src.match(/^```mermaid\n([\s\S]*?)\n```/);
            if (match) {
              return {
                type: 'mermaid',
                raw: match[0],
                text: match[1],
              };
            }
          },
          renderer: (token: any) => {
            return `<div class="mermaid">${token.text}</div>`;
          },
        }],
      });
    } catch (error) {
      console.warn('Mermaid extension not supported in this version of marked');
    }
  }

  /**
   * Add custom renderers
   */
  private addCustomRenderers(): void {
    if (!this.options.customRenderers) return;

    for (const [name, renderer] of Object.entries(this.options.customRenderers)) {
      if (typeof renderer === 'function') {
        (this.renderer as any)[name] = renderer;
      }
    }
  }

  /**
   * Reset parser state
   */
  private resetState(): void {
    this.toc = [];
    this.images = [];
    this.links = [];
    this.codeBlocks = [];
    this.tables = [];
    this.position = 0;
  }

  /**
   * Build hierarchical table of contents
   */
  buildHierarchicalTOC(entries: TableOfContentsEntry[]): TableOfContentsEntry[] {
    const result: TableOfContentsEntry[] = [];
    const stack: TableOfContentsEntry[] = [];

    for (const entry of entries) {
      // Find the correct parent level
      while (stack.length > 0 && stack[stack.length - 1].level >= entry.level) {
        stack.pop();
      }

      if (stack.length === 0) {
        result.push(entry);
      } else {
        const parent = stack[stack.length - 1];
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(entry);
      }

      stack.push(entry);
    }

    return result;
  }
}
