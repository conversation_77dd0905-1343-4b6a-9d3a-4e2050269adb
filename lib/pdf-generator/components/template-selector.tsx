/**
 * Template Selector Component
 * 
 * Modular component for selecting and previewing document templates
 * with categorization and template information.
 */

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Search, 
  Grid, 
  List, 
  Check,
  Eye,
  Info
} from 'lucide-react';
import { useTemplates } from '../hooks/useTemplates';
import { DocumentTemplate } from '../types';

export interface TemplateSelectorProps {
  selectedTemplateId?: string;
  onTemplateSelect: (template: DocumentTemplate) => void;
  onTemplatePreview?: (template: DocumentTemplate) => void;
  showPreview?: boolean;
  viewMode?: 'grid' | 'list';
  className?: string;
}

export function TemplateSelector({
  selectedTemplateId,
  onTemplateSelect,
  onTemplatePreview,
  showPreview = true,
  viewMode: initialViewMode = 'grid',
  className = '',
}: TemplateSelectorProps) {
  const {
    templates,
    selectedTemplate,
    isLoading,
    error,
    selectTemplate,
    templatesByType,
  } = useTemplates({ autoLoad: true });

  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(initialViewMode);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Filter templates based on search and category
  const filteredTemplates = useMemo(() => {
    let filtered = templates;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.type === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.type.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [templates, selectedCategory, searchQuery]);

  // Get unique categories
  const categories = useMemo(() => {
    const types = [...new Set(templates.map(t => t.type))];
    return [
      { value: 'all', label: 'All Templates', count: templates.length },
      ...types.map(type => ({
        value: type,
        label: type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' '),
        count: templatesByType[type]?.length || 0,
      })),
    ];
  }, [templates, templatesByType]);

  // Handle template selection
  const handleTemplateSelect = useCallback(async (templateId: string) => {
    try {
      const template = await selectTemplate(templateId);
      if (template) {
        onTemplateSelect(template);
      }
    } catch (error) {
      console.error('Failed to select template:', error);
    }
  }, [selectTemplate, onTemplateSelect]);

  // Handle template preview
  const handleTemplatePreview = useCallback(async (templateId: string) => {
    if (!onTemplatePreview) return;

    try {
      const template = await selectTemplate(templateId);
      if (template) {
        onTemplatePreview(template);
      }
    } catch (error) {
      console.error('Failed to preview template:', error);
    }
  }, [selectTemplate, onTemplatePreview]);

  // Get template type color
  const getTypeColor = useCallback((type: string) => {
    const colors = {
      'research-report': 'bg-blue-100 text-blue-800',
      'technical-document': 'bg-green-100 text-green-800',
      'article': 'bg-purple-100 text-purple-800',
      'presentation': 'bg-orange-100 text-orange-800',
      'custom': 'bg-gray-100 text-gray-800',
    };
    return colors[type as keyof typeof colors] || colors.custom;
  }, []);

  // Render template card
  const renderTemplateCard = useCallback((template: { id: string; name: string; type: string }) => {
    const isSelected = selectedTemplateId === template.id;

    return (
      <Card
        key={template.id}
        className={`cursor-pointer transition-all hover:shadow-md ${
          isSelected ? 'ring-2 ring-primary' : ''
        }`}
        onClick={() => handleTemplateSelect(template.id)}
      >
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <CardTitle className="text-sm font-medium">{template.name}</CardTitle>
            {isSelected && <Check className="w-4 h-4 text-primary" />}
          </div>
          <Badge className={`w-fit text-xs ${getTypeColor(template.type)}`}>
            {template.type.replace('-', ' ')}
          </Badge>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex justify-between items-center">
            <div className="flex items-center text-xs text-muted-foreground">
              <FileText className="w-3 h-3 mr-1" />
              Template
            </div>
            {showPreview && onTemplatePreview && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleTemplatePreview(template.id);
                }}
              >
                <Eye className="w-3 h-3" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }, [selectedTemplateId, handleTemplateSelect, handleTemplatePreview, showPreview, onTemplatePreview, getTypeColor]);

  // Render template list item
  const renderTemplateListItem = useCallback((template: { id: string; name: string; type: string }) => {
    const isSelected = selectedTemplateId === template.id;

    return (
      <div
        key={template.id}
        className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-all hover:bg-muted/50 ${
          isSelected ? 'ring-2 ring-primary bg-muted/30' : ''
        }`}
        onClick={() => handleTemplateSelect(template.id)}
      >
        <div className="flex items-center space-x-3">
          <FileText className="w-4 h-4 text-muted-foreground" />
          <div>
            <div className="font-medium text-sm">{template.name}</div>
            <Badge className={`text-xs ${getTypeColor(template.type)}`}>
              {template.type.replace('-', ' ')}
            </Badge>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {showPreview && onTemplatePreview && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleTemplatePreview(template.id);
              }}
            >
              <Eye className="w-4 h-4" />
            </Button>
          )}
          {isSelected && <Check className="w-4 h-4 text-primary" />}
        </div>
      </div>
    );
  }, [selectedTemplateId, handleTemplateSelect, handleTemplatePreview, showPreview, onTemplatePreview, getTypeColor]);

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <Info className="w-8 h-8 mx-auto mb-2" />
            <p>Failed to load templates</p>
            <p className="text-sm text-muted-foreground">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <FileText className="w-4 h-4 mr-2" />
            Select Template
          </span>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Categories */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-3">
            {categories.slice(0, 6).map((category) => (
              <TabsTrigger key={category.value} value={category.value} className="text-xs">
                {category.label}
                <Badge variant="secondary" className="ml-1 text-xs">
                  {category.count}
                </Badge>
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value={selectedCategory} className="mt-4">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">Loading templates...</p>
              </div>
            ) : filteredTemplates.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">No templates found</p>
              </div>
            ) : viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTemplates.map(renderTemplateCard)}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredTemplates.map(renderTemplateListItem)}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Selected template info */}
        {selectedTemplate && (
          <div className="mt-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-sm">Selected: {selectedTemplate.name}</p>
                <p className="text-xs text-muted-foreground">
                  {selectedTemplate.sections.length} sections
                </p>
              </div>
              <Badge className={getTypeColor(selectedTemplate.type)}>
                {selectedTemplate.type.replace('-', ' ')}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
