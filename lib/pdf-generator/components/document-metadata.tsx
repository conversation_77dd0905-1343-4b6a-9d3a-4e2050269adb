/**
 * Document Metadata Component
 * 
 * Modular component for editing document metadata including
 * title, author, keywords, and custom fields.
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  FileText, 
  Calendar, 
  Tag, 
  Plus, 
  X,
  Info
} from 'lucide-react';

export interface DocumentMetadataProps {
  metadata: Record<string, any>;
  onChange: (metadata: Record<string, any>) => void;
  readonly?: boolean;
  showAdvanced?: boolean;
  className?: string;
}

export function DocumentMetadata({
  metadata,
  onChange,
  readonly = false,
  showAdvanced = false,
  className = '',
}: DocumentMetadataProps) {
  const [newKeyword, setNewKeyword] = useState('');
  const [showCustomFields, setShowCustomFields] = useState(false);
  const [newCustomField, setNewCustomField] = useState({ key: '', value: '' });

  // Handle basic field changes
  const handleFieldChange = useCallback((field: string, value: any) => {
    if (readonly) return;
    
    onChange({
      ...metadata,
      [field]: value,
    });
  }, [metadata, onChange, readonly]);

  // Handle keyword management
  const addKeyword = useCallback(() => {
    if (!newKeyword.trim() || readonly) return;
    
    const keywords = metadata.keywords || [];
    if (!keywords.includes(newKeyword.trim())) {
      handleFieldChange('keywords', [...keywords, newKeyword.trim()]);
    }
    setNewKeyword('');
  }, [newKeyword, metadata.keywords, handleFieldChange, readonly]);

  const removeKeyword = useCallback((keyword: string) => {
    if (readonly) return;
    
    const keywords = metadata.keywords || [];
    handleFieldChange('keywords', keywords.filter((k: string) => k !== keyword));
  }, [metadata.keywords, handleFieldChange, readonly]);

  // Handle custom fields
  const addCustomField = useCallback(() => {
    if (!newCustomField.key.trim() || !newCustomField.value.trim() || readonly) return;
    
    handleFieldChange(newCustomField.key, newCustomField.value);
    setNewCustomField({ key: '', value: '' });
  }, [newCustomField, handleFieldChange, readonly]);

  const removeCustomField = useCallback((key: string) => {
    if (readonly) return;
    
    const newMetadata = { ...metadata };
    delete newMetadata[key];
    onChange(newMetadata);
  }, [metadata, onChange, readonly]);

  // Standard metadata fields
  const standardFields = ['title', 'author', 'subject', 'description', 'keywords', 'creationDate', 'modificationDate'];
  
  // Custom fields (non-standard)
  const customFields = Object.keys(metadata).filter(key => !standardFields.includes(key));

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Info className="w-4 h-4 mr-2" />
          Document Metadata
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Title */}
        <div className="space-y-2">
          <Label htmlFor="title" className="flex items-center">
            <FileText className="w-3 h-3 mr-1" />
            Title
          </Label>
          <Input
            id="title"
            value={metadata.title || ''}
            onChange={(e) => handleFieldChange('title', e.target.value)}
            placeholder="Document title"
            disabled={readonly}
          />
        </div>

        {/* Author */}
        <div className="space-y-2">
          <Label htmlFor="author" className="flex items-center">
            <User className="w-3 h-3 mr-1" />
            Author
          </Label>
          <Input
            id="author"
            value={metadata.author || ''}
            onChange={(e) => handleFieldChange('author', e.target.value)}
            placeholder="Document author"
            disabled={readonly}
          />
        </div>

        {/* Subject */}
        <div className="space-y-2">
          <Label htmlFor="subject">Subject</Label>
          <Input
            id="subject"
            value={metadata.subject || ''}
            onChange={(e) => handleFieldChange('subject', e.target.value)}
            placeholder="Document subject"
            disabled={readonly}
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={metadata.description || ''}
            onChange={(e) => handleFieldChange('description', e.target.value)}
            placeholder="Document description"
            rows={3}
            disabled={readonly}
          />
        </div>

        {/* Keywords */}
        <div className="space-y-2">
          <Label className="flex items-center">
            <Tag className="w-3 h-3 mr-1" />
            Keywords
          </Label>
          
          {/* Existing keywords */}
          <div className="flex flex-wrap gap-2">
            {(metadata.keywords || []).map((keyword: string, index: number) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {keyword}
                {!readonly && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-0 w-4 h-4"
                    onClick={() => removeKeyword(keyword)}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                )}
              </Badge>
            ))}
          </div>

          {/* Add new keyword */}
          {!readonly && (
            <div className="flex gap-2">
              <Input
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                placeholder="Add keyword"
                onKeyDown={(e) => e.key === 'Enter' && addKeyword()}
              />
              <Button onClick={addKeyword} size="sm">
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Advanced fields */}
        {showAdvanced && (
          <>
            <Separator />
            
            {/* Creation Date */}
            <div className="space-y-2">
              <Label htmlFor="creationDate" className="flex items-center">
                <Calendar className="w-3 h-3 mr-1" />
                Creation Date
              </Label>
              <Input
                id="creationDate"
                type="datetime-local"
                value={metadata.creationDate ? new Date(metadata.creationDate).toISOString().slice(0, 16) : ''}
                onChange={(e) => handleFieldChange('creationDate', new Date(e.target.value))}
                disabled={readonly}
              />
            </div>

            {/* Modification Date */}
            <div className="space-y-2">
              <Label htmlFor="modificationDate">Modification Date</Label>
              <Input
                id="modificationDate"
                type="datetime-local"
                value={metadata.modificationDate ? new Date(metadata.modificationDate).toISOString().slice(0, 16) : ''}
                onChange={(e) => handleFieldChange('modificationDate', new Date(e.target.value))}
                disabled={readonly}
              />
            </div>

            {/* Creator */}
            <div className="space-y-2">
              <Label htmlFor="creator">Creator</Label>
              <Input
                id="creator"
                value={metadata.creator || ''}
                onChange={(e) => handleFieldChange('creator', e.target.value)}
                placeholder="Document creator"
                disabled={readonly}
              />
            </div>

            {/* Producer */}
            <div className="space-y-2">
              <Label htmlFor="producer">Producer</Label>
              <Input
                id="producer"
                value={metadata.producer || ''}
                onChange={(e) => handleFieldChange('producer', e.target.value)}
                placeholder="Document producer"
                disabled={readonly}
              />
            </div>
          </>
        )}

        {/* Custom fields */}
        {customFields.length > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label>Custom Fields</Label>
              {customFields.map((key) => (
                <div key={key} className="flex items-center gap-2">
                  <Input
                    value={key}
                    disabled
                    className="flex-1"
                  />
                  <Input
                    value={metadata[key] || ''}
                    onChange={(e) => handleFieldChange(key, e.target.value)}
                    placeholder="Value"
                    className="flex-1"
                    disabled={readonly}
                  />
                  {!readonly && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeCustomField(key)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </>
        )}

        {/* Add custom field */}
        {!readonly && (
          <>
            {!showCustomFields ? (
              <Button
                variant="outline"
                onClick={() => setShowCustomFields(true)}
                className="w-full"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Custom Field
              </Button>
            ) : (
              <div className="space-y-2">
                <Label>Add Custom Field</Label>
                <div className="flex gap-2">
                  <Input
                    value={newCustomField.key}
                    onChange={(e) => setNewCustomField(prev => ({ ...prev, key: e.target.value }))}
                    placeholder="Field name"
                  />
                  <Input
                    value={newCustomField.value}
                    onChange={(e) => setNewCustomField(prev => ({ ...prev, value: e.target.value }))}
                    placeholder="Field value"
                  />
                  <Button onClick={addCustomField} size="sm">
                    <Plus className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowCustomFields(false);
                      setNewCustomField({ key: '', value: '' });
                    }}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
