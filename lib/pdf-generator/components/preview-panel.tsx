/**
 * Preview Panel Component
 * 
 * Modular component for document preview with multiple view modes,
 * zoom controls, and preview statistics.
 */

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Eye, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw,
  Maximize2,
  Minimize2,
  RefreshCw,
  FileText,
  Globe,
  Code,
  Printer,
  BarChart3
} from 'lucide-react';
import { usePreview } from '../hooks/usePreview';
import { PDFDocument, PreviewMode } from '../types';

export interface PreviewPanelProps {
  document: PDFDocument | null;
  mode?: PreviewMode;
  onModeChange?: (mode: PreviewMode) => void;
  showStats?: boolean;
  showControls?: boolean;
  className?: string;
}

export function PreviewPanel({
  document,
  mode = 'html',
  onModeChange,
  showStats = true,
  showControls = true,
  className = '',
}: PreviewPanelProps) {
  const {
    previewHtml,
    previewMode,
    isLoading,
    error,
    lastUpdated,
    updatePreview,
    setPreviewMode,
    refreshPreview,
    hasPreview,
    previewStats,
  } = usePreview({
    defaultMode: mode,
    autoUpdate: true,
    cachePreview: true,
  });

  const [zoom, setZoom] = useState(100);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Update preview when document changes
  React.useEffect(() => {
    if (document) {
      updatePreview(document);
    }
  }, [document, updatePreview]);

  // Handle mode change
  const handleModeChange = useCallback((newMode: PreviewMode) => {
    setPreviewMode(newMode);
    onModeChange?.(newMode);
  }, [setPreviewMode, onModeChange]);

  // Handle zoom
  const handleZoomChange = useCallback((value: number[]) => {
    setZoom(value[0]);
  }, []);

  const zoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + 25, 200));
  }, []);

  const zoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - 25, 50));
  }, []);

  const resetZoom = useCallback(() => {
    setZoom(100);
  }, []);

  // Get preview content based on mode
  const previewContent = useMemo(() => {
    if (!document || !hasPreview) return '';

    switch (previewMode) {
      case 'markdown':
        return `<pre class="whitespace-pre-wrap font-mono text-sm">${document.content}</pre>`;
      case 'html':
      case 'pdf':
      case 'split':
        return previewHtml;
      default:
        return previewHtml;
    }
  }, [document, hasPreview, previewMode, previewHtml]);

  // Get preview style based on mode
  const previewStyle = useMemo(() => {
    const baseStyle = {
      transform: `scale(${zoom / 100})`,
      transformOrigin: 'top left',
      width: `${(100 / zoom) * 100}%`,
    };

    if (previewMode === 'pdf') {
      return {
        ...baseStyle,
        fontFamily: 'serif',
        lineHeight: '1.6',
        maxWidth: '8.5in',
        margin: '0 auto',
        padding: '1in',
        backgroundColor: 'white',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      };
    }

    return baseStyle;
  }, [zoom, previewMode]);

  // Preview mode options
  const previewModes = [
    { value: 'markdown', label: 'Markdown', icon: Code },
    { value: 'html', label: 'HTML', icon: Globe },
    { value: 'pdf', label: 'PDF', icon: FileText },
  ] as const;

  return (
    <Card className={`${className} ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Eye className="w-4 h-4 mr-2" />
            Preview
            {lastUpdated && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {lastUpdated.toLocaleTimeString()}
              </Badge>
            )}
          </span>
          
          {showControls && (
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshPreview}
                disabled={isLoading || !document}
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </Button>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Preview Mode Tabs */}
        <Tabs value={previewMode} onValueChange={handleModeChange as any}>
          <TabsList className="grid w-full grid-cols-3">
            {previewModes.map((modeOption) => {
              const Icon = modeOption.icon;
              return (
                <TabsTrigger key={modeOption.value} value={modeOption.value}>
                  <Icon className="w-4 h-4 mr-1" />
                  {modeOption.label}
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Controls */}
          {showControls && (
            <div className="flex items-center justify-between p-2 bg-muted rounded">
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={zoomOut}>
                  <ZoomOut className="w-4 h-4" />
                </Button>
                <div className="flex items-center space-x-2 min-w-32">
                  <Slider
                    value={[zoom]}
                    onValueChange={handleZoomChange}
                    min={50}
                    max={200}
                    step={25}
                    className="flex-1"
                  />
                  <span className="text-sm font-mono w-12">{zoom}%</span>
                </div>
                <Button variant="outline" size="sm" onClick={zoomIn}>
                  <ZoomIn className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={resetZoom}>
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </div>

              {previewMode === 'pdf' && (
                <Button variant="outline" size="sm">
                  <Printer className="w-4 h-4 mr-1" />
                  Print
                </Button>
              )}
            </div>
          )}

          {/* Preview Content */}
          <TabsContent value={previewMode} className="mt-4">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Generating preview...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center text-red-600">
                  <FileText className="w-8 h-8 mx-auto mb-2" />
                  <p className="font-medium">Preview Error</p>
                  <p className="text-sm text-muted-foreground">{error.message}</p>
                </div>
              </div>
            ) : !document ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center text-muted-foreground">
                  <FileText className="w-8 h-8 mx-auto mb-2" />
                  <p>No document to preview</p>
                </div>
              </div>
            ) : (
              <ScrollArea className={`${isFullscreen ? 'h-[calc(100vh-200px)]' : 'h-96'} border rounded`}>
                <div
                  className="prose max-w-none p-4"
                  style={previewStyle}
                  dangerouslySetInnerHTML={{ __html: previewContent }}
                />
              </ScrollArea>
            )}
          </TabsContent>
        </Tabs>

        {/* Preview Statistics */}
        {showStats && hasPreview && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-sm">
                <BarChart3 className="w-4 h-4 mr-2" />
                Document Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                <div className="space-y-1">
                  <div className="text-lg font-bold">{previewStats.wordCount}</div>
                  <div className="text-xs text-muted-foreground">Words</div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold">{previewStats.characterCount}</div>
                  <div className="text-xs text-muted-foreground">Characters</div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold">{previewStats.headingCount}</div>
                  <div className="text-xs text-muted-foreground">Headings</div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold">{previewStats.imageCount}</div>
                  <div className="text-xs text-muted-foreground">Images</div>
                </div>
                <div className="space-y-1">
                  <div className="text-lg font-bold">{previewStats.linkCount}</div>
                  <div className="text-xs text-muted-foreground">Links</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}
