/**
 * Enhanced Block Editor
 * 
 * Block editor with remark-jsx integration, four-state lifecycle,
 * modal editors, and dynamic data support
 */

'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus,
  Undo,
  Redo,
  Save,
  Eye,
  Download,
  Settings,
  Zap,
  Database,
  Code,
  Play,
} from 'lucide-react';

// Import enhanced components and systems
import { useBlockEditor } from '../hooks/useBlockEditor';
import { EnhancedBlockContainer } from './blocks/enhanced-block-container';
import { remarkJSXProcessor } from '../blocks/remark-integration';
import { blockStateManager, BlockState } from '../blocks/block-state-manager';
import { dynamicDataManager } from '../blocks/dynamic-data-manager';
import { Block, BlockType } from '../blocks/types';

export interface EnhancedBlockEditorProps {
  initialBlocks?: Block[];
  initialMarkdown?: string;
  enableRemarkJSX?: boolean;
  enableDynamicData?: boolean;
  enableLiveExecution?: boolean;
  defaultBlockState?: BlockState;
  onSave?: (blocks: Block[], markdown: string, jsx?: string) => void;
  onPreview?: (blocks: Block[], markdown: string) => void;
  onExport?: (blocks: Block[], markdown: string, format: string) => void;
  onStateChange?: (blockId: string, state: BlockState) => void;
  autoSave?: boolean;
  autoSaveInterval?: number;
  placeholder?: string;
  className?: string;
}

export function EnhancedBlockEditor({
  initialBlocks,
  initialMarkdown,
  enableRemarkJSX = true,
  enableDynamicData = true,
  enableLiveExecution = false,
  defaultBlockState = 'preview',
  onSave,
  onPreview,
  onExport,
  onStateChange,
  autoSave = false,
  autoSaveInterval = 5000,
  placeholder = 'Start writing...',
  className = '',
}: EnhancedBlockEditorProps) {
  const {
    blocks,
    selection,
    focusedBlockId,
    isEditing,
    draggedBlockId,
    validationErrors,
    addBlock,
    deleteBlock,
    updateBlock,
    moveBlock,
    duplicateBlockById,
    convertBlockType,
    selectBlock,
    clearSelection,
    focusBlock,
    focusNextBlock,
    focusPreviousBlock,
    startDrag,
    endDrag,
    toMarkdown,
    fromMarkdown,
    undo,
    redo,
    canUndo,
    canRedo,
    isEmpty,
    blockCount,
  } = useBlockEditor({
    initialBlocks,
    initialMarkdown,
    autoSave,
    autoSaveInterval,
    placeholder,
    onChange: async (blocks) => {
      // Convert to markdown using remark-jsx if enabled
      let markdown: string;
      if (enableRemarkJSX) {
        markdown = remarkJSXProcessor.blocksToMarkdown(blocks);
      } else {
        markdown = toMarkdown();
      }

      // Auto-save or callback when blocks change
      if (onSave && autoSave) {
        onSave(blocks, markdown);
      }
    },
  });

  const [dropTargetIndex, setDropTargetIndex] = useState<number | null>(null);
  const [activeMode, setActiveMode] = useState<'blocks' | 'markdown' | 'jsx'>('blocks');
  const [globalBlockState, setGlobalBlockState] = useState<BlockState>(defaultBlockState);
  const [dynamicDataEnabled, setDynamicDataEnabled] = useState(enableDynamicData);
  const [liveExecutionEnabled, setLiveExecutionEnabled] = useState(enableLiveExecution);
  const editorRef = useRef<HTMLDivElement>(null);

  // Initialize enhanced features
  useEffect(() => {
    // Set up global state change listener
    blockStateManager.onStateTransition('global', 'after', 'edit', (context) => {
      onStateChange?.(context.blockId, 'edit');
    });

    blockStateManager.onStateTransition('global', 'after', 'preview', (context) => {
      onStateChange?.(context.blockId, 'preview');
    });

    return () => {
      // Cleanup
      blocks.forEach(block => {
        blockStateManager.cleanup(block.id);
        if (dynamicDataEnabled) {
          dynamicDataManager.cleanup(block.id);
        }
      });
    };
  }, [blocks, onStateChange, dynamicDataEnabled]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        switch (e.key) {
          case 'z':
            if (e.shiftKey) {
              e.preventDefault();
              redo();
            } else {
              e.preventDefault();
              undo();
            }
            break;
          case 's':
            e.preventDefault();
            handleSave();
            break;
          case 'p':
            e.preventDefault();
            handlePreview();
            break;
          case 'e':
            e.preventDefault();
            handleExport('pdf');
            break;
          case 'r':
            if (e.shiftKey) {
              e.preventDefault();
              handleBatchStateTransition('render');
            }
            break;
        }
      }

      if (e.key === 'Escape') {
        clearSelection();
        handleBatchStateTransition('preview');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [undo, redo, clearSelection]);

  // Enhanced block operations
  const handleBlockUpdate = useCallback((blockId: string, updates: Partial<Block>) => {
    updateBlock(blockId, updates);
    
    // Update dynamic data if enabled
    if (dynamicDataEnabled) {
      dynamicDataManager.onBlockUpdate(blockId, (data: any) => {
        updateBlock(blockId, data);
      });
    }
  }, [updateBlock, dynamicDataEnabled]);

  const handleBlockFocus = useCallback((blockId: string) => {
    focusBlock(blockId);
    selectBlock(blockId);
  }, [focusBlock, selectBlock]);

  const handleBlockKeyDown = useCallback((
    blockId: string,
    e: React.KeyboardEvent,
    action?: string,
    data?: any
  ) => {
    const blockIndex = blocks.findIndex(b => b.id === blockId);

    switch (action) {
      case 'create-block':
        e.preventDefault();
        const newBlock = addBlock(data?.type || 'paragraph', blockIndex + 1);
        setTimeout(() => focusBlock(newBlock.id), 0);
        break;

      case 'merge-with-previous':
        if (blockIndex > 0) {
          e.preventDefault();
          deleteBlock(blockId);
          focusPreviousBlock(blockId);
        }
        break;

      default:
        if (e.key === 'ArrowUp' && e.metaKey) {
          e.preventDefault();
          focusPreviousBlock(blockId);
        } else if (e.key === 'ArrowDown' && e.metaKey) {
          e.preventDefault();
          focusNextBlock(blockId);
        }
        break;
    }
  }, [blocks, addBlock, deleteBlock, focusBlock, focusPreviousBlock, focusNextBlock]);

  // State management operations
  const handleBatchStateTransition = useCallback(async (state: BlockState) => {
    const blockIds = blocks.map(b => b.id);
    const result = await blockStateManager.batchTransition(blockIds, state);
    
    if (result.failed.length > 0) {
      console.warn(`Failed to transition ${result.failed.length} blocks to ${state}`);
    }
    
    setGlobalBlockState(state);
  }, [blocks]);

  // Enhanced save with multiple formats
  const handleSave = useCallback(async () => {
    let markdown: string;
    let jsx: string | undefined;

    if (enableRemarkJSX) {
      markdown = remarkJSXProcessor.blocksToMarkdown(blocks);
      // Generate JSX representation if needed
      jsx = await generateJSXRepresentation(blocks);
    } else {
      markdown = toMarkdown();
    }

    onSave?.(blocks, markdown, jsx);
  }, [blocks, toMarkdown, onSave, enableRemarkJSX]);

  const handlePreview = useCallback(() => {
    handleBatchStateTransition('render');
    const markdown = enableRemarkJSX ? 
      remarkJSXProcessor.blocksToMarkdown(blocks) : 
      toMarkdown();
    onPreview?.(blocks, markdown);
  }, [blocks, toMarkdown, onPreview, enableRemarkJSX, handleBatchStateTransition]);

  const handleExport = useCallback((format: string) => {
    handleBatchStateTransition(format === 'print' ? 'print' : 'render');
    const markdown = enableRemarkJSX ? 
      remarkJSXProcessor.blocksToMarkdown(blocks) : 
      toMarkdown();
    onExport?.(blocks, markdown, format);
  }, [blocks, toMarkdown, onExport, enableRemarkJSX, handleBatchStateTransition]);

  // Dynamic data operations
  const handleEnableDynamicData = useCallback((blockId: string) => {
    if (!dynamicDataEnabled) return;

    const block = blocks.find(b => b.id === blockId);
    if (!block) return;

    // Set up dynamic data based on block type
    switch (block.type) {
      case 'list':
        const dynamicList = dynamicDataManager.createDynamicList(blockId, (block as any).items || []);
        // Bind the dynamic list to the block
        break;
      
      case 'table':
        const tableBlock = block as any;
        const dynamicTable = dynamicDataManager.createDataTable(
          blockId,
          tableBlock.rows || [],
          tableBlock.headers?.map((h: string, i: number) => ({ key: i, label: h })) || []
        );
        break;
      
      case 'code':
        if (liveExecutionEnabled) {
          dynamicDataManager.setupLiveExecution({
            blockId,
            language: (block as any).language || 'javascript',
            code: block.content,
            environment: 'browser',
            dependencies: [],
            timeout: 5000,
            allowNetworkAccess: false,
          });
        }
        break;
      
      case 'math':
        dynamicDataManager.setupMathRendering({
          blockId,
          formula: (block as any).formula || block.content,
          engine: 'katex',
          displayMode: (block as any).displayMode || 'block',
          autoRender: true,
        });
        break;
    }
  }, [blocks, dynamicDataEnabled, liveExecutionEnabled]);

  // Mode switching
  const handleModeSwitch = useCallback(async (mode: 'blocks' | 'markdown' | 'jsx') => {
    if (mode === 'markdown' && activeMode === 'blocks') {
      // Convert blocks to markdown
      const markdown = enableRemarkJSX ? 
        remarkJSXProcessor.blocksToMarkdown(blocks) : 
        toMarkdown();
      // Update markdown editor content
    } else if (mode === 'blocks' && activeMode === 'markdown') {
      // Convert markdown to blocks
      // This would need to be implemented based on the markdown editor content
    }
    
    setActiveMode(mode);
  }, [activeMode, blocks, toMarkdown, enableRemarkJSX]);

  // Generate JSX representation
  const generateJSXRepresentation = async (blocks: Block[]): Promise<string> => {
    // This would generate JSX components for each block
    return blocks.map(block => {
      switch (block.type) {
        case 'heading':
          const level = (block as any).level || 1;
          return `<h${level}>${block.content}</h${level}>`;
        case 'paragraph':
          return `<p>${block.content}</p>`;
        case 'code':
          return `<pre><code className="language-${(block as any).language || 'text'}">${block.content}</code></pre>`;
        default:
          return `<div data-block-type="${block.type}">${block.content}</div>`;
      }
    }).join('\n');
  };

  return (
    <div className={`enhanced-block-editor ${className}`}>
      {/* Enhanced toolbar */}
      <div className="flex items-center justify-between p-4 border-b bg-white sticky top-0 z-20">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={undo} disabled={!canUndo}>
            <Undo className="w-4 h-4" />
          </Button>
          
          <Button variant="outline" size="sm" onClick={redo} disabled={!canRedo}>
            <Redo className="w-4 h-4" />
          </Button>

          <div className="h-4 w-px bg-gray-300" />

          <Button variant="outline" size="sm" onClick={() => addBlock('paragraph')}>
            <Plus className="w-4 h-4 mr-1" />
            Add Block
          </Button>

          {/* State controls */}
          <div className="flex items-center space-x-1 border rounded">
            <Button
              variant={globalBlockState === 'preview' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleBatchStateTransition('preview')}
            >
              <Eye className="w-4 h-4" />
            </Button>
            <Button
              variant={globalBlockState === 'edit' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleBatchStateTransition('edit')}
            >
              <Settings className="w-4 h-4" />
            </Button>
            <Button
              variant={globalBlockState === 'render' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleBatchStateTransition('render')}
            >
              <Code className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Mode selector */}
          <Tabs value={activeMode} onValueChange={(value) => handleModeSwitch(value as any)}>
            <TabsList>
              <TabsTrigger value="blocks">Blocks</TabsTrigger>
              <TabsTrigger value="markdown">Markdown</TabsTrigger>
              {enableRemarkJSX && <TabsTrigger value="jsx">JSX</TabsTrigger>}
            </TabsList>
          </Tabs>

          {/* Feature toggles */}
          {enableDynamicData && (
            <Button
              variant={dynamicDataEnabled ? 'default' : 'outline'}
              size="sm"
              onClick={() => setDynamicDataEnabled(!dynamicDataEnabled)}
            >
              <Database className="w-4 h-4" />
            </Button>
          )}

          {enableLiveExecution && (
            <Button
              variant={liveExecutionEnabled ? 'default' : 'outline'}
              size="sm"
              onClick={() => setLiveExecutionEnabled(!liveExecutionEnabled)}
            >
              <Play className="w-4 h-4" />
            </Button>
          )}

          <div className="h-4 w-px bg-gray-300" />

          <Button variant="outline" size="sm" onClick={handlePreview}>
            <Eye className="w-4 h-4" />
          </Button>

          <Button variant="outline" size="sm" onClick={handleSave}>
            <Save className="w-4 h-4" />
          </Button>

          <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
            <Download className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Editor content */}
      <ScrollArea className="flex-1">
        <div ref={editorRef} className="max-w-4xl mx-auto p-6 min-h-screen">
          {isEmpty ? (
            <div className="text-center py-12 text-gray-500">
              <div className="text-lg mb-2">{placeholder}</div>
              <Button onClick={() => addBlock('paragraph')}>
                <Plus className="w-4 h-4 mr-2" />
                Add your first block
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              {blocks.map((block, index) => (
                <EnhancedBlockContainer
                  key={block.id}
                  block={block}
                  index={index}
                  isSelected={selection?.start.blockId === block.id}
                  isFocused={focusedBlockId === block.id}
                  isDragging={draggedBlockId === block.id}
                  isDropTarget={dropTargetIndex === index}
                  initialState={defaultBlockState}
                  onUpdate={(updates) => handleBlockUpdate(block.id, updates)}
                  onFocus={() => handleBlockFocus(block.id)}
                  onBlur={() => {}}
                  onSelect={() => selectBlock(block.id)}
                  onKeyDown={(e, action, data) => handleBlockKeyDown(block.id, e, action, data)}
                  onAddBlock={(type, position) => {
                    const insertIndex = position === 'before' ? index : index + 1;
                    const newBlock = addBlock(type, insertIndex);
                    setTimeout(() => focusBlock(newBlock.id), 0);
                  }}
                  onDeleteBlock={() => deleteBlock(block.id)}
                  onDuplicateBlock={() => duplicateBlockById(block.id)}
                  onMoveBlock={(direction) => {
                    const newIndex = direction === 'up' ? index - 1 : index + 1;
                    if (newIndex >= 0 && newIndex < blocks.length) {
                      moveBlock(block.id, newIndex);
                    }
                  }}
                  onConvertBlock={(newType) => convertBlockType(block.id, newType)}
                  onDragStart={() => startDrag(block.id)}
                  onDragEnd={endDrag}
                  onDragOver={(e) => {
                    e.preventDefault();
                    setDropTargetIndex(index);
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    const draggedBlockId = e.dataTransfer.getData('text/plain');
                    if (draggedBlockId && draggedBlockId !== block.id) {
                      moveBlock(draggedBlockId, index);
                    }
                    setDropTargetIndex(null);
                  }}
                />
              ))}
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Enhanced status bar */}
      <div className="flex items-center justify-between p-2 border-t bg-muted/30 text-xs">
        <div className="flex items-center space-x-4">
          <span>{blockCount} blocks</span>
          <span>State: {globalBlockState}</span>
          <span>Mode: {activeMode}</span>
          {validationErrors.length > 0 && (
            <span className="text-red-500">{validationErrors.length} errors</span>
          )}
          {dynamicDataEnabled && <span className="text-blue-500">Dynamic Data</span>}
          {liveExecutionEnabled && <span className="text-green-500">Live Execution</span>}
        </div>
        
        <div className="flex items-center space-x-4">
          <span>Words: {toMarkdown().split(/\s+/).filter(w => w.length > 0).length}</span>
          <span>Characters: {toMarkdown().length}</span>
        </div>
      </div>
    </div>
  );
}
