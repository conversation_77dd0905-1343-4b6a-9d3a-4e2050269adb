/**
 * Export Controls Component
 * 
 * Modular component for document export with format selection,
 * options configuration, and progress tracking.
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  FileText, 
  Globe, 
  Code, 
  File,
  Settings,
  Clock,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { useExport } from '../hooks/useExport';
import { PDFDocument } from '../types';

export interface ExportControlsProps {
  document: PDFDocument | null;
  disabled?: boolean;
  onExportComplete?: (format: string, result: Buffer | string) => void;
  onExportError?: (format: string, error: Error) => void;
  className?: string;
}

const exportFormats = [
  { value: 'pdf', label: 'PDF', icon: FileText, description: 'Portable Document Format' },
  { value: 'html', label: 'HTML', icon: Globe, description: 'Web page format' },
  { value: 'markdown', label: 'Markdown', icon: Code, description: 'Plain text markup' },
  { value: 'docx', label: 'DOCX', icon: File, description: 'Microsoft Word document' },
];

const mimeTypes = {
  pdf: 'application/pdf',
  html: 'text/html',
  markdown: 'text/markdown',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
};

export function ExportControls({
  document,
  disabled = false,
  onExportComplete,
  onExportError,
  className = '',
}: ExportControlsProps) {
  const {
    isExporting,
    currentJob,
    exportHistory,
    progress,
    exportToPDF,
    exportToHTML,
    exportToMarkdown,
    exportToFormat,
    downloadFile,
    getExportStats,
  } = useExport({
    onExportComplete,
    onExportError,
  });

  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [exportOptions, setExportOptions] = useState({
    filename: '',
    includeAssets: true,
    bundleAssets: false,
    autoDownload: true,
  });
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Handle export
  const handleExport = useCallback(async () => {
    if (!document || isExporting) return;

    try {
      let result: Buffer | string;
      const filename = exportOptions.filename || `${document.title}.${selectedFormat}`;

      switch (selectedFormat) {
        case 'pdf':
          result = await exportToPDF(document, {
            filename,
            includeAssets: exportOptions.includeAssets,
            bundleAssets: exportOptions.bundleAssets,
          });
          break;
        case 'html':
          result = await exportToHTML(document, {
            filename,
            includeAssets: exportOptions.includeAssets,
            bundleAssets: exportOptions.bundleAssets,
          });
          break;
        case 'markdown':
          result = await exportToMarkdown(document, {
            filename,
            includeAssets: exportOptions.includeAssets,
            bundleAssets: exportOptions.bundleAssets,
          });
          break;
        default:
          result = await exportToFormat(document, selectedFormat, {
            filename,
            includeAssets: exportOptions.includeAssets,
            bundleAssets: exportOptions.bundleAssets,
          });
      }

      // Auto-download if enabled
      if (exportOptions.autoDownload) {
        downloadFile(result, filename, mimeTypes[selectedFormat as keyof typeof mimeTypes]);
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, [
    document,
    isExporting,
    selectedFormat,
    exportOptions,
    exportToPDF,
    exportToHTML,
    exportToMarkdown,
    exportToFormat,
    downloadFile,
  ]);

  // Handle option change
  const handleOptionChange = useCallback((key: string, value: any) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  }, []);

  // Get format icon
  const getFormatIcon = useCallback((format: string) => {
    const formatData = exportFormats.find(f => f.value === format);
    return formatData?.icon || File;
  }, []);

  // Get export stats
  const stats = getExportStats();

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Download className="w-4 h-4 mr-2" />
            Export Document
          </span>
          {stats.total > 0 && (
            <Badge variant="secondary">
              {stats.successful}/{stats.total} exports
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Format Selection */}
        <div className="space-y-2">
          <Label>Export Format</Label>
          <Select value={selectedFormat} onValueChange={setSelectedFormat}>
            <SelectTrigger>
              <SelectValue placeholder="Select format" />
            </SelectTrigger>
            <SelectContent>
              {exportFormats.map((format) => {
                const Icon = format.icon;
                return (
                  <SelectItem key={format.value} value={format.value}>
                    <div className="flex items-center space-x-2">
                      <Icon className="w-4 h-4" />
                      <div>
                        <div className="font-medium">{format.label}</div>
                        <div className="text-xs text-muted-foreground">{format.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        {/* Filename */}
        <div className="space-y-2">
          <Label htmlFor="filename">Filename (optional)</Label>
          <Input
            id="filename"
            value={exportOptions.filename}
            onChange={(e) => handleOptionChange('filename', e.target.value)}
            placeholder={`${document?.title || 'document'}.${selectedFormat}`}
          />
        </div>

        {/* Basic Options */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="autoDownload"
              checked={exportOptions.autoDownload}
              onCheckedChange={(checked) => handleOptionChange('autoDownload', checked)}
            />
            <Label htmlFor="autoDownload" className="text-sm">
              Auto-download after export
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="includeAssets"
              checked={exportOptions.includeAssets}
              onCheckedChange={(checked) => handleOptionChange('includeAssets', checked)}
            />
            <Label htmlFor="includeAssets" className="text-sm">
              Include images and assets
            </Label>
          </div>
        </div>

        {/* Advanced Options */}
        <div className="space-y-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="w-full justify-start"
          >
            <Settings className="w-4 h-4 mr-2" />
            Advanced Options
          </Button>

          {showAdvanced && (
            <div className="space-y-3 pl-4 border-l-2 border-muted">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="bundleAssets"
                  checked={exportOptions.bundleAssets}
                  onCheckedChange={(checked) => handleOptionChange('bundleAssets', checked)}
                />
                <Label htmlFor="bundleAssets" className="text-sm">
                  Bundle assets inline
                </Label>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Export Button */}
        <Button
          onClick={handleExport}
          disabled={!document || disabled || isExporting}
          className="w-full"
          size="lg"
        >
          {isExporting ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Exporting...
            </>
          ) : (
            <>
              {React.createElement(getFormatIcon(selectedFormat), { className: "w-4 h-4 mr-2" })}
              Export as {selectedFormat.toUpperCase()}
            </>
          )}
        </Button>

        {/* Progress */}
        {isExporting && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Progress</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="w-full" />
            {currentJob && (
              <p className="text-xs text-muted-foreground">
                Exporting {currentJob.format.toUpperCase()}...
              </p>
            )}
          </div>
        )}

        {/* Recent Exports */}
        {exportHistory.length > 0 && (
          <div className="space-y-2">
            <Label>Recent Exports</Label>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {exportHistory.slice(-3).reverse().map((job) => {
                const Icon = getFormatIcon(job.format);
                const StatusIcon = job.status === 'completed' ? CheckCircle : 
                                 job.status === 'failed' ? XCircle : Clock;
                
                return (
                  <div key={job.id} className="flex items-center justify-between p-2 bg-muted rounded text-sm">
                    <div className="flex items-center space-x-2">
                      <Icon className="w-3 h-3" />
                      <span>{job.format.toUpperCase()}</span>
                      <StatusIcon className={`w-3 h-3 ${
                        job.status === 'completed' ? 'text-green-500' :
                        job.status === 'failed' ? 'text-red-500' : 'text-gray-500'
                      }`} />
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {job.endTime?.toLocaleTimeString() || 'In progress'}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Export Stats */}
        {stats.total > 0 && (
          <div className="grid grid-cols-3 gap-2 text-center">
            <div className="p-2 bg-muted rounded">
              <div className="text-lg font-bold">{stats.total}</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
            <div className="p-2 bg-muted rounded">
              <div className="text-lg font-bold text-green-600">{stats.successful}</div>
              <div className="text-xs text-muted-foreground">Success</div>
            </div>
            <div className="p-2 bg-muted rounded">
              <div className="text-lg font-bold text-red-600">{stats.failed}</div>
              <div className="text-xs text-muted-foreground">Failed</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
