/**
 * Validation Panel Component
 * 
 * Modular component for displaying validation results with
 * error details, warnings, and validation statistics.
 */

'use client';

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Info, 
  RefreshCw,
  Clock,
  TrendingUp
} from 'lucide-react';
import { ValidationError, ValidationWarning } from '../types';

export interface ValidationPanelProps {
  errors: ValidationError[];
  warnings: ValidationWarning[];
  isValidating: boolean;
  isValid: boolean;
  lastValidated: Date | null;
  validationCount: number;
  onRevalidate?: () => void;
  onErrorClick?: (error: ValidationError) => void;
  onWarningClick?: (warning: ValidationWarning) => void;
  className?: string;
}

export function ValidationPanel({
  errors,
  warnings,
  isValidating,
  isValid,
  lastValidated,
  validationCount,
  onRevalidate,
  onErrorClick,
  onWarningClick,
  className = '',
}: ValidationPanelProps) {
  // Group errors by type
  const errorsByType = useMemo(() => {
    return errors.reduce((acc, error) => {
      if (!acc[error.type]) {
        acc[error.type] = [];
      }
      acc[error.type].push(error);
      return acc;
    }, {} as Record<string, ValidationError[]>);
  }, [errors]);

  // Get validation status
  const validationStatus = useMemo(() => {
    if (isValidating) return { icon: RefreshCw, color: 'text-blue-500', label: 'Validating...' };
    if (errors.length > 0) return { icon: XCircle, color: 'text-red-500', label: 'Invalid' };
    if (warnings.length > 0) return { icon: AlertTriangle, color: 'text-yellow-500', label: 'Valid with warnings' };
    return { icon: CheckCircle, color: 'text-green-500', label: 'Valid' };
  }, [isValidating, errors.length, warnings.length]);

  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'info': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'syntax': return '📝';
      case 'reference': return '🔗';
      case 'image': return '🖼️';
      case 'link': return '🌐';
      case 'template': return '📄';
      case 'styling': return '🎨';
      case 'metadata': return 'ℹ️';
      default: return '❓';
    }
  };

  // Render error item
  const renderErrorItem = (error: ValidationError, index: number) => (
    <div
      key={`error-${index}`}
      className={`p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors ${getSeverityColor(error.severity)}`}
      onClick={() => onErrorClick?.(error)}
    >
      <div className="flex items-start space-x-2">
        <span className="text-lg">{getTypeIcon(error.type)}</span>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {error.type}
            </Badge>
            <Badge variant={error.severity === 'error' ? 'destructive' : 'secondary'} className="text-xs">
              {error.severity}
            </Badge>
          </div>
          <p className="text-sm font-medium mt-1">{error.message}</p>
          {error.line && (
            <p className="text-xs text-muted-foreground mt-1">
              Line {error.line}{error.column && `, Column ${error.column}`}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  // Render warning item
  const renderWarningItem = (warning: ValidationWarning, index: number) => (
    <div
      key={`warning-${index}`}
      className="p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors text-yellow-600 bg-yellow-50 border-yellow-200"
      onClick={() => onWarningClick?.(warning)}
    >
      <div className="flex items-start space-x-2">
        <AlertTriangle className="w-4 h-4 mt-0.5" />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium">{warning.message}</p>
          {warning.suggestion && (
            <p className="text-xs text-muted-foreground mt-1">
              💡 {warning.suggestion}
            </p>
          )}
          {warning.line && (
            <p className="text-xs text-muted-foreground mt-1">
              Line {warning.line}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <validationStatus.icon 
              className={`w-4 h-4 mr-2 ${validationStatus.color} ${isValidating ? 'animate-spin' : ''}`} 
            />
            Validation
          </span>
          {onRevalidate && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRevalidate}
              disabled={isValidating}
            >
              <RefreshCw className={`w-4 h-4 ${isValidating ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Summary */}
        <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
          <div className="flex items-center space-x-2">
            <validationStatus.icon className={`w-5 h-5 ${validationStatus.color}`} />
            <span className="font-medium">{validationStatus.label}</span>
          </div>
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            {errors.length > 0 && (
              <span className="flex items-center">
                <XCircle className="w-4 h-4 mr-1 text-red-500" />
                {errors.length}
              </span>
            )}
            {warnings.length > 0 && (
              <span className="flex items-center">
                <AlertTriangle className="w-4 h-4 mr-1 text-yellow-500" />
                {warnings.length}
              </span>
            )}
          </div>
        </div>

        {/* Validation Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-2 bg-muted rounded">
            <div className="flex items-center justify-center mb-1">
              <TrendingUp className="w-4 h-4 mr-1" />
              <span className="text-sm font-medium">Validations</span>
            </div>
            <span className="text-lg font-bold">{validationCount}</span>
          </div>
          <div className="text-center p-2 bg-muted rounded">
            <div className="flex items-center justify-center mb-1">
              <Clock className="w-4 h-4 mr-1" />
              <span className="text-sm font-medium">Last Check</span>
            </div>
            <span className="text-xs">
              {lastValidated ? lastValidated.toLocaleTimeString() : 'Never'}
            </span>
          </div>
        </div>

        {/* Validation Results */}
        {(errors.length > 0 || warnings.length > 0) ? (
          <Tabs defaultValue="errors" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="errors" className="flex items-center">
                <XCircle className="w-4 h-4 mr-1" />
                Errors ({errors.length})
              </TabsTrigger>
              <TabsTrigger value="warnings" className="flex items-center">
                <AlertTriangle className="w-4 h-4 mr-1" />
                Warnings ({warnings.length})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="errors" className="mt-4">
              {errors.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-500" />
                  <p className="text-sm text-muted-foreground">No errors found</p>
                </div>
              ) : (
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {errors.map(renderErrorItem)}
                  </div>
                </ScrollArea>
              )}
            </TabsContent>
            
            <TabsContent value="warnings" className="mt-4">
              {warnings.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-500" />
                  <p className="text-sm text-muted-foreground">No warnings found</p>
                </div>
              ) : (
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {warnings.map(renderWarningItem)}
                  </div>
                </ScrollArea>
              )}
            </TabsContent>
          </Tabs>
        ) : !isValidating && (
          <div className="text-center py-8">
            <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-500" />
            <p className="text-sm font-medium">Document is valid!</p>
            <p className="text-xs text-muted-foreground">No errors or warnings found</p>
          </div>
        )}

        {/* Error Summary by Type */}
        {Object.keys(errorsByType).length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Errors by Type</h4>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(errorsByType).map(([type, typeErrors]) => (
                <div key={type} className="flex items-center justify-between p-2 bg-muted rounded text-sm">
                  <span className="flex items-center">
                    <span className="mr-1">{getTypeIcon(type)}</span>
                    {type}
                  </span>
                  <Badge variant="destructive" className="text-xs">
                    {typeErrors.length}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
