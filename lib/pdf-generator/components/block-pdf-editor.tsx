/**
 * Block-based PDF Editor
 * 
 * Integration of the block editor with the existing PDF generator system
 */

'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { 
  FileText, 
  Save, 
  Download,
  Eye,
  Settings,
  Blocks,
  Code,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

// Import existing components and hooks
import { usePDFGenerator } from '../hooks/usePDFGenerator';
import { useValidation } from '../hooks/useValidation';
import { DocumentMetadata } from './document-metadata';
import { TemplateSelector } from './template-selector';
import { ValidationPanel } from './validation-panel';
import { ExportControls } from './export-controls';
import { PreviewPanel } from './preview-panel';

// Import new block editor components
import { BlockEditor } from './block-editor';
import { useBlockEditor } from '../hooks/useBlockEditor';
import { Block, BlockType } from '../blocks/types';
import { DocumentTemplate } from '../types';

export interface BlockPDFEditorProps {
  initialTitle?: string;
  initialContent?: string;
  initialBlocks?: Block[];
  initialTemplate?: string;
  onSave?: (document: any, blocks: Block[], markdown: string) => void;
  onExport?: (format: string, result: Buffer | string) => void;
  className?: string;
}

export function BlockPDFEditor({
  initialTitle = 'New Document',
  initialContent = '',
  initialBlocks,
  initialTemplate = 'research-report',
  onSave,
  onExport,
  className = '',
}: BlockPDFEditorProps) {
  // Main PDF generator hook
  const {
    document,
    isGenerating,
    errors: pdfErrors,
    warnings: pdfWarnings,
    createDocument,
    updateDocument,
    generatePDF,
    isDirty,
    canGenerate,
    lastSaved,
  } = usePDFGenerator({
    autoValidate: true,
    autoPreview: true,
    debounceMs: 500,
  });

  // Block editor hook
  const {
    blocks,
    toMarkdown,
    fromMarkdown,
    validationErrors: blockErrors,
    isEmpty: isBlocksEmpty,
    blockCount,
  } = useBlockEditor({
    initialBlocks,
    initialMarkdown: initialContent,
    onChange: (blocks) => {
      // Update PDF document when blocks change
      const markdown = toMarkdown();
      if (document) {
        updateDocument({ content: markdown });
      }
    },
  });

  // Validation hook for real-time feedback
  const {
    isValidating,
    validate,
    validationSummary,
  } = useValidation({
    autoValidate: true,
    debounceMs: 300,
  });

  // Local state
  const [activeTab, setActiveTab] = useState('blocks');
  const [title, setTitle] = useState(initialTitle);
  const [editorMode, setEditorMode] = useState<'blocks' | 'markdown'>('blocks');

  // Initialize document
  useEffect(() => {
    if (!document) {
      createDocument(initialTitle, initialContent || toMarkdown(), initialTemplate);
    }
  }, [document, createDocument, initialTitle, initialContent, initialTemplate, toMarkdown]);

  // Sync blocks with markdown when switching modes
  useEffect(() => {
    if (document && editorMode === 'markdown') {
      // When switching to markdown mode, ensure blocks are synced
      const currentMarkdown = toMarkdown();
      if (currentMarkdown !== document.content) {
        updateDocument({ content: currentMarkdown });
      }
    }
  }, [editorMode, document, toMarkdown, updateDocument]);

  // Handle title change
  const handleTitleChange = useCallback((newTitle: string) => {
    setTitle(newTitle);
    if (document) {
      updateDocument({ title: newTitle });
    }
  }, [document, updateDocument]);

  // Handle markdown content change (when in markdown mode)
  const handleMarkdownChange = useCallback((newContent: string) => {
    if (document) {
      updateDocument({ content: newContent });
      // Update blocks from markdown
      fromMarkdown(newContent);
    }
    // Also validate the content
    validate(newContent);
  }, [document, updateDocument, fromMarkdown, validate]);

  // Handle metadata change
  const handleMetadataChange = useCallback((metadata: Record<string, any>) => {
    if (document) {
      updateDocument({ metadata });
    }
  }, [document, updateDocument]);

  // Handle template selection
  const handleTemplateSelect = useCallback((template: DocumentTemplate) => {
    if (document) {
      updateDocument({ template });
    }
  }, [document, updateDocument]);

  // Handle save
  const handleSave = useCallback(() => {
    if (document && isDirty) {
      const markdown = toMarkdown();
      onSave?.(document, blocks, markdown);
    }
  }, [document, isDirty, blocks, toMarkdown, onSave]);

  // Handle export
  const handleExport = useCallback((format: string, result: Buffer | string) => {
    onExport?.(format, result);
  }, [onExport]);

  // Handle PDF generation
  const handleGeneratePDF = useCallback(async () => {
    if (canGenerate) {
      try {
        const pdfBuffer = await generatePDF();
        // Auto-download the PDF
        const blob = new Blob([pdfBuffer], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${document?.title || 'document'}.pdf`;
        link.click();
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error('PDF generation failed:', error);
      }
    }
  }, [canGenerate, generatePDF, document]);

  // Handle block editor save
  const handleBlockEditorSave = useCallback((blocks: Block[], markdown: string) => {
    if (document) {
      updateDocument({ content: markdown });
      handleSave();
    }
  }, [document, updateDocument, handleSave]);

  // Handle block editor preview
  const handleBlockEditorPreview = useCallback((blocks: Block[], markdown: string) => {
    if (document) {
      updateDocument({ content: markdown });
    }
  }, [document, updateDocument]);

  // Handle block editor export
  const handleBlockEditorExport = useCallback((blocks: Block[], markdown: string) => {
    if (document) {
      updateDocument({ content: markdown });
      handleGeneratePDF();
    }
  }, [document, updateDocument, handleGeneratePDF]);

  // Handle editor mode switch
  const handleEditorModeSwitch = useCallback((mode: 'blocks' | 'markdown') => {
    if (mode === 'markdown' && editorMode === 'blocks') {
      // Switching from blocks to markdown - update document content
      const markdown = toMarkdown();
      if (document) {
        updateDocument({ content: markdown });
      }
    } else if (mode === 'blocks' && editorMode === 'markdown') {
      // Switching from markdown to blocks - update blocks from document
      if (document?.content) {
        fromMarkdown(document.content);
      }
    }
    setEditorMode(mode);
  }, [editorMode, toMarkdown, fromMarkdown, document, updateDocument]);

  // Combine all validation errors
  const allErrors = [...pdfErrors, ...blockErrors];
  const allWarnings = [...pdfWarnings];
  const hasValidationIssues = allErrors.length > 0 || allWarnings.length > 0;

  return (
    <div className={`h-screen flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center space-x-4">
          <FileText className="w-6 h-6" />
          <input
            type="text"
            value={title}
            onChange={(e) => handleTitleChange(e.target.value)}
            className="text-lg font-semibold border-none shadow-none p-0 h-auto bg-transparent outline-none"
            placeholder="Document title"
          />
          {isDirty && (
            <span className="text-xs text-muted-foreground">• Unsaved changes</span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Editor mode toggle */}
          <div className="flex items-center border rounded-lg p-1">
            <Button
              variant={editorMode === 'blocks' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleEditorModeSwitch('blocks')}
            >
              <Blocks className="w-4 h-4 mr-1" />
              Blocks
            </Button>
            <Button
              variant={editorMode === 'markdown' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleEditorModeSwitch('markdown')}
            >
              <Code className="w-4 h-4 mr-1" />
              Markdown
            </Button>
          </div>

          <Button
            onClick={handleSave}
            disabled={!isDirty}
            variant="outline"
            size="sm"
          >
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          
          <Button
            onClick={handleGeneratePDF}
            disabled={!canGenerate || isGenerating}
            size="sm"
          >
            <Download className="w-4 h-4 mr-2" />
            {isGenerating ? 'Generating...' : 'Generate PDF'}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal">
          {/* Left Panel - Editor and Settings */}
          <ResizablePanel defaultSize={60} minSize={40}>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="blocks">
                  {editorMode === 'blocks' ? 'Block Editor' : 'Markdown Editor'}
                </TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
                <TabsTrigger value="validation">
                  Validation
                  {hasValidationIssues && (
                    <AlertTriangle className="w-3 h-3 ml-1 text-yellow-500" />
                  )}
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="blocks" className="flex-1 overflow-hidden">
                {editorMode === 'blocks' ? (
                  <BlockEditor
                    initialBlocks={blocks}
                    onSave={handleBlockEditorSave}
                    onPreview={handleBlockEditorPreview}
                    onExport={handleBlockEditorExport}
                    autoSave={false}
                    placeholder="Start writing your document..."
                    className="h-full"
                  />
                ) : (
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Code className="w-4 h-4 mr-2" />
                        Markdown Editor
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="h-full">
                      <textarea
                        value={document?.content || ''}
                        onChange={(e) => handleMarkdownChange(e.target.value)}
                        className="w-full h-full resize-none font-mono text-sm border rounded p-4"
                        placeholder="Write your markdown content here..."
                      />
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
              
              <TabsContent value="settings" className="flex-1 p-4 space-y-4 overflow-auto">
                <DocumentMetadata
                  metadata={document?.metadata || {}}
                  onChange={handleMetadataChange}
                  showAdvanced={true}
                />
                
                <TemplateSelector
                  selectedTemplateId={document?.template?.id}
                  onTemplateSelect={handleTemplateSelect}
                  showPreview={true}
                />
              </TabsContent>
              
              <TabsContent value="validation" className="flex-1 p-4 overflow-auto">
                <ValidationPanel
                  errors={allErrors}
                  warnings={allWarnings}
                  isValidating={isValidating}
                  isValid={validationSummary.isValid && allErrors.length === 0}
                  lastValidated={validationSummary.lastValidated}
                  validationCount={validationSummary.total}
                  onRevalidate={() => validate(document?.content || '')}
                />
              </TabsContent>
            </Tabs>
          </ResizablePanel>

          <ResizableHandle />

          {/* Right Panel - Preview and Export */}
          <ResizablePanel defaultSize={40} minSize={30}>
            <Tabs defaultValue="preview" className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="preview">
                  <Eye className="w-4 h-4 mr-1" />
                  Preview
                </TabsTrigger>
                <TabsTrigger value="export">
                  <Download className="w-4 h-4 mr-1" />
                  Export
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="preview" className="flex-1 p-4 overflow-hidden">
                <PreviewPanel
                  document={document}
                  showStats={true}
                  showControls={true}
                  className="h-full"
                />
              </TabsContent>
              
              <TabsContent value="export" className="flex-1 p-4 overflow-auto">
                <ExportControls
                  document={document}
                  disabled={!canGenerate}
                  onExportComplete={handleExport}
                  className="h-full"
                />
              </TabsContent>
            </Tabs>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between p-2 border-t bg-muted/30 text-xs">
        <div className="flex items-center space-x-4">
          <span>
            {validationSummary.isValid && allErrors.length === 0 ? (
              <span className="flex items-center text-green-600">
                <CheckCircle className="w-3 h-3 mr-1" />
                Valid
              </span>
            ) : (
              <span className="text-red-600">
                {allErrors.length} errors, {allWarnings.length} warnings
              </span>
            )}
          </span>
          
          <span>
            Mode: {editorMode === 'blocks' ? `${blockCount} blocks` : 'Markdown'}
          </span>
          
          <span>
            Words: {(document?.content || '').split(/\s+/).filter(w => w.length > 0).length}
          </span>
          
          <span>
            Characters: {(document?.content || '').length}
          </span>
        </div>
        
        <div className="flex items-center space-x-4">
          {lastSaved && (
            <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
          )}
          
          <span>
            Template: {document?.template?.name || 'None'}
          </span>
        </div>
      </div>
    </div>
  );
}
