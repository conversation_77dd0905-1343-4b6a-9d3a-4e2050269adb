/**
 * Modular PDF Editor
 * 
 * Example component demonstrating how to use all modular components
 * and hooks together to create a complete PDF editing experience.
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { 
  FileText, 
  Save, 
  Settings,
  Eye,
  Download,
  CheckCircle
} from 'lucide-react';

// Import modular components and hooks
import { usePDFGenerator } from '../hooks/usePDFGenerator';
import { useValidation } from '../hooks/useValidation';
import { DocumentMetadata } from './document-metadata';
import { TemplateSelector } from './template-selector';
import { ValidationPanel } from './validation-panel';
import { ExportControls } from './export-controls';
import { PreviewPanel } from './preview-panel';
import { DocumentTemplate } from '../types';

export interface ModularPDFEditorProps {
  initialTitle?: string;
  initialContent?: string;
  initialTemplate?: string;
  onSave?: (document: any) => void;
  onExport?: (format: string, result: Buffer | string) => void;
  className?: string;
}

export function ModularPDFEditor({
  initialTitle = 'New Document',
  initialContent = '# New Document\n\nStart writing your content here...',
  initialTemplate = 'research-report',
  onSave,
  onExport,
  className = '',
}: ModularPDFEditorProps) {
  // Main PDF generator hook
  const {
    document,
    isGenerating,
    errors,
    warnings,
    previewHtml,
    createDocument,
    updateDocument,
    generatePDF,
    isDirty,
    canGenerate,
    lastSaved,
  } = usePDFGenerator({
    autoValidate: true,
    autoPreview: true,
    debounceMs: 500,
  });

  // Validation hook for real-time feedback
  const {
    isValidating,
    validate,
    validationSummary,
  } = useValidation({
    autoValidate: true,
    debounceMs: 300,
  });

  // Local state
  const [activeTab, setActiveTab] = useState('editor');
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);

  // Initialize document
  React.useEffect(() => {
    if (!document) {
      createDocument(initialTitle, initialContent, initialTemplate);
    }
  }, [document, createDocument, initialTitle, initialContent, initialTemplate]);

  // Handle title change
  const handleTitleChange = useCallback((newTitle: string) => {
    setTitle(newTitle);
    if (document) {
      updateDocument({ title: newTitle });
    }
  }, [document, updateDocument]);

  // Handle content change
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
    if (document) {
      updateDocument({ content: newContent });
    }
    // Also validate the content
    validate(newContent);
  }, [document, updateDocument, validate]);

  // Handle metadata change
  const handleMetadataChange = useCallback((metadata: Record<string, any>) => {
    if (document) {
      updateDocument({ metadata });
    }
  }, [document, updateDocument]);

  // Handle template selection
  const handleTemplateSelect = useCallback((template: DocumentTemplate) => {
    if (document) {
      updateDocument({ template });
    }
  }, [document, updateDocument]);

  // Handle save
  const handleSave = useCallback(async () => {
    if (document && isDirty) {
      // In a real app, you'd save to a backend here
      onSave?.(document);
    }
  }, [document, isDirty, onSave]);

  // Handle export
  const handleExport = useCallback((format: string, result: Buffer | string) => {
    onExport?.(format, result);
  }, [onExport]);

  // Handle PDF generation
  const handleGeneratePDF = useCallback(async () => {
    if (canGenerate) {
      try {
        const pdfBuffer = await generatePDF();
        // Auto-download the PDF
        const blob = new Blob([pdfBuffer], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${document?.title || 'document'}.pdf`;
        link.click();
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error('PDF generation failed:', error);
      }
    }
  }, [canGenerate, generatePDF, document]);

  return (
    <div className={`h-screen flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center space-x-4">
          <FileText className="w-6 h-6" />
          <Input
            value={title}
            onChange={(e) => handleTitleChange(e.target.value)}
            className="text-lg font-semibold border-none shadow-none p-0 h-auto"
            placeholder="Document title"
          />
          {isDirty && (
            <span className="text-xs text-muted-foreground">• Unsaved changes</span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleSave}
            disabled={!isDirty}
            variant="outline"
            size="sm"
          >
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          
          <Button
            onClick={handleGeneratePDF}
            disabled={!canGenerate || isGenerating}
            size="sm"
          >
            <Download className="w-4 h-4 mr-2" />
            {isGenerating ? 'Generating...' : 'Generate PDF'}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal">
          {/* Left Panel - Editor and Settings */}
          <ResizablePanel defaultSize={60} minSize={40}>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="editor">Editor</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
                <TabsTrigger value="validation">Validation</TabsTrigger>
              </TabsList>
              
              <TabsContent value="editor" className="flex-1 p-4">
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="w-4 h-4 mr-2" />
                      Content Editor
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="h-full">
                    <Textarea
                      value={content}
                      onChange={(e) => handleContentChange(e.target.value)}
                      className="h-full resize-none font-mono text-sm"
                      placeholder="Write your markdown content here..."
                    />
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="settings" className="flex-1 p-4 space-y-4">
                <DocumentMetadata
                  metadata={document?.metadata || {}}
                  onChange={handleMetadataChange}
                  showAdvanced={true}
                />
                
                <TemplateSelector
                  selectedTemplateId={document?.template?.id}
                  onTemplateSelect={handleTemplateSelect}
                  showPreview={true}
                />
              </TabsContent>
              
              <TabsContent value="validation" className="flex-1 p-4">
                <ValidationPanel
                  errors={errors}
                  warnings={warnings}
                  isValidating={isValidating}
                  isValid={validationSummary.isValid}
                  lastValidated={validationSummary.lastValidated}
                  validationCount={validationSummary.total}
                  onRevalidate={() => validate(content)}
                />
              </TabsContent>
            </Tabs>
          </ResizablePanel>

          <ResizableHandle />

          {/* Right Panel - Preview and Export */}
          <ResizablePanel defaultSize={40} minSize={30}>
            <Tabs defaultValue="preview" className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="preview">
                  <Eye className="w-4 h-4 mr-1" />
                  Preview
                </TabsTrigger>
                <TabsTrigger value="export">
                  <Download className="w-4 h-4 mr-1" />
                  Export
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="preview" className="flex-1 p-4">
                <PreviewPanel
                  document={document}
                  showStats={true}
                  showControls={true}
                  className="h-full"
                />
              </TabsContent>
              
              <TabsContent value="export" className="flex-1 p-4">
                <ExportControls
                  document={document}
                  disabled={!canGenerate}
                  onExportComplete={handleExport}
                  className="h-full"
                />
              </TabsContent>
            </Tabs>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between p-2 border-t bg-muted/30 text-xs">
        <div className="flex items-center space-x-4">
          <span>
            {validationSummary.isValid ? (
              <span className="flex items-center text-green-600">
                <CheckCircle className="w-3 h-3 mr-1" />
                Valid
              </span>
            ) : (
              <span className="text-red-600">
                {validationSummary.errors} errors, {validationSummary.warnings} warnings
              </span>
            )}
          </span>
          
          <span>
            Words: {content.split(/\s+/).filter(w => w.length > 0).length}
          </span>
          
          <span>
            Characters: {content.length}
          </span>
        </div>
        
        <div className="flex items-center space-x-4">
          {lastSaved && (
            <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
          )}
          
          <span>
            Template: {document?.template?.name || 'None'}
          </span>
        </div>
      </div>
    </div>
  );
}
