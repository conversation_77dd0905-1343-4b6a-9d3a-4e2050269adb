/**
 * PDF Generator Components
 * 
 * Modular React components for PDF generation functionality
 */

// Main editor components
export { PDFEditor } from './pdf-editor';
export { EnhancedBlockEditor as BlockEditor } from './enhanced-block-editor'; // Use enhanced as main
export { EnhancedBlockEditor } from './enhanced-block-editor';
export { BlockPDFEditor } from './block-pdf-editor';
export { ModularPDFEditor } from './modular-pdf-editor';

// Modular components
export { DocumentMetadata } from './document-metadata';
export { TemplateSelector } from './template-selector';
export { ValidationPanel } from './validation-panel';
export { ExportControls } from './export-controls';
export { PreviewPanel } from './preview-panel';

// Block components
export * from './blocks';

// Component types
export type { DocumentMetadataProps } from './document-metadata';
export type { TemplateSelectorProps } from './template-selector';
export type { ValidationPanelProps } from './validation-panel';
export type { ExportControlsProps } from './export-controls';
export type { PreviewPanelProps } from './preview-panel';
export type { EnhancedBlockEditorProps as BlockEditorProps } from './enhanced-block-editor'; // Use enhanced as main
export type { EnhancedBlockEditorProps } from './enhanced-block-editor';
export type { BlockPDFEditorProps } from './block-pdf-editor';
export type { ModularPDFEditorProps } from './modular-pdf-editor';
