/**
 * PDF Editor Component
 * 
 * React component for real-time PDF editing with markdown preview,
 * template selection, and live validation.
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  FileText, 
  Download, 
  Eye, 
  AlertTriangle, 
  CheckCircle, 
  Settings,
  Save,
  RefreshCw
} from 'lucide-react';

import { 
  PDFDocument, 
  PDFEditorState, 
  ValidationError, 
  ValidationWarning,
  PreviewMode 
} from '../types';
import { 
  pdfGeneratorService,
  validateMarkdown,
  getAvailableTemplates,
  previewDocumentHTML,
  generatePDFFromMarkdown
} from '../index';

interface PDFEditorProps {
  initialDocument?: Partial<PDFDocument>;
  onDocumentChange?: (document: PDFDocument) => void;
  onSave?: (document: PDFDocument) => void;
  onExport?: (document: PDFDocument, format: string) => void;
  className?: string;
}

export function PDFEditor({
  initialDocument,
  onDocumentChange,
  onSave,
  onExport,
  className = '',
}: PDFEditorProps) {
  const [editorState, setEditorState] = useState<PDFEditorState | null>(null);
  const [templates, setTemplates] = useState<Array<{ id: string; name: string; type: string }>>([]);
  const [previewHtml, setPreviewHtml] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [lastValidation, setLastValidation] = useState<Date | null>(null);

  // Initialize editor
  useEffect(() => {
    const initializeEditor = async () => {
      try {
        // Load templates
        const availableTemplates = await getAvailableTemplates();
        setTemplates(availableTemplates);

        // Create or load document
        let document: PDFDocument;
        if (initialDocument?.id) {
          document = initialDocument as PDFDocument;
        } else {
          document = await pdfGeneratorService.createDocument(
            initialDocument?.title || 'New Document',
            initialDocument?.content || '# New Document\n\nStart writing your content here...',
            initialDocument?.template?.id || 'research-report',
            initialDocument?.metadata || {}
          );
        }

        // Initialize editor state
        const state = await pdfGeneratorService.getEditorState(document);
        setEditorState(state);

        // Generate initial preview
        await updatePreview(document);
      } catch (error) {
        console.error('Failed to initialize editor:', error);
      }
    };

    initializeEditor();
  }, [initialDocument]);

  // Update preview
  const updatePreview = useCallback(async (document: PDFDocument) => {
    try {
      const html = await previewDocumentHTML(document);
      setPreviewHtml(html);
    } catch (error) {
      console.error('Failed to update preview:', error);
    }
  }, []);

  // Validate content
  const validateContent = useCallback(async (content: string) => {
    setIsValidating(true);
    try {
      const validation = await validateMarkdown(content);
      setLastValidation(new Date());
      return validation;
    } catch (error) {
      console.error('Validation failed:', error);
      return { isValid: false, errors: [], warnings: [] };
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Handle content change
  const handleContentChange = useCallback(async (content: string) => {
    if (!editorState) return;

    const updatedDocument = {
      ...editorState.document,
      content,
      updatedAt: new Date(),
    };

    const validation = await validateContent(content);
    
    const newState = await pdfGeneratorService.updateEditorState(editorState, {
      document: updatedDocument,
      isDirty: true,
      errors: validation.errors,
      warnings: validation.warnings,
    });

    setEditorState(newState);
    onDocumentChange?.(updatedDocument);

    // Update preview if in split or preview mode
    if (newState.previewMode === 'split' || newState.previewMode === 'pdf') {
      await updatePreview(updatedDocument);
    }
  }, [editorState, validateContent, onDocumentChange, updatePreview]);

  // Handle metadata change
  const handleMetadataChange = useCallback((field: string, value: any) => {
    if (!editorState) return;

    const updatedDocument = {
      ...editorState.document,
      metadata: {
        ...editorState.document.metadata,
        [field]: value,
      },
      updatedAt: new Date(),
    };

    const newState = {
      ...editorState,
      document: updatedDocument,
      isDirty: true,
    };

    setEditorState(newState);
    onDocumentChange?.(updatedDocument);
  }, [editorState, onDocumentChange]);

  // Handle template change
  const handleTemplateChange = useCallback(async (templateId: string) => {
    if (!editorState) return;

    const template = templates.find(t => t.id === templateId);
    if (!template) return;

    const updatedDocument = {
      ...editorState.document,
      template: {
        ...editorState.document.template,
        id: templateId,
        name: template.name,
        type: template.type as any,
      },
      updatedAt: new Date(),
    };

    const newState = {
      ...editorState,
      document: updatedDocument,
      isDirty: true,
    };

    setEditorState(newState);
    onDocumentChange?.(updatedDocument);
    await updatePreview(updatedDocument);
  }, [editorState, templates, onDocumentChange, updatePreview]);

  // Handle save
  const handleSave = useCallback(async () => {
    if (!editorState || !editorState.isDirty) return;

    try {
      const updatedDocument = await pdfGeneratorService.updateDocument(
        editorState.document,
        { updatedAt: new Date() }
      );

      const newState = {
        ...editorState,
        document: updatedDocument,
        isDirty: false,
        lastSaved: new Date(),
      };

      setEditorState(newState);
      onSave?.(updatedDocument);
    } catch (error) {
      console.error('Save failed:', error);
    }
  }, [editorState, onSave]);

  // Handle export
  const handleExport = useCallback(async (format: string) => {
    if (!editorState) return;

    setIsGenerating(true);
    try {
      if (format === 'pdf') {
        await generatePDFFromMarkdown(
          editorState.document.title,
          editorState.document.content,
          {
            template: editorState.document.template.id,
            metadata: editorState.document.metadata,
          }
        );
      }
      
      onExport?.(editorState.document, format);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [editorState, onExport]);

  // Handle preview mode change
  const handlePreviewModeChange = useCallback(async (mode: PreviewMode) => {
    if (!editorState) return;

    const newState = {
      ...editorState,
      previewMode: mode,
    };

    setEditorState(newState);

    if (mode === 'pdf' || mode === 'split') {
      await updatePreview(editorState.document);
    }
  }, [editorState, updatePreview]);

  if (!editorState) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-6 h-6 animate-spin" />
        <span className="ml-2">Loading editor...</span>
      </div>
    );
  }

  const { document, errors, warnings, isDirty, previewMode } = editorState;

  return (
    <div className={`pdf-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <FileText className="w-6 h-6" />
          <Input
            value={document.title}
            onChange={(e) => handleMetadataChange('title', e.target.value)}
            className="text-lg font-semibold"
            placeholder="Document title"
          />
          {isDirty && <Badge variant="secondary">Unsaved</Badge>}
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={document.template.id} onValueChange={handleTemplateChange}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select template" />
            </SelectTrigger>
            <SelectContent>
              {templates.map((template) => (
                <SelectItem key={template.id} value={template.id}>
                  {template.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button onClick={handleSave} disabled={!isDirty} variant="outline">
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          
          <Button onClick={() => handleExport('pdf')} disabled={isGenerating}>
            <Download className="w-4 h-4 mr-2" />
            {isGenerating ? 'Generating...' : 'Export PDF'}
          </Button>
        </div>
      </div>

      {/* Validation Status */}
      {(errors.length > 0 || warnings.length > 0) && (
        <div className="p-4 border-b">
          {errors.length > 0 && (
            <Alert className="mb-2">
              <AlertTriangle className="w-4 h-4" />
              <AlertDescription>
                {errors.length} error(s) found. Please fix them before generating PDF.
              </AlertDescription>
            </Alert>
          )}
          
          {warnings.length > 0 && (
            <Alert>
              <CheckCircle className="w-4 h-4" />
              <AlertDescription>
                {warnings.length} warning(s) found. Consider reviewing them.
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex">
        <Tabs value={previewMode} onValueChange={handlePreviewModeChange as any} className="flex-1">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="markdown">Markdown</TabsTrigger>
            <TabsTrigger value="html">HTML</TabsTrigger>
            <TabsTrigger value="split">Split</TabsTrigger>
            <TabsTrigger value="pdf">Preview</TabsTrigger>
          </TabsList>
          
          <TabsContent value="markdown" className="flex-1">
            <div className="h-full p-4">
              <Textarea
                value={document.content}
                onChange={(e) => handleContentChange(e.target.value)}
                className="h-full resize-none font-mono"
                placeholder="Write your markdown content here..."
              />
            </div>
          </TabsContent>
          
          <TabsContent value="html" className="flex-1">
            <div className="h-full p-4">
              <div 
                className="prose max-w-none h-full overflow-auto border rounded p-4"
                dangerouslySetInnerHTML={{ __html: previewHtml }}
              />
            </div>
          </TabsContent>
          
          <TabsContent value="split" className="flex-1">
            <div className="flex h-full">
              <div className="w-1/2 p-4">
                <Textarea
                  value={document.content}
                  onChange={(e) => handleContentChange(e.target.value)}
                  className="h-full resize-none font-mono"
                  placeholder="Write your markdown content here..."
                />
              </div>
              <Separator orientation="vertical" />
              <div className="w-1/2 p-4">
                <div 
                  className="prose max-w-none h-full overflow-auto border rounded p-4"
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="pdf" className="flex-1">
            <div className="h-full p-4">
              <div 
                className="prose max-w-none h-full overflow-auto border rounded p-4 bg-white"
                dangerouslySetInnerHTML={{ __html: previewHtml }}
                style={{ 
                  fontFamily: 'serif',
                  lineHeight: '1.6',
                  maxWidth: '8.5in',
                  margin: '0 auto',
                  padding: '1in',
                }}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Sidebar */}
      <div className="w-80 border-l p-4 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="w-4 h-4 mr-2" />
              Document Info
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Author</label>
              <Input
                value={document.metadata.author || ''}
                onChange={(e) => handleMetadataChange('author', e.target.value)}
                placeholder="Document author"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Subject</label>
              <Input
                value={document.metadata.subject || ''}
                onChange={(e) => handleMetadataChange('subject', e.target.value)}
                placeholder="Document subject"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Keywords</label>
              <Input
                value={(document.metadata.keywords || []).join(', ')}
                onChange={(e) => handleMetadataChange('keywords', e.target.value.split(',').map(k => k.trim()))}
                placeholder="keyword1, keyword2, keyword3"
              />
            </div>
          </CardContent>
        </Card>

        {/* Validation Results */}
        {(errors.length > 0 || warnings.length > 0) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="w-4 h-4 mr-2" />
                Validation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {errors.map((error, index) => (
                  <div key={index} className="text-sm text-red-600">
                    <strong>Error:</strong> {error.message}
                    {error.line && <span className="text-gray-500"> (Line {error.line})</span>}
                  </div>
                ))}
                
                {warnings.map((warning, index) => (
                  <div key={index} className="text-sm text-yellow-600">
                    <strong>Warning:</strong> {warning.message}
                    {warning.line && <span className="text-gray-500"> (Line {warning.line})</span>}
                  </div>
                ))}
              </div>
              
              {lastValidation && (
                <div className="text-xs text-gray-500 mt-2">
                  Last validated: {lastValidation.toLocaleTimeString()}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
