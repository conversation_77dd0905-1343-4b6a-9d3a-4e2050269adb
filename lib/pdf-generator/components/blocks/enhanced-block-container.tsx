/**
 * Enhanced Block Container
 * 
 * Block container with four-state lifecycle support and modal editors
 */

'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Block, BlockType } from '../../blocks/types';
import { BlockState, blockStateManager } from '../../blocks/block-state-manager';
import { BlockToolbar } from './block-toolbar';
import { BlockModalEditor } from './block-modal-editor';
import { ParagraphBlockComponent } from './paragraph-block';
import { HeadingBlockComponent } from './heading-block';
import { CodeBlockComponent } from './code-block';

export interface EnhancedBlockContainerProps {
  block: Block;
  index: number;
  isSelected: boolean;
  isFocused: boolean;
  isDragging: boolean;
  isDropTarget: boolean;
  initialState?: BlockState;
  onUpdate: (updates: Partial<Block>) => void;
  onFocus: () => void;
  onBlur: () => void;
  onSelect: () => void;
  onKeyDown: (e: React.KeyboardEvent, action?: string, data?: any) => void;
  onAddBlock: (type: BlockType, position: 'before' | 'after') => void;
  onDeleteBlock: () => void;
  onDuplicateBlock: () => void;
  onMoveBlock: (direction: 'up' | 'down') => void;
  onConvertBlock: (newType: BlockType) => void;
  onDragStart: () => void;
  onDragEnd: () => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
  className?: string;
}

export function EnhancedBlockContainer({
  block,
  index,
  isSelected,
  isFocused,
  isDragging,
  isDropTarget,
  initialState = 'preview',
  onUpdate,
  onFocus,
  onBlur,
  onSelect,
  onKeyDown,
  onAddBlock,
  onDeleteBlock,
  onDuplicateBlock,
  onMoveBlock,
  onConvertBlock,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDrop,
  className = '',
}: EnhancedBlockContainerProps) {
  const [currentState, setCurrentState] = useState<BlockState>(initialState);
  const [showModal, setShowModal] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Initialize block state manager
  useEffect(() => {
    const context = blockStateManager.initializeBlock(block.id, initialState);
    
    // Register state transition callbacks
    blockStateManager.onStateTransition(block.id, 'after', 'edit', () => {
      setCurrentState('edit');
    });
    
    blockStateManager.onStateTransition(block.id, 'after', 'preview', () => {
      setCurrentState('preview');
    });

    blockStateManager.onStateTransition(block.id, 'after', 'render', () => {
      setCurrentState('render');
    });

    blockStateManager.onStateTransition(block.id, 'after', 'print', () => {
      setCurrentState('print');
    });

    return () => {
      blockStateManager.cleanup(block.id);
    };
  }, [block.id, initialState]);

  // Handle state transitions based on selection and focus
  useEffect(() => {
    if (isFocused && currentState === 'preview') {
      handleStateTransition('edit');
    } else if (!isFocused && !isSelected && currentState === 'edit') {
      handleStateTransition('preview');
    }
  }, [isFocused, isSelected, currentState]);

  // Handle state transition
  const handleStateTransition = useCallback(async (newState: BlockState) => {
    if (currentState === newState || isTransitioning) return;

    setIsTransitioning(true);
    
    const success = await blockStateManager.transitionTo(block.id, newState, {
      callback: (success, error) => {
        if (!success) {
          console.warn(`State transition failed: ${error}`);
        }
        setIsTransitioning(false);
      }
    });

    if (success) {
      setCurrentState(newState);
    }
  }, [block.id, currentState, isTransitioning]);

  // Handle click based on current state
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    
    switch (currentState) {
      case 'preview':
        onSelect();
        handleStateTransition('edit');
        break;
      case 'edit':
        // Already in edit mode, just ensure focus
        onFocus();
        break;
      default:
        onSelect();
        break;
    }
  }, [currentState, onSelect, onFocus, handleStateTransition]);

  // Handle double-click for modal editor
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasModalEditor(block.type)) {
      setShowModal(true);
    }
  }, [block.type]);

  // Handle modal editor save
  const handleModalSave = useCallback((updatedBlock: Partial<Block>) => {
    onUpdate(updatedBlock);
    setShowModal(false);
  }, [onUpdate]);

  // Handle modal editor cancel
  const handleModalCancel = useCallback(() => {
    setShowModal(false);
  }, []);

  // Get state-specific configuration
  const stateConfig = blockStateManager.getStateConfig(block.id, currentState);
  const stateClasses = blockStateManager.getStateClasses(block.id, className);

  // Render block component based on current state
  const renderBlockComponent = () => {
    const componentProps = {
      block: block as any,
      isSelected: isSelected && currentState !== 'render' && currentState !== 'print',
      isFocused: isFocused && currentState === 'edit',
      isEditing: currentState === 'edit',
      onUpdate,
      onFocus,
      onBlur,
      onKeyDown,
      readOnly: !stateConfig.interactive,
      optimizeForExport: stateConfig.optimizeForExport,
      optimizeForPrint: stateConfig.optimizeForPrint,
    };

    switch (block.type) {
      case 'paragraph':
        return <ParagraphBlockComponent {...componentProps} />;
      case 'heading':
        return <HeadingBlockComponent {...componentProps} />;
      case 'code':
        return <CodeBlockComponent {...componentProps} />;
      default:
        return <ParagraphBlockComponent {...componentProps} />;
    }
  };

  // Check if block type has modal editor
  const hasModalEditor = (blockType: BlockType): boolean => {
    return ['table', 'image', 'code', 'math', 'embed'].includes(blockType);
  };

  return (
    <>
      <div
        ref={containerRef}
        className={`
          relative group transition-all duration-200
          ${stateClasses}
          ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
          ${isDragging ? 'opacity-50 scale-95' : ''}
          ${isDropTarget ? 'ring-2 ring-green-400' : ''}
          ${isTransitioning ? 'opacity-75' : ''}
        `}
        onClick={handleClick}
        onDoubleClick={handleDoubleClick}
        onDragStart={onDragStart}
        onDragEnd={onDragEnd}
        onDragOver={onDragOver}
        onDrop={onDrop}
        draggable={currentState !== 'edit'}
        data-block-id={block.id}
        data-block-state={currentState}
      >
        {/* Drop indicator */}
        {isDropTarget && (
          <div className="absolute inset-0 bg-green-100 border-2 border-green-400 border-dashed rounded pointer-events-none" />
        )}

        {/* State indicator */}
        {process.env.NODE_ENV === 'development' && (
          <div className="absolute top-0 left-0 transform -translate-x-2 -translate-y-2">
            <div className={`
              text-xs px-1 py-0.5 rounded text-white
              ${currentState === 'preview' ? 'bg-blue-500' : ''}
              ${currentState === 'edit' ? 'bg-green-500' : ''}
              ${currentState === 'render' ? 'bg-orange-500' : ''}
              ${currentState === 'print' ? 'bg-purple-500' : ''}
            `}>
              {currentState}
            </div>
          </div>
        )}

        {/* Block content */}
        <div className="relative z-10 p-2">
          {renderBlockComponent()}
        </div>

        {/* Block toolbar - only show in edit state */}
        {currentState === 'edit' && stateConfig.showToolbar && (
          <BlockToolbar
            block={block}
            position="left"
            onAddBlock={onAddBlock}
            onDeleteBlock={onDeleteBlock}
            onDuplicateBlock={onDuplicateBlock}
            onMoveBlock={onMoveBlock}
            onConvertBlock={onConvertBlock}
            onDragStart={onDragStart}
            onOpenModal={() => hasModalEditor(block.type) && setShowModal(true)}
            className="opacity-0 group-hover:opacity-100 transition-opacity"
          />
        )}

        {/* State transition indicator */}
        {isTransitioning && (
          <div className="absolute inset-0 bg-blue-100 bg-opacity-50 flex items-center justify-center">
            <div className="bg-white rounded-full p-2 shadow-lg">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            </div>
          </div>
        )}

        {/* Block type indicator */}
        {isSelected && currentState !== 'render' && currentState !== 'print' && (
          <div className="absolute top-0 right-0 transform translate-x-2 -translate-y-2">
            <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded">
              {block.type}
            </div>
          </div>
        )}

        {/* Modal editor indicator */}
        {hasModalEditor(block.type) && currentState === 'edit' && (
          <div className="absolute bottom-0 right-0 transform translate-x-1 translate-y-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowModal(true);
              }}
              className="bg-purple-500 text-white text-xs px-2 py-1 rounded hover:bg-purple-600 transition-colors"
            >
              ⚡ Modal
            </button>
          </div>
        )}
      </div>

      {/* Modal Editor */}
      {showModal && hasModalEditor(block.type) && (
        <BlockModalEditor
          block={block}
          isOpen={showModal}
          onSave={handleModalSave}
          onCancel={handleModalCancel}
        />
      )}
    </>
  );
}

// Export utility functions
export function transitionBlockToState(blockId: string, state: BlockState): Promise<boolean> {
  return blockStateManager.transitionTo(blockId, state);
}

export function getBlockCurrentState(blockId: string): BlockState | null {
  return blockStateManager.getCurrentState(blockId);
}

export function batchTransitionBlocks(blockIds: string[], state: BlockState): Promise<{ success: string[]; failed: string[] }> {
  return blockStateManager.batchTransition(blockIds, state);
}
