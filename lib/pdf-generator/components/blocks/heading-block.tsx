/**
 * Heading Block Component
 * 
 * Editable heading block with level selection
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { HeadingBlock, HeadingLevel } from '../../blocks/types';
import { BaseBlockProps } from './base-block';

export interface HeadingBlockProps extends BaseBlockProps<HeadingBlock> {}

export function HeadingBlockComponent({
  block,
  isSelected,
  isFocused,
  isEditing,
  onUpdate,
  onFocus,
  onBlur,
  onKeyDown,
  className = '',
}: HeadingBlockProps) {
  const [content, setContent] = useState(block.content);
  const [level, setLevel] = useState(block.level);
  const inputRef = useRef<HTMLInputElement>(null);

  // Focus input when block is focused
  useEffect(() => {
    if (isFocused && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isFocused]);

  const handleContentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newContent = e.target.value;
    setContent(newContent);
    onUpdate({ content: newContent });
  };

  const handleLevelChange = (newLevel: string) => {
    const levelNum = parseInt(newLevel) as HeadingLevel;
    setLevel(levelNum);
    onUpdate({ level: levelNum });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle Enter key to create new paragraph
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onKeyDown?.(e, 'create-block', { type: 'paragraph' });
      return;
    }

    // Handle Backspace at beginning to convert to paragraph
    if (e.key === 'Backspace' && inputRef.current?.selectionStart === 0 && content === '') {
      e.preventDefault();
      onKeyDown?.(e, 'merge-with-previous');
      return;
    }

    onKeyDown?.(e);
  };

  const handleFocus = () => {
    onFocus?.();
  };

  const handleBlur = () => {
    onBlur?.();
  };

  // Get heading styles based on level
  const getHeadingStyles = (level: HeadingLevel) => {
    const styles = {
      1: 'text-3xl font-bold',
      2: 'text-2xl font-bold',
      3: 'text-xl font-bold',
      4: 'text-lg font-semibold',
      5: 'text-base font-semibold',
      6: 'text-sm font-semibold',
    };
    return styles[level];
  };

  return (
    <div className={`heading-block ${className}`}>
      <div className="flex items-center space-x-2">
        {/* Level selector */}
        {(isSelected || isFocused) && (
          <Select value={level.toString()} onValueChange={handleLevelChange}>
            <SelectTrigger className="w-16 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {[1, 2, 3, 4, 5, 6].map(l => (
                <SelectItem key={l} value={l.toString()}>
                  H{l}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* Heading input */}
        <input
          ref={inputRef}
          type="text"
          value={content}
          onChange={handleContentChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={`Heading ${level}`}
          className={`
            flex-1 border-none outline-none bg-transparent
            ${getHeadingStyles(level)}
            ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
            ${isFocused ? 'ring-2 ring-blue-400' : ''}
          `}
        />
      </div>
    </div>
  );
}
