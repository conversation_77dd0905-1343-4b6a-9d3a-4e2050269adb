/**
 * Code Block Component
 * 
 * Editable code block with syntax highlighting and language selection
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Copy, Settings } from 'lucide-react';
import { CodeBlock } from '../../blocks/types';
import { BaseBlockProps } from './base-block';

export interface CodeBlockProps extends BaseBlockProps<CodeBlock> {}

const LANGUAGES = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'csharp', label: 'C#' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'html', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'sql', label: 'SQL' },
  { value: 'bash', label: 'Bash' },
  { value: 'json', label: 'JSON' },
  { value: 'yaml', label: 'YAML' },
  { value: 'markdown', label: 'Markdown' },
  { value: 'plaintext', label: 'Plain Text' },
];

export function CodeBlockComponent({
  block,
  isSelected,
  isFocused,
  isEditing,
  onUpdate,
  onFocus,
  onBlur,
  onKeyDown,
  className = '',
}: CodeBlockProps) {
  const [content, setContent] = useState(block.content);
  const [language, setLanguage] = useState(block.language || 'javascript');
  const [filename, setFilename] = useState(block.filename || '');
  const [showLineNumbers, setShowLineNumbers] = useState(block.showLineNumbers ?? true);
  const [showSettings, setShowSettings] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [content]);

  // Focus textarea when block is focused
  useEffect(() => {
    if (isFocused && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isFocused]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);
    onUpdate({ content: newContent });
  };

  const handleLanguageChange = (newLanguage: string) => {
    setLanguage(newLanguage);
    onUpdate({ language: newLanguage });
  };

  const handleFilenameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFilename = e.target.value;
    setFilename(newFilename);
    onUpdate({ filename: newFilename });
  };

  const handleLineNumbersToggle = () => {
    const newShowLineNumbers = !showLineNumbers;
    setShowLineNumbers(newShowLineNumbers);
    onUpdate({ showLineNumbers: newShowLineNumbers });
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      // Could show a toast notification here
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle Tab key for indentation
    if (e.key === 'Tab') {
      e.preventDefault();
      const textarea = textareaRef.current;
      if (textarea) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const newContent = content.substring(0, start) + '  ' + content.substring(end);
        setContent(newContent);
        onUpdate({ content: newContent });
        
        // Set cursor position after the inserted spaces
        setTimeout(() => {
          textarea.selectionStart = textarea.selectionEnd = start + 2;
        }, 0);
      }
      return;
    }

    // Handle Enter key to stay in code block
    if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey) {
      // Allow normal Enter behavior in code blocks
      return;
    }

    // Handle Ctrl+Enter to create new block after code block
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      onKeyDown?.(e, 'create-block', { type: 'paragraph' });
      return;
    }

    onKeyDown?.(e);
  };

  const handleFocus = () => {
    onFocus?.();
  };

  const handleBlur = () => {
    onBlur?.();
  };

  // Generate line numbers
  const lineNumbers = showLineNumbers 
    ? content.split('\n').map((_, index) => index + 1)
    : [];

  return (
    <div className={`code-block ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-2 bg-gray-100 border-b">
        <div className="flex items-center space-x-2">
          <Select value={language} onValueChange={handleLanguageChange}>
            <SelectTrigger className="w-32 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {LANGUAGES.map(lang => (
                <SelectItem key={lang.value} value={lang.value}>
                  {lang.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {filename && (
            <span className="text-sm text-gray-600 font-mono">{filename}</span>
          )}
        </div>

        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-8 w-8 p-0"
          >
            <Copy className="w-4 h-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
            className="h-8 w-8 p-0"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Settings panel */}
      {showSettings && (
        <div className="p-3 bg-gray-50 border-b space-y-2">
          <div className="flex items-center space-x-2">
            <Input
              value={filename}
              onChange={handleFilenameChange}
              placeholder="Filename (optional)"
              className="flex-1 h-8"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="line-numbers"
              checked={showLineNumbers}
              onChange={handleLineNumbersToggle}
              className="rounded"
            />
            <label htmlFor="line-numbers" className="text-sm">
              Show line numbers
            </label>
          </div>
        </div>
      )}

      {/* Code content */}
      <div className="relative">
        <div className="flex">
          {/* Line numbers */}
          {showLineNumbers && (
            <div className="flex-shrink-0 p-4 bg-gray-50 border-r text-right">
              {lineNumbers.map(num => (
                <div key={num} className="text-xs text-gray-500 font-mono leading-6">
                  {num}
                </div>
              ))}
            </div>
          )}

          {/* Code textarea */}
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={content}
              onChange={handleContentChange}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              placeholder="Enter your code here..."
              className={`
                w-full p-4 resize-none border-none outline-none bg-white
                font-mono text-sm leading-6
                ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
                ${isFocused ? 'ring-2 ring-blue-400' : ''}
              `}
              style={{
                minHeight: '120px',
                tabSize: 2,
              }}
              spellCheck={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
