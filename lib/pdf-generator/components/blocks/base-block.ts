/**
 * Base Block Types
 * 
 * Common types and interfaces for all block components
 */

import { Block, BlockType } from '../../blocks/types';

export interface BaseBlockProps<T extends Block = Block> {
  block: T;
  isSelected: boolean;
  isFocused: boolean;
  isEditing: boolean;
  isDragging?: boolean;
  isDropTarget?: boolean;
  onUpdate: (updates: Partial<T>) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onKeyDown?: (e: React.KeyboardEvent, action?: string, data?: any) => void;
  onDelete?: () => void;
  onDuplicate?: () => void;
  onConvert?: (newType: BlockType) => void;
  className?: string;
}

export interface BlockAction {
  type: 'create-block' | 'delete-block' | 'merge-with-previous' | 'merge-with-next' | 'convert-block' | 'move-up' | 'move-down';
  data?: any;
}

export interface BlockKeyboardEvent extends React.KeyboardEvent {
  action?: BlockAction;
}
