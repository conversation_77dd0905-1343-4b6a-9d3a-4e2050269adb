/**
 * Block Modal Editor
 * 
 * Modal-based editors for complex block types
 */

'use client';

import React, { useState, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Block, BlockType } from '../../blocks/types';
import { TableModalEditor } from './modal-editors/table-modal-editor';
import { ImageModalEditor } from './modal-editors/image-modal-editor';
import { CodeModalEditor } from './modal-editors/code-modal-editor';
import { MathModalEditor } from './modal-editors/math-modal-editor';
import { EmbedModalEditor } from './modal-editors/embed-modal-editor';

export interface BlockModalEditorProps {
  block: Block;
  isOpen: boolean;
  onSave: (updatedBlock: Partial<Block>) => void;
  onCancel: () => void;
}

export function BlockModalEditor({
  block,
  isOpen,
  onSave,
  onCancel,
}: BlockModalEditorProps) {
  const [activeTab, setActiveTab] = useState('content');
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Handle save
  const handleSave = useCallback(async (updatedData: Partial<Block>) => {
    setIsLoading(true);
    try {
      onSave(updatedData);
    } finally {
      setIsLoading(false);
    }
  }, [onSave]);

  // Handle cancel with confirmation if there are changes
  const handleCancel = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm('You have unsaved changes. Are you sure you want to cancel?');
      if (!confirmed) return;
    }
    onCancel();
  }, [hasChanges, onCancel]);

  // Track changes
  const handleChange = useCallback(() => {
    setHasChanges(true);
  }, []);

  // Get modal title based on block type
  const getModalTitle = (blockType: BlockType): string => {
    const titles = {
      table: 'Table Editor',
      image: 'Image Editor',
      code: 'Code Editor',
      math: 'Math Formula Editor',
      embed: 'Embed Editor',
    };
    return titles[blockType as keyof typeof titles] || 'Block Editor';
  };

  // Render the appropriate modal editor
  const renderModalEditor = () => {
    const editorProps = {
      block,
      onChange: handleChange,
      onSave: handleSave,
    };

    switch (block.type) {
      case 'table':
        return <TableModalEditor {...editorProps} />;
      case 'image':
        return <ImageModalEditor {...editorProps} />;
      case 'code':
        return <CodeModalEditor {...editorProps} />;
      case 'math':
        return <MathModalEditor {...editorProps} />;
      case 'embed':
        return <EmbedModalEditor {...editorProps} />;
      default:
        return (
          <div className="p-8 text-center text-gray-500">
            No modal editor available for {block.type} blocks.
          </div>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <span>{getModalTitle(block.type)}</span>
            {hasChanges && (
              <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                Unsaved changes
              </span>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-auto">
              <TabsContent value="content" className="h-full">
                {renderModalEditor()}
              </TabsContent>

              <TabsContent value="settings" className="p-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Block Settings</h3>
                  
                  {/* Common settings for all block types */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Block ID
                      </label>
                      <input
                        type="text"
                        value={block.id}
                        readOnly
                        className="w-full p-2 border rounded bg-gray-50 text-gray-600"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Block Type
                      </label>
                      <input
                        type="text"
                        value={block.type}
                        readOnly
                        className="w-full p-2 border rounded bg-gray-50 text-gray-600"
                      />
                    </div>
                  </div>

                  {/* Metadata editor */}
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Metadata (JSON)
                    </label>
                    <textarea
                      value={JSON.stringify(block.metadata || {}, null, 2)}
                      onChange={(e) => {
                        try {
                          const metadata = JSON.parse(e.target.value);
                          handleSave({ metadata });
                          handleChange();
                        } catch {
                          // Invalid JSON, ignore
                        }
                      }}
                      className="w-full p-2 border rounded font-mono text-sm h-32"
                      placeholder="{}"
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="preview" className="p-4">
                <div className="border rounded-lg p-4 bg-gray-50">
                  <h3 className="text-lg font-medium mb-4">Preview</h3>
                  <div className="bg-white border rounded p-4">
                    {/* Render a preview of the block */}
                    <div className="prose max-w-none">
                      {block.type === 'code' && (
                        <pre className="bg-gray-100 p-4 rounded">
                          <code>{block.content}</code>
                        </pre>
                      )}
                      {block.type === 'math' && (
                        <div className="text-center p-4 border rounded">
                          {(block as any).formula || block.content}
                        </div>
                      )}
                      {block.type === 'table' && (
                        <div className="overflow-x-auto">
                          <table className="min-w-full border-collapse border">
                            <thead>
                              <tr>
                                {((block as any).headers || []).map((header: string, i: number) => (
                                  <th key={i} className="border p-2 bg-gray-50">
                                    {header}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {((block as any).rows || []).map((row: string[], i: number) => (
                                <tr key={i}>
                                  {row.map((cell, j) => (
                                    <td key={j} className="border p-2">
                                      {cell}
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )}
                      {block.type === 'image' && (
                        <div className="text-center">
                          <img
                            src={(block as any).src || ''}
                            alt={(block as any).alt || ''}
                            className="max-w-full h-auto mx-auto"
                          />
                          {(block as any).caption && (
                            <p className="text-sm text-gray-600 mt-2">
                              {(block as any).caption}
                            </p>
                          )}
                        </div>
                      )}
                      {block.type === 'embed' && (
                        <div className="border-2 border-dashed border-gray-300 p-8 text-center">
                          <p className="text-gray-600">
                            Embed: {(block as any).url || 'No URL specified'}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">
              Last updated: {block.updatedAt?.toLocaleTimeString() || 'Never'}
            </span>
          </div>
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            
            <Button
              onClick={() => handleSave({})}
              disabled={isLoading || !hasChanges}
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
