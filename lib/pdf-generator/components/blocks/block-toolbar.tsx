/**
 * Block Toolbar Component
 * 
 * Floating toolbar for block operations and formatting
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Plus,
  GripVertical,
  MoreHorizontal,
  Copy,
  Trash2,
  ArrowUp,
  ArrowDown,
  Type,
  Code,
  Quote,
  List,
  Image,
  Table,
  Minus,
  Calculator,
  AlertCircle,
} from 'lucide-react';
import { Block, BlockType } from '../../blocks/types';
import { getBlockDefinition, getConversionTargets } from '../../blocks/definitions';

export interface BlockToolbarProps {
  block: Block;
  position: 'top' | 'bottom' | 'left' | 'right';
  onAddBlock: (type: BlockType, position: 'before' | 'after') => void;
  onDeleteBlock: () => void;
  onDuplicateBlock: () => void;
  onMoveBlock: (direction: 'up' | 'down') => void;
  onConvertBlock: (newType: BlockType) => void;
  onDragStart: () => void;
  onOpenModal?: () => void;
  className?: string;
}

const BLOCK_TYPE_ICONS: Record<BlockType, React.ComponentType<any>> = {
  paragraph: Type,
  heading: Type,
  code: Code,
  quote: Quote,
  list: List,
  table: Table,
  image: Image,
  divider: Minus,
  embed: Code,
  math: Calculator,
  callout: AlertCircle,
};

export function BlockToolbar({
  block,
  position,
  onAddBlock,
  onDeleteBlock,
  onDuplicateBlock,
  onMoveBlock,
  onConvertBlock,
  onDragStart,
  onOpenModal,
  className = '',
}: BlockToolbarProps) {
  const [showAddMenu, setShowAddMenu] = useState(false);
  const [showConvertMenu, setShowConvertMenu] = useState(false);

  const blockDefinition = getBlockDefinition(block.type);
  const conversionTargets = getConversionTargets(block.type);

  const handleAddBlock = (type: BlockType, position: 'before' | 'after') => {
    onAddBlock(type, position);
    setShowAddMenu(false);
  };

  const handleConvertBlock = (newType: BlockType) => {
    onConvertBlock(newType);
    setShowConvertMenu(false);
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'top-0 left-0 transform -translate-y-full';
      case 'bottom':
        return 'bottom-0 left-0 transform translate-y-full';
      case 'left':
        return 'left-0 top-1/2 transform -translate-x-full -translate-y-1/2';
      case 'right':
        return 'right-0 top-1/2 transform translate-x-full -translate-y-1/2';
      default:
        return 'top-0 left-0';
    }
  };

  return (
    <div className={`
      absolute z-10 flex items-center space-x-1 
      bg-white border border-gray-200 rounded-lg shadow-lg p-1
      ${getPositionClasses()}
      ${className}
    `}>
      {/* Drag handle */}
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 cursor-grab active:cursor-grabbing"
        onMouseDown={onDragStart}
      >
        <GripVertical className="w-4 h-4" />
      </Button>

      {/* Add block */}
      <DropdownMenu open={showAddMenu} onOpenChange={setShowAddMenu}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Plus className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-48">
          <DropdownMenuItem onClick={() => handleAddBlock('paragraph', 'before')}>
            <Type className="w-4 h-4 mr-2" />
            Add Paragraph Above
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddBlock('paragraph', 'after')}>
            <Type className="w-4 h-4 mr-2" />
            Add Paragraph Below
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => handleAddBlock('heading', 'after')}>
            <Type className="w-4 h-4 mr-2" />
            Heading
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddBlock('code', 'after')}>
            <Code className="w-4 h-4 mr-2" />
            Code Block
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddBlock('quote', 'after')}>
            <Quote className="w-4 h-4 mr-2" />
            Quote
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddBlock('list', 'after')}>
            <List className="w-4 h-4 mr-2" />
            List
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddBlock('table', 'after')}>
            <Table className="w-4 h-4 mr-2" />
            Table
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddBlock('image', 'after')}>
            <Image className="w-4 h-4 mr-2" />
            Image
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddBlock('divider', 'after')}>
            <Minus className="w-4 h-4 mr-2" />
            Divider
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddBlock('math', 'after')}>
            <Calculator className="w-4 h-4 mr-2" />
            Math
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAddBlock('callout', 'after')}>
            <AlertCircle className="w-4 h-4 mr-2" />
            Callout
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Move up */}
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0"
        onClick={() => onMoveBlock('up')}
      >
        <ArrowUp className="w-4 h-4" />
      </Button>

      {/* Move down */}
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0"
        onClick={() => onMoveBlock('down')}
      >
        <ArrowDown className="w-4 h-4" />
      </Button>

      {/* More options */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {/* Convert block type */}
          {conversionTargets.length > 0 && (
            <>
              <DropdownMenu open={showConvertMenu} onOpenChange={setShowConvertMenu}>
                <DropdownMenuTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Type className="w-4 h-4 mr-2" />
                    Convert to...
                  </DropdownMenuItem>
                </DropdownMenuTrigger>
                <DropdownMenuContent side="right" align="start" className="w-40">
                  {conversionTargets.map(targetType => {
                    const targetDefinition = getBlockDefinition(targetType);
                    const IconComponent = BLOCK_TYPE_ICONS[targetType];
                    return (
                      <DropdownMenuItem
                        key={targetType}
                        onClick={() => handleConvertBlock(targetType)}
                      >
                        <IconComponent className="w-4 h-4 mr-2" />
                        {targetDefinition.label}
                      </DropdownMenuItem>
                    );
                  })}
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenuSeparator />
            </>
          )}

          {/* Duplicate */}
          <DropdownMenuItem onClick={onDuplicateBlock}>
            <Copy className="w-4 h-4 mr-2" />
            Duplicate
          </DropdownMenuItem>

          {/* Delete */}
          <DropdownMenuItem onClick={onDeleteBlock} className="text-red-600">
            <Trash2 className="w-4 h-4 mr-2" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Block-specific toolbar actions */}
      {blockDefinition.toolbarActions.map(action => {
        const isActive = action.isActive?.(block) || false;
        const isDisabled = action.isDisabled?.(block) || false;

        return (
          <Button
            key={action.id}
            variant={isActive ? "default" : "ghost"}
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => action.action(block)}
            disabled={isDisabled}
            title={`${action.label}${action.shortcut ? ` (${action.shortcut})` : ''}`}
          >
            <span className="text-xs">{action.icon}</span>
          </Button>
        );
      })}
    </div>
  );
}
