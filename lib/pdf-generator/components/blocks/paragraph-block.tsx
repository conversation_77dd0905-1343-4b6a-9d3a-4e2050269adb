/**
 * Paragraph Block Component
 * 
 * Editable paragraph block with rich text formatting
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ParagraphBlock } from '../../blocks/types';
import { BaseBlockProps } from './base-block';

export interface ParagraphBlockProps extends BaseBlockProps<ParagraphBlock> {}

export function ParagraphBlockComponent({
  block,
  isSelected,
  isFocused,
  isEditing,
  onUpdate,
  onFocus,
  onBlur,
  onKeyDown,
  className = '',
}: ParagraphBlockProps) {
  const [content, setContent] = useState(block.content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [content]);

  // Focus textarea when block is focused
  useEffect(() => {
    if (isFocused && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isFocused]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);
    onUpdate({ content: newContent });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle Enter key to create new paragraph
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onKeyDown?.(e, 'create-block', { type: 'paragraph' });
      return;
    }

    // Handle Backspace at beginning to merge with previous block
    if (e.key === 'Backspace' && textareaRef.current?.selectionStart === 0 && content === '') {
      e.preventDefault();
      onKeyDown?.(e, 'merge-with-previous');
      return;
    }

    onKeyDown?.(e);
  };

  const handleFocus = () => {
    onFocus?.();
  };

  const handleBlur = () => {
    onBlur?.();
  };

  return (
    <div className={`paragraph-block ${className}`}>
      <textarea
        ref={textareaRef}
        value={content}
        onChange={handleContentChange}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder="Type your text here..."
        className={`
          w-full resize-none border-none outline-none bg-transparent
          text-base leading-relaxed
          ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
          ${isFocused ? 'ring-2 ring-blue-400' : ''}
        `}
        style={{
          minHeight: '1.5rem',
          fontFamily: 'inherit',
        }}
      />
    </div>
  );
}
