/**
 * Embed Modal Editor
 * 
 * Embed editor for external content
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Link, Eye, RefreshCw } from 'lucide-react';
import { Block } from '../../../blocks/types';

export interface EmbedModalEditorProps {
  block: Block;
  onChange: () => void;
  onSave: (updatedBlock: Partial<Block>) => void;
}

export function EmbedModalEditor({ block, onChange, onSave }: EmbedModalEditorProps) {
  const embedBlock = block as any;
  const [url, setUrl] = useState<string>(embedBlock.url || '');
  const [embedType, setEmbedType] = useState<string>(embedBlock.embedType || 'generic');
  const [title, setTitle] = useState<string>(embedBlock.title || '');
  const [description, setDescription] = useState<string>(embedBlock.description || '');

  const handleSave = useCallback(() => {
    const updatedBlock = {
      url,
      embedType,
      title,
      description,
      content: url, // Keep content in sync with url
    };
    onSave(updatedBlock);
  }, [url, embedType, title, description, onSave]);

  const detectEmbedType = useCallback((inputUrl: string) => {
    if (inputUrl.includes('youtube.com') || inputUrl.includes('youtu.be')) {
      return 'youtube';
    } else if (inputUrl.includes('vimeo.com')) {
      return 'vimeo';
    } else if (inputUrl.includes('codepen.io')) {
      return 'codepen';
    } else if (inputUrl.includes('figma.com')) {
      return 'figma';
    } else if (inputUrl.includes('miro.com')) {
      return 'miro';
    }
    return 'generic';
  }, []);

  const handleUrlChange = useCallback((newUrl: string) => {
    setUrl(newUrl);
    const detectedType = detectEmbedType(newUrl);
    setEmbedType(detectedType);
    onChange();
  }, [detectEmbedType, onChange]);

  const embedTypes = [
    { value: 'generic', label: 'Generic' },
    { value: 'youtube', label: 'YouTube' },
    { value: 'vimeo', label: 'Vimeo' },
    { value: 'codepen', label: 'CodePen' },
    { value: 'figma', label: 'Figma' },
    { value: 'miro', label: 'Miro' },
  ];

  const getEmbedPreview = () => {
    if (!url) return null;

    switch (embedType) {
      case 'youtube':
        const youtubeId = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)?.[1];
        return youtubeId ? (
          <iframe
            width="100%"
            height="315"
            src={`https://www.youtube.com/embed/${youtubeId}`}
            frameBorder="0"
            allowFullScreen
          />
        ) : null;

      case 'vimeo':
        const vimeoId = url.match(/vimeo\.com\/(\d+)/)?.[1];
        return vimeoId ? (
          <iframe
            width="100%"
            height="315"
            src={`https://player.vimeo.com/video/${vimeoId}`}
            frameBorder="0"
            allowFullScreen
          />
        ) : null;

      case 'codepen':
        return (
          <iframe
            width="100%"
            height="300"
            src={url.replace('/pen/', '/embed/')}
            frameBorder="0"
            allowFullScreen
          />
        );

      default:
        return (
          <div className="border-2 border-dashed border-gray-300 p-8 text-center">
            <Link className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p className="text-gray-600">Embed preview not available</p>
            <p className="text-sm text-gray-500">{url}</p>
          </div>
        );
    }
  };

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Embed Editor</h3>
        <Button onClick={handleSave} size="sm">
          Save Embed
        </Button>
      </div>

      {/* URL and type */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">URL</label>
          <Input
            value={url}
            onChange={(e) => handleUrlChange(e.target.value)}
            placeholder="Enter URL to embed"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Embed Type</label>
          <Select value={embedType} onValueChange={(value) => {
            setEmbedType(value);
            onChange();
          }}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {embedTypes.map(type => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Title and description */}
        <div>
          <label className="block text-sm font-medium mb-1">Title (optional)</label>
          <Input
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
              onChange();
            }}
            placeholder="Embed title"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Description (optional)</label>
          <Textarea
            value={description}
            onChange={(e) => {
              setDescription(e.target.value);
              onChange();
            }}
            placeholder="Embed description"
            rows={2}
          />
        </div>
      </div>

      {/* Preview */}
      {url && (
        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium flex items-center">
              <Eye className="w-4 h-4 mr-1" />
              Preview
            </h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Refresh preview
                const detectedType = detectEmbedType(url);
                setEmbedType(detectedType);
              }}
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
          
          {title && (
            <h5 className="font-medium mb-1">{title}</h5>
          )}
          
          {description && (
            <p className="text-sm text-gray-600 mb-3">{description}</p>
          )}
          
          <div className="bg-gray-50 rounded">
            {getEmbedPreview()}
          </div>
        </div>
      )}

      {/* Supported platforms */}
      <div className="text-xs text-gray-500">
        <p><strong>Supported platforms:</strong></p>
        <ul className="list-disc list-inside mt-1">
          <li>YouTube (youtube.com, youtu.be)</li>
          <li>Vimeo (vimeo.com)</li>
          <li>CodePen (codepen.io)</li>
          <li>Figma (figma.com)</li>
          <li>Miro (miro.com)</li>
          <li>Generic URLs (iframe or link)</li>
        </ul>
      </div>
    </div>
  );
}
