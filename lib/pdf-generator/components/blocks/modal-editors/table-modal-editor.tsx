/**
 * Table Modal Editor
 * 
 * Advanced table editor with dynamic rows/columns and data binding
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Minus, ArrowUp, ArrowDown, Database } from 'lucide-react';
import { Block } from '../../../blocks/types';
import { createDataTable } from '../../../blocks/dynamic-data-manager';

export interface TableModalEditorProps {
  block: Block;
  onChange: () => void;
  onSave: (updatedBlock: Partial<Block>) => void;
}

export function TableModalEditor({ block, onChange, onSave }: TableModalEditorProps) {
  const tableBlock = block as any;
  const [headers, setHeaders] = useState<string[]>(tableBlock.headers || ['Column 1', 'Column 2']);
  const [rows, setRows] = useState<string[][]>(tableBlock.rows || [['', '']]);
  const [caption, setCaption] = useState<string>(tableBlock.caption || '');
  const [enableDynamicData, setEnableDynamicData] = useState(false);

  // Dynamic data table
  const dynamicTable = enableDynamicData ? 
    createDataTable(
      block.id,
      rows.map(row => Object.fromEntries(headers.map((h, i) => [h, row[i] || '']))),
      headers.map((h, i) => ({ key: h, label: h }))
    ) : null;

  const handleSave = useCallback(() => {
    const updatedBlock = {
      headers,
      rows,
      caption,
      metadata: {
        ...block.metadata,
        dynamicData: enableDynamicData,
      },
    };
    onSave(updatedBlock);
  }, [headers, rows, caption, enableDynamicData, onSave, block.metadata]);

  const addColumn = useCallback(() => {
    const newHeaders = [...headers, `Column ${headers.length + 1}`];
    const newRows = rows.map(row => [...row, '']);
    setHeaders(newHeaders);
    setRows(newRows);
    onChange();
  }, [headers, rows, onChange]);

  const removeColumn = useCallback((index: number) => {
    if (headers.length <= 1) return;
    const newHeaders = headers.filter((_, i) => i !== index);
    const newRows = rows.map(row => row.filter((_, i) => i !== index));
    setHeaders(newHeaders);
    setRows(newRows);
    onChange();
  }, [headers, rows, onChange]);

  const addRow = useCallback(() => {
    const newRow = new Array(headers.length).fill('');
    setRows([...rows, newRow]);
    onChange();
  }, [headers.length, rows, onChange]);

  const removeRow = useCallback((index: number) => {
    if (rows.length <= 1) return;
    const newRows = rows.filter((_, i) => i !== index);
    setRows(newRows);
    onChange();
  }, [rows, onChange]);

  const updateHeader = useCallback((index: number, value: string) => {
    const newHeaders = [...headers];
    newHeaders[index] = value;
    setHeaders(newHeaders);
    onChange();
  }, [headers, onChange]);

  const updateCell = useCallback((rowIndex: number, colIndex: number, value: string) => {
    const newRows = [...rows];
    newRows[rowIndex][colIndex] = value;
    setRows(newRows);
    onChange();
  }, [rows, onChange]);

  const moveRow = useCallback((fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= rows.length) return;
    const newRows = [...rows];
    const [movedRow] = newRows.splice(fromIndex, 1);
    newRows.splice(toIndex, 0, movedRow);
    setRows(newRows);
    onChange();
  }, [rows, onChange]);

  return (
    <div className="p-4 space-y-4">
      {/* Table settings */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Table Editor</h3>
        <div className="flex items-center space-x-2">
          <Button
            variant={enableDynamicData ? 'default' : 'outline'}
            size="sm"
            onClick={() => setEnableDynamicData(!enableDynamicData)}
          >
            <Database className="w-4 h-4 mr-1" />
            Dynamic Data
          </Button>
          <Button onClick={handleSave} size="sm">
            Save Table
          </Button>
        </div>
      </div>

      {/* Caption */}
      <div>
        <label className="block text-sm font-medium mb-1">Table Caption</label>
        <Input
          value={caption}
          onChange={(e) => {
            setCaption(e.target.value);
            onChange();
          }}
          placeholder="Optional table caption"
        />
      </div>

      {/* Table editor */}
      <div className="border rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            {/* Headers */}
            <thead className="bg-gray-50">
              <tr>
                <th className="w-8 p-2 border-r"></th>
                {headers.map((header, index) => (
                  <th key={index} className="p-2 border-r min-w-32">
                    <div className="flex items-center space-x-1">
                      <Input
                        value={header}
                        onChange={(e) => updateHeader(index, e.target.value)}
                        className="text-center font-medium"
                        size="sm"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeColumn(index)}
                        disabled={headers.length <= 1}
                        className="p-1 h-6 w-6"
                      >
                        <Minus className="w-3 h-3" />
                      </Button>
                    </div>
                  </th>
                ))}
                <th className="w-8 p-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={addColumn}
                    className="p-1 h-6 w-6"
                  >
                    <Plus className="w-3 h-3" />
                  </Button>
                </th>
              </tr>
            </thead>

            {/* Rows */}
            <tbody>
              {rows.map((row, rowIndex) => (
                <tr key={rowIndex} className="hover:bg-gray-50">
                  <td className="p-2 border-r bg-gray-50">
                    <div className="flex flex-col space-y-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => moveRow(rowIndex, rowIndex - 1)}
                        disabled={rowIndex === 0}
                        className="p-1 h-5 w-5"
                      >
                        <ArrowUp className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => moveRow(rowIndex, rowIndex + 1)}
                        disabled={rowIndex === rows.length - 1}
                        className="p-1 h-5 w-5"
                      >
                        <ArrowDown className="w-3 h-3" />
                      </Button>
                    </div>
                  </td>
                  {row.map((cell, colIndex) => (
                    <td key={colIndex} className="p-1 border-r">
                      <Input
                        value={cell}
                        onChange={(e) => updateCell(rowIndex, colIndex, e.target.value)}
                        className="border-none shadow-none"
                        size="sm"
                      />
                    </td>
                  ))}
                  <td className="p-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeRow(rowIndex)}
                      disabled={rows.length <= 1}
                      className="p-1 h-6 w-6"
                    >
                      <Minus className="w-3 h-3" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Add row button */}
        <div className="p-2 border-t bg-gray-50">
          <Button
            variant="ghost"
            size="sm"
            onClick={addRow}
            className="w-full"
          >
            <Plus className="w-4 h-4 mr-1" />
            Add Row
          </Button>
        </div>
      </div>

      {/* Dynamic data controls */}
      {enableDynamicData && dynamicTable && (
        <div className="border rounded-lg p-4 bg-blue-50">
          <h4 className="font-medium mb-2">Dynamic Data Controls</h4>
          <div className="flex space-x-2">
            <Button
              size="sm"
              onClick={() => {
                dynamicTable.sortBy(headers[0], 'asc');
                onChange();
              }}
            >
              Sort Ascending
            </Button>
            <Button
              size="sm"
              onClick={() => {
                dynamicTable.sortBy(headers[0], 'desc');
                onChange();
              }}
            >
              Sort Descending
            </Button>
            <Button
              size="sm"
              onClick={() => {
                dynamicTable.addRow(new Array(headers.length).fill('New'));
                onChange();
              }}
            >
              Add Dynamic Row
            </Button>
          </div>
        </div>
      )}

      {/* Table statistics */}
      <div className="text-xs text-gray-500 flex justify-between">
        <span>{headers.length} columns × {rows.length} rows</span>
        <span>{headers.length * rows.length} total cells</span>
      </div>
    </div>
  );
}
