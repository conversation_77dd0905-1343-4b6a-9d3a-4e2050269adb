/**
 * Code Modal Editor
 * 
 * Advanced code editor with syntax highlighting and live execution
 */

'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Play, Copy, Download, Settings, Zap } from 'lucide-react';
import { Block } from '../../../blocks/types';
import { setupLiveExecution, dynamicDataManager } from '../../../blocks/dynamic-data-manager';

const LANGUAGES = [
  { value: 'javascript', label: 'JavaScript', executable: true },
  { value: 'typescript', label: 'TypeScript', executable: false },
  { value: 'python', label: 'Python', executable: true },
  { value: 'java', label: 'Java', executable: false },
  { value: 'cpp', label: 'C++', executable: false },
  { value: 'csharp', label: 'C#', executable: false },
  { value: 'php', label: 'PHP', executable: false },
  { value: 'ruby', label: 'Ruby', executable: false },
  { value: 'go', label: 'Go', executable: false },
  { value: 'rust', label: 'Rust', executable: false },
  { value: 'html', label: 'HTML', executable: false },
  { value: 'css', label: 'CSS', executable: false },
  { value: 'sql', label: 'SQL', executable: false },
  { value: 'bash', label: 'Bash', executable: false },
  { value: 'json', label: 'JSON', executable: false },
  { value: 'yaml', label: 'YAML', executable: false },
  { value: 'markdown', label: 'Markdown', executable: false },
  { value: 'plaintext', label: 'Plain Text', executable: false },
];

export interface CodeModalEditorProps {
  block: Block;
  onChange: () => void;
  onSave: (updatedBlock: Partial<Block>) => void;
}

export function CodeModalEditor({ block, onChange, onSave }: CodeModalEditorProps) {
  const codeBlock = block as any;
  const [code, setCode] = useState<string>(codeBlock.content || '');
  const [language, setLanguage] = useState<string>(codeBlock.language || 'javascript');
  const [filename, setFilename] = useState<string>(codeBlock.filename || '');
  const [showLineNumbers, setShowLineNumbers] = useState<boolean>(codeBlock.showLineNumbers ?? true);
  const [enableLiveExecution, setEnableLiveExecution] = useState<boolean>(false);
  const [executionResult, setExecutionResult] = useState<any>(null);
  const [executionError, setExecutionError] = useState<string | null>(null);
  const [isExecuting, setIsExecuting] = useState<boolean>(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const selectedLanguage = LANGUAGES.find(l => l.value === language);
  const canExecute = selectedLanguage?.executable && enableLiveExecution;

  const handleSave = useCallback(() => {
    const updatedBlock = {
      content: code,
      language,
      filename,
      showLineNumbers,
      metadata: {
        ...block.metadata,
        liveExecution: enableLiveExecution,
      },
    };
    onSave(updatedBlock);
  }, [code, language, filename, showLineNumbers, enableLiveExecution, onSave, block.metadata]);

  const handleCodeChange = useCallback((newCode: string) => {
    setCode(newCode);
    onChange();
  }, [onChange]);

  const handleLanguageChange = useCallback((newLanguage: string) => {
    setLanguage(newLanguage);
    onChange();
  }, [onChange]);

  const handleExecuteCode = useCallback(async () => {
    if (!canExecute) return;

    setIsExecuting(true);
    setExecutionError(null);
    setExecutionResult(null);

    try {
      // Set up live execution context
      setupLiveExecution({
        blockId: block.id,
        language,
        code,
        environment: 'browser',
        dependencies: [],
        timeout: 5000,
        allowNetworkAccess: false,
      });

      // Execute the code
      const result = await dynamicDataManager.executeLiveCode(block.id);
      
      if (result.error) {
        setExecutionError(result.error);
      } else {
        setExecutionResult(result.result);
      }
    } catch (error) {
      setExecutionError(error.message);
    } finally {
      setIsExecuting(false);
    }
  }, [canExecute, block.id, language, code]);

  const handleCopyCode = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(code);
      // Could show a toast notification here
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  }, [code]);

  const handleDownloadCode = useCallback(() => {
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `code.${getFileExtension(language)}`;
    link.click();
    URL.revokeObjectURL(url);
  }, [code, filename, language]);

  const getFileExtension = (lang: string): string => {
    const extensions: Record<string, string> = {
      javascript: 'js',
      typescript: 'ts',
      python: 'py',
      java: 'java',
      cpp: 'cpp',
      csharp: 'cs',
      php: 'php',
      ruby: 'rb',
      go: 'go',
      rust: 'rs',
      html: 'html',
      css: 'css',
      sql: 'sql',
      bash: 'sh',
      json: 'json',
      yaml: 'yml',
      markdown: 'md',
    };
    return extensions[lang] || 'txt';
  };

  const insertSnippet = useCallback((snippet: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const newCode = code.substring(0, start) + snippet + code.substring(end);
    
    setCode(newCode);
    onChange();

    // Set cursor position after the inserted snippet
    setTimeout(() => {
      textarea.selectionStart = textarea.selectionEnd = start + snippet.length;
      textarea.focus();
    }, 0);
  }, [code, onChange]);

  const getLanguageSnippets = (lang: string): { label: string; code: string }[] => {
    const snippets: Record<string, { label: string; code: string }[]> = {
      javascript: [
        { label: 'Function', code: 'function functionName() {\n  // code here\n}' },
        { label: 'Arrow Function', code: 'const functionName = () => {\n  // code here\n};' },
        { label: 'For Loop', code: 'for (let i = 0; i < array.length; i++) {\n  // code here\n}' },
        { label: 'If Statement', code: 'if (condition) {\n  // code here\n}' },
        { label: 'Try/Catch', code: 'try {\n  // code here\n} catch (error) {\n  console.error(error);\n}' },
      ],
      python: [
        { label: 'Function', code: 'def function_name():\n    # code here\n    pass' },
        { label: 'Class', code: 'class ClassName:\n    def __init__(self):\n        # code here\n        pass' },
        { label: 'For Loop', code: 'for item in items:\n    # code here\n    pass' },
        { label: 'If Statement', code: 'if condition:\n    # code here\n    pass' },
        { label: 'Try/Except', code: 'try:\n    # code here\n    pass\nexcept Exception as e:\n    print(e)' },
      ],
    };
    return snippets[lang] || [];
  };

  return (
    <div className="p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Code Editor</h3>
        <div className="flex items-center space-x-2">
          {canExecute && (
            <Button
              variant={enableLiveExecution ? 'default' : 'outline'}
              size="sm"
              onClick={() => setEnableLiveExecution(!enableLiveExecution)}
            >
              <Zap className="w-4 h-4 mr-1" />
              Live Execution
            </Button>
          )}
          <Button onClick={handleSave} size="sm">
            Save Code
          </Button>
        </div>
      </div>

      <Tabs defaultValue="editor" className="space-y-4">
        <TabsList>
          <TabsTrigger value="editor">Editor</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="snippets">Snippets</TabsTrigger>
          {canExecute && <TabsTrigger value="execution">Execution</TabsTrigger>}
        </TabsList>

        <TabsContent value="editor" className="space-y-4">
          {/* Language and filename */}
          <div className="flex space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Language</label>
              <Select value={language} onValueChange={handleLanguageChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {LANGUAGES.map(lang => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {lang.label} {lang.executable && '⚡'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Filename (optional)</label>
              <Input
                value={filename}
                onChange={(e) => {
                  setFilename(e.target.value);
                  onChange();
                }}
                placeholder={`example.${getFileExtension(language)}`}
              />
            </div>
          </div>

          {/* Code editor */}
          <div className="border rounded-lg overflow-hidden">
            <div className="flex items-center justify-between p-2 bg-gray-50 border-b">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{selectedLanguage?.label}</span>
                {filename && (
                  <span className="text-sm text-gray-600">• {filename}</span>
                )}
              </div>
              <div className="flex items-center space-x-1">
                <Button variant="ghost" size="sm" onClick={handleCopyCode}>
                  <Copy className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={handleDownloadCode}>
                  <Download className="w-4 h-4" />
                </Button>
                {canExecute && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleExecuteCode}
                    disabled={isExecuting}
                  >
                    <Play className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
            
            <div className="relative">
              {showLineNumbers && (
                <div className="absolute left-0 top-0 bottom-0 w-12 bg-gray-50 border-r flex flex-col text-xs text-gray-500 font-mono">
                  {code.split('\n').map((_, index) => (
                    <div key={index} className="px-2 py-0.5 text-right leading-6">
                      {index + 1}
                    </div>
                  ))}
                </div>
              )}
              
              <textarea
                ref={textareaRef}
                value={code}
                onChange={(e) => handleCodeChange(e.target.value)}
                className={`
                  w-full h-96 p-4 font-mono text-sm resize-none border-none outline-none
                  ${showLineNumbers ? 'pl-16' : 'pl-4'}
                `}
                placeholder="Enter your code here..."
                spellCheck={false}
                style={{ tabSize: 2 }}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="line-numbers"
                checked={showLineNumbers}
                onChange={(e) => {
                  setShowLineNumbers(e.target.checked);
                  onChange();
                }}
              />
              <label htmlFor="line-numbers" className="text-sm">
                Show line numbers
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="live-execution"
                checked={enableLiveExecution}
                onChange={(e) => setEnableLiveExecution(e.target.checked)}
                disabled={!selectedLanguage?.executable}
              />
              <label htmlFor="live-execution" className="text-sm">
                Enable live execution {!selectedLanguage?.executable && '(not supported for this language)'}
              </label>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="snippets" className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            {getLanguageSnippets(language).map((snippet, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => insertSnippet(snippet.code)}
                className="text-left justify-start"
              >
                {snippet.label}
              </Button>
            ))}
          </div>
        </TabsContent>

        {canExecute && (
          <TabsContent value="execution" className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Code Execution</h4>
              <Button
                onClick={handleExecuteCode}
                disabled={isExecuting}
                size="sm"
              >
                <Play className="w-4 h-4 mr-1" />
                {isExecuting ? 'Executing...' : 'Run Code'}
              </Button>
            </div>

            {/* Execution result */}
            {(executionResult !== null || executionError) && (
              <div className="border rounded-lg p-4">
                <h5 className="font-medium mb-2">Output</h5>
                {executionError ? (
                  <div className="bg-red-50 border border-red-200 rounded p-3">
                    <div className="text-red-800 font-mono text-sm">
                      Error: {executionError}
                    </div>
                  </div>
                ) : (
                  <div className="bg-green-50 border border-green-200 rounded p-3">
                    <pre className="text-green-800 font-mono text-sm whitespace-pre-wrap">
                      {typeof executionResult === 'object' 
                        ? JSON.stringify(executionResult, null, 2)
                        : String(executionResult)
                      }
                    </pre>
                  </div>
                )}
              </div>
            )}
          </TabsContent>
        )}
      </Tabs>

      {/* Code statistics */}
      <div className="text-xs text-gray-500 flex justify-between">
        <span>{code.split('\n').length} lines</span>
        <span>{code.length} characters</span>
      </div>
    </div>
  );
}
