/**
 * Math Modal Editor
 * 
 * Math formula editor with live preview
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calculator, Eye } from 'lucide-react';
import { Block } from '../../../blocks/types';
import { setupMathRendering } from '../../../blocks/dynamic-data-manager';

export interface MathModalEditorProps {
  block: Block;
  onChange: () => void;
  onSave: (updatedBlock: Partial<Block>) => void;
}

export function MathModalEditor({ block, onChange, onSave }: MathModalEditorProps) {
  const mathBlock = block as any;
  const [formula, setFormula] = useState<string>(mathBlock.formula || mathBlock.content || '');
  const [displayMode, setDisplayMode] = useState<'inline' | 'block'>(mathBlock.displayMode || 'block');

  const handleSave = useCallback(() => {
    const updatedBlock = {
      formula,
      displayMode,
      content: formula, // Keep content in sync with formula
    };
    onSave(updatedBlock);
  }, [formula, displayMode, onSave]);

  const insertSymbol = useCallback((symbol: string) => {
    setFormula(prev => prev + symbol);
    onChange();
  }, [onChange]);

  const mathSymbols = [
    { label: 'α', value: '\\alpha' },
    { label: 'β', value: '\\beta' },
    { label: 'γ', value: '\\gamma' },
    { label: 'δ', value: '\\delta' },
    { label: 'ε', value: '\\epsilon' },
    { label: 'π', value: '\\pi' },
    { label: 'σ', value: '\\sigma' },
    { label: 'τ', value: '\\tau' },
    { label: 'φ', value: '\\phi' },
    { label: 'ψ', value: '\\psi' },
    { label: 'ω', value: '\\omega' },
    { label: '∞', value: '\\infty' },
    { label: '∑', value: '\\sum' },
    { label: '∫', value: '\\int' },
    { label: '√', value: '\\sqrt{}' },
    { label: 'x²', value: '^2' },
    { label: 'x₁', value: '_1' },
    { label: '≤', value: '\\leq' },
    { label: '≥', value: '\\geq' },
    { label: '≠', value: '\\neq' },
    { label: '±', value: '\\pm' },
    { label: '×', value: '\\times' },
    { label: '÷', value: '\\div' },
    { label: '∂', value: '\\partial' },
  ];

  const mathTemplates = [
    { label: 'Fraction', value: '\\frac{numerator}{denominator}' },
    { label: 'Square Root', value: '\\sqrt{expression}' },
    { label: 'Power', value: 'base^{exponent}' },
    { label: 'Subscript', value: 'variable_{subscript}' },
    { label: 'Sum', value: '\\sum_{i=1}^{n} expression' },
    { label: 'Integral', value: '\\int_{a}^{b} f(x) dx' },
    { label: 'Limit', value: '\\lim_{x \\to \\infty} f(x)' },
    { label: 'Matrix', value: '\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}' },
    { label: 'Equation', value: 'E = mc^2' },
    { label: 'Quadratic', value: 'ax^2 + bx + c = 0' },
  ];

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Math Formula Editor</h3>
        <Button onClick={handleSave} size="sm">
          Save Formula
        </Button>
      </div>

      <Tabs defaultValue="editor" className="space-y-4">
        <TabsList>
          <TabsTrigger value="editor">Editor</TabsTrigger>
          <TabsTrigger value="symbols">Symbols</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="editor" className="space-y-4">
          {/* Display mode */}
          <div>
            <label className="block text-sm font-medium mb-1">Display Mode</label>
            <div className="flex space-x-2">
              <Button
                variant={displayMode === 'inline' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setDisplayMode('inline');
                  onChange();
                }}
              >
                Inline
              </Button>
              <Button
                variant={displayMode === 'block' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setDisplayMode('block');
                  onChange();
                }}
              >
                Block
              </Button>
            </div>
          </div>

          {/* Formula editor */}
          <div>
            <label className="block text-sm font-medium mb-1">LaTeX Formula</label>
            <Textarea
              value={formula}
              onChange={(e) => {
                setFormula(e.target.value);
                onChange();
              }}
              placeholder="Enter LaTeX formula (e.g., E = mc^2)"
              rows={6}
              className="font-mono"
            />
          </div>

          {/* Preview */}
          <div className="border rounded-lg p-4 bg-gray-50">
            <h4 className="font-medium mb-2 flex items-center">
              <Eye className="w-4 h-4 mr-1" />
              Preview
            </h4>
            <div className={`text-center p-4 bg-white border rounded ${displayMode === 'block' ? 'text-lg' : 'text-base'}`}>
              {formula ? (
                displayMode === 'block' ? (
                  <div className="math-block">$$${formula}$$</div>
                ) : (
                  <span className="math-inline">${formula}$</span>
                )
              ) : (
                <span className="text-gray-400">Formula preview will appear here</span>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="symbols" className="space-y-4">
          <div className="grid grid-cols-6 gap-2">
            {mathSymbols.map((symbol, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => insertSymbol(symbol.value)}
                className="text-center"
                title={symbol.value}
              >
                {symbol.label}
              </Button>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            {mathTemplates.map((template, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => insertSymbol(template.value)}
                className="text-left justify-start text-xs"
              >
                {template.label}
              </Button>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Help */}
      <div className="text-xs text-gray-500 space-y-1">
        <p><strong>Tips:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>Use curly braces {} for grouping: x^{2+3}</li>
          <li>Use backslash for commands: \alpha, \beta, \sum</li>
          <li>Use _ for subscripts and ^ for superscripts</li>
          <li>Use \frac{num}{den} for fractions</li>
        </ul>
      </div>
    </div>
  );
}
