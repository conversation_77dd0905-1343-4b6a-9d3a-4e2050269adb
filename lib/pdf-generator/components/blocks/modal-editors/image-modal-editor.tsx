/**
 * Image Modal Editor
 * 
 * Image editor with upload, URL, and caption functionality
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Upload, Link, Image as ImageIcon } from 'lucide-react';
import { Block } from '../../../blocks/types';

export interface ImageModalEditorProps {
  block: Block;
  onChange: () => void;
  onSave: (updatedBlock: Partial<Block>) => void;
}

export function ImageModalEditor({ block, onChange, onSave }: ImageModalEditorProps) {
  const imageBlock = block as any;
  const [src, setSrc] = useState<string>(imageBlock.src || '');
  const [alt, setAlt] = useState<string>(imageBlock.alt || '');
  const [caption, setCaption] = useState<string>(imageBlock.caption || '');
  const [alignment, setAlignment] = useState<string>(imageBlock.alignment || 'center');

  const handleSave = useCallback(() => {
    const updatedBlock = {
      src,
      alt,
      caption,
      alignment,
      content: src, // Keep content in sync with src
    };
    onSave(updatedBlock);
  }, [src, alt, caption, alignment, onSave]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // In a real implementation, you would upload the file to a server
      // For now, create a local URL
      const url = URL.createObjectURL(file);
      setSrc(url);
      if (!alt) {
        setAlt(file.name);
      }
      onChange();
    }
  }, [alt, onChange]);

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Image Editor</h3>
        <Button onClick={handleSave} size="sm">
          Save Image
        </Button>
      </div>

      {/* Image source */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Image Source</label>
          <div className="flex space-x-2">
            <Input
              value={src}
              onChange={(e) => {
                setSrc(e.target.value);
                onChange();
              }}
              placeholder="Enter image URL or upload file"
              className="flex-1"
            />
            <Button variant="outline" size="sm" asChild>
              <label>
                <Upload className="w-4 h-4 mr-1" />
                Upload
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </Button>
          </div>
        </div>

        {/* Alt text */}
        <div>
          <label className="block text-sm font-medium mb-1">Alt Text</label>
          <Input
            value={alt}
            onChange={(e) => {
              setAlt(e.target.value);
              onChange();
            }}
            placeholder="Describe the image for accessibility"
          />
        </div>

        {/* Caption */}
        <div>
          <label className="block text-sm font-medium mb-1">Caption (optional)</label>
          <Textarea
            value={caption}
            onChange={(e) => {
              setCaption(e.target.value);
              onChange();
            }}
            placeholder="Image caption"
            rows={2}
          />
        </div>

        {/* Alignment */}
        <div>
          <label className="block text-sm font-medium mb-1">Alignment</label>
          <div className="flex space-x-2">
            {['left', 'center', 'right'].map((align) => (
              <Button
                key={align}
                variant={alignment === align ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setAlignment(align);
                  onChange();
                }}
              >
                {align.charAt(0).toUpperCase() + align.slice(1)}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Preview */}
      {src && (
        <div className="border rounded-lg p-4">
          <h4 className="font-medium mb-2">Preview</h4>
          <div className={`text-${alignment}`}>
            <img
              src={src}
              alt={alt}
              className="max-w-full h-auto max-h-64 mx-auto"
              onError={() => console.error('Failed to load image')}
            />
            {caption && (
              <p className="text-sm text-gray-600 mt-2">{caption}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
