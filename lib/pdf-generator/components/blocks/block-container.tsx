/**
 * Block Container Component
 * 
 * Wrapper component for individual blocks with drag/drop, selection,
 * and toolbar functionality
 */

'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Block, BlockType } from '../../blocks/types';
import { BlockToolbar } from './block-toolbar';
import { ParagraphBlockComponent } from './paragraph-block';
import { HeadingBlockComponent } from './heading-block';
import { CodeBlockComponent } from './code-block';

export interface BlockContainerProps {
  block: Block;
  index: number;
  isSelected: boolean;
  isFocused: boolean;
  isEditing: boolean;
  isDragging: boolean;
  isDropTarget: boolean;
  onUpdate: (updates: Partial<Block>) => void;
  onFocus: () => void;
  onBlur: () => void;
  onSelect: () => void;
  onKeyDown: (e: React.KeyboardEvent, action?: string, data?: any) => void;
  onAddBlock: (type: BlockType, position: 'before' | 'after') => void;
  onDeleteBlock: () => void;
  onDuplicateBlock: () => void;
  onMoveBlock: (direction: 'up' | 'down') => void;
  onConvertBlock: (newType: BlockType) => void;
  onDragStart: () => void;
  onDragEnd: () => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
  className?: string;
}

export function BlockContainer({
  block,
  index,
  isSelected,
  isFocused,
  isEditing,
  isDragging,
  isDropTarget,
  onUpdate,
  onFocus,
  onBlur,
  onSelect,
  onKeyDown,
  onAddBlock,
  onDeleteBlock,
  onDuplicateBlock,
  onMoveBlock,
  onConvertBlock,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDrop,
  className = '',
}: BlockContainerProps) {
  const [showToolbar, setShowToolbar] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle mouse enter/leave for toolbar visibility
  const handleMouseEnter = useCallback(() => {
    if (!isDragging) {
      setShowToolbar(true);
    }
  }, [isDragging]);

  const handleMouseLeave = useCallback(() => {
    if (!isFocused && !isSelected) {
      setShowToolbar(false);
    }
  }, [isFocused, isSelected]);

  // Handle click to select block
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect();
  }, [onSelect]);

  // Handle drag events
  const handleDragStart = useCallback((e: React.DragEvent) => {
    e.dataTransfer.setData('text/plain', block.id);
    e.dataTransfer.effectAllowed = 'move';
    onDragStart();
  }, [block.id, onDragStart]);

  const handleDragEnd = useCallback(() => {
    onDragEnd();
  }, [onDragEnd]);

  // Render the appropriate block component
  const renderBlockComponent = () => {
    const blockProps = {
      block: block as any,
      isSelected,
      isFocused,
      isEditing,
      onUpdate,
      onFocus,
      onBlur,
      onKeyDown,
    };

    switch (block.type) {
      case 'paragraph':
        return <ParagraphBlockComponent {...blockProps} />;

      case 'heading':
        return <HeadingBlockComponent {...blockProps} />;

      case 'code':
        return <CodeBlockComponent {...blockProps} />;

      case 'quote':
        return (
          <div className="border-l-4 border-gray-300 pl-4 italic">
            <ParagraphBlockComponent {...blockProps} />
          </div>
        );

      case 'list':
        return (
          <div className="space-y-1">
            {/* Simplified list rendering - would need full implementation */}
            <ParagraphBlockComponent {...blockProps} />
          </div>
        );

      case 'table':
        return (
          <div className="border border-gray-200 rounded">
            {/* Simplified table rendering - would need full implementation */}
            <div className="p-4 text-center text-gray-500">
              Table Block (Implementation needed)
            </div>
          </div>
        );

      case 'image':
        return (
          <div className="border border-gray-200 rounded p-4 text-center">
            {/* Simplified image rendering - would need full implementation */}
            <div className="text-gray-500">Image Block (Implementation needed)</div>
          </div>
        );

      case 'divider':
        return (
          <div className="my-4">
            <hr className="border-gray-300" />
          </div>
        );

      case 'embed':
        return (
          <div className="border border-gray-200 rounded p-4 text-center">
            {/* Simplified embed rendering - would need full implementation */}
            <div className="text-gray-500">Embed Block (Implementation needed)</div>
          </div>
        );

      case 'math':
        return (
          <div className="border border-gray-200 rounded p-4 text-center font-mono">
            {/* Simplified math rendering - would need full implementation */}
            <div className="text-gray-500">Math: {block.content}</div>
          </div>
        );

      case 'callout':
        return (
          <div className="border border-blue-200 bg-blue-50 rounded p-4">
            {/* Simplified callout rendering - would need full implementation */}
            <ParagraphBlockComponent {...blockProps} />
          </div>
        );

      default:
        return <ParagraphBlockComponent {...blockProps} />;
    }
  };

  return (
    <div
      ref={containerRef}
      className={`
        relative group
        ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
        ${isFocused ? 'ring-2 ring-blue-400' : ''}
        ${isDragging ? 'opacity-50' : ''}
        ${isDropTarget ? 'ring-2 ring-green-400' : ''}
        ${className}
      `}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={onDragOver}
      onDrop={onDrop}
      draggable
    >
      {/* Drop indicator */}
      {isDropTarget && (
        <div className="absolute inset-0 bg-green-100 border-2 border-green-400 border-dashed rounded pointer-events-none" />
      )}

      {/* Block content */}
      <div className="relative z-10 p-2">
        {renderBlockComponent()}
      </div>

      {/* Block toolbar */}
      {(showToolbar || isSelected || isFocused) && !isDragging && (
        <BlockToolbar
          block={block}
          position="left"
          onAddBlock={onAddBlock}
          onDeleteBlock={onDeleteBlock}
          onDuplicateBlock={onDuplicateBlock}
          onMoveBlock={onMoveBlock}
          onConvertBlock={onConvertBlock}
          onDragStart={onDragStart}
          className="opacity-0 group-hover:opacity-100 transition-opacity"
        />
      )}

      {/* Block type indicator */}
      {isSelected && (
        <div className="absolute top-0 right-0 transform translate-x-2 -translate-y-2">
          <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded">
            {block.type}
          </div>
        </div>
      )}

      {/* Validation errors indicator */}
      {/* This would be connected to validation system */}
      
      {/* Block index for debugging */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-0 left-0 transform -translate-x-2 translate-y-2">
          <div className="bg-gray-500 text-white text-xs px-1 py-0.5 rounded">
            {index}
          </div>
        </div>
      )}
    </div>
  );
}
