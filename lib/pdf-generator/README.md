# PDF Generator Library

A comprehensive, production-ready PDF document generation library with markdown support, real-time editing capabilities, and seamless integration with existing tools.

## 🚀 Features

### Core Functionality
- **Markdown to PDF**: Convert markdown content to professional PDF documents
- **Template System**: Customizable templates for different document types
- **Real-time Editing**: Live preview and editing capabilities
- **Block-based Editor**: Modern block-based editing interface with four-state lifecycle
- **Validation**: Content validation and sanitization with comprehensive error handling
- **Multiple Output Formats**: PDF, HTML, and more
- **Research Integration**: Generate documents from research projects
- **Batch Processing**: Process multiple documents simultaneously

### Advanced Features
- **Remark JSX Integration**: Seamless markdown ↔ JSX conversion
- **Dynamic Data Support**: Auto-updating lists, data-driven tables, live code execution
- **Modal Editors**: Advanced editing interfaces for complex content types
- **State Management**: Four-state block lifecycle (Preview, Edit, Render, Print)
- **Production-Ready**: Comprehensive error handling, validation, and security

## 📦 Installation

```bash
# Using pnpm (recommended)
pnpm add @your-org/pdf-generator

# Using npm
npm install @your-org/pdf-generator

# Using yarn
yarn add @your-org/pdf-generator
```

### Dependencies

The library requires these peer dependencies:

```bash
pnpm add react react-dom next ai zod
```

## 🚀 Quick Start

### Basic Usage

```typescript
import { generatePDFFromMarkdown } from '@your-org/pdf-generator';

const markdown = `
# My Document

This is a **sample** document with *formatting*.

## Features

- Easy to use
- Professional output
- Customizable templates
`;

const pdf = await generatePDFFromMarkdown(markdown, {
  template: 'professional',
  title: 'My Document',
  author: 'Your Name',
  subject: 'Sample Document'
});

// Save or display the PDF
```

### Enhanced Block Editor

```tsx
import { EnhancedBlockEditor } from '@your-org/pdf-generator/components';

function MyEditor() {
  return (
    <EnhancedBlockEditor
      enableRemarkJSX={true}
      enableDynamicData={true}
      enableLiveExecution={true}
      defaultBlockState="preview"
      onSave={(blocks, markdown, jsx) => {
        console.log('Saved:', { blocks, markdown, jsx });
      }}
    />
  );
}
```

## 📚 API Reference

### Core Functions

#### `generatePDFFromMarkdown(markdown, options)`

Generates a PDF document from markdown content with comprehensive validation.

**Parameters:**
- `markdown` (string): The markdown content to convert
- `options` (GenerationOptions): Generation options
  - `template` (string): Template to use (default: 'default')
  - `title` (string): Document title
  - `author` (string): Document author
  - `subject` (string): Document subject
  - `keywords` (string[]): Document keywords
  - `enableValidation` (boolean): Enable content validation
  - `sanitizeContent` (boolean): Sanitize HTML content

**Returns:** Promise<PDFDocument>

#### `createDocumentFromTemplate(template, data, metadata?)`

Creates a document using a specific template and data with production-ready error handling.

**Parameters:**
- `template` (string): Template identifier
- `data` (object): Data to populate the template
- `metadata` (DocumentMetadata): Optional document metadata

**Returns:** Promise<PDFDocument>

#### `generateFromResearch(projectId, options)`

Generate documents from research projects with Deep Research integration.

**Parameters:**
- `projectId` (string): Research project identifier
- `options` (ResearchIntegrationOptions): Research integration options
  - `includeFindings` (boolean): Include research findings
  - `includeAnalysis` (boolean): Include analysis section
  - `includeSources` (boolean): Include source references
  - `autoGenerateReferences` (boolean): Auto-generate reference list
  - `template` ('research-report' | 'academic-paper' | 'executive-summary'): Template type

**Returns:** Promise<PDFDocument>

### Enhanced Components

#### `<EnhancedBlockEditor>`

Production-ready block-based editor with four-state lifecycle and modal editors.

```tsx
import { EnhancedBlockEditor } from '@your-org/pdf-generator/components';

function MyEditor() {
  return (
    <EnhancedBlockEditor
      // Core features
      enableRemarkJSX={true}
      enableDynamicData={true}
      enableLiveExecution={true}
      
      // State management
      defaultBlockState="preview"
      allowedTransitions={['preview', 'edit', 'render']}
      
      // Event handlers
      onSave={(blocks, markdown, jsx) => handleSave(blocks, markdown, jsx)}
      onStateChange={(blockId, state) => handleStateChange(blockId, state)}
      onValidationError={(errors) => handleValidationErrors(errors)}
      
      // Advanced options
      autoSave={true}
      validateOnTransition={true}
      enableModalEditors={true}
    />
  );
}
```

#### `<BlockModalEditor>`

Advanced modal editors for complex content types.

```tsx
import { BlockModalEditor } from '@your-org/pdf-generator/components';

function MyModalEditor() {
  return (
    <BlockModalEditor
      block={block}
      isOpen={showModal}
      onSave={(updatedBlock) => handleSave(updatedBlock)}
      onCancel={() => setShowModal(false)}
    />
  );
}
```

### Block State Management

#### Four-State Lifecycle

```typescript
import { 
  blockStateManager, 
  transitionBlockState,
  BlockState 
} from '@your-org/pdf-generator/blocks';

// Initialize block with state
blockStateManager.initializeBlock('block-id', 'preview', {
  allowedTransitions: ['preview', 'edit', 'render', 'print'],
  autoSave: true,
  validateOnTransition: true
});

// Transition between states
await transitionBlockState('block-id', 'edit');

// Batch operations
const results = await blockStateManager.batchTransition(
  ['block-1', 'block-2', 'block-3'], 
  'render'
);
```

### Dynamic Data Management

#### Auto-updating Lists

```typescript
import { createDynamicList } from '@your-org/pdf-generator/blocks';

const dynamicList = createDynamicList('list-block-id', [
  { id: '1', content: 'Item 1' },
  { id: '2', content: 'Item 2' }
]);

// Add items dynamically
dynamicList.addItem({ id: '3', content: 'New Item' });

// Remove items
dynamicList.removeItem('1');

// Reorder items
dynamicList.reorderItems(['2', '3', '1']);
```

#### Data-driven Tables

```typescript
import { createDataTable } from '@your-org/pdf-generator/blocks';

const dataTable = createDataTable('table-block-id', data, [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'role', label: 'Role', filterable: true }
]);

// Sort data
dataTable.sortBy('name', 'asc');

// Filter data
dataTable.filterBy('role', 'admin');

// Add new rows
dataTable.addRow({ name: 'John Doe', email: '<EMAIL>', role: 'user' });
```

#### Live Code Execution

```typescript
import { setupLiveExecution } from '@your-org/pdf-generator/blocks';

setupLiveExecution({
  blockId: 'code-block-id',
  language: 'javascript',
  code: 'console.log("Hello, World!");',
  environment: 'browser',
  timeout: 5000,
  allowNetworkAccess: false
});

// Execute code
const result = await dynamicDataManager.executeLiveCode('code-block-id');
```

### Remark JSX Integration

#### Markdown ↔ JSX Conversion

```typescript
import { remarkJSXProcessor } from '@your-org/pdf-generator/blocks';

// Convert markdown to blocks
const blocks = await remarkJSXProcessor.markdownToBlocks(markdown);

// Convert blocks to markdown
const markdown = remarkJSXProcessor.blocksToMarkdown(blocks);

// Convert blocks to JSX
const jsx = remarkJSXProcessor.blocksToJSX(blocks, {
  renderMode: 'preview',
  theme: 'default'
});
```

## 🎨 Templates

### Built-in Templates

The library includes several production-ready templates:

- `default`: Basic document template
- `professional`: Professional business document
- `academic`: Academic paper format
- `research-report`: Research report with findings and analysis
- `executive-summary`: Executive summary format
- `technical-report`: Technical documentation format
- `letter`: Business letter format

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Unified](https://unifiedjs.com/) for markdown processing
- [React PDF](https://react-pdf.org/) for PDF generation
- [Remark](https://remark.js.org/) for markdown parsing
- [Puppeteer](https://pptr.dev/) for HTML to PDF conversion

## 📞 Support

- **Documentation**: [Full API Documentation](https://docs.your-org.com/pdf-generator)
- **Issues**: [GitHub Issues](https://github.com/your-org/pdf-generator/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/pdf-generator/discussions)
- **Email**: <EMAIL>
