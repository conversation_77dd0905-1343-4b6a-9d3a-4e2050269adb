/**
 * PDF Generator API Usage Examples
 * 
 * Comprehensive examples demonstrating how to use the enhanced PDF Generator APIs
 * including block-based editing, dynamic data, batch processing, and AI integration.
 */

import { 
  pdfGeneratorTools,
  generatePDFFromMarkdown,
  validateMarkdown,
  getAvailableTemplates,
  generateResearchReport,
  parseMarkdownStructure,
  routeHandlers
} from '../index';

// Example 1: Basic PDF Generation
export async function basicPDFGeneration() {
  console.log('=== Basic PDF Generation ===');
  
  const markdown = `
# Sample Document

This is a **sample document** with various formatting:

## Features

- Easy to use
- Professional output
- Customizable templates

## Code Example

\`\`\`javascript
console.log('Hello, World!');
\`\`\`

> This is a quote block with important information.

| Feature | Status |
|---------|--------|
| PDF Generation | ✅ |
| Block Editor | ✅ |
| Dynamic Data | ✅ |
`;

  try {
    // Using the AI tool
    const result = await pdfGeneratorTools.generatePDF.execute({
      title: 'Sample Document',
      content: markdown,
      template: 'professional',
      author: '<PERSON>',
      metadata: {
        subject: 'API Example',
        keywords: ['pdf', 'generation', 'example'],
        description: 'A sample document demonstrating PDF generation'
      },
      options: {
        enableValidation: true,
        sanitizeContent: true,
        quality: 'high',
        compression: true
      }
    });

    console.log('PDF Generation Result:', result);
    return result;
  } catch (error) {
    console.error('PDF Generation failed:', error);
    throw error;
  }
}

// Example 2: Block-Based PDF Generation
export async function blockBasedGeneration() {
  console.log('=== Block-Based PDF Generation ===');
  
  const blocks = [
    {
      id: 'title-block',
      type: 'heading' as const,
      content: 'Advanced Document with Blocks',
      metadata: { level: 1 },
      state: 'render' as const
    },
    {
      id: 'intro-block',
      type: 'paragraph' as const,
      content: 'This document demonstrates the power of block-based editing with state management and dynamic content.',
      state: 'render' as const
    },
    {
      id: 'features-block',
      type: 'heading' as const,
      content: 'Key Features',
      metadata: { level: 2 },
      state: 'render' as const
    },
    {
      id: 'list-block',
      type: 'list' as const,
      content: 'Four-state lifecycle (Preview, Edit, Render, Print)\nDynamic data support\nLive code execution\nModal editors for complex content',
      state: 'render' as const
    },
    {
      id: 'code-block',
      type: 'code' as const,
      content: `// Example of live code execution
function generateReport(data) {
  return data.map(item => ({
    ...item,
    processed: true,
    timestamp: new Date().toISOString()
  }));
}

const sampleData = [
  { id: 1, name: 'Item 1' },
  { id: 2, name: 'Item 2' }
];

console.log(generateReport(sampleData));`,
      metadata: { language: 'javascript' },
      state: 'render' as const
    },
    {
      id: 'callout-block',
      type: 'callout' as const,
      content: 'This is an important callout that highlights key information for readers.',
      metadata: { calloutType: 'info' },
      state: 'render' as const
    },
    {
      id: 'math-block',
      type: 'math' as const,
      content: 'E = mc^2',
      state: 'render' as const
    },
    {
      id: 'divider-block',
      type: 'divider' as const,
      content: '',
      state: 'render' as const
    }
  ];

  try {
    const result = await pdfGeneratorTools.generateFromBlocks.execute({
      title: 'Advanced Block Document',
      blocks,
      template: 'academic',
      metadata: {
        author: 'Block Editor',
        subject: 'Block-based Document Generation',
        keywords: ['blocks', 'state-management', 'dynamic-content']
      },
      options: {
        enableDynamicData: true,
        enableLiveExecution: false, // Disabled for security in this example
        validateBlocks: true
      }
    });

    console.log('Block-based Generation Result:', result);
    return result;
  } catch (error) {
    console.error('Block-based generation failed:', error);
    throw error;
  }
}

// Example 3: Batch PDF Generation
export async function batchGeneration() {
  console.log('=== Batch PDF Generation ===');
  
  const documents = [
    {
      id: 'report-1',
      title: 'Monthly Sales Report',
      content: `# Monthly Sales Report\n\n## Summary\n\nSales increased by 15% this month.\n\n## Details\n\n- New customers: 45\n- Revenue: $125,000\n- Top product: Widget Pro`,
      template: 'professional',
      metadata: { department: 'Sales', month: 'January' }
    },
    {
      id: 'report-2',
      title: 'Technical Documentation',
      content: `# API Documentation\n\n## Overview\n\nThis API provides comprehensive functionality.\n\n## Endpoints\n\n### GET /api/data\n\nReturns data in JSON format.\n\n\`\`\`json\n{\n  "status": "success",\n  "data": []\n}\n\`\`\``,
      template: 'technical-report',
      metadata: { version: '2.0', team: 'Engineering' }
    },
    {
      id: 'report-3',
      title: 'Research Summary',
      content: `# Research Summary\n\n## Methodology\n\nWe conducted a comprehensive study.\n\n## Findings\n\n1. Primary finding\n2. Secondary finding\n3. Additional insights\n\n## Conclusion\n\nThe research shows promising results.`,
      template: 'research-report',
      metadata: { researcher: 'Dr. Smith', date: '2024-01-01' }
    }
  ];

  try {
    const result = await pdfGeneratorTools.batchGeneratePDFs.execute({
      documents,
      options: {
        parallelProcessing: true,
        continueOnError: true,
        maxConcurrency: 2
      }
    });

    console.log('Batch Generation Result:', result);
    return result;
  } catch (error) {
    console.error('Batch generation failed:', error);
    throw error;
  }
}

// Example 4: Dynamic Data Management
export async function dynamicDataExample() {
  console.log('=== Dynamic Data Management ===');
  
  // Create dynamic table
  try {
    const tableResult = await pdfGeneratorTools.createDynamicData.execute({
      blockId: 'sales-table',
      type: 'table',
      data: {
        rows: [
          { product: 'Widget Pro', sales: 150, revenue: 15000, growth: '+12%' },
          { product: 'Widget Lite', sales: 89, revenue: 8900, growth: '+5%' },
          { product: 'Widget Max', sales: 234, revenue: 46800, growth: '+25%' },
          { product: 'Widget Mini', sales: 67, revenue: 3350, growth: '-3%' }
        ],
        columns: [
          { key: 'product', label: 'Product', sortable: true },
          { key: 'sales', label: 'Units Sold', sortable: true },
          { key: 'revenue', label: 'Revenue ($)', sortable: true },
          { key: 'growth', label: 'Growth', filterable: true }
        ]
      },
      options: {
        autoUpdate: false,
        refreshInterval: 30000
      }
    });

    console.log('Dynamic Table Result:', tableResult);

    // Create dynamic list
    const listResult = await pdfGeneratorTools.createDynamicData.execute({
      blockId: 'todo-list',
      type: 'list',
      data: {
        items: [
          { id: '1', content: 'Complete API documentation', completed: true },
          { id: '2', content: 'Implement batch processing', completed: true },
          { id: '3', content: 'Add dynamic data support', completed: false },
          { id: '4', content: 'Create usage examples', completed: false }
        ]
      },
      options: {
        autoUpdate: true,
        refreshInterval: 10000
      }
    });

    console.log('Dynamic List Result:', listResult);

    return { table: tableResult, list: listResult };
  } catch (error) {
    console.error('Dynamic data creation failed:', error);
    throw error;
  }
}

// Example 5: Block Operations
export async function blockOperationsExample() {
  console.log('=== Block Operations ===');
  
  const blockId = 'example-block-123';
  
  try {
    // Transition block state
    const transitionResult = await pdfGeneratorTools.blockOperation.execute({
      blockId,
      operation: 'transition',
      data: { state: 'edit' }
    });

    console.log('Block Transition Result:', transitionResult);

    // Validate block
    const validationResult = await pdfGeneratorTools.blockOperation.execute({
      blockId,
      operation: 'validate'
    });

    console.log('Block Validation Result:', validationResult);

    // Update block data
    const updateResult = await pdfGeneratorTools.blockOperation.execute({
      blockId,
      operation: 'update',
      data: {
        content: 'Updated block content',
        metadata: { lastModified: new Date().toISOString() }
      }
    });

    console.log('Block Update Result:', updateResult);

    return { transition: transitionResult, validation: validationResult, update: updateResult };
  } catch (error) {
    console.error('Block operations failed:', error);
    throw error;
  }
}

// Example 6: Content Validation
export async function contentValidationExample() {
  console.log('=== Content Validation ===');
  
  const testContent = `
# Test Document

This document contains various elements to test validation:

## Valid Elements
- Lists work fine
- **Bold text** is supported
- *Italic text* is supported

## Potentially Problematic Elements
<script>alert('This should be caught by validation');</script>

[Broken link](http://nonexistent-domain-12345.com)

![Missing image](missing-image.jpg)

## Code Block
\`\`\`javascript
// This is valid code
function test() {
  return "Hello, World!";
}
\`\`\`
`;

  try {
    const result = await pdfGeneratorTools.validateMarkdown.execute({
      content: testContent,
      options: {
        strictMode: true,
        checkXSS: true,
        maxLength: 50000
      }
    });

    console.log('Validation Result:', result);
    return result;
  } catch (error) {
    console.error('Content validation failed:', error);
    throw error;
  }
}

// Example 7: Research Report Generation
export async function researchReportExample() {
  console.log('=== Research Report Generation ===');
  
  try {
    const result = await pdfGeneratorTools.generateResearchReport.execute({
      projectId: 'research-project-2024-001',
      options: {
        template: 'academic-paper',
        includeFindings: true,
        includeAnalysis: true,
        includeSources: true,
        autoGenerateReferences: true
      }
    });

    console.log('Research Report Result:', result);
    return result;
  } catch (error) {
    console.error('Research report generation failed:', error);
    throw error;
  }
}

// Example 8: Template Management
export async function templateManagementExample() {
  console.log('=== Template Management ===');
  
  try {
    const templatesResult = await pdfGeneratorTools.getTemplates.execute({});
    console.log('Available Templates:', templatesResult);

    // Create document from specific template
    const documentResult = await pdfGeneratorTools.createDocument.execute({
      templateId: 'professional',
      title: 'Professional Document',
      content: '# Professional Document\n\nThis document uses the professional template.',
      metadata: {
        author: 'Template User',
        subject: 'Template Example',
        keywords: ['template', 'professional', 'example']
      }
    });

    console.log('Document Creation Result:', documentResult);

    return { templates: templatesResult, document: documentResult };
  } catch (error) {
    console.error('Template management failed:', error);
    throw error;
  }
}

// Example 9: Comprehensive Workflow
export async function comprehensiveWorkflow() {
  console.log('=== Comprehensive Workflow Example ===');
  
  try {
    // Step 1: Validate content
    const content = `# Comprehensive Report\n\nThis report demonstrates the full workflow.`;
    const validation = await contentValidationExample();
    
    if (!validation.data.isValid) {
      console.warn('Content validation issues found:', validation.data.errors);
    }

    // Step 2: Get available templates
    const templates = await templateManagementExample();
    
    // Step 3: Create blocks for the document
    const blocks = [
      {
        id: 'title',
        type: 'heading' as const,
        content: 'Comprehensive Workflow Report',
        metadata: { level: 1 },
        state: 'render' as const
      },
      {
        id: 'summary',
        type: 'paragraph' as const,
        content: 'This report demonstrates the complete PDF generation workflow with all features.',
        state: 'render' as const
      }
    ];

    // Step 4: Generate PDF from blocks
    const blockDocument = await pdfGeneratorTools.generateFromBlocks.execute({
      title: 'Comprehensive Workflow Report',
      blocks,
      template: 'professional',
      options: {
        enableDynamicData: true,
        validateBlocks: true
      }
    });

    // Step 5: Create dynamic data
    const dynamicData = await dynamicDataExample();

    console.log('Comprehensive Workflow Completed Successfully');
    return {
      validation,
      templates,
      document: blockDocument,
      dynamicData
    };
  } catch (error) {
    console.error('Comprehensive workflow failed:', error);
    throw error;
  }
}

// Export all examples for easy testing
export const examples = {
  basicPDFGeneration,
  blockBasedGeneration,
  batchGeneration,
  dynamicDataExample,
  blockOperationsExample,
  contentValidationExample,
  researchReportExample,
  templateManagementExample,
  comprehensiveWorkflow
};

// Run all examples (for testing)
export async function runAllExamples() {
  console.log('🚀 Running all PDF Generator API examples...\n');
  
  const results = {};
  
  for (const [name, example] of Object.entries(examples)) {
    try {
      console.log(`\n📋 Running ${name}...`);
      results[name] = await example();
      console.log(`✅ ${name} completed successfully`);
    } catch (error) {
      console.error(`❌ ${name} failed:`, error);
      results[name] = { error: error.message };
    }
  }
  
  console.log('\n🎉 All examples completed!');
  return results;
}
