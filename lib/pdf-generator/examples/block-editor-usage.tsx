/**
 * Block Editor Usage Examples
 * 
 * Examples demonstrating the block-based editor functionality
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Import block editor components
import {
  BlockEditor,
  BlockPDFEditor,
  useBlockEditor,
  Block,
  BlockType,
  createBlock,
  blocksToMarkdown,
  markdownToBlocks,
} from '../index';

/**
 * Example 1: Simple Block Editor
 */
export function SimpleBlockEditor() {
  const [savedContent, setSavedContent] = useState<string>('');

  const handleSave = (blocks: Block[], markdown: string) => {
    setSavedContent(markdown);
    console.log('Saved blocks:', blocks);
    console.log('Saved markdown:', markdown);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Simple Block Editor</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-96 border rounded">
          <BlockEditor
            onSave={handleSave}
            placeholder="Start writing with blocks..."
          />
        </div>
        {savedContent && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <h4 className="font-medium mb-2">Saved Content (Markdown):</h4>
            <pre className="text-xs overflow-auto whitespace-pre-wrap">
              {savedContent}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Example 2: Block Editor with Initial Content
 */
export function BlockEditorWithContent() {
  const initialBlocks: Block[] = [
    createBlock('heading', { content: 'Welcome to Block Editor', level: 1 }),
    createBlock('paragraph', { content: 'This is a paragraph block with some initial content.' }),
    createBlock('code', { 
      content: 'console.log("Hello, World!");', 
      language: 'javascript' 
    }),
    createBlock('quote', { 
      content: 'This is a quote block with some inspirational text.',
      author: 'Block Editor'
    }),
    createBlock('list', {
      listType: 'unordered',
      items: [
        { id: 'item1', content: 'First list item', indent: 0 },
        { id: 'item2', content: 'Second list item', indent: 0 },
        { id: 'item3', content: 'Nested item', indent: 1 },
      ]
    }),
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Block Editor with Initial Content</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-96 border rounded">
          <BlockEditor
            initialBlocks={initialBlocks}
            onSave={(blocks, markdown) => {
              console.log('Saved:', { blocks, markdown });
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Example 3: Block Editor Hook Usage
 */
export function BlockEditorHookExample() {
  const {
    blocks,
    addBlock,
    deleteBlock,
    updateBlock,
    toMarkdown,
    fromMarkdown,
    blockCount,
    isEmpty,
    undo,
    redo,
    canUndo,
    canRedo,
  } = useBlockEditor({
    initialMarkdown: '# Hook Example\n\nThis demonstrates using the block editor hook directly.',
  });

  const [markdownInput, setMarkdownInput] = useState('');

  const handleAddParagraph = () => {
    addBlock('paragraph');
  };

  const handleAddHeading = () => {
    addBlock('heading', undefined, { level: 2 });
  };

  const handleAddCode = () => {
    addBlock('code', undefined, { language: 'typescript' });
  };

  const handleLoadMarkdown = () => {
    if (markdownInput.trim()) {
      fromMarkdown(markdownInput);
      setMarkdownInput('');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Block Editor Hook Example</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Controls */}
        <div className="flex items-center space-x-2">
          <Button onClick={handleAddParagraph} size="sm">
            Add Paragraph
          </Button>
          <Button onClick={handleAddHeading} size="sm">
            Add Heading
          </Button>
          <Button onClick={handleAddCode} size="sm">
            Add Code
          </Button>
          <div className="h-4 w-px bg-gray-300" />
          <Button onClick={undo} disabled={!canUndo} size="sm">
            Undo
          </Button>
          <Button onClick={redo} disabled={!canRedo} size="sm">
            Redo
          </Button>
        </div>

        {/* Stats */}
        <div className="text-sm text-gray-600">
          Blocks: {blockCount} | Empty: {isEmpty ? 'Yes' : 'No'}
        </div>

        {/* Blocks display */}
        <div className="space-y-2 max-h-48 overflow-auto border rounded p-4">
          {blocks.map((block, index) => (
            <div key={block.id} className="flex items-center justify-between p-2 border rounded">
              <div className="flex-1">
                <span className="font-mono text-xs text-gray-500">{block.type}</span>
                <div className="text-sm truncate">{block.content || 'Empty'}</div>
              </div>
              <Button
                onClick={() => deleteBlock(block.id)}
                variant="outline"
                size="sm"
              >
                Delete
              </Button>
            </div>
          ))}
        </div>

        {/* Markdown conversion */}
        <div className="space-y-2">
          <div className="flex space-x-2">
            <input
              type="text"
              value={markdownInput}
              onChange={(e) => setMarkdownInput(e.target.value)}
              placeholder="Enter markdown to load..."
              className="flex-1 p-2 border rounded"
            />
            <Button onClick={handleLoadMarkdown}>Load Markdown</Button>
          </div>
          
          <div className="p-2 bg-gray-50 rounded">
            <div className="text-xs font-medium mb-1">Current Markdown:</div>
            <pre className="text-xs overflow-auto whitespace-pre-wrap">
              {toMarkdown()}
            </pre>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Example 4: Complete Block PDF Editor
 */
export function CompleteBlockPDFEditor() {
  const [exportedContent, setExportedContent] = useState<string>('');

  const handleSave = (document: any, blocks: Block[], markdown: string) => {
    console.log('Document saved:', { document, blocks, markdown });
  };

  const handleExport = (format: string, result: Buffer | string) => {
    setExportedContent(`Exported as ${format}: ${typeof result === 'string' ? result.length + ' characters' : result.length + ' bytes'}`);
    console.log('Exported:', format, result);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Complete Block PDF Editor</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-96 border rounded">
          <BlockPDFEditor
            initialTitle="Block PDF Document"
            initialContent="# Welcome to Block PDF Editor\n\nThis combines the block editor with PDF generation capabilities."
            onSave={handleSave}
            onExport={handleExport}
          />
        </div>
        {exportedContent && (
          <div className="mt-4 p-2 bg-green-50 border border-green-200 rounded text-sm">
            {exportedContent}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Example 5: Block Type Showcase
 */
export function BlockTypeShowcase() {
  const blockTypes: { type: BlockType; label: string; example: Partial<Block> }[] = [
    {
      type: 'paragraph',
      label: 'Paragraph',
      example: { content: 'This is a paragraph block with regular text content.' }
    },
    {
      type: 'heading',
      label: 'Heading',
      example: { content: 'This is a Heading', level: 2 } as any
    },
    {
      type: 'code',
      label: 'Code Block',
      example: { 
        content: 'function example() {\n  return "Hello, World!";\n}',
        language: 'javascript'
      } as any
    },
    {
      type: 'quote',
      label: 'Quote',
      example: { 
        content: 'The best way to predict the future is to invent it.',
        author: 'Alan Kay'
      } as any
    },
    {
      type: 'list',
      label: 'List',
      example: {
        listType: 'unordered',
        items: [
          { id: '1', content: 'First item', indent: 0 },
          { id: '2', content: 'Second item', indent: 0 },
        ]
      } as any
    },
    {
      type: 'divider',
      label: 'Divider',
      example: { style: 'solid' } as any
    },
    {
      type: 'math',
      label: 'Math',
      example: { 
        formula: 'E = mc^2',
        displayMode: 'block'
      } as any
    },
    {
      type: 'callout',
      label: 'Callout',
      example: {
        content: 'This is an important callout with useful information.',
        calloutType: 'info',
        title: 'Information'
      } as any
    },
  ];

  const [selectedBlocks, setSelectedBlocks] = useState<Block[]>([]);

  const handleAddBlock = (type: BlockType, example: Partial<Block>) => {
    const newBlock = createBlock(type, example);
    setSelectedBlocks(prev => [...prev, newBlock]);
  };

  const handleClearBlocks = () => {
    setSelectedBlocks([]);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Block Type Showcase</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Block type buttons */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {blockTypes.map(({ type, label, example }) => (
            <Button
              key={type}
              onClick={() => handleAddBlock(type, example)}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              {label}
            </Button>
          ))}
        </div>

        {/* Controls */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">
            {selectedBlocks.length} blocks selected
          </span>
          <Button onClick={handleClearBlocks} variant="outline" size="sm">
            Clear All
          </Button>
        </div>

        {/* Preview */}
        {selectedBlocks.length > 0 && (
          <div className="border rounded p-4 max-h-64 overflow-auto">
            <div className="text-sm font-medium mb-2">Generated Markdown:</div>
            <pre className="text-xs bg-gray-50 p-2 rounded whitespace-pre-wrap">
              {blocksToMarkdown(selectedBlocks)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Main Examples Component
 */
export function BlockEditorExamples() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Block Editor Examples</h1>
        <p className="text-gray-600">
          Examples demonstrating the block-based editor functionality for PDF generation.
        </p>
      </div>

      <Tabs defaultValue="simple" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="simple">Simple</TabsTrigger>
          <TabsTrigger value="content">With Content</TabsTrigger>
          <TabsTrigger value="hook">Hook Usage</TabsTrigger>
          <TabsTrigger value="complete">Complete</TabsTrigger>
          <TabsTrigger value="showcase">Showcase</TabsTrigger>
        </TabsList>

        <TabsContent value="simple">
          <SimpleBlockEditor />
        </TabsContent>

        <TabsContent value="content">
          <BlockEditorWithContent />
        </TabsContent>

        <TabsContent value="hook">
          <BlockEditorHookExample />
        </TabsContent>

        <TabsContent value="complete">
          <CompleteBlockPDFEditor />
        </TabsContent>

        <TabsContent value="showcase">
          <BlockTypeShowcase />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default BlockEditorExamples;
