/**
 * Modular Components Usage Examples
 * 
 * Examples demonstrating how to use individual modular components
 * and hooks for different use cases.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Import modular components and hooks
import {
  usePDFGenerator,
  useTemplates,
  useValidation,
  usePreview,
  useExport,
  DocumentMetadata,
  TemplateSelector,
  ValidationPanel,
  ExportControls,
  PreviewPanel,
  ModularPDFEditor,
} from '../index';

/**
 * Example 1: Simple Document Creator
 */
export function SimpleDocumentCreator() {
  const { document, createDocument, updateDocument } = usePDFGenerator();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');

  const handleCreate = async () => {
    if (title && content) {
      await createDocument(title, content);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Simple Document Creator</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <input
          type="text"
          placeholder="Document title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full p-2 border rounded"
        />
        <textarea
          placeholder="Document content (Markdown)"
          value={content}
          onChange={(e) => setContent(e.target.value)}
          className="w-full p-2 border rounded h-32"
        />
        <Button onClick={handleCreate} disabled={!title || !content}>
          Create Document
        </Button>
        {document && (
          <div className="p-2 bg-green-50 rounded">
            Document created: {document.title}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Example 2: Template Showcase
 */
export function TemplateShowcase() {
  const { templates, selectedTemplate, selectTemplate } = useTemplates();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Template Showcase</CardTitle>
      </CardHeader>
      <CardContent>
        <TemplateSelector
          selectedTemplateId={selectedTemplate?.id}
          onTemplateSelect={selectTemplate}
          showPreview={true}
          viewMode="grid"
        />
        {selectedTemplate && (
          <div className="mt-4 p-4 bg-blue-50 rounded">
            <h3 className="font-medium">{selectedTemplate.name}</h3>
            <p className="text-sm text-gray-600">
              {selectedTemplate.sections.length} sections
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Example 3: Real-time Validation
 */
export function RealTimeValidation() {
  const [content, setContent] = useState('# Test Document\n\nThis is a [broken link](');
  const { errors, warnings, isValidating, validate, validationSummary } = useValidation({
    autoValidate: true,
    debounceMs: 500,
  });

  React.useEffect(() => {
    validate(content);
  }, [content, validate]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Real-time Validation</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          className="w-full p-2 border rounded h-32 font-mono text-sm"
          placeholder="Type markdown content..."
        />
        <ValidationPanel
          errors={errors}
          warnings={warnings}
          isValidating={isValidating}
          isValid={validationSummary.isValid}
          lastValidated={validationSummary.lastValidated}
          validationCount={validationSummary.total}
        />
      </CardContent>
    </Card>
  );
}

/**
 * Example 4: Export Workflow
 */
export function ExportWorkflow() {
  const { document, createDocument } = usePDFGenerator();
  const [hasDocument, setHasDocument] = useState(false);

  const handleCreateSample = async () => {
    await createDocument(
      'Sample Document',
      '# Sample Document\n\nThis is a sample document for export testing.\n\n## Features\n\n- PDF export\n- HTML export\n- Markdown export'
    );
    setHasDocument(true);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Export Workflow</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!hasDocument ? (
          <Button onClick={handleCreateSample}>
            Create Sample Document
          </Button>
        ) : (
          <ExportControls
            document={document}
            onExportComplete={(format, result) => {
              console.log(`Exported as ${format}:`, result);
            }}
          />
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Example 5: Preview Modes
 */
export function PreviewModes() {
  const { document, createDocument } = usePDFGenerator();
  const [hasDocument, setHasDocument] = useState(false);

  const handleCreateSample = async () => {
    await createDocument(
      'Preview Sample',
      '# Preview Sample\n\nThis document demonstrates different preview modes.\n\n## Code Example\n\n```javascript\nconsole.log("Hello, World!");\n```\n\n## List\n\n- Item 1\n- Item 2\n- Item 3'
    );
    setHasDocument(true);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Preview Modes</CardTitle>
      </CardHeader>
      <CardContent>
        {!hasDocument ? (
          <Button onClick={handleCreateSample}>
            Create Sample Document
          </Button>
        ) : (
          <PreviewPanel
            document={document}
            showStats={true}
            showControls={true}
          />
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Example 6: Metadata Editor
 */
export function MetadataEditor() {
  const [metadata, setMetadata] = useState({
    title: 'Sample Document',
    author: 'John Doe',
    subject: 'PDF Generation',
    keywords: ['pdf', 'generation', 'example'],
    description: 'A sample document for testing metadata editing',
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Metadata Editor</CardTitle>
      </CardHeader>
      <CardContent>
        <DocumentMetadata
          metadata={metadata}
          onChange={setMetadata}
          showAdvanced={true}
        />
        <div className="mt-4 p-4 bg-gray-50 rounded">
          <h4 className="font-medium mb-2">Current Metadata:</h4>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(metadata, null, 2)}
          </pre>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Main Examples Component
 */
export function ModularComponentsExamples() {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">PDF Generator Modular Components</h1>
        <p className="text-gray-600">
          Examples demonstrating the modular components and hooks for PDF generation.
        </p>
      </div>

      <Tabs defaultValue="complete" className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="complete">Complete Editor</TabsTrigger>
          <TabsTrigger value="creator">Creator</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="validation">Validation</TabsTrigger>
          <TabsTrigger value="export">Export</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
          <TabsTrigger value="metadata">Metadata</TabsTrigger>
        </TabsList>

        <TabsContent value="complete">
          <Card>
            <CardHeader>
              <CardTitle>Complete Modular PDF Editor</CardTitle>
            </CardHeader>
            <CardContent>
              <ModularPDFEditor
                initialTitle="Complete Example"
                initialContent="# Complete PDF Editor Example\n\nThis demonstrates all modular components working together.\n\n## Features\n\n- Real-time validation\n- Live preview\n- Template selection\n- Metadata editing\n- Multi-format export\n\n## Code Example\n\n```javascript\nconst editor = new ModularPDFEditor({\n  template: 'research-report',\n  autoSave: true\n});\n```"
                onSave={(doc) => console.log('Document saved:', doc)}
                onExport={(format, result) => console.log('Exported:', format, result)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="creator">
          <SimpleDocumentCreator />
        </TabsContent>

        <TabsContent value="templates">
          <TemplateShowcase />
        </TabsContent>

        <TabsContent value="validation">
          <RealTimeValidation />
        </TabsContent>

        <TabsContent value="export">
          <ExportWorkflow />
        </TabsContent>

        <TabsContent value="preview">
          <PreviewModes />
        </TabsContent>

        <TabsContent value="metadata">
          <MetadataEditor />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ModularComponentsExamples;
