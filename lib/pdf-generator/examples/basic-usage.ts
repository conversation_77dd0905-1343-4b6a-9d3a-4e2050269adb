/**
 * PDF Generator Basic Usage Examples
 * 
 * Comprehensive examples demonstrating the PDF generator library capabilities
 */

import { 
  generatePDFFromMarkdown,
  createDocumentFromTemplate,
  validateMarkdown,
  getAvailableTemplates,
  exportDocument,
  generateResearchReport,
  parseMarkdownStructure,
  pdfGeneratorService
} from '../index';

/**
 * Example 1: Simple PDF Generation
 */
export async function basicPDFGeneration() {
  console.log('🔄 Generating basic PDF...');

  const markdown = `
# My First PDF Document

## Introduction

This is a simple example of generating a PDF from markdown content.

### Features Demonstrated

- **Headers**: Multiple levels of headings
- **Lists**: Both ordered and unordered
- **Code**: \`inline code\` and code blocks
- **Links**: [Visit our website](https://example.com)
- **Emphasis**: *italic* and **bold** text

### Code Example

\`\`\`javascript
function generatePDF() {
  console.log('Generating PDF...');
  return 'PDF generated successfully!';
}
\`\`\`

### Table Example

| Feature | Status | Priority |
|---------|--------|----------|
| PDF Generation | ✅ Complete | High |
| Templates | ✅ Complete | High |
| Validation | ✅ Complete | Medium |

## Conclusion

This demonstrates the basic PDF generation capabilities of the library.
`;

  try {
    const pdfBuffer = await generatePDFFromMarkdown(
      'My First PDF Document',
      markdown,
      {
        template: 'research-report',
        author: 'John Doe',
        metadata: {
          subject: 'PDF Generation Example',
          keywords: ['pdf', 'generation', 'example'],
          description: 'A basic example of PDF generation from markdown',
        },
      }
    );

    console.log('✅ PDF generated successfully!');
    console.log(`📄 Size: ${pdfBuffer.length} bytes`);
    
    return pdfBuffer;
  } catch (error) {
    console.error('❌ PDF generation failed:', error);
    throw error;
  }
}

/**
 * Example 2: Document Creation with Template
 */
export async function documentFromTemplate() {
  console.log('🔄 Creating document from template...');

  try {
    const document = await createDocumentFromTemplate('technical-document', {
      title: 'API Documentation',
      markdown: `
# API Documentation

## Overview

This document provides comprehensive documentation for our REST API.

## Authentication

All API requests require authentication using API keys:

\`\`\`bash
curl -H "Authorization: Bearer YOUR_API_KEY" https://api.example.com/v1/users
\`\`\`

## Endpoints

### Users

#### GET /api/v1/users

Retrieve a list of users.

**Parameters:**
- \`limit\` (optional): Number of users to return (default: 10)
- \`offset\` (optional): Number of users to skip (default: 0)

**Response:**
\`\`\`json
{
  "users": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  ],
  "total": 100,
  "limit": 10,
  "offset": 0
}
\`\`\`

#### POST /api/v1/users

Create a new user.

**Request Body:**
\`\`\`json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "password": "secure_password"
}
\`\`\`

## Error Handling

The API uses standard HTTP status codes:

- \`200\` - Success
- \`400\` - Bad Request
- \`401\` - Unauthorized
- \`404\` - Not Found
- \`500\` - Internal Server Error

## Rate Limiting

API requests are limited to 1000 requests per hour per API key.
`,
      metadata: {
        author: 'API Team',
        subject: 'REST API Documentation',
        keywords: ['api', 'documentation', 'rest', 'endpoints'],
        version: '1.0.0',
      },
    });

    console.log('✅ Document created successfully!');
    console.log(`📄 Document ID: ${document.id}`);
    console.log(`📝 Template: ${document.template.name}`);
    
    return document;
  } catch (error) {
    console.error('❌ Document creation failed:', error);
    throw error;
  }
}

/**
 * Example 3: Content Validation
 */
export async function contentValidation() {
  console.log('🔄 Validating markdown content...');

  const validMarkdown = `
# Valid Document

This is a well-formed markdown document.

## Section 1

Content with proper structure.

![Valid Image](https://example.com/image.jpg)

[Valid Link](https://example.com)
`;

  const invalidMarkdown = `
# Invalid Document

This document has some issues:

- Unmatched brackets: [missing closing bracket
- Invalid image: ![Missing URL]()
- Empty header: ##
- Malformed link: [text](
`;

  try {
    // Validate good content
    const validResult = await validateMarkdown(validMarkdown);
    console.log('✅ Valid content validation:');
    console.log(`   - Is valid: ${validResult.isValid}`);
    console.log(`   - Errors: ${validResult.errors.length}`);
    console.log(`   - Warnings: ${validResult.warnings.length}`);

    // Validate problematic content
    const invalidResult = await validateMarkdown(invalidMarkdown);
    console.log('⚠️  Invalid content validation:');
    console.log(`   - Is valid: ${invalidResult.isValid}`);
    console.log(`   - Errors: ${invalidResult.errors.length}`);
    console.log(`   - Warnings: ${invalidResult.warnings.length}`);

    if (invalidResult.errors.length > 0) {
      console.log('   Errors found:');
      invalidResult.errors.forEach((error, index) => {
        console.log(`     ${index + 1}. ${error.message} (${error.type})`);
      });
    }

    return { validResult, invalidResult };
  } catch (error) {
    console.error('❌ Validation failed:', error);
    throw error;
  }
}

/**
 * Example 4: Template Management
 */
export async function templateManagement() {
  console.log('🔄 Managing templates...');

  try {
    const templates = await getAvailableTemplates();
    
    console.log('📋 Available templates:');
    templates.forEach((template, index) => {
      console.log(`   ${index + 1}. ${template.name} (${template.id}) - ${template.type}`);
    });

    return templates;
  } catch (error) {
    console.error('❌ Template management failed:', error);
    throw error;
  }
}

/**
 * Example 5: Multi-format Export
 */
export async function multiFormatExport() {
  console.log('🔄 Exporting document in multiple formats...');

  const document = await createDocumentFromTemplate('article', {
    title: 'Export Example',
    markdown: `
# Export Example

This document will be exported in multiple formats.

## Content

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

### Code Example

\`\`\`python
def hello_world():
    print("Hello, World!")
    return "success"
\`\`\`

### List Example

1. First item
2. Second item
3. Third item

## Conclusion

This demonstrates multi-format export capabilities.
`,
    metadata: {
      author: 'Export Team',
      subject: 'Multi-format Export Example',
    },
  });

  try {
    const formats = ['pdf', 'html', 'markdown'] as const;
    const results: Record<string, any> = {};

    for (const format of formats) {
      console.log(`   Exporting as ${format.toUpperCase()}...`);
      const result = await exportDocument(document, format);
      
      if (typeof result === 'string') {
        results[format] = { type: 'string', size: result.length };
      } else {
        results[format] = { type: 'buffer', size: result.length };
      }
      
      console.log(`   ✅ ${format.toUpperCase()} export completed (${results[format].size} ${results[format].type === 'string' ? 'characters' : 'bytes'})`);
    }

    return results;
  } catch (error) {
    console.error('❌ Multi-format export failed:', error);
    throw error;
  }
}

/**
 * Example 6: Markdown Structure Parsing
 */
export async function markdownStructureParsing() {
  console.log('🔄 Parsing markdown structure...');

  const complexMarkdown = `
---
title: Complex Document
author: Structure Parser
tags: [parsing, structure, example]
---

# Complex Document Structure

## Introduction

This document demonstrates structure parsing capabilities.

### Subsection 1.1

Content with various elements.

#### Deep Subsection 1.1.1

Even deeper content.

## Main Content

### Images

![Example Image](https://example.com/image1.jpg "Image Title")
![Another Image](./local-image.png)

### Links

- [External Link](https://example.com)
- [Internal Link](#introduction)
- [Email Link](mailto:<EMAIL>)

### Code Blocks

\`\`\`javascript
function example() {
  return "This is a code block";
}
\`\`\`

\`\`\`python
def another_example():
    return "Another language"
\`\`\`

### Tables

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Data 1   | Data 2   | Data 3   |
| Data 4   | Data 5   | Data 6   |

## Conclusion

This concludes the structure parsing example.
`;

  try {
    const structure = await parseMarkdownStructure(complexMarkdown);
    
    console.log('📊 Parsed structure:');
    console.log(`   - Headings: ${structure.toc.length}`);
    console.log(`   - Images: ${structure.images.length}`);
    console.log(`   - Links: ${structure.links.length}`);
    console.log(`   - Code blocks: ${structure.codeBlocks.length}`);
    console.log(`   - Tables: ${structure.tables.length}`);
    console.log(`   - Metadata keys: ${Object.keys(structure.metadata).length}`);

    console.log('\n📋 Table of Contents:');
    structure.toc.forEach((heading, index) => {
      const indent = '  '.repeat(heading.level - 1);
      console.log(`   ${indent}${index + 1}. ${heading.title} (Level ${heading.level})`);
    });

    if (structure.images.length > 0) {
      console.log('\n🖼️  Images found:');
      structure.images.forEach((image, index) => {
        console.log(`   ${index + 1}. ${image.alt} - ${image.src}`);
      });
    }

    return structure;
  } catch (error) {
    console.error('❌ Structure parsing failed:', error);
    throw error;
  }
}

/**
 * Example 7: Research Integration (Placeholder)
 */
export async function researchIntegration() {
  console.log('🔄 Generating research report...');

  try {
    // This would integrate with the Deep Research library
    // For demonstration, we'll use a placeholder project ID
    const projectId = 'research-project-123';
    
    console.log(`   Generating report for project: ${projectId}`);
    
    // Note: This would fail in practice without actual research data
    // but demonstrates the API
    const document = await generateResearchReport(projectId, {
      template: 'research-report',
      includeFindings: true,
      includeSources: true,
      includeAnalysis: true,
      autoGenerateReferences: true,
    });

    console.log('✅ Research report generated successfully!');
    console.log(`📄 Document ID: ${document.id}`);
    console.log(`📝 Title: ${document.title}`);
    
    return document;
  } catch (error) {
    console.log('ℹ️  Research integration example (would require actual research data)');
    console.log(`   Error: ${error.message}`);
    return null;
  }
}

/**
 * Run all examples
 */
export async function runAllExamples() {
  console.log('🚀 Running PDF Generator Examples\n');

  const examples = [
    { name: 'Basic PDF Generation', fn: basicPDFGeneration },
    { name: 'Document from Template', fn: documentFromTemplate },
    { name: 'Content Validation', fn: contentValidation },
    { name: 'Template Management', fn: templateManagement },
    { name: 'Multi-format Export', fn: multiFormatExport },
    { name: 'Markdown Structure Parsing', fn: markdownStructureParsing },
    { name: 'Research Integration', fn: researchIntegration },
  ];

  const results: Record<string, any> = {};

  for (const example of examples) {
    try {
      console.log(`\n📖 ${example.name}`);
      console.log('─'.repeat(50));
      
      const result = await example.fn();
      results[example.name] = { success: true, result };
      
      console.log(`✅ ${example.name} completed successfully\n`);
    } catch (error) {
      results[example.name] = { success: false, error: error.message };
      console.log(`❌ ${example.name} failed: ${error.message}\n`);
    }
  }

  console.log('📊 Summary');
  console.log('─'.repeat(50));
  
  const successful = Object.values(results).filter(r => r.success).length;
  const failed = Object.values(results).filter(r => !r.success).length;
  
  console.log(`✅ Successful: ${successful}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${examples.length}`);

  return results;
}

// Export for use in other files
export default {
  basicPDFGeneration,
  documentFromTemplate,
  contentValidation,
  templateManagement,
  multiFormatExport,
  markdownStructureParsing,
  researchIntegration,
  runAllExamples,
};
