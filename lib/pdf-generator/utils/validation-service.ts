/**
 * Validation Service
 * 
 * Comprehensive validation for PDF documents, templates, content,
 * and configuration with detailed error reporting.
 */

import { 
  PDFDocument, 
  DocumentTemplate, 
  DocumentStyling,
  ValidationError,
  ValidationWarning,
  ParsedMarkdown
} from '../types';
import { validationRules, errorMessages } from '../config';

export class ValidationService {
  /**
   * Validate complete document
   */
  async validateDocument(document: PDFDocument): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];

    // Validate basic document properties
    errors.push(...this.validateDocumentBasics(document));

    // Validate metadata
    errors.push(...this.validateMetadata(document.metadata));

    // Validate template
    errors.push(...this.validateTemplate(document.template));

    // Validate styling
    errors.push(...this.validateStyling(document.styling));

    // Validate content
    errors.push(...await this.validateContent(document.content));

    return errors;
  }

  /**
   * Validate basic document properties
   */
  private validateDocumentBasics(document: PDFDocument): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!document.id) {
      errors.push({
        id: 'doc-id-missing',
        type: 'metadata',
        message: 'Document ID is required',
        severity: 'error',
      });
    }

    if (!document.title || document.title.trim().length === 0) {
      errors.push({
        id: 'doc-title-missing',
        type: 'metadata',
        message: 'Document title is required',
        severity: 'error',
      });
    }

    if (document.title && document.title.length > validationRules.maxTitleLength) {
      errors.push({
        id: 'doc-title-too-long',
        type: 'metadata',
        message: `Title exceeds maximum length of ${validationRules.maxTitleLength} characters`,
        severity: 'error',
      });
    }

    if (!document.content || document.content.trim().length === 0) {
      errors.push({
        id: 'doc-content-missing',
        type: 'syntax',
        message: 'Document content is required',
        severity: 'error',
      });
    }

    if (document.content && document.content.length > validationRules.maxContentLength) {
      errors.push({
        id: 'doc-content-too-long',
        type: 'syntax',
        message: `Content exceeds maximum length of ${validationRules.maxContentLength} characters`,
        severity: 'error',
      });
    }

    return errors;
  }

  /**
   * Validate document metadata
   */
  private validateMetadata(metadata: any): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!metadata.author || metadata.author.trim().length === 0) {
      errors.push({
        id: 'metadata-author-missing',
        type: 'metadata',
        message: 'Author is required',
        severity: 'warning',
      });
    }

    if (metadata.author && metadata.author.length > validationRules.maxAuthorLength) {
      errors.push({
        id: 'metadata-author-too-long',
        type: 'metadata',
        message: `Author name exceeds maximum length of ${validationRules.maxAuthorLength} characters`,
        severity: 'error',
      });
    }

    if (metadata.description && metadata.description.length > validationRules.maxDescriptionLength) {
      errors.push({
        id: 'metadata-description-too-long',
        type: 'metadata',
        message: `Description exceeds maximum length of ${validationRules.maxDescriptionLength} characters`,
        severity: 'error',
      });
    }

    if (metadata.keywords && Array.isArray(metadata.keywords)) {
      if (metadata.keywords.length > validationRules.maxKeywords) {
        errors.push({
          id: 'metadata-too-many-keywords',
          type: 'metadata',
          message: `Too many keywords (max: ${validationRules.maxKeywords})`,
          severity: 'warning',
        });
      }
    }

    if (metadata.creationDate && !this.isValidDate(metadata.creationDate)) {
      errors.push({
        id: 'metadata-invalid-creation-date',
        type: 'metadata',
        message: 'Invalid creation date format',
        severity: 'error',
      });
    }

    return errors;
  }

  /**
   * Validate document template
   */
  private validateTemplate(template: DocumentTemplate): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!template.id) {
      errors.push({
        id: 'template-id-missing',
        type: 'template',
        message: 'Template ID is required',
        severity: 'error',
      });
    }

    if (!template.name) {
      errors.push({
        id: 'template-name-missing',
        type: 'template',
        message: 'Template name is required',
        severity: 'error',
      });
    }

    if (!template.sections || template.sections.length === 0) {
      errors.push({
        id: 'template-no-sections',
        type: 'template',
        message: 'Template must have at least one section',
        severity: 'error',
      });
    }

    if (template.sections && template.sections.length > validationRules.maxSections) {
      errors.push({
        id: 'template-too-many-sections',
        type: 'template',
        message: `Too many sections (max: ${validationRules.maxSections})`,
        severity: 'error',
      });
    }

    // Validate sections
    if (template.sections) {
      const requiredSections = template.sections.filter(s => s.required);
      if (requiredSections.length === 0) {
        errors.push({
          id: 'template-no-required-sections',
          type: 'template',
          message: 'Template must have at least one required section',
          severity: 'warning',
        });
      }

      // Check for duplicate section orders
      const orders = template.sections.map(s => s.order);
      const uniqueOrders = new Set(orders);
      if (orders.length !== uniqueOrders.size) {
        errors.push({
          id: 'template-duplicate-orders',
          type: 'template',
          message: 'Section orders must be unique',
          severity: 'error',
        });
      }

      // Validate individual sections
      template.sections.forEach((section, index) => {
        if (!section.id) {
          errors.push({
            id: `template-section-${index}-no-id`,
            type: 'template',
            message: `Section ${index + 1} is missing an ID`,
            severity: 'error',
          });
        }

        if (!section.name) {
          errors.push({
            id: `template-section-${index}-no-name`,
            type: 'template',
            message: `Section ${index + 1} is missing a name`,
            severity: 'error',
          });
        }

        if (!section.type) {
          errors.push({
            id: `template-section-${index}-no-type`,
            type: 'template',
            message: `Section ${index + 1} is missing a type`,
            severity: 'error',
          });
        }
      });
    }

    return errors;
  }

  /**
   * Validate document styling
   */
  private validateStyling(styling: DocumentStyling): ValidationError[] {
    const errors: ValidationError[] = [];

    // Validate fonts
    if (!styling.fonts?.primary) {
      errors.push({
        id: 'styling-no-primary-font',
        type: 'styling',
        message: 'Primary font configuration is required',
        severity: 'error',
      });
    }

    if (styling.fonts?.primary && !this.isValidFont(styling.fonts.primary)) {
      errors.push({
        id: 'styling-invalid-primary-font',
        type: 'styling',
        message: 'Invalid primary font configuration',
        severity: 'error',
      });
    }

    // Validate colors
    if (!styling.colors?.background || !styling.colors?.text) {
      errors.push({
        id: 'styling-missing-colors',
        type: 'styling',
        message: 'Background and text colors are required',
        severity: 'error',
      });
    }

    if (styling.colors) {
      Object.entries(styling.colors).forEach(([key, color]) => {
        if (typeof color === 'string' && !this.isValidColor(color)) {
          errors.push({
            id: `styling-invalid-color-${key}`,
            type: 'styling',
            message: `Invalid color format for ${key}: ${color}`,
            severity: 'error',
          });
        }
      });
    }

    // Validate layout
    if (!styling.layout?.pageSize) {
      errors.push({
        id: 'styling-no-page-size',
        type: 'styling',
        message: 'Page size is required',
        severity: 'error',
      });
    }

    if (styling.layout?.margins) {
      const { margins } = styling.layout;
      if (margins.top < 0 || margins.right < 0 || margins.bottom < 0 || margins.left < 0) {
        errors.push({
          id: 'styling-negative-margins',
          type: 'styling',
          message: 'Margins cannot be negative',
          severity: 'error',
        });
      }
    }

    return errors;
  }

  /**
   * Validate markdown content
   */
  async validateContent(content: string): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];

    if (!content || content.trim().length === 0) {
      return [{
        id: 'content-empty',
        type: 'syntax',
        message: 'Content cannot be empty',
        severity: 'error',
      }];
    }

    // Check for common markdown syntax errors
    errors.push(...this.validateMarkdownSyntax(content));

    // Check for image references
    errors.push(...this.validateImageReferences(content));

    // Check for link references
    errors.push(...this.validateLinkReferences(content));

    return errors;
  }

  /**
   * Validate markdown syntax
   */
  private validateMarkdownSyntax(content: string): ValidationError[] {
    const errors: ValidationError[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // Check for unmatched brackets
      const openBrackets = (line.match(/\[/g) || []).length;
      const closeBrackets = (line.match(/\]/g) || []).length;
      if (openBrackets !== closeBrackets) {
        errors.push({
          id: `syntax-unmatched-brackets-${lineNumber}`,
          type: 'syntax',
          message: 'Unmatched brackets in markdown',
          line: lineNumber,
          severity: 'warning',
        });
      }

      // Check for unmatched parentheses in links
      const openParens = (line.match(/\(/g) || []).length;
      const closeParens = (line.match(/\)/g) || []).length;
      if (openParens !== closeParens) {
        errors.push({
          id: `syntax-unmatched-parens-${lineNumber}`,
          type: 'syntax',
          message: 'Unmatched parentheses in markdown',
          line: lineNumber,
          severity: 'warning',
        });
      }

      // Check for malformed headers
      const headerMatch = line.match(/^(#+)\s*(.*)/);
      if (headerMatch) {
        const level = headerMatch[1].length;
        const text = headerMatch[2].trim();
        
        if (level > 6) {
          errors.push({
            id: `syntax-invalid-header-level-${lineNumber}`,
            type: 'syntax',
            message: 'Header level cannot exceed 6',
            line: lineNumber,
            severity: 'error',
          });
        }

        if (!text) {
          errors.push({
            id: `syntax-empty-header-${lineNumber}`,
            type: 'syntax',
            message: 'Header text cannot be empty',
            line: lineNumber,
            severity: 'warning',
          });
        }
      }
    });

    return errors;
  }

  /**
   * Validate image references
   */
  private validateImageReferences(content: string): ValidationError[] {
    const errors: ValidationError[] = [];
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
    let match;

    while ((match = imageRegex.exec(content)) !== null) {
      const alt = match[1];
      const src = match[2];

      if (!alt.trim()) {
        errors.push({
          id: `image-no-alt-${src}`,
          type: 'image',
          message: `Image missing alt text: ${src}`,
          severity: 'warning',
        });
      }

      if (!this.isValidImageUrl(src)) {
        errors.push({
          id: `image-invalid-url-${src}`,
          type: 'image',
          message: `Invalid image URL: ${src}`,
          severity: 'error',
        });
      }
    }

    return errors;
  }

  /**
   * Validate link references
   */
  private validateLinkReferences(content: string): ValidationError[] {
    const errors: ValidationError[] = [];
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    let match;

    while ((match = linkRegex.exec(content)) !== null) {
      const text = match[1];
      const href = match[2];

      if (!text.trim()) {
        errors.push({
          id: `link-no-text-${href}`,
          type: 'link',
          message: `Link missing text: ${href}`,
          severity: 'warning',
        });
      }

      if (!this.isValidUrl(href) && !href.startsWith('#')) {
        errors.push({
          id: `link-invalid-url-${href}`,
          type: 'link',
          message: `Invalid link URL: ${href}`,
          severity: 'warning',
        });
      }
    }

    return errors;
  }

  /**
   * Generate validation warnings
   */
  generateWarnings(document: PDFDocument, parsedMarkdown: ParsedMarkdown): ValidationWarning[] {
    const warnings: ValidationWarning[] = [];

    // Check for missing table of contents
    if (parsedMarkdown.toc.length === 0) {
      warnings.push({
        id: 'no-toc',
        message: 'Document has no headings for table of contents',
        suggestion: 'Add headings to structure your document',
      });
    }

    // Check for missing images
    if (parsedMarkdown.images.length === 0) {
      warnings.push({
        id: 'no-images',
        message: 'Document contains no images',
        suggestion: 'Consider adding relevant images to enhance readability',
      });
    }

    // Check for very long paragraphs
    const paragraphs = document.content.split('\n\n');
    paragraphs.forEach((paragraph, index) => {
      if (paragraph.length > 1000) {
        warnings.push({
          id: `long-paragraph-${index}`,
          message: `Paragraph ${index + 1} is very long (${paragraph.length} characters)`,
          suggestion: 'Consider breaking long paragraphs into smaller ones',
        });
      }
    });

    // Check for missing metadata
    if (!document.metadata.description) {
      warnings.push({
        id: 'no-description',
        message: 'Document missing description',
        suggestion: 'Add a description to improve document metadata',
      });
    }

    return warnings;
  }

  /**
   * Helper validation methods
   */
  private isValidDate(date: any): boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }

  private isValidFont(font: any): boolean {
    return font && 
           typeof font.family === 'string' && 
           typeof font.size === 'number' && 
           font.size > 0;
  }

  private isValidColor(color: string): boolean {
    // Basic color validation (hex, rgb, named colors)
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    const rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/;
    const namedColors = ['red', 'blue', 'green', 'black', 'white', 'gray', 'yellow', 'orange', 'purple', 'pink'];
    
    return hexRegex.test(color) || 
           rgbRegex.test(color) || 
           namedColors.includes(color.toLowerCase());
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private isValidImageUrl(url: string): boolean {
    if (!this.isValidUrl(url) && !url.startsWith('./') && !url.startsWith('../')) {
      return false;
    }

    const extension = url.split('.').pop()?.toLowerCase();
    return extension ? validationRules.allowedImageFormats.includes(extension) : false;
  }
}
