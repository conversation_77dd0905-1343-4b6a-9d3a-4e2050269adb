/**
 * Style Generator
 * 
 * Generates CSS styles from document styling configuration,
 * supporting themes, responsive design, and print optimization.
 */

import { 
  DocumentStyling, 
  FontConfiguration, 
  ColorScheme, 
  SpacingConfiguration,
  LayoutConfiguration,
  FontDefinition
} from '../types';

export class StyleGenerator {
  /**
   * Generate complete CSS from document styling
   */
  generateCSS(styling: DocumentStyling): string {
    const sections = [
      this.generateFontStyles(styling.fonts),
      this.generateColorStyles(styling.colors),
      this.generateSpacingStyles(styling.spacing),
      this.generateLayoutStyles(styling.layout),
      this.generateComponentStyles(styling),
      this.generatePrintStyles(styling),
    ];

    return sections.filter(Boolean).join('\n\n');
  }

  /**
   * Generate font-related CSS
   */
  private generateFontStyles(fonts: FontConfiguration): string {
    const styles = [];

    // Base font styles
    styles.push(`
body {
    font-family: ${fonts.primary.family};
    font-size: ${fonts.primary.size}pt;
    font-weight: ${fonts.primary.weight};
    font-style: ${fonts.primary.style};
    line-height: ${fonts.primary.lineHeight || 1.6};
}`);

    // Heading styles
    Object.entries(fonts.headings).forEach(([tag, font]) => {
      styles.push(`
${tag} {
    font-family: ${font.family};
    font-size: ${font.size}pt;
    font-weight: ${font.weight};
    font-style: ${font.style};
    line-height: ${font.lineHeight || 1.2};
}`);
    });

    // Monospace styles
    styles.push(`
code, pre, .monospace {
    font-family: ${fonts.monospace.family};
    font-size: ${fonts.monospace.size}pt;
    font-weight: ${fonts.monospace.weight};
    line-height: ${fonts.monospace.lineHeight || 1.4};
}`);

    // Secondary font styles
    styles.push(`
.secondary-font {
    font-family: ${fonts.secondary.family};
    font-size: ${fonts.secondary.size}pt;
    font-weight: ${fonts.secondary.weight};
    font-style: ${fonts.secondary.style};
}`);

    return styles.join('\n');
  }

  /**
   * Generate color-related CSS
   */
  private generateColorStyles(colors: ColorScheme): string {
    return `
:root {
    --color-primary: ${colors.primary};
    --color-secondary: ${colors.secondary};
    --color-accent: ${colors.accent};
    --color-background: ${colors.background};
    --color-text: ${colors.text};
    --color-heading: ${colors.heading};
    --color-link: ${colors.link};
    --color-border: ${colors.border};
}

body {
    background-color: ${colors.background};
    color: ${colors.text};
}

h1, h2, h3, h4, h5, h6 {
    color: ${colors.heading};
}

a {
    color: ${colors.link};
}

.primary {
    color: ${colors.primary};
}

.secondary {
    color: ${colors.secondary};
}

.accent {
    color: ${colors.accent};
}

.border {
    border-color: ${colors.border};
}

.bg-primary {
    background-color: ${colors.primary};
}

.bg-secondary {
    background-color: ${colors.secondary};
}

.bg-accent {
    background-color: ${colors.accent};
}`;
  }

  /**
   * Generate spacing-related CSS
   */
  private generateSpacingStyles(spacing: SpacingConfiguration): string {
    return `
.document-container {
    margin: ${spacing.margin.top}px ${spacing.margin.right}px ${spacing.margin.bottom}px ${spacing.margin.left}px;
    padding: ${spacing.padding.top}px ${spacing.padding.right}px ${spacing.padding.bottom}px ${spacing.padding.left}px;
}

p {
    margin-bottom: ${spacing.paragraphSpacing}px;
    line-height: ${spacing.lineHeight};
}

.section {
    margin-bottom: ${spacing.sectionSpacing}px;
}

.section + .section {
    margin-top: ${spacing.sectionSpacing}px;
}`;
  }

  /**
   * Generate layout-related CSS
   */
  private generateLayoutStyles(layout: LayoutConfiguration): string {
    const styles = [];

    // Page setup
    styles.push(`
@page {
    size: ${layout.pageSize} ${layout.orientation};
    margin: ${layout.margins.top}px ${layout.margins.right}px ${layout.margins.bottom}px ${layout.margins.left}px;
}`);

    // Multi-column layout
    if (layout.columns > 1) {
      styles.push(`
.main-content {
    column-count: ${layout.columns};
    column-gap: ${layout.columnGap}px;
    column-fill: balance;
}`);
    }

    // Header styles
    if (layout.header?.enabled) {
      styles.push(`
.header {
    height: ${layout.header.height}px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}`);
    }

    // Footer styles
    if (layout.footer?.enabled) {
      styles.push(`
.footer {
    height: ${layout.footer.height}px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}`);
    }

    return styles.join('\n');
  }

  /**
   * Generate component-specific styles
   */
  private generateComponentStyles(styling: DocumentStyling): string {
    return `
/* Cover Page Styles */
.cover-page {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    text-align: center;
    page-break-after: always;
}

.cover-title {
    font-size: 2.5em;
    margin-bottom: 0.5em;
    font-weight: bold;
}

.cover-subtitle {
    font-size: 1.5em;
    margin-bottom: 1em;
    color: ${styling.colors.secondary};
}

.cover-author {
    font-size: 1.2em;
    margin-bottom: 0.5em;
}

.cover-organization {
    font-size: 1em;
    margin-bottom: 1em;
    color: ${styling.colors.secondary};
}

.cover-date {
    font-size: 1em;
    color: ${styling.colors.secondary};
}

/* Table of Contents Styles */
.toc {
    page-break-after: always;
}

.toc-title {
    font-size: 1.8em;
    margin-bottom: 1em;
    text-align: center;
}

.toc-entry {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin: 0.5em 0;
    position: relative;
}

.toc-title {
    flex: 1;
}

.toc-dots {
    flex: 1;
    border-bottom: 1px dotted ${styling.colors.border};
    margin: 0 0.5em;
    height: 1px;
    align-self: flex-end;
}

.toc-page {
    font-weight: bold;
}

.toc-level-1 {
    font-weight: bold;
    margin-top: 1em;
}

.toc-level-2 {
    margin-left: 1em;
}

.toc-level-3 {
    margin-left: 2em;
    font-size: 0.9em;
}

.toc-level-4 {
    margin-left: 3em;
    font-size: 0.85em;
}

/* Content Styles */
.main-content {
    orphans: 3;
    widows: 3;
}

.main-content h1 {
    page-break-before: always;
    margin-top: 0;
}

.main-content h1:first-child {
    page-break-before: auto;
}

.main-content h2,
.main-content h3,
.main-content h4,
.main-content h5,
.main-content h6 {
    page-break-after: avoid;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

/* Table Styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
    page-break-inside: avoid;
}

th, td {
    border: 1px solid ${styling.colors.border};
    padding: 0.5em;
    text-align: left;
}

th {
    background-color: ${styling.colors.primary};
    color: white;
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* Code Block Styles */
pre {
    background-color: #f5f5f5;
    border: 1px solid ${styling.colors.border};
    border-radius: 4px;
    padding: 1em;
    overflow-x: auto;
    page-break-inside: avoid;
}

code {
    background-color: #f5f5f5;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 0.9em;
}

pre code {
    background-color: transparent;
    padding: 0;
}

/* Blockquote Styles */
blockquote {
    border-left: 4px solid ${styling.colors.accent};
    margin: 1em 0;
    padding-left: 1em;
    font-style: italic;
    color: ${styling.colors.secondary};
}

/* List Styles */
ul, ol {
    margin: 1em 0;
    padding-left: 2em;
}

li {
    margin: 0.5em 0;
}

/* Image Styles */
img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1em auto;
    page-break-inside: avoid;
}

.image-caption {
    text-align: center;
    font-style: italic;
    margin-top: 0.5em;
    color: ${styling.colors.secondary};
}

/* Reference Styles */
.references-section {
    page-break-before: always;
}

.reference-item {
    margin: 0.5em 0;
    padding-left: 1em;
    text-indent: -1em;
}

/* Abstract Styles */
.abstract-section {
    page-break-after: always;
}

.abstract-content {
    font-style: italic;
    margin: 1em 0;
    padding: 1em;
    border: 1px solid ${styling.colors.border};
    background-color: #f9f9f9;
}`;
  }

  /**
   * Generate print-specific styles
   */
  private generatePrintStyles(styling: DocumentStyling): string {
    return `
@media print {
    body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .no-print {
        display: none !important;
    }

    a {
        text-decoration: none;
        color: inherit;
    }

    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }

    a[href^="#"]:after,
    a[href^="javascript:"]:after {
        content: "";
    }

    .page-break {
        page-break-before: always;
    }

    .no-break {
        page-break-inside: avoid;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }

    img {
        page-break-inside: avoid;
    }

    table {
        page-break-inside: avoid;
    }

    pre {
        page-break-inside: avoid;
    }

    blockquote {
        page-break-inside: avoid;
    }
}`;
  }

  /**
   * Generate CSS for specific font definition
   */
  generateFontCSS(font: FontDefinition): string {
    return `
    font-family: ${font.family};
    font-size: ${font.size}pt;
    font-weight: ${font.weight};
    font-style: ${font.style};
    ${font.lineHeight ? `line-height: ${font.lineHeight};` : ''}
    `.trim();
  }

  /**
   * Generate responsive CSS
   */
  generateResponsiveCSS(styling: DocumentStyling): string {
    return `
@media screen and (max-width: 768px) {
    .document-container {
        margin: 1em;
        padding: 1em;
    }

    .main-content {
        column-count: 1 !important;
    }

    .cover-title {
        font-size: 2em;
    }

    .cover-subtitle {
        font-size: 1.2em;
    }

    table {
        font-size: 0.8em;
    }

    pre {
        font-size: 0.8em;
        overflow-x: scroll;
    }
}

@media screen and (max-width: 480px) {
    .document-container {
        margin: 0.5em;
        padding: 0.5em;
    }

    .cover-title {
        font-size: 1.5em;
    }

    .toc-entry {
        flex-direction: column;
        align-items: flex-start;
    }

    .toc-dots {
        display: none;
    }
}`;
  }

  /**
   * Validate styling configuration
   */
  validateStyling(styling: DocumentStyling): string[] {
    const errors: string[] = [];

    // Validate fonts
    if (!styling.fonts?.primary) {
      errors.push('Primary font is required');
    }

    // Validate colors
    if (!styling.colors?.background || !styling.colors?.text) {
      errors.push('Background and text colors are required');
    }

    // Validate layout
    if (!styling.layout?.pageSize) {
      errors.push('Page size is required');
    }

    return errors;
  }
}
