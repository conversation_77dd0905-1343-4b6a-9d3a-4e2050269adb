/**
 * Template Renderer
 * 
 * Renders document templates with dynamic content, supporting multiple
 * template types and custom sections.
 */

import { 
  DocumentTemplate, 
  PDFDocument, 
  ParsedMarkdown,
  TemplateSection,
  TableOfContentsEntry
} from '../types';
import { builtInTemplates } from '../config';

export interface TemplateContext {
  document: PDFDocument;
  parsedMarkdown: ParsedMarkdown;
  metadata: Record<string, any>;
}

export class TemplateRenderer {
  /**
   * Render template with context
   */
  async render(template: DocumentTemplate, context: TemplateContext): Promise<string> {
    try {
      const sections = template.sections.sort((a, b) => a.order - b.order);
      const renderedSections: string[] = [];

      for (const section of sections) {
        const renderedSection = await this.renderSection(section, context);
        if (renderedSection) {
          renderedSections.push(renderedSection);
        }
      }

      return renderedSections.join('\n\n');
    } catch (error) {
      throw new Error(`Template rendering failed: ${error.message}`);
    }
  }

  /**
   * Render individual section
   */
  private async renderSection(section: TemplateSection, context: TemplateContext): Promise<string> {
    const { document, parsedMarkdown, metadata } = context;

    switch (section.type) {
      case 'cover-page':
        return this.renderCoverPage(section, document, metadata);
      
      case 'table-of-contents':
        return this.renderTableOfContents(section, parsedMarkdown.toc);
      
      case 'abstract':
        return this.renderAbstract(section, document, metadata);
      
      case 'introduction':
        return this.renderIntroduction(section, document);
      
      case 'content':
        return this.renderContent(section, parsedMarkdown);
      
      case 'conclusion':
        return this.renderConclusion(section, document);
      
      case 'references':
        return this.renderReferences(section, parsedMarkdown);
      
      case 'appendix':
        return this.renderAppendix(section, document);
      
      case 'custom':
        return this.renderCustomSection(section, context);
      
      default:
        console.warn(`Unknown section type: ${section.type}`);
        return '';
    }
  }

  /**
   * Render cover page
   */
  private renderCoverPage(section: TemplateSection, document: PDFDocument, metadata: Record<string, any>): string {
    const title = document.title || metadata.title || 'Untitled Document';
    const author = metadata.author || 'Unknown Author';
    const date = metadata.date || new Date().toLocaleDateString();
    const subtitle = metadata.subtitle || '';
    const organization = metadata.organization || '';

    return `
<div class="cover-page">
    <div class="cover-content">
        <h1 class="cover-title">${title}</h1>
        ${subtitle ? `<h2 class="cover-subtitle">${subtitle}</h2>` : ''}
        <div class="cover-author">${author}</div>
        ${organization ? `<div class="cover-organization">${organization}</div>` : ''}
        <div class="cover-date">${date}</div>
    </div>
</div>`;
  }

  /**
   * Render table of contents
   */
  private renderTableOfContents(section: TemplateSection, toc: TableOfContentsEntry[]): string {
    if (!toc || toc.length === 0) return '';

    const tocHtml = this.buildTOCHtml(toc);
    
    return `
<div class="toc page-break">
    <h1 class="toc-title">Table of Contents</h1>
    <div class="toc-content">
        ${tocHtml}
    </div>
</div>`;
  }

  /**
   * Build hierarchical TOC HTML
   */
  private buildTOCHtml(entries: TableOfContentsEntry[], level = 1): string {
    return entries.map(entry => {
      const indent = 'toc-level-' + level;
      const children = entry.children && entry.children.length > 0 
        ? this.buildTOCHtml(entry.children, level + 1)
        : '';

      return `
        <div class="toc-entry ${indent}">
            <span class="toc-title">${entry.title}</span>
            <span class="toc-dots"></span>
            <span class="toc-page">${entry.page || ''}</span>
        </div>
        ${children}
      `;
    }).join('');
  }

  /**
   * Render abstract section
   */
  private renderAbstract(section: TemplateSection, document: PDFDocument, metadata: Record<string, any>): string {
    const abstract = metadata.abstract || section.content || '';
    if (!abstract) return '';

    return `
<div class="abstract-section">
    <h1>Abstract</h1>
    <div class="abstract-content">
        ${abstract}
    </div>
</div>`;
  }

  /**
   * Render introduction section
   */
  private renderIntroduction(section: TemplateSection, document: PDFDocument): string {
    // Extract introduction from content or use section content
    const content = this.extractSectionContent(document.content, 'introduction') || section.content || '';
    if (!content) return '';

    return `
<div class="introduction-section">
    <h1>Introduction</h1>
    <div class="introduction-content">
        ${content}
    </div>
</div>`;
  }

  /**
   * Render main content
   */
  private renderContent(section: TemplateSection, parsedMarkdown: ParsedMarkdown): string {
    return `
<div class="main-content">
    ${parsedMarkdown.html}
</div>`;
  }

  /**
   * Render conclusion section
   */
  private renderConclusion(section: TemplateSection, document: PDFDocument): string {
    const content = this.extractSectionContent(document.content, 'conclusion') || section.content || '';
    if (!content) return '';

    return `
<div class="conclusion-section">
    <h1>Conclusion</h1>
    <div class="conclusion-content">
        ${content}
    </div>
</div>`;
  }

  /**
   * Render references section
   */
  private renderReferences(section: TemplateSection, parsedMarkdown: ParsedMarkdown): string {
    const references = this.extractReferences(parsedMarkdown);
    if (references.length === 0) return '';

    const referencesHtml = references.map((ref, index) => 
      `<div class="reference-item">[${index + 1}] ${ref}</div>`
    ).join('');

    return `
<div class="references-section">
    <h1>References</h1>
    <div class="references-content">
        ${referencesHtml}
    </div>
</div>`;
  }

  /**
   * Render appendix section
   */
  private renderAppendix(section: TemplateSection, document: PDFDocument): string {
    const content = this.extractSectionContent(document.content, 'appendix') || section.content || '';
    if (!content) return '';

    return `
<div class="appendix-section">
    <h1>Appendix</h1>
    <div class="appendix-content">
        ${content}
    </div>
</div>`;
  }

  /**
   * Render custom section
   */
  private renderCustomSection(section: TemplateSection, context: TemplateContext): string {
    const content = section.content || '';
    
    // Process template variables
    const processedContent = this.processTemplateVariables(content, context);

    return `
<div class="custom-section" data-section-id="${section.id}">
    <h1>${section.name}</h1>
    <div class="custom-content">
        ${processedContent}
    </div>
</div>`;
  }

  /**
   * Extract section content from markdown
   */
  private extractSectionContent(markdown: string, sectionName: string): string {
    const sectionRegex = new RegExp(`^#+\\s*${sectionName}\\s*$`, 'im');
    const lines = markdown.split('\n');
    
    let startIndex = -1;
    let endIndex = lines.length;
    
    // Find section start
    for (let i = 0; i < lines.length; i++) {
      if (sectionRegex.test(lines[i])) {
        startIndex = i + 1;
        break;
      }
    }
    
    if (startIndex === -1) return '';
    
    // Find section end (next heading of same or higher level)
    const startLevel = this.getHeadingLevel(lines[startIndex - 1]);
    for (let i = startIndex; i < lines.length; i++) {
      const level = this.getHeadingLevel(lines[i]);
      if (level > 0 && level <= startLevel) {
        endIndex = i;
        break;
      }
    }
    
    return lines.slice(startIndex, endIndex).join('\n').trim();
  }

  /**
   * Get heading level from line
   */
  private getHeadingLevel(line: string): number {
    const match = line.match(/^(#+)\s/);
    return match ? match[1].length : 0;
  }

  /**
   * Extract references from parsed markdown
   */
  private extractReferences(parsedMarkdown: ParsedMarkdown): string[] {
    const references: string[] = [];
    
    // Extract from links
    parsedMarkdown.links.forEach(link => {
      if (link.href.startsWith('http')) {
        references.push(`${link.text} - ${link.href}`);
      }
    });
    
    // Extract from reference-style links in HTML
    const refRegex = /\[(\d+)\]:\s*(.+)/g;
    let match;
    while ((match = refRegex.exec(parsedMarkdown.html)) !== null) {
      references.push(match[2]);
    }
    
    return [...new Set(references)]; // Remove duplicates
  }

  /**
   * Process template variables in content
   */
  private processTemplateVariables(content: string, context: TemplateContext): string {
    const { document, metadata } = context;
    
    let processed = content;
    
    // Replace document variables
    processed = processed.replace(/\{\{document\.title\}\}/g, document.title);
    processed = processed.replace(/\{\{document\.author\}\}/g, metadata.author || '');
    processed = processed.replace(/\{\{document\.date\}\}/g, new Date().toLocaleDateString());
    
    // Replace metadata variables
    for (const [key, value] of Object.entries(metadata)) {
      const regex = new RegExp(`\\{\\{metadata\\.${key}\\}\\}`, 'g');
      processed = processed.replace(regex, String(value));
    }
    
    // Replace current date/time
    processed = processed.replace(/\{\{now\}\}/g, new Date().toISOString());
    processed = processed.replace(/\{\{date\}\}/g, new Date().toLocaleDateString());
    processed = processed.replace(/\{\{time\}\}/g, new Date().toLocaleTimeString());
    
    return processed;
  }

  /**
   * Get template by ID
   */
  getTemplate(templateId: string): DocumentTemplate | null {
    const builtIn = builtInTemplates[templateId];
    if (!builtIn) return null;

    return {
      id: templateId,
      layout: {
        size: 'A4',
        orientation: 'portrait',
        margins: { top: 72, right: 72, bottom: 72, left: 72 },
      },
      styling: {},
      variables: {},
      ...builtIn,
    } as DocumentTemplate;
  }

  /**
   * List available templates
   */
  listTemplates(): Array<{ id: string; name: string; type: string }> {
    return Object.entries(builtInTemplates).map(([id, template]) => ({
      id,
      name: template.name || id,
      type: template.type || 'custom',
    }));
  }

  /**
   * Validate template
   */
  validateTemplate(template: DocumentTemplate): string[] {
    const errors: string[] = [];

    if (!template.id) {
      errors.push('Template ID is required');
    }

    if (!template.name) {
      errors.push('Template name is required');
    }

    if (!template.sections || template.sections.length === 0) {
      errors.push('Template must have at least one section');
    }

    // Check for required sections
    const requiredSections = template.sections.filter(s => s.required);
    if (requiredSections.length === 0) {
      errors.push('Template must have at least one required section');
    }

    // Check section order
    const orders = template.sections.map(s => s.order);
    const uniqueOrders = new Set(orders);
    if (orders.length !== uniqueOrders.size) {
      errors.push('Section orders must be unique');
    }

    return errors;
  }
}
