/**
 * Block Editor Types
 * 
 * Type definitions for the block-based editor system
 */

export type BlockType = 
  | 'paragraph'
  | 'heading'
  | 'code'
  | 'quote'
  | 'list'
  | 'table'
  | 'image'
  | 'divider'
  | 'embed'
  | 'math'
  | 'callout';

export type HeadingLevel = 1 | 2 | 3 | 4 | 5 | 6;
export type ListType = 'ordered' | 'unordered' | 'checklist';
export type CalloutType = 'info' | 'warning' | 'error' | 'success' | 'note';

export interface BaseBlock {
  id: string;
  type: BlockType;
  content: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ParagraphBlock extends BaseBlock {
  type: 'paragraph';
}

export interface HeadingBlock extends BaseBlock {
  type: 'heading';
  level: HeadingLevel;
}

export interface CodeBlock extends BaseBlock {
  type: 'code';
  language?: string;
  showLineNumbers?: boolean;
  filename?: string;
}

export interface QuoteBlock extends BaseBlock {
  type: 'quote';
  author?: string;
  source?: string;
}

export interface ListBlock extends BaseBlock {
  type: 'list';
  listType: ListType;
  items: ListItem[];
}

export interface ListItem {
  id: string;
  content: string;
  checked?: boolean; // for checklist items
  indent: number;
}

export interface TableBlock extends BaseBlock {
  type: 'table';
  headers: string[];
  rows: string[][];
  caption?: string;
}

export interface ImageBlock extends BaseBlock {
  type: 'image';
  src: string;
  alt: string;
  caption?: string;
  width?: number;
  height?: number;
  alignment?: 'left' | 'center' | 'right';
}

export interface DividerBlock extends BaseBlock {
  type: 'divider';
  style?: 'solid' | 'dashed' | 'dotted';
}

export interface EmbedBlock extends BaseBlock {
  type: 'embed';
  url: string;
  embedType: 'youtube' | 'vimeo' | 'codepen' | 'figma' | 'miro' | 'generic';
  title?: string;
  description?: string;
}

export interface MathBlock extends BaseBlock {
  type: 'math';
  formula: string;
  displayMode: 'inline' | 'block';
}

export interface CalloutBlock extends BaseBlock {
  type: 'callout';
  calloutType: CalloutType;
  title?: string;
  icon?: string;
}

export type Block = 
  | ParagraphBlock
  | HeadingBlock
  | CodeBlock
  | QuoteBlock
  | ListBlock
  | TableBlock
  | ImageBlock
  | DividerBlock
  | EmbedBlock
  | MathBlock
  | CalloutBlock;

export interface BlockPosition {
  blockId: string;
  index: number;
}

export interface BlockSelection {
  start: BlockPosition;
  end?: BlockPosition;
  isRange: boolean;
}

export interface BlockOperation {
  type: 'insert' | 'delete' | 'move' | 'update' | 'convert';
  blockId: string;
  data?: any;
  position?: number;
  targetPosition?: number;
}

export interface BlockValidationError {
  blockId: string;
  field?: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface BlockEditorState {
  blocks: Block[];
  selection: BlockSelection | null;
  focusedBlockId: string | null;
  isEditing: boolean;
  draggedBlockId: string | null;
  clipboard: Block | null;
  history: {
    past: Block[][];
    present: Block[];
    future: Block[][];
  };
}

export interface BlockToolbarAction {
  id: string;
  label: string;
  icon: string;
  action: (block: Block) => void;
  isActive?: (block: Block) => boolean;
  isDisabled?: (block: Block) => boolean;
  shortcut?: string;
}

export interface BlockTypeDefinition {
  type: BlockType;
  label: string;
  icon: string;
  description: string;
  defaultContent: string;
  category: 'text' | 'media' | 'structure' | 'advanced';
  canConvertTo: BlockType[];
  canConvertFrom: BlockType[];
  toolbarActions: BlockToolbarAction[];
  validation?: (block: Block) => BlockValidationError[];
}

export interface BlockEditorConfig {
  allowedBlockTypes: BlockType[];
  maxBlocks?: number;
  enableDragDrop: boolean;
  enableKeyboardShortcuts: boolean;
  autoSave: boolean;
  autoSaveInterval: number;
  placeholder: string;
  theme: 'light' | 'dark' | 'auto';
}

export interface BlockEditorCallbacks {
  onBlockAdd?: (block: Block, position: number) => void;
  onBlockDelete?: (blockId: string) => void;
  onBlockUpdate?: (block: Block) => void;
  onBlockMove?: (blockId: string, fromIndex: number, toIndex: number) => void;
  onBlockConvert?: (blockId: string, fromType: BlockType, toType: BlockType) => void;
  onSelectionChange?: (selection: BlockSelection | null) => void;
  onFocusChange?: (blockId: string | null) => void;
  onChange?: (blocks: Block[]) => void;
  onValidationChange?: (errors: BlockValidationError[]) => void;
}

// Utility types for block creation
export type CreateBlockData<T extends BlockType> = 
  T extends 'paragraph' ? Partial<ParagraphBlock> :
  T extends 'heading' ? Partial<HeadingBlock> :
  T extends 'code' ? Partial<CodeBlock> :
  T extends 'quote' ? Partial<QuoteBlock> :
  T extends 'list' ? Partial<ListBlock> :
  T extends 'table' ? Partial<TableBlock> :
  T extends 'image' ? Partial<ImageBlock> :
  T extends 'divider' ? Partial<DividerBlock> :
  T extends 'embed' ? Partial<EmbedBlock> :
  T extends 'math' ? Partial<MathBlock> :
  T extends 'callout' ? Partial<CalloutBlock> :
  Partial<BaseBlock>;

// Block factory function type
export type BlockFactory = {
  [K in BlockType]: (data?: CreateBlockData<K>) => Extract<Block, { type: K }>;
};

// Export utility functions
export interface BlockUtils {
  generateId: () => string;
  createBlock: <T extends BlockType>(type: T, data?: CreateBlockData<T>) => Extract<Block, { type: T }>;
  duplicateBlock: (block: Block) => Block;
  convertBlock: <T extends BlockType>(block: Block, targetType: T) => Extract<Block, { type: T }> | null;
  validateBlock: (block: Block) => BlockValidationError[];
  serializeBlocks: (blocks: Block[]) => string;
  deserializeBlocks: (content: string) => Block[];
  blocksToMarkdown: (blocks: Block[]) => string;
  markdownToBlocks: (markdown: string) => Block[];
}
