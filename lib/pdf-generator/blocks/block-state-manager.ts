/**
 * Block State Manager
 * 
 * Manages the four-state lifecycle for blocks: Preview, Edit, Render, Print
 */

import { Block, BlockType } from './types';

export type BlockState = 'preview' | 'edit' | 'render' | 'print';

export interface BlockStateConfig {
  allowedTransitions: BlockState[];
  autoSave: boolean;
  validateOnTransition: boolean;
  persistState: boolean;
}

export interface BlockStateContext {
  blockId: string;
  currentState: BlockState;
  previousState: BlockState | null;
  stateHistory: BlockState[];
  stateData: Record<BlockState, any>;
  transitionCallbacks: Record<string, Function[]>;
  config: BlockStateConfig;
}

export interface StateTransitionOptions {
  force?: boolean;
  skipValidation?: boolean;
  preserveData?: boolean;
  callback?: (success: boolean, error?: string) => void;
}

export interface BlockStateManagerOptions {
  defaultState?: BlockState;
  globalConfig?: Partial<BlockStateConfig>;
  enableStateHistory?: boolean;
  maxHistorySize?: number;
}

/**
 * Block State Manager
 * Handles state transitions and lifecycle management for blocks
 */
export class BlockStateManager {
  private contexts: Map<string, BlockStateContext> = new Map();
  private globalCallbacks: Map<string, Function[]> = new Map();
  private options: BlockStateManagerOptions;

  constructor(options: BlockStateManagerOptions = {}) {
    this.options = {
      defaultState: 'preview',
      enableStateHistory: true,
      maxHistorySize: 10,
      globalConfig: {
        allowedTransitions: ['preview', 'edit', 'render', 'print'],
        autoSave: true,
        validateOnTransition: true,
        persistState: false,
      },
      ...options,
    };
  }

  /**
   * Initialize block state context
   */
  public initializeBlock(
    blockId: string, 
    initialState?: BlockState,
    config?: Partial<BlockStateConfig>
  ): BlockStateContext {
    const state = initialState || this.options.defaultState || 'preview';
    
    const context: BlockStateContext = {
      blockId,
      currentState: state,
      previousState: null,
      stateHistory: [state],
      stateData: {
        preview: {},
        edit: {},
        render: {},
        print: {},
      },
      transitionCallbacks: {},
      config: {
        ...this.options.globalConfig!,
        ...config,
      },
    };

    this.contexts.set(blockId, context);
    return context;
  }

  /**
   * Get block state context
   */
  public getContext(blockId: string): BlockStateContext | null {
    return this.contexts.get(blockId) || null;
  }

  /**
   * Get current state of a block
   */
  public getCurrentState(blockId: string): BlockState | null {
    const context = this.contexts.get(blockId);
    return context?.currentState || null;
  }

  /**
   * Check if transition is allowed
   */
  public canTransition(blockId: string, toState: BlockState): boolean {
    const context = this.contexts.get(blockId);
    if (!context) return false;

    return context.config.allowedTransitions.includes(toState);
  }

  /**
   * Transition block to new state
   */
  public async transitionTo(
    blockId: string, 
    toState: BlockState, 
    options: StateTransitionOptions = {}
  ): Promise<boolean> {
    const context = this.contexts.get(blockId);
    if (!context) {
      options.callback?.(false, 'Block context not found');
      return false;
    }

    // Check if transition is allowed
    if (!options.force && !this.canTransition(blockId, toState)) {
      options.callback?.(false, `Transition from ${context.currentState} to ${toState} not allowed`);
      return false;
    }

    // Validate current state before transition
    if (!options.skipValidation && context.config.validateOnTransition) {
      const isValid = await this.validateState(blockId, context.currentState);
      if (!isValid) {
        options.callback?.(false, 'Current state validation failed');
        return false;
      }
    }

    try {
      // Execute pre-transition callbacks
      await this.executeCallbacks(`${blockId}:before:${toState}`, context);
      await this.executeCallbacks(`global:before:${toState}`, context);

      // Perform state transition
      const previousState = context.currentState;
      context.previousState = previousState;
      context.currentState = toState;

      // Update state history
      if (this.options.enableStateHistory) {
        context.stateHistory.push(toState);
        if (context.stateHistory.length > (this.options.maxHistorySize || 10)) {
          context.stateHistory.shift();
        }
      }

      // Auto-save if enabled
      if (context.config.autoSave) {
        await this.saveStateData(blockId, previousState);
      }

      // Execute post-transition callbacks
      await this.executeCallbacks(`${blockId}:after:${toState}`, context);
      await this.executeCallbacks(`global:after:${toState}`, context);

      options.callback?.(true);
      return true;

    } catch (error) {
      // Rollback on error
      context.currentState = context.previousState || context.currentState;
      options.callback?.(false, error.message);
      return false;
    }
  }

  /**
   * Get state-specific data
   */
  public getStateData(blockId: string, state?: BlockState): any {
    const context = this.contexts.get(blockId);
    if (!context) return null;

    const targetState = state || context.currentState;
    return context.stateData[targetState];
  }

  /**
   * Set state-specific data
   */
  public setStateData(blockId: string, data: any, state?: BlockState): void {
    const context = this.contexts.get(blockId);
    if (!context) return;

    const targetState = state || context.currentState;
    context.stateData[targetState] = { ...context.stateData[targetState], ...data };
  }

  /**
   * Register state transition callback
   */
  public onStateTransition(
    blockId: string | 'global',
    event: 'before' | 'after',
    state: BlockState,
    callback: Function
  ): void {
    const key = `${blockId}:${event}:${state}`;
    
    if (blockId === 'global') {
      if (!this.globalCallbacks.has(key)) {
        this.globalCallbacks.set(key, []);
      }
      this.globalCallbacks.get(key)!.push(callback);
    } else {
      const context = this.contexts.get(blockId);
      if (context) {
        if (!context.transitionCallbacks[key]) {
          context.transitionCallbacks[key] = [];
        }
        context.transitionCallbacks[key].push(callback);
      }
    }
  }

  /**
   * Get state-specific styling/classes
   */
  public getStateClasses(blockId: string, baseClasses: string = ''): string {
    const context = this.contexts.get(blockId);
    if (!context) return baseClasses;

    const stateClasses = {
      preview: 'block-state-preview cursor-pointer hover:bg-gray-50',
      edit: 'block-state-edit ring-2 ring-blue-500 bg-white',
      render: 'block-state-render print:block hidden',
      print: 'block-state-print print-optimized',
    };

    return `${baseClasses} ${stateClasses[context.currentState]}`.trim();
  }

  /**
   * Get state-specific configuration
   */
  public getStateConfig(blockId: string, state?: BlockState): any {
    const context = this.contexts.get(blockId);
    if (!context) return {};

    const targetState = state || context.currentState;
    
    const stateConfigs = {
      preview: {
        interactive: true,
        showToolbar: false,
        allowSelection: true,
        optimizeForReading: true,
      },
      edit: {
        interactive: true,
        showToolbar: true,
        allowSelection: true,
        focusOnMount: true,
        autoSave: true,
      },
      render: {
        interactive: false,
        showToolbar: false,
        allowSelection: false,
        optimizeForExport: true,
        includeMetadata: true,
      },
      print: {
        interactive: false,
        showToolbar: false,
        allowSelection: false,
        optimizeForPrint: true,
        pageBreakAware: true,
      },
    };

    return stateConfigs[targetState];
  }

  /**
   * Batch transition multiple blocks
   */
  public async batchTransition(
    blockIds: string[],
    toState: BlockState,
    options: StateTransitionOptions = {}
  ): Promise<{ success: string[]; failed: string[] }> {
    const results = await Promise.allSettled(
      blockIds.map(id => this.transitionTo(id, toState, options))
    );

    const success: string[] = [];
    const failed: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        success.push(blockIds[index]);
      } else {
        failed.push(blockIds[index]);
      }
    });

    return { success, failed };
  }

  /**
   * Cleanup block context
   */
  public cleanup(blockId: string): void {
    this.contexts.delete(blockId);
  }

  /**
   * Get all blocks in specific state
   */
  public getBlocksInState(state: BlockState): string[] {
    const blockIds: string[] = [];
    
    for (const [blockId, context] of this.contexts) {
      if (context.currentState === state) {
        blockIds.push(blockId);
      }
    }

    return blockIds;
  }

  // Private methods
  private async executeCallbacks(key: string, context: BlockStateContext): Promise<void> {
    // Execute block-specific callbacks
    const blockCallbacks = context.transitionCallbacks[key] || [];
    for (const callback of blockCallbacks) {
      await callback(context);
    }

    // Execute global callbacks
    const globalCallbacks = this.globalCallbacks.get(key) || [];
    for (const callback of globalCallbacks) {
      await callback(context);
    }
  }

  private async validateState(blockId: string, state: BlockState): Promise<boolean> {
    // Implement state-specific validation logic
    const context = this.contexts.get(blockId);
    if (!context) return false;

    // Add validation logic based on state
    switch (state) {
      case 'edit':
        // Validate edit state data
        return true;
      case 'render':
        // Validate render readiness
        return true;
      case 'print':
        // Validate print formatting
        return true;
      default:
        return true;
    }
  }

  private async saveStateData(blockId: string, state: BlockState): Promise<void> {
    // Implement auto-save logic
    const context = this.contexts.get(blockId);
    if (!context || !context.config.persistState) return;

    // Save state data to storage (localStorage, IndexedDB, etc.)
    const stateData = context.stateData[state];
    if (stateData && Object.keys(stateData).length > 0) {
      localStorage.setItem(`block-state-${blockId}-${state}`, JSON.stringify(stateData));
    }
  }
}

// Export singleton instance
export const blockStateManager = new BlockStateManager();

// Export utility functions
export function createStateManager(options?: BlockStateManagerOptions): BlockStateManager {
  return new BlockStateManager(options);
}

export function getBlockState(blockId: string): BlockState | null {
  return blockStateManager.getCurrentState(blockId);
}

export function transitionBlockState(
  blockId: string, 
  toState: BlockState, 
  options?: StateTransitionOptions
): Promise<boolean> {
  return blockStateManager.transitionTo(blockId, toState, options);
}
