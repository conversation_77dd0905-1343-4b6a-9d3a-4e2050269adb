/**
 * Block Definitions
 * 
 * Definitions for all block types including metadata, toolbar actions,
 * and conversion rules.
 */

import { BlockTypeDefinition, BlockType, Block } from './types';

export const blockDefinitions: Record<BlockType, BlockTypeDefinition> = {
  paragraph: {
    type: 'paragraph',
    label: 'Paragraph',
    icon: '¶',
    description: 'Basic text paragraph',
    defaultContent: 'Type your text here...',
    category: 'text',
    canConvertTo: ['heading', 'quote', 'code', 'callout'],
    canConvertFrom: ['heading', 'quote', 'code', 'callout'],
    toolbarActions: [
      {
        id: 'bold',
        label: 'Bold',
        icon: 'B',
        action: (block) => console.log('Bold action', block),
        shortcut: 'Cmd+B',
      },
      {
        id: 'italic',
        label: 'Italic',
        icon: 'I',
        action: (block) => console.log('Italic action', block),
        shortcut: 'Cmd+I',
      },
      {
        id: 'link',
        label: 'Link',
        icon: '🔗',
        action: (block) => console.log('Link action', block),
        shortcut: 'Cmd+K',
      },
    ],
  },

  heading: {
    type: 'heading',
    label: 'Heading',
    icon: 'H',
    description: 'Section heading (H1-H6)',
    defaultContent: 'Heading',
    category: 'text',
    canConvertTo: ['paragraph', 'quote'],
    canConvertFrom: ['paragraph', 'quote'],
    toolbarActions: [
      {
        id: 'level-1',
        label: 'H1',
        icon: 'H1',
        action: (block) => console.log('H1 action', block),
        isActive: (block) => (block as any).level === 1,
      },
      {
        id: 'level-2',
        label: 'H2',
        icon: 'H2',
        action: (block) => console.log('H2 action', block),
        isActive: (block) => (block as any).level === 2,
      },
      {
        id: 'level-3',
        label: 'H3',
        icon: 'H3',
        action: (block) => console.log('H3 action', block),
        isActive: (block) => (block as any).level === 3,
      },
    ],
  },

  code: {
    type: 'code',
    label: 'Code Block',
    icon: '</>', 
    description: 'Code with syntax highlighting',
    defaultContent: 'console.log("Hello, World!");',
    category: 'text',
    canConvertTo: ['paragraph'],
    canConvertFrom: ['paragraph'],
    toolbarActions: [
      {
        id: 'language',
        label: 'Language',
        icon: '🔤',
        action: (block) => console.log('Language action', block),
      },
      {
        id: 'line-numbers',
        label: 'Line Numbers',
        icon: '#',
        action: (block) => console.log('Line numbers action', block),
        isActive: (block) => (block as any).showLineNumbers,
      },
      {
        id: 'copy',
        label: 'Copy',
        icon: '📋',
        action: (block) => console.log('Copy action', block),
      },
    ],
  },

  quote: {
    type: 'quote',
    label: 'Quote',
    icon: '"',
    description: 'Blockquote with optional attribution',
    defaultContent: 'Quote text goes here...',
    category: 'text',
    canConvertTo: ['paragraph', 'heading'],
    canConvertFrom: ['paragraph', 'heading'],
    toolbarActions: [
      {
        id: 'author',
        label: 'Add Author',
        icon: '👤',
        action: (block) => console.log('Author action', block),
      },
      {
        id: 'source',
        label: 'Add Source',
        icon: '📖',
        action: (block) => console.log('Source action', block),
      },
    ],
  },

  list: {
    type: 'list',
    label: 'List',
    icon: '•',
    description: 'Bulleted, numbered, or checklist',
    defaultContent: '',
    category: 'text',
    canConvertTo: ['paragraph'],
    canConvertFrom: ['paragraph'],
    toolbarActions: [
      {
        id: 'unordered',
        label: 'Bullet List',
        icon: '•',
        action: (block) => console.log('Unordered list action', block),
        isActive: (block) => (block as any).listType === 'unordered',
      },
      {
        id: 'ordered',
        label: 'Numbered List',
        icon: '1.',
        action: (block) => console.log('Ordered list action', block),
        isActive: (block) => (block as any).listType === 'ordered',
      },
      {
        id: 'checklist',
        label: 'Checklist',
        icon: '☑',
        action: (block) => console.log('Checklist action', block),
        isActive: (block) => (block as any).listType === 'checklist',
      },
      {
        id: 'indent',
        label: 'Indent',
        icon: '→',
        action: (block) => console.log('Indent action', block),
        shortcut: 'Tab',
      },
      {
        id: 'outdent',
        label: 'Outdent',
        icon: '←',
        action: (block) => console.log('Outdent action', block),
        shortcut: 'Shift+Tab',
      },
    ],
  },

  table: {
    type: 'table',
    label: 'Table',
    icon: '⊞',
    description: 'Data table with headers',
    defaultContent: '',
    category: 'structure',
    canConvertTo: [],
    canConvertFrom: [],
    toolbarActions: [
      {
        id: 'add-row',
        label: 'Add Row',
        icon: '➕',
        action: (block) => console.log('Add row action', block),
      },
      {
        id: 'add-column',
        label: 'Add Column',
        icon: '⊞',
        action: (block) => console.log('Add column action', block),
      },
      {
        id: 'delete-row',
        label: 'Delete Row',
        icon: '➖',
        action: (block) => console.log('Delete row action', block),
      },
      {
        id: 'delete-column',
        label: 'Delete Column',
        icon: '⊟',
        action: (block) => console.log('Delete column action', block),
      },
    ],
  },

  image: {
    type: 'image',
    label: 'Image',
    icon: '🖼',
    description: 'Image with caption and alignment',
    defaultContent: '',
    category: 'media',
    canConvertTo: [],
    canConvertFrom: [],
    toolbarActions: [
      {
        id: 'upload',
        label: 'Upload',
        icon: '📁',
        action: (block) => console.log('Upload action', block),
      },
      {
        id: 'url',
        label: 'From URL',
        icon: '🔗',
        action: (block) => console.log('URL action', block),
      },
      {
        id: 'align-left',
        label: 'Align Left',
        icon: '⬅',
        action: (block) => console.log('Align left action', block),
        isActive: (block) => (block as any).alignment === 'left',
      },
      {
        id: 'align-center',
        label: 'Align Center',
        icon: '⬌',
        action: (block) => console.log('Align center action', block),
        isActive: (block) => (block as any).alignment === 'center',
      },
      {
        id: 'align-right',
        label: 'Align Right',
        icon: '➡',
        action: (block) => console.log('Align right action', block),
        isActive: (block) => (block as any).alignment === 'right',
      },
    ],
  },

  divider: {
    type: 'divider',
    label: 'Divider',
    icon: '—',
    description: 'Horizontal line separator',
    defaultContent: '',
    category: 'structure',
    canConvertTo: [],
    canConvertFrom: [],
    toolbarActions: [
      {
        id: 'solid',
        label: 'Solid',
        icon: '—',
        action: (block) => console.log('Solid divider action', block),
        isActive: (block) => (block as any).style === 'solid',
      },
      {
        id: 'dashed',
        label: 'Dashed',
        icon: '- -',
        action: (block) => console.log('Dashed divider action', block),
        isActive: (block) => (block as any).style === 'dashed',
      },
      {
        id: 'dotted',
        label: 'Dotted',
        icon: '• •',
        action: (block) => console.log('Dotted divider action', block),
        isActive: (block) => (block as any).style === 'dotted',
      },
    ],
  },

  embed: {
    type: 'embed',
    label: 'Embed',
    icon: '🔗',
    description: 'Embedded content from external sources',
    defaultContent: '',
    category: 'media',
    canConvertTo: [],
    canConvertFrom: [],
    toolbarActions: [
      {
        id: 'url',
        label: 'Change URL',
        icon: '🔗',
        action: (block) => console.log('Change URL action', block),
      },
      {
        id: 'refresh',
        label: 'Refresh',
        icon: '🔄',
        action: (block) => console.log('Refresh action', block),
      },
    ],
  },

  math: {
    type: 'math',
    label: 'Math',
    icon: '∑',
    description: 'Mathematical equations and formulas',
    defaultContent: 'E = mc^2',
    category: 'advanced',
    canConvertTo: ['paragraph'],
    canConvertFrom: ['paragraph'],
    toolbarActions: [
      {
        id: 'inline',
        label: 'Inline',
        icon: '$',
        action: (block) => console.log('Inline math action', block),
        isActive: (block) => (block as any).displayMode === 'inline',
      },
      {
        id: 'block',
        label: 'Block',
        icon: '$$',
        action: (block) => console.log('Block math action', block),
        isActive: (block) => (block as any).displayMode === 'block',
      },
      {
        id: 'preview',
        label: 'Preview',
        icon: '👁',
        action: (block) => console.log('Preview math action', block),
      },
    ],
  },

  callout: {
    type: 'callout',
    label: 'Callout',
    icon: '💡',
    description: 'Highlighted information box',
    defaultContent: 'Important information goes here...',
    category: 'text',
    canConvertTo: ['paragraph', 'quote'],
    canConvertFrom: ['paragraph', 'quote'],
    toolbarActions: [
      {
        id: 'info',
        label: 'Info',
        icon: 'ℹ️',
        action: (block) => console.log('Info callout action', block),
        isActive: (block) => (block as any).calloutType === 'info',
      },
      {
        id: 'warning',
        label: 'Warning',
        icon: '⚠️',
        action: (block) => console.log('Warning callout action', block),
        isActive: (block) => (block as any).calloutType === 'warning',
      },
      {
        id: 'error',
        label: 'Error',
        icon: '❌',
        action: (block) => console.log('Error callout action', block),
        isActive: (block) => (block as any).calloutType === 'error',
      },
      {
        id: 'success',
        label: 'Success',
        icon: '✅',
        action: (block) => console.log('Success callout action', block),
        isActive: (block) => (block as any).calloutType === 'success',
      },
    ],
  },
};

/**
 * Get block definition by type
 */
export function getBlockDefinition(type: BlockType): BlockTypeDefinition {
  return blockDefinitions[type];
}

/**
 * Get all block definitions by category
 */
export function getBlockDefinitionsByCategory(): Record<string, BlockTypeDefinition[]> {
  const categories: Record<string, BlockTypeDefinition[]> = {};
  
  Object.values(blockDefinitions).forEach(definition => {
    if (!categories[definition.category]) {
      categories[definition.category] = [];
    }
    categories[definition.category].push(definition);
  });

  return categories;
}

/**
 * Get available conversion targets for a block type
 */
export function getConversionTargets(type: BlockType): BlockType[] {
  return blockDefinitions[type]?.canConvertTo || [];
}

/**
 * Check if conversion is allowed
 */
export function canConvertBlock(fromType: BlockType, toType: BlockType): boolean {
  return blockDefinitions[fromType]?.canConvertTo.includes(toType) || false;
}
