/**
 * Block Utilities
 * 
 * Utility functions for creating, manipulating, and converting blocks
 */

import { 
  Block, 
  BlockType, 
  CreateBlockData, 
  BlockValidationError,
  ParagraphBlock,
  HeadingBlock,
  CodeBlock,
  QuoteBlock,
  ListBlock,
  TableBlock,
  ImageBlock,
  DividerBlock,
  EmbedBlock,
  MathBlock,
  CalloutBlock,
  ListItem
} from './types';

/**
 * Generate unique block ID
 */
export function generateBlockId(): string {
  return `block_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Create a new block of specified type
 */
export function createBlock<T extends BlockType>(
  type: T, 
  data?: CreateBlockData<T>
): Extract<Block, { type: T }> {
  const baseBlock = {
    id: generateBlockId(),
    type,
    content: '',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...data,
  };

  switch (type) {
    case 'paragraph':
      return {
        ...baseBlock,
        type: 'paragraph',
        content: data?.content || '',
      } as Extract<Block, { type: T }>;

    case 'heading':
      return {
        ...baseBlock,
        type: 'heading',
        content: data?.content || 'Heading',
        level: (data as any)?.level || 1,
      } as Extract<Block, { type: T }>;

    case 'code':
      return {
        ...baseBlock,
        type: 'code',
        content: data?.content || '',
        language: (data as any)?.language || 'javascript',
        showLineNumbers: (data as any)?.showLineNumbers ?? true,
      } as Extract<Block, { type: T }>;

    case 'quote':
      return {
        ...baseBlock,
        type: 'quote',
        content: data?.content || 'Quote text',
        author: (data as any)?.author,
        source: (data as any)?.source,
      } as Extract<Block, { type: T }>;

    case 'list':
      return {
        ...baseBlock,
        type: 'list',
        content: '',
        listType: (data as any)?.listType || 'unordered',
        items: (data as any)?.items || [
          { id: generateBlockId(), content: 'List item', indent: 0 }
        ],
      } as Extract<Block, { type: T }>;

    case 'table':
      return {
        ...baseBlock,
        type: 'table',
        content: '',
        headers: (data as any)?.headers || ['Column 1', 'Column 2'],
        rows: (data as any)?.rows || [['Row 1 Col 1', 'Row 1 Col 2']],
        caption: (data as any)?.caption,
      } as Extract<Block, { type: T }>;

    case 'image':
      return {
        ...baseBlock,
        type: 'image',
        content: (data as any)?.src || '',
        src: (data as any)?.src || '',
        alt: (data as any)?.alt || 'Image',
        caption: (data as any)?.caption,
        alignment: (data as any)?.alignment || 'center',
      } as Extract<Block, { type: T }>;

    case 'divider':
      return {
        ...baseBlock,
        type: 'divider',
        content: '',
        style: (data as any)?.style || 'solid',
      } as Extract<Block, { type: T }>;

    case 'embed':
      return {
        ...baseBlock,
        type: 'embed',
        content: (data as any)?.url || '',
        url: (data as any)?.url || '',
        embedType: (data as any)?.embedType || 'generic',
        title: (data as any)?.title,
        description: (data as any)?.description,
      } as Extract<Block, { type: T }>;

    case 'math':
      return {
        ...baseBlock,
        type: 'math',
        content: (data as any)?.formula || 'E = mc^2',
        formula: (data as any)?.formula || 'E = mc^2',
        displayMode: (data as any)?.displayMode || 'block',
      } as Extract<Block, { type: T }>;

    case 'callout':
      return {
        ...baseBlock,
        type: 'callout',
        content: data?.content || 'Callout content',
        calloutType: (data as any)?.calloutType || 'info',
        title: (data as any)?.title,
        icon: (data as any)?.icon,
      } as Extract<Block, { type: T }>;

    default:
      throw new Error(`Unknown block type: ${type}`);
  }
}

/**
 * Duplicate a block with new ID
 */
export function duplicateBlock(block: Block): Block {
  return {
    ...block,
    id: generateBlockId(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

/**
 * Convert block to different type
 */
export function convertBlock<T extends BlockType>(
  block: Block, 
  targetType: T
): Extract<Block, { type: T }> | null {
  // Define conversion rules
  const conversionMap: Record<string, string[]> = {
    paragraph: ['heading', 'quote', 'code'],
    heading: ['paragraph', 'quote'],
    quote: ['paragraph', 'heading'],
    code: ['paragraph'],
    list: ['paragraph'],
    callout: ['paragraph', 'quote'],
  };

  if (!conversionMap[block.type]?.includes(targetType)) {
    return null; // Conversion not allowed
  }

  const baseData = {
    content: block.content,
    metadata: block.metadata,
  };

  try {
    return createBlock(targetType, baseData as any);
  } catch {
    return null;
  }
}

/**
 * Validate block content
 */
export function validateBlock(block: Block): BlockValidationError[] {
  const errors: BlockValidationError[] = [];

  switch (block.type) {
    case 'heading':
      const headingBlock = block as HeadingBlock;
      if (!headingBlock.content.trim()) {
        errors.push({
          blockId: block.id,
          field: 'content',
          message: 'Heading cannot be empty',
          severity: 'error',
        });
      }
      if (headingBlock.level < 1 || headingBlock.level > 6) {
        errors.push({
          blockId: block.id,
          field: 'level',
          message: 'Heading level must be between 1 and 6',
          severity: 'error',
        });
      }
      break;

    case 'image':
      const imageBlock = block as ImageBlock;
      if (!imageBlock.src) {
        errors.push({
          blockId: block.id,
          field: 'src',
          message: 'Image source is required',
          severity: 'error',
        });
      }
      if (!imageBlock.alt) {
        errors.push({
          blockId: block.id,
          field: 'alt',
          message: 'Alt text is recommended for accessibility',
          severity: 'warning',
        });
      }
      break;

    case 'embed':
      const embedBlock = block as EmbedBlock;
      if (!embedBlock.url) {
        errors.push({
          blockId: block.id,
          field: 'url',
          message: 'Embed URL is required',
          severity: 'error',
        });
      }
      break;

    case 'table':
      const tableBlock = block as TableBlock;
      if (tableBlock.headers.length === 0) {
        errors.push({
          blockId: block.id,
          field: 'headers',
          message: 'Table must have at least one column',
          severity: 'error',
        });
      }
      if (tableBlock.rows.length === 0) {
        errors.push({
          blockId: block.id,
          field: 'rows',
          message: 'Table must have at least one row',
          severity: 'warning',
        });
      }
      break;

    case 'list':
      const listBlock = block as ListBlock;
      if (listBlock.items.length === 0) {
        errors.push({
          blockId: block.id,
          field: 'items',
          message: 'List must have at least one item',
          severity: 'warning',
        });
      }
      break;

    case 'math':
      const mathBlock = block as MathBlock;
      if (!mathBlock.formula.trim()) {
        errors.push({
          blockId: block.id,
          field: 'formula',
          message: 'Math formula cannot be empty',
          severity: 'error',
        });
      }
      break;
  }

  return errors;
}

/**
 * Convert blocks to markdown
 */
export function blocksToMarkdown(blocks: Block[]): string {
  return blocks.map(blockToMarkdown).join('\n\n');
}

/**
 * Convert single block to markdown
 */
function blockToMarkdown(block: Block): string {
  switch (block.type) {
    case 'paragraph':
      return block.content;

    case 'heading':
      const headingBlock = block as HeadingBlock;
      return `${'#'.repeat(headingBlock.level)} ${block.content}`;

    case 'code':
      const codeBlock = block as CodeBlock;
      return `\`\`\`${codeBlock.language || ''}\n${block.content}\n\`\`\``;

    case 'quote':
      const quoteBlock = block as QuoteBlock;
      const quote = block.content.split('\n').map(line => `> ${line}`).join('\n');
      return quoteBlock.author ? `${quote}\n>\n> — ${quoteBlock.author}` : quote;

    case 'list':
      const listBlock = block as ListBlock;
      return listBlock.items.map((item, index) => {
        const indent = '  '.repeat(item.indent);
        const marker = listBlock.listType === 'ordered' ? `${index + 1}.` :
                      listBlock.listType === 'checklist' ? `- [${item.checked ? 'x' : ' '}]` : '-';
        return `${indent}${marker} ${item.content}`;
      }).join('\n');

    case 'table':
      const tableBlock = block as TableBlock;
      const headerRow = `| ${tableBlock.headers.join(' | ')} |`;
      const separatorRow = `| ${tableBlock.headers.map(() => '---').join(' | ')} |`;
      const dataRows = tableBlock.rows.map(row => `| ${row.join(' | ')} |`);
      return [headerRow, separatorRow, ...dataRows].join('\n');

    case 'image':
      const imageBlock = block as ImageBlock;
      return `![${imageBlock.alt}](${imageBlock.src}${imageBlock.caption ? ` "${imageBlock.caption}"` : ''})`;

    case 'divider':
      return '---';

    case 'embed':
      const embedBlock = block as EmbedBlock;
      return `[${embedBlock.title || 'Embed'}](${embedBlock.url})`;

    case 'math':
      const mathBlock = block as MathBlock;
      return mathBlock.displayMode === 'block' ? 
        `$$\n${mathBlock.formula}\n$$` : 
        `$${mathBlock.formula}$`;

    case 'callout':
      const calloutBlock = block as CalloutBlock;
      const icon = calloutBlock.icon || getCalloutIcon(calloutBlock.calloutType);
      const title = calloutBlock.title ? `**${calloutBlock.title}**\n` : '';
      return `> ${icon} ${title}${block.content}`;

    default:
      return block.content;
  }
}

/**
 * Get default icon for callout type
 */
function getCalloutIcon(type: string): string {
  const icons = {
    info: 'ℹ️',
    warning: '⚠️',
    error: '❌',
    success: '✅',
    note: '📝',
  };
  return icons[type as keyof typeof icons] || '📝';
}

/**
 * Convert markdown to blocks (basic implementation)
 */
export function markdownToBlocks(markdown: string): Block[] {
  const blocks: Block[] = [];
  const lines = markdown.split('\n');
  let currentBlock: string[] = [];
  let inCodeBlock = false;
  let codeLanguage = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Handle code blocks
    if (line.startsWith('```')) {
      if (inCodeBlock) {
        // End code block
        blocks.push(createBlock('code', {
          content: currentBlock.join('\n'),
          language: codeLanguage,
        }));
        currentBlock = [];
        inCodeBlock = false;
        codeLanguage = '';
      } else {
        // Start code block
        if (currentBlock.length > 0) {
          blocks.push(createBlock('paragraph', { content: currentBlock.join('\n') }));
          currentBlock = [];
        }
        inCodeBlock = true;
        codeLanguage = line.slice(3).trim();
      }
      continue;
    }

    if (inCodeBlock) {
      currentBlock.push(line);
      continue;
    }

    // Handle headings
    const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
    if (headingMatch) {
      if (currentBlock.length > 0) {
        blocks.push(createBlock('paragraph', { content: currentBlock.join('\n') }));
        currentBlock = [];
      }
      blocks.push(createBlock('heading', {
        content: headingMatch[2],
        level: headingMatch[1].length as any,
      }));
      continue;
    }

    // Handle horizontal rules
    if (line.match(/^---+$/)) {
      if (currentBlock.length > 0) {
        blocks.push(createBlock('paragraph', { content: currentBlock.join('\n') }));
        currentBlock = [];
      }
      blocks.push(createBlock('divider'));
      continue;
    }

    // Handle empty lines
    if (line.trim() === '') {
      if (currentBlock.length > 0) {
        blocks.push(createBlock('paragraph', { content: currentBlock.join('\n') }));
        currentBlock = [];
      }
      continue;
    }

    // Accumulate regular content
    currentBlock.push(line);
  }

  // Handle remaining content
  if (currentBlock.length > 0) {
    blocks.push(createBlock('paragraph', { content: currentBlock.join('\n') }));
  }

  return blocks;
}

/**
 * Serialize blocks to JSON
 */
export function serializeBlocks(blocks: Block[]): string {
  return JSON.stringify(blocks, null, 2);
}

/**
 * Deserialize blocks from JSON
 */
export function deserializeBlocks(content: string): Block[] {
  try {
    const parsed = JSON.parse(content);
    return Array.isArray(parsed) ? parsed : [];
  } catch {
    return [];
  }
}
