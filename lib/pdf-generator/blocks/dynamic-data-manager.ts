/**
 * Dynamic Data Manager
 * 
 * Handles dynamic content for blocks including auto-updating lists,
 * data-driven tables, live code execution, and real-time math rendering
 */

import { Block, BlockType } from './types';

export type DataSourceType = 'static' | 'api' | 'computed' | 'live' | 'reactive';

export interface DataSource {
  id: string;
  type: DataSourceType;
  url?: string;
  query?: string;
  computeFn?: (data: any) => any;
  refreshInterval?: number;
  dependencies?: string[];
  lastUpdated?: Date;
  cache?: any;
}

export interface DynamicDataBinding {
  blockId: string;
  blockType: BlockType;
  dataSourceId: string;
  fieldMappings: Record<string, string>;
  transformFn?: (data: any) => any;
  autoUpdate: boolean;
  updateTriggers: string[];
}

export interface LiveExecutionContext {
  blockId: string;
  language: string;
  code: string;
  environment: 'browser' | 'node' | 'python' | 'custom';
  dependencies: string[];
  timeout: number;
  allowNetworkAccess: boolean;
}

export interface MathRenderingContext {
  blockId: string;
  formula: string;
  engine: 'katex' | 'mathjax' | 'custom';
  displayMode: 'inline' | 'block';
  macros?: Record<string, string>;
  autoRender: boolean;
}

/**
 * Dynamic Data Manager
 * Manages dynamic content and real-time updates for blocks
 */
export class DynamicDataManager {
  private dataSources: Map<string, DataSource> = new Map();
  private dataBindings: Map<string, DynamicDataBinding> = new Map();
  private liveExecutors: Map<string, LiveExecutionContext> = new Map();
  private mathRenderers: Map<string, MathRenderingContext> = new Map();
  private updateCallbacks: Map<string, Function[]> = new Map();
  private refreshIntervals: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Register a data source
   */
  public registerDataSource(dataSource: DataSource): void {
    this.dataSources.set(dataSource.id, dataSource);
    
    // Set up auto-refresh if specified
    if (dataSource.refreshInterval && dataSource.refreshInterval > 0) {
      this.setupAutoRefresh(dataSource.id, dataSource.refreshInterval);
    }
  }

  /**
   * Bind block to data source
   */
  public bindBlockToData(binding: DynamicDataBinding): void {
    this.dataBindings.set(binding.blockId, binding);
    
    // Initial data load
    this.updateBlockData(binding.blockId);
    
    // Set up auto-update if enabled
    if (binding.autoUpdate) {
      this.setupAutoUpdate(binding.blockId);
    }
  }

  /**
   * Update block data from its bound data source
   */
  public async updateBlockData(blockId: string): Promise<any> {
    const binding = this.dataBindings.get(blockId);
    if (!binding) return null;

    const dataSource = this.dataSources.get(binding.dataSourceId);
    if (!dataSource) return null;

    try {
      let data = await this.fetchData(dataSource);
      
      // Apply transformation if specified
      if (binding.transformFn) {
        data = binding.transformFn(data);
      }

      // Map data to block fields
      const mappedData = this.mapDataToBlock(data, binding.fieldMappings);
      
      // Trigger update callbacks
      this.triggerUpdateCallbacks(blockId, mappedData);
      
      return mappedData;
    } catch (error) {
      console.error(`Failed to update block data for ${blockId}:`, error);
      return null;
    }
  }

  /**
   * Set up live code execution for a block
   */
  public setupLiveExecution(context: LiveExecutionContext): void {
    this.liveExecutors.set(context.blockId, context);
  }

  /**
   * Execute code in live context
   */
  public async executeLiveCode(blockId: string): Promise<{ result: any; error?: string }> {
    const context = this.liveExecutors.get(blockId);
    if (!context) {
      return { result: null, error: 'No execution context found' };
    }

    try {
      const result = await this.executeCode(context);
      return { result };
    } catch (error) {
      return { result: null, error: error.message };
    }
  }

  /**
   * Set up math rendering for a block
   */
  public setupMathRendering(context: MathRenderingContext): void {
    this.mathRenderers.set(context.blockId, context);
    
    if (context.autoRender) {
      this.renderMath(context.blockId);
    }
  }

  /**
   * Render math formula
   */
  public async renderMath(blockId: string): Promise<string | null> {
    const context = this.mathRenderers.get(blockId);
    if (!context) return null;

    try {
      const rendered = await this.renderMathFormula(context);
      this.triggerUpdateCallbacks(blockId, { renderedMath: rendered });
      return rendered;
    } catch (error) {
      console.error(`Failed to render math for ${blockId}:`, error);
      return null;
    }
  }

  /**
   * Create dynamic list with add/remove functionality
   */
  public createDynamicList(blockId: string, initialItems: any[] = []): {
    items: any[];
    addItem: (item: any) => void;
    removeItem: (index: number) => void;
    updateItem: (index: number, item: any) => void;
    moveItem: (fromIndex: number, toIndex: number) => void;
  } {
    let items = [...initialItems];

    const addItem = (item: any) => {
      items.push(item);
      this.triggerUpdateCallbacks(blockId, { items: [...items] });
    };

    const removeItem = (index: number) => {
      if (index >= 0 && index < items.length) {
        items.splice(index, 1);
        this.triggerUpdateCallbacks(blockId, { items: [...items] });
      }
    };

    const updateItem = (index: number, item: any) => {
      if (index >= 0 && index < items.length) {
        items[index] = item;
        this.triggerUpdateCallbacks(blockId, { items: [...items] });
      }
    };

    const moveItem = (fromIndex: number, toIndex: number) => {
      if (fromIndex >= 0 && fromIndex < items.length && 
          toIndex >= 0 && toIndex < items.length) {
        const [movedItem] = items.splice(fromIndex, 1);
        items.splice(toIndex, 0, movedItem);
        this.triggerUpdateCallbacks(blockId, { items: [...items] });
      }
    };

    return { items, addItem, removeItem, updateItem, moveItem };
  }

  /**
   * Create data-driven table with sorting/filtering
   */
  public createDataTable(blockId: string, data: any[], columns: any[]): {
    data: any[];
    sortBy: (column: string, direction: 'asc' | 'desc') => void;
    filterBy: (filters: Record<string, any>) => void;
    addRow: (row: any) => void;
    removeRow: (index: number) => void;
    updateRow: (index: number, row: any) => void;
  } {
    let tableData = [...data];
    let originalData = [...data];

    const sortBy = (column: string, direction: 'asc' | 'desc') => {
      tableData.sort((a, b) => {
        const aVal = a[column];
        const bVal = b[column];
        
        if (direction === 'asc') {
          return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
        } else {
          return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
        }
      });
      
      this.triggerUpdateCallbacks(blockId, { 
        rows: tableData.map(row => columns.map(col => row[col.key] || ''))
      });
    };

    const filterBy = (filters: Record<string, any>) => {
      tableData = originalData.filter(row => {
        return Object.entries(filters).every(([key, value]) => {
          if (!value) return true;
          return String(row[key]).toLowerCase().includes(String(value).toLowerCase());
        });
      });
      
      this.triggerUpdateCallbacks(blockId, { 
        rows: tableData.map(row => columns.map(col => row[col.key] || ''))
      });
    };

    const addRow = (row: any) => {
      originalData.push(row);
      tableData.push(row);
      this.triggerUpdateCallbacks(blockId, { 
        rows: tableData.map(row => columns.map(col => row[col.key] || ''))
      });
    };

    const removeRow = (index: number) => {
      if (index >= 0 && index < tableData.length) {
        const removedRow = tableData[index];
        tableData.splice(index, 1);
        originalData = originalData.filter(row => row !== removedRow);
        this.triggerUpdateCallbacks(blockId, { 
          rows: tableData.map(row => columns.map(col => row[col.key] || ''))
        });
      }
    };

    const updateRow = (index: number, newRow: any) => {
      if (index >= 0 && index < tableData.length) {
        tableData[index] = newRow;
        const originalIndex = originalData.findIndex(row => row === tableData[index]);
        if (originalIndex >= 0) {
          originalData[originalIndex] = newRow;
        }
        this.triggerUpdateCallbacks(blockId, { 
          rows: tableData.map(row => columns.map(col => row[col.key] || ''))
        });
      }
    };

    return { data: tableData, sortBy, filterBy, addRow, removeRow, updateRow };
  }

  /**
   * Register update callback for a block
   */
  public onBlockUpdate(blockId: string, callback: Function): void {
    if (!this.updateCallbacks.has(blockId)) {
      this.updateCallbacks.set(blockId, []);
    }
    this.updateCallbacks.get(blockId)!.push(callback);
  }

  /**
   * Remove update callback
   */
  public offBlockUpdate(blockId: string, callback: Function): void {
    const callbacks = this.updateCallbacks.get(blockId);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index >= 0) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Cleanup block data
   */
  public cleanup(blockId: string): void {
    this.dataBindings.delete(blockId);
    this.liveExecutors.delete(blockId);
    this.mathRenderers.delete(blockId);
    this.updateCallbacks.delete(blockId);
    
    // Clear any refresh intervals
    const interval = this.refreshIntervals.get(blockId);
    if (interval) {
      clearInterval(interval);
      this.refreshIntervals.delete(blockId);
    }
  }

  // Private methods
  private async fetchData(dataSource: DataSource): Promise<any> {
    switch (dataSource.type) {
      case 'static':
        return dataSource.cache || {};
      
      case 'api':
        if (!dataSource.url) throw new Error('API URL not specified');
        const response = await fetch(dataSource.url);
        const data = await response.json();
        dataSource.cache = data;
        dataSource.lastUpdated = new Date();
        return data;
      
      case 'computed':
        if (!dataSource.computeFn) throw new Error('Compute function not specified');
        const computed = dataSource.computeFn(dataSource.cache || {});
        dataSource.cache = computed;
        dataSource.lastUpdated = new Date();
        return computed;
      
      default:
        return {};
    }
  }

  private mapDataToBlock(data: any, fieldMappings: Record<string, string>): any {
    const mapped: any = {};
    
    for (const [blockField, dataPath] of Object.entries(fieldMappings)) {
      mapped[blockField] = this.getNestedValue(data, dataPath);
    }
    
    return mapped;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async executeCode(context: LiveExecutionContext): Promise<any> {
    // This is a simplified implementation
    // In a real implementation, you'd use a sandboxed environment
    switch (context.language) {
      case 'javascript':
        return new Function(context.code)();
      default:
        throw new Error(`Unsupported language: ${context.language}`);
    }
  }

  private async renderMathFormula(context: MathRenderingContext): Promise<string> {
    // This would integrate with KaTeX or MathJax
    // For now, return the formula wrapped in appropriate markup
    if (context.displayMode === 'block') {
      return `<div class="math-block">$$${context.formula}$$</div>`;
    } else {
      return `<span class="math-inline">$${context.formula}$</span>`;
    }
  }

  private setupAutoRefresh(dataSourceId: string, interval: number): void {
    const refreshInterval = setInterval(async () => {
      // Find all blocks bound to this data source and update them
      for (const [blockId, binding] of this.dataBindings) {
        if (binding.dataSourceId === dataSourceId) {
          await this.updateBlockData(blockId);
        }
      }
    }, interval);

    this.refreshIntervals.set(dataSourceId, refreshInterval);
  }

  private setupAutoUpdate(blockId: string): void {
    // Set up reactive updates based on triggers
    const binding = this.dataBindings.get(blockId);
    if (!binding) return;

    // This would integrate with a reactive system
    // For now, just set up periodic updates
    const updateInterval = setInterval(() => {
      this.updateBlockData(blockId);
    }, 5000); // Update every 5 seconds

    this.refreshIntervals.set(blockId, updateInterval);
  }

  private triggerUpdateCallbacks(blockId: string, data: any): void {
    const callbacks = this.updateCallbacks.get(blockId);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in update callback:', error);
        }
      });
    }
  }
}

// Export singleton instance
export const dynamicDataManager = new DynamicDataManager();

// Export utility functions
export function bindBlockToData(binding: DynamicDataBinding): void {
  dynamicDataManager.bindBlockToData(binding);
}

export function createDynamicList(blockId: string, initialItems: any[] = []) {
  return dynamicDataManager.createDynamicList(blockId, initialItems);
}

export function createDataTable(blockId: string, data: any[], columns: any[]) {
  return dynamicDataManager.createDataTable(blockId, data, columns);
}

export function setupLiveExecution(context: LiveExecutionContext): void {
  dynamicDataManager.setupLiveExecution(context);
}

export function setupMathRendering(context: MathRenderingContext): void {
  dynamicDataManager.setupMathRendering(context);
}
