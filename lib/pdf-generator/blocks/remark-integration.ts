/**
 * Remark JSX Integration
 * 
 * Seamless markdown-to-JSX conversion with bidirectional support
 */

import { Block, BlockType } from './types';

// Production-ready remark integration with graceful fallback
interface RemarkProcessor {
  process: (markdown: string) => Promise<{ value: string; data: any }>;
}

// Check if remark packages are available
const isRemarkAvailable = (): boolean => {
  try {
    // This will be true when packages are installed
    return typeof require !== 'undefined' &&
           !!require.resolve('unified') &&
           !!require.resolve('remark-parse');
  } catch {
    return false;
  }
};

// Create remark processor when packages are available
const createRemarkProcessorInternal = async (options: RemarkJSXOptions): Promise<RemarkProcessor | null> => {
  if (!isRemarkAvailable()) {
    return null;
  }

  try {
    // Dynamic require for production use
    const { unified } = require('unified');
    const remarkParse = require('remark-parse');
    const remarkStringify = require('remark-stringify');
    const remarkGfm = require('remark-gfm');
    const remarkMath = require('remark-math');
    const remarkFrontmatter = require('remark-frontmatter');

    let processor = unified().use(remarkParse);

    if (options.enableGfm) {
      processor = processor.use(remarkGfm);
    }

    if (options.enableMath) {
      processor = processor.use(remarkMath);
    }

    if (options.enableFrontmatter) {
      processor = processor.use(remarkFrontmatter);
    }

    return processor.use(remarkStringify);
  } catch (error) {
    console.warn('Failed to create remark processor:', error);
    return null;
  }
};

export interface RemarkJSXOptions {
  enableGfm?: boolean;
  enableMath?: boolean;
  enableFrontmatter?: boolean;
  customComponents?: Record<string, React.ComponentType<any>>;
  preserveBlockStructure?: boolean;
}

export interface JSXBlockNode {
  type: 'jsx-block';
  blockType: BlockType;
  blockId: string;
  props: Record<string, any>;
  children?: JSXBlockNode[];
  content: string;
  metadata: Record<string, any>;
}

export interface ConversionContext {
  blockMap: Map<string, Block>;
  componentRegistry: Map<BlockType, React.ComponentType<any>>;
  renderMode: 'preview' | 'edit' | 'render' | 'print';
  theme: string;
}

/**
 * Production-ready Remark JSX Processor for Block Editor
 * Automatically detects and uses remark packages when available,
 * falls back to simplified implementation otherwise.
 */
export class RemarkJSXProcessor {
  private options: RemarkJSXOptions;
  private processor: RemarkProcessor | null = null;
  private isInitialized = false;

  constructor(options: RemarkJSXOptions = {}) {
    this.options = {
      enableGfm: true,
      enableMath: true,
      enableFrontmatter: true,
      preserveBlockStructure: true,
      ...options,
    };
  }

  private async ensureInitialized(): Promise<void> {
    if (this.isInitialized) return;

    this.processor = await createRemarkProcessorInternal(this.options);
    this.isInitialized = true;

    if (this.processor) {
      console.log('✅ Remark processor initialized with full functionality');
    } else {
      console.log('⚠️ Using fallback markdown processor (install remark packages for full functionality)');
    }
  }

  /**
   * Simple markdown parsing (basic implementation)
   */
  private parseMarkdown(markdown: string): JSXBlockNode[] {
    const blocks: JSXBlockNode[] = [];
    const lines = markdown.split('\n');
    let currentBlock: string[] = [];
    let blockType: BlockType = 'paragraph';

    for (const line of lines) {
      if (line.trim() === '') {
        if (currentBlock.length > 0) {
          blocks.push(this.createJSXBlock(blockType, currentBlock.join('\n')));
          currentBlock = [];
          blockType = 'paragraph';
        }
      } else if (line.startsWith('#')) {
        if (currentBlock.length > 0) {
          blocks.push(this.createJSXBlock(blockType, currentBlock.join('\n')));
          currentBlock = [];
        }
        blockType = 'heading';
        currentBlock.push(line);
      } else if (line.startsWith('```')) {
        if (blockType === 'code') {
          blocks.push(this.createJSXBlock('code', currentBlock.join('\n')));
          currentBlock = [];
          blockType = 'paragraph';
        } else {
          if (currentBlock.length > 0) {
            blocks.push(this.createJSXBlock(blockType, currentBlock.join('\n')));
            currentBlock = [];
          }
          blockType = 'code';
        }
      } else {
        currentBlock.push(line);
      }
    }

    if (currentBlock.length > 0) {
      blocks.push(this.createJSXBlock(blockType, currentBlock.join('\n')));
    }

    return blocks;
  }

  private createJSXBlock(type: BlockType, content: string): JSXBlockNode {
    return {
      type: 'jsx-block',
      blockType: type,
      blockId: this.generateBlockId(),
      props: this.getBlockProps(type, content),
      content: this.cleanContent(type, content),
      metadata: {},
    };
  }

  private getBlockProps(type: BlockType, content: string): Record<string, any> {
    switch (type) {
      case 'heading':
        const level = (content.match(/^#+/) || [''])[0].length;
        return { level: Math.min(level, 6) || 1 };
      case 'code':
        const match = content.match(/^```(\w+)?/);
        return { language: match?.[1] || 'text' };
      default:
        return {};
    }
  }

  private cleanContent(type: BlockType, content: string): string {
    switch (type) {
      case 'heading':
        return content.replace(/^#+\s*/, '');
      case 'code':
        return content.replace(/^```\w*\n?/, '').replace(/\n?```$/, '');
      default:
        return content;
    }
  }



  /**
   * Convert blocks to markdown with JSX preservation
   */
  public blocksToMarkdown(blocks: Block[]): string {
    const markdownParts: string[] = [];

    for (const block of blocks) {
      const markdown = this.blockToMarkdown(block);
      if (markdown) {
        markdownParts.push(markdown);
      }
    }

    return markdownParts.join('\n\n');
  }

  /**
   * Convert single block to markdown
   */
  private blockToMarkdown(block: Block): string {
    switch (block.type) {
      case 'paragraph':
        return block.content;

      case 'heading':
        const level = (block as any).level || 1;
        return `${'#'.repeat(level)} ${block.content}`;

      case 'code':
        const codeBlock = block as any;
        const lang = codeBlock.language || '';
        const filename = codeBlock.filename ? ` ${codeBlock.filename}` : '';
        return `\`\`\`${lang}${filename}\n${block.content}\n\`\`\``;

      case 'quote':
        const lines = block.content.split('\n');
        return lines.map(line => `> ${line}`).join('\n');

      case 'list':
        const listBlock = block as any;
        return listBlock.items?.map((item: any, index: number) => {
          const marker = listBlock.listType === 'ordered' ? `${index + 1}.` : '-';
          const indent = '  '.repeat(item.indent || 0);
          return `${indent}${marker} ${item.content}`;
        }).join('\n') || '';

      case 'table':
        const tableBlock = block as any;
        if (!tableBlock.headers || !tableBlock.rows) return '';
        
        const headerRow = `| ${tableBlock.headers.join(' | ')} |`;
        const separatorRow = `| ${tableBlock.headers.map(() => '---').join(' | ')} |`;
        const dataRows = tableBlock.rows.map((row: string[]) => `| ${row.join(' | ')} |`);
        
        return [headerRow, separatorRow, ...dataRows].join('\n');

      case 'divider':
        return '---';

      case 'math':
        const mathBlock = block as any;
        const formula = mathBlock.formula || block.content;
        return mathBlock.displayMode === 'inline' ? `$${formula}$` : `$$\n${formula}\n$$`;

      case 'callout':
        const calloutBlock = block as any;
        const icon = this.getCalloutIcon(calloutBlock.calloutType);
        const title = calloutBlock.title ? `**${calloutBlock.title}**\n` : '';
        return `> ${icon} ${title}${block.content}`;

      default:
        return block.content;
    }
  }

  /**
   * Convert markdown to blocks with JSX awareness
   */
  public async markdownToBlocks(markdown: string): Promise<Block[]> {
    await this.ensureInitialized();

    if (this.processor) {
      try {
        // Use full remark processor when available
        const result = await this.processor.process(markdown);
        const jsxBlocks = result.data.blocks || this.parseMarkdown(markdown);
        return jsxBlocks.map((jsxBlock: JSXBlockNode) => this.jsxBlockToBlock(jsxBlock));
      } catch (error) {
        console.warn('Remark processing failed, falling back to simple parser:', error);
      }
    }

    // Fallback to simple parser
    const jsxBlocks = this.parseMarkdown(markdown);
    return jsxBlocks.map((jsxBlock: JSXBlockNode) => this.jsxBlockToBlock(jsxBlock));
  }

  /**
   * Convert JSX block node to Block
   */
  private jsxBlockToBlock(jsxBlock: JSXBlockNode): Block {
    const baseBlock = {
      id: jsxBlock.blockId,
      type: jsxBlock.blockType,
      content: jsxBlock.content,
      metadata: jsxBlock.metadata,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add type-specific properties
    switch (jsxBlock.blockType) {
      case 'heading':
        return { ...baseBlock, level: jsxBlock.props.level || 1 } as any;
      
      case 'code':
        return { 
          ...baseBlock, 
          language: jsxBlock.props.language,
          showLineNumbers: jsxBlock.props.showLineNumbers,
          filename: jsxBlock.metadata.filename,
        } as any;
      
      case 'list':
        return {
          ...baseBlock,
          listType: jsxBlock.props.listType,
          items: jsxBlock.metadata.items || [],
        } as any;
      
      case 'table':
        return {
          ...baseBlock,
          headers: jsxBlock.metadata.headers || [],
          rows: jsxBlock.metadata.rows || [],
        } as any;
      
      case 'math':
        return {
          ...baseBlock,
          formula: jsxBlock.content,
          displayMode: jsxBlock.props.displayMode,
        } as any;
      
      default:
        return baseBlock as any;
    }
  }

  // Helper methods
  private visitNodes(node: any, visitor: (node: any, index: number, parent: any) => void) {
    if (node.children) {
      node.children.forEach((child: any, index: number) => {
        visitor(child, index, node);
        this.visitNodes(child, visitor);
      });
    }
  }

  private nodeToText(node: any): string {
    if (node.value) return node.value;
    if (node.children) {
      return node.children.map((child: any) => this.nodeToText(child)).join('');
    }
    return '';
  }



  private generateBlockId(): string {
    return `block_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private getCalloutIcon(type: string): string {
    const icons = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
      success: '✅',
      note: '📝',
    };
    return icons[type as keyof typeof icons] || '📝';
  }
}

// Export singleton instance
export const remarkJSXProcessor = new RemarkJSXProcessor();

// Export utility functions
export function createRemarkJSXProcessor(options?: RemarkJSXOptions): RemarkJSXProcessor {
  return new RemarkJSXProcessor(options);
}

export function isJSXBlock(node: any): node is JSXBlockNode {
  return node && node.type === 'jsx-block';
}
