/**
 * PDF Generator Service Tests
 */

import { PDFGeneratorService } from '../core/pdf-generator-service';

describe('PDFGeneratorService', () => {
  let service: PDFGeneratorService;

  beforeEach(() => {
    service = new PDFGeneratorService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createDocument', () => {
    it('should create PDF document from valid markdown', async () => {
      const title = 'Test Document';
      const markdown = '# Test Document\n\nThis is a test.';
      const template = 'default';

      const result = await service.createDocument(title, markdown, template);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.metadata.title).toBe(title);
    });

    it('should handle empty markdown', async () => {
      const title = 'Empty Document';
      const markdown = '';
      const template = 'default';

      const result = await service.createDocument(title, markdown, template);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.metadata.title).toBe(title);
    });

    it('should use default template when none specified', async () => {
      const title = 'Test Document';
      const markdown = '# Test Document\n\nThis is a test.';

      const result = await service.createDocument(title, markdown);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.metadata.title).toBe(title);
    });
  });

  describe('generateFromResearch', () => {
    it('should generate PDF from research project', async () => {
      const projectId = 'test-project-123';
      const options = {
        projectId,
        includeFindings: true,
        includeAnalysis: true,
        includeSources: true,
        autoGenerateReferences: true,
        template: 'research-report' as const
      };

      const result = await service.generateFromResearch(projectId, options);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
    });

    it('should handle missing research project', async () => {
      const projectId = '';
      const options = {
        projectId,
        includeFindings: true,
        includeAnalysis: true,
        includeSources: true,
        autoGenerateReferences: true,
        template: 'research-report' as const
      };

      await expect(service.generateFromResearch(projectId, options)).rejects.toThrow('Invalid project ID provided');
    });
  });

  describe('event handling', () => {
    it('should emit events during generation', async () => {
      const title = 'Test Document';
      const markdown = '# Test Document\n\nThis is a test.';

      const generationStartedSpy = jest.fn();
      const generationCompletedSpy = jest.fn();

      service.on('generationStarted', generationStartedSpy);
      service.on('generationCompleted', generationCompletedSpy);

      await service.createDocument(title, markdown);

      expect(generationStartedSpy).toHaveBeenCalled();
      expect(generationCompletedSpy).toHaveBeenCalled();
    });
  });
});
