/**
 * Block State Manager Tests
 */

import { 
  blockStateManager, 
  transitionBlockState, 
  BlockState 
} from '../blocks/block-state-manager';

describe('BlockStateManager', () => {
  beforeEach(() => {
    // Clear all blocks before each test
    blockStateManager.clearAllBlocks();
  });

  describe('initializeBlock', () => {
    it('should initialize block with default state', () => {
      const blockId = 'test-block-1';
      
      blockStateManager.initializeBlock(blockId, 'preview');
      
      const state = blockStateManager.getBlockState(blockId);
      expect(state).toBe('preview');
    });

    it('should initialize block with custom options', () => {
      const blockId = 'test-block-2';
      const options = {
        allowedTransitions: ['preview', 'edit'] as BlockState[],
        autoSave: true,
        validateOnTransition: true
      };
      
      blockStateManager.initializeBlock(blockId, 'preview', options);
      
      const state = blockStateManager.getBlockState(blockId);
      expect(state).toBe('preview');
    });
  });

  describe('transitionBlockState', () => {
    it('should transition to valid state', async () => {
      const blockId = 'test-block-3';
      
      blockStateManager.initializeBlock(blockId, 'preview');
      
      const result = await transitionBlockState(blockId, 'edit');
      
      expect(result).toBe(true);
      expect(blockStateManager.getBlockState(blockId)).toBe('edit');
    });

    it('should reject invalid transitions', async () => {
      const blockId = 'test-block-4';
      const options = {
        allowedTransitions: ['preview', 'edit'] as BlockState[]
      };
      
      blockStateManager.initializeBlock(blockId, 'preview', options);
      
      const result = await transitionBlockState(blockId, 'render');
      
      expect(result).toBe(false);
      expect(blockStateManager.getBlockState(blockId)).toBe('preview');
    });

    it('should call transition callbacks', async () => {
      const blockId = 'test-block-5';
      const onStateChange = jest.fn();
      
      blockStateManager.initializeBlock(blockId, 'preview');
      blockStateManager.onStateChange(blockId, onStateChange);
      
      await transitionBlockState(blockId, 'edit');
      
      expect(onStateChange).toHaveBeenCalledWith('preview', 'edit');
    });
  });

  describe('batchTransition', () => {
    it('should transition multiple blocks', async () => {
      const blockIds = ['block-1', 'block-2', 'block-3'];
      
      blockIds.forEach(id => {
        blockStateManager.initializeBlock(id, 'preview');
      });
      
      const results = await blockStateManager.batchTransition(blockIds, 'edit');
      
      expect(results.successful).toEqual(blockIds);
      expect(results.failed).toEqual([]);
      
      blockIds.forEach(id => {
        expect(blockStateManager.getBlockState(id)).toBe('edit');
      });
    });

    it('should handle partial failures in batch transition', async () => {
      const blockIds = ['block-1', 'block-2', 'block-3'];
      
      // Initialize first two blocks normally
      blockStateManager.initializeBlock(blockIds[0], 'preview');
      blockStateManager.initializeBlock(blockIds[1], 'preview');
      
      // Initialize third block with restricted transitions
      blockStateManager.initializeBlock(blockIds[2], 'preview', {
        allowedTransitions: ['preview'] as BlockState[]
      });
      
      const results = await blockStateManager.batchTransition(blockIds, 'edit');
      
      expect(results.successful).toEqual([blockIds[0], blockIds[1]]);
      expect(results.failed).toEqual([blockIds[2]]);
    });
  });

  describe('getBlockHistory', () => {
    it('should track state history', async () => {
      const blockId = 'test-block-6';
      
      blockStateManager.initializeBlock(blockId, 'preview');
      await transitionBlockState(blockId, 'edit');
      await transitionBlockState(blockId, 'render');
      
      const history = blockStateManager.getBlockHistory(blockId);
      
      expect(history).toHaveLength(3);
      expect(history[0].state).toBe('preview');
      expect(history[1].state).toBe('edit');
      expect(history[2].state).toBe('render');
    });
  });

  describe('getAllBlockStates', () => {
    it('should return all block states', () => {
      const blocks = [
        { id: 'block-1', state: 'preview' as BlockState },
        { id: 'block-2', state: 'edit' as BlockState },
        { id: 'block-3', state: 'render' as BlockState }
      ];
      
      blocks.forEach(({ id, state }) => {
        blockStateManager.initializeBlock(id, state);
      });
      
      const allStates = blockStateManager.getAllBlockStates();
      
      expect(Object.keys(allStates)).toHaveLength(3);
      expect(allStates['block-1']).toBe('preview');
      expect(allStates['block-2']).toBe('edit');
      expect(allStates['block-3']).toBe('render');
    });
  });

  describe('removeBlock', () => {
    it('should remove block and its state', () => {
      const blockId = 'test-block-7';
      
      blockStateManager.initializeBlock(blockId, 'preview');
      expect(blockStateManager.getBlockState(blockId)).toBe('preview');
      
      blockStateManager.removeBlock(blockId);
      expect(blockStateManager.getBlockState(blockId)).toBeUndefined();
    });
  });

  describe('validation', () => {
    it('should validate state transitions when enabled', async () => {
      const blockId = 'test-block-8';
      const validator = jest.fn().mockResolvedValue(true);
      
      blockStateManager.initializeBlock(blockId, 'preview', {
        validateOnTransition: true
      });
      
      blockStateManager.setValidator(blockId, validator);
      
      await transitionBlockState(blockId, 'edit');
      
      expect(validator).toHaveBeenCalledWith('preview', 'edit');
    });

    it('should reject transition when validation fails', async () => {
      const blockId = 'test-block-9';
      const validator = jest.fn().mockResolvedValue(false);
      
      blockStateManager.initializeBlock(blockId, 'preview', {
        validateOnTransition: true
      });
      
      blockStateManager.setValidator(blockId, validator);
      
      const result = await transitionBlockState(blockId, 'edit');
      
      expect(result).toBe(false);
      expect(blockStateManager.getBlockState(blockId)).toBe('preview');
    });
  });

  describe('auto-save', () => {
    it('should trigger auto-save when enabled', async () => {
      const blockId = 'test-block-10';
      const autoSaveHandler = jest.fn();
      
      blockStateManager.initializeBlock(blockId, 'preview', {
        autoSave: true
      });
      
      blockStateManager.setAutoSaveHandler(blockId, autoSaveHandler);
      
      await transitionBlockState(blockId, 'edit');
      
      expect(autoSaveHandler).toHaveBeenCalled();
    });
  });

  describe('cleanup', () => {
    it('should clear all blocks', () => {
      const blockIds = ['block-1', 'block-2', 'block-3'];
      
      blockIds.forEach(id => {
        blockStateManager.initializeBlock(id, 'preview');
      });
      
      expect(Object.keys(blockStateManager.getAllBlockStates())).toHaveLength(3);
      
      blockStateManager.clearAllBlocks();
      
      expect(Object.keys(blockStateManager.getAllBlockStates())).toHaveLength(0);
    });
  });
});
