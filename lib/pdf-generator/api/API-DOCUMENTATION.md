# PDF Generator API Documentation

## Overview

The PDF Generator API provides comprehensive endpoints for generating PDF documents from markdown content, managing block-based editing, and integrating with AI agent systems. This documentation covers all available endpoints, request/response formats, and integration examples.

## Base URL

```
/api/pdf-generator
```

## Authentication

Currently, the API does not require authentication. In production environments, implement appropriate authentication mechanisms.

## Rate Limiting

- **PDF Generation**: 50 requests per 15 minutes per IP
- **Validation**: 100 requests per minute per IP
- **Batch Operations**: 10 requests per 30 minutes per IP

## Core Endpoints

### 1. Generate PDF from Markdown

**Endpoint**: `POST /generate`

Generate a PDF document from markdown content with advanced options.

**Request Body**:
```json
{
  "title": "Document Title",
  "content": "# Markdown Content\n\nThis is the document content.",
  "template": "default",
  "author": "Author Name",
  "metadata": {
    "subject": "Document Subject",
    "keywords": ["pdf", "generation"],
    "description": "Document description"
  },
  "options": {
    "enableValidation": true,
    "sanitizeContent": true,
    "quality": "medium",
    "compression": true
  }
}
```

**Response**:
```json
{
  "success": true,
  "document": {
    "id": "doc_123456",
    "title": "Document Title",
    "createdAt": "2024-01-01T00:00:00Z",
    "downloadUrl": "/api/pdf-generator/download/doc_123456",
    "previewUrl": "/api/pdf-generator/preview/doc_123456",
    "metadata": {
      "title": "Document Title",
      "author": "Author Name",
      "generatedAt": "2024-01-01T00:00:00Z",
      "apiVersion": "2.0"
    }
  }
}
```

### 2. Generate PDF from Blocks

**Endpoint**: `POST /generate-from-blocks`

Generate a PDF document from structured blocks with state management.

**Request Body**:
```json
{
  "title": "Block Document",
  "blocks": [
    {
      "id": "block_1",
      "type": "heading",
      "content": "Introduction",
      "metadata": { "level": 1 },
      "state": "render"
    },
    {
      "id": "block_2",
      "type": "paragraph",
      "content": "This is a paragraph block.",
      "state": "render"
    },
    {
      "id": "block_3",
      "type": "code",
      "content": "console.log('Hello, World!');",
      "metadata": { "language": "javascript" },
      "state": "render"
    }
  ],
  "template": "default",
  "options": {
    "enableDynamicData": false,
    "enableLiveExecution": false,
    "validateBlocks": true
  }
}
```

**Response**:
```json
{
  "success": true,
  "document": {
    "id": "doc_789012",
    "title": "Block Document",
    "createdAt": "2024-01-01T00:00:00Z",
    "downloadUrl": "/api/pdf-generator/download/doc_789012",
    "previewUrl": "/api/pdf-generator/preview/doc_789012",
    "metadata": {
      "generatedFrom": "blocks",
      "blockCount": 3,
      "enabledFeatures": {
        "dynamicData": false,
        "liveExecution": false
      }
    },
    "originalBlocks": 3
  }
}
```

### 3. Batch Generate PDFs

**Endpoint**: `POST /batch-generate`

Generate multiple PDF documents in batch with parallel processing.

**Request Body**:
```json
{
  "documents": [
    {
      "id": "doc1",
      "title": "First Document",
      "content": "# First Document\n\nContent here.",
      "template": "default"
    },
    {
      "id": "doc2",
      "title": "Second Document",
      "content": "# Second Document\n\nMore content.",
      "template": "professional"
    }
  ],
  "options": {
    "parallelProcessing": true,
    "continueOnError": true,
    "maxConcurrency": 3
  }
}
```

**Response**:
```json
{
  "success": true,
  "results": {
    "total": 2,
    "successful": 2,
    "failed": 0,
    "documents": [
      {
        "id": "doc1",
        "success": true,
        "document": {
          "id": "generated_doc1",
          "title": "First Document",
          "downloadUrl": "/api/pdf-generator/download/generated_doc1"
        }
      },
      {
        "id": "doc2",
        "success": true,
        "document": {
          "id": "generated_doc2",
          "title": "Second Document",
          "downloadUrl": "/api/pdf-generator/download/generated_doc2"
        }
      }
    ]
  }
}
```

### 4. Generate Research Report

**Endpoint**: `POST /generate-research-report`

Generate a comprehensive research report from a research project.

**Request Body**:
```json
{
  "projectId": "research_project_123",
  "options": {
    "template": "research-report",
    "includeFindings": true,
    "includeAnalysis": true,
    "includeSources": true,
    "autoGenerateReferences": true
  }
}
```

**Response**:
```json
{
  "success": true,
  "document": {
    "id": "research_doc_456",
    "title": "Research Report: Project 123",
    "createdAt": "2024-01-01T00:00:00Z",
    "downloadUrl": "/api/pdf-generator/download/research_doc_456",
    "previewUrl": "/api/pdf-generator/preview/research_doc_456",
    "metadata": {
      "type": "research-report",
      "projectId": "research_project_123"
    },
    "projectId": "research_project_123"
  }
}
```

## Validation Endpoints

### 5. Validate Content

**Endpoint**: `POST /validate`

Validate markdown content for syntax, structure, and security issues.

**Request Body**:
```json
{
  "content": "# Test Document\n\nThis is test content.",
  "options": {
    "strictMode": false,
    "checkXSS": true,
    "maxLength": 100000
  }
}
```

**Response**:
```json
{
  "success": true,
  "validation": {
    "isValid": true,
    "errors": [],
    "warnings": [],
    "statistics": {
      "wordCount": 5,
      "characterCount": 35,
      "headingCount": 1
    }
  }
}
```

## Template Endpoints

### 6. Get Available Templates

**Endpoint**: `GET /templates`

Retrieve list of available PDF templates.

**Response**:
```json
{
  "success": true,
  "templates": [
    {
      "id": "default",
      "name": "Default Template",
      "description": "Basic document template",
      "category": "general",
      "features": ["basic-styling", "headers-footers"]
    },
    {
      "id": "professional",
      "name": "Professional Template",
      "description": "Professional business document",
      "category": "business",
      "features": ["advanced-styling", "cover-page", "table-of-contents"]
    }
  ],
  "count": 2,
  "categories": ["general", "business", "academic"]
}
```

## Block Operation Endpoints

### 7. Block Operations

**Endpoint**: `POST /block-operation`

Perform operations on blocks including state transitions and validation.

**Request Body**:
```json
{
  "blockId": "block_123",
  "operation": "transition",
  "data": {
    "state": "edit"
  }
}
```

**Response**:
```json
{
  "success": true,
  "result": {
    "blockId": "block_123",
    "previousState": "preview",
    "newState": "edit",
    "transitionTime": "2024-01-01T00:00:00Z"
  }
}
```

### 8. Dynamic Data Management

**Endpoint**: `POST /dynamic-data`

Create and manage dynamic data elements.

**Request Body**:
```json
{
  "blockId": "table_block_1",
  "type": "table",
  "data": {
    "rows": [
      {"name": "John", "age": 30, "city": "New York"},
      {"name": "Jane", "age": 25, "city": "Los Angeles"}
    ],
    "columns": [
      {"key": "name", "label": "Name", "sortable": true},
      {"key": "age", "label": "Age", "sortable": true},
      {"key": "city", "label": "City", "filterable": true}
    ]
  },
  "options": {
    "autoUpdate": false,
    "refreshInterval": 5000
  }
}
```

**Response**:
```json
{
  "success": true,
  "result": {
    "blockId": "table_block_1",
    "type": "table",
    "rowCount": 2,
    "columnCount": 3,
    "features": ["sorting", "filtering"]
  }
}
```

## System Endpoints

### 9. Get Status

**Endpoint**: `GET /status`

Get system status and feature information.

**Response**:
```json
{
  "success": true,
  "status": {
    "service": "running",
    "version": "2.0.0",
    "uptime": 3600,
    "blocks": {
      "totalBlocks": 150,
      "activeBlocks": 25,
      "states": {
        "preview": 10,
        "edit": 5,
        "render": 8,
        "print": 2
      }
    },
    "features": {
      "blockEditor": true,
      "dynamicData": true,
      "liveExecution": true,
      "remarkJSX": true,
      "batchProcessing": true
    }
  }
}
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "Error description",
  "details": "Detailed error message",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Common Error Codes

- **400 Bad Request**: Invalid request data or parameters
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server-side processing error

## Integration Examples

### Using with AI Agents

```typescript
import { pdfGeneratorTools } from '@/lib/pdf-generator/api/ai-tools';

// Generate PDF from markdown
const result = await pdfGeneratorTools.generatePDF.execute({
  title: "AI Generated Report",
  content: markdownContent,
  template: "professional",
  options: {
    enableValidation: true,
    quality: "high"
  }
});

// Generate from blocks
const blockResult = await pdfGeneratorTools.generateFromBlocks.execute({
  title: "Block Document",
  blocks: structuredBlocks,
  options: {
    enableDynamicData: true,
    validateBlocks: true
  }
});
```

### Batch Processing

```typescript
// Process multiple documents
const batchResult = await pdfGeneratorTools.batchGeneratePDFs.execute({
  documents: documentList,
  options: {
    parallelProcessing: true,
    maxConcurrency: 3,
    continueOnError: true
  }
});
```

### Block Management

```typescript
// Transition block state
await pdfGeneratorTools.blockOperation.execute({
  blockId: "block_123",
  operation: "transition",
  data: { state: "edit" }
});

// Create dynamic table
await pdfGeneratorTools.createDynamicData.execute({
  blockId: "table_1",
  type: "table",
  data: { rows: tableData, columns: columnConfig }
});
```

## Best Practices

1. **Validation**: Always validate content before generation
2. **Rate Limiting**: Respect rate limits and implement retry logic
3. **Error Handling**: Handle errors gracefully with fallback options
4. **Batch Processing**: Use batch endpoints for multiple documents
5. **Block States**: Manage block states properly for optimal performance
6. **Security**: Sanitize content and validate inputs
7. **Performance**: Use appropriate quality settings for your use case

## Support

For additional support and advanced integration examples, refer to the main README.md and contact the development team.
