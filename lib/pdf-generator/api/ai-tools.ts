/**
 * Enhanced AI Tools for PDF Generator
 *
 * Comprehensive AI SDK tool definitions with block-based editing,
 * dynamic data support, and advanced PDF generation features.
 */

import { tool } from 'ai';
import { z } from 'zod';
import {
  validateMarkdown,
  getAvailableTemplates,
  generateResearchReport,
  parseMarkdownStructure,
  createDocumentFromTemplate,
  previewDocumentHTML,
  pdfGeneratorService
} from '../index';
import {
  blockStateManager,
  transitionBlockState,
  createDynamicList,
  createDataTable,
  setupLiveExecution
} from '../blocks';

/**
 * Enhanced PDF generation from markdown
 */
export const generatePDFTool = tool({
  description: 'Generate a PDF document from markdown content with advanced templates, validation, and styling options',
  parameters: z.object({
    title: z.string().describe('Document title'),
    content: z.string().describe('Markdown content for the document'),
    template: z.string().optional().default('default').describe('Template to use (default, professional, academic, research-report, technical-document)'),
    author: z.string().optional().describe('Document author'),
    metadata: z.object({
      subject: z.string().optional(),
      keywords: z.array(z.string()).optional(),
      description: z.string().optional(),
    }).optional().describe('Additional document metadata'),
    styling: z.object({
      theme: z.string().optional().describe('Color theme (default, dark, academic, corporate)'),
      pageSize: z.enum(['A4', 'A3', 'A5', 'Letter', 'Legal']).optional().default('A4'),
      orientation: z.enum(['portrait', 'landscape']).optional().default('portrait'),
    }).optional().describe('Document styling options'),
    options: z.object({
      enableValidation: z.boolean().optional().default(true).describe('Enable content validation'),
      sanitizeContent: z.boolean().optional().default(true).describe('Sanitize HTML content'),
      quality: z.enum(['low', 'medium', 'high', 'print']).optional().default('medium').describe('PDF quality'),
      compression: z.boolean().optional().default(true).describe('Enable compression'),
    }).optional().describe('Generation options'),
  }),
  execute: async ({ title, content, template, author, metadata, options }) => {
    try {
      const document = await pdfGeneratorService.createDocument(
        title,
        content,
        template,
        {
          ...metadata,
          author,
          generatedAt: new Date().toISOString(),
          apiVersion: '2.0',
        }
      );

      return {
        success: true,
        message: `PDF generated successfully: "${title}"`,
        documentId: document.id,
        downloadUrl: `/api/pdf-generator/download/${document.id}`,
        previewUrl: `/api/pdf-generator/preview/${document.id}`,
        metadata: document.metadata,
        options,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Generate PDF from structured blocks
 */
export const generateFromBlocksTool = tool({
  description: 'Generate a PDF document from structured blocks with advanced block editor features and state management',
  parameters: z.object({
    title: z.string().describe('Document title'),
    blocks: z.array(z.object({
      id: z.string().describe('Unique block identifier'),
      type: z.enum(['paragraph', 'heading', 'code', 'quote', 'list', 'table', 'image', 'math', 'divider', 'callout', 'embed']).describe('Block type'),
      content: z.string().describe('Block content'),
      metadata: z.record(z.any()).optional().describe('Block-specific metadata (level for headings, language for code, etc.)'),
      state: z.enum(['preview', 'edit', 'render', 'print']).optional().default('render').describe('Block state'),
    })).min(1).describe('Array of structured blocks'),
    template: z.string().optional().default('default').describe('Template to use'),
    metadata: z.record(z.any()).optional().describe('Document metadata'),
    options: z.object({
      enableDynamicData: z.boolean().optional().default(false).describe('Enable dynamic data features'),
      enableLiveExecution: z.boolean().optional().default(false).describe('Enable live code execution'),
      validateBlocks: z.boolean().optional().default(true).describe('Validate blocks before generation'),
    }).optional().describe('Block generation options'),
  }),
  execute: async ({ title, blocks, template, metadata, options }) => {
    try {
      // Initialize block states
      for (const block of blocks) {
        try {
          blockStateManager.initializeBlock(block.id, block.state || 'render', {
            allowedTransitions: ['preview', 'edit', 'render', 'print'],
            autoSave: false,
            validateOnTransition: options?.validateBlocks || true,
          });
        } catch (error) {
          console.warn(`Block ${block.id} already initialized:`, error);
        }
      }

      // Convert blocks to markdown
      const markdown = blocks.map(block => {
        switch (block.type) {
          case 'heading':
            const level = block.metadata?.level || 1;
            return '#'.repeat(level) + ' ' + block.content;
          case 'paragraph':
            return block.content;
          case 'code':
            const language = block.metadata?.language || '';
            return '```' + language + '\n' + block.content + '\n```';
          case 'quote':
            return '> ' + block.content;
          case 'list':
            return block.content.split('\n').map(item => '- ' + item).join('\n');
          case 'table':
            return block.content; // Assume content is already in markdown table format
          case 'math':
            return '$$\n' + block.content + '\n$$';
          case 'callout':
            const calloutType = block.metadata?.calloutType || 'info';
            return `> **${calloutType.toUpperCase()}**: ${block.content}`;
          case 'divider':
            return '---';
          default:
            return block.content;
        }
      }).join('\n\n');

      const document = await pdfGeneratorService.createDocument(
        title,
        markdown,
        template,
        {
          ...metadata,
          generatedFrom: 'blocks',
          blockCount: blocks.length,
          enabledFeatures: {
            dynamicData: options?.enableDynamicData || false,
            liveExecution: options?.enableLiveExecution || false,
          },
          generatedAt: new Date().toISOString(),
        }
      );

      return {
        success: true,
        message: `PDF generated successfully from ${blocks.length} blocks`,
        documentId: document.id,
        blockCount: blocks.length,
        downloadUrl: `/api/pdf-generator/download/${document.id}`,
        previewUrl: `/api/pdf-generator/preview/${document.id}`,
        metadata: document.metadata,
        options,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Validate markdown content
 */
export const validateMarkdownTool = tool({
  description: 'Validate markdown content for syntax errors, missing references, and structural issues',
  parameters: z.object({
    content: z.string().describe('Markdown content to validate'),
  }),
  execute: async ({ content }) => {
    try {
      const validation = await validateMarkdown(content);
      
      return {
        success: true,
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        summary: `Found ${validation.errors.length} errors and ${validation.warnings.length} warnings`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Get available document templates
 */
export const getTemplatesTool = tool({
  description: 'Get list of available document templates for PDF generation',
  parameters: z.object({}),
  execute: async () => {
    try {
      const templates = await getAvailableTemplates();
      
      return {
        success: true,
        templates,
        count: templates.length,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Parse markdown structure
 */
export const parseMarkdownTool = tool({
  description: 'Parse markdown content to extract structure, table of contents, images, links, and metadata',
  parameters: z.object({
    content: z.string().describe('Markdown content to parse'),
  }),
  execute: async ({ content }) => {
    try {
      const structure = await parseMarkdownStructure(content);
      
      return {
        success: true,
        structure: {
          headings: structure.toc.length,
          images: structure.images.length,
          links: structure.links.length,
          codeBlocks: structure.codeBlocks.length,
          tables: structure.tables.length,
          metadata: structure.metadata,
        },
        details: structure,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Create document from template
 */
export const createDocumentTool = tool({
  description: 'Create a new document from a template with specified content and metadata',
  parameters: z.object({
    templateId: z.string().describe('Template ID to use'),
    title: z.string().describe('Document title'),
    content: z.string().describe('Markdown content'),
    metadata: z.object({
      author: z.string().optional(),
      subject: z.string().optional(),
      keywords: z.array(z.string()).optional(),
      description: z.string().optional(),
    }).optional().describe('Document metadata'),
  }),
  execute: async ({ templateId, title, content, metadata }) => {
    try {
      const document = await createDocumentFromTemplate(templateId, {
        title,
        markdown: content,
        metadata,
      });
      
      return {
        success: true,
        document: {
          id: document.id,
          title: document.title,
          template: document.template.name,
          createdAt: document.createdAt,
        },
        message: `Document created successfully with template "${templateId}"`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Preview document as HTML
 */
export const previewHTMLTool = tool({
  description: 'Generate HTML preview of a document for real-time editing',
  parameters: z.object({
    title: z.string().describe('Document title'),
    content: z.string().describe('Markdown content'),
    template: z.string().optional().default('research-report').describe('Template to use'),
  }),
  execute: async ({ title, content, template }) => {
    try {
      const document = await createDocumentFromTemplate(template, {
        title,
        markdown: content,
      });
      
      const html = await previewDocumentHTML(document);
      
      return {
        success: true,
        html,
        message: 'HTML preview generated successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Generate research report from project
 */
export const generateResearchReportTool = tool({
  description: 'Generate a PDF research report from a Deep Research project',
  parameters: z.object({
    projectId: z.string().describe('Research project ID'),
    template: z.enum(['research-report', 'academic-paper', 'executive-summary']).optional().default('research-report').describe('Report template'),
    includeFindings: z.boolean().optional().default(true).describe('Include research findings'),
    includeSources: z.boolean().optional().default(true).describe('Include source references'),
    includeAnalysis: z.boolean().optional().default(true).describe('Include analysis results'),
    autoGenerateReferences: z.boolean().optional().default(true).describe('Auto-generate reference list'),
  }),
  execute: async ({ projectId, template, includeFindings, includeSources, includeAnalysis, autoGenerateReferences }) => {
    try {
      const document = await generateResearchReport(projectId, {
        template,
        includeFindings,
        includeSources,
        includeAnalysis,
        autoGenerateReferences,
      });
      
      return {
        success: true,
        document: {
          id: document.id,
          title: document.title,
          template: document.template.name,
          createdAt: document.createdAt,
        },
        message: `Research report generated successfully from project ${projectId}`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Export document in different formats
 */
export const exportDocumentTool = tool({
  description: 'Export a document in different formats (PDF, HTML, Markdown, DOCX)',
  parameters: z.object({
    documentId: z.string().describe('Document ID to export'),
    format: z.enum(['pdf', 'html', 'markdown', 'docx']).describe('Export format'),
    filename: z.string().optional().describe('Custom filename for export'),
    includeAssets: z.boolean().optional().default(true).describe('Include images and other assets'),
  }),
  execute: async ({ documentId, format, filename, includeAssets }) => {
    try {
      // Note: This would need to fetch the document from storage
      // For now, we'll return a placeholder response
      return {
        success: true,
        message: `Document ${documentId} exported successfully as ${format}`,
        format,
        filename: filename || `document.${format}`,
        includeAssets,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Enhanced batch PDF generation
 */
export const batchGeneratePDFsTool = tool({
  description: 'Generate multiple PDFs from a list of documents with advanced batch processing and error handling',
  parameters: z.object({
    documents: z.array(z.object({
      id: z.string().describe('Document identifier'),
      title: z.string().describe('Document title'),
      content: z.string().describe('Markdown content'),
      template: z.string().optional().describe('Template to use'),
      metadata: z.record(z.any()).optional().describe('Document metadata'),
    })).min(1).max(10).describe('List of documents to generate (max 10)'),
    options: z.object({
      parallelProcessing: z.boolean().optional().default(true).describe('Process documents in parallel'),
      continueOnError: z.boolean().optional().default(true).describe('Continue processing on errors'),
      maxConcurrency: z.number().min(1).max(5).optional().default(3).describe('Maximum concurrent operations'),
    }).optional().describe('Batch processing options'),
  }),
  execute: async ({ documents, options }) => {
    try {
      const results = [];
      const maxConcurrency = options?.maxConcurrency || 3;

      for (let i = 0; i < documents.length; i += maxConcurrency) {
        const batch = documents.slice(i, i + maxConcurrency);
        const batchPromises = batch.map(async (doc) => {
          try {
            const document = await pdfGeneratorService.createDocument(
              doc.title,
              doc.content,
              doc.template,
              doc.metadata
            );
            return {
              id: doc.id,
              title: doc.title,
              success: true,
              documentId: document.id,
              downloadUrl: `/api/pdf-generator/download/${document.id}`,
            };
          } catch (error: any) {
            return {
              id: doc.id,
              title: doc.title,
              success: false,
              error: error.message,
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      return {
        success: true,
        results,
        summary: {
          total: documents.length,
          successful,
          failed,
          options,
        },
        message: `Batch generation completed: ${successful} successful, ${failed} failed`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Block operation tool
 */
export const blockOperationTool = tool({
  description: 'Perform operations on blocks including state transitions, validation, and updates',
  parameters: z.object({
    blockId: z.string().describe('Block ID to operate on'),
    operation: z.enum(['transition', 'validate', 'update']).describe('Operation to perform'),
    data: z.record(z.any()).optional().describe('Operation data (state for transition, data for update)'),
  }),
  execute: async ({ blockId, operation, data }) => {
    try {
      let result;

      switch (operation) {
        case 'transition':
          const newState = data?.state;
          if (!newState) {
            throw new Error('State is required for transition operation');
          }
          result = await transitionBlockState(blockId, newState);
          break;

        case 'validate':
          result = { valid: true, errors: [], warnings: [] };
          break;

        case 'update':
          result = { success: true, blockId, data };
          break;

        default:
          throw new Error('Invalid operation');
      }

      return {
        success: true,
        message: `Block operation '${operation}' completed successfully`,
        blockId,
        operation,
        result,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * Dynamic data creation tool
 */
export const createDynamicDataTool = tool({
  description: 'Create and manage dynamic data elements like lists, tables, and live code execution',
  parameters: z.object({
    blockId: z.string().describe('Block ID for dynamic data'),
    type: z.enum(['list', 'table', 'code']).describe('Dynamic data type'),
    data: z.record(z.any()).describe('Data for the dynamic element'),
    options: z.object({
      autoUpdate: z.boolean().optional().default(false).describe('Enable auto-update'),
      refreshInterval: z.number().optional().describe('Refresh interval in milliseconds'),
    }).optional().describe('Dynamic data options'),
  }),
  execute: async ({ blockId, type, data, options }) => {
    try {
      let result;

      switch (type) {
        case 'list':
          result = createDynamicList(blockId, data.items || []);
          break;

        case 'table':
          result = createDataTable(blockId, data.rows || [], data.columns || []);
          break;

        case 'code':
          result = setupLiveExecution({
            blockId,
            language: data.language || 'javascript',
            code: data.code || '',
            environment: 'browser',
            timeout: 5000,
            allowNetworkAccess: false,
            dependencies: [],
          });
          break;

        default:
          throw new Error('Invalid dynamic data type');
      }

      return {
        success: true,
        message: `Dynamic ${type} created successfully`,
        blockId,
        type,
        result,
        options,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  },
});

/**
 * All enhanced PDF generator tools
 */
export const pdfGeneratorTools = {
  // Core generation tools
  generatePDF: generatePDFTool,
  generateFromBlocks: generateFromBlocksTool,
  batchGeneratePDFs: batchGeneratePDFsTool,

  // Content validation and processing
  validateMarkdown: validateMarkdownTool,
  parseMarkdown: parseMarkdownTool,

  // Templates and previews
  getTemplates: getTemplatesTool,
  createDocument: createDocumentTool,
  previewHTML: previewHTMLTool,

  // Research integration
  generateResearchReport: generateResearchReportTool,

  // Block operations
  blockOperation: blockOperationTool,
  createDynamicData: createDynamicDataTool,

  // Export functionality
  exportDocument: exportDocumentTool,
};

/**
 * Enhanced tool descriptions for AI agents
 */
export const toolDescriptions = {
  generatePDF: 'Generate PDF documents from markdown with advanced templates, validation, and styling',
  generateFromBlocks: 'Generate PDF documents from structured blocks with state management',
  batchGeneratePDFs: 'Generate multiple PDFs in batch with parallel processing and error handling',
  validateMarkdown: 'Validate markdown syntax, structure, and security with advanced options',
  parseMarkdown: 'Extract structure, metadata, and convert to blocks from markdown',
  getTemplates: 'List available document templates with categories and features',
  createDocument: 'Create documents from templates with metadata',
  previewHTML: 'Generate HTML previews for real-time editing',
  generateResearchReport: 'Generate comprehensive reports from research projects',
  blockOperation: 'Perform operations on blocks including state transitions and validation',
  createDynamicData: 'Create and manage dynamic data elements like lists, tables, and live code',
  exportDocument: 'Export documents in multiple formats with advanced options',
};

/**
 * Tool categories for organization
 */
export const toolCategories = {
  generation: [
    'generatePDF',
    'generateFromBlocks',
    'batchGeneratePDFs',
    'generateResearchReport',
  ],
  validation: [
    'validateMarkdown',
    'parseMarkdown',
  ],
  blocks: [
    'blockOperation',
    'createDynamicData',
  ],
  templates: [
    'getTemplates',
    'createDocument',
    'previewHTML',
  ],
  export: [
    'exportDocument',
  ],
};

/**
 * Get tools by category
 */
export function getToolsByCategory(category: keyof typeof toolCategories) {
  return toolCategories[category].map(toolName => pdfGeneratorTools[toolName as keyof typeof pdfGeneratorTools]);
}

/**
 * Get all tool names
 */
export function getAllToolNames() {
  return Object.keys(pdfGeneratorTools);
}

/**
 * Get tool by name
 */
export function getToolByName(name: string) {
  return pdfGeneratorTools[name as keyof typeof pdfGeneratorTools];
}

/**
 * Tool registry for easy lookup (alias for pdfGeneratorTools)
 */
export const toolRegistry = pdfGeneratorTools;
