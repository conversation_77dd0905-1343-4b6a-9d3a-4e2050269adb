/**
 * PDF Generator Basic Tests
 * 
 * Basic test suite to verify the PDF generator library functionality
 */

import { 
  generatePDFFromMarkdown,
  validateMarkdown,
  getAvailableTemplates,
  parseMarkdownStructure,
  createDocumentFromTemplate,
  pdfGeneratorService
} from '../index';

/**
 * Test library imports and basic functionality
 */
export async function testImports() {
  console.log('🧪 Testing library imports...');

  try {
    // Test main service import
    const { pdfGeneratorService } = await import('../core/pdf-generator-service');
    if (!pdfGeneratorService) {
      throw new Error('PDF Generator Service not imported');
    }
    console.log('✅ PDF Generator Service imported');

    // Test main functions
    const { generatePDFFromMarkdown } = await import('../index');
    if (typeof generatePDFFromMarkdown !== 'function') {
      throw new Error('generatePDFFromMarkdown not imported');
    }
    console.log('✅ Main functions imported');

    // Test validation functions
    const { validateMarkdown } = await import('../utils/validation');
    if (typeof validateMarkdown !== 'function') {
      throw new Error('validateMarkdown not imported');
    }
    console.log('✅ Validation functions imported');

    // Test template functions
    const { getAvailableTemplates } = await import('../templates');
    if (typeof getAvailableTemplates !== 'function') {
      throw new Error('getAvailableTemplates not imported');
    }
    console.log('✅ Template functions imported');

    // Test utility functions
    const { parseMarkdownStructure } = await import('../utils');
    if (typeof parseMarkdownStructure !== 'function') {
      throw new Error('parseMarkdownStructure not imported');
    }
    console.log('✅ Utility functions imported');

    // Test component imports
    const { EnhancedBlockEditor } = await import('../components');
    if (!EnhancedBlockEditor) {
      throw new Error('EnhancedBlockEditor not imported');
    }
    console.log('✅ Components imported');

    // Test block system
    const { blockStateManager } = await import('../blocks');
    if (!blockStateManager) {
      throw new Error('Block system not imported');
    }
    console.log('✅ Block system imported');

    return true;
  } catch (error) {
    console.error('❌ Import test failed:', error);
    return false;
  }
}

/**
 * Test template system
 */
export async function testTemplates() {
  console.log('🧪 Testing template system...');

  try {
    const templates = await getAvailableTemplates();
    
    if (templates.length === 0) {
      throw new Error('No templates available');
    }

    console.log(`✅ Found ${templates.length} templates`);
    
    // Test each template
    for (const template of templates) {
      if (!template.id || !template.name) {
        throw new Error(`Invalid template: ${JSON.stringify(template)}`);
      }
    }

    console.log('✅ All templates valid');
    return true;
  } catch (error) {
    console.error('❌ Template test failed:', error);
    return false;
  }
}

/**
 * Test markdown validation
 */
export async function testValidation() {
  console.log('🧪 Testing markdown validation...');

  try {
    // Test valid markdown
    const validMarkdown = '# Test\n\nThis is valid markdown.';
    const validResult = await validateMarkdown(validMarkdown);
    
    if (!validResult.isValid) {
      throw new Error('Valid markdown failed validation');
    }

    console.log('✅ Valid markdown passed validation');

    // Test invalid markdown
    const invalidMarkdown = '# Test\n\n[Invalid link](';
    const invalidResult = await validateMarkdown(invalidMarkdown);
    
    if (invalidResult.isValid) {
      console.log('⚠️  Invalid markdown passed validation (expected)');
    } else {
      console.log('✅ Invalid markdown correctly failed validation');
    }

    return true;
  } catch (error) {
    console.error('❌ Validation test failed:', error);
    return false;
  }
}

/**
 * Test markdown parsing
 */
export async function testParsing() {
  console.log('🧪 Testing markdown parsing...');

  try {
    const markdown = `
# Test Document

## Section 1

This is a test document with:

- Lists
- **Bold text**
- *Italic text*
- \`code\`

### Subsection

\`\`\`javascript
console.log('Hello, World!');
\`\`\`

![Test Image](https://example.com/image.jpg)

[Test Link](https://example.com)

| Column 1 | Column 2 |
|----------|----------|
| Data 1   | Data 2   |
`;

    const structure = await parseMarkdownStructure(markdown);
    
    if (structure.toc.length === 0) {
      throw new Error('No table of contents generated');
    }

    console.log(`✅ Parsed ${structure.toc.length} headings`);
    console.log(`✅ Found ${structure.images.length} images`);
    console.log(`✅ Found ${structure.links.length} links`);
    console.log(`✅ Found ${structure.codeBlocks.length} code blocks`);
    console.log(`✅ Found ${structure.tables.length} tables`);

    return true;
  } catch (error) {
    console.error('❌ Parsing test failed:', error);
    return false;
  }
}

/**
 * Test document creation
 */
export async function testDocumentCreation() {
  console.log('🧪 Testing document creation...');

  try {
    const document = await createDocumentFromTemplate('research-report', {
      title: 'Test Document',
      markdown: '# Test\n\nThis is a test document.',
      metadata: {
        author: 'Test Author',
        subject: 'Testing',
      },
    });

    if (!document.id || !document.title) {
      throw new Error('Invalid document created');
    }

    console.log(`✅ Document created with ID: ${document.id}`);
    console.log(`✅ Template: ${document.template.name}`);

    return true;
  } catch (error) {
    console.error('❌ Document creation test failed:', error);
    return false;
  }
}

/**
 * Test service status
 */
export async function testServiceStatus() {
  console.log('🧪 Testing service status...');

  try {
    const status = pdfGeneratorService.getStatus();
    
    if (typeof status.ready !== 'boolean') {
      throw new Error('Invalid status response');
    }

    console.log(`✅ Service ready: ${status.ready}`);
    console.log(`✅ Active jobs: ${status.activeJobs}`);

    return true;
  } catch (error) {
    console.error('❌ Service status test failed:', error);
    return false;
  }
}

/**
 * Test PDF generation (basic)
 */
export async function testPDFGeneration() {
  console.log('🧪 Testing PDF generation...');

  try {
    const markdown = `
# Test PDF Document

This is a test document for PDF generation.

## Features

- Markdown to PDF conversion
- Template support
- Styling options

## Code Example

\`\`\`javascript
function test() {
  return 'PDF generation works!';
}
\`\`\`

## Conclusion

PDF generation test completed successfully.
`;

    const pdfBuffer = await generatePDFFromMarkdown(
      'Test PDF Document',
      markdown,
      {
        template: 'research-report',
        author: 'Test Suite',
        metadata: {
          subject: 'PDF Generation Test',
          keywords: ['test', 'pdf'],
        },
      }
    );

    if (!pdfBuffer || pdfBuffer.length === 0) {
      throw new Error('PDF generation returned empty buffer');
    }

    console.log(`✅ PDF generated successfully (${pdfBuffer.length} bytes)`);

    return true;
  } catch (error) {
    console.error('❌ PDF generation test failed:', error);
    console.error('   This might be expected if Puppeteer is not properly configured');
    return false;
  }
}

/**
 * Run all tests
 */
export async function runAllTests() {
  console.log('🚀 Running PDF Generator Tests\n');

  const tests = [
    { name: 'Library Imports', fn: testImports },
    { name: 'Template System', fn: testTemplates },
    { name: 'Markdown Validation', fn: testValidation },
    { name: 'Markdown Parsing', fn: testParsing },
    { name: 'Document Creation', fn: testDocumentCreation },
    { name: 'Service Status', fn: testServiceStatus },
    { name: 'PDF Generation', fn: testPDFGeneration },
  ];

  const results: Record<string, boolean> = {};

  for (const test of tests) {
    try {
      console.log(`\n🧪 ${test.name}`);
      console.log('─'.repeat(40));
      
      const result = await test.fn();
      results[test.name] = result;
      
      if (result) {
        console.log(`✅ ${test.name} passed\n`);
      } else {
        console.log(`❌ ${test.name} failed\n`);
      }
    } catch (error) {
      results[test.name] = false;
      console.log(`❌ ${test.name} failed: ${error.message}\n`);
    }
  }

  console.log('📊 Test Summary');
  console.log('─'.repeat(40));
  
  const passed = Object.values(results).filter(r => r).length;
  const failed = Object.values(results).filter(r => !r).length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${tests.length}`);

  const passRate = (passed / tests.length) * 100;
  console.log(`📈 Pass Rate: ${passRate.toFixed(1)}%`);

  return results;
}

// Export for use in other files
export default {
  testImports,
  testTemplates,
  testValidation,
  testParsing,
  testDocumentCreation,
  testServiceStatus,
  testPDFGeneration,
  runAllTests,
};
