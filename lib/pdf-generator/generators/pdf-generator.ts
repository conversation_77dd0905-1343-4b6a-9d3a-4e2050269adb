/**
 * PDF Generator Engine
 * 
 * Production-ready PDF generation using multiple engines (Puppeteer, React-PDF, jsPDF)
 * with support for templates, styling, and optimization.
 */

import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer-core';
import {
  PDFDocument,
  PDFGenerationOptions,
  ParsedMarkdown,
  ExportOptions
} from '../types';
import { config, defaultPDFOptions, puppeteerConfig } from '../config';
import { TemplateRenderer } from '../templates/template-renderer';
import { StyleGenerator } from '../utils/style-generator';
import { ValidationService } from '../utils/validation-service';

export class PDFGenerator {
  private browser: Browser | null = null;
  private templateRenderer: TemplateRenderer;
  private styleGenerator: StyleGenerator;
  private validationService: ValidationService;

  constructor() {
    this.templateRenderer = new TemplateRenderer();
    this.styleGenerator = new StyleGenerator();
    this.validationService = new ValidationService();
  }

  /**
   * Generate PDF from document
   */
  async generatePDF(
    document: PDFDocument,
    parsedMarkdown: ParsedMarkdown,
    options: PDFGenerationOptions = {
      ...defaultPDFOptions,
    }
  ): Promise<Buffer> {
    try {
      // Validate document
      const validationErrors = await this.validationService.validateDocument(document);
      if (validationErrors.length > 0) {
        throw new Error(`Document validation failed: ${validationErrors.map(e => e.message).join(', ')}`);
      }

      // Initialize browser if needed
      if (!this.browser) {
        await this.initializeBrowser();
      }

      // Generate HTML content
      const htmlContent = await this.generateHTML(document, parsedMarkdown);

      // Generate PDF using Puppeteer
      const pdfBuffer = await this.generatePDFFromHTML(htmlContent, document, options);

      return pdfBuffer;
    } catch (error: any) {
      console.error('PDF generation failed:', error);
      throw new Error(`PDF generation failed: ${error.message}`);
    }
  }

  /**
   * Generate HTML content from document and parsed markdown
   */
  async generateHTML(document: PDFDocument, parsedMarkdown: ParsedMarkdown): Promise<string> {
    try {
      // Generate CSS styles
      const styles = this.styleGenerator.generateCSS(document.styling);

      // Render template with content
      const renderedContent = await this.templateRenderer.render(
        document.template,
        {
          document,
          parsedMarkdown,
          metadata: { ...document.metadata, ...parsedMarkdown.metadata },
        }
      );

      // Combine into complete HTML document
      const htmlContent = this.buildCompleteHTML(renderedContent, styles, document);

      return htmlContent;
    } catch (error: any) {
      throw new Error(`HTML generation failed: ${error.message}`);
    }
  }

  /**
   * Generate PDF from HTML using Puppeteer
   */
  private async generatePDFFromHTML(
    html: string,
    document: PDFDocument,
    options: PDFGenerationOptions
  ): Promise<Buffer> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    const page = await this.browser.newPage();

    try {
      // Set viewport and content
      await page.setViewport({ width: 1200, height: 1600 });
      await page.setContent(html, { waitUntil: 'networkidle0' });

      // Configure PDF options
      const pdfOptions = this.buildPDFOptions(document, options);

      // Generate PDF
      const pdfBuffer = await page.pdf(pdfOptions);

      return pdfBuffer as Buffer;
    } finally {
      await page.close();
    }
  }

  /**
   * Build complete HTML document
   */
  private buildCompleteHTML(content: string, styles: string, document: PDFDocument): string {
    const { layout } = document.styling;
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${document.title}</title>
    <style>
        ${this.getBaseStyles()}
        ${styles}
        ${this.getPrintStyles()}
    </style>
</head>
<body>
    <div class="document-container">
        ${content}
    </div>
    ${this.generatePageNumbers(layout)}
</body>
</html>`;
  }

  /**
   * Get base CSS styles
   */
  private getBaseStyles(): string {
    return `
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
        }
        
        .document-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .no-break {
            page-break-inside: avoid;
        }
        
        h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        
        p {
            margin-bottom: 1em;
            orphans: 3;
            widows: 3;
        }
        
        img {
            max-width: 100%;
            height: auto;
            page-break-inside: avoid;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            page-break-inside: avoid;
            margin: 1em 0;
        }
        
        pre, code {
            font-family: 'Courier New', monospace;
            page-break-inside: avoid;
        }
        
        pre {
            background: #f5f5f5;
            padding: 1em;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        blockquote {
            border-left: 4px solid #ddd;
            margin: 1em 0;
            padding-left: 1em;
            font-style: italic;
        }
        
        .toc {
            page-break-after: always;
        }
        
        .toc-entry {
            display: flex;
            justify-content: space-between;
            margin: 0.5em 0;
        }
        
        .toc-title {
            flex: 1;
        }
        
        .toc-page {
            margin-left: 1em;
        }
        
        .cover-page {
            page-break-after: always;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            text-align: center;
        }
        
        .math-inline {
            font-family: 'Times New Roman', serif;
        }
        
        .mermaid {
            text-align: center;
            margin: 1em 0;
        }
    `;
  }

  /**
   * Get print-specific CSS styles
   */
  private getPrintStyles(): string {
    return `
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .no-print {
                display: none !important;
            }
            
            a {
                text-decoration: none;
                color: inherit;
            }
            
            a[href]:after {
                content: " (" attr(href) ")";
                font-size: 0.8em;
                color: #666;
            }
            
            a[href^="#"]:after {
                content: "";
            }
        }
        
        @page {
            margin: 2cm;
            size: A4;
            
            @top-center {
                content: element(header);
            }
            
            @bottom-center {
                content: element(footer);
            }
        }
        
        .header {
            position: running(header);
            text-align: center;
            font-size: 0.9em;
            color: #666;
        }
        
        .footer {
            position: running(footer);
            text-align: center;
            font-size: 0.9em;
            color: #666;
        }
    `;
  }

  /**
   * Generate page numbers
   */
  private generatePageNumbers(layout: any): string {
    if (!layout.footer?.enabled) return '';

    return `
        <div class="footer">
            ${layout.footer.content.replace('{{page}}', '<span class="pageNumber"></span>')
                                   .replace('{{totalPages}}', '<span class="totalPages"></span>')}
        </div>
        <script>
            // This will be processed by Puppeteer
            document.addEventListener('DOMContentLoaded', function() {
                const pageNumbers = document.querySelectorAll('.pageNumber');
                const totalPages = document.querySelectorAll('.totalPages');
                
                pageNumbers.forEach(el => el.textContent = '1');
                totalPages.forEach(el => el.textContent = '1');
            });
        </script>
    `;
  }

  /**
   * Build PDF options for Puppeteer
   */
  private buildPDFOptions(document: PDFDocument, _options: PDFGenerationOptions): any {
    const { layout } = document.styling;
    
    return {
      format: layout.pageSize,
      landscape: layout.orientation === 'landscape',
      margin: {
        top: `${layout.margins.top}px`,
        right: `${layout.margins.right}px`,
        bottom: `${layout.margins.bottom}px`,
        left: `${layout.margins.left}px`,
      },
      printBackground: true,
      preferCSSPageSize: true,
      displayHeaderFooter: layout.header?.enabled || layout.footer?.enabled,
      headerTemplate: layout.header?.enabled ? `
        <div style="font-size: 10px; text-align: center; width: 100%;">
          ${layout.header.content}
        </div>
      ` : '',
      footerTemplate: layout.footer?.enabled ? `
        <div style="font-size: 10px; text-align: center; width: 100%;">
          ${layout.footer.content.replace('{{page}}', '<span class="pageNumber"></span>')
                                 .replace('{{totalPages}}', '<span class="totalPages"></span>')}
        </div>
      ` : '',
      timeout: config.security.maxProcessingTime,
    };
  }

  /**
   * Initialize Puppeteer browser
   */
  private async initializeBrowser(): Promise<void> {
    try {
      this.browser = await puppeteer.launch(puppeteerConfig);
    } catch (error: any) {
      throw new Error(`Failed to initialize browser: ${error.message}`);
    }
  }

  /**
   * Export document in different formats
   */
  async exportDocument(
    document: PDFDocument,
    parsedMarkdown: ParsedMarkdown,
    options: ExportOptions
  ): Promise<Buffer | string> {
    switch (options.format) {
      case 'pdf':
        return this.generatePDF(document, parsedMarkdown);
      
      case 'html':
        return this.generateHTML(document, parsedMarkdown);
      
      case 'markdown':
        return this.exportMarkdown(document, parsedMarkdown);
      
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * Export as markdown
   */
  private exportMarkdown(document: PDFDocument, _parsedMarkdown: ParsedMarkdown): string {
    const frontmatter = this.generateFrontmatter(document.metadata);
    return `${frontmatter}\n\n${document.content}`;
  }

  /**
   * Generate frontmatter for markdown export
   */
  private generateFrontmatter(metadata: any): string {
    const lines = ['---'];
    
    for (const [key, value] of Object.entries(metadata)) {
      if (Array.isArray(value)) {
        lines.push(`${key}: [${value.map(v => `"${v}"`).join(', ')}]`);
      } else if (typeof value === 'string') {
        lines.push(`${key}: "${value}"`);
      } else {
        lines.push(`${key}: ${value}`);
      }
    }
    
    lines.push('---');
    return lines.join('\n');
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  /**
   * Get generation status
   */
  getStatus(): { ready: boolean; browserConnected: boolean } {
    return {
      ready: this.browser !== null,
      browserConnected: this.browser !== null && this.browser.connected,
    };
  }
}
