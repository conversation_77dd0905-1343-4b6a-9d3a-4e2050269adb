/**
 * PDF Generator Configuration
 * 
 * Central configuration for the PDF generation library with environment-based settings,
 * default templates, styling options, and performance tuning.
 */

import { 
  PDFGeneratorConfig, 
  DocumentTemplate, 
  DocumentStyling, 
  FontConfiguration,
  ColorScheme,
  SpacingConfiguration,
  LayoutConfiguration,
  MarkdownParsingOptions,
  PDFGenerationOptions
} from './types';

/**
 * Environment-based configuration
 */
export const config: PDFGeneratorConfig = {
  // Basic settings
  defaultTemplate: process.env.PDF_DEFAULT_TEMPLATE || 'research-report',
  outputDirectory: process.env.PDF_OUTPUT_DIR || './output/pdfs',
  tempDirectory: process.env.PDF_TEMP_DIR || './temp/pdf-generation',
  maxFileSize: parseInt(process.env.PDF_MAX_FILE_SIZE || '50') * 1024 * 1024, // 50MB default
  allowedFormats: ['pdf', 'html', 'docx', 'markdown'],

  // Security configuration
  security: {
    enableSandbox: process.env.PDF_ENABLE_SANDBOX === 'true',
    allowExternalImages: process.env.PDF_ALLOW_EXTERNAL_IMAGES !== 'false',
    allowExternalLinks: process.env.PDF_ALLOW_EXTERNAL_LINKS !== 'false',
    maxProcessingTime: parseInt(process.env.PDF_MAX_PROCESSING_TIME || '300000'), // 5 minutes
  },

  // Performance configuration
  performance: {
    maxConcurrentJobs: parseInt(process.env.PDF_MAX_CONCURRENT_JOBS || '3'),
    cacheEnabled: process.env.PDF_CACHE_ENABLED !== 'false',
    cacheTTL: parseInt(process.env.PDF_CACHE_TTL || '3600'), // 1 hour
    compressionLevel: parseInt(process.env.PDF_COMPRESSION_LEVEL || '6'),
  },
};

/**
 * Default font configurations
 */
export const defaultFonts: FontConfiguration = {
  primary: {
    family: 'Inter, system-ui, sans-serif',
    size: 11,
    weight: 'normal',
    style: 'normal',
    lineHeight: 1.6,
  },
  secondary: {
    family: 'Georgia, serif',
    size: 10,
    weight: 'normal',
    style: 'normal',
    lineHeight: 1.5,
  },
  monospace: {
    family: 'JetBrains Mono, Consolas, monospace',
    size: 9,
    weight: 'normal',
    style: 'normal',
    lineHeight: 1.4,
  },
  headings: {
    h1: {
      family: 'Inter, system-ui, sans-serif',
      size: 24,
      weight: 'bold',
      style: 'normal',
      lineHeight: 1.2,
    },
    h2: {
      family: 'Inter, system-ui, sans-serif',
      size: 20,
      weight: 'bold',
      style: 'normal',
      lineHeight: 1.3,
    },
    h3: {
      family: 'Inter, system-ui, sans-serif',
      size: 16,
      weight: 'bold',
      style: 'normal',
      lineHeight: 1.4,
    },
    h4: {
      family: 'Inter, system-ui, sans-serif',
      size: 14,
      weight: 'bold',
      style: 'normal',
      lineHeight: 1.4,
    },
    h5: {
      family: 'Inter, system-ui, sans-serif',
      size: 12,
      weight: 'bold',
      style: 'normal',
      lineHeight: 1.4,
    },
    h6: {
      family: 'Inter, system-ui, sans-serif',
      size: 11,
      weight: 'bold',
      style: 'normal',
      lineHeight: 1.4,
    },
  },
};

/**
 * Default color schemes
 */
export const colorSchemes: Record<string, ColorScheme> = {
  default: {
    primary: '#2563eb',
    secondary: '#64748b',
    accent: '#0ea5e9',
    background: '#ffffff',
    text: '#1e293b',
    heading: '#0f172a',
    link: '#2563eb',
    border: '#e2e8f0',
  },
  dark: {
    primary: '#3b82f6',
    secondary: '#94a3b8',
    accent: '#06b6d4',
    background: '#0f172a',
    text: '#e2e8f0',
    heading: '#f8fafc',
    link: '#3b82f6',
    border: '#334155',
  },
  academic: {
    primary: '#1e40af',
    secondary: '#6b7280',
    accent: '#059669',
    background: '#ffffff',
    text: '#374151',
    heading: '#111827',
    link: '#1e40af',
    border: '#d1d5db',
  },
  corporate: {
    primary: '#1f2937',
    secondary: '#6b7280',
    accent: '#dc2626',
    background: '#ffffff',
    text: '#374151',
    heading: '#111827',
    link: '#1f2937',
    border: '#e5e7eb',
  },
};

/**
 * Default spacing configuration
 */
export const defaultSpacing: SpacingConfiguration = {
  margin: { top: 72, right: 72, bottom: 72, left: 72 }, // 1 inch margins
  padding: { top: 12, right: 12, bottom: 12, left: 12 },
  lineHeight: 1.6,
  paragraphSpacing: 12,
  sectionSpacing: 24,
};

/**
 * Default layout configuration
 */
export const defaultLayout: LayoutConfiguration = {
  pageSize: 'A4',
  orientation: 'portrait',
  margins: { top: 72, right: 72, bottom: 72, left: 72 },
  columns: 1,
  columnGap: 24,
  header: {
    enabled: false,
    content: '',
    height: 36,
  },
  footer: {
    enabled: true,
    content: 'Page {{page}} of {{totalPages}}',
    height: 36,
  },
};

/**
 * Default document styling
 */
export const defaultStyling: DocumentStyling = {
  fonts: defaultFonts,
  colors: colorSchemes.default,
  spacing: defaultSpacing,
  layout: defaultLayout,
  theme: 'default',
};

/**
 * Default markdown parsing options
 */
export const defaultMarkdownOptions: MarkdownParsingOptions = {
  gfm: true,
  breaks: false,
  tables: true,
  sanitize: true,
  smartypants: true,
  highlight: true,
  math: true,
  mermaid: true,
};

/**
 * Default PDF generation options
 */
export const defaultPDFOptions: PDFGenerationOptions = {
  format: 'pdf',
  quality: 'high',
  compression: true,
  embedFonts: true,
  includeMetadata: true,
  optimization: {
    compressImages: true,
    imageQuality: 85,
    removeUnusedObjects: true,
    linearize: true,
    subsampleImages: false,
  },
};

/**
 * Built-in document templates
 */
export const builtInTemplates: Record<string, Partial<DocumentTemplate>> = {
  'research-report': {
    name: 'Research Report',
    type: 'research-report',
    sections: [
      { id: 'cover', name: 'Cover Page', type: 'cover-page', required: true, order: 1 },
      { id: 'abstract', name: 'Abstract', type: 'abstract', required: false, order: 2 },
      { id: 'toc', name: 'Table of Contents', type: 'table-of-contents', required: true, order: 3 },
      { id: 'introduction', name: 'Introduction', type: 'introduction', required: true, order: 4 },
      { id: 'content', name: 'Main Content', type: 'content', required: true, order: 5 },
      { id: 'conclusion', name: 'Conclusion', type: 'conclusion', required: false, order: 6 },
      { id: 'references', name: 'References', type: 'references', required: false, order: 7 },
      { id: 'appendix', name: 'Appendix', type: 'appendix', required: false, order: 8 },
    ],
  },
  'technical-document': {
    name: 'Technical Documentation',
    type: 'technical-document',
    sections: [
      { id: 'cover', name: 'Cover Page', type: 'cover-page', required: true, order: 1 },
      { id: 'toc', name: 'Table of Contents', type: 'table-of-contents', required: true, order: 2 },
      { id: 'introduction', name: 'Introduction', type: 'introduction', required: true, order: 3 },
      { id: 'content', name: 'Documentation', type: 'content', required: true, order: 4 },
      { id: 'appendix', name: 'Appendix', type: 'appendix', required: false, order: 5 },
    ],
  },
  'article': {
    name: 'Article',
    type: 'article',
    sections: [
      { id: 'content', name: 'Article Content', type: 'content', required: true, order: 1 },
      { id: 'references', name: 'References', type: 'references', required: false, order: 2 },
    ],
  },
  'presentation': {
    name: 'Presentation',
    type: 'presentation',
    sections: [
      { id: 'cover', name: 'Title Slide', type: 'cover-page', required: true, order: 1 },
      { id: 'content', name: 'Slides', type: 'content', required: true, order: 2 },
    ],
  },
};

/**
 * Puppeteer configuration for PDF generation
 */
export const puppeteerConfig = {
  headless: true,
  args: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-accelerated-2d-canvas',
    '--no-first-run',
    '--no-zygote',
    '--single-process',
    '--disable-gpu',
  ],
  timeout: config.security.maxProcessingTime,
};

/**
 * File type mappings
 */
export const mimeTypes = {
  pdf: 'application/pdf',
  html: 'text/html',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  epub: 'application/epub+zip',
  markdown: 'text/markdown',
};

/**
 * Validation rules
 */
export const validationRules = {
  maxTitleLength: 200,
  maxAuthorLength: 100,
  maxDescriptionLength: 1000,
  maxKeywords: 20,
  maxSections: 50,
  maxContentLength: 1000000, // 1MB of text
  allowedImageFormats: ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'],
  maxImageSize: 10 * 1024 * 1024, // 10MB
  maxImages: 100,
};

/**
 * Error messages
 */
export const errorMessages = {
  INVALID_TEMPLATE: 'Invalid or missing template',
  INVALID_CONTENT: 'Invalid markdown content',
  MISSING_REQUIRED_SECTION: 'Missing required section',
  INVALID_STYLING: 'Invalid styling configuration',
  GENERATION_FAILED: 'PDF generation failed',
  PARSING_FAILED: 'Markdown parsing failed',
  VALIDATION_FAILED: 'Document validation failed',
  TEMPLATE_NOT_FOUND: 'Template not found',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions',
  FILE_TOO_LARGE: 'File size exceeds maximum limit',
  INVALID_FORMAT: 'Invalid export format',
  PROCESSING_TIMEOUT: 'Processing timeout exceeded',
};

/**
 * Success messages
 */
export const successMessages = {
  DOCUMENT_CREATED: 'Document created successfully',
  DOCUMENT_UPDATED: 'Document updated successfully',
  PDF_GENERATED: 'PDF generated successfully',
  TEMPLATE_APPLIED: 'Template applied successfully',
  VALIDATION_PASSED: 'Document validation passed',
  EXPORT_COMPLETED: 'Export completed successfully',
};

/**
 * Get configuration with environment overrides
 */
export function getConfig(): PDFGeneratorConfig {
  return { ...config };
}

/**
 * Get template by ID
 */
export function getTemplate(templateId: string): Partial<DocumentTemplate> | null {
  return builtInTemplates[templateId] || null;
}

/**
 * Get color scheme by name
 */
export function getColorScheme(schemeName: string): ColorScheme {
  return colorSchemes[schemeName] || colorSchemes.default;
}

/**
 * Validate configuration
 */
export function validateConfig(config: Partial<PDFGeneratorConfig>): boolean {
  // Add validation logic here
  return true;
}
