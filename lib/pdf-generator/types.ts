/**
 * PDF Generator Library Types
 * 
 * Comprehensive TypeScript interfaces for PDF document generation,
 * editing, and management with markdown support.
 */

// Core document types
export interface PDFDocument {
  id: string;
  title: string;
  content: string; // Markdown content
  metadata: DocumentMetadata;
  template: DocumentTemplate;
  styling: DocumentStyling;
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

export interface DocumentMetadata {
  title: string;
  author: string;
  subject?: string;
  keywords?: string[];
  creator: string;
  producer: string;
  creationDate: Date;
  modificationDate: Date;
  tags?: string[];
  category?: string;
  language?: string;
  description?: string;
}

export interface DocumentTemplate {
  id: string;
  name: string;
  type: TemplateType;
  layout: PageLayout;
  sections: TemplateSection[];
  styling: TemplateStyling;
  variables?: Record<string, any>;
}

export type TemplateType = 
  | 'research-report'
  | 'technical-document'
  | 'presentation'
  | 'article'
  | 'book'
  | 'manual'
  | 'custom';

export interface TemplateSection {
  id: string;
  name: string;
  type: SectionType;
  required: boolean;
  order: number;
  content?: string;
  styling?: SectionStyling;
}

export type SectionType =
  | 'cover-page'
  | 'table-of-contents'
  | 'abstract'
  | 'introduction'
  | 'content'
  | 'conclusion'
  | 'references'
  | 'appendix'
  | 'custom';

// Styling and layout types
export interface DocumentStyling {
  fonts: FontConfiguration;
  colors: ColorScheme;
  spacing: SpacingConfiguration;
  layout: LayoutConfiguration;
  theme: string;
}

export interface FontConfiguration {
  primary: FontDefinition;
  secondary: FontDefinition;
  monospace: FontDefinition;
  headings: Record<string, FontDefinition>;
}

export interface FontDefinition {
  family: string;
  size: number;
  weight: FontWeight;
  style: FontStyle;
  lineHeight?: number;
}

export type FontWeight = 'normal' | 'bold' | 'lighter' | 'bolder' | number;
export type FontStyle = 'normal' | 'italic' | 'oblique';

export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  heading: string;
  link: string;
  border: string;
}

export interface SpacingConfiguration {
  margin: Spacing;
  padding: Spacing;
  lineHeight: number;
  paragraphSpacing: number;
  sectionSpacing: number;
}

export interface Spacing {
  top: number;
  right: number;
  bottom: number;
  left: number;
}

export interface LayoutConfiguration {
  pageSize: PageSize;
  orientation: PageOrientation;
  margins: Spacing;
  columns: number;
  columnGap: number;
  header: HeaderFooterConfig;
  footer: HeaderFooterConfig;
}

export type PageSize = 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
export type PageOrientation = 'portrait' | 'landscape';

export interface HeaderFooterConfig {
  enabled: boolean;
  content: string;
  height: number;
  styling?: Partial<DocumentStyling>;
}

export interface PageLayout {
  size: PageSize;
  orientation: PageOrientation;
  margins: Spacing;
  header?: HeaderFooterConfig;
  footer?: HeaderFooterConfig;
}

export interface TemplateStyling {
  coverPage?: CoverPageStyling;
  tableOfContents?: TOCStyling;
  content?: ContentStyling;
}

export interface CoverPageStyling {
  titleFont: FontDefinition;
  subtitleFont: FontDefinition;
  authorFont: FontDefinition;
  dateFont: FontDefinition;
  backgroundColor: string;
  textColor: string;
  alignment: 'left' | 'center' | 'right';
}

export interface TOCStyling {
  titleFont: FontDefinition;
  entryFont: FontDefinition;
  indentation: number;
  dotLeader: boolean;
  pageNumbers: boolean;
}

export interface ContentStyling {
  headingStyles: Record<string, FontDefinition>;
  paragraphStyle: FontDefinition;
  listStyle: ListStyling;
  codeBlockStyle: CodeBlockStyling;
  tableStyle: TableStyling;
}

export interface SectionStyling {
  font?: FontDefinition;
  color?: string;
  backgroundColor?: string;
  margin?: Spacing;
  padding?: Spacing;
  border?: BorderStyling;
}

export interface ListStyling {
  bulletStyle: string;
  numberStyle: string;
  indentation: number;
  spacing: number;
}

export interface CodeBlockStyling {
  font: FontDefinition;
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  borderRadius: number;
  padding: Spacing;
  syntax: boolean;
}

export interface TableStyling {
  headerFont: FontDefinition;
  cellFont: FontDefinition;
  borderColor: string;
  borderWidth: number;
  headerBackground: string;
  alternateRowBackground: string;
  cellPadding: Spacing;
}

export interface BorderStyling {
  width: number;
  style: 'solid' | 'dashed' | 'dotted';
  color: string;
  radius?: number;
}

// Generation and processing types
export interface PDFGenerationOptions {
  format: PDFFormat;
  quality: PDFQuality;
  compression: boolean;
  embedFonts: boolean;
  includeMetadata: boolean;
  watermark?: WatermarkOptions;
  security?: SecurityOptions;
  optimization?: OptimizationOptions;
}

export type PDFFormat = 'pdf' | 'pdf/a' | 'pdf/x';
export type PDFQuality = 'low' | 'medium' | 'high' | 'print';

export interface WatermarkOptions {
  text: string;
  opacity: number;
  rotation: number;
  position: WatermarkPosition;
  font: FontDefinition;
  color: string;
}

export type WatermarkPosition = 
  | 'center'
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right';

export interface SecurityOptions {
  userPassword?: string;
  ownerPassword?: string;
  permissions: PDFPermissions;
}

export interface PDFPermissions {
  printing: boolean;
  copying: boolean;
  editing: boolean;
  annotating: boolean;
  formFilling: boolean;
  accessibility: boolean;
  assembly: boolean;
}

export interface OptimizationOptions {
  compressImages: boolean;
  imageQuality: number;
  removeUnusedObjects: boolean;
  linearize: boolean;
  subsampleImages: boolean;
}

// Parsing and processing types
export interface MarkdownParsingOptions {
  gfm: boolean; // GitHub Flavored Markdown
  breaks: boolean;
  tables: boolean;
  sanitize: boolean;
  smartypants: boolean;
  highlight: boolean;
  math: boolean;
  mermaid: boolean;
  customRenderers?: Record<string, Function>;
}

export interface ParsedMarkdown {
  html: string;
  toc: TableOfContentsEntry[];
  metadata: Record<string, any>;
  images: ImageReference[];
  links: LinkReference[];
  codeBlocks: CodeBlockReference[];
  tables: TableReference[];
}

export interface TableOfContentsEntry {
  id: string;
  level: number;
  title: string;
  anchor: string;
  page?: number;
  children?: TableOfContentsEntry[];
}

export interface ImageReference {
  src: string;
  alt: string;
  title?: string;
  width?: number;
  height?: number;
  position: number;
}

export interface LinkReference {
  href: string;
  text: string;
  title?: string;
  position: number;
}

export interface CodeBlockReference {
  language: string;
  code: string;
  position: number;
  highlighted?: string;
}

export interface TableReference {
  headers: string[];
  rows: string[][];
  position: number;
  styling?: TableStyling;
}

// Editor and UI types
export interface PDFEditorState {
  document: PDFDocument;
  currentSection: string;
  previewMode: PreviewMode;
  isEditing: boolean;
  isDirty: boolean;
  lastSaved: Date;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export type PreviewMode = 'markdown' | 'html' | 'pdf' | 'split';

export interface ValidationError {
  id: string;
  type: ErrorType;
  message: string;
  line?: number;
  column?: number;
  severity: 'error' | 'warning' | 'info';
}

export interface ValidationWarning {
  id: string;
  message: string;
  suggestion?: string;
  line?: number;
  column?: number;
}

export type ErrorType =
  | 'syntax'
  | 'reference'
  | 'image'
  | 'link'
  | 'template'
  | 'styling'
  | 'metadata';

// Export and integration types
export interface ExportOptions {
  format: ExportFormat;
  destination: ExportDestination;
  filename?: string;
  includeAssets: boolean;
  bundleAssets: boolean;
}

export type ExportFormat = 'pdf' | 'html' | 'docx' | 'epub' | 'markdown';
export type ExportDestination = 'download' | 'storage' | 'email' | 'cloud';

export interface BatchGenerationOptions {
  documents: string[]; // Document IDs
  template?: string; // Template ID
  outputFormat: ExportFormat;
  mergeDocuments: boolean;
  parallelProcessing: boolean;
  progressCallback?: (progress: BatchProgress) => void;
}

export interface BatchProgress {
  total: number;
  completed: number;
  current: string;
  errors: string[];
  warnings: string[];
}

// Integration with Deep Research
export interface ResearchIntegrationOptions {
  projectId: string;
  includeFindings: boolean;
  includeSources: boolean;
  includeAnalysis: boolean;
  template: 'research-report' | 'academic-paper' | 'executive-summary';
  autoGenerateReferences: boolean;
}

// Service and API types
export interface PDFGeneratorConfig {
  defaultTemplate: string;
  outputDirectory: string;
  tempDirectory: string;
  maxFileSize: number;
  allowedFormats: ExportFormat[];
  security: SecurityConfig;
  performance: PerformanceConfig;
}

export interface SecurityConfig {
  enableSandbox: boolean;
  allowExternalImages: boolean;
  allowExternalLinks: boolean;
  maxProcessingTime: number;
}

export interface PerformanceConfig {
  maxConcurrentJobs: number;
  cacheEnabled: boolean;
  cacheTTL: number;
  compressionLevel: number;
}
