/**
 * Nodebox Client API
 * 
 * Client-side API wrapper for interacting with Nodebox instances
 */

import {
  NodeboxConfig,
  NodeboxInstance,
  FileEntry,
  ShellProcess,
  PreviewInfo,
  NodeboxError
} from './nodebox-types';

/**
 * NodeboxClient
 * 
 * Provides a client-side API for interacting with Nodebox instances via HTTP
 */
export class NodeboxClient {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/nodebox') {
    this.baseUrl = baseUrl;
  }

  /**
   * Create a new Nodebox instance
   */
  async createInstance(config: Omit<NodeboxConfig, 'instanceId'>): Promise<NodeboxInstance> {
    try {
      const response = await fetch(`${this.baseUrl}/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to create instance: ${response.status} ${response.statusText}`,
          undefined,
          'CREATE_FAILED'
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error creating instance: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * Get an existing Nodebox instance
   */
  async getInstance(instanceId: string): Promise<NodeboxInstance> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}`);

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to get instance: ${response.status} ${response.statusText}`,
          instanceId,
          'GET_FAILED'
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error getting instance: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * List all Nodebox instances
   */
  async listInstances(): Promise<NodeboxInstance[]> {
    try {
      const response = await fetch(`${this.baseUrl}/list`);

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to list instances: ${response.status} ${response.statusText}`,
          undefined,
          'LIST_FAILED'
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error listing instances: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * Destroy a Nodebox instance
   */
  async destroyInstance(instanceId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to destroy instance: ${response.status} ${response.statusText}`,
          instanceId,
          'DESTROY_FAILED'
        );
      }

      const result = await response.json();
      return result.success || false;
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error destroying instance: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * Read a file from an instance
   */
  async readFile(instanceId: string, filePath: string): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}/files/${encodeURIComponent(filePath)}`);

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to read file: ${response.status} ${response.statusText}`,
          instanceId,
          'FILE_READ_FAILED'
        );
      }

      const result = await response.json();
      return result.content;
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error reading file: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * Write a file to an instance
   */
  async writeFile(instanceId: string, filePath: string, content: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}/files/${encodeURIComponent(filePath)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content }),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to write file: ${response.status} ${response.statusText}`,
          instanceId,
          'FILE_WRITE_FAILED'
        );
      }
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error writing file: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * List files in an instance
   */
  async listFiles(instanceId: string, directory: string = '/'): Promise<FileEntry[]> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}/files?dir=${encodeURIComponent(directory)}`);

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to list files: ${response.status} ${response.statusText}`,
          instanceId,
          'FILE_LIST_FAILED'
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error listing files: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * Execute a command in an instance
   */
  async executeCommand(instanceId: string, command: string, args: string[] = []): Promise<ShellProcess> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}/shell/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ command, args }),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to execute command: ${response.status} ${response.statusText}`,
          instanceId,
          'COMMAND_FAILED'
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error executing command: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * Get process information
   */
  async getProcess(instanceId: string, processId: string): Promise<ShellProcess> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}/shell/process/${processId}`);

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to get process: ${response.status} ${response.statusText}`,
          instanceId,
          'PROCESS_GET_FAILED'
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error getting process: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * Kill a process
   */
  async killProcess(instanceId: string, processId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}/shell/process/${processId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to kill process: ${response.status} ${response.statusText}`,
          instanceId,
          'PROCESS_KILL_FAILED'
        );
      }

      const result = await response.json();
      return result.success || false;
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error killing process: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * Get preview information
   */
  async getPreview(instanceId: string, processId: string): Promise<PreviewInfo> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}/preview/${processId}`);

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to get preview: ${response.status} ${response.statusText}`,
          instanceId,
          'PREVIEW_GET_FAILED'
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error getting preview: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }

  /**
   * Get instance statistics
   */
  async getInstanceStats(instanceId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/${instanceId}/stats`);

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new NodeboxError(
          error.message || `Failed to get stats: ${response.status} ${response.statusText}`,
          instanceId,
          'STATS_GET_FAILED'
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof NodeboxError) {
        throw error;
      }
      throw new NodeboxError(
        `Network error getting stats: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'NETWORK_ERROR'
      );
    }
  }
}
