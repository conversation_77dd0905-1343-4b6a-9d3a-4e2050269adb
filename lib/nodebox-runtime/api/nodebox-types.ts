/**
 * Nodebox Runtime Types and Interfaces
 * 
 * TypeScript definitions for Nodebox integration
 */

import { Nodebox } from '@codesandbox/nodebox';

// Core Nodebox instance configuration
export interface NodeboxConfig {
  instanceId: string;
  projectId?: string;
  name: string;
  description?: string;
  template?: ProjectTemplate;
  environment?: Record<string, string>;
  settings?: NodeboxSettings;
}

// Nodebox instance settings
export interface NodeboxSettings {
  autoSave?: boolean;
  autoPreview?: boolean;
  memoryLimit?: number;
  timeoutMs?: number;
  allowNetworking?: boolean;
  enableHotReload?: boolean;
}

// Project template types
export type ProjectTemplate = 
  | 'react'
  | 'nextjs'
  | 'express'
  | 'vanilla-js'
  | 'typescript'
  | 'research-dashboard'
  | 'data-analysis'
  | 'custom';

// File system entry
export interface FileEntry {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  lastModified?: Date;
  content?: string;
  children?: FileEntry[];
}

// Shell process information
export interface ShellProcess {
  id: string;
  command: string;
  args: string[];
  status: 'running' | 'completed' | 'failed' | 'killed';
  pid?: number;
  startTime: Date;
  endTime?: Date;
  exitCode?: number;
  output: string[];
  errors: string[];
}

// Preview information
export interface PreviewInfo {
  id: string;
  url: string;
  title?: string;
  status: 'loading' | 'ready' | 'error';
  port?: number;
  processId?: string;
}

// Nodebox instance state
export interface NodeboxInstance {
  id: string;
  config: NodeboxConfig;
  runtime: Nodebox;
  status: 'initializing' | 'ready' | 'running' | 'stopped' | 'error';
  createdAt: Date;
  lastActivity: Date;
  filesystemManager: any; // Will be typed properly when implemented
  shellManager: any; // Will be typed properly when implemented
  previewManager: any; // Will be typed properly when implemented
  error?: string;
}

// Events emitted by Nodebox instances
export interface NodeboxEvents {
  'instance-created': (instance: NodeboxInstance) => void;
  'instance-destroyed': (instanceId: string) => void;
  'instance-error': (instanceId: string, error: Error) => void;
  'file-changed': (instanceId: string, filePath: string, content: string) => void;
  'process-started': (instanceId: string, process: ShellProcess) => void;
  'process-completed': (instanceId: string, process: ShellProcess) => void;
  'preview-ready': (instanceId: string, preview: PreviewInfo) => void;
}

// Template configuration
export interface TemplateConfig {
  name: string;
  description: string;
  files: Record<string, string>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  scripts?: Record<string, string>;
  startCommand?: string;
  buildCommand?: string;
  previewPort?: number;
}

// Error types
export class NodeboxError extends Error {
  constructor(
    message: string,
    public instanceId?: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'NodeboxError';
  }
}

export class NodeboxFileSystemError extends NodeboxError {
  constructor(message: string, instanceId?: string, filePath?: string) {
    super(message, instanceId, 'FILESYSTEM_ERROR', { filePath });
    this.name = 'NodeboxFileSystemError';
  }
}

export class NodeboxShellError extends NodeboxError {
  constructor(message: string, instanceId?: string, command?: string) {
    super(message, instanceId, 'SHELL_ERROR', { command });
    this.name = 'NodeboxShellError';
  }
}

export class NodeboxPreviewError extends NodeboxError {
  constructor(message: string, instanceId?: string, previewId?: string) {
    super(message, instanceId, 'PREVIEW_ERROR', { previewId });
    this.name = 'NodeboxPreviewError';
  }
}

// Utility types
export type NodeboxEventHandler<T extends keyof NodeboxEvents> = NodeboxEvents[T];

export interface NodeboxManagerOptions {
  maxInstances?: number;
  defaultSettings?: NodeboxSettings;
  autoCleanup?: boolean;
  cleanupInterval?: number;
}
