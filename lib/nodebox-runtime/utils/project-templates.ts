/**
 * Project Templates
 * 
 * Pre-configured project templates for Nodebox instances
 */

import { TemplateConfig, ProjectTemplate } from '../api/nodebox-types';

/**
 * React Template
 */
export const REACT_TEMPLATE: TemplateConfig = {
  name: 'React Application',
  description: 'A basic React application with modern tooling',
  files: {
    'package.json': JSON.stringify({
      name: 'react-app',
      version: '1.0.0',
      private: true,
      dependencies: {
        'react': '^18.2.0',
        'react-dom': '^18.2.0',
        'react-scripts': '^5.0.1'
      },
      scripts: {
        'start': 'react-scripts start',
        'build': 'react-scripts build',
        'test': 'react-scripts test',
        'eject': 'react-scripts eject'
      },
      browserslist: {
        production: ['>0.2%', 'not dead', 'not op_mini all'],
        development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version']
      }
    }, null, 2),
    'public/index.html': `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="React app created with Nodebox" />
    <title>React App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>`,
    'src/index.js': `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`,
    'src/App.js': `import React, { useState } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);

  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to React with Nodebox!</h1>
        <p>
          Edit <code>src/App.js</code> and save to reload.
        </p>
        <div className="counter">
          <button onClick={() => setCount(count - 1)}>-</button>
          <span>Count: {count}</span>
          <button onClick={() => setCount(count + 1)}>+</button>
        </div>
      </header>
    </div>
  );
}

export default App;`,
    'src/App.css': `.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

.counter {
  margin: 20px 0;
}

.counter button {
  margin: 0 10px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  background-color: #61dafb;
  border: none;
  border-radius: 4px;
  color: #282c34;
}

.counter span {
  margin: 0 20px;
  font-size: 18px;
}`,
    'src/index.css': `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}`
  },
  startCommand: 'start',
  buildCommand: 'build',
  previewPort: 3000
};

/**
 * Next.js Template
 */
export const NEXTJS_TEMPLATE: TemplateConfig = {
  name: 'Next.js Application',
  description: 'A Next.js application with TypeScript support',
  files: {
    'package.json': JSON.stringify({
      name: 'nextjs-app',
      version: '1.0.0',
      private: true,
      scripts: {
        'dev': 'next dev',
        'build': 'next build',
        'start': 'next start',
        'lint': 'next lint'
      },
      dependencies: {
        'next': '^13.4.19',
        'react': '^18.2.0',
        'react-dom': '^18.2.0'
      },
      devDependencies: {
        '@types/node': '^20.5.1',
        '@types/react': '^18.2.20',
        '@types/react-dom': '^18.2.7',
        'typescript': '^5.1.6'
      }
    }, null, 2),
    'next.config.js': `/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
}

module.exports = nextConfig`,
    'tsconfig.json': JSON.stringify({
      compilerOptions: {
        target: 'es5',
        lib: ['dom', 'dom.iterable', 'es6'],
        allowJs: true,
        skipLibCheck: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        noEmit: true,
        esModuleInterop: true,
        module: 'esnext',
        moduleResolution: 'node',
        resolveJsonModule: true,
        isolatedModules: true,
        jsx: 'preserve',
        incremental: true,
        plugins: [{ name: 'next' }],
        paths: { '@/*': ['./src/*'] }
      },
      include: ['next-env.d.ts', '**/*.ts', '**/*.tsx', '.next/types/**/*.ts'],
      exclude: ['node_modules']
    }, null, 2),
    'src/app/layout.tsx': `export const metadata = {
  title: 'Next.js with Nodebox',
  description: 'A Next.js app running in the browser',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}`,
    'src/app/page.tsx': `'use client'

import { useState } from 'react'

export default function Home() {
  const [count, setCount] = useState(0)

  return (
    <main style={{ padding: '2rem', textAlign: 'center' }}>
      <h1>Welcome to Next.js with Nodebox!</h1>
      <p>This is a Next.js application running entirely in your browser.</p>
      
      <div style={{ margin: '2rem 0' }}>
        <button 
          onClick={() => setCount(count - 1)}
          style={{ margin: '0 1rem', padding: '0.5rem 1rem' }}
        >
          -
        </button>
        <span>Count: {count}</span>
        <button 
          onClick={() => setCount(count + 1)}
          style={{ margin: '0 1rem', padding: '0.5rem 1rem' }}
        >
          +
        </button>
      </div>

      <p>
        Edit <code>src/app/page.tsx</code> and save to see changes!
      </p>
    </main>
  )
}`
  },
  startCommand: 'dev',
  buildCommand: 'build',
  previewPort: 3000
};

/**
 * Express.js Template
 */
export const EXPRESS_TEMPLATE: TemplateConfig = {
  name: 'Express.js Server',
  description: 'A basic Express.js server with API routes',
  files: {
    'package.json': JSON.stringify({
      name: 'express-server',
      version: '1.0.0',
      description: 'Express server running in Nodebox',
      main: 'server.js',
      scripts: {
        'start': 'node server.js',
        'dev': 'node server.js'
      },
      dependencies: {
        'express': '^4.18.2',
        'cors': '^2.8.5'
      }
    }, null, 2),
    'server.js': `const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Routes
app.get('/', (req, res) => {
  res.json({ 
    message: 'Welcome to Express.js with Nodebox!',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/hello', (req, res) => {
  res.json({ message: 'Hello from the API!' });
});

app.get('/api/time', (req, res) => {
  res.json({ 
    time: new Date().toISOString(),
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  });
});

app.post('/api/echo', (req, res) => {
  res.json({ 
    echo: req.body,
    received_at: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
  console.log(\`Visit http://localhost:\${PORT} to see your app\`);
});`,
    'public/index.html': `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Express.js with Nodebox</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 2rem; }
        .api-test { margin: 1rem 0; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 0.5rem 1rem; margin: 0.5rem; cursor: pointer; }
        pre { background: #f5f5f5; padding: 1rem; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Express.js Server with Nodebox</h1>
    <p>This Express.js server is running entirely in your browser!</p>
    
    <div class="api-test">
        <h3>API Tests</h3>
        <button onclick="testAPI('/api/hello')">Test Hello API</button>
        <button onclick="testAPI('/api/time')">Test Time API</button>
        <button onclick="testEcho()">Test Echo API</button>
        <div id="result"></div>
    </div>

    <script>
        async function testAPI(endpoint) {
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                document.getElementById('result').innerHTML = 
                    \`<h4>Response from \${endpoint}:</h4><pre>\${JSON.stringify(data, null, 2)}</pre>\`;
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    \`<h4>Error:</h4><pre>\${error.message}</pre>\`;
            }
        }

        async function testEcho() {
            try {
                const response = await fetch('/api/echo', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: 'Hello from the frontend!', timestamp: new Date() })
                });
                const data = await response.json();
                document.getElementById('result').innerHTML = 
                    \`<h4>Response from /api/echo:</h4><pre>\${JSON.stringify(data, null, 2)}</pre>\`;
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    \`<h4>Error:</h4><pre>\${error.message}</pre>\`;
            }
        }
    </script>
</body>
</html>`
  },
  startCommand: 'dev',
  previewPort: 3000
};

/**
 * Research Dashboard Template
 */
export const RESEARCH_DASHBOARD_TEMPLATE: TemplateConfig = {
  name: 'Research Dashboard',
  description: 'A data analysis dashboard for research projects',
  files: {
    'package.json': JSON.stringify({
      name: 'research-dashboard',
      version: '1.0.0',
      description: 'Research data analysis dashboard',
      main: 'server.js',
      scripts: {
        'start': 'node server.js',
        'dev': 'node server.js'
      },
      dependencies: {
        'express': '^4.18.2',
        'cors': '^2.8.5'
      }
    }, null, 2),
    'server.js': `const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Mock research data
const researchData = {
  projects: [
    { id: 1, name: 'AI Ethics Study', status: 'active', progress: 75 },
    { id: 2, name: 'Climate Data Analysis', status: 'completed', progress: 100 },
    { id: 3, name: 'Social Media Impact', status: 'planning', progress: 25 }
  ],
  metrics: {
    totalProjects: 3,
    activeProjects: 1,
    completedProjects: 1,
    totalDataPoints: 15420
  }
};

app.get('/api/research/projects', (req, res) => {
  res.json(researchData.projects);
});

app.get('/api/research/metrics', (req, res) => {
  res.json(researchData.metrics);
});

app.listen(PORT, () => {
  console.log(\`Research Dashboard running on port \${PORT}\`);
});`,
    'public/index.html': `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Research Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 2rem; background: #f5f5f5; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; }
        .card { background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric { text-align: center; }
        .metric-value { font-size: 2rem; font-weight: bold; color: #2563eb; }
        .project { padding: 1rem; border-left: 4px solid #2563eb; margin: 0.5rem 0; background: #f8fafc; }
        .progress-bar { width: 100%; height: 8px; background: #e5e7eb; border-radius: 4px; overflow: hidden; }
        .progress-fill { height: 100%; background: #10b981; transition: width 0.3s ease; }
    </style>
</head>
<body>
    <h1>Research Dashboard</h1>
    <div class="dashboard">
        <div class="card">
            <h3>Metrics</h3>
            <div id="metrics"></div>
        </div>
        <div class="card">
            <h3>Active Projects</h3>
            <div id="projects"></div>
        </div>
    </div>

    <script>
        async function loadDashboard() {
            try {
                const [metricsRes, projectsRes] = await Promise.all([
                    fetch('/api/research/metrics'),
                    fetch('/api/research/projects')
                ]);
                
                const metrics = await metricsRes.json();
                const projects = await projectsRes.json();
                
                // Display metrics
                document.getElementById('metrics').innerHTML = \`
                    <div class="metric">
                        <div class="metric-value">\${metrics.totalProjects}</div>
                        <div>Total Projects</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">\${metrics.activeProjects}</div>
                        <div>Active Projects</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">\${metrics.totalDataPoints.toLocaleString()}</div>
                        <div>Data Points</div>
                    </div>
                \`;
                
                // Display projects
                document.getElementById('projects').innerHTML = projects.map(project => \`
                    <div class="project">
                        <h4>\${project.name}</h4>
                        <p>Status: \${project.status}</p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: \${project.progress}%"></div>
                        </div>
                        <small>\${project.progress}% complete</small>
                    </div>
                \`).join('');
                
            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }
        
        loadDashboard();
    </script>
</body>
</html>`
  },
  startCommand: 'dev',
  previewPort: 3000
};

/**
 * Template registry
 */
export const TEMPLATES: Record<ProjectTemplate, TemplateConfig> = {
  'react': REACT_TEMPLATE,
  'nextjs': NEXTJS_TEMPLATE,
  'express': EXPRESS_TEMPLATE,
  'research-dashboard': RESEARCH_DASHBOARD_TEMPLATE,
  'vanilla-js': {
    name: 'Vanilla JavaScript',
    description: 'A simple HTML/CSS/JS project',
    files: {
      'index.html': `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vanilla JS with Nodebox</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Vanilla JavaScript with Nodebox</h1>
        <p>A simple web page running in the browser!</p>
        <button id="clickBtn">Click me!</button>
        <p id="output"></p>
    </div>
    <script src="script.js"></script>
</body>
</html>`,
      'style.css': `body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

button {
    padding: 1rem 2rem;
    font-size: 1rem;
    background: white;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

button:hover {
    transform: scale(1.05);
}`,
      'script.js': `document.getElementById('clickBtn').addEventListener('click', function() {
    const output = document.getElementById('output');
    const messages = [
        'Hello from Nodebox!',
        'JavaScript is running in the browser!',
        'This is pretty cool!',
        'No servers needed!',
        'Browser-based development rocks!'
    ];
    
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    output.textContent = randomMessage;
    output.style.fontSize = '1.2rem';
    output.style.marginTop = '1rem';
});`
    },
    previewPort: 3000
  },
  'typescript': {
    name: 'TypeScript Project',
    description: 'A TypeScript project with modern tooling',
    files: {
      'package.json': JSON.stringify({
        name: 'typescript-project',
        version: '1.0.0',
        scripts: {
          'build': 'tsc',
          'start': 'node dist/index.js',
          'dev': 'tsc && node dist/index.js'
        },
        devDependencies: {
          'typescript': '^5.1.6',
          '@types/node': '^20.5.1'
        }
      }, null, 2),
      'tsconfig.json': JSON.stringify({
        compilerOptions: {
          target: 'ES2020',
          module: 'commonjs',
          outDir: './dist',
          rootDir: './src',
          strict: true,
          esModuleInterop: true,
          skipLibCheck: true,
          forceConsistentCasingInFileNames: true
        },
        include: ['src/**/*'],
        exclude: ['node_modules', 'dist']
      }, null, 2),
      'src/index.ts': `interface User {
  id: number;
  name: string;
  email: string;
}

class UserManager {
  private users: User[] = [];

  addUser(user: User): void {
    this.users.push(user);
    console.log(\`Added user: \${user.name}\`);
  }

  getUser(id: number): User | undefined {
    return this.users.find(user => user.id === id);
  }

  getAllUsers(): User[] {
    return [...this.users];
  }
}

// Example usage
const userManager = new UserManager();

userManager.addUser({
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>'
});

userManager.addUser({
  id: 2,
  name: 'Jane Smith',
  email: '<EMAIL>'
});

console.log('All users:', userManager.getAllUsers());
console.log('User 1:', userManager.getUser(1));

console.log('TypeScript is running in Nodebox!');`
    },
    startCommand: 'dev',
    buildCommand: 'build'
  },
  'data-analysis': {
    name: 'Data Analysis',
    description: 'A Node.js project for data analysis and visualization',
    files: {
      'package.json': JSON.stringify({
        name: 'data-analysis',
        version: '1.0.0',
        scripts: {
          'start': 'node analysis.js',
          'dev': 'node analysis.js'
        }
      }, null, 2),
      'analysis.js': `// Simple data analysis example
const data = [
  { name: 'Product A', sales: 1200, category: 'Electronics' },
  { name: 'Product B', sales: 800, category: 'Clothing' },
  { name: 'Product C', sales: 1500, category: 'Electronics' },
  { name: 'Product D', sales: 600, category: 'Books' },
  { name: 'Product E', sales: 900, category: 'Clothing' }
];

console.log('=== Data Analysis Report ===\\n');

// Total sales
const totalSales = data.reduce((sum, item) => sum + item.sales, 0);
console.log(\`Total Sales: $\${totalSales.toLocaleString()}\`);

// Average sales
const avgSales = totalSales / data.length;
console.log(\`Average Sales: $\${avgSales.toFixed(2)}\`);

// Sales by category
const salesByCategory = data.reduce((acc, item) => {
  acc[item.category] = (acc[item.category] || 0) + item.sales;
  return acc;
}, {});

console.log('\\nSales by Category:');
Object.entries(salesByCategory).forEach(([category, sales]) => {
  console.log(\`  \${category}: $\${sales.toLocaleString()}\`);
});

// Top performing product
const topProduct = data.reduce((max, item) => 
  item.sales > max.sales ? item : max
);

console.log(\`\\nTop Performing Product: \${topProduct.name} ($\${topProduct.sales.toLocaleString()})\`);

console.log('\\n=== Analysis Complete ===');`
    },
    startCommand: 'dev'
  },
  'custom': {
    name: 'Custom Project',
    description: 'Start with an empty project',
    files: {
      'README.md': '# Custom Project\n\nStart building your custom project here!'
    }
  }
};

/**
 * Get template by name
 */
export function getTemplate(templateName: ProjectTemplate): TemplateConfig {
  return TEMPLATES[templateName];
}

/**
 * Get all available templates
 */
export function getAllTemplates(): TemplateConfig[] {
  return Object.values(TEMPLATES);
}

/**
 * Get template names
 */
export function getTemplateNames(): ProjectTemplate[] {
  return Object.keys(TEMPLATES) as ProjectTemplate[];
}
