/**
 * Nodebox Helper Utilities
 * 
 * Utility functions for working with Nodebox instances
 */

import { NodeboxConfig, NodeboxInstance, ProjectTemplate, TemplateConfig } from '../api/nodebox-types';
import { getTemplate } from './project-templates';

/**
 * Generate a unique instance ID
 */
export function generateInstanceId(prefix: string = 'nodebox'): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${random}`;
}

/**
 * Validate instance configuration
 */
export function validateInstanceConfig(config: Partial<NodeboxConfig>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!config.name || config.name.trim().length === 0) {
    errors.push('Instance name is required');
  }

  if (config.name && config.name.length > 100) {
    errors.push('Instance name must be less than 100 characters');
  }

  if (config.name && !/^[a-zA-Z0-9\s\-_]+$/.test(config.name)) {
    errors.push('Instance name can only contain letters, numbers, spaces, hyphens, and underscores');
  }

  if (config.settings?.memoryLimit && config.settings.memoryLimit < 128) {
    errors.push('Memory limit must be at least 128 MB');
  }

  if (config.settings?.memoryLimit && config.settings.memoryLimit > 2048) {
    errors.push('Memory limit cannot exceed 2048 MB');
  }

  if (config.settings?.timeoutMs && config.settings.timeoutMs < 1000) {
    errors.push('Timeout must be at least 1000 ms');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Create default instance configuration
 */
export function createDefaultConfig(
  name: string,
  template?: ProjectTemplate,
  projectId?: string
): NodeboxConfig {
  return {
    instanceId: generateInstanceId(),
    name,
    description: `Nodebox instance: ${name}`,
    template: template || 'vanilla-js',
    projectId,
    environment: {},
    settings: {
      autoSave: true,
      autoPreview: true,
      memoryLimit: 512,
      timeoutMs: 30000,
      allowNetworking: false,
      enableHotReload: true
    }
  };
}

/**
 * Get template files for initialization
 */
export function getTemplateFiles(template: ProjectTemplate): Record<string, string> {
  const templateConfig = getTemplate(template);
  return templateConfig.files || {};
}

/**
 * Format instance status for display
 */
export function formatInstanceStatus(status: string): {
  label: string;
  color: string;
  description: string;
} {
  switch (status) {
    case 'initializing':
      return {
        label: 'Initializing',
        color: 'yellow',
        description: 'Setting up the instance'
      };
    case 'ready':
      return {
        label: 'Ready',
        color: 'green',
        description: 'Instance is ready to use'
      };
    case 'running':
      return {
        label: 'Running',
        color: 'blue',
        description: 'Instance is actively running'
      };
    case 'stopped':
      return {
        label: 'Stopped',
        color: 'gray',
        description: 'Instance has been stopped'
      };
    case 'error':
      return {
        label: 'Error',
        color: 'red',
        description: 'Instance encountered an error'
      };
    default:
      return {
        label: status,
        color: 'gray',
        description: 'Unknown status'
      };
  }
}

/**
 * Calculate instance uptime
 */
export function calculateUptime(createdAt: Date): {
  duration: number;
  formatted: string;
} {
  const now = new Date();
  const duration = now.getTime() - createdAt.getTime();
  
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  let formatted: string;
  if (days > 0) {
    formatted = `${days}d ${hours % 24}h ${minutes % 60}m`;
  } else if (hours > 0) {
    formatted = `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    formatted = `${minutes}m ${seconds % 60}s`;
  } else {
    formatted = `${seconds}s`;
  }

  return { duration, formatted };
}

/**
 * Format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

/**
 * Get file extension
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

/**
 * Get file type from extension
 */
export function getFileType(filename: string): {
  type: string;
  category: string;
  icon: string;
} {
  const extension = getFileExtension(filename);
  
  const typeMap: Record<string, { type: string; category: string; icon: string }> = {
    // JavaScript/TypeScript
    js: { type: 'JavaScript', category: 'code', icon: '📄' },
    jsx: { type: 'React JSX', category: 'code', icon: '⚛️' },
    ts: { type: 'TypeScript', category: 'code', icon: '📘' },
    tsx: { type: 'React TSX', category: 'code', icon: '⚛️' },
    
    // Web
    html: { type: 'HTML', category: 'web', icon: '🌐' },
    css: { type: 'CSS', category: 'web', icon: '🎨' },
    scss: { type: 'SCSS', category: 'web', icon: '🎨' },
    sass: { type: 'Sass', category: 'web', icon: '🎨' },
    
    // Data
    json: { type: 'JSON', category: 'data', icon: '📋' },
    xml: { type: 'XML', category: 'data', icon: '📋' },
    yaml: { type: 'YAML', category: 'data', icon: '📋' },
    yml: { type: 'YAML', category: 'data', icon: '📋' },
    
    // Documentation
    md: { type: 'Markdown', category: 'docs', icon: '📝' },
    txt: { type: 'Text', category: 'docs', icon: '📄' },
    
    // Images
    png: { type: 'PNG Image', category: 'image', icon: '🖼️' },
    jpg: { type: 'JPEG Image', category: 'image', icon: '🖼️' },
    jpeg: { type: 'JPEG Image', category: 'image', icon: '🖼️' },
    gif: { type: 'GIF Image', category: 'image', icon: '🖼️' },
    svg: { type: 'SVG Image', category: 'image', icon: '🖼️' },
    
    // Config
    gitignore: { type: 'Git Ignore', category: 'config', icon: '⚙️' },
    env: { type: 'Environment', category: 'config', icon: '⚙️' },
    config: { type: 'Configuration', category: 'config', icon: '⚙️' }
  };
  
  return typeMap[extension] || { type: 'Unknown', category: 'other', icon: '📄' };
}

/**
 * Sanitize filename
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9\-_\.]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '');
}

/**
 * Check if path is safe (no directory traversal)
 */
export function isSafePath(path: string): boolean {
  const normalizedPath = path.replace(/\\/g, '/');
  return !normalizedPath.includes('../') && !normalizedPath.startsWith('/');
}

/**
 * Join paths safely
 */
export function joinPaths(...paths: string[]): string {
  return paths
    .filter(Boolean)
    .join('/')
    .replace(/\/+/g, '/')
    .replace(/\/$/, '') || '/';
}

/**
 * Get parent directory path
 */
export function getParentPath(path: string): string {
  if (path === '/' || !path) return '/';
  
  const parts = path.split('/').filter(Boolean);
  if (parts.length <= 1) return '/';
  
  return '/' + parts.slice(0, -1).join('/');
}

/**
 * Get filename from path
 */
export function getFilename(path: string): string {
  return path.split('/').pop() || '';
}

/**
 * Check if instance is healthy
 */
export function isInstanceHealthy(instance: NodeboxInstance): boolean {
  const now = new Date();
  const lastActivity = new Date(instance.lastActivity);
  const timeSinceActivity = now.getTime() - lastActivity.getTime();
  
  // Consider instance unhealthy if no activity for more than 30 minutes
  const maxInactiveTime = 30 * 60 * 1000;
  
  return (
    instance.status !== 'error' &&
    timeSinceActivity < maxInactiveTime
  );
}

/**
 * Get recommended actions for instance status
 */
export function getRecommendedActions(instance: NodeboxInstance): string[] {
  const actions: string[] = [];
  
  switch (instance.status) {
    case 'error':
      actions.push('Check error logs');
      actions.push('Restart instance');
      break;
    case 'stopped':
      actions.push('Start instance');
      break;
    case 'initializing':
      actions.push('Wait for initialization to complete');
      break;
    case 'ready':
      actions.push('Start development server');
      actions.push('Open file explorer');
      break;
    case 'running':
      actions.push('View preview');
      actions.push('Check terminal output');
      break;
  }
  
  if (!isInstanceHealthy(instance)) {
    actions.push('Check instance health');
  }
  
  return actions;
}

/**
 * Export instance configuration
 */
export function exportInstanceConfig(instance: NodeboxInstance): string {
  const exportData = {
    name: instance.config.name,
    description: instance.config.description,
    template: instance.config.template,
    settings: instance.config.settings,
    environment: instance.config.environment,
    exportedAt: new Date().toISOString()
  };
  
  return JSON.stringify(exportData, null, 2);
}

/**
 * Import instance configuration
 */
export function importInstanceConfig(configJson: string): Partial<NodeboxConfig> {
  try {
    const data = JSON.parse(configJson);
    
    return {
      name: data.name,
      description: data.description,
      template: data.template,
      settings: data.settings,
      environment: data.environment
    };
  } catch (error) {
    throw new Error('Invalid configuration format');
  }
}
