/**
 * Nodebox Runtime Integration Library
 * 
 * This module provides a comprehensive TypeScript implementation for integrating
 * Nodebox (browser-based Node.js runtime) into the application.
 */

// Core exports
export * from './core/nodebox-manager';
export * from './core/filesystem-manager';
export * from './core/shell-manager';
export * from './core/preview-manager';

// API exports
export * from './api/nodebox-client';
export * from './api/nodebox-types';

// Component exports
export * from './components/nodebox-workspace';
export * from './components/nodebox-terminal';
export * from './components/nodebox-preview';
export * from './components/nodebox-file-explorer';

// Hook exports
export * from './hooks/use-nodebox';
export * from './hooks/use-nodebox-filesystem';
export * from './hooks/use-nodebox-shell';

// Utility exports
export * from './utils/project-templates';
export * from './utils/nodebox-helpers';

// Default export for convenience
export { NodeboxManager } from './core/nodebox-manager';
