/**
 * Nodebox Manager
 * 
 * Central manager for creating, managing, and destroying Nodebox instances
 */

import { EventEmitter } from 'events';
import { Nodebox } from '@codesandbox/nodebox';
import {
  NodeboxConfig,
  NodeboxInstance,
  NodeboxManagerOptions,
  NodeboxError
} from '../api/nodebox-types';

/**
 * NodeboxManager
 * 
 * Manages multiple Nodebox instances, their lifecycle, and provides
 * a centralized API for interacting with browser-based Node.js environments
 */
export class NodeboxManager extends EventEmitter {
  private instances: Map<string, NodeboxInstance> = new Map();
  private options: NodeboxManagerOptions;
  private cleanupInterval?: NodeJS.Timeout;

  constructor(options: NodeboxManagerOptions = {}) {
    super();
    this.options = {
      maxInstances: 10,
      defaultSettings: {
        autoSave: true,
        autoPreview: true,
        memoryLimit: 512, // MB
        timeoutMs: 30000,
        allowNetworking: false,
        enableHotReload: true
      },
      autoCleanup: true,
      cleanupInterval: 300000, // 5 minutes
      ...options
    };

    // Start cleanup interval if enabled
    if (this.options.autoCleanup) {
      this.startCleanupInterval();
    }
  }

  /**
   * Create a new Nodebox instance
   */
  async createInstance(config: NodeboxConfig): Promise<NodeboxInstance> {
    // Check instance limit
    if (this.instances.size >= (this.options.maxInstances || 10)) {
      throw new NodeboxError(
        'Maximum number of instances reached',
        undefined,
        'MAX_INSTANCES_EXCEEDED'
      );
    }

    // Check if instance already exists
    if (this.instances.has(config.instanceId)) {
      throw new NodeboxError(
        `Instance with ID ${config.instanceId} already exists`,
        config.instanceId,
        'INSTANCE_EXISTS'
      );
    }

    try {
      // Create iframe element for Nodebox
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      document.body.appendChild(iframe);

      // Initialize Nodebox runtime
      const runtime = new Nodebox({
        iframe: iframe
      });

      // Create instance object
      const instance: NodeboxInstance = {
        id: config.instanceId,
        config: {
          ...config,
          settings: {
            ...this.options.defaultSettings,
            ...config.settings
          }
        },
        runtime,
        status: 'initializing',
        createdAt: new Date(),
        lastActivity: new Date(),
        filesystemManager: null, // Will be initialized when needed
        shellManager: null, // Will be initialized when needed
        previewManager: null // Will be initialized when needed
      };

      // Store instance
      this.instances.set(config.instanceId, instance);

      // Connect to runtime
      await runtime.connect();

      // Initialize with template if provided
      if (config.template) {
        await this.initializeWithTemplate(instance, config.template);
      }

      // Update status
      instance.status = 'ready';
      instance.lastActivity = new Date();

      // Emit event
      this.emit('instance-created', instance);

      return instance;
    } catch (error) {
      // Clean up on error
      this.instances.delete(config.instanceId);
      
      const nodeboxError = new NodeboxError(
        `Failed to create instance: ${error instanceof Error ? error.message : String(error)}`,
        config.instanceId,
        'CREATION_FAILED',
        error
      );

      this.emit('instance-error', config.instanceId, nodeboxError);
      throw nodeboxError;
    }
  }

  /**
   * Get an existing Nodebox instance
   */
  getInstance(instanceId: string): NodeboxInstance | null {
    return this.instances.get(instanceId) || null;
  }

  /**
   * List all Nodebox instances
   */
  listInstances(): NodeboxInstance[] {
    return Array.from(this.instances.values());
  }

  /**
   * Destroy a Nodebox instance
   */
  async destroyInstance(instanceId: string): Promise<boolean> {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      return false;
    }

    try {
      // Stop any running processes
      if (instance.shellManager) {
        await instance.shellManager.killAllProcesses();
      }

      // Clean up runtime resources
      // Note: Nodebox doesn't have an explicit cleanup method,
      // but we can try to clean up any associated DOM elements
      try {
        // Attempt to clean up any iframe or other DOM elements
        // This is a placeholder for actual cleanup logic
        console.log(`Cleaning up Nodebox instance ${instanceId}`);
      } catch (cleanupError) {
        console.warn('Error during Nodebox cleanup:', cleanupError);
      }

      // Remove from instances map
      this.instances.delete(instanceId);

      // Emit event
      this.emit('instance-destroyed', instanceId);

      return true;
    } catch (error) {
      const nodeboxError = new NodeboxError(
        `Failed to destroy instance: ${error instanceof Error ? error.message : String(error)}`,
        instanceId,
        'DESTRUCTION_FAILED',
        error
      );

      this.emit('instance-error', instanceId, nodeboxError);
      throw nodeboxError;
    }
  }

  /**
   * Destroy all instances
   */
  async destroyAllInstances(): Promise<void> {
    const instanceIds = Array.from(this.instances.keys());
    
    await Promise.all(
      instanceIds.map(id => this.destroyInstance(id).catch(console.error))
    );
  }

  /**
   * Update instance activity timestamp
   */
  updateActivity(instanceId: string): void {
    const instance = this.instances.get(instanceId);
    if (instance) {
      instance.lastActivity = new Date();
    }
  }

  /**
   * Get instance statistics
   */
  getStats(): {
    totalInstances: number;
    activeInstances: number;
    instancesByStatus: Record<string, number>;
    oldestInstance?: Date;
    newestInstance?: Date;
  } {
    const instances = Array.from(this.instances.values());
    
    const stats = {
      totalInstances: instances.length,
      activeInstances: instances.filter(i => i.status === 'running').length,
      instancesByStatus: {} as Record<string, number>,
      oldestInstance: undefined as Date | undefined,
      newestInstance: undefined as Date | undefined
    };

    // Count by status
    instances.forEach(instance => {
      stats.instancesByStatus[instance.status] = 
        (stats.instancesByStatus[instance.status] || 0) + 1;
    });

    // Find oldest and newest
    if (instances.length > 0) {
      stats.oldestInstance = new Date(Math.min(...instances.map(i => i.createdAt.getTime())));
      stats.newestInstance = new Date(Math.max(...instances.map(i => i.createdAt.getTime())));
    }

    return stats;
  }

  /**
   * Initialize instance with a project template
   */
  private async initializeWithTemplate(
    instance: NodeboxInstance, 
    template: string
  ): Promise<void> {
    // This will be implemented when we create the template system
    console.log(`Initializing instance ${instance.id} with template: ${template}`);
  }

  /**
   * Start cleanup interval for inactive instances
   */
  private startCleanupInterval(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(() => {
      this.cleanupInactiveInstances();
    }, this.options.cleanupInterval);
  }

  /**
   * Clean up inactive instances
   */
  private async cleanupInactiveInstances(): Promise<void> {
    const now = new Date();
    const maxInactiveTime = 30 * 60 * 1000; // 30 minutes

    const inactiveInstances = Array.from(this.instances.values())
      .filter(instance => {
        const inactiveTime = now.getTime() - instance.lastActivity.getTime();
        return inactiveTime > maxInactiveTime && instance.status !== 'running';
      });

    for (const instance of inactiveInstances) {
      try {
        await this.destroyInstance(instance.id);
        console.log(`Cleaned up inactive instance: ${instance.id}`);
      } catch (error) {
        console.error(`Failed to cleanup instance ${instance.id}:`, error);
      }
    }
  }

  /**
   * Stop cleanup interval
   */
  stopCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }
  }

  /**
   * Cleanup on manager destruction
   */
  async destroy(): Promise<void> {
    this.stopCleanup();
    await this.destroyAllInstances();
    this.removeAllListeners();
  }
}
