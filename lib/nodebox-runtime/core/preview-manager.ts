/**
 * Preview Manager
 * 
 * Manages preview functionality for Nodebox instances
 */

import { Nodebox } from '@codesandbox/nodebox';
import { PreviewInfo, NodeboxPreviewError } from '../api/nodebox-types';

/**
 * PreviewManager
 * 
 * Provides a high-level API for managing application previews in Nodebox
 */
export class PreviewManager {
  private runtime: Nodebox;
  private previews: Map<string, PreviewInfo> = new Map();
  private activePreview: string | null = null;

  constructor(runtime: Nodebox) {
    this.runtime = runtime;
  }

  /**
   * Get preview by shell/process ID
   */
  async getPreviewByShellId(shellId: string): Promise<PreviewInfo> {
    try {
      const previewInfo = await this.runtime.preview.getByShellId(shellId);
      
      const preview: PreviewInfo = {
        id: `preview_${shellId}`,
        url: previewInfo.url,
        title: previewInfo.title || 'Application Preview',
        status: 'ready',
        port: previewInfo.port,
        processId: shellId
      };

      this.previews.set(preview.id, preview);
      this.activePreview = preview.id;

      return preview;
    } catch (error) {
      throw new NodeboxPreviewError(
        `Failed to get preview for shell ${shellId}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        shellId
      );
    }
  }

  /**
   * Get preview by port
   */
  async getPreviewByPort(port: number): Promise<PreviewInfo | null> {
    try {
      // Nodebox doesn't have a direct getByPort method, so we'll search through existing previews
      const existingPreview = Array.from(this.previews.values()).find(p => p.port === port);
      if (existingPreview) {
        return existingPreview;
      }

      // If not found, we can't create a preview without a shell ID
      return null;
    } catch (error) {
      throw new NodeboxPreviewError(
        `Failed to get preview for port ${port}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        `port_${port}`
      );
    }
  }

  /**
   * Get all previews
   */
  getAllPreviews(): PreviewInfo[] {
    return Array.from(this.previews.values());
  }

  /**
   * Get active preview
   */
  getActivePreview(): PreviewInfo | null {
    if (!this.activePreview) {
      return null;
    }
    return this.previews.get(this.activePreview) || null;
  }

  /**
   * Set active preview
   */
  setActivePreview(previewId: string): boolean {
    if (this.previews.has(previewId)) {
      this.activePreview = previewId;
      return true;
    }
    return false;
  }

  /**
   * Get preview by ID
   */
  getPreview(previewId: string): PreviewInfo | null {
    return this.previews.get(previewId) || null;
  }

  /**
   * Remove a preview
   */
  removePreview(previewId: string): boolean {
    const removed = this.previews.delete(previewId);
    
    if (this.activePreview === previewId) {
      // Set a new active preview if available
      const remainingPreviews = Array.from(this.previews.keys());
      this.activePreview = remainingPreviews.length > 0 ? remainingPreviews[0] : null;
    }

    return removed;
  }

  /**
   * Clear all previews
   */
  clearPreviews(): void {
    this.previews.clear();
    this.activePreview = null;
  }

  /**
   * Update preview status
   */
  updatePreviewStatus(previewId: string, status: PreviewInfo['status']): boolean {
    const preview = this.previews.get(previewId);
    if (preview) {
      preview.status = status;
      return true;
    }
    return false;
  }

  /**
   * Refresh a preview
   */
  async refreshPreview(previewId: string): Promise<PreviewInfo> {
    const preview = this.previews.get(previewId);
    if (!preview) {
      throw new NodeboxPreviewError(`Preview ${previewId} not found`, undefined, previewId);
    }

    if (!preview.processId) {
      throw new NodeboxPreviewError(`Preview ${previewId} has no associated process`, undefined, previewId);
    }

    // Get fresh preview info
    return this.getPreviewByShellId(preview.processId);
  }

  /**
   * Check if preview is ready
   */
  async isPreviewReady(previewId: string): Promise<boolean> {
    const preview = this.previews.get(previewId);
    if (!preview) {
      return false;
    }

    try {
      // Try to fetch the preview URL to check if it's ready
      const response = await fetch(preview.url, { method: 'HEAD' });
      const isReady = response.ok;
      
      // Update status
      this.updatePreviewStatus(previewId, isReady ? 'ready' : 'loading');
      
      return isReady;
    } catch {
      this.updatePreviewStatus(previewId, 'error');
      return false;
    }
  }

  /**
   * Wait for preview to be ready
   */
  async waitForPreview(previewId: string, timeoutMs: number = 30000): Promise<PreviewInfo> {
    const preview = this.previews.get(previewId);
    if (!preview) {
      throw new NodeboxPreviewError(`Preview ${previewId} not found`, undefined, previewId);
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new NodeboxPreviewError(`Preview ${previewId} timed out after ${timeoutMs}ms`, undefined, previewId));
      }, timeoutMs);

      const checkInterval = setInterval(async () => {
        try {
          const isReady = await this.isPreviewReady(previewId);
          if (isReady) {
            clearTimeout(timeout);
            clearInterval(checkInterval);
            resolve(preview);
          }
        } catch (error) {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          reject(error);
        }
      }, 1000);
    });
  }

  /**
   * Get preview statistics
   */
  getStats(): {
    totalPreviews: number;
    readyPreviews: number;
    loadingPreviews: number;
    errorPreviews: number;
    activePreview: string | null;
  } {
    const previews = Array.from(this.previews.values());
    
    return {
      totalPreviews: previews.length,
      readyPreviews: previews.filter(p => p.status === 'ready').length,
      loadingPreviews: previews.filter(p => p.status === 'loading').length,
      errorPreviews: previews.filter(p => p.status === 'error').length,
      activePreview: this.activePreview
    };
  }

  /**
   * Create a custom preview URL
   */
  createCustomPreviewUrl(baseUrl: string, path: string = ''): string {
    const url = new URL(baseUrl);
    if (path) {
      url.pathname = path;
    }
    return url.toString();
  }

  /**
   * Open preview in new window/tab
   */
  openPreviewInNewWindow(previewId: string): boolean {
    const preview = this.previews.get(previewId);
    if (!preview) {
      return false;
    }

    try {
      window.open(preview.url, '_blank');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get preview iframe HTML
   */
  getPreviewIframeHtml(previewId: string, options: {
    width?: string;
    height?: string;
    sandbox?: string;
    allow?: string;
  } = {}): string {
    const preview = this.previews.get(previewId);
    if (!preview) {
      return '';
    }

    const {
      width = '100%',
      height = '100%',
      sandbox = 'allow-scripts allow-same-origin allow-forms allow-popups',
      allow = 'accelerometer; camera; encrypted-media; geolocation; gyroscope; microphone'
    } = options;

    return `<iframe 
      src="${preview.url}" 
      width="${width}" 
      height="${height}"
      sandbox="${sandbox}"
      allow="${allow}"
      title="${preview.title || 'Preview'}"
      style="border: none;"
    ></iframe>`;
  }

  /**
   * Monitor preview health
   */
  startHealthMonitoring(intervalMs: number = 5000): () => void {
    const interval = setInterval(async () => {
      const previews = Array.from(this.previews.keys());
      
      for (const previewId of previews) {
        try {
          await this.isPreviewReady(previewId);
        } catch (error) {
          console.warn(`Health check failed for preview ${previewId}:`, error);
        }
      }
    }, intervalMs);

    // Return cleanup function
    return () => clearInterval(interval);
  }
}
