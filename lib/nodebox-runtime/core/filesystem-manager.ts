/**
 * Filesystem Manager
 * 
 * Manages file system operations for Nodebox instances
 */

import { Nodebox } from '@codesandbox/nodebox';
import { FileEntry, NodeboxFileSystemError } from '../api/nodebox-types';

/**
 * FilesystemManager
 * 
 * Provides a high-level API for file system operations in Nodebox
 */
export class FilesystemManager {
  private runtime: Nodebox;
  private fileCache: Map<string, string> = new Map();

  constructor(runtime: Nodebox) {
    this.runtime = runtime;
  }

  /**
   * Initialize the file system with project files
   */
  async initializeProject(files: Record<string, string>): Promise<void> {
    try {
      await this.runtime.fs.init(files);
      
      // Update cache
      Object.entries(files).forEach(([path, content]) => {
        this.fileCache.set(path, content);
      });
    } catch (error) {
      throw new NodeboxFileSystemError(
        `Failed to initialize project: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        'project-init'
      );
    }
  }

  /**
   * Read a file from the file system
   */
  async readFile(path: string): Promise<string> {
    try {
      // Check cache first
      if (this.fileCache.has(path)) {
        return this.fileCache.get(path)!;
      }

      // Read from Nodebox filesystem
      const content = await this.runtime.fs.readFile(path);
      
      // Update cache
      this.fileCache.set(path, content);
      
      return content;
    } catch (error) {
      throw new NodeboxFileSystemError(
        `Failed to read file ${path}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        path
      );
    }
  }

  /**
   * Write a file to the file system
   */
  async writeFile(path: string, content: string): Promise<void> {
    try {
      await this.runtime.fs.writeFile(path, content);
      
      // Update cache
      this.fileCache.set(path, content);
    } catch (error) {
      throw new NodeboxFileSystemError(
        `Failed to write file ${path}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        path
      );
    }
  }

  /**
   * Delete a file from the file system
   */
  async deleteFile(path: string): Promise<void> {
    try {
      await this.runtime.fs.rm(path);
      
      // Remove from cache
      this.fileCache.delete(path);
    } catch (error) {
      throw new NodeboxFileSystemError(
        `Failed to delete file ${path}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        path
      );
    }
  }

  /**
   * Create a directory
   */
  async createDirectory(path: string): Promise<void> {
    try {
      await this.runtime.fs.mkdir(path);
    } catch (error) {
      throw new NodeboxFileSystemError(
        `Failed to create directory ${path}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        path
      );
    }
  }

  /**
   * List files and directories
   */
  async listFiles(directory: string = '/'): Promise<FileEntry[]> {
    try {
      const entries = await this.runtime.fs.readdir(directory);
      
      const fileEntries: FileEntry[] = [];
      
      for (const entry of entries) {
        const fullPath = directory === '/' ? `/${entry}` : `${directory}/${entry}`;
        
        try {
          // Try to get file stats
          const stats = await this.runtime.fs.stat(fullPath);
          
          const fileEntry: FileEntry = {
            name: entry,
            path: fullPath,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.isFile() ? stats.size : undefined,
            lastModified: new Date(stats.mtime)
          };

          // If it's a file, optionally load content
          if (fileEntry.type === 'file' && this.fileCache.has(fullPath)) {
            fileEntry.content = this.fileCache.get(fullPath);
          }

          fileEntries.push(fileEntry);
        } catch (statError) {
          // If we can't get stats, create a basic entry
          fileEntries.push({
            name: entry,
            path: fullPath,
            type: 'file' // Default to file if we can't determine
          });
        }
      }
      
      return fileEntries.sort((a, b) => {
        // Directories first, then files
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
      });
    } catch (error) {
      throw new NodeboxFileSystemError(
        `Failed to list files in ${directory}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        directory
      );
    }
  }

  /**
   * Check if a file or directory exists
   */
  async exists(path: string): Promise<boolean> {
    try {
      await this.runtime.fs.stat(path);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file statistics
   */
  async getStats(path: string): Promise<{
    size: number;
    isFile: boolean;
    isDirectory: boolean;
    lastModified: Date;
  }> {
    try {
      const stats = await this.runtime.fs.stat(path);
      
      return {
        size: stats.size,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
        lastModified: new Date(stats.mtime)
      };
    } catch (error) {
      throw new NodeboxFileSystemError(
        `Failed to get stats for ${path}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        path
      );
    }
  }

  /**
   * Copy a file
   */
  async copyFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      const content = await this.readFile(sourcePath);
      await this.writeFile(destinationPath, content);
    } catch (error) {
      throw new NodeboxFileSystemError(
        `Failed to copy file from ${sourcePath} to ${destinationPath}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        sourcePath
      );
    }
  }

  /**
   * Move/rename a file
   */
  async moveFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      await this.copyFile(sourcePath, destinationPath);
      await this.deleteFile(sourcePath);
    } catch (error) {
      throw new NodeboxFileSystemError(
        `Failed to move file from ${sourcePath} to ${destinationPath}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        sourcePath
      );
    }
  }

  /**
   * Get the current working directory
   */
  getCurrentDirectory(): string {
    return '/'; // Nodebox typically starts at root
  }

  /**
   * Clear the file cache
   */
  clearCache(): void {
    this.fileCache.clear();
  }

  /**
   * Get cached file content
   */
  getCachedContent(path: string): string | undefined {
    return this.fileCache.get(path);
  }

  /**
   * Get all cached files
   */
  getCachedFiles(): Record<string, string> {
    return Object.fromEntries(this.fileCache.entries());
  }

  /**
   * Watch for file changes (if supported)
   */
  watchFile(path: string, callback: (content: string) => void): () => void {
    // Nodebox doesn't have built-in file watching, so we'll implement polling
    const interval = setInterval(async () => {
      try {
        const content = await this.readFile(path);
        const cachedContent = this.fileCache.get(path);
        
        if (content !== cachedContent) {
          this.fileCache.set(path, content);
          callback(content);
        }
      } catch (error) {
        console.warn(`Error watching file ${path}:`, error);
      }
    }, 1000); // Poll every second

    // Return cleanup function
    return () => clearInterval(interval);
  }
}
