/**
 * Shell Manager
 * 
 * Manages shell operations and command execution for Nodebox instances
 */

import { Nodebox } from '@codesandbox/nodebox';
import { ShellProcess, NodeboxShellError } from '../api/nodebox-types';

/**
 * ShellManager
 * 
 * Provides a high-level API for executing commands and managing processes in Nodebox
 */
export class ShellManager {
  private runtime: Nodebox;
  private processes: Map<string, ShellProcess> = new Map();
  private shell: any = null; // Will be typed when we have the actual shell instance

  constructor(runtime: Nodebox) {
    this.runtime = runtime;
  }

  /**
   * Initialize the shell
   */
  async initializeShell(): Promise<void> {
    try {
      if (!this.shell) {
        this.shell = this.runtime.shell.create();
      }
    } catch (error) {
      throw new NodeboxShellError(
        `Failed to initialize shell: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Execute a command
   */
  async executeCommand(command: string, args: string[] = []): Promise<ShellProcess> {
    await this.initializeShell();

    const processId = `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const process: ShellProcess = {
      id: processId,
      command,
      args,
      status: 'running',
      startTime: new Date(),
      output: [],
      errors: []
    };

    this.processes.set(processId, process);

    try {
      // Execute the command using Nodebox shell
      const shellProcess = await this.shell.runCommand(command, args);
      
      // Update process with shell process info
      process.pid = shellProcess.id;

      // Listen for output
      if (shellProcess.stdout) {
        shellProcess.stdout.on('data', (data: string) => {
          process.output.push(data);
        });
      }

      if (shellProcess.stderr) {
        shellProcess.stderr.on('data', (data: string) => {
          process.errors.push(data);
        });
      }

      // Listen for process completion
      shellProcess.on('exit', (code: number) => {
        process.status = code === 0 ? 'completed' : 'failed';
        process.exitCode = code;
        process.endTime = new Date();
      });

      return process;
    } catch (error) {
      process.status = 'failed';
      process.endTime = new Date();
      process.errors.push(error instanceof Error ? error.message : String(error));

      throw new NodeboxShellError(
        `Failed to execute command ${command}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        command
      );
    }
  }

  /**
   * Execute npm command
   */
  async executeNpmCommand(subcommand: string, args: string[] = []): Promise<ShellProcess> {
    return this.executeCommand('npm', [subcommand, ...args]);
  }

  /**
   * Install npm packages
   */
  async installPackages(packages: string[]): Promise<ShellProcess> {
    return this.executeNpmCommand('install', packages);
  }

  /**
   * Run npm script
   */
  async runNpmScript(scriptName: string): Promise<ShellProcess> {
    return this.executeNpmCommand('run', [scriptName]);
  }

  /**
   * Start development server
   */
  async startDevServer(): Promise<ShellProcess> {
    return this.executeNpmCommand('run', ['dev']);
  }

  /**
   * Build project
   */
  async buildProject(): Promise<ShellProcess> {
    return this.executeNpmCommand('run', ['build']);
  }

  /**
   * Get a process by ID
   */
  getProcess(processId: string): ShellProcess | null {
    return this.processes.get(processId) || null;
  }

  /**
   * Get all running processes
   */
  getRunningProcesses(): ShellProcess[] {
    return Array.from(this.processes.values()).filter(p => p.status === 'running');
  }

  /**
   * Get all processes
   */
  getAllProcesses(): ShellProcess[] {
    return Array.from(this.processes.values());
  }

  /**
   * Kill a process
   */
  async killProcess(processId: string): Promise<boolean> {
    const process = this.processes.get(processId);
    if (!process || process.status !== 'running') {
      return false;
    }

    try {
      // Kill the process using Nodebox shell
      if (process.pid && this.shell) {
        await this.shell.kill(process.pid);
      }

      // Update process status
      process.status = 'killed';
      process.endTime = new Date();

      return true;
    } catch (error) {
      throw new NodeboxShellError(
        `Failed to kill process ${processId}: ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        process.command
      );
    }
  }

  /**
   * Kill all running processes
   */
  async killAllProcesses(): Promise<void> {
    const runningProcesses = this.getRunningProcesses();
    
    await Promise.all(
      runningProcesses.map(process => 
        this.killProcess(process.id).catch(console.error)
      )
    );
  }

  /**
   * Get process output
   */
  getProcessOutput(processId: string): string {
    const process = this.processes.get(processId);
    if (!process) {
      return '';
    }

    return process.output.join('');
  }

  /**
   * Get process errors
   */
  getProcessErrors(processId: string): string {
    const process = this.processes.get(processId);
    if (!process) {
      return '';
    }

    return process.errors.join('');
  }

  /**
   * Get combined output (stdout + stderr)
   */
  getCombinedOutput(processId: string): string {
    const process = this.processes.get(processId);
    if (!process) {
      return '';
    }

    const output = process.output.join('');
    const errors = process.errors.join('');
    
    return output + (errors ? '\n' + errors : '');
  }

  /**
   * Clear process history
   */
  clearProcessHistory(): void {
    // Only keep running processes
    const runningProcesses = new Map();
    
    this.processes.forEach((process, id) => {
      if (process.status === 'running') {
        runningProcesses.set(id, process);
      }
    });

    this.processes = runningProcesses;
  }

  /**
   * Get shell statistics
   */
  getStats(): {
    totalProcesses: number;
    runningProcesses: number;
    completedProcesses: number;
    failedProcesses: number;
    killedProcesses: number;
  } {
    const processes = Array.from(this.processes.values());
    
    return {
      totalProcesses: processes.length,
      runningProcesses: processes.filter(p => p.status === 'running').length,
      completedProcesses: processes.filter(p => p.status === 'completed').length,
      failedProcesses: processes.filter(p => p.status === 'failed').length,
      killedProcesses: processes.filter(p => p.status === 'killed').length
    };
  }

  /**
   * Execute command with real-time output streaming
   */
  async executeCommandWithStreaming(
    command: string,
    args: string[] = [],
    onOutput?: (data: string) => void,
    onError?: (data: string) => void
  ): Promise<ShellProcess> {
    const process = await this.executeCommand(command, args);

    // Set up real-time streaming if callbacks provided
    if (onOutput) {
      const outputInterval = setInterval(() => {
        const currentProcess = this.processes.get(process.id);
        if (currentProcess && currentProcess.output.length > 0) {
          const newOutput = currentProcess.output.splice(0);
          newOutput.forEach(onOutput);
        }
        
        if (currentProcess?.status !== 'running') {
          clearInterval(outputInterval);
        }
      }, 100);
    }

    if (onError) {
      const errorInterval = setInterval(() => {
        const currentProcess = this.processes.get(process.id);
        if (currentProcess && currentProcess.errors.length > 0) {
          const newErrors = currentProcess.errors.splice(0);
          newErrors.forEach(onError);
        }
        
        if (currentProcess?.status !== 'running') {
          clearInterval(errorInterval);
        }
      }, 100);
    }

    return process;
  }

  /**
   * Wait for process completion
   */
  async waitForProcess(processId: string, timeoutMs: number = 30000): Promise<ShellProcess> {
    return new Promise((resolve, reject) => {
      const process = this.processes.get(processId);
      if (!process) {
        reject(new NodeboxShellError(`Process ${processId} not found`));
        return;
      }

      if (process.status !== 'running') {
        resolve(process);
        return;
      }

      const timeout = setTimeout(() => {
        reject(new NodeboxShellError(`Process ${processId} timed out after ${timeoutMs}ms`));
      }, timeoutMs);

      const checkInterval = setInterval(() => {
        const currentProcess = this.processes.get(processId);
        if (currentProcess && currentProcess.status !== 'running') {
          clearTimeout(timeout);
          clearInterval(checkInterval);
          resolve(currentProcess);
        }
      }, 100);
    });
  }
}
