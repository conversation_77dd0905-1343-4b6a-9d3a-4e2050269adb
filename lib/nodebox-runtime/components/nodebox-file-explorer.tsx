/**
 * Nodebox File Explorer Component
 * 
 * File system browser for Nodebox instances
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Folder, 
  File, 
  Plus, 
  Trash2, 
  Edit, 
  Download,
  Upload,
  RefreshCw,
  Search,
  FolderPlus,
  FilePlus
} from 'lucide-react';
import { FileEntry } from '../api/nodebox-types';
import { NodeboxClient } from '../api/nodebox-client';

interface NodeboxFileExplorerProps {
  instanceId: string;
  className?: string;
}

export function NodeboxFileExplorer({ instanceId, className }: NodeboxFileExplorerProps) {
  const [files, setFiles] = useState<FileEntry[]>([]);
  const [currentPath, setCurrentPath] = useState('/');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFile, setSelectedFile] = useState<FileEntry | null>(null);
  const [isCreatingFile, setIsCreatingFile] = useState(false);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newItemName, setNewItemName] = useState('');

  const client = new NodeboxClient();

  // Load files for current directory
  const loadFiles = async (path: string = currentPath) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const fileList = await client.listFiles(instanceId, path);
      setFiles(fileList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load files');
      console.error('Error loading files:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    if (instanceId) {
      loadFiles();
    }
  }, [instanceId]);

  // Filter files based on search query
  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle directory navigation
  const navigateToDirectory = (path: string) => {
    setCurrentPath(path);
    loadFiles(path);
  };

  // Handle file selection
  const handleFileSelect = (file: FileEntry) => {
    setSelectedFile(file);
    if (file.type === 'directory') {
      navigateToDirectory(file.path);
    }
  };

  // Create new file
  const handleCreateFile = async () => {
    if (!newItemName.trim()) return;
    
    try {
      const filePath = currentPath === '/' ? `/${newItemName}` : `${currentPath}/${newItemName}`;
      await client.writeFile(instanceId, filePath, '// New file\n');
      setNewItemName('');
      setIsCreatingFile(false);
      await loadFiles();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create file');
    }
  };

  // Create new folder
  const handleCreateFolder = async () => {
    if (!newItemName.trim()) return;
    
    try {
      // Note: This is a placeholder - actual folder creation would need to be implemented
      // in the Nodebox filesystem manager
      console.log('Creating folder:', newItemName);
      setNewItemName('');
      setIsCreatingFolder(false);
      await loadFiles();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create folder');
    }
  };

  // Navigate up one level
  const navigateUp = () => {
    if (currentPath !== '/') {
      const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
      navigateToDirectory(parentPath);
    }
  };

  // Get file icon
  const getFileIcon = (file: FileEntry) => {
    if (file.type === 'directory') {
      return <Folder className="h-4 w-4 text-blue-500" />;
    }
    
    // Different icons based on file extension
    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <File className="h-4 w-4 text-yellow-500" />;
      case 'json':
        return <File className="h-4 w-4 text-green-500" />;
      case 'css':
      case 'scss':
        return <File className="h-4 w-4 text-blue-500" />;
      case 'html':
        return <File className="h-4 w-4 text-orange-500" />;
      case 'md':
        return <File className="h-4 w-4 text-gray-500" />;
      default:
        return <File className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">File Explorer</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => loadFiles()}
              variant="outline"
              size="sm"
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              onClick={() => setIsCreatingFile(true)}
              variant="outline"
              size="sm"
            >
              <FilePlus className="h-4 w-4" />
            </Button>
            <Button
              onClick={() => setIsCreatingFolder(true)}
              variant="outline"
              size="sm"
            >
              <FolderPlus className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Search and Navigation */}
        <div className="space-y-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search files..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Path:</span>
            <code className="bg-muted px-2 py-1 rounded text-xs">{currentPath}</code>
            {currentPath !== '/' && (
              <Button onClick={navigateUp} variant="ghost" size="sm">
                ↑ Up
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {error && (
          <div className="text-red-600 text-sm mb-4 p-2 bg-red-50 rounded">
            {error}
          </div>
        )}

        {/* Create new file/folder forms */}
        {(isCreatingFile || isCreatingFolder) && (
          <div className="mb-4 p-3 border rounded-lg bg-muted/50">
            <div className="flex items-center gap-2">
              <Input
                placeholder={isCreatingFile ? 'File name...' : 'Folder name...'}
                value={newItemName}
                onChange={(e) => setNewItemName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    isCreatingFile ? handleCreateFile() : handleCreateFolder();
                  } else if (e.key === 'Escape') {
                    setIsCreatingFile(false);
                    setIsCreatingFolder(false);
                    setNewItemName('');
                  }
                }}
                autoFocus
              />
              <Button
                onClick={isCreatingFile ? handleCreateFile : handleCreateFolder}
                size="sm"
              >
                Create
              </Button>
              <Button
                onClick={() => {
                  setIsCreatingFile(false);
                  setIsCreatingFolder(false);
                  setNewItemName('');
                }}
                variant="outline"
                size="sm"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* File list */}
        <div className="space-y-1">
          {isLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              Loading files...
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchQuery ? 'No files match your search' : 'No files found'}
            </div>
          ) : (
            filteredFiles.map((file) => (
              <div
                key={file.path}
                className={`flex items-center justify-between p-2 rounded-lg cursor-pointer transition-colors ${
                  selectedFile?.path === file.path
                    ? 'bg-primary/10 border border-primary/20'
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => handleFileSelect(file)}
              >
                <div className="flex items-center gap-3">
                  {getFileIcon(file)}
                  <div>
                    <p className="font-medium text-sm">{file.name}</p>
                    {file.size && (
                      <p className="text-xs text-muted-foreground">
                        {(file.size / 1024).toFixed(1)} KB
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-1">
                  {file.type === 'file' && (
                    <>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="h-3 w-3" />
                      </Button>
                    </>
                  )}
                  <Button variant="ghost" size="sm">
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
