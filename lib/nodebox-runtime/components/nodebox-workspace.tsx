/**
 * Nodebox Workspace Component
 * 
 * Main workspace component for Nodebox instances
 */

'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Play, 
  Square, 
  RefreshCw, 
  Trash2, 
  Monitor, 
  Terminal, 
  Files,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2
} from 'lucide-react';
import { useNodebox } from '../hooks/use-nodebox';
import { NodeboxInstance, ProjectTemplate } from '../api/nodebox-types';
import { NodeboxFileExplorer } from './nodebox-file-explorer';
import { NodeboxTerminal } from './nodebox-terminal';
import { NodeboxPreview } from './nodebox-preview';

interface NodeboxWorkspaceProps {
  instanceId?: string;
  projectId?: string;
  className?: string;
  onInstanceChange?: (instance: NodeboxInstance | null) => void;
}

export function NodeboxWorkspace({
  instanceId,
  projectId,
  className,
  onInstanceChange
}: NodeboxWorkspaceProps) {
  const {
    instances,
    activeInstance,
    isLoading,
    error,
    createInstance,
    destroyInstance,
    setActiveInstance,
    refreshInstances,
    createFromTemplate
  } = useNodebox({ autoConnect: true, clientMode: true });

  const [activeTab, setActiveTab] = useState('files');
  const [isCreating, setIsCreating] = useState(false);

  // Set active instance based on prop
  useEffect(() => {
    if (instanceId && instances.length > 0) {
      const instance = instances.find(i => i.id === instanceId);
      if (instance) {
        setActiveInstance(instanceId);
      }
    }
  }, [instanceId, instances, setActiveInstance]);

  // Notify parent of instance changes
  useEffect(() => {
    onInstanceChange?.(activeInstance);
  }, [activeInstance, onInstanceChange]);

  // Get status icon and color
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'ready':
        return { icon: CheckCircle, color: 'text-green-500', label: 'Ready' };
      case 'running':
        return { icon: Play, color: 'text-blue-500', label: 'Running' };
      case 'initializing':
        return { icon: Loader2, color: 'text-yellow-500', label: 'Initializing' };
      case 'error':
        return { icon: AlertCircle, color: 'text-red-500', label: 'Error' };
      case 'stopped':
        return { icon: Square, color: 'text-gray-500', label: 'Stopped' };
      default:
        return { icon: Clock, color: 'text-gray-400', label: status };
    }
  };

  // Create new instance from template
  const handleCreateFromTemplate = async (template: ProjectTemplate) => {
    setIsCreating(true);
    try {
      const name = `${template}-${Date.now()}`;
      await createFromTemplate(template, name, projectId);
    } catch (error) {
      console.error('Error creating instance from template:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // Handle instance destruction
  const handleDestroyInstance = async (id: string) => {
    if (confirm('Are you sure you want to destroy this instance? This action cannot be undone.')) {
      await destroyInstance(id);
    }
  };

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            Nodebox Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{error.message}</p>
          <Button onClick={refreshInstances} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* Instance Selection Header */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Nodebox Workspace</CardTitle>
              <CardDescription>
                Browser-based Node.js development environment
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                onClick={refreshInstances}
                variant="outline"
                size="sm"
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {instances.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">No Nodebox instances found</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {(['react', 'nextjs', 'express', 'vanilla-js'] as ProjectTemplate[]).map(template => (
                  <Button
                    key={template}
                    onClick={() => handleCreateFromTemplate(template)}
                    disabled={isCreating}
                    variant="outline"
                    size="sm"
                  >
                    {isCreating ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4 mr-2" />
                    )}
                    Create {template}
                  </Button>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {instances.map(instance => {
                const statusDisplay = getStatusDisplay(instance.status);
                const StatusIcon = statusDisplay.icon;
                
                return (
                  <div
                    key={instance.id}
                    className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                      activeInstance?.id === instance.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:bg-muted/50'
                    }`}
                    onClick={() => setActiveInstance(instance.id)}
                  >
                    <div className="flex items-center gap-3">
                      <StatusIcon className={`h-4 w-4 ${statusDisplay.color}`} />
                      <div>
                        <p className="font-medium">{instance.config.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {instance.config.description}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{statusDisplay.label}</Badge>
                      {instance.config.template && (
                        <Badge variant="outline">{instance.config.template}</Badge>
                      )}
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDestroyInstance(instance.id);
                        }}
                        variant="ghost"
                        size="sm"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Main Workspace */}
      {activeInstance && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">{activeInstance.config.name}</CardTitle>
              <div className="flex items-center gap-2">
                {(() => {
                  const statusDisplay = getStatusDisplay(activeInstance.status);
                  const StatusIcon = statusDisplay.icon;
                  return (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <StatusIcon className={`h-3 w-3 ${statusDisplay.color}`} />
                      {statusDisplay.label}
                    </Badge>
                  );
                })()}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="files" className="flex items-center gap-2">
                  <Files className="h-4 w-4" />
                  Files
                </TabsTrigger>
                <TabsTrigger value="terminal" className="flex items-center gap-2">
                  <Terminal className="h-4 w-4" />
                  Terminal
                </TabsTrigger>
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <Monitor className="h-4 w-4" />
                  Preview
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Settings
                </TabsTrigger>
              </TabsList>

              <TabsContent value="files" className="mt-4">
                <NodeboxFileExplorer instanceId={activeInstance.id} />
              </TabsContent>

              <TabsContent value="terminal" className="mt-4">
                <NodeboxTerminal instanceId={activeInstance.id} />
              </TabsContent>

              <TabsContent value="preview" className="mt-4">
                <NodeboxPreview instanceId={activeInstance.id} />
              </TabsContent>

              <TabsContent value="settings" className="mt-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Instance Settings</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Instance ID</label>
                      <p className="text-sm text-muted-foreground font-mono">
                        {activeInstance.id}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Template</label>
                      <p className="text-sm text-muted-foreground">
                        {activeInstance.config.template || 'None'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Created</label>
                      <p className="text-sm text-muted-foreground">
                        {new Date(activeInstance.createdAt).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Last Activity</label>
                      <p className="text-sm text-muted-foreground">
                        {new Date(activeInstance.lastActivity).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
