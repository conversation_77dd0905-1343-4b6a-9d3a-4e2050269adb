/**
 * Nodebox Terminal Component
 * 
 * Terminal interface for Nodebox instances
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Terminal as TerminalIcon, 
  Play, 
  Square, 
  Trash2, 
  RefreshCw,
  Copy,
  Download
} from 'lucide-react';
import { ShellProcess } from '../api/nodebox-types';
import { NodeboxClient } from '../api/nodebox-client';

interface NodeboxTerminalProps {
  instanceId: string;
  className?: string;
}

export function NodeboxTerminal({ instanceId, className }: NodeboxTerminalProps) {
  const [processes, setProcesses] = useState<ShellProcess[]>([]);
  const [activeProcess, setActiveProcess] = useState<ShellProcess | null>(null);
  const [command, setCommand] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [output, setOutput] = useState<string[]>([]);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  const outputRef = useRef<HTMLDivElement>(null);
  const client = new NodeboxClient();

  // Auto-scroll to bottom when output changes
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [output]);

  // Load processes on mount
  useEffect(() => {
    if (instanceId) {
      loadProcesses();
    }
  }, [instanceId]);

  // Load running processes
  const loadProcesses = async () => {
    try {
      // This would need to be implemented in the API
      // For now, we'll use a placeholder
      console.log('Loading processes for instance:', instanceId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load processes');
    }
  };

  // Execute command
  const executeCommand = async () => {
    if (!command.trim()) return;

    setIsExecuting(true);
    setError(null);

    try {
      // Add command to history
      setCommandHistory(prev => [...prev, command]);
      setHistoryIndex(-1);

      // Add command to output
      setOutput(prev => [...prev, `$ ${command}`]);

      // Parse command and arguments
      const parts = command.trim().split(' ');
      const cmd = parts[0];
      const args = parts.slice(1);

      // Execute command
      const process = await client.executeCommand(instanceId, cmd, args);
      setProcesses(prev => [...prev, process]);
      setActiveProcess(process);

      // Add initial output
      if (process.output.length > 0) {
        setOutput(prev => [...prev, ...process.output]);
      }

      // Clear command input
      setCommand('');

      // Poll for process updates (in a real implementation, you'd use WebSockets)
      pollProcessOutput(process.id);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to execute command');
      setOutput(prev => [...prev, `Error: ${err instanceof Error ? err.message : String(err)}`]);
    } finally {
      setIsExecuting(false);
    }
  };

  // Poll for process output updates
  const pollProcessOutput = async (processId: string) => {
    try {
      const process = await client.getProcess(instanceId, processId);
      
      // Update output if there's new content
      if (process.output.length > 0) {
        setOutput(prev => [...prev, ...process.output]);
      }
      
      if (process.errors.length > 0) {
        setOutput(prev => [...prev, ...process.errors.map(err => `Error: ${err}`)]);
      }

      // Continue polling if process is still running
      if (process.status === 'running') {
        setTimeout(() => pollProcessOutput(processId), 1000);
      } else {
        setOutput(prev => [...prev, `Process ${processId} ${process.status} with exit code ${process.exitCode || 0}`]);
      }
    } catch (err) {
      console.error('Error polling process output:', err);
    }
  };

  // Kill process
  const killProcess = async (processId: string) => {
    try {
      await client.killProcess(instanceId, processId);
      setProcesses(prev => prev.filter(p => p.id !== processId));
      if (activeProcess?.id === processId) {
        setActiveProcess(null);
      }
      setOutput(prev => [...prev, `Process ${processId} killed`]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to kill process');
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      executeCommand();
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1);
        setHistoryIndex(newIndex);
        setCommand(commandHistory[newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1;
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1);
          setCommand('');
        } else {
          setHistoryIndex(newIndex);
          setCommand(commandHistory[newIndex]);
        }
      }
    }
  };

  // Clear output
  const clearOutput = () => {
    setOutput([]);
  };

  // Copy output to clipboard
  const copyOutput = () => {
    navigator.clipboard.writeText(output.join('\n'));
  };

  // Get process status badge
  const getProcessStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
      running: 'default',
      completed: 'secondary',
      failed: 'destructive',
      killed: 'outline'
    };
    
    return <Badge variant={variants[status] || 'outline'}>{status}</Badge>;
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <TerminalIcon className="h-5 w-5" />
            Terminal
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button onClick={clearOutput} variant="outline" size="sm">
              <Trash2 className="h-4 w-4" />
            </Button>
            <Button onClick={copyOutput} variant="outline" size="sm">
              <Copy className="h-4 w-4" />
            </Button>
            <Button onClick={loadProcesses} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {error && (
          <div className="text-red-600 text-sm p-2 bg-red-50 rounded">
            {error}
          </div>
        )}

        {/* Running Processes */}
        {processes.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">Running Processes</h4>
            <div className="space-y-2">
              {processes.filter(p => p.status === 'running').map(process => (
                <div key={process.id} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex items-center gap-2">
                    <code className="text-sm">{process.command} {process.args.join(' ')}</code>
                    {getProcessStatusBadge(process.status)}
                  </div>
                  <Button
                    onClick={() => killProcess(process.id)}
                    variant="outline"
                    size="sm"
                  >
                    <Square className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Terminal Output */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Output</h4>
          <div
            ref={outputRef}
            className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto"
          >
            {output.length === 0 ? (
              <div className="text-gray-500">
                Welcome to Nodebox Terminal. Type a command to get started.
              </div>
            ) : (
              output.map((line, index) => (
                <div key={index} className="whitespace-pre-wrap">
                  {line}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Command Input */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-mono">$</span>
          <Input
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Enter command..."
            disabled={isExecuting}
            className="font-mono"
          />
          <Button
            onClick={executeCommand}
            disabled={isExecuting || !command.trim()}
          >
            {isExecuting ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Quick Commands */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={() => setCommand('npm install')}
            variant="outline"
            size="sm"
          >
            npm install
          </Button>
          <Button
            onClick={() => setCommand('npm run dev')}
            variant="outline"
            size="sm"
          >
            npm run dev
          </Button>
          <Button
            onClick={() => setCommand('npm run build')}
            variant="outline"
            size="sm"
          >
            npm run build
          </Button>
          <Button
            onClick={() => setCommand('ls -la')}
            variant="outline"
            size="sm"
          >
            ls -la
          </Button>
          <Button
            onClick={() => setCommand('pwd')}
            variant="outline"
            size="sm"
          >
            pwd
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
