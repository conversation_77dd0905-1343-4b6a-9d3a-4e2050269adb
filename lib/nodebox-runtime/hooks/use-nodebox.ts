/**
 * Nodebox React Hook
 * 
 * Main React hook for managing Nodebox instances
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { NodeboxManager } from '../core/nodebox-manager';
import { NodeboxClient } from '../api/nodebox-client';
import {
  NodeboxConfig,
  NodeboxInstance,
  NodeboxError,
  ProjectTemplate
} from '../api/nodebox-types';

interface UseNodeboxOptions {
  autoConnect?: boolean;
  clientMode?: boolean; // Use client API instead of direct manager
  baseUrl?: string; // For client mode
}

interface UseNodeboxReturn {
  // Instance management
  instances: NodeboxInstance[];
  activeInstance: NodeboxInstance | null;
  isLoading: boolean;
  error: NodeboxError | null;

  // Actions
  createInstance: (config: Omit<NodeboxConfig, 'instanceId'>) => Promise<NodeboxInstance | null>;
  destroyInstance: (instanceId: string) => Promise<boolean>;
  setActiveInstance: (instanceId: string) => void;
  refreshInstances: () => Promise<void>;

  // Template helpers
  createFromTemplate: (template: ProjectTemplate, name: string, projectId?: string) => Promise<NodeboxInstance | null>;

  // Manager/Client instance
  manager: NodeboxManager | null;
  client: NodeboxClient | null;
}

/**
 * Main Nodebox hook
 */
export function useNodebox(options: UseNodeboxOptions = {}): UseNodeboxReturn {
  const {
    autoConnect = true,
    clientMode = false,
    baseUrl = '/api/nodebox'
  } = options;

  // State
  const [instances, setInstances] = useState<NodeboxInstance[]>([]);
  const [activeInstance, setActiveInstanceState] = useState<NodeboxInstance | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<NodeboxError | null>(null);

  // Refs for manager/client
  const managerRef = useRef<NodeboxManager | null>(null);
  const clientRef = useRef<NodeboxClient | null>(null);

  // Initialize manager or client
  useEffect(() => {
    if (clientMode) {
      clientRef.current = new NodeboxClient(baseUrl);
    } else {
      managerRef.current = new NodeboxManager();
    }

    // Load initial instances if auto-connect is enabled
    if (autoConnect) {
      refreshInstances();
    }

    // Cleanup on unmount
    return () => {
      if (managerRef.current) {
        managerRef.current.destroy();
      }
    };
  }, [clientMode, baseUrl, autoConnect]);

  // Refresh instances list
  const refreshInstances = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      let instancesList: NodeboxInstance[];

      if (clientMode && clientRef.current) {
        instancesList = await clientRef.current.listInstances();
      } else if (managerRef.current) {
        instancesList = managerRef.current.listInstances();
      } else {
        throw new NodeboxError('No manager or client available');
      }

      setInstances(instancesList);

      // Set active instance if none is set and we have instances
      if (!activeInstance && instancesList.length > 0) {
        setActiveInstanceState(instancesList[0]);
      }
    } catch (err) {
      const nodeboxError = err instanceof NodeboxError 
        ? err 
        : new NodeboxError(`Failed to refresh instances: ${err instanceof Error ? err.message : String(err)}`);
      
      setError(nodeboxError);
      console.error('Error refreshing instances:', nodeboxError);
    } finally {
      setIsLoading(false);
    }
  }, [clientMode, activeInstance]);

  // Create a new instance
  const createInstance = useCallback(async (
    config: Omit<NodeboxConfig, 'instanceId'>
  ): Promise<NodeboxInstance | null> => {
    setIsLoading(true);
    setError(null);

    try {
      // Generate instance ID
      const instanceId = `nodebox_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const fullConfig: NodeboxConfig = { ...config, instanceId };

      let newInstance: NodeboxInstance;

      if (clientMode && clientRef.current) {
        newInstance = await clientRef.current.createInstance(fullConfig);
      } else if (managerRef.current) {
        newInstance = await managerRef.current.createInstance(fullConfig);
      } else {
        throw new NodeboxError('No manager or client available');
      }

      // Update instances list
      setInstances(prev => [...prev, newInstance]);
      
      // Set as active instance
      setActiveInstanceState(newInstance);

      return newInstance;
    } catch (err) {
      const nodeboxError = err instanceof NodeboxError 
        ? err 
        : new NodeboxError(`Failed to create instance: ${err instanceof Error ? err.message : String(err)}`);
      
      setError(nodeboxError);
      console.error('Error creating instance:', nodeboxError);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [clientMode]);

  // Destroy an instance
  const destroyInstance = useCallback(async (instanceId: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      let success: boolean;

      if (clientMode && clientRef.current) {
        success = await clientRef.current.destroyInstance(instanceId);
      } else if (managerRef.current) {
        success = await managerRef.current.destroyInstance(instanceId);
      } else {
        throw new NodeboxError('No manager or client available');
      }

      if (success) {
        // Remove from instances list
        setInstances(prev => prev.filter(instance => instance.id !== instanceId));
        
        // Clear active instance if it was the destroyed one
        if (activeInstance?.id === instanceId) {
          setActiveInstanceState(null);
        }
      }

      return success;
    } catch (err) {
      const nodeboxError = err instanceof NodeboxError 
        ? err 
        : new NodeboxError(`Failed to destroy instance: ${err instanceof Error ? err.message : String(err)}`);
      
      setError(nodeboxError);
      console.error('Error destroying instance:', nodeboxError);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [clientMode, activeInstance]);

  // Set active instance
  const setActiveInstance = useCallback((instanceId: string) => {
    const instance = instances.find(i => i.id === instanceId);
    if (instance) {
      setActiveInstanceState(instance);
    }
  }, [instances]);

  // Create instance from template
  const createFromTemplate = useCallback(async (
    template: ProjectTemplate,
    name: string,
    projectId?: string
  ): Promise<NodeboxInstance | null> => {
    const config: Omit<NodeboxConfig, 'instanceId'> = {
      name,
      description: `Instance created from ${template} template`,
      template,
      projectId,
      settings: {
        autoSave: true,
        autoPreview: true,
        enableHotReload: true
      }
    };

    return createInstance(config);
  }, [createInstance]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    instances,
    activeInstance,
    isLoading,
    error,

    // Actions
    createInstance,
    destroyInstance,
    setActiveInstance,
    refreshInstances,
    createFromTemplate,

    // Manager/Client
    manager: managerRef.current,
    client: clientRef.current,

    // Additional helpers
    clearError
  } as UseNodeboxReturn & { clearError: () => void };
}
