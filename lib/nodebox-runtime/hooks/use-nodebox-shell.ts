/**
 * Nodebox Shell Hook
 * 
 * React hook for managing shell operations in Nodebox instances
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { NodeboxClient } from '../api/nodebox-client';
import { ShellProcess, NodeboxShellError } from '../api/nodebox-types';

interface UseNodeboxShellOptions {
  instanceId: string;
  autoLoadProcesses?: boolean;
  pollInterval?: number;
}

interface UseNodeboxShellReturn {
  // State
  processes: ShellProcess[];
  activeProcess: ShellProcess | null;
  isExecuting: boolean;
  error: NodeboxShellError | null;
  output: string[];
  commandHistory: string[];

  // Actions
  executeCommand: (command: string, args?: string[]) => Promise<ShellProcess>;
  executeNpmCommand: (subcommand: string, args?: string[]) => Promise<ShellProcess>;
  killProcess: (processId: string) => Promise<boolean>;
  killAllProcesses: () => Promise<void>;
  setActiveProcess: (process: ShellProcess | null) => void;
  clearOutput: () => void;
  clearHistory: () => void;
  refreshProcesses: () => Promise<void>;

  // Utilities
  getProcessOutput: (processId: string) => string;
  getProcessErrors: (processId: string) => string;
  getCombinedOutput: (processId: string) => string;
  isProcessRunning: (processId: string) => boolean;
  getRunningProcesses: () => ShellProcess[];
  getCompletedProcesses: () => ShellProcess[];
  getFailedProcesses: () => ShellProcess[];

  // Quick commands
  installPackages: (packages: string[]) => Promise<ShellProcess>;
  runNpmScript: (scriptName: string) => Promise<ShellProcess>;
  startDevServer: () => Promise<ShellProcess>;
  buildProject: () => Promise<ShellProcess>;
}

/**
 * Nodebox shell hook
 */
export function useNodeboxShell({
  instanceId,
  autoLoadProcesses = true,
  pollInterval = 2000
}: UseNodeboxShellOptions): UseNodeboxShellReturn {
  // State
  const [processes, setProcesses] = useState<ShellProcess[]>([]);
  const [activeProcess, setActiveProcessState] = useState<ShellProcess | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [error, setError] = useState<NodeboxShellError | null>(null);
  const [output, setOutput] = useState<string[]>([]);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);

  const client = new NodeboxClient();
  const pollTimeoutRef = useRef<NodeJS.Timeout>();

  // Execute command
  const executeCommand = useCallback(async (command: string, args: string[] = []): Promise<ShellProcess> => {
    if (!instanceId) throw new NodeboxShellError('No instance ID provided');

    setIsExecuting(true);
    setError(null);

    try {
      // Add to command history
      const fullCommand = [command, ...args].join(' ');
      setCommandHistory(prev => [...prev, fullCommand]);

      // Add command to output
      setOutput(prev => [...prev, `$ ${fullCommand}`]);

      // Execute command
      const process = await client.executeCommand(instanceId, command, args);
      
      // Add to processes list
      setProcesses(prev => [...prev, process]);
      setActiveProcessState(process);

      // Add initial output if available
      if (process.output.length > 0) {
        setOutput(prev => [...prev, ...process.output]);
      }

      if (process.errors.length > 0) {
        setOutput(prev => [...prev, ...process.errors.map(err => `Error: ${err}`)]);
      }

      return process;
    } catch (err) {
      const error = err instanceof NodeboxShellError 
        ? err 
        : new NodeboxShellError(
            `Failed to execute command: ${err instanceof Error ? err.message : String(err)}`,
            instanceId,
            command
          );
      
      setError(error);
      setOutput(prev => [...prev, `Error: ${error.message}`]);
      throw error;
    } finally {
      setIsExecuting(false);
    }
  }, [instanceId, client]);

  // Execute npm command
  const executeNpmCommand = useCallback(async (subcommand: string, args: string[] = []): Promise<ShellProcess> => {
    return executeCommand('npm', [subcommand, ...args]);
  }, [executeCommand]);

  // Kill process
  const killProcess = useCallback(async (processId: string): Promise<boolean> => {
    if (!instanceId) return false;

    try {
      const success = await client.killProcess(instanceId, processId);
      
      if (success) {
        // Update process status
        setProcesses(prev => prev.map(p => 
          p.id === processId 
            ? { ...p, status: 'killed', endTime: new Date() }
            : p
        ));

        // Clear active process if it was killed
        if (activeProcess?.id === processId) {
          setActiveProcessState(null);
        }

        setOutput(prev => [...prev, `Process ${processId} killed`]);
      }

      return success;
    } catch (err) {
      const error = err instanceof NodeboxShellError 
        ? err 
        : new NodeboxShellError(
            `Failed to kill process: ${err instanceof Error ? err.message : String(err)}`,
            instanceId,
            processId
          );
      
      setError(error);
      return false;
    }
  }, [instanceId, activeProcess, client]);

  // Kill all processes
  const killAllProcesses = useCallback(async (): Promise<void> => {
    const runningProcesses = processes.filter(p => p.status === 'running');
    
    await Promise.all(
      runningProcesses.map(process => 
        killProcess(process.id).catch(console.error)
      )
    );
  }, [processes, killProcess]);

  // Set active process
  const setActiveProcess = useCallback((process: ShellProcess | null) => {
    setActiveProcessState(process);
  }, []);

  // Clear output
  const clearOutput = useCallback(() => {
    setOutput([]);
  }, []);

  // Clear command history
  const clearHistory = useCallback(() => {
    setCommandHistory([]);
  }, []);

  // Refresh processes
  const refreshProcesses = useCallback(async () => {
    // This would need to be implemented in the API
    console.log('Refreshing processes for instance:', instanceId);
  }, [instanceId]);

  // Get process output
  const getProcessOutput = useCallback((processId: string): string => {
    const process = processes.find(p => p.id === processId);
    return process ? process.output.join('\n') : '';
  }, [processes]);

  // Get process errors
  const getProcessErrors = useCallback((processId: string): string => {
    const process = processes.find(p => p.id === processId);
    return process ? process.errors.join('\n') : '';
  }, [processes]);

  // Get combined output
  const getCombinedOutput = useCallback((processId: string): string => {
    const process = processes.find(p => p.id === processId);
    if (!process) return '';
    
    const output = process.output.join('\n');
    const errors = process.errors.join('\n');
    
    return output + (errors ? '\n' + errors : '');
  }, [processes]);

  // Check if process is running
  const isProcessRunning = useCallback((processId: string): boolean => {
    const process = processes.find(p => p.id === processId);
    return process?.status === 'running';
  }, [processes]);

  // Get running processes
  const getRunningProcesses = useCallback((): ShellProcess[] => {
    return processes.filter(p => p.status === 'running');
  }, [processes]);

  // Get completed processes
  const getCompletedProcesses = useCallback((): ShellProcess[] => {
    return processes.filter(p => p.status === 'completed');
  }, [processes]);

  // Get failed processes
  const getFailedProcesses = useCallback((): ShellProcess[] => {
    return processes.filter(p => p.status === 'failed');
  }, [processes]);

  // Quick command: Install packages
  const installPackages = useCallback(async (packages: string[]): Promise<ShellProcess> => {
    return executeNpmCommand('install', packages);
  }, [executeNpmCommand]);

  // Quick command: Run npm script
  const runNpmScript = useCallback(async (scriptName: string): Promise<ShellProcess> => {
    return executeNpmCommand('run', [scriptName]);
  }, [executeNpmCommand]);

  // Quick command: Start dev server
  const startDevServer = useCallback(async (): Promise<ShellProcess> => {
    return executeNpmCommand('run', ['dev']);
  }, [executeNpmCommand]);

  // Quick command: Build project
  const buildProject = useCallback(async (): Promise<ShellProcess> => {
    return executeNpmCommand('run', ['build']);
  }, [executeNpmCommand]);

  // Poll for process updates
  const pollProcessUpdates = useCallback(async () => {
    const runningProcesses = processes.filter(p => p.status === 'running');
    
    for (const process of runningProcesses) {
      try {
        const updatedProcess = await client.getProcess(instanceId, process.id);
        
        // Update process in state
        setProcesses(prev => prev.map(p => 
          p.id === process.id ? updatedProcess : p
        ));

        // Update output if there's new content
        if (updatedProcess.output.length > process.output.length) {
          const newOutput = updatedProcess.output.slice(process.output.length);
          setOutput(prev => [...prev, ...newOutput]);
        }

        if (updatedProcess.errors.length > process.errors.length) {
          const newErrors = updatedProcess.errors.slice(process.errors.length);
          setOutput(prev => [...prev, ...newErrors.map(err => `Error: ${err}`)]);
        }
      } catch (err) {
        console.error(`Error polling process ${process.id}:`, err);
      }
    }
  }, [processes, instanceId, client]);

  // Start polling for process updates
  useEffect(() => {
    if (processes.some(p => p.status === 'running')) {
      pollTimeoutRef.current = setTimeout(() => {
        pollProcessUpdates();
      }, pollInterval);
    }

    return () => {
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, [processes, pollInterval, pollProcessUpdates]);

  // Load processes on mount
  useEffect(() => {
    if (autoLoadProcesses && instanceId) {
      refreshProcesses();
    }
  }, [autoLoadProcesses, instanceId, refreshProcesses]);

  return {
    // State
    processes,
    activeProcess,
    isExecuting,
    error,
    output,
    commandHistory,

    // Actions
    executeCommand,
    executeNpmCommand,
    killProcess,
    killAllProcesses,
    setActiveProcess,
    clearOutput,
    clearHistory,
    refreshProcesses,

    // Utilities
    getProcessOutput,
    getProcessErrors,
    getCombinedOutput,
    isProcessRunning,
    getRunningProcesses,
    getCompletedProcesses,
    getFailedProcesses,

    // Quick commands
    installPackages,
    runNpmScript,
    startDevServer,
    buildProject
  };
}
