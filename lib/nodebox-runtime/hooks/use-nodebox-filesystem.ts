/**
 * Nodebox Filesystem Hook
 * 
 * React hook for managing filesystem operations in Nodebox instances
 */

import { useState, useEffect, useCallback } from 'react';
import { NodeboxClient } from '../api/nodebox-client';
import { FileEntry, NodeboxFileSystemError } from '../api/nodebox-types';

interface UseNodeboxFilesystemOptions {
  instanceId: string;
  autoLoad?: boolean;
  initialPath?: string;
}

interface UseNodeboxFilesystemReturn {
  // State
  files: FileEntry[];
  currentPath: string;
  selectedFile: FileEntry | null;
  isLoading: boolean;
  error: NodeboxFileSystemError | null;

  // Actions
  loadFiles: (path?: string) => Promise<void>;
  readFile: (path: string) => Promise<string>;
  writeFile: (path: string, content: string) => Promise<void>;
  deleteFile: (path: string) => Promise<void>;
  createFile: (path: string, content?: string) => Promise<void>;
  createDirectory: (path: string) => Promise<void>;
  navigateToPath: (path: string) => void;
  selectFile: (file: FileEntry) => void;
  clearSelection: () => void;
  refreshCurrentPath: () => Promise<void>;

  // Utilities
  getFileContent: (path: string) => string | null;
  isFileSelected: (path: string) => boolean;
  getParentPath: () => string;
  canNavigateUp: boolean;
}

/**
 * Nodebox filesystem hook
 */
export function useNodeboxFilesystem({
  instanceId,
  autoLoad = true,
  initialPath = '/'
}: UseNodeboxFilesystemOptions): UseNodeboxFilesystemReturn {
  // State
  const [files, setFiles] = useState<FileEntry[]>([]);
  const [currentPath, setCurrentPath] = useState(initialPath);
  const [selectedFile, setSelectedFile] = useState<FileEntry | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<NodeboxFileSystemError | null>(null);
  const [fileContentCache, setFileContentCache] = useState<Map<string, string>>(new Map());

  const client = new NodeboxClient();

  // Load files for a given path
  const loadFiles = useCallback(async (path: string = currentPath) => {
    if (!instanceId) return;

    setIsLoading(true);
    setError(null);

    try {
      const fileList = await client.listFiles(instanceId, path);
      setFiles(fileList);
      setCurrentPath(path);
    } catch (err) {
      const error = err instanceof NodeboxFileSystemError 
        ? err 
        : new NodeboxFileSystemError(
            `Failed to load files: ${err instanceof Error ? err.message : String(err)}`,
            instanceId,
            path
          );
      setError(error);
      console.error('Error loading files:', error);
    } finally {
      setIsLoading(false);
    }
  }, [instanceId, currentPath, client]);

  // Read file content
  const readFile = useCallback(async (path: string): Promise<string> => {
    if (!instanceId) throw new NodeboxFileSystemError('No instance ID provided');

    // Check cache first
    if (fileContentCache.has(path)) {
      return fileContentCache.get(path)!;
    }

    try {
      const content = await client.readFile(instanceId, path);
      
      // Cache the content
      setFileContentCache(prev => new Map(prev).set(path, content));
      
      return content;
    } catch (err) {
      const error = err instanceof NodeboxFileSystemError 
        ? err 
        : new NodeboxFileSystemError(
            `Failed to read file: ${err instanceof Error ? err.message : String(err)}`,
            instanceId,
            path
          );
      throw error;
    }
  }, [instanceId, fileContentCache, client]);

  // Write file content
  const writeFile = useCallback(async (path: string, content: string): Promise<void> => {
    if (!instanceId) throw new NodeboxFileSystemError('No instance ID provided');

    try {
      await client.writeFile(instanceId, path, content);
      
      // Update cache
      setFileContentCache(prev => new Map(prev).set(path, content));
      
      // Refresh file list if we're in the same directory
      const fileDir = path.split('/').slice(0, -1).join('/') || '/';
      if (fileDir === currentPath) {
        await loadFiles();
      }
    } catch (err) {
      const error = err instanceof NodeboxFileSystemError 
        ? err 
        : new NodeboxFileSystemError(
            `Failed to write file: ${err instanceof Error ? err.message : String(err)}`,
            instanceId,
            path
          );
      throw error;
    }
  }, [instanceId, currentPath, loadFiles, client]);

  // Delete file
  const deleteFile = useCallback(async (path: string): Promise<void> => {
    if (!instanceId) throw new NodeboxFileSystemError('No instance ID provided');

    try {
      // This would need to be implemented in the client
      console.log('Deleting file:', path);
      
      // Remove from cache
      setFileContentCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(path);
        return newCache;
      });
      
      // Clear selection if deleted file was selected
      if (selectedFile?.path === path) {
        setSelectedFile(null);
      }
      
      // Refresh file list
      await loadFiles();
    } catch (err) {
      const error = err instanceof NodeboxFileSystemError 
        ? err 
        : new NodeboxFileSystemError(
            `Failed to delete file: ${err instanceof Error ? err.message : String(err)}`,
            instanceId,
            path
          );
      throw error;
    }
  }, [instanceId, selectedFile, loadFiles]);

  // Create new file
  const createFile = useCallback(async (path: string, content: string = ''): Promise<void> => {
    await writeFile(path, content);
  }, [writeFile]);

  // Create directory
  const createDirectory = useCallback(async (path: string): Promise<void> => {
    if (!instanceId) throw new NodeboxFileSystemError('No instance ID provided');

    try {
      // This would need to be implemented in the client
      console.log('Creating directory:', path);
      
      // Refresh file list
      await loadFiles();
    } catch (err) {
      const error = err instanceof NodeboxFileSystemError 
        ? err 
        : new NodeboxFileSystemError(
            `Failed to create directory: ${err instanceof Error ? err.message : String(err)}`,
            instanceId,
            path
          );
      throw error;
    }
  }, [instanceId, loadFiles]);

  // Navigate to path
  const navigateToPath = useCallback((path: string) => {
    setCurrentPath(path);
    loadFiles(path);
  }, [loadFiles]);

  // Select file
  const selectFile = useCallback((file: FileEntry) => {
    setSelectedFile(file);
  }, []);

  // Clear selection
  const clearSelection = useCallback(() => {
    setSelectedFile(null);
  }, []);

  // Refresh current path
  const refreshCurrentPath = useCallback(async () => {
    await loadFiles(currentPath);
  }, [loadFiles, currentPath]);

  // Get cached file content
  const getFileContent = useCallback((path: string): string | null => {
    return fileContentCache.get(path) || null;
  }, [fileContentCache]);

  // Check if file is selected
  const isFileSelected = useCallback((path: string): boolean => {
    return selectedFile?.path === path;
  }, [selectedFile]);

  // Get parent path
  const getParentPath = useCallback((): string => {
    if (currentPath === '/') return '/';
    
    const parts = currentPath.split('/').filter(Boolean);
    if (parts.length <= 1) return '/';
    
    return '/' + parts.slice(0, -1).join('/');
  }, [currentPath]);

  // Check if can navigate up
  const canNavigateUp = currentPath !== '/';

  // Auto-load files on mount or when instanceId changes
  useEffect(() => {
    if (autoLoad && instanceId) {
      loadFiles(initialPath);
    }
  }, [autoLoad, instanceId, initialPath, loadFiles]);

  return {
    // State
    files,
    currentPath,
    selectedFile,
    isLoading,
    error,

    // Actions
    loadFiles,
    readFile,
    writeFile,
    deleteFile,
    createFile,
    createDirectory,
    navigateToPath,
    selectFile,
    clearSelection,
    refreshCurrentPath,

    // Utilities
    getFileContent,
    isFileSelected,
    getParentPath,
    canNavigateUp
  };
}
