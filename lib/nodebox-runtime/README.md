# Nodebox Runtime Integration

This library provides a comprehensive TypeScript implementation for integrating Nodebox (browser-based Node.js runtime) into the application.

## Overview

Nodebox is a runtime for executing Node.js modules directly in the browser, developed by CodeSandbox. This integration allows users to:

- Run Node.js applications entirely in the browser
- Create and manage multiple Nodebox instances
- Execute shell commands and manage processes
- Browse and edit files in a virtual filesystem
- Preview applications with live reload
- Use pre-configured project templates

## Features

### Core Capabilities
- **Browser-based Node.js runtime**: No server infrastructure required
- **Multiple instance management**: Create and manage multiple isolated environments
- **File system operations**: Full CRUD operations on virtual files and directories
- **Shell command execution**: Run npm commands, scripts, and other shell operations
- **Live preview**: Real-time application preview with multiple viewport sizes
- **Project templates**: Pre-configured templates for React, Next.js, Express, etc.

### Components
- **NodeboxWorkspace**: Main workspace component with tabbed interface
- **NodeboxFileExplorer**: File system browser with search and operations
- **NodeboxTerminal**: Terminal interface for command execution
- **NodeboxPreview**: Application preview with responsive viewport controls

### API Integration
- **REST API routes**: Backend API for instance management
- **Client SDK**: TypeScript client for API interactions
- **React hooks**: Custom hooks for state management and operations

## Architecture

```
lib/nodebox-runtime/
├── core/                    # Core runtime management
│   ├── nodebox-manager.ts   # Main instance manager
│   ├── filesystem-manager.ts # File operations
│   ├── shell-manager.ts     # Command execution
│   └── preview-manager.ts   # Preview management
├── api/                     # API layer
│   ├── nodebox-client.ts    # HTTP client
│   └── nodebox-types.ts     # TypeScript definitions
├── components/              # React components
│   ├── nodebox-workspace.tsx
│   ├── nodebox-file-explorer.tsx
│   ├── nodebox-terminal.tsx
│   └── nodebox-preview.tsx
├── hooks/                   # React hooks
│   ├── use-nodebox.ts
│   ├── use-nodebox-filesystem.ts
│   └── use-nodebox-shell.ts
├── utils/                   # Utilities
│   ├── project-templates.ts
│   └── nodebox-helpers.ts
└── index.ts                 # Main exports
```

## Usage

### Basic Usage

```typescript
import { useNodebox } from '@/lib/nodebox-runtime';

function MyComponent() {
  const {
    instances,
    activeInstance,
    createInstance,
    createFromTemplate
  } = useNodebox();

  const handleCreateReactApp = async () => {
    await createFromTemplate('react', 'My React App');
  };

  return (
    <div>
      <button onClick={handleCreateReactApp}>
        Create React App
      </button>
      {/* ... */}
    </div>
  );
}
```

### Using the Workspace Component

```typescript
import { NodeboxWorkspace } from '@/lib/nodebox-runtime';

function App() {
  return (
    <NodeboxWorkspace
      projectId="my-project"
      onInstanceChange={(instance) => {
        console.log('Active instance:', instance);
      }}
    />
  );
}
```

### File Operations

```typescript
import { useNodeboxFilesystem } from '@/lib/nodebox-runtime';

function FileManager({ instanceId }: { instanceId: string }) {
  const {
    files,
    readFile,
    writeFile,
    createFile
  } = useNodeboxFilesystem({ instanceId });

  const handleCreateFile = async () => {
    await createFile('/src/new-file.js', 'console.log("Hello World");');
  };

  return (
    <div>
      {files.map(file => (
        <div key={file.path}>{file.name}</div>
      ))}
    </div>
  );
}
```

### Shell Operations

```typescript
import { useNodeboxShell } from '@/lib/nodebox-runtime';

function Terminal({ instanceId }: { instanceId: string }) {
  const {
    executeCommand,
    installPackages,
    startDevServer,
    output
  } = useNodeboxShell({ instanceId });

  const handleInstallReact = async () => {
    await installPackages(['react', 'react-dom']);
  };

  return (
    <div>
      <button onClick={handleInstallReact}>Install React</button>
      <button onClick={startDevServer}>Start Dev Server</button>
      <pre>{output.join('\n')}</pre>
    </div>
  );
}
```

## API Routes

The integration includes REST API routes for backend operations:

- `POST /api/nodebox/create` - Create new instance
- `GET /api/nodebox/list` - List all instances
- `GET /api/nodebox/[instanceId]` - Get instance details
- `DELETE /api/nodebox/[instanceId]` - Destroy instance
- `GET /api/nodebox/[instanceId]/files` - List files
- `PUT /api/nodebox/[instanceId]/files/[path]` - Write file
- `POST /api/nodebox/[instanceId]/shell/execute` - Execute command

## Project Templates

Available templates:
- **React**: Modern React application with hooks
- **Next.js**: Next.js application with TypeScript
- **Express**: Express.js server with API routes
- **Vanilla JS**: Simple HTML/CSS/JS project
- **TypeScript**: TypeScript project with build setup
- **Research Dashboard**: Data analysis dashboard
- **Data Analysis**: Node.js data processing scripts

## Integration with Main Application

The Nodebox integration is fully integrated with the main workspace:

1. **Activity Bar**: Nodebox items in the activity bar
2. **Agent System**: Dedicated "nodebox" agent type
3. **Tab System**: Nodebox tabs in main and panel areas
4. **Workspace Layout**: Seamless integration with existing layout

## Testing

Visit `/test-nodebox` to test the integration:

```bash
# Navigate to the test page
http://localhost:3000/test-nodebox
```

## Limitations

Current limitations of the Nodebox runtime:
- No N-API modules support
- No external network sockets
- No synchronous exec/spawn
- Manual process.exit required for some operations

## Future Enhancements

Planned improvements:
- Real-time collaboration features
- Enhanced debugging capabilities
- More project templates
- Integration with version control
- Performance optimizations
- Advanced security features

## Dependencies

- `@codesandbox/nodebox`: Core Nodebox runtime
- React 18+ for UI components
- Next.js for API routes
- TypeScript for type safety

## Contributing

When contributing to the Nodebox integration:

1. Follow the existing code structure
2. Add comprehensive TypeScript types
3. Include error handling and validation
4. Write tests for new functionality
5. Update documentation as needed

## License

This integration follows the same license as the main application.
