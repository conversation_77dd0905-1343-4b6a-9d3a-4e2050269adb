/**
 * SSH WebSocket Proxy Service
 * 
 * This service handles WebSocket connections for SSH, providing a bridge
 * between the browser terminal and the SSH server on the VM.
 */

import WebSocket from 'ws';
import { IncomingMessage } from 'http';
import { URL } from 'url';
import { Client, ClientChannel } from 'ssh2';
import { createNamespacedLogger } from '@/lib/logger';
import { randomBytes } from 'crypto';

// Create a logger for this module
const logger = createNamespacedLogger('ssh-proxy');

// Store active SSH sessions
const activeSessions = new Map<string, {
  sessionId: string;
  expires: number;
  client: Client;
  channel?: ClientChannel;
}>();

// Session expiration time (4 hours)
const SESSION_EXPIRATION = 4 * 60 * 60 * 1000;

/**
 * Generate a secure session ID
 */
function generateSessionId(): string {
  return randomBytes(16).toString('hex');
}

/**
 * Clean up expired sessions
 */
function cleanupExpiredSessions() {
  const now = Date.now();
  for (const [sessionId, session] of activeSessions.entries()) {
    if (session.expires < now) {
      // Close the SSH connection
      if (session.channel) {
        try {
          session.channel.close();
        } catch (error) {
          logger.warn('Error closing SSH channel', { sessionId, error });
        }
      }
      
      try {
        session.client.end();
      } catch (error) {
        logger.warn('Error ending SSH client', { sessionId, error });
      }
      
      activeSessions.delete(sessionId);
      logger.debug(`Removed expired SSH session: ${sessionId}`);
    }
  }
}

// Run cleanup every 15 minutes
setInterval(cleanupExpiredSessions, 15 * 60 * 1000);

/**
 * Handle a WebSocket connection for SSH
 */
export function handleSSHWebSocket(
  ws: WebSocket,
  request: IncomingMessage
) {
  try {
    // Parse the URL to get query parameters
    const url = new URL(request.url || '', `http://${request.headers.host}`);
    const host = url.searchParams.get('host');
    const port = parseInt(url.searchParams.get('port') || '22', 10);
    const username = url.searchParams.get('username') || 'root';
    const vmId = url.searchParams.get('vmId');
    
    // Validate required parameters
    if (!host) {
      logger.warn('Missing required SSH parameters', { host, port, username });
      ws.close(4000, 'Missing required parameters');
      return;
    }
    
    // Generate a session ID
    const sessionId = generateSessionId();
    
    logger.info('SSH WebSocket connection established', { 
      sessionId, 
      host, 
      port, 
      username,
      vmId
    });
    
    // Create SSH client
    const sshClient = new Client();
    
    // Store the session
    activeSessions.set(sessionId, {
      sessionId,
      expires: Date.now() + SESSION_EXPIRATION,
      client: sshClient,
    });
    
    // Handle authentication
    let authenticated = false;
    let authenticationMethod: 'password' | 'privateKey' | null = null;
    
    // Handle messages from the client
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());
        
        // Handle authentication
        if (data.type === 'auth') {
          if (authenticated) {
            ws.send(JSON.stringify({ 
              type: 'error', 
              message: 'Already authenticated' 
            }));
            return;
          }
          
          authenticationMethod = data.method;
          
          if (authenticationMethod === 'password') {
            // Connect with password authentication
            sshClient.connect({
              host,
              port,
              username,
              password: data.password,
              readyTimeout: 30000,
              keepaliveInterval: 30000,
            });
          } else if (authenticationMethod === 'privateKey') {
            // Connect with private key authentication
            sshClient.connect({
              host,
              port,
              username,
              privateKey: data.privateKey,
              readyTimeout: 30000,
              keepaliveInterval: 30000,
            });
          } else {
            ws.send(JSON.stringify({ 
              type: 'error', 
              message: 'Invalid authentication method' 
            }));
          }
        }
        // Handle terminal data
        else if (data.type === 'data') {
          if (!authenticated) {
            ws.send(JSON.stringify({ 
              type: 'error', 
              message: 'Not authenticated' 
            }));
            return;
          }
          
          const session = activeSessions.get(sessionId);
          if (session && session.channel) {
            session.channel.write(data.data);
          }
        }
        // Handle terminal resize
        else if (data.type === 'resize') {
          if (!authenticated) {
            ws.send(JSON.stringify({ 
              type: 'error', 
              message: 'Not authenticated' 
            }));
            return;
          }
          
          const session = activeSessions.get(sessionId);
          if (session && session.channel) {
            session.channel.setWindow(data.rows, data.cols, data.height, data.width);
          }
        }
        // Handle unknown message types
        else {
          logger.warn('Unknown message type', { type: data.type });
        }
      } catch (error) {
        logger.error('Error processing client message', { 
          error: error instanceof Error ? error.message : String(error) 
        });
      }
    });
    
    // Handle SSH client events
    sshClient.on('ready', () => {
      authenticated = true;
      
      // Update session expiration
      const session = activeSessions.get(sessionId);
      if (session) {
        session.expires = Date.now() + SESSION_EXPIRATION;
      }
      
      // Send authentication success message
      ws.send(JSON.stringify({ 
        type: 'auth', 
        success: true 
      }));
      
      logger.debug('SSH client authenticated', { sessionId, host, username });
      
      // Create a new shell session
      sshClient.shell({ term: 'xterm-256color' }, (err, channel) => {
        if (err) {
          logger.error('Error creating SSH shell', { 
            sessionId, 
            error: err.message 
          });
          ws.send(JSON.stringify({ 
            type: 'error', 
            message: `Error creating shell: ${err.message}` 
          }));
          return;
        }
        
        // Store the channel in the session
        const session = activeSessions.get(sessionId);
        if (session) {
          session.channel = channel;
        }
        
        // Handle data from the SSH server
        channel.on('data', (data) => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ 
              type: 'data', 
              data: data.toString() 
            }));
          }
        });
        
        // Handle extended data (stderr)
        channel.on('extended data', (data) => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ 
              type: 'data', 
              data: data.toString() 
            }));
          }
        });
        
        // Handle channel close
        channel.on('close', () => {
          logger.debug('SSH channel closed', { sessionId });
          ws.send(JSON.stringify({ 
            type: 'close', 
            message: 'SSH connection closed' 
          }));
          ws.close();
          
          // Remove the session
          activeSessions.delete(sessionId);
        });
        
        // Handle channel errors
        channel.on('error', (error) => {
          logger.error('SSH channel error', { 
            sessionId, 
            error: error.message 
          });
          ws.send(JSON.stringify({ 
            type: 'error', 
            message: `SSH error: ${error.message}` 
          }));
        });
      });
    });
    
    // Handle SSH client errors
    sshClient.on('error', (error) => {
      logger.error('SSH client error', { 
        sessionId, 
        host, 
        username, 
        error: error.message 
      });
      
      ws.send(JSON.stringify({ 
        type: 'error', 
        message: `SSH connection error: ${error.message}` 
      }));
      
      // Remove the session
      activeSessions.delete(sessionId);
    });
    
    // Handle SSH client end
    sshClient.on('end', () => {
      logger.debug('SSH client ended', { sessionId });
      
      ws.send(JSON.stringify({ 
        type: 'close', 
        message: 'SSH connection ended' 
      }));
      
      // Remove the session
      activeSessions.delete(sessionId);
    });
    
    // Handle SSH client close
    sshClient.on('close', () => {
      logger.debug('SSH client closed', { sessionId });
      
      ws.send(JSON.stringify({ 
        type: 'close', 
        message: 'SSH connection closed' 
      }));
      
      // Close the WebSocket if it's still open
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
      
      // Remove the session
      activeSessions.delete(sessionId);
    });
    
    // Handle WebSocket close
    ws.on('close', () => {
      logger.debug('WebSocket closed', { sessionId });
      
      // Get the session
      const session = activeSessions.get(sessionId);
      
      // Close the SSH connection
      if (session) {
        if (session.channel) {
          try {
            session.channel.close();
          } catch (error) {
            logger.warn('Error closing SSH channel', { 
              sessionId, 
              error: error instanceof Error ? error.message : String(error) 
            });
          }
        }
        
        try {
          session.client.end();
        } catch (error) {
          logger.warn('Error ending SSH client', { 
            sessionId, 
            error: error instanceof Error ? error.message : String(error) 
          });
        }
        
        // Remove the session
        activeSessions.delete(sessionId);
      }
    });
    
  } catch (error) {
    logger.error('Error handling SSH WebSocket connection', { 
      error: error instanceof Error ? error.message : String(error) 
    });
    ws.close(4004, `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
