/**
 * WebSocket Handler for Next.js
 * 
 * This module provides a WebSocket handler for Next.js API routes,
 * allowing WebSocket connections to be handled by the API.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { IncomingMessage, ServerResponse } from 'http';
import { Server as WebSocketServer } from 'ws';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('websocket-handler');

// Store WebSocket servers by path
const webSocketServers = new Map<string, WebSocketServer>();

/**
 * WebSocket handler function type
 */
export type WebSocketHandler = (
  ws: WebSocket,
  request: IncomingMessage
) => void;

/**
 * Create a WebSocket handler for a Next.js API route
 */
export function createWebSocketHandler(
  path: string,
  handler: WebSocketHandler
) {
  // Create a new WebSocket server if one doesn't exist for this path
  if (!webSocketServers.has(path)) {
    const wss = new WebSocketServer({ noServer: true });
    
    // Set up connection handler
    wss.on('connection', (ws, request) => {
      logger.debug('WebSocket connection established', { path });
      
      // Call the handler
      handler(ws as unknown as WebSocket, request);
    });
    
    // Store the WebSocket server
    webSocketServers.set(path, wss);
    
    logger.info('WebSocket server created', { path });
  }
  
  // Return the handler function for the Next.js API route
  return async (req: NextApiRequest, res: NextApiResponse) => {
    // Only handle WebSocket upgrade requests
    if (req.method !== 'GET') {
      res.status(405).json({ error: 'Method not allowed' });
      return;
    }
    
    // Check if this is a WebSocket upgrade request
    if (!req.headers['upgrade'] || req.headers['upgrade'].toLowerCase() !== 'websocket') {
      res.status(400).json({ error: 'Expected WebSocket connection' });
      return;
    }
    
    // Get the WebSocket server for this path
    const wss = webSocketServers.get(path);
    if (!wss) {
      res.status(500).json({ error: 'WebSocket server not found' });
      return;
    }
    
    // Handle the upgrade request
    const { socket, head } = res.socket as any;
    
    try {
      wss.handleUpgrade(req, socket, head, (ws) => {
        wss.emit('connection', ws, req);
      });
    } catch (error) {
      logger.error('Error handling WebSocket upgrade', { 
        path, 
        error: error instanceof Error ? error.message : String(error) 
      });
      socket.destroy();
    }
  };
}

/**
 * Get a WebSocket server by path
 */
export function getWebSocketServer(path: string): WebSocketServer | undefined {
  return webSocketServers.get(path);
}

/**
 * Close all WebSocket servers
 */
export function closeAllWebSocketServers() {
  for (const [path, wss] of webSocketServers.entries()) {
    wss.close();
    webSocketServers.delete(path);
    logger.info('WebSocket server closed', { path });
  }
}
