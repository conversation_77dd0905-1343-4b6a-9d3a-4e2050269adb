/**
 * VNC WebSocket Proxy Service
 * 
 * This service handles WebSocket connections for VNC, proxying between
 * the browser client and the Proxmox VNC server.
 */

import WebSocket from 'ws';
import { IncomingMessage } from 'http';
import { URL } from 'url';
import { createNamespacedLogger } from '@/lib/logger';
import { getServerById } from '@/lib/server-config';
import { createProxmoxClient } from '@/lib/proxmox/client';
import { randomBytes } from 'crypto';

// Create a logger for this module
const logger = createNamespacedLogger('vnc-proxy');

// Store active connections with security tokens
const activeConnections = new Map<string, {
  token: string;
  expires: number;
  vmId: string;
  serverId: string;
  node: string;
  port: number;
  ticket: string;
}>();

// Token expiration time (15 minutes)
const TOKEN_EXPIRATION = 15 * 60 * 1000;

/**
 * Generate a secure token for VNC connections
 */
function generateSecureToken(): string {
  return randomBytes(32).toString('hex');
}

/**
 * Clean up expired tokens
 */
function cleanupExpiredTokens() {
  const now = Date.now();
  for (const [token, connection] of activeConnections.entries()) {
    if (connection.expires < now) {
      activeConnections.delete(token);
      logger.debug(`Removed expired VNC token: ${token}`);
    }
  }
}

// Run cleanup every minute
setInterval(cleanupExpiredTokens, 60 * 1000);

/**
 * Get VNC connection details from Proxmox
 */
export async function getVNCConnectionDetails(serverId: string, vmId: string) {
  try {
    logger.debug('Getting VNC connection details', { serverId, vmId });
    
    // Get server configuration
    const server = await getServerById(serverId);
    if (!server) {
      throw new Error(`Server not found: ${serverId}`);
    }
    
    // Create Proxmox client
    const proxmoxClient = createProxmoxClient(server);
    
    // Get authentication ticket
    const authResponse = await fetch(`${server.url}/api2/json/access/ticket`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        username: server.username,
        password: server.password,
      }),
    });
    
    if (!authResponse.ok) {
      throw new Error(`Authentication failed: ${authResponse.statusText}`);
    }
    
    const authData = await authResponse.json();
    const ticket = {
      ticket: authData.data.ticket,
      csrfToken: authData.data.CSRFPreventionToken,
      username: authData.data.username,
    };
    
    // Set the authentication ticket
    proxmoxClient.setTicket(ticket);
    
    // Find the node for this VM
    const resources = await proxmoxClient.getResources('vm');
    const vmResource = resources.find(r => r.id === `qemu/${vmId}`);
    
    if (!vmResource) {
      throw new Error(`VM not found: ${vmId}`);
    }
    
    const node = vmResource.node;
    
    // Get VNC proxy details
    const vncProxyDetails = await proxmoxClient.getVNCProxy(node, vmId);
    
    // Generate a secure token for this connection
    const token = generateSecureToken();
    
    // Store connection details with the token
    activeConnections.set(token, {
      token,
      expires: Date.now() + TOKEN_EXPIRATION,
      vmId,
      serverId,
      node,
      port: vncProxyDetails.port,
      ticket: vncProxyDetails.ticket,
    });
    
    logger.info('VNC connection details retrieved', { 
      serverId, 
      vmId, 
      node, 
      port: vncProxyDetails.port 
    });
    
    // Return connection details to the client
    return {
      token,
      password: vncProxyDetails.ticket,
      port: vncProxyDetails.port,
      node,
    };
    
  } catch (error) {
    logger.error('Error getting VNC connection details', { 
      serverId, 
      vmId, 
      error: error instanceof Error ? error.message : String(error) 
    });
    throw error;
  }
}

/**
 * Handle a WebSocket connection for VNC
 */
export function handleVNCWebSocket(
  ws: WebSocket,
  request: IncomingMessage
) {
  try {
    // Parse the URL to get query parameters
    const url = new URL(request.url || '', `http://${request.headers.host}`);
    const token = url.searchParams.get('token');
    
    // Validate the token
    if (!token || !activeConnections.has(token)) {
      logger.warn('Invalid or missing VNC token', { token });
      ws.close(4000, 'Invalid or missing token');
      return;
    }
    
    // Get connection details
    const connection = activeConnections.get(token)!;
    
    // Check if token is expired
    if (connection.expires < Date.now()) {
      logger.warn('Expired VNC token', { token });
      activeConnections.delete(token);
      ws.close(4001, 'Token expired');
      return;
    }
    
    logger.info('VNC WebSocket connection established', { 
      vmId: connection.vmId, 
      serverId: connection.serverId,
      node: connection.node,
      port: connection.port
    });
    
    // Get server configuration
    getServerById(connection.serverId).then(server => {
      if (!server) {
        throw new Error(`Server not found: ${connection.serverId}`);
      }
      
      // Extract hostname from server URL
      const serverUrl = new URL(server.url);
      const hostname = serverUrl.hostname;
      
      // Create a WebSocket connection to the Proxmox VNC server
      const proxmoxWs = new WebSocket(`wss://${hostname}:${connection.port}/websockify`, {
        rejectUnauthorized: false, // For development - should be configurable in production
      });
      
      // Handle messages from the client
      ws.on('message', (message) => {
        if (proxmoxWs.readyState === WebSocket.OPEN) {
          proxmoxWs.send(message);
        }
      });
      
      // Handle messages from the Proxmox server
      proxmoxWs.on('message', (message) => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(message);
        }
      });
      
      // Handle errors
      proxmoxWs.on('error', (error) => {
        logger.error('Proxmox VNC WebSocket error', { 
          vmId: connection.vmId, 
          error: error.message 
        });
        ws.close(4002, `Proxmox VNC error: ${error.message}`);
      });
      
      // Handle close
      proxmoxWs.on('close', (code, reason) => {
        logger.debug('Proxmox VNC WebSocket closed', { 
          vmId: connection.vmId, 
          code, 
          reason: reason.toString() 
        });
        ws.close(code, reason.toString());
      });
      
      // Handle client close
      ws.on('close', (code, reason) => {
        logger.debug('Client VNC WebSocket closed', { 
          vmId: connection.vmId, 
          code, 
          reason: reason.toString() 
        });
        proxmoxWs.close(code, reason.toString());
      });
      
    }).catch(error => {
      logger.error('Error establishing VNC proxy connection', { 
        vmId: connection.vmId, 
        error: error.message 
      });
      ws.close(4003, `Error establishing VNC proxy: ${error.message}`);
    });
    
  } catch (error) {
    logger.error('Error handling VNC WebSocket connection', { 
      error: error instanceof Error ? error.message : String(error) 
    });
    ws.close(4004, `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
