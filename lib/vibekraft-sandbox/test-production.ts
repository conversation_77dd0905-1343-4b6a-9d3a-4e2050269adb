/**
 * Test script for production VibeKraft Sandbox Manager
 * 
 * This script tests the production Docker-based sandbox manager
 */

import { SandboxManager } from './core/sandbox-manager';
import { SandboxConfig } from './types/sandbox';

async function testProductionSandboxManager() {
  console.log('🚀 Testing Production VibeKraft Sandbox Manager...\n');

  // Initialize sandbox manager with production configuration
  const sandboxManager = new SandboxManager({
    maxConcurrentSandboxes: 10,
    defaultResourceLimits: {
      memory: 1 * 1024 * 1024 * 1024, // 1GB
      cpus: 1,
      diskQuota: 5 * 1024 * 1024 * 1024 // 5GB
    },
    networkConfig: {
      defaultNetwork: 'vibekraft-sandbox-network',
      enableNetworking: true
    },
    securityConfig: {
      allowPrivileged: false,
      defaultUser: '1000:1000',
      readOnlyRootfs: false
    },
    enableLogging: true,
    enableMonitoring: true
  });

  try {
    // Test 1: Initialize the manager
    console.log('📋 Test 1: Initializing sandbox manager...');
    await sandboxManager.initialize();
    console.log('✅ Sandbox manager initialized successfully\n');

    // Test 2: Create a sandbox
    console.log('📋 Test 2: Creating a new sandbox...');
    const sandboxConfig: Partial<SandboxConfig> = {
      name: 'Test Production Sandbox',
      template: 'ubuntu:22.04',
      userId: 'test-user',
      resources: {
        vcpuCount: 1,
        memSizeMib: 512,
        diskSizeGb: 2,
        diskType: 'ssd'
      },
      network: {
        ports: [],
        isolated: false
      },
      security: {
        readOnlyRootfs: false,
        noNewPrivileges: true,
        capabilities: {
          drop: ['ALL'],
          add: []
        },
        runAsUser: 1000,
        runAsGroup: 1000,
        allowSudo: false,
        allowNetworkAccess: true,
        allowFileSystemAccess: true
      },
      environment: {
        'NODE_ENV': 'development',
        'WORKSPACE': '/workspace'
      }
    };

    const sandboxId = await sandboxManager.createSandbox(sandboxConfig);
    console.log(`✅ Sandbox created successfully: ${sandboxId}\n`);

    // Test 3: Get sandbox configuration
    console.log('📋 Test 3: Getting sandbox configuration...');
    const config = sandboxManager.getSandboxConfig(sandboxId);
    console.log(`✅ Retrieved config for sandbox: ${config.name}\n`);

    // Test 4: Get sandbox status
    console.log('📋 Test 4: Getting sandbox status...');
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    console.log(`✅ Sandbox status: ${status.state} (health: ${status.health.status})\n`);

    // Test 5: Execute a command
    console.log('📋 Test 5: Executing command in sandbox...');
    const commandResult = await sandboxManager.executeCommand(sandboxId, {
      command: ['echo', 'Hello from VibeKraft Sandbox!'],
      timeout: 10000
    });
    console.log(`✅ Command executed successfully:`);
    console.log(`   Exit code: ${commandResult.exitCode}`);
    console.log(`   Output: ${commandResult.stdout.trim()}`);
    console.log(`   Duration: ${commandResult.duration}ms\n`);

    // Test 6: List all sandboxes
    console.log('📋 Test 6: Listing all sandboxes...');
    const sandboxes = await sandboxManager.listSandboxes();
    console.log(`✅ Found ${sandboxes.length} sandbox(es)\n`);

    // Test 7: Clean up
    console.log('📋 Test 7: Cleaning up sandbox...');
    await sandboxManager.deleteSandbox(sandboxId);
    console.log('✅ Sandbox deleted successfully\n');

    // Test 8: Cleanup manager
    console.log('📋 Test 8: Cleaning up sandbox manager...');
    await sandboxManager.cleanup();
    console.log('✅ Sandbox manager cleaned up successfully\n');

    console.log('🎉 All tests passed! Production sandbox manager is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Attempt cleanup on error
    try {
      await sandboxManager.cleanup();
    } catch (cleanupError) {
      console.error('❌ Cleanup failed:', cleanupError);
    }
    
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testProductionSandboxManager()
    .then(() => {
      console.log('\n✨ Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed with error:', error);
      process.exit(1);
    });
}

export { testProductionSandboxManager };
