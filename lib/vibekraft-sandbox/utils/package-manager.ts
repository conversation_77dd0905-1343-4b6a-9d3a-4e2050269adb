/**
 * VibeKraft Package Manager Utilities
 * 
 * Utilities for package management operations within sandbox containers
 */

import { 
  PackageManager, 
  PackageInfo, 
  PackageInstallRequest, 
  PackageInstallResponse,
  PackageListResponse,
  PackageSearchRequest,
  PackageSearchResult
} from '../types/sandbox';

export interface PackageManagerOptions {
  enableGlobalInstalls?: boolean;
  maxInstallTime?: number;
  allowedManagers?: string[];
  blockedPackages?: string[];
  enableCache?: boolean;
}

export class PackageManagerService {
  private options: Required<PackageManagerOptions>;
  private dockerManager: any; // Will be injected

  constructor(dockerManager: any, options: PackageManagerOptions = {}) {
    this.dockerManager = dockerManager;
    this.options = {
      enableGlobalInstalls: options.enableGlobalInstalls ?? false,
      maxInstallTime: options.maxInstallTime || 600, // 10 minutes
      allowedManagers: options.allowedManagers || [
        'npm', 'yarn', 'pnpm', 'pip', 'poetry', 'apt', 'cargo', 'go'
      ],
      blockedPackages: options.blockedPackages || [
        'sudo', 'passwd', 'shadow-utils', 'systemd', 'init'
      ],
      enableCache: options.enableCache ?? true
    };
  }

  /**
   * Detect available package managers in the container
   */
  async detectPackageManagers(containerId: string): Promise<PackageManager[]> {
    const managers: PackageManager[] = [];
    
    const managerCommands = {
      npm: 'npm --version',
      yarn: 'yarn --version',
      pnpm: 'pnpm --version',
      pip: 'pip --version',
      poetry: 'poetry --version',
      apt: 'apt --version',
      yum: 'yum --version',
      cargo: 'cargo --version',
      go: 'go version',
      maven: 'mvn --version',
      gradle: 'gradle --version'
    };

    for (const [name, command] of Object.entries(managerCommands)) {
      try {
        const result = await this.dockerManager.executeCommand(containerId, {
          command: command.split(' '),
          timeout: 10
        });

        if (result.exitCode === 0) {
          const version = this.extractVersion(result.stdout, name);
          managers.push({
            name: name as any,
            version,
            available: true,
            configPath: await this.getConfigPath(containerId, name),
            lockFile: await this.getLockFile(containerId, name)
          });
        }
      } catch (error) {
        // Manager not available
        managers.push({
          name: name as any,
          version: 'N/A',
          available: false
        });
      }
    }

    return managers;
  }

  /**
   * Install packages using specified package manager
   */
  async installPackages(
    containerId: string, 
    request: PackageInstallRequest
  ): Promise<PackageInstallResponse> {
    const startTime = Date.now();
    
    try {
      // Validate request
      this.validateInstallRequest(request);
      
      // Detect best package manager if not specified
      const manager = request.manager || await this.detectBestManager(containerId, request.workingDir);
      
      // Build install command
      const command = this.buildInstallCommand(manager, request);
      
      // Execute installation
      const result = await this.dockerManager.executeCommand(containerId, {
        command,
        workingDir: request.workingDir || '/workspace',
        timeout: request.timeout || this.options.maxInstallTime
      });

      const duration = Date.now() - startTime;

      if (result.exitCode !== 0) {
        return {
          success: false,
          packages: request.packages,
          manager,
          operation: 'install',
          output: result.stdout,
          error: result.stderr,
          duration,
          timestamp: new Date()
        };
      }

      return {
        success: true,
        packages: request.packages,
        manager,
        operation: 'install',
        output: result.stdout,
        duration,
        timestamp: new Date()
      };

    } catch (error: any) {
      return {
        success: false,
        packages: request.packages,
        manager: request.manager || 'unknown',
        operation: 'install',
        output: '',
        error: error.message,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    }
  }

  /**
   * Uninstall packages
   */
  async uninstallPackages(
    containerId: string,
    packages: string[],
    manager?: string,
    workingDir?: string
  ): Promise<PackageInstallResponse> {
    const startTime = Date.now();
    
    try {
      // Detect package manager if not specified
      const detectedManager = manager || await this.detectBestManager(containerId, workingDir);
      
      // Build uninstall command
      const command = this.buildUninstallCommand(detectedManager, packages);
      
      // Execute uninstallation
      const result = await this.dockerManager.executeCommand(containerId, {
        command,
        workingDir: workingDir || '/workspace',
        timeout: this.options.maxInstallTime
      });

      const duration = Date.now() - startTime;

      return {
        success: result.exitCode === 0,
        packages,
        manager: detectedManager,
        operation: 'uninstall',
        output: result.stdout,
        error: result.exitCode !== 0 ? result.stderr : undefined,
        duration,
        timestamp: new Date()
      };

    } catch (error: any) {
      return {
        success: false,
        packages,
        manager: manager || 'unknown',
        operation: 'uninstall',
        output: '',
        error: error.message,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    }
  }

  /**
   * List installed packages
   */
  async listPackages(
    containerId: string,
    manager?: string,
    workingDir?: string
  ): Promise<PackageListResponse> {
    try {
      // Detect package manager if not specified
      const detectedManager = manager || await this.detectBestManager(containerId, workingDir);
      
      // Build list command
      const command = this.buildListCommand(detectedManager);
      
      // Execute list command
      const result = await this.dockerManager.executeCommand(containerId, {
        command,
        workingDir: workingDir || '/workspace',
        timeout: 60
      });

      if (result.exitCode !== 0) {
        throw new Error(`Failed to list packages: ${result.stderr}`);
      }

      // Parse package list
      const packages = this.parsePackageList(result.stdout, detectedManager);
      
      // Count outdated packages
      const outdatedPackages = packages.filter(pkg => pkg.updateAvailable).length;

      return {
        manager: detectedManager,
        packages,
        totalPackages: packages.length,
        outdatedPackages,
        workingDir: workingDir || '/workspace',
        timestamp: new Date()
      };

    } catch (error: any) {
      throw new Error(`Failed to list packages: ${error.message}`);
    }
  }

  /**
   * Update packages
   */
  async updatePackages(
    containerId: string,
    packages?: string[],
    manager?: string,
    workingDir?: string
  ): Promise<PackageInstallResponse> {
    const startTime = Date.now();
    
    try {
      // Detect package manager if not specified
      const detectedManager = manager || await this.detectBestManager(containerId, workingDir);
      
      // Build update command
      const command = this.buildUpdateCommand(detectedManager, packages);
      
      // Execute update
      const result = await this.dockerManager.executeCommand(containerId, {
        command,
        workingDir: workingDir || '/workspace',
        timeout: this.options.maxInstallTime
      });

      const duration = Date.now() - startTime;

      return {
        success: result.exitCode === 0,
        packages: packages || ['all'],
        manager: detectedManager,
        operation: 'update',
        output: result.stdout,
        error: result.exitCode !== 0 ? result.stderr : undefined,
        duration,
        timestamp: new Date()
      };

    } catch (error: any) {
      return {
        success: false,
        packages: packages || ['all'],
        manager: manager || 'unknown',
        operation: 'update',
        output: '',
        error: error.message,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };
    }
  }

  /**
   * Search for packages
   */
  async searchPackages(
    containerId: string,
    request: PackageSearchRequest
  ): Promise<PackageSearchResult[]> {
    try {
      // Build search command
      const command = this.buildSearchCommand(request.manager, request.query, request.limit);
      
      // Execute search
      const result = await this.dockerManager.executeCommand(containerId, {
        command,
        workingDir: '/workspace',
        timeout: 60
      });

      if (result.exitCode !== 0) {
        throw new Error(`Search failed: ${result.stderr}`);
      }

      // Parse search results
      return this.parseSearchResults(result.stdout, request.manager);

    } catch (error: any) {
      throw new Error(`Package search failed: ${error.message}`);
    }
  }

  // Private helper methods
  private validateInstallRequest(request: PackageInstallRequest): void {
    if (!request.packages || request.packages.length === 0) {
      throw new Error('No packages specified for installation');
    }

    // Check for blocked packages
    for (const pkg of request.packages) {
      if (this.options.blockedPackages.includes(pkg)) {
        throw new Error(`Package '${pkg}' is not allowed`);
      }
    }

    // Check if manager is allowed
    if (request.manager && !this.options.allowedManagers.includes(request.manager)) {
      throw new Error(`Package manager '${request.manager}' is not allowed`);
    }

    // Check global installs
    if (request.global && !this.options.enableGlobalInstalls) {
      throw new Error('Global package installations are not allowed');
    }
  }

  private async detectBestManager(containerId: string, workingDir?: string): Promise<string> {
    const dir = workingDir || '/workspace';
    
    // Check for lock files to determine the best manager
    const lockFiles = {
      'package-lock.json': 'npm',
      'yarn.lock': 'yarn',
      'pnpm-lock.yaml': 'pnpm',
      'poetry.lock': 'poetry',
      'Pipfile.lock': 'pip',
      'Cargo.lock': 'cargo',
      'go.mod': 'go',
      'pom.xml': 'maven',
      'build.gradle': 'gradle'
    };

    for (const [lockFile, manager] of Object.entries(lockFiles)) {
      try {
        const result = await this.dockerManager.executeCommand(containerId, {
          command: ['test', '-f', `${dir}/${lockFile}`],
          timeout: 5
        });
        
        if (result.exitCode === 0) {
          return manager;
        }
      } catch {
        // Continue checking
      }
    }

    // Default fallback based on available managers
    const managers = await this.detectPackageManagers(containerId);
    const availableManager = managers.find(m => m.available && this.options.allowedManagers.includes(m.name));
    
    return availableManager?.name || 'npm';
  }

  private buildInstallCommand(manager: string, request: PackageInstallRequest): string[] {
    const packages = request.packages;
    
    switch (manager) {
      case 'npm':
        return [
          'npm', 'install',
          ...(request.global ? ['-g'] : []),
          ...(request.dev ? ['--save-dev'] : []),
          ...(request.exact ? ['--save-exact'] : []),
          ...(request.optional ? ['--save-optional'] : []),
          ...packages
        ];
      
      case 'yarn':
        return [
          'yarn', 'add',
          ...(request.global ? ['global'] : []),
          ...(request.dev ? ['--dev'] : []),
          ...(request.exact ? ['--exact'] : []),
          ...packages
        ];
      
      case 'pnpm':
        return [
          'pnpm', 'add',
          ...(request.global ? ['-g'] : []),
          ...(request.dev ? ['-D'] : []),
          ...(request.exact ? ['-E'] : []),
          ...packages
        ];
      
      case 'pip':
        return [
          'pip', 'install',
          ...(request.global ? [] : ['--user']),
          ...packages
        ];
      
      case 'poetry':
        return [
          'poetry', 'add',
          ...(request.dev ? ['--group', 'dev'] : []),
          ...packages
        ];
      
      case 'apt':
        return ['apt-get', 'install', '-y', ...packages];
      
      case 'cargo':
        return ['cargo', 'install', ...packages];
      
      case 'go':
        return ['go', 'install', ...packages];
      
      default:
        throw new Error(`Unsupported package manager: ${manager}`);
    }
  }

  private buildUninstallCommand(manager: string, packages: string[]): string[] {
    switch (manager) {
      case 'npm':
        return ['npm', 'uninstall', ...packages];
      case 'yarn':
        return ['yarn', 'remove', ...packages];
      case 'pnpm':
        return ['pnpm', 'remove', ...packages];
      case 'pip':
        return ['pip', 'uninstall', '-y', ...packages];
      case 'poetry':
        return ['poetry', 'remove', ...packages];
      case 'apt':
        return ['apt-get', 'remove', '-y', ...packages];
      default:
        throw new Error(`Unsupported package manager: ${manager}`);
    }
  }

  private buildListCommand(manager: string): string[] {
    switch (manager) {
      case 'npm':
        return ['npm', 'list', '--json', '--depth=0'];
      case 'yarn':
        return ['yarn', 'list', '--json', '--depth=0'];
      case 'pnpm':
        return ['pnpm', 'list', '--json', '--depth=0'];
      case 'pip':
        return ['pip', 'list', '--format=json'];
      case 'poetry':
        return ['poetry', 'show', '--json'];
      case 'apt':
        return ['apt', 'list', '--installed'];
      default:
        throw new Error(`Unsupported package manager: ${manager}`);
    }
  }

  private buildUpdateCommand(manager: string, packages?: string[]): string[] {
    switch (manager) {
      case 'npm':
        return packages ? ['npm', 'update', ...packages] : ['npm', 'update'];
      case 'yarn':
        return packages ? ['yarn', 'upgrade', ...packages] : ['yarn', 'upgrade'];
      case 'pnpm':
        return packages ? ['pnpm', 'update', ...packages] : ['pnpm', 'update'];
      case 'pip':
        return packages ? ['pip', 'install', '--upgrade', ...packages] : ['pip', 'list', '--outdated'];
      case 'poetry':
        return packages ? ['poetry', 'update', ...packages] : ['poetry', 'update'];
      case 'apt':
        return ['apt-get', 'upgrade', '-y'];
      default:
        throw new Error(`Unsupported package manager: ${manager}`);
    }
  }

  private buildSearchCommand(manager: string, query: string, limit?: number): string[] {
    switch (manager) {
      case 'npm':
        return ['npm', 'search', query, '--json'];
      case 'yarn':
        return ['yarn', 'search', query];
      case 'pip':
        return ['pip', 'search', query];
      case 'apt':
        return ['apt', 'search', query];
      default:
        throw new Error(`Search not supported for package manager: ${manager}`);
    }
  }

  private extractVersion(output: string, manager: string): string {
    const lines = output.split('\n');
    const firstLine = lines[0].trim();
    
    // Extract version number from output
    const versionMatch = firstLine.match(/(\d+\.\d+\.\d+)/);
    return versionMatch ? versionMatch[1] : 'unknown';
  }

  private async getConfigPath(containerId: string, manager: string): Promise<string | undefined> {
    const configPaths: Record<string, string> = {
      npm: '.npmrc',
      yarn: '.yarnrc',
      pip: 'pip.conf',
      poetry: 'pyproject.toml'
    };

    const configFile = configPaths[manager];
    if (!configFile) return undefined;

    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['find', '/workspace', '-name', configFile, '-type', 'f'],
        timeout: 10
      });
      
      return result.exitCode === 0 && result.stdout.trim() ? result.stdout.trim() : undefined;
    } catch {
      return undefined;
    }
  }

  private async getLockFile(containerId: string, manager: string): Promise<string | undefined> {
    const lockFiles: Record<string, string> = {
      npm: 'package-lock.json',
      yarn: 'yarn.lock',
      pnpm: 'pnpm-lock.yaml',
      poetry: 'poetry.lock',
      cargo: 'Cargo.lock'
    };

    const lockFile = lockFiles[manager];
    if (!lockFile) return undefined;

    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['find', '/workspace', '-name', lockFile, '-type', 'f'],
        timeout: 10
      });
      
      return result.exitCode === 0 && result.stdout.trim() ? result.stdout.trim() : undefined;
    } catch {
      return undefined;
    }
  }

  private parsePackageList(output: string, manager: string): PackageInfo[] {
    try {
      switch (manager) {
        case 'npm':
        case 'yarn':
        case 'pnpm':
          return this.parseNodePackageList(output);
        case 'pip':
          return this.parsePipPackageList(output);
        case 'poetry':
          return this.parsePoetryPackageList(output);
        case 'apt':
          return this.parseAptPackageList(output);
        default:
          return [];
      }
    } catch {
      return [];
    }
  }

  private parseNodePackageList(output: string): PackageInfo[] {
    try {
      const data = JSON.parse(output);
      const packages: PackageInfo[] = [];
      
      if (data.dependencies) {
        for (const [name, info] of Object.entries(data.dependencies as any)) {
          packages.push({
            name,
            version: info.version || 'unknown',
            description: info.description,
            homepage: info.homepage,
            repository: info.repository?.url,
            license: info.license
          });
        }
      }
      
      return packages;
    } catch {
      return [];
    }
  }

  private parsePipPackageList(output: string): PackageInfo[] {
    try {
      const data = JSON.parse(output);
      return data.map((pkg: any) => ({
        name: pkg.name,
        version: pkg.version,
        description: pkg.summary
      }));
    } catch {
      return [];
    }
  }

  private parsePoetryPackageList(output: string): PackageInfo[] {
    try {
      const data = JSON.parse(output);
      return data.map((pkg: any) => ({
        name: pkg.name,
        version: pkg.version,
        description: pkg.description
      }));
    } catch {
      return [];
    }
  }

  private parseAptPackageList(output: string): PackageInfo[] {
    const packages: PackageInfo[] = [];
    const lines = output.split('\n');
    
    for (const line of lines) {
      if (line.includes('[installed]')) {
        const parts = line.split('/');
        if (parts.length > 0) {
          const name = parts[0].trim();
          packages.push({
            name,
            version: 'installed'
          });
        }
      }
    }
    
    return packages;
  }

  private parseSearchResults(output: string, manager: string): PackageSearchResult[] {
    try {
      switch (manager) {
        case 'npm':
          return this.parseNpmSearchResults(output);
        default:
          return [];
      }
    } catch {
      return [];
    }
  }

  private parseNpmSearchResults(output: string): PackageSearchResult[] {
    try {
      const data = JSON.parse(output);
      return data.map((pkg: any) => ({
        name: pkg.name,
        version: pkg.version,
        description: pkg.description,
        homepage: pkg.links?.homepage,
        repository: pkg.links?.repository,
        keywords: pkg.keywords
      }));
    } catch {
      return [];
    }
  }
}
