/**
 * VibeKraft Filesystem Utilities
 * 
 * Utilities for filesystem operations within sandbox containers
 */

import path from 'path';
import { 
  FileSystemItem, 
  DirectoryListing, 
  FileContent, 
  FileUploadRequest,
  FileSearchRequest,
  FileSearchResult,
  FileSystemResponse
} from '../types/sandbox';

export interface FileSystemManagerOptions {
  maxFileSize?: number;
  allowedExtensions?: string[];
  blockedPaths?: string[];
  enableSymlinks?: boolean;
  enableHiddenFiles?: boolean;
}

export class FileSystemManager {
  private options: Required<FileSystemManagerOptions>;
  private dockerManager: any; // Will be injected

  constructor(dockerManager: any, options: FileSystemManagerOptions = {}) {
    this.dockerManager = dockerManager;
    this.options = {
      maxFileSize: options.maxFileSize || 100 * 1024 * 1024, // 100MB
      allowedExtensions: options.allowedExtensions || [],
      blockedPaths: options.blockedPaths || [
        '/etc/passwd',
        '/etc/shadow',
        '/etc/sudoers',
        '/root',
        '/proc',
        '/sys',
        '/dev'
      ],
      enableSymlinks: options.enableSymlinks ?? false,
      enableHiddenFiles: options.enableHiddenFiles ?? true
    };
  }

  /**
   * List directory contents
   */
  async listDirectory(containerId: string, dirPath: string): Promise<DirectoryListing> {
    try {
      // Validate and sanitize path
      const sanitizedPath = this.sanitizePath(dirPath);
      this.validatePath(sanitizedPath);

      // Execute ls command with detailed output
      const command = [
        'ls', '-la', '--time-style=iso', '--group-directories-first', sanitizedPath
      ];

      const result = await this.dockerManager.executeCommand(containerId, {
        command,
        workingDir: '/workspace',
        timeout: 30
      });

      if (result.exitCode !== 0) {
        throw new Error(`Failed to list directory: ${result.stderr}`);
      }

      // Parse ls output
      const items = this.parseLsOutput(result.stdout, sanitizedPath);
      
      // Get directory permissions
      const permissions = await this.getDirectoryPermissions(containerId, sanitizedPath);

      return {
        path: sanitizedPath,
        items,
        totalItems: items.length,
        totalSize: items.reduce((sum, item) => sum + item.size, 0),
        permissions
      };

    } catch (error: any) {
      throw new Error(`Directory listing failed: ${error.message}`);
    }
  }

  /**
   * Read file content
   */
  async readFile(containerId: string, filePath: string): Promise<FileContent> {
    try {
      const sanitizedPath = this.sanitizePath(filePath);
      this.validatePath(sanitizedPath);

      // Check if file exists and get info
      const fileInfo = await this.getFileInfo(containerId, sanitizedPath);
      if (!fileInfo || fileInfo.type !== 'file') {
        throw new Error('File not found or is not a regular file');
      }

      // Check file size
      if (fileInfo.size > this.options.maxFileSize) {
        throw new Error(`File too large: ${fileInfo.size} bytes (max: ${this.options.maxFileSize})`);
      }

      // Determine if file is binary
      const mimeType = this.getMimeType(sanitizedPath);
      const isBinary = this.isBinaryFile(mimeType);

      let content: string | Buffer;
      let encoding: 'utf8' | 'base64' | 'binary';

      if (isBinary) {
        // Read binary file and encode as base64
        const result = await this.dockerManager.executeCommand(containerId, {
          command: ['base64', '-w', '0', sanitizedPath],
          timeout: 60
        });
        
        if (result.exitCode !== 0) {
          throw new Error(`Failed to read binary file: ${result.stderr}`);
        }
        
        content = result.stdout;
        encoding = 'base64';
      } else {
        // Read text file
        const result = await this.dockerManager.executeCommand(containerId, {
          command: ['cat', sanitizedPath],
          timeout: 60
        });
        
        if (result.exitCode !== 0) {
          throw new Error(`Failed to read file: ${result.stderr}`);
        }
        
        content = result.stdout;
        encoding = 'utf8';
      }

      return {
        path: sanitizedPath,
        content,
        encoding,
        size: fileInfo.size,
        mimeType,
        lastModified: fileInfo.modified
      };

    } catch (error: any) {
      throw new Error(`File read failed: ${error.message}`);
    }
  }

  /**
   * Write file content
   */
  async writeFile(containerId: string, request: FileUploadRequest): Promise<FileSystemResponse> {
    try {
      const sanitizedPath = this.sanitizePath(request.path);
      this.validatePath(sanitizedPath);

      // Check file size
      const contentSize = Buffer.isBuffer(request.content) 
        ? request.content.length 
        : Buffer.byteLength(request.content, request.encoding || 'utf8');

      if (contentSize > this.options.maxFileSize) {
        throw new Error(`Content too large: ${contentSize} bytes (max: ${this.options.maxFileSize})`);
      }

      // Check if file exists and overwrite is allowed
      if (!request.overwrite) {
        const exists = await this.fileExists(containerId, sanitizedPath);
        if (exists) {
          throw new Error('File already exists and overwrite is not allowed');
        }
      }

      // Create directories if needed
      if (request.createDirectories) {
        const dirPath = path.dirname(sanitizedPath);
        await this.createDirectory(containerId, dirPath);
      }

      // Write content to temporary file first
      const tempPath = `/tmp/vibekraft-upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      let writeCommand: string[];
      if (request.encoding === 'base64') {
        // Decode base64 content
        writeCommand = ['bash', '-c', `echo '${request.content}' | base64 -d > '${tempPath}'`];
      } else {
        // Write text content
        const escapedContent = typeof request.content === 'string' 
          ? request.content.replace(/'/g, "'\"'\"'")
          : request.content.toString();
        writeCommand = ['bash', '-c', `cat > '${tempPath}' << 'EOF'\n${escapedContent}\nEOF`];
      }

      const writeResult = await this.dockerManager.executeCommand(containerId, {
        command: writeCommand,
        timeout: 120
      });

      if (writeResult.exitCode !== 0) {
        throw new Error(`Failed to write temporary file: ${writeResult.stderr}`);
      }

      // Move temporary file to final location
      const moveResult = await this.dockerManager.executeCommand(containerId, {
        command: ['mv', tempPath, sanitizedPath],
        timeout: 30
      });

      if (moveResult.exitCode !== 0) {
        // Clean up temp file
        await this.dockerManager.executeCommand(containerId, {
          command: ['rm', '-f', tempPath],
          timeout: 10
        });
        throw new Error(`Failed to move file to final location: ${moveResult.stderr}`);
      }

      // Set permissions if specified
      if (request.permissions) {
        await this.dockerManager.executeCommand(containerId, {
          command: ['chmod', request.permissions, sanitizedPath],
          timeout: 10
        });
      }

      return {
        success: true,
        path: sanitizedPath,
        operation: 'write',
        timestamp: new Date(),
        data: {
          size: contentSize,
          encoding: request.encoding || 'utf8'
        }
      };

    } catch (error: any) {
      return {
        success: false,
        path: request.path,
        operation: 'write',
        timestamp: new Date(),
        error: error.message
      };
    }
  }

  /**
   * Create directory
   */
  async createDirectory(containerId: string, dirPath: string): Promise<FileSystemResponse> {
    try {
      const sanitizedPath = this.sanitizePath(dirPath);
      this.validatePath(sanitizedPath);

      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['mkdir', '-p', sanitizedPath],
        timeout: 30
      });

      if (result.exitCode !== 0) {
        throw new Error(`Failed to create directory: ${result.stderr}`);
      }

      return {
        success: true,
        path: sanitizedPath,
        operation: 'mkdir',
        timestamp: new Date()
      };

    } catch (error: any) {
      return {
        success: false,
        path: dirPath,
        operation: 'mkdir',
        timestamp: new Date(),
        error: error.message
      };
    }
  }

  /**
   * Delete file or directory
   */
  async delete(containerId: string, targetPath: string, recursive: boolean = false): Promise<FileSystemResponse> {
    try {
      const sanitizedPath = this.sanitizePath(targetPath);
      this.validatePath(sanitizedPath);

      // Check if target exists
      const exists = await this.fileExists(containerId, sanitizedPath);
      if (!exists) {
        throw new Error('File or directory not found');
      }

      const command = recursive ? ['rm', '-rf', sanitizedPath] : ['rm', sanitizedPath];
      
      const result = await this.dockerManager.executeCommand(containerId, {
        command,
        timeout: 60
      });

      if (result.exitCode !== 0) {
        throw new Error(`Failed to delete: ${result.stderr}`);
      }

      return {
        success: true,
        path: sanitizedPath,
        operation: 'delete',
        timestamp: new Date(),
        data: { recursive }
      };

    } catch (error: any) {
      return {
        success: false,
        path: targetPath,
        operation: 'delete',
        timestamp: new Date(),
        error: error.message
      };
    }
  }

  /**
   * Move/rename file or directory
   */
  async move(containerId: string, sourcePath: string, destinationPath: string): Promise<FileSystemResponse> {
    try {
      const sanitizedSource = this.sanitizePath(sourcePath);
      const sanitizedDestination = this.sanitizePath(destinationPath);
      
      this.validatePath(sanitizedSource);
      this.validatePath(sanitizedDestination);

      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['mv', sanitizedSource, sanitizedDestination],
        timeout: 60
      });

      if (result.exitCode !== 0) {
        throw new Error(`Failed to move: ${result.stderr}`);
      }

      return {
        success: true,
        path: sanitizedSource,
        operation: 'move',
        timestamp: new Date(),
        data: { destination: sanitizedDestination }
      };

    } catch (error: any) {
      return {
        success: false,
        path: sourcePath,
        operation: 'move',
        timestamp: new Date(),
        error: error.message
      };
    }
  }

  /**
   * Copy file or directory
   */
  async copy(containerId: string, sourcePath: string, destinationPath: string): Promise<FileSystemResponse> {
    try {
      const sanitizedSource = this.sanitizePath(sourcePath);
      const sanitizedDestination = this.sanitizePath(destinationPath);
      
      this.validatePath(sanitizedSource);
      this.validatePath(sanitizedDestination);

      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['cp', '-r', sanitizedSource, sanitizedDestination],
        timeout: 120
      });

      if (result.exitCode !== 0) {
        throw new Error(`Failed to copy: ${result.stderr}`);
      }

      return {
        success: true,
        path: sanitizedSource,
        operation: 'copy',
        timestamp: new Date(),
        data: { destination: sanitizedDestination }
      };

    } catch (error: any) {
      return {
        success: false,
        path: sourcePath,
        operation: 'copy',
        timestamp: new Date(),
        error: error.message
      };
    }
  }

  // Private helper methods
  private sanitizePath(filePath: string): string {
    // Remove any dangerous characters and normalize path
    const normalized = path.posix.normalize(filePath);
    
    // Ensure path starts with /workspace or is relative
    if (normalized.startsWith('/')) {
      if (!normalized.startsWith('/workspace')) {
        return path.posix.join('/workspace', normalized.substring(1));
      }
      return normalized;
    }
    
    return path.posix.join('/workspace', normalized);
  }

  private validatePath(filePath: string): void {
    // Check for blocked paths
    for (const blockedPath of this.options.blockedPaths) {
      if (filePath.startsWith(blockedPath)) {
        throw new Error(`Access denied to path: ${filePath}`);
      }
    }

    // Check for path traversal attempts
    if (filePath.includes('..')) {
      throw new Error('Path traversal not allowed');
    }

    // Check for null bytes
    if (filePath.includes('\0')) {
      throw new Error('Null bytes not allowed in path');
    }
  }

  private async fileExists(containerId: string, filePath: string): Promise<boolean> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['test', '-e', filePath],
        timeout: 10
      });
      return result.exitCode === 0;
    } catch {
      return false;
    }
  }

  private async getFileInfo(containerId: string, filePath: string): Promise<FileSystemItem | null> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['stat', '-c', '%n|%s|%Y|%A|%U|%G|%F', filePath],
        timeout: 10
      });

      if (result.exitCode !== 0) {
        return null;
      }

      const [name, size, mtime, permissions, owner, group, fileType] = result.stdout.trim().split('|');
      
      return {
        name: path.basename(name),
        path: filePath,
        type: fileType.includes('directory') ? 'directory' : 
              fileType.includes('symbolic link') ? 'symlink' : 'file',
        size: parseInt(size),
        permissions,
        owner,
        group,
        modified: new Date(parseInt(mtime) * 1000),
        isHidden: path.basename(name).startsWith('.'),
        mimeType: this.getMimeType(filePath),
        extension: path.extname(name).substring(1)
      };
    } catch {
      return null;
    }
  }

  private parseLsOutput(output: string, dirPath: string): FileSystemItem[] {
    const lines = output.split('\n').filter(line => line.trim());
    const items: FileSystemItem[] = [];

    for (const line of lines) {
      if (line.startsWith('total ') || line.trim() === '') continue;

      const parts = line.split(/\s+/);
      if (parts.length < 9) continue;

      const permissions = parts[0];
      const owner = parts[2];
      const group = parts[3];
      const size = parseInt(parts[4]);
      const date = parts[5];
      const time = parts[6];
      const name = parts.slice(8).join(' ');

      if (name === '.' || name === '..') continue;

      const isDirectory = permissions.startsWith('d');
      const isSymlink = permissions.startsWith('l');
      const isHidden = name.startsWith('.');

      if (!this.options.enableHiddenFiles && isHidden) continue;
      if (!this.options.enableSymlinks && isSymlink) continue;

      const fullPath = path.posix.join(dirPath, name);
      const modified = new Date(`${date} ${time}`);

      items.push({
        name,
        path: fullPath,
        type: isDirectory ? 'directory' : isSymlink ? 'symlink' : 'file',
        size,
        permissions,
        owner,
        group,
        modified,
        isHidden,
        mimeType: isDirectory ? 'inode/directory' : this.getMimeType(name),
        extension: isDirectory ? undefined : path.extname(name).substring(1)
      });
    }

    return items;
  }

  private async getDirectoryPermissions(containerId: string, dirPath: string): Promise<any> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['test', '-r', dirPath, '&&', 'echo', 'r', '||', 'echo', 'nr'],
        timeout: 10
      });

      return {
        readable: result.stdout.includes('r'),
        writable: true, // Simplified for now
        executable: true // Simplified for now
      };
    } catch {
      return { readable: false, writable: false, executable: false };
    }
  }

  private getMimeType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.js': 'application/javascript',
      '.ts': 'application/typescript',
      '.json': 'application/json',
      '.html': 'text/html',
      '.css': 'text/css',
      '.md': 'text/markdown',
      '.py': 'text/x-python',
      '.java': 'text/x-java-source',
      '.cpp': 'text/x-c++src',
      '.c': 'text/x-csrc',
      '.h': 'text/x-chdr',
      '.xml': 'application/xml',
      '.yaml': 'application/x-yaml',
      '.yml': 'application/x-yaml',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.pdf': 'application/pdf',
      '.zip': 'application/zip',
      '.tar': 'application/x-tar',
      '.gz': 'application/gzip'
    };

    return mimeTypes[ext] || 'application/octet-stream';
  }

  private isBinaryFile(mimeType: string): boolean {
    return !mimeType.startsWith('text/') && 
           !mimeType.includes('json') && 
           !mimeType.includes('xml') && 
           !mimeType.includes('javascript') && 
           !mimeType.includes('typescript');
  }
}
