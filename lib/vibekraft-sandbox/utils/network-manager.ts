/**
 * VibeKraft Network Manager Utilities
 * 
 * Utilities for network configuration and management within sandbox containers
 */

import { 
  NetworkInterface, 
  NetworkPort, 
  NetworkRule,
  // Remove unused import
  NetworkStatusDetailed,
  NetworkConnection,
  NetworkConfigRequest,
  NetworkResponse
} from '../types/sandbox';

export interface NetworkManagerOptions {
  enableFirewall?: boolean;
  enableBandwidthLimiting?: boolean;
  enableProxySupport?: boolean;
  defaultDnsServers?: string[];
  allowedPortRange?: { min: number; max: number };
  maxPortMappings?: number;
  maxFirewallRules?: number;
}

export class NetworkManager {
  private options: Required<NetworkManagerOptions>;
  private dockerManager: any; // Will be injected

  constructor(dockerManager: any, options: NetworkManagerOptions = {}) {
    this.dockerManager = dockerManager;
    this.options = {
      enableFirewall: options.enableFirewall ?? true,
      enableBandwidthLimiting: options.enableBandwidthLimiting ?? false,
      enableProxySupport: options.enableProxySupport ?? true,
      defaultDnsServers: options.defaultDnsServers || ['*******', '8.8.4.4', '1.1.1.1'],
      allowedPortRange: options.allowedPortRange || { min: 3000, max: 9999 },
      maxPortMappings: options.maxPortMappings || 20,
      maxFirewallRules: options.maxFirewallRules || 50
    };
  }

  /**
   * Get current network status
   */
  async getNetworkStatus(containerId: string): Promise<NetworkStatusDetailed> {
    try {
      // Get network interfaces
      const interfaces = await this.getNetworkInterfaces(containerId);
      
      // Get port mappings
      const ports = await this.getPortMappings(containerId);
      
      // Get network connections
      const connections = await this.getNetworkConnections(containerId);
      
      // Test DNS resolution
      const dnsResolution = await this.testDnsResolution(containerId);
      
      // Test internet connectivity
      const internetConnectivity = await this.testInternetConnectivity(containerId);
      
      // Check firewall status
      const firewallEnabled = await this.isFirewallEnabled(containerId);
      
      // Check proxy status
      const proxyEnabled = await this.isProxyEnabled(containerId);
      
      // Get bandwidth usage
      const bandwidth = await this.getBandwidthUsage(containerId);

      return {
        interfaces,
        ports,
        connections,
        dnsResolution,
        internetConnectivity,
        firewallEnabled,
        proxyEnabled,
        bandwidth
      };

    } catch (error: any) {
      throw new Error(`Failed to get network status: ${error.message}`);
    }
  }

  /**
   * Configure network settings
   */
  async configureNetwork(
    containerId: string, 
    config: NetworkConfigRequest
  ): Promise<NetworkResponse> {
    try {
      const operations: string[] = [];

      // Configure internet access
      if (config.internetAccess !== undefined) {
        await this.configureInternetAccess(containerId, config.internetAccess);
        operations.push(`internet access: ${config.internetAccess ? 'enabled' : 'disabled'}`);
      }

      // Configure inbound access
      if (config.inboundAccess !== undefined) {
        await this.configureInboundAccess(containerId, config.inboundAccess);
        operations.push(`inbound access: ${config.inboundAccess ? 'enabled' : 'disabled'}`);
      }

      // Configure DNS servers
      if (config.dnsServers) {
        await this.configureDnsServers(containerId, config.dnsServers);
        operations.push(`DNS servers: ${config.dnsServers.join(', ')}`);
      }

      // Configure host filtering
      if (config.allowedHosts || config.blockedHosts) {
        await this.configureHostFiltering(containerId, config.allowedHosts, config.blockedHosts);
        operations.push('host filtering updated');
      }

      // Configure port mappings
      if (config.portMappings) {
        await this.configurePortMappings(containerId, config.portMappings);
        operations.push(`${config.portMappings.length} port mappings configured`);
      }

      // Configure firewall rules
      if (config.firewallRules) {
        await this.configureFirewallRules(containerId, config.firewallRules);
        operations.push(`${config.firewallRules.length} firewall rules configured`);
      }

      // Configure bandwidth limits
      if (config.bandwidth) {
        await this.configureBandwidthLimits(containerId, config.bandwidth);
        operations.push('bandwidth limits configured');
      }

      // Get updated status
      const status = await this.getNetworkStatus(containerId);

      return {
        success: true,
        operation: `network configuration: ${operations.join(', ')}`,
        status,
        timestamp: new Date()
      };

    } catch (error: any) {
      return {
        success: false,
        operation: 'network configuration',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Add port mapping
   */
  async addPortMapping(
    containerId: string, 
    port: Omit<NetworkPort, 'state' | 'processId' | 'processName'>
  ): Promise<NetworkResponse> {
    try {
      // Validate port range
      if (port.containerPort < this.options.allowedPortRange.min || 
          port.containerPort > this.options.allowedPortRange.max) {
        throw new Error(`Port ${port.containerPort} is outside allowed range ${this.options.allowedPortRange.min}-${this.options.allowedPortRange.max}`);
      }

      // Check if port is already mapped
      const existingPorts = await this.getPortMappings(containerId);
      if (existingPorts.some(p => p.containerPort === port.containerPort)) {
        throw new Error(`Port ${port.containerPort} is already mapped`);
      }

      // Check port mapping limit
      if (existingPorts.length >= this.options.maxPortMappings) {
        throw new Error(`Maximum port mappings (${this.options.maxPortMappings}) exceeded`);
      }

      // Add port mapping using iptables
      const hostPort = port.hostPort || await this.findAvailableHostPort();
      await this.addIptablesRule(containerId, port.containerPort, hostPort, port.protocol);

      return {
        success: true,
        operation: `add port mapping: ${port.containerPort}:${hostPort}/${port.protocol}`,
        timestamp: new Date()
      };

    } catch (error: any) {
      return {
        success: false,
        operation: 'add port mapping',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Remove port mapping
   */
  async removePortMapping(
    containerId: string, 
    containerPort: number, 
    protocol: 'tcp' | 'udp' = 'tcp'
  ): Promise<NetworkResponse> {
    try {
      // Remove iptables rule
      await this.removeIptablesRule(containerId, containerPort, protocol);

      return {
        success: true,
        operation: `remove port mapping: ${containerPort}/${protocol}`,
        timestamp: new Date()
      };

    } catch (error: any) {
      return {
        success: false,
        operation: 'remove port mapping',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Add firewall rule
   */
  async addFirewallRule(
    containerId: string, 
    rule: Omit<NetworkRule, 'id'>
  ): Promise<NetworkResponse> {
    try {
      // Check firewall rule limit
      const existingRules = await this.getFirewallRules(containerId);
      if (existingRules.length >= this.options.maxFirewallRules) {
        throw new Error(`Maximum firewall rules (${this.options.maxFirewallRules}) exceeded`);
      }

      // Generate rule ID
      const ruleId = `rule-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      
      // Add iptables rule
      await this.addFirewallIptablesRule(containerId, { ...rule, id: ruleId });

      return {
        success: true,
        operation: `add firewall rule: ${rule.type} ${rule.direction} ${rule.protocol}`,
        timestamp: new Date()
      };

    } catch (error: any) {
      return {
        success: false,
        operation: 'add firewall rule',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Test network connectivity
   */
  async testConnectivity(
    containerId: string, 
    target: string, 
    port?: number
  ): Promise<{ success: boolean; latency?: number; error?: string }> {
    try {
      const command = port 
        ? ['nc', '-z', '-v', '-w', '5', target, port.toString()]
        : ['ping', '-c', '1', '-W', '5', target];

      const startTime = Date.now();
      const result = await this.dockerManager.executeCommand(containerId, {
        command,
        timeout: 10
      });
      const latency = Date.now() - startTime;

      return {
        success: result.exitCode === 0,
        latency: result.exitCode === 0 ? latency : undefined,
        error: result.exitCode !== 0 ? result.stderr : undefined
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Private helper methods
  private async getNetworkInterfaces(containerId: string): Promise<NetworkInterface[]> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['ip', '-j', 'addr', 'show'],
        timeout: 10
      });

      if (result.exitCode !== 0) {
        throw new Error(`Failed to get network interfaces: ${result.stderr}`);
      }

      const interfaces = JSON.parse(result.stdout);
      return interfaces.map((iface: any) => ({
        name: iface.ifname,
        type: this.getInterfaceType(iface.ifname),
        state: iface.operstate === 'UP' ? 'up' : 'down',
        ipAddress: this.extractIpAddress(iface.addr_info),
        netmask: this.extractNetmask(iface.addr_info),
        gateway: '', // Would need additional command to get gateway
        macAddress: iface.address || '',
        mtu: iface.mtu || 1500,
        rxBytes: iface.stats64?.rx_bytes || 0,
        txBytes: iface.stats64?.tx_bytes || 0,
        rxPackets: iface.stats64?.rx_packets || 0,
        txPackets: iface.stats64?.tx_packets || 0
      }));

    } catch (error) {
      return [];
    }
  }

  private async getPortMappings(containerId: string): Promise<NetworkPort[]> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['netstat', '-tlnp'],
        timeout: 10
      });

      if (result.exitCode !== 0) {
        return [];
      }

      const ports: NetworkPort[] = [];
      const lines = result.stdout.split('\n');

      for (const line of lines) {
        if (line.includes('LISTEN')) {
          const parts = line.split(/\s+/);
          const address = parts[3];
          const processInfo = parts[6];

          if (address && address.includes(':')) {
            const port = parseInt(address.split(':').pop() || '0');
            if (port > 0) {
              ports.push({
                containerPort: port,
                protocol: 'tcp',
                state: 'listening',
                processId: this.extractProcessId(processInfo),
                processName: this.extractProcessName(processInfo),
                public: false,
                description: `Listening on port ${port}`
              });
            }
          }
        }
      }

      return ports;

    } catch (error) {
      return [];
    }
  }

  private async getNetworkConnections(containerId: string): Promise<NetworkConnection[]> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['netstat', '-tnp'],
        timeout: 10
      });

      if (result.exitCode !== 0) {
        return [];
      }

      const connections: NetworkConnection[] = [];
      const lines = result.stdout.split('\n');

      for (const line of lines) {
        if (line.includes(':') && !line.includes('LISTEN')) {
          const parts = line.split(/\s+/);
          if (parts.length >= 6) {
            const localAddr = parts[3];
            const remoteAddr = parts[4];
            const state = parts[5];
            const processInfo = parts[6];

            const [localIp, localPort] = localAddr.split(':');
            const [remoteIp, remotePort] = remoteAddr.split(':');

            connections.push({
              localAddress: localIp,
              localPort: parseInt(localPort),
              remoteAddress: remoteIp,
              remotePort: parseInt(remotePort),
              state: this.normalizeConnectionState(state),
              protocol: 'tcp',
              processId: this.extractProcessId(processInfo),
              processName: this.extractProcessName(processInfo)
            });
          }
        }
      }

      return connections;

    } catch (error) {
      return [];
    }
  }

  private async testDnsResolution(containerId: string): Promise<boolean> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['nslookup', 'google.com'],
        timeout: 10
      });
      return result.exitCode === 0;
    } catch {
      return false;
    }
  }

  private async testInternetConnectivity(containerId: string): Promise<boolean> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['ping', '-c', '1', '-W', '5', '*******'],
        timeout: 10
      });
      return result.exitCode === 0;
    } catch {
      return false;
    }
  }

  private async isFirewallEnabled(containerId: string): Promise<boolean> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['iptables', '-L'],
        timeout: 5
      });
      return result.exitCode === 0 && result.stdout.includes('Chain');
    } catch {
      return false;
    }
  }

  private async isProxyEnabled(containerId: string): Promise<boolean> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['env'],
        timeout: 5
      });
      return result.stdout.includes('http_proxy') || result.stdout.includes('HTTP_PROXY');
    } catch {
      return false;
    }
  }

  private async getBandwidthUsage(containerId: string): Promise<{ upload: number; download: number }> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['cat', '/proc/net/dev'],
        timeout: 5
      });

      if (result.exitCode === 0) {
        // Parse network interface statistics
        // This is a simplified implementation
        return { upload: 0, download: 0 };
      }
    } catch {
      // Fallback
    }
    
    return { upload: 0, download: 0 };
  }

  private async configureInternetAccess(containerId: string, enabled: boolean): Promise<void> {
    // Implementation would configure iptables rules for internet access
    const action = enabled ? 'ACCEPT' : 'DROP';
    await this.dockerManager.executeCommand(containerId, {
      command: ['iptables', '-A', 'OUTPUT', '-j', action],
      timeout: 10
    });
  }

  private async configureInboundAccess(containerId: string, enabled: boolean): Promise<void> {
    // Implementation would configure iptables rules for inbound access
    const action = enabled ? 'ACCEPT' : 'DROP';
    await this.dockerManager.executeCommand(containerId, {
      command: ['iptables', '-A', 'INPUT', '-j', action],
      timeout: 10
    });
  }

  private async configureDnsServers(containerId: string, dnsServers: string[]): Promise<void> {
    const resolvConf = dnsServers.map(dns => `nameserver ${dns}`).join('\n');
    await this.dockerManager.executeCommand(containerId, {
      command: ['bash', '-c', `echo '${resolvConf}' > /etc/resolv.conf`],
      timeout: 10
    });
  }

  private async configureHostFiltering(
    containerId: string,
    _allowedHosts?: string[],
    blockedHosts?: string[]
  ): Promise<void> {
    // Implementation would configure /etc/hosts or iptables rules
    if (blockedHosts) {
      for (const host of blockedHosts) {
        await this.dockerManager.executeCommand(containerId, {
          command: ['iptables', '-A', 'OUTPUT', '-d', host, '-j', 'DROP'],
          timeout: 5
        });
      }
    }
  }

  private async configurePortMappings(
    containerId: string, 
    portMappings: Omit<NetworkPort, 'state' | 'processId' | 'processName'>[]
  ): Promise<void> {
    for (const port of portMappings) {
      await this.addPortMapping(containerId, port);
    }
  }

  private async configureFirewallRules(
    containerId: string, 
    rules: Omit<NetworkRule, 'id'>[]
  ): Promise<void> {
    for (const rule of rules) {
      await this.addFirewallRule(containerId, rule);
    }
  }

  private async configureBandwidthLimits(
    containerId: string, 
    bandwidth: { uploadLimit?: number; downloadLimit?: number }
  ): Promise<void> {
    // Implementation would use tc (traffic control) commands
    if (bandwidth.uploadLimit) {
      await this.dockerManager.executeCommand(containerId, {
        command: ['tc', 'qdisc', 'add', 'dev', 'eth0', 'root', 'tbf', 'rate', `${bandwidth.uploadLimit}kbit`],
        timeout: 10
      });
    }
  }

  private async addIptablesRule(
    containerId: string, 
    containerPort: number, 
    hostPort: number, 
    protocol: 'tcp' | 'udp'
  ): Promise<void> {
    await this.dockerManager.executeCommand(containerId, {
      command: [
        'iptables', '-t', 'nat', '-A', 'PREROUTING',
        '-p', protocol, '--dport', hostPort.toString(),
        '-j', 'REDIRECT', '--to-port', containerPort.toString()
      ],
      timeout: 10
    });
  }

  private async removeIptablesRule(
    containerId: string, 
    containerPort: number, 
    protocol: 'tcp' | 'udp'
  ): Promise<void> {
    await this.dockerManager.executeCommand(containerId, {
      command: [
        'iptables', '-t', 'nat', '-D', 'PREROUTING',
        '-p', protocol, '--dport', containerPort.toString()
      ],
      timeout: 10
    });
  }

  private async addFirewallIptablesRule(containerId: string, rule: NetworkRule): Promise<void> {
    const chain = rule.direction === 'inbound' ? 'INPUT' : 'OUTPUT';
    const action = rule.type === 'allow' ? 'ACCEPT' : 'DROP';
    
    const command = ['iptables', '-A', chain];
    
    if (rule.protocol !== 'all') {
      command.push('-p', rule.protocol);
    }
    
    if (rule.sourceIp) {
      command.push('-s', rule.sourceIp);
    }
    
    if (rule.destinationIp) {
      command.push('-d', rule.destinationIp);
    }
    
    if (rule.destinationPort) {
      command.push('--dport', rule.destinationPort.toString());
    }
    
    command.push('-j', action);
    
    await this.dockerManager.executeCommand(containerId, {
      command,
      timeout: 10
    });
  }

  private async getFirewallRules(_containerId: string): Promise<NetworkRule[]> {
    // Implementation would parse iptables output
    return [];
  }

  private async findAvailableHostPort(): Promise<number> {
    // Implementation would find an available port in the allowed range
    return Math.floor(Math.random() * (this.options.allowedPortRange.max - this.options.allowedPortRange.min)) + this.options.allowedPortRange.min;
  }

  private getInterfaceType(name: string): 'ethernet' | 'bridge' | 'loopback' | 'tunnel' {
    if (name === 'lo') return 'loopback';
    if (name.startsWith('br')) return 'bridge';
    if (name.startsWith('tun') || name.startsWith('tap')) return 'tunnel';
    return 'ethernet';
  }

  private extractIpAddress(addrInfo: any[]): string {
    const ipv4 = addrInfo?.find(addr => addr.family === 'inet');
    return ipv4?.local || '';
  }

  private extractNetmask(addrInfo: any[]): string {
    const ipv4 = addrInfo?.find(addr => addr.family === 'inet');
    return ipv4?.prefixlen ? `/${ipv4.prefixlen}` : '';
  }

  private extractProcessId(processInfo: string): number | undefined {
    const match = processInfo?.match(/(\d+)\//);
    return match ? parseInt(match[1]) : undefined;
  }

  private extractProcessName(processInfo: string): string | undefined {
    const match = processInfo?.match(/\/(.+)$/);
    return match ? match[1] : undefined;
  }

  private normalizeConnectionState(state: string): 'established' | 'listening' | 'time_wait' | 'close_wait' {
    switch (state.toLowerCase()) {
      case 'established': return 'established';
      case 'listen': return 'listening';
      case 'time_wait': return 'time_wait';
      case 'close_wait': return 'close_wait';
      default: return 'established';
    }
  }
}
