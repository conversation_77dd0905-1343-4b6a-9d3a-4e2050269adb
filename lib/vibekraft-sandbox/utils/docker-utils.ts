/**
 * VibeKraft Docker Utilities
 * 
 * Docker operations and container management utilities
 */

import { EventEmitter } from 'events';
import { SandboxConfig, ResourceUsage, NetworkStatus, ProcessInfo } from '../types/sandbox';

export interface DockerContainerInfo {
  id: string;
  name: string;
  image: string;
  state: string;
  status: string;
  created: Date;
  ports: DockerPortMapping[];
  networks: DockerNetworkInfo[];
  mounts: DockerMountInfo[];
  labels: Record<string, string>;
}

export interface DockerPortMapping {
  containerPort: number;
  hostPort: number;
  protocol: 'tcp' | 'udp';
  hostIp?: string;
}

export interface DockerNetworkInfo {
  name: string;
  id: string;
  ipAddress: string;
  gateway: string;
  macAddress: string;
}

export interface DockerMountInfo {
  source: string;
  destination: string;
  type: 'bind' | 'volume' | 'tmpfs';
  readOnly: boolean;
}

export interface DockerStats {
  containerId: string;
  cpu: {
    usage: number;
    systemUsage: number;
    cores: number;
    throttled: boolean;
  };
  memory: {
    usage: number;
    limit: number;
    cache: number;
    swap: number;
  };
  network: {
    bytesReceived: number;
    bytesSent: number;
    packetsReceived: number;
    packetsSent: number;
  };
  disk: {
    bytesRead: number;
    bytesWritten: number;
    ioRead: number;
    ioWritten: number;
  };
  timestamp: Date;
}

export interface DockerExecOptions {
  command: string[];
  workingDir?: string;
  environment?: Record<string, string>;
  user?: string;
  privileged?: boolean;
  interactive?: boolean;
  tty?: boolean;
  detach?: boolean;
  timeout?: number;
}

export interface DockerExecResult {
  exitCode: number;
  stdout: string;
  stderr: string;
  duration: number;
  timedOut: boolean;
}

export interface DockerBuildOptions {
  dockerfile?: string;
  context: string;
  tags: string[];
  buildArgs?: Record<string, string>;
  labels?: Record<string, string>;
  target?: string;
  noCache?: boolean;
  pull?: boolean;
  squash?: boolean;
}

export class DockerManager extends EventEmitter {
  private dockerHost: string;
  private containers = new Map<string, DockerContainerInfo>();
  private statsStreams = new Map<string, any>();
  private initialized = false;

  constructor(dockerHost: string = '/var/run/docker.sock') {
    super();
    this.dockerHost = dockerHost;
  }

  /**
   * Initialize Docker manager
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Test Docker connection
      await this.ping();
      
      // Load existing containers
      await this.refreshContainers();
      
      this.initialized = true;
      this.emit('initialized');
    } catch (error) {
      throw new Error(`Failed to initialize Docker manager: ${error}`);
    }
  }

  /**
   * Ping Docker daemon
   */
  async ping(): Promise<boolean> {
    try {
      // Implementation would ping Docker daemon
      return true;
    } catch (error) {
      throw new Error(`Docker daemon not accessible: ${error}`);
    }
  }

  /**
   * Create a container from sandbox config
   */
  async createContainer(config: SandboxConfig): Promise<string> {
    try {
      const containerConfig = this.buildContainerConfig(config);
      
      // Create container using Docker API
      const containerId = await this.dockerCreate(containerConfig);
      
      // Store container info
      const containerInfo = await this.inspectContainer(containerId);
      this.containers.set(containerId, containerInfo);
      
      this.emit('containerCreated', containerId, config.id);
      
      return containerId;
    } catch (error) {
      throw new Error(`Failed to create container: ${error}`);
    }
  }

  /**
   * Start a container
   */
  async startContainer(containerId: string): Promise<void> {
    try {
      await this.dockerStart(containerId);
      
      // Update container info
      const containerInfo = await this.inspectContainer(containerId);
      this.containers.set(containerId, containerInfo);
      
      // Start monitoring if enabled
      await this.startMonitoring(containerId);
      
      this.emit('containerStarted', containerId);
    } catch (error) {
      throw new Error(`Failed to start container: ${error}`);
    }
  }

  /**
   * Stop a container
   */
  async stopContainer(containerId: string, timeout: number = 10): Promise<void> {
    try {
      await this.dockerStop(containerId, timeout);
      
      // Stop monitoring
      await this.stopMonitoring(containerId);
      
      // Update container info
      const containerInfo = await this.inspectContainer(containerId);
      this.containers.set(containerId, containerInfo);
      
      this.emit('containerStopped', containerId);
    } catch (error) {
      throw new Error(`Failed to stop container: ${error}`);
    }
  }

  /**
   * Restart a container
   */
  async restartContainer(containerId: string, timeout: number = 10): Promise<void> {
    try {
      await this.dockerRestart(containerId, timeout);
      
      // Update container info
      const containerInfo = await this.inspectContainer(containerId);
      this.containers.set(containerId, containerInfo);
      
      this.emit('containerRestarted', containerId);
    } catch (error) {
      throw new Error(`Failed to restart container: ${error}`);
    }
  }

  /**
   * Remove a container
   */
  async removeContainer(containerId: string, force: boolean = false): Promise<void> {
    try {
      // Stop monitoring first
      await this.stopMonitoring(containerId);
      
      await this.dockerRemove(containerId, force);
      
      // Remove from local cache
      this.containers.delete(containerId);
      
      this.emit('containerRemoved', containerId);
    } catch (error) {
      throw new Error(`Failed to remove container: ${error}`);
    }
  }

  /**
   * Execute command in container
   */
  async executeCommand(containerId: string, options: DockerExecOptions): Promise<DockerExecResult> {
    try {
      const startTime = Date.now();
      
      // Create exec instance
      const execId = await this.dockerExecCreate(containerId, options);
      
      // Start exec and capture output
      const result = await this.dockerExecStart(execId, options);
      
      const duration = Date.now() - startTime;
      
      return {
        ...result,
        duration,
        timedOut: options.timeout ? duration > options.timeout : false
      };
    } catch (error) {
      throw new Error(`Failed to execute command: ${error}`);
    }
  }

  /**
   * Get container stats
   */
  async getContainerStats(containerId: string): Promise<DockerStats> {
    try {
      const stats = await this.dockerStats(containerId);
      return this.parseStats(containerId, stats);
    } catch (error) {
      throw new Error(`Failed to get container stats: ${error}`);
    }
  }

  /**
   * Get container logs
   */
  async getContainerLogs(
    containerId: string, 
    options: {
      since?: Date;
      until?: Date;
      tail?: number;
      follow?: boolean;
      stdout?: boolean;
      stderr?: boolean;
    } = {}
  ): Promise<string> {
    try {
      return await this.dockerLogs(containerId, options);
    } catch (error) {
      throw new Error(`Failed to get container logs: ${error}`);
    }
  }

  /**
   * Copy files to container
   */
  async copyToContainer(
    containerId: string, 
    sourcePath: string, 
    destinationPath: string
  ): Promise<void> {
    try {
      await this.dockerCopyTo(containerId, sourcePath, destinationPath);
      this.emit('filesCopiedTo', containerId, sourcePath, destinationPath);
    } catch (error) {
      throw new Error(`Failed to copy files to container: ${error}`);
    }
  }

  /**
   * Copy files from container
   */
  async copyFromContainer(
    containerId: string, 
    sourcePath: string, 
    destinationPath: string
  ): Promise<void> {
    try {
      await this.dockerCopyFrom(containerId, sourcePath, destinationPath);
      this.emit('filesCopiedFrom', containerId, sourcePath, destinationPath);
    } catch (error) {
      throw new Error(`Failed to copy files from container: ${error}`);
    }
  }

  /**
   * Build Docker image
   */
  async buildImage(options: DockerBuildOptions): Promise<string> {
    try {
      const imageId = await this.dockerBuild(options);
      this.emit('imageBuild', imageId, options.tags);
      return imageId;
    } catch (error) {
      throw new Error(`Failed to build image: ${error}`);
    }
  }

  /**
   * Pull Docker image
   */
  async pullImage(image: string): Promise<void> {
    try {
      await this.dockerPull(image);
      this.emit('imagePulled', image);
    } catch (error) {
      throw new Error(`Failed to pull image: ${error}`);
    }
  }

  /**
   * List containers
   */
  async listContainers(all: boolean = false): Promise<DockerContainerInfo[]> {
    try {
      const containers = await this.dockerList(all);
      return containers;
    } catch (error) {
      throw new Error(`Failed to list containers: ${error}`);
    }
  }

  /**
   * Inspect container
   */
  async inspectContainer(containerId: string): Promise<DockerContainerInfo> {
    try {
      const info = await this.dockerInspect(containerId);
      return this.parseContainerInfo(info);
    } catch (error) {
      throw new Error(`Failed to inspect container: ${error}`);
    }
  }

  /**
   * Get container by sandbox ID
   */
  getContainerBySandboxId(sandboxId: string): DockerContainerInfo | undefined {
    for (const container of this.containers.values()) {
      if (container.labels['vibekraft.sandbox.id'] === sandboxId) {
        return container;
      }
    }
    return undefined;
  }

  /**
   * Start monitoring container
   */
  async startMonitoring(containerId: string): Promise<void> {
    if (this.statsStreams.has(containerId)) {
      return; // Already monitoring
    }

    try {
      const stream = await this.dockerStatsStream(containerId);
      this.statsStreams.set(containerId, stream);
      
      stream.on('data', (data: any) => {
        const stats = this.parseStats(containerId, data);
        this.emit('containerStats', containerId, stats);
      });
      
      stream.on('error', (error: Error) => {
        this.emit('monitoringError', containerId, error);
        this.statsStreams.delete(containerId);
      });
    } catch (error) {
      throw new Error(`Failed to start monitoring: ${error}`);
    }
  }

  /**
   * Stop monitoring container
   */
  async stopMonitoring(containerId: string): Promise<void> {
    const stream = this.statsStreams.get(containerId);
    if (stream) {
      stream.destroy();
      this.statsStreams.delete(containerId);
    }
  }

  /**
   * Cleanup and shutdown
   */
  async cleanup(): Promise<void> {
    // Stop all monitoring streams
    for (const [containerId, stream] of this.statsStreams) {
      stream.destroy();
    }
    this.statsStreams.clear();
    
    this.emit('cleanup');
  }

  // Private helper methods
  private buildContainerConfig(config: SandboxConfig): any {
    return {
      Image: this.getImageName(config.template),
      name: `vibekraft-${config.id}`,
      Labels: {
        'vibekraft.sandbox.id': config.id,
        'vibekraft.sandbox.name': config.name,
        'vibekraft.sandbox.template': config.template,
        'vibekraft.sandbox.project': config.projectId || '',
        'vibekraft.sandbox.user': config.userId
      },
      Env: Object.entries(config.environment).map(([key, value]) => `${key}=${value}`),
      HostConfig: {
        Memory: config.resources.memSizeMib * 1024 * 1024,
        CpuShares: config.resources.cpuShares || 1024,
        CpuQuota: config.resources.cpuQuota,
        CpuPeriod: config.resources.cpuPeriod,
        PortBindings: this.buildPortBindings(config.network.ports),
        CapDrop: config.security.capabilities.drop,
        CapAdd: config.security.capabilities.add,
        ReadonlyRootfs: config.security.readOnlyRootfs,
        SecurityOpt: config.security.noNewPrivileges ? ['no-new-privileges'] : [],
        PidsLimit: config.resources.maxProcesses,
        Ulimits: [
          {
            Name: 'nofile',
            Soft: config.resources.maxOpenFiles || 1024,
            Hard: config.resources.maxOpenFiles || 1024
          }
        ]
      },
      User: `${config.security.runAsUser}:${config.security.runAsGroup}`,
      WorkingDir: '/workspace',
      ExposedPorts: this.buildExposedPorts(config.network.ports)
    };
  }

  private getImageName(template: string): string {
    return `vibekraft/sandbox-${template}:latest`;
  }

  private buildPortBindings(ports: any[]): any {
    const bindings: any = {};
    
    for (const port of ports) {
      const key = `${port.containerPort}/${port.protocol}`;
      bindings[key] = [{ HostPort: port.hostPort?.toString() || '' }];
    }
    
    return bindings;
  }

  private buildExposedPorts(ports: any[]): any {
    const exposed: any = {};
    
    for (const port of ports) {
      exposed[`${port.containerPort}/${port.protocol}`] = {};
    }
    
    return exposed;
  }

  private parseContainerInfo(info: any): DockerContainerInfo {
    return {
      id: info.Id,
      name: info.Name.replace(/^\//, ''),
      image: info.Config.Image,
      state: info.State.Status,
      status: info.State.Status,
      created: new Date(info.Created),
      ports: this.parsePorts(info.NetworkSettings.Ports),
      networks: this.parseNetworks(info.NetworkSettings.Networks),
      mounts: this.parseMounts(info.Mounts),
      labels: info.Config.Labels || {}
    };
  }

  private parsePorts(ports: any): DockerPortMapping[] {
    const mappings: DockerPortMapping[] = [];
    
    for (const [key, bindings] of Object.entries(ports)) {
      if (bindings && Array.isArray(bindings)) {
        const [port, protocol] = key.split('/');
        for (const binding of bindings as any[]) {
          mappings.push({
            containerPort: parseInt(port),
            hostPort: parseInt(binding.HostPort),
            protocol: protocol as 'tcp' | 'udp',
            hostIp: binding.HostIp
          });
        }
      }
    }
    
    return mappings;
  }

  private parseNetworks(networks: any): DockerNetworkInfo[] {
    const networkInfos: DockerNetworkInfo[] = [];
    
    for (const [name, network] of Object.entries(networks)) {
      networkInfos.push({
        name,
        id: (network as any).NetworkID,
        ipAddress: (network as any).IPAddress,
        gateway: (network as any).Gateway,
        macAddress: (network as any).MacAddress
      });
    }
    
    return networkInfos;
  }

  private parseMounts(mounts: any[]): DockerMountInfo[] {
    return mounts.map(mount => ({
      source: mount.Source,
      destination: mount.Destination,
      type: mount.Type,
      readOnly: mount.RW === false
    }));
  }

  private parseStats(containerId: string, stats: any): DockerStats {
    return {
      containerId,
      cpu: {
        usage: this.calculateCpuUsage(stats),
        systemUsage: stats.cpu_stats?.system_cpu_usage || 0,
        cores: stats.cpu_stats?.online_cpus || 1,
        throttled: stats.cpu_stats?.throttling_data?.throttled_time > 0
      },
      memory: {
        usage: stats.memory_stats?.usage || 0,
        limit: stats.memory_stats?.limit || 0,
        cache: stats.memory_stats?.stats?.cache || 0,
        swap: stats.memory_stats?.stats?.swap || 0
      },
      network: this.parseNetworkStats(stats.networks),
      disk: this.parseDiskStats(stats.blkio_stats),
      timestamp: new Date()
    };
  }

  private calculateCpuUsage(stats: any): number {
    const cpuDelta = stats.cpu_stats?.cpu_usage?.total_usage - stats.precpu_stats?.cpu_usage?.total_usage;
    const systemDelta = stats.cpu_stats?.system_cpu_usage - stats.precpu_stats?.system_cpu_usage;
    const cores = stats.cpu_stats?.online_cpus || 1;
    
    if (systemDelta > 0 && cpuDelta > 0) {
      return (cpuDelta / systemDelta) * cores * 100;
    }
    
    return 0;
  }

  private parseNetworkStats(networks: any): any {
    let bytesReceived = 0;
    let bytesSent = 0;
    let packetsReceived = 0;
    let packetsSent = 0;
    
    if (networks) {
      for (const network of Object.values(networks)) {
        const net = network as any;
        bytesReceived += net.rx_bytes || 0;
        bytesSent += net.tx_bytes || 0;
        packetsReceived += net.rx_packets || 0;
        packetsSent += net.tx_packets || 0;
      }
    }
    
    return { bytesReceived, bytesSent, packetsReceived, packetsSent };
  }

  private parseDiskStats(blkio: any): any {
    let bytesRead = 0;
    let bytesWritten = 0;
    let ioRead = 0;
    let ioWritten = 0;
    
    if (blkio?.io_service_bytes_recursive) {
      for (const stat of blkio.io_service_bytes_recursive) {
        if (stat.op === 'Read') bytesRead += stat.value;
        if (stat.op === 'Write') bytesWritten += stat.value;
      }
    }
    
    if (blkio?.io_serviced_recursive) {
      for (const stat of blkio.io_serviced_recursive) {
        if (stat.op === 'Read') ioRead += stat.value;
        if (stat.op === 'Write') ioWritten += stat.value;
      }
    }
    
    return { bytesRead, bytesWritten, ioRead, ioWritten };
  }

  private async refreshContainers(): Promise<void> {
    const containers = await this.listContainers(true);
    this.containers.clear();
    
    for (const container of containers) {
      this.containers.set(container.id, container);
    }
  }

  // Docker API wrapper methods (these would use actual Docker API)
  private async dockerCreate(config: any): Promise<string> {
    // Implementation would use Docker API to create container
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerStart(containerId: string): Promise<void> {
    // Implementation would use Docker API to start container
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerStop(containerId: string, timeout: number): Promise<void> {
    // Implementation would use Docker API to stop container
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerRestart(containerId: string, timeout: number): Promise<void> {
    // Implementation would use Docker API to restart container
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerRemove(containerId: string, force: boolean): Promise<void> {
    // Implementation would use Docker API to remove container
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerExecCreate(containerId: string, options: DockerExecOptions): Promise<string> {
    // Implementation would use Docker API to create exec instance
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerExecStart(execId: string, options: DockerExecOptions): Promise<any> {
    // Implementation would use Docker API to start exec and capture output
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerStats(containerId: string): Promise<any> {
    // Implementation would use Docker API to get container stats
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerStatsStream(containerId: string): Promise<any> {
    // Implementation would use Docker API to stream container stats
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerLogs(containerId: string, options: any): Promise<string> {
    // Implementation would use Docker API to get container logs
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerCopyTo(containerId: string, source: string, destination: string): Promise<void> {
    // Implementation would use Docker API to copy files to container
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerCopyFrom(containerId: string, source: string, destination: string): Promise<void> {
    // Implementation would use Docker API to copy files from container
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerBuild(options: DockerBuildOptions): Promise<string> {
    // Implementation would use Docker API to build image
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerPull(image: string): Promise<void> {
    // Implementation would use Docker API to pull image
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerList(all: boolean): Promise<DockerContainerInfo[]> {
    // Implementation would use Docker API to list containers
    throw new Error('Not implemented - requires Docker API integration');
  }

  private async dockerInspect(containerId: string): Promise<any> {
    // Implementation would use Docker API to inspect container
    throw new Error('Not implemented - requires Docker API integration');
  }
}
