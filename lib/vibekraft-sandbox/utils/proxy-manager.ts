/**
 * VibeKraft Proxy Manager Utilities
 * 
 * Utilities for application preview and reverse proxy management within sandbox containers
 */

import { 
  ProxyTarget, 
  ProxyConfiguration, 
  ProxyStats,
  ProxyRequest,
  ProxyResponse
} from '../types/sandbox';

export interface ProxyManagerOptions {
  baseDomain?: string;
  basePort?: number;
  enableSSL?: boolean;
  enableAuth?: boolean;
  enableRateLimit?: boolean;
  enableHealthChecks?: boolean;
  maxTargets?: number;
  defaultTimeout?: number;
}

export class ProxyManager {
  private options: Required<ProxyManagerOptions>;
  private dockerManager: any; // Will be injected
  private targets: Map<string, ProxyTarget> = new Map();
  private stats: ProxyStats;

  constructor(dockerManager: any, options: ProxyManagerOptions = {}) {
    this.dockerManager = dockerManager;
    this.options = {
      baseDomain: options.baseDomain || 'vibekraft.local',
      basePort: options.basePort || 8000,
      enableSSL: options.enableSSL ?? false,
      enableAuth: options.enableAuth ?? true,
      enableRateLimit: options.enableRateLimit ?? true,
      enableHealthChecks: options.enableHealthChecks ?? true,
      maxTargets: options.maxTargets || 50,
      defaultTimeout: options.defaultTimeout || 30000
    };

    this.stats = {
      totalRequests: 0,
      activeConnections: 0,
      bytesTransferred: 0,
      averageResponseTime: 0,
      errorRate: 0,
      uptime: Date.now(),
      targets: []
    };
  }

  /**
   * Create a new proxy target for an application
   */
  async createProxyTarget(
    containerId: string,
    sandboxId: string,
    request: ProxyRequest
  ): Promise<ProxyTarget> {
    try {
      // Validate request
      this.validateProxyRequest(request);

      // Check if port is accessible in container
      await this.validateContainerPort(containerId, request.containerPort);

      // Generate unique ID and URLs
      const targetId = this.generateTargetId(sandboxId, request.name);
      const { targetUrl, publicUrl } = this.generateUrls(sandboxId, request);

      // Create proxy target
      const target: ProxyTarget = {
        id: targetId,
        name: request.name,
        containerPort: request.containerPort,
        protocol: request.protocol || 'http',
        path: request.path || '/',
        targetUrl,
        publicUrl,
        enabled: true,
        healthCheck: request.healthCheck ? {
          enabled: request.healthCheck.enabled ?? true,
          path: request.healthCheck.path || '/health',
          interval: request.healthCheck.interval || 30,
          timeout: request.healthCheck.timeout || 5,
          retries: request.healthCheck.retries || 3
        } : undefined,
        ssl: request.ssl ? {
          enabled: request.ssl.enabled ?? false,
          cert: undefined,
          key: undefined
        } : undefined,
        auth: request.auth ? {
          enabled: request.auth.enabled ?? false,
          type: request.auth.type || 'basic',
          credentials: request.auth.credentials
        } : undefined,
        rateLimit: request.rateLimit ? {
          enabled: request.rateLimit.enabled ?? false,
          requests: request.rateLimit.requests || 100,
          window: request.rateLimit.window || 60
        } : undefined,
        cors: request.cors ? {
          enabled: request.cors.enabled ?? true,
          origins: request.cors.origins || ['*'],
          methods: request.cors.methods || ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
          headers: request.cors.headers || ['Content-Type', 'Authorization']
        } : undefined,
        createdAt: new Date(),
        accessCount: 0
      };

      // Configure reverse proxy
      await this.configureReverseProxy(containerId, target);

      // Store target
      this.targets.set(targetId, target);

      // Start health checks if enabled
      if (target.healthCheck?.enabled) {
        this.startHealthCheck(containerId, target);
      }

      return target;

    } catch (error: any) {
      throw new Error(`Failed to create proxy target: ${error.message}`);
    }
  }

  /**
   * Get all proxy targets for a sandbox
   */
  async getProxyTargets(sandboxId: string): Promise<ProxyTarget[]> {
    const targets = Array.from(this.targets.values())
      .filter(target => target.id.startsWith(sandboxId));
    
    return targets;
  }

  /**
   * Get specific proxy target
   */
  async getProxyTarget(targetId: string): Promise<ProxyTarget | null> {
    return this.targets.get(targetId) || null;
  }

  /**
   * Update proxy target configuration
   */
  async updateProxyTarget(
    containerId: string,
    targetId: string,
    updates: Partial<ProxyRequest>
  ): Promise<ProxyTarget> {
    const target = this.targets.get(targetId);
    if (!target) {
      throw new Error('Proxy target not found');
    }

    // Update target properties
    if (updates.name) target.name = updates.name;
    if (updates.path) target.path = updates.path;
    if (updates.healthCheck) {
      target.healthCheck = {
        ...target.healthCheck,
        ...updates.healthCheck
      };
    }
    if (updates.auth) {
      target.auth = {
        ...target.auth,
        ...updates.auth
      };
    }
    if (updates.rateLimit) {
      target.rateLimit = {
        ...target.rateLimit,
        ...updates.rateLimit
      };
    }
    if (updates.cors) {
      target.cors = {
        ...target.cors,
        ...updates.cors
      };
    }

    // Reconfigure reverse proxy
    await this.configureReverseProxy(containerId, target);

    // Update stored target
    this.targets.set(targetId, target);

    return target;
  }

  /**
   * Delete proxy target
   */
  async deleteProxyTarget(containerId: string, targetId: string): Promise<void> {
    const target = this.targets.get(targetId);
    if (!target) {
      throw new Error('Proxy target not found');
    }

    // Remove reverse proxy configuration
    await this.removeReverseProxy(containerId, target);

    // Stop health checks
    this.stopHealthCheck(target);

    // Remove from storage
    this.targets.delete(targetId);
  }

  /**
   * Enable/disable proxy target
   */
  async toggleProxyTarget(containerId: string, targetId: string, enabled: boolean): Promise<ProxyTarget> {
    const target = this.targets.get(targetId);
    if (!target) {
      throw new Error('Proxy target not found');
    }

    target.enabled = enabled;

    if (enabled) {
      await this.configureReverseProxy(containerId, target);
      if (target.healthCheck?.enabled) {
        this.startHealthCheck(containerId, target);
      }
    } else {
      await this.removeReverseProxy(containerId, target);
      this.stopHealthCheck(target);
    }

    this.targets.set(targetId, target);
    return target;
  }

  /**
   * Get proxy statistics
   */
  async getProxyStats(sandboxId?: string): Promise<ProxyStats> {
    const targets = sandboxId 
      ? Array.from(this.targets.values()).filter(t => t.id.startsWith(sandboxId))
      : Array.from(this.targets.values());

    const targetStats = targets.map(target => ({
      id: target.id,
      requests: target.accessCount,
      errors: 0, // Would be tracked in real implementation
      avgResponseTime: 0, // Would be calculated from metrics
      status: target.enabled ? 'healthy' : 'unhealthy' as const
    }));

    return {
      ...this.stats,
      targets: targetStats,
      uptime: Date.now() - this.stats.uptime
    };
  }

  /**
   * Test proxy target accessibility
   */
  async testProxyTarget(containerId: string, targetId: string): Promise<{
    success: boolean;
    responseTime?: number;
    statusCode?: number;
    error?: string;
  }> {
    const target = this.targets.get(targetId);
    if (!target) {
      throw new Error('Proxy target not found');
    }

    try {
      const startTime = Date.now();
      
      // Test connection to container port
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['curl', '-s', '-o', '/dev/null', '-w', '%{http_code}', 
                 `http://localhost:${target.containerPort}${target.path}`],
        timeout: 10
      });

      const responseTime = Date.now() - startTime;
      const statusCode = parseInt(result.stdout.trim());

      return {
        success: result.exitCode === 0 && statusCode >= 200 && statusCode < 400,
        responseTime,
        statusCode,
        error: result.exitCode !== 0 ? result.stderr : undefined
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Private helper methods
  private validateProxyRequest(request: ProxyRequest): void {
    if (!request.name || request.name.trim() === '') {
      throw new Error('Proxy target name is required');
    }

    if (!request.containerPort || request.containerPort < 1 || request.containerPort > 65535) {
      throw new Error('Valid container port is required (1-65535)');
    }

    if (this.targets.size >= this.options.maxTargets) {
      throw new Error(`Maximum proxy targets (${this.options.maxTargets}) exceeded`);
    }

    // Validate protocol
    if (request.protocol && !['http', 'https', 'ws', 'wss'].includes(request.protocol)) {
      throw new Error('Invalid protocol. Must be http, https, ws, or wss');
    }

    // Validate path
    if (request.path && !request.path.startsWith('/')) {
      throw new Error('Path must start with /');
    }
  }

  private async validateContainerPort(containerId: string, port: number): Promise<void> {
    try {
      const result = await this.dockerManager.executeCommand(containerId, {
        command: ['netstat', '-tlnp'],
        timeout: 10
      });

      if (result.exitCode === 0) {
        const isListening = result.stdout.includes(`:${port} `) && result.stdout.includes('LISTEN');
        if (!isListening) {
          throw new Error(`Port ${port} is not listening in container`);
        }
      }
    } catch (error) {
      // Port validation failed, but we'll allow it and let the proxy handle the error
      console.warn(`Could not validate port ${port}:`, error);
    }
  }

  private generateTargetId(sandboxId: string, name: string): string {
    const sanitizedName = name.toLowerCase().replace(/[^a-z0-9-]/g, '-');
    return `${sandboxId}-${sanitizedName}-${Date.now()}`;
  }

  private generateUrls(sandboxId: string, request: ProxyRequest): {
    targetUrl: string;
    publicUrl: string;
  } {
    const protocol = request.protocol || 'http';
    const targetUrl = `${protocol}://localhost:${request.containerPort}`;
    
    let publicUrl: string;
    
    if (request.customDomain) {
      publicUrl = `${this.options.enableSSL ? 'https' : 'http'}://${request.customDomain}`;
    } else if (request.subdomain) {
      publicUrl = `${this.options.enableSSL ? 'https' : 'http'}://${request.subdomain}.${this.options.baseDomain}`;
    } else {
      const sanitizedName = request.name.toLowerCase().replace(/[^a-z0-9-]/g, '-');
      publicUrl = `${this.options.enableSSL ? 'https' : 'http'}://${sandboxId}-${sanitizedName}.${this.options.baseDomain}`;
    }

    return { targetUrl, publicUrl };
  }

  private async configureReverseProxy(containerId: string, target: ProxyTarget): Promise<void> {
    // In a real implementation, this would configure nginx, traefik, or another reverse proxy
    // For now, we'll simulate the configuration
    
    try {
      // Create nginx configuration for this target
      const nginxConfig = this.generateNginxConfig(target);
      
      // Write nginx config to container
      await this.dockerManager.executeCommand(containerId, {
        command: ['bash', '-c', `echo '${nginxConfig}' > /etc/nginx/sites-available/${target.id}.conf`],
        timeout: 10
      });

      // Enable the site
      await this.dockerManager.executeCommand(containerId, {
        command: ['ln', '-sf', `/etc/nginx/sites-available/${target.id}.conf`, `/etc/nginx/sites-enabled/${target.id}.conf`],
        timeout: 5
      });

      // Reload nginx
      await this.dockerManager.executeCommand(containerId, {
        command: ['nginx', '-s', 'reload'],
        timeout: 10
      });

    } catch (error) {
      console.warn('Failed to configure reverse proxy:', error);
      // In a real implementation, we might fall back to a simpler proxy method
    }
  }

  private async removeReverseProxy(containerId: string, target: ProxyTarget): Promise<void> {
    try {
      // Remove nginx configuration
      await this.dockerManager.executeCommand(containerId, {
        command: ['rm', '-f', `/etc/nginx/sites-enabled/${target.id}.conf`, `/etc/nginx/sites-available/${target.id}.conf`],
        timeout: 10
      });

      // Reload nginx
      await this.dockerManager.executeCommand(containerId, {
        command: ['nginx', '-s', 'reload'],
        timeout: 10
      });

    } catch (error) {
      console.warn('Failed to remove reverse proxy configuration:', error);
    }
  }

  private generateNginxConfig(target: ProxyTarget): string {
    const config = `
server {
    listen 80;
    server_name ${target.publicUrl.replace(/^https?:\/\//, '')};
    
    location ${target.path} {
        proxy_pass ${target.targetUrl};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        ${target.cors?.enabled ? `
        # CORS headers
        add_header Access-Control-Allow-Origin "${target.cors.origins.join(' ')}";
        add_header Access-Control-Allow-Methods "${target.cors.methods.join(' ')}";
        add_header Access-Control-Allow-Headers "${target.cors.headers.join(' ')}";
        ` : ''}
        
        ${target.rateLimit?.enabled ? `
        # Rate limiting
        limit_req zone=api burst=${target.rateLimit.requests} nodelay;
        ` : ''}
    }
    
    ${target.healthCheck?.enabled ? `
    location ${target.healthCheck.path} {
        proxy_pass ${target.targetUrl}${target.healthCheck.path};
        access_log off;
    }
    ` : ''}
}`;

    return config.trim();
  }

  private startHealthCheck(containerId: string, target: ProxyTarget): void {
    if (!target.healthCheck?.enabled) return;

    // In a real implementation, this would start a periodic health check
    console.log(`Starting health check for ${target.id} every ${target.healthCheck.interval}s`);
  }

  private stopHealthCheck(target: ProxyTarget): void {
    // In a real implementation, this would stop the periodic health check
    console.log(`Stopping health check for ${target.id}`);
  }
}
