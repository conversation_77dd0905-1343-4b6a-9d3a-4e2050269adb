# VibeKraft Sandbox Base Image
# 
# A comprehensive base image for VibeKraft sandboxes with multiple runtime support
# Includes XFCE desktop, VNC, Node.js, Python, PostgreSQL, and development tools

FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Set up environment variables
ENV DISPLAY=:1
ENV VNC_PORT=5901
ENV NOVNC_PORT=6080
ENV RESOLUTION=1280x800
ENV VNC_PASSWORD=vibekraft
ENV USER=sandbox
ENV HOME=/home/<USER>
ENV WORKSPACE=/workspace

# Install system packages and dependencies
RUN apt-get update && apt-get install -y \
    # Core system utilities
    curl wget gnupg2 ca-certificates sudo apt-transport-https software-properties-common \
    build-essential git vim nano htop tree unzip zip \
    # Desktop environment
    xfce4 xfce4-terminal xfce4-session xfconf dbus-x11 \
    xfonts-base xserver-xorg-video-dummy \
    # VNC and remote access
    tigervnc-standalone-server tigervnc-common \
    websockify novnc \
    # Development tools
    gcc g++ make cmake \
    pkg-config autoconf automake libtool \
    # Network tools
    net-tools iputils-ping telnet netcat-openbsd \
    # File and archive tools
    file rsync \
    # Process and system monitoring
    procps psmisc lsof \
    # Text processing
    jq yq \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Node.js (LTS version)
RUN curl -fsSL https://deb.nodesource.com/setup_lts.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm@latest yarn pnpm

# Install Python and pip
RUN apt-get update && apt-get install -y \
    python3.10 python3.10-dev python3.10-venv \
    python3-pip python3-setuptools python3-wheel \
    && ln -sf /usr/bin/python3.10 /usr/bin/python \
    && ln -sf /usr/bin/pip3 /usr/bin/pip \
    && pip install --upgrade pip setuptools wheel \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install common Python packages
RUN pip install \
    # Web frameworks
    flask fastapi django \
    # Data science
    numpy pandas matplotlib seaborn plotly \
    # Machine learning
    scikit-learn tensorflow torch \
    # Development tools
    jupyter notebook jupyterlab \
    # Utilities
    requests beautifulsoup4 lxml \
    python-dotenv pyyaml \
    # Testing
    pytest pytest-cov \
    # Code quality
    black flake8 mypy

# Install PostgreSQL
RUN apt-get update && apt-get install -y \
    postgresql-14 postgresql-client-14 postgresql-contrib-14 \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install additional development tools
RUN apt-get update && apt-get install -y \
    # Version control
    git-lfs \
    # Databases
    sqlite3 redis-tools \
    # Container tools (for development)
    docker.io \
    # Cloud tools
    awscli \
    # Text editors
    code-server \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Go
RUN wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz \
    && tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz \
    && rm go1.21.0.linux-amd64.tar.gz
ENV PATH=$PATH:/usr/local/go/bin

# Install Rust
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
ENV PATH=$PATH:/root/.cargo/bin

# Install Java
RUN apt-get update && apt-get install -y \
    openjdk-17-jdk maven gradle \
    && apt-get clean && rm -rf /var/lib/apt/lists/*
ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64

# Create sandbox user
RUN useradd -ms /bin/bash $USER \
    && echo "$USER ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers \
    && usermod -aG docker $USER

# Set up PostgreSQL for sandbox user
USER postgres
RUN /etc/init.d/postgresql start \
    && psql --command "CREATE USER sandbox WITH SUPERUSER PASSWORD 'sandbox';" \
    && createdb -O sandbox sandbox \
    && /etc/init.d/postgresql stop

# Switch back to root for remaining setup
USER root

# Create workspace directory
RUN mkdir -p $WORKSPACE \
    && chown -R $USER:$USER $WORKSPACE \
    && chmod 755 $WORKSPACE

# Set up VNC directory and password
USER $USER
RUN mkdir -p $HOME/.vnc \
    && echo $VNC_PASSWORD | vncpasswd -f > $HOME/.vnc/passwd \
    && chmod 600 $HOME/.vnc/passwd

# Create VNC startup script
RUN echo '#!/bin/bash\n\
export DISPLAY=:1\n\
export RESOLUTION=1280x800\n\
\n\
# Start Xvfb\n\
Xvfb $DISPLAY -screen 0 ${RESOLUTION}x24 -ac +extension GLX +render -noreset &\n\
sleep 2\n\
\n\
# Start XFCE\n\
startxfce4 &\n\
sleep 3\n\
\n\
# Start VNC server\n\
vncserver $DISPLAY -geometry $RESOLUTION -depth 24 -localhost no &\n\
sleep 2\n\
\n\
# Start noVNC\n\
websockify --web=/usr/share/novnc/ $NOVNC_PORT localhost:$VNC_PORT &\n\
\n\
echo "VNC server started on port $VNC_PORT"\n\
echo "noVNC web interface available on port $NOVNC_PORT"\n\
' > $HOME/start-vnc.sh \
    && chmod +x $HOME/start-vnc.sh

# Create PostgreSQL startup script
RUN echo '#!/bin/bash\n\
# Start PostgreSQL\n\
sudo service postgresql start\n\
\n\
# Wait for PostgreSQL to be ready\n\
until sudo -u postgres pg_isready; do\n\
  echo "Waiting for PostgreSQL..."\n\
  sleep 1\n\
done\n\
\n\
echo "PostgreSQL is ready"\n\
' > $HOME/start-postgres.sh \
    && chmod +x $HOME/start-postgres.sh

# Create main startup script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
echo "Starting VibeKraft Sandbox..."\n\
\n\
# Start PostgreSQL\n\
$HOME/start-postgres.sh\n\
\n\
# Start VNC and desktop\n\
$HOME/start-vnc.sh\n\
\n\
# Start code-server if requested\n\
if [ "$START_CODE_SERVER" = "true" ]; then\n\
  echo "Starting code-server..."\n\
  code-server --bind-addr 0.0.0.0:8080 --auth none $WORKSPACE &\n\
fi\n\
\n\
# Change to workspace directory\n\
cd $WORKSPACE\n\
\n\
# Run custom initialization script if it exists\n\
if [ -f "$WORKSPACE/init.sh" ]; then\n\
  echo "Running custom initialization script..."\n\
  bash $WORKSPACE/init.sh\n\
fi\n\
\n\
# Keep container running\n\
echo "Sandbox is ready!"\n\
echo "VNC: localhost:$VNC_PORT (password: $VNC_PASSWORD)"\n\
echo "noVNC: http://localhost:$NOVNC_PORT"\n\
echo "PostgreSQL: localhost:5432 (user: sandbox, password: sandbox)"\n\
if [ "$START_CODE_SERVER" = "true" ]; then\n\
  echo "Code Server: http://localhost:8080"\n\
fi\n\
echo "Workspace: $WORKSPACE"\n\
\n\
# Execute command if provided, otherwise keep running\n\
if [ $# -gt 0 ]; then\n\
  exec "$@"\n\
else\n\
  tail -f /dev/null\n\
fi\n\
' > $HOME/start-sandbox.sh \
    && chmod +x $HOME/start-sandbox.sh

# Create health check script
RUN echo '#!/bin/bash\n\
# Check if VNC server is running\n\
if ! pgrep -f "Xvfb.*:1" > /dev/null; then\n\
  exit 1\n\
fi\n\
\n\
# Check if PostgreSQL is running\n\
if ! sudo -u postgres pg_isready > /dev/null 2>&1; then\n\
  exit 1\n\
fi\n\
\n\
exit 0\n\
' > $HOME/health-check.sh \
    && chmod +x $HOME/health-check.sh

# Set up XFCE configuration
RUN mkdir -p $HOME/.config/xfce4/xfconf/xfce-perchannel-xml \
    && echo '<?xml version="1.0" encoding="UTF-8"?>\n\
<channel name="xfce4-desktop" version="1.0">\n\
  <property name="backdrop" type="empty">\n\
    <property name="screen0" type="empty">\n\
      <property name="monitor0" type="empty">\n\
        <property name="workspace0" type="empty">\n\
          <property name="color-style" type="int" value="0"/>\n\
          <property name="image-style" type="int" value="5"/>\n\
          <property name="last-image" type="string" value=""/>\n\
        </property>\n\
      </property>\n\
    </property>\n\
  </property>\n\
</channel>' > $HOME/.config/xfce4/xfconf/xfce-perchannel-xml/xfce4-desktop.xml

# Create sample files for different languages
RUN mkdir -p $WORKSPACE/examples

# Node.js example
RUN echo 'const express = require("express");\n\
const app = express();\n\
const port = process.env.PORT || 3000;\n\
\n\
app.get("/", (req, res) => {\n\
  res.json({ message: "Hello from VibeKraft Sandbox!", language: "Node.js" });\n\
});\n\
\n\
app.listen(port, () => {\n\
  console.log(`Server running on port ${port}`);\n\
});' > $WORKSPACE/examples/app.js

# Python example
RUN echo 'from flask import Flask, jsonify\n\
import os\n\
\n\
app = Flask(__name__)\n\
\n\
@app.route("/")\n\
def hello():\n\
    return jsonify({\n\
        "message": "Hello from VibeKraft Sandbox!",\n\
        "language": "Python"\n\
    })\n\
\n\
if __name__ == "__main__":\n\
    port = int(os.environ.get("PORT", 8000))\n\
    app.run(host="0.0.0.0", port=port, debug=True)' > $WORKSPACE/examples/app.py

# Package.json for Node.js example
RUN echo '{\n\
  "name": "vibekraft-sandbox-example",\n\
  "version": "1.0.0",\n\
  "description": "VibeKraft Sandbox Example Application",\n\
  "main": "app.js",\n\
  "scripts": {\n\
    "start": "node app.js",\n\
    "dev": "nodemon app.js"\n\
  },\n\
  "dependencies": {\n\
    "express": "^4.18.2"\n\
  },\n\
  "devDependencies": {\n\
    "nodemon": "^3.0.1"\n\
  }\n\
}' > $WORKSPACE/examples/package.json

# Requirements.txt for Python example
RUN echo 'Flask==2.3.3\n\
requests==2.31.0\n\
python-dotenv==1.0.0' > $WORKSPACE/examples/requirements.txt

# README file
RUN echo '# VibeKraft Sandbox\n\
\n\
Welcome to your VibeKraft Sandbox! This environment includes:\n\
\n\
## Available Runtimes\n\
- Node.js (with npm, yarn, pnpm)\n\
- Python 3.10 (with pip, common packages)\n\
- Go 1.21\n\
- Rust (latest stable)\n\
- Java 17 (with Maven, Gradle)\n\
\n\
## Services\n\
- PostgreSQL 14 (user: sandbox, password: sandbox)\n\
- VNC Desktop (XFCE)\n\
- Code Server (if enabled)\n\
\n\
## Getting Started\n\
\n\
### Node.js\n\
```bash\n\
cd examples\n\
npm install\n\
npm start\n\
```\n\
\n\
### Python\n\
```bash\n\
cd examples\n\
pip install -r requirements.txt\n\
python app.py\n\
```\n\
\n\
### Database\n\
```bash\n\
psql -h localhost -U sandbox sandbox\n\
```\n\
\n\
## Ports\n\
- 3000: Node.js applications\n\
- 8000: Python applications\n\
- 5432: PostgreSQL\n\
- 5901: VNC\n\
- 6080: noVNC (web interface)\n\
- 8080: Code Server (if enabled)\n\
\n\
Happy coding!\n\
' > $WORKSPACE/README.md

# Set ownership of all files
RUN chown -R $USER:$USER $HOME $WORKSPACE

# Switch back to root for final setup
USER root

# Expose ports
EXPOSE 3000 8000 5432 5901 6080 8080

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD su - $USER -c '$HOME/health-check.sh'

# Switch to sandbox user
USER $USER
WORKDIR $WORKSPACE

# Set entrypoint
ENTRYPOINT ["/home/<USER>/start-sandbox.sh"]
CMD []
