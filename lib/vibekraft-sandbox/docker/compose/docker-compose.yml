# VibeKraft Sandbox Infrastructure
# Docker Compose configuration for development and production deployment

version: '3.8'

services:
  # VibeKraft Sandbox API Server
  vibekraft-api:
    build:
      context: ../../../..
      dockerfile: lib/vibekraft-sandbox/docker/api/Dockerfile
    container_name: vibekraft-sandbox-api
    restart: unless-stopped
    ports:
      - "4000:4000"
    volumes:
      # Mount Docker socket for container management
      - /var/run/docker.sock:/var/run/docker.sock
      # Mount workspace for file operations
      - vibekraft_workspaces:/workspaces
      # Mount templates
      - ../templates:/app/templates:ro
      # Mount configuration
      - ../config:/app/config:ro
    environment:
      - NODE_ENV=production
      - API_PORT=4000
      - DOCKER_HOST=unix:///var/run/docker.sock
      - MAX_CONCURRENT_SANDBOXES=100
      - ENABLE_MONITORING=true
      - ENABLE_LOGGING=true
      - LOG_LEVEL=info
      # Database connection
      - DATABASE_URL=**********************************************/vibekraft
      # Redis for caching and sessions
      - REDIS_URL=redis://redis:6379
      # Security
      - JWT_SECRET=your-jwt-secret-change-in-production
      - ENCRYPTION_KEY=your-encryption-key-change-in-production
    depends_on:
      - postgres
      - redis
    networks:
      - vibekraft-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: vibekraft-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=vibekraft
      - POSTGRES_USER=vibekraft
      - POSTGRES_PASSWORD=vibekraft
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - vibekraft-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U vibekraft -d vibekraft"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: vibekraft-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass vibekraft
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - vibekraft-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Apache Guacamole for remote desktop access
  guacd:
    image: guacamole/guacd:1.5.3
    container_name: vibekraft-guacd
    restart: unless-stopped
    networks:
      - vibekraft-network
    volumes:
      - guacamole_data:/var/lib/guacamole

  guacamole:
    image: guacamole/guacamole:1.5.3
    container_name: vibekraft-guacamole
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - GUACD_HOSTNAME=guacd
      - GUACD_PORT=4822
      - POSTGRES_HOSTNAME=postgres
      - POSTGRES_DATABASE=vibekraft
      - POSTGRES_USER=vibekraft
      - POSTGRES_PASSWORD=vibekraft
    depends_on:
      - guacd
      - postgres
    networks:
      - vibekraft-network
    volumes:
      - guacamole_data:/var/lib/guacamole

  # Nginx reverse proxy and load balancer
  nginx:
    image: nginx:alpine
    container_name: vibekraft-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - vibekraft-api
      - guacamole
    networks:
      - vibekraft-network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: vibekraft-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - vibekraft-network

  # Grafana for monitoring dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: vibekraft-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=vibekraft
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - vibekraft-network

  # Log aggregation with Loki
  loki:
    image: grafana/loki:latest
    container_name: vibekraft-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./loki.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - vibekraft-network

  # Log collection with Promtail
  promtail:
    image: grafana/promtail:latest
    container_name: vibekraft-promtail
    restart: unless-stopped
    volumes:
      - ./promtail.yml:/etc/promtail/config.yml:ro
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    networks:
      - vibekraft-network

  # File storage service (MinIO)
  minio:
    image: minio/minio:latest
    container_name: vibekraft-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=vibekraft
      - MINIO_ROOT_PASSWORD=vibekraft123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - vibekraft-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Container registry (for custom images)
  registry:
    image: registry:2
    container_name: vibekraft-registry
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - REGISTRY_STORAGE_FILESYSTEM_ROOTDIRECTORY=/var/lib/registry
      - REGISTRY_AUTH=htpasswd
      - REGISTRY_AUTH_HTPASSWD_REALM=Registry Realm
      - REGISTRY_AUTH_HTPASSWD_PATH=/auth/htpasswd
    volumes:
      - registry_data:/var/lib/registry
      - ./auth:/auth:ro
    networks:
      - vibekraft-network

# Networks
networks:
  vibekraft-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  guacamole_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  minio_data:
    driver: local
  registry_data:
    driver: local
  nginx_logs:
    driver: local
  vibekraft_workspaces:
    driver: local
