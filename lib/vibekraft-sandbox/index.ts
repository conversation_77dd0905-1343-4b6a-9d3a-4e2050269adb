/**
 * VibeKraft Sandbox Infrastructure
 * 
 * Complete sandbox-as-a-service infrastructure with optimized Docker images
 */

// Core Components
export { SandboxManager } from './components/sandbox-manager';
export { SandboxList } from './components/sandbox-manager/sandbox-list';
export { SandboxCard } from './components/sandbox-manager/sandbox-card';
export { SandboxGrid } from './components/sandbox-manager/sandbox-grid';
export { CreateSandboxForm } from './components/sandbox-manager/create-sandbox-form';
export { SandboxDetails } from './components/sandbox-manager/sandbox-details';
export { TemplateManager } from './components/sandbox-manager/template-manager';

// Dialog Components
export {
  ConfirmationDialog,
  SandboxActionDialog,
  TemplateSelectionDialog,
  TerminalDialog
} from './components/dialogs';
export {
  DialogManager,
  DialogManagerProvider,
  useDialogManager
} from './components/dialogs/dialog-manager';
export { DialogDemo } from './components/dialogs/dialog-demo';

// Hooks
export { useSandboxManager } from './hooks/use-sandbox-manager';
export { useOptimizedTemplates } from './hooks/use-optimized-templates';

// Client-side Classes (no Docker dependencies)
export { SandboxManagerClient, sandboxManagerClient } from './client/sandbox-manager-client';
export { SandboxAPIClient, sandboxAPI } from './api/sandbox-client';

// Types (from API client)
export type {
  SandboxConfig,
  SandboxStatus,
  CreateSandboxRequest,
  ExecuteCommandRequest,
  ExecuteCommandResponse,
  FileOperation,
  PackageOperation
} from './api/sandbox-client';

export type {
  SandboxListItem,
  UseSandboxManagerOptions,
  UseSandboxManagerReturn
} from './hooks/use-sandbox-manager';

export type {
  OptimizedTemplate,
  TemplateAnalysis,
  TemplateStatistics,
  TemplateRecommendations,
  UseOptimizedTemplatesOptions,
  UseOptimizedTemplatesReturn
} from './hooks/use-optimized-templates';

// Client Configuration Types
export type { SandboxManagerClientConfig } from './client/sandbox-manager-client';

// Re-export for convenience
export default SandboxManager;
