/**
 * VibeKraft Image Optimizer
 * 
 * Production-grade Docker image optimization for sandbox containers
 */

const Dockerode = require('dockerode');
import { EventEmitter } from 'events';
import * as tar from 'tar';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface ImageOptimizationConfig {
  baseImage: string;
  targetImage: string;
  optimizations: {
    multiStage?: boolean;
    minifyLayers?: boolean;
    removeCache?: boolean;
    compressLayers?: boolean;
    stripDebugSymbols?: boolean;
    removeDocumentation?: boolean;
    optimizePackageManager?: boolean;
  };
  packageManagers?: {
    apt?: boolean;
    npm?: boolean;
    pip?: boolean;
    cargo?: boolean;
  };
  customOptimizations?: string[];
}

export class ImageOptimizer extends EventEmitter {
  private docker: any;

  constructor() {
    super();
    this.docker = new Dockerode();
  }

  /**
   * Generate optimized Dockerfile for sandbox environments
   */
  generateOptimizedDockerfile(config: ImageOptimizationConfig): string {
    const { baseImage, optimizations, packageManagers, customOptimizations } = config;
    
    let dockerfile = '';

    if (optimizations.multiStage) {
      // Multi-stage build for minimal final image
      dockerfile += this.generateMultiStageDockerfile(baseImage, packageManagers, customOptimizations);
    } else {
      // Single-stage optimized build
      dockerfile += this.generateSingleStageDockerfile(baseImage, optimizations, packageManagers, customOptimizations);
    }

    return dockerfile;
  }

  /**
   * Multi-stage Dockerfile for maximum optimization
   */
  private generateMultiStageDockerfile(
    baseImage: string, 
    packageManagers?: any, 
    customOptimizations?: string[]
  ): string {
    return `
# Multi-stage build for optimized VibeKraft sandbox
# Stage 1: Build environment with all tools
FROM ${baseImage} as builder

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \\
    build-essential \\
    curl \\
    wget \\
    git \\
    ca-certificates \\
    && rm -rf /var/lib/apt/lists/*

# Install package managers if needed
${packageManagers?.npm ? this.getNpmInstallCommands() : ''}
${packageManagers?.pip ? this.getPipInstallCommands() : ''}
${packageManagers?.cargo ? this.getCargoInstallCommands() : ''}

# Custom optimizations
${customOptimizations?.join('\n') || ''}

# Stage 2: Runtime environment (minimal)
FROM ${baseImage.includes('alpine') ? baseImage : 'ubuntu:22.04'} as runtime

# Create non-root user
RUN groupadd -r vibekraft && useradd -r -g vibekraft -u 1000 vibekraft

# Install only runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \\
    ca-certificates \\
    ${packageManagers?.npm ? 'nodejs npm' : ''} \\
    ${packageManagers?.pip ? 'python3 python3-pip' : ''} \\
    && apt-get clean \\
    && rm -rf /var/lib/apt/lists/* \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/*

# Copy only necessary files from builder
COPY --from=builder /usr/local/bin/* /usr/local/bin/
COPY --from=builder /usr/local/lib/* /usr/local/lib/

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD echo "healthy" || exit 1

CMD ["/bin/bash"]
`;
  }

  /**
   * Single-stage optimized Dockerfile
   */
  private generateSingleStageDockerfile(
    baseImage: string,
    optimizations: any,
    packageManagers?: any,
    customOptimizations?: string[]
  ): string {
    return `
# Optimized single-stage VibeKraft sandbox
FROM ${baseImage}

# Optimize package manager
${optimizations.optimizePackageManager ? this.getPackageManagerOptimizations() : ''}

# Install dependencies in single layer
RUN apt-get update && apt-get install -y --no-install-recommends \\
    ca-certificates \\
    curl \\
    wget \\
    ${packageManagers?.npm ? 'nodejs npm' : ''} \\
    ${packageManagers?.pip ? 'python3 python3-pip' : ''} \\
    ${packageManagers?.cargo ? 'cargo' : ''} \\
    && ${optimizations.removeCache ? this.getCacheCleanupCommands() : ''} \\
    && ${optimizations.removeDocumentation ? this.getDocumentationCleanupCommands() : ''} \\
    && apt-get clean \\
    && rm -rf /var/lib/apt/lists/* \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/*

# Create non-root user
RUN groupadd -r vibekraft && useradd -r -g vibekraft -u 1000 vibekraft

# Create and configure workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Custom optimizations
${customOptimizations?.join('\n') || ''}

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD echo "healthy" || exit 1

CMD ["/bin/bash"]
`;
  }

  /**
   * Build optimized image
   */
  async buildOptimizedImage(config: ImageOptimizationConfig): Promise<string> {
    try {
      this.emit('buildStart', { targetImage: config.targetImage });

      // Generate optimized Dockerfile
      const dockerfile = this.generateOptimizedDockerfile(config);
      
      // Create build context
      const buildContext = await this.createBuildContext(dockerfile);
      
      // Build image with optimizations
      const stream = await this.docker.buildImage(buildContext, {
        t: config.targetImage,
        dockerfile: 'Dockerfile',
        buildargs: {
          BUILDKIT_INLINE_CACHE: '1'
        },
        // Enable BuildKit for better optimization
        version: '2',
        // Squash layers if requested
        squash: config.optimizations.minifyLayers || false
      });

      // Monitor build progress
      await this.monitorBuildProgress(stream);

      // Post-build optimizations
      if (config.optimizations.compressLayers) {
        await this.compressImageLayers(config.targetImage);
      }

      this.emit('buildComplete', { targetImage: config.targetImage });
      return config.targetImage;

    } catch (error) {
      this.emit('buildError', { error, targetImage: config.targetImage });
      throw error;
    }
  }

  /**
   * Get npm installation commands optimized for size
   */
  private getNpmInstallCommands(): string {
    return `
# Install Node.js and npm (optimized)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \\
    && apt-get install -y nodejs \\
    && npm config set cache /tmp/.npm \\
    && npm config set fund false \\
    && npm config set audit false
`;
  }

  /**
   * Get pip installation commands optimized for size
   */
  private getPipInstallCommands(): string {
    return `
# Install Python and pip (optimized)
RUN apt-get install -y python3 python3-pip \\
    && pip3 config set global.cache-dir /tmp/pip-cache \\
    && pip3 config set global.no-cache-dir true
`;
  }

  /**
   * Get Cargo installation commands optimized for size
   */
  private getCargoInstallCommands(): string {
    return `
# Install Rust and Cargo (optimized)
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y \\
    && . ~/.cargo/env \\
    && rustup component remove rust-docs \\
    && cargo install-update -a
`;
  }

  /**
   * Package manager optimizations
   */
  private getPackageManagerOptimizations(): string {
    return `
# Configure package managers for minimal installs
RUN echo 'APT::Install-Recommends "0";' >> /etc/apt/apt.conf.d/01norecommend \\
    && echo 'APT::Install-Suggests "0";' >> /etc/apt/apt.conf.d/01norecommend \\
    && echo 'APT::Get::Assume-Yes "true";' >> /etc/apt/apt.conf.d/01norecommend \\
    && echo 'APT::Keep-Downloaded-Packages "false";' >> /etc/apt/apt.conf.d/01norecommend
`;
  }

  /**
   * Cache cleanup commands
   */
  private getCacheCleanupCommands(): string {
    return `
# Clean package manager caches
apt-get autoremove -y \\
&& apt-get autoclean \\
&& rm -rf /var/cache/apt/* \\
&& rm -rf /var/lib/apt/lists/* \\
&& rm -rf /tmp/* \\
&& rm -rf /var/tmp/* \\
&& find /var/log -type f -delete
`;
  }

  /**
   * Documentation cleanup commands
   */
  private getDocumentationCleanupCommands(): string {
    return `
# Remove documentation and man pages
rm -rf /usr/share/doc/* \\
&& rm -rf /usr/share/man/* \\
&& rm -rf /usr/share/info/* \\
&& rm -rf /usr/share/locale/* \\
&& rm -rf /var/cache/man/*
`;
  }

  /**
   * Create build context with Dockerfile
   */
  private async createBuildContext(dockerfile: string): Promise<NodeJS.ReadableStream> {
    const tarStream = tar.create({ gzip: true }, []);
    
    // Add Dockerfile to tar stream
    tarStream.entry({ name: 'Dockerfile' }, dockerfile);
    
    return tarStream;
  }

  /**
   * Monitor build progress
   */
  private async monitorBuildProgress(stream: NodeJS.ReadableStream): Promise<void> {
    return new Promise((resolve, reject) => {
      this.docker.modem.followProgress(stream, (err: any, res: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(res);
        }
      }, (event: any) => {
        if (event.stream) {
          this.emit('buildProgress', { message: event.stream.trim() });
        }
      });
    });
  }

  /**
   * Compress image layers post-build
   */
  private async compressImageLayers(imageName: string): Promise<void> {
    try {
      const image = this.docker.getImage(imageName);
      
      // Export and re-import with compression
      const exportStream = await image.get();
      const importStream = await this.docker.loadImage(exportStream);
      
      this.emit('compressionComplete', { imageName });
    } catch (error) {
      this.emit('compressionError', { error, imageName });
      // Don't throw - compression is optional
    }
  }

  /**
   * Analyze image size and layers
   */
  async analyzeImage(imageName: string): Promise<any> {
    try {
      const image = this.docker.getImage(imageName);
      const inspect = await image.inspect();
      
      return {
        size: inspect.Size,
        virtualSize: inspect.VirtualSize,
        layers: inspect.RootFS?.Layers?.length || 0,
        created: inspect.Created,
        architecture: inspect.Architecture,
        os: inspect.Os
      };
    } catch (error) {
      throw new Error(`Failed to analyze image ${imageName}: ${error}`);
    }
  }
}
