/**
 * VibeKraft Sandbox Manager
 *
 * Production-grade sandbox manager using Docker for the VibeKraft infrastructure
 */

import Dockerode from 'dockerode';
import { EventEmitter } from 'events';

import {
  SandboxConfig,
  SandboxStatus,
  SandboxState,
  ExecuteCommandRequest,
  ExecuteCommandResponse
} from '../types/sandbox';
import { OptimizedTemplateManager } from './optimized-templates';

export interface DockerManagerConfig {
  socketPath?: string;
  host?: string;
  port?: number;
  protocol?: 'https' | 'http' | 'ssh';
  ca?: string;
  cert?: string;
  key?: string;
  timeout?: number;
  version?: string;
}

export interface SandboxManagerOptions {
  docker?: DockerManagerConfig;
  maxConcurrentSandboxes?: number;
  defaultResourceLimits?: {
    memory?: number;
    cpus?: number;
    diskQuota?: number;
  };
  networkConfig?: {
    defaultNetwork?: string;
    enableNetworking?: boolean;
  };
  securityConfig?: {
    allowPrivileged?: boolean;
    defaultUser?: string;
    readOnlyRootfs?: boolean;
  };
  enableLogging?: boolean;
  enableMonitoring?: boolean;
  enableImageOptimization?: boolean;
  autoOptimizeImages?: boolean;
}

export class ProductionDockerManager extends EventEmitter {
  private docker: any;
  private containers: Map<string, any> = new Map();
  private networks: Map<string, any> = new Map();
  private volumes: Map<string, any> = new Map();

  constructor(config: DockerManagerConfig = {}) {
    super();

    // Initialize Docker client
    this.docker = new Dockerode({
      socketPath: config.socketPath || '/var/run/docker.sock',
      host: config.host,
      port: config.port,
      protocol: config.protocol,
      ca: config.ca,
      cert: config.cert,
      key: config.key,
      timeout: config.timeout || 30000,
      version: config.version || 'v1.41'
    });
  }

  async initialize(): Promise<void> {
    try {
      // Test Docker connection
      await this.docker.ping();
      console.log('Docker connection established successfully');

      // Create default network for sandboxes if it doesn't exist
      await this.ensureNetwork('vibekraft-sandbox-network');

      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize Docker manager:', error);
      this.emit('error', error);
      throw error;
    }
  }

  async createContainer(config: SandboxConfig): Promise<string> {
    try {
      const containerName = `vibekraft-${config.id}`;

      // Prepare container configuration
      const containerConfig: any = {
        name: containerName,
        Image: config.template,
        Env: this.buildEnvironmentVariables(config.environment),
        WorkingDir: '/workspace',
        User: `${config.security.runAsUser}:${config.security.runAsGroup}`,

        // Resource limits
        HostConfig: {
          Memory: config.resources.memSizeMib * 1024 * 1024,
          CpuShares: config.resources.cpuShares || 1024,
          CpuQuota: config.resources.cpuQuota || 100000,
          CpuPeriod: config.resources.cpuPeriod || 100000,

          // Security settings
          ReadonlyRootfs: config.security.readOnlyRootfs,
          SecurityOpt: config.security.noNewPrivileges ? ['no-new-privileges:true'] : [],
          CapDrop: config.security.capabilities.drop,
          CapAdd: config.security.capabilities.add,

          // Network settings
          NetworkMode: 'vibekraft-sandbox-network',
          PortBindings: this.buildPortBindings(config.network.ports),

          // Volume mounts - create workspace volume
          Binds: [`vibekraft-${config.id}-workspace:/workspace:rw`],

          // Tmpfs mounts for writable directories when using read-only root
          Tmpfs: config.security.readOnlyRootfs ? {
            '/tmp': 'rw,noexec,nosuid,size=100m',
            '/var/tmp': 'rw,noexec,nosuid,size=50m'
          } : undefined,

          // Resource cleanup
          AutoRemove: false,
          RestartPolicy: { Name: 'no' }
        },

        // Networking
        NetworkingConfig: {
          EndpointsConfig: {
            'vibekraft-sandbox-network': {
              Aliases: [containerName]
            }
          }
        },

        // Labels for identification and management
        Labels: {
          'vibekraft.sandbox.id': config.id,
          'vibekraft.sandbox.name': config.name,
          'vibekraft.sandbox.userId': config.userId,
          'vibekraft.sandbox.template': config.template,
          'vibekraft.sandbox.createdAt': config.metadata.createdAt.toISOString(),
          ...config.metadata.labels
        }
      };

      // Create the container
      const container = await this.docker.createContainer(containerConfig);
      const containerId = container.id;

      // Store container reference
      this.containers.set(containerId, container);

      console.log(`Container created: ${containerId} (${containerName})`);
      this.emit('containerCreated', { containerId, sandboxId: config.id });

      return containerId;

    } catch (error) {
      console.error('Failed to create container:', error);
      this.emit('error', error);
      throw error;
    }
  }

  async startContainer(containerId: string): Promise<void> {
    try {
      const container = this.containers.get(containerId);
      if (!container) {
        throw new Error(`Container not found: ${containerId}`);
      }

      await container.start();
      console.log(`Container started: ${containerId}`);
      this.emit('containerStarted', { containerId });
    } catch (error) {
      console.error('Failed to start container:', error);
      this.emit('error', error);
      throw error;
    }
  }

  async stopContainer(containerId: string): Promise<void> {
    try {
      const container = this.containers.get(containerId);
      if (!container) {
        throw new Error(`Container not found: ${containerId}`);
      }

      await container.stop({ t: 10 }); // 10 second grace period
      console.log(`Container stopped: ${containerId}`);
      this.emit('containerStopped', { containerId });
    } catch (error) {
      console.error('Failed to stop container:', error);
      this.emit('error', error);
      throw error;
    }
  }

  async removeContainer(containerId: string): Promise<void> {
    try {
      const container = this.containers.get(containerId);
      if (!container) {
        throw new Error(`Container not found: ${containerId}`);
      }

      await container.remove({ force: true });
      this.containers.delete(containerId);
      console.log(`Container removed: ${containerId}`);
      this.emit('containerRemoved', { containerId });
    } catch (error) {
      console.error('Failed to remove container:', error);
      this.emit('error', error);
      throw error;
    }
  }

  async getContainerStatus(containerId: string): Promise<{ state: string; health: string }> {
    try {
      const container = this.containers.get(containerId);
      if (!container) {
        throw new Error(`Container not found: ${containerId}`);
      }

      const inspect = await container.inspect();
      return {
        state: inspect.State.Status,
        health: inspect.State.Health?.Status || 'none'
      };
    } catch (error) {
      console.error('Failed to get container status:', error);
      throw error;
    }
  }

  async executeCommand(containerId: string, request: ExecuteCommandRequest): Promise<ExecuteCommandResponse> {
    const startTime = Date.now();

    try {
      const container = this.containers.get(containerId);
      if (!container) {
        throw new Error(`Container not found: ${containerId}`);
      }

      // Create exec instance
      const exec = await container.exec({
        Cmd: request.command,
        AttachStdout: true,
        AttachStderr: true,
        WorkingDir: request.workingDir || '/workspace',
        User: request.user || undefined,
        Env: request.environment ? Object.entries(request.environment).map(([k, v]) => `${k}=${v}`) : undefined
      });

      // Start execution
      const stream = await exec.start({ Detach: false, Tty: false });

      let stdout = '';
      let stderr = '';

      // Handle timeout
      const timeout = request.timeout || 30000;
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Command execution timed out')), timeout);
      });

      // Collect output
      const outputPromise = new Promise<void>((resolve, reject) => {
        container.modem.demuxStream(stream,
          // stdout
          (chunk: Buffer) => { stdout += chunk.toString(); },
          // stderr
          (chunk: Buffer) => { stderr += chunk.toString(); }
        );

        stream.on('end', resolve);
        stream.on('error', reject);
      });

      // Wait for completion or timeout
      let timedOut = false;
      try {
        await Promise.race([outputPromise, timeoutPromise]);
      } catch (error) {
        if (error instanceof Error && error.message.includes('timed out')) {
          timedOut = true;
        } else {
          throw error;
        }
      }

      // Get exit code
      const inspect = await exec.inspect();
      const exitCode = inspect.ExitCode || 0;
      const duration = Date.now() - startTime;

      return {
        exitCode,
        stdout,
        stderr,
        duration,
        timedOut
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error('Failed to execute command:', error);

      return {
        exitCode: 1,
        stdout: '',
        stderr: error instanceof Error ? error.message : 'Unknown error',
        duration,
        timedOut: false
      };
    }
  }

  // Helper methods
  private async ensureNetwork(networkName: string): Promise<void> {
    try {
      const networks = await this.docker.listNetworks({ filters: { name: [networkName] } });

      if (networks.length === 0) {
        const network = await this.docker.createNetwork({
          Name: networkName,
          Driver: 'bridge',
          IPAM: {
            Driver: 'default',
            Config: [{
              Subnet: '**********/16',
              Gateway: '**********'
            }]
          },
          Options: {
            'com.docker.network.bridge.enable_icc': 'true',
            'com.docker.network.bridge.enable_ip_masquerade': 'true'
          }
        });

        this.networks.set(networkName, this.docker.getNetwork(network.id));
        console.log(`Created network: ${networkName}`);
      } else {
        const network = this.docker.getNetwork(networks[0].Id);
        this.networks.set(networkName, network);
        console.log(`Using existing network: ${networkName}`);
      }
    } catch (error) {
      console.error(`Failed to ensure network ${networkName}:`, error);
      throw error;
    }
  }

  private buildEnvironmentVariables(env: Record<string, string>): string[] {
    return Object.entries(env).map(([key, value]) => `${key}=${value}`);
  }

  private buildPortBindings(ports: { containerPort: number; hostPort?: number; protocol: 'tcp' | 'udp' }[]): Record<string, any> {
    const bindings: Record<string, any> = {};

    for (const port of ports) {
      const containerPort = `${port.containerPort}/${port.protocol || 'tcp'}`;
      bindings[containerPort] = [{ HostPort: port.hostPort?.toString() || '' }];
    }

    return bindings;
  }

  async cleanup(): Promise<void> {
    try {
      // Stop and remove all containers
      const containerPromises = Array.from(this.containers.keys()).map(async (containerId) => {
        try {
          await this.stopContainer(containerId);
          await this.removeContainer(containerId);
        } catch (error) {
          console.error(`Failed to cleanup container ${containerId}:`, error);
        }
      });

      await Promise.all(containerPromises);

      this.containers.clear();
      this.networks.clear();
      this.volumes.clear();

      console.log('Docker manager cleanup completed');
      this.emit('cleanup');
    } catch (error) {
      console.error('Failed to cleanup Docker manager:', error);
      this.emit('error', error);
      throw error;
    }
  }
}

export class SandboxManager extends EventEmitter {
  private dockerManager: ProductionDockerManager;
  private templateManager: OptimizedTemplateManager;
  private sandboxes: Map<string, SandboxConfig> = new Map();
  private initialized = false;
  private options: SandboxManagerOptions;

  constructor(options: SandboxManagerOptions = {}) {
    super();
    this.options = {
      maxConcurrentSandboxes: 100,
      defaultResourceLimits: {
        memory: 2 * 1024 * 1024 * 1024, // 2GB
        cpus: 2,
        diskQuota: 10 * 1024 * 1024 * 1024 // 10GB
      },
      networkConfig: {
        defaultNetwork: 'vibekraft-sandbox-network',
        enableNetworking: true
      },
      securityConfig: {
        allowPrivileged: false,
        defaultUser: '1000:1000',
        readOnlyRootfs: false
      },
      enableLogging: true,
      enableMonitoring: true,
      enableImageOptimization: true,
      autoOptimizeImages: false,
      ...options
    };

    this.dockerManager = new ProductionDockerManager(options.docker);
    this.templateManager = new OptimizedTemplateManager();
  }

  /**
   * Initialize the sandbox manager
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.dockerManager.initialize();
      this.initialized = true;
      console.log('SandboxManager initialized successfully');
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize SandboxManager:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Get docker manager
   */
  getDockerManager(): ProductionDockerManager {
    return this.dockerManager;
  }

  /**
   * Create a new sandbox
   */
  async createSandbox(config: Partial<SandboxConfig>): Promise<string> {
    try {
      // Check if we've reached the maximum number of concurrent sandboxes
      if (this.sandboxes.size >= this.options.maxConcurrentSandboxes!) {
        throw new Error('Maximum concurrent sandboxes limit reached');
      }

      // Generate sandbox ID
      const sandboxId = this.generateSandboxId();

      // Build complete configuration with defaults
      const fullConfig: SandboxConfig = this.buildSandboxConfig(sandboxId, config);

      // Store sandbox configuration
      this.sandboxes.set(sandboxId, fullConfig);

      // Create container
      const containerId = await this.dockerManager.createContainer(fullConfig);

      // Start container
      await this.dockerManager.startContainer(containerId);

      console.log(`Sandbox created and started: ${sandboxId}`);
      this.emit('sandboxCreated', { sandboxId, containerId });

      return sandboxId;

    } catch (error) {
      console.error('Failed to create sandbox:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Delete a sandbox
   */
  async deleteSandbox(sandboxId: string): Promise<void> {
    try {
      const config = this.sandboxes.get(sandboxId);
      if (!config) {
        throw new Error(`Sandbox not found: ${sandboxId}`);
      }

      const containerId = `vibekraft-${sandboxId}`;

      // Stop and remove container
      try {
        await this.dockerManager.stopContainer(containerId);
      } catch (error) {
        console.warn(`Failed to stop container ${containerId}:`, error);
      }

      try {
        await this.dockerManager.removeContainer(containerId);
      } catch (error) {
        console.warn(`Failed to remove container ${containerId}:`, error);
      }

      // Remove from our tracking
      this.sandboxes.delete(sandboxId);

      console.log(`Sandbox deleted: ${sandboxId}`);
      this.emit('sandboxDeleted', { sandboxId });

    } catch (error) {
      console.error('Failed to delete sandbox:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * List all sandboxes
   */
  async listSandboxes(): Promise<SandboxConfig[]> {
    return Array.from(this.sandboxes.values());
  }

  /**
   * Get sandbox configuration
   */
  getSandboxConfig(sandboxId: string): SandboxConfig {
    const config = this.sandboxes.get(sandboxId);
    if (!config) {
      throw new Error(`Sandbox not found: ${sandboxId}`);
    }
    return config;
  }

  /**
   * Execute command in sandbox
   */
  async executeCommand(sandboxId: string, request: ExecuteCommandRequest): Promise<ExecuteCommandResponse> {
    const containerId = `vibekraft-${sandboxId}`;
    return await this.dockerManager.executeCommand(containerId, request);
  }

  /**
   * Get sandbox status
   */
  async getSandboxStatus(sandboxId: string): Promise<SandboxStatus> {
    const containerId = `vibekraft-${sandboxId}`;

    try {
      const containerStatus = await this.dockerManager.getContainerStatus(containerId);

      return {
        id: sandboxId,
        state: this.mapContainerState(containerStatus.state),
        health: {
          status: containerStatus.health === 'healthy' ? 'healthy' : 'unhealthy',
          checks: [],
          lastChecked: new Date()
        },
        resources: {
          cpu: { usage: 0, cores: 1, throttled: false },
          memory: { usage: 0, limit: 1024 * 1024 * 1024, percentage: 0, swap: 0 },
          disk: { usage: 0, limit: 10 * 1024 * 1024 * 1024, percentage: 0, iops: 0 },
          network: { bytesIn: 0, bytesOut: 0, packetsIn: 0, packetsOut: 0, connections: 0 }
        },
        network: {
          interfaces: [],
          ports: [],
          connections: []
        },
        processes: [],
        logs: []
      };
    } catch (error) {
      return {
        id: sandboxId,
        state: 'error',
        health: {
          status: 'unhealthy',
          checks: [],
          lastChecked: new Date()
        },
        resources: {
          cpu: { usage: 0, cores: 1, throttled: false },
          memory: { usage: 0, limit: 1024 * 1024 * 1024, percentage: 0, swap: 0 },
          disk: { usage: 0, limit: 10 * 1024 * 1024 * 1024, percentage: 0, iops: 0 },
          network: { bytesIn: 0, bytesOut: 0, packetsIn: 0, packetsOut: 0, connections: 0 }
        },
        network: {
          interfaces: [],
          ports: [],
          connections: []
        },
        processes: [],
        logs: []
      };
    }
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    try {
      await this.dockerManager.cleanup();
      this.sandboxes.clear();
      this.initialized = false;
      console.log('SandboxManager cleanup completed');
      this.emit('cleanup');
    } catch (error) {
      console.error('Failed to cleanup SandboxManager:', error);
      this.emit('error', error);
      throw error;
    }
  }

  // Private helper methods
  private generateSandboxId(): string {
    return `sb-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  }

  private buildSandboxConfig(sandboxId: string, config: Partial<SandboxConfig>): SandboxConfig {
    const now = new Date();

    // Optimize template selection if enabled
    let template = config.template || 'ubuntu:latest';
    if (this.options.enableImageOptimization) {
      template = this.getOptimizedTemplate(template, config);
    }

    return {
      id: sandboxId,
      name: config.name || `Sandbox ${sandboxId}`,
      userId: config.userId || 'anonymous',
      template,
      resources: {
        vcpuCount: config.resources?.vcpuCount || this.options.defaultResourceLimits!.cpus!,
        cpuShares: config.resources?.cpuShares || 1024,
        memSizeMib: config.resources?.memSizeMib || (this.options.defaultResourceLimits!.memory! / (1024 * 1024)),
        diskSizeGb: config.resources?.diskSizeGb || (this.options.defaultResourceLimits!.diskQuota! / (1024 * 1024 * 1024)),
        diskType: config.resources?.diskType || 'ssd',
        maxNetworkConnections: config.resources?.maxNetworkConnections || 100
      },
      environment: config.environment || {},
      network: {
        ports: config.network?.ports || [],
        isolated: config.network?.isolated || false,
        bandwidthLimitMbps: config.network?.bandwidthLimitMbps || 100
      },
      security: {
        runAsUser: config.security?.runAsUser || 1000,
        runAsGroup: config.security?.runAsGroup || 1000,
        allowSudo: config.security?.allowSudo || false,
        allowNetworkAccess: config.security?.allowNetworkAccess !== false,
        allowFileSystemAccess: config.security?.allowFileSystemAccess !== false,
        readOnlyRootfs: config.security?.readOnlyRootfs || this.options.securityConfig!.readOnlyRootfs!,
        noNewPrivileges: config.security?.noNewPrivileges !== false,
        capabilities: {
          drop: config.security?.capabilities?.drop || ['ALL'],
          add: config.security?.capabilities?.add || []
        }
      },
      metadata: {
        createdAt: now,
        updatedAt: now,
        templateVersion: config.metadata?.templateVersion || '1.0.0',
        templateSource: config.metadata?.templateSource || 'default',
        labels: config.metadata?.labels || {},
        annotations: config.metadata?.annotations || {},
        totalUptime: 0,
        totalCpuTime: 0,
        totalMemoryUsage: 0,
        totalNetworkTraffic: 0
      }
    };
  }

  private mapContainerState(containerState: string): SandboxState {
    switch (containerState.toLowerCase()) {
      case 'running': return 'running';
      case 'stopped': return 'stopped';
      case 'created': return 'starting';
      case 'exited': return 'stopped';
      case 'dead': return 'error';
      default: return 'error';
    }
  }

  /**
   * Get optimized template based on requirements
   */
  private getOptimizedTemplate(requestedTemplate: string, config: Partial<SandboxConfig>): string {
    // Map common templates to optimized versions
    const templateMappings: Record<string, string> = {
      'node:latest': 'vibekraft/nodejs-minimal:latest',
      'node:18': 'vibekraft/nodejs-minimal:latest',
      'node:18-alpine': 'vibekraft/nodejs-minimal:latest',
      'python:latest': 'vibekraft/python-minimal:latest',
      'python:3.11': 'vibekraft/python-minimal:latest',
      'python:3.11-alpine': 'vibekraft/python-minimal:latest',
      'rust:latest': 'vibekraft/rust-dev:latest',
      'rust:1.70': 'vibekraft/rust-dev:latest',
      'ubuntu:latest': 'vibekraft/fullstack-dev:latest',
      'ubuntu:22.04': 'vibekraft/fullstack-dev:latest'
    };

    // Check if we have an optimized version
    if (templateMappings[requestedTemplate]) {
      return templateMappings[requestedTemplate];
    }

    // Try to find a suitable template based on requirements
    const requirements = this.extractRequirements(config);
    const recommendedTemplates = this.templateManager.getRecommendedTemplates(requirements);

    if (recommendedTemplates.length > 0) {
      // Return the most optimized (smallest) template that meets requirements
      return recommendedTemplates[0].optimizedImage;
    }

    // Fall back to original template
    return requestedTemplate;
  }

  /**
   * Extract requirements from sandbox config
   */
  private extractRequirements(config: Partial<SandboxConfig>): any {
    const requirements: any = {
      maxSize: 'standard' as const
    };

    // Determine required languages based on environment variables or template
    const languages: string[] = [];
    const packageManagers: string[] = [];

    if (config.template?.includes('node')) {
      languages.push('javascript', 'typescript');
      packageManagers.push('npm');
    }
    if (config.template?.includes('python')) {
      languages.push('python');
      packageManagers.push('pip');
    }
    if (config.template?.includes('rust')) {
      languages.push('rust');
      packageManagers.push('cargo');
    }

    // Check environment variables for hints
    if (config.environment) {
      if (config.environment['NODE_ENV']) {
        languages.push('javascript', 'typescript');
        packageManagers.push('npm');
      }
      if (config.environment['PYTHON_VERSION']) {
        languages.push('python');
        packageManagers.push('pip');
      }
    }

    // Determine size preference based on resource limits
    if (config.resources) {
      const memoryMB = config.resources.memSizeMib || 0;
      if (memoryMB <= 512) {
        requirements.maxSize = 'minimal';
      } else if (memoryMB <= 2048) {
        requirements.maxSize = 'standard';
      } else {
        requirements.maxSize = 'full';
      }
    }

    if (languages.length > 0) requirements.languages = languages;
    if (packageManagers.length > 0) requirements.packageManagers = packageManagers;

    return requirements;
  }

  /**
   * Get available optimized templates
   */
  getAvailableTemplates() {
    return this.templateManager.getTemplates();
  }

  /**
   * Build optimized templates
   */
  async buildOptimizedTemplates(): Promise<void> {
    if (!this.options.enableImageOptimization) {
      console.log('Image optimization is disabled');
      return;
    }

    console.log('Building optimized templates...');
    try {
      await this.templateManager.buildAllTemplates();
      console.log('All optimized templates built successfully');
    } catch (error) {
      console.error('Failed to build optimized templates:', error);
      throw error;
    }
  }

  /**
   * Get template size comparison
   */
  async getTemplateSizeComparison() {
    return await this.templateManager.getTemplateSizeComparison();
  }
}
