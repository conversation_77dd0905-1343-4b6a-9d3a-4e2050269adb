/**
 * VibeKraft Optimized Templates
 * 
 * Pre-built optimized Docker templates for common development environments
 */

import { ImageOptimizer, ImageOptimizationConfig } from './image-optimizer';

export interface OptimizedTemplate {
  id: string;
  name: string;
  description: string;
  baseImage: string;
  optimizedImage: string;
  size: 'minimal' | 'standard' | 'full';
  languages: string[];
  packageManagers: string[];
  preInstalled: string[];
  optimizationConfig: ImageOptimizationConfig;
}

export class OptimizedTemplateManager {
  private imageOptimizer: ImageOptimizer;
  private templates: Map<string, OptimizedTemplate> = new Map();

  constructor() {
    this.imageOptimizer = new ImageOptimizer();
    this.initializeTemplates();
  }

  /**
   * Initialize pre-defined optimized templates
   */
  private initializeTemplates(): void {
    // Minimal Node.js template (< 200MB)
    this.templates.set('nodejs-minimal', {
      id: 'nodejs-minimal',
      name: 'Node.js Minimal',
      description: 'Minimal Node.js environment with npm',
      baseImage: 'node:18-alpine',
      optimizedImage: 'vibekraft/nodejs-minimal:latest',
      size: 'minimal',
      languages: ['javascript', 'typescript'],
      packageManagers: ['npm'],
      preInstalled: ['node', 'npm'],
      optimizationConfig: {
        baseImage: 'node:18-alpine',
        targetImage: 'vibekraft/nodejs-minimal:latest',
        optimizations: {
          multiStage: true,
          minifyLayers: true,
          removeCache: true,
          compressLayers: true,
          removeDocumentation: true,
          optimizePackageManager: true
        },
        packageManagers: { npm: true },
        customOptimizations: [
          '# Remove unnecessary Node.js modules',
          'RUN npm cache clean --force',
          'RUN rm -rf /usr/local/lib/node_modules/npm/docs',
          'RUN rm -rf /usr/local/lib/node_modules/npm/man'
        ]
      }
    });

    // Python minimal template (< 150MB)
    this.templates.set('python-minimal', {
      id: 'python-minimal',
      name: 'Python Minimal',
      description: 'Minimal Python environment with pip',
      baseImage: 'python:3.11-alpine',
      optimizedImage: 'vibekraft/python-minimal:latest',
      size: 'minimal',
      languages: ['python'],
      packageManagers: ['pip'],
      preInstalled: ['python3', 'pip'],
      optimizationConfig: {
        baseImage: 'python:3.11-alpine',
        targetImage: 'vibekraft/python-minimal:latest',
        optimizations: {
          multiStage: true,
          minifyLayers: true,
          removeCache: true,
          compressLayers: true,
          removeDocumentation: true,
          optimizePackageManager: true
        },
        packageManagers: { pip: true },
        customOptimizations: [
          '# Remove Python cache and docs',
          'RUN find /usr/local -depth \\( -type d -a -name test -o -name tests \\) -exec rm -rf {} +',
          'RUN find /usr/local -name "*.pyc" -delete',
          'RUN find /usr/local -name "*.pyo" -delete'
        ]
      }
    });

    // Full-stack development template (< 800MB)
    this.templates.set('fullstack-dev', {
      id: 'fullstack-dev',
      name: 'Full-Stack Development',
      description: 'Complete development environment with Node.js, Python, and tools',
      baseImage: 'ubuntu:22.04',
      optimizedImage: 'vibekraft/fullstack-dev:latest',
      size: 'standard',
      languages: ['javascript', 'typescript', 'python', 'bash'],
      packageManagers: ['npm', 'pip', 'apt'],
      preInstalled: ['node', 'npm', 'python3', 'pip', 'git', 'curl', 'wget', 'vim'],
      optimizationConfig: {
        baseImage: 'ubuntu:22.04',
        targetImage: 'vibekraft/fullstack-dev:latest',
        optimizations: {
          multiStage: true,
          minifyLayers: true,
          removeCache: true,
          compressLayers: true,
          removeDocumentation: true,
          optimizePackageManager: true
        },
        packageManagers: { npm: true, pip: true },
        customOptimizations: [
          '# Install development tools efficiently',
          'RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash -',
          'RUN apt-get update && apt-get install -y --no-install-recommends \\',
          '    nodejs python3 python3-pip git curl wget vim nano \\',
          '    build-essential && \\',
          '    npm install -g typescript @types/node && \\',
          '    pip3 install --no-cache-dir virtualenv pipenv && \\',
          '    apt-get clean && rm -rf /var/lib/apt/lists/*'
        ]
      }
    });

    // Rust development template (< 400MB)
    this.templates.set('rust-dev', {
      id: 'rust-dev',
      name: 'Rust Development',
      description: 'Optimized Rust development environment',
      baseImage: 'rust:1.70-alpine',
      optimizedImage: 'vibekraft/rust-dev:latest',
      size: 'standard',
      languages: ['rust'],
      packageManagers: ['cargo'],
      preInstalled: ['rustc', 'cargo'],
      optimizationConfig: {
        baseImage: 'rust:1.70-alpine',
        targetImage: 'vibekraft/rust-dev:latest',
        optimizations: {
          multiStage: true,
          minifyLayers: true,
          removeCache: true,
          compressLayers: true,
          stripDebugSymbols: true,
          removeDocumentation: true,
          optimizePackageManager: true
        },
        packageManagers: { cargo: true },
        customOptimizations: [
          '# Optimize Rust installation',
          'RUN rustup component remove rust-docs',
          'RUN rm -rf /usr/local/cargo/registry/cache',
          'RUN rm -rf /usr/local/cargo/git/db'
        ]
      }
    });

    // Data Science template (< 600MB)
    this.templates.set('datascience', {
      id: 'datascience',
      name: 'Data Science',
      description: 'Python data science environment with common libraries',
      baseImage: 'python:3.11-slim',
      optimizedImage: 'vibekraft/datascience:latest',
      size: 'standard',
      languages: ['python', 'jupyter'],
      packageManagers: ['pip'],
      preInstalled: ['python3', 'pip', 'jupyter', 'pandas', 'numpy', 'matplotlib'],
      optimizationConfig: {
        baseImage: 'python:3.11-slim',
        targetImage: 'vibekraft/datascience:latest',
        optimizations: {
          multiStage: true,
          minifyLayers: true,
          removeCache: true,
          compressLayers: true,
          removeDocumentation: true,
          optimizePackageManager: true
        },
        packageManagers: { pip: true },
        customOptimizations: [
          '# Install data science packages efficiently',
          'RUN pip install --no-cache-dir \\',
          '    jupyter pandas numpy matplotlib seaborn scikit-learn \\',
          '    plotly requests beautifulsoup4 && \\',
          '    jupyter notebook --generate-config && \\',
          '    find /usr/local -name "*.pyc" -delete'
        ]
      }
    });
  }

  /**
   * Get all available templates
   */
  getTemplates(): OptimizedTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Get template by ID
   */
  getTemplate(templateId: string): OptimizedTemplate | undefined {
    return this.templates.get(templateId);
  }

  /**
   * Build optimized template
   */
  async buildTemplate(templateId: string): Promise<string> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    console.log(`Building optimized template: ${template.name}`);
    
    // Build the optimized image
    const imageName = await this.imageOptimizer.buildOptimizedImage(template.optimizationConfig);
    
    // Analyze the built image
    const analysis = await this.imageOptimizer.analyzeImage(imageName);
    console.log(`Template built successfully:`, {
      name: template.name,
      image: imageName,
      size: `${Math.round(analysis.size / 1024 / 1024)}MB`,
      layers: analysis.layers
    });

    return imageName;
  }

  /**
   * Build all templates
   */
  async buildAllTemplates(): Promise<Map<string, string>> {
    const results = new Map<string, string>();
    
    for (const [templateId, template] of this.templates) {
      try {
        const imageName = await this.buildTemplate(templateId);
        results.set(templateId, imageName);
      } catch (error) {
        console.error(`Failed to build template ${templateId}:`, error);
        throw error;
      }
    }
    
    return results;
  }

  /**
   * Get template recommendations based on requirements
   */
  getRecommendedTemplates(requirements: {
    languages?: string[];
    packageManagers?: string[];
    maxSize?: 'minimal' | 'standard' | 'full';
  }): OptimizedTemplate[] {
    const templates = this.getTemplates();
    
    return templates.filter(template => {
      // Filter by languages
      if (requirements.languages) {
        const hasLanguage = requirements.languages.some(lang => 
          template.languages.includes(lang)
        );
        if (!hasLanguage) return false;
      }
      
      // Filter by package managers
      if (requirements.packageManagers) {
        const hasPackageManager = requirements.packageManagers.some(pm => 
          template.packageManagers.includes(pm)
        );
        if (!hasPackageManager) return false;
      }
      
      // Filter by size
      if (requirements.maxSize) {
        const sizeOrder = { minimal: 0, standard: 1, full: 2 };
        if (sizeOrder[template.size] > sizeOrder[requirements.maxSize]) {
          return false;
        }
      }
      
      return true;
    });
  }

  /**
   * Create custom optimized template
   */
  async createCustomTemplate(
    templateId: string,
    name: string,
    description: string,
    config: ImageOptimizationConfig
  ): Promise<OptimizedTemplate> {
    const template: OptimizedTemplate = {
      id: templateId,
      name,
      description,
      baseImage: config.baseImage,
      optimizedImage: config.targetImage,
      size: 'standard',
      languages: [],
      packageManagers: Object.keys(config.packageManagers || {}),
      preInstalled: [],
      optimizationConfig: config
    };
    
    this.templates.set(templateId, template);
    
    // Build the template
    await this.buildTemplate(templateId);
    
    return template;
  }

  /**
   * Get template size comparison
   */
  async getTemplateSizeComparison(): Promise<any[]> {
    const comparisons = [];
    
    for (const template of this.templates.values()) {
      try {
        const analysis = await this.imageOptimizer.analyzeImage(template.optimizedImage);
        comparisons.push({
          name: template.name,
          optimizedSize: Math.round(analysis.size / 1024 / 1024),
          layers: analysis.layers,
          languages: template.languages,
          packageManagers: template.packageManagers
        });
      } catch (error) {
        // Template not built yet
        comparisons.push({
          name: template.name,
          optimizedSize: 'Not built',
          layers: 'Unknown',
          languages: template.languages,
          packageManagers: template.packageManagers
        });
      }
    }
    
    return comparisons.sort((a, b) => {
      if (typeof a.optimizedSize === 'number' && typeof b.optimizedSize === 'number') {
        return a.optimizedSize - b.optimizedSize;
      }
      return 0;
    });
  }
}
