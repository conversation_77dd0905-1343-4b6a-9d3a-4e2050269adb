
# VibeKraft Full-Stack Development - Optimized multi-language environment
FROM ubuntu:22.04

# Prevent interactive prompts
ENV DEBIAN_FRONTEND=noninteractive

# Create non-root user
RUN groupadd -r vibekraft && useradd -r -g vibekraft -u 1001 vibekraft

# Configure package manager for minimal installs
RUN echo 'APT::Install-Recommends "0";' >> /etc/apt/apt.conf.d/01norecommend \
    && echo 'APT::Install-Suggests "0";' >> /etc/apt/apt.conf.d/01norecommend

# Install Node.js repository and update packages
RUN apt-get update \
    && apt-get install -y --no-install-recommends curl ca-certificates gnupg \
    && curl -fsSL https://deb.nodesource.com/setup_18.x | bash -

# Install all dependencies in a single layer
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    wget \
    git \
    vim \
    nano \
    nodejs \
    python3 \
    python3-pip \
    python3-venv \
    build-essential \
    && npm install -g typescript @types/node \
    && pip3 install --no-cache-dir virtualenv requests \
    && apt-get autoremove -y \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/* \
    && rm -rf /usr/share/doc/* \
    && rm -rf /usr/share/man/* \
    && find /var/log -type f -delete \
    && npm cache clean --force

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Set environment variables
ENV NODE_ENV=development
ENV PYTHONPATH=/workspace
ENV PATH="/workspace/node_modules/.bin:$PATH"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node --version && python3 --version || exit 1

CMD ["/bin/bash"]
