
# VibeKraft Python Minimal - Optimized for size and performance
FROM python:3.11-alpine

# Create non-root user (handle existing group gracefully)
RUN addgroup -g 1001 vibekraft 2>/dev/null || true \
    && adduser -D -s /bin/sh -u 1001 -G vibekraft vibekraft 2>/dev/null || adduser -D -s /bin/sh -u 1001 vibekraft

# Install runtime dependencies and common Python packages
RUN apk add --no-cache ca-certificates \
    && pip install --no-cache-dir \
        virtualenv \
        requests \
        click \
    && find /usr/local -name "*.pyc" -delete \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Configure pip for optimization
RUN pip config set global.cache-dir /tmp/pip-cache \
    && pip config set global.no-cache-dir true

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD python --version || exit 1

CMD ["/bin/sh"]
