
# VibeKraft Rust Development - Optimized for compilation speed
FROM rust:1.70-alpine as builder

# Install build dependencies
RUN apk add --no-cache \
    build-base \
    curl \
    wget \
    git \
    ca-certificates

# Configure Rust for optimization
RUN rustup component remove rust-docs \
    && rustup component add clippy rustfmt

# Stage 2: Runtime environment
FROM rust:1.70-alpine as runtime

# Create non-root user
RUN addgroup -g 1000 vibekraft \
    && adduser -D -s /bin/sh -u 1000 -G vibekraft vibekraft

# Install runtime dependencies
RUN apk add --no-cache \
    ca-certificates \
    build-base \
    && rm -rf /var/cache/apk/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Copy Rust tools from builder
COPY --from=builder /usr/local/cargo /usr/local/cargo
COPY --from=builder /usr/local/rustup /usr/local/rustup

# Clean up Rust installation
RUN rm -rf /usr/local/cargo/registry/cache \
    && rm -rf /usr/local/cargo/git/db

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Set Rust environment
ENV PATH="/usr/local/cargo/bin:$PATH"
ENV CARGO_HOME="/usr/local/cargo"
ENV RUSTUP_HOME="/usr/local/rustup"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD rustc --version || exit 1

CMD ["/bin/sh"]
