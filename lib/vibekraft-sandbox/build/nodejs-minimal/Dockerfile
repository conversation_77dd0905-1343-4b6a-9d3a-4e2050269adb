
# VibeKraft Node.js Minimal - Optimized for size and performance
FROM node:18-alpine

# Create non-root user (handle existing group gracefully)
RUN addgroup -g 1001 vibekraft 2>/dev/null || true \
    && adduser -D -s /bin/sh -u 1001 -G vibekraft vibekraft 2>/dev/null || adduser -D -s /bin/sh -u 1001 vibekraft

# Install runtime dependencies and TypeScript
RUN apk add --no-cache ca-certificates \
    && npm install -g typescript @types/node \
    && npm cache clean --force \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node --version || exit 1

CMD ["/bin/sh"]
