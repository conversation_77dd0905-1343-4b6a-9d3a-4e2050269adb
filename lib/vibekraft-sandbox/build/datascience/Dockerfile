
# VibeKraft Data Science - Optimized Python environment with ML libraries
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    wget \
    git

# Install data science packages
RUN pip install --no-cache-dir \
    jupyter \
    pandas \
    numpy \
    matplotlib \
    seaborn \
    scikit-learn \
    plotly \
    requests \
    beautifulsoup4

# Stage 2: Runtime environment
FROM python:3.11-slim as runtime

# Create non-root user
RUN groupadd -r vibekraft && useradd -r -g vibekraft -u 1000 vibekraft

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/* \
    && find /usr/local -name "*.pyc" -delete

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin/jupyter /usr/local/bin/jupyter

# Configure Jupyter
RUN jupyter notebook --generate-config

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD python --version && jupyter --version || exit 1

CMD ["/bin/bash"]
