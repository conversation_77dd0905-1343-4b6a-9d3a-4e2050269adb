/**
 * VibeKraft Network Management React Hook
 * 
 * React hook for network configuration and management within sandbox containers
 */

import { useState, useCallback, useEffect } from 'react';
import { 
  NetworkInterface, 
  NetworkPort, 
  NetworkRule,
  NetworkStatusDetailed,
  NetworkConnection,
  NetworkConfigRequest,
  NetworkResponse
} from '../types/sandbox';

export interface UseNetworkOptions {
  sandboxId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface ConnectivityTestResult {
  success: boolean;
  latency?: number;
  error?: string;
  target: string;
  port?: number;
  timestamp: string;
}

export function useNetwork({
  sandboxId,
  autoRefresh = false,
  refreshInterval = 10000 // 10 seconds
}: UseNetworkOptions) {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatusDetailed | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastOperation, setLastOperation] = useState<NetworkResponse | null>(null);

  // Get network status
  const getNetworkStatus = useCallback(async (): Promise<NetworkStatusDetailed | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network?operation=status`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get network status');
      }

      const data = await response.json();
      setNetworkStatus(data);
      
      return data;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId]);

  // Configure network settings
  const configureNetwork = useCallback(async (
    config: NetworkConfigRequest
  ): Promise<NetworkResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'configure',
          ...config
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to configure network');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh network status if configuration was successful
      if (result.success) {
        await getNetworkStatus();
      }
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, getNetworkStatus]);

  // Add port mapping
  const addPortMapping = useCallback(async (
    port: Omit<NetworkPort, 'state' | 'processId' | 'processName'>
  ): Promise<NetworkResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'add-port',
          ...port
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add port mapping');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh network status if operation was successful
      if (result.success) {
        await getNetworkStatus();
      }
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, getNetworkStatus]);

  // Remove port mapping
  const removePortMapping = useCallback(async (
    containerPort: number,
    protocol: 'tcp' | 'udp' = 'tcp'
  ): Promise<NetworkResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        operation: 'remove-port',
        containerPort: containerPort.toString(),
        protocol
      });

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network?${params}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove port mapping');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh network status if operation was successful
      if (result.success) {
        await getNetworkStatus();
      }
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, getNetworkStatus]);

  // Add firewall rule
  const addFirewallRule = useCallback(async (
    rule: Omit<NetworkRule, 'id'>
  ): Promise<NetworkResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'add-firewall-rule',
          ...rule
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add firewall rule');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh network status if operation was successful
      if (result.success) {
        await getNetworkStatus();
      }
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, getNetworkStatus]);

  // Test connectivity
  const testConnectivity = useCallback(async (
    target: string,
    port?: number
  ): Promise<ConnectivityTestResult | null> => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        operation: 'test',
        target,
        ...(port && { port: port.toString() })
      });

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to test connectivity');
      }

      const result = await response.json();
      return {
        success: result.success,
        latency: result.latency,
        error: result.error,
        target: result.target,
        port: result.port,
        timestamp: result.timestamp
      };
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId]);

  // Enable/disable internet access
  const setInternetAccess = useCallback(async (enabled: boolean): Promise<NetworkResponse | null> => {
    return await configureNetwork({ internetAccess: enabled });
  }, [configureNetwork]);

  // Enable/disable inbound access
  const setInboundAccess = useCallback(async (enabled: boolean): Promise<NetworkResponse | null> => {
    return await configureNetwork({ inboundAccess: enabled });
  }, [configureNetwork]);

  // Configure DNS servers
  const setDnsServers = useCallback(async (dnsServers: string[]): Promise<NetworkResponse | null> => {
    return await configureNetwork({ dnsServers });
  }, [configureNetwork]);

  // Configure host filtering
  const setHostFiltering = useCallback(async (
    allowedHosts?: string[],
    blockedHosts?: string[]
  ): Promise<NetworkResponse | null> => {
    return await configureNetwork({ allowedHosts, blockedHosts });
  }, [configureNetwork]);

  // Configure bandwidth limits
  const setBandwidthLimits = useCallback(async (
    uploadLimit?: number,
    downloadLimit?: number
  ): Promise<NetworkResponse | null> => {
    return await configureNetwork({
      bandwidth: { uploadLimit, downloadLimit }
    });
  }, [configureNetwork]);

  // Convenience methods for common operations
  const openPort = useCallback(async (
    port: number,
    protocol: 'tcp' | 'udp' = 'tcp',
    description?: string
  ): Promise<NetworkResponse | null> => {
    return await addPortMapping({
      containerPort: port,
      protocol,
      description: description || `Port ${port}/${protocol}`,
      public: false
    });
  }, [addPortMapping]);

  const closePort = useCallback(async (
    port: number,
    protocol: 'tcp' | 'udp' = 'tcp'
  ): Promise<NetworkResponse | null> => {
    return await removePortMapping(port, protocol);
  }, [removePortMapping]);

  const allowTraffic = useCallback(async (
    direction: 'inbound' | 'outbound',
    protocol: 'tcp' | 'udp' | 'icmp' | 'all' = 'tcp',
    port?: number,
    ip?: string
  ): Promise<NetworkResponse | null> => {
    return await addFirewallRule({
      type: 'allow',
      direction,
      protocol,
      destinationPort: port,
      destinationIp: ip,
      enabled: true,
      priority: 50,
      description: `Allow ${direction} ${protocol}${port ? ` on port ${port}` : ''}${ip ? ` from ${ip}` : ''}`
    });
  }, [addFirewallRule]);

  const blockTraffic = useCallback(async (
    direction: 'inbound' | 'outbound',
    protocol: 'tcp' | 'udp' | 'icmp' | 'all' = 'tcp',
    port?: number,
    ip?: string
  ): Promise<NetworkResponse | null> => {
    return await addFirewallRule({
      type: 'deny',
      direction,
      protocol,
      destinationPort: port,
      destinationIp: ip,
      enabled: true,
      priority: 50,
      description: `Block ${direction} ${protocol}${port ? ` on port ${port}` : ''}${ip ? ` from ${ip}` : ''}`
    });
  }, [addFirewallRule]);

  // Get specific network information
  const getInterfaces = useCallback((): NetworkInterface[] => {
    return networkStatus?.interfaces || [];
  }, [networkStatus]);

  const getPorts = useCallback((): NetworkPort[] => {
    return networkStatus?.ports || [];
  }, [networkStatus]);

  const getConnections = useCallback((): NetworkConnection[] => {
    return networkStatus?.connections || [];
  }, [networkStatus]);

  const isPortOpen = useCallback((port: number): boolean => {
    return networkStatus?.ports.some(p => p.containerPort === port && p.state === 'listening') || false;
  }, [networkStatus]);

  const hasInternetAccess = useCallback((): boolean => {
    return networkStatus?.internetConnectivity || false;
  }, [networkStatus]);

  const isFirewallEnabled = useCallback((): boolean => {
    return networkStatus?.firewallEnabled || false;
  }, [networkStatus]);

  // Refresh network status
  const refresh = useCallback(async () => {
    await getNetworkStatus();
  }, [getNetworkStatus]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(refresh, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refresh]);

  // Initial load
  useEffect(() => {
    refresh();
  }, [sandboxId]);

  return {
    // State
    networkStatus,
    loading,
    error,
    lastOperation,
    
    // Core operations
    getNetworkStatus,
    configureNetwork,
    addPortMapping,
    removePortMapping,
    addFirewallRule,
    testConnectivity,
    
    // Configuration shortcuts
    setInternetAccess,
    setInboundAccess,
    setDnsServers,
    setHostFiltering,
    setBandwidthLimits,
    
    // Convenience methods
    openPort,
    closePort,
    allowTraffic,
    blockTraffic,
    
    // Information getters
    getInterfaces,
    getPorts,
    getConnections,
    isPortOpen,
    hasInternetAccess,
    isFirewallEnabled,
    
    // Utilities
    refresh
  };
}
