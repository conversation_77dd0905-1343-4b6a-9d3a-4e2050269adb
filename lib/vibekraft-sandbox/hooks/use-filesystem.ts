/**
 * VibeKraft Filesystem React Hook
 * 
 * React hook for filesystem operations within sandbox containers
 */

import { useState, useCallback } from 'react';
import { FileSystemItem, DirectoryListing, FileContent, FileSystemResponse } from '../types/sandbox';

export interface UseFilesystemOptions {
  sandboxId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface FileOperationRequest {
  operation: 'write' | 'mkdir' | 'move' | 'copy';
  path?: string;
  source?: string;
  destination?: string;
  content?: string;
  encoding?: 'utf8' | 'base64' | 'binary';
  permissions?: string;
  overwrite?: boolean;
  createDirectories?: boolean;
}

export function useFilesystem({ sandboxId, autoRefresh = false, refreshInterval = 5000 }: UseFilesystemOptions) {
  const [currentPath, setCurrentPath] = useState('/workspace');
  const [files, setFiles] = useState<FileSystemItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // List directory contents
  const listDirectory = useCallback(async (
    path: string = currentPath,
    options: {
      recursive?: boolean;
      includeHidden?: boolean;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<DirectoryListing | null> => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        operation: 'list',
        path,
        recursive: options.recursive?.toString() || 'false',
        includeHidden: options.includeHidden?.toString() || 'false',
        sortBy: options.sortBy || 'name',
        sortOrder: options.sortOrder || 'asc'
      });

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to list directory');
      }

      const data = await response.json();
      setFiles(data.files);
      setCurrentPath(data.path);
      
      return data;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentPath]);

  // Read file content
  const readFile = useCallback(async (filePath: string): Promise<FileContent | null> => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        operation: 'read',
        filePath
      });

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to read file');
      }

      return await response.json();
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId]);

  // Write file content
  const writeFile = useCallback(async (
    path: string,
    content: string,
    options: {
      encoding?: 'utf8' | 'base64' | 'binary';
      permissions?: string;
      overwrite?: boolean;
      createDirectories?: boolean;
    } = {}
  ): Promise<FileSystemResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'write',
          path,
          content,
          encoding: options.encoding || 'utf8',
          permissions: options.permissions,
          overwrite: options.overwrite || false,
          createDirectories: options.createDirectories || true
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to write file');
      }

      const result = await response.json();
      
      // Refresh directory listing if the file is in current directory
      if (path.startsWith(currentPath)) {
        await listDirectory(currentPath);
      }
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentPath, listDirectory]);

  // Create directory
  const createDirectory = useCallback(async (
    path: string,
    options: { permissions?: string } = {}
  ): Promise<FileSystemResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'mkdir',
          path,
          permissions: options.permissions
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create directory');
      }

      const result = await response.json();
      
      // Refresh directory listing
      await listDirectory(currentPath);
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentPath, listDirectory]);

  // Move file or directory
  const moveItem = useCallback(async (
    source: string,
    destination: string
  ): Promise<FileSystemResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'move',
          source,
          destination
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to move item');
      }

      const result = await response.json();
      
      // Refresh directory listing
      await listDirectory(currentPath);
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentPath, listDirectory]);

  // Copy file or directory
  const copyItem = useCallback(async (
    source: string,
    destination: string
  ): Promise<FileSystemResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'copy',
          source,
          destination
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to copy item');
      }

      const result = await response.json();
      
      // Refresh directory listing
      await listDirectory(currentPath);
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentPath, listDirectory]);

  // Delete file or directory
  const deleteItem = useCallback(async (
    path: string,
    recursive: boolean = false
  ): Promise<FileSystemResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        path,
        recursive: recursive.toString()
      });

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem?${params}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete item');
      }

      const result = await response.json();
      
      // Refresh directory listing
      await listDirectory(currentPath);
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentPath, listDirectory]);

  // Navigate to directory
  const navigateToDirectory = useCallback(async (path: string) => {
    await listDirectory(path);
  }, [listDirectory]);

  // Navigate up one level
  const navigateUp = useCallback(async () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
    await navigateToDirectory(parentPath);
  }, [currentPath, navigateToDirectory]);

  // Refresh current directory
  const refresh = useCallback(async () => {
    await listDirectory(currentPath);
  }, [currentPath, listDirectory]);

  return {
    // State
    currentPath,
    files,
    loading,
    error,
    
    // Operations
    listDirectory,
    readFile,
    writeFile,
    createDirectory,
    moveItem,
    copyItem,
    deleteItem,
    
    // Navigation
    navigateToDirectory,
    navigateUp,
    refresh,
    
    // Utilities
    setCurrentPath
  };
}
