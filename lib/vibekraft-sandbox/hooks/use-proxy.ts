/**
 * VibeKraft Proxy Management React Hook
 * 
 * React hook for application preview and reverse proxy management within sandbox containers
 */

import { useState, useCallback, useEffect } from 'react';
import { 
  ProxyTarget, 
  ProxyStats,
  ProxyRequest,
  ProxyResponse
} from '../types/sandbox';

export interface UseProxyOptions {
  sandboxId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface ProxyTestResult {
  success: boolean;
  responseTime?: number;
  statusCode?: number;
  error?: string;
  timestamp: string;
}

export function useProxy({
  sandboxId,
  autoRefresh = false,
  refreshInterval = 30000 // 30 seconds
}: UseProxyOptions) {
  const [targets, setTargets] = useState<ProxyTarget[]>([]);
  const [stats, setStats] = useState<ProxyStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastOperation, setLastOperation] = useState<ProxyResponse | null>(null);

  // Get all proxy targets
  const getProxyTargets = useCallback(async (): Promise<ProxyTarget[]> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy?operation=list`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get proxy targets');
      }

      const data = await response.json();
      setTargets(data.targets);
      
      return data.targets;
    } catch (err: any) {
      setError(err.message);
      return [];
    } finally {
      setLoading(false);
    }
  }, [sandboxId]);

  // Get specific proxy target
  const getProxyTarget = useCallback(async (targetId: string): Promise<ProxyTarget | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy?operation=get&targetId=${targetId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get proxy target');
      }

      const data = await response.json();
      return data.target;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId]);

  // Create proxy target
  const createProxyTarget = useCallback(async (
    request: ProxyRequest
  ): Promise<ProxyTarget | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'create',
          ...request
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create proxy target');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh targets list
      await getProxyTargets();
      
      return result.target;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, getProxyTargets]);

  // Update proxy target
  const updateProxyTarget = useCallback(async (
    targetId: string,
    updates: Partial<ProxyRequest>
  ): Promise<ProxyTarget | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'update',
          targetId,
          ...updates
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update proxy target');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh targets list
      await getProxyTargets();
      
      return result.target;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, getProxyTargets]);

  // Delete proxy target
  const deleteProxyTarget = useCallback(async (targetId: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy?targetId=${targetId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete proxy target');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh targets list
      await getProxyTargets();
      
      return true;
    } catch (err: any) {
      setError(err.message);
      return false;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, getProxyTargets]);

  // Toggle proxy target (enable/disable)
  const toggleProxyTarget = useCallback(async (
    targetId: string,
    enabled: boolean
  ): Promise<ProxyTarget | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'toggle',
          targetId,
          enabled
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle proxy target');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh targets list
      await getProxyTargets();
      
      return result.target;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, getProxyTargets]);

  // Test proxy target
  const testProxyTarget = useCallback(async (targetId: string): Promise<ProxyTestResult | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy?operation=test&targetId=${targetId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to test proxy target');
      }

      const data = await response.json();
      return {
        success: data.success,
        responseTime: data.responseTime,
        statusCode: data.statusCode,
        error: data.error,
        timestamp: data.timestamp
      };
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId]);

  // Get proxy statistics
  const getProxyStats = useCallback(async (): Promise<ProxyStats | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy?operation=stats`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get proxy stats');
      }

      const data = await response.json();
      setStats(data.stats);
      
      return data.stats;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId]);

  // Convenience methods for common operations
  const createWebApp = useCallback(async (
    name: string,
    port: number,
    options: Partial<ProxyRequest> = {}
  ): Promise<ProxyTarget | null> => {
    return await createProxyTarget({
      name,
      containerPort: port,
      protocol: 'http',
      path: '/',
      healthCheck: {
        enabled: true,
        path: '/',
        interval: 30,
        timeout: 5,
        retries: 3
      },
      cors: {
        enabled: true,
        origins: ['*'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        headers: ['Content-Type', 'Authorization']
      },
      ...options
    });
  }, [createProxyTarget]);

  const createAPI = useCallback(async (
    name: string,
    port: number,
    options: Partial<ProxyRequest> = {}
  ): Promise<ProxyTarget | null> => {
    return await createProxyTarget({
      name,
      containerPort: port,
      protocol: 'http',
      path: '/api',
      healthCheck: {
        enabled: true,
        path: '/api/health',
        interval: 30,
        timeout: 5,
        retries: 3
      },
      cors: {
        enabled: true,
        origins: ['*'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        headers: ['Content-Type', 'Authorization']
      },
      rateLimit: {
        enabled: true,
        requests: 100,
        window: 60
      },
      ...options
    });
  }, [createProxyTarget]);

  const createWebSocket = useCallback(async (
    name: string,
    port: number,
    options: Partial<ProxyRequest> = {}
  ): Promise<ProxyTarget | null> => {
    return await createProxyTarget({
      name,
      containerPort: port,
      protocol: 'ws',
      path: '/ws',
      healthCheck: {
        enabled: false // WebSocket health checks are more complex
      },
      ...options
    });
  }, [createProxyTarget]);

  // Get targets by status
  const getActiveTargets = useCallback((): ProxyTarget[] => {
    return targets.filter(target => target.enabled);
  }, [targets]);

  const getInactiveTargets = useCallback((): ProxyTarget[] => {
    return targets.filter(target => !target.enabled);
  }, [targets]);

  // Get target by name or port
  const getTargetByName = useCallback((name: string): ProxyTarget | undefined => {
    return targets.find(target => target.name === name);
  }, [targets]);

  const getTargetByPort = useCallback((port: number): ProxyTarget | undefined => {
    return targets.find(target => target.containerPort === port);
  }, [targets]);

  // Check if port is already proxied
  const isPortProxied = useCallback((port: number): boolean => {
    return targets.some(target => target.containerPort === port);
  }, [targets]);

  // Get public URL for a port
  const getPublicUrl = useCallback((port: number): string | null => {
    const target = getTargetByPort(port);
    return target ? target.publicUrl : null;
  }, [getTargetByPort]);

  // Refresh all data
  const refresh = useCallback(async () => {
    await Promise.all([
      getProxyTargets(),
      getProxyStats()
    ]);
  }, [getProxyTargets, getProxyStats]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(refresh, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refresh]);

  // Initial load
  useEffect(() => {
    refresh();
  }, [sandboxId]);

  return {
    // State
    targets,
    stats,
    loading,
    error,
    lastOperation,
    
    // Core operations
    getProxyTargets,
    getProxyTarget,
    createProxyTarget,
    updateProxyTarget,
    deleteProxyTarget,
    toggleProxyTarget,
    testProxyTarget,
    getProxyStats,
    
    // Convenience methods
    createWebApp,
    createAPI,
    createWebSocket,
    
    // Information getters
    getActiveTargets,
    getInactiveTargets,
    getTargetByName,
    getTargetByPort,
    isPortProxied,
    getPublicUrl,
    
    // Utilities
    refresh
  };
}
