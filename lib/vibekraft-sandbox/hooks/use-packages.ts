/**
 * VibeKraft Package Management React Hook
 * 
 * React hook for package management operations within sandbox containers
 */

import { useState, useCallback, useEffect } from 'react';
import { 
  PackageManager, 
  PackageInfo, 
  PackageInstallRequest, 
  PackageInstallResponse,
  PackageListResponse,
  PackageSearchRequest,
  PackageSearchResult
} from '../types/sandbox';

export interface UsePackagesOptions {
  sandboxId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  defaultManager?: string;
  defaultWorkingDir?: string;
}

export function usePackages({
  sandboxId,
  autoRefresh = false,
  refreshInterval = 30000, // 30 seconds
  defaultManager,
  defaultWorkingDir = '/workspace'
}: UsePackagesOptions) {
  const [managers, setManagers] = useState<PackageManager[]>([]);
  const [packages, setPackages] = useState<PackageInfo[]>([]);
  const [currentManager, setCurrentManager] = useState<string>(defaultManager || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastOperation, setLastOperation] = useState<PackageInstallResponse | null>(null);

  // Get available package managers
  const getPackageManagers = useCallback(async (): Promise<PackageManager[]> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages?operation=managers`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get package managers');
      }

      const data = await response.json();
      setManagers(data.managers);
      
      // Set default manager if not set
      if (!currentManager && data.managers.length > 0) {
        const availableManager = data.managers.find((m: PackageManager) => m.available);
        if (availableManager) {
          setCurrentManager(availableManager.name);
        }
      }
      
      return data.managers;
    } catch (err: any) {
      setError(err.message);
      return [];
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentManager]);

  // List installed packages
  const listPackages = useCallback(async (
    manager?: string,
    workingDir?: string
  ): Promise<PackageListResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        operation: 'list',
        ...(manager && { manager }),
        workingDir: workingDir || defaultWorkingDir
      });

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to list packages');
      }

      const data = await response.json();
      setPackages(data.packages);
      
      return data;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, defaultWorkingDir]);

  // Install packages
  const installPackages = useCallback(async (
    packages: string[],
    options: Partial<PackageInstallRequest> = {}
  ): Promise<PackageInstallResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const requestBody = {
        operation: 'install',
        packages,
        manager: options.manager || currentManager,
        workingDir: options.workingDir || defaultWorkingDir,
        dev: options.dev || false,
        global: options.global || false,
        save: options.save !== false, // Default to true
        exact: options.exact || false,
        optional: options.optional || false,
        timeout: options.timeout || 600
      };

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to install packages');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh package list if installation was successful
      if (result.success) {
        await listPackages(currentManager);
      }
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentManager, defaultWorkingDir, listPackages]);

  // Uninstall packages
  const uninstallPackages = useCallback(async (
    packages: string[],
    manager?: string,
    workingDir?: string
  ): Promise<PackageInstallResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const requestBody = {
        operation: 'uninstall',
        packages,
        manager: manager || currentManager,
        workingDir: workingDir || defaultWorkingDir
      };

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to uninstall packages');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh package list if uninstallation was successful
      if (result.success) {
        await listPackages(currentManager);
      }
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentManager, defaultWorkingDir, listPackages]);

  // Update packages
  const updatePackages = useCallback(async (
    packages?: string[],
    manager?: string,
    workingDir?: string
  ): Promise<PackageInstallResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const requestBody = {
        operation: 'update',
        packages,
        manager: manager || currentManager,
        workingDir: workingDir || defaultWorkingDir
      };

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update packages');
      }

      const result = await response.json();
      setLastOperation(result);
      
      // Refresh package list if update was successful
      if (result.success) {
        await listPackages(currentManager);
      }
      
      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentManager, defaultWorkingDir, listPackages]);

  // Search packages
  const searchPackages = useCallback(async (
    query: string,
    manager?: string,
    options: { limit?: number; exact?: boolean } = {}
  ): Promise<PackageSearchResult[]> => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        operation: 'search',
        query,
        manager: manager || currentManager,
        limit: (options.limit || 10).toString(),
        exact: (options.exact || false).toString()
      });

      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to search packages');
      }

      const data = await response.json();
      return data.results;
    } catch (err: any) {
      setError(err.message);
      return [];
    } finally {
      setLoading(false);
    }
  }, [sandboxId, currentManager]);

  // Install package by name (convenience method)
  const installPackage = useCallback(async (
    packageName: string,
    options: Partial<PackageInstallRequest> = {}
  ): Promise<PackageInstallResponse | null> => {
    return await installPackages([packageName], options);
  }, [installPackages]);

  // Uninstall package by name (convenience method)
  const uninstallPackage = useCallback(async (
    packageName: string,
    manager?: string,
    workingDir?: string
  ): Promise<PackageInstallResponse | null> => {
    return await uninstallPackages([packageName], manager, workingDir);
  }, [uninstallPackages]);

  // Get package info by name
  const getPackageInfo = useCallback((packageName: string): PackageInfo | undefined => {
    return packages.find(pkg => pkg.name === packageName);
  }, [packages]);

  // Check if package is installed
  const isPackageInstalled = useCallback((packageName: string): boolean => {
    return packages.some(pkg => pkg.name === packageName);
  }, [packages]);

  // Get outdated packages
  const getOutdatedPackages = useCallback((): PackageInfo[] => {
    return packages.filter(pkg => pkg.updateAvailable);
  }, [packages]);

  // Refresh all data
  const refresh = useCallback(async () => {
    await Promise.all([
      getPackageManagers(),
      listPackages(currentManager)
    ]);
  }, [getPackageManagers, listPackages, currentManager]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(refresh, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refresh]);

  // Initial load
  useEffect(() => {
    refresh();
  }, [sandboxId]);

  return {
    // State
    managers,
    packages,
    currentManager,
    loading,
    error,
    lastOperation,
    
    // Manager operations
    getPackageManagers,
    setCurrentManager,
    
    // Package operations
    listPackages,
    installPackages,
    uninstallPackages,
    updatePackages,
    searchPackages,
    
    // Convenience methods
    installPackage,
    uninstallPackage,
    getPackageInfo,
    isPackageInstalled,
    getOutdatedPackages,
    
    // Utilities
    refresh
  };
}
