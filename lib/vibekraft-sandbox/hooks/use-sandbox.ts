/**
 * VibeKraft Sandbox React Hook
 * 
 * React hook for managing sandbox state and operations
 */

import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/components/ui/use-toast';

export interface SandboxSummary {
  id: string;
  name: string;
  template: string;
  projectId?: string;
  state: 'creating' | 'starting' | 'running' | 'stopping' | 'stopped' | 'error' | 'destroyed';
  health: {
    status: 'healthy' | 'unhealthy' | 'unknown';
    lastChecked: Date;
  };
  resources: {
    vcpuCount: number;
    memSizeMib: number;
    diskSizeGb: number;
  };
  network: {
    ports: number;
    isolated: boolean;
  };
  createdAt: Date;
  lastAccessedAt?: Date;
  labels: Record<string, string>;
  annotations: Record<string, string>;
}

export interface SandboxDetails extends SandboxSummary {
  userId: string;
  runtime: {
    resources: {
      cpu: { usage: number; cores: number; throttled: boolean };
      memory: { usage: number; limit: number; percentage: number; swap: number };
      disk: { usage: number; limit: number; percentage: number; iops: number };
      network: { bytesIn: number; bytesOut: number; packetsIn: number; packetsOut: number; connections: number };
    };
    network: {
      interfaces: Array<{ name: string; ipAddress: string; state: string }>;
      ports: Array<{ containerPort: number; hostPort: number; protocol: string; state: string }>;
      connections: Array<{ localAddress: string; localPort: number; remoteAddress: string; remotePort: number }>;
    };
    processes: Array<{ pid: number; name: string; cpu: number; memory: number; user: string }>;
    uptime: number;
  };
  environment: Record<string, string>;
  operations: Array<{
    id: string;
    type: string;
    status: string;
    startedAt: Date;
    completedAt?: Date;
    error?: string;
  }>;
  logs: Array<{
    timestamp: Date;
    level: string;
    source: string;
    message: string;
  }>;
}

export interface CreateSandboxRequest {
  name: string;
  template: string;
  projectId?: string;
  resources?: {
    vcpuCount?: number;
    memSizeMib?: number;
    diskSizeGb?: number;
    diskType?: 'ssd' | 'hdd';
  };
  network?: {
    ports?: Array<{
      containerPort: number;
      hostPort?: number;
      protocol?: 'tcp' | 'udp';
      description?: string;
      public?: boolean;
    }>;
    isolated?: boolean;
    allowedHosts?: string[];
    blockedHosts?: string[];
  };
  security?: {
    readOnlyRootfs?: boolean;
    noNewPrivileges?: boolean;
    allowSudo?: boolean;
    allowNetworkAccess?: boolean;
    allowFileSystemAccess?: boolean;
  };
  environment?: Record<string, string>;
  labels?: Record<string, string>;
  annotations?: Record<string, string>;
}

export interface ExecuteCommandRequest {
  command: string[];
  workingDir?: string;
  environment?: Record<string, string>;
  user?: string;
  timeout?: number;
  interactive?: boolean;
}

export interface ExecuteCommandResponse {
  sandboxId: string;
  command: string;
  exitCode: number;
  stdout: string;
  stderr: string;
  duration: number;
  timedOut: boolean;
  workingDir: string;
  user: string;
  timestamp: string;
}

export function useSandboxes(projectId?: string) {
  const [sandboxes, setSandboxes] = useState<SandboxSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch sandboxes
  const refreshSandboxes = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      if (projectId) params.append('projectId', projectId);
      
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch sandboxes');
      }
      
      const data = await response.json();
      setSandboxes(data.sandboxes.map((sandbox: any) => ({
        ...sandbox,
        createdAt: new Date(sandbox.createdAt),
        lastAccessedAt: sandbox.lastAccessedAt ? new Date(sandbox.lastAccessedAt) : undefined,
        health: {
          ...sandbox.health,
          lastChecked: new Date(sandbox.health.lastChecked)
        }
      })));
    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching sandboxes:', err);
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  // Create sandbox
  const createSandbox = useCallback(async (request: CreateSandboxRequest): Promise<SandboxSummary> => {
    const response = await fetch('/api/vibekraft-sandbox/sandboxes', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create sandbox');
    }
    
    const data = await response.json();
    return {
      ...data,
      createdAt: new Date(data.createdAt)
    };
  }, []);

  // Start sandbox
  const startSandbox = useCallback(async (sandboxId: string): Promise<void> => {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'start' })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to start sandbox');
    }
  }, []);

  // Stop sandbox
  const stopSandbox = useCallback(async (sandboxId: string): Promise<void> => {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'stop' })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to stop sandbox');
    }
  }, []);

  // Restart sandbox
  const restartSandbox = useCallback(async (sandboxId: string): Promise<void> => {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'restart' })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to restart sandbox');
    }
  }, []);

  // Destroy sandbox
  const destroySandbox = useCallback(async (sandboxId: string, force: boolean = false): Promise<void> => {
    const params = force ? '?force=true' : '';
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}${params}`, {
      method: 'DELETE'
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to destroy sandbox');
    }
  }, []);

  // Execute command
  const executeCommand = useCallback(async (
    sandboxId: string, 
    request: ExecuteCommandRequest
  ): Promise<ExecuteCommandResponse> => {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/execute`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to execute command');
    }
    
    return await response.json();
  }, []);

  // Bulk operations
  const bulkOperation = useCallback(async (
    operation: 'start' | 'stop' | 'restart' | 'destroy',
    sandboxIds: string[]
  ): Promise<{ successful: number; failed: number; results: any[]; errors: any[] }> => {
    const response = await fetch('/api/vibekraft-sandbox/sandboxes', {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ operation, sandboxIds })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform bulk operation');
    }
    
    const data = await response.json();
    return {
      successful: data.summary.successful,
      failed: data.summary.failed,
      results: data.results,
      errors: data.errors
    };
  }, []);

  // Auto-refresh sandboxes
  useEffect(() => {
    refreshSandboxes();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(refreshSandboxes, 30000);
    
    return () => clearInterval(interval);
  }, [refreshSandboxes]);

  return {
    sandboxes,
    loading,
    error,
    refreshSandboxes,
    createSandbox,
    startSandbox,
    stopSandbox,
    restartSandbox,
    destroySandbox,
    executeCommand,
    bulkOperation
  };
}

export function useSandboxDetails(sandboxId: string | null) {
  const [sandbox, setSandbox] = useState<SandboxDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch sandbox details
  const refreshSandbox = useCallback(async () => {
    if (!sandboxId) {
      setSandbox(null);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch sandbox details');
      }
      
      const data = await response.json();
      setSandbox({
        ...data,
        createdAt: new Date(data.createdAt),
        lastAccessedAt: data.lastAccessedAt ? new Date(data.lastAccessedAt) : undefined,
        health: {
          ...data.health,
          lastChecked: new Date(data.health.lastChecked)
        },
        operations: data.operations.map((op: any) => ({
          ...op,
          startedAt: new Date(op.startedAt),
          completedAt: op.completedAt ? new Date(op.completedAt) : undefined
        })),
        logs: data.logs.map((log: any) => ({
          ...log,
          timestamp: new Date(log.timestamp)
        }))
      });
    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching sandbox details:', err);
    } finally {
      setLoading(false);
    }
  }, [sandboxId]);

  // Auto-refresh sandbox details
  useEffect(() => {
    refreshSandbox();
    
    if (sandboxId) {
      // Set up auto-refresh every 10 seconds for details
      const interval = setInterval(refreshSandbox, 10000);
      return () => clearInterval(interval);
    }
  }, [refreshSandbox, sandboxId]);

  return {
    sandbox,
    loading,
    error,
    refreshSandbox
  };
}
