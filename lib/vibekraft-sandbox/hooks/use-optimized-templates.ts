/**
 * VibeKraft Optimized Templates Hook
 *
 * React hook for managing optimized Docker templates using API client
 */

import { useState, useEffect, useCallback } from 'react';
import { sandboxAPI } from '../api/sandbox-client';

export interface OptimizedTemplate {
  id: string;
  name: string;
  description: string;
  size: 'minimal' | 'standard' | 'full';
  languages: string[];
  packageManagers: string[];
  preInstalled: string[];
  optimizedImage: string;
  baseImage: string;
}

export interface TemplateAnalysis {
  name: string;
  optimizedSize: number | string;
  layers: number | string;
  languages: string[];
  packageManagers: string[];
}

export interface TemplateStatistics {
  totalTemplates: number;
  builtTemplates: number;
  pendingTemplates: number;
  totalSizeMB: number;
  averageSizeMB: number;
  minSizeMB: number;
  maxSizeMB: number;
}

export interface TemplateRecommendations {
  mostOptimized: TemplateAnalysis | null;
  mostVersatile: TemplateAnalysis | null;
}

export interface UseOptimizedTemplatesOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UseOptimizedTemplatesReturn {
  // State
  templates: OptimizedTemplate[];
  analysis: TemplateAnalysis[];
  statistics: TemplateStatistics | null;
  recommendations: TemplateRecommendations | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  refreshTemplates: () => Promise<void>;
  buildTemplates: (templateIds?: string[]) => Promise<any>;
  buildAllTemplates: () => Promise<any>;
  getRecommendations: (requirements: {
    languages?: string[];
    packageManagers?: string[];
    maxSize?: 'minimal' | 'standard' | 'full';
  }) => OptimizedTemplate[];
  
  // Filters
  filterTemplates: (filters: {
    language?: string;
    packageManager?: string;
    maxSize?: 'minimal' | 'standard' | 'full';
  }) => OptimizedTemplate[];
}

export function useOptimizedTemplates(options: UseOptimizedTemplatesOptions = {}): UseOptimizedTemplatesReturn {
  const {
    autoRefresh = false,
    refreshInterval = 60000 // 1 minute
  } = options;

  // State
  const [templates, setTemplates] = useState<OptimizedTemplate[]>([]);
  const [analysis, setAnalysis] = useState<TemplateAnalysis[]>([]);
  const [statistics, setStatistics] = useState<TemplateStatistics | null>(null);
  const [recommendations, setRecommendations] = useState<TemplateRecommendations | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch templates
  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await sandboxAPI.listTemplates({ includeAnalysis: true });
      setTemplates(data.templates || []);

      if (data.analysis) {
        setAnalysis(data.analysis);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch templates');
      console.error('Error fetching templates:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch analysis
  const fetchAnalysis = useCallback(async () => {
    try {
      const data = await sandboxAPI.getTemplateAnalysis();
      setAnalysis(data.templates || []);
      setStatistics(data.statistics || null);
      setRecommendations(data.recommendations || null);
    } catch (err) {
      console.error('Error fetching analysis:', err);
      // Don't set error for analysis failure
    }
  }, []);

  // Build templates
  const buildTemplates = useCallback(async (templateIds?: string[]): Promise<any> => {
    try {
      setError(null);

      const result = await sandboxAPI.buildTemplates(templateIds);

      // Refresh templates and analysis after build
      await Promise.all([fetchTemplates(), fetchAnalysis()]);

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to build templates';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [fetchTemplates, fetchAnalysis]);

  // Build all templates
  const buildAllTemplates = useCallback(async (): Promise<any> => {
    return buildTemplates();
  }, [buildTemplates]);

  // Get recommendations
  const getRecommendations = useCallback((requirements: {
    languages?: string[];
    packageManagers?: string[];
    maxSize?: 'minimal' | 'standard' | 'full';
  }): OptimizedTemplate[] => {
    return templates.filter(template => {
      // Filter by languages
      if (requirements.languages) {
        const hasLanguage = requirements.languages.some(lang => 
          template.languages.includes(lang)
        );
        if (!hasLanguage) return false;
      }
      
      // Filter by package managers
      if (requirements.packageManagers) {
        const hasPackageManager = requirements.packageManagers.some(pm => 
          template.packageManagers.includes(pm)
        );
        if (!hasPackageManager) return false;
      }
      
      // Filter by size
      if (requirements.maxSize) {
        const sizeOrder = { minimal: 0, standard: 1, full: 2 };
        if (sizeOrder[template.size] > sizeOrder[requirements.maxSize]) {
          return false;
        }
      }
      
      return true;
    });
  }, [templates]);

  // Filter templates
  const filterTemplates = useCallback((filters: {
    language?: string;
    packageManager?: string;
    maxSize?: 'minimal' | 'standard' | 'full';
  }): OptimizedTemplate[] => {
    return templates.filter(template => {
      // Filter by language
      if (filters.language && !template.languages.includes(filters.language)) {
        return false;
      }
      
      // Filter by package manager
      if (filters.packageManager && !template.packageManagers.includes(filters.packageManager)) {
        return false;
      }
      
      // Filter by size
      if (filters.maxSize) {
        const sizeOrder = { minimal: 0, standard: 1, full: 2 };
        if (sizeOrder[template.size] > sizeOrder[filters.maxSize]) {
          return false;
        }
      }
      
      return true;
    });
  }, [templates]);

  // Refresh templates
  const refreshTemplates = useCallback(async () => {
    await Promise.all([fetchTemplates(), fetchAnalysis()]);
  }, [fetchTemplates, fetchAnalysis]);

  // Auto-refresh effect
  useEffect(() => {
    // Initial fetch
    refreshTemplates();

    // Set up auto-refresh
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(refreshTemplates, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshTemplates, autoRefresh, refreshInterval]);

  return {
    // State
    templates,
    analysis,
    statistics,
    recommendations,
    loading,
    error,
    
    // Actions
    refreshTemplates,
    buildTemplates,
    buildAllTemplates,
    getRecommendations,
    
    // Filters
    filterTemplates,
  };
}
