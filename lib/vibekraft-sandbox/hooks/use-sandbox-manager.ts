/**
 * VibeKraft Sandbox Manager Hook
 *
 * React hook for managing sandbox operations using API client
 */

import { useState, useEffect, useCallback } from 'react';
import { sandboxAPI, SandboxStatus, CreateSandboxRequest } from '../api/sandbox-client';

export interface SandboxListItem {
  id: string;
  name: string;
  template: string;
  projectId?: string;
  state: string;
  health: {
    status: string;
    checks: any[];
    lastChecked: Date;
  };
  resources: {
    vcpuCount: number;
    memSizeMib: number;
    diskSizeGb: number;
  };
  network: {
    ports: number;
    isolated: boolean;
  };
  createdAt: Date;
  lastAccessedAt: Date;
  labels: Record<string, string>;
  annotations: Record<string, string>;
}

// Re-export from API client for convenience
export type { CreateSandboxRequest } from '../api/sandbox-client';

export interface UseSandboxManagerOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  projectId?: string;
}

export interface UseSandboxManagerReturn {
  // State
  sandboxes: SandboxListItem[];
  loading: boolean;
  error: string | null;
  
  // Actions
  createSandbox: (config: CreateSandboxRequest) => Promise<string>;
  deleteSandbox: (sandboxId: string) => Promise<void>;
  refreshSandboxes: () => Promise<void>;
  getSandboxStatus: (sandboxId: string) => Promise<SandboxStatus>;
  
  // Bulk operations
  bulkOperation: (operation: string, sandboxIds: string[]) => Promise<any>;
  
  // Filters
  setFilter: (filter: { status?: string; template?: string }) => void;
  filter: { status?: string; template?: string };
}

export function useSandboxManager(options: UseSandboxManagerOptions = {}): UseSandboxManagerReturn {
  const {
    autoRefresh = true,
    refreshInterval = 30000, // 30 seconds
    projectId
  } = options;

  // State
  const [sandboxes, setSandboxes] = useState<SandboxListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<{ status?: string; template?: string }>({});

  // Fetch sandboxes
  const fetchSandboxes = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {};
      if (projectId) params.projectId = projectId;
      if (filter.status) params.status = filter.status;
      if (filter.template) params.template = filter.template;

      const data = await sandboxAPI.listSandboxes(params);

      // Convert API response to SandboxListItem format
      const sandboxItems: SandboxListItem[] = data.sandboxes.map(sandbox => ({
        id: sandbox.id,
        name: sandbox.name,
        template: sandbox.template,
        projectId: sandbox.projectId,
        state: 'unknown', // Will be updated by status calls
        health: {
          status: 'unknown',
          checks: [],
          lastChecked: new Date()
        },
        resources: sandbox.resources,
        network: {
          ports: sandbox.network?.ports?.length || 0,
          isolated: sandbox.network?.isolated || false
        },
        createdAt: sandbox.createdAt,
        lastAccessedAt: sandbox.updatedAt,
        labels: sandbox.labels || {},
        annotations: sandbox.annotations || {}
      }));

      setSandboxes(sandboxItems);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch sandboxes');
      console.error('Error fetching sandboxes:', err);
    } finally {
      setLoading(false);
    }
  }, [projectId, filter]);

  // Create sandbox
  const createSandbox = useCallback(async (config: CreateSandboxRequest): Promise<string> => {
    try {
      setError(null);

      const data = await sandboxAPI.createSandbox(config);

      // Refresh the list
      await fetchSandboxes();

      return data.id;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create sandbox';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [fetchSandboxes]);

  // Delete sandbox
  const deleteSandbox = useCallback(async (sandboxId: string, options?: { force?: boolean; deleteVolumes?: boolean }): Promise<void> => {
    try {
      setError(null);

      await sandboxAPI.deleteSandbox(sandboxId, options);

      // Refresh the list
      await fetchSandboxes();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete sandbox';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [fetchSandboxes]);

  // Get sandbox status
  const getSandboxStatus = useCallback(async (sandboxId: string): Promise<SandboxStatus> => {
    try {
      setError(null);

      return await sandboxAPI.getSandboxStatus(sandboxId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get sandbox status';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // Bulk operations
  const bulkOperation = useCallback(async (operation: string, sandboxIds: string[], options?: any): Promise<any> => {
    try {
      setError(null);

      const result = await sandboxAPI.bulkOperation(operation, sandboxIds, options);

      // Refresh the list
      await fetchSandboxes();

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to perform bulk operation';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [fetchSandboxes]);

  // Refresh sandboxes
  const refreshSandboxes = useCallback(async () => {
    await fetchSandboxes();
  }, [fetchSandboxes]);

  // Auto-refresh effect
  useEffect(() => {
    // Initial fetch
    fetchSandboxes();

    // Set up auto-refresh
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchSandboxes, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchSandboxes, autoRefresh, refreshInterval]);

  // Filter effect
  useEffect(() => {
    fetchSandboxes();
  }, [filter, fetchSandboxes]);

  return {
    // State
    sandboxes,
    loading,
    error,
    
    // Actions
    createSandbox,
    deleteSandbox,
    refreshSandboxes,
    getSandboxStatus,
    
    // Bulk operations
    bulkOperation,
    
    // Filters
    setFilter,
    filter,
  };
}
