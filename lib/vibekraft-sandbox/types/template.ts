/**
 * VibeKraft Sandbox Template Type Definitions
 * 
 * Type definitions for sandbox templates and marketplace
 */

export interface SandboxTemplate {
  id: string;
  name: string;
  description: string;
  version: string;
  
  // Template metadata
  metadata: TemplateMetadata;
  
  // Template configuration
  config: TemplateConfig;
  
  // Files and structure
  files: TemplateFile[];
  
  // Dependencies and setup
  setup: TemplateSetup;
  
  // Customization options
  customization: TemplateCustomization;
}

export interface TemplateMetadata {
  author: string;
  authorEmail?: string;
  category: TemplateCategory;
  tags: string[];
  
  // Versioning
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  
  // Usage statistics
  downloads: number;
  rating: number;
  reviews: number;
  
  // Compatibility
  minVersion: string;
  maxVersion?: string;
  
  // Documentation
  readme?: string;
  changelog?: string;
  documentation?: string;
  
  // Media
  icon?: string;
  screenshots: string[];
  
  // Licensing
  license: string;
  licenseUrl?: string;
  
  // Repository
  repository?: string;
  homepage?: string;
  issues?: string;
}

export type TemplateCategory =
  | 'web-development'
  | 'mobile-development'
  | 'data-science'
  | 'machine-learning'
  | 'devops'
  | 'education'
  | 'prototyping'
  | 'testing'
  | 'research'
  | 'other';

export interface TemplateConfig {
  // Base image configuration
  baseImage: string;
  dockerfile?: string;
  
  // Runtime configuration
  runtime: RuntimeConfig;
  
  // Default resource requirements
  resources: {
    min: Partial<ResourceRequirements>;
    recommended: Partial<ResourceRequirements>;
    max: Partial<ResourceRequirements>;
  };
  
  // Network requirements
  network: NetworkRequirements;
  
  // Security requirements
  security: SecurityRequirements;
  
  // Environment configuration
  environment: EnvironmentConfig;
}

export interface RuntimeConfig {
  language: string;
  version: string;
  framework?: string;
  frameworkVersion?: string;
  
  // Package managers
  packageManager: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'poetry' | 'cargo' | 'go' | 'maven' | 'gradle';
  
  // Build configuration
  buildCommand?: string;
  startCommand: string;
  testCommand?: string;
  
  // Development server
  devServer?: {
    port: number;
    host?: string;
    hotReload?: boolean;
  };
  
  // Production configuration
  production?: {
    buildDir: string;
    staticDir?: string;
    port: number;
  };
}

export interface ResourceRequirements {
  vcpuCount: number;
  memSizeMib: number;
  diskSizeGb: number;
  
  // Performance requirements
  cpuIntensive?: boolean;
  memoryIntensive?: boolean;
  diskIntensive?: boolean;
  networkIntensive?: boolean;
}

export interface NetworkRequirements {
  // Required ports
  ports: RequiredPort[];
  
  // External access requirements
  internetAccess: boolean;
  inboundAccess: boolean;
  
  // Service dependencies
  services: ServiceDependency[];
}

export interface RequiredPort {
  port: number;
  protocol: 'tcp' | 'udp';
  description: string;
  required: boolean;
  public?: boolean;
}

export interface ServiceDependency {
  name: string;
  type: 'database' | 'cache' | 'message-queue' | 'storage' | 'api' | 'other';
  required: boolean;
  version?: string;
  configuration?: Record<string, any>;
}

export interface SecurityRequirements {
  // Privilege requirements
  privileged: boolean;
  capabilities: string[];
  
  // File system access
  readOnlyRootfs: boolean;
  volumeMounts: VolumeMount[];
  
  // Network access
  networkPolicy: 'none' | 'restricted' | 'full';
  allowedHosts?: string[];
  
  // User requirements
  runAsRoot: boolean;
  sudoAccess: boolean;
}

export interface VolumeMount {
  source: string;
  destination: string;
  readOnly: boolean;
  type: 'bind' | 'volume' | 'tmpfs';
}

export interface EnvironmentConfig {
  // Required environment variables
  required: EnvironmentVariable[];
  
  // Optional environment variables
  optional: EnvironmentVariable[];
  
  // Default values
  defaults: Record<string, string>;
  
  // Configuration files
  configFiles: ConfigFile[];
}

export interface EnvironmentVariable {
  name: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'url' | 'email' | 'password' | 'json';
  required: boolean;
  defaultValue?: string;
  validation?: ValidationRule;
  sensitive?: boolean;
}

export interface ValidationRule {
  pattern?: string;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  enum?: string[];
}

export interface ConfigFile {
  path: string;
  template: string;
  description: string;
  required: boolean;
  variables: string[];
}

export interface TemplateFile {
  path: string;
  content: string;
  type: 'text' | 'binary';
  encoding?: 'utf8' | 'base64';
  permissions?: string;
  template?: boolean; // If true, content will be processed as a template
  variables?: string[]; // Variables used in template
}

export interface TemplateSetup {
  // Pre-setup commands (run before file creation)
  preSetup: SetupCommand[];
  
  // Post-setup commands (run after file creation)
  postSetup: SetupCommand[];
  
  // Health checks
  healthChecks: HealthCheckConfig[];
  
  // Initialization scripts
  initScripts: InitScript[];
}

export interface SetupCommand {
  command: string;
  description: string;
  workingDir?: string;
  environment?: Record<string, string>;
  timeout?: number;
  retries?: number;
  continueOnError?: boolean;
}

export interface HealthCheckConfig {
  name: string;
  command: string;
  interval: number;
  timeout: number;
  retries: number;
  startPeriod?: number;
}

export interface InitScript {
  name: string;
  script: string;
  description: string;
  runOnce?: boolean;
  runOnStart?: boolean;
}

export interface TemplateCustomization {
  // Customizable parameters
  parameters: CustomizationParameter[];
  
  // Conditional features
  features: TemplateFeature[];
  
  // Add-ons and extensions
  addons: TemplateAddon[];
  
  // Themes and styling
  themes: TemplateTheme[];
}

export interface CustomizationParameter {
  name: string;
  displayName: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect' | 'json';
  required: boolean;
  defaultValue?: any;
  options?: ParameterOption[];
  validation?: ValidationRule;
  group?: string;
  order?: number;
  dependsOn?: string[];
  affects?: string[];
}

export interface ParameterOption {
  value: any;
  label: string;
  description?: string;
  icon?: string;
}

export interface TemplateFeature {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  required: boolean;
  dependencies: string[];
  conflicts: string[];
  configuration?: Record<string, any>;
}

export interface TemplateAddon {
  id: string;
  name: string;
  description: string;
  version: string;
  type: 'plugin' | 'extension' | 'integration' | 'tool';
  optional: boolean;
  configuration?: Record<string, any>;
  files?: TemplateFile[];
  setup?: SetupCommand[];
}

export interface TemplateTheme {
  id: string;
  name: string;
  description: string;
  preview?: string;
  files: TemplateFile[];
  variables: Record<string, string>;
}

export interface TemplateValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  code: string;
  message: string;
  path?: string;
  severity: 'error' | 'warning' | 'info';
}

export interface ValidationWarning {
  code: string;
  message: string;
  path?: string;
  suggestion?: string;
}

export interface TemplateInstallation {
  templateId: string;
  sandboxId: string;
  parameters: Record<string, any>;
  features: string[];
  addons: string[];
  theme?: string;
  status: InstallationStatus;
  progress: number;
  logs: InstallationLog[];
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}

export type InstallationStatus =
  | 'pending'
  | 'downloading'
  | 'extracting'
  | 'configuring'
  | 'installing'
  | 'finalizing'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface InstallationLog {
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  step?: string;
  progress?: number;
}
