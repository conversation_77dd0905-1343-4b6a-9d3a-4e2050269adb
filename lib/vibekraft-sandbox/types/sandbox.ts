/**
 * VibeKraft Sandbox Type Definitions
 * 
 * Comprehensive type definitions for the sandbox infrastructure
 */

export interface SandboxConfig {
  id: string;
  name: string;
  template: string;
  projectId?: string;
  userId: string;
  
  // Resource configuration
  resources: ResourceConfig;
  
  // Network configuration
  network: NetworkConfig;
  
  // Security configuration
  security: SecurityConfig;
  
  // Environment variables
  environment: Record<string, string>;
  
  // Metadata
  metadata: SandboxMetadata;
}

export interface ResourceConfig {
  // CPU configuration
  vcpuCount: number;
  cpuShares?: number;
  cpuQuota?: number;
  cpuPeriod?: number;
  
  // Memory configuration
  memSizeMib: number;
  memSwapSizeMib?: number;
  
  // Storage configuration
  diskSizeGb: number;
  diskType: 'ssd' | 'hdd';
  
  // Limits
  maxProcesses?: number;
  maxOpenFiles?: number;
  maxNetworkConnections?: number;
}

export interface NetworkConfig {
  // Port mappings
  ports: PortMapping[];
  
  // Network isolation
  isolated: boolean;
  allowedHosts?: string[];
  blockedHosts?: string[];
  
  // Bandwidth limits
  bandwidthLimitMbps?: number;
}

export interface PortMapping {
  containerPort: number;
  hostPort?: number;
  protocol: 'tcp' | 'udp';
  description?: string;
  public?: boolean;
}

export interface SecurityConfig {
  // Container security
  readOnlyRootfs: boolean;
  noNewPrivileges: boolean;
  capabilities: {
    drop: string[];
    add: string[];
  };
  
  // User configuration
  runAsUser: number;
  runAsGroup: number;
  
  // Security profiles
  seccompProfile?: string;
  apparmorProfile?: string;
  selinuxOptions?: Record<string, string>;
  
  // Access control
  allowSudo: boolean;
  allowNetworkAccess: boolean;
  allowFileSystemAccess: boolean;
}

export interface SandboxMetadata {
  createdAt: Date;
  updatedAt: Date;
  lastAccessedAt?: Date;
  
  // Template information
  templateVersion: string;
  templateSource: string;
  
  // Labels and annotations
  labels: Record<string, string>;
  annotations: Record<string, string>;
  
  // Usage tracking
  totalUptime: number;
  totalCpuTime: number;
  totalMemoryUsage: number;
  totalNetworkTraffic: number;
}

export interface SandboxStatus {
  id: string;
  state: SandboxState;
  health: HealthStatus;
  
  // Resource usage
  resources: ResourceUsage;
  
  // Network information
  network: NetworkStatus;
  
  // Process information
  processes: ProcessInfo[];
  
  // Logs
  logs: LogEntry[];
}

export type SandboxState = 
  | 'creating'
  | 'starting'
  | 'running'
  | 'stopping'
  | 'stopped'
  | 'error'
  | 'destroyed';

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'unknown';
  checks: HealthCheck[];
  lastChecked: Date;
}

export interface HealthCheck {
  name: string;
  status: 'pass' | 'fail' | 'warn';
  message?: string;
  timestamp: Date;
}

export interface ResourceUsage {
  cpu: {
    usage: number; // percentage
    cores: number;
    throttled: boolean;
  };
  memory: {
    usage: number; // bytes
    limit: number; // bytes
    percentage: number;
    swap: number; // bytes
  };
  disk: {
    usage: number; // bytes
    limit: number; // bytes
    percentage: number;
    iops: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
    connections: number;
  };
}

export interface NetworkStatus {
  interfaces: NetworkInterfaceBasic[];
  ports: PortStatus[];
  connections: NetworkConnection[];
}

export interface NetworkInterfaceBasic {
  name: string;
  ipAddress: string;
  macAddress: string;
  mtu: number;
  state: 'up' | 'down';
}

export interface PortStatus {
  containerPort: number;
  hostPort: number;
  protocol: 'tcp' | 'udp';
  state: 'open' | 'closed' | 'listening';
  service?: string;
}

export interface NetworkConnection {
  localAddress: string;
  localPort: number;
  remoteAddress: string;
  remotePort: number;
  state: 'established' | 'listening' | 'time_wait' | 'close_wait';
  protocol: 'tcp' | 'udp';
  processId?: number;
  processName?: string;
}

export interface ProcessInfo {
  pid: number;
  ppid: number;
  name: string;
  command: string;
  user: string;
  cpu: number; // percentage
  memory: number; // bytes
  startTime: Date;
  state: string;
}

export interface LogEntry {
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  source: string;
  message: string;
  metadata?: Record<string, any>;
}

export interface SandboxOperation {
  id: string;
  sandboxId: string;
  type: OperationType;
  status: OperationStatus;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
  result?: any;
}

export type OperationType =
  | 'create'
  | 'start'
  | 'stop'
  | 'restart'
  | 'destroy'
  | 'execute'
  | 'upload'
  | 'download'
  | 'snapshot'
  | 'restore';

export type OperationStatus =
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface ExecuteCommandRequest {
  command: string[];
  workingDir?: string;
  environment?: Record<string, string>;
  user?: string;
  timeout?: number;
  interactive?: boolean;
}

export interface ExecuteCommandResponse {
  exitCode: number;
  stdout: string;
  stderr: string;
  duration: number;
  timedOut: boolean;
}

export interface FileOperation {
  type: 'upload' | 'download' | 'create' | 'delete' | 'move' | 'copy' | 'read' | 'write' | 'mkdir' | 'list';
  source: string;
  destination?: string;
  content?: string | Buffer;
  permissions?: string;
  owner?: string;
  group?: string;
}

export interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory' | 'symlink';
  size: number;
  permissions: string;
  owner: string;
  group: string;
  modified: Date;
  created?: Date;
  isHidden: boolean;
  mimeType?: string;
  extension?: string;
}

export interface FileSystemResponse {
  success: boolean;
  data?: any;
  error?: string;
  path: string;
  operation: string;
  timestamp: Date;
}

export interface DirectoryListing {
  path: string;
  items: FileSystemItem[];
  totalItems: number;
  totalSize: number;
  permissions: {
    readable: boolean;
    writable: boolean;
    executable: boolean;
  };
}

export interface FileContent {
  path: string;
  content: string | Buffer;
  encoding: 'utf8' | 'base64' | 'binary';
  size: number;
  mimeType: string;
  lastModified: Date;
}

export interface FileUploadRequest {
  path: string;
  content: string | Buffer;
  encoding?: 'utf8' | 'base64' | 'binary';
  permissions?: string;
  overwrite?: boolean;
  createDirectories?: boolean;
}

export interface FileSearchRequest {
  path: string;
  pattern: string;
  recursive?: boolean;
  includeHidden?: boolean;
  fileType?: 'file' | 'directory' | 'all';
  maxResults?: number;
}

export interface FileSearchResult {
  items: FileSystemItem[];
  totalMatches: number;
  searchTime: number;
  query: FileSearchRequest;
}

export interface SandboxEvent {
  id: string;
  sandboxId: string;
  type: EventType;
  timestamp: Date;
  data: Record<string, any>;
  source: string;
}

export type EventType =
  | 'created'
  | 'started'
  | 'stopped'
  | 'destroyed'
  | 'error'
  | 'resource_limit_exceeded'
  | 'health_check_failed'
  | 'network_activity'
  | 'file_system_activity'
  | 'process_started'
  | 'process_stopped'
  | 'package_installed'
  | 'package_removed'
  | 'package_updated';

export interface PackageManager {
  name: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'poetry' | 'apt' | 'yum' | 'cargo' | 'go' | 'maven' | 'gradle';
  version: string;
  available: boolean;
  configPath?: string;
  lockFile?: string;
}

export interface PackageInfo {
  name: string;
  version: string;
  description?: string;
  homepage?: string;
  repository?: string;
  license?: string;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  size?: number;
  installDate?: Date;
  updateAvailable?: string;
}

export interface PackageInstallRequest {
  packages: string[];
  manager?: string;
  version?: string;
  dev?: boolean;
  global?: boolean;
  save?: boolean;
  exact?: boolean;
  optional?: boolean;
  workingDir?: string;
  timeout?: number;
}

export interface PackageInstallResponse {
  success: boolean;
  packages: string[];
  manager: string;
  operation: 'install' | 'uninstall' | 'update' | 'list';
  output: string;
  error?: string;
  duration: number;
  timestamp: Date;
}

export interface PackageListResponse {
  manager: string;
  packages: PackageInfo[];
  totalPackages: number;
  outdatedPackages: number;
  workingDir: string;
  timestamp: Date;
}

export interface PackageSearchRequest {
  query: string;
  manager: string;
  limit?: number;
  exact?: boolean;
}

export interface PackageSearchResult {
  name: string;
  version: string;
  description: string;
  downloads?: number;
  rating?: number;
  homepage?: string;
  repository?: string;
  license?: string;
  keywords?: string[];
}

export interface NetworkInterface {
  name: string;
  type: 'ethernet' | 'bridge' | 'loopback' | 'tunnel';
  state: 'up' | 'down' | 'unknown';
  ipAddress: string;
  netmask: string;
  gateway?: string;
  macAddress: string;
  mtu: number;
  rxBytes: number;
  txBytes: number;
  rxPackets: number;
  txPackets: number;
}

export interface NetworkPort {
  containerPort: number;
  hostPort?: number;
  protocol: 'tcp' | 'udp';
  state: 'open' | 'closed' | 'listening';
  processId?: number;
  processName?: string;
  description?: string;
  public: boolean;
  proxyEnabled?: boolean;
  proxyUrl?: string;
}

export interface NetworkRule {
  id: string;
  type: 'allow' | 'deny';
  direction: 'inbound' | 'outbound';
  protocol: 'tcp' | 'udp' | 'icmp' | 'all';
  sourceIp?: string;
  sourcePort?: number;
  destinationIp?: string;
  destinationPort?: number;
  description?: string;
  enabled: boolean;
  priority: number;
}

export interface NetworkConfiguration {
  internetAccess: boolean;
  inboundAccess: boolean;
  dnsServers: string[];
  allowedHosts: string[];
  blockedHosts: string[];
  portMappings: NetworkPort[];
  firewallRules: NetworkRule[];
  bandwidth?: {
    uploadLimit?: number; // KB/s
    downloadLimit?: number; // KB/s
  };
  proxy?: {
    enabled: boolean;
    type: 'http' | 'socks5';
    host: string;
    port: number;
    auth?: {
      username: string;
      password: string;
    };
  };
}

export interface NetworkStatusDetailed {
  interfaces: NetworkInterface[];
  ports: NetworkPort[];
  connections: NetworkConnection[];
  dnsResolution: boolean;
  internetConnectivity: boolean;
  firewallEnabled: boolean;
  proxyEnabled: boolean;
  bandwidth: {
    upload: number;
    download: number;
  };
}



export interface NetworkConfigRequest {
  internetAccess?: boolean;
  inboundAccess?: boolean;
  dnsServers?: string[];
  allowedHosts?: string[];
  blockedHosts?: string[];
  portMappings?: Omit<NetworkPort, 'state' | 'processId' | 'processName'>[];
  firewallRules?: Omit<NetworkRule, 'id'>[];
  bandwidth?: {
    uploadLimit?: number;
    downloadLimit?: number;
  };
}

export interface NetworkResponse {
  success: boolean;
  operation: string;
  configuration?: NetworkConfiguration;
  status?: NetworkStatusDetailed;
  error?: string;
  timestamp: Date;
}

export interface ProxyTarget {
  id: string;
  name: string;
  containerPort: number;
  protocol: 'http' | 'https' | 'ws' | 'wss';
  path: string;
  targetUrl: string;
  publicUrl: string;
  enabled: boolean;
  healthCheck?: {
    enabled: boolean;
    path: string;
    interval: number;
    timeout: number;
    retries: number;
  };
  ssl?: {
    enabled: boolean;
    cert?: string;
    key?: string;
  };
  auth?: {
    enabled: boolean;
    type: 'basic' | 'bearer' | 'custom';
    credentials?: Record<string, string>;
  };
  rateLimit?: {
    enabled: boolean;
    requests: number;
    window: number; // seconds
  };
  cors?: {
    enabled: boolean;
    origins: string[];
    methods: string[];
    headers: string[];
  };
  createdAt: Date;
  lastAccessed?: Date;
  accessCount: number;
}

export interface ProxyConfiguration {
  enabled: boolean;
  domain: string;
  port: number;
  ssl: boolean;
  targets: ProxyTarget[];
  middleware: {
    compression: boolean;
    logging: boolean;
    security: boolean;
    rateLimit: boolean;
  };
  loadBalancing?: {
    enabled: boolean;
    algorithm: 'round-robin' | 'least-connections' | 'ip-hash';
  };
}

export interface ProxyStats {
  totalRequests: number;
  activeConnections: number;
  bytesTransferred: number;
  averageResponseTime: number;
  errorRate: number;
  uptime: number;
  targets: Array<{
    id: string;
    requests: number;
    errors: number;
    avgResponseTime: number;
    status: 'healthy' | 'unhealthy' | 'unknown';
  }>;
}

export interface ProxyRequest {
  name: string;
  containerPort: number;
  protocol?: 'http' | 'https' | 'ws' | 'wss';
  path?: string;
  subdomain?: string;
  customDomain?: string;
  healthCheck?: {
    enabled?: boolean;
    path?: string;
    interval?: number;
    timeout?: number;
    retries?: number;
  };
  ssl?: {
    enabled?: boolean;
    autoGenerate?: boolean;
  };
  auth?: {
    enabled?: boolean;
    type?: 'basic' | 'bearer' | 'custom';
    credentials?: Record<string, string>;
  };
  rateLimit?: {
    enabled?: boolean;
    requests?: number;
    window?: number;
  };
  cors?: {
    enabled?: boolean;
    origins?: string[];
    methods?: string[];
    headers?: string[];
  };
}

export interface ProxyResponse {
  success: boolean;
  operation: string;
  target?: ProxyTarget;
  targets?: ProxyTarget[];
  stats?: ProxyStats;
  error?: string;
  timestamp: Date;
}
