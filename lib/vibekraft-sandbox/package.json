{"name": "@vibekraft/sandbox", "version": "1.0.0", "description": "VibeKraft Sandbox Infrastructure - A comprehensive sandbox-as-a-service platform with enhanced customizability", "main": "index.js", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "docker:build": "docker build -f docker/base/Dockerfile -t vibekraft/sandbox-base:latest .", "docker:build-api": "docker build -f docker/api/Dockerfile -t vibekraft/sandbox-api:latest .", "docker:up": "cd docker/compose && docker-compose up -d", "docker:down": "cd docker/compose && docker-compose down", "docker:logs": "cd docker/compose && docker-compose logs -f", "docker:clean": "docker system prune -f && docker volume prune -f", "setup": "pnpm install && pnpm docker:build", "setup:dev": "pnpm install && pnpm docker:up", "validate:templates": "node scripts/validate-templates.js", "generate:types": "node scripts/generate-types.js", "docs:build": "typedoc --out docs src", "docs:serve": "serve docs"}, "keywords": ["sandbox", "containerization", "docker", "development", "environment", "vibekraft", "nextjs", "react", "typescript"], "author": "VibeKraft Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vibekraft/sandbox.git"}, "bugs": {"url": "https://github.com/vibekraft/sandbox/issues"}, "homepage": "https://github.com/vibekraft/sandbox#readme", "dependencies": {"@types/node": "^20.5.0", "dockerode": "^3.3.5", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "rate-limiter-flexible": "^3.0.8", "ws": "^8.13.0", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "zod": "^3.22.2", "prisma": "^5.2.0", "@prisma/client": "^5.2.0", "redis": "^4.6.7", "ioredis": "^5.3.2", "bull": "^4.11.3", "node-cron": "^3.0.2", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "prom-client": "^14.2.0", "opentelemetry": "^0.1.0", "dotenv": "^16.3.1", "config": "^3.3.9", "lodash": "^4.17.21", "uuid": "^9.0.0", "moment": "^2.29.4", "axios": "^1.5.0", "form-data": "^4.0.0", "multer": "^1.4.5-lts.1", "archiver": "^5.3.1", "unzipper": "^0.10.14", "tar": "^6.1.15", "mime-types": "^2.1.35", "sharp": "^0.32.5", "pdf-parse": "^1.1.1", "csv-parser": "^3.0.0", "xml2js": "^0.6.2", "yaml": "^2.3.2", "markdown-it": "^13.0.1", "highlight.js": "^11.8.0", "prismjs": "^1.29.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/ws": "^8.5.5", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/lodash": "^4.14.196", "@types/uuid": "^9.0.2", "@types/multer": "^1.4.7", "@types/archiver": "^5.3.2", "@types/unzipper": "^0.10.6", "@types/tar": "^6.1.5", "@types/mime-types": "^2.1.1", "@types/xml2js": "^0.4.11", "@types/markdown-it": "^13.0.1", "@types/jest": "^29.5.4", "@types/supertest": "^2.0.12", "typescript": "^5.1.6", "ts-node": "^10.9.1", "ts-jest": "^29.1.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.2", "nodemon": "^3.0.1", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "rimraf": "^5.0.1", "typedoc": "^0.24.8", "serve": "^14.2.1", "husky": "^8.0.3", "lint-staged": "^14.0.1", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "next": "^13.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.6.12", "files": ["core/", "components/", "hooks/", "types/", "utils/", "config/", "docker/", "scripts/", "README.md", "LICENSE", "package.json"], "exports": {".": {"import": "./index.js", "require": "./index.cjs", "types": "./index.d.ts"}, "./components": {"import": "./components/index.js", "require": "./components/index.cjs", "types": "./components/index.d.ts"}, "./hooks": {"import": "./hooks/index.js", "require": "./hooks/index.cjs", "types": "./hooks/index.d.ts"}, "./types": {"import": "./types/index.js", "require": "./types/index.cjs", "types": "./types/index.d.ts"}, "./utils": {"import": "./utils/index.js", "require": "./utils/index.cjs", "types": "./utils/index.d.ts"}}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "transform": {"^.+\\.ts$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/**/*.test.ts", "!src/**/*.spec.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.ts"]}, "eslintConfig": {"extends": ["@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off"}, "env": {"node": true, "es6": true}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}