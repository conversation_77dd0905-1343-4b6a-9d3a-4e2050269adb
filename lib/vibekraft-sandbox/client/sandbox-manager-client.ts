/**
 * VibeKraft Sandbox Manager Client
 * 
 * Client-side sandbox manager that uses API routes instead of direct Docker operations
 */

import { sandboxAPI, SandboxAPIClient, CreateSandboxRequest, SandboxConfig, SandboxStatus } from '../api/sandbox-client';

export interface SandboxManagerClientConfig {
  apiBaseUrl?: string;
  enableImageOptimization?: boolean;
  autoOptimizeImages?: boolean;
  maxConcurrentSandboxes?: number;
  defaultResourceLimits?: {
    memory?: number;
    cpus?: number;
    diskQuota?: number;
  };
  networkConfig?: {
    defaultNetwork?: string;
    enableNetworking?: boolean;
  };
  securityConfig?: {
    allowPrivileged?: boolean;
    defaultUser?: string;
    readOnlyRootfs?: boolean;
  };
}

export class SandboxManagerClient {
  private api: SandboxAPIClient;
  private config: SandboxManagerClientConfig;

  constructor(config: SandboxManagerClientConfig = {}) {
    this.config = {
      enableImageOptimization: true,
      autoOptimizeImages: true,
      maxConcurrentSandboxes: 50,
      defaultResourceLimits: {
        memory: 2 * 1024 * 1024 * 1024, // 2GB
        cpus: 2,
        diskQuota: 10 * 1024 * 1024 * 1024 // 10GB
      },
      networkConfig: {
        defaultNetwork: 'vibekraft-sandbox-network',
        enableNetworking: true
      },
      securityConfig: {
        allowPrivileged: false,
        defaultUser: '1001:1001',
        readOnlyRootfs: false
      },
      ...config
    };

    this.api = config.apiBaseUrl ? new SandboxAPIClient(config.apiBaseUrl) : sandboxAPI;
  }

  // Sandbox lifecycle management
  async createSandbox(request: CreateSandboxRequest): Promise<string> {
    // Apply default configurations
    const sandboxConfig: CreateSandboxRequest = {
      ...request,
      resources: {
        vcpuCount: 1,
        memSizeMib: 512,
        diskSizeGb: 5,
        diskType: 'ssd',
        ...request.resources
      },
      network: {
        isolated: false,
        ports: [],
        ...request.network
      },
      security: {
        readOnlyRootfs: this.config.securityConfig?.readOnlyRootfs || false,
        noNewPrivileges: true,
        allowSudo: false,
        allowNetworkAccess: true,
        allowFileSystemAccess: true,
        ...request.security
      },
      environment: {
        NODE_ENV: 'development',
        ...request.environment
      },
      labels: {
        'vibekraft.managed': 'true',
        'vibekraft.version': '1.0.0',
        ...request.labels
      },
      annotations: {
        'vibekraft.created-by': 'sandbox-manager-client',
        'vibekraft.created-at': new Date().toISOString(),
        ...request.annotations
      }
    };

    // Auto-optimize template if enabled
    if (this.config.autoOptimizeImages && this.config.enableImageOptimization) {
      sandboxConfig.template = this.optimizeTemplate(sandboxConfig.template, sandboxConfig.resources);
    }

    const result = await this.api.createSandbox(sandboxConfig);
    return result.id;
  }

  async listSandboxes(filters?: {
    projectId?: string;
    status?: string;
    template?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ sandboxes: SandboxConfig[]; total: number }> {
    return this.api.listSandboxes(filters);
  }

  async getSandbox(sandboxId: string): Promise<SandboxConfig> {
    return this.api.getSandbox(sandboxId);
  }

  async deleteSandbox(sandboxId: string, options?: { force?: boolean; deleteVolumes?: boolean }): Promise<void> {
    return this.api.deleteSandbox(sandboxId, options);
  }

  // Sandbox operations
  async startSandbox(sandboxId: string, options?: { waitForHealthy?: boolean; timeout?: number }): Promise<void> {
    return this.api.startSandbox(sandboxId, options);
  }

  async stopSandbox(sandboxId: string, options?: { force?: boolean; timeout?: number }): Promise<void> {
    return this.api.stopSandbox(sandboxId, options);
  }

  async restartSandbox(sandboxId: string, options?: { preserveData?: boolean; waitForHealthy?: boolean }): Promise<void> {
    return this.api.restartSandbox(sandboxId, options);
  }

  async getSandboxStatus(sandboxId: string): Promise<SandboxStatus> {
    return this.api.getSandboxStatus(sandboxId);
  }

  // Command execution
  async executeCommand(sandboxId: string, command: string[], options?: {
    workingDir?: string;
    environment?: Record<string, string>;
    timeout?: number;
    user?: string;
  }) {
    return this.api.executeCommand(sandboxId, {
      command,
      workingDir: options?.workingDir || '/workspace',
      environment: options?.environment,
      timeout: options?.timeout || 30000,
      user: options?.user || 'vibekraft'
    });
  }

  // File operations
  async readFile(sandboxId: string, path: string, encoding: 'utf8' | 'base64' = 'utf8') {
    return this.api.readFile(sandboxId, path, encoding);
  }

  async writeFile(sandboxId: string, path: string, content: string, encoding: 'utf8' | 'base64' = 'utf8') {
    return this.api.writeFile(sandboxId, { path, content, encoding });
  }

  async deleteFile(sandboxId: string, path: string, recursive: boolean = false) {
    return this.api.deleteFile(sandboxId, path, recursive);
  }

  async listFiles(sandboxId: string, path: string = '/workspace') {
    return this.api.listFiles(sandboxId, path);
  }

  // Package management
  async installPackages(sandboxId: string, manager: 'npm' | 'pip' | 'apt' | 'cargo', packages: string[], options?: string[]) {
    return this.api.managePackages(sandboxId, {
      manager,
      operation: 'install',
      packages,
      options
    });
  }

  async uninstallPackages(sandboxId: string, manager: 'npm' | 'pip' | 'apt' | 'cargo', packages: string[]) {
    return this.api.managePackages(sandboxId, {
      manager,
      operation: 'uninstall',
      packages
    });
  }

  async listPackages(sandboxId: string, manager: 'npm' | 'pip' | 'apt' | 'cargo') {
    return this.api.managePackages(sandboxId, {
      manager,
      operation: 'list'
    });
  }

  // Bulk operations
  async bulkStart(sandboxIds: string[], options?: { waitForHealthy?: boolean; timeout?: number }) {
    return this.api.bulkOperation('start', sandboxIds, options);
  }

  async bulkStop(sandboxIds: string[], options?: { force?: boolean; timeout?: number }) {
    return this.api.bulkOperation('stop', sandboxIds, options);
  }

  async bulkRestart(sandboxIds: string[], options?: { preserveData?: boolean; waitForHealthy?: boolean }) {
    return this.api.bulkOperation('restart', sandboxIds, options);
  }

  async bulkDelete(sandboxIds: string[], options?: { force?: boolean; deleteVolumes?: boolean }) {
    return this.api.bulkOperation('destroy', sandboxIds, options);
  }

  // Template optimization
  private optimizeTemplate(template: string, resources?: { vcpuCount?: number; memSizeMib?: number }): string {
    // Template optimization logic
    const templateMap: Record<string, string> = {
      'node:18': 'vibekraft/nodejs-minimal:latest',
      'node:18-alpine': 'vibekraft/nodejs-minimal:latest',
      'node:latest': 'vibekraft/nodejs-minimal:latest',
      'python:3.11': 'vibekraft/python-minimal:latest',
      'python:3.11-alpine': 'vibekraft/python-minimal:latest',
      'python:latest': 'vibekraft/python-minimal:latest',
      'ubuntu:22.04': 'vibekraft/fullstack-dev:latest',
      'ubuntu:latest': 'vibekraft/fullstack-dev:latest'
    };

    // Check if template should be optimized
    const optimizedTemplate = templateMap[template];
    if (optimizedTemplate) {
      // Consider resource requirements
      if (resources && resources.memSizeMib && resources.memSizeMib <= 512) {
        // For low memory, prefer minimal templates
        if (template.includes('node')) {
          return 'vibekraft/nodejs-minimal:latest';
        } else if (template.includes('python')) {
          return 'vibekraft/python-minimal:latest';
        }
      }
      return optimizedTemplate;
    }

    return template;
  }

  // Template management
  async listTemplates(filters?: {
    language?: string;
    packageManager?: string;
    maxSize?: 'minimal' | 'standard' | 'full';
  }) {
    return this.api.listTemplates(filters);
  }

  async buildTemplates(templateIds?: string[]) {
    return this.api.buildTemplates(templateIds);
  }

  async getTemplateAnalysis() {
    return this.api.getTemplateAnalysis();
  }

  // Health and monitoring
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    sandboxes: {
      total: number;
      running: number;
      stopped: number;
      error: number;
    };
    resources: {
      totalMemory: number;
      usedMemory: number;
      totalCpu: number;
      usedCpu: number;
    };
  }> {
    try {
      const sandboxes = await this.listSandboxes();
      
      // Get status for all sandboxes
      const statusPromises = sandboxes.sandboxes.map(async (sandbox) => {
        try {
          return await this.getSandboxStatus(sandbox.id);
        } catch {
          return { state: 'error' as const };
        }
      });
      
      const statuses = await Promise.all(statusPromises);
      
      const counts = statuses.reduce(
        (acc, status) => {
          acc[status.state] = (acc[status.state] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      return {
        status: counts.error > 0 ? 'unhealthy' : counts.running > 0 ? 'healthy' : 'degraded',
        sandboxes: {
          total: sandboxes.total,
          running: counts.running || 0,
          stopped: counts.stopped || 0,
          error: counts.error || 0
        },
        resources: {
          totalMemory: this.config.maxConcurrentSandboxes! * 2048, // Estimate
          usedMemory: (counts.running || 0) * 512, // Estimate
          totalCpu: this.config.maxConcurrentSandboxes! * 2, // Estimate
          usedCpu: (counts.running || 0) * 1 // Estimate
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        sandboxes: { total: 0, running: 0, stopped: 0, error: 0 },
        resources: { totalMemory: 0, usedMemory: 0, totalCpu: 0, usedCpu: 0 }
      };
    }
  }

  // Configuration
  getConfig(): SandboxManagerClientConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<SandboxManagerClientConfig>): void {
    this.config = { ...this.config, ...updates };
  }
}

// Default client instance
export const sandboxManagerClient = new SandboxManagerClient();
