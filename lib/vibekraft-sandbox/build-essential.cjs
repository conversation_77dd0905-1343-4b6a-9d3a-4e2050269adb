#!/usr/bin/env node

/**
 * VibeKraft Essential Images Builder
 * 
 * Builds optimized Docker images for Python, Node.js, and Full-stack development
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 VibeKraft Essential Images Builder\n');

// Check if Docker is available
function checkDockerAvailable() {
  try {
    execSync('docker --version', { stdio: 'pipe' });
    console.log('✅ Docker is available');
    return true;
  } catch (error) {
    console.error('❌ Docker is not available. Please install Docker first.');
    return false;
  }
}

// Essential optimized Dockerfiles
const dockerfiles = {
  'nodejs-minimal': `
# VibeKraft Node.js Minimal - Optimized for size and performance
FROM node:18-alpine

# Create non-root user (handle existing group gracefully)
RUN addgroup -g 1001 vibekraft 2>/dev/null || true \\
    && adduser -D -s /bin/sh -u 1001 -G vibekraft vibekraft 2>/dev/null || adduser -D -s /bin/sh -u 1001 vibekraft

# Install runtime dependencies and TypeScript
RUN apk add --no-cache ca-certificates \\
    && npm install -g typescript @types/node \\
    && npm cache clean --force \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/*

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD node --version || exit 1

CMD ["/bin/sh"]
`,

  'python-minimal': `
# VibeKraft Python Minimal - Optimized for size and performance
FROM python:3.11-alpine

# Create non-root user (handle existing group gracefully)
RUN addgroup -g 1001 vibekraft 2>/dev/null || true \\
    && adduser -D -s /bin/sh -u 1001 -G vibekraft vibekraft 2>/dev/null || adduser -D -s /bin/sh -u 1001 vibekraft

# Install runtime dependencies and common Python packages
RUN apk add --no-cache ca-certificates \\
    && pip install --no-cache-dir \\
        virtualenv \\
        requests \\
        click \\
    && find /usr/local -name "*.pyc" -delete \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/*

# Configure pip for optimization
RUN pip config set global.cache-dir /tmp/pip-cache \\
    && pip config set global.no-cache-dir true

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD python --version || exit 1

CMD ["/bin/sh"]
`,

  'fullstack-dev': `
# VibeKraft Full-Stack Development - Optimized multi-language environment
FROM ubuntu:22.04

# Prevent interactive prompts
ENV DEBIAN_FRONTEND=noninteractive

# Create non-root user
RUN groupadd -r vibekraft && useradd -r -g vibekraft -u 1001 vibekraft

# Configure package manager for minimal installs
RUN echo 'APT::Install-Recommends "0";' >> /etc/apt/apt.conf.d/01norecommend \\
    && echo 'APT::Install-Suggests "0";' >> /etc/apt/apt.conf.d/01norecommend

# Install Node.js repository and update packages
RUN apt-get update \\
    && apt-get install -y --no-install-recommends curl ca-certificates gnupg \\
    && curl -fsSL https://deb.nodesource.com/setup_18.x | bash -

# Install all dependencies in a single layer
RUN apt-get update && apt-get install -y --no-install-recommends \\
    ca-certificates \\
    curl \\
    wget \\
    git \\
    vim \\
    nano \\
    nodejs \\
    python3 \\
    python3-pip \\
    python3-venv \\
    build-essential \\
    && npm install -g typescript @types/node \\
    && pip3 install --no-cache-dir virtualenv requests \\
    && apt-get autoremove -y \\
    && apt-get clean \\
    && rm -rf /var/lib/apt/lists/* \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/* \\
    && rm -rf /usr/share/doc/* \\
    && rm -rf /usr/share/man/* \\
    && find /var/log -type f -delete \\
    && npm cache clean --force

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Set environment variables
ENV NODE_ENV=development
ENV PYTHONPATH=/workspace
ENV PATH="/workspace/node_modules/.bin:$PATH"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD node --version && python3 --version || exit 1

CMD ["/bin/bash"]
`
};

// Build a single image
async function buildImage(name, dockerfile) {
  console.log(`🔨 Building ${name}...`);
  
  const buildDir = path.join(__dirname, 'build', name);
  
  // Create build directory
  if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir, { recursive: true });
  }
  
  // Write Dockerfile
  fs.writeFileSync(path.join(buildDir, 'Dockerfile'), dockerfile);
  
  const imageName = `vibekraft/${name}:latest`;
  
  return new Promise((resolve, reject) => {
    console.log(`   Building image: ${imageName}`);
    
    const buildProcess = spawn('docker', [
      'build',
      '-t', imageName,
      '--no-cache',
      '.'
    ], {
      cwd: buildDir,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    let errorOutput = '';
    
    buildProcess.stdout.on('data', (data) => {
      output += data.toString();
      // Show progress for important steps
      const line = data.toString().trim();
      if (line.includes('Step ') || line.includes('Successfully built') || line.includes('Successfully tagged')) {
        console.log(`   ${line}`);
      }
    });
    
    buildProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log(`   ✅ Successfully built ${imageName}`);
        
        // Get image size
        try {
          const sizeOutput = execSync(`docker images ${imageName} --format "table {{.Size}}"`, { encoding: 'utf8' });
          const size = sizeOutput.split('\n')[1]?.trim();
          if (size) {
            console.log(`   📦 Image size: ${size}`);
          }
        } catch (error) {
          // Size check failed, but build succeeded
        }
        
        resolve({ name, imageName, success: true });
      } else {
        console.error(`   ❌ Failed to build ${imageName}`);
        if (errorOutput) {
          console.error(`   Error: ${errorOutput.slice(-500)}`); // Show last 500 chars
        }
        reject({ name, imageName, success: false, error: errorOutput });
      }
    });
  });
}

// Main build function
async function buildEssentialImages() {
  if (!checkDockerAvailable()) {
    process.exit(1);
  }
  
  console.log(`\n🏗️  Building ${Object.keys(dockerfiles).length} essential optimized images...\n`);
  
  const results = [];
  const startTime = Date.now();
  
  for (const [name, dockerfile] of Object.entries(dockerfiles)) {
    try {
      const result = await buildImage(name, dockerfile);
      results.push(result);
    } catch (error) {
      results.push(error);
    }
    console.log(); // Add spacing between builds
  }
  
  const endTime = Date.now();
  const totalTime = Math.round((endTime - startTime) / 1000);
  
  // Summary
  console.log('📊 Build Summary');
  console.log('================\n');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful builds: ${successful.length}`);
  successful.forEach(result => {
    console.log(`   - ${result.imageName}`);
  });
  
  if (failed.length > 0) {
    console.log(`\n❌ Failed builds: ${failed.length}`);
    failed.forEach(result => {
      console.log(`   - ${result.imageName}`);
    });
  }
  
  console.log(`\n⏱️  Total build time: ${totalTime} seconds`);
  
  // Show all built images
  if (successful.length > 0) {
    console.log('\n📦 Built Images:');
    try {
      const imagesOutput = execSync('docker images vibekraft/* --format "table {{.Repository}}:{{.Tag}}\\t{{.Size}}\\t{{.CreatedAt}}"', { encoding: 'utf8' });
      console.log(imagesOutput);
    } catch (error) {
      console.log('   (Unable to list images)');
    }
  }
  
  // Test the images
  console.log('\n🧪 Quick Tests:');
  console.log('===============');
  for (const result of successful) {
    try {
      console.log(`Testing ${result.name}...`);
      if (result.name === 'nodejs-minimal') {
        const nodeVersion = execSync(`docker run --rm ${result.imageName} node --version`, { encoding: 'utf8' }).trim();
        const tscVersion = execSync(`docker run --rm ${result.imageName} tsc --version`, { encoding: 'utf8' }).trim();
        console.log(`   ✅ Node.js: ${nodeVersion}, TypeScript: ${tscVersion}`);
      } else if (result.name === 'python-minimal') {
        const pythonVersion = execSync(`docker run --rm ${result.imageName} python --version`, { encoding: 'utf8' }).trim();
        console.log(`   ✅ ${pythonVersion}`);
      } else if (result.name === 'fullstack-dev') {
        const nodeVersion = execSync(`docker run --rm ${result.imageName} node --version`, { encoding: 'utf8' }).trim();
        const pythonVersion = execSync(`docker run --rm ${result.imageName} python3 --version`, { encoding: 'utf8' }).trim();
        console.log(`   ✅ Node.js: ${nodeVersion}, ${pythonVersion}`);
      }
    } catch (error) {
      console.log(`   ⚠️  Test failed for ${result.name}`);
    }
  }
  
  // Next steps
  console.log('\n🚀 Next Steps:');
  console.log('==============');
  console.log('1. Test the images:');
  console.log('   docker run -it vibekraft/nodejs-minimal:latest');
  console.log('   docker run -it vibekraft/python-minimal:latest');
  console.log('   docker run -it vibekraft/fullstack-dev:latest');
  console.log('');
  console.log('2. Use in VibeKraft sandbox:');
  console.log('   The images are now available for the sandbox manager!');
  console.log('');
  console.log('3. Expected optimizations:');
  console.log('   - Node.js: ~150MB (vs 400MB standard)');
  console.log('   - Python: ~120MB (vs 350MB standard)');
  console.log('   - Full-stack: ~600MB (vs 1.2GB standard)');
  
  return results;
}

// Run if called directly
if (require.main === module) {
  buildEssentialImages()
    .then((results) => {
      const successful = results.filter(r => r.success).length;
      const total = results.length;
      
      if (successful === total) {
        console.log(`\n🎉 All ${total} essential images built successfully!`);
        process.exit(0);
      } else {
        console.log(`\n⚠️  ${successful}/${total} images built successfully.`);
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Build process failed:', error);
      process.exit(1);
    });
}

module.exports = { buildEssentialImages, buildImage, dockerfiles };
