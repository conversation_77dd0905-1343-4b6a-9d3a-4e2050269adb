/**
 * VibeKraft Sandbox API Client Usage Examples
 * 
 * Examples showing how to use the API-based sandbox infrastructure
 */

'use client';

import React, { useState } from 'react';
import { 
  SandboxManager,
  DialogManagerProvider,
  sandboxManagerClient,
  sandboxAPI,
  useSandboxManager,
  useDialogManager
} from '../index';

// Example 1: Basic Sandbox Management Component
export function BasicSandboxExample() {
  return (
    <DialogManagerProvider>
      <div className="p-6">
        <h2 className="text-xl font-bold mb-4">Basic Sandbox Management</h2>
        <SandboxManager projectId="example-project" />
      </div>
    </DialogManagerProvider>
  );
}

// Example 2: Custom Hook Usage
export function CustomHookExample() {
  const { sandboxes, createSandbox, loading, error } = useSandboxManager();
  const { showConfirmation } = useDialogManager();
  const [creating, setCreating] = useState(false);

  const handleCreateSandbox = async () => {
    try {
      setCreating(true);
      await createSandbox({
        name: 'My Custom Sandbox',
        template: 'vibekraft/nodejs-minimal:latest',
        resources: {
          vcpuCount: 1,
          memSizeMib: 512,
          diskSizeGb: 5
        },
        environment: {
          NODE_ENV: 'development'
        }
      });
    } catch (error) {
      console.error('Failed to create sandbox:', error);
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteSandbox = (sandboxId: string, sandboxName: string) => {
    showConfirmation({
      title: 'Delete Sandbox',
      message: `Are you sure you want to delete "${sandboxName}"? This action cannot be undone.`,
      type: 'danger',
      onConfirm: async () => {
        // Delete logic handled by the dialog
      }
    });
  };

  if (loading) {
    return <div className="p-6">Loading sandboxes...</div>;
  }

  if (error) {
    return <div className="p-6 text-red-600">Error: {error}</div>;
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Custom Hook Example</h2>
        <button
          onClick={handleCreateSandbox}
          disabled={creating}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {creating ? 'Creating...' : 'Create Sandbox'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sandboxes.map((sandbox) => (
          <div key={sandbox.id} className="border rounded-lg p-4">
            <h3 className="font-medium">{sandbox.name}</h3>
            <p className="text-sm text-gray-500">{sandbox.template}</p>
            <p className="text-sm text-gray-500">Status: {sandbox.state}</p>
            <div className="mt-2 flex space-x-2">
              <button
                onClick={() => handleDeleteSandbox(sandbox.id, sandbox.name)}
                className="px-2 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Example 3: Direct API Client Usage
export function DirectAPIExample() {
  const [sandboxes, setSandboxes] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadSandboxes = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await sandboxAPI.listSandboxes();
      setSandboxes(result.sandboxes);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load sandboxes');
    } finally {
      setLoading(false);
    }
  };

  const createSandbox = async () => {
    try {
      setError(null);
      const result = await sandboxAPI.createSandbox({
        name: 'API Example Sandbox',
        template: 'vibekraft/python-minimal:latest',
        resources: {
          vcpuCount: 1,
          memSizeMib: 256,
          diskSizeGb: 3
        }
      });
      console.log('Created sandbox:', result.id);
      await loadSandboxes(); // Refresh list
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create sandbox');
    }
  };

  const executeCommand = async (sandboxId: string) => {
    try {
      const result = await sandboxAPI.executeCommand(sandboxId, {
        command: ['python', '--version'],
        timeout: 10000
      });
      alert(`Command output: ${result.stdout}`);
    } catch (err) {
      alert(`Command failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  React.useEffect(() => {
    loadSandboxes();
  }, []);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Direct API Client Example</h2>
        <div className="space-x-2">
          <button
            onClick={loadSandboxes}
            disabled={loading}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Refresh'}
          </button>
          <button
            onClick={createSandbox}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Create Python Sandbox
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-300 text-red-700 rounded">
          {error}
        </div>
      )}

      <div className="space-y-4">
        {sandboxes.map((sandbox) => (
          <div key={sandbox.id} className="border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">{sandbox.name}</h3>
                <p className="text-sm text-gray-500">ID: {sandbox.id}</p>
                <p className="text-sm text-gray-500">Template: {sandbox.template}</p>
              </div>
              <div className="space-x-2">
                <button
                  onClick={() => executeCommand(sandbox.id)}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                >
                  Run Command
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Example 4: Sandbox Manager Client Usage
export function SandboxManagerClientExample() {
  const [health, setHealth] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const checkHealth = async () => {
    try {
      setLoading(true);
      const healthStatus = await sandboxManagerClient.getHealthStatus();
      setHealth(healthStatus);
    } catch (error) {
      console.error('Health check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const createOptimizedSandbox = async () => {
    try {
      const sandboxId = await sandboxManagerClient.createSandbox({
        name: 'Optimized Sandbox',
        template: 'node:18', // Will be auto-optimized to vibekraft/nodejs-minimal:latest
        resources: {
          vcpuCount: 1,
          memSizeMib: 512
        }
      });
      alert(`Created optimized sandbox: ${sandboxId}`);
      await checkHealth(); // Refresh health
    } catch (error) {
      alert(`Failed to create sandbox: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  React.useEffect(() => {
    checkHealth();
  }, []);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Sandbox Manager Client Example</h2>
        <div className="space-x-2">
          <button
            onClick={checkHealth}
            disabled={loading}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50"
          >
            {loading ? 'Checking...' : 'Check Health'}
          </button>
          <button
            onClick={createOptimizedSandbox}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Create Optimized Sandbox
          </button>
        </div>
      </div>

      {health && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-2">System Health</h3>
            <div className={`inline-block px-2 py-1 rounded text-sm ${
              health.status === 'healthy' ? 'bg-green-100 text-green-800' :
              health.status === 'degraded' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {health.status.toUpperCase()}
            </div>
          </div>

          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-2">Sandboxes</h3>
            <div className="text-sm space-y-1">
              <div>Total: {health.sandboxes.total}</div>
              <div>Running: {health.sandboxes.running}</div>
              <div>Stopped: {health.sandboxes.stopped}</div>
              <div>Error: {health.sandboxes.error}</div>
            </div>
          </div>
        </div>
      )}

      <div className="bg-gray-50 border rounded-lg p-4">
        <h3 className="font-medium mb-2">Configuration</h3>
        <pre className="text-sm text-gray-600">
          {JSON.stringify(sandboxManagerClient.getConfig(), null, 2)}
        </pre>
      </div>
    </div>
  );
}

// Main demo component
export function APIClientDemo() {
  const [activeExample, setActiveExample] = useState<'basic' | 'hooks' | 'api' | 'client'>('basic');

  const examples = [
    { id: 'basic', label: 'Basic Sandbox Manager', component: BasicSandboxExample },
    { id: 'hooks', label: 'Custom Hooks', component: CustomHookExample },
    { id: 'api', label: 'Direct API Client', component: DirectAPIExample },
    { id: 'client', label: 'Manager Client', component: SandboxManagerClientExample }
  ];

  const ActiveComponent = examples.find(ex => ex.id === activeExample)?.component || BasicSandboxExample;

  return (
    <DialogManagerProvider>
      <div className="min-h-screen bg-gray-100">
        <div className="bg-white border-b">
          <div className="max-w-6xl mx-auto px-6 py-4">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              VibeKraft Sandbox API Client Examples
            </h1>
            <nav className="flex space-x-4">
              {examples.map((example) => (
                <button
                  key={example.id}
                  onClick={() => setActiveExample(example.id as any)}
                  className={`px-4 py-2 rounded-md text-sm font-medium ${
                    activeExample === example.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {example.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        <div className="max-w-6xl mx-auto">
          <ActiveComponent />
        </div>
      </div>
    </DialogManagerProvider>
  );
}
