/**
 * VibeKraft Application Preview/Proxy API Demo
 * 
 * Example usage of the application preview and reverse proxy API for sandbox containers
 */

// Example API calls for proxy management operations

export const proxyApiExamples = {
  
  // 1. Get all proxy targets
  async getProxyTargets(sandboxId: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy?operation=list`);
    
    if (!response.ok) {
      throw new Error('Failed to get proxy targets');
    }

    const data = await response.json();
    console.log('Proxy targets:', data);
    return data.targets;
  },

  // 2. Create proxy target
  async createProxyTarget(sandboxId: string, config: any) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'create',
        ...config
      })
    });

    if (!response.ok) {
      throw new Error('Failed to create proxy target');
    }

    const data = await response.json();
    console.log('Proxy target created:', data);
    return data.target;
  },

  // 3. Update proxy target
  async updateProxyTarget(sandboxId: string, targetId: string, updates: any) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'update',
        targetId,
        ...updates
      })
    });

    if (!response.ok) {
      throw new Error('Failed to update proxy target');
    }

    const data = await response.json();
    console.log('Proxy target updated:', data);
    return data.target;
  },

  // 4. Delete proxy target
  async deleteProxyTarget(sandboxId: string, targetId: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy?targetId=${targetId}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error('Failed to delete proxy target');
    }

    const data = await response.json();
    console.log('Proxy target deleted:', data);
    return data;
  },

  // 5. Toggle proxy target
  async toggleProxyTarget(sandboxId: string, targetId: string, enabled: boolean) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'toggle',
        targetId,
        enabled
      })
    });

    if (!response.ok) {
      throw new Error('Failed to toggle proxy target');
    }

    const data = await response.json();
    console.log('Proxy target toggled:', data);
    return data.target;
  },

  // 6. Test proxy target
  async testProxyTarget(sandboxId: string, targetId: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy?operation=test&targetId=${targetId}`);
    
    if (!response.ok) {
      throw new Error('Failed to test proxy target');
    }

    const data = await response.json();
    console.log('Proxy target test result:', data);
    return data;
  },

  // 7. Get proxy statistics
  async getProxyStats(sandboxId: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/proxy?operation=stats`);
    
    if (!response.ok) {
      throw new Error('Failed to get proxy stats');
    }

    const data = await response.json();
    console.log('Proxy statistics:', data);
    return data.stats;
  }
};

// Example usage scenarios
export const proxyScenarios = {
  
  // Scenario 1: Set up a full-stack web application
  async setupFullStackApp(sandboxId: string) {
    console.log('Setting up full-stack web application...');
    
    // Create frontend proxy (React/Next.js)
    const frontend = await proxyApiExamples.createProxyTarget(sandboxId, {
      name: 'frontend',
      containerPort: 3000,
      protocol: 'http',
      path: '/',
      subdomain: 'app',
      healthCheck: {
        enabled: true,
        path: '/',
        interval: 30,
        timeout: 5,
        retries: 3
      },
      cors: {
        enabled: true,
        origins: ['*'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        headers: ['Content-Type', 'Authorization']
      }
    });
    
    // Create backend API proxy
    const backend = await proxyApiExamples.createProxyTarget(sandboxId, {
      name: 'api',
      containerPort: 8000,
      protocol: 'http',
      path: '/api',
      subdomain: 'api',
      healthCheck: {
        enabled: true,
        path: '/api/health',
        interval: 30,
        timeout: 5,
        retries: 3
      },
      cors: {
        enabled: true,
        origins: ['*'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        headers: ['Content-Type', 'Authorization']
      },
      rateLimit: {
        enabled: true,
        requests: 100,
        window: 60
      },
      auth: {
        enabled: false // Can be enabled later
      }
    });
    
    // Create WebSocket proxy for real-time features
    const websocket = await proxyApiExamples.createProxyTarget(sandboxId, {
      name: 'websocket',
      containerPort: 8080,
      protocol: 'ws',
      path: '/ws',
      subdomain: 'ws',
      healthCheck: {
        enabled: false // WebSocket health checks are complex
      }
    });
    
    console.log('Full-stack application setup completed!');
    console.log(`Frontend: ${frontend.publicUrl}`);
    console.log(`API: ${backend.publicUrl}`);
    console.log(`WebSocket: ${websocket.publicUrl}`);
    
    return { frontend, backend, websocket };
  },

  // Scenario 2: Set up microservices architecture
  async setupMicroservices(sandboxId: string) {
    console.log('Setting up microservices architecture...');
    
    const services = [
      { name: 'user-service', port: 3001, path: '/api/users' },
      { name: 'product-service', port: 3002, path: '/api/products' },
      { name: 'order-service', port: 3003, path: '/api/orders' },
      { name: 'notification-service', port: 3004, path: '/api/notifications' }
    ];
    
    const createdServices = [];
    
    for (const service of services) {
      const proxy = await proxyApiExamples.createProxyTarget(sandboxId, {
        name: service.name,
        containerPort: service.port,
        protocol: 'http',
        path: service.path,
        subdomain: service.name.replace('-service', ''),
        healthCheck: {
          enabled: true,
          path: `${service.path}/health`,
          interval: 30,
          timeout: 5,
          retries: 3
        },
        cors: {
          enabled: true,
          origins: ['*'],
          methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
          headers: ['Content-Type', 'Authorization']
        },
        rateLimit: {
          enabled: true,
          requests: 200,
          window: 60
        }
      });
      
      createdServices.push(proxy);
      console.log(`${service.name}: ${proxy.publicUrl}`);
    }
    
    // Create API Gateway
    const gateway = await proxyApiExamples.createProxyTarget(sandboxId, {
      name: 'api-gateway',
      containerPort: 8000,
      protocol: 'http',
      path: '/api',
      subdomain: 'gateway',
      healthCheck: {
        enabled: true,
        path: '/api/health',
        interval: 30,
        timeout: 5,
        retries: 3
      },
      cors: {
        enabled: true,
        origins: ['*'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        headers: ['Content-Type', 'Authorization']
      },
      rateLimit: {
        enabled: true,
        requests: 1000,
        window: 60
      },
      auth: {
        enabled: true,
        type: 'bearer'
      }
    });
    
    console.log('Microservices architecture setup completed!');
    console.log(`API Gateway: ${gateway.publicUrl}`);
    
    return { services: createdServices, gateway };
  },

  // Scenario 3: Development environment with hot reload
  async setupDevEnvironment(sandboxId: string) {
    console.log('Setting up development environment...');
    
    // Create development server proxy
    const devServer = await proxyApiExamples.createProxyTarget(sandboxId, {
      name: 'dev-server',
      containerPort: 3000,
      protocol: 'http',
      path: '/',
      subdomain: 'dev',
      healthCheck: {
        enabled: true,
        path: '/',
        interval: 10, // More frequent checks for dev
        timeout: 3,
        retries: 2
      },
      cors: {
        enabled: true,
        origins: ['*'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        headers: ['Content-Type', 'Authorization', 'X-Requested-With']
      }
    });
    
    // Create hot reload WebSocket proxy
    const hotReload = await proxyApiExamples.createProxyTarget(sandboxId, {
      name: 'hot-reload',
      containerPort: 3001,
      protocol: 'ws',
      path: '/ws',
      subdomain: 'hmr',
      healthCheck: {
        enabled: false
      }
    });
    
    // Create API development proxy
    const devApi = await proxyApiExamples.createProxyTarget(sandboxId, {
      name: 'dev-api',
      containerPort: 8000,
      protocol: 'http',
      path: '/api',
      subdomain: 'dev-api',
      healthCheck: {
        enabled: true,
        path: '/api/health',
        interval: 15,
        timeout: 5,
        retries: 2
      },
      cors: {
        enabled: true,
        origins: ['*'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        headers: ['Content-Type', 'Authorization']
      },
      rateLimit: {
        enabled: false // Disabled for development
      }
    });
    
    console.log('Development environment setup completed!');
    console.log(`Dev Server: ${devServer.publicUrl}`);
    console.log(`Hot Reload: ${hotReload.publicUrl}`);
    console.log(`Dev API: ${devApi.publicUrl}`);
    
    return { devServer, hotReload, devApi };
  },

  // Scenario 4: Production-ready deployment
  async setupProductionDeployment(sandboxId: string) {
    console.log('Setting up production deployment...');
    
    // Create production web app proxy
    const webapp = await proxyApiExamples.createProxyTarget(sandboxId, {
      name: 'webapp',
      containerPort: 80,
      protocol: 'https',
      path: '/',
      customDomain: 'myapp.com',
      ssl: {
        enabled: true,
        autoGenerate: true
      },
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 60,
        timeout: 10,
        retries: 3
      },
      cors: {
        enabled: true,
        origins: ['https://myapp.com', 'https://www.myapp.com'],
        methods: ['GET', 'POST', 'PUT', 'DELETE'],
        headers: ['Content-Type', 'Authorization']
      },
      rateLimit: {
        enabled: true,
        requests: 1000,
        window: 60
      },
      auth: {
        enabled: false // Handled by the app
      }
    });
    
    // Create production API proxy
    const api = await proxyApiExamples.createProxyTarget(sandboxId, {
      name: 'api',
      containerPort: 8080,
      protocol: 'https',
      path: '/api',
      customDomain: 'api.myapp.com',
      ssl: {
        enabled: true,
        autoGenerate: true
      },
      healthCheck: {
        enabled: true,
        path: '/api/health',
        interval: 30,
        timeout: 10,
        retries: 5
      },
      cors: {
        enabled: true,
        origins: ['https://myapp.com', 'https://www.myapp.com'],
        methods: ['GET', 'POST', 'PUT', 'DELETE'],
        headers: ['Content-Type', 'Authorization']
      },
      rateLimit: {
        enabled: true,
        requests: 500,
        window: 60
      },
      auth: {
        enabled: true,
        type: 'bearer'
      }
    });
    
    console.log('Production deployment setup completed!');
    console.log(`Web App: ${webapp.publicUrl}`);
    console.log(`API: ${api.publicUrl}`);
    
    return { webapp, api };
  },

  // Scenario 5: Proxy management and monitoring
  async manageProxies(sandboxId: string) {
    console.log('Managing and monitoring proxies...');
    
    // Get current proxy targets
    const targets = await proxyApiExamples.getProxyTargets(sandboxId);
    console.log(`Current proxy targets: ${targets.length}`);
    
    // Test all proxy targets
    const testResults = [];
    for (const target of targets) {
      try {
        const result = await proxyApiExamples.testProxyTarget(sandboxId, target.id);
        testResults.push({
          name: target.name,
          url: target.publicUrl,
          success: result.success,
          responseTime: result.responseTime,
          statusCode: result.statusCode,
          error: result.error
        });
      } catch (error) {
        testResults.push({
          name: target.name,
          url: target.publicUrl,
          success: false,
          error: error.message
        });
      }
    }
    
    console.log('Proxy test results:');
    testResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const responseTime = result.responseTime ? ` (${result.responseTime}ms)` : '';
      console.log(`${status} ${result.name}: ${result.url}${responseTime}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    // Get proxy statistics
    const stats = await proxyApiExamples.getProxyStats(sandboxId);
    console.log('Proxy statistics:', {
      totalRequests: stats.totalRequests,
      activeConnections: stats.activeConnections,
      averageResponseTime: stats.averageResponseTime,
      errorRate: stats.errorRate,
      uptime: Math.round(stats.uptime / 1000 / 60) + ' minutes'
    });
    
    // Disable unhealthy targets
    const unhealthyTargets = testResults.filter(r => !r.success);
    for (const unhealthy of unhealthyTargets) {
      const target = targets.find(t => t.name === unhealthy.name);
      if (target && target.enabled) {
        await proxyApiExamples.toggleProxyTarget(sandboxId, target.id, false);
        console.log(`Disabled unhealthy target: ${target.name}`);
      }
    }
    
    return {
      targets,
      testResults,
      stats,
      summary: {
        total: targets.length,
        healthy: testResults.filter(r => r.success).length,
        unhealthy: testResults.filter(r => !r.success).length
      }
    };
  }
};

// Usage example:
/*
async function demoProxyOperations() {
  const sandboxId = 'your-sandbox-id';
  
  try {
    // Set up different application architectures
    await proxyScenarios.setupFullStackApp(sandboxId);
    await proxyScenarios.setupMicroservices(sandboxId);
    await proxyScenarios.setupDevEnvironment(sandboxId);
    await proxyScenarios.setupProductionDeployment(sandboxId);
    
    // Manage and monitor proxies
    await proxyScenarios.manageProxies(sandboxId);
    
    console.log('All proxy management operations completed successfully!');
  } catch (error) {
    console.error('Proxy management operation failed:', error);
  }
}
*/
