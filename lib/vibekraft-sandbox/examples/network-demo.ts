/**
 * VibeKraft Network Management API Demo
 * 
 * Example usage of the network management API for sandbox containers
 */

// Example API calls for network management operations

export const networkApiExamples = {
  
  // 1. Get network status
  async getNetworkStatus(sandboxId: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network?operation=status`);
    
    if (!response.ok) {
      throw new Error('Failed to get network status');
    }

    const data = await response.json();
    console.log('Network status:', data);
    return data;
  },

  // 2. Configure network settings
  async configureNetwork(sandboxId: string, config: any) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'configure',
        ...config
      })
    });

    if (!response.ok) {
      throw new Error('Failed to configure network');
    }

    const data = await response.json();
    console.log('Network configuration result:', data);
    return data;
  },

  // 3. Add port mapping
  async addPortMapping(sandboxId: string, port: any) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'add-port',
        ...port
      })
    });

    if (!response.ok) {
      throw new Error('Failed to add port mapping');
    }

    const data = await response.json();
    console.log('Port mapping result:', data);
    return data;
  },

  // 4. Remove port mapping
  async removePortMapping(sandboxId: string, containerPort: number, protocol: 'tcp' | 'udp' = 'tcp') {
    const params = new URLSearchParams({
      operation: 'remove-port',
      containerPort: containerPort.toString(),
      protocol
    });

    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network?${params}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error('Failed to remove port mapping');
    }

    const data = await response.json();
    console.log('Port removal result:', data);
    return data;
  },

  // 5. Add firewall rule
  async addFirewallRule(sandboxId: string, rule: any) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'add-firewall-rule',
        ...rule
      })
    });

    if (!response.ok) {
      throw new Error('Failed to add firewall rule');
    }

    const data = await response.json();
    console.log('Firewall rule result:', data);
    return data;
  },

  // 6. Test connectivity
  async testConnectivity(sandboxId: string, target: string, port?: number) {
    const params = new URLSearchParams({
      operation: 'test',
      target,
      ...(port && { port: port.toString() })
    });

    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/network?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to test connectivity');
    }

    const data = await response.json();
    console.log('Connectivity test result:', data);
    return data;
  }
};

// Example usage scenarios
export const networkScenarios = {
  
  // Scenario 1: Set up a web development environment
  async setupWebDevEnvironment(sandboxId: string) {
    console.log('Setting up web development environment...');
    
    // Configure basic network settings
    await networkApiExamples.configureNetwork(sandboxId, {
      internetAccess: true,
      inboundAccess: true,
      dnsServers: ['*******', '*******']
    });
    
    // Open common development ports
    const devPorts = [
      { containerPort: 3000, protocol: 'tcp', description: 'React/Next.js dev server', public: true },
      { containerPort: 8000, protocol: 'tcp', description: 'Python/Django dev server', public: true },
      { containerPort: 5000, protocol: 'tcp', description: 'Flask dev server', public: true },
      { containerPort: 8080, protocol: 'tcp', description: 'Alternative web server', public: true }
    ];
    
    for (const port of devPorts) {
      await networkApiExamples.addPortMapping(sandboxId, port);
    }
    
    // Add firewall rules for development
    const firewallRules = [
      {
        type: 'allow',
        direction: 'inbound',
        protocol: 'tcp',
        destinationPort: 3000,
        description: 'Allow React dev server access',
        enabled: true,
        priority: 10
      },
      {
        type: 'allow',
        direction: 'outbound',
        protocol: 'tcp',
        destinationPort: 443,
        description: 'Allow HTTPS outbound',
        enabled: true,
        priority: 20
      }
    ];
    
    for (const rule of firewallRules) {
      await networkApiExamples.addFirewallRule(sandboxId, rule);
    }
    
    console.log('Web development environment setup completed!');
    
    // Test connectivity
    const connectivityTests = [
      { target: 'google.com', port: 443 },
      { target: 'npmjs.com', port: 443 },
      { target: 'github.com', port: 443 }
    ];
    
    for (const test of connectivityTests) {
      const result = await networkApiExamples.testConnectivity(sandboxId, test.target, test.port);
      console.log(`Connectivity to ${test.target}:${test.port}: ${result.success ? 'OK' : 'FAILED'}`);
    }
    
    return await networkApiExamples.getNetworkStatus(sandboxId);
  },

  // Scenario 2: Set up a secure production-like environment
  async setupSecureEnvironment(sandboxId: string) {
    console.log('Setting up secure production-like environment...');
    
    // Configure restrictive network settings
    await networkApiExamples.configureNetwork(sandboxId, {
      internetAccess: true,
      inboundAccess: false, // Restrict inbound access
      dnsServers: ['*******', '*******'], // Use Cloudflare DNS
      blockedHosts: [
        'malicious-site.com',
        'suspicious-domain.net'
      ],
      allowedHosts: [
        'api.trusted-service.com',
        'cdn.example.com'
      ]
    });
    
    // Open only necessary ports
    await networkApiExamples.addPortMapping(sandboxId, {
      containerPort: 8080,
      protocol: 'tcp',
      description: 'Application server',
      public: false // Not publicly accessible
    });
    
    // Add strict firewall rules
    const securityRules = [
      {
        type: 'deny',
        direction: 'inbound',
        protocol: 'all',
        description: 'Deny all inbound by default',
        enabled: true,
        priority: 100
      },
      {
        type: 'allow',
        direction: 'inbound',
        protocol: 'tcp',
        destinationPort: 8080,
        sourceIp: '10.0.0.0/8', // Only allow from internal network
        description: 'Allow internal access to app',
        enabled: true,
        priority: 10
      },
      {
        type: 'deny',
        direction: 'outbound',
        protocol: 'tcp',
        destinationPort: 22,
        description: 'Block SSH outbound',
        enabled: true,
        priority: 90
      }
    ];
    
    for (const rule of securityRules) {
      await networkApiExamples.addFirewallRule(sandboxId, rule);
    }
    
    console.log('Secure environment setup completed!');
    
    return await networkApiExamples.getNetworkStatus(sandboxId);
  },

  // Scenario 3: Set up a microservices environment
  async setupMicroservicesEnvironment(sandboxId: string) {
    console.log('Setting up microservices environment...');
    
    // Configure network for microservices
    await networkApiExamples.configureNetwork(sandboxId, {
      internetAccess: true,
      inboundAccess: true,
      dnsServers: ['*******', '*******']
    });
    
    // Open ports for different services
    const servicePorts = [
      { containerPort: 3001, description: 'User Service API', public: true },
      { containerPort: 3002, description: 'Product Service API', public: true },
      { containerPort: 3003, description: 'Order Service API', public: true },
      { containerPort: 5432, description: 'PostgreSQL Database', public: false },
      { containerPort: 6379, description: 'Redis Cache', public: false },
      { containerPort: 9090, description: 'Prometheus Metrics', public: false },
      { containerPort: 3000, description: 'API Gateway', public: true }
    ];
    
    for (const port of servicePorts) {
      await networkApiExamples.addPortMapping(sandboxId, {
        containerPort: port.containerPort,
        protocol: 'tcp',
        description: port.description,
        public: port.public
      });
    }
    
    // Add service-specific firewall rules
    const serviceRules = [
      {
        type: 'allow',
        direction: 'inbound',
        protocol: 'tcp',
        destinationPort: 3000,
        description: 'Allow API Gateway access',
        enabled: true,
        priority: 10
      },
      {
        type: 'deny',
        direction: 'inbound',
        protocol: 'tcp',
        destinationPort: 5432,
        description: 'Block external database access',
        enabled: true,
        priority: 20
      }
    ];
    
    for (const rule of serviceRules) {
      await networkApiExamples.addFirewallRule(sandboxId, rule);
    }
    
    console.log('Microservices environment setup completed!');
    
    return await networkApiExamples.getNetworkStatus(sandboxId);
  },

  // Scenario 4: Network troubleshooting and diagnostics
  async performNetworkDiagnostics(sandboxId: string) {
    console.log('Performing network diagnostics...');
    
    // Get current network status
    const status = await networkApiExamples.getNetworkStatus(sandboxId);
    console.log('Current network status:', {
      interfaces: status.interfaces.length,
      ports: status.ports.length,
      connections: status.connections.length,
      internetConnectivity: status.internetConnectivity,
      dnsResolution: status.dnsResolution,
      firewallEnabled: status.firewallEnabled
    });
    
    // Test connectivity to various services
    const connectivityTests = [
      { target: 'google.com', description: 'Internet connectivity' },
      { target: 'github.com', port: 443, description: 'HTTPS to GitHub' },
      { target: 'npmjs.com', port: 443, description: 'NPM registry' },
      { target: 'pypi.org', port: 443, description: 'Python package index' },
      { target: '*******', description: 'Google DNS' },
      { target: 'localhost', port: 3000, description: 'Local development server' }
    ];
    
    const testResults = [];
    for (const test of connectivityTests) {
      try {
        const result = await networkApiExamples.testConnectivity(sandboxId, test.target, test.port);
        testResults.push({
          ...test,
          success: result.success,
          latency: result.latency,
          error: result.error
        });
      } catch (error) {
        testResults.push({
          ...test,
          success: false,
          error: error.message
        });
      }
    }
    
    console.log('Connectivity test results:');
    testResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const latency = result.latency ? ` (${result.latency}ms)` : '';
      console.log(`${status} ${result.description}: ${result.target}${result.port ? `:${result.port}` : ''}${latency}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    return {
      networkStatus: status,
      connectivityTests: testResults,
      summary: {
        totalTests: testResults.length,
        passed: testResults.filter(r => r.success).length,
        failed: testResults.filter(r => !r.success).length
      }
    };
  },

  // Scenario 5: Dynamic port management
  async manageDynamicPorts(sandboxId: string) {
    console.log('Managing dynamic ports...');
    
    // Add multiple ports dynamically
    const portsToAdd = [4000, 4001, 4002, 4003, 4004];
    
    for (const port of portsToAdd) {
      await networkApiExamples.addPortMapping(sandboxId, {
        containerPort: port,
        protocol: 'tcp',
        description: `Dynamic service on port ${port}`,
        public: true
      });
      console.log(`Added port mapping for ${port}`);
    }
    
    // Get current status
    let status = await networkApiExamples.getNetworkStatus(sandboxId);
    console.log(`Total ports mapped: ${status.ports.length}`);
    
    // Remove some ports
    const portsToRemove = [4001, 4003];
    
    for (const port of portsToRemove) {
      await networkApiExamples.removePortMapping(sandboxId, port);
      console.log(`Removed port mapping for ${port}`);
    }
    
    // Get final status
    status = await networkApiExamples.getNetworkStatus(sandboxId);
    console.log(`Final ports mapped: ${status.ports.length}`);
    
    return status;
  }
};

// Usage example:
/*
async function demoNetworkOperations() {
  const sandboxId = 'your-sandbox-id';
  
  try {
    // Set up different environments
    await networkScenarios.setupWebDevEnvironment(sandboxId);
    await networkScenarios.setupSecureEnvironment(sandboxId);
    await networkScenarios.setupMicroservicesEnvironment(sandboxId);
    
    // Perform diagnostics
    await networkScenarios.performNetworkDiagnostics(sandboxId);
    
    // Manage dynamic ports
    await networkScenarios.manageDynamicPorts(sandboxId);
    
    console.log('All network management operations completed successfully!');
  } catch (error) {
    console.error('Network management operation failed:', error);
  }
}
*/
