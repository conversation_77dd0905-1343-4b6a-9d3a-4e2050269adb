/**
 * VibeKraft Filesystem API Demo
 * 
 * Example usage of the filesystem API for sandbox containers
 */

// Example API calls for filesystem operations

export const filesystemApiExamples = {
  
  // 1. List directory contents
  async listDirectory(sandboxId: string, path: string = '/workspace') {
    const params = new URLSearchParams({
      operation: 'list',
      path,
      includeHidden: 'false',
      sortBy: 'name',
      sortOrder: 'asc'
    });

    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to list directory');
    }

    const data = await response.json();
    console.log('Directory listing:', data);
    return data;
  },

  // 2. Read file content
  async readFile(sandboxId: string, filePath: string) {
    const params = new URLSearchParams({
      operation: 'read',
      filePath
    });

    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to read file');
    }

    const data = await response.json();
    console.log('File content:', data);
    return data;
  },

  // 3. Write file content
  async writeFile(sandboxId: string, path: string, content: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'write',
        path,
        content,
        encoding: 'utf8',
        overwrite: true,
        createDirectories: true
      })
    });

    if (!response.ok) {
      throw new Error('Failed to write file');
    }

    const data = await response.json();
    console.log('File written:', data);
    return data;
  },

  // 4. Create directory
  async createDirectory(sandboxId: string, path: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'mkdir',
        path,
        permissions: '755'
      })
    });

    if (!response.ok) {
      throw new Error('Failed to create directory');
    }

    const data = await response.json();
    console.log('Directory created:', data);
    return data;
  },

  // 5. Move file or directory
  async moveItem(sandboxId: string, source: string, destination: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'move',
        source,
        destination
      })
    });

    if (!response.ok) {
      throw new Error('Failed to move item');
    }

    const data = await response.json();
    console.log('Item moved:', data);
    return data;
  },

  // 6. Copy file or directory
  async copyItem(sandboxId: string, source: string, destination: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'copy',
        source,
        destination
      })
    });

    if (!response.ok) {
      throw new Error('Failed to copy item');
    }

    const data = await response.json();
    console.log('Item copied:', data);
    return data;
  },

  // 7. Delete file or directory
  async deleteItem(sandboxId: string, path: string, recursive: boolean = false) {
    const params = new URLSearchParams({
      path,
      recursive: recursive.toString()
    });

    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/filesystem?${params}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error('Failed to delete item');
    }

    const data = await response.json();
    console.log('Item deleted:', data);
    return data;
  }
};

// Example usage scenarios
export const filesystemScenarios = {
  
  // Scenario 1: Create a new project structure
  async createProjectStructure(sandboxId: string, projectName: string) {
    console.log(`Creating project structure for: ${projectName}`);
    
    // Create main project directory
    await filesystemApiExamples.createDirectory(sandboxId, `/workspace/${projectName}`);
    
    // Create subdirectories
    const directories = ['src', 'tests', 'docs', 'config'];
    for (const dir of directories) {
      await filesystemApiExamples.createDirectory(sandboxId, `/workspace/${projectName}/${dir}`);
    }
    
    // Create initial files
    const files = [
      {
        path: `/workspace/${projectName}/README.md`,
        content: `# ${projectName}\n\nA new project created in VibeKraft Sandbox.\n`
      },
      {
        path: `/workspace/${projectName}/package.json`,
        content: JSON.stringify({
          name: projectName.toLowerCase(),
          version: '1.0.0',
          description: `${projectName} project`,
          main: 'src/index.js',
          scripts: {
            start: 'node src/index.js',
            test: 'echo "No tests yet"'
          }
        }, null, 2)
      },
      {
        path: `/workspace/${projectName}/src/index.js`,
        content: `console.log('Hello from ${projectName}!');\n`
      }
    ];
    
    for (const file of files) {
      await filesystemApiExamples.writeFile(sandboxId, file.path, file.content);
    }
    
    console.log(`Project structure created successfully!`);
    
    // List the created structure
    return await filesystemApiExamples.listDirectory(sandboxId, `/workspace/${projectName}`);
  },

  // Scenario 2: Backup and restore files
  async backupFiles(sandboxId: string, sourcePath: string, backupPath: string) {
    console.log(`Backing up ${sourcePath} to ${backupPath}`);
    
    // Create backup directory
    await filesystemApiExamples.createDirectory(sandboxId, backupPath);
    
    // Copy files to backup location
    await filesystemApiExamples.copyItem(sandboxId, sourcePath, `${backupPath}/backup-${Date.now()}`);
    
    console.log('Backup completed successfully!');
    
    return await filesystemApiExamples.listDirectory(sandboxId, backupPath);
  },

  // Scenario 3: Code file management
  async manageCodeFiles(sandboxId: string) {
    console.log('Managing code files...');
    
    // Create a simple Express app
    const appCode = `const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.get('/', (req, res) => {
  res.json({ message: 'Hello from VibeKraft Sandbox!' });
});

app.listen(port, () => {
  console.log(\`Server running on port \${port}\`);
});`;

    await filesystemApiExamples.writeFile(sandboxId, '/workspace/app.js', appCode);
    
    // Create package.json
    const packageJson = {
      name: 'vibekraft-app',
      version: '1.0.0',
      main: 'app.js',
      dependencies: {
        express: '^4.18.2'
      },
      scripts: {
        start: 'node app.js',
        dev: 'nodemon app.js'
      }
    };

    await filesystemApiExamples.writeFile(
      sandboxId, 
      '/workspace/package.json', 
      JSON.stringify(packageJson, null, 2)
    );
    
    // Read back the created files to verify
    const appContent = await filesystemApiExamples.readFile(sandboxId, '/workspace/app.js');
    const packageContent = await filesystemApiExamples.readFile(sandboxId, '/workspace/package.json');
    
    console.log('Code files created and verified!');
    
    return {
      app: appContent,
      package: packageContent
    };
  },

  // Scenario 4: File organization and cleanup
  async organizeFiles(sandboxId: string) {
    console.log('Organizing files...');
    
    // List current files
    const currentFiles = await filesystemApiExamples.listDirectory(sandboxId, '/workspace');
    
    // Create organization structure
    const orgDirs = ['javascript', 'python', 'docs', 'assets', 'temp'];
    for (const dir of orgDirs) {
      await filesystemApiExamples.createDirectory(sandboxId, `/workspace/organized/${dir}`);
    }
    
    // Move files based on extension (simulation)
    const fileExtensions = {
      '.js': 'javascript',
      '.py': 'python',
      '.md': 'docs',
      '.txt': 'docs',
      '.json': 'javascript'
    };
    
    // This would iterate through actual files and move them
    // For demo purposes, we'll just show the structure
    
    console.log('Files organized successfully!');
    
    return await filesystemApiExamples.listDirectory(sandboxId, '/workspace/organized');
  }
};

// Usage example:
/*
async function demoFilesystemOperations() {
  const sandboxId = 'your-sandbox-id';
  
  try {
    // Create a new project
    await filesystemScenarios.createProjectStructure(sandboxId, 'MyAwesomeApp');
    
    // Manage code files
    await filesystemScenarios.manageCodeFiles(sandboxId);
    
    // Create backup
    await filesystemScenarios.backupFiles(sandboxId, '/workspace/MyAwesomeApp', '/workspace/backups');
    
    // Organize files
    await filesystemScenarios.organizeFiles(sandboxId);
    
    console.log('All filesystem operations completed successfully!');
  } catch (error) {
    console.error('Filesystem operation failed:', error);
  }
}
*/
