/**
 * VibeKraft Package Management API Demo
 * 
 * Example usage of the package management API for sandbox containers
 */

// Example API calls for package management operations

export const packageApiExamples = {
  
  // 1. Get available package managers
  async getPackageManagers(sandboxId: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages?operation=managers`);
    
    if (!response.ok) {
      throw new Error('Failed to get package managers');
    }

    const data = await response.json();
    console.log('Available package managers:', data.managers);
    return data.managers;
  },

  // 2. List installed packages
  async listPackages(sandboxId: string, manager?: string, workingDir?: string) {
    const params = new URLSearchParams({
      operation: 'list',
      ...(manager && { manager }),
      ...(workingDir && { workingDir })
    });

    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to list packages');
    }

    const data = await response.json();
    console.log('Installed packages:', data);
    return data;
  },

  // 3. Install packages
  async installPackages(sandboxId: string, packages: string[], options: any = {}) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'install',
        packages,
        manager: options.manager || 'npm',
        dev: options.dev || false,
        global: options.global || false,
        workingDir: options.workingDir || '/workspace',
        timeout: options.timeout || 600
      })
    });

    if (!response.ok) {
      throw new Error('Failed to install packages');
    }

    const data = await response.json();
    console.log('Package installation result:', data);
    return data;
  },

  // 4. Uninstall packages
  async uninstallPackages(sandboxId: string, packages: string[], manager?: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'uninstall',
        packages,
        manager: manager || 'npm',
        workingDir: '/workspace'
      })
    });

    if (!response.ok) {
      throw new Error('Failed to uninstall packages');
    }

    const data = await response.json();
    console.log('Package uninstallation result:', data);
    return data;
  },

  // 5. Update packages
  async updatePackages(sandboxId: string, packages?: string[], manager?: string) {
    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operation: 'update',
        packages,
        manager: manager || 'npm',
        workingDir: '/workspace'
      })
    });

    if (!response.ok) {
      throw new Error('Failed to update packages');
    }

    const data = await response.json();
    console.log('Package update result:', data);
    return data;
  },

  // 6. Search packages
  async searchPackages(sandboxId: string, query: string, manager: string, limit?: number) {
    const params = new URLSearchParams({
      operation: 'search',
      query,
      manager,
      limit: (limit || 10).toString()
    });

    const response = await fetch(`/api/vibekraft-sandbox/sandboxes/${sandboxId}/packages?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to search packages');
    }

    const data = await response.json();
    console.log('Package search results:', data);
    return data.results;
  }
};

// Example usage scenarios
export const packageScenarios = {
  
  // Scenario 1: Set up a Node.js Express project
  async setupExpressProject(sandboxId: string) {
    console.log('Setting up Express.js project...');
    
    // Install Express and related packages
    const packages = [
      'express',
      'cors',
      'helmet',
      'morgan',
      'dotenv'
    ];
    
    const devPackages = [
      'nodemon',
      'jest',
      'supertest',
      '@types/node',
      '@types/express'
    ];
    
    // Install production dependencies
    await packageApiExamples.installPackages(sandboxId, packages, {
      manager: 'npm',
      save: true
    });
    
    // Install development dependencies
    await packageApiExamples.installPackages(sandboxId, devPackages, {
      manager: 'npm',
      dev: true
    });
    
    console.log('Express.js project setup completed!');
    
    // List installed packages to verify
    return await packageApiExamples.listPackages(sandboxId, 'npm');
  },

  // Scenario 2: Set up a Python Flask project
  async setupFlaskProject(sandboxId: string) {
    console.log('Setting up Flask project...');
    
    const packages = [
      'flask',
      'flask-cors',
      'flask-sqlalchemy',
      'python-dotenv',
      'requests'
    ];
    
    const devPackages = [
      'pytest',
      'pytest-flask',
      'black',
      'flake8'
    ];
    
    // Install packages using pip
    await packageApiExamples.installPackages(sandboxId, packages, {
      manager: 'pip'
    });
    
    await packageApiExamples.installPackages(sandboxId, devPackages, {
      manager: 'pip'
    });
    
    console.log('Flask project setup completed!');
    
    return await packageApiExamples.listPackages(sandboxId, 'pip');
  },

  // Scenario 3: Package management workflow
  async packageManagementWorkflow(sandboxId: string) {
    console.log('Demonstrating package management workflow...');
    
    // 1. Check available package managers
    const managers = await packageApiExamples.getPackageManagers(sandboxId);
    console.log('Available managers:', managers.filter(m => m.available).map(m => m.name));
    
    // 2. Install some packages
    await packageApiExamples.installPackages(sandboxId, ['lodash', 'axios'], {
      manager: 'npm'
    });
    
    // 3. List installed packages
    const packageList = await packageApiExamples.listPackages(sandboxId, 'npm');
    console.log(`Installed ${packageList.totalPackages} packages`);
    
    // 4. Search for packages
    const searchResults = await packageApiExamples.searchPackages(sandboxId, 'react', 'npm', 5);
    console.log(`Found ${searchResults.length} packages matching 'react'`);
    
    // 5. Update packages
    await packageApiExamples.updatePackages(sandboxId, undefined, 'npm');
    
    // 6. Uninstall a package
    await packageApiExamples.uninstallPackages(sandboxId, ['lodash'], 'npm');
    
    console.log('Package management workflow completed!');
    
    return await packageApiExamples.listPackages(sandboxId, 'npm');
  },

  // Scenario 4: Multi-language environment setup
  async setupMultiLanguageEnvironment(sandboxId: string) {
    console.log('Setting up multi-language development environment...');
    
    // Node.js packages
    const nodePackages = ['express', 'react', 'typescript'];
    await packageApiExamples.installPackages(sandboxId, nodePackages, {
      manager: 'npm'
    });
    
    // Python packages
    const pythonPackages = ['flask', 'numpy', 'pandas'];
    await packageApiExamples.installPackages(sandboxId, pythonPackages, {
      manager: 'pip'
    });
    
    // System packages (if apt is available)
    try {
      const systemPackages = ['curl', 'git', 'vim'];
      await packageApiExamples.installPackages(sandboxId, systemPackages, {
        manager: 'apt'
      });
    } catch (error) {
      console.log('System package installation skipped (apt not available)');
    }
    
    console.log('Multi-language environment setup completed!');
    
    // Get summary of all installed packages
    const results = {
      node: await packageApiExamples.listPackages(sandboxId, 'npm'),
      python: await packageApiExamples.listPackages(sandboxId, 'pip')
    };
    
    return results;
  },

  // Scenario 5: Package cleanup and optimization
  async cleanupAndOptimize(sandboxId: string) {
    console.log('Cleaning up and optimizing packages...');
    
    // List current packages
    const currentPackages = await packageApiExamples.listPackages(sandboxId, 'npm');
    console.log(`Current packages: ${currentPackages.totalPackages}`);
    
    // Update all packages to latest versions
    await packageApiExamples.updatePackages(sandboxId, undefined, 'npm');
    
    // Remove unused development packages (example)
    const devPackagesToRemove = ['@types/jest', 'ts-node'];
    for (const pkg of devPackagesToRemove) {
      try {
        await packageApiExamples.uninstallPackages(sandboxId, [pkg], 'npm');
        console.log(`Removed unused package: ${pkg}`);
      } catch (error) {
        console.log(`Package ${pkg} was not installed`);
      }
    }
    
    // Final package list
    const finalPackages = await packageApiExamples.listPackages(sandboxId, 'npm');
    console.log(`Final packages: ${finalPackages.totalPackages}`);
    
    return {
      before: currentPackages.totalPackages,
      after: finalPackages.totalPackages,
      saved: currentPackages.totalPackages - finalPackages.totalPackages
    };
  }
};

// Usage example:
/*
async function demoPackageOperations() {
  const sandboxId = 'your-sandbox-id';
  
  try {
    // Set up different project types
    await packageScenarios.setupExpressProject(sandboxId);
    await packageScenarios.setupFlaskProject(sandboxId);
    
    // Demonstrate package management workflow
    await packageScenarios.packageManagementWorkflow(sandboxId);
    
    // Set up multi-language environment
    await packageScenarios.setupMultiLanguageEnvironment(sandboxId);
    
    // Cleanup and optimize
    await packageScenarios.cleanupAndOptimize(sandboxId);
    
    console.log('All package management operations completed successfully!');
  } catch (error) {
    console.error('Package management operation failed:', error);
  }
}
*/
