/**
 * VibeKraft Create Sandbox Form Component
 * 
 * Form for creating new sandboxes with optimized templates
 */

'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Cpu, 
  HardDrive, 
  Network, 
  Shield, 
  Settings,
  Plus,
  X,
  Info
} from 'lucide-react';
import { CreateSandboxRequest } from '../../hooks/use-sandbox-manager';

interface CreateSandboxFormProps {
  onSubmit: (config: CreateSandboxRequest) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const OPTIMIZED_TEMPLATES = [
  {
    id: 'vibekraft/nodejs-minimal:latest',
    name: 'Node.js Minimal',
    description: 'Optimized Node.js environment with TypeScript',
    size: '214MB',
    languages: ['JavaScript', 'TypeScript'],
    icon: '🟢',
    recommended: true
  },
  {
    id: 'vibekraft/python-minimal:latest',
    name: 'Python Minimal',
    description: 'Optimized Python environment with common packages',
    size: '114MB',
    languages: ['Python'],
    icon: '🐍',
    recommended: true
  },
  {
    id: 'vibekraft/fullstack-dev:latest',
    name: 'Full-Stack Development',
    description: 'Complete development environment with Node.js and Python',
    size: '1.04GB',
    languages: ['JavaScript', 'TypeScript', 'Python'],
    icon: '🚀',
    recommended: false
  }
];

export function CreateSandboxForm({ onSubmit, onCancel, loading = false }: CreateSandboxFormProps) {
  const [config, setConfig] = useState<CreateSandboxRequest>({
    name: '',
    template: 'vibekraft/nodejs-minimal:latest',
    resources: {
      vcpuCount: 1,
      memSizeMib: 512,
      diskSizeGb: 5,
      diskType: 'ssd'
    },
    network: {
      ports: [],
      isolated: false
    },
    security: {
      readOnlyRootfs: false,
      noNewPrivileges: true,
      allowSudo: false,
      allowNetworkAccess: true,
      allowFileSystemAccess: true
    },
    environment: {},
    labels: {},
    annotations: {}
  });

  const [activeTab, setActiveTab] = useState<'basic' | 'resources' | 'network' | 'security' | 'advanced'>('basic');
  const [newEnvVar, setNewEnvVar] = useState({ key: '', value: '' });
  const [newPort, setNewPort] = useState({ containerPort: '', hostPort: '', protocol: 'tcp' as const });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!config.name.trim()) {
      alert('Please enter a sandbox name');
      return;
    }
    await onSubmit(config);
  };

  const addEnvironmentVariable = () => {
    if (newEnvVar.key && newEnvVar.value) {
      setConfig({
        ...config,
        environment: {
          ...config.environment,
          [newEnvVar.key]: newEnvVar.value
        }
      });
      setNewEnvVar({ key: '', value: '' });
    }
  };

  const removeEnvironmentVariable = (key: string) => {
    const newEnv = { ...config.environment };
    delete newEnv[key];
    setConfig({ ...config, environment: newEnv });
  };

  const addPort = () => {
    if (newPort.containerPort) {
      const port = {
        containerPort: parseInt(newPort.containerPort),
        hostPort: newPort.hostPort ? parseInt(newPort.hostPort) : undefined,
        protocol: newPort.protocol,
        public: false
      };
      setConfig({
        ...config,
        network: {
          ...config.network,
          ports: [...(config.network?.ports || []), port]
        }
      });
      setNewPort({ containerPort: '', hostPort: '', protocol: 'tcp' });
    }
  };

  const removePort = (index: number) => {
    const newPorts = [...(config.network?.ports || [])];
    newPorts.splice(index, 1);
    setConfig({
      ...config,
      network: {
        ...config.network,
        ports: newPorts
      }
    });
  };

  const selectedTemplate = OPTIMIZED_TEMPLATES.find(t => t.id === config.template);

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <Container className="w-5 h-5 mr-2" />
            Create New Sandbox
          </h2>
          <button
            onClick={onCancel}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'basic', label: 'Basic', icon: Container },
              { id: 'resources', label: 'Resources', icon: Cpu },
              { id: 'network', label: 'Network', icon: Network },
              { id: 'security', label: 'Security', icon: Shield },
              { id: 'advanced', label: 'Advanced', icon: Settings }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                type="button"
                onClick={() => setActiveTab(id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Basic Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sandbox Name *
                </label>
                <input
                  type="text"
                  value={config.name}
                  onChange={(e) => setConfig({ ...config, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter sandbox name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template
                </label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {OPTIMIZED_TEMPLATES.map((template) => (
                    <div
                      key={template.id}
                      className={`relative p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                        config.template === template.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setConfig({ ...config, template: template.id })}
                    >
                      {template.recommended && (
                        <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                          Recommended
                        </div>
                      )}
                      <div className="flex items-center mb-2">
                        <span className="text-2xl mr-2">{template.icon}</span>
                        <div>
                          <h3 className="font-medium text-gray-900">{template.name}</h3>
                          <p className="text-sm text-gray-500">{template.size}</p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {template.languages.map((lang) => (
                          <span
                            key={lang}
                            className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                          >
                            {lang}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
                {selectedTemplate && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-start">
                      <Info className="w-4 h-4 text-blue-600 mt-0.5 mr-2" />
                      <div className="text-sm text-blue-800">
                        <strong>{selectedTemplate.name}</strong> - {selectedTemplate.description}
                        <br />
                        Optimized size: {selectedTemplate.size} (60-70% smaller than standard images)
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Resources Tab */}
          {activeTab === 'resources' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    CPU Cores
                  </label>
                  <select
                    value={config.resources?.vcpuCount || 1}
                    onChange={(e) => setConfig({
                      ...config,
                      resources: { ...config.resources, vcpuCount: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={1}>1 Core</option>
                    <option value={2}>2 Cores</option>
                    <option value={4}>4 Cores</option>
                    <option value={8}>8 Cores</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Memory (MB)
                  </label>
                  <select
                    value={config.resources?.memSizeMib || 512}
                    onChange={(e) => setConfig({
                      ...config,
                      resources: { ...config.resources, memSizeMib: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={256}>256 MB</option>
                    <option value={512}>512 MB</option>
                    <option value={1024}>1 GB</option>
                    <option value={2048}>2 GB</option>
                    <option value={4096}>4 GB</option>
                    <option value={8192}>8 GB</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Disk Size (GB)
                  </label>
                  <select
                    value={config.resources?.diskSizeGb || 5}
                    onChange={(e) => setConfig({
                      ...config,
                      resources: { ...config.resources, diskSizeGb: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={5}>5 GB</option>
                    <option value={10}>10 GB</option>
                    <option value={20}>20 GB</option>
                    <option value={50}>50 GB</option>
                    <option value={100}>100 GB</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Disk Type
                  </label>
                  <select
                    value={config.resources?.diskType || 'ssd'}
                    onChange={(e) => setConfig({
                      ...config,
                      resources: { ...config.resources, diskType: e.target.value as 'ssd' | 'hdd' }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="ssd">SSD (Faster)</option>
                    <option value="hdd">HDD (Cheaper)</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Network Tab */}
          {activeTab === 'network' && (
            <div className="space-y-6">
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.network?.isolated || false}
                    onChange={(e) => setConfig({
                      ...config,
                      network: { ...config.network, isolated: e.target.checked }
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Isolated Network (No external access)
                  </span>
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Port Mappings
                </label>
                <div className="space-y-2">
                  {config.network?.ports?.map((port, index) => (
                    <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                      <span className="text-sm">
                        {port.hostPort || 'auto'}:{port.containerPort} ({port.protocol})
                      </span>
                      <button
                        type="button"
                        onClick={() => removePort(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      placeholder="Container port"
                      value={newPort.containerPort}
                      onChange={(e) => setNewPort({ ...newPort, containerPort: e.target.value })}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <input
                      type="number"
                      placeholder="Host port (optional)"
                      value={newPort.hostPort}
                      onChange={(e) => setNewPort({ ...newPort, hostPort: e.target.value })}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <select
                      value={newPort.protocol}
                      onChange={(e) => setNewPort({ ...newPort, protocol: e.target.value as 'tcp' | 'udp' })}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="tcp">TCP</option>
                      <option value="udp">UDP</option>
                    </select>
                    <button
                      type="button"
                      onClick={addPort}
                      className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Security Tab */}
          {activeTab === 'security' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.security?.readOnlyRootfs || false}
                    onChange={(e) => setConfig({
                      ...config,
                      security: { ...config.security, readOnlyRootfs: e.target.checked }
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Read-only root filesystem
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.security?.noNewPrivileges || true}
                    onChange={(e) => setConfig({
                      ...config,
                      security: { ...config.security, noNewPrivileges: e.target.checked }
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    No new privileges
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.security?.allowSudo || false}
                    onChange={(e) => setConfig({
                      ...config,
                      security: { ...config.security, allowSudo: e.target.checked }
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Allow sudo access
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.security?.allowNetworkAccess !== false}
                    onChange={(e) => setConfig({
                      ...config,
                      security: { ...config.security, allowNetworkAccess: e.target.checked }
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Allow network access
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.security?.allowFileSystemAccess !== false}
                    onChange={(e) => setConfig({
                      ...config,
                      security: { ...config.security, allowFileSystemAccess: e.target.checked }
                    })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Allow filesystem access
                  </span>
                </label>
              </div>
            </div>
          )}

          {/* Advanced Tab */}
          {activeTab === 'advanced' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Environment Variables
                </label>
                <div className="space-y-2">
                  {Object.entries(config.environment || {}).map(([key, value]) => (
                    <div key={key} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                      <span className="text-sm font-mono">{key}={value}</span>
                      <button
                        type="button"
                        onClick={() => removeEnvironmentVariable(key)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      placeholder="Variable name"
                      value={newEnvVar.key}
                      onChange={(e) => setNewEnvVar({ ...newEnvVar, key: e.target.value })}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <input
                      type="text"
                      placeholder="Variable value"
                      value={newEnvVar.value}
                      onChange={(e) => setNewEnvVar({ ...newEnvVar, value: e.target.value })}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <button
                      type="button"
                      onClick={addEnvironmentVariable}
                      className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            {selectedTemplate && (
              <span>
                Selected: {selectedTemplate.name} ({selectedTemplate.size})
              </span>
            )}
          </div>
          <div className="flex items-center space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !config.name.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Container className="w-4 h-4 mr-2" />
                  Create Sandbox
                </>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
