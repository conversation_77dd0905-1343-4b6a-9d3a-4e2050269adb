/**
 * VibeKraft Sandbox Details Component
 * 
 * Displays detailed information about a sandbox
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Cpu, 
  HardDrive, 
  Network, 
  Shield, 
  Clock,
  Play,
  Square,
  RotateCcw,
  Trash2,
  Terminal,
  FileText,
  Activity,
  Info,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { SandboxListItem, useSandboxManager } from '../../hooks/use-sandbox-manager';
import { SandboxStatus } from '../../types/sandbox';

interface SandboxDetailsProps {
  sandbox: SandboxListItem;
  onClose: () => void;
}

export function SandboxDetails({ sandbox, onClose }: SandboxDetailsProps) {
  const { getSandboxStatus, deleteSandbox } = useSandboxManager();
  const [status, setStatus] = useState<SandboxStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'resources' | 'network' | 'security' | 'logs'>('overview');

  // Fetch sandbox status
  useEffect(() => {
    const fetchStatus = async () => {
      try {
        setLoading(true);
        const sandboxStatus = await getSandboxStatus(sandbox.id);
        setStatus(sandboxStatus);
      } catch (error) {
        console.error('Failed to fetch sandbox status:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStatus();
    
    // Refresh status every 10 seconds
    const interval = setInterval(fetchStatus, 10000);
    return () => clearInterval(interval);
  }, [sandbox.id, getSandboxStatus]);

  // Handle delete
  const handleDelete = async () => {
    if (confirm(`Are you sure you want to delete sandbox "${sandbox.name}"?`)) {
      try {
        await deleteSandbox(sandbox.id);
        onClose();
      } catch (error) {
        console.error('Failed to delete sandbox:', error);
      }
    }
  };

  // Get status color and icon
  const getStatusDisplay = (state: string) => {
    switch (state.toLowerCase()) {
      case 'running':
        return { color: 'text-green-600', bg: 'bg-green-100', icon: CheckCircle };
      case 'stopped':
        return { color: 'text-gray-600', bg: 'bg-gray-100', icon: Square };
      case 'starting':
        return { color: 'text-blue-600', bg: 'bg-blue-100', icon: Play };
      case 'error':
        return { color: 'text-red-600', bg: 'bg-red-100', icon: XCircle };
      default:
        return { color: 'text-yellow-600', bg: 'bg-yellow-100', icon: AlertCircle };
    }
  };

  const statusDisplay = getStatusDisplay(sandbox.state);
  const StatusIcon = statusDisplay.icon;

  return (
    <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Container className="w-6 h-6 text-gray-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{sandbox.name}</h2>
              <p className="text-sm text-gray-500">{sandbox.id}</p>
            </div>
            <div className={`flex items-center px-3 py-1 rounded-full ${statusDisplay.bg}`}>
              <StatusIcon className={`w-4 h-4 mr-2 ${statusDisplay.color}`} />
              <span className={`text-sm font-medium ${statusDisplay.color}`}>
                {sandbox.state}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              className="p-2 text-gray-400 hover:text-green-600 rounded-md hover:bg-gray-100"
              title="Start"
            >
              <Play className="w-5 h-5" />
            </button>
            <button
              className="p-2 text-gray-400 hover:text-yellow-600 rounded-md hover:bg-gray-100"
              title="Stop"
            >
              <Square className="w-5 h-5" />
            </button>
            <button
              className="p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-gray-100"
              title="Restart"
            >
              <RotateCcw className="w-5 h-5" />
            </button>
            <button
              className="p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-gray-100"
              title="Terminal"
            >
              <Terminal className="w-5 h-5" />
            </button>
            <button
              onClick={handleDelete}
              className="p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-gray-100"
              title="Delete"
            >
              <Trash2 className="w-5 h-5" />
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Close
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'overview', label: 'Overview', icon: Info },
            { id: 'resources', label: 'Resources', icon: Cpu },
            { id: 'network', label: 'Network', icon: Network },
            { id: 'security', label: 'Security', icon: Shield },
            { id: 'logs', label: 'Logs', icon: FileText }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon className="w-4 h-4 mr-2" />
              {label}
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Template</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {sandbox.template.replace('vibekraft/', '').replace(':latest', '')}
                    </p>
                  </div>
                  <Container className="w-8 h-8 text-gray-400" />
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">CPU</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {sandbox.resources.vcpuCount} Core{sandbox.resources.vcpuCount !== 1 ? 's' : ''}
                    </p>
                  </div>
                  <Cpu className="w-8 h-8 text-gray-400" />
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Memory</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {sandbox.resources.memSizeMib} MB
                    </p>
                  </div>
                  <HardDrive className="w-8 h-8 text-gray-400" />
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Disk</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {sandbox.resources.diskSizeGb} GB
                    </p>
                  </div>
                  <HardDrive className="w-8 h-8 text-gray-400" />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                    <dd className="text-sm text-gray-900">
                      {new Date(sandbox.createdAt).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Accessed</dt>
                    <dd className="text-sm text-gray-900">
                      {new Date(sandbox.lastAccessedAt).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Health Status</dt>
                    <dd className="text-sm text-gray-900">
                      {sandbox.health.status}
                    </dd>
                  </div>
                </dl>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Network</h3>
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Ports</dt>
                    <dd className="text-sm text-gray-900">
                      {sandbox.network.ports || 'None'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Isolated</dt>
                    <dd className="text-sm text-gray-900">
                      {sandbox.network.isolated ? 'Yes' : 'No'}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Labels and Annotations */}
            {(Object.keys(sandbox.labels).length > 0 || Object.keys(sandbox.annotations).length > 0) && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Metadata</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {Object.keys(sandbox.labels).length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Labels</h4>
                      <div className="space-y-1">
                        {Object.entries(sandbox.labels).map(([key, value]) => (
                          <div key={key} className="text-sm">
                            <span className="font-mono text-gray-600">{key}:</span>
                            <span className="ml-2 text-gray-900">{value}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {Object.keys(sandbox.annotations).length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Annotations</h4>
                      <div className="space-y-1">
                        {Object.entries(sandbox.annotations).map(([key, value]) => (
                          <div key={key} className="text-sm">
                            <span className="font-mono text-gray-600">{key}:</span>
                            <span className="ml-2 text-gray-900">{value}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Resources Tab */}
        {activeTab === 'resources' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">CPU Usage</h3>
                  <Cpu className="w-6 h-6 text-gray-400" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Allocated</span>
                    <span>{sandbox.resources.vcpuCount} cores</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }}></div>
                  </div>
                  <div className="text-xs text-gray-500">45% utilized</div>
                </div>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Memory Usage</h3>
                  <HardDrive className="w-6 h-6 text-gray-400" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Allocated</span>
                    <span>{sandbox.resources.memSizeMib} MB</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '32%' }}></div>
                  </div>
                  <div className="text-xs text-gray-500">32% utilized</div>
                </div>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Disk Usage</h3>
                  <HardDrive className="w-6 h-6 text-gray-400" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Allocated</span>
                    <span>{sandbox.resources.diskSizeGb} GB</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '18%' }}></div>
                  </div>
                  <div className="text-xs text-gray-500">18% utilized</div>
                </div>
              </div>
            </div>

            {status && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Resource Limits</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">CPU Limit</dt>
                    <dd className="text-sm text-gray-900">{status.resources?.cpuUsage || 'N/A'}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Memory Limit</dt>
                    <dd className="text-sm text-gray-900">{status.resources?.memoryUsage || 'N/A'}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Disk I/O</dt>
                    <dd className="text-sm text-gray-900">{status.resources?.diskUsage || 'N/A'}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Network I/O</dt>
                    <dd className="text-sm text-gray-900">{status.resources?.networkUsage || 'N/A'}</dd>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Network Tab */}
        {activeTab === 'network' && (
          <div className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Network Configuration</h3>
              <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Isolated Network</dt>
                  <dd className="text-sm text-gray-900">
                    {sandbox.network.isolated ? 'Yes' : 'No'}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Port Mappings</dt>
                  <dd className="text-sm text-gray-900">
                    {sandbox.network.ports || 'None configured'}
                  </dd>
                </div>
              </dl>
            </div>

            {status?.network && (
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Network Status</h3>
                <div className="space-y-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">IP Address</dt>
                    <dd className="text-sm text-gray-900 font-mono">
                      {status.network.ipAddress || 'Not assigned'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Network Mode</dt>
                    <dd className="text-sm text-gray-900">
                      {status.network.mode || 'Default'}
                    </dd>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <div className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Security Configuration</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Read-only root filesystem</span>
                    <span className="text-sm font-medium text-gray-900">
                      {status?.security?.readOnlyRootfs ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">No new privileges</span>
                    <span className="text-sm font-medium text-gray-900">
                      {status?.security?.noNewPrivileges ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Sudo access</span>
                    <span className="text-sm font-medium text-gray-900">
                      {status?.security?.allowSudo ? 'Allowed' : 'Denied'}
                    </span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Network access</span>
                    <span className="text-sm font-medium text-gray-900">
                      {status?.security?.allowNetworkAccess ? 'Allowed' : 'Denied'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Filesystem access</span>
                    <span className="text-sm font-medium text-gray-900">
                      {status?.security?.allowFileSystemAccess ? 'Allowed' : 'Denied'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">User ID</span>
                    <span className="text-sm font-medium text-gray-900 font-mono">
                      {status?.security?.runAsUser || '1001'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Logs Tab */}
        {activeTab === 'logs' && (
          <div className="space-y-6">
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
              <div className="flex items-center justify-between mb-2">
                <span className="text-gray-400">Container Logs</span>
                <button className="text-gray-400 hover:text-white">
                  <Activity className="w-4 h-4" />
                </button>
              </div>
              <div className="space-y-1 max-h-96 overflow-y-auto">
                <div>[2024-01-15 10:30:15] Container started successfully</div>
                <div>[2024-01-15 10:30:16] Initializing workspace...</div>
                <div>[2024-01-15 10:30:17] Node.js v18.20.8 ready</div>
                <div>[2024-01-15 10:30:17] TypeScript 5.8.3 ready</div>
                <div>[2024-01-15 10:30:18] Sandbox ready for connections</div>
                <div className="text-gray-500">[Live logs will appear here...]</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
