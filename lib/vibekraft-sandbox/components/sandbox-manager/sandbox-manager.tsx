"use client";

/**
 * VibeKraft Sandbox Manager Component
 * 
 * Main UI component for managing sandboxes with enhanced customizability
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Plus, 
  Play, 
  Square, 
  RotateCw, 
  Trash2, 
  MoreVertical,
  Search,
  Filter,
  RefreshCw,
  Monitor,
  Terminal,
  Code,
  Database,
  Settings,
  Activity,
  Clock,
  Cpu,
  HardDrive,
  Network
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { useSandboxes } from '../../hooks/use-sandbox';
import { useOptimizedTemplates } from '../../hooks/use-optimized-templates';
import { SandboxCard } from './sandbox-card';
import { CreateSandboxForm } from './create-sandbox-form';
import { SandboxDetails } from './sandbox-details';

export interface SandboxManagerProps {
  projectId?: string;
  className?: string;
  onSandboxSelect?: (sandboxId: string) => void;
  showCreateButton?: boolean;
  showFilters?: boolean;
  defaultView?: 'grid' | 'list';
}

export function SandboxManager({
  projectId,
  className = '',
  onSandboxSelect,
  showCreateButton = true,
  showFilters = true,
  defaultView = 'grid'
}: SandboxManagerProps) {
  // State management
  const [activeTab, setActiveTab] = useState('sandboxes');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [templateFilter, setTemplateFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(defaultView);
  const [selectedSandbox, setSelectedSandbox] = useState<string | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);

  // Hooks
  const {
    sandboxes,
    loading: sandboxesLoading,
    error: sandboxesError,
    refreshSandboxes,
    createSandbox,
    startSandbox,
    stopSandbox,
    restartSandbox,
    destroySandbox
  } = useSandboxes(projectId);

  const {
    loading: templatesLoading,
    refreshTemplates
  } = useOptimizedTemplates();

  // Effects
  useEffect(() => {
    refreshSandboxes();
    refreshTemplates();
  }, [projectId]);

  // Filter sandboxes based on search and filters
  const filteredSandboxes = sandboxes.filter(sandbox => {
    const matchesSearch = !searchQuery || 
      sandbox.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sandbox.template.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || sandbox.state === statusFilter;
    const matchesTemplate = templateFilter === 'all' || sandbox.template === templateFilter;
    
    return matchesSearch && matchesStatus && matchesTemplate;
  });

  // Get unique templates for filter
  const availableTemplates = [...new Set(sandboxes.map(s => s.template))];

  // Handlers
  const handleCreateSandbox = async (sandboxData: any) => {
    try {
      const newSandbox = await createSandbox(sandboxData);
      toast({
        title: 'Sandbox Created',
        description: `Sandbox "${sandboxData.name}" has been created successfully.`,
      });
      setShowCreateDialog(false);
      refreshSandboxes();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create sandbox',
        variant: 'destructive',
      });
    }
  };

  const handleSandboxAction = async (sandboxId: string, action: string) => {
    try {
      switch (action) {
        case 'start':
          await startSandbox(sandboxId);
          toast({
            title: 'Sandbox Started',
            description: 'Sandbox has been started successfully.',
          });
          break;
        case 'stop':
          await stopSandbox(sandboxId);
          toast({
            title: 'Sandbox Stopped',
            description: 'Sandbox has been stopped successfully.',
          });
          break;
        case 'restart':
          await restartSandbox(sandboxId);
          toast({
            title: 'Sandbox Restarted',
            description: 'Sandbox has been restarted successfully.',
          });
          break;
        case 'destroy':
          if (confirm('Are you sure you want to destroy this sandbox? This action cannot be undone.')) {
            await destroySandbox(sandboxId);
            toast({
              title: 'Sandbox Destroyed',
              description: 'Sandbox has been destroyed successfully.',
            });
          }
          break;
      }
      refreshSandboxes();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || `Failed to ${action} sandbox`,
        variant: 'destructive',
      });
    }
  };

  const handleSandboxSelect = (sandboxId: string) => {
    setSelectedSandbox(sandboxId);
    if (onSandboxSelect) {
      onSandboxSelect(sandboxId);
    }
  };

  const handleViewDetails = (sandboxId: string) => {
    setSelectedSandbox(sandboxId);
    setShowDetailsDialog(true);
  };

  // Get status counts for badges
  const statusCounts = sandboxes.reduce((acc, sandbox) => {
    acc[sandbox.state] = (acc[sandbox.state] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Sandbox Manager</h2>
          <p className="text-muted-foreground">
            Manage your development sandboxes and environments
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshSandboxes}
            disabled={sandboxesLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${sandboxesLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          {showCreateButton && (
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Sandbox
            </Button>
          )}
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm font-medium">Running</p>
                <p className="text-2xl font-bold">{statusCounts.running || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Square className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Stopped</p>
                <p className="text-2xl font-bold">{statusCounts.stopped || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-yellow-500" />
              <div>
                <p className="text-sm font-medium">Creating</p>
                <p className="text-2xl font-bold">{statusCounts.creating || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Monitor className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Total</p>
                <p className="text-2xl font-bold">{sandboxes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      {showFilters && (
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search sandboxes..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full md:w-48">
                <Label htmlFor="status-filter">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="running">Running</SelectItem>
                    <SelectItem value="stopped">Stopped</SelectItem>
                    <SelectItem value="creating">Creating</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-48">
                <Label htmlFor="template-filter">Template</Label>
                <Select value={templateFilter} onValueChange={setTemplateFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All templates" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Templates</SelectItem>
                    {availableTemplates.map(template => (
                      <SelectItem key={template} value={template}>
                        {template}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sandboxes List */}
      <div className="space-y-4">
        {sandboxesLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-4"></div>
                  <div className="flex space-x-2">
                    <div className="h-8 w-16 bg-gray-200 rounded"></div>
                    <div className="h-8 w-16 bg-gray-200 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : sandboxesError ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-red-500 mb-4">Error loading sandboxes: {sandboxesError}</p>
              <Button onClick={refreshSandboxes}>Try Again</Button>
            </CardContent>
          </Card>
        ) : filteredSandboxes.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Monitor className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No sandboxes found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || statusFilter !== 'all' || templateFilter !== 'all'
                  ? 'No sandboxes match your current filters.'
                  : 'Get started by creating your first sandbox.'}
              </p>
              {showCreateButton && (
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Sandbox
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className={viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
            : 'space-y-4'
          }>
            {filteredSandboxes.map(sandbox => (
              <SandboxCard
                key={sandbox.id}
                sandbox={sandbox}
                viewMode={viewMode}
                onAction={handleSandboxAction}
                onSelect={handleSandboxSelect}
                onViewDetails={handleViewDetails}
              />
            ))}
          </div>
        )}
      </div>

      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Sandbox</DialogTitle>
            <DialogDescription>
              Create a new sandbox for your project.
            </DialogDescription>
          </DialogHeader>
          <CreateSandboxForm
            onSubmit={handleCreateSandbox}
            onCancel={() => setShowCreateDialog(false)}
            loading={sandboxesLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Sandbox Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Sandbox Details</DialogTitle>
            <DialogDescription>
              View and manage the details of your sandbox.
            </DialogDescription>
          </DialogHeader>
          <SandboxDetails
            sandboxId={selectedSandbox || ''}
            onClose={() => setShowDetailsDialog(false)}
          />
        </DialogContent>
      </Dialog>

      
    </div>
  );
}
