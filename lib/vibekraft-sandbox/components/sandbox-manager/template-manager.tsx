/**
 * VibeKraft Template Manager Component
 * 
 * Manages optimized Docker templates
 */

'use client';

import React, { useState } from 'react';
import { 
  Package, 
  Download, 
  RefreshCw, 
  BarChart3, 
  Zap,
  CheckCircle,
  Clock,
  AlertCircle,
  TrendingDown,
  Award,
  Layers
} from 'lucide-react';
import { useOptimizedTemplates } from '../../hooks/use-optimized-templates';

interface TemplateManagerProps {
  onClose?: () => void;
}

export function TemplateManager({ onClose }: TemplateManagerProps) {
  const {
    templates,
    analysis,
    statistics,
    recommendations,
    loading,
    error,
    refreshTemplates,
    buildAllTemplates,
    buildTemplates
  } = useOptimizedTemplates();

  const [building, setBuilding] = useState(false);
  const [selectedTemplates, setSelectedTemplates] = useState<Set<string>>(new Set());

  // Handle build all templates
  const handleBuildAll = async () => {
    try {
      setBuilding(true);
      await buildAllTemplates();
    } catch (error) {
      console.error('Failed to build templates:', error);
    } finally {
      setBuilding(false);
    }
  };

  // Handle build selected templates
  const handleBuildSelected = async () => {
    if (selectedTemplates.size === 0) return;

    try {
      setBuilding(true);
      await buildTemplates(Array.from(selectedTemplates));
      setSelectedTemplates(new Set());
    } catch (error) {
      console.error('Failed to build selected templates:', error);
    } finally {
      setBuilding(false);
    }
  };

  // Handle template selection
  const handleSelectTemplate = (templateId: string, checked: boolean) => {
    const newSelected = new Set(selectedTemplates);
    if (checked) {
      newSelected.add(templateId);
    } else {
      newSelected.delete(templateId);
    }
    setSelectedTemplates(newSelected);
  };

  // Get template status
  const getTemplateStatus = (templateId: string) => {
    const templateAnalysis = analysis.find(a => a.name.includes(templateId.split('/')[1]?.split(':')[0] || ''));
    if (!templateAnalysis) return { status: 'unknown', size: 'Unknown' };
    
    if (typeof templateAnalysis.optimizedSize === 'number') {
      return { status: 'built', size: `${templateAnalysis.optimizedSize}MB` };
    } else {
      return { status: 'pending', size: templateAnalysis.optimizedSize };
    }
  };

  // Get status icon and color
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'built':
        return { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100' };
      case 'pending':
        return { icon: Clock, color: 'text-yellow-600', bg: 'bg-yellow-100' };
      default:
        return { icon: AlertCircle, color: 'text-gray-600', bg: 'bg-gray-100' };
    }
  };

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-800 font-medium">Error loading templates</h3>
        <p className="text-red-600 mt-1">{error}</p>
        <button
          onClick={refreshTemplates}
          className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <Package className="w-5 h-5 mr-2" />
            Optimized Templates
          </h2>
          <span className="text-sm text-gray-500">
            {templates.length} template{templates.length !== 1 ? 's' : ''} available
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={refreshTemplates}
            disabled={loading}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
          
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Close
            </button>
          )}
        </div>
      </div>

      {/* Statistics */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Templates</p>
                <p className="text-2xl font-bold text-blue-900">{statistics.totalTemplates}</p>
              </div>
              <Package className="w-8 h-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Built Templates</p>
                <p className="text-2xl font-bold text-green-900">{statistics.builtTemplates}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">Average Size</p>
                <p className="text-2xl font-bold text-purple-900">{statistics.averageSizeMB}MB</p>
              </div>
              <BarChart3 className="w-8 h-8 text-purple-400" />
            </div>
          </div>

          <div className="bg-orange-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Total Size</p>
                <p className="text-2xl font-bold text-orange-900">{statistics.totalSizeMB}MB</p>
              </div>
              <TrendingDown className="w-8 h-8 text-orange-400" />
            </div>
          </div>
        </div>
      )}

      {/* Recommendations */}
      {recommendations && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {recommendations.mostOptimized && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Zap className="w-5 h-5 text-green-600 mr-2" />
                <h3 className="font-medium text-green-800">Most Optimized</h3>
              </div>
              <p className="text-sm text-green-700">
                <strong>{recommendations.mostOptimized.name}</strong> - 
                {typeof recommendations.mostOptimized.optimizedSize === 'number' 
                  ? ` ${recommendations.mostOptimized.optimizedSize}MB` 
                  : ' Size unknown'}
              </p>
            </div>
          )}

          {recommendations.mostVersatile && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Award className="w-5 h-5 text-blue-600 mr-2" />
                <h3 className="font-medium text-blue-800">Most Versatile</h3>
              </div>
              <p className="text-sm text-blue-700">
                <strong>{recommendations.mostVersatile.name}</strong> - 
                {recommendations.mostVersatile.languages.length} languages
              </p>
            </div>
          )}
        </div>
      )}

      {/* Build Actions */}
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            {selectedTemplates.size > 0 
              ? `${selectedTemplates.size} template${selectedTemplates.size !== 1 ? 's' : ''} selected`
              : 'No templates selected'
            }
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {selectedTemplates.size > 0 && (
            <button
              onClick={handleBuildSelected}
              disabled={building}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              {building ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Building...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Build Selected
                </>
              )}
            </button>
          )}
          
          <button
            onClick={handleBuildAll}
            disabled={building}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center"
          >
            {building ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Building All...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Build All Templates
              </>
            )}
          </button>
        </div>
      </div>

      {/* Templates List */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        {loading && templates.length === 0 ? (
          <div className="p-8 text-center">
            <RefreshCw className="w-8 h-8 animate-spin mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">Loading templates...</p>
          </div>
        ) : templates.length === 0 ? (
          <div className="p-8 text-center">
            <Package className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-500">Templates will appear here once loaded.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedTemplates.size === templates.length && templates.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedTemplates(new Set(templates.map(t => t.id)));
                        } else {
                          setSelectedTemplates(new Set());
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Template
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Languages
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Package Managers
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {templates.map((template) => {
                  const templateStatus = getTemplateStatus(template.id);
                  const statusDisplay = getStatusDisplay(templateStatus.status);
                  const StatusIcon = statusDisplay.icon;

                  return (
                    <tr key={template.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4">
                        <input
                          type="checkbox"
                          checked={selectedTemplates.has(template.id)}
                          onChange={(e) => handleSelectTemplate(template.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-4 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{template.name}</div>
                          <div className="text-sm text-gray-500">{template.description}</div>
                          <div className="text-xs text-gray-400 font-mono">{template.optimizedImage}</div>
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${statusDisplay.bg}`}>
                          <StatusIcon className={`w-3 h-3 mr-1 ${statusDisplay.color}`} />
                          <span className={statusDisplay.color}>
                            {templateStatus.status}
                          </span>
                        </span>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex items-center">
                          <span className="text-sm text-gray-900">{templateStatus.size}</span>
                          {template.size === 'minimal' && (
                            <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                              Minimal
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex flex-wrap gap-1">
                          {template.languages.map((lang) => (
                            <span
                              key={lang}
                              className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                            >
                              {lang}
                            </span>
                          ))}
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex flex-wrap gap-1">
                          {template.packageManagers.map((pm) => (
                            <span
                              key={pm}
                              className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded"
                            >
                              {pm}
                            </span>
                          ))}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Size Analysis */}
      {analysis.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Layers className="w-5 h-5 mr-2" />
            Size Analysis
          </h3>
          <div className="space-y-3">
            {analysis.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <span className="font-medium text-gray-900">{item.name}</span>
                  <div className="text-sm text-gray-500">
                    {item.languages.join(', ')} • {item.packageManagers.join(', ')}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {typeof item.optimizedSize === 'number' ? `${item.optimizedSize}MB` : item.optimizedSize}
                  </div>
                  <div className="text-xs text-gray-500">
                    {typeof item.layers === 'number' ? `${item.layers} layers` : item.layers}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
