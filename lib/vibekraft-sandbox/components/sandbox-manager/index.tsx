/**
 * VibeKraft Sandbox Manager
 * 
 * Main component for managing sandboxes
 */

'use client';

import React, { useState } from 'react';
import { SandboxList } from './sandbox-list';
import { SandboxGrid } from './sandbox-grid';
import { CreateSandboxForm } from './create-sandbox-form';
import { SandboxDetails } from './sandbox-details';
import { SandboxListItem, CreateSandboxRequest, useSandboxManager } from '../../hooks/use-sandbox-manager';

interface SandboxManagerProps {
  projectId?: string;
  className?: string;
}

type ViewMode = 'list' | 'create' | 'details';

export function SandboxManager({ projectId, className = '' }: SandboxManagerProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedSandbox, setSelectedSandbox] = useState<SandboxListItem | null>(null);
  const [creating, setCreating] = useState(false);
  
  const { createSandbox, error } = useSandboxManager({ projectId });

  // Handle create sandbox
  const handleCreateSandbox = async (config: CreateSandboxRequest) => {
    try {
      setCreating(true);
      const sandboxId = await createSandbox(config);
      console.log('Sandbox created:', sandboxId);
      setViewMode('list');
    } catch (error) {
      console.error('Failed to create sandbox:', error);
      throw error;
    } finally {
      setCreating(false);
    }
  };

  // Handle sandbox selection
  const handleSandboxSelect = (sandbox: SandboxListItem) => {
    setSelectedSandbox(sandbox);
    setViewMode('details');
  };

  // Handle navigation
  const handleShowCreateForm = () => {
    setViewMode('create');
  };

  const handleBackToList = () => {
    setViewMode('list');
    setSelectedSandbox(null);
  };

  return (
    <div className={`w-full h-full ${className}`}>
      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {viewMode === 'list' && (
        <SandboxList
          projectId={projectId}
          onCreateSandbox={handleShowCreateForm}
          onSandboxSelect={handleSandboxSelect}
        />
      )}

      {viewMode === 'create' && (
        <CreateSandboxForm
          onSubmit={handleCreateSandbox}
          onCancel={handleBackToList}
          loading={creating}
        />
      )}

      {viewMode === 'details' && selectedSandbox && (
        <SandboxDetails
          sandbox={selectedSandbox}
          onClose={handleBackToList}
        />
      )}
    </div>
  );
}

// Export individual components for flexibility
export { SandboxList } from './sandbox-list';
export { CreateSandboxForm } from './create-sandbox-form';
export { SandboxDetails } from './sandbox-details';
export { useSandboxManager } from '../../hooks/use-sandbox-manager';
export type { SandboxListItem, CreateSandboxRequest } from '../../hooks/use-sandbox-manager';
