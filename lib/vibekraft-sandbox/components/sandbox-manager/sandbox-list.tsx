/**
 * VibeKraft Sandbox List Component
 * 
 * Displays a list of sandboxes with management capabilities
 */

'use client';

import React, { useState } from 'react';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Trash2, 
  MoreHorizontal, 
  RefreshCw,
  Filter,
  Plus,
  Container,
  Clock,
  Cpu,
  HardDrive,
  Network
} from 'lucide-react';
import { useSandboxManager, SandboxListItem } from '../../hooks/use-sandbox-manager';

interface SandboxListProps {
  projectId?: string;
  onCreateSandbox?: () => void;
  onSandboxSelect?: (sandbox: SandboxListItem) => void;
}

export function SandboxList({ projectId, onCreateSandbox, onSandboxSelect }: SandboxListProps) {
  const {
    sandboxes,
    loading,
    error,
    refreshSandboxes,
    deleteSandbox,
    bulkOperation,
    setFilter,
    filter
  } = useSandboxManager({ projectId });

  const [selectedSandboxes, setSelectedSandboxes] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);

  // Handle sandbox selection
  const handleSelectSandbox = (sandboxId: string, checked: boolean) => {
    const newSelected = new Set(selectedSandboxes);
    if (checked) {
      newSelected.add(sandboxId);
    } else {
      newSelected.delete(sandboxId);
    }
    setSelectedSandboxes(newSelected);
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSandboxes(new Set(sandboxes.map(s => s.id)));
    } else {
      setSelectedSandboxes(new Set());
    }
  };

  // Handle bulk operations
  const handleBulkOperation = async (operation: string) => {
    if (selectedSandboxes.size === 0) return;

    try {
      await bulkOperation(operation, Array.from(selectedSandboxes));
      setSelectedSandboxes(new Set());
    } catch (error) {
      console.error(`Bulk ${operation} failed:`, error);
    }
  };

  // Handle delete sandbox
  const handleDeleteSandbox = async (sandboxId: string) => {
    if (confirm('Are you sure you want to delete this sandbox?')) {
      try {
        await deleteSandbox(sandboxId);
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
  };

  // Get status color
  const getStatusColor = (state: string) => {
    switch (state.toLowerCase()) {
      case 'running': return 'text-green-600 bg-green-100';
      case 'stopped': return 'text-gray-600 bg-gray-100';
      case 'starting': return 'text-blue-600 bg-blue-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString() + ' ' + new Date(date).toLocaleTimeString();
  };

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-800 font-medium">Error loading sandboxes</h3>
        <p className="text-red-600 mt-1">{error}</p>
        <button
          onClick={refreshSandboxes}
          className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <Container className="w-5 h-5 mr-2" />
            Sandboxes
          </h2>
          <span className="text-sm text-gray-500">
            {sandboxes.length} sandbox{sandboxes.length !== 1 ? 'es' : ''}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
          >
            <Filter className="w-4 h-4" />
          </button>
          
          <button
            onClick={refreshSandboxes}
            disabled={loading}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
          
          {onCreateSandbox && (
            <button
              onClick={onCreateSandbox}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Sandbox
            </button>
          )}
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="p-4 bg-gray-50 rounded-lg border">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={filter.status || ''}
                onChange={(e) => setFilter({ ...filter, status: e.target.value || undefined })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All statuses</option>
                <option value="running">Running</option>
                <option value="stopped">Stopped</option>
                <option value="starting">Starting</option>
                <option value="error">Error</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Template
              </label>
              <select
                value={filter.template || ''}
                onChange={(e) => setFilter({ ...filter, template: e.target.value || undefined })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All templates</option>
                <option value="vibekraft/nodejs-minimal:latest">Node.js Minimal</option>
                <option value="vibekraft/python-minimal:latest">Python Minimal</option>
                <option value="vibekraft/fullstack-dev:latest">Full-Stack Dev</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Actions */}
      {selectedSandboxes.size > 0 && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-blue-800 font-medium">
              {selectedSandboxes.size} sandbox{selectedSandboxes.size !== 1 ? 'es' : ''} selected
            </span>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBulkOperation('start')}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Start
              </button>
              <button
                onClick={() => handleBulkOperation('stop')}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
              >
                Stop
              </button>
              <button
                onClick={() => handleBulkOperation('destroy')}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Sandbox List */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        {loading && sandboxes.length === 0 ? (
          <div className="p-8 text-center">
            <RefreshCw className="w-8 h-8 animate-spin mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">Loading sandboxes...</p>
          </div>
        ) : sandboxes.length === 0 ? (
          <div className="p-8 text-center">
            <Container className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No sandboxes found</h3>
            <p className="text-gray-500 mb-4">Get started by creating your first sandbox.</p>
            {onCreateSandbox && (
              <button
                onClick={onCreateSandbox}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Create Sandbox
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedSandboxes.size === sandboxes.length && sandboxes.length > 0}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Template
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Resources
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sandboxes.map((sandbox) => (
                  <tr
                    key={sandbox.id}
                    className="hover:bg-gray-50 cursor-pointer"
                    onClick={() => onSandboxSelect?.(sandbox)}
                  >
                    <td className="px-4 py-4">
                      <input
                        type="checkbox"
                        checked={selectedSandboxes.has(sandbox.id)}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleSelectSandbox(sandbox.id, e.target.checked);
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-4 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{sandbox.name}</div>
                        <div className="text-sm text-gray-500">{sandbox.id}</div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(sandbox.state)}`}>
                        {sandbox.state}
                      </span>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-900">
                      {sandbox.template.replace('vibekraft/', '').replace(':latest', '')}
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center">
                          <Cpu className="w-3 h-3 mr-1" />
                          {sandbox.resources.vcpuCount}
                        </div>
                        <div className="flex items-center">
                          <HardDrive className="w-3 h-3 mr-1" />
                          {sandbox.resources.memSizeMib}MB
                        </div>
                        <div className="flex items-center">
                          <Network className="w-3 h-3 mr-1" />
                          {sandbox.network.ports}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="w-3 h-3 mr-1" />
                        {formatDate(sandbox.createdAt)}
                      </div>
                    </td>
                    <td className="px-4 py-4 text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle start/stop
                          }}
                          className="p-1 text-gray-400 hover:text-green-600"
                          title="Start"
                        >
                          <Play className="w-4 h-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteSandbox(sandbox.id);
                          }}
                          className="p-1 text-gray-400 hover:text-red-600"
                          title="Delete"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <MoreHorizontal className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
