/**
 * VibeKraft Sandbox Grid Component
 * 
 * Grid layout for sandbox cards with filtering and bulk operations
 */

'use client';

import React, { useState } from 'react';
import { 
  Grid, 
  List, 
  Plus, 
  RefreshCw,
  Filter,
  Search,
  Container,
  CheckSquare,
  Square
} from 'lucide-react';
import { SandboxCard } from './sandbox-card';
import { useSandboxManager, SandboxListItem } from '../../hooks/use-sandbox-manager';

interface SandboxGridProps {
  projectId?: string;
  onCreateSandbox?: () => void;
  onSandboxSelect?: (sandbox: SandboxListItem) => void;
  onSwitchToList?: () => void;
  compact?: boolean;
}

export function SandboxGrid({ 
  projectId, 
  onCreateSandbox, 
  onSandboxSelect,
  onSwitchToList,
  compact = false 
}: SandboxGridProps) {
  const {
    sandboxes,
    loading,
    error,
    refreshSandboxes,
    deleteSandbox,
    bulkOperation,
    setFilter,
    filter
  } = useSandboxManager({ projectId });

  const [selectedSandboxes, setSelectedSandboxes] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'compact'>('grid');

  // Filter sandboxes based on search and filters
  const filteredSandboxes = sandboxes.filter(sandbox => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!sandbox.name.toLowerCase().includes(query) && 
          !sandbox.id.toLowerCase().includes(query) &&
          !sandbox.template.toLowerCase().includes(query)) {
        return false;
      }
    }
    return true;
  });

  // Handle sandbox selection
  const handleSelectSandbox = (sandboxId: string, checked: boolean) => {
    const newSelected = new Set(selectedSandboxes);
    if (checked) {
      newSelected.add(sandboxId);
    } else {
      newSelected.delete(sandboxId);
    }
    setSelectedSandboxes(newSelected);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedSandboxes.size === filteredSandboxes.length) {
      setSelectedSandboxes(new Set());
    } else {
      setSelectedSandboxes(new Set(filteredSandboxes.map(s => s.id)));
    }
  };

  // Handle bulk operations
  const handleBulkOperation = async (operation: string) => {
    if (selectedSandboxes.size === 0) return;

    try {
      await bulkOperation(operation, Array.from(selectedSandboxes));
      setSelectedSandboxes(new Set());
    } catch (error) {
      console.error(`Bulk ${operation} failed:`, error);
    }
  };

  // Handle individual sandbox actions
  const handleSandboxAction = async (action: string, sandboxId: string) => {
    try {
      switch (action) {
        case 'start':
        case 'stop':
        case 'restart':
          await bulkOperation(action, [sandboxId]);
          break;
        case 'delete':
          await deleteSandbox(sandboxId);
          break;
      }
    } catch (error) {
      console.error(`Action ${action} failed:`, error);
    }
  };

  // Handle terminal access
  const handleTerminal = (sandboxId: string) => {
    // Open terminal in new window/tab
    window.open(`/sandbox/${sandboxId}/terminal`, '_blank');
  };

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-800 font-medium">Error loading sandboxes</h3>
        <p className="text-red-600 mt-1">{error}</p>
        <button
          onClick={refreshSandboxes}
          className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <Container className="w-5 h-5 mr-2" />
            Sandboxes
          </h2>
          <span className="text-sm text-gray-500">
            {filteredSandboxes.length} of {sandboxes.length} sandbox{sandboxes.length !== 1 ? 'es' : ''}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 rounded-md p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-1.5 rounded ${viewMode === 'grid' ? 'bg-white shadow-sm' : 'text-gray-500'}`}
              title="Grid view"
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('compact')}
              className={`p-1.5 rounded ${viewMode === 'compact' ? 'bg-white shadow-sm' : 'text-gray-500'}`}
              title="Compact view"
            >
              <Container className="w-4 h-4" />
            </button>
            {onSwitchToList && (
              <button
                onClick={onSwitchToList}
                className="p-1.5 rounded text-gray-500 hover:bg-white hover:shadow-sm"
                title="List view"
              >
                <List className="w-4 h-4" />
              </button>
            )}
          </div>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-md hover:bg-gray-100 ${showFilters ? 'text-blue-600 bg-blue-50' : 'text-gray-400'}`}
          >
            <Filter className="w-4 h-4" />
          </button>
          
          <button
            onClick={refreshSandboxes}
            disabled={loading}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
          
          {onCreateSandbox && (
            <button
              onClick={onCreateSandbox}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Sandbox
            </button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search sandboxes by name, ID, or template..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="p-4 bg-gray-50 rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filter.status || ''}
                  onChange={(e) => setFilter({ ...filter, status: e.target.value || undefined })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All statuses</option>
                  <option value="running">Running</option>
                  <option value="stopped">Stopped</option>
                  <option value="starting">Starting</option>
                  <option value="error">Error</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template
                </label>
                <select
                  value={filter.template || ''}
                  onChange={(e) => setFilter({ ...filter, template: e.target.value || undefined })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All templates</option>
                  <option value="vibekraft/nodejs-minimal:latest">Node.js Minimal</option>
                  <option value="vibekraft/python-minimal:latest">Python Minimal</option>
                  <option value="vibekraft/fullstack-dev:latest">Full-Stack Dev</option>
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={() => {
                    setFilter({});
                    setSearchQuery('');
                  }}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bulk Actions */}
      {selectedSandboxes.size > 0 && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleSelectAll}
                className="flex items-center text-blue-800 hover:text-blue-900"
              >
                {selectedSandboxes.size === filteredSandboxes.length ? (
                  <CheckSquare className="w-4 h-4 mr-2" />
                ) : (
                  <Square className="w-4 h-4 mr-2" />
                )}
                <span className="font-medium">
                  {selectedSandboxes.size} selected
                </span>
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBulkOperation('start')}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Start
              </button>
              <button
                onClick={() => handleBulkOperation('stop')}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
              >
                Stop
              </button>
              <button
                onClick={() => handleBulkOperation('destroy')}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Sandbox Grid */}
      <div className="min-h-96">
        {loading && sandboxes.length === 0 ? (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">Loading sandboxes...</p>
            </div>
          </div>
        ) : filteredSandboxes.length === 0 ? (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <Container className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchQuery || filter.status || filter.template ? 'No matching sandboxes' : 'No sandboxes found'}
              </h3>
              <p className="text-gray-500 mb-4">
                {searchQuery || filter.status || filter.template 
                  ? 'Try adjusting your search or filters.'
                  : 'Get started by creating your first sandbox.'
                }
              </p>
              {onCreateSandbox && !searchQuery && !filter.status && !filter.template && (
                <button
                  onClick={onCreateSandbox}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Create Sandbox
                </button>
              )}
            </div>
          </div>
        ) : (
          <div className={`
            grid gap-4
            ${viewMode === 'compact' 
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
            }
          `}>
            {filteredSandboxes.map((sandbox) => (
              <SandboxCard
                key={sandbox.id}
                sandbox={sandbox}
                onSelect={onSandboxSelect}
                onStart={(id) => handleSandboxAction('start', id)}
                onStop={(id) => handleSandboxAction('stop', id)}
                onRestart={(id) => handleSandboxAction('restart', id)}
                onDelete={(id) => handleSandboxAction('delete', id)}
                onTerminal={handleTerminal}
                selected={selectedSandboxes.has(sandbox.id)}
                onSelectionChange={handleSelectSandbox}
                compact={viewMode === 'compact'}
                showActions={true}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
