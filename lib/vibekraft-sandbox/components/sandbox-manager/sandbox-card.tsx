/**
 * VibeKraft Sandbox Card Component
 * 
 * Card-based view for individual sandboxes with quick actions
 */

'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Cpu, 
  HardDrive, 
  Network, 
  Clock,
  Play,
  Square,
  RotateCcw,
  Trash2,
  Terminal,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertCircle,
  Pause,
  ExternalLink,
  Settings,
  Activity,
  Zap
} from 'lucide-react';
import { SandboxListItem } from '../../hooks/use-sandbox-manager';

interface SandboxCardProps {
  sandbox: SandboxListItem;
  onSelect?: (sandbox: SandboxListItem) => void;
  onStart?: (sandboxId: string) => void;
  onStop?: (sandboxId: string) => void;
  onRestart?: (sandboxId: string) => void;
  onDelete?: (sandboxId: string) => void;
  onTerminal?: (sandboxId: string) => void;
  selected?: boolean;
  onSelectionChange?: (sandboxId: string, selected: boolean) => void;
  compact?: boolean;
  showActions?: boolean;
}

export function SandboxCard({
  sandbox,
  onSelect,
  onStart,
  onStop,
  onRestart,
  onDelete,
  onTerminal,
  selected = false,
  onSelectionChange,
  compact = false,
  showActions = true
}: SandboxCardProps) {
  const [showMenu, setShowMenu] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Get status display
  const getStatusDisplay = (state: string) => {
    switch (state.toLowerCase()) {
      case 'running':
        return { 
          icon: CheckCircle, 
          color: 'text-green-600', 
          bg: 'bg-green-100',
          border: 'border-green-200',
          pulse: true
        };
      case 'stopped':
        return { 
          icon: Square, 
          color: 'text-gray-600', 
          bg: 'bg-gray-100',
          border: 'border-gray-200',
          pulse: false
        };
      case 'starting':
        return { 
          icon: Play, 
          color: 'text-blue-600', 
          bg: 'bg-blue-100',
          border: 'border-blue-200',
          pulse: true
        };
      case 'stopping':
        return { 
          icon: Pause, 
          color: 'text-yellow-600', 
          bg: 'bg-yellow-100',
          border: 'border-yellow-200',
          pulse: true
        };
      case 'error':
        return { 
          icon: XCircle, 
          color: 'text-red-600', 
          bg: 'bg-red-100',
          border: 'border-red-200',
          pulse: false
        };
      default:
        return { 
          icon: AlertCircle, 
          color: 'text-yellow-600', 
          bg: 'bg-yellow-100',
          border: 'border-yellow-200',
          pulse: false
        };
    }
  };

  // Handle action with loading state
  const handleAction = async (action: string, handler?: (id: string) => void) => {
    if (!handler) return;
    
    try {
      setActionLoading(action);
      await handler(sandbox.id);
    } catch (error) {
      console.error(`Action ${action} failed:`, error);
    } finally {
      setActionLoading(null);
      setShowMenu(false);
    }
  };

  // Handle delete with confirmation
  const handleDelete = () => {
    if (confirm(`Are you sure you want to delete "${sandbox.name}"?`)) {
      handleAction('delete', onDelete);
    }
  };

  // Get template display name
  const getTemplateDisplayName = (template: string) => {
    const templateMap: Record<string, { name: string; icon: string }> = {
      'vibekraft/nodejs-minimal:latest': { name: 'Node.js', icon: '🟢' },
      'vibekraft/python-minimal:latest': { name: 'Python', icon: '🐍' },
      'vibekraft/fullstack-dev:latest': { name: 'Full-Stack', icon: '🚀' },
      'vibekraft/rust-dev:latest': { name: 'Rust', icon: '🦀' },
      'vibekraft/datascience:latest': { name: 'Data Science', icon: '📊' }
    };

    const templateInfo = templateMap[template];
    if (templateInfo) {
      return templateInfo;
    }

    // Fallback for unknown templates
    const name = template.replace('vibekraft/', '').replace(':latest', '');
    return { name: name.charAt(0).toUpperCase() + name.slice(1), icon: '📦' };
  };

  // Format date
  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - new Date(date).getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(date).toLocaleDateString();
  };

  const statusDisplay = getStatusDisplay(sandbox.state);
  const StatusIcon = statusDisplay.icon;
  const templateInfo = getTemplateDisplayName(sandbox.template);

  return (
    <div 
      className={`
        relative bg-white border-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer
        ${selected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}
        ${compact ? 'p-4' : 'p-6'}
      `}
      onClick={() => onSelect?.(sandbox)}
    >
      {/* Selection Checkbox */}
      {onSelectionChange && (
        <div className="absolute top-3 left-3">
          <input
            type="checkbox"
            checked={selected}
            onChange={(e) => {
              e.stopPropagation();
              onSelectionChange(sandbox.id, e.target.checked);
            }}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
        </div>
      )}

      {/* Status Indicator */}
      <div className={`absolute top-3 right-3 flex items-center px-2 py-1 rounded-full ${statusDisplay.bg} ${statusDisplay.border} border`}>
        <StatusIcon className={`w-3 h-3 mr-1 ${statusDisplay.color} ${statusDisplay.pulse ? 'animate-pulse' : ''}`} />
        <span className={`text-xs font-medium ${statusDisplay.color}`}>
          {sandbox.state}
        </span>
      </div>

      {/* Main Content */}
      <div className={`${onSelectionChange ? 'ml-6' : ''} ${compact ? 'space-y-3' : 'space-y-4'}`}>
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0 pr-4">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-lg">{templateInfo.icon}</span>
              <h3 className={`font-semibold text-gray-900 truncate ${compact ? 'text-sm' : 'text-base'}`}>
                {sandbox.name}
              </h3>
            </div>
            <p className={`text-gray-500 truncate ${compact ? 'text-xs' : 'text-sm'}`}>
              {sandbox.id}
            </p>
          </div>
        </div>

        {/* Template Info */}
        <div className="flex items-center space-x-2">
          <Container className="w-4 h-4 text-gray-400" />
          <span className={`text-gray-600 ${compact ? 'text-xs' : 'text-sm'}`}>
            {templateInfo.name}
          </span>
          {sandbox.state === 'running' && (
            <div className="flex items-center space-x-1">
              <Activity className="w-3 h-3 text-green-500" />
              <span className="text-xs text-green-600">Live</span>
            </div>
          )}
        </div>

        {/* Resources */}
        <div className={`grid grid-cols-3 gap-2 ${compact ? 'text-xs' : 'text-sm'}`}>
          <div className="flex items-center space-x-1 text-gray-500">
            <Cpu className="w-3 h-3" />
            <span>{sandbox.resources.vcpuCount}</span>
          </div>
          <div className="flex items-center space-x-1 text-gray-500">
            <HardDrive className="w-3 h-3" />
            <span>{sandbox.resources.memSizeMib}MB</span>
          </div>
          <div className="flex items-center space-x-1 text-gray-500">
            <Network className="w-3 h-3" />
            <span>{sandbox.network.ports || 0}</span>
          </div>
        </div>

        {/* Health Status */}
        {sandbox.health && (
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              sandbox.health.status === 'healthy' ? 'bg-green-500' : 
              sandbox.health.status === 'unhealthy' ? 'bg-red-500' : 'bg-yellow-500'
            }`} />
            <span className={`text-xs text-gray-500`}>
              Health: {sandbox.health.status}
            </span>
          </div>
        )}

        {/* Timestamps */}
        <div className="flex items-center justify-between text-xs text-gray-400">
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3" />
            <span>Created {formatDate(sandbox.createdAt)}</span>
          </div>
          {sandbox.lastAccessedAt && (
            <span>Active {formatDate(sandbox.lastAccessedAt)}</span>
          )}
        </div>

        {/* Quick Actions */}
        {showActions && (
          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <div className="flex items-center space-x-1">
              {sandbox.state === 'running' ? (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAction('stop', onStop);
                  }}
                  disabled={actionLoading === 'stop'}
                  className="p-1.5 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded transition-colors disabled:opacity-50"
                  title="Stop"
                >
                  {actionLoading === 'stop' ? (
                    <div className="w-3 h-3 border border-yellow-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Square className="w-3 h-3" />
                  )}
                </button>
              ) : (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAction('start', onStart);
                  }}
                  disabled={actionLoading === 'start'}
                  className="p-1.5 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded transition-colors disabled:opacity-50"
                  title="Start"
                >
                  {actionLoading === 'start' ? (
                    <div className="w-3 h-3 border border-green-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Play className="w-3 h-3" />
                  )}
                </button>
              )}

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction('restart', onRestart);
                }}
                disabled={actionLoading === 'restart'}
                className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors disabled:opacity-50"
                title="Restart"
              >
                {actionLoading === 'restart' ? (
                  <div className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin" />
                ) : (
                  <RotateCcw className="w-3 h-3" />
                )}
              </button>

              {onTerminal && sandbox.state === 'running' && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onTerminal(sandbox.id);
                  }}
                  className="p-1.5 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded transition-colors"
                  title="Terminal"
                >
                  <Terminal className="w-3 h-3" />
                </button>
              )}
            </div>

            <div className="flex items-center space-x-1">
              {sandbox.state === 'running' && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // Open sandbox in new tab/window
                    window.open(`/sandbox/${sandbox.id}`, '_blank');
                  }}
                  className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                  title="Open"
                >
                  <ExternalLink className="w-3 h-3" />
                </button>
              )}

              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowMenu(!showMenu);
                  }}
                  className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded transition-colors"
                  title="More actions"
                >
                  <MoreHorizontal className="w-3 h-3" />
                </button>

                {showMenu && (
                  <div className="absolute right-0 bottom-full mb-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    <div className="py-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onSelect?.(sandbox);
                          setShowMenu(false);
                        }}
                        className="w-full px-3 py-1.5 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <Settings className="w-3 h-3 mr-2" />
                        Details
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete();
                        }}
                        disabled={actionLoading === 'delete'}
                        className="w-full px-3 py-1.5 text-left text-sm text-red-600 hover:bg-red-50 flex items-center disabled:opacity-50"
                      >
                        {actionLoading === 'delete' ? (
                          <div className="w-3 h-3 border border-red-600 border-t-transparent rounded-full animate-spin mr-2" />
                        ) : (
                          <Trash2 className="w-3 h-3 mr-2" />
                        )}
                        Delete
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Performance Indicator */}
        {sandbox.state === 'running' && (
          <div className="flex items-center space-x-2 pt-2">
            <Zap className="w-3 h-3 text-green-500" />
            <div className="flex-1 bg-gray-200 rounded-full h-1">
              <div 
                className="bg-green-500 h-1 rounded-full transition-all duration-300" 
                style={{ width: '75%' }} // This would be dynamic based on actual performance
              />
            </div>
            <span className="text-xs text-gray-500">75%</span>
          </div>
        )}
      </div>

      {/* Click overlay to close menu */}
      {showMenu && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  );
}
