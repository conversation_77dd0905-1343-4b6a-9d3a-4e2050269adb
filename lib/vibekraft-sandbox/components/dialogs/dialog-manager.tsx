/**
 * VibeKraft Dialog Manager Component
 * 
 * Centralized dialog management for sandbox operations
 */

'use client';

import React, { useState, useCallback } from 'react';
import { ConfirmationDialog } from './confirmation-dialog';
import { SandboxActionDialog } from './sandbox-action-dialog';
import { TemplateSelectionDialog } from './template-selection-dialog';
import { TerminalDialog } from './terminal-dialog';
import { SandboxListItem } from '../../hooks/use-sandbox-manager';

export interface DialogState {
  type: 'confirmation' | 'sandboxAction' | 'templateSelection' | 'terminal' | null;
  isOpen: boolean;
  data?: any;
}

export interface DialogManagerProps {
  onSandboxAction?: (action: string, sandboxId: string, options?: any) => Promise<void>;
  onTemplateSelect?: (templateId: string) => void;
  children?: React.ReactNode;
}

export interface DialogManagerContextType {
  showConfirmation: (config: {
    title: string;
    message: string;
    onConfirm: () => void;
    type?: 'danger' | 'warning' | 'info' | 'success';
    confirmText?: string;
    cancelText?: string;
  }) => void;
  showSandboxAction: (config: {
    sandbox: SandboxListItem;
    action: 'start' | 'stop' | 'restart' | 'delete' | 'clone' | 'export' | 'configure';
    onConfirm: (action: string, options?: any) => Promise<void>;
  }) => void;
  showTemplateSelection: (config: {
    selectedTemplate?: string;
    requirements?: {
      languages?: string[];
      packageManagers?: string[];
      maxSize?: 'minimal' | 'standard' | 'full';
    };
    onSelect: (templateId: string) => void;
  }) => void;
  showTerminal: (config: {
    sandbox: SandboxListItem;
    fullscreen?: boolean;
  }) => void;
  closeDialog: () => void;
}

// Create context for dialog manager
export const DialogManagerContext = React.createContext<DialogManagerContextType | null>(null);

// Hook to use dialog manager
export function useDialogManager() {
  const context = React.useContext(DialogManagerContext);
  if (!context) {
    throw new Error('useDialogManager must be used within a DialogManagerProvider');
  }
  return context;
}

export function DialogManager({ onSandboxAction, onTemplateSelect, children }: DialogManagerProps) {
  const [dialogState, setDialogState] = useState<DialogState>({
    type: null,
    isOpen: false,
    data: {}
  });
  const [loading, setLoading] = useState(false);

  // Show confirmation dialog
  const showConfirmation = useCallback((config: any) => {
    setDialogState({
      type: 'confirmation',
      isOpen: true,
      data: config
    });
  }, []);

  // Show sandbox action dialog
  const showSandboxAction = useCallback((config: any) => {
    setDialogState({
      type: 'sandboxAction',
      isOpen: true,
      data: config
    });
  }, []);

  // Show template selection dialog
  const showTemplateSelection = useCallback((config: any) => {
    setDialogState({
      type: 'templateSelection',
      isOpen: true,
      data: config
    });
  }, []);

  // Show terminal dialog
  const showTerminal = useCallback((config: any) => {
    setDialogState({
      type: 'terminal',
      isOpen: true,
      data: config
    });
  }, []);

  // Close dialog
  const closeDialog = useCallback(() => {
    setDialogState({
      type: null,
      isOpen: false,
      data: {}
    });
    setLoading(false);
  }, []);

  // Handle confirmation dialog confirm
  const handleConfirmationConfirm = async () => {
    if (dialogState.data?.onConfirm) {
      try {
        setLoading(true);
        await dialogState.data.onConfirm();
        closeDialog();
      } catch (error) {
        console.error('Confirmation action failed:', error);
        setLoading(false);
      }
    }
  };

  // Handle sandbox action confirm
  const handleSandboxActionConfirm = async (action: string, options?: any) => {
    if (dialogState.data?.onConfirm) {
      try {
        setLoading(true);
        await dialogState.data.onConfirm(action, options);
        closeDialog();
      } catch (error) {
        console.error('Sandbox action failed:', error);
        setLoading(false);
      }
    }
  };

  // Handle template selection
  const handleTemplateSelect = (templateId: string) => {
    if (dialogState.data?.onSelect) {
      dialogState.data.onSelect(templateId);
    }
    if (onTemplateSelect) {
      onTemplateSelect(templateId);
    }
    closeDialog();
  };

  // Context value
  const contextValue: DialogManagerContextType = {
    showConfirmation,
    showSandboxAction,
    showTemplateSelection,
    showTerminal,
    closeDialog
  };

  return (
    <DialogManagerContext.Provider value={contextValue}>
      {children}

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={dialogState.type === 'confirmation' && dialogState.isOpen}
        onClose={closeDialog}
        onConfirm={handleConfirmationConfirm}
        title={dialogState.data?.title || ''}
        message={dialogState.data?.message || ''}
        confirmText={dialogState.data?.confirmText}
        cancelText={dialogState.data?.cancelText}
        type={dialogState.data?.type}
        loading={loading}
      />

      {/* Sandbox Action Dialog */}
      <SandboxActionDialog
        isOpen={dialogState.type === 'sandboxAction' && dialogState.isOpen}
        onClose={closeDialog}
        sandbox={dialogState.data?.sandbox || null}
        action={dialogState.data?.action || null}
        onConfirm={handleSandboxActionConfirm}
        loading={loading}
      />

      {/* Template Selection Dialog */}
      <TemplateSelectionDialog
        isOpen={dialogState.type === 'templateSelection' && dialogState.isOpen}
        onClose={closeDialog}
        onSelect={handleTemplateSelect}
        selectedTemplate={dialogState.data?.selectedTemplate}
        requirements={dialogState.data?.requirements}
      />

      {/* Terminal Dialog */}
      <TerminalDialog
        isOpen={dialogState.type === 'terminal' && dialogState.isOpen}
        onClose={closeDialog}
        sandbox={dialogState.data?.sandbox || null}
        fullscreen={dialogState.data?.fullscreen}
        onToggleFullscreen={() => {
          setDialogState(prev => ({
            ...prev,
            data: {
              ...prev.data,
              fullscreen: !prev.data?.fullscreen
            }
          }));
        }}
      />
    </DialogManagerContext.Provider>
  );
}

// Provider component for easier usage
export function DialogManagerProvider({ children, ...props }: DialogManagerProps) {
  return (
    <DialogManager {...props}>
      {children}
    </DialogManager>
  );
}
