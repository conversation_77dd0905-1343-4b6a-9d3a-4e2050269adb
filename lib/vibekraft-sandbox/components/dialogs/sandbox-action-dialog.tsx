/**
 * VibeKraft Sandbox Action Dialog Component
 * 
 * Dialog for performing actions on sandboxes with options
 */

'use client';

import React, { useState } from 'react';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Trash2, 
  Terminal,
  Download,
  Upload,
  Copy,
  Settings,
  X,
  Container,
  <PERSON><PERSON><PERSON>riangle,
  Clock
} from 'lucide-react';
import { SandboxListItem } from '../../hooks/use-sandbox-manager';

interface SandboxActionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  sandbox: SandboxListItem | null;
  action: 'start' | 'stop' | 'restart' | 'delete' | 'clone' | 'export' | 'configure' | null;
  onConfirm: (action: string, options?: any) => Promise<void>;
  loading?: boolean;
}

export function SandboxActionDialog({
  isOpen,
  onClose,
  sandbox,
  action,
  onConfirm,
  loading = false
}: SandboxActionDialogProps) {
  const [options, setOptions] = useState<any>({});
  const [step, setStep] = useState(1);

  if (!isOpen || !sandbox || !action) return null;

  // Get action configuration
  const getActionConfig = () => {
    switch (action) {
      case 'start':
        return {
          title: 'Start Sandbox',
          icon: <Play className="w-6 h-6" />,
          iconBg: 'bg-green-100',
          iconColor: 'text-green-600',
          confirmText: 'Start',
          confirmBg: 'bg-green-600 hover:bg-green-700',
          description: `Start the sandbox "${sandbox.name}". This will boot the container and make it available for use.`,
          hasOptions: true
        };
      case 'stop':
        return {
          title: 'Stop Sandbox',
          icon: <Square className="w-6 h-6" />,
          iconBg: 'bg-yellow-100',
          iconColor: 'text-yellow-600',
          confirmText: 'Stop',
          confirmBg: 'bg-yellow-600 hover:bg-yellow-700',
          description: `Stop the sandbox "${sandbox.name}". This will gracefully shut down the container.`,
          hasOptions: true
        };
      case 'restart':
        return {
          title: 'Restart Sandbox',
          icon: <RotateCcw className="w-6 h-6" />,
          iconBg: 'bg-blue-100',
          iconColor: 'text-blue-600',
          confirmText: 'Restart',
          confirmBg: 'bg-blue-600 hover:bg-blue-700',
          description: `Restart the sandbox "${sandbox.name}". This will stop and then start the container.`,
          hasOptions: true
        };
      case 'delete':
        return {
          title: 'Delete Sandbox',
          icon: <Trash2 className="w-6 h-6" />,
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600',
          confirmText: 'Delete',
          confirmBg: 'bg-red-600 hover:bg-red-700',
          description: `Permanently delete the sandbox "${sandbox.name}". This action cannot be undone.`,
          hasOptions: true
        };
      case 'clone':
        return {
          title: 'Clone Sandbox',
          icon: <Copy className="w-6 h-6" />,
          iconBg: 'bg-purple-100',
          iconColor: 'text-purple-600',
          confirmText: 'Clone',
          confirmBg: 'bg-purple-600 hover:bg-purple-700',
          description: `Create a copy of the sandbox "${sandbox.name}" with the same configuration.`,
          hasOptions: true
        };
      case 'export':
        return {
          title: 'Export Sandbox',
          icon: <Download className="w-6 h-6" />,
          iconBg: 'bg-indigo-100',
          iconColor: 'text-indigo-600',
          confirmText: 'Export',
          confirmBg: 'bg-indigo-600 hover:bg-indigo-700',
          description: `Export the sandbox "${sandbox.name}" as a template or backup.`,
          hasOptions: true
        };
      case 'configure':
        return {
          title: 'Configure Sandbox',
          icon: <Settings className="w-6 h-6" />,
          iconBg: 'bg-gray-100',
          iconColor: 'text-gray-600',
          confirmText: 'Apply',
          confirmBg: 'bg-gray-600 hover:bg-gray-700',
          description: `Modify the configuration of the sandbox "${sandbox.name}".`,
          hasOptions: true
        };
      default:
        return {
          title: 'Sandbox Action',
          icon: <Container className="w-6 h-6" />,
          iconBg: 'bg-gray-100',
          iconColor: 'text-gray-600',
          confirmText: 'Confirm',
          confirmBg: 'bg-gray-600 hover:bg-gray-700',
          description: 'Perform action on sandbox.',
          hasOptions: false
        };
    }
  };

  const config = getActionConfig();

  // Handle confirm
  const handleConfirm = async () => {
    try {
      await onConfirm(action, options);
      onClose();
    } catch (error) {
      console.error('Action failed:', error);
    }
  };

  // Render action-specific options
  const renderOptions = () => {
    switch (action) {
      case 'start':
        return (
          <div className="space-y-4">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.waitForHealthy || false}
                  onChange={(e) => setOptions({ ...options, waitForHealthy: e.target.checked })}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500 mr-2"
                />
                <span className="text-sm text-gray-700">Wait for health check to pass</span>
              </label>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Startup timeout (seconds)
              </label>
              <input
                type="number"
                value={options.timeout || 60}
                onChange={(e) => setOptions({ ...options, timeout: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                min="10"
                max="300"
              />
            </div>
          </div>
        );

      case 'stop':
        return (
          <div className="space-y-4">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.force || false}
                  onChange={(e) => setOptions({ ...options, force: e.target.checked })}
                  className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 mr-2"
                />
                <span className="text-sm text-gray-700">Force stop (kill immediately)</span>
              </label>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Graceful shutdown timeout (seconds)
              </label>
              <input
                type="number"
                value={options.timeout || 30}
                onChange={(e) => setOptions({ ...options, timeout: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                min="5"
                max="120"
                disabled={options.force}
              />
            </div>
          </div>
        );

      case 'restart':
        return (
          <div className="space-y-4">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.preserveData || true}
                  onChange={(e) => setOptions({ ...options, preserveData: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                />
                <span className="text-sm text-gray-700">Preserve data and configuration</span>
              </label>
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.waitForHealthy || false}
                  onChange={(e) => setOptions({ ...options, waitForHealthy: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                />
                <span className="text-sm text-gray-700">Wait for health check after restart</span>
              </label>
            </div>
          </div>
        );

      case 'delete':
        return (
          <div className="space-y-4">
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
                <span className="text-sm font-medium text-red-800">Warning</span>
              </div>
              <p className="text-sm text-red-700 mt-1">
                This action cannot be undone. All data in the sandbox will be permanently lost.
              </p>
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.deleteVolumes || false}
                  onChange={(e) => setOptions({ ...options, deleteVolumes: e.target.checked })}
                  className="rounded border-gray-300 text-red-600 focus:ring-red-500 mr-2"
                />
                <span className="text-sm text-gray-700">Also delete associated volumes</span>
              </label>
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.force || false}
                  onChange={(e) => setOptions({ ...options, force: e.target.checked })}
                  className="rounded border-gray-300 text-red-600 focus:ring-red-500 mr-2"
                />
                <span className="text-sm text-gray-700">Force delete (ignore running state)</span>
              </label>
            </div>
          </div>
        );

      case 'clone':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                New sandbox name
              </label>
              <input
                type="text"
                value={options.name || `${sandbox.name} (Copy)`}
                onChange={(e) => setOptions({ ...options, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Enter name for cloned sandbox"
              />
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.includeData || false}
                  onChange={(e) => setOptions({ ...options, includeData: e.target.checked })}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500 mr-2"
                />
                <span className="text-sm text-gray-700">Include current data and files</span>
              </label>
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.startAfterClone || false}
                  onChange={(e) => setOptions({ ...options, startAfterClone: e.target.checked })}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500 mr-2"
                />
                <span className="text-sm text-gray-700">Start cloned sandbox immediately</span>
              </label>
            </div>
          </div>
        );

      case 'export':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Export format
              </label>
              <select
                value={options.format || 'template'}
                onChange={(e) => setOptions({ ...options, format: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="template">Template (configuration only)</option>
                <option value="backup">Full backup (with data)</option>
                <option value="image">Docker image</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Export name
              </label>
              <input
                type="text"
                value={options.exportName || sandbox.name}
                onChange={(e) => setOptions({ ...options, exportName: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="Enter export name"
              />
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.compress || true}
                  onChange={(e) => setOptions({ ...options, compress: e.target.checked })}
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 mr-2"
                />
                <span className="text-sm text-gray-700">Compress export file</span>
              </label>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Center the modal */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
          &#8203;
        </span>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className={`flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full ${config.iconBg}`}>
                <div className={config.iconColor}>
                  {config.icon}
                </div>
              </div>
              <h3 className="ml-3 text-lg leading-6 font-medium text-gray-900">
                {config.title}
              </h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <div className="mb-6">
            <p className="text-sm text-gray-500 mb-4">
              {config.description}
            </p>

            {/* Sandbox Info */}
            <div className="p-3 bg-gray-50 rounded-md mb-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">{sandbox.name}</p>
                  <p className="text-xs text-gray-500">{sandbox.id}</p>
                </div>
                <div className="text-right">
                  <p className="text-xs text-gray-500">Status</p>
                  <p className="text-sm font-medium text-gray-900">{sandbox.state}</p>
                </div>
              </div>
            </div>

            {/* Action Options */}
            {config.hasOptions && renderOptions()}
          </div>

          {/* Actions */}
          <div className="flex flex-row-reverse space-x-reverse space-x-3">
            <button
              type="button"
              onClick={handleConfirm}
              disabled={loading}
              className={`inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed ${config.confirmBg} text-white`}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                config.confirmText
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
