/**
 * VibeKraft Terminal Dialog Component
 * 
 * Embedded terminal for sandbox access
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { 
  Terminal, 
  X, 
  Maximize2, 
  Minimize2,
  Copy,
  Download,
  Upload,
  Settings,
  Power,
  RefreshCw
} from 'lucide-react';
import { SandboxListItem } from '../../hooks/use-sandbox-manager';

interface TerminalDialogProps {
  isOpen: boolean;
  onClose: () => void;
  sandbox: SandboxListItem | null;
  fullscreen?: boolean;
  onToggleFullscreen?: () => void;
}

export function TerminalDialog({
  isOpen,
  onClose,
  sandbox,
  fullscreen = false,
  onToggleFullscreen
}: TerminalDialogProps) {
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [output, setOutput] = useState<string[]>([]);
  const [input, setInput] = useState('');
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [fontSize, setFontSize] = useState(14);
  const [theme, setTheme] = useState<'dark' | 'light'>('dark');
  
  const terminalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // Initialize terminal when dialog opens
  useEffect(() => {
    if (isOpen && sandbox) {
      connectToTerminal();
    }
    
    return () => {
      disconnectFromTerminal();
    };
  }, [isOpen, sandbox]);

  // Auto-scroll to bottom when output changes
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [output]);

  // Focus input when dialog opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Connect to terminal WebSocket
  const connectToTerminal = async () => {
    if (!sandbox) return;

    try {
      setConnecting(true);
      
      // In a real implementation, this would connect to a WebSocket endpoint
      // For demo purposes, we'll simulate a terminal connection
      
      setOutput([
        `Connecting to sandbox: ${sandbox.name}`,
        `Container ID: ${sandbox.id}`,
        `Template: ${sandbox.template}`,
        '',
        'Welcome to VibeKraft Sandbox Terminal',
        'Type "help" for available commands.',
        ''
      ]);
      
      // Simulate connection delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setConnected(true);
      setOutput(prev => [...prev, `vibekraft@${sandbox.id.substring(0, 8)}:~$ `]);
      
    } catch (error) {
      console.error('Failed to connect to terminal:', error);
      setOutput(prev => [...prev, 'Error: Failed to connect to terminal']);
    } finally {
      setConnecting(false);
    }
  };

  // Disconnect from terminal
  const disconnectFromTerminal = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    setConnected(false);
    setConnecting(false);
  };

  // Handle command execution
  const executeCommand = async (command: string) => {
    if (!connected || !sandbox) return;

    // Add command to history
    if (command.trim()) {
      setHistory(prev => [...prev, command]);
      setHistoryIndex(-1);
    }

    // Add command to output
    setOutput(prev => [...prev, `vibekraft@${sandbox.id.substring(0, 8)}:~$ ${command}`]);

    // Simulate command execution
    try {
      const response = await simulateCommand(command);
      setOutput(prev => [...prev, ...response, `vibekraft@${sandbox.id.substring(0, 8)}:~$ `]);
    } catch (error) {
      setOutput(prev => [...prev, `Error: ${error}`, `vibekraft@${sandbox.id.substring(0, 8)}:~$ `]);
    }
  };

  // Simulate command execution (replace with real WebSocket communication)
  const simulateCommand = async (command: string): Promise<string[]> => {
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 500));

    const cmd = command.trim().toLowerCase();
    
    switch (cmd) {
      case 'help':
        return [
          'Available commands:',
          '  ls          - List files and directories',
          '  pwd         - Show current directory',
          '  whoami      - Show current user',
          '  node -v     - Show Node.js version',
          '  python3 -V  - Show Python version',
          '  clear       - Clear terminal',
          '  exit        - Close terminal',
          ''
        ];
      
      case 'ls':
        return ['package.json', 'src/', 'node_modules/', 'README.md', ''];
      
      case 'pwd':
        return ['/workspace', ''];
      
      case 'whoami':
        return ['vibekraft', ''];
      
      case 'node -v':
        return ['v18.20.8', ''];
      
      case 'python3 -v':
        return ['Python 3.11.12', ''];
      
      case 'clear':
        setOutput([`vibekraft@${sandbox?.id.substring(0, 8)}:~$ `]);
        return [];
      
      case 'exit':
        onClose();
        return [];
      
      default:
        if (cmd.startsWith('echo ')) {
          return [command.substring(5), ''];
        }
        return [`bash: ${cmd}: command not found`, ''];
    }
  };

  // Handle input submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim()) {
      executeCommand(input);
      setInput('');
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (history.length > 0) {
        const newIndex = historyIndex === -1 ? history.length - 1 : Math.max(0, historyIndex - 1);
        setHistoryIndex(newIndex);
        setInput(history[newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1;
        if (newIndex >= history.length) {
          setHistoryIndex(-1);
          setInput('');
        } else {
          setHistoryIndex(newIndex);
          setInput(history[newIndex]);
        }
      }
    } else if (e.key === 'Tab') {
      e.preventDefault();
      // Tab completion could be implemented here
    }
  };

  // Copy terminal content
  const copyContent = () => {
    const content = output.join('\n');
    navigator.clipboard.writeText(content);
  };

  if (!isOpen || !sandbox) return null;

  const terminalClasses = `
    ${theme === 'dark' ? 'bg-gray-900 text-green-400' : 'bg-white text-gray-900'}
    font-mono text-sm leading-relaxed
  `;

  return (
    <div className={`fixed inset-0 z-50 ${fullscreen ? '' : 'p-4'}`}>
      {/* Backdrop */}
      {!fullscreen && (
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />
      )}

      {/* Terminal Window */}
      <div className={`
        ${fullscreen 
          ? 'w-full h-full' 
          : 'relative mx-auto mt-8 max-w-4xl h-96 rounded-lg shadow-xl'
        }
        bg-gray-800 overflow-hidden
      `}>
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-2 bg-gray-700 border-b border-gray-600">
          <div className="flex items-center space-x-3">
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div className="flex items-center space-x-2">
              <Terminal className="w-4 h-4 text-gray-300" />
              <span className="text-sm text-gray-300">
                {sandbox.name} - Terminal
              </span>
              {connected && (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-400">Connected</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={copyContent}
              className="p-1 text-gray-400 hover:text-gray-200"
              title="Copy content"
            >
              <Copy className="w-4 h-4" />
            </button>
            
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setFontSize(Math.max(10, fontSize - 1))}
                className="px-2 py-1 text-xs text-gray-400 hover:text-gray-200"
              >
                A-
              </button>
              <span className="text-xs text-gray-400">{fontSize}px</span>
              <button
                onClick={() => setFontSize(Math.min(20, fontSize + 1))}
                className="px-2 py-1 text-xs text-gray-400 hover:text-gray-200"
              >
                A+
              </button>
            </div>

            <button
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
              className="p-1 text-gray-400 hover:text-gray-200"
              title="Toggle theme"
            >
              <Settings className="w-4 h-4" />
            </button>

            {onToggleFullscreen && (
              <button
                onClick={onToggleFullscreen}
                className="p-1 text-gray-400 hover:text-gray-200"
                title={fullscreen ? "Exit fullscreen" : "Fullscreen"}
              >
                {fullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </button>
            )}

            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-200"
              title="Close terminal"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Terminal Content */}
        <div className={`flex flex-col h-full ${terminalClasses}`} style={{ fontSize: `${fontSize}px` }}>
          {/* Output */}
          <div 
            ref={terminalRef}
            className="flex-1 p-4 overflow-y-auto whitespace-pre-wrap"
          >
            {connecting ? (
              <div className="flex items-center space-x-2">
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span>Connecting to terminal...</span>
              </div>
            ) : (
              output.map((line, index) => (
                <div key={index} className="min-h-[1.5em]">
                  {line}
                </div>
              ))
            )}
          </div>

          {/* Input */}
          {connected && (
            <form onSubmit={handleSubmit} className="border-t border-gray-600">
              <div className="flex items-center p-4">
                <span className="text-green-400 mr-2">
                  vibekraft@{sandbox.id.substring(0, 8)}:~$
                </span>
                <input
                  ref={inputRef}
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="flex-1 bg-transparent outline-none text-green-400 placeholder-gray-500"
                  placeholder="Type a command..."
                  autoComplete="off"
                  spellCheck={false}
                />
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
