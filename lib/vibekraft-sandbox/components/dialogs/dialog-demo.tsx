/**
 * VibeKraft Dialog Demo Component
 * 
 * Demonstrates all available dialogs
 */

'use client';

import React from 'react';
import { 
  AlertTriangle, 
  Play, 
  Package, 
  Terminal,
  Trash2,
  Copy,
  Download,
  Settings
} from 'lucide-react';
import { DialogManagerProvider, useDialogManager } from './dialog-manager';
import { SandboxListItem } from '../../hooks/use-sandbox-manager';

// Demo sandbox data
const demoSandbox: SandboxListItem = {
  id: 'demo-sandbox-123',
  name: 'Demo Sandbox',
  template: 'vibekraft/nodejs-minimal:latest',
  projectId: 'demo-project',
  state: 'running',
  health: {
    status: 'healthy',
    checks: [],
    lastChecked: new Date()
  },
  resources: {
    vcpuCount: 2,
    memSizeMib: 1024,
    diskSizeGb: 10
  },
  network: {
    ports: 3,
    isolated: false
  },
  createdAt: new Date(Date.now() - 86400000), // 1 day ago
  lastAccessedAt: new Date(Date.now() - 3600000), // 1 hour ago
  labels: {
    'environment': 'development',
    'project': 'demo'
  },
  annotations: {
    'created-by': 'demo-user'
  }
};

function DialogDemoContent() {
  const {
    showConfirmation,
    showSandboxAction,
    showTemplateSelection,
    showTerminal
  } = useDialogManager();

  // Demo handlers
  const handleSandboxAction = async (action: string, options?: any) => {
    console.log('Sandbox action:', action, options);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    alert(`${action} completed successfully!`);
  };

  const handleTemplateSelect = (templateId: string) => {
    console.log('Template selected:', templateId);
    alert(`Template selected: ${templateId}`);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          VibeKraft Dialog System Demo
        </h1>
        <p className="text-gray-600">
          Explore all available dialogs and their features
        </p>
      </div>

      {/* Confirmation Dialogs */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2" />
          Confirmation Dialogs
        </h2>
        <p className="text-gray-600 mb-4">
          Use confirmation dialogs for destructive or important actions.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          <button
            onClick={() => showConfirmation({
              title: 'Delete Item',
              message: 'Are you sure you want to delete this item? This action cannot be undone.',
              type: 'danger',
              confirmText: 'Delete',
              onConfirm: () => alert('Item deleted!')
            })}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center justify-center"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Danger
          </button>

          <button
            onClick={() => showConfirmation({
              title: 'Warning',
              message: 'This action may have unintended consequences. Do you want to continue?',
              type: 'warning',
              confirmText: 'Continue',
              onConfirm: () => alert('Action continued!')
            })}
            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700"
          >
            Warning
          </button>

          <button
            onClick={() => showConfirmation({
              title: 'Information',
              message: 'This will update your settings. Do you want to proceed?',
              type: 'info',
              confirmText: 'Update',
              onConfirm: () => alert('Settings updated!')
            })}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Info
          </button>

          <button
            onClick={() => showConfirmation({
              title: 'Success',
              message: 'Your changes have been saved. Would you like to continue?',
              type: 'success',
              confirmText: 'Continue',
              onConfirm: () => alert('Continued!')
            })}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Success
          </button>
        </div>
      </div>

      {/* Sandbox Action Dialogs */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Play className="w-5 h-5 mr-2" />
          Sandbox Action Dialogs
        </h2>
        <p className="text-gray-600 mb-4">
          Perform actions on sandboxes with configurable options.
        </p>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
          <button
            onClick={() => showSandboxAction({
              sandbox: demoSandbox,
              action: 'start',
              onConfirm: handleSandboxAction
            })}
            className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
          >
            Start
          </button>

          <button
            onClick={() => showSandboxAction({
              sandbox: demoSandbox,
              action: 'stop',
              onConfirm: handleSandboxAction
            })}
            className="px-3 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 text-sm"
          >
            Stop
          </button>

          <button
            onClick={() => showSandboxAction({
              sandbox: demoSandbox,
              action: 'restart',
              onConfirm: handleSandboxAction
            })}
            className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
          >
            Restart
          </button>

          <button
            onClick={() => showSandboxAction({
              sandbox: demoSandbox,
              action: 'delete',
              onConfirm: handleSandboxAction
            })}
            className="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
          >
            Delete
          </button>

          <button
            onClick={() => showSandboxAction({
              sandbox: demoSandbox,
              action: 'clone',
              onConfirm: handleSandboxAction
            })}
            className="px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm flex items-center justify-center"
          >
            <Copy className="w-3 h-3 mr-1" />
            Clone
          </button>

          <button
            onClick={() => showSandboxAction({
              sandbox: demoSandbox,
              action: 'export',
              onConfirm: handleSandboxAction
            })}
            className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm flex items-center justify-center"
          >
            <Download className="w-3 h-3 mr-1" />
            Export
          </button>
        </div>
      </div>

      {/* Template Selection Dialog */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Package className="w-5 h-5 mr-2" />
          Template Selection Dialog
        </h2>
        <p className="text-gray-600 mb-4">
          Choose from optimized Docker templates with recommendations.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <button
            onClick={() => showTemplateSelection({
              onSelect: handleTemplateSelect
            })}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Select Template
          </button>

          <button
            onClick={() => showTemplateSelection({
              selectedTemplate: 'vibekraft/nodejs-minimal:latest',
              onSelect: handleTemplateSelect
            })}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            With Pre-selection
          </button>

          <button
            onClick={() => showTemplateSelection({
              requirements: {
                languages: ['javascript', 'typescript'],
                maxSize: 'minimal'
              },
              onSelect: handleTemplateSelect
            })}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            With Requirements
          </button>
        </div>
      </div>

      {/* Terminal Dialog */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Terminal className="w-5 h-5 mr-2" />
          Terminal Dialog
        </h2>
        <p className="text-gray-600 mb-4">
          Access sandbox terminals with full functionality.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <button
            onClick={() => showTerminal({
              sandbox: demoSandbox
            })}
            className="px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-900 flex items-center justify-center"
          >
            <Terminal className="w-4 h-4 mr-2" />
            Open Terminal
          </button>

          <button
            onClick={() => showTerminal({
              sandbox: demoSandbox,
              fullscreen: true
            })}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center justify-center"
          >
            <Terminal className="w-4 h-4 mr-2" />
            Fullscreen Terminal
          </button>
        </div>
      </div>

      {/* Features Overview */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2" />
          Dialog Features
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">✅ Features Included</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Confirmation dialogs with multiple types</li>
              <li>• Sandbox action dialogs with options</li>
              <li>• Template selection with recommendations</li>
              <li>• Interactive terminal with command history</li>
              <li>• Centralized dialog management</li>
              <li>• Loading states and error handling</li>
              <li>• Keyboard shortcuts and accessibility</li>
              <li>• Responsive design for all screen sizes</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">🎨 Customization Options</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Custom icons and colors</li>
              <li>• Configurable button text</li>
              <li>• Multiple dialog types and themes</li>
              <li>• Terminal themes and font sizes</li>
              <li>• Fullscreen and windowed modes</li>
              <li>• Action-specific options and forms</li>
              <li>• Template filtering and search</li>
              <li>• Context-aware recommendations</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Usage Example */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Usage Example</h2>
        <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
          <div className="text-gray-400">// Import and use dialogs</div>
          <div className="mt-2">
            <span className="text-blue-400">import</span> {`{ DialogManagerProvider, useDialogManager }`} <span className="text-blue-400">from</span> <span className="text-yellow-400">'@/lib/vibekraft-sandbox'</span>;
          </div>
          <div className="mt-4">
            <span className="text-blue-400">function</span> <span className="text-green-400">MyComponent</span>() {`{`}
          </div>
          <div className="ml-4">
            <span className="text-blue-400">const</span> {`{ showConfirmation, showSandboxAction }`} = <span className="text-green-400">useDialogManager</span>();
          </div>
          <div className="ml-4 mt-2">
            <span className="text-blue-400">const</span> <span className="text-green-400">handleDelete</span> = () => {`{`}
          </div>
          <div className="ml-8">
            <span className="text-green-400">showConfirmation</span>({`{`}
          </div>
          <div className="ml-12">
            title: <span className="text-yellow-400">'Delete Sandbox'</span>,
          </div>
          <div className="ml-12">
            message: <span className="text-yellow-400">'This action cannot be undone.'</span>,
          </div>
          <div className="ml-12">
            type: <span className="text-yellow-400">'danger'</span>,
          </div>
          <div className="ml-12">
            onConfirm: () => <span className="text-green-400">deleteSandbox</span>()
          </div>
          <div className="ml-8">{`});`}</div>
          <div className="ml-4">{`};`}</div>
          <div>{`}`}</div>
        </div>
      </div>
    </div>
  );
}

export function DialogDemo() {
  return (
    <DialogManagerProvider>
      <DialogDemoContent />
    </DialogManagerProvider>
  );
}
