/**
 * VibeKraft Template Selection Dialog Component
 * 
 * Dialog for selecting optimized templates with recommendations
 */

'use client';

import React, { useState } from 'react';
import { 
  Package, 
  X, 
  Search,
  Filter,
  CheckCircle,
  Star,
  Zap,
  Info,
  Download,
  Layers
} from 'lucide-react';
import { useOptimizedTemplates } from '../../hooks/use-optimized-templates';

interface TemplateSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (templateId: string) => void;
  selectedTemplate?: string;
  requirements?: {
    languages?: string[];
    packageManagers?: string[];
    maxSize?: 'minimal' | 'standard' | 'full';
  };
}

export function TemplateSelectionDialog({
  isOpen,
  onClose,
  onSelect,
  selectedTemplate,
  requirements
}: TemplateSelectionDialogProps) {
  const { templates, loading, error, getRecommendations } = useOptimizedTemplates();
  const [searchQuery, setSearchQuery] = useState('');
  const [sizeFilter, setSizeFilter] = useState<'all' | 'minimal' | 'standard' | 'full'>('all');
  const [languageFilter, setLanguageFilter] = useState<string>('all');

  if (!isOpen) return null;

  // Get recommended templates
  const recommendedTemplates = requirements ? getRecommendations(requirements) : [];

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!template.name.toLowerCase().includes(query) &&
          !template.description.toLowerCase().includes(query) &&
          !template.languages.some(lang => lang.toLowerCase().includes(query))) {
        return false;
      }
    }

    // Size filter
    if (sizeFilter !== 'all' && template.size !== sizeFilter) {
      return false;
    }

    // Language filter
    if (languageFilter !== 'all' && !template.languages.includes(languageFilter)) {
      return false;
    }

    return true;
  });

  // Get unique languages for filter
  const allLanguages = Array.from(new Set(templates.flatMap(t => t.languages)));

  // Get template display info
  const getTemplateInfo = (templateId: string) => {
    const templateMap: Record<string, { icon: string; color: string; size: string }> = {
      'vibekraft/nodejs-minimal:latest': { icon: '🟢', color: 'text-green-600', size: '214MB' },
      'vibekraft/python-minimal:latest': { icon: '🐍', color: 'text-blue-600', size: '114MB' },
      'vibekraft/fullstack-dev:latest': { icon: '🚀', color: 'text-purple-600', size: '1.04GB' },
      'vibekraft/rust-dev:latest': { icon: '🦀', color: 'text-orange-600', size: '350MB' },
      'vibekraft/datascience:latest': { icon: '📊', color: 'text-indigo-600', size: '500MB' }
    };

    return templateMap[templateId] || { icon: '📦', color: 'text-gray-600', size: 'Unknown' };
  };

  // Handle template selection
  const handleSelect = (templateId: string) => {
    onSelect(templateId);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Center the modal */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
          &#8203;
        </span>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Package className="w-6 h-6 text-gray-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">
                  Select Template
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 sm:space-x-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Filters */}
              <div className="flex items-center space-x-3">
                <select
                  value={sizeFilter}
                  onChange={(e) => setSizeFilter(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All sizes</option>
                  <option value="minimal">Minimal</option>
                  <option value="standard">Standard</option>
                  <option value="full">Full</option>
                </select>

                <select
                  value={languageFilter}
                  onChange={(e) => setLanguageFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All languages</option>
                  {allLanguages.map(lang => (
                    <option key={lang} value={lang}>{lang}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-4 max-h-96 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading templates...</span>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-600">{error}</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Recommended Templates */}
                {recommendedTemplates.length > 0 && (
                  <div>
                    <div className="flex items-center mb-3">
                      <Star className="w-4 h-4 text-yellow-500 mr-2" />
                      <h4 className="text-sm font-medium text-gray-900">Recommended for you</h4>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
                      {recommendedTemplates.slice(0, 2).map((template) => {
                        const info = getTemplateInfo(template.id);
                        const isSelected = selectedTemplate === template.id;
                        
                        return (
                          <div
                            key={template.id}
                            onClick={() => handleSelect(template.id)}
                            className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all ${
                              isSelected 
                                ? 'border-blue-500 bg-blue-50' 
                                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                            }`}
                          >
                            <div className="absolute top-2 right-2">
                              <div className="flex items-center space-x-1">
                                <Star className="w-3 h-3 text-yellow-500" />
                                {isSelected && <CheckCircle className="w-4 h-4 text-blue-600" />}
                              </div>
                            </div>
                            
                            <div className="flex items-start space-x-3">
                              <span className="text-2xl">{info.icon}</span>
                              <div className="flex-1 min-w-0">
                                <h5 className="font-medium text-gray-900">{template.name}</h5>
                                <p className="text-sm text-gray-500 mt-1">{template.description}</p>
                                <div className="flex items-center justify-between mt-2">
                                  <div className="flex items-center space-x-2">
                                    <span className="text-xs text-gray-500">{info.size}</span>
                                    <Zap className="w-3 h-3 text-green-500" />
                                  </div>
                                  <div className="flex flex-wrap gap-1">
                                    {template.languages.slice(0, 2).map((lang) => (
                                      <span
                                        key={lang}
                                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                                      >
                                        {lang}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* All Templates */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">
                    All Templates ({filteredTemplates.length})
                  </h4>
                  <div className="space-y-2">
                    {filteredTemplates.map((template) => {
                      const info = getTemplateInfo(template.id);
                      const isSelected = selectedTemplate === template.id;
                      const isRecommended = recommendedTemplates.some(r => r.id === template.id);
                      
                      return (
                        <div
                          key={template.id}
                          onClick={() => handleSelect(template.id)}
                          className={`relative p-4 border rounded-lg cursor-pointer transition-all ${
                            isSelected 
                              ? 'border-blue-500 bg-blue-50' 
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <span className="text-xl">{info.icon}</span>
                              <div>
                                <div className="flex items-center space-x-2">
                                  <h5 className="font-medium text-gray-900">{template.name}</h5>
                                  {isRecommended && <Star className="w-3 h-3 text-yellow-500" />}
                                  {template.size === 'minimal' && (
                                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                                      Optimized
                                    </span>
                                  )}
                                </div>
                                <p className="text-sm text-gray-500">{template.description}</p>
                                <div className="flex items-center space-x-4 mt-1">
                                  <span className="text-xs text-gray-500 flex items-center">
                                    <Layers className="w-3 h-3 mr-1" />
                                    {info.size}
                                  </span>
                                  <div className="flex flex-wrap gap-1">
                                    {template.languages.map((lang) => (
                                      <span
                                        key={lang}
                                        className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                                      >
                                        {lang}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              {isSelected && <CheckCircle className="w-5 h-5 text-blue-600" />}
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // Show template details
                                }}
                                className="p-1 text-gray-400 hover:text-gray-600"
                              >
                                <Info className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {filteredTemplates.length === 0 && (
                  <div className="text-center py-8">
                    <Package className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
                    <p className="text-gray-500">Try adjusting your search or filters.</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {selectedTemplate && (
                  <span>Selected: {templates.find(t => t.id === selectedTemplate)?.name}</span>
                )}
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={() => selectedTemplate && handleSelect(selectedTemplate)}
                  disabled={!selectedTemplate}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Select Template
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
