/**
 * VibeKraft Sandbox API Client
 * 
 * Client-side wrapper for sandbox API operations
 */

export interface SandboxConfig {
  id: string;
  name: string;
  userId: string;
  template: string;
  projectId?: string;
  resources: {
    vcpuCount: number;
    memSizeMib: number;
    diskSizeGb: number;
    diskType?: 'ssd' | 'hdd';
  };
  network?: {
    ports?: Array<{
      containerPort: number;
      hostPort?: number;
      protocol?: 'tcp' | 'udp';
      description?: string;
      public?: boolean;
    }>;
    isolated?: boolean;
    allowedHosts?: string[];
    blockedHosts?: string[];
  };
  security?: {
    readOnlyRootfs?: boolean;
    noNewPrivileges?: boolean;
    allowSudo?: boolean;
    allowNetworkAccess?: boolean;
    allowFileSystemAccess?: boolean;
    runAsUser?: string;
  };
  environment?: Record<string, string>;
  labels?: Record<string, string>;
  annotations?: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

export interface SandboxStatus {
  id: string;
  state: 'starting' | 'running' | 'stopping' | 'stopped' | 'error';
  health: {
    status: 'healthy' | 'unhealthy' | 'unknown';
    checks: Array<{
      name: string;
      status: 'passing' | 'failing';
      output?: string;
    }>;
    lastChecked: Date;
  };
  resources?: {
    cpuUsage?: string;
    memoryUsage?: string;
    diskUsage?: string;
    networkUsage?: string;
  };
  network?: {
    ipAddress?: string;
    ports?: Array<{
      containerPort: number;
      hostPort: number;
      protocol: string;
    }>;
    mode?: string;
  };
  security?: {
    readOnlyRootfs?: boolean;
    noNewPrivileges?: boolean;
    allowSudo?: boolean;
    allowNetworkAccess?: boolean;
    allowFileSystemAccess?: boolean;
    runAsUser?: string;
  };
  uptime?: number;
  lastAccessed?: Date;
}

export interface CreateSandboxRequest {
  name: string;
  template: string;
  projectId?: string;
  resources?: {
    vcpuCount?: number;
    memSizeMib?: number;
    diskSizeGb?: number;
    diskType?: 'ssd' | 'hdd';
  };
  network?: {
    ports?: Array<{
      containerPort: number;
      hostPort?: number;
      protocol?: 'tcp' | 'udp';
      description?: string;
      public?: boolean;
    }>;
    isolated?: boolean;
    allowedHosts?: string[];
    blockedHosts?: string[];
  };
  security?: {
    readOnlyRootfs?: boolean;
    noNewPrivileges?: boolean;
    allowSudo?: boolean;
    allowNetworkAccess?: boolean;
    allowFileSystemAccess?: boolean;
  };
  environment?: Record<string, string>;
  labels?: Record<string, string>;
  annotations?: Record<string, string>;
}

export interface ExecuteCommandRequest {
  command: string[];
  workingDir?: string;
  environment?: Record<string, string>;
  timeout?: number;
  user?: string;
}

export interface ExecuteCommandResponse {
  exitCode: number;
  stdout: string;
  stderr: string;
  duration: number;
}

export interface FileOperation {
  path: string;
  content?: string;
  encoding?: 'utf8' | 'base64';
  recursive?: boolean;
}

export interface PackageOperation {
  manager: 'npm' | 'pip' | 'apt' | 'cargo';
  operation: 'install' | 'uninstall' | 'update' | 'list';
  packages?: string[];
  options?: string[];
}

export class SandboxAPIClient {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/vibekraft-sandbox') {
    this.baseUrl = baseUrl;
  }

  // Sandbox CRUD operations
  async createSandbox(config: CreateSandboxRequest): Promise<{ id: string }> {
    const response = await fetch(`${this.baseUrl}/sandboxes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(config),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create sandbox');
    }

    return response.json();
  }

  async listSandboxes(params?: {
    projectId?: string;
    status?: string;
    template?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ sandboxes: SandboxConfig[]; total: number }> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${this.baseUrl}/sandboxes?${searchParams}`);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to list sandboxes');
    }

    return response.json();
  }

  async getSandbox(sandboxId: string): Promise<SandboxConfig> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}`);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to get sandbox');
    }

    return response.json();
  }

  async deleteSandbox(sandboxId: string, options?: { force?: boolean; deleteVolumes?: boolean }): Promise<void> {
    const searchParams = new URLSearchParams();
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}?${searchParams}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete sandbox');
    }
  }

  // Sandbox operations
  async getSandboxStatus(sandboxId: string): Promise<SandboxStatus> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/status`);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to get sandbox status');
    }

    return response.json();
  }

  async startSandbox(sandboxId: string, options?: { waitForHealthy?: boolean; timeout?: number }): Promise<void> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options || {}),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to start sandbox');
    }
  }

  async stopSandbox(sandboxId: string, options?: { force?: boolean; timeout?: number }): Promise<void> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/stop`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options || {}),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to stop sandbox');
    }
  }

  async restartSandbox(sandboxId: string, options?: { preserveData?: boolean; waitForHealthy?: boolean }): Promise<void> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/restart`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options || {}),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to restart sandbox');
    }
  }

  // Command execution
  async executeCommand(sandboxId: string, request: ExecuteCommandRequest): Promise<ExecuteCommandResponse> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/execute`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to execute command');
    }

    return response.json();
  }

  // File operations
  async readFile(sandboxId: string, path: string, encoding: 'utf8' | 'base64' = 'utf8'): Promise<{ content: string; encoding: string }> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/files?path=${encodeURIComponent(path)}&encoding=${encoding}`);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to read file');
    }

    return response.json();
  }

  async writeFile(sandboxId: string, operation: FileOperation): Promise<void> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/files`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(operation),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to write file');
    }
  }

  async deleteFile(sandboxId: string, path: string, recursive: boolean = false): Promise<void> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/files`, {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ path, recursive }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete file');
    }
  }

  async listFiles(sandboxId: string, path: string = '/'): Promise<{ files: Array<{ name: string; type: 'file' | 'directory'; size?: number; modified?: Date }> }> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/files/list?path=${encodeURIComponent(path)}`);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to list files');
    }

    return response.json();
  }

  // Package management
  async managePackages(sandboxId: string, operation: PackageOperation): Promise<{ output: string; exitCode: number }> {
    const response = await fetch(`${this.baseUrl}/sandboxes/${sandboxId}/packages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(operation),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to manage packages');
    }

    return response.json();
  }

  // Bulk operations
  async bulkOperation(operation: string, sandboxIds: string[], options?: any): Promise<{ results: any[]; errors: any[] }> {
    const response = await fetch(`${this.baseUrl}/sandboxes`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ operation, sandboxIds, options }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to perform bulk operation');
    }

    return response.json();
  }

  // Template operations
  async listTemplates(filters?: {
    language?: string;
    packageManager?: string;
    maxSize?: 'minimal' | 'standard' | 'full';
    includeAnalysis?: boolean;
  }): Promise<{ templates: any[]; analysis?: any }> {
    const searchParams = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${this.baseUrl}/templates/optimized?${searchParams}`);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to list templates');
    }

    return response.json();
  }

  async buildTemplates(templateIds?: string[]): Promise<{ results: any[]; errors: any[] }> {
    const response = await fetch(`${this.baseUrl}/templates/optimized/build`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        templateIds: templateIds || [],
        buildAll: !templateIds
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to build templates');
    }

    return response.json();
  }

  async getTemplateAnalysis(): Promise<{ templates: any[]; statistics: any; recommendations: any }> {
    const response = await fetch(`${this.baseUrl}/templates/optimized/analysis`);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to get template analysis');
    }

    return response.json();
  }
}

// Default client instance
export const sandboxAPI = new SandboxAPIClient();
