{"templates": [{"id": "nodejs-express", "name": "Node.js Express", "description": "A Node.js application with Express.js framework for building web APIs and applications", "version": "1.0.0", "metadata": {"author": "VibeKraft", "category": "web-development", "tags": ["nodejs", "express", "javascript", "api", "web"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "downloads": 1250, "rating": 4.8, "reviews": 45, "minVersion": "1.0.0", "license": "MIT", "screenshots": []}, "config": {"baseImage": "vibekraft/sandbox-base:latest", "runtime": {"language": "javascript", "version": "18.x", "framework": "express", "frameworkVersion": "4.18.x", "packageManager": "npm", "startCommand": "npm start", "buildCommand": "npm run build", "testCommand": "npm test", "devServer": {"port": 3000, "host": "0.0.0.0", "hotReload": true}}, "resources": {"min": {"vcpuCount": 1, "memSizeMib": 512, "diskSizeGb": 5}, "recommended": {"vcpuCount": 2, "memSizeMib": 1024, "diskSizeGb": 10}, "max": {"vcpuCount": 4, "memSizeMib": 4096, "diskSizeGb": 50}}, "network": {"ports": [{"port": 3000, "protocol": "tcp", "description": "Express server", "required": true, "public": true}], "internetAccess": true, "inboundAccess": true}, "security": {"privileged": false, "capabilities": [], "readOnlyRootfs": false, "runAsRoot": false, "sudoAccess": false}, "environment": {"required": [{"name": "NODE_ENV", "description": "Node.js environment", "type": "string", "required": false, "defaultValue": "development"}, {"name": "PORT", "description": "Server port", "type": "number", "required": false, "defaultValue": "3000"}], "defaults": {"NODE_ENV": "development", "PORT": "3000"}}}, "files": [{"path": "package.json", "content": "{\n  \"name\": \"vibekraft-express-app\",\n  \"version\": \"1.0.0\",\n  \"description\": \"Express.js application\",\n  \"main\": \"server.js\",\n  \"scripts\": {\n    \"start\": \"node server.js\",\n    \"dev\": \"nodemon server.js\",\n    \"test\": \"jest\"\n  },\n  \"dependencies\": {\n    \"express\": \"^4.18.2\",\n    \"cors\": \"^2.8.5\",\n    \"helmet\": \"^7.0.0\",\n    \"morgan\": \"^1.10.0\",\n    \"dotenv\": \"^16.3.1\"\n  },\n  \"devDependencies\": {\n    \"nodemon\": \"^3.0.1\",\n    \"jest\": \"^29.6.2\",\n    \"supertest\": \"^6.3.3\"\n  }\n}", "type": "text", "template": false}, {"path": "server.js", "content": "const express = require('express');\nconst cors = require('cors');\nconst helmet = require('helmet');\nconst morgan = require('morgan');\nrequire('dotenv').config();\n\nconst app = express();\nconst PORT = process.env.PORT || 3000;\n\n// Middleware\napp.use(helmet());\napp.use(cors());\napp.use(morgan('combined'));\napp.use(express.json());\napp.use(express.urlencoded({ extended: true }));\n\n// Routes\napp.get('/', (req, res) => {\n  res.json({\n    message: 'Welcome to VibeKraft Express App!',\n    timestamp: new Date().toISOString(),\n    environment: process.env.NODE_ENV\n  });\n});\n\napp.get('/health', (req, res) => {\n  res.json({ status: 'healthy', uptime: process.uptime() });\n});\n\n// Error handling\napp.use((err, req, res, next) => {\n  console.error(err.stack);\n  res.status(500).json({ error: 'Something went wrong!' });\n});\n\n// 404 handler\napp.use('*', (req, res) => {\n  res.status(404).json({ error: 'Route not found' });\n});\n\napp.listen(PORT, '0.0.0.0', () => {\n  console.log(`Server running on port ${PORT}`);\n});", "type": "text", "template": false}, {"path": ".env", "content": "NODE_ENV=development\nPORT=3000\n", "type": "text", "template": false}], "setup": {"preSetup": [], "postSetup": [{"command": "npm install", "description": "Install Node.js dependencies", "workingDir": "/workspace", "timeout": 300}]}, "customization": {"parameters": [{"name": "appName", "displayName": "Application Name", "description": "Name of your Express application", "type": "string", "required": false, "defaultValue": "vibekraft-express-app"}, {"name": "includeDatabase", "displayName": "Include Database", "description": "Include database connection setup", "type": "boolean", "required": false, "defaultValue": false}], "features": [{"id": "authentication", "name": "Authentication", "description": "Add JWT-based authentication", "enabled": false, "required": false, "dependencies": [], "conflicts": []}, {"id": "database", "name": "Database Integration", "description": "Add PostgreSQL database integration", "enabled": false, "required": false, "dependencies": [], "conflicts": []}], "addons": [], "themes": []}}, {"id": "python-flask", "name": "Python Flask", "description": "A Python web application using Flask framework for rapid development", "version": "1.0.0", "metadata": {"author": "VibeKraft", "category": "web-development", "tags": ["python", "flask", "web", "api", "backend"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "downloads": 980, "rating": 4.7, "reviews": 32, "minVersion": "1.0.0", "license": "MIT", "screenshots": []}, "config": {"baseImage": "vibekraft/sandbox-base:latest", "runtime": {"language": "python", "version": "3.10", "framework": "flask", "frameworkVersion": "2.3.x", "packageManager": "pip", "startCommand": "python app.py", "testCommand": "pytest", "devServer": {"port": 8000, "host": "0.0.0.0", "hotReload": true}}, "resources": {"min": {"vcpuCount": 1, "memSizeMib": 512, "diskSizeGb": 5}, "recommended": {"vcpuCount": 2, "memSizeMib": 1024, "diskSizeGb": 10}, "max": {"vcpuCount": 4, "memSizeMib": 4096, "diskSizeGb": 50}}, "network": {"ports": [{"port": 8000, "protocol": "tcp", "description": "Flask server", "required": true, "public": true}], "internetAccess": true, "inboundAccess": true}, "security": {"privileged": false, "capabilities": [], "readOnlyRootfs": false, "runAsRoot": false, "sudoAccess": false}, "environment": {"required": [{"name": "FLASK_ENV", "description": "Flask environment", "type": "string", "required": false, "defaultValue": "development"}, {"name": "FLASK_PORT", "description": "Flask port", "type": "number", "required": false, "defaultValue": "8000"}], "defaults": {"FLASK_ENV": "development", "FLASK_PORT": "8000"}}}, "files": [{"path": "requirements.txt", "content": "Flask==2.3.3\nFlask-CORS==4.0.0\nFlask-SQLAlchemy==3.0.5\npython-dotenv==1.0.0\nrequests==2.31.0\npytest==7.4.0\npytest-flask==1.2.0\n", "type": "text", "template": false}, {"path": "app.py", "content": "from flask import Flask, jsonify, request\nfrom flask_cors import CORS\nfrom datetime import datetime\nimport os\nfrom dotenv import load_dotenv\n\nload_dotenv()\n\napp = Flask(__name__)\nCORS(app)\n\n# Configuration\napp.config['DEBUG'] = os.getenv('FLASK_ENV') == 'development'\n\***********('/')\ndef home():\n    return jsonify({\n        'message': 'Welcome to VibeKraft Flask App!',\n        'timestamp': datetime.now().isoformat(),\n        'environment': os.getenv('FLASK_ENV', 'production')\n    })\n\***********('/health')\ndef health():\n    return jsonify({\n        'status': 'healthy',\n        'version': '1.0.0'\n    })\n\***********('/api/data', methods=['GET', 'POST'])\ndef api_data():\n    if request.method == 'POST':\n        data = request.get_json()\n        return jsonify({\n            'message': 'Data received',\n            'data': data,\n            'timestamp': datetime.now().isoformat()\n        })\n    else:\n        return jsonify({\n            'message': 'API endpoint',\n            'methods': ['GET', 'POST']\n        })\n\******************(404)\ndef not_found(error):\n    return jsonify({'error': 'Route not found'}), 404\n\******************(500)\ndef internal_error(error):\n    return jsonify({'error': 'Internal server error'}), 500\n\nif __name__ == '__main__':\n    port = int(os.getenv('FLASK_PORT', 8000))\n    app.run(host='0.0.0.0', port=port, debug=app.config['DEBUG'])", "type": "text", "template": false}, {"path": ".env", "content": "FLASK_ENV=development\nFLASK_PORT=8000\n", "type": "text", "template": false}], "setup": {"preSetup": [], "postSetup": [{"command": "pip install -r requirements.txt", "description": "Install Python dependencies", "workingDir": "/workspace", "timeout": 300}]}, "customization": {"parameters": [{"name": "appName", "displayName": "Application Name", "description": "Name of your Flask application", "type": "string", "required": false, "defaultValue": "vibekraft-flask-app"}], "features": [{"id": "database", "name": "Database Integration", "description": "Add SQLAlchemy database integration", "enabled": false, "required": false, "dependencies": [], "conflicts": []}, {"id": "authentication", "name": "Authentication", "description": "Add Flask-Login authentication", "enabled": false, "required": false, "dependencies": [], "conflicts": []}], "addons": [], "themes": []}}, {"id": "react-vite", "name": "React with Vite", "description": "A modern React application with Vite for fast development and building", "version": "1.0.0", "metadata": {"author": "VibeKraft", "category": "web-development", "tags": ["react", "vite", "javascript", "frontend", "spa"], "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "downloads": 1450, "rating": 4.9, "reviews": 67, "minVersion": "1.0.0", "license": "MIT", "screenshots": []}, "config": {"baseImage": "vibekraft/sandbox-base:latest", "runtime": {"language": "javascript", "version": "18.x", "framework": "react", "frameworkVersion": "18.x", "packageManager": "npm", "startCommand": "npm run dev", "buildCommand": "npm run build", "testCommand": "npm test", "devServer": {"port": 3000, "host": "0.0.0.0", "hotReload": true}}, "resources": {"min": {"vcpuCount": 1, "memSizeMib": 1024, "diskSizeGb": 5}, "recommended": {"vcpuCount": 2, "memSizeMib": 2048, "diskSizeGb": 15}, "max": {"vcpuCount": 4, "memSizeMib": 8192, "diskSizeGb": 50}}, "network": {"ports": [{"port": 3000, "protocol": "tcp", "description": "Vite dev server", "required": true, "public": true}], "internetAccess": true, "inboundAccess": true}, "security": {"privileged": false, "capabilities": [], "readOnlyRootfs": false, "runAsRoot": false, "sudoAccess": false}, "environment": {"required": [], "defaults": {"NODE_ENV": "development"}}}, "files": [{"path": "package.json", "content": "{\n  \"name\": \"vibekraft-react-app\",\n  \"private\": true,\n  \"version\": \"1.0.0\",\n  \"type\": \"module\",\n  \"scripts\": {\n    \"dev\": \"vite --host 0.0.0.0\",\n    \"build\": \"vite build\",\n    \"lint\": \"eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0\",\n    \"preview\": \"vite preview\",\n    \"test\": \"vitest\"\n  },\n  \"dependencies\": {\n    \"react\": \"^18.2.0\",\n    \"react-dom\": \"^18.2.0\",\n    \"react-router-dom\": \"^6.15.0\"\n  },\n  \"devDependencies\": {\n    \"@types/react\": \"^18.2.15\",\n    \"@types/react-dom\": \"^18.2.7\",\n    \"@vitejs/plugin-react\": \"^4.0.3\",\n    \"eslint\": \"^8.45.0\",\n    \"eslint-plugin-react\": \"^7.32.2\",\n    \"eslint-plugin-react-hooks\": \"^4.6.0\",\n    \"eslint-plugin-react-refresh\": \"^0.4.3\",\n    \"vite\": \"^4.4.5\",\n    \"vitest\": \"^0.34.0\"\n  }\n}", "type": "text", "template": false}, {"path": "vite.config.js", "content": "import { defineConfig } from 'vite'\nimport react from '@vitejs/plugin-react'\n\nexport default defineConfig({\n  plugins: [react()],\n  server: {\n    host: '0.0.0.0',\n    port: 3000\n  }\n})", "type": "text", "template": false}, {"path": "index.html", "content": "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <link rel=\"icon\" type=\"image/svg+xml\" href=\"/vite.svg\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>VibeKraft React App</title>\n  </head>\n  <body>\n    <div id=\"root\"></div>\n    <script type=\"module\" src=\"/src/main.jsx\"></script>\n  </body>\n</html>", "type": "text", "template": false}, {"path": "src/main.jsx", "content": "import React from 'react'\nimport ReactDOM from 'react-dom/client'\nimport App from './App.jsx'\nimport './index.css'\n\nReactDOM.createRoot(document.getElementById('root')).render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>,\n)", "type": "text", "template": false}, {"path": "src/App.jsx", "content": "import { useState } from 'react'\nimport './App.css'\n\nfunction App() {\n  const [count, setCount] = useState(0)\n\n  return (\n    <div className=\"App\">\n      <header className=\"App-header\">\n        <h1>Welcome to VibeKraft React App!</h1>\n        <div className=\"card\">\n          <button onClick={() => setCount((count) => count + 1)}>\n            count is {count}\n          </button>\n          <p>\n            Edit <code>src/App.jsx</code> and save to test HMR\n          </p>\n        </div>\n        <p className=\"read-the-docs\">\n          Click on the Vite and React logos to learn more\n        </p>\n      </header>\n    </div>\n  )\n}\n\nexport default App", "type": "text", "template": false}, {"path": "src/App.css", "content": ".App {\n  text-align: center;\n}\n\n.App-header {\n  background-color: #282c34;\n  padding: 20px;\n  color: white;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n}\n\n.card {\n  padding: 2em;\n}\n\n.read-the-docs {\n  color: #888;\n}", "type": "text", "template": false}, {"path": "src/index.css", "content": ":root {\n  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;\n  line-height: 1.5;\n  font-weight: 400;\n\n  color-scheme: light dark;\n  color: rgba(255, 255, 255, 0.87);\n  background-color: #242424;\n\n  font-synthesis: none;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-text-size-adjust: 100%;\n}\n\nbody {\n  margin: 0;\n  display: flex;\n  place-items: center;\n  min-width: 320px;\n  min-height: 100vh;\n}\n\n#root {\n  max-width: 1280px;\n  margin: 0 auto;\n  padding: 2rem;\n  text-align: center;\n}", "type": "text", "template": false}], "setup": {"preSetup": [], "postSetup": [{"command": "npm install", "description": "Install React dependencies", "workingDir": "/workspace", "timeout": 300}]}, "customization": {"parameters": [{"name": "appName", "displayName": "Application Name", "description": "Name of your React application", "type": "string", "required": false, "defaultValue": "vibekraft-react-app"}, {"name": "useTypeScript", "displayName": "Use TypeScript", "description": "Use TypeScript instead of JavaScript", "type": "boolean", "required": false, "defaultValue": false}], "features": [{"id": "router", "name": "React Router", "description": "Add React Router for navigation", "enabled": true, "required": false, "dependencies": [], "conflicts": []}, {"id": "state-management", "name": "State Management", "description": "Add Redux Toolkit for state management", "enabled": false, "required": false, "dependencies": [], "conflicts": []}], "addons": [], "themes": []}}]}