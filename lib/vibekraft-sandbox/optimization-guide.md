# VibeKraft Sandbox Image Optimization Guide

## Overview

The VibeKraft sandbox infrastructure now includes advanced Docker image optimization capabilities that can reduce image sizes by 60-80% while maintaining full functionality. This guide explains how to use these optimizations effectively.

## Optimization Strategies

### 1. Multi-Stage Builds
- **Benefit**: Reduces final image size by excluding build dependencies
- **Size Reduction**: 40-60%
- **Use Case**: When you need build tools but not in the final runtime

### 2. Alpine Linux Base Images
- **Benefit**: Minimal base OS with package manager
- **Size Reduction**: 70-80% compared to Ubuntu
- **Trade-off**: Some packages may not be available

### 3. Layer Optimization
- **Benefit**: Combines multiple RUN commands into single layers
- **Size Reduction**: 10-20%
- **Implementation**: Automatic in our optimization system

### 4. Cache Cleanup
- **Benefit**: Removes package manager caches and temporary files
- **Size Reduction**: 15-30%
- **Implementation**: Automatic cleanup after package installations

### 5. Documentation Removal
- **Benefit**: Removes man pages, docs, and locale files
- **Size Reduction**: 5-15%
- **Trade-off**: No offline documentation available

## Pre-Built Optimized Templates

### Minimal Templates (< 200MB)

#### Node.js Minimal
- **Base**: `node:18-alpine`
- **Optimized**: `vibekraft/nodejs-minimal:latest`
- **Size**: ~150MB (vs 400MB standard)
- **Includes**: Node.js 18, npm, basic tools
- **Best For**: Simple Node.js applications

#### Python Minimal
- **Base**: `python:3.11-alpine`
- **Optimized**: `vibekraft/python-minimal:latest`
- **Size**: ~120MB (vs 350MB standard)
- **Includes**: Python 3.11, pip, basic tools
- **Best For**: Simple Python scripts and applications

### Standard Templates (200-800MB)

#### Full-Stack Development
- **Base**: `ubuntu:22.04`
- **Optimized**: `vibekraft/fullstack-dev:latest`
- **Size**: ~600MB (vs 1.2GB standard)
- **Includes**: Node.js, Python, Git, build tools
- **Best For**: Multi-language development

#### Rust Development
- **Base**: `rust:1.70-alpine`
- **Optimized**: `vibekraft/rust-dev:latest`
- **Size**: ~350MB (vs 800MB standard)
- **Includes**: Rust, Cargo, build tools
- **Best For**: Rust development and compilation

#### Data Science
- **Base**: `python:3.11-slim`
- **Optimized**: `vibekraft/datascience:latest`
- **Size**: ~500MB (vs 1GB standard)
- **Includes**: Python, Jupyter, pandas, numpy, matplotlib
- **Best For**: Data analysis and machine learning

## Usage Examples

### 1. Enable Optimization in SandboxManager

```typescript
const sandboxManager = new SandboxManager({
  enableImageOptimization: true,
  autoOptimizeImages: true,
  maxConcurrentSandboxes: 50
});
```

### 2. Create Optimized Sandbox

```typescript
const sandboxConfig = {
  name: 'My Optimized Sandbox',
  template: 'node:18', // Will be automatically optimized to vibekraft/nodejs-minimal
  resources: {
    memSizeMib: 512, // Small memory = minimal template selection
    vcpuCount: 1
  }
};

const sandboxId = await sandboxManager.createSandbox(sandboxConfig);
```

### 3. Build Custom Optimized Template

```typescript
import { ImageOptimizer } from '@/lib/vibekraft-sandbox/core/image-optimizer';

const optimizer = new ImageOptimizer();

const config = {
  baseImage: 'ubuntu:22.04',
  targetImage: 'my-optimized-app:latest',
  optimizations: {
    multiStage: true,
    minifyLayers: true,
    removeCache: true,
    compressLayers: true,
    removeDocumentation: true,
    optimizePackageManager: true
  },
  packageManagers: { npm: true, pip: true },
  customOptimizations: [
    'RUN apt-get install -y --no-install-recommends my-package',
    'RUN npm install -g my-global-package'
  ]
};

const optimizedImage = await optimizer.buildOptimizedImage(config);
```

## Size Comparison

| Template Type | Standard Size | Optimized Size | Reduction |
|---------------|---------------|----------------|-----------|
| Node.js Basic | 400MB | 150MB | 62% |
| Python Basic | 350MB | 120MB | 66% |
| Full-Stack | 1.2GB | 600MB | 50% |
| Rust Dev | 800MB | 350MB | 56% |
| Data Science | 1GB | 500MB | 50% |

## Best Practices

### 1. Choose the Right Base Image
- Use Alpine for minimal size
- Use slim variants for balance of size and compatibility
- Use full images only when necessary

### 2. Optimize Package Installation
```dockerfile
# Good: Single layer with cleanup
RUN apt-get update && apt-get install -y --no-install-recommends \
    package1 package2 package3 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Bad: Multiple layers without cleanup
RUN apt-get update
RUN apt-get install -y package1
RUN apt-get install -y package2
```

### 3. Use .dockerignore
```dockerignore
node_modules
.git
.env
*.log
coverage/
.nyc_output
```

### 4. Minimize Layers
- Combine related commands
- Use multi-stage builds for complex applications
- Clean up in the same layer where you create mess

### 5. Remove Unnecessary Files
```dockerfile
RUN apt-get update && apt-get install -y package && \
    rm -rf /var/lib/apt/lists/* \
           /tmp/* \
           /var/tmp/* \
           /usr/share/doc/* \
           /usr/share/man/*
```

## Monitoring and Analysis

### 1. Check Template Sizes
```typescript
const analysis = await sandboxManager.getTemplateSizeComparison();
console.log(analysis);
```

### 2. Monitor Build Progress
```typescript
const optimizer = new ImageOptimizer();

optimizer.on('buildStart', ({ targetImage }) => {
  console.log(`Building ${targetImage}...`);
});

optimizer.on('buildProgress', ({ message }) => {
  console.log(message);
});

optimizer.on('buildComplete', ({ targetImage }) => {
  console.log(`Completed ${targetImage}`);
});
```

### 3. Analyze Image Layers
```typescript
const analysis = await optimizer.analyzeImage('my-image:latest');
console.log({
  size: `${Math.round(analysis.size / 1024 / 1024)}MB`,
  layers: analysis.layers,
  architecture: analysis.architecture
});
```

## Performance Impact

### Build Time
- **Multi-stage builds**: +20-30% build time
- **Layer optimization**: -10-15% build time
- **Cache cleanup**: +5-10% build time

### Runtime Performance
- **Smaller images**: Faster container startup (2-5x)
- **Reduced I/O**: Less disk space and network transfer
- **Memory efficiency**: Lower memory footprint

### Network Transfer
- **Pull time**: 60-80% reduction in download time
- **Registry storage**: Significant storage savings
- **Bandwidth**: Reduced network usage

## Troubleshooting

### Common Issues

1. **Missing packages in optimized images**
   - Solution: Add required packages to customOptimizations

2. **Build failures with Alpine**
   - Solution: Install build dependencies or use slim base

3. **Large final image despite optimization**
   - Solution: Check for unnecessary files or use multi-stage build

### Debug Commands
```bash
# Check image layers
docker history my-image:latest

# Analyze image size
docker images my-image:latest

# Inspect image contents
docker run --rm -it my-image:latest sh
```

## API Integration

### List Optimized Templates
```bash
GET /api/vibekraft-sandbox/templates/optimized
```

### Build Templates
```bash
POST /api/vibekraft-sandbox/templates/optimized/build
{
  "buildAll": true
}
```

### Get Size Analysis
```bash
GET /api/vibekraft-sandbox/templates/optimized/analysis
```

## Conclusion

Image optimization in VibeKraft can significantly reduce resource usage while maintaining full functionality. The automated optimization system makes it easy to benefit from these improvements without manual Dockerfile optimization.

For best results:
1. Enable optimization in SandboxManager
2. Use appropriate base images for your use case
3. Monitor image sizes and build times
4. Customize optimizations based on your specific needs
