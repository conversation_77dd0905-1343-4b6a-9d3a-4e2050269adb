#!/usr/bin/env node

/**
 * VibeKraft Sandbox Image Builder
 * 
 * Builds all optimized Docker images for the VibeKraft sandbox infrastructure
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 VibeKraft Sandbox Image Builder\n');

// Check if Docker is available
function checkDockerAvailable() {
  try {
    execSync('docker --version', { stdio: 'pipe' });
    console.log('✅ Docker is available');
    return true;
  } catch (error) {
    console.error('❌ Docker is not available. Please install Docker first.');
    return false;
  }
}

// Create optimized Dockerfiles
const dockerfiles = {
  'nodejs-minimal': `
# VibeKraft Node.js Minimal - Optimized for size and performance
FROM node:18-alpine as builder

# Install build dependencies
RUN apk add --no-cache \\
    build-base \\
    curl \\
    wget \\
    git \\
    ca-certificates

# Configure npm for optimization
RUN npm config set cache /tmp/.npm \\
    && npm config set fund false \\
    && npm config set audit false \\
    && npm install -g typescript @types/node

# Stage 2: Runtime environment (minimal)
FROM node:18-alpine as runtime

# Create non-root user
RUN addgroup -g 1000 vibekraft \\
    && adduser -D -s /bin/sh -u 1000 -G vibekraft vibekraft

# Install only runtime dependencies
RUN apk add --no-cache ca-certificates \\
    && rm -rf /var/cache/apk/* \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/*

# Copy TypeScript from builder
COPY --from=builder /usr/local/lib/node_modules/typescript /usr/local/lib/node_modules/typescript
COPY --from=builder /usr/local/lib/node_modules/@types /usr/local/lib/node_modules/@types
COPY --from=builder /usr/local/bin/tsc /usr/local/bin/tsc
COPY --from=builder /usr/local/bin/tsserver /usr/local/bin/tsserver

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD node --version || exit 1

CMD ["/bin/sh"]
`,

  'python-minimal': `
# VibeKraft Python Minimal - Optimized for size and performance
FROM python:3.11-alpine as builder

# Install build dependencies
RUN apk add --no-cache \\
    build-base \\
    libffi-dev \\
    openssl-dev \\
    curl \\
    wget \\
    git

# Install common Python packages
RUN pip install --no-cache-dir \\
    virtualenv \\
    pipenv \\
    requests \\
    click

# Stage 2: Runtime environment (minimal)
FROM python:3.11-alpine as runtime

# Create non-root user
RUN addgroup -g 1000 vibekraft \\
    && adduser -D -s /bin/sh -u 1000 -G vibekraft vibekraft

# Install only runtime dependencies
RUN apk add --no-cache \\
    ca-certificates \\
    libffi \\
    openssl \\
    && rm -rf /var/cache/apk/* \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/*

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin/virtualenv /usr/local/bin/virtualenv
COPY --from=builder /usr/local/bin/pipenv /usr/local/bin/pipenv

# Configure pip for optimization
RUN pip config set global.cache-dir /tmp/pip-cache \\
    && pip config set global.no-cache-dir true

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD python --version || exit 1

CMD ["/bin/sh"]
`,

  'fullstack-dev': `
# VibeKraft Full-Stack Development - Optimized multi-language environment
FROM ubuntu:22.04 as builder

# Prevent interactive prompts
ENV DEBIAN_FRONTEND=noninteractive

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \\
    curl \\
    wget \\
    ca-certificates \\
    gnupg \\
    lsb-release \\
    build-essential

# Install Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \\
    && apt-get install -y nodejs

# Install Python and pip
RUN apt-get install -y python3 python3-pip python3-venv

# Install development tools
RUN npm install -g typescript @types/node eslint prettier \\
    && pip3 install --no-cache-dir black flake8 mypy virtualenv

# Stage 2: Runtime environment
FROM ubuntu:22.04 as runtime

ENV DEBIAN_FRONTEND=noninteractive

# Create non-root user
RUN groupadd -r vibekraft && useradd -r -g vibekraft -u 1000 vibekraft

# Configure package manager for minimal installs
RUN echo 'APT::Install-Recommends "0";' >> /etc/apt/apt.conf.d/01norecommend \\
    && echo 'APT::Install-Suggests "0";' >> /etc/apt/apt.conf.d/01norecommend

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \\
    ca-certificates \\
    curl \\
    wget \\
    git \\
    vim \\
    nano \\
    nodejs \\
    npm \\
    python3 \\
    python3-pip \\
    python3-venv \\
    && apt-get clean \\
    && rm -rf /var/lib/apt/lists/* \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/* \\
    && rm -rf /usr/share/doc/* \\
    && rm -rf /usr/share/man/* \\
    && find /var/log -type f -delete

# Copy development tools from builder
COPY --from=builder /usr/local/lib/node_modules /usr/local/lib/node_modules
COPY --from=builder /usr/local/bin/tsc /usr/local/bin/tsc
COPY --from=builder /usr/local/bin/eslint /usr/local/bin/eslint
COPY --from=builder /usr/local/bin/prettier /usr/local/bin/prettier
COPY --from=builder /usr/local/lib/python3.10/dist-packages /usr/local/lib/python3.10/dist-packages

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Set environment variables
ENV NODE_ENV=development
ENV PYTHONPATH=/workspace
ENV PATH="/workspace/node_modules/.bin:$PATH"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD node --version && python3 --version || exit 1

CMD ["/bin/bash"]
`,

  'rust-dev': `
# VibeKraft Rust Development - Optimized for compilation speed
FROM rust:1.70-alpine as builder

# Install build dependencies
RUN apk add --no-cache \\
    build-base \\
    curl \\
    wget \\
    git \\
    ca-certificates

# Configure Rust for optimization
RUN rustup component remove rust-docs \\
    && rustup component add clippy rustfmt

# Stage 2: Runtime environment
FROM rust:1.70-alpine as runtime

# Create non-root user
RUN addgroup -g 1000 vibekraft \\
    && adduser -D -s /bin/sh -u 1000 -G vibekraft vibekraft

# Install runtime dependencies
RUN apk add --no-cache \\
    ca-certificates \\
    build-base \\
    && rm -rf /var/cache/apk/* \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/*

# Copy Rust tools from builder
COPY --from=builder /usr/local/cargo /usr/local/cargo
COPY --from=builder /usr/local/rustup /usr/local/rustup

# Clean up Rust installation
RUN rm -rf /usr/local/cargo/registry/cache \\
    && rm -rf /usr/local/cargo/git/db

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Set Rust environment
ENV PATH="/usr/local/cargo/bin:$PATH"
ENV CARGO_HOME="/usr/local/cargo"
ENV RUSTUP_HOME="/usr/local/rustup"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD rustc --version || exit 1

CMD ["/bin/sh"]
`,

  'datascience': `
# VibeKraft Data Science - Optimized Python environment with ML libraries
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \\
    build-essential \\
    curl \\
    wget \\
    git

# Install data science packages
RUN pip install --no-cache-dir \\
    jupyter \\
    pandas \\
    numpy \\
    matplotlib \\
    seaborn \\
    scikit-learn \\
    plotly \\
    requests \\
    beautifulsoup4

# Stage 2: Runtime environment
FROM python:3.11-slim as runtime

# Create non-root user
RUN groupadd -r vibekraft && useradd -r -g vibekraft -u 1000 vibekraft

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \\
    ca-certificates \\
    curl \\
    && apt-get clean \\
    && rm -rf /var/lib/apt/lists/* \\
    && rm -rf /tmp/* \\
    && rm -rf /var/tmp/* \\
    && find /usr/local -name "*.pyc" -delete

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin/jupyter /usr/local/bin/jupyter

# Configure Jupyter
RUN jupyter notebook --generate-config

# Create workspace
RUN mkdir -p /workspace && chown vibekraft:vibekraft /workspace
WORKDIR /workspace

# Switch to non-root user
USER vibekraft

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD python --version && jupyter --version || exit 1

CMD ["/bin/bash"]
`
};

// Build a single image
async function buildImage(name, dockerfile) {
  console.log(`🔨 Building ${name}...`);
  
  const buildDir = path.join(__dirname, 'build', name);
  
  // Create build directory
  if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir, { recursive: true });
  }
  
  // Write Dockerfile
  fs.writeFileSync(path.join(buildDir, 'Dockerfile'), dockerfile);
  
  const imageName = `vibekraft/${name}:latest`;
  
  return new Promise((resolve, reject) => {
    console.log(`   Building image: ${imageName}`);
    
    const buildProcess = spawn('docker', [
      'build',
      '-t', imageName,
      '--no-cache',
      '.'
    ], {
      cwd: buildDir,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    let errorOutput = '';
    
    buildProcess.stdout.on('data', (data) => {
      output += data.toString();
      // Show progress for important steps
      const line = data.toString().trim();
      if (line.includes('Step ') || line.includes('Successfully built') || line.includes('Successfully tagged')) {
        console.log(`   ${line}`);
      }
    });
    
    buildProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log(`   ✅ Successfully built ${imageName}`);
        
        // Get image size
        try {
          const sizeOutput = execSync(`docker images ${imageName} --format "table {{.Size}}"`, { encoding: 'utf8' });
          const size = sizeOutput.split('\n')[1]?.trim();
          if (size) {
            console.log(`   📦 Image size: ${size}`);
          }
        } catch (error) {
          // Size check failed, but build succeeded
        }
        
        resolve({ name, imageName, success: true });
      } else {
        console.error(`   ❌ Failed to build ${imageName}`);
        console.error(`   Error: ${errorOutput}`);
        reject({ name, imageName, success: false, error: errorOutput });
      }
    });
  });
}

// Main build function
async function buildAllImages() {
  if (!checkDockerAvailable()) {
    process.exit(1);
  }
  
  console.log(`\n🏗️  Building ${Object.keys(dockerfiles).length} optimized images...\n`);
  
  const results = [];
  const startTime = Date.now();
  
  for (const [name, dockerfile] of Object.entries(dockerfiles)) {
    try {
      const result = await buildImage(name, dockerfile);
      results.push(result);
    } catch (error) {
      results.push(error);
    }
    console.log(); // Add spacing between builds
  }
  
  const endTime = Date.now();
  const totalTime = Math.round((endTime - startTime) / 1000);
  
  // Summary
  console.log('📊 Build Summary');
  console.log('================\n');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful builds: ${successful.length}`);
  successful.forEach(result => {
    console.log(`   - ${result.imageName}`);
  });
  
  if (failed.length > 0) {
    console.log(`\n❌ Failed builds: ${failed.length}`);
    failed.forEach(result => {
      console.log(`   - ${result.imageName}`);
    });
  }
  
  console.log(`\n⏱️  Total build time: ${totalTime} seconds`);
  
  // Show all built images
  if (successful.length > 0) {
    console.log('\n📦 Built Images:');
    try {
      const imagesOutput = execSync('docker images vibekraft/* --format "table {{.Repository}}:{{.Tag}}\\t{{.Size}}\\t{{.CreatedAt}}"', { encoding: 'utf8' });
      console.log(imagesOutput);
    } catch (error) {
      console.log('   (Unable to list images)');
    }
  }
  
  // Next steps
  console.log('\n🚀 Next Steps:');
  console.log('==============');
  console.log('1. Test the images:');
  console.log('   docker run -it vibekraft/nodejs-minimal:latest');
  console.log('   docker run -it vibekraft/python-minimal:latest');
  console.log('   docker run -it vibekraft/fullstack-dev:latest');
  console.log('');
  console.log('2. Push to registry (optional):');
  console.log('   docker push vibekraft/nodejs-minimal:latest');
  console.log('   docker push vibekraft/python-minimal:latest');
  console.log('   # ... etc');
  console.log('');
  console.log('3. Use in VibeKraft sandbox:');
  console.log('   The images are now available for the sandbox manager!');
  
  return results;
}

// Run if called directly
if (require.main === module) {
  buildAllImages()
    .then((results) => {
      const successful = results.filter(r => r.success).length;
      const total = results.length;
      
      if (successful === total) {
        console.log(`\n🎉 All ${total} images built successfully!`);
        process.exit(0);
      } else {
        console.log(`\n⚠️  ${successful}/${total} images built successfully.`);
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Build process failed:', error);
      process.exit(1);
    });
}

module.exports = { buildAllImages, buildImage, dockerfiles };
