/**
 * Shadcn CLI Integration for Hybrid Execution Environment
 */

import { HybridExecutor } from '../hybrid-executor'
import { CommandResult, HybridExecutionError } from '../types'

export interface ShadcnInitOptions {
  style?: 'default' | 'new-york'
  baseColor?: 'slate' | 'gray' | 'zinc' | 'neutral' | 'stone'
  cssVariables?: boolean
  typescript?: boolean
  tailwindConfig?: string
  tailwindCss?: string
  components?: string
  utils?: string
  force?: boolean
}

export interface ShadcnAddOptions {
  components: string[]
  overwrite?: boolean
  cwd?: string
  all?: boolean
  yes?: boolean
}

export interface ShadcnComponent {
  name: string
  description: string
  dependencies: string[]
  files: string[]
  registryDependencies?: string[]
}

export class ShadcnCLI {
  private executor: HybridExecutor

  constructor(executor: HybridExecutor) {
    this.executor = executor
  }

  /**
   * Initialize Shadcn/UI in the project
   */
  async init(options: ShadcnInitOptions = {}): Promise<CommandResult> {
    const args = ['shadcn@latest', 'init']

    // Add options as CLI arguments
    if (options.style) {
      args.push('--style', options.style)
    }
    if (options.baseColor) {
      args.push('--base-color', options.baseColor)
    }
    if (options.cssVariables !== undefined) {
      args.push(options.cssVariables ? '--css-variables' : '--no-css-variables')
    }
    if (options.typescript !== undefined) {
      args.push(options.typescript ? '--typescript' : '--no-typescript')
    }
    if (options.tailwindConfig) {
      args.push('--tailwind-config', options.tailwindConfig)
    }
    if (options.tailwindCss) {
      args.push('--tailwind-css', options.tailwindCss)
    }
    if (options.components) {
      args.push('--components', options.components)
    }
    if (options.utils) {
      args.push('--utils', options.utils)
    }
    if (options.force) {
      args.push('--force')
    }

    // Always use yes flag for non-interactive execution
    args.push('--yes')

    try {
      const result = await this.executor.executeCommand({
        command: `npx ${args.join(' ')}`,
        environment: 'container',
        sync: true,
        timeout: 300000 // 5 minutes
      })

      if (!result.success) {
        throw new HybridExecutionError(
          `Shadcn init failed: ${result.stderr}`,
          'SHADCN_INIT_FAILED',
          { exitCode: result.exitCode, stderr: result.stderr }
        )
      }

      return result
    } catch (error) {
      if (error instanceof HybridExecutionError) {
        throw error
      }
      throw new HybridExecutionError(
        `Failed to initialize Shadcn: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'SHADCN_INIT_ERROR',
        error
      )
    }
  }

  /**
   * Add Shadcn components to the project
   */
  async addComponents(components: string[], options: Omit<ShadcnAddOptions, 'components'> = {}): Promise<CommandResult> {
    if (components.length === 0) {
      throw new HybridExecutionError(
        'No components specified',
        'INVALID_ARGUMENTS'
      )
    }

    const args = ['shadcn@latest', 'add']

    // Add components
    if (options.all) {
      args.push('--all')
    } else {
      args.push(...components)
    }

    // Add options
    if (options.overwrite) {
      args.push('--overwrite')
    }
    if (options.cwd) {
      args.push('--cwd', options.cwd)
    }
    if (options.yes !== false) {
      args.push('--yes')
    }

    try {
      const result = await this.executor.executeCommand({
        command: `npx ${args.join(' ')}`,
        environment: 'container',
        sync: true,
        timeout: 600000 // 10 minutes for multiple components
      })

      if (!result.success) {
        throw new HybridExecutionError(
          `Shadcn add failed: ${result.stderr}`,
          'SHADCN_ADD_FAILED',
          { exitCode: result.exitCode, stderr: result.stderr, components }
        )
      }

      return result
    } catch (error) {
      if (error instanceof HybridExecutionError) {
        throw error
      }
      throw new HybridExecutionError(
        `Failed to add Shadcn components: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'SHADCN_ADD_ERROR',
        error
      )
    }
  }

  /**
   * List available Shadcn components
   */
  async listComponents(): Promise<ShadcnComponent[]> {
    try {
      const result = await this.executor.executeCommand({
        command: 'npx shadcn@latest list',
        environment: 'container',
        sync: false,
        timeout: 60000 // 1 minute
      })

      if (!result.success) {
        throw new HybridExecutionError(
          `Failed to list components: ${result.stderr}`,
          'SHADCN_LIST_FAILED',
          { exitCode: result.exitCode, stderr: result.stderr }
        )
      }

      // Parse the output to extract component information
      return this.parseComponentList(result.stdout)
    } catch (error) {
      if (error instanceof HybridExecutionError) {
        throw error
      }
      throw new HybridExecutionError(
        `Failed to list Shadcn components: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'SHADCN_LIST_ERROR',
        error
      )
    }
  }

  /**
   * Check if Shadcn is initialized in the project
   */
  async isInitialized(): Promise<boolean> {
    try {
      // Check for components.json file
      const result = await this.executor.executeCommand({
        command: 'test -f components.json',
        environment: 'container',
        sync: false,
        timeout: 5000
      })

      return result.success
    } catch {
      return false
    }
  }

  /**
   * Get Shadcn configuration
   */
  async getConfig(): Promise<any> {
    try {
      const result = await this.executor.executeCommand({
        command: 'cat components.json',
        environment: 'container',
        sync: false,
        timeout: 5000
      })

      if (!result.success) {
        throw new HybridExecutionError(
          'components.json not found or not readable',
          'CONFIG_NOT_FOUND'
        )
      }

      return JSON.parse(result.stdout)
    } catch (error) {
      if (error instanceof HybridExecutionError) {
        throw error
      }
      throw new HybridExecutionError(
        `Failed to read Shadcn config: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'CONFIG_READ_ERROR',
        error
      )
    }
  }

  /**
   * Update Shadcn components
   */
  async updateComponents(components?: string[]): Promise<CommandResult> {
    const args = ['shadcn@latest', 'update']

    if (components && components.length > 0) {
      args.push(...components)
    }

    args.push('--yes')

    try {
      const result = await this.executor.executeCommand({
        command: `npx ${args.join(' ')}`,
        environment: 'container',
        sync: true,
        timeout: 600000 // 10 minutes
      })

      if (!result.success) {
        throw new HybridExecutionError(
          `Shadcn update failed: ${result.stderr}`,
          'SHADCN_UPDATE_FAILED',
          { exitCode: result.exitCode, stderr: result.stderr }
        )
      }

      return result
    } catch (error) {
      if (error instanceof HybridExecutionError) {
        throw error
      }
      throw new HybridExecutionError(
        `Failed to update Shadcn components: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'SHADCN_UPDATE_ERROR',
        error
      )
    }
  }

  /**
   * Remove Shadcn components
   */
  async removeComponents(components: string[]): Promise<CommandResult> {
    if (components.length === 0) {
      throw new HybridExecutionError(
        'No components specified for removal',
        'INVALID_ARGUMENTS'
      )
    }

    // Shadcn doesn't have a remove command, so we'll remove files manually
    const componentPaths = components.map(comp => `components/ui/${comp}.tsx`)
    const removeCommand = `rm -f ${componentPaths.join(' ')}`

    try {
      const result = await this.executor.executeCommand({
        command: removeCommand,
        environment: 'container',
        sync: true,
        timeout: 30000
      })

      return result
    } catch (error) {
      throw new HybridExecutionError(
        `Failed to remove Shadcn components: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'SHADCN_REMOVE_ERROR',
        error
      )
    }
  }

  /**
   * Parse component list output
   */
  private parseComponentList(output: string): ShadcnComponent[] {
    const components: ShadcnComponent[] = []
    const lines = output.split('\n')

    for (const line of lines) {
      const trimmed = line.trim()
      if (trimmed && !trimmed.startsWith('Available components:') && !trimmed.startsWith('-')) {
        // Simple parsing - in real implementation, this would be more sophisticated
        const match = trimmed.match(/^(\w+)\s*-?\s*(.*)$/)
        if (match) {
          components.push({
            name: match[1],
            description: match[2] || '',
            dependencies: [],
            files: [`components/ui/${match[1]}.tsx`]
          })
        }
      }
    }

    return components
  }
}

/**
 * Utility functions for Shadcn integration
 */
export class ShadcnUtils {
  /**
   * Validate component name
   */
  static isValidComponentName(name: string): boolean {
    return /^[a-z][a-z0-9-]*$/.test(name)
  }

  /**
   * Get component file path
   */
  static getComponentPath(name: string): string {
    return `components/ui/${name}.tsx`
  }

  /**
   * Check if component exists
   */
  static async componentExists(executor: HybridExecutor, name: string): Promise<boolean> {
    try {
      const result = await executor.executeCommand({
        command: `test -f ${ShadcnUtils.getComponentPath(name)}`,
        environment: 'container',
        sync: false,
        timeout: 5000
      })
      return result.success
    } catch {
      return false
    }
  }

  /**
   * Get installed components
   */
  static async getInstalledComponents(executor: HybridExecutor): Promise<string[]> {
    try {
      const result = await executor.executeCommand({
        command: 'ls components/ui/*.tsx 2>/dev/null | xargs -n1 basename -s .tsx || true',
        environment: 'container',
        sync: false,
        timeout: 10000
      })

      if (result.success && result.stdout.trim()) {
        return result.stdout.trim().split('\n').filter(Boolean)
      }
      return []
    } catch {
      return []
    }
  }
}
