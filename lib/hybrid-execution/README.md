# Hybrid Execution Environment

This library implements the hybrid Nodebox-native execution architecture for running CLI commands that require system-level access while maintaining seamless integration with Nodebox's browser-based runtime.

## Quick Start

```typescript
import { HybridExecutor } from '@/lib/hybrid-execution'

// Initialize hybrid executor
const executor = new HybridExecutor({
  projectId: 'my-project',
  containerConfig: {
    image: 'nodebox-cli:latest',
    timeout: 1800000, // 30 minutes
    resources: {
      memory: '512m',
      cpu: '0.5'
    }
  }
})

// Execute Shadcn CLI command
const result = await executor.executeCommand({
  command: 'npx shadcn@latest add button card',
  sync: true,
  environment: 'container'
})

console.log(result.filesChanged) // ['components/ui/button.tsx', 'components/ui/card.tsx']
```

## Architecture Overview

```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   Nodebox       │◄──────────────►│  Container      │
│   Browser       │                 │  Environment    │
│   Runtime       │                 │                 │
└─────────────────┘                 └─────────────────┘
         │                                   │
         │            File Sync              │
         └───────────────────────────────────┘
```

## Core Components

### 1. Container Manager
Handles Docker container lifecycle and orchestration.

### 2. File Sync Engine
Bidirectional file synchronization with conflict resolution.

### 3. WebSocket Bridge
Real-time communication between environments.

### 4. Command Router
Intelligent routing of commands to appropriate environment.

## Usage Examples

### Shadcn CLI Integration
```typescript
import { ShadcnCLI } from '@/lib/hybrid-execution/shadcn'

const shadcn = new ShadcnCLI(executor)

// Initialize Shadcn in project
await shadcn.init({
  style: 'default',
  baseColor: 'slate',
  cssVariables: true
})

// Add components
await shadcn.addComponents(['button', 'card', 'input'])

// List available components
const components = await shadcn.listComponents()
```

### Custom CLI Tools
```typescript
// Execute any CLI command
const result = await executor.executeCommand({
  command: 'npm install @radix-ui/react-dialog',
  environment: 'container',
  sync: true,
  timeout: 300000
})

// Execute with custom environment variables
const result = await executor.executeCommand({
  command: 'npm run build',
  environment: 'container',
  env: {
    NODE_ENV: 'production',
    NEXT_PUBLIC_API_URL: 'https://api.example.com'
  }
})
```

### File Operations
```typescript
// Sync specific files
await executor.syncFiles({
  files: ['package.json', 'components.json'],
  direction: 'to-container'
})

// Watch for file changes
executor.watchFiles(['**/*.tsx', '**/*.ts'], (changes) => {
  console.log('Files changed:', changes)
})

// Handle conflicts
executor.onConflict((conflict) => {
  // Custom conflict resolution logic
  return 'use-container' // or 'use-nodebox' or 'merge'
})
```

## Configuration

### Environment Variables
```bash
# Container configuration
HYBRID_CONTAINER_IMAGE=nodebox-cli:latest
HYBRID_CONTAINER_TIMEOUT=1800000
HYBRID_CONTAINER_MEMORY=512m
HYBRID_CONTAINER_CPU=0.5

# Sync configuration
HYBRID_SYNC_DEBOUNCE=500
HYBRID_SYNC_BATCH_SIZE=10
HYBRID_SYNC_COMPRESSION=true

# WebSocket configuration
HYBRID_WS_PORT=3001
HYBRID_WS_HEARTBEAT=30000
HYBRID_WS_RECONNECT_DELAY=5000
```

### Project Configuration
```json
{
  "hybrid": {
    "enabled": true,
    "containerImage": "nodebox-cli:latest",
    "syncConfig": {
      "include": ["**/*.{ts,tsx,js,jsx,json,md,css}"],
      "exclude": ["node_modules/**", ".next/**", "dist/**"],
      "priority": ["package.json", "components.json", "*.config.*"]
    },
    "tools": {
      "shadcn": {
        "enabled": true,
        "version": "latest"
      },
      "tailwind": {
        "enabled": true,
        "version": "latest"
      }
    }
  }
}
```

## API Reference

### HybridExecutor Class

#### Constructor
```typescript
new HybridExecutor(config: HybridExecutorConfig)
```

#### Methods
- `executeCommand(options: CommandOptions): Promise<CommandResult>`
- `syncFiles(options: SyncOptions): Promise<SyncResult>`
- `watchFiles(patterns: string[], callback: FileChangeCallback): void`
- `getContainerStatus(): Promise<ContainerStatus>`
- `cleanup(): Promise<void>`

### Types

```typescript
interface CommandOptions {
  command: string
  environment: 'nodebox' | 'container' | 'auto'
  sync?: boolean
  timeout?: number
  env?: Record<string, string>
  cwd?: string
}

interface CommandResult {
  success: boolean
  exitCode: number
  stdout: string
  stderr: string
  filesChanged: string[]
  duration: number
}

interface SyncOptions {
  files?: string[]
  direction: 'to-container' | 'to-nodebox' | 'bidirectional'
  force?: boolean
}
```

## Error Handling

```typescript
try {
  const result = await executor.executeCommand({
    command: 'npx shadcn@latest add button',
    environment: 'container'
  })
} catch (error) {
  if (error instanceof ContainerTimeoutError) {
    console.error('Container operation timed out')
  } else if (error instanceof SyncError) {
    console.error('File synchronization failed')
  } else if (error instanceof CommandExecutionError) {
    console.error('Command execution failed:', error.stderr)
  }
}
```

## Performance Considerations

### Optimization Strategies
1. **Container Pooling**: Reuse containers when possible
2. **Incremental Sync**: Only sync changed files
3. **Compression**: Use gzip for file transfers
4. **Debouncing**: Batch file changes to reduce sync frequency
5. **Caching**: Cache file hashes to detect changes efficiently

### Monitoring
```typescript
// Monitor performance metrics
executor.on('metrics', (metrics) => {
  console.log('Sync latency:', metrics.syncLatency)
  console.log('Command execution time:', metrics.commandDuration)
  console.log('Container memory usage:', metrics.memoryUsage)
})
```

## Security

### Container Security
- Containers run with non-root user
- Limited resource allocation
- No external network access except package registries
- Read-only base filesystem

### File Sync Security
- Path validation to prevent directory traversal
- Content scanning for malicious files
- Size limits to prevent large file attacks
- Rate limiting to prevent sync flooding

## Troubleshooting

### Common Issues

1. **Container fails to start**
   - Check Docker daemon is running
   - Verify container image exists
   - Check resource limits

2. **File sync is slow**
   - Increase batch size
   - Enable compression
   - Check network latency

3. **Commands timeout**
   - Increase timeout value
   - Check container resources
   - Monitor container logs

### Debug Mode
```typescript
const executor = new HybridExecutor({
  debug: true,
  logLevel: 'verbose'
})
```

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md) for development setup and contribution guidelines.
