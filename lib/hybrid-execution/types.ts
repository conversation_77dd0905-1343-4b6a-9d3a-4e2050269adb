/**
 * Type definitions for the Hybrid Execution Environment
 */

export interface HybridExecutorConfig {
  projectId: string
  containerConfig: ContainerConfig
  syncConfig?: SyncConfig
  wsConfig?: WebSocketConfig
  debug?: boolean
  logLevel?: 'error' | 'warn' | 'info' | 'debug' | 'verbose'
}

export interface ContainerConfig {
  image: string
  timeout: number
  resources: {
    memory: string
    cpu: string
  }
  env?: Record<string, string>
  volumes?: VolumeMount[]
  network?: string
}

export interface VolumeMount {
  source: string
  target: string
  readonly?: boolean
}

export interface SyncConfig {
  include: string[]
  exclude: string[]
  priority: string[]
  debounceMs: number
  batchSize: number
  compression: boolean
  conflictResolution: ConflictResolution
}

export type ConflictResolution = 'nodebox' | 'container' | 'merge' | 'prompt'

export interface WebSocketConfig {
  port: number
  heartbeatInterval: number
  reconnectDelay: number
  maxReconnectAttempts: number
}

export interface CommandOptions {
  command: string
  environment: ExecutionEnvironment
  sync?: boolean
  timeout?: number
  env?: Record<string, string>
  cwd?: string
  shell?: string
}

export type ExecutionEnvironment = 'nodebox' | 'container' | 'auto'

export interface CommandResult {
  success: boolean
  exitCode: number
  stdout: string
  stderr: string
  filesChanged: string[]
  duration: number
  environment: ExecutionEnvironment
  metadata?: Record<string, any>
}

export interface SyncOptions {
  files?: string[]
  patterns?: string[]
  direction: SyncDirection
  force?: boolean
  dryRun?: boolean
}

export type SyncDirection = 'to-container' | 'to-nodebox' | 'bidirectional'

export interface SyncResult {
  success: boolean
  filesTransferred: number
  bytesTransferred: number
  duration: number
  conflicts: FileConflict[]
  errors: SyncError[]
}

export interface FileConflict {
  path: string
  nodeboxHash: string
  containerHash: string
  resolution: ConflictResolution
  resolvedContent?: string
}

export interface SyncError {
  path: string
  error: string
  code: string
}

export interface FileChangeEvent {
  type: FileChangeType
  path: string
  content?: string
  metadata: FileMetadata
  source: 'nodebox' | 'container'
  timestamp: Date
}

export type FileChangeType = 'create' | 'update' | 'delete' | 'rename'

export interface FileMetadata {
  size: number
  modified: Date
  checksum: string
  permissions?: string
  encoding?: string
}

export interface ContainerStatus {
  id: string
  status: ContainerState
  created: Date
  started?: Date
  uptime?: number
  resources: ResourceUsage
  health: HealthStatus
}

export type ContainerState = 'creating' | 'running' | 'stopping' | 'stopped' | 'error'

export interface ResourceUsage {
  memory: {
    used: number
    limit: number
    percentage: number
  }
  cpu: {
    percentage: number
  }
  disk: {
    used: number
    available: number
  }
}

export type HealthStatus = 'healthy' | 'unhealthy' | 'starting' | 'unknown'

export interface PerformanceMetrics {
  syncLatency: number
  commandDuration: number
  memoryUsage: number
  cpuUsage: number
  networkLatency: number
  filesPerSecond: number
}

export interface FileWatchOptions {
  patterns: string[]
  ignored?: string[]
  persistent?: boolean
  ignoreInitial?: boolean
  followSymlinks?: boolean
  depth?: number
}

export type FileChangeCallback = (events: FileChangeEvent[]) => void | Promise<void>

export interface ToolDefinition {
  name: string
  mode: ToolMode
  requirements: ToolRequirements
  fallback?: ExecutionEnvironment
  timeout?: number
  retries?: number
}

export type ToolMode = 'nodebox-only' | 'container-only' | 'hybrid' | 'auto'

export interface ToolRequirements {
  cli?: string[]
  packages?: string[]
  systemAccess?: boolean
  networkAccess?: boolean
  fileSystemAccess?: boolean
}

export interface HybridToolResult {
  success: boolean
  result: any
  environment: ExecutionEnvironment
  duration: number
  filesChanged: string[]
  warnings: string[]
  errors: string[]
}

// Error types
export class HybridExecutionError extends Error {
  constructor(message: string, public code: string, public details?: any) {
    super(message)
    this.name = 'HybridExecutionError'
  }
}

export class ContainerError extends HybridExecutionError {
  constructor(message: string, public containerId?: string, details?: any) {
    super(message, 'CONTAINER_ERROR', details)
    this.name = 'ContainerError'
  }
}

export class ContainerTimeoutError extends ContainerError {
  constructor(timeout: number, containerId?: string) {
    super(`Container operation timed out after ${timeout}ms`, containerId)
    this.name = 'ContainerTimeoutError'
    this.code = 'CONTAINER_TIMEOUT'
  }
}

export class SyncError extends HybridExecutionError {
  constructor(message: string, public path?: string, details?: any) {
    super(message, 'SYNC_ERROR', details)
    this.name = 'SyncError'
  }
}

export class CommandExecutionError extends HybridExecutionError {
  constructor(
    message: string,
    public exitCode: number,
    public stdout: string,
    public stderr: string
  ) {
    super(message, 'COMMAND_EXECUTION_ERROR', { exitCode, stdout, stderr })
    this.name = 'CommandExecutionError'
  }
}

export class WebSocketError extends HybridExecutionError {
  constructor(message: string, details?: any) {
    super(message, 'WEBSOCKET_ERROR', details)
    this.name = 'WebSocketError'
  }
}

// Event types
export interface HybridExecutorEvents {
  'container-status': (status: ContainerStatus) => void
  'file-change': (event: FileChangeEvent) => void
  'sync-progress': (progress: SyncProgress) => void
  'command-start': (command: string, environment: ExecutionEnvironment) => void
  'command-complete': (result: CommandResult) => void
  'error': (error: HybridExecutionError) => void
  'metrics': (metrics: PerformanceMetrics) => void
  'conflict': (conflict: FileConflict) => ConflictResolution | Promise<ConflictResolution>
}

export interface SyncProgress {
  phase: 'scanning' | 'transferring' | 'applying' | 'complete'
  filesProcessed: number
  totalFiles: number
  bytesTransferred: number
  totalBytes: number
  currentFile?: string
  percentage: number
}

// Configuration schemas
export interface ProjectHybridConfig {
  enabled: boolean
  containerImage?: string
  syncConfig?: Partial<SyncConfig>
  tools?: Record<string, ToolConfig>
  security?: SecurityConfig
}

export interface ToolConfig {
  enabled: boolean
  version?: string
  config?: Record<string, any>
}

export interface SecurityConfig {
  allowedCommands?: string[]
  blockedCommands?: string[]
  maxFileSize?: number
  maxSyncFiles?: number
  sandboxNetwork?: boolean
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
