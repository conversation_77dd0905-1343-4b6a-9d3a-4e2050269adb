/**
 * HybridExecutor - Main class for managing hybrid Nodebox-native execution
 */

import { EventEmitter } from 'events'
import { ContainerManager } from './container-manager'
import { FileSyncEngine } from './file-sync-engine'
import { WebSocketBridge } from './websocket-bridge'
import { CommandRouter } from './command-router'
import { PerformanceMonitor } from './performance-monitor'
import {
  HybridExecutorConfig,
  CommandOptions,
  CommandResult,
  SyncOptions,
  SyncResult,
  ContainerStatus,
  FileChangeCallback,
  FileWatchOptions,
  HybridExecutorEvents,
  PerformanceMetrics,
  HybridExecutionError,
  ExecutionEnvironment
} from './types'

export class HybridExecutor extends EventEmitter {
  private containerManager: ContainerManager
  private fileSyncEngine: FileSyncEngine
  private webSocketBridge: WebSocketBridge
  private commandRouter: CommandRouter
  private performanceMonitor: PerformanceMonitor
  private config: HybridExecutorConfig
  private isInitialized = false
  private cleanupHandlers: (() => Promise<void>)[] = []

  constructor(config: HybridExecutorConfig) {
    super()
    this.config = config
    this.setupComponents()
    this.setupEventHandlers()
  }

  private setupComponents(): void {
    // Initialize core components
    this.containerManager = new ContainerManager(this.config.containerConfig, {
      debug: this.config.debug,
      logLevel: this.config.logLevel
    })

    this.fileSyncEngine = new FileSyncEngine(this.config.syncConfig, {
      debug: this.config.debug,
      logLevel: this.config.logLevel
    })

    this.webSocketBridge = new WebSocketBridge(this.config.wsConfig, {
      debug: this.config.debug,
      logLevel: this.config.logLevel
    })

    this.commandRouter = new CommandRouter({
      containerManager: this.containerManager,
      fileSyncEngine: this.fileSyncEngine,
      debug: this.config.debug
    })

    this.performanceMonitor = new PerformanceMonitor({
      enabled: this.config.debug || false,
      metricsInterval: 5000
    })
  }

  private setupEventHandlers(): void {
    // Container events
    this.containerManager.on('status-change', (status: ContainerStatus) => {
      this.emit('container-status', status)
    })

    this.containerManager.on('error', (error: Error) => {
      this.emit('error', new HybridExecutionError(
        `Container error: ${error.message}`,
        'CONTAINER_ERROR',
        error
      ))
    })

    // File sync events
    this.fileSyncEngine.on('file-change', (event) => {
      this.emit('file-change', event)
    })

    this.fileSyncEngine.on('sync-progress', (progress) => {
      this.emit('sync-progress', progress)
    })

    this.fileSyncEngine.on('conflict', (conflict) => {
      this.emit('conflict', conflict)
    })

    this.fileSyncEngine.on('error', (error: Error) => {
      this.emit('error', new HybridExecutionError(
        `Sync error: ${error.message}`,
        'SYNC_ERROR',
        error
      ))
    })

    // WebSocket events
    this.webSocketBridge.on('connection-status', (connected: boolean) => {
      if (this.config.debug) {
        console.log(`WebSocket ${connected ? 'connected' : 'disconnected'}`)
      }
    })

    this.webSocketBridge.on('error', (error: Error) => {
      this.emit('error', new HybridExecutionError(
        `WebSocket error: ${error.message}`,
        'WEBSOCKET_ERROR',
        error
      ))
    })

    // Performance monitoring
    this.performanceMonitor.on('metrics', (metrics: PerformanceMetrics) => {
      this.emit('metrics', metrics)
    })

    // Command execution events
    this.commandRouter.on('command-start', (command: string, environment: ExecutionEnvironment) => {
      this.emit('command-start', command, environment)
    })

    this.commandRouter.on('command-complete', (result: CommandResult) => {
      this.emit('command-complete', result)
    })
  }

  /**
   * Initialize the hybrid executor
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      // Start performance monitoring
      await this.performanceMonitor.start()

      // Initialize WebSocket bridge
      await this.webSocketBridge.initialize()

      // Initialize file sync engine
      await this.fileSyncEngine.initialize()

      // Initialize container manager
      await this.containerManager.initialize()

      // Setup cleanup handlers
      this.cleanupHandlers.push(
        () => this.performanceMonitor.stop(),
        () => this.webSocketBridge.cleanup(),
        () => this.fileSyncEngine.cleanup(),
        () => this.containerManager.cleanup()
      )

      this.isInitialized = true

      if (this.config.debug) {
        console.log('HybridExecutor initialized successfully')
      }
    } catch (error) {
      throw new HybridExecutionError(
        `Failed to initialize HybridExecutor: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'INITIALIZATION_ERROR',
        error
      )
    }
  }

  /**
   * Execute a command in the appropriate environment
   */
  async executeCommand(options: CommandOptions): Promise<CommandResult> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const startTime = Date.now()

    try {
      // Route command to appropriate environment
      const result = await this.commandRouter.executeCommand(options)

      // Record performance metrics
      this.performanceMonitor.recordCommandExecution(
        options.command,
        result.environment,
        Date.now() - startTime,
        result.success
      )

      return result
    } catch (error) {
      this.performanceMonitor.recordCommandExecution(
        options.command,
        options.environment,
        Date.now() - startTime,
        false
      )
      throw error
    }
  }

  /**
   * Synchronize files between environments
   */
  async syncFiles(options: SyncOptions): Promise<SyncResult> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const startTime = Date.now()

    try {
      const result = await this.fileSyncEngine.syncFiles(options)

      // Record performance metrics
      this.performanceMonitor.recordSyncOperation(
        result.filesTransferred,
        result.bytesTransferred,
        Date.now() - startTime
      )

      return result
    } catch (error) {
      this.performanceMonitor.recordSyncOperation(0, 0, Date.now() - startTime)
      throw error
    }
  }

  /**
   * Watch for file changes
   */
  watchFiles(patterns: string[], callback: FileChangeCallback, options?: FileWatchOptions): void {
    if (!this.isInitialized) {
      throw new HybridExecutionError(
        'HybridExecutor must be initialized before watching files',
        'NOT_INITIALIZED'
      )
    }

    this.fileSyncEngine.watchFiles(patterns, callback, options)
  }

  /**
   * Get container status
   */
  async getContainerStatus(): Promise<ContainerStatus> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    return this.containerManager.getStatus()
  }

  /**
   * Get performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return this.performanceMonitor.getMetrics()
  }

  /**
   * Check if container is ready
   */
  async isContainerReady(): Promise<boolean> {
    try {
      const status = await this.getContainerStatus()
      return status.status === 'running' && status.health === 'healthy'
    } catch {
      return false
    }
  }

  /**
   * Restart container
   */
  async restartContainer(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    await this.containerManager.restart()
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    try {
      // Execute cleanup handlers in reverse order
      for (const handler of this.cleanupHandlers.reverse()) {
        await handler()
      }

      this.cleanupHandlers = []
      this.isInitialized = false

      if (this.config.debug) {
        console.log('HybridExecutor cleaned up successfully')
      }
    } catch (error) {
      throw new HybridExecutionError(
        `Failed to cleanup HybridExecutor: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'CLEANUP_ERROR',
        error
      )
    }
  }

  /**
   * Get configuration
   */
  getConfig(): Readonly<HybridExecutorConfig> {
    return Object.freeze({ ...this.config })
  }

  /**
   * Update configuration (requires restart)
   */
  async updateConfig(newConfig: Partial<HybridExecutorConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig }

    if (this.isInitialized) {
      await this.cleanup()
      await this.initialize()
    }
  }
}

// Type-safe event emitter interface
export interface HybridExecutor {
  on<K extends keyof HybridExecutorEvents>(
    event: K,
    listener: HybridExecutorEvents[K]
  ): this
  
  emit<K extends keyof HybridExecutorEvents>(
    event: K,
    ...args: Parameters<HybridExecutorEvents[K]>
  ): boolean
}
