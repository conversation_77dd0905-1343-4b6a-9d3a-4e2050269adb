/**
 * Nodebox AI Integration Service
 *
 * Ensures seamless integration between nodebox-ai route, workspace layout,
 * agentic chat interface, and related components, stores, and services.
 */

import React from 'react';
import { useNodeboxStore } from '@/lib/stores/nodebox-store';
import { useFileStore } from '@/lib/stores/file-store';
import { useOpenFilesStore } from '@/lib/stores/open-files-store';
import { useWorkspaceStore } from '@/lib/stores/workspace-store';
import { NodeboxRuntimeService } from './nodebox-runtime-service';

export interface IntegrationContext {
  projectId: string;
  instanceId?: string;
  activeInstance?: any;
  projectContext?: {
    activeInstance?: string;
    template?: string;
    status?: string;
  };
}

export interface ToolExecutionContext extends IntegrationContext {
  toolName: string;
  args: Record<string, any>;
  messageId?: string;
  stepIndex?: number;
}

export interface IntegrationState {
  isConnected: boolean;
  lastSync: Date | null;
  syncErrors: string[];
  activeConnections: Set<string>;
}

export class NodeboxAIIntegrationService {
  private static instance: NodeboxAIIntegrationService;
  private state: IntegrationState = {
    isConnected: false,
    lastSync: null,
    syncErrors: [],
    activeConnections: new Set()
  };

  private syncCallbacks = new Set<(context: IntegrationContext) => void>();
  private errorCallbacks = new Set<(error: Error) => void>();

  static getInstance(): NodeboxAIIntegrationService {
    if (!NodeboxAIIntegrationService.instance) {
      NodeboxAIIntegrationService.instance = new NodeboxAIIntegrationService();
    }
    return NodeboxAIIntegrationService.instance;
  }

  /**
   * Initialize integration between all components
   */
  async initialize(projectId: string): Promise<void> {
    try {
      console.log('[Integration] Initializing Nodebox AI integration for project:', projectId);

      // Initialize Nodebox store
      const nodeboxStore = useNodeboxStore.getState();
      if (!nodeboxStore.manager && !nodeboxStore.client) {
        await nodeboxStore.initializeRuntime(false); // Use manager mode
      }

      // Sync file stores
      await this.syncFileStores(projectId);

      // Mark as connected
      this.state.isConnected = true;
      this.state.lastSync = new Date();
      this.state.activeConnections.add(projectId);

      console.log('[Integration] Successfully initialized for project:', projectId);
    } catch (error) {
      console.error('[Integration] Failed to initialize:', error);
      this.handleError(error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Get enhanced context for AI interactions
   */
  getEnhancedContext(projectId: string): IntegrationContext {
    const nodeboxStore = useNodeboxStore.getState();
    const activeInstance = nodeboxStore.getActiveInstance();
    const workspaceStore = useWorkspaceStore.getState();

    return {
      projectId,
      instanceId: activeInstance?.id,
      activeInstance,
      projectContext: {
        activeInstance: activeInstance?.config.name,
        template: activeInstance?.config.template,
        status: activeInstance?.status
      }
    };
  }

  /**
   * Enhance tool execution with integration context
   */
  enhanceToolExecution(context: ToolExecutionContext): Record<string, any> {
    const enhancedArgs = {
      ...context.args,
      projectId: context.projectId,
      instanceId: context.instanceId,
      projectContext: context.projectContext,
      integrationMetadata: {
        toolName: context.toolName,
        messageId: context.messageId,
        stepIndex: context.stepIndex,
        timestamp: new Date().toISOString(),
        syncState: this.state
      }
    };

    console.log(`[Integration] Enhanced tool execution for ${context.toolName}:`, {
      projectId: context.projectId,
      instanceId: context.instanceId,
      hasProjectContext: !!context.projectContext
    });

    return enhancedArgs;
  }

  /**
   * Sync file stores with Nodebox instance
   */
  async syncFileStores(projectId: string): Promise<void> {
    try {
      const nodeboxStore = useNodeboxStore.getState();
      const fileStore = useFileStore.getState();
      const openFilesStore = useOpenFilesStore.getState();

      const activeInstance = nodeboxStore.getActiveInstance();
      if (!activeInstance) {
        console.log('[Integration] No active instance for file sync');
        return;
      }

      // Load file system from Nodebox
      await nodeboxStore.loadFileSystem(activeInstance.id);

      // Get files from Nodebox store
      const nodeboxFiles = nodeboxStore.fileSystem[activeInstance.id] || [];

      // Clear and sync file store
      fileStore.clearFiles();

      nodeboxFiles.forEach(file => {
        if (file.type === 'file' && file.content) {
          fileStore.addFile(file.path, file.content);
        } else if (file.type === 'directory') {
          fileStore.addDirectory(file.path);
        }
      });

      // Sync open files if any exist
      const openFiles = openFilesStore.openFiles;
      for (const openFile of openFiles) {
        const nodeboxFile = nodeboxFiles.find(f => f.path === openFile.path);
        if (nodeboxFile && nodeboxFile.content !== openFile.content) {
          openFilesStore.updateFileContent(openFile.id, nodeboxFile.content || '');
        }
      }

      console.log(`[Integration] Synced ${nodeboxFiles.length} files for project ${projectId}`);
    } catch (error) {
      console.error('[Integration] File sync error:', error);
      this.handleError(error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Handle file changes from AI tools
   */
  async handleFileChange(projectId: string, filePath: string, content: string): Promise<void> {
    try {
      const nodeboxStore = useNodeboxStore.getState();
      const fileStore = useFileStore.getState();
      const openFilesStore = useOpenFilesStore.getState();

      const activeInstance = nodeboxStore.getActiveInstance();
      if (!activeInstance) {
        throw new Error('No active Nodebox instance for file change');
      }

      // Update in Nodebox store
      await nodeboxStore.writeFile(activeInstance.id, filePath, content);

      // Update in file store
      const existingFile = fileStore.getFileByPath(filePath);
      if (existingFile) {
        fileStore.updateFileContent(filePath, content);
      } else {
        fileStore.addFile(filePath, content);
      }

      // Update open files if the file is open
      const openFile = openFilesStore.openFiles.find(f => f.path === filePath);
      if (openFile) {
        openFilesStore.updateFileContent(openFile.id, content);
      }

      // Trigger sync callbacks
      this.notifySync({ projectId, instanceId: activeInstance.id });

      console.log(`[Integration] File change handled: ${filePath}`);
    } catch (error) {
      console.error('[Integration] File change error:', error);
      this.handleError(error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Handle project creation from AI tools
   */
  async handleProjectCreation(projectId: string, template: string, name: string): Promise<any> {
    try {
      const nodeboxStore = useNodeboxStore.getState();
      const workspaceStore = useWorkspaceStore.getState();

      // Create instance using Nodebox store
      const instance = await nodeboxStore.createFromTemplate(template as any, name, projectId);
      
      if (!instance) {
        throw new Error('Failed to create project instance');
      }

      // Load file system
      await nodeboxStore.loadFileSystem(instance.id);

      // Sync file stores
      await this.syncFileStores(projectId);

      // Update workspace if needed
      if (workspaceStore.setActiveTabId) {
        workspaceStore.setActiveTabId('nodebox-workspace');
      }

      // Trigger sync callbacks
      this.notifySync({ 
        projectId, 
        instanceId: instance.id,
        activeInstance: instance,
        projectContext: {
          activeInstance: instance.config.name,
          template: instance.config.template,
          status: instance.status
        }
      });

      console.log(`[Integration] Project created: ${name} (${template})`);
      return instance;
    } catch (error) {
      console.error('[Integration] Project creation error:', error);
      this.handleError(error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Register sync callback
   */
  onSync(callback: (context: IntegrationContext) => void): () => void {
    this.syncCallbacks.add(callback);
    return () => this.syncCallbacks.delete(callback);
  }

  /**
   * Register error callback
   */
  onError(callback: (error: Error) => void): () => void {
    this.errorCallbacks.add(callback);
    return () => this.errorCallbacks.delete(callback);
  }

  /**
   * Get integration state
   */
  getState(): IntegrationState {
    return { ...this.state };
  }

  /**
   * Check if project is connected
   */
  isProjectConnected(projectId: string): boolean {
    return this.state.isConnected && this.state.activeConnections.has(projectId);
  }

  /**
   * Disconnect project
   */
  disconnect(projectId: string): void {
    this.state.activeConnections.delete(projectId);
    if (this.state.activeConnections.size === 0) {
      this.state.isConnected = false;
    }
    console.log(`[Integration] Disconnected project: ${projectId}`);
  }

  /**
   * Clean up integration
   */
  cleanup(): void {
    this.state.activeConnections.clear();
    this.state.isConnected = false;
    this.syncCallbacks.clear();
    this.errorCallbacks.clear();
    console.log('[Integration] Cleaned up integration service');
  }

  private notifySync(context: IntegrationContext): void {
    this.state.lastSync = new Date();
    this.syncCallbacks.forEach(callback => {
      try {
        callback(context);
      } catch (error) {
        console.error('[Integration] Sync callback error:', error);
      }
    });
  }

  private handleError(error: Error): void {
    this.state.syncErrors.push(error.message);
    // Keep only last 10 errors
    if (this.state.syncErrors.length > 10) {
      this.state.syncErrors = this.state.syncErrors.slice(-10);
    }

    this.errorCallbacks.forEach(callback => {
      try {
        callback(error);
      } catch (callbackError) {
        console.error('[Integration] Error callback error:', callbackError);
      }
    });
  }
}

// Export singleton instance
export const integrationService = NodeboxAIIntegrationService.getInstance();

// React hook for integration
export function useNodeboxAIIntegration(projectId?: string) {
  const [state, setState] = React.useState(integrationService.getState());

  React.useEffect(() => {
    if (!projectId) return;

    // Initialize if not connected
    if (!integrationService.isProjectConnected(projectId)) {
      integrationService.initialize(projectId).catch(console.error);
    }

    // Subscribe to state changes
    const unsubscribeSync = integrationService.onSync(() => {
      setState(integrationService.getState());
    });

    const unsubscribeError = integrationService.onError(() => {
      setState(integrationService.getState());
    });

    return () => {
      unsubscribeSync();
      unsubscribeError();
    };
  }, [projectId]);

  return {
    ...state,
    isProjectConnected: projectId ? integrationService.isProjectConnected(projectId) : false,
    getEnhancedContext: () => projectId ? integrationService.getEnhancedContext(projectId) : null,
    disconnect: () => projectId && integrationService.disconnect(projectId)
  };
}
