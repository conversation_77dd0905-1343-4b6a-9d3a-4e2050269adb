/**
 * Code Validation Service
 * 
 * Provides comprehensive code validation including syntax checking,
 * best practices analysis, and security vulnerability detection
 */

import { z } from 'zod';
import { openai } from '@ai-sdk/openai';
import { generateObject } from 'ai';

export interface ValidationIssue {
  type: 'error' | 'warning' | 'info';
  line?: number;
  column?: number;
  message: string;
  rule: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  fixSuggestion?: string;
}

export interface ValidationResult {
  language: string;
  valid: boolean;
  issues: ValidationIssue[];
  suggestions: string[];
  score: number;
  metrics: {
    complexity: number;
    maintainability: number;
    security: number;
    performance: number;
  };
  success: boolean;
}

export interface ValidationOptions {
  code: string;
  language: string;
  strict?: boolean;
  checkSecurity?: boolean;
  checkPerformance?: boolean;
  framework?: string;
}

export class CodeValidationService {
  private static instance: CodeValidationService;
  private validationCache = new Map<string, ValidationResult>();
  private readonly CACHE_TTL = 1000 * 60 * 10; // 10 minutes

  static getInstance(): CodeValidationService {
    if (!CodeValidationService.instance) {
      CodeValidationService.instance = new CodeValidationService();
    }
    return CodeValidationService.instance;
  }

  /**
   * Validate code comprehensively
   */
  async validateCode(options: ValidationOptions): Promise<ValidationResult> {
    const { code, language, strict = false, checkSecurity = true, checkPerformance = true, framework } = options;
    const cacheKey = `${this.hashCode(code)}-${language}-${strict}`;

    // Check cache first
    if (this.validationCache.has(cacheKey)) {
      return this.validationCache.get(cacheKey)!;
    }

    try {
      const issues: ValidationIssue[] = [];
      const suggestions: string[] = [];

      // Syntax validation
      const syntaxIssues = await this.validateSyntax(code, language);
      issues.push(...syntaxIssues);

      // Best practices validation
      const practiceIssues = await this.validateBestPractices(code, language, framework);
      issues.push(...practiceIssues);

      // Security validation
      if (checkSecurity) {
        const securityIssues = await this.validateSecurity(code, language);
        issues.push(...securityIssues);
      }

      // Performance validation
      if (checkPerformance) {
        const performanceIssues = await this.validatePerformance(code, language);
        issues.push(...performanceIssues);
      }

      // Generate suggestions
      const generatedSuggestions = await this.generateSuggestions(code, language, issues);
      suggestions.push(...generatedSuggestions);

      // Calculate metrics
      const metrics = this.calculateMetrics(code, issues);

      // Calculate overall score
      const score = this.calculateScore(issues, metrics);

      const result: ValidationResult = {
        language,
        valid: issues.filter(i => i.type === 'error').length === 0,
        issues,
        suggestions,
        score,
        metrics,
        success: true
      };

      // Cache the result
      this.validationCache.set(cacheKey, result);
      setTimeout(() => this.validationCache.delete(cacheKey), this.CACHE_TTL);

      return result;
    } catch (error) {
      console.error('Code validation error:', error);
      return {
        language,
        valid: false,
        issues: [{
          type: 'error',
          message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          rule: 'validation-error',
          severity: 'high'
        }],
        suggestions: [],
        score: 0,
        metrics: {
          complexity: 0,
          maintainability: 0,
          security: 0,
          performance: 0
        },
        success: false
      };
    }
  }

  /**
   * Validate syntax using AI-powered analysis
   */
  private async validateSyntax(code: string, language: string): Promise<ValidationIssue[]> {
    try {
      const { object } = await generateObject({
        model: openai('gpt-4o-mini'),
        schema: z.object({
          issues: z.array(z.object({
            type: z.enum(['error', 'warning', 'info']),
            line: z.number().optional(),
            column: z.number().optional(),
            message: z.string(),
            rule: z.string(),
            severity: z.enum(['low', 'medium', 'high', 'critical'])
          }))
        }),
        prompt: `Analyze this ${language} code for syntax errors and issues:

\`\`\`${language}
${code}
\`\`\`

Check for:
- Syntax errors
- Missing semicolons (if applicable)
- Unclosed brackets/parentheses
- Invalid variable names
- Type errors (if TypeScript)
- Import/export issues

Return detailed issues with line numbers where possible.`
      });

      return object.issues;
    } catch (error) {
      console.error('Syntax validation error:', error);
      return [];
    }
  }

  /**
   * Validate best practices
   */
  private async validateBestPractices(code: string, language: string, framework?: string): Promise<ValidationIssue[]> {
    const issues: ValidationIssue[] = [];

    // JavaScript/TypeScript specific checks
    if (language.includes('javascript') || language.includes('typescript')) {
      // Check for var usage
      if (code.includes('var ')) {
        issues.push({
          type: 'warning',
          message: 'Consider using "const" or "let" instead of "var"',
          rule: 'no-var',
          severity: 'medium',
          fixSuggestion: 'Replace "var" with "const" for constants or "let" for variables'
        });
      }

      // Check for console.log in production code
      if (code.includes('console.log')) {
        issues.push({
          type: 'warning',
          message: 'Remove console.log statements from production code',
          rule: 'no-console',
          severity: 'low',
          fixSuggestion: 'Use a proper logging library or remove debug statements'
        });
      }

      // Check for missing exports
      if (!code.includes('export') && !code.includes('module.exports')) {
        issues.push({
          type: 'info',
          message: 'Consider adding exports to make this module reusable',
          rule: 'prefer-exports',
          severity: 'low',
          fixSuggestion: 'Add export statements for functions, classes, or constants'
        });
      }
    }

    // React specific checks
    if (framework === 'react') {
      if (code.includes('React.Component') && !code.includes('PureComponent')) {
        issues.push({
          type: 'info',
          message: 'Consider using functional components with hooks instead of class components',
          rule: 'prefer-functional-components',
          severity: 'low',
          fixSuggestion: 'Convert to functional component using useState and useEffect hooks'
        });
      }
    }

    return issues;
  }

  /**
   * Validate security issues
   */
  private async validateSecurity(code: string, language: string): Promise<ValidationIssue[]> {
    const issues: ValidationIssue[] = [];

    // Check for potential security vulnerabilities
    const securityPatterns = [
      {
        pattern: /eval\s*\(/g,
        message: 'Avoid using eval() as it can execute arbitrary code',
        rule: 'no-eval',
        severity: 'critical' as const
      },
      {
        pattern: /innerHTML\s*=/g,
        message: 'Using innerHTML can lead to XSS vulnerabilities',
        rule: 'no-inner-html',
        severity: 'high' as const
      },
      {
        pattern: /document\.write\s*\(/g,
        message: 'document.write can be dangerous and is deprecated',
        rule: 'no-document-write',
        severity: 'medium' as const
      }
    ];

    for (const { pattern, message, rule, severity } of securityPatterns) {
      if (pattern.test(code)) {
        issues.push({
          type: 'warning',
          message,
          rule,
          severity,
          fixSuggestion: 'Use safer alternatives or sanitize input properly'
        });
      }
    }

    return issues;
  }

  /**
   * Validate performance issues
   */
  private async validatePerformance(code: string, language: string): Promise<ValidationIssue[]> {
    const issues: ValidationIssue[] = [];

    // Check for performance anti-patterns
    if (code.includes('for') && code.includes('document.getElementById')) {
      issues.push({
        type: 'warning',
        message: 'Avoid DOM queries inside loops for better performance',
        rule: 'no-dom-in-loop',
        severity: 'medium',
        fixSuggestion: 'Cache DOM elements outside the loop'
      });
    }

    if (code.includes('setInterval') && !code.includes('clearInterval')) {
      issues.push({
        type: 'warning',
        message: 'Remember to clear intervals to prevent memory leaks',
        rule: 'clear-intervals',
        severity: 'medium',
        fixSuggestion: 'Store interval ID and call clearInterval when done'
      });
    }

    return issues;
  }

  /**
   * Generate improvement suggestions using AI
   */
  private async generateSuggestions(code: string, language: string, issues: ValidationIssue[]): Promise<string[]> {
    try {
      const { object } = await generateObject({
        model: openai('gpt-4o-mini'),
        schema: z.object({
          suggestions: z.array(z.string())
        }),
        prompt: `Based on this ${language} code and the identified issues, provide improvement suggestions:

Code:
\`\`\`${language}
${code}
\`\`\`

Issues found:
${issues.map(issue => `- ${issue.message} (${issue.severity})`).join('\n')}

Provide 3-5 specific, actionable suggestions for improving this code.`
      });

      return object.suggestions;
    } catch (error) {
      console.error('Suggestion generation error:', error);
      return [
        'Follow language-specific best practices',
        'Add proper error handling',
        'Include comprehensive documentation',
        'Consider performance optimizations'
      ];
    }
  }

  /**
   * Calculate code quality metrics
   */
  private calculateMetrics(code: string, issues: ValidationIssue[]): ValidationResult['metrics'] {
    const lines = code.split('\n').filter(line => line.trim().length > 0);
    const linesOfCode = lines.length;

    // Calculate complexity based on control structures
    const complexityIndicators = [
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g
    ];

    let complexityScore = 0;
    for (const pattern of complexityIndicators) {
      const matches = code.match(pattern);
      complexityScore += matches ? matches.length : 0;
    }

    const complexity = Math.min(100, Math.max(0, 100 - (complexityScore / linesOfCode) * 100));

    // Calculate maintainability based on code structure
    const hasComments = /\/\/|\/\*|\*\/|#/.test(code);
    const hasFunctions = /function|const\s+\w+\s*=|=>\s*{/.test(code);
    const maintainability = Math.min(100, 
      (hasComments ? 30 : 0) + 
      (hasFunctions ? 30 : 0) + 
      (linesOfCode < 50 ? 40 : linesOfCode < 100 ? 20 : 10)
    );

    // Calculate security score based on issues
    const securityIssues = issues.filter(i => i.rule.includes('security') || i.severity === 'critical');
    const security = Math.max(0, 100 - (securityIssues.length * 20));

    // Calculate performance score
    const performanceIssues = issues.filter(i => i.rule.includes('performance') || i.message.includes('performance'));
    const performance = Math.max(0, 100 - (performanceIssues.length * 15));

    return {
      complexity,
      maintainability,
      security,
      performance
    };
  }

  /**
   * Calculate overall quality score
   */
  private calculateScore(issues: ValidationIssue[], metrics: ValidationResult['metrics']): number {
    const errorPenalty = issues.filter(i => i.type === 'error').length * 20;
    const warningPenalty = issues.filter(i => i.type === 'warning').length * 10;
    const infoPenalty = issues.filter(i => i.type === 'info').length * 5;

    const metricsAverage = (metrics.complexity + metrics.maintainability + metrics.security + metrics.performance) / 4;
    
    return Math.max(0, Math.min(100, metricsAverage - errorPenalty - warningPenalty - infoPenalty));
  }

  /**
   * Generate hash for caching
   */
  private hashCode(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  /**
   * Clear validation cache
   */
  clearCache(): void {
    this.validationCache.clear();
  }
}
