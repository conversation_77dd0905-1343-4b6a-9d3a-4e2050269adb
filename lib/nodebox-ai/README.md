# Nodebox AI Services

A comprehensive suite of AI-powered development services with real web capabilities using Browserless.

## Services Overview

### Core Services
- **DocumentationService**: Real documentation search with caching and multiple sources
- **CodeGenerationService**: AI-powered code generation with language detection
- **CodeValidationService**: Comprehensive code validation and security analysis
- **CodeEvaluationService**: Quality evaluation with iterative improvement
- **TaskAnalysisService**: Intelligent task complexity analysis and routing
- **OrchestrationService**: Complex task orchestration with dependency management
- **ParallelProcessingService**: Concurrent task execution with efficiency optimization
- **WebService**: Real web scraping and interaction using Browserless

### Web Tools Features
- **Real Web Scraping**: Extract content from any webpage using headless Chrome
- **Web Search**: Search Google, Bing, DuckDuckGo with actual results
- **Web Interaction**: Click, type, scroll, and interact with web pages
- **Screenshots**: Capture full-page or viewport screenshots
- **Content Extraction**: Extract text, links, images, and metadata
- **Browser Automation**: Full browser automation capabilities

## Environment Setup

### Required Environment Variables

```bash
# Browserless Configuration
BROWSERLESS_TOKEN=your_browserless_token_here
BROWSERLESS_URL=https://chrome.browserless.io

# OpenAI Configuration (for AI services)
OPENAI_API_KEY=your_openai_api_key_here
```

### Browserless Setup Options

#### Option 1: Browserless Cloud (Recommended)
1. Sign up at [browserless.io](https://browserless.io)
2. Get your API token from the dashboard
3. Add to your `.env.local`:
   ```bash
   BROWSERLESS_TOKEN=your_token_here
   BROWSERLESS_URL=https://chrome.browserless.io
   ```

#### Option 2: Self-Hosted Browserless
1. Run Browserless locally with Docker:
   ```bash
   docker run -p 3000:3000 browserless/chrome
   ```
2. Add to your `.env.local`:
   ```bash
   BROWSERLESS_URL=http://localhost:3000
   # No token needed for local instance
   ```

#### Option 3: Browserless Docker Compose
```yaml
version: '3.8'
services:
  browserless:
    image: browserless/chrome
    ports:
      - "3000:3000"
    environment:
      - MAX_CONCURRENT_SESSIONS=10
      - CONNECTION_TIMEOUT=60000
```

## Usage Examples

### Web Scraping
```typescript
import { WebService } from '@/lib/nodebox-ai';

const webService = WebService.getInstance();

// Scrape a webpage
const result = await webService.scrapeWebpage({
  url: 'https://example.com',
  selector: '.main-content',
  screenshot: true,
  extractLinks: true
});
```

### Web Search
```typescript
// Search the web
const searchResult = await webService.searchWeb({
  query: 'Next.js documentation',
  engine: 'google',
  maxResults: 5
});
```

### Web Interaction
```typescript
// Interact with a webpage
const interaction = await webService.interactWithWebpage({
  url: 'https://example.com',
  actions: [
    { type: 'click', selector: '#search-button' },
    { type: 'type', selector: '#search-input', value: 'search term' },
    { type: 'wait', timeout: 2000 }
  ],
  screenshot: true
});
```

## API Tools Available

### Basic Nodebox Tools
- `read_file_nodebox` - Read file contents
- `write_file_nodebox` - Write/create files
- `create_directory_nodebox` - Create directories
- `list_files_nodebox` - List directory contents
- `run_command_nodebox` - Execute terminal commands
- `create_project_nodebox` - Create new projects

### Enhanced Tools
- Smart file merging and conflict resolution
- Backup management
- Advanced project templates

### Agentic Pattern Tools
- `analyze_task_complexity` - Intelligent task analysis
- `orchestrate_complex_task` - Break down complex tasks
- `execute_subtask` - Execute coordinated subtasks
- `evaluate_generated_code` - Quality evaluation
- `iterative_code_improvement` - Automatic code improvement
- `identify_parallel_tasks` - Find parallelizable work
- `execute_parallel_tasks` - Concurrent execution

### Web Tools
- `scrape_webpage` - Extract webpage content
- `search_web` - Search engines with real results
- `interact_with_webpage` - Browser automation
- `take_screenshot` - Capture webpage screenshots

## Agentic Patterns

The system supports multiple agentic patterns:

1. **Single-Step**: Direct implementation for simple tasks
2. **Sequential**: Step-by-step execution for medium complexity
3. **Evaluator-Optimizer**: Quality-focused with improvement loops
4. **Orchestrator-Worker**: Complex task coordination
5. **Parallel Processing**: Concurrent execution for efficiency

## Performance Features

- **Intelligent Caching**: 10-30 minute TTL for web requests
- **Concurrent Processing**: Real parallel task execution
- **Smart Routing**: Optimal pattern selection
- **Resource Management**: Proper cleanup and memory management
- **Error Recovery**: Graceful fallbacks and retry logic

## Security Considerations

- **Input Validation**: All inputs are validated with Zod schemas
- **Rate Limiting**: Built-in request throttling
- **Secure Defaults**: Safe configuration options
- **Error Handling**: No sensitive data in error messages
- **Token Management**: Secure API token handling

## Monitoring and Debugging

### Service Health Check
```typescript
import { NodeboxAIServiceFactory } from '@/lib/nodebox-ai';

const health = NodeboxAIServiceFactory.getServiceHealth();
console.log('Service Health:', health);
```

### Cache Management
```typescript
// Clear all caches
NodeboxAIServiceFactory.clearAllCaches();

// Get cache statistics
const stats = NodeboxAIServiceFactory.getServiceStatistics();
```

### Logging
All services include comprehensive logging:
- Request/response logging
- Error tracking
- Performance metrics
- Cache hit/miss ratios

## Troubleshooting

### Common Issues

1. **Browserless Connection Failed**
   - Check BROWSERLESS_TOKEN is set correctly
   - Verify BROWSERLESS_URL is accessible
   - Ensure sufficient credits/quota

2. **Web Scraping Timeouts**
   - Increase timeout values
   - Check target website availability
   - Verify CSS selectors are correct

3. **Search Results Empty**
   - Try different search engines
   - Adjust search query
   - Check for rate limiting

4. **Screenshot Failures**
   - Verify page loads completely
   - Check for JavaScript errors
   - Increase wait times

### Debug Mode
Set environment variable for detailed logging:
```bash
DEBUG=nodebox-ai:*
```

## Contributing

When adding new services:
1. Follow the singleton pattern
2. Implement proper caching
3. Add comprehensive error handling
4. Include TypeScript types
5. Add to service factory
6. Update documentation

## License

This service suite is part of the Nodebox AI system and follows the project's licensing terms.
