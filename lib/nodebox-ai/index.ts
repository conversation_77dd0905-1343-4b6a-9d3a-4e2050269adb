/**
 * Nodebox AI Services Index
 * 
 * Exports all modularized services for the Nodebox AI system
 */

// Core Services
export { DocumentationService } from './documentation-service';
export { CodeGenerationService } from './code-generation-service';
export { CodeValidationService } from './code-validation-service';
export { CodeEvaluationService } from './code-evaluation-service';
export { TaskAnalysisService } from './task-analysis-service';
export { OrchestrationService } from './orchestration-service';
export { ParallelProcessingService } from './parallel-processing-service';
export { WebService } from './web-service';
export { NodeboxRuntimeService } from './nodebox-runtime-service';
export { NodeboxAIIntegrationService, integrationService, useNodeboxAIIntegration } from './integration-service';
export { NodeboxAIIntegrationTest, integrationTest } from './integration-test';
export { NodeboxInstanceProvider, nodeboxInstanceProvider } from './nodebox-instance-provider';

// Type Exports
export type { 
  DocumentationResult, 
  DocumentationSearchOptions 
} from './documentation-service';

export type { 
  CodeGenerationOptions, 
  GeneratedCode 
} from './code-generation-service';

export type { 
  ValidationIssue, 
  ValidationResult, 
  ValidationOptions 
} from './code-validation-service';

export type { 
  EvaluationCriteria, 
  EvaluationResult, 
  ImprovementIteration, 
  IterativeImprovementResult 
} from './code-evaluation-service';

export type { 
  TaskComplexity, 
  TaskType, 
  RecommendedApproach, 
  TaskAnalysisResult, 
  TaskBreakdown 
} from './task-analysis-service';

export type { 
  Subtask, 
  ExecutionPlan, 
  SubtaskExecutionResult, 
  OrchestrationResult 
} from './orchestration-service';

export type {
  ParallelTask,
  ParallelGroup,
  TaskAnalysis,
  TaskExecutionResult,
  ParallelExecutionResult
} from './parallel-processing-service';

export type {
  WebScrapingOptions,
  WebScrapingResult,
  WebInteractionOptions,
  WebSearchOptions,
  WebSearchResult
} from './web-service';

export type {
  NodeboxInstance,
  Shell,
  PreviewInfo,
  ProjectTemplate,
  ProjectSetupResult,
  CommandExecutionResult
} from './nodebox-runtime-service';

export type {
  IntegrationContext,
  ToolExecutionContext,
  IntegrationState
} from './integration-service';

export type {
  IntegrationTestResult
} from './integration-test';

/**
 * Service Factory - Creates and manages service instances
 */
export class NodeboxAIServiceFactory {
  private static instances = new Map<string, any>();

  /**
   * Get or create service instance
   */
  static getService<T>(serviceClass: new () => T, serviceName: string): T {
    if (!this.instances.has(serviceName)) {
      this.instances.set(serviceName, new serviceClass());
    }
    return this.instances.get(serviceName);
  }

  /**
   * Get all service instances
   */
  static getAllServices() {
    return {
      documentation: DocumentationService.getInstance(),
      codeGeneration: CodeGenerationService.getInstance(),
      codeValidation: CodeValidationService.getInstance(),
      codeEvaluation: CodeEvaluationService.getInstance(),
      taskAnalysis: TaskAnalysisService.getInstance(),
      orchestration: OrchestrationService.getInstance(),
      parallelProcessing: ParallelProcessingService.getInstance(),
      web: WebService.getInstance(),
      nodeboxRuntime: NodeboxRuntimeService.getInstance()
    };
  }

  /**
   * Clear all service caches
   */
  static clearAllCaches(): void {
    const services = this.getAllServices();
    
    Object.values(services).forEach(service => {
      if (typeof service.clearCache === 'function') {
        service.clearCache();
      }
    });
  }

  /**
   * Get service health status
   */
  static getServiceHealth(): {
    [serviceName: string]: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      cacheSize?: number;
      activeOperations?: number;
      lastError?: string;
    };
  } {
    const services = this.getAllServices();
    const health: any = {};

    Object.entries(services).forEach(([name, service]) => {
      health[name] = {
        status: 'healthy',
        cacheSize: typeof service.getCacheStats === 'function' 
          ? service.getCacheStats().size 
          : undefined,
        activeOperations: typeof service.getActiveOrchestrationsCount === 'function'
          ? service.getActiveOrchestrationsCount()
          : typeof service.getExecutionQueueStatus === 'function'
          ? service.getExecutionQueueStatus().activeExecutions
          : undefined
      };
    });

    return health;
  }
}

/**
 * Utility functions for service management
 */
export const ServiceUtils = {
  /**
   * Initialize all services
   */
  initializeServices(): void {
    NodeboxAIServiceFactory.getAllServices();
  },

  /**
   * Cleanup all services
   */
  cleanupServices(): void {
    NodeboxAIServiceFactory.clearAllCaches();
    
    const services = NodeboxAIServiceFactory.getAllServices();
    
    // Clear completed operations
    if (services.orchestration.clearCompletedOrchestrations) {
      services.orchestration.clearCompletedOrchestrations();
    }
    
    if (services.parallelProcessing.clearCompletedExecutions) {
      services.parallelProcessing.clearCompletedExecutions();
    }
  },

  /**
   * Get comprehensive service statistics
   */
  getServiceStatistics(): {
    totalCacheSize: number;
    activeOperations: number;
    serviceHealth: any;
    uptime: string;
  } {
    const health = NodeboxAIServiceFactory.getServiceHealth();
    
    const totalCacheSize = Object.values(health).reduce((total, service) => {
      return total + (service.cacheSize || 0);
    }, 0);

    const activeOperations = Object.values(health).reduce((total, service) => {
      return total + (service.activeOperations || 0);
    }, 0);

    return {
      totalCacheSize,
      activeOperations,
      serviceHealth: health,
      uptime: process.uptime ? `${Math.floor(process.uptime())}s` : 'unknown'
    };
  }
};
