/**
 * Nodebox Runtime Service
 * 
 * Intelligent tools that compose functions using the actual Nodebox Runtime APIs
 * Based on the official Nodebox runtime API documentation
 */

import { z } from 'zod';

export interface NodeboxInstance {
  fs: {
    init: (files: Record<string, string>) => Promise<void>;
    readFile: (path: string, encoding?: string) => Promise<string | Uint8Array>;
    writeFile: (path: string, content?: string | Uint8Array, options?: any) => Promise<void>;
    mkdir: (path: string, options?: { recursive?: boolean }) => Promise<void>;
    readdir: (path: string) => Promise<string[]>;
    stat: (path: string) => Promise<any>;
    rm: (path: string, options?: { recursive?: boolean; force?: boolean }) => Promise<void>;
    watch: (glob: string, listener: (event: any) => void) => Promise<{ dispose: () => void }>;
  };
  shell: {
    create: () => Shell;
  };
  preview: {
    getByShellId: (shellId: string, timeout?: number) => Promise<PreviewInfo>;
    waitForPort: (port: number, timeout?: number) => Promise<PreviewInfo>;
  };
  connect: () => Promise<void>;
}

export interface Shell {
  runCommand: (binary: string, args: string[], options?: { cwd?: string; env?: Record<string, string> }) => Promise<void>;
  on: (event: string, listener: (data: any) => void) => void;
  stdout: { on: (event: string, listener: (data: any) => void) => void };
  stderr: { on: (event: string, listener: (data: any) => void) => void };
  stdin: { write: (data: string | Uint8Array) => void };
  kill: () => Promise<void>;
  id: string;
}

export interface PreviewInfo {
  url: string;
  sourceShellId: string;
  port: number;
}

export interface ProjectTemplate {
  name: string;
  files: Record<string, string>;
  dependencies?: Record<string, string>;
  scripts?: Record<string, string>;
  startCommand?: { binary: string; args: string[] };
  port?: number;
}

export interface ProjectSetupResult {
  success: boolean;
  projectPath: string;
  filesCreated: string[];
  dependenciesInstalled: boolean;
  previewUrl?: string;
  shellId?: string;
  error?: string;
}

export interface CommandExecutionResult {
  success: boolean;
  exitCode: number;
  stdout: string;
  stderr: string;
  duration: number;
  shellId: string;
}

export class NodeboxRuntimeService {
  private static instance: NodeboxRuntimeService;
  private activeShells = new Map<string, Shell>();
  private projectTemplates = new Map<string, ProjectTemplate>();

  constructor() {
    this.initializeTemplates();
  }

  static getInstance(): NodeboxRuntimeService {
    if (!NodeboxRuntimeService.instance) {
      NodeboxRuntimeService.instance = new NodeboxRuntimeService();
    }
    return NodeboxRuntimeService.instance;
  }

  /**
   * Initialize project templates
   */
  private initializeTemplates(): void {
    // React TypeScript Template
    this.projectTemplates.set('react-typescript', {
      name: 'React TypeScript',
      files: {
        'package.json': JSON.stringify({
          name: 'react-typescript-app',
          version: '1.0.0',
          dependencies: {
            'react': '^18.2.0',
            'react-dom': '^18.2.0',
            'typescript': '^5.0.0',
            '@types/react': '^18.2.0',
            '@types/react-dom': '^18.2.0'
          },
          scripts: {
            'dev': 'vite',
            'build': 'vite build',
            'preview': 'vite preview'
          }
        }, null, 2),
        'index.html': `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>React TypeScript App</title>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>
</html>`,
        'src/main.tsx': `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`,
        'src/App.tsx': `import React, { useState } from 'react';

function App() {
  const [count, setCount] = useState(0);

  return (
    <div style={{ padding: '2rem', textAlign: 'center' }}>
      <h1>React TypeScript App</h1>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
    </div>
  );
}

export default App;`,
        'vite.config.ts': `import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true
  }
});`
      },
      startCommand: { binary: 'npm', args: ['run', 'dev'] },
      port: 3000
    });

    // Next.js Template
    this.projectTemplates.set('nextjs', {
      name: 'Next.js',
      files: {
        'package.json': JSON.stringify({
          name: 'nextjs-app',
          version: '1.0.0',
          dependencies: {
            'next': '^14.0.0',
            'react': '^18.2.0',
            'react-dom': '^18.2.0',
            'typescript': '^5.0.0',
            '@types/react': '^18.2.0',
            '@types/node': '^20.0.0'
          },
          scripts: {
            'dev': 'next dev',
            'build': 'next build',
            'start': 'next start'
          }
        }, null, 2),
        'next.config.js': `/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
}

module.exports = nextConfig`,
        'app/page.tsx': `export default function Home() {
  return (
    <main style={{ padding: '2rem' }}>
      <h1>Welcome to Next.js!</h1>
      <p>This is a Next.js application running in Nodebox.</p>
    </main>
  );
}`,
        'app/layout.tsx': `export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  );
}`
      },
      startCommand: { binary: 'npm', args: ['run', 'dev'] },
      port: 3000
    });

    // Express API Template
    this.projectTemplates.set('express-api', {
      name: 'Express API',
      files: {
        'package.json': JSON.stringify({
          name: 'express-api',
          version: '1.0.0',
          dependencies: {
            'express': '^4.18.0',
            'cors': '^2.8.5',
            'typescript': '^5.0.0',
            '@types/express': '^4.17.0',
            '@types/cors': '^2.8.0',
            'ts-node': '^10.9.0'
          },
          scripts: {
            'dev': 'ts-node src/server.ts',
            'build': 'tsc',
            'start': 'node dist/server.js'
          }
        }, null, 2),
        'src/server.ts': `import express from 'express';
import cors from 'cors';

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Express API is running!' });
});

app.get('/api/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});`,
        'tsconfig.json': JSON.stringify({
          compilerOptions: {
            target: 'ES2020',
            module: 'commonjs',
            outDir: './dist',
            rootDir: './src',
            strict: true,
            esModuleInterop: true,
            skipLibCheck: true,
            forceConsistentCasingInFileNames: true
          },
          include: ['src/**/*'],
          exclude: ['node_modules', 'dist']
        }, null, 2)
      },
      startCommand: { binary: 'npm', args: ['run', 'dev'] },
      port: 3001
    });
  }

  /**
   * Create a complete project from template with intelligent setup
   */
  async createProjectFromTemplate(
    nodebox: NodeboxInstance,
    templateName: string,
    projectName?: string,
    customizations?: {
      additionalFiles?: Record<string, string>;
      environmentVariables?: Record<string, string>;
      customPort?: number;
    }
  ): Promise<ProjectSetupResult> {
    const startTime = Date.now();
    
    try {
      const template = this.projectTemplates.get(templateName);
      if (!template) {
        throw new Error(`Template '${templateName}' not found`);
      }

      // Prepare files with customizations
      let files = { ...template.files };
      
      if (projectName) {
        // Update package.json with custom project name
        const packageJson = JSON.parse(files['package.json']);
        packageJson.name = projectName;
        files['package.json'] = JSON.stringify(packageJson, null, 2);
      }

      if (customizations?.additionalFiles) {
        files = { ...files, ...customizations.additionalFiles };
      }

      // Initialize the file system
      await nodebox.fs.init(files);

      // Install dependencies
      const shell = nodebox.shell.create();
      this.activeShells.set(shell.id, shell);

      let installSuccess = false;
      try {
        await this.executeCommand(shell, 'npm', ['install'], {
          cwd: '/',
          env: customizations?.environmentVariables
        });
        installSuccess = true;
      } catch (error) {
        console.warn('npm install failed, trying yarn:', error);
        try {
          await this.executeCommand(shell, 'yarn', ['install']);
          installSuccess = true;
        } catch (yarnError) {
          console.warn('yarn install also failed:', yarnError);
        }
      }

      // Start the development server if specified
      let previewUrl: string | undefined;
      if (template.startCommand) {
        try {
          // Start the server in background
          shell.runCommand(
            template.startCommand.binary,
            template.startCommand.args,
            {
              cwd: '/',
              env: customizations?.environmentVariables
            }
          );

          // Wait for the preview to be available
          const port = customizations?.customPort || template.port || 3000;
          const previewInfo = await nodebox.preview.waitForPort(port, 15000);
          previewUrl = previewInfo.url;
        } catch (error) {
          console.warn('Failed to start development server:', error);
        }
      }

      return {
        success: true,
        projectPath: '/',
        filesCreated: Object.keys(files),
        dependenciesInstalled: installSuccess,
        previewUrl,
        shellId: shell.id
      };
    } catch (error) {
      return {
        success: false,
        projectPath: '/',
        filesCreated: [],
        dependenciesInstalled: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute command with comprehensive output capture
   */
  async executeCommand(
    shell: Shell,
    binary: string,
    args: string[],
    options?: { cwd?: string; env?: Record<string, string> }
  ): Promise<CommandExecutionResult> {
    const startTime = Date.now();
    let stdout = '';
    let stderr = '';
    let exitCode = 0;

    return new Promise((resolve, reject) => {
      // Capture stdout
      shell.stdout.on('data', (data) => {
        stdout += data;
      });

      // Capture stderr
      shell.stderr.on('data', (data) => {
        stderr += data;
      });

      // Handle exit
      shell.on('exit', (code) => {
        exitCode = code;
        const duration = Date.now() - startTime;
        
        resolve({
          success: exitCode === 0,
          exitCode,
          stdout,
          stderr,
          duration,
          shellId: shell.id
        });
      });

      // Execute the command
      shell.runCommand(binary, args, options).catch(reject);
    });
  }

  /**
   * Install packages intelligently (npm/yarn detection)
   */
  async installPackages(
    nodebox: NodeboxInstance,
    packages: string[],
    options?: {
      dev?: boolean;
      shellId?: string;
      packageManager?: 'npm' | 'yarn' | 'pnpm';
    }
  ): Promise<CommandExecutionResult> {
    const shell = options?.shellId 
      ? this.activeShells.get(options.shellId) || nodebox.shell.create()
      : nodebox.shell.create();

    if (!this.activeShells.has(shell.id)) {
      this.activeShells.set(shell.id, shell);
    }

    // Detect package manager if not specified
    let packageManager = options?.packageManager;
    if (!packageManager) {
      try {
        const files = await nodebox.fs.readdir('/');
        if (files.includes('yarn.lock')) {
          packageManager = 'yarn';
        } else if (files.includes('pnpm-lock.yaml')) {
          packageManager = 'pnpm';
        } else {
          packageManager = 'npm';
        }
      } catch {
        packageManager = 'npm';
      }
    }

    // Build install command
    let args: string[];
    switch (packageManager) {
      case 'yarn':
        args = ['add', ...packages];
        if (options?.dev) args.push('--dev');
        break;
      case 'pnpm':
        args = ['add', ...packages];
        if (options?.dev) args.push('--save-dev');
        break;
      default: // npm
        args = ['install', ...packages];
        if (options?.dev) args.push('--save-dev');
    }

    return this.executeCommand(shell, packageManager, args);
  }

  /**
   * Run development server with preview management
   */
  async runDevServer(
    nodebox: NodeboxInstance,
    command: { binary: string; args: string[] },
    options?: {
      port?: number;
      env?: Record<string, string>;
      shellId?: string;
    }
  ): Promise<{
    success: boolean;
    shellId: string;
    previewUrl?: string;
    error?: string;
  }> {
    try {
      const shell = options?.shellId 
        ? this.activeShells.get(options.shellId) || nodebox.shell.create()
        : nodebox.shell.create();

      if (!this.activeShells.has(shell.id)) {
        this.activeShells.set(shell.id, shell);
      }

      // Start the server
      shell.runCommand(command.binary, command.args, {
        cwd: '/',
        env: options?.env
      });

      // Wait for preview if port is specified
      let previewUrl: string | undefined;
      if (options?.port) {
        try {
          const previewInfo = await nodebox.preview.waitForPort(options.port, 15000);
          previewUrl = previewInfo.url;
        } catch (error) {
          console.warn('Preview not available:', error);
        }
      }

      return {
        success: true,
        shellId: shell.id,
        previewUrl
      };
    } catch (error) {
      return {
        success: false,
        shellId: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get available project templates
   */
  getAvailableTemplates(): Array<{ name: string; description: string; port?: number }> {
    return Array.from(this.projectTemplates.entries()).map(([key, template]) => ({
      name: key,
      description: template.name,
      port: template.port
    }));
  }

  /**
   * Clean up shell resources
   */
  async cleanupShell(shellId: string): Promise<boolean> {
    const shell = this.activeShells.get(shellId);
    if (shell) {
      try {
        await shell.kill();
        this.activeShells.delete(shellId);
        return true;
      } catch (error) {
        console.error('Failed to cleanup shell:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Get active shells
   */
  getActiveShells(): Array<{ id: string; active: boolean }> {
    return Array.from(this.activeShells.keys()).map(id => ({
      id,
      active: true
    }));
  }
}
