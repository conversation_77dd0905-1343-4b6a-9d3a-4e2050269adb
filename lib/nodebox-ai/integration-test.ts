/**
 * Nodebox AI Integration Test
 * 
 * Comprehensive test to ensure seamless integration between:
 * - nodebox-ai route
 * - ai-nodejs-workspace-layout.tsx
 * - agentic-chat-interface.tsx
 * - Related stores and services
 */

import { integrationService } from './integration-service';
import { NodeboxRuntimeService } from './nodebox-runtime-service';
import { WebService } from './web-service';

export interface IntegrationTestResult {
  success: boolean;
  testName: string;
  duration: number;
  details: any;
  error?: string;
}

export class NodeboxAIIntegrationTest {
  private static instance: NodeboxAIIntegrationTest;
  private testResults: IntegrationTestResult[] = [];

  static getInstance(): NodeboxAIIntegrationTest {
    if (!NodeboxAIIntegrationTest.instance) {
      NodeboxAIIntegrationTest.instance = new NodeboxAIIntegrationTest();
    }
    return NodeboxAIIntegrationTest.instance;
  }

  /**
   * Run comprehensive integration tests
   */
  async runAllTests(projectId: string = 'test-project'): Promise<IntegrationTestResult[]> {
    console.log('[Integration Test] Starting comprehensive integration tests...');
    
    this.testResults = [];

    // Test 1: Integration Service Initialization
    await this.testIntegrationServiceInit(projectId);

    // Test 2: Enhanced Context Generation
    await this.testEnhancedContextGeneration(projectId);

    // Test 3: Tool Execution Enhancement
    await this.testToolExecutionEnhancement(projectId);

    // Test 4: Service Coordination
    await this.testServiceCoordination();

    // Test 5: Error Handling and Recovery
    await this.testErrorHandlingAndRecovery(projectId);

    // Test 6: Real API Integration
    await this.testRealAPIIntegration();

    // Test 7: Component Communication
    await this.testComponentCommunication(projectId);

    console.log('[Integration Test] All tests completed:', {
      total: this.testResults.length,
      passed: this.testResults.filter(r => r.success).length,
      failed: this.testResults.filter(r => !r.success).length
    });

    return this.testResults;
  }

  /**
   * Test integration service initialization
   */
  private async testIntegrationServiceInit(projectId: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Test initialization
      await integrationService.initialize(projectId);
      
      // Verify state
      const state = integrationService.getState();
      const isConnected = integrationService.isProjectConnected(projectId);
      
      if (!state.isConnected || !isConnected) {
        throw new Error('Integration service not properly initialized');
      }

      this.testResults.push({
        success: true,
        testName: 'Integration Service Initialization',
        duration: Date.now() - startTime,
        details: {
          isConnected: state.isConnected,
          activeConnections: state.activeConnections.size,
          lastSync: state.lastSync
        }
      });
    } catch (error) {
      this.testResults.push({
        success: false,
        testName: 'Integration Service Initialization',
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Test enhanced context generation
   */
  private async testEnhancedContextGeneration(projectId: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      const context = integrationService.getEnhancedContext(projectId);
      
      if (!context.projectId || context.projectId !== projectId) {
        throw new Error('Enhanced context missing or incorrect project ID');
      }

      this.testResults.push({
        success: true,
        testName: 'Enhanced Context Generation',
        duration: Date.now() - startTime,
        details: {
          projectId: context.projectId,
          hasInstanceId: !!context.instanceId,
          hasProjectContext: !!context.projectContext,
          contextKeys: Object.keys(context)
        }
      });
    } catch (error) {
      this.testResults.push({
        success: false,
        testName: 'Enhanced Context Generation',
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Test tool execution enhancement
   */
  private async testToolExecutionEnhancement(projectId: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      const toolContext = {
        projectId,
        toolName: 'test_tool',
        args: { testParam: 'testValue' },
        messageId: 'test-message-123',
        stepIndex: 1
      };

      const enhancedArgs = integrationService.enhanceToolExecution(toolContext);
      
      if (!enhancedArgs.projectId || !enhancedArgs.integrationMetadata) {
        throw new Error('Tool execution enhancement failed');
      }

      this.testResults.push({
        success: true,
        testName: 'Tool Execution Enhancement',
        duration: Date.now() - startTime,
        details: {
          originalArgsKeys: Object.keys(toolContext.args),
          enhancedArgsKeys: Object.keys(enhancedArgs),
          hasIntegrationMetadata: !!enhancedArgs.integrationMetadata,
          hasProjectContext: !!enhancedArgs.projectContext
        }
      });
    } catch (error) {
      this.testResults.push({
        success: false,
        testName: 'Tool Execution Enhancement',
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Test service coordination
   */
  private async testServiceCoordination(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const nodeboxRuntimeService = NodeboxRuntimeService.getInstance();
      const webService = WebService.getInstance();
      
      // Test template availability
      const templates = nodeboxRuntimeService.getAvailableTemplates();
      
      // Test web service health
      const webServiceHealth = webService.getServiceHealth();
      
      if (templates.length === 0) {
        throw new Error('No templates available from runtime service');
      }

      this.testResults.push({
        success: true,
        testName: 'Service Coordination',
        duration: Date.now() - startTime,
        details: {
          templatesAvailable: templates.length,
          templateNames: templates.map(t => t.name),
          webServiceHealthy: webServiceHealth.isHealthy,
          webServiceCacheSize: webServiceHealth.cacheSize
        }
      });
    } catch (error) {
      this.testResults.push({
        success: false,
        testName: 'Service Coordination',
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Test error handling and recovery
   */
  private async testErrorHandlingAndRecovery(projectId: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Test error callback registration
      let errorCaught = false;
      const unsubscribe = integrationService.onError((error) => {
        errorCaught = true;
      });

      // Simulate an error condition
      try {
        await integrationService.handleFileChange(projectId, '/nonexistent/path', 'test content');
      } catch {
        // Expected to fail
      }

      // Clean up
      unsubscribe();

      // Test state after error
      const state = integrationService.getState();
      
      this.testResults.push({
        success: true,
        testName: 'Error Handling and Recovery',
        duration: Date.now() - startTime,
        details: {
          errorCallbackTriggered: errorCaught,
          syncErrorsRecorded: state.syncErrors.length > 0,
          stillConnected: state.isConnected
        }
      });
    } catch (error) {
      this.testResults.push({
        success: false,
        testName: 'Error Handling and Recovery',
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Test real API integration
   */
  private async testRealAPIIntegration(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const nodeboxRuntimeService = NodeboxRuntimeService.getInstance();
      
      // Test mock nodebox instance creation
      const mockNodebox = {
        fs: {
          init: async () => {},
          readdir: async () => ['package.json'],
          readFile: async () => 'test content',
          writeFile: async () => {},
          mkdir: async () => {},
          stat: async () => ({ isDirectory: () => false }),
          rm: async () => {},
          watch: async () => ({ dispose: () => {} })
        },
        shell: {
          create: () => ({
            id: 'test-shell',
            runCommand: async () => {},
            on: () => {},
            stdout: { on: () => {} },
            stderr: { on: () => {} },
            stdin: { write: () => {} },
            kill: async () => {}
          })
        },
        preview: {
          waitForPort: async () => ({
            url: 'https://test-preview.com',
            sourceShellId: 'test-shell',
            port: 3000
          }),
          getByShellId: async () => ({
            url: 'https://test-preview.com',
            sourceShellId: 'test-shell',
            port: 3000
          })
        },
        connect: async () => {}
      };

      // Test project creation
      const result = await nodeboxRuntimeService.createProjectFromTemplate(
        mockNodebox as any,
        'react-typescript',
        'test-project'
      );

      if (!result.success) {
        throw new Error(`Project creation failed: ${result.error}`);
      }

      this.testResults.push({
        success: true,
        testName: 'Real API Integration',
        duration: Date.now() - startTime,
        details: {
          projectCreated: result.success,
          filesCreated: result.filesCreated.length,
          dependenciesInstalled: result.dependenciesInstalled,
          hasPreviewUrl: !!result.previewUrl,
          hasShellId: !!result.shellId
        }
      });
    } catch (error) {
      this.testResults.push({
        success: false,
        testName: 'Real API Integration',
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Test component communication
   */
  private async testComponentCommunication(projectId: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Test sync callback registration
      let syncCallbackTriggered = false;
      const unsubscribe = integrationService.onSync((context) => {
        syncCallbackTriggered = true;
      });

      // Simulate a sync event
      await integrationService.handleFileChange(projectId, '/test/file.js', 'console.log("test");');

      // Clean up
      unsubscribe();

      this.testResults.push({
        success: true,
        testName: 'Component Communication',
        duration: Date.now() - startTime,
        details: {
          syncCallbackTriggered,
          projectConnected: integrationService.isProjectConnected(projectId)
        }
      });
    } catch (error) {
      this.testResults.push({
        success: false,
        testName: 'Component Communication',
        duration: Date.now() - startTime,
        details: {},
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get test results summary
   */
  getTestSummary(): {
    total: number;
    passed: number;
    failed: number;
    passRate: number;
    results: IntegrationTestResult[];
  } {
    const total = this.testResults.length;
    const passed = this.testResults.filter(r => r.success).length;
    const failed = total - passed;
    const passRate = total > 0 ? (passed / total) * 100 : 0;

    return {
      total,
      passed,
      failed,
      passRate,
      results: this.testResults
    };
  }

  /**
   * Clean up after tests
   */
  cleanup(projectId: string): void {
    integrationService.disconnect(projectId);
    this.testResults = [];
  }
}

// Export singleton instance
export const integrationTest = NodeboxAIIntegrationTest.getInstance();
