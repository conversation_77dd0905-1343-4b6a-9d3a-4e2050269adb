/**
 * Task Analysis Service
 * 
 * Analyzes development tasks to determine complexity, approach,
 * and optimal execution strategy for agentic patterns
 */

import { z } from 'zod';
import { openai } from '@ai-sdk/openai';
import { generateObject } from 'ai';

export type TaskComplexity = 'low' | 'medium' | 'high';
export type TaskType = 'component' | 'api' | 'page' | 'hook' | 'utility' | 'general';
export type RecommendedApproach = 'single-step' | 'sequential' | 'orchestrator-worker' | 'parallel';

export interface TaskAnalysisResult {
  taskDescription: string;
  complexity: TaskComplexity;
  taskType: TaskType;
  recommendedApproach: RecommendedApproach;
  estimatedSteps: number;
  estimatedTime: string;
  recommendations: string[];
  toolsToUse: string[];
  dependencies: string[];
  riskFactors: string[];
  success: boolean;
  message: string;
}

export interface TaskBreakdown {
  mainTask: string;
  subtasks: {
    id: string;
    name: string;
    description: string;
    complexity: TaskComplexity;
    estimatedTime: string;
    dependencies: string[];
    tools: string[];
  }[];
  executionOrder: string[];
  parallelizable: string[];
  criticalPath: string[];
}

export class TaskAnalysisService {
  private static instance: TaskAnalysisService;
  private analysisCache = new Map<string, TaskAnalysisResult>();
  private readonly CACHE_TTL = 1000 * 60 * 20; // 20 minutes

  static getInstance(): TaskAnalysisService {
    if (!TaskAnalysisService.instance) {
      TaskAnalysisService.instance = new TaskAnalysisService();
    }
    return TaskAnalysisService.instance;
  }

  /**
   * Analyze task complexity and determine optimal approach
   */
  async analyzeTask(taskDescription: string, projectContext?: string): Promise<TaskAnalysisResult> {
    const cacheKey = `${this.hashCode(taskDescription)}-${projectContext || 'no-context'}`;

    // Check cache first
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)!;
    }

    try {
      // Use AI to analyze the task
      const analysis = await this.performAIAnalysis(taskDescription, projectContext);

      // Enhance with rule-based analysis
      const enhancedAnalysis = this.enhanceWithRules(analysis, taskDescription);

      // Cache the result
      this.analysisCache.set(cacheKey, enhancedAnalysis);
      setTimeout(() => this.analysisCache.delete(cacheKey), this.CACHE_TTL);

      return enhancedAnalysis;
    } catch (error) {
      console.error('Task analysis error:', error);
      return this.getFallbackAnalysis(taskDescription);
    }
  }

  /**
   * Break down complex tasks into manageable subtasks
   */
  async breakdownTask(taskDescription: string, projectContext?: string): Promise<TaskBreakdown> {
    try {
      const { object } = await generateObject({
        model: openai('gpt-4o'),
        schema: z.object({
          mainTask: z.string(),
          subtasks: z.array(z.object({
            id: z.string(),
            name: z.string(),
            description: z.string(),
            complexity: z.enum(['low', 'medium', 'high']),
            estimatedTime: z.string(),
            dependencies: z.array(z.string()),
            tools: z.array(z.string())
          })),
          executionOrder: z.array(z.string()),
          parallelizable: z.array(z.string()),
          criticalPath: z.array(z.string())
        }),
        prompt: `Break down this development task into manageable subtasks:

Task: ${taskDescription}
${projectContext ? `Project Context: ${projectContext}` : ''}

Provide:
1. List of subtasks with IDs, names, descriptions
2. Complexity level for each subtask
3. Estimated time for each subtask
4. Dependencies between subtasks
5. Tools needed for each subtask
6. Execution order
7. Which subtasks can be parallelized
8. Critical path for the project

Consider:
- File creation and modification tasks
- Code generation and validation
- Testing and deployment steps
- Documentation requirements`
      });

      return object;
    } catch (error) {
      console.error('Task breakdown error:', error);
      return this.getFallbackBreakdown(taskDescription);
    }
  }

  /**
   * Perform AI-powered task analysis
   */
  private async performAIAnalysis(taskDescription: string, projectContext?: string): Promise<TaskAnalysisResult> {
    const { object } = await generateObject({
      model: openai('gpt-4o'),
      schema: z.object({
        complexity: z.enum(['low', 'medium', 'high']),
        taskType: z.enum(['component', 'api', 'page', 'hook', 'utility', 'general']),
        recommendedApproach: z.enum(['single-step', 'sequential', 'orchestrator-worker', 'parallel']),
        estimatedSteps: z.number().min(1).max(20),
        estimatedTime: z.string(),
        recommendations: z.array(z.string()),
        toolsToUse: z.array(z.string()),
        dependencies: z.array(z.string()),
        riskFactors: z.array(z.string())
      }),
      prompt: `Analyze this development task:

Task: ${taskDescription}
${projectContext ? `Project Context: ${projectContext}` : ''}

Determine:
1. Complexity level (low/medium/high)
2. Task type (component/api/page/hook/utility/general)
3. Recommended approach (single-step/sequential/orchestrator-worker/parallel)
4. Estimated number of steps
5. Estimated time to complete
6. Specific recommendations
7. Tools that should be used
8. Dependencies or prerequisites
9. Potential risk factors

Consider:
- Scope and requirements
- Technical complexity
- Integration needs
- Testing requirements
- Documentation needs`
    });

    return {
      taskDescription,
      ...object,
      success: true,
      message: `Task analyzed: ${object.complexity} complexity ${object.taskType} task. Recommended approach: ${object.recommendedApproach}`
    };
  }

  /**
   * Enhance AI analysis with rule-based logic
   */
  private enhanceWithRules(analysis: TaskAnalysisResult, taskDescription: string): TaskAnalysisResult {
    const enhanced = { ...analysis };

    // Rule-based complexity adjustment
    const complexityIndicators = [
      'multiple', 'complex', 'advanced', 'integration', 'full-stack',
      'authentication', 'database', 'real-time', 'performance'
    ];

    const hasComplexityIndicators = complexityIndicators.some(indicator =>
      taskDescription.toLowerCase().includes(indicator)
    );

    if (hasComplexityIndicators && enhanced.complexity === 'low') {
      enhanced.complexity = 'medium';
    }

    // Adjust approach based on complexity
    if (enhanced.complexity === 'high' && enhanced.recommendedApproach === 'single-step') {
      enhanced.recommendedApproach = 'orchestrator-worker';
    }

    // Add framework-specific tools
    const frameworkTools = this.getFrameworkSpecificTools(taskDescription);
    enhanced.toolsToUse = [...new Set([...enhanced.toolsToUse, ...frameworkTools])];

    // Add common risk factors
    if (enhanced.complexity === 'high') {
      enhanced.riskFactors.push(
        'Complex requirements may require additional time',
        'Integration challenges may arise',
        'Testing complexity increases with scope'
      );
    }

    return enhanced;
  }

  /**
   * Get framework-specific tools based on task description
   */
  private getFrameworkSpecificTools(taskDescription: string): string[] {
    const tools: string[] = [];
    const desc = taskDescription.toLowerCase();

    if (desc.includes('react') || desc.includes('component')) {
      tools.push('generate_contextual_code_nodebox', 'write_file_nodebox');
    }

    if (desc.includes('api') || desc.includes('endpoint')) {
      tools.push('create_file_nodebox', 'run_command_nodebox');
    }

    if (desc.includes('test') || desc.includes('testing')) {
      tools.push('validate_code', 'run_command_nodebox');
    }

    if (desc.includes('style') || desc.includes('css')) {
      tools.push('write_file_nodebox', 'generate_code_snippet');
    }

    return tools;
  }

  /**
   * Get fallback analysis when AI analysis fails
   */
  private getFallbackAnalysis(taskDescription: string): TaskAnalysisResult {
    // Simple rule-based fallback
    const desc = taskDescription.toLowerCase();
    
    let complexity: TaskComplexity = 'low';
    if (desc.length > 100 || desc.includes('complex') || desc.includes('multiple')) {
      complexity = 'medium';
    }
    if (desc.length > 200 || desc.includes('full') || desc.includes('complete')) {
      complexity = 'high';
    }

    let taskType: TaskType = 'general';
    if (desc.includes('component')) taskType = 'component';
    else if (desc.includes('api')) taskType = 'api';
    else if (desc.includes('page')) taskType = 'page';
    else if (desc.includes('hook')) taskType = 'hook';
    else if (desc.includes('util')) taskType = 'utility';

    const recommendedApproach: RecommendedApproach = 
      complexity === 'high' ? 'orchestrator-worker' :
      complexity === 'medium' ? 'sequential' : 'single-step';

    return {
      taskDescription,
      complexity,
      taskType,
      recommendedApproach,
      estimatedSteps: complexity === 'high' ? 8 : complexity === 'medium' ? 4 : 2,
      estimatedTime: complexity === 'high' ? '15-30 minutes' : complexity === 'medium' ? '5-15 minutes' : '2-5 minutes',
      recommendations: [
        'Follow best practices for the task type',
        'Ensure proper error handling',
        'Add comprehensive testing'
      ],
      toolsToUse: ['write_file_nodebox', 'validate_code'],
      dependencies: [],
      riskFactors: complexity === 'high' ? ['High complexity may require more time'] : [],
      success: true,
      message: `Fallback analysis: ${complexity} complexity ${taskType} task`
    };
  }

  /**
   * Get fallback task breakdown
   */
  private getFallbackBreakdown(taskDescription: string): TaskBreakdown {
    return {
      mainTask: taskDescription,
      subtasks: [
        {
          id: 'analyze',
          name: 'Analyze Requirements',
          description: 'Understand the task requirements',
          complexity: 'low',
          estimatedTime: '2 minutes',
          dependencies: [],
          tools: ['analyze_codebase_nodebox']
        },
        {
          id: 'implement',
          name: 'Implement Solution',
          description: 'Create the main implementation',
          complexity: 'medium',
          estimatedTime: '10 minutes',
          dependencies: ['analyze'],
          tools: ['write_file_nodebox', 'generate_contextual_code_nodebox']
        },
        {
          id: 'validate',
          name: 'Validate Implementation',
          description: 'Test and validate the solution',
          complexity: 'low',
          estimatedTime: '3 minutes',
          dependencies: ['implement'],
          tools: ['validate_code', 'run_command_nodebox']
        }
      ],
      executionOrder: ['analyze', 'implement', 'validate'],
      parallelizable: [],
      criticalPath: ['analyze', 'implement', 'validate']
    };
  }

  /**
   * Generate hash for caching
   */
  private hashCode(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  }

  /**
   * Clear analysis cache
   */
  clearCache(): void {
    this.analysisCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.analysisCache.size,
      keys: Array.from(this.analysisCache.keys())
    };
  }
}
