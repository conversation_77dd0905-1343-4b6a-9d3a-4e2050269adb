/**
 * Code Evaluation Service
 * 
 * Provides comprehensive code evaluation for quality, best practices,
 * and project fit with iterative improvement capabilities
 */

import { z } from 'zod';
import { openai } from '@ai-sdk/openai';
import { generateObject, generateText } from 'ai';
import { CodeValidationService } from './code-validation-service';

export interface EvaluationCriteria {
  functionality: boolean;
  performance: boolean;
  security: boolean;
  maintainability: boolean;
  consistency: boolean;
}

export interface EvaluationResult {
  filePath: string;
  overallScore: number;
  needsImprovement: boolean;
  evaluation: {
    [K in keyof EvaluationCriteria]?: {
      score: number;
      issues: string[];
      suggestions: string[];
    };
  };
  recommendations: string[];
  success: boolean;
  message: string;
}

export interface ImprovementIteration {
  iteration: number;
  improvement: string;
  scoreAfter: number;
  changes: string[];
  codeChanges?: {
    before: string;
    after: string;
    diff: string[];
  };
}

export interface IterativeImprovementResult {
  filePath: string;
  initialScore: number;
  finalScore: number;
  targetScore: number;
  iterations: ImprovementIteration[];
  totalIterations: number;
  targetAchieved: boolean;
  improvements: string[];
  success: boolean;
  message: string;
}

export class CodeEvaluationService {
  private static instance: CodeEvaluationService;
  private validationService: CodeValidationService;
  private evaluationCache = new Map<string, EvaluationResult>();
  private readonly CACHE_TTL = 1000 * 60 * 15; // 15 minutes

  constructor() {
    this.validationService = CodeValidationService.getInstance();
  }

  static getInstance(): CodeEvaluationService {
    if (!CodeEvaluationService.instance) {
      CodeEvaluationService.instance = new CodeEvaluationService();
    }
    return CodeEvaluationService.instance;
  }

  /**
   * Evaluate code quality across multiple criteria
   */
  async evaluateCode(
    code: string,
    language: string,
    criteria: (keyof EvaluationCriteria)[] = ['functionality', 'maintainability', 'consistency'],
    strictMode: boolean = false,
    projectContext?: any
  ): Promise<EvaluationResult> {
    const cacheKey = `${this.hashCode(code)}-${criteria.join(',')}-${strictMode}`;

    // Check cache first
    if (this.evaluationCache.has(cacheKey)) {
      return this.evaluationCache.get(cacheKey)!;
    }

    try {
      // Get validation results first
      const validationResult = await this.validationService.validateCode({
        code,
        language,
        strict: strictMode,
        checkSecurity: criteria.includes('security'),
        checkPerformance: criteria.includes('performance')
      });

      // Evaluate each criterion
      const evaluation: EvaluationResult['evaluation'] = {};

      for (const criterion of criteria) {
        evaluation[criterion] = await this.evaluateCriterion(code, language, criterion, validationResult, projectContext);
      }

      // Calculate overall score
      const scores = Object.values(evaluation).map(e => e!.score);
      const overallScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);

      // Determine if improvement is needed
      const needsImprovement = overallScore < (strictMode ? 90 : 75);

      // Generate recommendations
      const recommendations = await this.generateRecommendations(code, language, evaluation, needsImprovement);

      const result: EvaluationResult = {
        filePath: 'evaluated-code',
        overallScore,
        needsImprovement,
        evaluation,
        recommendations,
        success: true,
        message: `Code evaluation complete. Score: ${overallScore}/100${needsImprovement ? ' - Improvements needed' : ' - Good quality'}`
      };

      // Cache the result
      this.evaluationCache.set(cacheKey, result);
      setTimeout(() => this.evaluationCache.delete(cacheKey), this.CACHE_TTL);

      return result;
    } catch (error) {
      console.error('Code evaluation error:', error);
      return {
        filePath: 'evaluated-code',
        overallScore: 0,
        needsImprovement: true,
        evaluation: {},
        recommendations: ['Evaluation failed - please check code syntax'],
        success: false,
        message: `Evaluation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Iteratively improve code based on evaluation feedback
   */
  async iterativelyImproveCode(
    code: string,
    language: string,
    targetScore: number = 85,
    maxIterations: number = 3,
    focusAreas?: (keyof EvaluationCriteria)[]
  ): Promise<IterativeImprovementResult> {
    const iterations: ImprovementIteration[] = [];
    let currentCode = code;
    let currentScore = 0;
    let iterationCount = 0;

    try {
      // Initial evaluation
      const initialEvaluation = await this.evaluateCode(currentCode, language, focusAreas);
      currentScore = initialEvaluation.overallScore;

      while (currentScore < targetScore && iterationCount < maxIterations) {
        iterationCount++;

        // Identify specific improvements needed
        const improvementPlan = await this.planImprovement(currentCode, language, initialEvaluation);

        // Apply improvements
        const improvedCode = await this.applyImprovements(currentCode, language, improvementPlan);

        // Evaluate improved code
        const newEvaluation = await this.evaluateCode(improvedCode, language, focusAreas);
        const newScore = newEvaluation.overallScore;

        // Calculate diff
        const diff = this.calculateDiff(currentCode, improvedCode);

        iterations.push({
          iteration: iterationCount,
          improvement: improvementPlan.primaryImprovement,
          scoreAfter: newScore,
          changes: improvementPlan.changes,
          codeChanges: {
            before: currentCode,
            after: improvedCode,
            diff
          }
        });

        currentCode = improvedCode;
        currentScore = newScore;

        if (currentScore >= targetScore) break;
      }

      return {
        filePath: 'improved-code',
        initialScore: initialEvaluation.overallScore,
        finalScore: currentScore,
        targetScore,
        iterations,
        totalIterations: iterationCount,
        targetAchieved: currentScore >= targetScore,
        improvements: iterations.map(i => i.improvement),
        success: true,
        message: `Completed ${iterationCount} improvement iterations. Final score: ${currentScore}/100`
      };
    } catch (error) {
      console.error('Iterative improvement error:', error);
      return {
        filePath: 'improved-code',
        initialScore: 0,
        finalScore: 0,
        targetScore,
        iterations,
        totalIterations: iterationCount,
        targetAchieved: false,
        improvements: [],
        success: false,
        message: `Improvement failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Evaluate a specific criterion
   */
  private async evaluateCriterion(
    code: string,
    language: string,
    criterion: keyof EvaluationCriteria,
    validationResult: any,
    projectContext?: any
  ): Promise<{ score: number; issues: string[]; suggestions: string[] }> {
    try {
      const { object } = await generateObject({
        model: openai('gpt-4o-mini'),
        schema: z.object({
          score: z.number().min(0).max(100),
          issues: z.array(z.string()),
          suggestions: z.array(z.string())
        }),
        prompt: `Evaluate this ${language} code for ${criterion}:

\`\`\`${language}
${code}
\`\`\`

Validation issues found: ${JSON.stringify(validationResult.issues, null, 2)}

Focus on ${criterion} and provide:
1. A score from 0-100
2. Specific issues found
3. Actionable suggestions for improvement

Consider:
${this.getCriterionGuidelines(criterion)}`
      });

      return object;
    } catch (error) {
      console.error(`${criterion} evaluation error:`, error);
      return {
        score: 50,
        issues: [`Failed to evaluate ${criterion}`],
        suggestions: [`Review code for ${criterion} improvements`]
      };
    }
  }

  /**
   * Get evaluation guidelines for each criterion
   */
  private getCriterionGuidelines(criterion: keyof EvaluationCriteria): string {
    const guidelines = {
      functionality: 'Does the code work correctly? Are edge cases handled? Is error handling proper?',
      performance: 'Is the code efficient? Are there performance bottlenecks? Is memory usage optimized?',
      security: 'Are there security vulnerabilities? Is input validated? Are best security practices followed?',
      maintainability: 'Is the code readable? Is it well-documented? Is it modular and reusable?',
      consistency: 'Does it follow project conventions? Is naming consistent? Does it match existing patterns?'
    };

    return guidelines[criterion];
  }

  /**
   * Generate improvement recommendations
   */
  private async generateRecommendations(
    code: string,
    language: string,
    evaluation: EvaluationResult['evaluation'],
    needsImprovement: boolean
  ): Promise<string[]> {
    if (!needsImprovement) {
      return ['Code quality is good', 'Consider minor optimizations'];
    }

    const allIssues = Object.values(evaluation).flatMap(e => e?.issues || []);
    const allSuggestions = Object.values(evaluation).flatMap(e => e?.suggestions || []);

    return [
      'Refactor code based on identified issues',
      'Add comprehensive error handling',
      'Improve code documentation',
      'Follow project conventions more closely',
      ...allSuggestions.slice(0, 3)
    ];
  }

  /**
   * Plan specific improvements
   */
  private async planImprovement(code: string, language: string, evaluation: EvaluationResult): Promise<{
    primaryImprovement: string;
    changes: string[];
    priority: 'high' | 'medium' | 'low';
  }> {
    try {
      const { object } = await generateObject({
        model: openai('gpt-4o-mini'),
        schema: z.object({
          primaryImprovement: z.string(),
          changes: z.array(z.string()),
          priority: z.enum(['high', 'medium', 'low'])
        }),
        prompt: `Plan improvements for this ${language} code based on evaluation results:

Current Score: ${evaluation.overallScore}/100
Issues: ${evaluation.recommendations.join(', ')}

Provide:
1. Primary improvement to focus on
2. Specific changes to make
3. Priority level

Focus on the most impactful improvements first.`
      });

      return object;
    } catch (error) {
      console.error('Improvement planning error:', error);
      return {
        primaryImprovement: 'General code cleanup',
        changes: ['Improve code structure', 'Add error handling', 'Enhance documentation'],
        priority: 'medium'
      };
    }
  }

  /**
   * Apply improvements to code
   */
  private async applyImprovements(code: string, language: string, plan: any): Promise<string> {
    try {
      const { text } = await generateText({
        model: openai('gpt-4o'),
        prompt: `Improve this ${language} code based on the improvement plan:

Original Code:
\`\`\`${language}
${code}
\`\`\`

Improvement Plan:
- Primary Focus: ${plan.primaryImprovement}
- Changes: ${plan.changes.join(', ')}

Apply the improvements while maintaining the original functionality. Return only the improved code.`
      });

      return text;
    } catch (error) {
      console.error('Code improvement error:', error);
      return code; // Return original code if improvement fails
    }
  }

  /**
   * Calculate diff between two code versions
   */
  private calculateDiff(before: string, after: string): string[] {
    const beforeLines = before.split('\n');
    const afterLines = after.split('\n');
    const diff: string[] = [];

    const maxLines = Math.max(beforeLines.length, afterLines.length);
    for (let i = 0; i < maxLines; i++) {
      const beforeLine = beforeLines[i] || '';
      const afterLine = afterLines[i] || '';

      if (beforeLine !== afterLine) {
        if (beforeLine && afterLine) {
          diff.push(`~ Line ${i + 1}: ${beforeLine} → ${afterLine}`);
        } else if (beforeLine) {
          diff.push(`- Line ${i + 1}: ${beforeLine}`);
        } else {
          diff.push(`+ Line ${i + 1}: ${afterLine}`);
        }
      }
    }

    return diff;
  }

  /**
   * Generate hash for caching
   */
  private hashCode(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  }

  /**
   * Clear evaluation cache
   */
  clearCache(): void {
    this.evaluationCache.clear();
  }
}
