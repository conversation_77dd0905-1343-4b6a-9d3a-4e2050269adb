/**
 * Code Generation Service
 * 
 * Provides intelligent code generation based on requirements and context
 * Supports multiple languages, frameworks, and coding patterns
 */

import { z } from 'zod';
import { openai } from '@ai-sdk/openai';
import { generateText } from 'ai';

export interface CodeGenerationOptions {
  language: string;
  description: string;
  framework?: string;
  style?: 'functional' | 'class-based' | 'hooks' | 'minimal';
  features?: string[];
  context?: {
    existingCode?: string;
    projectType?: string;
    dependencies?: string[];
  };
}

export interface GeneratedCode {
  language: string;
  description: string;
  framework?: string;
  style?: string;
  snippet: string;
  imports: string[];
  exports: string[];
  dependencies: string[];
  metadata: {
    linesOfCode: number;
    complexity: 'low' | 'medium' | 'high';
    patterns: string[];
    bestPractices: string[];
  };
  success: boolean;
}

export class CodeGenerationService {
  private static instance: CodeGenerationService;
  private generationCache = new Map<string, GeneratedCode>();
  private readonly CACHE_TTL = 1000 * 60 * 15; // 15 minutes

  static getInstance(): CodeGenerationService {
    if (!CodeGenerationService.instance) {
      CodeGenerationService.instance = new CodeGenerationService();
    }
    return CodeGenerationService.instance;
  }

  /**
   * Generate code snippet based on requirements
   */
  async generateCodeSnippet(options: CodeGenerationOptions): Promise<GeneratedCode> {
    const { language, description, framework, style, features = [], context } = options;
    const cacheKey = `${language}-${description}-${framework}-${style}`;

    // Check cache first
    if (this.generationCache.has(cacheKey)) {
      return this.generationCache.get(cacheKey)!;
    }

    try {
      let generatedCode: string;
      let imports: string[] = [];
      let exports: string[] = [];
      let dependencies: string[] = [];

      if (language.toLowerCase().includes('typescript') || language.toLowerCase().includes('javascript')) {
        const result = await this.generateJavaScriptTypeScript(options);
        generatedCode = result.code;
        imports = result.imports;
        exports = result.exports;
        dependencies = result.dependencies;
      } else if (language.toLowerCase() === 'css') {
        generatedCode = await this.generateCSS(description, features);
      } else if (language.toLowerCase() === 'html') {
        generatedCode = await this.generateHTML(description, features);
      } else {
        generatedCode = await this.generateGenericCode(language, description, features);
      }

      const metadata = this.analyzeGeneratedCode(generatedCode, language);

      const result: GeneratedCode = {
        language,
        description,
        framework,
        style,
        snippet: generatedCode,
        imports,
        exports,
        dependencies,
        metadata,
        success: true
      };

      // Cache the result
      this.generationCache.set(cacheKey, result);
      setTimeout(() => this.generationCache.delete(cacheKey), this.CACHE_TTL);

      return result;
    } catch (error) {
      console.error('Code generation error:', error);
      return {
        language,
        description,
        framework,
        style,
        snippet: `// Error generating code: ${error instanceof Error ? error.message : 'Unknown error'}`,
        imports: [],
        exports: [],
        dependencies: [],
        metadata: {
          linesOfCode: 1,
          complexity: 'low',
          patterns: [],
          bestPractices: []
        },
        success: false
      };
    }
  }

  /**
   * Generate JavaScript/TypeScript code
   */
  private async generateJavaScriptTypeScript(options: CodeGenerationOptions): Promise<{
    code: string;
    imports: string[];
    exports: string[];
    dependencies: string[];
  }> {
    const { language, description, framework, style, features = [], context } = options;
    const isTypeScript = language.toLowerCase().includes('typescript');

    try {
      const prompt = `Generate ${isTypeScript ? 'TypeScript' : 'JavaScript'} code for: ${description}
      
Framework: ${framework || 'vanilla'}
Style: ${style || 'modern'}
Features: ${features.join(', ')}
Context: ${context ? JSON.stringify(context, null, 2) : 'None'}

Requirements:
- Use modern ES6+ syntax
- Include proper error handling
- Follow best practices
- Add helpful comments
${isTypeScript ? '- Include proper type annotations' : ''}
${framework === 'react' ? '- Use functional components and hooks' : ''}
${framework === 'nextjs' ? '- Follow Next.js conventions' : ''}

Generate clean, production-ready code.`;

      const { text } = await generateText({
        model: openai('gpt-4o-mini'),
        prompt,
        temperature: 0.3
      });

      // Extract imports, exports, and dependencies from generated code
      const imports = this.extractImports(text);
      const exports = this.extractExports(text);
      const dependencies = this.extractDependencies(text, framework);

      return {
        code: text,
        imports,
        exports,
        dependencies
      };
    } catch (error) {
      console.error('JS/TS generation error:', error);
      return this.getFallbackJavaScriptCode(options);
    }
  }

  /**
   * Generate CSS code
   */
  private async generateCSS(description: string, features: string[]): Promise<string> {
    try {
      const prompt = `Generate CSS code for: ${description}
      
Features: ${features.join(', ')}

Requirements:
- Use modern CSS features (flexbox, grid, custom properties)
- Include responsive design
- Follow BEM methodology if applicable
- Add helpful comments
- Ensure accessibility

Generate clean, maintainable CSS.`;

      const { text } = await generateText({
        model: openai('gpt-4o-mini'),
        prompt,
        temperature: 0.3
      });

      return text;
    } catch (error) {
      console.error('CSS generation error:', error);
      return `/* ${description} */
.component {
  /* Add your styles here */
  display: block;
  /* Error generating CSS: ${error instanceof Error ? error.message : 'Unknown error'} */
}`;
    }
  }

  /**
   * Generate HTML code
   */
  private async generateHTML(description: string, features: string[]): Promise<string> {
    try {
      const prompt = `Generate semantic HTML code for: ${description}
      
Features: ${features.join(', ')}

Requirements:
- Use semantic HTML5 elements
- Include proper accessibility attributes
- Add helpful comments
- Follow web standards

Generate clean, accessible HTML.`;

      const { text } = await generateText({
        model: openai('gpt-4o-mini'),
        prompt,
        temperature: 0.3
      });

      return text;
    } catch (error) {
      console.error('HTML generation error:', error);
      return `<!-- ${description} -->
<div class="component">
  <!-- Error generating HTML: ${error instanceof Error ? error.message : 'Unknown error'} -->
  <p>Component content here</p>
</div>`;
    }
  }

  /**
   * Generate code for other languages
   */
  private async generateGenericCode(language: string, description: string, features: string[]): Promise<string> {
    try {
      const prompt = `Generate ${language} code for: ${description}
      
Features: ${features.join(', ')}

Requirements:
- Follow ${language} best practices
- Include proper error handling
- Add helpful comments
- Use appropriate design patterns

Generate clean, maintainable code.`;

      const { text } = await generateText({
        model: openai('gpt-4o-mini'),
        prompt,
        temperature: 0.3
      });

      return text;
    } catch (error) {
      console.error('Generic code generation error:', error);
      return `# ${description}
# Generated ${language} code
# Error: ${error instanceof Error ? error.message : 'Unknown error'}

def main():
    # TODO: Implement ${description}
    pass

if __name__ == "__main__":
    main()`;
    }
  }

  /**
   * Extract imports from generated code
   */
  private extractImports(code: string): string[] {
    const importRegex = /import\s+.*?from\s+['"][^'"]+['"];?/g;
    const imports = code.match(importRegex) || [];
    return imports.map(imp => imp.trim());
  }

  /**
   * Extract exports from generated code
   */
  private extractExports(code: string): string[] {
    const exportRegex = /export\s+(?:default\s+)?(?:const\s+|function\s+|class\s+)?(\w+)/g;
    const exports: string[] = [];
    let match;
    
    while ((match = exportRegex.exec(code)) !== null) {
      exports.push(match[1]);
    }
    
    return exports;
  }

  /**
   * Extract dependencies based on framework and imports
   */
  private extractDependencies(code: string, framework?: string): string[] {
    const dependencies: string[] = [];
    
    if (framework === 'react') {
      dependencies.push('react');
      if (code.includes('useState') || code.includes('useEffect')) {
        dependencies.push('@types/react');
      }
    }
    
    if (framework === 'nextjs') {
      dependencies.push('next', 'react', 'react-dom');
    }
    
    // Extract from import statements
    const importRegex = /from\s+['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(code)) !== null) {
      const dep = match[1];
      if (!dep.startsWith('.') && !dep.startsWith('/')) {
        dependencies.push(dep);
      }
    }
    
    return [...new Set(dependencies)];
  }

  /**
   * Analyze generated code for metadata
   */
  private analyzeGeneratedCode(code: string, language: string): GeneratedCode['metadata'] {
    const lines = code.split('\n').filter(line => line.trim().length > 0);
    const linesOfCode = lines.length;
    
    let complexity: 'low' | 'medium' | 'high' = 'low';
    if (linesOfCode > 50) complexity = 'medium';
    if (linesOfCode > 100) complexity = 'high';
    
    const patterns: string[] = [];
    const bestPractices: string[] = [];
    
    // Detect patterns
    if (code.includes('useState') || code.includes('useEffect')) {
      patterns.push('React Hooks');
    }
    if (code.includes('async') && code.includes('await')) {
      patterns.push('Async/Await');
    }
    if (code.includes('try') && code.includes('catch')) {
      patterns.push('Error Handling');
      bestPractices.push('Proper error handling implemented');
    }
    
    return {
      linesOfCode,
      complexity,
      patterns,
      bestPractices
    };
  }

  /**
   * Get fallback JavaScript code when AI generation fails
   */
  private getFallbackJavaScriptCode(options: CodeGenerationOptions): {
    code: string;
    imports: string[];
    exports: string[];
    dependencies: string[];
  } {
    const { description, framework, style } = options;
    
    let code = `// ${description}\n`;
    
    if (framework === 'react') {
      code += `import React from 'react';\n\n`;
      code += `const Component = () => {\n`;
      code += `  // TODO: Implement ${description}\n`;
      code += `  return (\n`;
      code += `    <div>\n`;
      code += `      {/* ${description} */}\n`;
      code += `    </div>\n`;
      code += `  );\n`;
      code += `};\n\n`;
      code += `export default Component;`;
      
      return {
        code,
        imports: ['import React from \'react\';'],
        exports: ['Component'],
        dependencies: ['react']
      };
    }
    
    code += `function implementation() {\n`;
    code += `  // TODO: Implement ${description}\n`;
    code += `  console.log('Implementing: ${description}');\n`;
    code += `}\n\n`;
    code += `export { implementation };`;
    
    return {
      code,
      imports: [],
      exports: ['implementation'],
      dependencies: []
    };
  }

  /**
   * Clear generation cache
   */
  clearCache(): void {
    this.generationCache.clear();
  }
}
