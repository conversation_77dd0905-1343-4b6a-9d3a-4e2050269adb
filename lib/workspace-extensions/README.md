# Workspace Extension System

A powerful, React-based extension system designed specifically for the AI Node.js workspace. This system allows you to extend your workspace with custom tabs, dashboard widgets, AI tools, and more.

## Features

- **Tab Extensions**: Add custom tabs to main area and side panels
- **Dashboard Widgets**: Contribute widgets to the project dashboard
- **AI Tool Integration**: Register custom AI tools for the assistant
- **Header Actions**: Add custom actions to the workspace header
- **Nodebox Integration**: Full access to Nodebox runtime APIs
- **Type-Safe**: Full TypeScript support with comprehensive type definitions
- **React Integration**: Seamless integration with React components
- **Hot Reload**: Development-friendly with hot reload support

## Quick Start

### 1. Wrap Your Workspace

```tsx
import { ExtensionProvider } from '@/lib/workspace-extensions';

function App() {
  return (
    <ExtensionProvider projectId="my-project">
      <AINodejsWorkspaceLayout />
    </ExtensionProvider>
  );
}
```

### 2. Enhance Your Tabs

```tsx
import { useMergedMainTabs } from '@/lib/workspace-extensions';

function MyWorkspace({ projectId }: { projectId: string }) {
  const existingTabs = [
    { id: 'dashboard', title: 'Dashboard', content: <Dashboard /> },
    // ... your existing tabs
  ];
  
  // Automatically includes extension tabs
  const enhancedTabs = useMergedMainTabs(projectId, existingTabs);
  
  return <TabGroup tabs={enhancedTabs} />;
}
```

### 3. Add Extension Dashboard

```tsx
import { ExtensionDashboard } from '@/lib/workspace-extensions';

function Dashboard({ projectId }: { projectId: string }) {
  return (
    <div>
      {/* Your existing dashboard */}
      <MyDashboardContent />
      
      {/* Extension widgets */}
      <ExtensionDashboard projectId={projectId} />
    </div>
  );
}
```

## Creating Extensions

### Basic Extension Structure

```tsx
import { WorkspaceExtension } from '@/lib/workspace-extensions';

const MyExtension: WorkspaceExtension = {
  id: 'my-company.my-extension',
  name: 'my-extension',
  displayName: 'My Extension',
  description: 'A sample extension',
  version: '1.0.0',
  author: 'My Company',
  category: 'productivity',
  
  contributes: {
    mainTabs: [
      {
        id: 'my-tab',
        title: 'My Tab',
        icon: <MyIcon />,
        component: MyTabComponent,
        order: 10
      }
    ],
    
    dashboardWidgets: [
      {
        id: 'my-widget',
        title: 'My Widget',
        component: MyWidgetComponent,
        size: 'medium',
        order: 5
      }
    ],
    
    aiTools: [
      {
        id: 'my-tool',
        name: 'My AI Tool',
        description: 'Does something useful',
        category: 'code',
        handler: async (params, context) => {
          // Tool implementation
          return { success: true, data: 'result' };
        }
      }
    ]
  },
  
  async activate(context) {
    // Extension initialization
    context.ui.showToast('My Extension', 'Activated successfully');
  }
};
```

### Extension Components

Extension components receive props with project context:

```tsx
import { MainTabProps } from '@/lib/workspace-extensions';

function MyTabComponent({ projectId, context }: MainTabProps) {
  return (
    <div className="p-4">
      <h2>My Extension Tab</h2>
      <p>Project: {projectId}</p>
      
      <button onClick={() => {
        context.ui.showToast('Hello', 'From my extension!');
      }}>
        Show Toast
      </button>
    </div>
  );
}
```

## Extension APIs

### Workspace API

```tsx
// Tab management
context.workspace.addMainTab(tab);
context.workspace.addPanelTab('left', tab);
context.workspace.showPanel('right');

// Project info
const projectId = context.workspace.getProjectId();
const projectInfo = await context.workspace.getProjectInfo();
```

### UI API

```tsx
// Notifications
context.ui.showToast('Title', 'Message', 'success');
context.ui.showNotification('Info message');

// Dialogs
const confirmed = await context.ui.showConfirmDialog('Title', 'Message');
const input = await context.ui.showInputDialog('Enter value');

// Status bar
context.ui.setStatusBarText('Processing...');
```

### Nodebox API

```tsx
// File operations
const content = await context.nodebox.readFile(instanceId, 'package.json');
await context.nodebox.writeFile(instanceId, 'new-file.js', 'console.log("Hello");');

// Command execution
const result = await context.nodebox.executeCommand(instanceId, 'npm', ['install', 'lodash']);

// Package management
await context.nodebox.installPackages(instanceId, ['express', 'cors']);
```

### AI Assistant API

```tsx
// Register AI tools
context.ai.registerTool({
  id: 'my-tool',
  name: 'My Tool',
  handler: async (params) => ({ success: true, data: 'result' })
});

// Interact with AI
const response = await context.ai.sendMessage('Generate a React component');
```

## Extension Types

### Main Tabs
Add tabs to the main content area:

```tsx
mainTabs: [{
  id: 'my-tab',
  title: 'My Tab',
  icon: <Icon />,
  component: MyComponent,
  order: 10,
  closable: true
}]
```

### Panel Tabs
Add tabs to side panels:

```tsx
leftPanelTabs: [{
  id: 'my-panel-tab',
  title: 'My Panel',
  icon: <Icon />,
  component: MyPanelComponent,
  order: 5
}]
```

### Dashboard Widgets
Add widgets to the dashboard:

```tsx
dashboardWidgets: [{
  id: 'my-widget',
  title: 'My Widget',
  component: MyWidgetComponent,
  size: 'medium', // 'small' | 'medium' | 'large'
  order: 10
}]
```

### AI Tools
Register tools for the AI assistant:

```tsx
aiTools: [{
  id: 'my-ai-tool',
  name: 'My AI Tool',
  description: 'Tool description',
  category: 'code',
  handler: async (params, context) => {
    // Implementation
    return { success: true, data: result };
  },
  parameters: [{
    name: 'input',
    type: 'string',
    description: 'Input parameter',
    required: true
  }]
}]
```

## Built-in Extensions

The system comes with several built-in extensions:

- **Database Viewer**: Database management and schema exploration
- **Documentation Generator**: Project documentation tools
- **Performance Monitor**: Application performance metrics

## Development

### Extension Development Mode

Set `NODE_ENV=development` to enable:
- Hot reload for extensions
- Detailed logging
- Development tools

### Testing Extensions

```tsx
import { validateExtensionManifest } from '@/lib/workspace-extensions';

const validation = validateExtensionManifest(myExtension);
if (!validation.valid) {
  console.error('Extension validation failed:', validation.errors);
}
```

## Architecture

The extension system is built with:

- **Extension Manager**: Core orchestration and lifecycle management
- **Context System**: Provides APIs to extensions
- **React Integration**: Seamless component integration
- **Type Safety**: Full TypeScript support
- **Event System**: Extension communication and updates

## Best Practices

1. **Use TypeScript**: Full type safety and IntelliSense support
2. **Handle Errors**: Always wrap extension code in try-catch
3. **Clean Up**: Implement deactivate() for proper cleanup
4. **Performance**: Use React.memo and useMemo for expensive operations
5. **User Experience**: Provide loading states and error handling
6. **Testing**: Test extensions in isolation before integration

## Contributing

Extensions can be:
- Built-in (part of the workspace)
- Third-party (loaded dynamically)
- Project-specific (per-project extensions)

The system is designed to be extensible and maintainable, following React and TypeScript best practices.
