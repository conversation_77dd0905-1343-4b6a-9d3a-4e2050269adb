/**
 * Workspace UI API Implementation
 * 
 * Implements the UI API that extensions can use
 */

import { WorkspaceUIAPI } from './api';
import { 
  DashboardWidgetContribution, 
  HeaderActionContribution 
} from './types';
import { toast } from '@/hooks/use-toast';
import { createNamespacedLogger } from '@/lib/logger';

const logger = createNamespacedLogger('ui-api');

// Global state for UI contributions
let dashboardWidgets: DashboardWidgetContribution[] = [];
let headerActions: HeaderActionContribution[] = [];
let statusBarText: string = '';

/**
 * Create workspace UI API implementation
 */
export function createWorkspaceUIAPI(extensionManager: any): WorkspaceUIAPI {
  return {
    // Dashboard widgets
    addDashboardWidget(widget: DashboardWidgetContribution): void {
      try {
        // Check if widget already exists
        if (dashboardWidgets.some(w => w.id === widget.id)) {
          logger.warn(`Dashboard widget ${widget.id} already exists`);
          return;
        }

        dashboardWidgets.push(widget);
        logger.debug(`Added dashboard widget: ${widget.id}`);

        // Emit event to notify UI components
        extensionManager.emit('dashboard-widget-added', widget);
      } catch (error) {
        logger.error(`Failed to add dashboard widget ${widget.id}:`, error);
        throw error;
      }
    },

    removeDashboardWidget(widgetId: string): void {
      try {
        const index = dashboardWidgets.findIndex(w => w.id === widgetId);
        if (index === -1) {
          logger.warn(`Dashboard widget ${widgetId} not found`);
          return;
        }

        dashboardWidgets.splice(index, 1);
        logger.debug(`Removed dashboard widget: ${widgetId}`);

        // Emit event to notify UI components
        extensionManager.emit('dashboard-widget-removed', widgetId);
      } catch (error) {
        logger.error(`Failed to remove dashboard widget ${widgetId}:`, error);
        throw error;
      }
    },

    getDashboardWidgets(): DashboardWidgetContribution[] {
      return [...dashboardWidgets];
    },

    // Header actions
    addHeaderAction(action: HeaderActionContribution): void {
      try {
        // Check if action already exists
        if (headerActions.some(a => a.id === action.id)) {
          logger.warn(`Header action ${action.id} already exists`);
          return;
        }

        headerActions.push(action);
        
        // Sort by order
        headerActions.sort((a, b) => (a.order || 0) - (b.order || 0));
        
        logger.debug(`Added header action: ${action.id}`);

        // Emit event to notify UI components
        extensionManager.emit('header-action-added', action);
      } catch (error) {
        logger.error(`Failed to add header action ${action.id}:`, error);
        throw error;
      }
    },

    removeHeaderAction(actionId: string): void {
      try {
        const index = headerActions.findIndex(a => a.id === actionId);
        if (index === -1) {
          logger.warn(`Header action ${actionId} not found`);
          return;
        }

        headerActions.splice(index, 1);
        logger.debug(`Removed header action: ${actionId}`);

        // Emit event to notify UI components
        extensionManager.emit('header-action-removed', actionId);
      } catch (error) {
        logger.error(`Failed to remove header action ${actionId}:`, error);
        throw error;
      }
    },

    getHeaderActions(): HeaderActionContribution[] {
      return [...headerActions];
    },

    // Notifications
    showNotification(
      message: string, 
      type: 'info' | 'success' | 'warning' | 'error' = 'info'
    ): void {
      try {
        // Use the toast system for notifications
        toast({
          title: type.charAt(0).toUpperCase() + type.slice(1),
          description: message,
          variant: type === 'error' ? 'destructive' : 'default'
        });

        logger.debug(`Showed ${type} notification: ${message}`);
      } catch (error) {
        logger.error(`Failed to show notification:`, error);
        // Fallback to console
        console.log(`[${type.toUpperCase()}] ${message}`);
      }
    },

    showToast(
      title: string, 
      description?: string, 
      type: 'info' | 'success' | 'warning' | 'error' = 'info'
    ): void {
      try {
        toast({
          title,
          description,
          variant: type === 'error' ? 'destructive' : 'default'
        });

        logger.debug(`Showed ${type} toast: ${title}`);
      } catch (error) {
        logger.error(`Failed to show toast:`, error);
        // Fallback to console
        console.log(`[${type.toUpperCase()}] ${title}${description ? ': ' + description : ''}`);
      }
    },

    // Dialogs
    async showConfirmDialog(title: string, message: string): Promise<boolean> {
      try {
        // For now, use browser confirm dialog
        // In a real implementation, this would show a custom dialog component
        const result = window.confirm(`${title}\n\n${message}`);
        logger.debug(`Confirm dialog result: ${result}`);
        return result;
      } catch (error) {
        logger.error(`Failed to show confirm dialog:`, error);
        return false;
      }
    },

    async showInputDialog(
      title: string, 
      placeholder?: string, 
      defaultValue?: string
    ): Promise<string | null> {
      try {
        // For now, use browser prompt dialog
        // In a real implementation, this would show a custom input dialog component
        const result = window.prompt(title, defaultValue || '');
        logger.debug(`Input dialog result: ${result ? 'provided' : 'cancelled'}`);
        return result;
      } catch (error) {
        logger.error(`Failed to show input dialog:`, error);
        return null;
      }
    },

    // Status bar
    setStatusBarText(text: string): void {
      try {
        statusBarText = text;
        logger.debug(`Set status bar text: ${text}`);

        // Emit event to notify UI components
        extensionManager.emit('status-bar-text-changed', text);
      } catch (error) {
        logger.error(`Failed to set status bar text:`, error);
        throw error;
      }
    },

    clearStatusBarText(): void {
      try {
        statusBarText = '';
        logger.debug('Cleared status bar text');

        // Emit event to notify UI components
        extensionManager.emit('status-bar-text-changed', '');
      } catch (error) {
        logger.error(`Failed to clear status bar text:`, error);
        throw error;
      }
    }
  };
}

/**
 * Get current dashboard widgets (for UI components)
 */
export function getCurrentDashboardWidgets(): DashboardWidgetContribution[] {
  return [...dashboardWidgets];
}

/**
 * Get current header actions (for UI components)
 */
export function getCurrentHeaderActions(): HeaderActionContribution[] {
  return [...headerActions];
}

/**
 * Get current status bar text (for UI components)
 */
export function getCurrentStatusBarText(): string {
  return statusBarText;
}

/**
 * Clear all UI contributions (used when extensions are deactivated)
 */
export function clearUIContributions(extensionId?: string): void {
  if (extensionId) {
    // Clear contributions from specific extension
    dashboardWidgets = dashboardWidgets.filter(w => 
      !(w as any).extensionId || (w as any).extensionId !== extensionId
    );
    headerActions = headerActions.filter(a => 
      !(a as any).extensionId || (a as any).extensionId !== extensionId
    );
  } else {
    // Clear all contributions
    dashboardWidgets = [];
    headerActions = [];
    statusBarText = '';
  }
  
  logger.debug(`Cleared UI contributions${extensionId ? ` for extension ${extensionId}` : ''}`);
}
