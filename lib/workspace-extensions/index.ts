/**
 * Workspace Extensions - Main Export
 * 
 * Main entry point for the workspace extension system
 */

// Core types and interfaces
export * from './types';
export * from './api';

// Extension manager
export { 
  WorkspaceExtensionManager, 
  ExtensionManagerEvent,
  getExtensionManager,
  initializeExtensionManager 
} from './extension-manager';

// Context creation
export { createExtensionContext, updateExtensionContextWithInstance } from './context';

// API implementations
export { createWorkspaceAPI } from './workspace-api';
export { createWorkspaceUIAPI, getCurrentDashboardWidgets, getCurrentHeaderActions } from './ui-api';
export { createNodeboxAPI } from './nodebox-api';
export { createAIAssistantAPI, getCurrentAITools } from './ai-api';

// Utility functions
export { validateExtensionManifest, createExtensionId } from './utils';

// React hooks for extension integration
export { useExtensions, useExtensionContributions } from './hooks';

// React components for extension integration
export * from './components';

// Extension registry for built-in extensions
export { registerBuiltInExtensions } from './built-in-extensions';

/**
 * Initialize the workspace extension system
 */
export async function initializeWorkspaceExtensions(projectId: string) {
  const extensionManager = await initializeExtensionManager(projectId);
  
  // Register built-in extensions
  await registerBuiltInExtensions(extensionManager, projectId);
  
  return extensionManager;
}
