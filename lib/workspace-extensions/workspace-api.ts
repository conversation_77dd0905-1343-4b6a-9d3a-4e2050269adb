/**
 * Workspace API Implementation
 * 
 * Implements the workspace API that extensions can use
 */

import { TabData } from '@/components/ui/tabs-layout/types';
import { WorkspaceAPI, ProjectInfo, WorkspaceState } from './api';
import { useWorkspaceStore } from '@/lib/stores/workspace-store';
import { createNamespacedLogger } from '@/lib/logger';

const logger = createNamespacedLogger('workspace-api');

/**
 * Create workspace API implementation
 */
export function createWorkspaceAPI(
  projectId: string,
  extensionManager: any
): WorkspaceAPI {
  return {
    // Tab management
    addMainTab(tab: TabData): void {
      try {
        const store = useWorkspaceStore.getState();
        store.addTab(tab);
        logger.debug(`Added main tab: ${tab.id}`);
      } catch (error) {
        logger.error(`Failed to add main tab ${tab.id}:`, error);
        throw error;
      }
    },

    removeMainTab(tabId: string): void {
      try {
        const store = useWorkspaceStore.getState();
        store.removeTab(tabId);
        logger.debug(`Removed main tab: ${tabId}`);
      } catch (error) {
        logger.error(`Failed to remove main tab ${tabId}:`, error);
        throw error;
      }
    },

    setActiveMainTab(tabId: string): void {
      try {
        const store = useWorkspaceStore.getState();
        store.setActiveTabId(tabId);
        logger.debug(`Set active main tab: ${tabId}`);
      } catch (error) {
        logger.error(`Failed to set active main tab ${tabId}:`, error);
        throw error;
      }
    },

    getMainTabs(): TabData[] {
      try {
        const store = useWorkspaceStore.getState();
        return store.tabs;
      } catch (error) {
        logger.error('Failed to get main tabs:', error);
        return [];
      }
    },

    // Panel management
    addPanelTab(panelId: 'left' | 'right' | 'bottom', tab: TabData): void {
      try {
        const store = useWorkspaceStore.getState();
        store.addPanelTab(panelId, tab);
        logger.debug(`Added panel tab: ${tab.id} to ${panelId} panel`);
      } catch (error) {
        logger.error(`Failed to add panel tab ${tab.id} to ${panelId}:`, error);
        throw error;
      }
    },

    removePanelTab(panelId: 'left' | 'right' | 'bottom', tabId: string): void {
      try {
        const store = useWorkspaceStore.getState();
        store.removePanelTab(panelId, tabId);
        logger.debug(`Removed panel tab: ${tabId} from ${panelId} panel`);
      } catch (error) {
        logger.error(`Failed to remove panel tab ${tabId} from ${panelId}:`, error);
        throw error;
      }
    },

    setActivePanelTab(panelId: 'left' | 'right' | 'bottom', tabId: string): void {
      try {
        const store = useWorkspaceStore.getState();
        switch (panelId) {
          case 'left':
            store.setActiveLeftPanelTab(tabId);
            break;
          case 'right':
            store.setActiveRightPanelTab(tabId);
            break;
          case 'bottom':
            store.setActiveBottomPanelTab(tabId);
            break;
        }
        logger.debug(`Set active panel tab: ${tabId} in ${panelId} panel`);
      } catch (error) {
        logger.error(`Failed to set active panel tab ${tabId} in ${panelId}:`, error);
        throw error;
      }
    },

    getPanelTabs(panelId: 'left' | 'right' | 'bottom'): TabData[] {
      try {
        const store = useWorkspaceStore.getState();
        switch (panelId) {
          case 'left':
            return store.leftPanelTabs;
          case 'right':
            return store.rightPanelTabs;
          case 'bottom':
            return store.bottomPanelTabs;
        }
      } catch (error) {
        logger.error(`Failed to get panel tabs for ${panelId}:`, error);
        return [];
      }
    },

    // Panel visibility
    showPanel(panelId: 'left' | 'right' | 'bottom'): void {
      try {
        const store = useWorkspaceStore.getState();
        switch (panelId) {
          case 'left':
            if (!store.leftPanelVisible) {
              store.toggleLeftPanel();
            }
            break;
          case 'right':
            if (!store.rightPanelVisible) {
              store.toggleRightPanel();
            }
            break;
          case 'bottom':
            if (!store.bottomPanelVisible) {
              store.toggleBottomPanel();
            }
            break;
        }
        logger.debug(`Showed ${panelId} panel`);
      } catch (error) {
        logger.error(`Failed to show ${panelId} panel:`, error);
        throw error;
      }
    },

    hidePanel(panelId: 'left' | 'right' | 'bottom'): void {
      try {
        const store = useWorkspaceStore.getState();
        switch (panelId) {
          case 'left':
            if (store.leftPanelVisible) {
              store.toggleLeftPanel();
            }
            break;
          case 'right':
            if (store.rightPanelVisible) {
              store.toggleRightPanel();
            }
            break;
          case 'bottom':
            if (store.bottomPanelVisible) {
              store.toggleBottomPanel();
            }
            break;
        }
        logger.debug(`Hid ${panelId} panel`);
      } catch (error) {
        logger.error(`Failed to hide ${panelId} panel:`, error);
        throw error;
      }
    },

    togglePanel(panelId: 'left' | 'right' | 'bottom'): void {
      try {
        const store = useWorkspaceStore.getState();
        switch (panelId) {
          case 'left':
            store.toggleLeftPanel();
            break;
          case 'right':
            store.toggleRightPanel();
            break;
          case 'bottom':
            store.toggleBottomPanel();
            break;
        }
        logger.debug(`Toggled ${panelId} panel`);
      } catch (error) {
        logger.error(`Failed to toggle ${panelId} panel:`, error);
        throw error;
      }
    },

    isPanelVisible(panelId: 'left' | 'right' | 'bottom'): boolean {
      try {
        const store = useWorkspaceStore.getState();
        switch (panelId) {
          case 'left':
            return store.leftPanelVisible;
          case 'right':
            return store.rightPanelVisible;
          case 'bottom':
            return store.bottomPanelVisible;
        }
      } catch (error) {
        logger.error(`Failed to check ${panelId} panel visibility:`, error);
        return false;
      }
    },

    // Project info
    getProjectId(): string {
      return projectId;
    },

    async getProjectInfo(): Promise<ProjectInfo> {
      try {
        // This would typically fetch from an API
        // For now, return basic info based on projectId
        return {
          id: projectId,
          name: projectId,
          description: `AI Node.js project: ${projectId}`,
          type: 'ai-nodejs',
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {
            framework: 'nodejs',
            aiEnabled: true
          }
        };
      } catch (error) {
        logger.error(`Failed to get project info for ${projectId}:`, error);
        throw error;
      }
    },

    // Workspace state
    getWorkspaceState(): WorkspaceState {
      try {
        const store = useWorkspaceStore.getState();
        return {
          projectId: store.projectId || projectId,
          activeTabId: store.activeTabId,
          activeLeftPanelTab: store.activeLeftPanelTab,
          activeRightPanelTab: store.activeRightPanelTab,
          activeBottomPanelTab: store.activeBottomPanelTab,
          leftPanelVisible: store.leftPanelVisible,
          rightPanelVisible: store.rightPanelVisible,
          bottomPanelVisible: store.bottomPanelVisible,
          generationStatus: store.isGenerating ? 'generating' : 'idle',
          nodeboxStatus: store.containerStatus === 'running' ? 'connected' : 
                        store.containerStatus === 'starting' ? 'connecting' :
                        store.containerError ? 'error' : 'disconnected'
        };
      } catch (error) {
        logger.error('Failed to get workspace state:', error);
        throw error;
      }
    },

    subscribeToStateChanges(callback: (state: WorkspaceState) => void): () => void {
      try {
        // Subscribe to workspace store changes
        const unsubscribe = useWorkspaceStore.subscribe((state) => {
          const workspaceState: WorkspaceState = {
            projectId: state.projectId || projectId,
            activeTabId: state.activeTabId,
            activeLeftPanelTab: state.activeLeftPanelTab,
            activeRightPanelTab: state.activeRightPanelTab,
            activeBottomPanelTab: state.activeBottomPanelTab,
            leftPanelVisible: state.leftPanelVisible,
            rightPanelVisible: state.rightPanelVisible,
            bottomPanelVisible: state.bottomPanelVisible,
            generationStatus: state.isGenerating ? 'generating' : 'idle',
            nodeboxStatus: state.containerStatus === 'running' ? 'connected' : 
                          state.containerStatus === 'starting' ? 'connecting' :
                          state.containerError ? 'error' : 'disconnected'
          };
          callback(workspaceState);
        });

        logger.debug('Subscribed to workspace state changes');
        return unsubscribe;
      } catch (error) {
        logger.error('Failed to subscribe to workspace state changes:', error);
        return () => {}; // Return no-op unsubscribe function
      }
    }
  };
}
