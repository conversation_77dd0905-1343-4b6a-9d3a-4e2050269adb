/**
 * Extension System Utilities
 * 
 * Utility functions for the workspace extension system
 */

import { WorkspaceExtension } from './types';
import { createNamespacedLogger } from '@/lib/logger';

const logger = createNamespacedLogger('extension-utils');

/**
 * Validate extension manifest
 */
export function validateExtensionManifest(extension: WorkspaceExtension): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Required fields
  if (!extension.id) {
    errors.push('Extension must have an id');
  }

  if (!extension.name) {
    errors.push('Extension must have a name');
  }

  if (!extension.displayName) {
    errors.push('Extension must have a displayName');
  }

  if (!extension.version) {
    errors.push('Extension must have a version');
  }

  if (!extension.author) {
    errors.push('Extension must have an author');
  }

  if (!extension.category) {
    errors.push('Extension must have a category');
  }

  // Validate version format (semantic versioning)
  if (extension.version && !isValidVersion(extension.version)) {
    errors.push('Extension version must follow semantic versioning (e.g., 1.0.0)');
  }

  // Validate ID format
  if (extension.id && !isValidExtensionId(extension.id)) {
    errors.push('Extension ID must contain only lowercase letters, numbers, hyphens, and dots');
  }

  // Validate activate function
  if (typeof extension.activate !== 'function') {
    errors.push('Extension must have an activate function');
  }

  // Validate deactivate function if present
  if (extension.deactivate && typeof extension.deactivate !== 'function') {
    errors.push('Extension deactivate must be a function');
  }

  // Validate contributions
  if (extension.contributes) {
    const contributionErrors = validateContributions(extension.contributes);
    errors.push(...contributionErrors);
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate extension contributions
 */
function validateContributions(contributions: any): string[] {
  const errors: string[] = [];

  // Validate main tabs
  if (contributions.mainTabs) {
    if (!Array.isArray(contributions.mainTabs)) {
      errors.push('mainTabs must be an array');
    } else {
      contributions.mainTabs.forEach((tab: any, index: number) => {
        if (!tab.id) errors.push(`mainTabs[${index}] must have an id`);
        if (!tab.title) errors.push(`mainTabs[${index}] must have a title`);
        if (!tab.component) errors.push(`mainTabs[${index}] must have a component`);
      });
    }
  }

  // Validate panel tabs
  ['leftPanelTabs', 'rightPanelTabs', 'bottomPanelTabs'].forEach(panelType => {
    if (contributions[panelType]) {
      if (!Array.isArray(contributions[panelType])) {
        errors.push(`${panelType} must be an array`);
      } else {
        contributions[panelType].forEach((tab: any, index: number) => {
          if (!tab.id) errors.push(`${panelType}[${index}] must have an id`);
          if (!tab.title) errors.push(`${panelType}[${index}] must have a title`);
          if (!tab.component) errors.push(`${panelType}[${index}] must have a component`);
        });
      }
    }
  });

  // Validate dashboard widgets
  if (contributions.dashboardWidgets) {
    if (!Array.isArray(contributions.dashboardWidgets)) {
      errors.push('dashboardWidgets must be an array');
    } else {
      contributions.dashboardWidgets.forEach((widget: any, index: number) => {
        if (!widget.id) errors.push(`dashboardWidgets[${index}] must have an id`);
        if (!widget.title) errors.push(`dashboardWidgets[${index}] must have a title`);
        if (!widget.component) errors.push(`dashboardWidgets[${index}] must have a component`);
        if (!widget.size) errors.push(`dashboardWidgets[${index}] must have a size`);
      });
    }
  }

  // Validate AI tools
  if (contributions.aiTools) {
    if (!Array.isArray(contributions.aiTools)) {
      errors.push('aiTools must be an array');
    } else {
      contributions.aiTools.forEach((tool: any, index: number) => {
        if (!tool.id) errors.push(`aiTools[${index}] must have an id`);
        if (!tool.name) errors.push(`aiTools[${index}] must have a name`);
        if (!tool.handler) errors.push(`aiTools[${index}] must have a handler function`);
      });
    }
  }

  return errors;
}

/**
 * Check if version follows semantic versioning
 */
function isValidVersion(version: string): boolean {
  const semverRegex = /^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$/;
  return semverRegex.test(version);
}

/**
 * Check if extension ID is valid
 */
function isValidExtensionId(id: string): boolean {
  const idRegex = /^[a-z0-9.-]+$/;
  return idRegex.test(id) && id.length >= 3 && id.length <= 50;
}

/**
 * Create a unique extension ID
 */
export function createExtensionId(name: string, author: string): string {
  const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
  const cleanAuthor = author.toLowerCase().replace(/[^a-z0-9]/g, '-');
  return `${cleanAuthor}.${cleanName}`;
}

/**
 * Compare extension versions
 */
export function compareVersions(version1: string, version2: string): number {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;

    if (v1Part > v2Part) return 1;
    if (v1Part < v2Part) return -1;
  }

  return 0;
}

/**
 * Check if extension is compatible with workspace version
 */
export function isExtensionCompatible(extension: WorkspaceExtension, workspaceVersion: string): boolean {
  // For now, assume all extensions are compatible
  // In a real implementation, this would check version constraints
  return true;
}

/**
 * Sanitize extension input
 */
export function sanitizeExtensionInput(input: any): any {
  if (typeof input === 'string') {
    // Basic HTML/script sanitization
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  if (Array.isArray(input)) {
    return input.map(sanitizeExtensionInput);
  }

  if (typeof input === 'object' && input !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[key] = sanitizeExtensionInput(value);
    }
    return sanitized;
  }

  return input;
}

/**
 * Generate extension manifest template
 */
export function generateExtensionTemplate(
  name: string, 
  author: string, 
  description: string = ''
): Partial<WorkspaceExtension> {
  const id = createExtensionId(name, author);
  
  return {
    id,
    name,
    displayName: name,
    description: description || `${name} extension for AI Node.js workspace`,
    version: '1.0.0',
    author,
    category: 'other',
    contributes: {},
    settings: {}
  };
}

/**
 * Log extension activity
 */
export function logExtensionActivity(
  extensionId: string, 
  action: string, 
  details?: any
): void {
  logger.info(`Extension ${extensionId}: ${action}`, details);
}

/**
 * Measure extension performance
 */
export function measureExtensionPerformance<T>(
  extensionId: string,
  operation: string,
  fn: () => T | Promise<T>
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const startTime = performance.now();
    
    try {
      const result = await fn();
      const duration = performance.now() - startTime;
      
      logger.debug(`Extension ${extensionId} ${operation} took ${duration.toFixed(2)}ms`);
      resolve(result);
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error(`Extension ${extensionId} ${operation} failed after ${duration.toFixed(2)}ms:`, error);
      reject(error);
    }
  });
}
