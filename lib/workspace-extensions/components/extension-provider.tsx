/**
 * Extension Provider Component
 * 
 * React component that provides extension context to the workspace
 */

"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { WorkspaceExtensionManager } from '../extension-manager';
import { initializeWorkspaceExtensions } from '../index';
import { createNamespacedLogger } from '@/lib/logger';

const logger = createNamespacedLogger('extension-provider');

interface ExtensionContextType {
  extensionManager: WorkspaceExtensionManager | null;
  isInitialized: boolean;
  error: Error | null;
}

const ExtensionContext = createContext<ExtensionContextType>({
  extensionManager: null,
  isInitialized: false,
  error: null
});

interface ExtensionProviderProps {
  children: React.ReactNode;
  projectId: string;
}

/**
 * Extension Provider Component
 * 
 * Initializes and provides the extension system to child components
 */
export function ExtensionProvider({ children, projectId }: ExtensionProviderProps) {
  const [extensionManager, setExtensionManager] = useState<WorkspaceExtensionManager | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let mounted = true;

    async function initializeExtensions() {
      try {
        logger.info(`Initializing extension system for project: ${projectId}`);
        
        const manager = await initializeWorkspaceExtensions(projectId);
        
        if (mounted) {
          setExtensionManager(manager);
          setIsInitialized(true);
          setError(null);
          logger.info('Extension system initialized successfully');
        }
      } catch (err) {
        logger.error('Failed to initialize extension system:', err);
        
        if (mounted) {
          setError(err as Error);
          setIsInitialized(false);
        }
      }
    }

    initializeExtensions();

    return () => {
      mounted = false;
    };
  }, [projectId]);

  const contextValue: ExtensionContextType = {
    extensionManager,
    isInitialized,
    error
  };

  return (
    <ExtensionContext.Provider value={contextValue}>
      {children}
    </ExtensionContext.Provider>
  );
}

/**
 * Hook to use the extension context
 */
export function useExtensionContext(): ExtensionContextType {
  const context = useContext(ExtensionContext);
  
  if (!context) {
    throw new Error('useExtensionContext must be used within an ExtensionProvider');
  }
  
  return context;
}

/**
 * Hook to get the extension manager
 */
export function useExtensionManager(): WorkspaceExtensionManager | null {
  const { extensionManager } = useExtensionContext();
  return extensionManager;
}
