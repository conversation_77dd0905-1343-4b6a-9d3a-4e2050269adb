/**
 * Extension Dashboard Component
 * 
 * React component that renders extension-contributed dashboard widgets
 */

"use client";

import React from 'react';
import { useDashboardWidgetContributions, useExtensionContext } from '../hooks';
import { DashboardWidgetContribution } from '../types';
import { createNamespacedLogger } from '@/lib/logger';
import { cn } from '@/lib/utils';

const logger = createNamespacedLogger('extension-dashboard');

interface ExtensionDashboardProps {
  projectId: string;
  className?: string;
}

/**
 * Get CSS classes for widget size
 */
function getWidgetSizeClasses(size: 'small' | 'medium' | 'large'): string {
  switch (size) {
    case 'small':
      return 'col-span-1 row-span-1';
    case 'medium':
      return 'col-span-2 row-span-1';
    case 'large':
      return 'col-span-3 row-span-2';
    default:
      return 'col-span-1 row-span-1';
  }
}

/**
 * Render a single dashboard widget
 */
function DashboardWidget({ 
  widget, 
  projectId 
}: { 
  widget: DashboardWidgetContribution; 
  projectId: string;
}) {
  try {
    return (
      <div 
        className={cn(
          'bg-card border rounded-lg overflow-hidden',
          getWidgetSizeClasses(widget.size)
        )}
      >
        {React.createElement(widget.component, {
          projectId,
          context: null // Will be provided by the extension system
        })}
      </div>
    );
  } catch (error) {
    logger.error(`Failed to render dashboard widget ${widget.id}:`, error);
    
    return (
      <div 
        className={cn(
          'bg-card border rounded-lg p-4 flex items-center justify-center',
          getWidgetSizeClasses(widget.size)
        )}
      >
        <div className="text-center text-muted-foreground">
          <p className="text-sm">Widget Error</p>
          <p className="text-xs">{widget.title}</p>
        </div>
      </div>
    );
  }
}

/**
 * Extension Dashboard Component
 * 
 * Renders all extension-contributed dashboard widgets
 */
export function ExtensionDashboard({ projectId, className }: ExtensionDashboardProps) {
  const dashboardWidgets = useDashboardWidgetContributions();
  const { isInitialized, error } = useExtensionContext();

  if (!isInitialized) {
    return (
      <div className={cn('p-4', className)}>
        <div className="text-center text-muted-foreground">
          <p className="text-sm">Loading extensions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    logger.error('Extension system error:', error);
    return (
      <div className={cn('p-4', className)}>
        <div className="text-center text-muted-foreground">
          <p className="text-sm">Extension system error</p>
          <p className="text-xs">{error.message}</p>
        </div>
      </div>
    );
  }

  if (dashboardWidgets.length === 0) {
    return null;
  }

  return (
    <div className={cn('p-4', className)}>
      <div className="mb-4">
        <h3 className="text-lg font-medium">Extension Widgets</h3>
        <p className="text-sm text-muted-foreground">
          Widgets provided by active extensions
        </p>
      </div>
      
      <div className="grid grid-cols-4 gap-4 auto-rows-min">
        {dashboardWidgets.map(widget => (
          <DashboardWidget
            key={widget.id}
            widget={widget}
            projectId={projectId}
          />
        ))}
      </div>
    </div>
  );
}

/**
 * Hook to get dashboard widgets for integration
 */
export function useExtensionDashboardWidgets(projectId: string) {
  const dashboardWidgets = useDashboardWidgetContributions();
  const { isInitialized, error } = useExtensionContext();

  return {
    widgets: dashboardWidgets,
    isInitialized,
    error,
    hasWidgets: dashboardWidgets.length > 0
  };
}

/**
 * Compact Extension Dashboard for smaller spaces
 */
export function CompactExtensionDashboard({ 
  projectId, 
  className,
  maxWidgets = 4 
}: ExtensionDashboardProps & { maxWidgets?: number }) {
  const dashboardWidgets = useDashboardWidgetContributions();
  const { isInitialized, error } = useExtensionContext();

  if (!isInitialized || error || dashboardWidgets.length === 0) {
    return null;
  }

  // Show only the first few widgets in compact mode
  const visibleWidgets = dashboardWidgets.slice(0, maxWidgets);

  return (
    <div className={cn('space-y-3', className)}>
      {visibleWidgets.map(widget => (
        <div key={widget.id} className="bg-card border rounded-lg">
          {React.createElement(widget.component, {
            projectId,
            context: null
          })}
        </div>
      ))}
      
      {dashboardWidgets.length > maxWidgets && (
        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            +{dashboardWidgets.length - maxWidgets} more widgets
          </p>
        </div>
      )}
    </div>
  );
}
