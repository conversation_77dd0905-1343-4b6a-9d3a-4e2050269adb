/**
 * Workspace Extension APIs
 * 
 * API interfaces that extensions can use to interact with the workspace
 */

import { TabData } from '@/components/ui/tabs-layout/types';
import { NodeboxInstance } from '@/lib/nodebox-runtime/api/nodebox-types';
import { 
  DashboardWidgetContribution, 
  AIToolContribution, 
  HeaderActionContribution,
  CommandContribution 
} from './types';

/**
 * Workspace API - Core workspace operations
 */
export interface WorkspaceAPI {
  // Tab management
  addMainTab(tab: TabData): void;
  removeMainTab(tabId: string): void;
  setActiveMainTab(tabId: string): void;
  getMainTabs(): TabData[];
  
  // Panel management
  addPanelTab(panelId: 'left' | 'right' | 'bottom', tab: TabData): void;
  removePanelTab(panelId: 'left' | 'right' | 'bottom', tabId: string): void;
  setActivePanelTab(panelId: 'left' | 'right' | 'bottom', tabId: string): void;
  getPanelTabs(panelId: 'left' | 'right' | 'bottom'): TabData[];
  
  // Panel visibility
  showPanel(panelId: 'left' | 'right' | 'bottom'): void;
  hidePanel(panelId: 'left' | 'right' | 'bottom'): void;
  togglePanel(panelId: 'left' | 'right' | 'bottom'): void;
  isPanelVisible(panelId: 'left' | 'right' | 'bottom'): boolean;
  
  // Project info
  getProjectId(): string;
  getProjectInfo(): Promise<ProjectInfo>;
  
  // Workspace state
  getWorkspaceState(): WorkspaceState;
  subscribeToStateChanges(callback: (state: WorkspaceState) => void): () => void;
}

/**
 * Workspace UI API - UI-related operations
 */
export interface WorkspaceUIAPI {
  // Dashboard widgets
  addDashboardWidget(widget: DashboardWidgetContribution): void;
  removeDashboardWidget(widgetId: string): void;
  getDashboardWidgets(): DashboardWidgetContribution[];
  
  // Header actions
  addHeaderAction(action: HeaderActionContribution): void;
  removeHeaderAction(actionId: string): void;
  getHeaderActions(): HeaderActionContribution[];
  
  // Notifications
  showNotification(message: string, type?: 'info' | 'success' | 'warning' | 'error'): void;
  showToast(title: string, description?: string, type?: 'info' | 'success' | 'warning' | 'error'): void;
  
  // Dialogs
  showConfirmDialog(title: string, message: string): Promise<boolean>;
  showInputDialog(title: string, placeholder?: string, defaultValue?: string): Promise<string | null>;
  
  // Status bar
  setStatusBarText(text: string): void;
  clearStatusBarText(): void;
}

/**
 * Nodebox API - Nodebox runtime operations
 */
export interface NodeboxAPI {
  // Instance management
  getActiveInstance(): NodeboxInstance | undefined;
  getAllInstances(): NodeboxInstance[];
  createInstance(config: NodeboxInstanceConfig): Promise<NodeboxInstance>;
  destroyInstance(instanceId: string): Promise<void>;
  
  // File operations
  readFile(instanceId: string, path: string): Promise<string>;
  writeFile(instanceId: string, path: string, content: string): Promise<void>;
  createFile(instanceId: string, path: string, content?: string): Promise<void>;
  deleteFile(instanceId: string, path: string): Promise<void>;
  listFiles(instanceId: string, path?: string): Promise<FileInfo[]>;
  
  // Command execution
  executeCommand(instanceId: string, command: string, args?: string[]): Promise<CommandResult>;
  
  // Package management
  installPackages(instanceId: string, packages: string[]): Promise<void>;
  uninstallPackages(instanceId: string, packages: string[]): Promise<void>;
  listPackages(instanceId: string): Promise<PackageInfo[]>;
  
  // Process management
  startProcess(instanceId: string, command: string, args?: string[]): Promise<ProcessInfo>;
  stopProcess(instanceId: string, processId: string): Promise<void>;
  getProcesses(instanceId: string): Promise<ProcessInfo[]>;
}

/**
 * AI Assistant API - AI-related operations
 */
export interface AIAssistantAPI {
  // Tool registration
  registerTool(tool: AIToolContribution): void;
  unregisterTool(toolId: string): void;
  getRegisteredTools(): AIToolContribution[];
  
  // Chat interaction
  sendMessage(message: string, context?: any): Promise<string>;
  addSystemMessage(message: string): void;
  clearChat(): void;
  getChatHistory(): ChatMessage[];
  
  // Code analysis
  analyzeCode(code: string, language: string): Promise<CodeAnalysis>;
  generateCode(prompt: string, language: string, context?: any): Promise<string>;
  explainCode(code: string, language: string): Promise<string>;
  
  // Project assistance
  suggestImprovements(projectId: string): Promise<Suggestion[]>;
  generateDocumentation(projectId: string): Promise<string>;
  findBugs(projectId: string): Promise<BugReport[]>;
}

/**
 * Supporting interfaces
 */
export interface ProjectInfo {
  id: string;
  name: string;
  description?: string;
  type: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface WorkspaceState {
  projectId: string;
  activeTabId: string;
  activeLeftPanelTab: string;
  activeRightPanelTab: string;
  activeBottomPanelTab: string;
  leftPanelVisible: boolean;
  rightPanelVisible: boolean;
  bottomPanelVisible: boolean;
  generationStatus: 'idle' | 'generating' | 'completed' | 'error';
  nodeboxStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
}

export interface NodeboxInstanceConfig {
  name: string;
  template?: string;
  settings?: Record<string, any>;
}

export interface FileInfo {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modifiedAt?: Date;
}

export interface CommandResult {
  stdout: string;
  stderr: string;
  exitCode: number;
  duration: number;
}

export interface PackageInfo {
  name: string;
  version: string;
  description?: string;
  dependencies?: Record<string, string>;
}

export interface ProcessInfo {
  id: string;
  command: string;
  args: string[];
  status: 'running' | 'stopped' | 'error';
  pid?: number;
  startedAt: Date;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface CodeAnalysis {
  language: string;
  complexity: number;
  issues: CodeIssue[];
  suggestions: string[];
  metrics: CodeMetrics;
}

export interface CodeIssue {
  type: 'error' | 'warning' | 'info';
  message: string;
  line?: number;
  column?: number;
  severity: number;
}

export interface CodeMetrics {
  linesOfCode: number;
  cyclomaticComplexity: number;
  maintainabilityIndex: number;
  technicalDebt: number;
}

export interface Suggestion {
  id: string;
  title: string;
  description: string;
  category: 'performance' | 'security' | 'maintainability' | 'best-practices';
  priority: 'low' | 'medium' | 'high';
  implementation?: string;
}

export interface BugReport {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  file: string;
  line?: number;
  column?: number;
  suggestion?: string;
}
