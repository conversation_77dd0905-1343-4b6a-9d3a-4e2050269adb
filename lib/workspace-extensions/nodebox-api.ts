/**
 * Nodebox API Implementation
 * 
 * Implements the Nodebox API that extensions can use
 */

import { NodeboxAPI, NodeboxInstanceConfig, FileInfo, CommandResult, PackageInfo, ProcessInfo } from './api';
import { NodeboxInstance } from '@/lib/nodebox-runtime/api/nodebox-types';
import { useNodeboxIntegration } from '@/lib/stores/nodebox-store';
import { createNamespacedLogger } from '@/lib/logger';

const logger = createNamespacedLogger('nodebox-api');

/**
 * Create Nodebox API implementation
 */
export function createNodeboxAPI(projectId: string): NodeboxAPI {
  return {
    // Instance management
    getActiveInstance(): NodeboxInstance | undefined {
      try {
        // Get the active instance from the Nodebox store
        const { activeInstance } = useNodeboxIntegration.getState();
        return activeInstance || undefined;
      } catch (error) {
        logger.error('Failed to get active instance:', error);
        return undefined;
      }
    },

    getAllInstances(): NodeboxInstance[] {
      try {
        // Get all instances from the Nodebox store
        const { instances } = useNodeboxIntegration.getState();
        return instances || [];
      } catch (error) {
        logger.error('Failed to get all instances:', error);
        return [];
      }
    },

    async createInstance(config: NodeboxInstanceConfig): Promise<NodeboxInstance> {
      try {
        const { createInstance } = useNodeboxIntegration.getState();
        
        const nodeboxConfig = {
          instanceId: `ext_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: config.name,
          template: config.template || 'node',
          settings: config.settings || {}
        };

        const instance = await createInstance(nodeboxConfig);
        if (!instance) {
          throw new Error('Failed to create Nodebox instance');
        }

        logger.debug(`Created Nodebox instance: ${instance.id}`);
        return instance;
      } catch (error) {
        logger.error(`Failed to create Nodebox instance:`, error);
        throw error;
      }
    },

    async destroyInstance(instanceId: string): Promise<void> {
      try {
        const { destroyInstance } = useNodeboxIntegration.getState();
        const success = await destroyInstance(instanceId);
        
        if (!success) {
          throw new Error(`Failed to destroy instance ${instanceId}`);
        }

        logger.debug(`Destroyed Nodebox instance: ${instanceId}`);
      } catch (error) {
        logger.error(`Failed to destroy instance ${instanceId}:`, error);
        throw error;
      }
    },

    // File operations
    async readFile(instanceId: string, path: string): Promise<string> {
      try {
        const instance = this.getInstanceById(instanceId);
        if (!instance) {
          throw new Error(`Instance ${instanceId} not found`);
        }

        // Use Nodebox filesystem API
        const content = await instance.runtime.fs.readFile(path);
        logger.debug(`Read file: ${path} from instance ${instanceId}`);
        return content;
      } catch (error) {
        logger.error(`Failed to read file ${path} from instance ${instanceId}:`, error);
        throw error;
      }
    },

    async writeFile(instanceId: string, path: string, content: string): Promise<void> {
      try {
        const instance = this.getInstanceById(instanceId);
        if (!instance) {
          throw new Error(`Instance ${instanceId} not found`);
        }

        // Use Nodebox filesystem API
        await instance.runtime.fs.writeFile(path, content);
        logger.debug(`Wrote file: ${path} to instance ${instanceId}`);
      } catch (error) {
        logger.error(`Failed to write file ${path} to instance ${instanceId}:`, error);
        throw error;
      }
    },

    async createFile(instanceId: string, path: string, content: string = ''): Promise<void> {
      try {
        await this.writeFile(instanceId, path, content);
        logger.debug(`Created file: ${path} in instance ${instanceId}`);
      } catch (error) {
        logger.error(`Failed to create file ${path} in instance ${instanceId}:`, error);
        throw error;
      }
    },

    async deleteFile(instanceId: string, path: string): Promise<void> {
      try {
        const instance = this.getInstanceById(instanceId);
        if (!instance) {
          throw new Error(`Instance ${instanceId} not found`);
        }

        // Use Nodebox filesystem API
        await instance.runtime.fs.rm(path);
        logger.debug(`Deleted file: ${path} from instance ${instanceId}`);
      } catch (error) {
        logger.error(`Failed to delete file ${path} from instance ${instanceId}:`, error);
        throw error;
      }
    },

    async listFiles(instanceId: string, path: string = '/'): Promise<FileInfo[]> {
      try {
        const instance = this.getInstanceById(instanceId);
        if (!instance) {
          throw new Error(`Instance ${instanceId} not found`);
        }

        // Use Nodebox filesystem API
        const files = await instance.runtime.fs.readdir(path);
        
        const fileInfos: FileInfo[] = await Promise.all(
          files.map(async (file) => {
            const fullPath = path === '/' ? `/${file}` : `${path}/${file}`;
            try {
              const stats = await instance.runtime.fs.stat(fullPath);
              return {
                name: file,
                path: fullPath,
                type: stats.isDirectory() ? 'directory' : 'file',
                size: stats.isFile() ? stats.size : undefined,
                modifiedAt: new Date(stats.mtime)
              };
            } catch {
              return {
                name: file,
                path: fullPath,
                type: 'file'
              };
            }
          })
        );

        logger.debug(`Listed ${fileInfos.length} files in ${path} from instance ${instanceId}`);
        return fileInfos;
      } catch (error) {
        logger.error(`Failed to list files in ${path} from instance ${instanceId}:`, error);
        throw error;
      }
    },

    // Command execution
    async executeCommand(instanceId: string, command: string, args: string[] = []): Promise<CommandResult> {
      try {
        const instance = this.getInstanceById(instanceId);
        if (!instance) {
          throw new Error(`Instance ${instanceId} not found`);
        }

        const startTime = Date.now();
        
        // Use Nodebox shell API
        const fullCommand = args.length > 0 ? `${command} ${args.join(' ')}` : command;
        const result = await instance.runtime.shell.runCommand(fullCommand);
        
        const duration = Date.now() - startTime;

        const commandResult: CommandResult = {
          stdout: result.stdout || '',
          stderr: result.stderr || '',
          exitCode: result.exitCode || 0,
          duration
        };

        logger.debug(`Executed command: ${fullCommand} in instance ${instanceId}`);
        return commandResult;
      } catch (error) {
        logger.error(`Failed to execute command ${command} in instance ${instanceId}:`, error);
        throw error;
      }
    },

    // Package management
    async installPackages(instanceId: string, packages: string[]): Promise<void> {
      try {
        const command = 'npm';
        const args = ['install', ...packages];
        
        const result = await this.executeCommand(instanceId, command, args);
        
        if (result.exitCode !== 0) {
          throw new Error(`Package installation failed: ${result.stderr}`);
        }

        logger.debug(`Installed packages: ${packages.join(', ')} in instance ${instanceId}`);
      } catch (error) {
        logger.error(`Failed to install packages in instance ${instanceId}:`, error);
        throw error;
      }
    },

    async uninstallPackages(instanceId: string, packages: string[]): Promise<void> {
      try {
        const command = 'npm';
        const args = ['uninstall', ...packages];
        
        const result = await this.executeCommand(instanceId, command, args);
        
        if (result.exitCode !== 0) {
          throw new Error(`Package uninstallation failed: ${result.stderr}`);
        }

        logger.debug(`Uninstalled packages: ${packages.join(', ')} from instance ${instanceId}`);
      } catch (error) {
        logger.error(`Failed to uninstall packages from instance ${instanceId}:`, error);
        throw error;
      }
    },

    async listPackages(instanceId: string): Promise<PackageInfo[]> {
      try {
        const result = await this.executeCommand(instanceId, 'npm', ['list', '--json', '--depth=0']);
        
        if (result.exitCode !== 0) {
          // npm list can return non-zero even for successful listings
          logger.warn(`npm list returned non-zero exit code: ${result.exitCode}`);
        }

        const packageData = JSON.parse(result.stdout);
        const packages: PackageInfo[] = [];

        if (packageData.dependencies) {
          for (const [name, info] of Object.entries(packageData.dependencies)) {
            packages.push({
              name,
              version: (info as any).version || 'unknown',
              description: (info as any).description
            });
          }
        }

        logger.debug(`Listed ${packages.length} packages from instance ${instanceId}`);
        return packages;
      } catch (error) {
        logger.error(`Failed to list packages from instance ${instanceId}:`, error);
        return [];
      }
    },

    // Process management
    async startProcess(instanceId: string, command: string, args: string[] = []): Promise<ProcessInfo> {
      try {
        const instance = this.getInstanceById(instanceId);
        if (!instance) {
          throw new Error(`Instance ${instanceId} not found`);
        }

        // For now, we'll simulate process management
        // In a real implementation, this would use Nodebox's process management
        const processId = `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const processInfo: ProcessInfo = {
          id: processId,
          command,
          args,
          status: 'running',
          startedAt: new Date()
        };

        logger.debug(`Started process: ${command} in instance ${instanceId}`);
        return processInfo;
      } catch (error) {
        logger.error(`Failed to start process ${command} in instance ${instanceId}:`, error);
        throw error;
      }
    },

    async stopProcess(instanceId: string, processId: string): Promise<void> {
      try {
        // For now, we'll simulate process stopping
        // In a real implementation, this would use Nodebox's process management
        logger.debug(`Stopped process: ${processId} in instance ${instanceId}`);
      } catch (error) {
        logger.error(`Failed to stop process ${processId} in instance ${instanceId}:`, error);
        throw error;
      }
    },

    async getProcesses(instanceId: string): Promise<ProcessInfo[]> {
      try {
        // For now, return empty array
        // In a real implementation, this would list active processes
        return [];
      } catch (error) {
        logger.error(`Failed to get processes from instance ${instanceId}:`, error);
        return [];
      }
    },

    // Helper method to get instance by ID (not part of the interface)
    getInstanceById: (instanceId: string): NodeboxInstance | undefined => {
      const instances = this.getAllInstances();
      return instances.find(instance => instance.id === instanceId);
    }
  };
}
