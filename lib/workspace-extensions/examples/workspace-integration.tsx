/**
 * Workspace Integration Example
 * 
 * Example of how to integrate the extension system with your AI Node.js workspace layout
 */

"use client";

import React, { useMemo } from 'react';
import { TabData } from '@/components/ui/tabs-layout/types';
import { 
  ExtensionProvider,
  useMergedMainTabs,
  useMergedPanelTabs,
  ExtensionDashboard
} from '../components';

// This is how you would modify your AINodejsWorkspaceLayout component

interface WorkspaceWithExtensionsProps {
  projectId: string;
  children: React.ReactNode;
}

/**
 * Wrapper component that provides extension context
 */
export function WorkspaceWithExtensions({ projectId, children }: WorkspaceWithExtensionsProps) {
  return (
    <ExtensionProvider projectId={projectId}>
      {children}
    </ExtensionProvider>
  );
}

/**
 * Example of how to modify your main tabs to include extension tabs
 */
export function useEnhancedMainTabs(projectId: string) {
  // Your existing main tabs
  const existingMainTabs: TabData[] = useMemo(() => [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: null, // Your existing icons
      content: <div>Dashboard content</div>,
    },
    {
      id: 'nodebox-workspace',
      title: 'Workspace',
      icon: null,
      content: <div>Nodebox workspace content</div>,
    },
    {
      id: 'code-editor',
      title: 'Editor',
      icon: null,
      content: <div>Code editor content</div>,
    },
    // ... your other existing tabs
  ], []);

  // Merge with extension tabs
  const enhancedTabs = useMergedMainTabs(projectId, existingMainTabs);
  
  return enhancedTabs;
}

/**
 * Example of how to modify your panel tabs to include extension tabs
 */
export function useEnhancedPanelTabs(
  projectId: string, 
  panelId: 'left' | 'right' | 'bottom'
) {
  // Your existing panel tabs
  const existingPanelTabs: TabData[] = useMemo(() => {
    switch (panelId) {
      case 'left':
        return [
          {
            id: 'devbot',
            title: 'AI Assistant',
            icon: null,
            content: <div>AI Assistant content</div>,
          }
        ];
      case 'right':
        return [
          {
            id: 'sandbox-manager',
            title: 'Sandbox Manager',
            icon: null,
            content: <div>Sandbox manager content</div>,
          }
        ];
      case 'bottom':
        return [];
      default:
        return [];
    }
  }, [panelId]);

  // Merge with extension tabs
  const enhancedTabs = useMergedPanelTabs(projectId, panelId, existingPanelTabs);
  
  return enhancedTabs;
}

/**
 * Example of enhanced dashboard with extension widgets
 */
export function EnhancedDashboard({ projectId }: { projectId: string }) {
  return (
    <div className="space-y-6">
      {/* Your existing dashboard content */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Project Dashboard</h2>
        {/* Your existing dashboard widgets */}
      </div>
      
      {/* Extension-contributed widgets */}
      <ExtensionDashboard projectId={projectId} />
    </div>
  );
}

/**
 * Complete example of modified workspace layout
 */
export function ExampleWorkspaceLayout({ projectId }: { projectId: string }) {
  const mainTabs = useEnhancedMainTabs(projectId);
  const leftPanelTabs = useEnhancedPanelTabs(projectId, 'left');
  const rightPanelTabs = useEnhancedPanelTabs(projectId, 'right');

  return (
    <WorkspaceWithExtensions projectId={projectId}>
      <div className="flex h-full w-full flex-col">
        {/* Your existing header */}
        <div className="border-b p-4">
          <h1>AI Node.js Workspace with Extensions</h1>
        </div>

        {/* Main content with extension-enhanced tabs */}
        <div className="flex-1 flex">
          {/* Left panel with extension tabs */}
          <div className="w-80 border-r">
            {/* Render left panel tabs including extensions */}
            {leftPanelTabs.map(tab => (
              <div key={tab.id} className="p-2">
                {tab.title}
              </div>
            ))}
          </div>

          {/* Main content area with extension tabs */}
          <div className="flex-1">
            {/* Render main tabs including extensions */}
            {mainTabs.map(tab => (
              <div key={tab.id} className="hidden">
                {tab.content}
              </div>
            ))}
          </div>

          {/* Right panel with extension tabs */}
          <div className="w-80 border-l">
            {/* Render right panel tabs including extensions */}
            {rightPanelTabs.map(tab => (
              <div key={tab.id} className="p-2">
                {tab.title}
              </div>
            ))}
          </div>
        </div>
      </div>
    </WorkspaceWithExtensions>
  );
}

/**
 * Instructions for integrating with your existing workspace:
 * 
 * 1. Wrap your AINodejsWorkspaceLayout with ExtensionProvider:
 *    <ExtensionProvider projectId={projectId}>
 *      <AINodejsWorkspaceLayout />
 *    </ExtensionProvider>
 * 
 * 2. Replace your mainTabs useMemo with:
 *    const mainTabs = useMergedMainTabs(projectId, existingMainTabs);
 * 
 * 3. Replace your panel tabs with:
 *    const leftPanelTabs = useMergedPanelTabs(projectId, 'left', existingLeftTabs);
 *    const rightPanelTabs = useMergedPanelTabs(projectId, 'right', existingRightTabs);
 * 
 * 4. Add extension dashboard to your dashboard component:
 *    <ExtensionDashboard projectId={projectId} />
 * 
 * 5. Extensions will automatically be loaded and their contributions merged
 *    with your existing tabs and widgets.
 */
