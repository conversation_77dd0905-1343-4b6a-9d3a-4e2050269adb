/**
 * Test Extension Example
 * 
 * A simple test extension to verify the extension system works
 */

"use client";

import React from 'react';
import { TestTube, Zap } from 'lucide-react';
import { WorkspaceExtension } from '../types';
import { Button } from '@/components/ui/button';

/**
 * Test Extension Component
 */
function TestExtensionComponent({ projectId, context }: any) {
  const [count, setCount] = React.useState(0);

  return (
    <div className="p-6 space-y-4">
      <div className="text-center">
        <TestTube className="h-12 w-12 mx-auto mb-4 text-blue-500" />
        <h2 className="text-xl font-bold mb-2">Test Extension</h2>
        <p className="text-muted-foreground">
          This is a test extension for project: <code className="bg-muted px-1 rounded">{projectId}</code>
        </p>
      </div>

      <div className="border rounded-lg p-4 space-y-3">
        <h3 className="font-medium">Extension Features Test</h3>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Counter:</span>
            <span className="font-mono">{count}</span>
          </div>
          
          <div className="flex gap-2">
            <Button 
              size="sm" 
              onClick={() => setCount(c => c + 1)}
            >
              Increment
            </Button>
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => setCount(0)}
            >
              Reset
            </Button>
          </div>
        </div>
      </div>

      <div className="border rounded-lg p-4 space-y-3">
        <h3 className="font-medium">Extension API Test</h3>
        
        <div className="space-y-2">
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => {
              if (context?.ui) {
                context.ui.showToast('Test Extension', 'Toast notification works!', 'success');
              }
            }}
          >
            Test Toast
          </Button>
          
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => {
              if (context?.ui) {
                context.ui.showNotification('Test notification from extension', 'info');
              }
            }}
          >
            Test Notification
          </Button>
          
          <Button 
            size="sm" 
            variant="outline"
            onClick={async () => {
              if (context?.ui) {
                const confirmed = await context.ui.showConfirmDialog(
                  'Test Dialog', 
                  'This is a test confirmation dialog from the extension.'
                );
                context.ui.showToast(
                  'Dialog Result', 
                  `You clicked: ${confirmed ? 'OK' : 'Cancel'}`, 
                  'info'
                );
              }
            }}
          >
            Test Dialog
          </Button>
        </div>
      </div>

      <div className="border rounded-lg p-4 space-y-3">
        <h3 className="font-medium">Workspace API Test</h3>
        
        <div className="space-y-2">
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => {
              if (context?.workspace) {
                const projectId = context.workspace.getProjectId();
                const state = context.workspace.getWorkspaceState();
                context?.ui?.showToast(
                  'Workspace Info', 
                  `Project: ${projectId}, Active Tab: ${state.activeTabId}`, 
                  'info'
                );
              }
            }}
          >
            Get Workspace Info
          </Button>
          
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => {
              if (context?.workspace) {
                context.workspace.showPanel('right');
                context?.ui?.showToast('Panel Action', 'Showed right panel', 'success');
              }
            }}
          >
            Show Right Panel
          </Button>
        </div>
      </div>
    </div>
  );
}

/**
 * Test Dashboard Widget
 */
function TestDashboardWidget({ projectId }: any) {
  return (
    <div className="p-4 border rounded-lg">
      <div className="flex items-center gap-2 mb-3">
        <Zap className="h-5 w-5 text-yellow-500" />
        <h4 className="font-medium">Test Widget</h4>
      </div>
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Project ID</span>
          <span className="font-mono text-xs">{projectId}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Status</span>
          <span className="text-green-500 font-medium">Active</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Extensions</span>
          <span className="font-medium">Working</span>
        </div>
      </div>
    </div>
  );
}

/**
 * Test Extension Definition
 */
export const TestExtension: WorkspaceExtension = {
  id: 'workspace.test-extension',
  name: 'test-extension',
  displayName: 'Test Extension',
  description: 'A test extension to verify the extension system works correctly',
  version: '1.0.0',
  author: 'Workspace Team',
  category: 'development',
  icon: React.createElement(TestTube, { className: 'h-4 w-4' }),
  
  contributes: {
    mainTabs: [
      {
        id: 'test-extension',
        title: 'Test Extension',
        icon: React.createElement(TestTube, { className: 'h-4 w-4' }),
        component: TestExtensionComponent,
        order: 100 // Put it at the end
      }
    ],
    
    dashboardWidgets: [
      {
        id: 'test-widget',
        title: 'Test Widget',
        description: 'A test widget to verify dashboard integration',
        icon: React.createElement(Zap, { className: 'h-4 w-4' }),
        component: TestDashboardWidget,
        size: 'small',
        order: 100
      }
    ],

    aiTools: [
      {
        id: 'test-ai-tool',
        name: 'Test AI Tool',
        description: 'A test AI tool that echoes back the input',
        category: 'other',
        handler: async (parameters, context) => {
          const { message } = parameters;
          
          return {
            success: true,
            data: { 
              echo: message,
              timestamp: new Date().toISOString(),
              projectId: context.projectId
            },
            message: `Test tool executed successfully. Echo: "${message}"`
          };
        },
        parameters: [
          {
            name: 'message',
            type: 'string',
            description: 'Message to echo back',
            required: true,
            default: 'Hello from test extension!'
          }
        ]
      }
    ],

    headerActions: [
      {
        id: 'test-action',
        title: 'Test Action',
        icon: React.createElement(TestTube, { className: 'h-4 w-4' }),
        action: () => {
          console.log('Test header action clicked!');
          alert('Test header action works!');
        },
        order: 100
      }
    ]
  },

  async activate(context) {
    console.log('Test Extension activated for project:', context.projectId);
    
    // Show activation notification
    context.ui.showToast(
      'Test Extension', 
      'Extension activated successfully!', 
      'success'
    );

    // Set status bar text
    context.ui.setStatusBarText('Test Extension Active');

    // Log workspace state
    const state = context.workspace.getWorkspaceState();
    console.log('Current workspace state:', state);
  },

  async deactivate() {
    console.log('Test Extension deactivated');
  }
};

/**
 * Function to register the test extension
 */
export async function registerTestExtension(extensionManager: any, projectId: string) {
  try {
    await extensionManager.registerExtension(TestExtension, projectId);
    await extensionManager.activateExtension(TestExtension.id);
    console.log('Test extension registered and activated successfully');
  } catch (error) {
    console.error('Failed to register test extension:', error);
  }
}
