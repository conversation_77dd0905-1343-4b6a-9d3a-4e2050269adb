/**
 * Workspace Extension System Types
 * 
 * Type definitions for the AI Node.js workspace extension system
 */

import { ReactNode } from 'react';
import { TabData } from '@/components/ui/tabs-layout/types';
import { NodeboxInstance } from '@/lib/nodebox-runtime/api/nodebox-types';

/**
 * Main extension interface
 */
export interface WorkspaceExtension {
  id: string;
  name: string;
  displayName: string;
  description: string;
  version: string;
  author: string;
  
  // Extension metadata
  icon?: ReactNode;
  category: ExtensionCategory;
  tags?: string[];
  
  // What this extension contributes to the workspace
  contributes: ExtensionContributions;
  
  // Extension lifecycle
  activate(context: WorkspaceExtensionContext): Promise<void>;
  deactivate?(): Promise<void>;
  
  // Extension settings
  settings?: ExtensionSettings;
}

/**
 * Extension categories
 */
export type ExtensionCategory = 
  | 'ai-tools'
  | 'development'
  | 'database'
  | 'visualization'
  | 'productivity'
  | 'debugging'
  | 'testing'
  | 'deployment'
  | 'monitoring'
  | 'other';

/**
 * Extension contribution points
 */
export interface ExtensionContributions {
  // Main workspace tabs
  mainTabs?: MainTabContribution[];
  
  // Panel tabs
  leftPanelTabs?: PanelTabContribution[];
  rightPanelTabs?: PanelTabContribution[];
  bottomPanelTabs?: PanelTabContribution[];
  
  // Dashboard widgets
  dashboardWidgets?: DashboardWidgetContribution[];
  
  // AI assistant tools
  aiTools?: AIToolContribution[];
  
  // Nodebox extensions
  nodeboxTools?: NodeboxToolContribution[];
  
  // Header actions
  headerActions?: HeaderActionContribution[];
  
  // Context menu items
  contextMenus?: ContextMenuContribution[];
  
  // Commands
  commands?: CommandContribution[];
}

/**
 * Main tab contribution
 */
export interface MainTabContribution {
  id: string;
  title: string;
  icon: ReactNode;
  component: React.ComponentType<MainTabProps>;
  order?: number;
  when?: string; // Conditional expression
  closable?: boolean;
  pinnable?: boolean;
}

/**
 * Panel tab contribution
 */
export interface PanelTabContribution {
  id: string;
  title: string;
  icon: ReactNode;
  component: React.ComponentType<PanelTabProps>;
  order?: number;
  when?: string;
  closable?: boolean;
  pinnable?: boolean;
}

/**
 * Dashboard widget contribution
 */
export interface DashboardWidgetContribution {
  id: string;
  title: string;
  description?: string;
  icon?: ReactNode;
  component: React.ComponentType<DashboardWidgetProps>;
  size: 'small' | 'medium' | 'large';
  order?: number;
  when?: string;
}

/**
 * AI tool contribution
 */
export interface AIToolContribution {
  id: string;
  name: string;
  description: string;
  icon?: ReactNode;
  category: 'code' | 'analysis' | 'generation' | 'debugging' | 'other';
  handler: AIToolHandler;
  parameters?: AIToolParameter[];
}

/**
 * Nodebox tool contribution
 */
export interface NodeboxToolContribution {
  id: string;
  name: string;
  description: string;
  command: string;
  args?: string[];
  category: 'build' | 'test' | 'deploy' | 'debug' | 'other';
  when?: string;
}

/**
 * Header action contribution
 */
export interface HeaderActionContribution {
  id: string;
  title: string;
  icon: ReactNode;
  action: () => void;
  order?: number;
  when?: string;
}

/**
 * Context menu contribution
 */
export interface ContextMenuContribution {
  id: string;
  title: string;
  icon?: ReactNode;
  action: (context: ContextMenuContext) => void;
  when?: string;
  group?: string;
}

/**
 * Command contribution
 */
export interface CommandContribution {
  id: string;
  title: string;
  description?: string;
  handler: CommandHandler;
  keybinding?: string;
  when?: string;
}

/**
 * Extension context provided to extensions
 */
export interface WorkspaceExtensionContext {
  // Current workspace state
  projectId: string;
  activeInstance?: NodeboxInstance;
  
  // Workspace APIs
  workspace: WorkspaceAPI;
  ui: WorkspaceUIAPI;
  nodebox: NodeboxAPI;
  ai: AIAssistantAPI;
  
  // Extension storage
  storage: ExtensionStorage;
  
  // Event system
  events: ExtensionEventEmitter;
  
  // Utilities
  utils: ExtensionUtils;
}

/**
 * Component props for main tabs
 */
export interface MainTabProps {
  projectId: string;
  context: WorkspaceExtensionContext;
}

/**
 * Component props for panel tabs
 */
export interface PanelTabProps {
  projectId: string;
  context: WorkspaceExtensionContext;
  panelId: 'left' | 'right' | 'bottom';
}

/**
 * Component props for dashboard widgets
 */
export interface DashboardWidgetProps {
  projectId: string;
  context: WorkspaceExtensionContext;
}

/**
 * AI tool handler function
 */
export type AIToolHandler = (
  parameters: Record<string, any>,
  context: WorkspaceExtensionContext
) => Promise<AIToolResult>;

/**
 * AI tool parameter definition
 */
export interface AIToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  required?: boolean;
  default?: any;
}

/**
 * AI tool result
 */
export interface AIToolResult {
  success: boolean;
  data?: any;
  message?: string;
  error?: string;
}

/**
 * Command handler function
 */
export type CommandHandler = (
  args?: any[],
  context?: WorkspaceExtensionContext
) => Promise<void>;

/**
 * Context menu context
 */
export interface ContextMenuContext {
  type: 'tab' | 'panel' | 'dashboard' | 'editor';
  target: any;
  position: { x: number; y: number };
}

/**
 * Extension settings
 */
export interface ExtensionSettings {
  [key: string]: ExtensionSetting;
}

export interface ExtensionSetting {
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect';
  title: string;
  description?: string;
  default: any;
  options?: { label: string; value: any }[];
  validation?: (value: any) => boolean | string;
}

/**
 * Extension storage interface
 */
export interface ExtensionStorage {
  get<T>(key: string): Promise<T | undefined>;
  set<T>(key: string, value: T): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  keys(): Promise<string[]>;
}

/**
 * Extension event emitter
 */
export interface ExtensionEventEmitter {
  on(event: string, listener: (...args: any[]) => void): void;
  off(event: string, listener: (...args: any[]) => void): void;
  emit(event: string, ...args: any[]): void;
}

/**
 * Extension utilities
 */
export interface ExtensionUtils {
  generateId(): string;
  debounce<T extends (...args: any[]) => any>(fn: T, delay: number): T;
  throttle<T extends (...args: any[]) => any>(fn: T, delay: number): T;
  formatBytes(bytes: number): string;
  formatDuration(ms: number): string;
}
