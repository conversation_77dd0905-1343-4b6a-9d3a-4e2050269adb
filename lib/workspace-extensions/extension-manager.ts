/**
 * Workspace Extension Manager
 * 
 * Core manager for the workspace extension system
 */

import { EventEmitter } from 'events';
import { 
  WorkspaceExtension, 
  WorkspaceExtensionContext,
  ExtensionContributions,
  MainTabContribution,
  PanelTabContribution,
  DashboardWidgetContribution,
  AIToolContribution,
  HeaderActionContribution,
  CommandContribution
} from './types';
import { createExtensionContext } from './context';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('extension-manager');

/**
 * Extension manager events
 */
export enum ExtensionManagerEvent {
  EXTENSION_REGISTERED = 'extension:registered',
  EXTENSION_ACTIVATED = 'extension:activated',
  EXTENSION_DEACTIVATED = 'extension:deactivated',
  EXTENSION_ERROR = 'extension:error',
  CONTRIBUTIONS_CHANGED = 'contributions:changed'
}

/**
 * Extension registry entry
 */
interface ExtensionRegistryEntry {
  extension: WorkspaceExtension;
  context: WorkspaceExtensionContext;
  isActive: boolean;
  activatedAt?: Date;
  error?: Error;
}

/**
 * Extension manager configuration
 */
export interface ExtensionManagerConfig {
  maxExtensions: number;
  enableHotReload: boolean;
  developmentMode: boolean;
  extensionStoragePath: string;
}

/**
 * Workspace Extension Manager
 * 
 * Manages the lifecycle of workspace extensions and their contributions
 */
export class WorkspaceExtensionManager extends EventEmitter {
  private extensions: Map<string, ExtensionRegistryEntry> = new Map();
  private contributions: ExtensionContributions = {};
  private config: ExtensionManagerConfig;
  private initialized: boolean = false;

  constructor(config: Partial<ExtensionManagerConfig> = {}) {
    super();
    
    this.config = {
      maxExtensions: 50,
      enableHotReload: true,
      developmentMode: process.env.NODE_ENV === 'development',
      extensionStoragePath: '/tmp/workspace-extensions',
      ...config
    };
  }

  /**
   * Initialize the extension manager
   */
  async initialize(projectId: string): Promise<void> {
    if (this.initialized) {
      return;
    }

    logger.info('Initializing workspace extension manager', { projectId });

    try {
      // Initialize storage and other resources
      await this.initializeStorage();
      
      this.initialized = true;
      logger.info('Workspace extension manager initialized');
    } catch (error) {
      logger.error('Failed to initialize extension manager:', error);
      throw error;
    }
  }

  /**
   * Register an extension
   */
  async registerExtension(extension: WorkspaceExtension, projectId: string): Promise<void> {
    try {
      // Check if extension is already registered
      if (this.extensions.has(extension.id)) {
        throw new Error(`Extension ${extension.id} is already registered`);
      }

      // Check extension limit
      if (this.extensions.size >= this.config.maxExtensions) {
        throw new Error(`Maximum number of extensions (${this.config.maxExtensions}) reached`);
      }

      // Validate extension
      this.validateExtension(extension);

      // Create extension context
      const context = await createExtensionContext(extension, projectId, this);

      // Create registry entry
      const entry: ExtensionRegistryEntry = {
        extension,
        context,
        isActive: false
      };

      // Add to registry
      this.extensions.set(extension.id, entry);

      // Emit registration event
      this.emit(ExtensionManagerEvent.EXTENSION_REGISTERED, { 
        extensionId: extension.id, 
        extension 
      });

      logger.info(`Extension registered: ${extension.id}`);
    } catch (error) {
      logger.error(`Failed to register extension ${extension.id}:`, error);
      throw error;
    }
  }

  /**
   * Activate an extension
   */
  async activateExtension(extensionId: string): Promise<void> {
    const entry = this.extensions.get(extensionId);
    if (!entry) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    if (entry.isActive) {
      return;
    }

    logger.info(`Activating extension: ${extensionId}`);

    try {
      // Call extension's activate function
      await entry.extension.activate(entry.context);

      // Register contributions
      this.registerContributions(extensionId, entry.extension.contributes);

      // Update registry entry
      entry.isActive = true;
      entry.activatedAt = new Date();
      entry.error = undefined;

      // Emit activation event
      this.emit(ExtensionManagerEvent.EXTENSION_ACTIVATED, { 
        extensionId, 
        extension: entry.extension 
      });

      logger.info(`Extension activated: ${extensionId}`);
    } catch (error) {
      entry.error = error as Error;
      
      this.emit(ExtensionManagerEvent.EXTENSION_ERROR, { 
        extensionId, 
        error 
      });
      
      logger.error(`Failed to activate extension ${extensionId}:`, error);
      throw error;
    }
  }

  /**
   * Deactivate an extension
   */
  async deactivateExtension(extensionId: string): Promise<void> {
    const entry = this.extensions.get(extensionId);
    if (!entry || !entry.isActive) {
      return;
    }

    logger.info(`Deactivating extension: ${extensionId}`);

    try {
      // Call extension's deactivate function if it exists
      if (entry.extension.deactivate) {
        await entry.extension.deactivate();
      }

      // Unregister contributions
      this.unregisterContributions(extensionId);

      // Update registry entry
      entry.isActive = false;
      entry.activatedAt = undefined;
      entry.error = undefined;

      // Emit deactivation event
      this.emit(ExtensionManagerEvent.EXTENSION_DEACTIVATED, { 
        extensionId, 
        extension: entry.extension 
      });

      logger.info(`Extension deactivated: ${extensionId}`);
    } catch (error) {
      logger.error(`Failed to deactivate extension ${extensionId}:`, error);
      throw error;
    }
  }

  /**
   * Unregister an extension
   */
  async unregisterExtension(extensionId: string): Promise<void> {
    const entry = this.extensions.get(extensionId);
    if (!entry) {
      return;
    }

    // Deactivate if active
    if (entry.isActive) {
      await this.deactivateExtension(extensionId);
    }

    // Remove from registry
    this.extensions.delete(extensionId);

    logger.info(`Extension unregistered: ${extensionId}`);
  }

  /**
   * Get all registered extensions
   */
  getAllExtensions(): WorkspaceExtension[] {
    return Array.from(this.extensions.values()).map(entry => entry.extension);
  }

  /**
   * Get active extensions
   */
  getActiveExtensions(): WorkspaceExtension[] {
    return Array.from(this.extensions.values())
      .filter(entry => entry.isActive)
      .map(entry => entry.extension);
  }

  /**
   * Get extension by ID
   */
  getExtension(extensionId: string): WorkspaceExtension | undefined {
    return this.extensions.get(extensionId)?.extension;
  }

  /**
   * Get extension context
   */
  getExtensionContext(extensionId: string): WorkspaceExtensionContext | undefined {
    return this.extensions.get(extensionId)?.context;
  }

  /**
   * Get all contributions
   */
  getContributions(): ExtensionContributions {
    return { ...this.contributions };
  }

  /**
   * Get contributions by type
   */
  getMainTabContributions(): MainTabContribution[] {
    return this.contributions.mainTabs || [];
  }

  getPanelTabContributions(panelId: 'left' | 'right' | 'bottom'): PanelTabContribution[] {
    switch (panelId) {
      case 'left': return this.contributions.leftPanelTabs || [];
      case 'right': return this.contributions.rightPanelTabs || [];
      case 'bottom': return this.contributions.bottomPanelTabs || [];
    }
  }

  getDashboardWidgetContributions(): DashboardWidgetContribution[] {
    return this.contributions.dashboardWidgets || [];
  }

  getAIToolContributions(): AIToolContribution[] {
    return this.contributions.aiTools || [];
  }

  getHeaderActionContributions(): HeaderActionContribution[] {
    return this.contributions.headerActions || [];
  }

  getCommandContributions(): CommandContribution[] {
    return this.contributions.commands || [];
  }

  /**
   * Register contributions from an extension
   */
  private registerContributions(extensionId: string, contributions: ExtensionContributions): void {
    // Add extension ID to each contribution for tracking
    const addExtensionId = <T extends { id: string }>(items: T[] = []): (T & { extensionId: string })[] => {
      return items.map(item => ({ ...item, extensionId }));
    };

    // Register main tabs
    if (contributions.mainTabs) {
      this.contributions.mainTabs = [
        ...(this.contributions.mainTabs || []),
        ...addExtensionId(contributions.mainTabs)
      ];
    }

    // Register panel tabs
    if (contributions.leftPanelTabs) {
      this.contributions.leftPanelTabs = [
        ...(this.contributions.leftPanelTabs || []),
        ...addExtensionId(contributions.leftPanelTabs)
      ];
    }

    if (contributions.rightPanelTabs) {
      this.contributions.rightPanelTabs = [
        ...(this.contributions.rightPanelTabs || []),
        ...addExtensionId(contributions.rightPanelTabs)
      ];
    }

    if (contributions.bottomPanelTabs) {
      this.contributions.bottomPanelTabs = [
        ...(this.contributions.bottomPanelTabs || []),
        ...addExtensionId(contributions.bottomPanelTabs)
      ];
    }

    // Register other contributions
    if (contributions.dashboardWidgets) {
      this.contributions.dashboardWidgets = [
        ...(this.contributions.dashboardWidgets || []),
        ...addExtensionId(contributions.dashboardWidgets)
      ];
    }

    if (contributions.aiTools) {
      this.contributions.aiTools = [
        ...(this.contributions.aiTools || []),
        ...addExtensionId(contributions.aiTools)
      ];
    }

    if (contributions.headerActions) {
      this.contributions.headerActions = [
        ...(this.contributions.headerActions || []),
        ...addExtensionId(contributions.headerActions)
      ];
    }

    if (contributions.commands) {
      this.contributions.commands = [
        ...(this.contributions.commands || []),
        ...addExtensionId(contributions.commands)
      ];
    }

    // Emit contributions changed event
    this.emit(ExtensionManagerEvent.CONTRIBUTIONS_CHANGED, this.contributions);
  }

  /**
   * Unregister contributions from an extension
   */
  private unregisterContributions(extensionId: string): void {
    const filterByExtension = <T extends { extensionId?: string }>(items: T[] = []): T[] => {
      return items.filter(item => item.extensionId !== extensionId);
    };

    this.contributions.mainTabs = filterByExtension(this.contributions.mainTabs);
    this.contributions.leftPanelTabs = filterByExtension(this.contributions.leftPanelTabs);
    this.contributions.rightPanelTabs = filterByExtension(this.contributions.rightPanelTabs);
    this.contributions.bottomPanelTabs = filterByExtension(this.contributions.bottomPanelTabs);
    this.contributions.dashboardWidgets = filterByExtension(this.contributions.dashboardWidgets);
    this.contributions.aiTools = filterByExtension(this.contributions.aiTools);
    this.contributions.headerActions = filterByExtension(this.contributions.headerActions);
    this.contributions.commands = filterByExtension(this.contributions.commands);

    // Emit contributions changed event
    this.emit(ExtensionManagerEvent.CONTRIBUTIONS_CHANGED, this.contributions);
  }

  /**
   * Validate extension
   */
  private validateExtension(extension: WorkspaceExtension): void {
    if (!extension.id) {
      throw new Error('Extension must have an ID');
    }
    if (!extension.name) {
      throw new Error('Extension must have a name');
    }
    if (!extension.version) {
      throw new Error('Extension must have a version');
    }
    if (typeof extension.activate !== 'function') {
      throw new Error('Extension must have an activate function');
    }
  }

  /**
   * Initialize storage
   */
  private async initializeStorage(): Promise<void> {
    // Initialize extension storage
    // This would typically involve setting up file system or database storage
    logger.debug('Extension storage initialized');
  }
}

// Global extension manager instance
let globalExtensionManager: WorkspaceExtensionManager | null = null;

/**
 * Get the global extension manager
 */
export function getExtensionManager(): WorkspaceExtensionManager {
  if (!globalExtensionManager) {
    globalExtensionManager = new WorkspaceExtensionManager();
  }
  return globalExtensionManager;
}

/**
 * Initialize the global extension manager
 */
export async function initializeExtensionManager(
  projectId: string,
  config?: Partial<ExtensionManagerConfig>
): Promise<WorkspaceExtensionManager> {
  const manager = getExtensionManager();
  if (config) {
    Object.assign(manager['config'], config);
  }
  await manager.initialize(projectId);
  return manager;
}
