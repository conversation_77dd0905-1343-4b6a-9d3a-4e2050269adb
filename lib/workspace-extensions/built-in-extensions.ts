/**
 * Built-in Extensions Registry
 * 
 * Registry of built-in extensions for the workspace
 */

import React from 'react';
import { Database, FileText, Bug, Zap, Monitor } from 'lucide-react';
import { WorkspaceExtension } from './types';
import { WorkspaceExtensionManager } from './extension-manager';
import { createNamespacedLogger } from '@/lib/logger';

const logger = createNamespacedLogger('built-in-extensions');

/**
 * Sample Database Viewer Extension
 */
const DatabaseViewerExtension: WorkspaceExtension = {
  id: 'workspace.database-viewer',
  name: 'database-viewer',
  displayName: 'Database Viewer',
  description: 'View and manage database connections and schemas',
  version: '1.0.0',
  author: 'Workspace Team',
  category: 'database',
  icon: React.createElement(Database, { className: 'h-4 w-4' }),
  
  contributes: {
    mainTabs: [
      {
        id: 'database',
        title: 'Database',
        icon: React.createElement(Database, { className: 'h-4 w-4' }),
        component: ({ projectId, context }) => {
          return React.createElement('div', {
            className: 'p-4 h-full flex items-center justify-center'
          }, [
            React.createElement('div', {
              key: 'content',
              className: 'text-center'
            }, [
              React.createElement(Database, {
                key: 'icon',
                className: 'h-12 w-12 mx-auto mb-4 text-muted-foreground'
              }),
              React.createElement('h3', {
                key: 'title',
                className: 'text-lg font-medium mb-2'
              }, 'Database Viewer'),
              React.createElement('p', {
                key: 'description',
                className: 'text-muted-foreground'
              }, `Database management for project: ${projectId}`)
            ])
          ]);
        },
        order: 10
      }
    ],
    
    rightPanelTabs: [
      {
        id: 'db-schema',
        title: 'Schema',
        icon: React.createElement(Database, { className: 'h-4 w-4' }),
        component: ({ projectId }) => {
          return React.createElement('div', {
            className: 'p-4'
          }, [
            React.createElement('h4', {
              key: 'title',
              className: 'font-medium mb-2'
            }, 'Database Schema'),
            React.createElement('p', {
              key: 'content',
              className: 'text-sm text-muted-foreground'
            }, 'Schema explorer will appear here')
          ]);
        },
        order: 5
      }
    ],

    aiTools: [
      {
        id: 'generate-sql',
        name: 'Generate SQL Query',
        description: 'Generate SQL queries from natural language',
        category: 'code',
        handler: async (parameters, context) => {
          const { prompt } = parameters;
          
          // Mock SQL generation
          const sql = `-- Generated SQL for: ${prompt}\nSELECT * FROM users WHERE active = true;`;
          
          return {
            success: true,
            data: { sql },
            message: 'SQL query generated successfully'
          };
        },
        parameters: [
          {
            name: 'prompt',
            type: 'string',
            description: 'Natural language description of the query',
            required: true
          }
        ]
      }
    ]
  },

  async activate(context) {
    logger.info('Database Viewer extension activated');
    
    // Add a main tab
    context.workspace.addMainTab({
      id: 'database',
      title: 'Database',
      icon: React.createElement(Database, { className: 'h-4 w-4' }),
      content: React.createElement('div', {
        className: 'p-4 h-full flex items-center justify-center text-center'
      }, [
        React.createElement(Database, {
          key: 'icon',
          className: 'h-12 w-12 mx-auto mb-4 text-muted-foreground'
        }),
        React.createElement('h3', {
          key: 'title',
          className: 'text-lg font-medium'
        }, 'Database Viewer Active')
      ])
    });

    // Show notification
    context.ui.showToast('Database Viewer', 'Extension activated successfully', 'success');
  },

  async deactivate() {
    logger.info('Database Viewer extension deactivated');
  }
};

/**
 * Documentation Generator Extension
 */
const DocumentationGeneratorExtension: WorkspaceExtension = {
  id: 'workspace.docs-generator',
  name: 'docs-generator',
  displayName: 'Documentation Generator',
  description: 'Generate and manage project documentation',
  version: '1.0.0',
  author: 'Workspace Team',
  category: 'productivity',
  icon: React.createElement(FileText, { className: 'h-4 w-4' }),
  
  contributes: {
    dashboardWidgets: [
      {
        id: 'docs-status',
        title: 'Documentation Status',
        description: 'Overview of project documentation',
        icon: React.createElement(FileText, { className: 'h-4 w-4' }),
        component: ({ projectId }) => {
          return React.createElement('div', {
            className: 'p-4 border rounded-lg'
          }, [
            React.createElement('div', {
              key: 'header',
              className: 'flex items-center gap-2 mb-3'
            }, [
              React.createElement(FileText, {
                key: 'icon',
                className: 'h-5 w-5'
              }),
              React.createElement('h4', {
                key: 'title',
                className: 'font-medium'
              }, 'Documentation')
            ]),
            React.createElement('div', {
              key: 'content',
              className: 'space-y-2'
            }, [
              React.createElement('div', {
                key: 'coverage',
                className: 'flex justify-between text-sm'
              }, [
                React.createElement('span', { key: 'label' }, 'Coverage'),
                React.createElement('span', { key: 'value', className: 'font-medium' }, '75%')
              ]),
              React.createElement('div', {
                key: 'files',
                className: 'flex justify-between text-sm'
              }, [
                React.createElement('span', { key: 'label' }, 'Files'),
                React.createElement('span', { key: 'value', className: 'font-medium' }, '12/16')
              ])
            ])
          ]);
        },
        size: 'medium',
        order: 5
      }
    ],

    headerActions: [
      {
        id: 'generate-docs',
        title: 'Generate Docs',
        icon: React.createElement(FileText, { className: 'h-4 w-4' }),
        action: () => {
          console.log('Generating documentation...');
        },
        order: 10
      }
    ]
  },

  async activate(context) {
    logger.info('Documentation Generator extension activated');
    context.ui.showToast('Documentation Generator', 'Extension activated', 'success');
  }
};

/**
 * Performance Monitor Extension
 */
const PerformanceMonitorExtension: WorkspaceExtension = {
  id: 'workspace.performance-monitor',
  name: 'performance-monitor',
  displayName: 'Performance Monitor',
  description: 'Monitor application performance and metrics',
  version: '1.0.0',
  author: 'Workspace Team',
  category: 'monitoring',
  icon: React.createElement(Monitor, { className: 'h-4 w-4' }),
  
  contributes: {
    bottomPanelTabs: [
      {
        id: 'performance',
        title: 'Performance',
        icon: React.createElement(Monitor, { className: 'h-4 w-4' }),
        component: ({ projectId }) => {
          return React.createElement('div', {
            className: 'p-4'
          }, [
            React.createElement('h4', {
              key: 'title',
              className: 'font-medium mb-4'
            }, 'Performance Metrics'),
            React.createElement('div', {
              key: 'metrics',
              className: 'grid grid-cols-3 gap-4'
            }, [
              React.createElement('div', {
                key: 'cpu',
                className: 'text-center p-3 border rounded'
              }, [
                React.createElement('div', {
                  key: 'value',
                  className: 'text-2xl font-bold'
                }, '45%'),
                React.createElement('div', {
                  key: 'label',
                  className: 'text-sm text-muted-foreground'
                }, 'CPU Usage')
              ]),
              React.createElement('div', {
                key: 'memory',
                className: 'text-center p-3 border rounded'
              }, [
                React.createElement('div', {
                  key: 'value',
                  className: 'text-2xl font-bold'
                }, '2.1GB'),
                React.createElement('div', {
                  key: 'label',
                  className: 'text-sm text-muted-foreground'
                }, 'Memory')
              ]),
              React.createElement('div', {
                key: 'response',
                className: 'text-center p-3 border rounded'
              }, [
                React.createElement('div', {
                  key: 'value',
                  className: 'text-2xl font-bold'
                }, '120ms'),
                React.createElement('div', {
                  key: 'label',
                  className: 'text-sm text-muted-foreground'
                }, 'Response Time')
              ])
            ])
          ]);
        },
        order: 5
      }
    ]
  },

  async activate(context) {
    logger.info('Performance Monitor extension activated');
    context.ui.showToast('Performance Monitor', 'Extension activated', 'success');
  }
};

/**
 * Built-in extensions registry
 */
const BUILT_IN_EXTENSIONS: WorkspaceExtension[] = [
  DatabaseViewerExtension,
  DocumentationGeneratorExtension,
  PerformanceMonitorExtension
];

/**
 * Register all built-in extensions
 */
export async function registerBuiltInExtensions(
  extensionManager: WorkspaceExtensionManager,
  projectId: string
): Promise<void> {
  logger.info('Registering built-in extensions');

  for (const extension of BUILT_IN_EXTENSIONS) {
    try {
      await extensionManager.registerExtension(extension, projectId);
      await extensionManager.activateExtension(extension.id);
      logger.debug(`Registered and activated built-in extension: ${extension.id}`);
    } catch (error) {
      logger.error(`Failed to register built-in extension ${extension.id}:`, error);
    }
  }

  logger.info(`Registered ${BUILT_IN_EXTENSIONS.length} built-in extensions`);
}

/**
 * Get all built-in extensions
 */
export function getBuiltInExtensions(): WorkspaceExtension[] {
  return [...BUILT_IN_EXTENSIONS];
}
