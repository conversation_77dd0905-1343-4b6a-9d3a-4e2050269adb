/**
 * Extension Context Creation
 * 
 * Creates the context object that extensions receive
 */

import { EventEmitter } from 'events';
import { 
  WorkspaceExtension, 
  WorkspaceExtensionContext,
  ExtensionStorage,
  ExtensionEventEmitter,
  ExtensionUtils
} from './types';
import { 
  WorkspaceAPI, 
  WorkspaceUIAPI, 
  NodeboxAPI, 
  AIAssistantAPI 
} from './api';
import { createWorkspaceAP<PERSON> } from './workspace-api';
import { createWorkspaceUIAP<PERSON> } from './ui-api';
import { createNodeboxAPI } from './nodebox-api';
import { createAIAssistantAPI } from './ai-api';
import { createNamespacedLogger } from '@/lib/logger';

const logger = createNamespacedLogger('extension-context');

/**
 * Create extension context for an extension
 */
export async function createExtensionContext(
  extension: WorkspaceExtension,
  projectId: string,
  extensionManager: any
): Promise<WorkspaceExtensionContext> {
  logger.debug(`Creating context for extension: ${extension.id}`);

  // Create APIs
  const workspace = createWorkspaceAPI(projectId, extensionManager);
  const ui = createWorkspaceUIAPI(extensionManager);
  const nodebox = createNodeboxAPI(projectId);
  const ai = createAIAssistantAPI(extensionManager);

  // Create storage
  const storage = createExtensionStorage(extension.id);

  // Create event emitter
  const events = createExtensionEventEmitter(extension.id);

  // Create utilities
  const utils = createExtensionUtils();

  const context: WorkspaceExtensionContext = {
    projectId,
    workspace,
    ui,
    nodebox,
    ai,
    storage,
    events,
    utils
  };

  return context;
}

/**
 * Create extension storage
 */
function createExtensionStorage(extensionId: string): ExtensionStorage {
  // In-memory storage for now - could be replaced with persistent storage
  const storage = new Map<string, any>();
  const storageKey = (key: string) => `ext_${extensionId}_${key}`;

  return {
    async get<T>(key: string): Promise<T | undefined> {
      try {
        const value = storage.get(storageKey(key));
        return value as T;
      } catch (error) {
        logger.error(`Failed to get storage value for key ${key}:`, error);
        return undefined;
      }
    },

    async set<T>(key: string, value: T): Promise<void> {
      try {
        storage.set(storageKey(key), value);
      } catch (error) {
        logger.error(`Failed to set storage value for key ${key}:`, error);
        throw error;
      }
    },

    async delete(key: string): Promise<void> {
      try {
        storage.delete(storageKey(key));
      } catch (error) {
        logger.error(`Failed to delete storage value for key ${key}:`, error);
        throw error;
      }
    },

    async clear(): Promise<void> {
      try {
        const keysToDelete = Array.from(storage.keys()).filter(k => 
          k.startsWith(`ext_${extensionId}_`)
        );
        keysToDelete.forEach(key => storage.delete(key));
      } catch (error) {
        logger.error(`Failed to clear storage for extension ${extensionId}:`, error);
        throw error;
      }
    },

    async keys(): Promise<string[]> {
      try {
        const prefix = `ext_${extensionId}_`;
        return Array.from(storage.keys())
          .filter(k => k.startsWith(prefix))
          .map(k => k.substring(prefix.length));
      } catch (error) {
        logger.error(`Failed to get storage keys for extension ${extensionId}:`, error);
        return [];
      }
    }
  };
}

/**
 * Create extension event emitter
 */
function createExtensionEventEmitter(extensionId: string): ExtensionEventEmitter {
  const emitter = new EventEmitter();
  
  return {
    on(event: string, listener: (...args: any[]) => void): void {
      emitter.on(event, listener);
    },

    off(event: string, listener: (...args: any[]) => void): void {
      emitter.off(event, listener);
    },

    emit(event: string, ...args: any[]): void {
      emitter.emit(event, ...args);
      // Also emit to global extension event bus if needed
      logger.debug(`Extension ${extensionId} emitted event: ${event}`);
    }
  };
}

/**
 * Create extension utilities
 */
function createExtensionUtils(): ExtensionUtils {
  return {
    generateId(): string {
      return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    },

    debounce<T extends (...args: any[]) => any>(fn: T, delay: number): T {
      let timeoutId: NodeJS.Timeout;
      return ((...args: any[]) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => fn(...args), delay);
      }) as T;
    },

    throttle<T extends (...args: any[]) => any>(fn: T, delay: number): T {
      let lastCall = 0;
      return ((...args: any[]) => {
        const now = Date.now();
        if (now - lastCall >= delay) {
          lastCall = now;
          return fn(...args);
        }
      }) as T;
    },

    formatBytes(bytes: number): string {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    formatDuration(ms: number): string {
      if (ms < 1000) return `${ms}ms`;
      if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
      if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
      return `${(ms / 3600000).toFixed(1)}h`;
    }
  };
}

/**
 * Update extension context with active instance
 */
export function updateExtensionContextWithInstance(
  context: WorkspaceExtensionContext,
  activeInstance: any
): void {
  context.activeInstance = activeInstance;
  logger.debug(`Updated extension context with active instance: ${activeInstance?.id}`);
}
