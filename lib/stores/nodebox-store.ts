"use client";

/**
 * Nodebox Store
 *
 * Zustand store for managing Nodebox runtime instances and their actions
 * Provides centralized state management for the AI Node.js workspace layout
 */

import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { useEffect, useMemo } from 'react';
import { useFileStore } from '@/lib/stores/file-store';
// Remove unused imports
import {
  NodeboxInstance,
  NodeboxConfig,
  NodeboxError,
  ProjectTemplate,
  FileEntry,
  ShellProcess,
  PreviewInfo,
  TemplateConfig,
  NodeboxSettings
} from '@/lib/nodebox-runtime/api/nodebox-types';
import { NodeboxManager } from '@/lib/nodebox-runtime/core/nodebox-manager';
import { NodeboxClient } from '@/lib/nodebox-runtime/api/nodebox-client';

// Store state interface
interface NodeboxState {
  // Core state
  instances: NodeboxInstance[];
  activeInstanceId: string | null;
  isLoading: boolean;
  error: NodeboxError | null;
  
  // Runtime management
  manager: NodeboxManager | null;
  client: NodeboxClient | null;
  clientMode: boolean;
  
  // File system state
  fileSystem: Record<string, FileEntry[]>; // instanceId -> files
  activeFile: { instanceId: string; path: string; content: string } | null;
  
  // Shell/Terminal state
  processes: Record<string, ShellProcess[]>; // instanceId -> processes
  activeProcess: { instanceId: string; processId: string } | null;
  terminalOutput: Record<string, string[]>; // processId -> output lines
  
  // Preview state
  previews: Record<string, PreviewInfo[]>; // instanceId -> previews
  activePreview: { instanceId: string; previewId: string } | null;
  
  // Template management
  availableTemplates: Record<ProjectTemplate, TemplateConfig>;
  
  // Settings
  globalSettings: NodeboxSettings;
  instanceSettings: Record<string, NodeboxSettings>; // instanceId -> settings
}

// Store actions interface
interface NodeboxActions {
  // Instance management
  initializeRuntime: (clientMode?: boolean, baseUrl?: string) => Promise<void>;
  createInstance: (config: Omit<NodeboxConfig, 'instanceId'>) => Promise<NodeboxInstance | null>;
  destroyInstance: (instanceId: string) => Promise<boolean>;
  setActiveInstance: (instanceId: string | null) => void;
  refreshInstances: () => Promise<void>;
  
  // Template helpers
  createFromTemplate: (template: ProjectTemplate, name: string, projectId?: string) => Promise<NodeboxInstance | null>;
  loadTemplate: (template: ProjectTemplate) => TemplateConfig | null;
  
  // File system operations
  loadFileSystem: (instanceId: string) => Promise<void>;
  readFile: (instanceId: string, path: string) => Promise<string | null>;
  writeFile: (instanceId: string, path: string, content: string) => Promise<boolean>;
  createFile: (instanceId: string, path: string, content?: string) => Promise<boolean>;
  deleteFile: (instanceId: string, path: string) => Promise<boolean>;
  createDirectory: (instanceId: string, path: string) => Promise<boolean>;
  setActiveFile: (instanceId: string, path: string, content: string) => void;
  
  // Shell/Terminal operations
  runCommand: (instanceId: string, command: string, args?: string[]) => Promise<ShellProcess | null>;
  killProcess: (instanceId: string, processId: string) => Promise<boolean>;
  setActiveProcess: (instanceId: string, processId: string) => void;
  appendTerminalOutput: (processId: string, output: string) => void;
  clearTerminalOutput: (processId: string) => void;
  
  // Preview operations
  getPreview: (instanceId: string, processId?: string) => Promise<PreviewInfo | null>;
  refreshPreviews: (instanceId: string) => Promise<void>;
  setActivePreview: (instanceId: string, previewId: string) => void;
  
  // Settings management
  updateGlobalSettings: (settings: Partial<NodeboxSettings>) => void;
  updateInstanceSettings: (instanceId: string, settings: Partial<NodeboxSettings>) => void;
  
  // Error handling
  setError: (error: NodeboxError | null) => void;
  clearError: () => void;
  
  // Utility actions
  getActiveInstance: () => NodeboxInstance | null;
  getInstanceById: (instanceId: string) => NodeboxInstance | null;
  cleanup: () => void;
}

// Combined store type
type NodeboxStore = NodeboxState & NodeboxActions;

// Default templates configuration
function getDefaultTemplates(): Record<ProjectTemplate, TemplateConfig> {
  return {
    'react': {
      name: 'React App',
      description: 'A modern React application with TypeScript',
      files: {
        'package.json': JSON.stringify({
          name: 'react-app',
          version: '1.0.0',
          dependencies: {
            'react': '^18.2.0',
            'react-dom': '^18.2.0',
            'typescript': '^5.0.0',
            '@types/react': '^18.2.0',
            '@types/react-dom': '^18.2.0'
          },
          scripts: {
            'start': 'react-scripts start',
            'build': 'react-scripts build',
            'dev': 'react-scripts start'
          }
        }, null, 2),
        'src/App.tsx': `import React from 'react';

function App() {
  return (
    <div className="App">
      <header>
        <h1>Hello React!</h1>
        <p>Your React application is running.</p>
      </header>
    </div>
  );
}

export default App;`,
        'src/index.tsx': `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(<App />);`,
        'public/index.html': `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>React App</title>
</head>
<body>
  <div id="root"></div>
</body>
</html>`
      },
      startCommand: 'npm start',
      buildCommand: 'npm run build',
      previewPort: 3000
    },

    'nextjs': {
      name: 'Next.js App',
      description: 'A Next.js application with TypeScript',
      files: {
        'package.json': JSON.stringify({
          name: 'nextjs-app',
          version: '1.0.0',
          dependencies: {
            'next': '^14.0.0',
            'react': '^18.2.0',
            'react-dom': '^18.2.0',
            'typescript': '^5.0.0',
            '@types/react': '^18.2.0',
            '@types/react-dom': '^18.2.0'
          },
          scripts: {
            'dev': 'next dev',
            'build': 'next build',
            'start': 'next start'
          }
        }, null, 2),
        'pages/index.tsx': `import React from 'react';

export default function Home() {
  return (
    <div>
      <h1>Welcome to Next.js!</h1>
      <p>Your Next.js application is running.</p>
    </div>
  );
}`,
        'pages/_app.tsx': `import type { AppProps } from 'next/app';

export default function App({ Component, pageProps }: AppProps) {
  return <Component {...pageProps} />;
}`,
        'next.config.js': `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
}

module.exports = nextConfig`
      },
      startCommand: 'npm run dev',
      buildCommand: 'npm run build',
      previewPort: 3000
    },

    'express': {
      name: 'Express Server',
      description: 'A Node.js Express server with TypeScript',
      files: {
        'package.json': JSON.stringify({
          name: 'express-server',
          version: '1.0.0',
          dependencies: {
            'express': '^4.18.0',
            'typescript': '^5.0.0',
            '@types/express': '^4.17.0',
            '@types/node': '^20.0.0',
            'ts-node': '^10.9.0'
          },
          scripts: {
            'start': 'node dist/index.js',
            'dev': 'ts-node src/index.ts',
            'build': 'tsc'
          }
        }, null, 2),
        'src/index.ts': `import express from 'express';

const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Hello from Express!' });
});

app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(port, () => {
  console.log(\`Server running on port \${port}\`);
});`,
        'tsconfig.json': JSON.stringify({
          compilerOptions: {
            target: 'ES2020',
            module: 'commonjs',
            outDir: './dist',
            rootDir: './src',
            strict: true,
            esModuleInterop: true,
            skipLibCheck: true,
            forceConsistentCasingInFileNames: true
          },
          include: ['src/**/*'],
          exclude: ['node_modules', 'dist']
        }, null, 2)
      },
      startCommand: 'npm run dev',
      buildCommand: 'npm run build',
      previewPort: 3000
    },

    'vanilla-js': {
      name: 'Vanilla JavaScript',
      description: 'A simple vanilla JavaScript project',
      files: {
        'package.json': JSON.stringify({
          name: 'vanilla-js-app',
          version: '1.0.0',
          scripts: {
            'start': 'http-server .',
            'dev': 'http-server . -p 3000'
          },
          devDependencies: {
            'http-server': '^14.1.0'
          }
        }, null, 2),
        'index.html': `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vanilla JS App</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div id="app">
    <h1>Hello Vanilla JavaScript!</h1>
    <p>Your vanilla JS application is running.</p>
    <button id="clickBtn">Click me!</button>
  </div>
  <script src="script.js"></script>
</body>
</html>`,
        'script.js': `document.addEventListener('DOMContentLoaded', function() {
  const button = document.getElementById('clickBtn');
  let clickCount = 0;

  button.addEventListener('click', function() {
    clickCount++;
    button.textContent = \`Clicked \${clickCount} times!\`;
  });

  console.log('Vanilla JS app loaded!');
});`,
        'style.css': `body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
}

#app {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

button:hover {
  background-color: #0056b3;
}`
      },
      startCommand: 'npm run dev',
      previewPort: 3000
    },

    'typescript': {
      name: 'TypeScript Project',
      description: 'A TypeScript project with modern tooling',
      files: {
        'package.json': JSON.stringify({
          name: 'typescript-project',
          version: '1.0.0',
          dependencies: {
            'typescript': '^5.0.0',
            '@types/node': '^20.0.0',
            'ts-node': '^10.9.0'
          },
          scripts: {
            'start': 'node dist/index.js',
            'dev': 'ts-node src/index.ts',
            'build': 'tsc',
            'watch': 'tsc --watch'
          }
        }, null, 2),
        'src/index.ts': `interface User {
  id: number;
  name: string;
  email: string;
}

class UserManager {
  private users: User[] = [];

  addUser(user: Omit<User, 'id'>): User {
    const newUser: User = {
      id: this.users.length + 1,
      ...user
    };
    this.users.push(newUser);
    return newUser;
  }

  getUsers(): User[] {
    return this.users;
  }

  getUserById(id: number): User | undefined {
    return this.users.find(user => user.id === id);
  }
}

// Example usage
const userManager = new UserManager();

userManager.addUser({
  name: 'John Doe',
  email: '<EMAIL>'
});

userManager.addUser({
  name: 'Jane Smith',
  email: '<EMAIL>'
});

console.log('All users:', userManager.getUsers());
console.log('User 1:', userManager.getUserById(1));`,
        'tsconfig.json': JSON.stringify({
          compilerOptions: {
            target: 'ES2020',
            module: 'commonjs',
            outDir: './dist',
            rootDir: './src',
            strict: true,
            esModuleInterop: true,
            skipLibCheck: true,
            forceConsistentCasingInFileNames: true,
            declaration: true,
            sourceMap: true
          },
          include: ['src/**/*'],
          exclude: ['node_modules', 'dist']
        }, null, 2)
      },
      startCommand: 'npm run dev',
      buildCommand: 'npm run build',
      previewPort: 3000
    },

    'research-dashboard': {
      name: 'Research Dashboard',
      description: 'A data visualization dashboard for research',
      files: {
        'package.json': JSON.stringify({
          name: 'research-dashboard',
          version: '1.0.0',
          dependencies: {
            'react': '^18.2.0',
            'react-dom': '^18.2.0',
            'typescript': '^5.0.0',
            '@types/react': '^18.2.0',
            '@types/react-dom': '^18.2.0',
            'recharts': '^2.8.0',
            'axios': '^1.6.0'
          },
          scripts: {
            'start': 'react-scripts start',
            'build': 'react-scripts build',
            'dev': 'react-scripts start'
          }
        }, null, 2),
        'src/App.tsx': `import React, { useState, useEffect } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface DataPoint {
  name: string;
  value: number;
}

function App() {
  const [data, setData] = useState<DataPoint[]>([]);

  useEffect(() => {
    // Simulate data loading
    const mockData: DataPoint[] = [
      { name: 'Jan', value: 400 },
      { name: 'Feb', value: 300 },
      { name: 'Mar', value: 600 },
      { name: 'Apr', value: 800 },
      { name: 'May', value: 500 },
      { name: 'Jun', value: 700 },
    ];
    setData(mockData);
  }, []);

  return (
    <div style={{ padding: '20px' }}>
      <h1>Research Dashboard</h1>
      <div style={{ width: '100%', height: '400px' }}>
        <ResponsiveContainer>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Line type="monotone" dataKey="value" stroke="#8884d8" />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}

export default App;`,
        'src/index.tsx': `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(<App />);`,
        'public/index.html': `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Research Dashboard</title>
</head>
<body>
  <div id="root"></div>
</body>
</html>`
      },
      startCommand: 'npm start',
      buildCommand: 'npm run build',
      previewPort: 3000
    },

    'data-analysis': {
      name: 'Data Analysis',
      description: 'A Node.js data analysis project',
      files: {
        'package.json': JSON.stringify({
          name: 'data-analysis',
          version: '1.0.0',
          dependencies: {
            'typescript': '^5.0.0',
            '@types/node': '^20.0.0',
            'ts-node': '^10.9.0',
            'csv-parser': '^3.0.0',
            'fs-extra': '^11.0.0'
          },
          scripts: {
            'start': 'node dist/index.js',
            'dev': 'ts-node src/index.ts',
            'build': 'tsc',
            'analyze': 'ts-node src/analyze.ts'
          }
        }, null, 2),
        'src/index.ts': `import fs from 'fs-extra';
import csv from 'csv-parser';

interface DataRow {
  [key: string]: string | number;
}

class DataAnalyzer {
  private data: DataRow[] = [];

  async loadCSV(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.data = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row: DataRow) => {
          this.data.push(row);
        })
        .on('end', () => {
          console.log(\`Loaded \${this.data.length} rows from \${filePath}\`);
          resolve();
        })
        .on('error', reject);
    });
  }

  getStats(column: string): { mean: number; median: number; count: number } {
    const values = this.data
      .map(row => parseFloat(row[column] as string))
      .filter(val => !isNaN(val));

    if (values.length === 0) {
      return { mean: 0, median: 0, count: 0 };
    }

    const sorted = values.sort((a, b) => a - b);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const median = sorted[Math.floor(sorted.length / 2)];

    return { mean, median, count: values.length };
  }

  getData(): DataRow[] {
    return this.data;
  }
}

// Example usage
const analyzer = new DataAnalyzer();

// Create sample data
const sampleData = [
  { name: 'Alice', age: 25, score: 85 },
  { name: 'Bob', age: 30, score: 92 },
  { name: 'Charlie', age: 35, score: 78 },
  { name: 'Diana', age: 28, score: 95 },
];

analyzer['data'] = sampleData;

console.log('Sample Data Analysis:');
console.log('Age stats:', analyzer.getStats('age'));
console.log('Score stats:', analyzer.getStats('score'));
console.log('All data:', analyzer.getData());`,
        'tsconfig.json': JSON.stringify({
          compilerOptions: {
            target: 'ES2020',
            module: 'commonjs',
            outDir: './dist',
            rootDir: './src',
            strict: true,
            esModuleInterop: true,
            skipLibCheck: true,
            forceConsistentCasingInFileNames: true
          },
          include: ['src/**/*'],
          exclude: ['node_modules', 'dist']
        }, null, 2)
      },
      startCommand: 'npm run dev',
      buildCommand: 'npm run build',
      previewPort: 3000
    },

    'custom': {
      name: 'Custom Project',
      description: 'A custom project template',
      files: {
        'package.json': JSON.stringify({
          name: 'custom-project',
          version: '1.0.0',
          scripts: {
            'start': 'node index.js',
            'dev': 'node index.js'
          }
        }, null, 2),
        'index.js': `console.log('Hello from custom project!');

// Add your custom code here
const message = 'Welcome to your custom Nodebox project';
console.log(message);`
      },
      startCommand: 'npm run dev',
      previewPort: 3000
    }
  };
}

// Default global settings
const defaultGlobalSettings: NodeboxSettings = {
  autoSave: true,
  autoPreview: true,
  memoryLimit: 512, // MB
  timeoutMs: 30000, // 30 seconds
  allowNetworking: true,
  enableHotReload: true
};

// Create the Nodebox store
export const useNodeboxStore = create<NodeboxStore>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        // Initial state
        instances: [],
        activeInstanceId: null,
        isLoading: false,
        error: null,
        
        manager: null,
        client: null,
        clientMode: false,
        
        fileSystem: {},
        activeFile: null,
        
        processes: {},
        activeProcess: null,
        terminalOutput: {},
        
        previews: {},
        activePreview: null,
        
        availableTemplates: getDefaultTemplates(),
        
        globalSettings: defaultGlobalSettings,
        instanceSettings: {},

        // Initialize runtime (manager or client)
        initializeRuntime: async (clientMode = false, baseUrl = '/api/nodebox') => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
            state.clientMode = clientMode;
          });

          try {
            if (clientMode) {
              const client = new NodeboxClient(baseUrl);
              set((state) => {
                state.client = client;
                state.manager = null;
              });
            } else {
              const manager = new NodeboxManager();
              set((state) => {
                state.manager = manager;
                state.client = null;
              });
            }

            // Load existing instances
            await get().refreshInstances();
          } catch (error) {
            const nodeboxError = error instanceof NodeboxError 
              ? error 
              : new NodeboxError(`Failed to initialize runtime: ${error instanceof Error ? error.message : String(error)}`);
            
            set((state) => {
              state.error = nodeboxError;
            });
          } finally {
            set((state) => {
              state.isLoading = false;
            });
          }
        },

        // Create a new instance
        createInstance: async (config: Omit<NodeboxConfig, 'instanceId'>) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            // Generate instance ID
            const instanceId = `nodebox_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            const fullConfig: NodeboxConfig = { ...config, instanceId };

            let newInstance: NodeboxInstance;

            if (get().clientMode && get().client) {
              newInstance = await get().client!.createInstance(fullConfig);
            } else if (get().manager) {
              newInstance = await get().manager!.createInstance(fullConfig);
            } else {
              throw new NodeboxError('No manager or client available');
            }

            set((state) => {
              state.instances.push(newInstance);
              state.activeInstanceId = newInstance.id;
              state.fileSystem[newInstance.id] = [];
              state.processes[newInstance.id] = [];
              state.previews[newInstance.id] = [];
            });

            return newInstance;
          } catch (error) {
            const nodeboxError = error instanceof NodeboxError
              ? error
              : new NodeboxError(`Failed to create instance: ${error instanceof Error ? error.message : String(error)}`);

            set((state) => {
              state.error = nodeboxError;
            });
            return null;
          } finally {
            set((state) => {
              state.isLoading = false;
            });
          }
        },

        // Destroy an instance
        destroyInstance: async (instanceId: string) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            let success: boolean;

            if (get().clientMode && get().client) {
              success = await get().client!.destroyInstance(instanceId);
            } else if (get().manager) {
              success = await get().manager!.destroyInstance(instanceId);
            } else {
              throw new NodeboxError('No manager or client available');
            }

            if (success) {
              set((state) => {
                state.instances = state.instances.filter((instance) => instance.id !== instanceId);
                if (state.activeInstanceId === instanceId) {
                  state.activeInstanceId = state.instances.length > 0 ? state.instances[0].id : null;
                }
                delete state.fileSystem[instanceId];
                delete state.processes[instanceId];
                delete state.previews[instanceId];
                delete state.instanceSettings[instanceId];
              });
            }

            return success;
          } catch (error) {
            const nodeboxError = error instanceof NodeboxError
              ? error
              : new NodeboxError(`Failed to destroy instance: ${error instanceof Error ? error.message : String(error)}`);

            set((state) => {
              state.error = nodeboxError;
            });
            return false;
          } finally {
            set((state) => {
              state.isLoading = false;
            });
          }
        },

        // Set active instance
        setActiveInstance: (instanceId: string | null) => {
          set((state) => {
            if (instanceId === null || state.instances.find((i) => i.id === instanceId)) {
              state.activeInstanceId = instanceId;
            }
          });
        },

        // Refresh instances list
        refreshInstances: async () => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });

          try {
            let instancesList: NodeboxInstance[];

            if (get().clientMode && get().client) {
              instancesList = await get().client!.listInstances();
            } else if (get().manager) {
              instancesList = get().manager!.listInstances();
            } else {
              throw new NodeboxError('No manager or client available');
            }

            set((state) => {
              state.instances = instancesList;

              // Set active instance if none is set and we have instances
              if (!state.activeInstanceId && instancesList.length > 0) {
                state.activeInstanceId = instancesList[0].id;
              }

              // Initialize state for new instances
              instancesList.forEach((instance: NodeboxInstance) => {
                if (!state.fileSystem[instance.id]) {
                  state.fileSystem[instance.id] = [];
                }
                if (!state.processes[instance.id]) {
                  state.processes[instance.id] = [];
                }
                if (!state.previews[instance.id]) {
                  state.previews[instance.id] = [];
                }
              });
            });
          } catch (error) {
            const nodeboxError = error instanceof NodeboxError
              ? error
              : new NodeboxError(`Failed to refresh instances: ${error instanceof Error ? error.message : String(error)}`);

            set((state) => {
              state.error = nodeboxError;
            });
          } finally {
            set((state) => {
              state.isLoading = false;
            });
          }
        },

        // Create instance from template
        createFromTemplate: async (template: ProjectTemplate, name: string, projectId?: string) => {
          const templateConfig = get().availableTemplates[template];
          if (!templateConfig) {
            set((state) => {
              state.error = new NodeboxError(`Template '${template}' not found`);
            });
            return null;
          }

          const config: Omit<NodeboxConfig, 'instanceId'> = {
            name,
            description: `Instance created from ${template} template`,
            template,
            projectId,
            settings: {
              ...get().globalSettings,
              autoSave: true,
              autoPreview: true,
              enableHotReload: true
            }
          };

          return get().createInstance(config);
        },

        // Load template configuration
        loadTemplate: (template: ProjectTemplate) => {
          return get().availableTemplates[template] || null;
        },

        // File system operations with integration
        loadFileSystem: async (instanceId: string) => {
          const instance = get().instances.find(i => i.id === instanceId);
          if (!instance) return;

          try {
            // Get template files for the instance
            const template = get().availableTemplates[instance.config.template || 'custom'];
            if (template && template.files) {
              const files: FileEntry[] = [];

              // Convert template files to FileEntry format
              Object.entries(template.files).forEach(([filePath, content]) => {
                const pathParts = filePath.split('/');
                const fileName = pathParts[pathParts.length - 1];

                files.push({
                  name: fileName,
                  path: filePath.startsWith('/') ? filePath : `/${filePath}`,
                  type: 'file',
                  size: content.length,
                  lastModified: new Date(),
                  content: content
                });

                // Create directory entries for parent directories
                if (pathParts.length > 1) {
                  let currentPath = '';
                  for (let i = 0; i < pathParts.length - 1; i++) {
                    currentPath += '/' + pathParts[i];
                    if (!files.find(f => f.path === currentPath && f.type === 'directory')) {
                      files.push({
                        name: pathParts[i],
                        path: currentPath,
                        type: 'directory',
                        size: 0,
                        lastModified: new Date(),
                        children: []
                      });
                    }
                  }
                }
              });

              set((state) => {
                state.fileSystem[instanceId] = files;
              });

              // Sync with file store for editor integration
              const fileStore = useFileStore.getState();
              fileStore.clearFiles();

              files.forEach(file => {
                if (file.type === 'file' && file.content) {
                  fileStore.addFile(file.path, file.content);
                } else if (file.type === 'directory') {
                  fileStore.addDirectory(file.path);
                }
              });
            }
          } catch (error) {
            console.error('Error loading file system:', error);
          }
        },

        readFile: async (instanceId: string, path: string) => {
          const instance = get().instances.find(i => i.id === instanceId);
          if (!instance) return null;

          try {
            // First check if file exists in our file system
            const files = get().fileSystem[instanceId] || [];
            const file = files.find(f => f.path === path && f.type === 'file');

            if (file && file.content) {
              return file.content;
            }

            // Fallback to file store
            const fileStore = useFileStore.getState();
            const fileContent = fileStore.getFileContent(path);

            if (fileContent) {
              return fileContent.content;
            }

            // If not found, return template content or default
            const template = get().availableTemplates[instance.config.template || 'custom'];
            if (template && template.files && template.files[path.replace(/^\//, '')]) {
              return template.files[path.replace(/^\//, '')];
            }

            return null;
          } catch (error) {
            console.error('Error reading file:', error);
            return null;
          }
        },

        writeFile: async (instanceId: string, path: string, content: string) => {
          const instance = get().instances.find(i => i.id === instanceId);
          if (!instance) return false;

          try {
            // Update in Nodebox file system
            set((state) => {
              const files = state.fileSystem[instanceId] || [];
              const fileIndex = files.findIndex((f: FileEntry) => f.path === path);

              if (fileIndex >= 0) {
                files[fileIndex] = {
                  ...files[fileIndex],
                  content,
                  size: content.length,
                  lastModified: new Date()
                };
              } else {
                // Create new file
                const pathParts = path.split('/');
                const fileName = pathParts[pathParts.length - 1];

                files.push({
                  name: fileName,
                  path,
                  type: 'file',
                  size: content.length,
                  lastModified: new Date(),
                  content
                });
              }

              state.fileSystem[instanceId] = files;
            });

            // Sync with file store for editor integration
            const fileStore = useFileStore.getState();
            const existingFile = fileStore.getFileByPath(path);

            if (existingFile) {
              fileStore.updateFileContent(path, content);
            } else {
              fileStore.addFile(path, content);
            }

            // Note: Open files store will be updated automatically when file store changes
            // since they are synchronized through the file selection mechanism

            return true;
          } catch (error) {
            console.error('Error writing file:', error);
            return false;
          }
        },

        createFile: async (instanceId: string, path: string, content = '') => {
          return get().writeFile(instanceId, path, content);
        },

        deleteFile: async (instanceId: string, path: string) => {
          const instance = get().instances.find(i => i.id === instanceId);
          if (!instance) return false;

          try {
            // Mock file deletion - in real implementation, this would call the Nodebox API
            console.log(`Deleting file: ${path}`);
            return true;
          } catch (error) {
            console.error('Error deleting file:', error);
            return false;
          }
        },

        createDirectory: async (instanceId: string, path: string) => {
          const instance = get().instances.find(i => i.id === instanceId);
          if (!instance) return false;

          try {
            // Mock directory creation - in real implementation, this would call the Nodebox API
            console.log(`Creating directory: ${path}`);
            return true;
          } catch (error) {
            console.error('Error creating directory:', error);
            return false;
          }
        },

        setActiveFile: (instanceId: string, path: string, content: string) => {
          set((state) => {
            state.activeFile = { instanceId, path, content };
          });
        },

        // Shell/Terminal operations
        runCommand: async (instanceId: string, command: string, args = []) => {
          const instance = get().instances.find(i => i.id === instanceId);
          if (!instance) return null;

          try {
            const processId = `proc_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            const process: ShellProcess = {
              id: processId,
              command,
              args,
              status: 'running',
              startTime: new Date(),
              output: [],
              errors: []
            };

            set((state) => {
              if (!state.processes[instanceId]) {
                state.processes[instanceId] = [];
              }
              state.processes[instanceId].push(process);
              state.terminalOutput[processId] = [];
            });

            // Mock command execution
            setTimeout(() => {
              set((state) => {
                const proc = state.processes[instanceId]?.find((p: ShellProcess) => p.id === processId);
                if (proc) {
                  proc.status = 'completed';
                  proc.endTime = new Date();
                  proc.exitCode = 0;
                  state.terminalOutput[processId].push(`$ ${command} ${args.join(' ')}`);
                  state.terminalOutput[processId].push('Command completed successfully');
                }
              });
            }, 1000);

            return process;
          } catch (error) {
            console.error('Error running command:', error);
            return null;
          }
        },

        killProcess: async (instanceId: string, processId: string) => {
          try {
            set((state) => {
              const proc = state.processes[instanceId]?.find((p: ShellProcess) => p.id === processId);
              if (proc) {
                proc.status = 'killed';
                proc.endTime = new Date();
                proc.exitCode = -1;
              }
            });
            return true;
          } catch (error) {
            console.error('Error killing process:', error);
            return false;
          }
        },

        setActiveProcess: (instanceId: string, processId: string) => {
          set((state) => {
            state.activeProcess = { instanceId, processId };
          });
        },

        appendTerminalOutput: (processId: string, output: string) => {
          set((state) => {
            if (!state.terminalOutput[processId]) {
              state.terminalOutput[processId] = [];
            }
            state.terminalOutput[processId].push(output);
          });
        },

        clearTerminalOutput: (processId: string) => {
          set((state) => {
            state.terminalOutput[processId] = [];
          });
        },

        // Preview operations
        getPreview: async (instanceId: string, processId?: string) => {
          const instance = get().instances.find(i => i.id === instanceId);
          if (!instance) return null;

          try {
            // Mock preview creation - in real implementation, this would call the Nodebox API
            const previewId = `preview_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            const preview: PreviewInfo = {
              id: previewId,
              url: `http://localhost:3000/${instanceId}`,
              title: `Preview - ${instance.config.name}`,
              status: 'ready',
              port: 3000,
              processId
            };

            set((state) => {
              if (!state.previews[instanceId]) {
                state.previews[instanceId] = [];
              }
              state.previews[instanceId].push(preview);
            });

            return preview;
          } catch (error) {
            console.error('Error getting preview:', error);
            return null;
          }
        },

        refreshPreviews: async (instanceId: string) => {
          try {
            // Mock preview refresh - in real implementation, this would call the Nodebox API
            const previews: PreviewInfo[] = [
              {
                id: `preview_${instanceId}`,
                url: `http://localhost:3000/${instanceId}`,
                title: `Preview - ${instanceId}`,
                status: 'ready',
                port: 3000
              }
            ];

            set((state) => {
              state.previews[instanceId] = previews;
            });
          } catch (error) {
            console.error('Error refreshing previews:', error);
          }
        },

        setActivePreview: (instanceId: string, previewId: string) => {
          set((state) => {
            state.activePreview = { instanceId, previewId };
          });
        },

        // Settings management
        updateGlobalSettings: (settings: Partial<NodeboxSettings>) => {
          set((state) => {
            state.globalSettings = { ...state.globalSettings, ...settings };
          });
        },

        updateInstanceSettings: (instanceId: string, settings: Partial<NodeboxSettings>) => {
          set((state) => {
            if (!state.instanceSettings[instanceId]) {
              state.instanceSettings[instanceId] = { ...state.globalSettings };
            }
            state.instanceSettings[instanceId] = {
              ...state.instanceSettings[instanceId],
              ...settings
            };
          });
        },

        // Error handling
        setError: (error: NodeboxError | null) => {
          set((state) => {
            state.error = error;
          });
        },

        clearError: () => {
          set((state) => {
            state.error = null;
          });
        },

        // Utility actions
        getActiveInstance: () => {
          const state = get();
          return state.instances.find(i => i.id === state.activeInstanceId) || null;
        },

        getInstanceById: (instanceId: string) => {
          return get().instances.find(i => i.id === instanceId) || null;
        },

        cleanup: () => {
          set((state) => {
            // Clean up all instances
            state.instances.forEach((instance) => {
              if (state.manager) {
                state.manager.destroyInstance(instance.id).catch(console.error);
              }
            });

            // Reset state
            state.instances = [];
            state.activeInstanceId = null;
            state.fileSystem = {};
            state.activeFile = null;
            state.processes = {};
            state.activeProcess = null;
            state.terminalOutput = {};
            state.previews = {};
            state.activePreview = null;
            state.error = null;
            state.isLoading = false;

            // Destroy manager
            if (state.manager) {
              state.manager.destroy();
              state.manager = null;
            }
            state.client = null;
          });
        },
      }))
    ),
    {
      name: 'nodebox-store',
      partialize: (state: NodeboxState) => ({
        globalSettings: state.globalSettings,
        instanceSettings: state.instanceSettings,
      }),
    }
  )
);

// Selector hooks for easier access to specific parts of the store
export const useNodeboxInstances = () => useNodeboxStore((state) => state.instances);
export const useActiveNodeboxInstance = () => useNodeboxStore((state) => {
  return state.instances.find(i => i.id === state.activeInstanceId) || null;
});
export const useNodeboxLoading = () => useNodeboxStore((state) => state.isLoading);
export const useNodeboxError = () => useNodeboxStore((state) => state.error);
export const useNodeboxTemplates = () => useNodeboxStore((state) => state.availableTemplates);

// File system selectors
export const useNodeboxFileSystem = (instanceId?: string) => useNodeboxStore((state) => {
  if (!instanceId) return [];
  return state.fileSystem[instanceId] || [];
});
export const useActiveNodeboxFile = () => useNodeboxStore((state) => state.activeFile);

// Terminal/Process selectors
export const useNodeboxProcesses = (instanceId?: string) => useNodeboxStore((state) => {
  if (!instanceId) return [];
  return state.processes[instanceId] || [];
});
export const useNodeboxTerminalOutput = (processId?: string) => useNodeboxStore((state) => {
  if (!processId) return [];
  return state.terminalOutput[processId] || [];
});

// Preview selectors
export const useNodeboxPreviews = (instanceId?: string) => useNodeboxStore((state) => {
  if (!instanceId) return [];
  return state.previews[instanceId] || [];
});
export const useActiveNodeboxPreview = () => useNodeboxStore((state) => state.activePreview);

// Settings selectors
export const useNodeboxGlobalSettings = () => useNodeboxStore((state) => state.globalSettings);
export const useNodeboxInstanceSettings = (instanceId?: string) => useNodeboxStore((state) => {
  if (!instanceId) return state.globalSettings;
  return state.instanceSettings[instanceId] || state.globalSettings;
});

// Individual action selectors to avoid object recreation
export const useInitializeRuntime = () => useNodeboxStore((state) => state.initializeRuntime);
export const useCreateInstance = () => useNodeboxStore((state) => state.createInstance);
export const useDestroyInstance = () => useNodeboxStore((state) => state.destroyInstance);
export const useSetActiveInstance = () => useNodeboxStore((state) => state.setActiveInstance);
export const useRefreshInstances = () => useNodeboxStore((state) => state.refreshInstances);
export const useCreateFromTemplate = () => useNodeboxStore((state) => state.createFromTemplate);
export const useLoadTemplate = () => useNodeboxStore((state) => state.loadTemplate);

// File operation selectors
export const useLoadFileSystem = () => useNodeboxStore((state) => state.loadFileSystem);
export const useReadFile = () => useNodeboxStore((state) => state.readFile);
export const useWriteFile = () => useNodeboxStore((state) => state.writeFile);
export const useCreateFile = () => useNodeboxStore((state) => state.createFile);
export const useDeleteFile = () => useNodeboxStore((state) => state.deleteFile);
export const useCreateDirectory = () => useNodeboxStore((state) => state.createDirectory);
export const useSetActiveFile = () => useNodeboxStore((state) => state.setActiveFile);

// Terminal operation selectors
export const useRunCommand = () => useNodeboxStore((state) => state.runCommand);
export const useKillProcess = () => useNodeboxStore((state) => state.killProcess);
export const useSetActiveProcess = () => useNodeboxStore((state) => state.setActiveProcess);
export const useAppendTerminalOutput = () => useNodeboxStore((state) => state.appendTerminalOutput);
export const useClearTerminalOutput = () => useNodeboxStore((state) => state.clearTerminalOutput);

// Preview operation selectors
export const useGetPreview = () => useNodeboxStore((state) => state.getPreview);
export const useRefreshPreviews = () => useNodeboxStore((state) => state.refreshPreviews);
export const useSetActivePreview = () => useNodeboxStore((state) => state.setActivePreview);

// Settings selectors
export const useUpdateGlobalSettings = () => useNodeboxStore((state) => state.updateGlobalSettings);
export const useUpdateInstanceSettings = () => useNodeboxStore((state) => state.updateInstanceSettings);

// Error handling selectors
export const useSetError = () => useNodeboxStore((state) => state.setError);
export const useClearError = () => useNodeboxStore((state) => state.clearError);

// Utility selectors
export const useGetActiveInstance = () => useNodeboxStore((state) => state.getActiveInstance);
export const useGetInstanceById = () => useNodeboxStore((state) => state.getInstanceById);
export const useCleanup = () => useNodeboxStore((state) => state.cleanup);

// Composite hook that uses useMemo to create a stable actions object
export const useNodeboxActions = () => {
  const initializeRuntime = useInitializeRuntime();
  const createInstance = useCreateInstance();
  const destroyInstance = useDestroyInstance();
  const setActiveInstance = useSetActiveInstance();
  const refreshInstances = useRefreshInstances();
  const createFromTemplate = useCreateFromTemplate();
  const loadTemplate = useLoadTemplate();

  const loadFileSystem = useLoadFileSystem();
  const readFile = useReadFile();
  const writeFile = useWriteFile();
  const createFile = useCreateFile();
  const deleteFile = useDeleteFile();
  const createDirectory = useCreateDirectory();
  const setActiveFile = useSetActiveFile();

  const runCommand = useRunCommand();
  const killProcess = useKillProcess();
  const setActiveProcess = useSetActiveProcess();
  const appendTerminalOutput = useAppendTerminalOutput();
  const clearTerminalOutput = useClearTerminalOutput();

  const getPreview = useGetPreview();
  const refreshPreviews = useRefreshPreviews();
  const setActivePreview = useSetActivePreview();

  const updateGlobalSettings = useUpdateGlobalSettings();
  const updateInstanceSettings = useUpdateInstanceSettings();

  const setError = useSetError();
  const clearError = useClearError();

  const getActiveInstance = useGetActiveInstance();
  const getInstanceById = useGetInstanceById();
  const cleanup = useCleanup();

  return useMemo(() => ({
    initializeRuntime,
    createInstance,
    destroyInstance,
    setActiveInstance,
    refreshInstances,
    createFromTemplate,
    loadTemplate,
    loadFileSystem,
    readFile,
    writeFile,
    createFile,
    deleteFile,
    createDirectory,
    setActiveFile,
    runCommand,
    killProcess,
    setActiveProcess,
    appendTerminalOutput,
    clearTerminalOutput,
    getPreview,
    refreshPreviews,
    setActivePreview,
    updateGlobalSettings,
    updateInstanceSettings,
    setError,
    clearError,
    getActiveInstance,
    getInstanceById,
    cleanup,
  }), [
    initializeRuntime,
    createInstance,
    destroyInstance,
    setActiveInstance,
    refreshInstances,
    createFromTemplate,
    loadTemplate,
    loadFileSystem,
    readFile,
    writeFile,
    createFile,
    deleteFile,
    createDirectory,
    setActiveFile,
    runCommand,
    killProcess,
    setActiveProcess,
    appendTerminalOutput,
    clearTerminalOutput,
    getPreview,
    refreshPreviews,
    setActivePreview,
    updateGlobalSettings,
    updateInstanceSettings,
    setError,
    clearError,
    getActiveInstance,
    getInstanceById,
    cleanup,
  ]);
};

// Example integration hook for AI Node.js workspace
export const useNodeboxIntegration = (projectId?: string) => {
  const activeInstance = useActiveNodeboxInstance();
  const isLoading = useNodeboxLoading();
  const error = useNodeboxError();

  // Get individual actions to avoid object recreation
  const initializeRuntime = useInitializeRuntime();
  const createFromTemplate = useCreateFromTemplate();
  const runCommand = useRunCommand();
  const getPreview = useGetPreview();
  const readFile = useReadFile();
  const writeFile = useWriteFile();

  // Initialize runtime on mount
  useEffect(() => {
    initializeRuntime(false); // Use manager mode by default
  }, [initializeRuntime]);

  // Auto-create instance for project if needed
  useEffect(() => {
    if (projectId && !activeInstance && !isLoading && !error) {
      // Auto-create a default instance for the project
      createFromTemplate('nextjs', `Project ${projectId}`, projectId);
    }
  }, [projectId, activeInstance, isLoading, error, createFromTemplate]);

  // Get the composite actions object for backward compatibility
  const actions = useNodeboxActions();

  // Memoize the return object to prevent infinite loops
  return useMemo(() => ({
    // State
    activeInstance,
    isLoading,
    error,

    // Quick actions for convenience
    createApp: (template: ProjectTemplate, name: string) =>
      createFromTemplate(template, name, projectId),

    quickRunCommand: (command: string, args?: string[]) =>
      activeInstance ? runCommand(activeInstance.id, command, args) : null,

    quickGetPreview: () =>
      activeInstance ? getPreview(activeInstance.id) : null,

    // Quick file operations
    quickReadFile: (path: string) =>
      activeInstance ? readFile(activeInstance.id, path) : null,

    quickWriteFile: (path: string, content: string) =>
      activeInstance ? writeFile(activeInstance.id, path, content) : null,

    // All actions (includes the full API)
    ...actions,
  }), [
    activeInstance,
    isLoading,
    error,
    createFromTemplate,
    runCommand,
    getPreview,
    readFile,
    writeFile,
    actions,
    projectId
  ]);
};
