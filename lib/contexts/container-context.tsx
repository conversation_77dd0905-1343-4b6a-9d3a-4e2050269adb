"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useWorkspaceStore } from "@/lib/stores/workspace-store";
import { toast } from "sonner";

// Define the container context type
export interface ContainerContextType {
  // Container state
  containerId: string | null;
  containerType: "lxc" | "lxd" | "microvm";
  containerStatus: "idle" | "checking" | "creating" | "starting" | "ready" | "running" | "stopping" | "stopped" | "error";
  containerError: string | null;

  // Container operations
  executeCommand: (command: string) => Promise<{
    stdout: string;
    stderr: string;
    exitCode: number;
  }>;

  // File operations
  listFiles: (path: string) => Promise<any[]>;
  readFile: (path: string) => Promise<string>;
  writeFile: (path: string, content: string) => Promise<boolean>;
  createDirectory: (path: string) => Promise<boolean>;
  deleteFile: (path: string, recursive?: boolean) => Promise<boolean>;

  // Container management
  startContainer: () => Promise<boolean>;
  stopContainer: () => Promise<boolean>;
  restartContainer: () => Promise<boolean>;
}

// Create the context
const ContainerContext = createContext<ContainerContextType | null>(null);

// Create a provider component
export function ContainerProvider({ children }: { children: React.ReactNode }) {
  // Get container state from workspace store
  const {
    containerId,
    containerType,
    containerStatus,
    containerError,
    startContainer: startContainerStore,
    stopContainer: stopContainerStore,
    restartContainer: restartContainerStore,
  } = useWorkspaceStore();

  // Execute a command in the container
  const executeCommand = async (command: string) => {
    if (!containerId) {
      throw new Error("No container is active");
    }

    try {
      let endpoint;
      let commandBody;

      if (containerType === "lxd") {
        endpoint = `/api/containerization/lxd/containers/${containerId}/exec`;
        commandBody = {
          command: ["/bin/sh", "-c", command]
        };
      } else if (containerType === "microvm") {
        endpoint = `/api/containerization/microvm/${containerId}/exec`;
        commandBody = {
          command: command
        };
      } else {
        // Default to LXC
        endpoint = `/api/containerization/lxc/containers/${containerId}/exec`;
        commandBody = {
          command: command
        };
      }

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(commandBody),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to execute command");
      }

      const result = await response.json();
      return {
        stdout: result.stdout || "",
        stderr: result.stderr || "",
        exitCode: result.exitCode || 0,
      };
    } catch (error: any) {
      console.error("Error executing command:", error);
      throw error;
    }
  };

  // List files in a directory
  const listFiles = async (path: string) => {
    if (!containerId) {
      throw new Error("No container is active");
    }

    try {
      let endpoint;

      if (containerType === "lxd") {
        endpoint = `/api/containerization/lxd/containers/${containerId}/files?path=${encodeURIComponent(path)}`;
      } else if (containerType === "microvm") {
        endpoint = `/api/containerization/microvm/${containerId}/files?path=${encodeURIComponent(path)}`;
      } else {
        // Default to LXC
        endpoint = `/api/containerization/lxc/containers/${containerId}/files?path=${encodeURIComponent(path)}`;
      }

      const response = await fetch(endpoint);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to list files");
      }

      const result = await response.json();
      return result.files || [];
    } catch (error: any) {
      console.error("Error listing files:", error);
      throw error;
    }
  };

  // Read a file
  const readFile = async (path: string) => {
    if (!containerId) {
      throw new Error("No container is active");
    }

    try {
      let endpoint;

      if (containerType === "lxd") {
        endpoint = `/api/containerization/lxd/containers/${containerId}/files/content?path=${encodeURIComponent(path)}`;
      } else if (containerType === "microvm") {
        endpoint = `/api/containerization/microvm/${containerId}/files/content?path=${encodeURIComponent(path)}`;
      } else {
        // Default to LXC
        endpoint = `/api/containerization/lxc/containers/${containerId}/files/content?path=${encodeURIComponent(path)}`;
      }

      const response = await fetch(endpoint);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to read file");
      }

      const result = await response.json();
      return result.content || "";
    } catch (error: any) {
      console.error("Error reading file:", error);
      throw error;
    }
  };

  // Write to a file
  const writeFile = async (path: string, content: string) => {
    if (!containerId) {
      throw new Error("No container is active");
    }

    try {
      let endpoint;

      if (containerType === "lxd") {
        endpoint = `/api/containerization/lxd/containers/${containerId}/files/content`;
      } else if (containerType === "microvm") {
        endpoint = `/api/containerization/microvm/${containerId}/files/content`;
      } else {
        // Default to LXC
        endpoint = `/api/containerization/lxc/containers/${containerId}/files/content`;
      }

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          path,
          content,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to write file");
      }

      return true;
    } catch (error: any) {
      console.error("Error writing file:", error);
      throw error;
    }
  };

  // Create a directory
  const createDirectory = async (path: string) => {
    if (!containerId) {
      throw new Error("No container is active");
    }

    try {
      let endpoint;

      if (containerType === "lxd") {
        endpoint = `/api/containerization/lxd/containers/${containerId}/files/directory`;
      } else if (containerType === "microvm") {
        endpoint = `/api/containerization/microvm/${containerId}/files/directory`;
      } else {
        // Default to LXC
        endpoint = `/api/containerization/lxc/containers/${containerId}/files/directory`;
      }

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          path,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create directory");
      }

      return true;
    } catch (error: any) {
      console.error("Error creating directory:", error);
      throw error;
    }
  };

  // Delete a file or directory
  const deleteFile = async (path: string, recursive = false) => {
    if (!containerId) {
      throw new Error("No container is active");
    }

    try {
      let endpoint;

      if (containerType === "lxd") {
        endpoint = `/api/containerization/lxd/containers/${containerId}/files`;
      } else if (containerType === "microvm") {
        endpoint = `/api/containerization/microvm/${containerId}/files`;
      } else {
        // Default to LXC
        endpoint = `/api/containerization/lxc/containers/${containerId}/files`;
      }

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          path,
          recursive,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete file");
      }

      return true;
    } catch (error: any) {
      console.error("Error deleting file:", error);
      throw error;
    }
  };

  // Start the container
  const startContainer = async () => {
    if (!containerId) {
      toast.error("No container is available to start");
      return false;
    }

    try {
      await startContainerStore(containerId);
      return true;
    } catch (error: any) {
      console.error("Error starting container:", error);
      toast.error(`Failed to start container: ${error.message}`);
      return false;
    }
  };

  // Stop the container
  const stopContainer = async () => {
    try {
      await stopContainerStore();
      return true;
    } catch (error: any) {
      console.error("Error stopping container:", error);
      toast.error(`Failed to stop container: ${error.message}`);
      return false;
    }
  };

  // Restart the container
  const restartContainer = async () => {
    try {
      await restartContainerStore();
      return true;
    } catch (error: any) {
      console.error("Error restarting container:", error);
      toast.error(`Failed to restart container: ${error.message}`);
      return false;
    }
  };

  // Create the context value
  const contextValue: ContainerContextType = {
    containerId,
    containerType,
    containerStatus,
    containerError,
    executeCommand,
    listFiles,
    readFile,
    writeFile,
    createDirectory,
    deleteFile,
    startContainer,
    stopContainer,
    restartContainer,
  };

  return (
    <ContainerContext.Provider value={contextValue}>
      {children}
    </ContainerContext.Provider>
  );
}

// Create a hook to use the container context
export function useContainer() {
  const context = useContext(ContainerContext);
  if (!context) {
    throw new Error("useContainer must be used within a ContainerProvider");
  }
  return context;
}
