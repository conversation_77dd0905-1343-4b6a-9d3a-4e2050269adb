"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useProxmoxNodes, useProxmoxVMs, useProxmoxStorage, useProxmoxTasks } from '@/lib/hooks/use-proxmox';
import { NodeInfo, VMInfo, StorageInfo, TaskInfo } from '@/lib/proxmox/types';

interface ProxmoxContextType {
  // State
  selectedNode: string | null;
  selectedVM: string | null;
  nodes: NodeInfo[];
  vms: VMInfo[];
  storage: StorageInfo[];
  tasks: TaskInfo[];
  loading: {
    nodes: boolean;
    vms: boolean;
    storage: boolean;
    tasks: boolean;
  };
  error: {
    nodes: string | null;
    vms: string | null;
    storage: string | null;
    tasks: string | null;
  };
  
  // Actions
  setSelectedNode: (nodeId: string | null) => void;
  setSelectedVM: (vmId: string | null) => void;
  refreshNodes: () => Promise<void>;
  refreshVMs: () => Promise<void>;
  refreshStorage: () => Promise<void>;
  refreshTasks: () => Promise<void>;
  refreshAll: () => Promise<void>;
  
  // VM Operations
  getVM: (vmid: string) => Promise<VMInfo | null>;
  createVM: (params: any) => Promise<VMInfo | null>;
  startVM: (vmid: string) => Promise<any>;
  stopVM: (vmid: string) => Promise<any>;
  restartVM: (vmid: string) => Promise<any>;
  deleteVM: (vmid: string) => Promise<any>;
  createTemplate: (params: any) => Promise<VMInfo | null>;
  cloneVM: (params: any) => Promise<VMInfo | null>;
}

const ProxmoxContext = createContext<ProxmoxContextType | undefined>(undefined);

export function ProxmoxProvider({ children }: { children: ReactNode }) {
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [selectedVM, setSelectedVM] = useState<string | null>(null);
  
  // Use the hooks
  const {
    nodes,
    loading: nodesLoading,
    error: nodesError,
    refreshNodes,
  } = useProxmoxNodes();
  
  const {
    vms,
    loading: vmsLoading,
    error: vmsError,
    refreshVMs,
    getVM,
    createVM,
    startVM,
    stopVM,
    restartVM,
    deleteVM,
    createTemplate,
    cloneVM,
  } = useProxmoxVMs(selectedNode || '');
  
  const {
    storage,
    loading: storageLoading,
    error: storageError,
    refreshStorage,
  } = useProxmoxStorage();
  
  const {
    tasks,
    loading: tasksLoading,
    error: tasksError,
    refreshTasks,
  } = useProxmoxTasks(selectedNode || '');
  
  // Auto-select the first node if none is selected
  useEffect(() => {
    if (!selectedNode && nodes.length > 0) {
      setSelectedNode(nodes[0].id);
    }
  }, [nodes, selectedNode]);
  
  // Refresh all data
  const refreshAll = async () => {
    await Promise.all([
      refreshNodes(),
      selectedNode ? refreshVMs() : Promise.resolve(),
      refreshStorage(),
      selectedNode ? refreshTasks() : Promise.resolve(),
    ]);
  };
  
  const value: ProxmoxContextType = {
    // State
    selectedNode,
    selectedVM,
    nodes,
    vms,
    storage,
    tasks,
    loading: {
      nodes: nodesLoading,
      vms: vmsLoading,
      storage: storageLoading,
      tasks: tasksLoading,
    },
    error: {
      nodes: nodesError,
      vms: vmsError,
      storage: storageError,
      tasks: tasksError,
    },
    
    // Actions
    setSelectedNode,
    setSelectedVM,
    refreshNodes,
    refreshVMs,
    refreshStorage,
    refreshTasks,
    refreshAll,
    
    // VM Operations
    getVM,
    createVM,
    startVM,
    stopVM,
    restartVM,
    deleteVM,
    createTemplate,
    cloneVM,
  };
  
  return (
    <ProxmoxContext.Provider value={value}>
      {children}
    </ProxmoxContext.Provider>
  );
}

export function useProxmoxContext() {
  const context = useContext(ProxmoxContext);
  if (context === undefined) {
    throw new Error('useProxmoxContext must be used within a ProxmoxProvider');
  }
  return context;
}
