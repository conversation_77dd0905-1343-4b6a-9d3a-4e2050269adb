/**
 * VM Service
 * 
 * Provides methods for interacting with the VM API.
 * This is a client-side service that communicates with server-side API routes.
 */

import axios from 'axios';
import { VMStatus } from '@/lib/proxmox/types';

// API base URL
const API_BASE_URL = '/api/vms';

// VM Template interface
export interface VMTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  specs: {
    cpu: number;
    memory: number;
    storage: number;
    bandwidth: string;
  };
  price: number;
  popular: boolean;
  category: string;
  os: string;
  createdAt: Date;
  active: boolean;
  creator: string;
}

// VM interface
export interface VM {
  id: string;
  name: string;
  description: string;
  status: VMStatus;
  template: string;
  region: string;
  ipAddress: string;
  cpu: { cores: number; usage: number };
  memory: { total: number; used: number };
  storage: { total: number; used: number };
  bandwidth: { total: number; used: number };
  os: string;
  createdAt: Date;
  price: number;
  backups: boolean;
  containers: number;
  owner: string;
  node: string;
  host: string;
}

// VM Order interface
export interface VMOrder {
  id: string;
  vmId: string | null;
  vmName: string;
  template: string;
  user: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: Date;
  completedAt: Date | null;
  price: number;
  region: string;
}

// VM Purchase interface
export interface VMPurchase {
  templateId: string;
  vmName: string;
  region: string;
  cpu: number;
  memory: number;
  storage: number;
  operatingSystem: string;
  backups: boolean;
  autoStart: boolean;
  paymentMethod: string;
}

// VM Service class
export class VMService {
  constructor() {
    // No direct client initialization - we'll use API routes instead
  }

  // Get all VM templates
  async getTemplates(): Promise<VMTemplate[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/templates`);
      return response.data.map((template: any) => ({
        ...template,
        createdAt: new Date(template.createdAt),
      }));
    } catch (error) {
      console.error('Failed to fetch VM templates:', error);
      throw error;
    }
  }

  // Get VM template by ID
  async getTemplateById(id: string): Promise<VMTemplate> {
    try {
      const response = await axios.get(`${API_BASE_URL}/templates/${id}`);
      return {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
      };
    } catch (error) {
      console.error(`Failed to fetch VM template with ID ${id}:`, error);
      throw error;
    }
  }

  // Get all VMs for the current user
  async getUserVMs(): Promise<VM[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/user`);
      return response.data.map((vm: any) => ({
        ...vm,
        createdAt: new Date(vm.createdAt),
      }));
    } catch (error) {
      console.error('Failed to fetch user VMs:', error);
      throw error;
    }
  }

  // Get VM by ID
  async getVMById(id: string): Promise<VM> {
    try {
      const response = await axios.get(`${API_BASE_URL}/${id}`);
      return {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
      };
    } catch (error) {
      console.error(`Failed to fetch VM with ID ${id}:`, error);
      throw error;
    }
  }

  // Purchase a new VM
  async purchaseVM(purchase: VMPurchase): Promise<VMOrder> {
    try {
      const response = await axios.post(`${API_BASE_URL}/purchase`, purchase);
      return {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        completedAt: response.data.completedAt ? new Date(response.data.completedAt) : null,
      };
    } catch (error) {
      console.error('Failed to purchase VM:', error);
      throw error;
    }
  }

  // Start VM
  async startVM(id: string): Promise<boolean> {
    try {
      await axios.post(`${API_BASE_URL}/${id}/start`);
      return true;
    } catch (error) {
      console.error(`Failed to start VM with ID ${id}:`, error);
      throw error;
    }
  }

  // Stop VM
  async stopVM(id: string): Promise<boolean> {
    try {
      await axios.post(`${API_BASE_URL}/${id}/stop`);
      return true;
    } catch (error) {
      console.error(`Failed to stop VM with ID ${id}:`, error);
      throw error;
    }
  }

  // Restart VM
  async restartVM(id: string): Promise<boolean> {
    try {
      await axios.post(`${API_BASE_URL}/${id}/restart`);
      return true;
    } catch (error) {
      console.error(`Failed to restart VM with ID ${id}:`, error);
      throw error;
    }
  }

  // Delete VM
  async deleteVM(id: string): Promise<boolean> {
    try {
      await axios.delete(`${API_BASE_URL}/${id}`);
      return true;
    } catch (error) {
      console.error(`Failed to delete VM with ID ${id}:`, error);
      throw error;
    }
  }

  // Get VM containers
  async getVMContainers(id: string): Promise<any[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/${id}/containers`);
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch containers for VM with ID ${id}:`, error);
      throw error;
    }
  }

  // Get VM activity log
  async getVMActivityLog(id: string): Promise<any[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/${id}/activity`);
      return response.data.map((activity: any) => ({
        ...activity,
        timestamp: new Date(activity.timestamp),
      }));
    } catch (error) {
      console.error(`Failed to fetch activity log for VM with ID ${id}:`, error);
      throw error;
    }
  }
}

// Create a singleton instance
export const vmService = new VMService();
