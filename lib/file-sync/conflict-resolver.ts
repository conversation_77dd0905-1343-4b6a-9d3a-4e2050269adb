/**
 * Conflict Resolution System for File Synchronization
 */

import { EventEmitter } from 'events'
import { createHash } from 'crypto'
import {
  ConflictData,
  ConflictResolutionStrategy,
  ConflictResolutionOptions,
  SyncFileInfo
} from './types'

export class ConflictResolver extends EventEmitter {
  private defaultStrategy: ConflictResolutionStrategy

  constructor(defaultStrategy: ConflictResolutionStrategy = 'timestamp_based') {
    super()
    this.defaultStrategy = defaultStrategy
  }

  /**
   * Detect conflicts between Nodebox and database versions
   */
  detectConflict(nodeboxFile: SyncFileInfo, databaseFile: SyncFileInfo): ConflictData | null {
    // No conflict if files are identical
    if (nodeboxFile.contentHash === databaseFile.contentHash) {
      return null
    }

    // No conflict if one version is clearly newer
    const nodeboxTime = nodeboxFile.lastModified || new Date(0)
    const databaseTime = databaseFile.lastModified || new Date(0)
    const timeDiff = Math.abs(nodeboxTime.getTime() - databaseTime.getTime())

    // If time difference is less than 1 second, consider it a conflict
    // Otherwise, the newer version wins
    if (timeDiff > 1000) {
      return null
    }

    // Detect conflict type
    let conflictType: 'content' | 'metadata' | 'both' = 'content'
    
    if (nodeboxFile.size !== databaseFile.size ||
        nodeboxFile.mimeType !== databaseFile.mimeType ||
        nodeboxFile.encoding !== databaseFile.encoding) {
      conflictType = nodeboxFile.content !== databaseFile.content ? 'both' : 'metadata'
    }

    return {
      type: conflictType,
      nodeboxContent: nodeboxFile.content,
      databaseContent: databaseFile.content,
      nodeboxHash: nodeboxFile.contentHash,
      databaseHash: databaseFile.contentHash,
      nodeboxModified: nodeboxTime,
      databaseModified: databaseTime
    }
  }

  /**
   * Resolve conflict using specified strategy
   */
  async resolveConflict(
    conflict: ConflictData,
    options?: ConflictResolutionOptions
  ): Promise<ConflictData> {
    const strategy = options?.strategy || this.defaultStrategy
    const resolvedConflict = { ...conflict }

    switch (strategy) {
      case 'timestamp_based':
        resolvedConflict.resolvedContent = this.resolveByTimestamp(conflict)
        break

      case 'nodebox_wins':
        resolvedConflict.resolvedContent = conflict.nodeboxContent
        break

      case 'database_wins':
        resolvedConflict.resolvedContent = conflict.databaseContent
        break

      case 'merge_content':
        resolvedConflict.resolvedContent = await this.mergeContent(conflict, options)
        break

      case 'user_choice':
        resolvedConflict.resolvedContent = await this.getUserChoice(conflict, options)
        break

      case 'create_backup':
        resolvedConflict.resolvedContent = await this.createBackupAndResolve(conflict, options)
        break

      default:
        throw new Error(`Unknown conflict resolution strategy: ${strategy}`)
    }

    resolvedConflict.resolution = strategy
    resolvedConflict.resolvedAt = new Date()

    this.emit('conflict-resolved', resolvedConflict)
    return resolvedConflict
  }

  /**
   * Resolve conflict by timestamp (newer wins)
   */
  private resolveByTimestamp(conflict: ConflictData): string {
    return conflict.nodeboxModified > conflict.databaseModified
      ? conflict.nodeboxContent
      : conflict.databaseContent
  }

  /**
   * Merge content using different strategies
   */
  private async mergeContent(
    conflict: ConflictData,
    options?: ConflictResolutionOptions
  ): Promise<string> {
    const mergeStrategy = options?.mergeStrategy || 'line_by_line'

    switch (mergeStrategy) {
      case 'line_by_line':
        return this.mergeLineByLine(conflict)

      case 'block_based':
        return this.mergeBlockBased(conflict)

      case 'semantic':
        return this.mergeSemanticBased(conflict)

      default:
        throw new Error(`Unknown merge strategy: ${mergeStrategy}`)
    }
  }

  /**
   * Line-by-line merge (similar to git merge)
   */
  private mergeLineByLine(conflict: ConflictData): string {
    const nodeboxLines = conflict.nodeboxContent.split('\n')
    const databaseLines = conflict.databaseContent.split('\n')
    const mergedLines: string[] = []

    const maxLines = Math.max(nodeboxLines.length, databaseLines.length)

    for (let i = 0; i < maxLines; i++) {
      const nodeboxLine = nodeboxLines[i] || ''
      const databaseLine = databaseLines[i] || ''

      if (nodeboxLine === databaseLine) {
        mergedLines.push(nodeboxLine)
      } else {
        // Add conflict markers
        mergedLines.push('<<<<<<< Nodebox')
        mergedLines.push(nodeboxLine)
        mergedLines.push('=======')
        mergedLines.push(databaseLine)
        mergedLines.push('>>>>>>> Database')
      }
    }

    return mergedLines.join('\n')
  }

  /**
   * Block-based merge (merge by code blocks/sections)
   */
  private mergeBlockBased(conflict: ConflictData): string {
    // Simple implementation - can be enhanced with AST parsing
    const nodeboxBlocks = this.splitIntoBlocks(conflict.nodeboxContent)
    const databaseBlocks = this.splitIntoBlocks(conflict.databaseContent)

    const mergedBlocks: string[] = []
    const processedBlocks = new Set<string>()

    // Add unique blocks from both versions
    [...nodeboxBlocks, ...databaseBlocks].forEach(block => {
      const blockHash = this.calculateHash(block)
      if (!processedBlocks.has(blockHash)) {
        mergedBlocks.push(block)
        processedBlocks.add(blockHash)
      }
    })

    return mergedBlocks.join('\n\n')
  }

  /**
   * Semantic-based merge (for code files)
   */
  private mergeSemanticBased(conflict: ConflictData): string {
    // This would require AST parsing for proper implementation
    // For now, fall back to line-by-line merge
    return this.mergeLineByLine(conflict)
  }

  /**
   * Split content into logical blocks
   */
  private splitIntoBlocks(content: string): string[] {
    // Simple implementation - split by double newlines
    return content.split(/\n\s*\n/).filter(block => block.trim().length > 0)
  }

  /**
   * Get user choice for conflict resolution
   */
  private async getUserChoice(
    conflict: ConflictData,
    options?: ConflictResolutionOptions
  ): Promise<string> {
    if (options?.userChoice) {
      switch (options.userChoice) {
        case 'nodebox':
          return conflict.nodeboxContent
        case 'database':
          return conflict.databaseContent
        case 'merge':
          return this.mergeLineByLine(conflict)
        default:
          throw new Error(`Invalid user choice: ${options.userChoice}`)
      }
    }

    // If no user choice provided, fall back to timestamp-based resolution
    return this.resolveByTimestamp(conflict)
  }

  /**
   * Create backup and resolve conflict
   */
  private async createBackupAndResolve(
    conflict: ConflictData,
    options?: ConflictResolutionOptions
  ): Promise<string> {
    const suffix = options?.backupSuffix || '.backup'
    
    // In a real implementation, this would create backup files
    // For now, we'll use the newer version and note the backup in metadata
    const resolvedContent = this.resolveByTimestamp(conflict)
    
    // The backup creation would be handled by the calling code
    // This method just returns the resolved content
    return resolvedContent
  }

  /**
   * Calculate content hash
   */
  private calculateHash(content: string): string {
    return createHash('sha256').update(content, 'utf8').digest('hex')
  }

  /**
   * Validate conflict resolution options
   */
  validateResolutionOptions(options: ConflictResolutionOptions): string[] {
    const errors: string[] = []

    if (!options.strategy) {
      errors.push('Resolution strategy is required')
    }

    if (options.strategy === 'user_choice' && !options.userChoice) {
      errors.push('User choice is required when using user_choice strategy')
    }

    if (options.strategy === 'merge_content' && options.mergeStrategy) {
      const validMergeStrategies = ['line_by_line', 'block_based', 'semantic']
      if (!validMergeStrategies.includes(options.mergeStrategy)) {
        errors.push(`Invalid merge strategy: ${options.mergeStrategy}`)
      }
    }

    return errors
  }

  /**
   * Get conflict summary for display
   */
  getConflictSummary(conflict: ConflictData): {
    type: string
    nodeboxSize: number
    databaseSize: number
    timeDifference: number
    hasContentChanges: boolean
    hasMetadataChanges: boolean
  } {
    return {
      type: conflict.type,
      nodeboxSize: conflict.nodeboxContent.length,
      databaseSize: conflict.databaseContent.length,
      timeDifference: Math.abs(
        conflict.nodeboxModified.getTime() - conflict.databaseModified.getTime()
      ),
      hasContentChanges: conflict.nodeboxContent !== conflict.databaseContent,
      hasMetadataChanges: conflict.type === 'metadata' || conflict.type === 'both'
    }
  }

  /**
   * Check if conflict can be auto-resolved
   */
  canAutoResolve(conflict: ConflictData, strategy: ConflictResolutionStrategy): boolean {
    switch (strategy) {
      case 'timestamp_based':
      case 'nodebox_wins':
      case 'database_wins':
        return true

      case 'merge_content':
        // Can auto-resolve if content is mergeable (no overlapping changes)
        return this.isContentMergeable(conflict)

      case 'user_choice':
      case 'create_backup':
        return false

      default:
        return false
    }
  }

  /**
   * Check if content can be automatically merged
   */
  private isContentMergeable(conflict: ConflictData): boolean {
    // Simple heuristic - if one version is a subset of the other, it's mergeable
    const nodeboxContent = conflict.nodeboxContent
    const databaseContent = conflict.databaseContent

    return nodeboxContent.includes(databaseContent) || 
           databaseContent.includes(nodeboxContent)
  }

  /**
   * Get recommended resolution strategy
   */
  getRecommendedStrategy(conflict: ConflictData): ConflictResolutionStrategy {
    const summary = this.getConflictSummary(conflict)

    // If only metadata changes, prefer timestamp-based
    if (!summary.hasContentChanges) {
      return 'timestamp_based'
    }

    // If content is mergeable, suggest merge
    if (this.isContentMergeable(conflict)) {
      return 'merge_content'
    }

    // If time difference is significant, use timestamp-based
    if (summary.timeDifference > 60000) { // 1 minute
      return 'timestamp_based'
    }

    // Otherwise, require user choice
    return 'user_choice'
  }
}
