/**
 * File Watcher System for Real-time Synchronization
 */

import { EventEmitter } from 'events'
import { minimatch } from 'minimatch'
import {
  FileSyncConfig,
  FileWatchEvent,
  FileSyncEvents
} from './types'

export class FileWatcher extends EventEmitter {
  private config: FileSyncConfig
  private watchers: Map<string, any> = new Map()
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map()
  private isInitialized = false
  private watchedPaths: Set<string> = new Set()

  constructor(config: FileSyncConfig) {
    super()
    this.config = config
  }

  /**
   * Initialize file watchers
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // In a browser environment, we'll simulate file watching
      // In a Node.js environment, we would use fs.watch or chokidar
      this.setupSimulatedWatching()
      
      this.isInitialized = true
      console.log(`[FileWatcher] Initialized for project: ${this.config.projectId}`)
    } catch (error) {
      throw new Error(`Failed to initialize FileWatcher: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Setup simulated file watching for browser environment
   */
  private setupSimulatedWatching(): void {
    // In a real implementation, this would integrate with:
    // 1. Nodebox's file system events
    // 2. WebSocket connections for real-time updates
    // 3. Polling mechanisms for fallback
    
    console.log('[FileWatcher] Setting up simulated file watching')
    
    // Simulate periodic file system checks
    const checkInterval = setInterval(() => {
      this.performPeriodicCheck()
    }, this.config.syncInterval || 5000)

    this.watchers.set('periodic-check', checkInterval)
  }

  /**
   * Perform periodic file system check
   */
  private async performPeriodicCheck(): Promise<void> {
    try {
      // This would integrate with Nodebox's file system API
      // For now, we'll emit a simulated event
      const simulatedEvent: FileWatchEvent = {
        type: 'update',
        path: 'example/file.ts',
        timestamp: new Date(),
        source: 'nodebox'
      }

      // Only emit if file should be watched
      if (this.shouldWatchFile(simulatedEvent.path)) {
        this.handleFileEvent(simulatedEvent)
      }
    } catch (error) {
      console.error('[FileWatcher] Error during periodic check:', error)
    }
  }

  /**
   * Watch specific file or directory
   */
  async watchPath(path: string): Promise<void> {
    if (this.watchedPaths.has(path)) {
      return // Already watching
    }

    try {
      // In a real implementation, this would set up actual file system watchers
      this.watchedPaths.add(path)
      console.log(`[FileWatcher] Now watching: ${path}`)
      
      // Simulate watcher setup
      this.setupPathWatcher(path)
    } catch (error) {
      throw new Error(`Failed to watch path ${path}: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Stop watching specific path
   */
  async unwatchPath(path: string): Promise<void> {
    if (!this.watchedPaths.has(path)) {
      return // Not watching
    }

    try {
      this.watchedPaths.delete(path)
      
      // Remove any associated watchers
      const watcher = this.watchers.get(path)
      if (watcher) {
        if (typeof watcher.close === 'function') {
          watcher.close()
        } else if (typeof watcher === 'number') {
          clearInterval(watcher)
        }
        this.watchers.delete(path)
      }

      console.log(`[FileWatcher] Stopped watching: ${path}`)
    } catch (error) {
      console.error(`[FileWatcher] Error unwatching ${path}:`, error)
    }
  }

  /**
   * Setup watcher for specific path
   */
  private setupPathWatcher(path: string): void {
    // In a real implementation, this would use fs.watch or chokidar
    // For simulation, we'll create a mock watcher
    const mockWatcher = {
      path,
      close: () => {
        console.log(`[FileWatcher] Closed watcher for: ${path}`)
      }
    }

    this.watchers.set(path, mockWatcher)
  }

  /**
   * Handle file system events
   */
  private handleFileEvent(event: FileWatchEvent): void {
    // Check if file should be watched
    if (!this.shouldWatchFile(event.path)) {
      return
    }

    // Debounce rapid file changes
    this.debounceFileEvent(event)
  }

  /**
   * Debounce file events to prevent excessive sync operations
   */
  private debounceFileEvent(event: FileWatchEvent): void {
    const debounceKey = `${event.path}-${event.type}`
    
    // Clear existing timer
    const existingTimer = this.debounceTimers.get(debounceKey)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // Set new timer
    const timer = setTimeout(() => {
      this.emitFileEvent(event)
      this.debounceTimers.delete(debounceKey)
    }, 500) // 500ms debounce

    this.debounceTimers.set(debounceKey, timer)
  }

  /**
   * Emit file change event
   */
  private emitFileEvent(event: FileWatchEvent): void {
    console.log(`[FileWatcher] File ${event.type}: ${event.path}`)
    this.emit('file-change', event)
  }

  /**
   * Check if file should be watched based on include/exclude patterns
   */
  private shouldWatchFile(filePath: string): boolean {
    // Check exclude patterns first
    for (const pattern of this.config.excludePatterns) {
      if (minimatch(filePath, pattern)) {
        return false
      }
    }

    // Check include patterns
    if (this.config.includePatterns.length === 0) {
      return true // Include all if no patterns specified
    }

    for (const pattern of this.config.includePatterns) {
      if (minimatch(filePath, pattern)) {
        return true
      }
    }

    return false
  }

  /**
   * Manually trigger file change event (for testing/simulation)
   */
  triggerFileChange(path: string, type: 'create' | 'update' | 'delete' | 'rename', oldPath?: string): void {
    const event: FileWatchEvent = {
      type,
      path,
      oldPath,
      timestamp: new Date(),
      source: 'nodebox'
    }

    this.handleFileEvent(event)
  }

  /**
   * Get watched paths
   */
  getWatchedPaths(): string[] {
    return Array.from(this.watchedPaths)
  }

  /**
   * Get watcher statistics
   */
  getWatcherStats(): {
    watchedPaths: number
    activeWatchers: number
    pendingDebounces: number
  } {
    return {
      watchedPaths: this.watchedPaths.size,
      activeWatchers: this.watchers.size,
      pendingDebounces: this.debounceTimers.size
    }
  }

  /**
   * Enable/disable auto-sync on file changes
   */
  setAutoSync(enabled: boolean): void {
    this.config.autoSync = enabled
    console.log(`[FileWatcher] Auto-sync ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Update sync interval
   */
  setSyncInterval(interval: number): void {
    this.config.syncInterval = interval
    
    // Restart periodic check with new interval
    const periodicCheck = this.watchers.get('periodic-check')
    if (periodicCheck) {
      clearInterval(periodicCheck)
      
      const newInterval = setInterval(() => {
        this.performPeriodicCheck()
      }, interval)
      
      this.watchers.set('periodic-check', newInterval)
    }
    
    console.log(`[FileWatcher] Sync interval updated to ${interval}ms`)
  }

  /**
   * Update include/exclude patterns
   */
  updatePatterns(includePatterns?: string[], excludePatterns?: string[]): void {
    if (includePatterns) {
      this.config.includePatterns = includePatterns
    }
    if (excludePatterns) {
      this.config.excludePatterns = excludePatterns
    }
    
    console.log('[FileWatcher] Patterns updated:', {
      include: this.config.includePatterns,
      exclude: this.config.excludePatterns
    })
  }

  /**
   * Pause all watchers
   */
  pause(): void {
    // Clear all debounce timers
    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()
    
    console.log('[FileWatcher] Paused')
  }

  /**
   * Resume all watchers
   */
  resume(): void {
    console.log('[FileWatcher] Resumed')
  }

  /**
   * Cleanup all watchers and resources
   */
  async cleanup(): Promise<void> {
    try {
      // Clear all debounce timers
      this.debounceTimers.forEach(timer => clearTimeout(timer))
      this.debounceTimers.clear()

      // Close all watchers
      for (const [path, watcher] of this.watchers) {
        try {
          if (typeof watcher.close === 'function') {
            watcher.close()
          } else if (typeof watcher === 'number') {
            clearInterval(watcher)
          }
        } catch (error) {
          console.error(`[FileWatcher] Error closing watcher for ${path}:`, error)
        }
      }

      this.watchers.clear()
      this.watchedPaths.clear()
      this.isInitialized = false

      console.log('[FileWatcher] Cleanup completed')
    } catch (error) {
      console.error('[FileWatcher] Error during cleanup:', error)
      throw error
    }
  }

  /**
   * Health check
   */
  isHealthy(): boolean {
    return this.isInitialized && this.watchers.size > 0
  }
}

// Type-safe event emitter interface
export interface FileWatcher {
  on<K extends keyof FileSyncEvents>(
    event: K,
    listener: FileSyncEvents[K]
  ): this
  
  emit<K extends keyof FileSyncEvents>(
    event: K,
    ...args: Parameters<FileSyncEvents[K]>
  ): boolean
}
