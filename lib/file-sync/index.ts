/**
 * File Synchronization System - Main Entry Point
 */

import { PrismaClient } from '@prisma/client'
import { FileSyncEngine } from './sync-engine'
import { SyncDatabase } from './sync-database'
import { ConflictResolver } from './conflict-resolver'
import { FileWatcher } from './file-watcher'
import {
  FileSyncConfig,
  SyncResult,
  SyncProgress,
  FileSyncOptions,
  ConflictResolutionOptions,
  SyncSession,
  FileWatchEvent,
  SyncError,
  FileSyncEvents
} from './types'

export class FileSyncManager {
  private syncEngine: FileSyncEngine
  private syncDatabase: SyncDatabase
  private conflictResolver: ConflictResolver
  private fileWatcher: FileWatcher
  private config: FileSyncConfig
  private prisma: PrismaClient
  private isInitialized = false

  constructor(config: FileSyncConfig, prisma?: PrismaClient) {
    this.config = config
    this.prisma = prisma || new PrismaClient()
    
    this.syncDatabase = new SyncDatabase(this.prisma)
    this.conflictResolver = new ConflictResolver(config.conflictResolution)
    this.fileWatcher = new FileWatcher(config)
    this.syncEngine = new FileSyncEngine(config, this.prisma)
    
    this.setupEventHandlers()
  }

  private setupEventHandlers(): void {
    // Forward events from sync engine
    this.syncEngine.on('sync-start', (session) => this.emit('sync-start', session))
    this.syncEngine.on('sync-progress', (progress) => this.emit('sync-progress', progress))
    this.syncEngine.on('sync-complete', (result) => this.emit('sync-complete', result))
    this.syncEngine.on('sync-error', (error) => this.emit('sync-error', error))
    this.syncEngine.on('file-change', (event) => this.emit('file-change', event))
    this.syncEngine.on('conflict-detected', (conflict) => this.emit('conflict-detected', conflict))
    this.syncEngine.on('conflict-resolved', (conflict) => this.emit('conflict-resolved', conflict))
  }

  /**
   * Initialize the file sync system
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      await this.syncEngine.initialize()
      this.isInitialized = true
      
      console.log(`[FileSyncManager] Initialized for project: ${this.config.projectId}`)
    } catch (error) {
      throw new Error(`Failed to initialize FileSyncManager: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Perform full synchronization
   */
  async fullSync(options?: FileSyncOptions): Promise<SyncResult> {
    if (!this.isInitialized) {
      await this.initialize()
    }
    
    return this.syncEngine.fullSync(options)
  }

  /**
   * Perform incremental synchronization
   */
  async incrementalSync(options?: FileSyncOptions): Promise<SyncResult> {
    if (!this.isInitialized) {
      await this.initialize()
    }
    
    return this.syncEngine.incrementalSync(options)
  }

  /**
   * Sync specific files
   */
  async syncFiles(filePaths: string[], options?: FileSyncOptions): Promise<SyncResult> {
    if (!this.isInitialized) {
      await this.initialize()
    }
    
    return this.syncEngine.syncFiles(filePaths, options)
  }

  /**
   * Get project sync statistics
   */
  async getSyncStats(): Promise<{
    totalFiles: number
    syncedFiles: number
    conflictedFiles: number
    pendingFiles: number
    lastSyncTime: Date | null
    totalSize: number
  }> {
    return this.syncDatabase.getProjectSyncStats(this.config.projectId)
  }

  /**
   * Get sync history
   */
  async getSyncHistory(limit: number = 50): Promise<any[]> {
    return this.syncDatabase.getProjectSyncHistory(this.config.projectId, limit)
  }

  /**
   * Get sync sessions
   */
  async getSyncSessions(limit: number = 50): Promise<any[]> {
    return this.syncDatabase.getProjectSyncSessions(this.config.projectId, limit)
  }

  /**
   * Get files with conflicts
   */
  async getConflictedFiles(): Promise<any[]> {
    return this.syncDatabase.getFilesWithConflicts(this.config.projectId)
  }

  /**
   * Resolve specific conflict
   */
  async resolveConflict(
    filePath: string,
    options: ConflictResolutionOptions
  ): Promise<void> {
    const file = await this.syncDatabase.getProjectFile(this.config.projectId, filePath)
    if (!file || file.syncStatus !== 'conflict') {
      throw new Error(`No conflict found for file: ${filePath}`)
    }

    if (!file.conflictData) {
      throw new Error(`No conflict data found for file: ${filePath}`)
    }

    const resolvedConflict = await this.conflictResolver.resolveConflict(
      file.conflictData as any,
      options
    )

    if (!resolvedConflict.resolvedContent) {
      throw new Error('Failed to resolve conflict')
    }

    // Update file with resolved content
    await this.syncDatabase.resolveFileConflict(
      this.config.projectId,
      filePath,
      resolvedConflict.resolvedContent,
      this.calculateHash(resolvedConflict.resolvedContent),
      options.strategy
    )
  }

  /**
   * Watch files for changes
   */
  async startWatching(): Promise<void> {
    if (!this.config.enableWatchers) {
      throw new Error('File watching is disabled in configuration')
    }

    await this.fileWatcher.initialize()
  }

  /**
   * Stop watching files
   */
  async stopWatching(): Promise<void> {
    await this.fileWatcher.cleanup()
  }

  /**
   * Manually trigger file change (for testing)
   */
  triggerFileChange(
    path: string,
    type: 'create' | 'update' | 'delete' | 'rename',
    oldPath?: string
  ): void {
    this.fileWatcher.triggerFileChange(path, type, oldPath)
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<FileSyncConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // Update components with new config
    if (newConfig.autoSync !== undefined) {
      this.fileWatcher.setAutoSync(newConfig.autoSync)
    }
    
    if (newConfig.syncInterval !== undefined) {
      this.fileWatcher.setSyncInterval(newConfig.syncInterval)
    }
    
    if (newConfig.includePatterns || newConfig.excludePatterns) {
      this.fileWatcher.updatePatterns(
        newConfig.includePatterns,
        newConfig.excludePatterns
      )
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): Readonly<FileSyncConfig> {
    return Object.freeze({ ...this.config })
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    database: boolean
    syncEngine: boolean
    fileWatcher: boolean
    overall: boolean
  }> {
    const [databaseHealth, fileWatcherHealth] = await Promise.all([
      this.syncDatabase.healthCheck(),
      Promise.resolve(this.fileWatcher.isHealthy())
    ])

    const syncEngineHealth = this.isInitialized
    const overall = databaseHealth && syncEngineHealth

    return {
      database: databaseHealth,
      syncEngine: syncEngineHealth,
      fileWatcher: fileWatcherHealth,
      overall
    }
  }

  /**
   * Cleanup old sync data
   */
  async cleanup(olderThan: Date): Promise<{
    historyRecordsDeleted: number
    sessionsDeleted: number
  }> {
    const [historyRecordsDeleted, sessionsDeleted] = await Promise.all([
      this.syncDatabase.cleanupOldSyncHistory(this.config.projectId, olderThan),
      this.syncDatabase.cleanupOldSyncSessions(this.config.projectId, olderThan)
    ])

    return { historyRecordsDeleted, sessionsDeleted }
  }

  /**
   * Calculate content hash
   */
  private calculateHash(content: string): string {
    const crypto = require('crypto')
    return crypto.createHash('sha256').update(content, 'utf8').digest('hex')
  }

  /**
   * Shutdown the sync manager
   */
  async shutdown(): Promise<void> {
    try {
      await this.syncEngine.cleanup()
      await this.fileWatcher.cleanup()
      await this.syncDatabase.disconnect()
      
      this.isInitialized = false
      console.log(`[FileSyncManager] Shutdown completed for project: ${this.config.projectId}`)
    } catch (error) {
      console.error('[FileSyncManager] Error during shutdown:', error)
      throw error
    }
  }

  // Event emitter methods
  on(event: string, listener: (...args: any[]) => void): this {
    return super.on(event, listener)
  }

  emit(event: string, ...args: any[]): boolean {
    return super.emit(event, ...args)
  }
}

// Create default configuration
export function createDefaultSyncConfig(projectId: string): FileSyncConfig {
  return {
    projectId,
    autoSync: true,
    syncInterval: 5000, // 5 seconds
    conflictResolution: 'timestamp_based',
    excludePatterns: [
      'node_modules/**',
      '.next/**',
      'dist/**',
      'build/**',
      '.git/**',
      '*.log',
      '.env*',
      '*.tmp',
      '*.temp'
    ],
    includePatterns: [
      '**/*.{ts,tsx,js,jsx,json,md,css,scss,html,yml,yaml}'
    ],
    maxFileSize: 10 * 1024 * 1024, // 10MB
    enableWatchers: true,
    batchSize: 50,
    retryAttempts: 3,
    retryDelay: 1000
  }
}

// Export all types and classes
export * from './types'
export { FileSyncEngine } from './sync-engine'
export { SyncDatabase } from './sync-database'
export { ConflictResolver } from './conflict-resolver'
export { FileWatcher } from './file-watcher'
