/**
 * Type definitions for the File Synchronization System
 */

export interface FileSyncConfig {
  projectId: string
  autoSync: boolean
  syncInterval: number // milliseconds
  conflictResolution: ConflictResolutionStrategy
  excludePatterns: string[]
  includePatterns: string[]
  maxFileSize: number // bytes
  enableWatchers: boolean
  batchSize: number
  retryAttempts: number
  retryDelay: number
}

export type ConflictResolutionStrategy = 
  | 'timestamp_based'    // Use most recent timestamp
  | 'user_choice'        // Prompt user for decision
  | 'nodebox_wins'       // Always prefer Nodebox version
  | 'database_wins'      // Always prefer database version
  | 'merge_content'      // Attempt to merge content
  | 'create_backup'      // Create backup and use one version

export type SyncDirection = 
  | 'nodebox_to_db'      // Sync from Nodebox to database
  | 'db_to_nodebox'      // Sync from database to Nodebox
  | 'bidirectional'      // Sync in both directions

export type SyncOperation = 
  | 'create'
  | 'update' 
  | 'delete'
  | 'conflict_resolve'
  | 'backup_create'

export type SyncStatus = 
  | 'synced'
  | 'pending'
  | 'conflict'
  | 'error'
  | 'excluded'

export interface FileMetadata {
  path: string
  filename: string
  directory?: string
  size: number
  mimeType: string
  encoding: string
  contentHash: string
  nodeboxVersion: number
  dbVersion: number
  lastModified: Date
  isGenerated: boolean
  isUserModified: boolean
  isSystemFile: boolean
}

export interface SyncFileInfo extends FileMetadata {
  content: string
  syncStatus: SyncStatus
  conflictData?: ConflictData
  lastSyncedAt?: Date
}

export interface ConflictData {
  type: 'content' | 'metadata' | 'both'
  nodeboxContent: string
  databaseContent: string
  nodeboxHash: string
  databaseHash: string
  nodeboxModified: Date
  databaseModified: Date
  resolution?: ConflictResolutionStrategy
  resolvedContent?: string
  resolvedBy?: string
  resolvedAt?: Date
}

export interface SyncResult {
  success: boolean
  operation: SyncOperation
  direction: SyncDirection
  filesProcessed: number
  filesCreated: number
  filesUpdated: number
  filesDeleted: number
  conflictsFound: number
  conflictsResolved: number
  bytesTransferred: number
  duration: number
  errors: SyncError[]
  warnings: string[]
  sessionId: string
}

export interface SyncError {
  type: 'file_error' | 'network_error' | 'permission_error' | 'validation_error'
  message: string
  filePath?: string
  code?: string
  details?: any
  retryable: boolean
}

export interface SyncProgress {
  sessionId: string
  phase: SyncPhase
  progress: number // 0.0 to 1.0
  currentFile?: string
  filesProcessed: number
  totalFiles: number
  bytesTransferred: number
  totalBytes: number
  estimatedTimeRemaining?: number
  errors: SyncError[]
}

export type SyncPhase = 
  | 'initializing'
  | 'scanning'
  | 'comparing'
  | 'syncing'
  | 'resolving_conflicts'
  | 'finalizing'
  | 'completed'
  | 'failed'

export interface FileWatchEvent {
  type: 'create' | 'update' | 'delete' | 'rename'
  path: string
  oldPath?: string // for rename events
  timestamp: Date
  source: 'nodebox' | 'database'
}

export interface SyncSession {
  id: string
  projectId: string
  sessionType: 'full_sync' | 'incremental' | 'conflict_resolution' | 'manual'
  trigger: 'user_action' | 'auto_sync' | 'file_watcher' | 'api_call'
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  startedAt: Date
  completedAt?: Date
  progress: number
  statistics: SyncStatistics
  errorMessage?: string
  metadata?: Record<string, any>
}

export interface SyncStatistics {
  filesScanned: number
  filesCreated: number
  filesUpdated: number
  filesDeleted: number
  conflictsFound: number
  conflictsResolved: number
  bytesTransferred: number
  duration?: number
}

export interface FileSyncOptions {
  direction?: SyncDirection
  force?: boolean
  dryRun?: boolean
  includePatterns?: string[]
  excludePatterns?: string[]
  conflictResolution?: ConflictResolutionStrategy
  batchSize?: number
  maxConcurrency?: number
}

export interface ConflictResolutionOptions {
  strategy: ConflictResolutionStrategy
  userChoice?: 'nodebox' | 'database' | 'merge'
  mergeStrategy?: 'line_by_line' | 'block_based' | 'semantic'
  createBackup?: boolean
  backupSuffix?: string
}

// Event types for the sync engine
export interface FileSyncEvents {
  'sync-start': (session: SyncSession) => void
  'sync-progress': (progress: SyncProgress) => void
  'sync-complete': (result: SyncResult) => void
  'sync-error': (error: SyncError) => void
  'file-change': (event: FileWatchEvent) => void
  'conflict-detected': (conflict: ConflictData) => void
  'conflict-resolved': (conflict: ConflictData) => void
}

// Database models (matching Prisma schema)
export interface ProjectFileRecord {
  id: string
  projectId: string
  path: string
  filename: string
  directory?: string
  content: string
  contentHash: string
  mimeType: string
  size: number
  encoding: string
  nodeboxVersion: number
  dbVersion: number
  lastSyncedAt?: Date
  syncStatus: string
  conflictData?: any
  isGenerated: boolean
  isUserModified: boolean
  isSystemFile: boolean
  createdAt: Date
  updatedAt: Date
  nodeboxModifiedAt: Date
}

export interface FileSyncHistoryRecord {
  id: string
  projectFileId: string
  operation: string
  direction: string
  fromVersion: number
  toVersion: number
  contentBefore?: string
  contentAfter?: string
  hashBefore?: string
  hashAfter?: string
  syncMethod: string
  conflictResolution?: string
  syncDuration?: number
  bytesTransferred?: number
  success: boolean
  errorMessage?: string
  retryCount: number
  createdAt: Date
}

export interface SyncSessionRecord {
  id: string
  projectId: string
  sessionType: string
  trigger: string
  filesScanned: number
  filesCreated: number
  filesUpdated: number
  filesDeleted: number
  conflictsFound: number
  conflictsResolved: number
  startedAt: Date
  completedAt?: Date
  duration?: number
  bytesTransferred: number
  status: string
  progress: number
  errorMessage?: string
  errorDetails?: any
  metadata?: any
}

// Utility types
export type FileFilter = (file: FileMetadata) => boolean
export type ConflictResolver = (conflict: ConflictData) => Promise<ConflictResolutionOptions>
export type ProgressCallback = (progress: SyncProgress) => void
export type ErrorCallback = (error: SyncError) => void

// Configuration validation
export interface SyncConfigValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Performance metrics
export interface SyncPerformanceMetrics {
  sessionId: string
  totalDuration: number
  scanDuration: number
  syncDuration: number
  conflictResolutionDuration: number
  averageFileProcessingTime: number
  throughputBytesPerSecond: number
  throughputFilesPerSecond: number
  memoryUsage: number
  networkLatency?: number
}

// Export all types
export type {
  FileSyncConfig,
  ConflictResolutionStrategy,
  SyncDirection,
  SyncOperation,
  SyncStatus,
  FileMetadata,
  SyncFileInfo,
  ConflictData,
  SyncResult,
  SyncError,
  SyncProgress,
  SyncPhase,
  FileWatchEvent,
  SyncSession,
  SyncStatistics,
  FileSyncOptions,
  ConflictResolutionOptions,
  FileSyncEvents,
  ProjectFileRecord,
  FileSyncHistoryRecord,
  SyncSessionRecord,
  FileFilter,
  ConflictResolver,
  ProgressCallback,
  ErrorCallback,
  SyncConfigValidation,
  SyncPerformanceMetrics
}
