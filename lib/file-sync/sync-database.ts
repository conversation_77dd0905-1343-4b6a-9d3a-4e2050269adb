/**
 * Database interface for file synchronization
 */

import { PrismaClient } from '@prisma/client'
import {
  ProjectFileRecord,
  FileSyncHistoryRecord,
  SyncSessionRecord,
  SyncFileInfo,
  FileMetadata,
  ConflictData
} from './types'

export class SyncDatabase {
  private prisma: PrismaClient

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
  }

  async initialize(): Promise<void> {
    // Ensure database connection is ready
    await this.prisma.$connect()
  }

  /**
   * Project File Operations
   */
  async getProjectFile(projectId: string, path: string): Promise<ProjectFileRecord | null> {
    return await this.prisma.projectFile.findUnique({
      where: {
        projectId_path: {
          projectId,
          path
        }
      }
    })
  }

  async getProjectFiles(projectId: string): Promise<ProjectFileRecord[]> {
    return await this.prisma.projectFile.findMany({
      where: { projectId },
      orderBy: { path: 'asc' }
    })
  }

  async getModifiedFilesSince(projectId: string, since: Date): Promise<ProjectFileRecord[]> {
    return await this.prisma.projectFile.findMany({
      where: {
        projectId,
        OR: [
          { updatedAt: { gt: since } },
          { nodeboxModifiedAt: { gt: since } }
        ]
      },
      orderBy: { updatedAt: 'desc' }
    })
  }

  async createProjectFile(data: Omit<ProjectFileRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProjectFileRecord> {
    return await this.prisma.projectFile.create({
      data: {
        ...data,
        syncStatus: data.syncStatus || 'synced'
      }
    })
  }

  async updateProjectFile(
    projectId: string,
    path: string,
    data: Partial<Omit<ProjectFileRecord, 'id' | 'projectId' | 'path' | 'createdAt'>>
  ): Promise<ProjectFileRecord> {
    return await this.prisma.projectFile.update({
      where: {
        projectId_path: {
          projectId,
          path
        }
      },
      data: {
        ...data,
        updatedAt: new Date()
      }
    })
  }

  async deleteProjectFile(projectId: string, path: string): Promise<void> {
    await this.prisma.projectFile.delete({
      where: {
        projectId_path: {
          projectId,
          path
        }
      }
    })
  }

  async upsertProjectFile(
    projectId: string,
    path: string,
    data: Omit<ProjectFileRecord, 'id' | 'projectId' | 'path' | 'createdAt' | 'updatedAt'>
  ): Promise<ProjectFileRecord> {
    return await this.prisma.projectFile.upsert({
      where: {
        projectId_path: {
          projectId,
          path
        }
      },
      create: {
        projectId,
        path,
        ...data
      },
      update: {
        ...data,
        updatedAt: new Date()
      }
    })
  }

  /**
   * Sync History Operations
   */
  async createSyncHistory(data: Omit<FileSyncHistoryRecord, 'id' | 'createdAt'>): Promise<FileSyncHistoryRecord> {
    return await this.prisma.fileSyncHistory.create({
      data
    })
  }

  async getSyncHistory(
    projectFileId: string,
    limit: number = 50
  ): Promise<FileSyncHistoryRecord[]> {
    return await this.prisma.fileSyncHistory.findMany({
      where: { projectFileId },
      orderBy: { createdAt: 'desc' },
      take: limit
    })
  }

  async getProjectSyncHistory(
    projectId: string,
    limit: number = 100
  ): Promise<(FileSyncHistoryRecord & { projectFile: ProjectFileRecord })[]> {
    return await this.prisma.fileSyncHistory.findMany({
      where: {
        projectFile: {
          projectId
        }
      },
      include: {
        projectFile: true
      },
      orderBy: { createdAt: 'desc' },
      take: limit
    })
  }

  /**
   * Sync Session Operations
   */
  async createSyncSession(data: Omit<SyncSessionRecord, 'id' | 'startedAt'>): Promise<SyncSessionRecord> {
    return await this.prisma.syncSession.create({
      data: {
        ...data,
        startedAt: new Date()
      }
    })
  }

  async updateSyncSession(
    sessionId: string,
    data: Partial<Omit<SyncSessionRecord, 'id' | 'projectId' | 'startedAt'>>
  ): Promise<SyncSessionRecord> {
    return await this.prisma.syncSession.update({
      where: { id: sessionId },
      data
    })
  }

  async getSyncSession(sessionId: string): Promise<SyncSessionRecord | null> {
    return await this.prisma.syncSession.findUnique({
      where: { id: sessionId }
    })
  }

  async getProjectSyncSessions(
    projectId: string,
    limit: number = 50
  ): Promise<SyncSessionRecord[]> {
    return await this.prisma.syncSession.findMany({
      where: { projectId },
      orderBy: { startedAt: 'desc' },
      take: limit
    })
  }

  async getLastSyncTime(projectId: string): Promise<Date | null> {
    const lastSession = await this.prisma.syncSession.findFirst({
      where: {
        projectId,
        status: 'completed'
      },
      orderBy: { completedAt: 'desc' },
      select: { completedAt: true }
    })

    return lastSession?.completedAt || null
  }

  /**
   * Conflict Management
   */
  async getFilesWithConflicts(projectId: string): Promise<ProjectFileRecord[]> {
    return await this.prisma.projectFile.findMany({
      where: {
        projectId,
        syncStatus: 'conflict'
      },
      orderBy: { updatedAt: 'desc' }
    })
  }

  async markFileAsConflicted(
    projectId: string,
    path: string,
    conflictData: ConflictData
  ): Promise<ProjectFileRecord> {
    return await this.updateProjectFile(projectId, path, {
      syncStatus: 'conflict',
      conflictData: conflictData as any
    })
  }

  async resolveFileConflict(
    projectId: string,
    path: string,
    resolvedContent: string,
    resolvedHash: string,
    resolution: string
  ): Promise<ProjectFileRecord> {
    return await this.updateProjectFile(projectId, path, {
      content: resolvedContent,
      contentHash: resolvedHash,
      syncStatus: 'synced',
      conflictData: null,
      lastSyncedAt: new Date()
    })
  }

  /**
   * Batch Operations
   */
  async batchUpdateFiles(
    updates: Array<{
      projectId: string
      path: string
      data: Partial<Omit<ProjectFileRecord, 'id' | 'projectId' | 'path' | 'createdAt'>>
    }>
  ): Promise<void> {
    await this.prisma.$transaction(
      updates.map(({ projectId, path, data }) =>
        this.prisma.projectFile.update({
          where: {
            projectId_path: {
              projectId,
              path
            }
          },
          data: {
            ...data,
            updatedAt: new Date()
          }
        })
      )
    )
  }

  async batchCreateFiles(
    files: Array<Omit<ProjectFileRecord, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<void> {
    await this.prisma.projectFile.createMany({
      data: files.map(file => ({
        ...file,
        syncStatus: file.syncStatus || 'synced'
      })),
      skipDuplicates: true
    })
  }

  /**
   * Statistics and Analytics
   */
  async getProjectSyncStats(projectId: string): Promise<{
    totalFiles: number
    syncedFiles: number
    conflictedFiles: number
    pendingFiles: number
    lastSyncTime: Date | null
    totalSize: number
  }> {
    const [stats, lastSync] = await Promise.all([
      this.prisma.projectFile.groupBy({
        by: ['syncStatus'],
        where: { projectId },
        _count: { id: true },
        _sum: { size: true }
      }),
      this.getLastSyncTime(projectId)
    ])

    const result = {
      totalFiles: 0,
      syncedFiles: 0,
      conflictedFiles: 0,
      pendingFiles: 0,
      lastSyncTime: lastSync,
      totalSize: 0
    }

    stats.forEach(stat => {
      result.totalFiles += stat._count.id
      result.totalSize += stat._sum.size || 0

      switch (stat.syncStatus) {
        case 'synced':
          result.syncedFiles = stat._count.id
          break
        case 'conflict':
          result.conflictedFiles = stat._count.id
          break
        case 'pending':
          result.pendingFiles = stat._count.id
          break
      }
    })

    return result
  }

  /**
   * Cleanup Operations
   */
  async cleanupOldSyncHistory(projectId: string, olderThan: Date): Promise<number> {
    const result = await this.prisma.fileSyncHistory.deleteMany({
      where: {
        projectFile: {
          projectId
        },
        createdAt: {
          lt: olderThan
        }
      }
    })

    return result.count
  }

  async cleanupOldSyncSessions(projectId: string, olderThan: Date): Promise<number> {
    const result = await this.prisma.syncSession.deleteMany({
      where: {
        projectId,
        startedAt: {
          lt: olderThan
        },
        status: {
          in: ['completed', 'failed', 'cancelled']
        }
      }
    })

    return result.count
  }

  /**
   * Health Check
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`
      return true
    } catch {
      return false
    }
  }

  /**
   * Close database connection
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect()
  }
}
