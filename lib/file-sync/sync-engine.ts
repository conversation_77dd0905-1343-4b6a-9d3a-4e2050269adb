/**
 * File Synchronization Engine
 * Handles bidirectional sync between Nodebox and database
 */

import { EventEmitter } from 'events'
import { createHash } from 'crypto'
import { minimatch } from 'minimatch'
import { PrismaClient } from '@prisma/client'
import {
  FileSyncConfig,
  SyncDirection,
  SyncR<PERSON>ult,
  Sync<PERSON>rogress,
  SyncSession,
  FileMetadata,
  SyncFileInfo,
  ConflictData,
  ConflictResolutionStrategy,
  FileSyncOptions,
  SyncError,
  FileSyncEvents,
  SyncPhase,
  FileWatchEvent
} from './types'
import { ConflictResolver } from './conflict-resolver'
import { FileWatcher } from './file-watcher'
import { SyncDatabase } from './sync-database'

export class FileSyncEngine extends EventEmitter {
  private config: FileSyncConfig
  private prisma: PrismaClient
  private conflictResolver: ConflictResolver
  private fileWatcher: FileWatcher
  private syncDatabase: SyncDatabase
  private activeSessions: Map<string, SyncSession> = new Map()
  private isInitialized = false

  constructor(config: FileSyncConfig, prisma: PrismaClient) {
    super()
    this.config = config
    this.prisma = prisma
    this.conflictResolver = new ConflictResolver(config.conflictResolution)
    this.fileWatcher = new FileWatcher(config)
    this.syncDatabase = new SyncDatabase(prisma)
    
    this.setupEventHandlers()
  }

  private setupEventHandlers(): void {
    // File watcher events
    this.fileWatcher.on('file-change', (event: FileWatchEvent) => {
      this.emit('file-change', event)
      if (this.config.autoSync) {
        this.handleFileChange(event)
      }
    })

    // Conflict resolver events
    this.conflictResolver.on('conflict-resolved', (conflict: ConflictData) => {
      this.emit('conflict-resolved', conflict)
    })
  }

  /**
   * Initialize the sync engine
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      await this.syncDatabase.initialize()
      
      if (this.config.enableWatchers) {
        await this.fileWatcher.initialize()
      }

      this.isInitialized = true
      console.log(`[FileSyncEngine] Initialized for project: ${this.config.projectId}`)
    } catch (error) {
      throw new Error(`Failed to initialize FileSyncEngine: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Perform full synchronization
   */
  async fullSync(options: FileSyncOptions = {}): Promise<SyncResult> {
    const sessionId = await this.createSyncSession('full_sync', 'api_call')
    
    try {
      const session = await this.getSyncSession(sessionId)
      this.emit('sync-start', session)

      // Phase 1: Scan files
      this.updateProgress(sessionId, 'scanning', 0.1)
      const nodeboxFiles = await this.scanNodeboxFiles()
      const databaseFiles = await this.scanDatabaseFiles()

      // Phase 2: Compare and identify changes
      this.updateProgress(sessionId, 'comparing', 0.3)
      const syncPlan = await this.createSyncPlan(nodeboxFiles, databaseFiles, options)

      // Phase 3: Execute sync operations
      this.updateProgress(sessionId, 'syncing', 0.5)
      const result = await this.executeSyncPlan(sessionId, syncPlan, options)

      // Phase 4: Resolve conflicts if any
      if (result.conflictsFound > 0) {
        this.updateProgress(sessionId, 'resolving_conflicts', 0.8)
        await this.resolveConflicts(sessionId, options)
      }

      // Phase 5: Finalize
      this.updateProgress(sessionId, 'finalizing', 0.9)
      await this.finalizeSyncSession(sessionId, result)
      
      this.updateProgress(sessionId, 'completed', 1.0)
      this.emit('sync-complete', result)

      return result
    } catch (error) {
      await this.handleSyncError(sessionId, error as Error)
      throw error
    }
  }

  /**
   * Perform incremental synchronization
   */
  async incrementalSync(options: FileSyncOptions = {}): Promise<SyncResult> {
    const sessionId = await this.createSyncSession('incremental', 'api_call')
    
    try {
      const session = await this.getSyncSession(sessionId)
      this.emit('sync-start', session)

      // Get files modified since last sync
      const lastSyncTime = await this.getLastSyncTime()
      const modifiedFiles = await this.getModifiedFilesSince(lastSyncTime)

      if (modifiedFiles.length === 0) {
        const result: SyncResult = {
          success: true,
          operation: 'update',
          direction: options.direction || 'bidirectional',
          filesProcessed: 0,
          filesCreated: 0,
          filesUpdated: 0,
          filesDeleted: 0,
          conflictsFound: 0,
          conflictsResolved: 0,
          bytesTransferred: 0,
          duration: 0,
          errors: [],
          warnings: [],
          sessionId
        }
        
        await this.finalizeSyncSession(sessionId, result)
        this.emit('sync-complete', result)
        return result
      }

      // Process modified files
      this.updateProgress(sessionId, 'syncing', 0.5)
      const result = await this.processModifiedFiles(sessionId, modifiedFiles, options)

      await this.finalizeSyncSession(sessionId, result)
      this.emit('sync-complete', result)

      return result
    } catch (error) {
      await this.handleSyncError(sessionId, error as Error)
      throw error
    }
  }

  /**
   * Sync specific files
   */
  async syncFiles(filePaths: string[], options: FileSyncOptions = {}): Promise<SyncResult> {
    const sessionId = await this.createSyncSession('manual', 'api_call')
    
    try {
      const session = await this.getSyncSession(sessionId)
      this.emit('sync-start', session)

      const result = await this.processSyncFiles(sessionId, filePaths, options)
      
      await this.finalizeSyncSession(sessionId, result)
      this.emit('sync-complete', result)

      return result
    } catch (error) {
      await this.handleSyncError(sessionId, error as Error)
      throw error
    }
  }

  /**
   * Handle file change events
   */
  private async handleFileChange(event: FileWatchEvent): Promise<void> {
    if (!this.shouldSyncFile(event.path)) {
      return
    }

    try {
      const sessionId = await this.createSyncSession('incremental', 'file_watcher')
      await this.processSyncFiles(sessionId, [event.path], {
        direction: event.source === 'nodebox' ? 'nodebox_to_db' : 'db_to_nodebox'
      })
    } catch (error) {
      this.emit('sync-error', {
        type: 'file_error',
        message: `Failed to sync file change: ${error instanceof Error ? error.message : 'Unknown error'}`,
        filePath: event.path,
        retryable: true
      })
    }
  }

  /**
   * Check if file should be synced based on include/exclude patterns
   */
  private shouldSyncFile(filePath: string): boolean {
    // Check exclude patterns first
    for (const pattern of this.config.excludePatterns) {
      if (minimatch(filePath, pattern)) {
        return false
      }
    }

    // Check include patterns
    if (this.config.includePatterns.length === 0) {
      return true // Include all if no patterns specified
    }

    for (const pattern of this.config.includePatterns) {
      if (minimatch(filePath, pattern)) {
        return true
      }
    }

    return false
  }

  /**
   * Calculate content hash
   */
  private calculateHash(content: string): string {
    return createHash('sha256').update(content, 'utf8').digest('hex')
  }

  /**
   * Create sync session
   */
  private async createSyncSession(
    sessionType: string,
    trigger: string
  ): Promise<string> {
    const session = await this.syncDatabase.createSyncSession({
      projectId: this.config.projectId,
      sessionType,
      trigger,
      status: 'running',
      progress: 0
    })

    this.activeSessions.set(session.id, {
      id: session.id,
      projectId: this.config.projectId,
      sessionType: sessionType as any,
      trigger: trigger as any,
      status: 'running',
      startedAt: new Date(),
      progress: 0,
      statistics: {
        filesScanned: 0,
        filesCreated: 0,
        filesUpdated: 0,
        filesDeleted: 0,
        conflictsFound: 0,
        conflictsResolved: 0,
        bytesTransferred: 0
      }
    })

    return session.id
  }

  /**
   * Update sync progress
   */
  private updateProgress(
    sessionId: string,
    phase: SyncPhase,
    progress: number,
    currentFile?: string
  ): void {
    const session = this.activeSessions.get(sessionId)
    if (!session) return

    session.progress = progress

    const progressEvent: SyncProgress = {
      sessionId,
      phase,
      progress,
      currentFile,
      filesProcessed: session.statistics.filesCreated + session.statistics.filesUpdated,
      totalFiles: 0, // Will be updated as we discover files
      bytesTransferred: session.statistics.bytesTransferred,
      totalBytes: 0, // Will be updated as we discover files
      errors: []
    }

    this.emit('sync-progress', progressEvent)
  }

  /**
   * Get sync session
   */
  private async getSyncSession(sessionId: string): Promise<SyncSession> {
    const session = this.activeSessions.get(sessionId)
    if (!session) {
      throw new Error(`Sync session not found: ${sessionId}`)
    }
    return session
  }

  /**
   * Placeholder methods - will be implemented in the next part
   */
  private async scanNodeboxFiles(): Promise<SyncFileInfo[]> {
    // Implementation will be added
    return []
  }

  private async scanDatabaseFiles(): Promise<SyncFileInfo[]> {
    // Implementation will be added
    return []
  }

  private async createSyncPlan(
    nodeboxFiles: SyncFileInfo[],
    databaseFiles: SyncFileInfo[],
    options: FileSyncOptions
  ): Promise<any> {
    // Implementation will be added
    return {}
  }

  private async executeSyncPlan(
    sessionId: string,
    syncPlan: any,
    options: FileSyncOptions
  ): Promise<SyncResult> {
    // Implementation will be added
    return {} as SyncResult
  }

  private async resolveConflicts(
    sessionId: string,
    options: FileSyncOptions
  ): Promise<void> {
    // Implementation will be added
  }

  private async finalizeSyncSession(
    sessionId: string,
    result: SyncResult
  ): Promise<void> {
    // Implementation will be added
  }

  private async handleSyncError(sessionId: string, error: Error): Promise<void> {
    // Implementation will be added
  }

  private async getLastSyncTime(): Promise<Date> {
    // Implementation will be added
    return new Date()
  }

  private async getModifiedFilesSince(since: Date): Promise<SyncFileInfo[]> {
    // Implementation will be added
    return []
  }

  private async processModifiedFiles(
    sessionId: string,
    files: SyncFileInfo[],
    options: FileSyncOptions
  ): Promise<SyncResult> {
    // Implementation will be added
    return {} as SyncResult
  }

  private async processSyncFiles(
    sessionId: string,
    filePaths: string[],
    options: FileSyncOptions
  ): Promise<SyncResult> {
    // Implementation will be added
    return {} as SyncResult
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.fileWatcher) {
      await this.fileWatcher.cleanup()
    }
    this.activeSessions.clear()
    this.isInitialized = false
  }
}

// Type-safe event emitter interface
export interface FileSyncEngine {
  on<K extends keyof FileSyncEvents>(
    event: K,
    listener: FileSyncEvents[K]
  ): this
  
  emit<K extends keyof FileSyncEvents>(
    event: K,
    ...args: Parameters<FileSyncEvents[K]>
  ): boolean
}
