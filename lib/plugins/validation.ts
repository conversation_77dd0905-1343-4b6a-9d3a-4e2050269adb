/**
 * Plugin Validation
 * 
 * Validates plugin manifests and other plugin-related data.
 */

import { PluginManifest, PluginMetadata, PluginDependency, PluginSettingsDefinition } from './types';

/**
 * Validate a plugin manifest
 */
export function validateManifest(manifest: PluginManifest): void {
  // Check if manifest is an object
  if (!manifest || typeof manifest !== 'object') {
    throw new Error('Plugin manifest must be an object');
  }

  // Validate metadata
  validateMetadata(manifest.metadata);

  // Validate dependencies if present
  if (manifest.dependencies) {
    validateDependencies(manifest.dependencies);
  }

  // Validate permissions if present
  if (manifest.permissions) {
    validatePermissions(manifest.permissions);
  }

  // Validate settings if present
  if (manifest.settings) {
    validateSettings(manifest.settings);
  }
}

/**
 * Validate plugin metadata
 */
function validateMetadata(metadata: PluginMetadata): void {
  // Check if metadata is an object
  if (!metadata || typeof metadata !== 'object') {
    throw new Error('Plugin metadata must be an object');
  }

  // Check required fields
  const requiredFields: (keyof PluginMetadata)[] = ['id', 'name', 'version', 'description', 'author'];
  for (const field of requiredFields) {
    if (!metadata[field]) {
      throw new Error(`Plugin metadata is missing required field: ${field}`);
    }
  }

  // Validate ID format (alphanumeric, dashes, and dots only)
  if (!/^[a-z0-9-_.]+$/.test(metadata.id)) {
    throw new Error('Plugin ID must contain only lowercase letters, numbers, dashes, underscores, and dots');
  }

  // Validate version format (semver)
  if (!/^\d+\.\d+\.\d+(-[a-zA-Z0-9.]+)?$/.test(metadata.version)) {
    throw new Error('Plugin version must follow semantic versioning (e.g., 1.0.0 or 1.0.0-beta.1)');
  }

  // Validate URLs if present
  if (metadata.homepage && !isValidUrl(metadata.homepage)) {
    throw new Error('Plugin homepage must be a valid URL');
  }

  if (metadata.repository && !isValidUrl(metadata.repository)) {
    throw new Error('Plugin repository must be a valid URL');
  }
}

/**
 * Validate plugin dependencies
 */
function validateDependencies(dependencies: PluginDependency[]): void {
  // Check if dependencies is an array
  if (!Array.isArray(dependencies)) {
    throw new Error('Plugin dependencies must be an array');
  }

  // Validate each dependency
  for (const dependency of dependencies) {
    // Check if dependency is an object
    if (!dependency || typeof dependency !== 'object') {
      throw new Error('Plugin dependency must be an object');
    }

    // Check required fields
    if (!dependency.id) {
      throw new Error('Plugin dependency is missing required field: id');
    }

    if (!dependency.version) {
      throw new Error('Plugin dependency is missing required field: version');
    }

    // Validate version format (semver or semver range)
    if (!/^(\d+\.\d+\.\d+(-[a-zA-Z0-9.]+)?|\^|\~|\>|\<|\=|\*|x|X|\d+\.x|\d+\.X|\d+\.\*|\d+\.\d+\.x|\d+\.\d+\.X|\d+\.\d+\.\*)/.test(dependency.version)) {
      throw new Error(`Plugin dependency version must follow semantic versioning or version range (dependency: ${dependency.id})`);
    }
  }
}

/**
 * Validate plugin permissions
 */
function validatePermissions(permissions: string[]): void {
  // Check if permissions is an array
  if (!Array.isArray(permissions)) {
    throw new Error('Plugin permissions must be an array');
  }

  // Define allowed permissions
  const allowedPermissions = [
    'ui:tabs',
    'ui:panels',
    'ui:components',
    'ui:themes',
    'api:read',
    'api:write',
    'storage:read',
    'storage:write',
    'network:connect',
    'filesystem:read',
    'filesystem:write',
    'process:execute',
  ];

  // Validate each permission
  for (const permission of permissions) {
    if (!allowedPermissions.includes(permission)) {
      throw new Error(`Invalid plugin permission: ${permission}`);
    }
  }
}

/**
 * Validate plugin settings
 */
function validateSettings(settings: PluginSettingsDefinition): void {
  // Check if settings is an object
  if (!settings || typeof settings !== 'object') {
    throw new Error('Plugin settings must be an object');
  }

  // Validate each setting
  for (const [key, setting] of Object.entries(settings)) {
    // Check if setting is an object
    if (!setting || typeof setting !== 'object') {
      throw new Error(`Plugin setting ${key} must be an object`);
    }

    // Check required fields
    if (!setting.type) {
      throw new Error(`Plugin setting ${key} is missing required field: type`);
    }

    if (!setting.label) {
      throw new Error(`Plugin setting ${key} is missing required field: label`);
    }

    // Validate type
    const allowedTypes = ['string', 'number', 'boolean', 'select', 'multiselect', 'color'];
    if (!allowedTypes.includes(setting.type)) {
      throw new Error(`Invalid type for plugin setting ${key}: ${setting.type}`);
    }

    // Validate options for select and multiselect
    if ((setting.type === 'select' || setting.type === 'multiselect') && (!setting.options || !Array.isArray(setting.options) || setting.options.length === 0)) {
      throw new Error(`Plugin setting ${key} of type ${setting.type} must have non-empty options array`);
    }

    // Validate min/max for number
    if (setting.type === 'number') {
      if (setting.min !== undefined && typeof setting.min !== 'number') {
        throw new Error(`Plugin setting ${key} min value must be a number`);
      }

      if (setting.max !== undefined && typeof setting.max !== 'number') {
        throw new Error(`Plugin setting ${key} max value must be a number`);
      }

      if (setting.min !== undefined && setting.max !== undefined && setting.min > setting.max) {
        throw new Error(`Plugin setting ${key} min value must be less than or equal to max value`);
      }
    }
  }
}

/**
 * Check if a string is a valid URL
 */
function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}
