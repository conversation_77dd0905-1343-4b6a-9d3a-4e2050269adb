/**
 * Hello World Plugin
 * 
 * A simple example plugin that demonstrates the plugin system.
 */

import { PluginManifest, PluginLifecycle } from '../types';
import { Sparkles } from 'lucide-react';

/**
 * Plugin manifest
 */
export const manifest: PluginManifest = {
  metadata: {
    id: 'hello-world',
    name: 'Hello World',
    version: '1.0.0',
    description: 'A simple example plugin that demonstrates the plugin system',
    author: 'AppGen',
    icon: <Sparkles className="h-4 w-4" />,
    tags: ['example', 'demo'],
  },
  permissions: ['ui:tabs'],
  settings: {
    greeting: {
      type: 'string',
      label: 'Greeting',
      description: 'The greeting to display',
      default: 'Hello, World!',
    },
    showTimestamp: {
      type: 'boolean',
      label: 'Show Timestamp',
      description: 'Whether to show the timestamp',
      default: true,
    },
    theme: {
      type: 'select',
      label: 'Theme',
      description: 'The theme to use',
      default: 'light',
      options: [
        { label: 'Light', value: 'light' },
        { label: 'Dark', value: 'dark' },
        { label: 'System', value: 'system' },
      ],
    },
  },
};

/**
 * Hello World component
 */
function HelloWorldComponent({ greeting, showTimestamp, theme }: any) {
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">{greeting}</h1>
      {showTimestamp && (
        <p className="text-muted-foreground">
          Current time: {new Date().toLocaleTimeString()}
        </p>
      )}
      <p className="mt-4">
        This is a simple example plugin that demonstrates the plugin system.
      </p>
      <p className="mt-2">
        Current theme: <strong>{theme}</strong>
      </p>
    </div>
  );
}

/**
 * Plugin lifecycle
 */
export const lifecycle: PluginLifecycle = {
  /**
   * Called when the plugin is loaded
   */
  onLoad: async function() {
    console.log('Hello World plugin loaded');
  },

  /**
   * Called when the plugin is enabled
   */
  onEnable: async function() {
    console.log('Hello World plugin enabled');

    // Get plugin API
    const api = this.api;

    // Get settings
    const settings = api.getSettings();

    // Register a tab
    api.ui.registerTab({
      id: 'hello-world',
      title: 'Hello World',
      icon: <Sparkles className="h-4 w-4" />,
      component: HelloWorldComponent,
      location: 'main',
      props: {
        greeting: settings.greeting,
        showTimestamp: settings.showTimestamp,
        theme: settings.theme,
      },
    });
  },

  /**
   * Called when the plugin is disabled
   */
  onDisable: async function() {
    console.log('Hello World plugin disabled');

    // Get plugin API
    const api = this.api;

    // Unregister the tab
    api.ui.unregisterTab('hello-world');
  },

  /**
   * Called when the plugin is unloaded
   */
  onUnload: async function() {
    console.log('Hello World plugin unloaded');
  },
};
