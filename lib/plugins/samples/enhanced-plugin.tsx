/**
 * Enhanced Plugin
 * 
 * A more complex example plugin that demonstrates the extended plugin system.
 */

import React, { useState } from 'react';
import { PluginManifest, PluginLifecycle } from '../types';
import { 
  Sparkles, 
  Settings, 
  BarChart, 
  Terminal, 
  FileText, 
  Folder, 
  Clock, 
  Cpu,
  Database,
  Server,
  HardDrive,
  Network
} from 'lucide-react';

/**
 * Plugin manifest
 */
export const manifest: PluginManifest = {
  metadata: {
    id: 'enhanced-plugin',
    name: 'Enhanced Plugin',
    version: '1.0.0',
    description: 'A more complex example plugin that demonstrates the extended plugin system',
    author: 'AppGen',
    icon: <Sparkles className="h-4 w-4" />,
    tags: ['example', 'demo', 'advanced'],
  },
  permissions: [
    'ui:tabs',
    'ui:panels',
    'ui:components',
    'ui:themes',
  ],
  settings: {
    showToolbar: {
      type: 'boolean',
      label: 'Show Toolbar',
      description: 'Whether to show the toolbar',
      default: true,
    },
    showSidebar: {
      type: 'boolean',
      label: 'Show Sidebar',
      description: 'Whether to show the sidebar',
      default: true,
    },
    showStatusBar: {
      type: 'boolean',
      label: 'Show Status Bar',
      description: 'Whether to show the status bar',
      default: true,
    },
    theme: {
      type: 'select',
      label: 'Theme',
      description: 'The theme to use',
      default: 'light',
      options: [
        { label: 'Light', value: 'light' },
        { label: 'Dark', value: 'dark' },
        { label: 'System', value: 'system' },
      ],
    },
  },
};

/**
 * Main component
 */
function MainComponent() {
  const [count, setCount] = useState(0);
  
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Enhanced Plugin</h1>
      
      <p className="mb-4">
        This is a more complex example plugin that demonstrates the extended plugin system.
      </p>
      
      <div className="flex items-center gap-2 mb-4">
        <button
          className="px-4 py-2 bg-primary text-primary-foreground rounded"
          onClick={() => setCount(count + 1)}
        >
          Increment
        </button>
        
        <span className="text-lg font-medium">Count: {count}</span>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
        <div className="border rounded p-4">
          <h2 className="text-lg font-medium mb-2">Features</h2>
          <ul className="list-disc list-inside space-y-1">
            <li>Toolbar integration</li>
            <li>Sidebar panel</li>
            <li>Status bar items</li>
            <li>Context menu items</li>
            <li>Command palette commands</li>
          </ul>
        </div>
        
        <div className="border rounded p-4">
          <h2 className="text-lg font-medium mb-2">Usage</h2>
          <p>
            Right-click on various elements to see context menu items.
            Check the status bar for plugin information.
            Use the toolbar buttons for quick actions.
          </p>
        </div>
      </div>
    </div>
  );
}

/**
 * Sidebar component
 */
function SidebarComponent() {
  const [resources, setResources] = useState({
    cpu: Math.random() * 100,
    memory: Math.random() * 100,
    disk: Math.random() * 100,
    network: Math.random() * 100,
  });
  
  // Update resources every 2 seconds
  React.useEffect(() => {
    const interval = setInterval(() => {
      setResources({
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        disk: Math.random() * 100,
        network: Math.random() * 100,
      });
    }, 2000);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className="p-2">
      <h3 className="text-sm font-medium mb-2">System Resources</h3>
      
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Cpu className="h-3 w-3" />
            <span className="text-xs">CPU</span>
          </div>
          <span className="text-xs font-medium">{resources.cpu.toFixed(1)}%</span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Database className="h-3 w-3" />
            <span className="text-xs">Memory</span>
          </div>
          <span className="text-xs font-medium">{resources.memory.toFixed(1)}%</span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <HardDrive className="h-3 w-3" />
            <span className="text-xs">Disk</span>
          </div>
          <span className="text-xs font-medium">{resources.disk.toFixed(1)}%</span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Network className="h-3 w-3" />
            <span className="text-xs">Network</span>
          </div>
          <span className="text-xs font-medium">{resources.network.toFixed(1)}%</span>
        </div>
      </div>
    </div>
  );
}

/**
 * Status bar component
 */
function StatusBarComponent() {
  const [time, setTime] = useState(new Date());
  
  // Update time every second
  React.useEffect(() => {
    const interval = setInterval(() => {
      setTime(new Date());
    }, 1000);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <span>{time.toLocaleTimeString()}</span>
  );
}

/**
 * Plugin lifecycle
 */
export const lifecycle: PluginLifecycle = {
  /**
   * Called when the plugin is loaded
   */
  onLoad: async function() {
    console.log('Enhanced plugin loaded');
  },

  /**
   * Called when the plugin is enabled
   */
  onEnable: async function() {
    console.log('Enhanced plugin enabled');

    // Get plugin API
    const api = this.api;

    // Get settings
    const settings = api.getSettings();

    // Register a tab using the new API
    api.ui.registerTab({
      id: 'enhanced-main',
      title: 'Enhanced Plugin',
      icon: <Sparkles className="h-4 w-4" />,
      component: MainComponent,
      location: 'main',
    });

    // Register toolbar items
    if (settings.showToolbar) {
      api.ui.registerToolbarItem({
        id: 'enhanced-settings',
        title: 'Settings',
        icon: <Settings className="h-4 w-4" />,
        location: 'main',
        onClick: () => {
          console.log('Settings clicked');
        },
        tooltip: 'Open Settings',
      });

      api.ui.registerToolbarItem({
        id: 'enhanced-chart',
        title: 'Chart',
        icon: <BarChart className="h-4 w-4" />,
        location: 'main',
        onClick: () => {
          console.log('Chart clicked');
        },
        tooltip: 'Open Chart',
      });
    }

    // Register sidebar items
    if (settings.showSidebar) {
      api.ui.registerSidebarItem({
        id: 'enhanced-sidebar',
        title: 'Resources',
        icon: <Server className="h-4 w-4" />,
        location: 'right',
        component: SidebarComponent,
      });
    }

    // Register status bar items
    if (settings.showStatusBar) {
      api.ui.registerStatusBarItem({
        id: 'enhanced-status-time',
        icon: <Clock className="h-4 w-4" />,
        location: 'right',
        component: StatusBarComponent,
        tooltip: 'Current time',
      });

      api.ui.registerStatusBarItem({
        id: 'enhanced-status-info',
        text: 'Enhanced Plugin',
        icon: <Sparkles className="h-4 w-4" />,
        location: 'left',
        onClick: () => {
          console.log('Status bar clicked');
        },
        tooltip: 'Enhanced Plugin Information',
      });
    }

    // Register context menu items
    api.ui.registerContextMenuItem({
      id: 'enhanced-context-file',
      title: 'Open with Enhanced Plugin',
      icon: <FileText className="h-4 w-4" />,
      context: 'file',
      onClick: (contextData) => {
        console.log('Open file with Enhanced Plugin', contextData);
      },
    });

    api.ui.registerContextMenuItem({
      id: 'enhanced-context-folder',
      title: 'Analyze Folder',
      icon: <Folder className="h-4 w-4" />,
      context: 'folder',
      onClick: (contextData) => {
        console.log('Analyze folder with Enhanced Plugin', contextData);
      },
    });

    // Register command palette commands
    api.ui.registerCommand({
      id: 'enhanced-command-open',
      title: 'Enhanced: Open Main View',
      description: 'Open the Enhanced Plugin main view',
      icon: <Sparkles className="h-4 w-4" />,
      category: 'Enhanced Plugin',
      execute: () => {
        console.log('Open Enhanced Plugin main view');
      },
      shortcut: 'Ctrl+Shift+E',
    });

    api.ui.registerCommand({
      id: 'enhanced-command-analyze',
      title: 'Enhanced: Analyze Project',
      description: 'Analyze the current project with Enhanced Plugin',
      icon: <Terminal className="h-4 w-4" />,
      category: 'Enhanced Plugin',
      execute: () => {
        console.log('Analyze project with Enhanced Plugin');
      },
    });
  },

  /**
   * Called when the plugin is disabled
   */
  onDisable: async function() {
    console.log('Enhanced plugin disabled');

    // Get plugin API
    const api = this.api;

    // Unregister all extension points
    api.ui.unregisterAll();
  },

  /**
   * Called when the plugin is unloaded
   */
  onUnload: async function() {
    console.log('Enhanced plugin unloaded');
  },
};
