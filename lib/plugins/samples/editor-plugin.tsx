/**
 * Editor Plugin
 * 
 * A plugin that demonstrates editor decorations.
 */

import React from 'react';
import { PluginManifest, PluginLifecycle } from '../types';
import { 
  Highlighter, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle,
  Code,
  FileCode
} from 'lucide-react';

/**
 * Plugin manifest
 */
export const manifest: PluginManifest = {
  metadata: {
    id: 'editor-plugin',
    name: 'Editor Plugin',
    version: '1.0.0',
    description: 'A plugin that demonstrates editor decorations',
    author: 'AppGen',
    icon: <Highlighter className="h-4 w-4" />,
    tags: ['editor', 'decorations', 'demo'],
  },
  permissions: [
    'ui:tabs',
    'ui:components',
  ],
  settings: {
    highlightTodos: {
      type: 'boolean',
      label: 'Highlight TODOs',
      description: 'Whether to highlight TODO comments',
      default: true,
    },
    highlightFixmes: {
      type: 'boolean',
      label: 'Highlight FIXMEs',
      description: 'Whether to highlight FIXME comments',
      default: true,
    },
    highlightWarnings: {
      type: 'boolean',
      label: 'Highlight Warnings',
      description: 'Whether to highlight warning comments',
      default: true,
    },
    highlightErrors: {
      type: 'boolean',
      label: 'Highlight Errors',
      description: 'Whether to highlight error comments',
      default: true,
    },
  },
};

/**
 * Main component
 */
function MainComponent() {
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Editor Plugin</h1>
      
      <p className="mb-4">
        This plugin demonstrates editor decorations. It adds decorations to the code editor
        to highlight TODO, FIXME, WARNING, and ERROR comments.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
        <div className="border rounded p-4">
          <h2 className="text-lg font-medium mb-2">Features</h2>
          <ul className="list-disc list-inside space-y-1">
            <li>Highlight TODO comments</li>
            <li>Highlight FIXME comments</li>
            <li>Highlight WARNING comments</li>
            <li>Highlight ERROR comments</li>
          </ul>
        </div>
        
        <div className="border rounded p-4">
          <h2 className="text-lg font-medium mb-2">Usage</h2>
          <p>
            Open any code file in the editor and add comments with TODO, FIXME, WARNING, or ERROR.
            The plugin will automatically highlight these comments.
          </p>
        </div>
      </div>
      
      <div className="mt-8">
        <h2 className="text-lg font-medium mb-2">Example</h2>
        <pre className="p-4 bg-muted rounded">
          <code>
            {`// TODO: This is a todo comment
// FIXME: This is a fixme comment
// WARNING: This is a warning comment
// ERROR: This is an error comment`}
          </code>
        </pre>
      </div>
    </div>
  );
}

/**
 * Plugin lifecycle
 */
export const lifecycle: PluginLifecycle = {
  /**
   * Called when the plugin is loaded
   */
  onLoad: async function() {
    console.log('Editor plugin loaded');
  },

  /**
   * Called when the plugin is enabled
   */
  onEnable: async function() {
    console.log('Editor plugin enabled');

    // Get plugin API
    const api = this.api;

    // Get settings
    const settings = api.getSettings();

    // Register a tab
    api.ui.registerTab({
      id: 'editor-plugin',
      title: 'Editor Plugin',
      icon: <Highlighter className="h-4 w-4" />,
      component: MainComponent,
      location: 'main',
    });

    // Register editor decorations
    if (settings.highlightTodos) {
      api.ui.registerEditorDecoration({
        id: 'todo-decoration',
        type: 'line',
        pattern: /\/\/\s*TODO:.*$/gm,
        decoration: {
          className: 'bg-blue-100 dark:bg-blue-900/30',
          hoverMessage: 'TODO: This is a task that needs to be completed',
          overviewRulerColor: '#3b82f6',
        },
      });
    }

    if (settings.highlightFixmes) {
      api.ui.registerEditorDecoration({
        id: 'fixme-decoration',
        type: 'line',
        pattern: /\/\/\s*FIXME:.*$/gm,
        decoration: {
          className: 'bg-orange-100 dark:bg-orange-900/30',
          hoverMessage: 'FIXME: This is a bug that needs to be fixed',
          overviewRulerColor: '#f97316',
        },
      });
    }

    if (settings.highlightWarnings) {
      api.ui.registerEditorDecoration({
        id: 'warning-decoration',
        type: 'line',
        pattern: /\/\/\s*WARNING:.*$/gm,
        decoration: {
          className: 'bg-yellow-100 dark:bg-yellow-900/30',
          hoverMessage: 'WARNING: This is a warning that should be addressed',
          overviewRulerColor: '#eab308',
        },
      });
    }

    if (settings.highlightErrors) {
      api.ui.registerEditorDecoration({
        id: 'error-decoration',
        type: 'line',
        pattern: /\/\/\s*ERROR:.*$/gm,
        decoration: {
          className: 'bg-red-100 dark:bg-red-900/30',
          hoverMessage: 'ERROR: This is an error that must be fixed',
          overviewRulerColor: '#ef4444',
        },
      });
    }

    // Register toolbar items
    api.ui.registerToolbarItem({
      id: 'editor-plugin-info',
      title: 'Editor Plugin Info',
      icon: <Info className="h-4 w-4" />,
      location: 'editor',
      onClick: () => {
        console.log('Editor Plugin Info clicked');
        alert('Editor Plugin is active. Open a code file to see decorations.');
      },
      tooltip: 'Editor Plugin Info',
    });

    // Register command palette commands
    api.ui.registerCommand({
      id: 'toggle-todos',
      title: 'Editor: Toggle TODO Highlights',
      description: 'Toggle highlighting of TODO comments',
      icon: <CheckCircle className="h-4 w-4" />,
      category: 'Editor Plugin',
      execute: () => {
        const newSettings = { ...settings, highlightTodos: !settings.highlightTodos };
        api.updateSettings(newSettings);
        
        if (newSettings.highlightTodos) {
          api.ui.registerEditorDecoration({
            id: 'todo-decoration',
            type: 'line',
            pattern: /\/\/\s*TODO:.*$/gm,
            decoration: {
              className: 'bg-blue-100 dark:bg-blue-900/30',
              hoverMessage: 'TODO: This is a task that needs to be completed',
              overviewRulerColor: '#3b82f6',
            },
          });
        } else {
          api.ui.unregisterEditorDecoration('todo-decoration');
        }
      },
    });

    api.ui.registerCommand({
      id: 'toggle-fixmes',
      title: 'Editor: Toggle FIXME Highlights',
      description: 'Toggle highlighting of FIXME comments',
      icon: <XCircle className="h-4 w-4" />,
      category: 'Editor Plugin',
      execute: () => {
        const newSettings = { ...settings, highlightFixmes: !settings.highlightFixmes };
        api.updateSettings(newSettings);
        
        if (newSettings.highlightFixmes) {
          api.ui.registerEditorDecoration({
            id: 'fixme-decoration',
            type: 'line',
            pattern: /\/\/\s*FIXME:.*$/gm,
            decoration: {
              className: 'bg-orange-100 dark:bg-orange-900/30',
              hoverMessage: 'FIXME: This is a bug that needs to be fixed',
              overviewRulerColor: '#f97316',
            },
          });
        } else {
          api.ui.unregisterEditorDecoration('fixme-decoration');
        }
      },
    });

    api.ui.registerCommand({
      id: 'toggle-warnings',
      title: 'Editor: Toggle Warning Highlights',
      description: 'Toggle highlighting of WARNING comments',
      icon: <AlertTriangle className="h-4 w-4" />,
      category: 'Editor Plugin',
      execute: () => {
        const newSettings = { ...settings, highlightWarnings: !settings.highlightWarnings };
        api.updateSettings(newSettings);
        
        if (newSettings.highlightWarnings) {
          api.ui.registerEditorDecoration({
            id: 'warning-decoration',
            type: 'line',
            pattern: /\/\/\s*WARNING:.*$/gm,
            decoration: {
              className: 'bg-yellow-100 dark:bg-yellow-900/30',
              hoverMessage: 'WARNING: This is a warning that should be addressed',
              overviewRulerColor: '#eab308',
            },
          });
        } else {
          api.ui.unregisterEditorDecoration('warning-decoration');
        }
      },
    });
  },

  /**
   * Called when the plugin is disabled
   */
  onDisable: async function() {
    console.log('Editor plugin disabled');

    // Get plugin API
    const api = this.api;

    // Unregister all extension points
    api.ui.unregisterAll();
  },

  /**
   * Called when the plugin is unloaded
   */
  onUnload: async function() {
    console.log('Editor plugin unloaded');
  },
};
