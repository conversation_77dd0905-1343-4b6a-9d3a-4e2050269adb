/**
 * Plugin Marketplace Module
 * 
 * Main entry point for the plugin marketplace module.
 */

import { MarketplaceAPI } from './marketplace-api';
import { MarketplaceService, InstallationStatus } from './marketplace-service';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('marketplace');

// Export types
export * from './marketplace-api';
export { MarketplaceService, InstallationStatus } from './marketplace-service';

// Create singleton instances
const api = new MarketplaceAPI();
const service = new MarketplaceService(api);

/**
 * Initialize the marketplace module
 */
export async function initializeMarketplace(): Promise<void> {
  try {
    logger.info('Initializing marketplace module');
    
    // Initialize service
    await service.initialize();
    
    logger.info('Marketplace module initialized');
  } catch (error) {
    logger.error('Failed to initialize marketplace module:', error);
    throw error;
  }
}

/**
 * Get the marketplace service
 */
export function getMarketplaceService(): MarketplaceService {
  return service;
}
