/**
 * Plugin Marketplace Service
 * 
 * Manages the installation and management of plugins from the marketplace.
 */

import { MarketplaceAPI, PluginPackage } from './marketplace-api';
import { getPluginLoader, getPluginRegistry } from '../index';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('marketplace-service');

/**
 * Installation status
 */
export enum InstallationStatus {
  IDLE = 'idle',
  DOWNLOADING = 'downloading',
  INSTALLING = 'installing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

/**
 * Installation result
 */
export interface InstallationResult {
  status: InstallationStatus;
  pluginId?: string;
  error?: string;
}

/**
 * Marketplace service class
 */
export class MarketplaceService {
  private api: MarketplaceAPI;
  private installedPlugins: Set<string> = new Set();

  /**
   * Create a new marketplace service
   */
  constructor(api: MarketplaceAPI) {
    this.api = api;
  }

  /**
   * Initialize the marketplace service
   */
  async initialize(): Promise<void> {
    try {
      // Get installed plugins
      const registry = getPluginRegistry();
      const plugins = registry.getAllPlugins();
      
      // Add installed plugin IDs to set
      for (const plugin of plugins) {
        this.installedPlugins.add(plugin.manifest.metadata.id);
      }
      
      logger.info('Marketplace service initialized');
    } catch (error) {
      logger.error('Failed to initialize marketplace service:', error);
      throw error;
    }
  }

  /**
   * Check if a plugin is installed
   */
  isPluginInstalled(pluginId: string): boolean {
    return this.installedPlugins.has(pluginId);
  }

  /**
   * Install a plugin
   */
  async installPlugin(plugin: PluginPackage): Promise<InstallationResult> {
    try {
      // Check if plugin is already installed
      if (this.isPluginInstalled(plugin.id)) {
        return {
          status: InstallationStatus.COMPLETED,
          pluginId: plugin.id,
        };
      }
      
      // Download plugin
      logger.info(`Downloading plugin: ${plugin.id}`);
      const { manifest, code } = await this.api.downloadPlugin(plugin.packageUrl);
      
      // Install plugin
      logger.info(`Installing plugin: ${plugin.id}`);
      const loader = getPluginLoader();
      await loader.loadPluginFromCode(manifest, code);
      
      // Add to installed plugins
      this.installedPlugins.add(plugin.id);
      
      logger.info(`Plugin installed: ${plugin.id}`);
      return {
        status: InstallationStatus.COMPLETED,
        pluginId: plugin.id,
      };
    } catch (error) {
      logger.error(`Failed to install plugin ${plugin.id}:`, error);
      return {
        status: InstallationStatus.FAILED,
        pluginId: plugin.id,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Uninstall a plugin
   */
  async uninstallPlugin(pluginId: string): Promise<boolean> {
    try {
      // Check if plugin is installed
      if (!this.isPluginInstalled(pluginId)) {
        return false;
      }
      
      // Unload plugin
      logger.info(`Uninstalling plugin: ${pluginId}`);
      const registry = getPluginRegistry();
      await registry.unloadPlugin(pluginId);
      
      // Remove from installed plugins
      this.installedPlugins.delete(pluginId);
      
      logger.info(`Plugin uninstalled: ${pluginId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to uninstall plugin ${pluginId}:`, error);
      throw error;
    }
  }

  /**
   * Get categories
   */
  async getCategories() {
    return this.api.getCategories();
  }

  /**
   * Search plugins
   */
  async searchPlugins(options = {}) {
    return this.api.searchPlugins(options);
  }

  /**
   * Get a plugin by ID
   */
  async getPlugin(id: string) {
    return this.api.getPlugin(id);
  }

  /**
   * Get featured plugins
   */
  async getFeaturedPlugins() {
    return this.api.getFeaturedPlugins();
  }

  /**
   * Get popular plugins
   */
  async getPopularPlugins() {
    return this.api.getPopularPlugins();
  }

  /**
   * Get recent plugins
   */
  async getRecentPlugins() {
    return this.api.getRecentPlugins();
  }
}
