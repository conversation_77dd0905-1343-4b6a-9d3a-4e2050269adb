/**
 * Plugin Sandbox
 * 
 * Provides a secure sandbox for running plugin code.
 */

import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('plugin-sandbox');

/**
 * Sandbox options
 */
export interface SandboxOptions {
  timeout?: number;
  memoryLimit?: number;
  allowedGlobals?: string[];
}

/**
 * Default sandbox options
 */
const DEFAULT_OPTIONS: SandboxOptions = {
  timeout: 5000,
  memoryLimit: 50 * 1024 * 1024, // 50MB
  allowedGlobals: [
    'Array',
    'Boolean',
    'Date',
    'Error',
    'JSON',
    'Map',
    'Math',
    'Number',
    'Object',
    'Promise',
    'RegExp',
    'Set',
    'String',
    'Symbol',
    'console',
    'parseInt',
    'parseFloat',
    'setTimeout',
    'clearTimeout',
    'setInterval',
    'clearInterval',
  ],
};

/**
 * Create a sandboxed environment for running plugin code
 */
export function createSandbox(options: SandboxOptions = {}) {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  /**
   * Execute code in the sandbox
   */
  const execute = <T = any>(
    code: string,
    context: Record<string, any> = {}
  ): Promise<T> => {
    return new Promise((resolve, reject) => {
      // Create a timeout to prevent infinite loops
      const timeoutId = setTimeout(() => {
        reject(new Error(`Execution timed out after ${opts.timeout}ms`));
      }, opts.timeout);
      
      try {
        // Create a secure context with only allowed globals
        const secureContext: Record<string, any> = {};
        
        // Add allowed globals
        for (const global of opts.allowedGlobals || []) {
          if (global in window) {
            secureContext[global] = (window as any)[global];
          }
        }
        
        // Add custom context
        Object.assign(secureContext, context);
        
        // Add resolve and reject functions
        secureContext.resolve = resolve;
        secureContext.reject = reject;
        
        // Create parameter names and values for the Function constructor
        const paramNames = Object.keys(secureContext);
        const paramValues = Object.values(secureContext);
        
        // Create a function that executes the code in the secure context
        const fn = new Function(
          ...paramNames,
          `
          try {
            const result = (function() {
              "use strict";
              ${code}
            })();
            resolve(result);
          } catch (error) {
            reject(error);
          } finally {
            clearTimeout(timeoutId);
          }
          `
        );
        
        // Execute the function with the secure context
        fn(...paramValues);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  };
  
  return {
    execute,
  };
}
