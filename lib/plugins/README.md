# Platform Extension System

A comprehensive plugin architecture for extending the platform with custom functionality.

## Features

- **Plugin System**: A modular plugin system that allows third-party extensions with lifecycle management
- **Multiple UI Extension Points**: Seamless integration with the platform's UI through tabs, toolbars, sidebars, status bars, and context menus
- **MicroVM Architecture**: Isolated execution environments for running untrusted plugin code
- **Custom View Components**: Reusable UI components for plugin developers

## Architecture

The plugin system consists of the following components:

- **Plugin Registry**: Manages plugin registration, loading, and lifecycle
- **Plugin Loader**: Handles loading plugins from different sources
- **Plugin API**: Provides an interface for plugins to interact with the platform
- **MicroVM Service**: Manages isolated execution environments for plugins
- **UI Components**: Provides UI components for rendering plugin content

## Usage

### Loading a Plugin

```typescript
import { getPluginLoader } from '@/lib/plugins';

// Load a plugin from code
const loader = getPluginLoader();
await loader.loadPluginFromCode(
  manifest,
  lifecycleCode
);
```

### Creating a Plugin

```typescript
import { PluginManifest, PluginLifecycle } from '@/lib/plugins';

// Define plugin manifest
const manifest: PluginManifest = {
  metadata: {
    id: 'my-plugin',
    name: 'My Plugin',
    version: '1.0.0',
    description: 'A custom plugin',
    author: 'Your Name',
  },
  permissions: ['ui:tabs', 'ui:components', 'ui:panels'],
};

// Define plugin lifecycle
const lifecycle: PluginLifecycle = {
  onLoad: async function() {
    console.log('Plugin loaded');
  },
  onEnable: async function() {
    // Get plugin API
    const api = this.api;

    // Register a tab
    api.ui.registerTab({
      id: 'my-tab',
      title: 'My Tab',
      component: MyTabComponent,
      location: 'main',
    });

    // Register a toolbar item
    api.ui.registerToolbarItem({
      id: 'my-toolbar-item',
      title: 'My Toolbar Item',
      icon: <Icon className="h-4 w-4" />,
      location: 'main',
      onClick: () => {
        console.log('Toolbar item clicked');
      },
      tooltip: 'Click me!',
    });

    // Register a sidebar item
    api.ui.registerSidebarItem({
      id: 'my-sidebar-item',
      title: 'My Sidebar',
      icon: <Icon className="h-4 w-4" />,
      location: 'right',
      component: MySidebarComponent,
    });

    // Register a status bar item
    api.ui.registerStatusBarItem({
      id: 'my-status-item',
      text: 'Ready',
      icon: <Icon className="h-4 w-4" />,
      location: 'right',
      onClick: () => {
        console.log('Status bar item clicked');
      },
      tooltip: 'Plugin Status',
    });

    // Register a context menu item
    api.ui.registerContextMenuItem({
      id: 'my-context-menu-item',
      title: 'My Context Menu Item',
      icon: <Icon className="h-4 w-4" />,
      context: 'file',
      onClick: (contextData) => {
        console.log('Context menu item clicked', contextData);
      },
    });
  },
  onDisable: async function() {
    // Unregister all extension points
    this.api.ui.unregisterAll();
  },
};
```

### Using Plugin Components

```tsx
import { PluginTabRenderer } from '@/components/plugins/plugin-tab-renderer';

// Render a plugin tab
<PluginTabRenderer location="main" tabId="my-plugin:my-tab" />
```

## Plugin Lifecycle

Plugins have the following lifecycle hooks:

- **onLoad**: Called when the plugin is loaded
- **onEnable**: Called when the plugin is enabled
- **onDisable**: Called when the plugin is disabled
- **onUnload**: Called when the plugin is unloaded
- **onUpdate**: Called when the plugin is updated

## Plugin API

Plugins have access to the following API:

### Core API

- **getMetadata**: Get plugin metadata
- **getSettings**: Get plugin settings
- **updateSettings**: Update plugin settings
- **on**: Register an event listener
- **off**: Unregister an event listener
- **emit**: Emit an event

### UI Extension Points API

- **ui.registerTab**: Register a tab
- **ui.unregisterTab**: Unregister a tab
- **ui.registerToolbarItem**: Register a toolbar item
- **ui.unregisterToolbarItem**: Unregister a toolbar item
- **ui.registerMenuItem**: Register a menu item
- **ui.unregisterMenuItem**: Unregister a menu item
- **ui.registerContextMenuItem**: Register a context menu item
- **ui.unregisterContextMenuItem**: Unregister a context menu item
- **ui.registerSidebarItem**: Register a sidebar item
- **ui.unregisterSidebarItem**: Unregister a sidebar item
- **ui.registerStatusBarItem**: Register a status bar item
- **ui.unregisterStatusBarItem**: Unregister a status bar item
- **ui.registerCommand**: Register a command palette command
- **ui.unregisterCommand**: Unregister a command palette command
- **ui.registerEditorDecoration**: Register an editor decoration
- **ui.unregisterEditorDecoration**: Unregister an editor decoration
- **ui.registerTheme**: Register a theme
- **ui.unregisterTheme**: Unregister a theme
- **ui.unregisterAll**: Unregister all extension points

## Security Considerations

- Plugins run in isolated environments to prevent malicious code execution
- Permissions system restricts what plugins can do
- Validation ensures plugin manifests and code are safe

## Future Improvements

- Plugin marketplace for discovering and installing plugins
- Command palette integration
- Editor decoration support
- Enhanced security features
- Plugin versioning and dependency management
- Plugin settings UI
- Plugin debugging tools
