/**
 * Editor Decoration Service
 * 
 * Manages decorations for the Monaco editor.
 */

import { editor as monacoEditor, IRange, <PERSON><PERSON> } from 'monaco-editor';
import { EditorDecorationExtensionPoint } from '../extension-points';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('editor-decoration-service');

/**
 * Decoration type
 */
export type DecorationType = 'line' | 'gutter' | 'inline' | 'widget';

/**
 * Decoration options
 */
export interface DecorationOptions {
  range: IRange;
  hoverMessage?: string;
  className?: string;
  glyphMarginClassName?: string;
  overviewRulerColor?: string;
  overviewRulerLane?: monacoEditor.OverviewRulerLane;
  renderOptions?: any;
}

/**
 * Editor decoration service
 */
export class EditorDecorationService {
  private decorations: Map<string, string[]> = new Map();
  private decorationTypes: Map<string, string> = new Map();
  private extensionPoints: EditorDecorationExtensionPoint[] = [];
  private editor: monacoEditor.IStandaloneCodeEditor | null = null;
  private monaco: typeof monacoEditor | null = null;

  /**
   * Set the editor instance
   */
  setEditor(editor: monacoEditor.IStandaloneCodeEditor, monaco: typeof monacoEditor): void {
    this.editor = editor;
    this.monaco = monaco;
    
    // Apply decorations if there are any
    this.applyDecorations();
    
    // Listen for model changes
    editor.onDidChangeModel(() => {
      this.applyDecorations();
    });
    
    // Listen for content changes
    editor.onDidChangeModelContent(() => {
      this.applyDecorations();
    });
  }

  /**
   * Set extension points
   */
  setExtensionPoints(extensionPoints: EditorDecorationExtensionPoint[]): void {
    this.extensionPoints = extensionPoints;
    this.applyDecorations();
  }

  /**
   * Apply decorations to the editor
   */
  private applyDecorations(): void {
    if (!this.editor || !this.monaco) {
      return;
    }
    
    const model = this.editor.getModel();
    if (!model) {
      return;
    }
    
    // Get the current file path
    const uri = model.uri.toString();
    const filePath = uri.replace('file://', '');
    
    // Clear existing decorations
    this.clearDecorations();
    
    // Apply decorations for each extension point
    for (const extensionPoint of this.extensionPoints) {
      try {
        // Check if the decoration applies to this file
        if (this.shouldApplyToFile(extensionPoint, filePath)) {
          this.applyDecoration(extensionPoint, model);
        }
      } catch (error) {
        logger.error(`Failed to apply decoration for extension point ${extensionPoint.id}:`, error);
      }
    }
  }

  /**
   * Check if a decoration should be applied to a file
   */
  private shouldApplyToFile(extensionPoint: EditorDecorationExtensionPoint, filePath: string): boolean {
    const { files } = extensionPoint;
    
    if (!files) {
      // If no files specified, apply to all files
      return true;
    }
    
    if (typeof files === 'function') {
      // If files is a function, call it with the file path
      return files(filePath);
    }
    
    // If files is an array, check if the file path matches any of the patterns
    return files.some(pattern => {
      if (typeof pattern === 'string') {
        // Simple string match
        return filePath.includes(pattern);
      }
      
      // Regex match
      if (pattern instanceof RegExp) {
        return pattern.test(filePath);
      }
      
      return false;
    });
  }

  /**
   * Apply a decoration to the editor
   */
  private applyDecoration(extensionPoint: EditorDecorationExtensionPoint, model: monacoEditor.ITextModel): void {
    if (!this.editor || !this.monaco) {
      return;
    }
    
    const { id, pluginId, type, pattern, decoration } = extensionPoint;
    const decorationId = `${pluginId}:${id}`;
    
    // Create decoration type if it doesn't exist
    if (!this.decorationTypes.has(decorationId)) {
      const decorationType = this.createDecorationType(type, decoration);
      this.decorationTypes.set(decorationId, decorationType);
    }
    
    // Find matches in the model
    const text = model.getValue();
    const matches = this.findMatches(text, pattern);
    
    // Create decoration options for each match
    const decorationOptions = matches.map(match => {
      return this.createDecorationOptions(match, type, decoration);
    });
    
    // Apply decorations
    const decorationType = this.decorationTypes.get(decorationId);
    if (decorationType && decorationOptions.length > 0) {
      const decorationIds = this.editor.deltaDecorations([], decorationOptions);
      this.decorations.set(decorationId, decorationIds);
    }
  }

  /**
   * Find matches in text
   */
  private findMatches(text: string, pattern: string | RegExp): { start: number; end: number; match: string }[] {
    const matches: { start: number; end: number; match: string }[] = [];
    
    if (typeof pattern === 'string') {
      // Convert string pattern to regex
      pattern = new RegExp(pattern.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'g');
    }
    
    // Find all matches
    let match;
    while ((match = pattern.exec(text)) !== null) {
      matches.push({
        start: match.index,
        end: match.index + match[0].length,
        match: match[0],
      });
    }
    
    return matches;
  }

  /**
   * Create decoration options
   */
  private createDecorationOptions(
    match: { start: number; end: number; match: string },
    type: DecorationType,
    decoration: EditorDecorationExtensionPoint['decoration']
  ): DecorationOptions {
    if (!this.editor || !this.monaco) {
      throw new Error('Editor not initialized');
    }
    
    const model = this.editor.getModel();
    if (!model) {
      throw new Error('Model not initialized');
    }
    
    // Convert start and end positions to line and column
    const startPos = model.getPositionAt(match.start);
    const endPos = model.getPositionAt(match.end);
    
    // Create range
    const range: IRange = {
      startLineNumber: startPos.lineNumber,
      startColumn: startPos.column,
      endLineNumber: endPos.lineNumber,
      endColumn: endPos.column,
    };
    
    // Create decoration options based on type
    switch (type) {
      case 'line':
        return {
          range,
          className: decoration.className,
          hoverMessage: decoration.hoverMessage,
        };
      
      case 'gutter':
        return {
          range,
          glyphMarginClassName: decoration.className,
          hoverMessage: decoration.hoverMessage,
        };
      
      case 'inline':
        return {
          range,
          className: decoration.className,
          hoverMessage: decoration.hoverMessage,
          renderOptions: {
            after: {
              contentText: decoration.renderWidget ? '' : undefined,
            },
          },
        };
      
      case 'widget':
        return {
          range,
          renderOptions: {
            after: {
              contentText: ' ', // Placeholder for widget
            },
          },
        };
      
      default:
        return { range };
    }
  }

  /**
   * Create a decoration type
   */
  private createDecorationType(
    type: DecorationType,
    decoration: EditorDecorationExtensionPoint['decoration']
  ): string {
    if (!this.monaco) {
      throw new Error('Monaco not initialized');
    }
    
    // Create decoration type options based on type
    const options: monacoEditor.IModelDecorationOptions = {};
    
    switch (type) {
      case 'line':
        options.isWholeLine = true;
        options.className = decoration.lineClassName || decoration.className;
        options.hoverMessage = decoration.hoverMessage ? { value: decoration.hoverMessage } : undefined;
        break;
      
      case 'gutter':
        options.glyphMarginClassName = decoration.className;
        options.hoverMessage = decoration.hoverMessage ? { value: decoration.hoverMessage } : undefined;
        break;
      
      case 'inline':
        options.className = decoration.className;
        options.hoverMessage = decoration.hoverMessage ? { value: decoration.hoverMessage } : undefined;
        break;
      
      case 'widget':
        options.after = {
          contentText: ' ', // Placeholder for widget
        };
        break;
    }
    
    // Add overview ruler options if specified
    if (decoration.overviewRulerColor) {
      options.overviewRuler = {
        color: decoration.overviewRulerColor,
        position: decoration.overviewRulerLane || this.monaco.OverviewRulerLane.Center,
      };
    }
    
    return this.monaco.createDecorationsCollection([]).id;
  }

  /**
   * Clear all decorations
   */
  clearDecorations(): void {
    if (!this.editor) {
      return;
    }
    
    // Clear all decorations
    for (const [decorationId, decorationIds] of this.decorations.entries()) {
      this.editor.deltaDecorations(decorationIds, []);
      this.decorations.delete(decorationId);
    }
  }

  /**
   * Dispose the service
   */
  dispose(): void {
    this.clearDecorations();
    this.decorationTypes.clear();
    this.extensionPoints = [];
    this.editor = null;
    this.monaco = null;
  }
}
