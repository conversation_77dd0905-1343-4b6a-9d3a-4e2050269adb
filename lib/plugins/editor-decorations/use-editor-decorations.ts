/**
 * Use Editor Decorations Hook
 * 
 * A React hook for using the editor decoration service.
 */

import { useEffect, useState } from 'react';
import { editor as monacoEditor } from 'monaco-editor';
import { EditorDecorationService } from './decoration-service';
import { EditorDecorationExtensionPoint } from '../extension-points';
import { getExtensionPointRegistry, ExtensionPointRegistryEvent } from '../index';

// Create a singleton instance of the decoration service
const decorationService = new EditorDecorationService();

/**
 * Use editor decorations hook
 */
export function useEditorDecorations() {
  const [decorations, setDecorations] = useState<EditorDecorationExtensionPoint[]>([]);

  useEffect(() => {
    // Get extension point registry
    const registry = getExtensionPointRegistry();
    
    // Get editor decorations
    const updateDecorations = () => {
      const items = registry.getExtensionPoints<EditorDecorationExtensionPoint>('editorDecoration');
      setDecorations(items);
      decorationService.setExtensionPoints(items);
    };
    
    updateDecorations();
    
    // Listen for changes
    const handleExtensionPointRegistered = (data: any) => {
      if (data.type === 'editorDecoration') {
        updateDecorations();
      }
    };
    
    const handleExtensionPointUnregistered = () => {
      updateDecorations();
    };
    
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered);
    registry.on(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered);
    
    return () => {
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, handleExtensionPointRegistered);
      registry.off(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, handleExtensionPointUnregistered);
    };
  }, []);

  /**
   * Set the editor instance
   */
  const setEditor = (editor: monacoEditor.IStandaloneCodeEditor, monaco: typeof monacoEditor) => {
    decorationService.setEditor(editor, monaco);
  };

  /**
   * Clear all decorations
   */
  const clearDecorations = () => {
    decorationService.clearDecorations();
  };

  /**
   * Dispose the decoration service
   */
  const dispose = () => {
    decorationService.dispose();
  };

  return {
    decorations,
    setEditor,
    clearDecorations,
    dispose,
  };
}
