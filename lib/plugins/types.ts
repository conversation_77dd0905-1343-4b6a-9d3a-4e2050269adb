/**
 * Plugin System Types
 *
 * Core type definitions for the plugin system.
 */

import { ReactNode } from 'react';

/**
 * Plugin metadata
 */
export interface PluginMetadata {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  homepage?: string;
  repository?: string;
  license?: string;
  icon?: ReactNode;
  tags?: string[];
}

/**
 * Plugin dependency
 */
export interface PluginDependency {
  id: string;
  version: string;
  optional?: boolean;
}

/**
 * Plugin lifecycle hooks
 */
export interface PluginLifecycle {
  onLoad?: () => Promise<void> | void;
  onUnload?: () => Promise<void> | void;
  onEnable?: () => Promise<void> | void;
  onDisable?: () => Promise<void> | void;
  onUpdate?: (prevVersion: string) => Promise<void> | void;
}

/**
 * Plugin manifest
 */
export interface PluginManifest {
  metadata: PluginMetadata;
  dependencies?: PluginDependency[];
  permissions?: string[];
  settings?: PluginSettingsDefinition;
}

/**
 * Plugin settings definition
 */
export interface PluginSettingsDefinition {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect' | 'color';
    label: string;
    description?: string;
    default?: any;
    options?: { label: string; value: any }[];
    min?: number;
    max?: number;
    required?: boolean;
  };
}

/**
 * Plugin settings values
 */
export interface PluginSettings {
  [key: string]: any;
}

/**
 * Plugin instance
 */
export interface Plugin {
  manifest: PluginManifest;
  lifecycle: PluginLifecycle;
  enabled: boolean;
  loaded: boolean;
  api: PluginAPI;
  settings: PluginSettings;
}

/**
 * Plugin API
 */
export interface PluginAPI {
  // Core functionality
  getMetadata: () => PluginMetadata;
  getSettings: () => PluginSettings;
  updateSettings: (settings: Partial<PluginSettings>) => Promise<void>;

  // Legacy UI extensions (deprecated)
  registerTab: (tab: PluginTab) => void;
  unregisterTab: (tabId: string) => void;

  // Extension points
  ui: {
    // Tab extension points
    registerTab: (tab: any) => void;
    unregisterTab: (id: string) => void;

    // Toolbar extension points
    registerToolbarItem: (toolbar: any) => void;
    unregisterToolbarItem: (id: string) => void;

    // Menu extension points
    registerMenuItem: (menu: any) => void;
    unregisterMenuItem: (id: string) => void;

    // Context menu extension points
    registerContextMenuItem: (contextMenu: any) => void;
    unregisterContextMenuItem: (id: string) => void;

    // Sidebar extension points
    registerSidebarItem: (sidebar: any) => void;
    unregisterSidebarItem: (id: string) => void;

    // Status bar extension points
    registerStatusBarItem: (statusBar: any) => void;
    unregisterStatusBarItem: (id: string) => void;

    // Command palette extension points
    registerCommand: (command: any) => void;
    unregisterCommand: (id: string) => void;

    // Editor decoration extension points
    registerEditorDecoration: (decoration: any) => void;
    unregisterEditorDecoration: (id: string) => void;

    // Theme extension points
    registerTheme: (theme: any) => void;
    unregisterTheme: (id: string) => void;

    // Unregister all extension points
    unregisterAll: () => void;
  };

  // Custom fields API
  fields: {
    // Field registration
    registerField: (field: any) => void;
    unregisterField: (fieldId: string) => void;

    // Field retrieval
    getField: (fieldId: string) => any;
    getAllFields: () => any[];
    getFieldsByCategory: (category: string) => any[];

    // Category management
    registerCategory: (category: any) => void;
    unregisterCategory: (categoryId: string) => void;
    getCategories: () => any[];

    // Field rendering and validation
    renderField: (config: any, value: any, onChange: (value: any) => void, options?: any) => ReactNode;
    validateField: (config: any, value: any) => any;
  };

  // Event system
  on: <T = any>(event: string, callback: (data: T) => void) => void;
  off: <T = any>(event: string, callback: (data: T) => void) => void;
  emit: <T = any>(event: string, data: T) => void;
}

/**
 * Plugin tab definition
 */
export interface PluginTab {
  id: string;
  title: string;
  icon?: ReactNode;
  component: React.ComponentType<any>;
  location: 'main' | 'left' | 'right' | 'bottom';
  order?: number;
  props?: Record<string, any>;
}

/**
 * Plugin state
 */
export enum PluginState {
  REGISTERED = 'registered',
  LOADING = 'loading',
  LOADED = 'loaded',
  ENABLED = 'enabled',
  DISABLED = 'disabled',
  ERROR = 'error',
  UNLOADED = 'unloaded',
}

/**
 * Plugin error
 */
export interface PluginError {
  code: string;
  message: string;
  details?: any;
}

/**
 * Plugin source
 */
export interface PluginSource {
  type: 'local' | 'remote' | 'builtin';
  location: string;
  manifest: PluginManifest;
  code: string;
}

/**
 * Plugin registry entry
 */
export interface PluginRegistryEntry {
  plugin: Plugin;
  state: PluginState;
  error?: PluginError;
  dependencies: {
    required: PluginDependency[];
    optional: PluginDependency[];
  };
}
