/**
 * Plugin API
 *
 * Provides the API interface for plugins to interact with the platform.
 */

import { EventEmitter } from 'events';
import { PluginAPI, PluginMetadata, PluginSettings, PluginTab } from './types';
import { PluginRegistry } from './plugin-registry';
import { createNamespacedLogger } from '@/lib/logger';
import { createExtensionPointAPI } from './extension-points';
import { createFieldPluginAPI } from './custom-fields/api';

// Create a logger for this module
const logger = createNamespacedLogger('plugin-api');

/**
 * Create a plugin API instance
 */
export function createPluginAPI(
  registry: PluginRegistry,
  pluginId: string,
  initialSettings: PluginSettings
): PluginAPI {
  // Create event emitter for this plugin
  const eventEmitter = new EventEmitter();

  // Create extension point API
  const extensionPointAPI = createExtensionPointAPI(pluginId);

  // Create custom fields API
  const fieldsAPI = createFieldPluginAPI(pluginId);

  // Create API instance
  const api: PluginAPI = {
    // Core functionality
    getMetadata: () => {
      const plugin = registry.getPlugin(pluginId);
      if (!plugin) {
        throw new Error(`Plugin ${pluginId} not found`);
      }
      return plugin.manifest.metadata;
    },

    getSettings: () => {
      const plugin = registry.getPlugin(pluginId);
      if (!plugin) {
        throw new Error(`Plugin ${pluginId} not found`);
      }
      return { ...plugin.settings };
    },

    updateSettings: async (settings: Partial<PluginSettings>) => {
      try {
        await registry.updatePluginSettings(pluginId, settings);
      } catch (error) {
        logger.error(`Failed to update settings for plugin ${pluginId}:`, error);
        throw error;
      }
    },

    // Legacy UI extensions (deprecated)
    registerTab: (tab: PluginTab) => {
      try {
        registry.registerTab(pluginId, tab);
      } catch (error) {
        logger.error(`Failed to register tab for plugin ${pluginId}:`, error);
        throw error;
      }
    },

    unregisterTab: (tabId: string) => {
      try {
        registry.unregisterTab(pluginId, tabId);
      } catch (error) {
        logger.error(`Failed to unregister tab for plugin ${pluginId}:`, error);
        throw error;
      }
    },

    // Extension points
    ui: {
      // Tab extension points
      registerTab: (tab: any) => extensionPointAPI.registerTab(tab),
      unregisterTab: (id: string) => extensionPointAPI.unregisterTab(id),

      // Toolbar extension points
      registerToolbarItem: (toolbar: any) => extensionPointAPI.registerToolbarItem(toolbar),
      unregisterToolbarItem: (id: string) => extensionPointAPI.unregisterToolbarItem(id),

      // Menu extension points
      registerMenuItem: (menu: any) => extensionPointAPI.registerMenuItem(menu),
      unregisterMenuItem: (id: string) => extensionPointAPI.unregisterMenuItem(id),

      // Context menu extension points
      registerContextMenuItem: (contextMenu: any) => extensionPointAPI.registerContextMenuItem(contextMenu),
      unregisterContextMenuItem: (id: string) => extensionPointAPI.unregisterContextMenuItem(id),

      // Sidebar extension points
      registerSidebarItem: (sidebar: any) => extensionPointAPI.registerSidebarItem(sidebar),
      unregisterSidebarItem: (id: string) => extensionPointAPI.unregisterSidebarItem(id),

      // Status bar extension points
      registerStatusBarItem: (statusBar: any) => extensionPointAPI.registerStatusBarItem(statusBar),
      unregisterStatusBarItem: (id: string) => extensionPointAPI.unregisterStatusBarItem(id),

      // Command palette extension points
      registerCommand: (command: any) => extensionPointAPI.registerCommand(command),
      unregisterCommand: (id: string) => extensionPointAPI.unregisterCommand(id),

      // Editor decoration extension points
      registerEditorDecoration: (decoration: any) => extensionPointAPI.registerEditorDecoration(decoration),
      unregisterEditorDecoration: (id: string) => extensionPointAPI.unregisterEditorDecoration(id),

      // Theme extension points
      registerTheme: (theme: any) => extensionPointAPI.registerTheme(theme),
      unregisterTheme: (id: string) => extensionPointAPI.unregisterTheme(id),

      // Unregister all extension points
      unregisterAll: () => extensionPointAPI.unregisterAll(),
    },

    // Custom fields API
    fields: {
      // Field registration
      registerField: (field: any) => fieldsAPI.registerField(field),
      unregisterField: (fieldId: string) => fieldsAPI.unregisterField(fieldId),

      // Field retrieval
      getField: (fieldId: string) => fieldsAPI.getField(fieldId),
      getAllFields: () => fieldsAPI.getAllFields(),
      getFieldsByCategory: (category: string) => fieldsAPI.getFieldsByCategory(category),

      // Category management
      registerCategory: (category: any) => fieldsAPI.registerCategory(category),
      unregisterCategory: (categoryId: string) => fieldsAPI.unregisterCategory(categoryId),
      getCategories: () => fieldsAPI.getCategories(),

      // Field rendering and validation
      renderField: (config: any, value: any, onChange: (value: any) => void, options?: any) =>
        fieldsAPI.renderField(config, value, onChange, options),
      validateField: (config: any, value: any) => fieldsAPI.validateField(config, value),
    },

    // Event system
    on: <T = any>(event: string, callback: (data: T) => void) => {
      eventEmitter.on(event, callback);
      fieldsAPI.on(event, callback);
    },

    off: <T = any>(event: string, callback: (data: T) => void) => {
      eventEmitter.off(event, callback);
      fieldsAPI.off(event, callback);
    },

    emit: <T = any>(event: string, data: T) => {
      eventEmitter.emit(event, data);
      fieldsAPI.emit(event, data);
    },
  };

  return api;
}
