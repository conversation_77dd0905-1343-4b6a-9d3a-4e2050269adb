/**
 * Plugin Registry
 *
 * Manages the registration, loading, and lifecycle of plugins.
 */

import { EventEmitter } from 'events';
import {
  Plugin,
  PluginManifest,
  PluginLifecycle,
  PluginState,
  PluginError,
  PluginRegistryEntry,
  PluginSettings,
  PluginAPI,
  PluginTab
} from './types';
import { createPluginAPI } from './plugin-api';
import { validateManifest } from './validation';
import { createNamespacedLogger } from '@/lib/logger';
import { getExtensionPointRegistry } from './extension-points';

// Create a logger for this module
const logger = createNamespacedLogger('plugin-registry');

/**
 * Plugin registry events
 */
export enum PluginRegistryEvent {
  PLUGIN_REGISTERED = 'plugin:registered',
  PLUGIN_LOADED = 'plugin:loaded',
  PLUGIN_ENABLED = 'plugin:enabled',
  PLUGIN_DISABLED = 'plugin:disabled',
  PLUGIN_UNLOADED = 'plugin:unloaded',
  PLUGIN_ERROR = 'plugin:error',
  PLUGIN_SETTINGS_UPDATED = 'plugin:settings:updated',
}

/**
 * Plugin registry class
 */
export class PluginRegistry extends EventEmitter {
  private plugins: Map<string, PluginRegistryEntry> = new Map();
  private tabs: Map<string, PluginTab> = new Map();
  private initialized: boolean = false;

  /**
   * Initialize the plugin registry
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    logger.info('Initializing plugin registry');
    this.initialized = true;
    logger.info('Plugin registry initialized');
  }

  /**
   * Register a plugin
   */
  async registerPlugin(
    manifest: PluginManifest,
    lifecycle: PluginLifecycle
  ): Promise<Plugin> {
    try {
      // Validate manifest
      validateManifest(manifest);

      const pluginId = manifest.metadata.id;

      // Check if plugin is already registered
      if (this.plugins.has(pluginId)) {
        throw this.createError(
          'PLUGIN_ALREADY_REGISTERED',
          `Plugin ${pluginId} is already registered`,
          { pluginId }
        );
      }

      // Create plugin settings
      const settings: PluginSettings = {};
      if (manifest.settings) {
        // Initialize settings with default values
        Object.entries(manifest.settings).forEach(([key, definition]) => {
          settings[key] = definition.default;
        });
      }

      // Create plugin API
      const api = createPluginAPI(this, pluginId, settings);

      // Create plugin instance
      const plugin: Plugin = {
        manifest,
        lifecycle,
        enabled: false,
        loaded: false,
        api,
        settings,
      };

      // Create registry entry
      const entry: PluginRegistryEntry = {
        plugin,
        state: PluginState.REGISTERED,
        dependencies: {
          required: manifest.dependencies?.filter(d => !d.optional) || [],
          optional: manifest.dependencies?.filter(d => d.optional) || [],
        },
      };

      // Add to registry
      this.plugins.set(pluginId, entry);

      // Emit event
      this.emit(PluginRegistryEvent.PLUGIN_REGISTERED, { pluginId, plugin });

      logger.info(`Plugin registered: ${pluginId}`);
      return plugin;
    } catch (error) {
      logger.error('Failed to register plugin', error);
      throw error;
    }
  }

  /**
   * Load a plugin
   */
  async loadPlugin(pluginId: string): Promise<Plugin> {
    try {
      const entry = this.getPluginEntry(pluginId);

      // Check if plugin is already loaded
      if (entry.plugin.loaded) {
        return entry.plugin;
      }

      // Update state
      entry.state = PluginState.LOADING;

      // Check dependencies
      await this.checkDependencies(pluginId);

      // Call onLoad lifecycle hook
      if (entry.plugin.lifecycle.onLoad) {
        await entry.plugin.lifecycle.onLoad();
      }

      // Update state
      entry.plugin.loaded = true;
      entry.state = PluginState.LOADED;

      // Emit event
      this.emit(PluginRegistryEvent.PLUGIN_LOADED, { pluginId, plugin: entry.plugin });

      logger.info(`Plugin loaded: ${pluginId}`);
      return entry.plugin;
    } catch (error) {
      // Update state
      const entry = this.plugins.get(pluginId);
      if (entry) {
        entry.state = PluginState.ERROR;
        entry.error = this.normalizeError(error);
      }

      // Emit error event
      this.emit(PluginRegistryEvent.PLUGIN_ERROR, {
        pluginId,
        error: this.normalizeError(error),
      });

      logger.error(`Failed to load plugin ${pluginId}:`, error);
      throw error;
    }
  }

  /**
   * Enable a plugin
   */
  async enablePlugin(pluginId: string): Promise<Plugin> {
    try {
      const entry = this.getPluginEntry(pluginId);

      // Check if plugin is already enabled
      if (entry.plugin.enabled) {
        return entry.plugin;
      }

      // Load plugin if not loaded
      if (!entry.plugin.loaded) {
        await this.loadPlugin(pluginId);
      }

      // Call onEnable lifecycle hook
      if (entry.plugin.lifecycle.onEnable) {
        await entry.plugin.lifecycle.onEnable();
      }

      // Update state
      entry.plugin.enabled = true;
      entry.state = PluginState.ENABLED;

      // Emit event
      this.emit(PluginRegistryEvent.PLUGIN_ENABLED, { pluginId, plugin: entry.plugin });

      logger.info(`Plugin enabled: ${pluginId}`);
      return entry.plugin;
    } catch (error) {
      // Update state
      const entry = this.plugins.get(pluginId);
      if (entry) {
        entry.state = PluginState.ERROR;
        entry.error = this.normalizeError(error);
      }

      // Emit error event
      this.emit(PluginRegistryEvent.PLUGIN_ERROR, {
        pluginId,
        error: this.normalizeError(error),
      });

      logger.error(`Failed to enable plugin ${pluginId}:`, error);
      throw error;
    }
  }

  /**
   * Disable a plugin
   */
  async disablePlugin(pluginId: string): Promise<Plugin> {
    try {
      const entry = this.getPluginEntry(pluginId);

      // Check if plugin is already disabled
      if (!entry.plugin.enabled) {
        return entry.plugin;
      }

      // Call onDisable lifecycle hook
      if (entry.plugin.lifecycle.onDisable) {
        await entry.plugin.lifecycle.onDisable();
      }

      // Update state
      entry.plugin.enabled = false;
      entry.state = PluginState.DISABLED;

      // Emit event
      this.emit(PluginRegistryEvent.PLUGIN_DISABLED, { pluginId, plugin: entry.plugin });

      logger.info(`Plugin disabled: ${pluginId}`);
      return entry.plugin;
    } catch (error) {
      // Emit error event
      this.emit(PluginRegistryEvent.PLUGIN_ERROR, {
        pluginId,
        error: this.normalizeError(error),
      });

      logger.error(`Failed to disable plugin ${pluginId}:`, error);
      throw error;
    }
  }

  /**
   * Unload a plugin
   */
  async unloadPlugin(pluginId: string): Promise<void> {
    try {
      const entry = this.getPluginEntry(pluginId);

      // Check if plugin is already unloaded
      if (!entry.plugin.loaded) {
        return;
      }

      // Disable plugin if enabled
      if (entry.plugin.enabled) {
        await this.disablePlugin(pluginId);
      }

      // Call onUnload lifecycle hook
      if (entry.plugin.lifecycle.onUnload) {
        await entry.plugin.lifecycle.onUnload();
      }

      // Clean up extension points
      try {
        const extensionPointRegistry = getExtensionPointRegistry();
        extensionPointRegistry.unregisterAllExtensionPoints(pluginId);
      } catch (error) {
        logger.warn(`Failed to clean up extension points for plugin ${pluginId}:`, error);
      }

      // Update state
      entry.plugin.loaded = false;
      entry.state = PluginState.UNLOADED;

      // Emit event
      this.emit(PluginRegistryEvent.PLUGIN_UNLOADED, { pluginId });

      logger.info(`Plugin unloaded: ${pluginId}`);
    } catch (error) {
      // Update state
      const entry = this.plugins.get(pluginId);
      if (entry) {
        entry.state = PluginState.ERROR;
        entry.error = this.normalizeError(error);
      }

      // Emit error event
      this.emit(PluginRegistryEvent.PLUGIN_ERROR, {
        pluginId,
        error: this.normalizeError(error),
      });

      logger.error(`Failed to unload plugin ${pluginId}:`, error);
      throw error;
    }
  }

  /**
   * Get a plugin by ID
   */
  getPlugin(pluginId: string): Plugin | undefined {
    return this.plugins.get(pluginId)?.plugin;
  }

  /**
   * Get all plugins
   */
  getAllPlugins(): Plugin[] {
    return Array.from(this.plugins.values()).map(entry => entry.plugin);
  }

  /**
   * Register a tab
   */
  registerTab(pluginId: string, tab: PluginTab): void {
    const tabId = `${pluginId}:${tab.id}`;
    this.tabs.set(tabId, tab);
    logger.debug(`Tab registered: ${tabId}`);
  }

  /**
   * Unregister a tab
   */
  unregisterTab(pluginId: string, tabId: string): void {
    const fullTabId = `${pluginId}:${tabId}`;
    this.tabs.delete(fullTabId);
    logger.debug(`Tab unregistered: ${fullTabId}`);
  }

  /**
   * Get all tabs
   */
  getAllTabs(): PluginTab[] {
    return Array.from(this.tabs.values());
  }

  /**
   * Get tabs by location
   */
  getTabsByLocation(location: 'main' | 'left' | 'right' | 'bottom'): PluginTab[] {
    return Array.from(this.tabs.values())
      .filter(tab => tab.location === location)
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  }

  /**
   * Update plugin settings
   */
  async updatePluginSettings(
    pluginId: string,
    settings: Partial<PluginSettings>
  ): Promise<PluginSettings> {
    const entry = this.getPluginEntry(pluginId);

    // Merge settings
    entry.plugin.settings = {
      ...entry.plugin.settings,
      ...settings,
    };

    // Emit event
    this.emit(PluginRegistryEvent.PLUGIN_SETTINGS_UPDATED, {
      pluginId,
      settings: entry.plugin.settings,
    });

    logger.debug(`Plugin settings updated: ${pluginId}`);
    return entry.plugin.settings;
  }

  /**
   * Check plugin dependencies
   */
  private async checkDependencies(pluginId: string): Promise<void> {
    const entry = this.getPluginEntry(pluginId);

    // Check required dependencies
    for (const dep of entry.dependencies.required) {
      const depEntry = this.plugins.get(dep.id);

      if (!depEntry) {
        throw this.createError(
          'DEPENDENCY_NOT_FOUND',
          `Required dependency ${dep.id} not found for plugin ${pluginId}`,
          { pluginId, dependencyId: dep.id }
        );
      }

      // Load dependency if not loaded
      if (!depEntry.plugin.loaded) {
        await this.loadPlugin(dep.id);
      }
    }

    // Try to load optional dependencies
    for (const dep of entry.dependencies.optional) {
      try {
        const depEntry = this.plugins.get(dep.id);
        if (depEntry && !depEntry.plugin.loaded) {
          await this.loadPlugin(dep.id);
        }
      } catch (error) {
        logger.warn(`Failed to load optional dependency ${dep.id} for plugin ${pluginId}:`, error);
      }
    }
  }

  /**
   * Get plugin entry
   */
  private getPluginEntry(pluginId: string): PluginRegistryEntry {
    const entry = this.plugins.get(pluginId);

    if (!entry) {
      throw this.createError(
        'PLUGIN_NOT_FOUND',
        `Plugin ${pluginId} not found`,
        { pluginId }
      );
    }

    return entry;
  }

  /**
   * Create a plugin error
   */
  private createError(code: string, message: string, details?: any): PluginError {
    return { code, message, details };
  }

  /**
   * Normalize an error to PluginError
   */
  private normalizeError(error: any): PluginError {
    if (error && typeof error === 'object' && 'code' in error && 'message' in error) {
      return error as PluginError;
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: error instanceof Error ? error.message : String(error),
      details: error,
    };
  }
}
