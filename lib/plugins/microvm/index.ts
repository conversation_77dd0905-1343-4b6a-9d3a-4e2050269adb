/**
 * MicroVM Module
 * 
 * Main entry point for the MicroVM module.
 */

import { MicroVMService } from './microvm-service';
import { PluginRunner } from './plugin-runner';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('microvm');

// Export types
export * from './microvm-service';
export * from './plugin-runner';

// Create singleton instances
const microVMService = new MicroVMService();
const pluginRunner = new PluginRunner({ microVMService });

/**
 * Initialize the MicroVM module
 */
export async function initializeMicroVM(): Promise<void> {
  try {
    logger.info('Initializing MicroVM module');
    
    // Initialize plugin runner
    await pluginRunner.initialize();
    
    logger.info('MicroVM module initialized');
  } catch (error) {
    logger.error('Failed to initialize MicroVM module', error);
    throw error;
  }
}

/**
 * Get the MicroVM service
 */
export function getMicroVMService(): MicroVMService {
  return microVMService;
}

/**
 * Get the plugin runner
 */
export function getPluginRunner(): PluginRunner {
  return pluginRunner;
}
