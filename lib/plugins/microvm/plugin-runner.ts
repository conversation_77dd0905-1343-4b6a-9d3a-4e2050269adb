/**
 * Plugin Runner
 * 
 * Runs plugin code in a MicroVM.
 */

import { MicroVMService, MicroVM } from './microvm-service';
import { Plugin, PluginManifest, PluginLifecycle } from '../types';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('plugin-runner');

/**
 * Plugin runner options
 */
export interface PluginRunnerOptions {
  microVMService: MicroVMService;
}

/**
 * Plugin runner class
 */
export class PluginRunner {
  private microVMService: MicroVMService;
  private pluginVMs: Map<string, MicroVM> = new Map();
  private initialized: boolean = false;

  /**
   * Create a new plugin runner
   */
  constructor(options: PluginRunnerOptions) {
    this.microVMService = options.microVMService;
  }

  /**
   * Initialize the plugin runner
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    logger.info('Initializing plugin runner');
    
    // Initialize MicroVM service
    await this.microVMService.initialize();
    
    this.initialized = true;
    logger.info('Plugin runner initialized');
  }

  /**
   * Create a VM for a plugin
   */
  async createPluginVM(pluginId: string): Promise<MicroVM> {
    if (!this.initialized) {
      throw new Error('Plugin runner not initialized');
    }

    // Check if VM already exists
    if (this.pluginVMs.has(pluginId)) {
      return this.pluginVMs.get(pluginId)!;
    }

    try {
      // Create VM
      const vm = await this.microVMService.createVM({
        // Use minimal resources for plugin VMs
        vcpuCount: 1,
        memSizeMib: 256,
        diskSizeMib: 512,
        networkEnabled: false,
      });
      
      // Store VM
      this.pluginVMs.set(pluginId, vm);
      
      logger.info(`Created VM for plugin: ${pluginId}`);
      return vm;
    } catch (error) {
      logger.error(`Failed to create VM for plugin: ${pluginId}`, error);
      throw error;
    }
  }

  /**
   * Execute plugin lifecycle hook
   */
  async executeLifecycleHook(
    pluginId: string,
    hook: keyof PluginLifecycle,
    args: any[] = []
  ): Promise<any> {
    if (!this.initialized) {
      throw new Error('Plugin runner not initialized');
    }

    // Get or create VM
    let vm = this.pluginVMs.get(pluginId);
    if (!vm) {
      vm = await this.createPluginVM(pluginId);
    }

    try {
      // Create code to execute
      const code = `
        // Get lifecycle hook
        const hook = plugin.lifecycle['${hook}'];
        
        // Execute hook if it exists
        if (typeof hook === 'function') {
          return hook(${args.map(arg => JSON.stringify(arg)).join(', ')});
        }
        
        // Return undefined if hook doesn't exist
        return undefined;
      `;
      
      // Execute code
      const result = await this.microVMService.executeCode(vm.id, code);
      
      if (!result.success) {
        throw new Error(`Failed to execute lifecycle hook ${hook} for plugin ${pluginId}: ${result.error}`);
      }
      
      return result.output;
    } catch (error) {
      logger.error(`Failed to execute lifecycle hook ${hook} for plugin ${pluginId}`, error);
      throw error;
    }
  }

  /**
   * Clean up plugin VM
   */
  async cleanupPluginVM(pluginId: string): Promise<void> {
    if (!this.initialized) {
      return;
    }

    // Get VM
    const vm = this.pluginVMs.get(pluginId);
    if (!vm) {
      return;
    }

    try {
      // Delete VM
      await this.microVMService.deleteVM(vm.id);
      
      // Remove from map
      this.pluginVMs.delete(pluginId);
      
      logger.info(`Cleaned up VM for plugin: ${pluginId}`);
    } catch (error) {
      logger.error(`Failed to clean up VM for plugin: ${pluginId}`, error);
    }
  }

  /**
   * Clean up all plugin VMs
   */
  async cleanupAllPluginVMs(): Promise<void> {
    if (!this.initialized) {
      return;
    }

    const pluginIds = Array.from(this.pluginVMs.keys());
    
    for (const pluginId of pluginIds) {
      await this.cleanupPluginVM(pluginId);
    }
    
    logger.info('Cleaned up all plugin VMs');
  }
}
