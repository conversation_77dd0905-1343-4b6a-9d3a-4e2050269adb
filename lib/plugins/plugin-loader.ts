/**
 * Plugin Loader
 * 
 * Handles loading plugins from different sources.
 */

import { PluginManifest, PluginLifecycle, Plugin, PluginSource } from './types';
import { PluginRegistry } from './plugin-registry';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('plugin-loader');

/**
 * Plugin loader options
 */
export interface PluginLoaderOptions {
  registry: PluginRegistry;
  pluginsDir?: string;
  enabledPlugins?: string[];
  disabledPlugins?: string[];
  autoEnable?: boolean;
}

/**
 * Plugin loader class
 */
export class PluginLoader {
  private registry: PluginRegistry;
  private pluginsDir: string;
  private enabledPlugins: Set<string>;
  private disabledPlugins: Set<string>;
  private autoEnable: boolean;
  private initialized: boolean = false;

  /**
   * Create a new plugin loader
   */
  constructor(options: PluginLoaderOptions) {
    this.registry = options.registry;
    this.pluginsDir = options.pluginsDir || './plugins';
    this.enabledPlugins = new Set(options.enabledPlugins || []);
    this.disabledPlugins = new Set(options.disabledPlugins || []);
    this.autoEnable = options.autoEnable !== undefined ? options.autoEnable : true;
  }

  /**
   * Initialize the plugin loader
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    logger.info('Initializing plugin loader');
    
    // Initialize registry
    await this.registry.initialize();
    
    this.initialized = true;
    logger.info('Plugin loader initialized');
  }

  /**
   * Load a plugin from a source
   */
  async loadPluginFromSource(source: PluginSource): Promise<Plugin> {
    try {
      logger.info(`Loading plugin from ${source.type} source: ${source.location}`);
      
      // Create plugin lifecycle
      const lifecycle = this.createLifecycle(source);
      
      // Register plugin
      const plugin = await this.registry.registerPlugin(source.manifest, lifecycle);
      
      // Load plugin
      await this.registry.loadPlugin(plugin.manifest.metadata.id);
      
      // Enable plugin if auto-enable is true and not in disabled list
      if (
        this.autoEnable && 
        !this.disabledPlugins.has(plugin.manifest.metadata.id) &&
        (this.enabledPlugins.size === 0 || this.enabledPlugins.has(plugin.manifest.metadata.id))
      ) {
        await this.registry.enablePlugin(plugin.manifest.metadata.id);
      }
      
      return plugin;
    } catch (error) {
      logger.error(`Failed to load plugin from source: ${source.location}`, error);
      throw error;
    }
  }

  /**
   * Load a plugin from code
   */
  async loadPluginFromCode(
    manifest: PluginManifest,
    code: string
  ): Promise<Plugin> {
    try {
      logger.info(`Loading plugin from code: ${manifest.metadata.id}`);
      
      // Create plugin source
      const source: PluginSource = {
        type: 'builtin',
        location: 'memory',
        manifest,
        code,
      };
      
      // Load plugin from source
      return await this.loadPluginFromSource(source);
    } catch (error) {
      logger.error(`Failed to load plugin from code: ${manifest.metadata.id}`, error);
      throw error;
    }
  }

  /**
   * Create plugin lifecycle from source
   */
  private createLifecycle(source: PluginSource): PluginLifecycle {
    try {
      // For security, we use Function constructor to create isolated scope
      // In a production environment, this should be replaced with a more secure sandbox
      const createLifecycle = new Function('manifest', source.code);
      
      // Execute the code to get the lifecycle
      const lifecycle = createLifecycle(source.manifest);
      
      // Validate lifecycle
      this.validateLifecycle(lifecycle);
      
      return lifecycle;
    } catch (error) {
      logger.error(`Failed to create lifecycle for plugin: ${source.manifest.metadata.id}`, error);
      throw new Error(`Failed to create lifecycle for plugin: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Validate plugin lifecycle
   */
  private validateLifecycle(lifecycle: any): void {
    // Check if lifecycle is an object
    if (!lifecycle || typeof lifecycle !== 'object') {
      throw new Error('Plugin lifecycle must be an object');
    }
    
    // Check if lifecycle hooks are functions
    const hooks = ['onLoad', 'onUnload', 'onEnable', 'onDisable', 'onUpdate'];
    for (const hook of hooks) {
      if (lifecycle[hook] !== undefined && typeof lifecycle[hook] !== 'function') {
        throw new Error(`Plugin lifecycle hook ${hook} must be a function`);
      }
    }
  }
}
