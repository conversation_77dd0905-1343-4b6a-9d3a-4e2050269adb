/**
 * Plugin System
 *
 * Main entry point for the plugin system.
 */

import { PluginRegistry, PluginRegistryEvent } from './plugin-registry';
import { PluginLoader } from './plugin-loader';
import { createNamespacedLogger } from '@/lib/logger';
import { initializeMarketplace } from './marketplace';
import { initializeCustomFieldsRegistry } from './custom-fields';

// Create a logger for this module
const logger = createNamespacedLogger('plugin-system');

// Export types
export * from './types';
export { PluginRegistry, PluginRegistryEvent } from './plugin-registry';
export { PluginLoader } from './plugin-loader';
export * from './extension-points';
export * from './marketplace';

// Export custom fields system
export * from './custom-fields';

// Create singleton instances
const registry = new PluginRegistry();
const loader = new PluginLoader({ registry });

/**
 * Initialize the plugin system
 */
export async function initializePluginSystem(): Promise<void> {
  try {
    logger.info('Initializing plugin system');

    // Initialize registry
    await registry.initialize();

    // Initialize custom fields system
    await initializeCustomFieldsRegistry();

    // Initialize loader
    await loader.initialize();

    // Initialize marketplace
    await initializeMarketplace();

    logger.info('Plugin system initialized');
  } catch (error) {
    logger.error('Failed to initialize plugin system', error);
    throw error;
  }
}

/**
 * Get the plugin registry
 */
export function getPluginRegistry(): PluginRegistry {
  return registry;
}

/**
 * Get the plugin loader
 */
export function getPluginLoader(): PluginLoader {
  return loader;
}
