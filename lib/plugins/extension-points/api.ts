/**
 * Extension Point API
 * 
 * Provides an API for plugins to register and unregister extension points.
 */

import {
  ExtensionPointType,
  ExtensionPointData,
  TabExtensionPoint,
  ToolbarExtensionPoint,
  MenuExtensionPoint,
  ContextMenuExtensionPoint,
  SidebarExtensionPoint,
  StatusBarExtensionPoint,
  CommandPaletteExtensionPoint,
  EditorDecorationExtensionPoint,
  ThemeExtensionPoint
} from './types';
import { ExtensionPointRegistryManager } from './registry';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('extension-point-api');

/**
 * Extension point API class
 */
export class ExtensionPointAPI {
  private registry: ExtensionPointRegistryManager;
  private pluginId: string;

  /**
   * Create a new extension point API
   */
  constructor(registry: ExtensionPointRegistryManager, pluginId: string) {
    this.registry = registry;
    this.pluginId = pluginId;
  }

  /**
   * Register a tab extension point
   */
  registerTab(tab: Omit<TabExtensionPoint, 'pluginId'>): void {
    try {
      this.registry.registerExtensionPoint('tab', {
        ...tab,
        pluginId: this.pluginId,
      });
    } catch (error) {
      logger.error(`Failed to register tab extension point: ${tab.id}`, error);
      throw error;
    }
  }

  /**
   * Unregister a tab extension point
   */
  unregisterTab(id: string): void {
    try {
      this.registry.unregisterExtensionPoint('tab', this.pluginId, id);
    } catch (error) {
      logger.error(`Failed to unregister tab extension point: ${id}`, error);
      throw error;
    }
  }

  /**
   * Register a toolbar extension point
   */
  registerToolbarItem(toolbar: Omit<ToolbarExtensionPoint, 'pluginId'>): void {
    try {
      this.registry.registerExtensionPoint('toolbar', {
        ...toolbar,
        pluginId: this.pluginId,
      });
    } catch (error) {
      logger.error(`Failed to register toolbar extension point: ${toolbar.id}`, error);
      throw error;
    }
  }

  /**
   * Unregister a toolbar extension point
   */
  unregisterToolbarItem(id: string): void {
    try {
      this.registry.unregisterExtensionPoint('toolbar', this.pluginId, id);
    } catch (error) {
      logger.error(`Failed to unregister toolbar extension point: ${id}`, error);
      throw error;
    }
  }

  /**
   * Register a menu extension point
   */
  registerMenuItem(menu: Omit<MenuExtensionPoint, 'pluginId'>): void {
    try {
      this.registry.registerExtensionPoint('menu', {
        ...menu,
        pluginId: this.pluginId,
      });
    } catch (error) {
      logger.error(`Failed to register menu extension point: ${menu.id}`, error);
      throw error;
    }
  }

  /**
   * Unregister a menu extension point
   */
  unregisterMenuItem(id: string): void {
    try {
      this.registry.unregisterExtensionPoint('menu', this.pluginId, id);
    } catch (error) {
      logger.error(`Failed to unregister menu extension point: ${id}`, error);
      throw error;
    }
  }

  /**
   * Register a context menu extension point
   */
  registerContextMenuItem(contextMenu: Omit<ContextMenuExtensionPoint, 'pluginId'>): void {
    try {
      this.registry.registerExtensionPoint('contextMenu', {
        ...contextMenu,
        pluginId: this.pluginId,
      });
    } catch (error) {
      logger.error(`Failed to register context menu extension point: ${contextMenu.id}`, error);
      throw error;
    }
  }

  /**
   * Unregister a context menu extension point
   */
  unregisterContextMenuItem(id: string): void {
    try {
      this.registry.unregisterExtensionPoint('contextMenu', this.pluginId, id);
    } catch (error) {
      logger.error(`Failed to unregister context menu extension point: ${id}`, error);
      throw error;
    }
  }

  /**
   * Register a sidebar extension point
   */
  registerSidebarItem(sidebar: Omit<SidebarExtensionPoint, 'pluginId'>): void {
    try {
      this.registry.registerExtensionPoint('sidebar', {
        ...sidebar,
        pluginId: this.pluginId,
      });
    } catch (error) {
      logger.error(`Failed to register sidebar extension point: ${sidebar.id}`, error);
      throw error;
    }
  }

  /**
   * Unregister a sidebar extension point
   */
  unregisterSidebarItem(id: string): void {
    try {
      this.registry.unregisterExtensionPoint('sidebar', this.pluginId, id);
    } catch (error) {
      logger.error(`Failed to unregister sidebar extension point: ${id}`, error);
      throw error;
    }
  }

  /**
   * Register a status bar extension point
   */
  registerStatusBarItem(statusBar: Omit<StatusBarExtensionPoint, 'pluginId'>): void {
    try {
      this.registry.registerExtensionPoint('statusBar', {
        ...statusBar,
        pluginId: this.pluginId,
      });
    } catch (error) {
      logger.error(`Failed to register status bar extension point: ${statusBar.id}`, error);
      throw error;
    }
  }

  /**
   * Unregister a status bar extension point
   */
  unregisterStatusBarItem(id: string): void {
    try {
      this.registry.unregisterExtensionPoint('statusBar', this.pluginId, id);
    } catch (error) {
      logger.error(`Failed to unregister status bar extension point: ${id}`, error);
      throw error;
    }
  }

  /**
   * Register a command palette extension point
   */
  registerCommand(command: Omit<CommandPaletteExtensionPoint, 'pluginId'>): void {
    try {
      this.registry.registerExtensionPoint('commandPalette', {
        ...command,
        pluginId: this.pluginId,
      });
    } catch (error) {
      logger.error(`Failed to register command palette extension point: ${command.id}`, error);
      throw error;
    }
  }

  /**
   * Unregister a command palette extension point
   */
  unregisterCommand(id: string): void {
    try {
      this.registry.unregisterExtensionPoint('commandPalette', this.pluginId, id);
    } catch (error) {
      logger.error(`Failed to unregister command palette extension point: ${id}`, error);
      throw error;
    }
  }

  /**
   * Register an editor decoration extension point
   */
  registerEditorDecoration(decoration: Omit<EditorDecorationExtensionPoint, 'pluginId'>): void {
    try {
      this.registry.registerExtensionPoint('editorDecoration', {
        ...decoration,
        pluginId: this.pluginId,
      });
    } catch (error) {
      logger.error(`Failed to register editor decoration extension point: ${decoration.id}`, error);
      throw error;
    }
  }

  /**
   * Unregister an editor decoration extension point
   */
  unregisterEditorDecoration(id: string): void {
    try {
      this.registry.unregisterExtensionPoint('editorDecoration', this.pluginId, id);
    } catch (error) {
      logger.error(`Failed to unregister editor decoration extension point: ${id}`, error);
      throw error;
    }
  }

  /**
   * Register a theme extension point
   */
  registerTheme(theme: Omit<ThemeExtensionPoint, 'pluginId'>): void {
    try {
      this.registry.registerExtensionPoint('theme', {
        ...theme,
        pluginId: this.pluginId,
      });
    } catch (error) {
      logger.error(`Failed to register theme extension point: ${theme.id}`, error);
      throw error;
    }
  }

  /**
   * Unregister a theme extension point
   */
  unregisterTheme(id: string): void {
    try {
      this.registry.unregisterExtensionPoint('theme', this.pluginId, id);
    } catch (error) {
      logger.error(`Failed to unregister theme extension point: ${id}`, error);
      throw error;
    }
  }

  /**
   * Unregister all extension points for this plugin
   */
  unregisterAll(): void {
    try {
      this.registry.unregisterAllExtensionPoints(this.pluginId);
    } catch (error) {
      logger.error(`Failed to unregister all extension points for plugin: ${this.pluginId}`, error);
      throw error;
    }
  }
}
