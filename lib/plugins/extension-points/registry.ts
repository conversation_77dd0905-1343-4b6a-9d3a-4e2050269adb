/**
 * Extension Point Registry
 * 
 * Manages all extension points in the plugin system.
 */

import { EventEmitter } from 'events';
import {
  ExtensionPointRegistry,
  ExtensionPointType,
  ExtensionPointData,
  TabExtensionPoint,
  ToolbarExtensionPoint,
  MenuExtensionPoint,
  ContextMenuExtensionPoint,
  SidebarExtensionPoint,
  StatusBarExtensionPoint,
  CommandPaletteExtensionPoint,
  EditorDecorationExtensionPoint,
  ThemeExtensionPoint
} from './types';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('extension-point-registry');

/**
 * Extension point registry events
 */
export enum ExtensionPointRegistryEvent {
  EXTENSION_POINT_REGISTERED = 'extension-point:registered',
  EXTENSION_POINT_UNREGISTERED = 'extension-point:unregistered',
}

/**
 * Extension point registry class
 */
export class ExtensionPointRegistryManager extends EventEmitter {
  private registry: ExtensionPointRegistry = {
    tabs: new Map<string, TabExtensionPoint>(),
    toolbars: new Map<string, ToolbarExtensionPoint>(),
    menus: new Map<string, MenuExtensionPoint>(),
    contextMenus: new Map<string, ContextMenuExtensionPoint>(),
    sidebars: new Map<string, SidebarExtensionPoint>(),
    statusBars: new Map<string, StatusBarExtensionPoint>(),
    commandPalette: new Map<string, CommandPaletteExtensionPoint>(),
    editorDecorations: new Map<string, EditorDecorationExtensionPoint>(),
    themes: new Map<string, ThemeExtensionPoint>(),
  };

  /**
   * Register an extension point
   */
  registerExtensionPoint(
    type: ExtensionPointType,
    data: ExtensionPointData
  ): void {
    const fullId = `${data.pluginId}:${data.id}`;
    
    try {
      switch (type) {
        case 'tab':
          this.registry.tabs.set(fullId, data as TabExtensionPoint);
          break;
        case 'toolbar':
          this.registry.toolbars.set(fullId, data as ToolbarExtensionPoint);
          break;
        case 'menu':
          this.registry.menus.set(fullId, data as MenuExtensionPoint);
          break;
        case 'contextMenu':
          this.registry.contextMenus.set(fullId, data as ContextMenuExtensionPoint);
          break;
        case 'sidebar':
          this.registry.sidebars.set(fullId, data as SidebarExtensionPoint);
          break;
        case 'statusBar':
          this.registry.statusBars.set(fullId, data as StatusBarExtensionPoint);
          break;
        case 'commandPalette':
          this.registry.commandPalette.set(fullId, data as CommandPaletteExtensionPoint);
          break;
        case 'editorDecoration':
          this.registry.editorDecorations.set(fullId, data as EditorDecorationExtensionPoint);
          break;
        case 'theme':
          this.registry.themes.set(fullId, data as ThemeExtensionPoint);
          break;
        default:
          throw new Error(`Unknown extension point type: ${type}`);
      }
      
      // Emit event
      this.emit(ExtensionPointRegistryEvent.EXTENSION_POINT_REGISTERED, { type, id: fullId, data });
      
      logger.debug(`Registered extension point: ${type}:${fullId}`);
    } catch (error) {
      logger.error(`Failed to register extension point: ${type}:${fullId}`, error);
      throw error;
    }
  }

  /**
   * Unregister an extension point
   */
  unregisterExtensionPoint(
    type: ExtensionPointType,
    pluginId: string,
    id: string
  ): void {
    const fullId = `${pluginId}:${id}`;
    
    try {
      let deleted = false;
      
      switch (type) {
        case 'tab':
          deleted = this.registry.tabs.delete(fullId);
          break;
        case 'toolbar':
          deleted = this.registry.toolbars.delete(fullId);
          break;
        case 'menu':
          deleted = this.registry.menus.delete(fullId);
          break;
        case 'contextMenu':
          deleted = this.registry.contextMenus.delete(fullId);
          break;
        case 'sidebar':
          deleted = this.registry.sidebars.delete(fullId);
          break;
        case 'statusBar':
          deleted = this.registry.statusBars.delete(fullId);
          break;
        case 'commandPalette':
          deleted = this.registry.commandPalette.delete(fullId);
          break;
        case 'editorDecoration':
          deleted = this.registry.editorDecorations.delete(fullId);
          break;
        case 'theme':
          deleted = this.registry.themes.delete(fullId);
          break;
        default:
          throw new Error(`Unknown extension point type: ${type}`);
      }
      
      if (deleted) {
        // Emit event
        this.emit(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, { type, id: fullId });
        
        logger.debug(`Unregistered extension point: ${type}:${fullId}`);
      }
    } catch (error) {
      logger.error(`Failed to unregister extension point: ${type}:${fullId}`, error);
      throw error;
    }
  }

  /**
   * Unregister all extension points for a plugin
   */
  unregisterAllExtensionPoints(pluginId: string): void {
    try {
      // Helper function to unregister extension points from a map
      const unregisterFromMap = <T extends ExtensionPointData>(
        map: Map<string, T>,
        type: ExtensionPointType
      ) => {
        const toDelete: string[] = [];
        
        // Find all extension points for this plugin
        for (const [id, data] of map.entries()) {
          if (data.pluginId === pluginId) {
            toDelete.push(id);
          }
        }
        
        // Delete them
        for (const id of toDelete) {
          map.delete(id);
          
          // Emit event
          this.emit(ExtensionPointRegistryEvent.EXTENSION_POINT_UNREGISTERED, { type, id });
        }
        
        return toDelete.length;
      };
      
      // Unregister from all maps
      const counts = {
        tabs: unregisterFromMap(this.registry.tabs, 'tab'),
        toolbars: unregisterFromMap(this.registry.toolbars, 'toolbar'),
        menus: unregisterFromMap(this.registry.menus, 'menu'),
        contextMenus: unregisterFromMap(this.registry.contextMenus, 'contextMenu'),
        sidebars: unregisterFromMap(this.registry.sidebars, 'sidebar'),
        statusBars: unregisterFromMap(this.registry.statusBars, 'statusBar'),
        commandPalette: unregisterFromMap(this.registry.commandPalette, 'commandPalette'),
        editorDecorations: unregisterFromMap(this.registry.editorDecorations, 'editorDecoration'),
        themes: unregisterFromMap(this.registry.themes, 'theme'),
      };
      
      const total = Object.values(counts).reduce((sum, count) => sum + count, 0);
      
      logger.debug(`Unregistered ${total} extension points for plugin: ${pluginId}`);
    } catch (error) {
      logger.error(`Failed to unregister all extension points for plugin: ${pluginId}`, error);
      throw error;
    }
  }

  /**
   * Get all extension points of a specific type
   */
  getExtensionPoints<T extends ExtensionPointData>(type: ExtensionPointType): T[] {
    try {
      switch (type) {
        case 'tab':
          return Array.from(this.registry.tabs.values()) as unknown as T[];
        case 'toolbar':
          return Array.from(this.registry.toolbars.values()) as unknown as T[];
        case 'menu':
          return Array.from(this.registry.menus.values()) as unknown as T[];
        case 'contextMenu':
          return Array.from(this.registry.contextMenus.values()) as unknown as T[];
        case 'sidebar':
          return Array.from(this.registry.sidebars.values()) as unknown as T[];
        case 'statusBar':
          return Array.from(this.registry.statusBars.values()) as unknown as T[];
        case 'commandPalette':
          return Array.from(this.registry.commandPalette.values()) as unknown as T[];
        case 'editorDecoration':
          return Array.from(this.registry.editorDecorations.values()) as unknown as T[];
        case 'theme':
          return Array.from(this.registry.themes.values()) as unknown as T[];
        default:
          throw new Error(`Unknown extension point type: ${type}`);
      }
    } catch (error) {
      logger.error(`Failed to get extension points of type: ${type}`, error);
      throw error;
    }
  }

  /**
   * Get extension points of a specific type by location
   */
  getExtensionPointsByLocation<T extends ExtensionPointData>(
    type: ExtensionPointType,
    location: string
  ): T[] {
    try {
      const extensionPoints = this.getExtensionPoints<T>(type);
      
      // Filter by location if the extension point has a location property
      return extensionPoints.filter((ep: any) => {
        return ep.location === location;
      });
    } catch (error) {
      logger.error(`Failed to get extension points of type: ${type} by location: ${location}`, error);
      throw error;
    }
  }

  /**
   * Get extension points of a specific type by context
   */
  getExtensionPointsByContext<T extends ExtensionPointData>(
    type: ExtensionPointType,
    context: string
  ): T[] {
    try {
      const extensionPoints = this.getExtensionPoints<T>(type);
      
      // Filter by context if the extension point has a context property
      return extensionPoints.filter((ep: any) => {
        return ep.context === context;
      });
    } catch (error) {
      logger.error(`Failed to get extension points of type: ${type} by context: ${context}`, error);
      throw error;
    }
  }

  /**
   * Get extension points of a specific type for a plugin
   */
  getExtensionPointsForPlugin<T extends ExtensionPointData>(
    type: ExtensionPointType,
    pluginId: string
  ): T[] {
    try {
      const extensionPoints = this.getExtensionPoints<T>(type);
      
      // Filter by plugin ID
      return extensionPoints.filter(ep => ep.pluginId === pluginId);
    } catch (error) {
      logger.error(`Failed to get extension points of type: ${type} for plugin: ${pluginId}`, error);
      throw error;
    }
  }
}
