/**
 * Extension Points Module
 * 
 * Main entry point for the extension points module.
 */

import { ExtensionPointRegistryManager, ExtensionPointRegistryEvent } from './registry';
import { ExtensionPointAPI } from './api';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('extension-points');

// Export types
export * from './types';
export { ExtensionPointRegistryManager, ExtensionPointRegistryEvent } from './registry';
export { ExtensionPointAPI } from './api';

// Create singleton instance
const registry = new ExtensionPointRegistryManager();

/**
 * Get the extension point registry
 */
export function getExtensionPointRegistry(): ExtensionPointRegistryManager {
  return registry;
}

/**
 * Create an extension point API for a plugin
 */
export function createExtensionPointAPI(pluginId: string): ExtensionPointAPI {
  return new ExtensionPointAPI(registry, pluginId);
}
