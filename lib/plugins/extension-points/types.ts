/**
 * Extension Point Types
 * 
 * Defines the types for all extension points in the plugin system.
 */

import { ReactNode } from 'react';

/**
 * Base extension point interface
 */
export interface ExtensionPoint {
  id: string;
  pluginId: string;
  order?: number;
}

/**
 * Tab extension point
 * 
 * Allows plugins to add tabs to the main content area or panels.
 */
export interface TabExtensionPoint extends ExtensionPoint {
  title: string;
  icon?: ReactNode;
  component: React.ComponentType<any>;
  location: 'main' | 'left' | 'right' | 'bottom';
  props?: Record<string, any>;
}

/**
 * Toolbar extension point
 * 
 * Allows plugins to add buttons to toolbars.
 */
export interface ToolbarExtensionPoint extends ExtensionPoint {
  title: string;
  icon: ReactNode;
  location: 'main' | 'editor' | 'panel' | 'statusbar';
  onClick: () => void;
  disabled?: boolean;
  tooltip?: string;
  variant?: 'default' | 'secondary' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  shortcut?: string;
}

/**
 * Menu extension point
 * 
 * Allows plugins to add items to menus.
 */
export interface MenuExtensionPoint extends ExtensionPoint {
  title: string;
  icon?: ReactNode;
  location: 'main' | 'file' | 'edit' | 'view' | 'help';
  onClick: () => void;
  disabled?: boolean;
  shortcut?: string;
  submenu?: MenuItemExtensionPoint[];
}

/**
 * Menu item extension point
 */
export interface MenuItemExtensionPoint {
  id: string;
  title: string;
  icon?: ReactNode;
  onClick: () => void;
  disabled?: boolean;
  shortcut?: string;
  submenu?: MenuItemExtensionPoint[];
}

/**
 * Context menu extension point
 * 
 * Allows plugins to add items to context menus.
 */
export interface ContextMenuExtensionPoint extends ExtensionPoint {
  title: string;
  icon?: ReactNode;
  context: 'editor' | 'file' | 'folder' | 'tab' | 'panel';
  onClick: (contextData: any) => void;
  disabled?: boolean;
  shortcut?: string;
  condition?: (contextData: any) => boolean;
}

/**
 * Sidebar extension point
 * 
 * Allows plugins to add items to the sidebar.
 */
export interface SidebarExtensionPoint extends ExtensionPoint {
  title: string;
  icon: ReactNode;
  location: 'left' | 'right';
  component: React.ComponentType<any>;
  props?: Record<string, any>;
}

/**
 * Status bar extension point
 * 
 * Allows plugins to add items to the status bar.
 */
export interface StatusBarExtensionPoint extends ExtensionPoint {
  text?: string;
  icon?: ReactNode;
  location: 'left' | 'right';
  onClick?: () => void;
  tooltip?: string;
  component?: React.ComponentType<any>;
  props?: Record<string, any>;
}

/**
 * Command palette extension point
 * 
 * Allows plugins to add commands to the command palette.
 */
export interface CommandPaletteExtensionPoint extends ExtensionPoint {
  title: string;
  description?: string;
  icon?: ReactNode;
  category: string;
  execute: () => void;
  shortcut?: string;
}

/**
 * Editor decoration extension point
 * 
 * Allows plugins to add decorations to the code editor.
 */
export interface EditorDecorationExtensionPoint extends ExtensionPoint {
  type: 'line' | 'gutter' | 'inline' | 'widget';
  pattern: string | RegExp;
  decoration: {
    className?: string;
    hoverMessage?: string;
    renderWidget?: (match: string, file: string) => ReactNode;
    lineClassName?: string;
    gutterIconPath?: string;
    gutterIconSize?: string;
    overviewRulerColor?: string;
    overviewRulerLane?: number;
  };
  files?: string[] | ((file: string) => boolean);
}

/**
 * Theme extension point
 * 
 * Allows plugins to add custom themes.
 */
export interface ThemeExtensionPoint extends ExtensionPoint {
  name: string;
  description?: string;
  theme: {
    colors: Record<string, string>;
    fonts?: Record<string, string>;
    borderRadius?: Record<string, string>;
    spacing?: Record<string, string>;
  };
}

/**
 * Extension point registry
 */
export interface ExtensionPointRegistry {
  tabs: Map<string, TabExtensionPoint>;
  toolbars: Map<string, ToolbarExtensionPoint>;
  menus: Map<string, MenuExtensionPoint>;
  contextMenus: Map<string, ContextMenuExtensionPoint>;
  sidebars: Map<string, SidebarExtensionPoint>;
  statusBars: Map<string, StatusBarExtensionPoint>;
  commandPalette: Map<string, CommandPaletteExtensionPoint>;
  editorDecorations: Map<string, EditorDecorationExtensionPoint>;
  themes: Map<string, ThemeExtensionPoint>;
}

/**
 * Extension point type
 */
export type ExtensionPointType = 
  | 'tab'
  | 'toolbar'
  | 'menu'
  | 'contextMenu'
  | 'sidebar'
  | 'statusBar'
  | 'commandPalette'
  | 'editorDecoration'
  | 'theme';

/**
 * Extension point data
 */
export type ExtensionPointData = 
  | TabExtensionPoint
  | ToolbarExtensionPoint
  | MenuExtensionPoint
  | ContextMenuExtensionPoint
  | SidebarExtensionPoint
  | StatusBarExtensionPoint
  | CommandPaletteExtensionPoint
  | EditorDecorationExtensionPoint
  | ThemeExtensionPoint;
