/**
 * Custom Fields Plugin API
 * 
 * API interface for plugins to interact with the custom fields system.
 */

import { ReactNode } from 'react';
import { EventEmitter } from 'events';
import {
  CustomFieldComponent,
  FieldCategory,
  FieldPluginAPI,
  FieldRendererOptions,
  FieldValidationResult,
  FieldSearchCriteria,
  FieldSearchResult
} from './types';
import { FieldConfig, FieldValue } from '@/lib/core/dynamic-editor-generator/custom-fields/types';
import { getCustomFieldsRegistry } from './registry';
import { createFieldRenderer } from './renderer';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('custom-fields-api');

/**
 * Create a custom fields plugin API for a specific plugin
 */
export function createFieldPluginAPI(pluginId: string): FieldPluginAPI {
  const registry = getCustomFieldsRegistry();
  const eventEmitter = new EventEmitter();

  return {
    // Field registration
    registerField: (field: CustomFieldComponent) => {
      try {
        registry.registerField(pluginId, field);
        logger.debug(`Field registered by plugin ${pluginId}: ${field.id}`);
      } catch (error) {
        logger.error(`Failed to register field ${field.id} for plugin ${pluginId}:`, error);
        throw error;
      }
    },

    unregisterField: (fieldId: string) => {
      try {
        registry.unregisterField(fieldId);
        logger.debug(`Field unregistered by plugin ${pluginId}: ${fieldId}`);
      } catch (error) {
        logger.error(`Failed to unregister field ${fieldId} for plugin ${pluginId}:`, error);
        throw error;
      }
    },

    // Field retrieval
    getField: (fieldId: string) => {
      return registry.getField(fieldId);
    },

    getAllFields: () => {
      return registry.getAllFields();
    },

    getFieldsByCategory: (category: string) => {
      return registry.getFieldsByCategory(category);
    },

    getFieldsByPlugin: (targetPluginId: string) => {
      return registry.getFieldsByPlugin(targetPluginId);
    },

    // Category management
    registerCategory: (category: FieldCategory) => {
      try {
        registry.registerCategory(category);
        logger.debug(`Category registered by plugin ${pluginId}: ${category.id}`);
      } catch (error) {
        logger.error(`Failed to register category ${category.id} for plugin ${pluginId}:`, error);
        throw error;
      }
    },

    unregisterCategory: (categoryId: string) => {
      try {
        registry.unregisterCategory(categoryId);
        logger.debug(`Category unregistered by plugin ${pluginId}: ${categoryId}`);
      } catch (error) {
        logger.error(`Failed to unregister category ${categoryId} for plugin ${pluginId}:`, error);
        throw error;
      }
    },

    getCategories: () => {
      return registry.getCategories();
    },

    // Field rendering
    renderField: (
      config: FieldConfig,
      value: FieldValue,
      onChange: (value: FieldValue) => void,
      options?: FieldRendererOptions
    ): ReactNode => {
      try {
        const renderer = createFieldRenderer(registry);
        return renderer.render(config, value, onChange, options);
      } catch (error) {
        logger.error(`Failed to render field ${config.type}:`, error);
        throw error;
      }
    },

    validateField: (config: FieldConfig, value: FieldValue): FieldValidationResult => {
      try {
        return registry.validateField(config, value);
      } catch (error) {
        logger.error(`Failed to validate field ${config.type}:`, error);
        throw error;
      }
    },

    // Events
    on: (event: string, callback: (...args: any[]) => void) => {
      eventEmitter.on(event, callback);
      
      // Also listen to registry events
      registry.on(event, callback);
    },

    off: (event: string, callback: (...args: any[]) => void) => {
      eventEmitter.off(event, callback);
      
      // Also remove from registry events
      registry.off(event, callback);
    },

    emit: (event: string, ...args: any[]) => {
      eventEmitter.emit(event, ...args);
    },
  };
}

/**
 * Global field plugin API (for use outside of plugins)
 */
export class GlobalFieldPluginAPI implements FieldPluginAPI {
  private registry = getCustomFieldsRegistry();
  private eventEmitter = new EventEmitter();

  // Field registration (restricted for global API)
  registerField = (field: CustomFieldComponent) => {
    throw new Error('Global API cannot register fields. Use a plugin context.');
  };

  unregisterField = (fieldId: string) => {
    throw new Error('Global API cannot unregister fields. Use a plugin context.');
  };

  // Field retrieval
  getField = (fieldId: string) => {
    return this.registry.getField(fieldId);
  };

  getAllFields = () => {
    return this.registry.getAllFields();
  };

  getFieldsByCategory = (category: string) => {
    return this.registry.getFieldsByCategory(category);
  };

  getFieldsByPlugin = (pluginId: string) => {
    return this.registry.getFieldsByPlugin(pluginId);
  };

  // Category management (restricted for global API)
  registerCategory = (category: FieldCategory) => {
    throw new Error('Global API cannot register categories. Use a plugin context.');
  };

  unregisterCategory = (categoryId: string) => {
    throw new Error('Global API cannot unregister categories. Use a plugin context.');
  };

  getCategories = () => {
    return this.registry.getCategories();
  };

  // Field rendering
  renderField = (
    config: FieldConfig,
    value: FieldValue,
    onChange: (value: FieldValue) => void,
    options?: FieldRendererOptions
  ): ReactNode => {
    const renderer = createFieldRenderer(this.registry);
    return renderer.render(config, value, onChange, options);
  };

  validateField = (config: FieldConfig, value: FieldValue): FieldValidationResult => {
    return this.registry.validateField(config, value);
  };

  // Events
  on = (event: string, callback: (...args: any[]) => void) => {
    this.eventEmitter.on(event, callback);
    this.registry.on(event, callback);
  };

  off = (event: string, callback: (...args: any[]) => void) => {
    this.eventEmitter.off(event, callback);
    this.registry.off(event, callback);
  };

  emit = (event: string, ...args: any[]) => {
    this.eventEmitter.emit(event, ...args);
  };

  // Additional methods for global API
  searchFields = (criteria: FieldSearchCriteria): FieldSearchResult => {
    return this.registry.searchFields(criteria);
  };

  getPluginFields = (pluginId: string) => {
    return this.registry.getFieldsByPlugin(pluginId);
  };

  getFieldInfo = (fieldId: string) => {
    const field = this.registry.getField(fieldId);
    if (!field) return null;

    return {
      field,
      category: this.registry.getCategories().find(c => c.id === field.category),
      plugin: Array.from(this.registry['fields'].values()).find(e => e.component.id === fieldId)?.pluginId,
    };
  };
}

// Global API instance
let globalAPI: GlobalFieldPluginAPI | null = null;

/**
 * Get the global field plugin API
 */
export function getGlobalFieldPluginAPI(): GlobalFieldPluginAPI {
  if (!globalAPI) {
    globalAPI = new GlobalFieldPluginAPI();
  }
  return globalAPI;
}

/**
 * Utility functions for field management
 */
export const FieldUtils = {
  /**
   * Create a field configuration with defaults
   */
  createFieldConfig: (
    type: string,
    overrides: Partial<FieldConfig> = {}
  ): FieldConfig => {
    const registry = getCustomFieldsRegistry();
    const field = registry.getField(type);
    
    if (!field) {
      throw new Error(`Unknown field type: ${type}`);
    }

    return {
      id: `field_${Date.now()}`,
      type,
      label: field.name,
      description: field.description,
      ...field.defaultConfig,
      ...overrides,
    };
  },

  /**
   * Validate multiple fields
   */
  validateFields: (
    configs: FieldConfig[],
    values: Record<string, FieldValue>
  ): Record<string, FieldValidationResult> => {
    const registry = getCustomFieldsRegistry();
    const results: Record<string, FieldValidationResult> = {};

    configs.forEach(config => {
      const value = values[config.id];
      results[config.id] = registry.validateField(config, value);
    });

    return results;
  },

  /**
   * Get field default value
   */
  getFieldDefaultValue: (config: FieldConfig): FieldValue => {
    const registry = getCustomFieldsRegistry();
    const field = registry.getField(config.type);
    
    if (config.defaultValue !== undefined) {
      return config.defaultValue;
    }

    if (field?.defaultConfig?.defaultValue !== undefined) {
      return field.defaultConfig.defaultValue;
    }

    // Type-specific defaults
    switch (config.type) {
      case 'boolean':
        return false;
      case 'number':
      case 'range':
        return 0;
      case 'multi-select':
        return [];
      case 'object':
        return {};
      default:
        return '';
    }
  },

  /**
   * Check if field type exists
   */
  isFieldTypeRegistered: (type: string): boolean => {
    const registry = getCustomFieldsRegistry();
    return registry.getField(type) !== undefined;
  },

  /**
   * Get field component by type
   */
  getFieldComponent: (type: string): React.ComponentType<any> | undefined => {
    const registry = getCustomFieldsRegistry();
    const field = registry.getField(type);
    return field?.component;
  },
};
