/**
 * Custom Fields Plugin Types
 * 
 * Type definitions for the custom fields plugin system.
 */

import { ReactNode } from 'react';
import { FieldConfig, FieldValue, CustomFieldProps } from '@/lib/core/dynamic-editor-generator/custom-fields/types';

/**
 * Custom field component definition
 */
export interface CustomFieldComponent {
  id: string;
  type: string;
  name: string;
  description?: string;
  icon?: ReactNode;
  category?: string;
  component: React.ComponentType<CustomFieldProps>;
  configSchema?: FieldConfig[];
  defaultConfig?: Partial<FieldConfig>;
  preview?: React.ComponentType<{ config: FieldConfig }>;
  tags?: string[];
}

/**
 * Field plugin manifest
 */
export interface FieldPluginManifest {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  fields: CustomFieldComponent[];
  dependencies?: string[];
  icon?: ReactNode;
  tags?: string[];
}

/**
 * Field plugin registration data
 */
export interface FieldPluginRegistration {
  pluginId: string;
  manifest: FieldPluginManifest;
  fields: Map<string, CustomFieldComponent>;
  enabled: boolean;
  loaded: boolean;
}

/**
 * Field registry entry
 */
export interface FieldRegistryEntry {
  fieldId: string;
  pluginId: string;
  component: CustomFieldComponent;
  registered: Date;
}

/**
 * Field category definition
 */
export interface FieldCategory {
  id: string;
  name: string;
  description?: string;
  icon?: ReactNode;
  order?: number;
}

/**
 * Field validation result
 */
export interface FieldValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Field renderer options
 */
export interface FieldRendererOptions {
  enableValidation?: boolean;
  enablePreview?: boolean;
  enableDragDrop?: boolean;
  enableConditionalLogic?: boolean;
  customValidators?: Record<string, (value: FieldValue) => FieldValidationResult>;
}

/**
 * Field plugin API
 */
export interface FieldPluginAPI {
  // Field registration
  registerField: (field: CustomFieldComponent) => void;
  unregisterField: (fieldId: string) => void;
  
  // Field retrieval
  getField: (fieldId: string) => CustomFieldComponent | undefined;
  getAllFields: () => CustomFieldComponent[];
  getFieldsByCategory: (category: string) => CustomFieldComponent[];
  getFieldsByPlugin: (pluginId: string) => CustomFieldComponent[];
  
  // Category management
  registerCategory: (category: FieldCategory) => void;
  unregisterCategory: (categoryId: string) => void;
  getCategories: () => FieldCategory[];
  
  // Field rendering
  renderField: (config: FieldConfig, value: FieldValue, onChange: (value: FieldValue) => void, options?: FieldRendererOptions) => ReactNode;
  validateField: (config: FieldConfig, value: FieldValue) => FieldValidationResult;
  
  // Events
  on: (event: string, callback: (...args: any[]) => void) => void;
  off: (event: string, callback: (...args: any[]) => void) => void;
  emit: (event: string, ...args: any[]) => void;
}

/**
 * Field plugin events
 */
export enum FieldPluginEvent {
  FIELD_REGISTERED = 'field:registered',
  FIELD_UNREGISTERED = 'field:unregistered',
  CATEGORY_REGISTERED = 'category:registered',
  CATEGORY_UNREGISTERED = 'category:unregistered',
  PLUGIN_LOADED = 'plugin:loaded',
  PLUGIN_UNLOADED = 'plugin:unloaded',
  FIELD_RENDERED = 'field:rendered',
  FIELD_VALIDATED = 'field:validated',
}

/**
 * Field plugin configuration
 */
export interface FieldPluginConfig {
  enableHotReload?: boolean;
  enableValidation?: boolean;
  enablePreview?: boolean;
  enableDragDrop?: boolean;
  maxFieldsPerPlugin?: number;
  allowDuplicateTypes?: boolean;
  defaultCategory?: string;
}

/**
 * Field search criteria
 */
export interface FieldSearchCriteria {
  query?: string;
  category?: string;
  tags?: string[];
  pluginId?: string;
  type?: string;
}

/**
 * Field search result
 */
export interface FieldSearchResult {
  fields: CustomFieldComponent[];
  total: number;
  categories: FieldCategory[];
  plugins: string[];
}
