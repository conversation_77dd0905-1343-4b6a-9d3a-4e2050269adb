/**
 * Custom Fields Registry
 * 
 * Central registry for managing custom field components from plugins.
 */

import { EventEmitter } from 'events';
import {
  CustomFieldComponent,
  FieldPluginManifest,
  FieldPluginRegistration,
  FieldRegistryEntry,
  FieldCategory,
  FieldPluginEvent,
  FieldPluginConfig,
  FieldSearchCriteria,
  FieldSearchResult,
  FieldValidationResult
} from './types';
import { FieldConfig, FieldValue } from '@/lib/core/dynamic-editor-generator/custom-fields/types';
import { createNamespacedLogger } from '@/lib/logger';

// Create a logger for this module
const logger = createNamespacedLogger('custom-fields-registry');

/**
 * Default field categories
 */
const DEFAULT_CATEGORIES: FieldCategory[] = [
  { id: 'basic', name: 'Basic', description: 'Basic input fields', order: 1 },
  { id: 'advanced', name: 'Advanced', description: 'Advanced input fields', order: 2 },
  { id: 'layout', name: 'Layout', description: 'Layout and structure fields', order: 3 },
  { id: 'media', name: 'Media', description: 'Media and file fields', order: 4 },
  { id: 'data', name: 'Data', description: 'Data and API fields', order: 5 },
  { id: 'custom', name: 'Custom', description: 'Custom plugin fields', order: 6 },
];

/**
 * Custom Fields Registry Class
 */
export class CustomFieldsRegistry extends EventEmitter {
  private plugins: Map<string, FieldPluginRegistration> = new Map();
  private fields: Map<string, FieldRegistryEntry> = new Map();
  private categories: Map<string, FieldCategory> = new Map();
  private config: FieldPluginConfig;
  private initialized: boolean = false;

  constructor(config: FieldPluginConfig = {}) {
    super();
    this.config = {
      enableHotReload: true,
      enableValidation: true,
      enablePreview: true,
      enableDragDrop: true,
      maxFieldsPerPlugin: 50,
      allowDuplicateTypes: false,
      defaultCategory: 'custom',
      ...config,
    };
  }

  /**
   * Initialize the registry
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    logger.info('Initializing custom fields registry');

    // Register default categories
    DEFAULT_CATEGORIES.forEach(category => {
      this.categories.set(category.id, category);
    });

    this.initialized = true;
    logger.info('Custom fields registry initialized');
  }

  /**
   * Register a field plugin
   */
  async registerPlugin(manifest: FieldPluginManifest): Promise<void> {
    try {
      const pluginId = manifest.id;

      // Check if plugin is already registered
      if (this.plugins.has(pluginId)) {
        throw new Error(`Field plugin ${pluginId} is already registered`);
      }

      // Validate manifest
      this.validateManifest(manifest);

      // Create plugin registration
      const registration: FieldPluginRegistration = {
        pluginId,
        manifest,
        fields: new Map(),
        enabled: false,
        loaded: false,
      };

      // Register fields
      for (const field of manifest.fields) {
        await this.registerFieldInternal(pluginId, field);
        registration.fields.set(field.id, field);
      }

      // Add to registry
      this.plugins.set(pluginId, registration);
      registration.loaded = true;

      // Emit event
      this.emit(FieldPluginEvent.PLUGIN_LOADED, { pluginId, manifest });

      logger.info(`Field plugin registered: ${pluginId} (${manifest.fields.length} fields)`);
    } catch (error) {
      logger.error(`Failed to register field plugin ${manifest.id}:`, error);
      throw error;
    }
  }

  /**
   * Unregister a field plugin
   */
  async unregisterPlugin(pluginId: string): Promise<void> {
    try {
      const registration = this.plugins.get(pluginId);
      if (!registration) {
        return;
      }

      // Unregister all fields from this plugin
      for (const fieldId of registration.fields.keys()) {
        this.unregisterField(fieldId);
      }

      // Remove from registry
      this.plugins.delete(pluginId);

      // Emit event
      this.emit(FieldPluginEvent.PLUGIN_UNLOADED, { pluginId });

      logger.info(`Field plugin unregistered: ${pluginId}`);
    } catch (error) {
      logger.error(`Failed to unregister field plugin ${pluginId}:`, error);
      throw error;
    }
  }

  /**
   * Register a custom field
   */
  registerField(pluginId: string, field: CustomFieldComponent): void {
    this.registerFieldInternal(pluginId, field);
  }

  /**
   * Unregister a custom field
   */
  unregisterField(fieldId: string): void {
    const entry = this.fields.get(fieldId);
    if (entry) {
      this.fields.delete(fieldId);
      this.emit(FieldPluginEvent.FIELD_UNREGISTERED, { fieldId, pluginId: entry.pluginId });
      logger.debug(`Field unregistered: ${fieldId}`);
    }
  }

  /**
   * Get a field by ID
   */
  getField(fieldId: string): CustomFieldComponent | undefined {
    return this.fields.get(fieldId)?.component;
  }

  /**
   * Get all fields
   */
  getAllFields(): CustomFieldComponent[] {
    return Array.from(this.fields.values()).map(entry => entry.component);
  }

  /**
   * Get fields by category
   */
  getFieldsByCategory(category: string): CustomFieldComponent[] {
    return this.getAllFields().filter(field => field.category === category);
  }

  /**
   * Get fields by plugin
   */
  getFieldsByPlugin(pluginId: string): CustomFieldComponent[] {
    return Array.from(this.fields.values())
      .filter(entry => entry.pluginId === pluginId)
      .map(entry => entry.component);
  }

  /**
   * Register a category
   */
  registerCategory(category: FieldCategory): void {
    this.categories.set(category.id, category);
    this.emit(FieldPluginEvent.CATEGORY_REGISTERED, { category });
    logger.debug(`Category registered: ${category.id}`);
  }

  /**
   * Unregister a category
   */
  unregisterCategory(categoryId: string): void {
    this.categories.delete(categoryId);
    this.emit(FieldPluginEvent.CATEGORY_UNREGISTERED, { categoryId });
    logger.debug(`Category unregistered: ${categoryId}`);
  }

  /**
   * Get all categories
   */
  getCategories(): FieldCategory[] {
    return Array.from(this.categories.values()).sort((a, b) => (a.order || 0) - (b.order || 0));
  }

  /**
   * Search fields
   */
  searchFields(criteria: FieldSearchCriteria): FieldSearchResult {
    let fields = this.getAllFields();

    // Filter by query
    if (criteria.query) {
      const query = criteria.query.toLowerCase();
      fields = fields.filter(field =>
        field.name.toLowerCase().includes(query) ||
        field.description?.toLowerCase().includes(query) ||
        field.type.toLowerCase().includes(query)
      );
    }

    // Filter by category
    if (criteria.category) {
      fields = fields.filter(field => field.category === criteria.category);
    }

    // Filter by tags
    if (criteria.tags && criteria.tags.length > 0) {
      fields = fields.filter(field =>
        field.tags?.some(tag => criteria.tags!.includes(tag))
      );
    }

    // Filter by plugin
    if (criteria.pluginId) {
      fields = fields.filter(field => {
        const entry = Array.from(this.fields.values()).find(e => e.component.id === field.id);
        return entry?.pluginId === criteria.pluginId;
      });
    }

    // Filter by type
    if (criteria.type) {
      fields = fields.filter(field => field.type === criteria.type);
    }

    return {
      fields,
      total: fields.length,
      categories: this.getCategories(),
      plugins: Array.from(this.plugins.keys()),
    };
  }

  /**
   * Validate field configuration
   */
  validateField(config: FieldConfig, value: FieldValue): FieldValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (config.validation?.required && (value === null || value === undefined || value === '')) {
      errors.push(`${config.label} is required`);
    }

    // Type-specific validation
    const field = this.getField(config.type);
    if (field && field.configSchema) {
      // Additional validation logic can be added here
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Internal method to register a field
   */
  private registerFieldInternal(pluginId: string, field: CustomFieldComponent): void {
    // Check for duplicate field IDs
    if (this.fields.has(field.id)) {
      throw new Error(`Field with ID ${field.id} is already registered`);
    }

    // Check for duplicate types if not allowed
    if (!this.config.allowDuplicateTypes) {
      const existingField = this.getAllFields().find(f => f.type === field.type);
      if (existingField) {
        throw new Error(`Field type ${field.type} is already registered`);
      }
    }

    // Set default category if not specified
    if (!field.category) {
      field.category = this.config.defaultCategory;
    }

    // Create registry entry
    const entry: FieldRegistryEntry = {
      fieldId: field.id,
      pluginId,
      component: field,
      registered: new Date(),
    };

    // Add to registry
    this.fields.set(field.id, entry);

    // Emit event
    this.emit(FieldPluginEvent.FIELD_REGISTERED, { fieldId: field.id, pluginId, field });

    logger.debug(`Field registered: ${field.id} (type: ${field.type})`);
  }

  /**
   * Validate plugin manifest
   */
  private validateManifest(manifest: FieldPluginManifest): void {
    if (!manifest.id) {
      throw new Error('Plugin manifest must have an ID');
    }

    if (!manifest.name) {
      throw new Error('Plugin manifest must have a name');
    }

    if (!manifest.version) {
      throw new Error('Plugin manifest must have a version');
    }

    if (!manifest.fields || manifest.fields.length === 0) {
      throw new Error('Plugin manifest must have at least one field');
    }

    if (this.config.maxFieldsPerPlugin && manifest.fields.length > this.config.maxFieldsPerPlugin) {
      throw new Error(`Plugin cannot have more than ${this.config.maxFieldsPerPlugin} fields`);
    }

    // Validate each field
    for (const field of manifest.fields) {
      if (!field.id) {
        throw new Error('Field must have an ID');
      }

      if (!field.type) {
        throw new Error('Field must have a type');
      }

      if (!field.name) {
        throw new Error('Field must have a name');
      }

      if (!field.component) {
        throw new Error('Field must have a component');
      }
    }
  }
}

// Global registry instance
let globalRegistry: CustomFieldsRegistry | null = null;

/**
 * Get the global custom fields registry
 */
export function getCustomFieldsRegistry(): CustomFieldsRegistry {
  if (!globalRegistry) {
    globalRegistry = new CustomFieldsRegistry();
  }
  return globalRegistry;
}

/**
 * Initialize the global registry
 */
export async function initializeCustomFieldsRegistry(config?: FieldPluginConfig): Promise<CustomFieldsRegistry> {
  const registry = getCustomFieldsRegistry();
  if (config) {
    Object.assign(registry['config'], config);
  }
  await registry.initialize();
  return registry;
}
