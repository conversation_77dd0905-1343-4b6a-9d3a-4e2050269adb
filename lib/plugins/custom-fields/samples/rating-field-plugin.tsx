/**
 * Rating Field Plugin
 * 
 * Example plugin that adds a star rating field component.
 */

'use client'

import React, { useState } from 'react';
import { Star } from 'lucide-react';
import { CustomFieldProps } from '@/lib/core/dynamic-editor-generator/custom-fields/types';
import { 
  createFieldPluginManifest, 
  createCustomFieldComponent,
  FieldPluginManifest 
} from '../index';

/**
 * Star Rating Field Component
 */
function StarRatingField({ config, value, onChange, disabled }: CustomFieldProps) {
  const [hoverRating, setHoverRating] = useState<number>(0);
  const maxRating = config.maxRating || 5;
  const currentRating = typeof value === 'number' ? value : 0;

  const handleClick = (rating: number) => {
    if (!disabled) {
      onChange(rating);
    }
  };

  const handleMouseEnter = (rating: number) => {
    if (!disabled) {
      setHoverRating(rating);
    }
  };

  const handleMouseLeave = () => {
    setHoverRating(0);
  };

  return (
    <div className="space-y-2">
      {config.label && (
        <label className="text-sm font-medium text-foreground">
          {config.label}
          {config.validation?.required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      
      <div className="flex items-center gap-1">
        {Array.from({ length: maxRating }, (_, index) => {
          const rating = index + 1;
          const isFilled = rating <= (hoverRating || currentRating);
          
          return (
            <button
              key={rating}
              type="button"
              className={`p-1 transition-colors ${
                disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:scale-110'
              }`}
              onClick={() => handleClick(rating)}
              onMouseEnter={() => handleMouseEnter(rating)}
              onMouseLeave={handleMouseLeave}
              disabled={disabled}
            >
              <Star
                className={`h-6 w-6 transition-colors ${
                  isFilled 
                    ? 'fill-yellow-400 text-yellow-400' 
                    : 'text-muted-foreground'
                }`}
              />
            </button>
          );
        })}
        
        {currentRating > 0 && (
          <span className="ml-2 text-sm text-muted-foreground">
            {currentRating} / {maxRating}
          </span>
        )}
      </div>
      
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}
    </div>
  );
}

/**
 * Rating Field Preview Component
 */
function RatingFieldPreview({ config }: { config: any }) {
  const maxRating = config.maxRating || 5;
  
  return (
    <div className="flex items-center gap-1">
      <span className="text-xs text-muted-foreground">Rating:</span>
      {Array.from({ length: maxRating }, (_, index) => (
        <Star key={index} className="h-3 w-3 text-muted-foreground" />
      ))}
    </div>
  );
}

/**
 * Emoji Rating Field Component
 */
function EmojiRatingField({ config, value, onChange, disabled }: CustomFieldProps) {
  const emojis = config.emojis || ['😞', '😐', '🙂', '😊', '🤩'];
  const currentRating = typeof value === 'number' ? value : 0;

  const handleClick = (rating: number) => {
    if (!disabled) {
      onChange(rating);
    }
  };

  return (
    <div className="space-y-2">
      {config.label && (
        <label className="text-sm font-medium text-foreground">
          {config.label}
          {config.validation?.required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      
      <div className="flex items-center gap-2">
        {emojis.map((emoji: string, index: number) => {
          const rating = index + 1;
          const isSelected = rating === currentRating;
          
          return (
            <button
              key={rating}
              type="button"
              className={`p-2 text-2xl transition-all rounded-lg ${
                disabled 
                  ? 'cursor-not-allowed opacity-50' 
                  : 'cursor-pointer hover:scale-110 hover:bg-muted'
              } ${
                isSelected ? 'bg-primary/10 ring-2 ring-primary' : ''
              }`}
              onClick={() => handleClick(rating)}
              disabled={disabled}
            >
              {emoji}
            </button>
          );
        })}
        
        {currentRating > 0 && (
          <span className="ml-2 text-sm text-muted-foreground">
            {emojis[currentRating - 1]} ({currentRating}/{emojis.length})
          </span>
        )}
      </div>
      
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}
    </div>
  );
}

/**
 * Emoji Rating Field Preview Component
 */
function EmojiRatingFieldPreview({ config }: { config: any }) {
  const emojis = config.emojis || ['😞', '😐', '🙂', '😊', '🤩'];
  
  return (
    <div className="flex items-center gap-1">
      <span className="text-xs text-muted-foreground">Emoji Rating:</span>
      {emojis.slice(0, 3).map((emoji: string, index: number) => (
        <span key={index} className="text-xs">{emoji}</span>
      ))}
      {emojis.length > 3 && <span className="text-xs">...</span>}
    </div>
  );
}

/**
 * Rating Field Plugin Manifest
 */
export const ratingFieldPluginManifest: FieldPluginManifest = createFieldPluginManifest(
  'rating-field-plugin',
  'Rating Field Plugin',
  '1.0.0',
  'Adds star and emoji rating field components',
  'AppGen Team',
  [
    createCustomFieldComponent(
      'star-rating',
      'star-rating',
      'Star Rating',
      StarRatingField,
      {
        description: 'A star-based rating field',
        icon: <Star className="h-4 w-4" />,
        category: 'advanced',
        preview: RatingFieldPreview,
        tags: ['rating', 'stars', 'feedback'],
        configSchema: [
          {
            id: 'maxRating',
            type: 'number',
            label: 'Maximum Rating',
            description: 'Maximum number of stars',
            defaultValue: 5,
            validation: { min: 1, max: 10 }
          }
        ],
        defaultConfig: {
          maxRating: 5,
          defaultValue: 0
        }
      }
    ),
    createCustomFieldComponent(
      'emoji-rating',
      'emoji-rating',
      'Emoji Rating',
      EmojiRatingField,
      {
        description: 'An emoji-based rating field',
        icon: <span className="text-sm">😊</span>,
        category: 'advanced',
        preview: EmojiRatingFieldPreview,
        tags: ['rating', 'emoji', 'feedback'],
        configSchema: [
          {
            id: 'emojis',
            type: 'multi-select',
            label: 'Emojis',
            description: 'List of emojis to use for rating',
            defaultValue: ['😞', '😐', '🙂', '😊', '🤩'],
            options: [
              { label: '😞 Sad', value: '😞' },
              { label: '😐 Neutral', value: '😐' },
              { label: '🙂 Slightly Happy', value: '🙂' },
              { label: '😊 Happy', value: '😊' },
              { label: '🤩 Excited', value: '🤩' },
              { label: '😍 Love', value: '😍' },
              { label: '🥰 Adore', value: '🥰' },
            ]
          }
        ],
        defaultConfig: {
          emojis: ['😞', '😐', '🙂', '😊', '🤩'],
          defaultValue: 0
        }
      }
    )
  ],
  {
    icon: <Star className="h-4 w-4" />,
    tags: ['rating', 'feedback', 'ui']
  }
);

/**
 * Plugin lifecycle for rating fields
 */
export const ratingFieldPluginLifecycle = {
  onLoad: async function() {
    console.log('Rating field plugin loaded');
  },

  onEnable: async function() {
    console.log('Rating field plugin enabled');
    
    // Register the fields using the plugin API
    const api = this.api;
    
    // Register the star rating field
    api.fields.registerField(ratingFieldPluginManifest.fields[0]);
    
    // Register the emoji rating field
    api.fields.registerField(ratingFieldPluginManifest.fields[1]);
    
    console.log('Rating fields registered successfully');
  },

  onDisable: async function() {
    console.log('Rating field plugin disabled');
    
    // Unregister the fields
    const api = this.api;
    api.fields.unregisterField('star-rating');
    api.fields.unregisterField('emoji-rating');
  },

  onUnload: async function() {
    console.log('Rating field plugin unloaded');
  },
};
