/**
 * Advanced Input Field Plugin
 * 
 * Example plugin that adds advanced input field components.
 */

'use client'

import React, { useState, useRef } from 'react';
import { <PERSON>, <PERSON>Off, Copy, Check, Hash, AtSign } from 'lucide-react';
import { CustomFieldProps } from '@/lib/core/dynamic-editor-generator/custom-fields/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  createFieldPluginManifest, 
  createCustomFieldComponent,
  FieldPluginManifest 
} from '../index';

/**
 * Password Field Component
 */
function PasswordField({ config, value, onChange, disabled }: CustomFieldProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [strength, setStrength] = useState(0);

  const calculateStrength = (password: string): number => {
    let score = 0;
    if (password.length >= 8) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    return score;
  };

  const handleChange = (newValue: string) => {
    onChange(newValue);
    setStrength(calculateStrength(newValue));
  };

  const getStrengthColor = (strength: number): string => {
    if (strength <= 1) return 'bg-red-500';
    if (strength <= 2) return 'bg-orange-500';
    if (strength <= 3) return 'bg-yellow-500';
    if (strength <= 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getStrengthText = (strength: number): string => {
    if (strength <= 1) return 'Very Weak';
    if (strength <= 2) return 'Weak';
    if (strength <= 3) return 'Fair';
    if (strength <= 4) return 'Good';
    return 'Strong';
  };

  return (
    <div className="space-y-2">
      {config.label && (
        <label className="text-sm font-medium text-foreground">
          {config.label}
          {config.validation?.required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <Input
          type={showPassword ? 'text' : 'password'}
          value={value || ''}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={config.placeholder}
          disabled={disabled}
          className="pr-10"
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3"
          onClick={() => setShowPassword(!showPassword)}
          disabled={disabled}
        >
          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
        </Button>
      </div>
      
      {config.showStrength && value && (
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span>Password Strength</span>
            <span className={`font-medium ${
              strength <= 2 ? 'text-red-500' : 
              strength <= 3 ? 'text-yellow-500' : 
              'text-green-500'
            }`}>
              {getStrengthText(strength)}
            </span>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all ${getStrengthColor(strength)}`}
              style={{ width: `${(strength / 5) * 100}%` }}
            />
          </div>
        </div>
      )}
      
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}
    </div>
  );
}

/**
 * Copy Field Component
 */
function CopyField({ config, value, onChange, disabled }: CustomFieldProps) {
  const [copied, setCopied] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleCopy = async () => {
    if (value) {
      try {
        await navigator.clipboard.writeText(String(value));
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        // Fallback for older browsers
        if (inputRef.current) {
          inputRef.current.select();
          document.execCommand('copy');
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        }
      }
    }
  };

  return (
    <div className="space-y-2">
      {config.label && (
        <label className="text-sm font-medium text-foreground">
          {config.label}
          {config.validation?.required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      
      <div className="flex gap-2">
        <Input
          ref={inputRef}
          type="text"
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={config.placeholder}
          disabled={disabled}
          className="flex-1"
        />
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleCopy}
          disabled={disabled || !value}
          className="px-3"
        >
          {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
        </Button>
      </div>
      
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}
    </div>
  );
}

/**
 * Hash Tag Field Component
 */
function HashTagField({ config, value, onChange, disabled }: CustomFieldProps) {
  const [inputValue, setInputValue] = useState('');
  const tags = Array.isArray(value) ? value : [];

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim().replace(/^#/, '');
    if (trimmedTag && !tags.includes(trimmedTag)) {
      onChange([...tags, trimmedTag]);
    }
    setInputValue('');
  };

  const removeTag = (tagToRemove: string) => {
    onChange(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addTag(inputValue);
    } else if (e.key === 'Backspace' && !inputValue && tags.length > 0) {
      removeTag(tags[tags.length - 1]);
    }
  };

  return (
    <div className="space-y-2">
      {config.label && (
        <label className="text-sm font-medium text-foreground">
          {config.label}
          {config.validation?.required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      
      <div className="space-y-2">
        <div className="relative">
          <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={config.placeholder || "Type and press Enter to add tags"}
            disabled={disabled}
            className="pl-10"
          />
        </div>
        
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {tags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary text-xs rounded-md"
              >
                #{tag}
                {!disabled && (
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="hover:text-destructive"
                  >
                    ×
                  </button>
                )}
              </span>
            ))}
          </div>
        )}
      </div>
      
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}
    </div>
  );
}

/**
 * Mention Field Component
 */
function MentionField({ config, value, onChange, disabled }: CustomFieldProps) {
  const [inputValue, setInputValue] = useState(value || '');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const suggestions = config.suggestions || ['@user1', '@user2', '@admin'];

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    onChange(newValue);
    
    // Show suggestions when @ is typed
    const lastAtIndex = newValue.lastIndexOf('@');
    if (lastAtIndex !== -1 && lastAtIndex === newValue.length - 1) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const insertMention = (mention: string) => {
    const lastAtIndex = inputValue.lastIndexOf('@');
    const newValue = inputValue.substring(0, lastAtIndex) + mention + ' ';
    setInputValue(newValue);
    onChange(newValue);
    setShowSuggestions(false);
  };

  return (
    <div className="space-y-2">
      {config.label && (
        <label className="text-sm font-medium text-foreground">
          {config.label}
          {config.validation?.required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <AtSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <textarea
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          placeholder={config.placeholder || "Type @ to mention someone"}
          disabled={disabled}
          className="w-full min-h-[80px] pl-10 pr-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-md resize-none"
        />
        
        {showSuggestions && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-popover border border-border rounded-md shadow-md z-10">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                type="button"
                onClick={() => insertMention(suggestion)}
                className="w-full px-3 py-2 text-left text-sm hover:bg-muted first:rounded-t-md last:rounded-b-md"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>
      
      {config.description && (
        <p className="text-xs text-muted-foreground">{config.description}</p>
      )}
    </div>
  );
}

/**
 * Advanced Input Field Plugin Manifest
 */
export const advancedInputPluginManifest: FieldPluginManifest = createFieldPluginManifest(
  'advanced-input-plugin',
  'Advanced Input Plugin',
  '1.0.0',
  'Adds advanced input field components like password, copy, hashtag, and mention fields',
  'AppGen Team',
  [
    createCustomFieldComponent(
      'password-field',
      'password',
      'Password Field',
      PasswordField,
      {
        description: 'A password input with strength indicator',
        icon: <Eye className="h-4 w-4" />,
        category: 'advanced',
        tags: ['password', 'security', 'input'],
        configSchema: [
          {
            id: 'showStrength',
            type: 'boolean',
            label: 'Show Strength Indicator',
            description: 'Display password strength indicator',
            defaultValue: true
          }
        ],
        defaultConfig: {
          showStrength: true
        }
      }
    ),
    createCustomFieldComponent(
      'copy-field',
      'copy',
      'Copy Field',
      CopyField,
      {
        description: 'A text input with copy to clipboard functionality',
        icon: <Copy className="h-4 w-4" />,
        category: 'advanced',
        tags: ['copy', 'clipboard', 'input']
      }
    ),
    createCustomFieldComponent(
      'hashtag-field',
      'hashtag',
      'Hashtag Field',
      HashTagField,
      {
        description: 'A field for entering hashtags',
        icon: <Hash className="h-4 w-4" />,
        category: 'advanced',
        tags: ['hashtag', 'tags', 'social']
      }
    ),
    createCustomFieldComponent(
      'mention-field',
      'mention',
      'Mention Field',
      MentionField,
      {
        description: 'A text area with @ mention functionality',
        icon: <AtSign className="h-4 w-4" />,
        category: 'advanced',
        tags: ['mention', 'social', 'textarea'],
        configSchema: [
          {
            id: 'suggestions',
            type: 'multi-select',
            label: 'Mention Suggestions',
            description: 'List of available mentions',
            defaultValue: ['@user1', '@user2', '@admin']
          }
        ],
        defaultConfig: {
          suggestions: ['@user1', '@user2', '@admin']
        }
      }
    )
  ],
  {
    icon: <Hash className="h-4 w-4" />,
    tags: ['input', 'advanced', 'ui']
  }
);

/**
 * Plugin lifecycle for advanced input fields
 */
export const advancedInputPluginLifecycle = {
  onLoad: async function() {
    console.log('Advanced input plugin loaded');
  },

  onEnable: async function() {
    console.log('Advanced input plugin enabled');
    
    // Register all fields using the plugin API
    const api = this.api;
    
    advancedInputPluginManifest.fields.forEach(field => {
      api.fields.registerField(field);
    });
    
    console.log('Advanced input fields registered successfully');
  },

  onDisable: async function() {
    console.log('Advanced input plugin disabled');
    
    // Unregister all fields
    const api = this.api;
    advancedInputPluginManifest.fields.forEach(field => {
      api.fields.unregisterField(field.id);
    });
  },

  onUnload: async function() {
    console.log('Advanced input plugin unloaded');
  },
};
