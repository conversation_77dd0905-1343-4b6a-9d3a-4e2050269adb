/**
 * Custom Fields Plugin System
 * 
 * Main entry point for the custom fields plugin system.
 */

// Export types
export type {
  CustomFieldComponent,
  FieldPluginManifest,
  FieldPluginRegistration,
  FieldRegistryEntry,
  FieldCategory,
  FieldValidationResult,
  FieldRendererOptions,
  FieldPluginAPI,
  FieldPluginConfig,
  FieldSearchCriteria,
  FieldSearchResult,
} from './types';

export { FieldPluginEvent } from './types';

// Export registry
export {
  CustomFieldsRegistry,
  getCustomFieldsRegistry,
  initializeCustomFieldsRegistry,
} from './registry';

// Export API
export {
  createFieldPluginAPI,
  GlobalFieldPluginAPI,
  getGlobalFieldPluginAPI,
  FieldUtils,
} from './api';

// Export renderer
export {
  PluginFieldRenderer,
  createFieldRenderer,
  useFieldRenderer,
  EnhancedCustomFieldRenderer,
  FieldRendererProvider,
  useFieldRendererContext,
} from './renderer';

// Re-export core field types for convenience
export type {
  FieldConfig,
  FieldValue,
  CustomFieldProps,
  FieldValidation,
  FieldSchema,
  FieldGroupConfig,
  FieldSectionConfig,
} from '@/lib/core/dynamic-editor-generator/custom-fields/types';

/**
 * Initialize the custom fields plugin system
 */
export async function initializeCustomFieldsSystem(config?: any) {
  const registry = await initializeCustomFieldsRegistry(config);
  
  // Register built-in field categories if needed
  const categories = registry.getCategories();
  if (categories.length === 0) {
    // Categories are already registered in the registry constructor
  }
  
  return registry;
}

/**
 * Utility function to create a field plugin manifest
 */
export function createFieldPluginManifest(
  id: string,
  name: string,
  version: string,
  description: string,
  author: string,
  fields: any[],
  options: {
    dependencies?: string[];
    icon?: any;
    tags?: string[];
  } = {}
): any {
  return {
    id,
    name,
    version,
    description,
    author,
    fields,
    dependencies: options.dependencies || [],
    icon: options.icon,
    tags: options.tags || [],
  };
}

/**
 * Utility function to create a custom field component
 */
export function createCustomFieldComponent(
  id: string,
  type: string,
  name: string,
  component: any,
  options: {
    description?: string;
    icon?: any;
    category?: string;
    configSchema?: any[];
    defaultConfig?: any;
    preview?: any;
    tags?: string[];
  } = {}
): any {
  return {
    id,
    type,
    name,
    component,
    description: options.description,
    icon: options.icon,
    category: options.category || 'custom',
    configSchema: options.configSchema,
    defaultConfig: options.defaultConfig,
    preview: options.preview,
    tags: options.tags || [],
  };
}

/**
 * Utility function to create a field category
 */
export function createFieldCategory(
  id: string,
  name: string,
  options: {
    description?: string;
    icon?: any;
    order?: number;
  } = {}
): any {
  return {
    id,
    name,
    description: options.description,
    icon: options.icon,
    order: options.order,
  };
}

/**
 * Default field plugin configuration
 */
export const DEFAULT_FIELD_PLUGIN_CONFIG = {
  enableHotReload: true,
  enableValidation: true,
  enablePreview: true,
  enableDragDrop: true,
  maxFieldsPerPlugin: 50,
  allowDuplicateTypes: false,
  defaultCategory: 'custom',
};

/**
 * Built-in field categories
 */
export const BUILT_IN_CATEGORIES = [
  { id: 'basic', name: 'Basic', description: 'Basic input fields', order: 1 },
  { id: 'advanced', name: 'Advanced', description: 'Advanced input fields', order: 2 },
  { id: 'layout', name: 'Layout', description: 'Layout and structure fields', order: 3 },
  { id: 'media', name: 'Media', description: 'Media and file fields', order: 4 },
  { id: 'data', name: 'Data', description: 'Data and API fields', order: 5 },
  { id: 'custom', name: 'Custom', description: 'Custom plugin fields', order: 6 },
];

/**
 * Field type validation utilities
 */
export const FieldTypeValidation = {
  /**
   * Validate field type name
   */
  isValidFieldType: (type: string): boolean => {
    return /^[a-z][a-z0-9-]*$/.test(type);
  },

  /**
   * Validate field ID
   */
  isValidFieldId: (id: string): boolean => {
    return /^[a-zA-Z][a-zA-Z0-9_-]*$/.test(id);
  },

  /**
   * Validate category ID
   */
  isValidCategoryId: (id: string): boolean => {
    return /^[a-z][a-z0-9-]*$/.test(id);
  },

  /**
   * Get field type suggestions
   */
  getFieldTypeSuggestions: (input: string): string[] => {
    const commonTypes = [
      'text', 'number', 'boolean', 'select', 'multi-select',
      'color', 'range', 'textarea', 'richtext', 'code',
      'image', 'file', 'date', 'time', 'datetime',
      'icon', 'font', 'spacing', 'border', 'shadow',
      'gradient', 'animation', 'link', 'media',
      'repeater', 'object', 'conditional', 'tabs', 'accordion'
    ];

    return commonTypes.filter(type => 
      type.toLowerCase().includes(input.toLowerCase())
    );
  },
};

/**
 * Field plugin development utilities
 */
export const FieldPluginDev = {
  /**
   * Create a development field plugin
   */
  createDevPlugin: (
    id: string,
    fields: any[]
  ) => {
    return createFieldPluginManifest(
      id,
      `${id} Development Plugin`,
      '0.1.0',
      'Development plugin for testing custom fields',
      'Developer',
      fields,
      {
        tags: ['development', 'testing'],
      }
    );
  },

  /**
   * Create a test field component
   */
  createTestField: (
    type: string,
    component: any
  ) => {
    return createCustomFieldComponent(
      `test-${type}`,
      type,
      `Test ${type} Field`,
      component,
      {
        description: `Test field for ${type} type`,
        category: 'custom',
        tags: ['test', 'development'],
      }
    );
  },

  /**
   * Validate field plugin manifest
   */
  validateManifest: (manifest: any): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!manifest.id) errors.push('Manifest must have an id');
    if (!manifest.name) errors.push('Manifest must have a name');
    if (!manifest.version) errors.push('Manifest must have a version');
    if (!manifest.fields || !Array.isArray(manifest.fields)) {
      errors.push('Manifest must have a fields array');
    }

    if (manifest.fields) {
      manifest.fields.forEach((field: any, index: number) => {
        if (!field.id) errors.push(`Field ${index} must have an id`);
        if (!field.type) errors.push(`Field ${index} must have a type`);
        if (!field.name) errors.push(`Field ${index} must have a name`);
        if (!field.component) errors.push(`Field ${index} must have a component`);
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
};
