/**
 * Custom Fields Plugin System Demo
 * 
 * Demonstrates the custom fields plugin system with sample plugins.
 */

'use client'

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Star, 
  Hash, 
  Eye, 
  Copy, 
  AtSign, 
  Puzzle, 
  Settings, 
  Play, 
  Square,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

import { 
  getCustomFieldsRegistry, 
  initializeCustomFieldsRegistry,
  createFieldPluginAPI,
  FieldUtils
} from './index';
import { CustomFieldRenderer } from '@/lib/core/dynamic-editor-generator/custom-fields/field-renderer';
import { FieldConfig, FieldValue } from '@/lib/core/dynamic-editor-generator/custom-fields/types';

// Import sample plugins
import { ratingFieldPluginManifest, ratingFieldPluginLifecycle } from './samples/rating-field-plugin';
import { advancedInputPluginManifest, advancedInputPluginLifecycle } from './samples/advanced-input-plugin';

interface PluginStatus {
  id: string;
  name: string;
  loaded: boolean;
  enabled: boolean;
  fieldCount: number;
  error?: string;
}

export function CustomFieldsPluginDemo() {
  const [registry, setRegistry] = useState<any>(null);
  const [plugins, setPlugins] = useState<PluginStatus[]>([]);
  const [availableFields, setAvailableFields] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [demoValues, setDemoValues] = useState<Record<string, FieldValue>>({});
  const [initialized, setInitialized] = useState(false);

  // Initialize the system
  useEffect(() => {
    const initSystem = async () => {
      try {
        const reg = await initializeCustomFieldsRegistry();
        setRegistry(reg);
        setCategories(reg.getCategories());
        setInitialized(true);
      } catch (error) {
        console.error('Failed to initialize custom fields system:', error);
      }
    };

    initSystem();
  }, []);

  // Load sample plugins
  const loadSamplePlugins = async () => {
    if (!registry) return;

    try {
      // Register rating field plugin
      await registry.registerPlugin(ratingFieldPluginManifest);
      
      // Register advanced input plugin
      await registry.registerPlugin(advancedInputPluginManifest);

      // Update state
      updatePluginsList();
      updateFieldsList();
    } catch (error) {
      console.error('Failed to load sample plugins:', error);
    }
  };

  // Update plugins list
  const updatePluginsList = () => {
    if (!registry) return;

    const pluginsList: PluginStatus[] = [
      {
        id: 'rating-field-plugin',
        name: 'Rating Field Plugin',
        loaded: true,
        enabled: true,
        fieldCount: ratingFieldPluginManifest.fields.length,
      },
      {
        id: 'advanced-input-plugin',
        name: 'Advanced Input Plugin',
        loaded: true,
        enabled: true,
        fieldCount: advancedInputPluginManifest.fields.length,
      }
    ];

    setPlugins(pluginsList);
  };

  // Update fields list
  const updateFieldsList = () => {
    if (!registry) return;

    const fields = registry.getAllFields();
    setAvailableFields(fields);
  };

  // Handle field value change
  const handleFieldChange = (fieldId: string, value: FieldValue) => {
    setDemoValues(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  // Create demo field configurations
  const createDemoFields = (): FieldConfig[] => {
    const fields: FieldConfig[] = [];

    // Add plugin fields if available
    availableFields.forEach(field => {
      const config: FieldConfig = {
        id: `demo_${field.type}`,
        type: field.type,
        label: field.name,
        description: field.description,
        ...field.defaultConfig,
      };

      fields.push(config);
    });

    return fields;
  };

  if (!initialized) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Settings className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p>Initializing Custom Fields Plugin System...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Puzzle className="h-5 w-5" />
            Custom Fields Plugin System Demo
          </CardTitle>
          <CardDescription>
            Demonstrates the extensible custom fields plugin architecture with sample plugins.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button onClick={loadSamplePlugins} className="flex items-center gap-2">
              <Play className="h-4 w-4" />
              Load Sample Plugins
            </Button>
            <Button variant="outline" onClick={updateFieldsList}>
              Refresh Fields
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="plugins" className="space-y-4">
        <TabsList>
          <TabsTrigger value="plugins">Plugins</TabsTrigger>
          <TabsTrigger value="fields">Available Fields</TabsTrigger>
          <TabsTrigger value="demo">Field Demo</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
        </TabsList>

        <TabsContent value="plugins" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Loaded Plugins</CardTitle>
              <CardDescription>
                Plugins that have been registered with the custom fields system.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {plugins.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Square className="h-8 w-8 mx-auto mb-2" />
                  <p>No plugins loaded. Click "Load Sample Plugins" to get started.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {plugins.map(plugin => (
                    <div key={plugin.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {plugin.enabled ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-yellow-500" />
                        )}
                        <div>
                          <h4 className="font-medium">{plugin.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {plugin.fieldCount} field{plugin.fieldCount !== 1 ? 's' : ''}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={plugin.enabled ? 'default' : 'secondary'}>
                          {plugin.enabled ? 'Enabled' : 'Disabled'}
                        </Badge>
                        <Badge variant="outline">
                          {plugin.loaded ? 'Loaded' : 'Not Loaded'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fields" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Available Field Types</CardTitle>
              <CardDescription>
                Custom field components registered by plugins.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {availableFields.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Square className="h-8 w-8 mx-auto mb-2" />
                  <p>No custom fields available. Load plugins to see custom fields.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availableFields.map(field => (
                    <div key={field.id} className="border rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        {field.icon}
                        <h4 className="font-medium">{field.name}</h4>
                        <Badge variant="outline" className="text-xs">
                          {field.type}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {field.description}
                      </p>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="secondary" className="text-xs">
                          {field.category}
                        </Badge>
                        {field.tags?.map((tag: string) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="demo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Field Demo</CardTitle>
              <CardDescription>
                Interactive demonstration of custom field components.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px]">
                <div className="space-y-6">
                  {createDemoFields().map(config => (
                    <div key={config.id} className="border rounded-lg p-4">
                      <div className="mb-3">
                        <h4 className="font-medium flex items-center gap-2">
                          {config.label}
                          <Badge variant="outline" className="text-xs">
                            {config.type}
                          </Badge>
                        </h4>
                        {config.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {config.description}
                          </p>
                        )}
                      </div>
                      
                      <CustomFieldRenderer
                        config={config}
                        value={demoValues[config.id]}
                        onChange={(value) => handleFieldChange(config.id, value)}
                      />
                      
                      {demoValues[config.id] !== undefined && (
                        <div className="mt-3 p-2 bg-muted rounded text-xs">
                          <strong>Value:</strong> {JSON.stringify(demoValues[config.id])}
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {availableFields.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Square className="h-8 w-8 mx-auto mb-2" />
                      <p>Load plugins to see field demos.</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Field Categories</CardTitle>
              <CardDescription>
                Categories used to organize custom fields.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {categories.map(category => (
                  <div key={category.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {category.icon}
                      <div>
                        <h4 className="font-medium">{category.name}</h4>
                        {category.description && (
                          <p className="text-sm text-muted-foreground">
                            {category.description}
                          </p>
                        )}
                      </div>
                    </div>
                    <Badge variant="outline">
                      {availableFields.filter(f => f.category === category.id).length} fields
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
