# Custom Fields Plugin System

A production-ready, extensible plugin system for custom field components that integrates seamlessly with the existing dynamic editor generator and plugin architecture.

## Overview

The Custom Fields Plugin System allows developers to create and register custom field components that can be used throughout the application for various content creation and editing scenarios. It provides a robust infrastructure for field registration, management, rendering, and validation.

## Features

- **Plugin-based Architecture**: Register custom field components through plugins
- **Type Safety**: Full TypeScript support with comprehensive type definitions
- **Dynamic Rendering**: Automatic field rendering with fallback support
- **Validation System**: Built-in and custom validation support
- **Category Management**: Organize fields into logical categories
- **Hot Reload**: Development-friendly hot reload support
- **Event System**: Comprehensive event system for field lifecycle management
- **Integration**: Seamless integration with existing dynamic editor generator

## Architecture

### Core Components

1. **Registry** (`registry.ts`): Central registry for managing field plugins and components
2. **API** (`api.ts`): Plugin API for field registration and management
3. **Renderer** (`renderer.tsx`): Enhanced field renderer with plugin support
4. **Types** (`types.ts`): Comprehensive type definitions
5. **Samples** (`samples/`): Example plugins demonstrating the system

### Plugin Integration

The system extends the existing plugin API with custom fields functionality:

```typescript
// Plugin API now includes fields namespace
api.fields.registerField(fieldComponent);
api.fields.getField(fieldId);
api.fields.renderField(config, value, onChange);
```

## Quick Start

### 1. Initialize the System

```typescript
import { initializeCustomFieldsSystem } from '@/lib/plugins/custom-fields';

// Initialize with default configuration
await initializeCustomFieldsSystem();

// Or with custom configuration
await initializeCustomFieldsSystem({
  enableHotReload: true,
  enableValidation: true,
  maxFieldsPerPlugin: 50
});
```

### 2. Create a Custom Field Component

```typescript
import React from 'react';
import { CustomFieldProps } from '@/lib/core/dynamic-editor-generator/custom-fields/types';

function MyCustomField({ config, value, onChange, disabled }: CustomFieldProps) {
  return (
    <div>
      <label>{config.label}</label>
      <input
        type="text"
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        placeholder={config.placeholder}
      />
    </div>
  );
}
```

### 3. Create a Field Plugin

```typescript
import { createFieldPluginManifest, createCustomFieldComponent } from '@/lib/plugins/custom-fields';

export const myFieldPlugin = createFieldPluginManifest(
  'my-field-plugin',
  'My Field Plugin',
  '1.0.0',
  'Custom field plugin example',
  'Your Name',
  [
    createCustomFieldComponent(
      'my-custom-field',
      'my-custom',
      'My Custom Field',
      MyCustomField,
      {
        description: 'A custom field component',
        category: 'custom',
        tags: ['example', 'custom']
      }
    )
  ]
);
```

### 4. Register the Plugin

```typescript
// In your plugin lifecycle
export const lifecycle = {
  onEnable: async function() {
    const api = this.api;
    
    // Register the field
    api.fields.registerField(myFieldPlugin.fields[0]);
  },
  
  onDisable: async function() {
    const api = this.api;
    
    // Unregister the field
    api.fields.unregisterField('my-custom-field');
  }
};
```

### 5. Use the Field

```typescript
import { CustomFieldRenderer } from '@/lib/core/dynamic-editor-generator/custom-fields/field-renderer';

function MyForm() {
  const [value, setValue] = useState('');
  
  const fieldConfig = {
    id: 'my-field',
    type: 'my-custom',
    label: 'My Custom Field',
    placeholder: 'Enter value...'
  };
  
  return (
    <CustomFieldRenderer
      config={fieldConfig}
      value={value}
      onChange={setValue}
    />
  );
}
```

## Advanced Usage

### Custom Validation

```typescript
const fieldWithValidation = createCustomFieldComponent(
  'validated-field',
  'validated',
  'Validated Field',
  ValidatedFieldComponent,
  {
    configSchema: [
      {
        id: 'minLength',
        type: 'number',
        label: 'Minimum Length',
        defaultValue: 3
      }
    ],
    defaultConfig: {
      validation: {
        required: true,
        minLength: 3,
        custom: (value) => {
          if (typeof value === 'string' && value.includes('test')) {
            return 'Value cannot contain "test"';
          }
          return null;
        }
      }
    }
  }
);
```

### Field Categories

```typescript
// Register a custom category
api.fields.registerCategory({
  id: 'social',
  name: 'Social Media',
  description: 'Social media related fields',
  icon: <Share className="h-4 w-4" />,
  order: 10
});
```

### Field Search and Discovery

```typescript
import { getGlobalFieldPluginAPI } from '@/lib/plugins/custom-fields';

const api = getGlobalFieldPluginAPI();

// Search for fields
const results = api.searchFields({
  query: 'rating',
  category: 'advanced',
  tags: ['feedback']
});

// Get fields by category
const socialFields = api.getFieldsByCategory('social');
```

## Sample Plugins

The system includes several sample plugins to demonstrate capabilities:

### Rating Field Plugin
- **Star Rating**: Interactive star-based rating component
- **Emoji Rating**: Emoji-based rating with customizable emojis

### Advanced Input Plugin
- **Password Field**: Password input with strength indicator
- **Copy Field**: Text input with copy-to-clipboard functionality
- **Hashtag Field**: Tag input with hashtag formatting
- **Mention Field**: Text area with @ mention suggestions

## API Reference

### Field Plugin API

```typescript
interface FieldPluginAPI {
  // Field registration
  registerField(field: CustomFieldComponent): void;
  unregisterField(fieldId: string): void;
  
  // Field retrieval
  getField(fieldId: string): CustomFieldComponent | undefined;
  getAllFields(): CustomFieldComponent[];
  getFieldsByCategory(category: string): CustomFieldComponent[];
  
  // Category management
  registerCategory(category: FieldCategory): void;
  getCategories(): FieldCategory[];
  
  // Field rendering and validation
  renderField(config: FieldConfig, value: FieldValue, onChange: Function): ReactNode;
  validateField(config: FieldConfig, value: FieldValue): FieldValidationResult;
}
```

### Field Component Interface

```typescript
interface CustomFieldComponent {
  id: string;
  type: string;
  name: string;
  component: React.ComponentType<CustomFieldProps>;
  description?: string;
  icon?: ReactNode;
  category?: string;
  configSchema?: FieldConfig[];
  defaultConfig?: Partial<FieldConfig>;
  preview?: React.ComponentType<{ config: FieldConfig }>;
  tags?: string[];
}
```

## Integration with Existing Systems

### Dynamic Editor Generator

The system seamlessly integrates with the existing dynamic editor generator:

```typescript
// The existing CustomFieldRenderer now supports plugin fields
import { CustomFieldRenderer } from '@/lib/core/dynamic-editor-generator/custom-fields/field-renderer';

// Plugin fields are automatically available
<CustomFieldRenderer
  config={{ type: 'star-rating', label: 'Rate this' }}
  value={rating}
  onChange={setRating}
/>
```

### Block-based Editors

Plugin fields can be used in block-based editors and PDF generation systems:

```typescript
// In block configuration
const blockConfig = {
  type: 'form-block',
  fields: [
    { type: 'star-rating', label: 'Rating' },
    { type: 'hashtag', label: 'Tags' }
  ]
};
```

## Development

### Running the Demo

```typescript
import { CustomFieldsPluginDemo } from '@/lib/plugins/custom-fields/demo';

function App() {
  return <CustomFieldsPluginDemo />;
}
```

### Creating New Field Types

1. Create the field component implementing `CustomFieldProps`
2. Define the field configuration with `createCustomFieldComponent`
3. Create a plugin manifest with `createFieldPluginManifest`
4. Implement plugin lifecycle hooks
5. Register the plugin with the system

### Best Practices

- Use descriptive field IDs and types
- Provide comprehensive validation
- Include preview components for better UX
- Use appropriate categories and tags
- Handle edge cases and error states
- Follow accessibility guidelines
- Provide clear documentation

## Events

The system emits various events for monitoring and debugging:

```typescript
api.on('field:registered', ({ fieldId, pluginId, field }) => {
  console.log(`Field ${fieldId} registered by ${pluginId}`);
});

api.on('field:rendered', ({ fieldId, config, value }) => {
  console.log(`Field ${fieldId} rendered with value:`, value);
});
```

## Configuration

```typescript
interface FieldPluginConfig {
  enableHotReload?: boolean;      // Enable hot reload in development
  enableValidation?: boolean;     // Enable field validation
  enablePreview?: boolean;        // Enable field previews
  enableDragDrop?: boolean;       // Enable drag and drop
  maxFieldsPerPlugin?: number;    // Maximum fields per plugin
  allowDuplicateTypes?: boolean;  // Allow duplicate field types
  defaultCategory?: string;       // Default category for fields
}
```

## Contributing

When contributing new field types or improvements:

1. Follow the existing patterns and conventions
2. Include comprehensive tests
3. Update documentation
4. Provide sample usage
5. Consider accessibility and performance
6. Test with the demo component

## License

This custom fields plugin system is part of the AppGen project and follows the same licensing terms.
