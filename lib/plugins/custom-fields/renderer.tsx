/**
 * Custom Fields Renderer
 * 
 * Enhanced field renderer that works with the plugin system.
 */

'use client'

import React, { ReactNode } from 'react';
import { FieldConfig, FieldValue, CustomFieldProps } from '@/lib/core/dynamic-editor-generator/custom-fields/types';
import { CustomFieldsRegistry } from './registry';
import { FieldRendererOptions, FieldValidationResult } from './types';
import { createNamespacedLogger } from '@/lib/logger';

// Import fallback field components
import { TextField } from '@/lib/core/dynamic-editor-generator/custom-fields/fields/text-field';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

// Create a logger for this module
const logger = createNamespacedLogger('custom-fields-renderer');

/**
 * Enhanced field renderer that supports plugin fields
 */
export class PluginFieldRenderer {
  private registry: CustomFieldsRegistry;

  constructor(registry: CustomFieldsRegistry) {
    this.registry = registry;
  }

  /**
   * Render a field using the plugin system
   */
  render(
    config: FieldConfig,
    value: FieldValue,
    onChange: (value: FieldValue) => void,
    options: FieldRendererOptions = {}
  ): ReactNode {
    try {
      // Get the field component from registry
      const fieldComponent = this.registry.getField(config.type);

      if (!fieldComponent) {
        logger.warn(`Unknown field type: ${config.type}. Falling back to text field.`);
        return this.renderFallback(config, value, onChange, options);
      }

      // Prepare field props
      const fieldProps: CustomFieldProps = {
        config,
        value,
        onChange,
        onValidate: options.enableValidation ? this.createValidationHandler(config, options) : undefined,
        disabled: config.disabled,
        className: config.className,
      };

      // Render the field component
      const FieldComponent = fieldComponent.component;
      
      return (
        <div key={config.id} className="field-wrapper">
          {options.enablePreview && this.renderPreview(fieldComponent, config)}
          <FieldComponent {...fieldProps} />
          {options.enableValidation && this.renderValidationErrors(config, value)}
        </div>
      );
    } catch (error) {
      logger.error(`Failed to render field ${config.type}:`, error);
      return this.renderError(config, error);
    }
  }

  /**
   * Render multiple fields
   */
  renderFields(
    configs: FieldConfig[],
    values: Record<string, FieldValue>,
    onChange: (fieldId: string, value: FieldValue) => void,
    options: FieldRendererOptions = {}
  ): ReactNode[] {
    return configs.map(config => {
      const fieldValue = values[config.id];
      const fieldOnChange = (value: FieldValue) => onChange(config.id, value);

      return this.render(config, fieldValue, fieldOnChange, options);
    });
  }

  /**
   * Render field with validation
   */
  renderWithValidation(
    config: FieldConfig,
    value: FieldValue,
    onChange: (value: FieldValue) => void,
    onValidate?: (isValid: boolean, errors: string[]) => void
  ): ReactNode {
    const options: FieldRendererOptions = {
      enableValidation: true,
      enablePreview: false,
    };

    // Custom validation handler
    const validationHandler = (isValid: boolean, message?: string) => {
      const errors = message ? [message] : [];
      onValidate?.(isValid, errors);
    };

    const fieldProps: CustomFieldProps = {
      config,
      value,
      onChange,
      onValidate: validationHandler,
      disabled: config.disabled,
      className: config.className,
    };

    const fieldComponent = this.registry.getField(config.type);
    if (!fieldComponent) {
      return this.renderFallback(config, value, onChange, options);
    }

    const FieldComponent = fieldComponent.component;
    
    return (
      <div className="field-wrapper">
        <FieldComponent {...fieldProps} />
        {this.renderValidationErrors(config, value)}
      </div>
    );
  }

  /**
   * Create validation handler
   */
  private createValidationHandler(
    config: FieldConfig,
    options: FieldRendererOptions
  ) {
    return (isValid: boolean, message?: string) => {
      if (!isValid && message) {
        logger.debug(`Validation failed for field ${config.id}: ${message}`);
      }

      // Custom validators
      if (options.customValidators && options.customValidators[config.type]) {
        const customResult = options.customValidators[config.type](config.defaultValue);
        if (!customResult.isValid) {
          logger.debug(`Custom validation failed for field ${config.id}:`, customResult.errors);
        }
      }
    };
  }

  /**
   * Render field preview
   */
  private renderPreview(fieldComponent: any, config: FieldConfig): ReactNode {
    if (!fieldComponent.preview) {
      return null;
    }

    const PreviewComponent = fieldComponent.preview;
    
    return (
      <div className="field-preview mb-2 p-2 bg-muted rounded">
        <PreviewComponent config={config} />
      </div>
    );
  }

  /**
   * Render validation errors
   */
  private renderValidationErrors(config: FieldConfig, value: FieldValue): ReactNode {
    const validation = this.registry.validateField(config, value);
    
    if (validation.isValid) {
      return null;
    }

    return (
      <div className="field-errors mt-1">
        {validation.errors.map((error, index) => (
          <Alert key={index} variant="destructive" className="mt-1">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ))}
        {validation.warnings.map((warning, index) => (
          <Alert key={`warning-${index}`} className="mt-1">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{warning}</AlertDescription>
          </Alert>
        ))}
      </div>
    );
  }

  /**
   * Render fallback field
   */
  private renderFallback(
    config: FieldConfig,
    value: FieldValue,
    onChange: (value: FieldValue) => void,
    options: FieldRendererOptions
  ): ReactNode {
    const fieldProps: CustomFieldProps = {
      config: {
        ...config,
        type: 'text', // Force text type for fallback
      },
      value,
      onChange,
      disabled: config.disabled,
      className: config.className,
    };

    return (
      <div className="field-wrapper">
        <Alert className="mb-2">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Unknown field type "{config.type}". Using text field as fallback.
          </AlertDescription>
        </Alert>
        <TextField {...fieldProps} />
      </div>
    );
  }

  /**
   * Render error state
   */
  private renderError(config: FieldConfig, error: any): ReactNode {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to render field "{config.label || config.id}": {error.message}
        </AlertDescription>
      </Alert>
    );
  }
}

/**
 * Create a field renderer instance
 */
export function createFieldRenderer(registry: CustomFieldsRegistry): PluginFieldRenderer {
  return new PluginFieldRenderer(registry);
}

/**
 * React hook for using the field renderer
 */
export function useFieldRenderer() {
  const [registry] = React.useState(() => {
    // This would typically come from a context or global state
    const { getCustomFieldsRegistry } = require('./registry');
    return getCustomFieldsRegistry();
  });

  const renderer = React.useMemo(() => createFieldRenderer(registry), [registry]);

  return {
    renderField: renderer.render.bind(renderer),
    renderFields: renderer.renderFields.bind(renderer),
    renderWithValidation: renderer.renderWithValidation.bind(renderer),
    registry,
  };
}

/**
 * Enhanced CustomFieldRenderer component that uses the plugin system
 */
interface EnhancedCustomFieldRendererProps {
  config: FieldConfig;
  value: FieldValue;
  onChange: (value: FieldValue) => void;
  onValidate?: (isValid: boolean, errors: string[]) => void;
  options?: FieldRendererOptions;
  className?: string;
}

export function EnhancedCustomFieldRenderer({
  config,
  value,
  onChange,
  onValidate,
  options = {},
  className,
}: EnhancedCustomFieldRendererProps) {
  const { renderWithValidation } = useFieldRenderer();

  return (
    <div className={className}>
      {renderWithValidation(config, value, onChange, onValidate)}
    </div>
  );
}

/**
 * Field renderer context for React components
 */
const FieldRendererContext = React.createContext<PluginFieldRenderer | null>(null);

export function FieldRendererProvider({ 
  children, 
  registry 
}: { 
  children: React.ReactNode;
  registry?: CustomFieldsRegistry;
}) {
  const renderer = React.useMemo(() => {
    const reg = registry || require('./registry').getCustomFieldsRegistry();
    return createFieldRenderer(reg);
  }, [registry]);

  return (
    <FieldRendererContext.Provider value={renderer}>
      {children}
    </FieldRendererContext.Provider>
  );
}

export function useFieldRendererContext(): PluginFieldRenderer {
  const renderer = React.useContext(FieldRendererContext);
  if (!renderer) {
    throw new Error('useFieldRendererContext must be used within a FieldRendererProvider');
  }
  return renderer;
}
