/**
 * Proxmox API Library
 *
 * This library provides a comprehensive interface for interacting with Proxmox VE
 * through its REST API. It includes modules for authentication, VM provisioning,
 * cluster management, storage management, network configuration, and more.
 */

// Export types
export * from './types';

// Export authentication module
export * from './auth';

// Export API client
export * from './api/proxmox-api';
export * from './api/proxmox-client';

// Export VM management
export * from './vm/vm-manager';
export * from './vm/vm-operations';

// Export cluster management
export * from './cluster/cluster-manager';

// Export storage management
export * from './storage/storage-manager';

// Export network management
export * from './network/network-manager';

// Export backup and snapshot management
export * from './backup/backup-manager';

// Export task monitoring
export * from './tasks/task-manager';

// Export utilities
export * from './utils/logger';
export * from './utils/error-handler';
