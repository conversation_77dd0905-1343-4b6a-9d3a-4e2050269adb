/**
 * Proxmox API Types
 *
 * This file defines the TypeScript interfaces and types used throughout the Proxmox API library.
 */

/**
 * Proxmox authentication credentials
 */
export interface ProxmoxCredentials {
  /**
   * Username for authentication
   */
  username: string;

  /**
   * Password for authentication
   */
  password?: string;

  /**
   * API token for authentication
   */
  token?: string;

  /**
   * API token ID for authentication
   */
  tokenId?: string;

  /**
   * Authentication realm (default: 'pam')
   */
  realm?: string;
}

/**
 * Proxmox API response
 */
export interface ProxmoxApiResponse<T> {
  /**
   * Success status
   */
  success: boolean;

  /**
   * Response data
   */
  data?: T;

  /**
   * Error message if success is false
   */
  message?: string;
}

/**
 * Proxmox node status
 */
export enum NodeStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  UNKNOWN = 'unknown'
}

/**
 * Proxmox node information
 */
export interface NodeInfo {
  /**
   * Node ID
   */
  id: string;

  /**
   * Node name
   */
  name: string;

  /**
   * Node status
   */
  status: NodeStatus;

  /**
   * Node IP address
   */
  ip?: string;

  /**
   * CPU information
   */
  cpu?: {
    /**
     * CPU model
     */
    model: string;

    /**
     * CPU cores
     */
    cores: number;

    /**
     * CPU usage (0-1)
     */
    usage: number;
  };

  /**
   * Memory information
   */
  memory?: {
    /**
     * Total memory in bytes
     */
    total: number;

    /**
     * Used memory in bytes
     */
    used: number;

    /**
     * Free memory in bytes
     */
    free: number;
  };

  /**
   * Uptime in seconds
   */
  uptime?: number;
}

/**
 * VM status
 */
export enum VMStatus {
  RUNNING = 'running',
  STOPPED = 'stopped',
  PAUSED = 'paused',
  SUSPENDED = 'suspended',
  UNKNOWN = 'unknown'
}

/**
 * VM configuration
 */
export interface VMConfig {
  /**
   * VM name
   */
  name: string;

  /**
   * VM description
   */
  description?: string;

  /**
   * VM template ID
   */
  template?: string;

  /**
   * CPU configuration
   */
  cpu?: {
    /**
     * Number of CPU cores
     */
    cores: number;

    /**
     * CPU type
     */
    type?: string;
  };

  /**
   * Memory configuration in MB
   */
  memory?: number;

  /**
   * Disk configuration
   */
  disks?: Array<{
    /**
     * Disk size in GB
     */
    size: number;

    /**
     * Storage pool
     */
    storage: string;

    /**
     * Disk format
     */
    format?: 'raw' | 'qcow2' | 'vmdk';
  }>;

  /**
   * Network configuration
   */
  networks?: Array<{
    /**
     * Network model
     */
    model: string;

    /**
     * Bridge name
     */
    bridge?: string;

    /**
     * VLAN tag
     */
    vlan?: number;
  }>;

  /**
   * Start on boot
   */
  onBoot?: boolean;
}

/**
 * VM information
 */
export interface VMInfo {
  /**
   * VM ID
   */
  id: string;

  /**
   * VM name
   */
  name: string;

  /**
   * VM status
   */
  status: VMStatus;

  /**
   * Node name
   */
  node: string;

  /**
   * VM type (qemu, lxc, etc.)
   */
  type: string;

  /**
   * CPU usage (0-1)
   */
  cpuUsage?: number;

  /**
   * Memory usage in bytes
   */
  memoryUsage?: number;

  /**
   * Disk usage in bytes
   */
  diskUsage?: number;

  /**
   * Uptime in seconds
   */
  uptime?: number;

  /**
   * VM configuration
   */
  config?: VMConfig;
}

/**
 * Storage type
 */
export enum StorageType {
  DIR = 'dir',
  LVM = 'lvm',
  ZFS = 'zfs',
  CIFS = 'cifs',
  NFS = 'nfs',
  ISCSI = 'iscsi',
  GLUSTERFS = 'glusterfs'
}

/**
 * Storage information
 */
export interface StorageInfo {
  /**
   * Storage ID
   */
  id: string;

  /**
   * Storage name
   */
  name: string;

  /**
   * Storage type
   */
  type: StorageType;

  /**
   * Storage path
   */
  path?: string;

  /**
   * Total storage in bytes
   */
  total?: number;

  /**
   * Used storage in bytes
   */
  used?: number;

  /**
   * Available storage in bytes
   */
  available?: number;
}

/**
 * Task status
 */
export enum TaskStatus {
  RUNNING = 'running',
  STOPPED = 'stopped',
  COMPLETED = 'completed',
  FAILED = 'failed',
  UNKNOWN = 'unknown'
}

/**
 * Task information
 */
export interface TaskInfo {
  /**
   * Task ID
   */
  id: string;

  /**
   * Task type
   */
  type: string;

  /**
   * Task status
   */
  status: TaskStatus;

  /**
   * Task start time
   */
  startTime: Date;

  /**
   * Task end time
   */
  endTime?: Date;

  /**
   * Task node
   */
  node: string;

  /**
   * Task user
   */
  user: string;

  /**
   * Task description
   */
  description?: string;
}
