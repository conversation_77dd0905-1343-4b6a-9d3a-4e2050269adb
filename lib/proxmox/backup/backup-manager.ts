/**
 * Backup Manager
 *
 * This module provides functionality for managing Proxmox backups and snapshots.
 */

import { EventEmitter } from 'events';
import { ProxmoxApi } from '../api/proxmox-api';
import { ProxmoxBackupError } from '../utils/error-handler';
import logger from '../utils/logger';

/**
 * Backup manager events
 */
export enum BackupManagerEvent {
  BACKUP_CREATED = 'backup:created',
  BACKUP_DELETED = 'backup:deleted',
  SNAPSHOT_CREATED = 'snapshot:created',
  SNAPSHOT_DELETED = 'snapshot:deleted',
  SNAPSHOT_ROLLED_BACK = 'snapshot:rolled-back',
  ERROR = 'error',
}

/**
 * Backup information
 */
export interface BackupInfo {
  /**
   * Backup ID
   */
  id: string;

  /**
   * Backup VM ID
   */
  vmid: string;

  /**
   * Backup storage
   */
  storage: string;

  /**
   * Backup filename
   */
  filename: string;

  /**
   * Backup creation time
   */
  timestamp: Date;

  /**
   * Backup size in bytes
   */
  size: number;

  /**
   * Backup notes
   */
  notes?: string;
}

/**
 * Snapshot information
 */
export interface SnapshotInfo {
  /**
   * Snapshot name
   */
  name: string;

  /**
   * Snapshot description
   */
  description?: string;

  /**
   * Snapshot creation time
   */
  timestamp: Date;

  /**
   * Snapshot VM state
   */
  vmstate: boolean;

  /**
   * Snapshot is current
   */
  current?: boolean;
}

/**
 * Backup configuration
 */
export interface BackupConfig {
  /**
   * Backup storage
   */
  storage: string;

  /**
   * Backup mode
   */
  mode?: 'snapshot' | 'suspend' | 'stop';

  /**
   * Backup compression
   */
  compress?: 'gzip' | 'lzo' | 'zstd' | 0;

  /**
   * Backup notes
   */
  notes?: string;
}

/**
 * Snapshot configuration
 */
export interface SnapshotConfig {
  /**
   * Snapshot name
   */
  name: string;

  /**
   * Snapshot description
   */
  description?: string;

  /**
   * Include VM state
   */
  vmstate?: boolean;
}

/**
 * Backup manager class
 */
export class BackupManager extends EventEmitter {
  private api: ProxmoxApi;
  private initialized: boolean = false;

  /**
   * Create a new BackupManager instance
   */
  constructor(api: ProxmoxApi) {
    super();
    this.api = api;
  }

  /**
   * Initialize the backup manager
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Nothing to initialize for now
      this.initialized = true;
      logger.info('Backup manager initialized');
    } catch (error) {
      logger.error('Failed to initialize backup manager', error);
      throw error;
    }
  }

  /**
   * List backups for a VM
   */
  async listBackups(node: string, vmid: string): Promise<BackupInfo[]> {
    try {
      logger.info(`Listing backups for VM ${vmid} on node ${node}`);

      const response = await this.api.get(`/nodes/${node}/qemu/${vmid}/backup`);
      const backups = response.data.data;

      return backups.map((backup: any) => ({
        id: backup.volid,
        vmid,
        storage: backup.storage,
        filename: backup.filename,
        timestamp: new Date(backup.timestamp * 1000),
        size: backup.size,
        notes: backup.notes,
      }));
    } catch (error) {
      logger.error(`Failed to list backups for VM ${vmid} on node ${node}`, error);
      this.emit(BackupManagerEvent.ERROR, error);
      throw new ProxmoxBackupError(`Failed to list backups for VM ${vmid} on node ${node}`, { cause: error });
    }
  }

  /**
   * Create a backup for a VM
   */
  async createBackup(node: string, vmid: string, config: BackupConfig): Promise<BackupInfo> {
    try {
      logger.info(`Creating backup for VM ${vmid} on node ${node}`);

      // Prepare backup configuration
      const backupConfig: Record<string, any> = {
        storage: config.storage,
      };

      if (config.mode) {
        backupConfig.mode = config.mode;
      }

      if (config.compress) {
        backupConfig.compress = config.compress;
      }

      if (config.notes) {
        backupConfig.notes = config.notes;
      }

      // Create backup
      const response = await this.api.post(`/nodes/${node}/qemu/${vmid}/backup`, backupConfig);
      
      // Wait for the task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);

      // Get the latest backup
      const backups = await this.listBackups(node, vmid);
      const latestBackup = backups.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

      if (!latestBackup) {
        throw new ProxmoxBackupError(`Failed to find the created backup for VM ${vmid} on node ${node}`);
      }

      this.emit(BackupManagerEvent.BACKUP_CREATED, latestBackup);
      return latestBackup;
    } catch (error) {
      logger.error(`Failed to create backup for VM ${vmid} on node ${node}`, error);
      this.emit(BackupManagerEvent.ERROR, error);
      throw new ProxmoxBackupError(`Failed to create backup for VM ${vmid} on node ${node}`, { cause: error });
    }
  }

  /**
   * Delete a backup
   */
  async deleteBackup(node: string, vmid: string, backupId: string): Promise<void> {
    try {
      logger.info(`Deleting backup ${backupId} for VM ${vmid} on node ${node}`);

      // Get backup info before deleting
      const backups = await this.listBackups(node, vmid);
      const backup = backups.find(b => b.id === backupId);

      if (!backup) {
        throw new ProxmoxBackupError(`Backup ${backupId} not found for VM ${vmid} on node ${node}`);
      }

      // Delete backup
      await this.api.delete(`/nodes/${node}/storage/${backup.storage}/content/${backupId}`);

      this.emit(BackupManagerEvent.BACKUP_DELETED, backup);
    } catch (error) {
      logger.error(`Failed to delete backup ${backupId} for VM ${vmid} on node ${node}`, error);
      this.emit(BackupManagerEvent.ERROR, error);
      throw new ProxmoxBackupError(`Failed to delete backup ${backupId} for VM ${vmid} on node ${node}`, { cause: error });
    }
  }

  /**
   * Restore a backup
   */
  async restoreBackup(node: string, vmid: string, backupId: string, targetVmid?: string): Promise<void> {
    try {
      logger.info(`Restoring backup ${backupId} for VM ${vmid} on node ${node}`);

      // Prepare restore configuration
      const restoreConfig: Record<string, any> = {
        archive: backupId,
      };

      if (targetVmid) {
        restoreConfig.vmid = targetVmid;
      }

      // Restore backup
      const response = await this.api.post(`/nodes/${node}/qemu/${vmid}/snapshot`, restoreConfig);
      
      // Wait for the task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);
    } catch (error) {
      logger.error(`Failed to restore backup ${backupId} for VM ${vmid} on node ${node}`, error);
      this.emit(BackupManagerEvent.ERROR, error);
      throw new ProxmoxBackupError(`Failed to restore backup ${backupId} for VM ${vmid} on node ${node}`, { cause: error });
    }
  }

  /**
   * List snapshots for a VM
   */
  async listSnapshots(node: string, vmid: string): Promise<SnapshotInfo[]> {
    try {
      logger.info(`Listing snapshots for VM ${vmid} on node ${node}`);

      const response = await this.api.get(`/nodes/${node}/qemu/${vmid}/snapshot`);
      const snapshots = response.data.data;

      return snapshots.map((snapshot: any) => ({
        name: snapshot.name,
        description: snapshot.description,
        timestamp: new Date(snapshot.timestamp * 1000),
        vmstate: snapshot.vmstate,
        current: snapshot.current,
      }));
    } catch (error) {
      logger.error(`Failed to list snapshots for VM ${vmid} on node ${node}`, error);
      this.emit(BackupManagerEvent.ERROR, error);
      throw new ProxmoxBackupError(`Failed to list snapshots for VM ${vmid} on node ${node}`, { cause: error });
    }
  }

  /**
   * Create a snapshot for a VM
   */
  async createSnapshot(node: string, vmid: string, config: SnapshotConfig): Promise<SnapshotInfo> {
    try {
      logger.info(`Creating snapshot ${config.name} for VM ${vmid} on node ${node}`);

      // Prepare snapshot configuration
      const snapshotConfig: Record<string, any> = {
        snapname: config.name,
      };

      if (config.description) {
        snapshotConfig.description = config.description;
      }

      if (config.vmstate !== undefined) {
        snapshotConfig.vmstate = config.vmstate ? 1 : 0;
      }

      // Create snapshot
      const response = await this.api.post(`/nodes/${node}/qemu/${vmid}/snapshot`, snapshotConfig);
      
      // Wait for the task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);

      // Get the created snapshot
      const snapshots = await this.listSnapshots(node, vmid);
      const snapshot = snapshots.find(s => s.name === config.name);

      if (!snapshot) {
        throw new ProxmoxBackupError(`Failed to find the created snapshot ${config.name} for VM ${vmid} on node ${node}`);
      }

      this.emit(BackupManagerEvent.SNAPSHOT_CREATED, snapshot);
      return snapshot;
    } catch (error) {
      logger.error(`Failed to create snapshot ${config.name} for VM ${vmid} on node ${node}`, error);
      this.emit(BackupManagerEvent.ERROR, error);
      throw new ProxmoxBackupError(`Failed to create snapshot ${config.name} for VM ${vmid} on node ${node}`, { cause: error });
    }
  }

  /**
   * Delete a snapshot
   */
  async deleteSnapshot(node: string, vmid: string, snapshot: string): Promise<void> {
    try {
      logger.info(`Deleting snapshot ${snapshot} for VM ${vmid} on node ${node}`);

      // Get snapshot info before deleting
      const snapshots = await this.listSnapshots(node, vmid);
      const snapshotInfo = snapshots.find(s => s.name === snapshot);

      if (!snapshotInfo) {
        throw new ProxmoxBackupError(`Snapshot ${snapshot} not found for VM ${vmid} on node ${node}`);
      }

      // Delete snapshot
      const response = await this.api.delete(`/nodes/${node}/qemu/${vmid}/snapshot/${snapshot}`);
      
      // Wait for the task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);

      this.emit(BackupManagerEvent.SNAPSHOT_DELETED, snapshotInfo);
    } catch (error) {
      logger.error(`Failed to delete snapshot ${snapshot} for VM ${vmid} on node ${node}`, error);
      this.emit(BackupManagerEvent.ERROR, error);
      throw new ProxmoxBackupError(`Failed to delete snapshot ${snapshot} for VM ${vmid} on node ${node}`, { cause: error });
    }
  }

  /**
   * Rollback to a snapshot
   */
  async rollbackSnapshot(node: string, vmid: string, snapshot: string): Promise<void> {
    try {
      logger.info(`Rolling back to snapshot ${snapshot} for VM ${vmid} on node ${node}`);

      // Get snapshot info before rolling back
      const snapshots = await this.listSnapshots(node, vmid);
      const snapshotInfo = snapshots.find(s => s.name === snapshot);

      if (!snapshotInfo) {
        throw new ProxmoxBackupError(`Snapshot ${snapshot} not found for VM ${vmid} on node ${node}`);
      }

      // Rollback to snapshot
      const response = await this.api.post(`/nodes/${node}/qemu/${vmid}/snapshot/${snapshot}/rollback`);
      
      // Wait for the task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);

      this.emit(BackupManagerEvent.SNAPSHOT_ROLLED_BACK, snapshotInfo);
    } catch (error) {
      logger.error(`Failed to rollback to snapshot ${snapshot} for VM ${vmid} on node ${node}`, error);
      this.emit(BackupManagerEvent.ERROR, error);
      throw new ProxmoxBackupError(`Failed to rollback to snapshot ${snapshot} for VM ${vmid} on node ${node}`, { cause: error });
    }
  }

  /**
   * Wait for a task to complete
   */
  private async waitForTask(node: string, taskId: string, timeout: number = 60000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await this.api.get(`/nodes/${node}/tasks/${taskId}/status`);
        const status = response.data.data.status;
        
        if (status === 'stopped') {
          const exitStatus = response.data.data.exitstatus;
          
          if (exitStatus === 'OK') {
            return;
          } else {
            throw new ProxmoxBackupError(`Task ${taskId} failed with status ${exitStatus}`);
          }
        }
        
        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        if (error instanceof ProxmoxBackupError) {
          throw error;
        }
        
        logger.warn(`Failed to check task ${taskId} status`, error);
        // Continue waiting
      }
    }
    
    throw new ProxmoxBackupError(`Task ${taskId} timed out after ${timeout}ms`);
  }
}
