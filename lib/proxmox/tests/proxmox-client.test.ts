/**
 * Proxmox Client Tests
 *
 * This file contains unit tests for the Proxmox client.
 */

import { ProxmoxClient } from '../api/proxmox-client';
import { ProxmoxApi } from '../api/proxmox-api';
import { ProxmoxAuth } from '../auth';
import { VMStatus } from '../types';

// Mock the ProxmoxApi class
jest.mock('../api/proxmox-api');
jest.mock('../auth');

describe('ProxmoxClient', () => {
  let client: ProxmoxClient;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create a new client for each test
    client = new ProxmoxClient({
      apiUrl: 'https://proxmox.example.com:8006',
      credentials: {
        username: 'root',
        password: 'password',
        realm: 'pam',
      },
      autoInitialize: false,
    });
  });
  
  describe('initialize', () => {
    it('should initialize the client and all managers', async () => {
      // Mock the API initialize method
      const initializeMock = jest.spyOn(client.api, 'initialize').mockResolvedValue();
      
      // Mock the manager initialize methods
      const vmInitializeMock = jest.spyOn(client.vm, 'initialize').mockResolvedValue();
      const clusterInitializeMock = jest.spyOn(client.cluster, 'initialize').mockResolvedValue();
      const storageInitializeMock = jest.spyOn(client.storage, 'initialize').mockResolvedValue();
      const networkInitializeMock = jest.spyOn(client.network, 'initialize').mockResolvedValue();
      const backupInitializeMock = jest.spyOn(client.backup, 'initialize').mockResolvedValue();
      const taskInitializeMock = jest.spyOn(client.task, 'initialize').mockResolvedValue();
      
      // Initialize the client
      await client.initialize();
      
      // Verify that all initialize methods were called
      expect(initializeMock).toHaveBeenCalled();
      expect(vmInitializeMock).toHaveBeenCalled();
      expect(clusterInitializeMock).toHaveBeenCalled();
      expect(storageInitializeMock).toHaveBeenCalled();
      expect(networkInitializeMock).toHaveBeenCalled();
      expect(backupInitializeMock).toHaveBeenCalled();
      expect(taskInitializeMock).toHaveBeenCalled();
    });
    
    it('should not initialize twice', async () => {
      // Mock the API initialize method
      const initializeMock = jest.spyOn(client.api, 'initialize').mockResolvedValue();
      
      // Initialize the client
      await client.initialize();
      
      // Reset the mock
      initializeMock.mockClear();
      
      // Initialize the client again
      await client.initialize();
      
      // Verify that initialize was not called again
      expect(initializeMock).not.toHaveBeenCalled();
    });
  });
  
  describe('isAuthenticated', () => {
    it('should check if the client is authenticated', async () => {
      // Mock the API isAuthenticated method
      const isAuthenticatedMock = jest.spyOn(client.api, 'isAuthenticated').mockResolvedValue(true);
      
      // Check if the client is authenticated
      const result = await client.isAuthenticated();
      
      // Verify that isAuthenticated was called
      expect(isAuthenticatedMock).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });
  
  describe('logout', () => {
    it('should logout from the API', async () => {
      // Mock the API logout method
      const logoutMock = jest.spyOn(client.api, 'logout').mockResolvedValue();
      
      // Logout from the API
      await client.logout();
      
      // Verify that logout was called
      expect(logoutMock).toHaveBeenCalled();
    });
  });
  
  describe('VM Manager', () => {
    it('should list VMs', async () => {
      // Mock the VM manager listVMs method
      const listVMsMock = jest.spyOn(client.vm, 'listVMs').mockResolvedValue([
        {
          id: '100',
          name: 'test-vm',
          status: VMStatus.RUNNING,
          node: 'node1',
          type: 'qemu',
        },
      ]);
      
      // List VMs
      const vms = await client.vm.listVMs('node1');
      
      // Verify that listVMs was called
      expect(listVMsMock).toHaveBeenCalledWith('node1');
      expect(vms).toHaveLength(1);
      expect(vms[0].name).toBe('test-vm');
    });
    
    it('should create a VM', async () => {
      // Mock the VM manager createVM method
      const createVMMock = jest.spyOn(client.vm, 'createVM').mockResolvedValue({
        id: '100',
        name: 'test-vm',
        status: VMStatus.STOPPED,
        node: 'node1',
        type: 'qemu',
      });
      
      // Create a VM
      const vm = await client.vm.createVM({
        node: 'node1',
        vmid: 100,
        name: 'test-vm',
        cores: 1,
        memory: 512,
      });
      
      // Verify that createVM was called
      expect(createVMMock).toHaveBeenCalledWith({
        node: 'node1',
        vmid: 100,
        name: 'test-vm',
        cores: 1,
        memory: 512,
      });
      expect(vm.name).toBe('test-vm');
    });
  });
  
  describe('Cluster Manager', () => {
    it('should list nodes', async () => {
      // Mock the cluster manager listNodes method
      const listNodesMock = jest.spyOn(client.cluster, 'listNodes').mockResolvedValue([
        {
          id: 'node1',
          name: 'node1',
          status: 'online',
        },
      ]);
      
      // List nodes
      const nodes = await client.cluster.listNodes();
      
      // Verify that listNodes was called
      expect(listNodesMock).toHaveBeenCalled();
      expect(nodes).toHaveLength(1);
      expect(nodes[0].name).toBe('node1');
    });
  });
  
  describe('Storage Manager', () => {
    it('should list storage', async () => {
      // Mock the storage manager listStorage method
      const listStorageMock = jest.spyOn(client.storage, 'listStorage').mockResolvedValue([
        {
          id: 'local',
          name: 'local',
          type: 'dir',
          path: '/var/lib/vz',
        },
      ]);
      
      // List storage
      const storage = await client.storage.listStorage();
      
      // Verify that listStorage was called
      expect(listStorageMock).toHaveBeenCalled();
      expect(storage).toHaveLength(1);
      expect(storage[0].name).toBe('local');
    });
  });
  
  describe('Network Manager', () => {
    it('should list interfaces', async () => {
      // Mock the network manager listInterfaces method
      const listInterfacesMock = jest.spyOn(client.network, 'listInterfaces').mockResolvedValue([
        {
          name: 'eth0',
          type: 'bridge',
        },
      ]);
      
      // List interfaces
      const interfaces = await client.network.listInterfaces('node1');
      
      // Verify that listInterfaces was called
      expect(listInterfacesMock).toHaveBeenCalledWith('node1');
      expect(interfaces).toHaveLength(1);
      expect(interfaces[0].name).toBe('eth0');
    });
  });
  
  describe('Backup Manager', () => {
    it('should list backups', async () => {
      // Mock the backup manager listBackups method
      const listBackupsMock = jest.spyOn(client.backup, 'listBackups').mockResolvedValue([
        {
          id: 'local:backup/vm-100-disk-0.qcow2',
          vmid: '100',
          storage: 'local',
          filename: 'vm-100-disk-0.qcow2',
          timestamp: new Date(),
          size: 1024,
        },
      ]);
      
      // List backups
      const backups = await client.backup.listBackups('node1', '100');
      
      // Verify that listBackups was called
      expect(listBackupsMock).toHaveBeenCalledWith('node1', '100');
      expect(backups).toHaveLength(1);
      expect(backups[0].vmid).toBe('100');
    });
  });
  
  describe('Task Manager', () => {
    it('should list tasks', async () => {
      // Mock the task manager listTasks method
      const listTasksMock = jest.spyOn(client.task, 'listTasks').mockResolvedValue([
        {
          id: 'UPID:node1:00000000:00000000:00000000:task:100:root@pam:',
          type: 'qmcreate',
          status: 'running',
          startTime: new Date(),
          node: 'node1',
          user: 'root@pam',
          description: 'Create VM 100',
        },
      ]);
      
      // List tasks
      const tasks = await client.task.listTasks('node1');
      
      // Verify that listTasks was called
      expect(listTasksMock).toHaveBeenCalledWith('node1', {});
      expect(tasks).toHaveLength(1);
      expect(tasks[0].type).toBe('qmcreate');
    });
  });
});
