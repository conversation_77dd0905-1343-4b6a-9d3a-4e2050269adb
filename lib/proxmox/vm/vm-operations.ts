/**
 * VM Operations
 *
 * This module provides operations for managing VMs in Proxmox.
 */

import { ProxmoxApi } from '../api/proxmox-api';
import { VMConfig, VMInfo, VMStatus } from '../types';
import { ProxmoxVMError } from '../utils/error-handler';
import logger from '../utils/logger';

/**
 * Create VM options
 */
export interface CreateVMOptions {
  /**
   * Node name
   */
  node: string;

  /**
   * VM ID
   */
  vmid: number;

  /**
   * VM name
   */
  name: string;

  /**
   * VM description
   */
  description?: string;

  /**
   * VM template ID
   */
  template?: string;

  /**
   * Number of CPU cores
   */
  cores?: number;

  /**
   * CPU type
   */
  cpuType?: string;

  /**
   * Memory in MB
   */
  memory?: number;

  /**
   * Disk configuration
   */
  disks?: Array<{
    /**
     * Storage pool
     */
    storage: string;

    /**
     * Disk size in GB
     */
    size: number;

    /**
     * Disk format
     */
    format?: 'raw' | 'qcow2' | 'vmdk';
  }>;

  /**
   * Network configuration
   */
  networks?: Array<{
    /**
     * Network model
     */
    model: string;

    /**
     * Bridge name
     */
    bridge?: string;

    /**
     * VLAN tag
     */
    vlan?: number;
  }>;

  /**
   * Start on boot
   */
  onBoot?: boolean;

  /**
   * Start after creation
   */
  start?: boolean;
}

/**
 * VM operations class
 */
export class VMOperations {
  private api: ProxmoxApi;

  /**
   * Create a new VMOperations instance
   */
  constructor(api: ProxmoxApi) {
    this.api = api;
  }

  /**
   * Create a new VM
   */
  async createVM(options: CreateVMOptions): Promise<VMInfo> {
    try {
      logger.info(`Creating VM ${options.name} on node ${options.node}`);

      // Prepare VM configuration
      const vmConfig: Record<string, any> = {
        name: options.name,
        description: options.description,
        cores: options.cores,
        memory: options.memory,
        onboot: options.onBoot ? 1 : 0,
      };

      // Add CPU type if specified
      if (options.cpuType) {
        vmConfig.cpu = options.cpuType;
      }

      // Add disks if specified
      if (options.disks && options.disks.length > 0) {
        options.disks.forEach((disk, index) => {
          vmConfig[`scsi${index}`] = `${disk.storage}:${disk.size}${disk.format ? `,format=${disk.format}` : ''}`;
        });
      }

      // Add networks if specified
      if (options.networks && options.networks.length > 0) {
        options.networks.forEach((network, index) => {
          let netConfig = `model=${network.model}`;
          
          if (network.bridge) {
            netConfig += `,bridge=${network.bridge}`;
          }
          
          if (network.vlan) {
            netConfig += `,tag=${network.vlan}`;
          }
          
          vmConfig[`net${index}`] = netConfig;
        });
      }

      // Create VM from template if specified
      if (options.template) {
        // Clone from template
        const response = await this.api.post(`/nodes/${options.node}/qemu/${options.template}/clone`, {
          newid: options.vmid,
          name: options.name,
          description: options.description,
          full: 1, // Full clone
        });

        // Wait for clone task to complete
        const taskId = response.data.data;
        await this.waitForTask(options.node, taskId);

        // Update VM configuration
        await this.api.put(`/nodes/${options.node}/qemu/${options.vmid}/config`, vmConfig);
      } else {
        // Create new VM
        await this.api.post(`/nodes/${options.node}/qemu`, {
          vmid: options.vmid,
          ...vmConfig,
        });
      }

      // Start VM if requested
      if (options.start) {
        await this.startVM(options.node, options.vmid.toString());
      }

      // Get VM info
      return await this.getVM(options.node, options.vmid.toString());
    } catch (error) {
      logger.error(`Failed to create VM ${options.name} on node ${options.node}`, error);
      throw new ProxmoxVMError(`Failed to create VM ${options.name}`, { cause: error });
    }
  }

  /**
   * Get VM information
   */
  async getVM(node: string, vmid: string): Promise<VMInfo> {
    try {
      logger.info(`Getting VM ${vmid} on node ${node}`);

      // Get VM status
      const statusResponse = await this.api.get(`/nodes/${node}/qemu/${vmid}/status/current`);
      const statusData = statusResponse.data.data;

      // Get VM config
      const configResponse = await this.api.get(`/nodes/${node}/qemu/${vmid}/config`);
      const configData = configResponse.data.data;

      // Map status to VMStatus enum
      let status: VMStatus;
      switch (statusData.status) {
        case 'running':
          status = VMStatus.RUNNING;
          break;
        case 'stopped':
          status = VMStatus.STOPPED;
          break;
        case 'paused':
          status = VMStatus.PAUSED;
          break;
        case 'suspended':
          status = VMStatus.SUSPENDED;
          break;
        default:
          status = VMStatus.UNKNOWN;
      }

      // Create VM info object
      const vmInfo: VMInfo = {
        id: vmid,
        name: configData.name,
        status,
        node,
        type: 'qemu',
        uptime: statusData.uptime,
      };

      // Add resource usage if available
      if (statusData.cpu !== undefined) {
        vmInfo.cpuUsage = statusData.cpu;
      }

      if (statusData.mem !== undefined && statusData.maxmem !== undefined) {
        vmInfo.memoryUsage = statusData.mem;
      }

      // Add VM configuration
      vmInfo.config = {
        name: configData.name,
        description: configData.description,
        cpu: {
          cores: configData.cores || 1,
          type: configData.cpu,
        },
        memory: configData.memory,
        onBoot: configData.onboot === 1,
      };

      return vmInfo;
    } catch (error) {
      logger.error(`Failed to get VM ${vmid} on node ${node}`, error);
      throw new ProxmoxVMError(`Failed to get VM ${vmid}`, { cause: error });
    }
  }

  /**
   * List all VMs on a node
   */
  async listVMs(node: string): Promise<VMInfo[]> {
    try {
      logger.info(`Listing VMs on node ${node}`);

      const response = await this.api.get(`/nodes/${node}/qemu`);
      const vms = response.data.data;

      return Promise.all(
        vms.map(async (vm: any) => {
          try {
            return await this.getVM(node, vm.vmid.toString());
          } catch (error) {
            logger.warn(`Failed to get VM ${vm.vmid} details`, error);
            
            // Return basic info if detailed info is not available
            return {
              id: vm.vmid.toString(),
              name: vm.name,
              status: this.mapStatus(vm.status),
              node,
              type: 'qemu',
            };
          }
        })
      );
    } catch (error) {
      logger.error(`Failed to list VMs on node ${node}`, error);
      throw new ProxmoxVMError(`Failed to list VMs on node ${node}`, { cause: error });
    }
  }

  /**
   * Start a VM
   */
  async startVM(node: string, vmid: string): Promise<void> {
    try {
      logger.info(`Starting VM ${vmid} on node ${node}`);

      const response = await this.api.post(`/nodes/${node}/qemu/${vmid}/status/start`);
      
      // Wait for the task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);
    } catch (error) {
      logger.error(`Failed to start VM ${vmid} on node ${node}`, error);
      throw new ProxmoxVMError(`Failed to start VM ${vmid}`, { cause: error });
    }
  }

  /**
   * Stop a VM
   */
  async stopVM(node: string, vmid: string): Promise<void> {
    try {
      logger.info(`Stopping VM ${vmid} on node ${node}`);

      const response = await this.api.post(`/nodes/${node}/qemu/${vmid}/status/stop`);
      
      // Wait for the task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);
    } catch (error) {
      logger.error(`Failed to stop VM ${vmid} on node ${node}`, error);
      throw new ProxmoxVMError(`Failed to stop VM ${vmid}`, { cause: error });
    }
  }

  /**
   * Restart a VM
   */
  async restartVM(node: string, vmid: string): Promise<void> {
    try {
      logger.info(`Restarting VM ${vmid} on node ${node}`);

      const response = await this.api.post(`/nodes/${node}/qemu/${vmid}/status/reset`);
      
      // Wait for the task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);
    } catch (error) {
      logger.error(`Failed to restart VM ${vmid} on node ${node}`, error);
      throw new ProxmoxVMError(`Failed to restart VM ${vmid}`, { cause: error });
    }
  }

  /**
   * Delete a VM
   */
  async deleteVM(node: string, vmid: string): Promise<void> {
    try {
      logger.info(`Deleting VM ${vmid} on node ${node}`);

      // Get VM status
      const vm = await this.getVM(node, vmid);

      // Stop VM if running
      if (vm.status === VMStatus.RUNNING) {
        await this.stopVM(node, vmid);
      }

      // Delete VM
      const response = await this.api.delete(`/nodes/${node}/qemu/${vmid}`);
      
      // Wait for the task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);
    } catch (error) {
      logger.error(`Failed to delete VM ${vmid} on node ${node}`, error);
      throw new ProxmoxVMError(`Failed to delete VM ${vmid}`, { cause: error });
    }
  }

  /**
   * Update VM configuration
   */
  async updateVM(node: string, vmid: string, config: Partial<VMConfig>): Promise<VMInfo> {
    try {
      logger.info(`Updating VM ${vmid} on node ${node}`);

      // Prepare VM configuration
      const vmConfig: Record<string, any> = {};

      if (config.name) {
        vmConfig.name = config.name;
      }

      if (config.description) {
        vmConfig.description = config.description;
      }

      if (config.cpu?.cores) {
        vmConfig.cores = config.cpu.cores;
      }

      if (config.cpu?.type) {
        vmConfig.cpu = config.cpu.type;
      }

      if (config.memory) {
        vmConfig.memory = config.memory;
      }

      if (config.onBoot !== undefined) {
        vmConfig.onboot = config.onBoot ? 1 : 0;
      }

      // Update VM configuration
      await this.api.put(`/nodes/${node}/qemu/${vmid}/config`, vmConfig);

      // Get updated VM info
      return await this.getVM(node, vmid);
    } catch (error) {
      logger.error(`Failed to update VM ${vmid} on node ${node}`, error);
      throw new ProxmoxVMError(`Failed to update VM ${vmid}`, { cause: error });
    }
  }

  /**
   * Wait for a task to complete
   */
  private async waitForTask(node: string, taskId: string, timeout: number = 60000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await this.api.get(`/nodes/${node}/tasks/${taskId}/status`);
        const status = response.data.data.status;
        
        if (status === 'stopped') {
          const exitStatus = response.data.data.exitstatus;
          
          if (exitStatus === 'OK') {
            return;
          } else {
            throw new ProxmoxVMError(`Task ${taskId} failed with status ${exitStatus}`);
          }
        }
        
        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        if (error instanceof ProxmoxVMError) {
          throw error;
        }
        
        logger.warn(`Failed to check task ${taskId} status`, error);
        // Continue waiting
      }
    }
    
    throw new ProxmoxVMError(`Task ${taskId} timed out after ${timeout}ms`);
  }

  /**
   * Map Proxmox status to VMStatus enum
   */
  private mapStatus(status: string): VMStatus {
    switch (status) {
      case 'running':
        return VMStatus.RUNNING;
      case 'stopped':
        return VMStatus.STOPPED;
      case 'paused':
        return VMStatus.PAUSED;
      case 'suspended':
        return VMStatus.SUSPENDED;
      default:
        return VMStatus.UNKNOWN;
    }
  }
}
