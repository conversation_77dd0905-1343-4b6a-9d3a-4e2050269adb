/**
 * VM Manager
 *
 * This module provides a high-level interface for managing VMs in Proxmox.
 */

import { EventEmitter } from 'events';
import { ProxmoxApi } from '../api/proxmox-api';
import { VMOperations, CreateVMOptions } from './vm-operations';
import { VMConfig, VMInfo, VMStatus } from '../types';
import { ProxmoxVMError } from '../utils/error-handler';
import logger from '../utils/logger';

/**
 * VM manager events
 */
export enum VMManagerEvent {
  VM_CREATED = 'vm:created',
  VM_UPDATED = 'vm:updated',
  VM_DELETED = 'vm:deleted',
  VM_STARTED = 'vm:started',
  VM_STOPPED = 'vm:stopped',
  VM_RESTARTED = 'vm:restarted',
  ERROR = 'error',
}

/**
 * VM template options
 */
export interface VMTemplateOptions {
  /**
   * Node name
   */
  node: string;

  /**
   * VM ID
   */
  vmid: number;

  /**
   * Template name
   */
  name: string;

  /**
   * Template description
   */
  description?: string;

  /**
   * Number of CPU cores
   */
  cores?: number;

  /**
   * CPU type
   */
  cpuType?: string;

  /**
   * Memory in MB
   */
  memory?: number;

  /**
   * Disk configuration
   */
  disks?: Array<{
    /**
     * Storage pool
     */
    storage: string;

    /**
     * Disk size in GB
     */
    size: number;

    /**
     * Disk format
     */
    format?: 'raw' | 'qcow2' | 'vmdk';
  }>;

  /**
   * Network configuration
   */
  networks?: Array<{
    /**
     * Network model
     */
    model: string;

    /**
     * Bridge name
     */
    bridge?: string;

    /**
     * VLAN tag
     */
    vlan?: number;
  }>;
}

/**
 * VM manager class
 */
export class VMManager extends EventEmitter {
  private api: ProxmoxApi;
  private operations: VMOperations;
  private initialized: boolean = false;

  /**
   * Create a new VMManager instance
   */
  constructor(api: ProxmoxApi) {
    super();
    this.api = api;
    this.operations = new VMOperations(api);
  }

  /**
   * Initialize the VM manager
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Nothing to initialize for now
      this.initialized = true;
      logger.info('VM manager initialized');
    } catch (error) {
      logger.error('Failed to initialize VM manager', error);
      throw error;
    }
  }

  /**
   * Create a new VM
   */
  async createVM(options: CreateVMOptions): Promise<VMInfo> {
    try {
      const vm = await this.operations.createVM(options);
      this.emit(VMManagerEvent.VM_CREATED, vm);
      return vm;
    } catch (error) {
      this.emit(VMManagerEvent.ERROR, error);
      throw error;
    }
  }

  /**
   * Get VM information
   */
  async getVM(node: string, vmid: string): Promise<VMInfo> {
    return this.operations.getVM(node, vmid);
  }

  /**
   * List all VMs on a node
   */
  async listVMs(node: string): Promise<VMInfo[]> {
    return this.operations.listVMs(node);
  }

  /**
   * Start a VM
   */
  async startVM(node: string, vmid: string): Promise<void> {
    try {
      await this.operations.startVM(node, vmid);
      const vm = await this.getVM(node, vmid);
      this.emit(VMManagerEvent.VM_STARTED, vm);
    } catch (error) {
      this.emit(VMManagerEvent.ERROR, error);
      throw error;
    }
  }

  /**
   * Stop a VM
   */
  async stopVM(node: string, vmid: string): Promise<void> {
    try {
      await this.operations.stopVM(node, vmid);
      const vm = await this.getVM(node, vmid);
      this.emit(VMManagerEvent.VM_STOPPED, vm);
    } catch (error) {
      this.emit(VMManagerEvent.ERROR, error);
      throw error;
    }
  }

  /**
   * Restart a VM
   */
  async restartVM(node: string, vmid: string): Promise<void> {
    try {
      await this.operations.restartVM(node, vmid);
      const vm = await this.getVM(node, vmid);
      this.emit(VMManagerEvent.VM_RESTARTED, vm);
    } catch (error) {
      this.emit(VMManagerEvent.ERROR, error);
      throw error;
    }
  }

  /**
   * Delete a VM
   */
  async deleteVM(node: string, vmid: string): Promise<void> {
    try {
      // Get VM info before deleting
      const vm = await this.getVM(node, vmid);
      
      await this.operations.deleteVM(node, vmid);
      this.emit(VMManagerEvent.VM_DELETED, vm);
    } catch (error) {
      this.emit(VMManagerEvent.ERROR, error);
      throw error;
    }
  }

  /**
   * Update VM configuration
   */
  async updateVM(node: string, vmid: string, config: Partial<VMConfig>): Promise<VMInfo> {
    try {
      const vm = await this.operations.updateVM(node, vmid, config);
      this.emit(VMManagerEvent.VM_UPDATED, vm);
      return vm;
    } catch (error) {
      this.emit(VMManagerEvent.ERROR, error);
      throw error;
    }
  }

  /**
   * Create a VM template
   */
  async createTemplate(options: VMTemplateOptions): Promise<VMInfo> {
    try {
      logger.info(`Creating VM template ${options.name} on node ${options.node}`);

      // Create a regular VM first
      const createOptions: CreateVMOptions = {
        node: options.node,
        vmid: options.vmid,
        name: options.name,
        description: options.description,
        cores: options.cores,
        cpuType: options.cpuType,
        memory: options.memory,
        disks: options.disks,
        networks: options.networks,
        onBoot: false,
        start: false,
      };

      const vm = await this.createVM(createOptions);

      // Convert VM to template
      await this.api.post(`/nodes/${options.node}/qemu/${options.vmid}/template`);

      return vm;
    } catch (error) {
      logger.error(`Failed to create VM template ${options.name} on node ${options.node}`, error);
      this.emit(VMManagerEvent.ERROR, error);
      throw new ProxmoxVMError(`Failed to create VM template ${options.name}`, { cause: error });
    }
  }

  /**
   * Clone a VM from a template
   */
  async cloneFromTemplate(
    node: string,
    templateId: string,
    newVmId: number,
    name: string,
    options: {
      description?: string;
      start?: boolean;
      full?: boolean;
    } = {}
  ): Promise<VMInfo> {
    try {
      logger.info(`Cloning VM from template ${templateId} to ${name} on node ${node}`);

      // Clone VM from template
      const response = await this.api.post(`/nodes/${node}/qemu/${templateId}/clone`, {
        newid: newVmId,
        name,
        description: options.description,
        full: options.full ? 1 : 0,
      });

      // Wait for clone task to complete
      const taskId = response.data.data;
      await this.waitForTask(node, taskId);

      // Start VM if requested
      if (options.start) {
        await this.startVM(node, newVmId.toString());
      }

      // Get VM info
      const vm = await this.getVM(node, newVmId.toString());
      this.emit(VMManagerEvent.VM_CREATED, vm);
      return vm;
    } catch (error) {
      logger.error(`Failed to clone VM from template ${templateId} on node ${node}`, error);
      this.emit(VMManagerEvent.ERROR, error);
      throw new ProxmoxVMError(`Failed to clone VM from template ${templateId}`, { cause: error });
    }
  }

  /**
   * Wait for a task to complete
   */
  private async waitForTask(node: string, taskId: string, timeout: number = 60000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await this.api.get(`/nodes/${node}/tasks/${taskId}/status`);
        const status = response.data.data.status;
        
        if (status === 'stopped') {
          const exitStatus = response.data.data.exitstatus;
          
          if (exitStatus === 'OK') {
            return;
          } else {
            throw new ProxmoxVMError(`Task ${taskId} failed with status ${exitStatus}`);
          }
        }
        
        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        if (error instanceof ProxmoxVMError) {
          throw error;
        }
        
        logger.warn(`Failed to check task ${taskId} status`, error);
        // Continue waiting
      }
    }
    
    throw new ProxmoxVMError(`Task ${taskId} timed out after ${timeout}ms`);
  }
}
