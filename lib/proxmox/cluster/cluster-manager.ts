/**
 * Cluster Manager
 *
 * This module provides functionality for managing Proxmox clusters.
 */

import { EventEmitter } from 'events';
import { ProxmoxApi } from '../api/proxmox-api';
import { NodeInfo, NodeStatus } from '../types';
import { ProxmoxApiError } from '../utils/error-handler';
import logger from '../utils/logger';

/**
 * Cluster manager events
 */
export enum ClusterManagerEvent {
  NODE_STATUS_CHANGED = 'node:status-changed',
  ERROR = 'error',
}

/**
 * Cluster resources
 */
export interface ClusterResources {
  /**
   * CPU usage (0-1)
   */
  cpu: number;

  /**
   * Memory usage in bytes
   */
  memory: {
    /**
     * Total memory in bytes
     */
    total: number;

    /**
     * Used memory in bytes
     */
    used: number;

    /**
     * Free memory in bytes
     */
    free: number;
  };

  /**
   * Storage usage in bytes
   */
  storage: {
    /**
     * Total storage in bytes
     */
    total: number;

    /**
     * Used storage in bytes
     */
    used: number;

    /**
     * Free storage in bytes
     */
    free: number;
  };
}

/**
 * Cluster manager class
 */
export class ClusterManager extends EventEmitter {
  private api: ProxmoxApi;
  private initialized: boolean = false;
  private nodeStatusCache: Map<string, NodeStatus> = new Map();

  /**
   * Create a new ClusterManager instance
   */
  constructor(api: ProxmoxApi) {
    super();
    this.api = api;
  }

  /**
   * Initialize the cluster manager
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Initialize node status cache
      const nodes = await this.listNodes();
      
      for (const node of nodes) {
        this.nodeStatusCache.set(node.id, node.status);
      }
      
      this.initialized = true;
      logger.info('Cluster manager initialized');
    } catch (error) {
      logger.error('Failed to initialize cluster manager', error);
      throw error;
    }
  }

  /**
   * List all nodes in the cluster
   */
  async listNodes(): Promise<NodeInfo[]> {
    try {
      logger.info('Listing cluster nodes');

      const response = await this.api.get('/nodes');
      const nodes = response.data.data;

      return nodes.map((node: any) => {
        // Map node status
        let status: NodeStatus;
        if (node.status === 'online') {
          status = NodeStatus.ONLINE;
        } else if (node.status === 'offline') {
          status = NodeStatus.OFFLINE;
        } else {
          status = NodeStatus.UNKNOWN;
        }

        // Check if status has changed
        const previousStatus = this.nodeStatusCache.get(node.id);
        if (previousStatus && previousStatus !== status) {
          this.emit(ClusterManagerEvent.NODE_STATUS_CHANGED, {
            id: node.id,
            name: node.node,
            previousStatus,
            currentStatus: status,
          });
        }

        // Update status cache
        this.nodeStatusCache.set(node.id, status);

        // Create node info object
        const nodeInfo: NodeInfo = {
          id: node.id,
          name: node.node,
          status,
          ip: node.ip,
          uptime: node.uptime,
        };

        // Add CPU info if available
        if (node.cpu !== undefined) {
          nodeInfo.cpu = {
            model: node.cpuinfo?.model || 'Unknown',
            cores: node.cpuinfo?.cores || 1,
            usage: node.cpu,
          };
        }

        // Add memory info if available
        if (node.mem !== undefined && node.maxmem !== undefined) {
          nodeInfo.memory = {
            total: node.maxmem,
            used: node.mem,
            free: node.maxmem - node.mem,
          };
        }

        return nodeInfo;
      });
    } catch (error) {
      logger.error('Failed to list cluster nodes', error);
      this.emit(ClusterManagerEvent.ERROR, error);
      throw new ProxmoxApiError('Failed to list cluster nodes', { cause: error });
    }
  }

  /**
   * Get node information
   */
  async getNode(nodeId: string): Promise<NodeInfo> {
    try {
      logger.info(`Getting node ${nodeId}`);

      const nodes = await this.listNodes();
      const node = nodes.find(n => n.id === nodeId || n.name === nodeId);

      if (!node) {
        throw new ProxmoxApiError(`Node ${nodeId} not found`);
      }

      return node;
    } catch (error) {
      logger.error(`Failed to get node ${nodeId}`, error);
      this.emit(ClusterManagerEvent.ERROR, error);
      throw new ProxmoxApiError(`Failed to get node ${nodeId}`, { cause: error });
    }
  }

  /**
   * Get cluster resources
   */
  async getClusterResources(): Promise<ClusterResources> {
    try {
      logger.info('Getting cluster resources');

      const response = await this.api.get('/cluster/resources');
      const resources = response.data.data;

      // Calculate total CPU usage
      let totalCpu = 0;
      let nodeCount = 0;

      // Calculate total memory
      let totalMemory = 0;
      let usedMemory = 0;

      // Calculate total storage
      let totalStorage = 0;
      let usedStorage = 0;

      // Process resources
      for (const resource of resources) {
        if (resource.type === 'node') {
          // Add CPU usage
          if (resource.cpu !== undefined) {
            totalCpu += resource.cpu;
            nodeCount++;
          }

          // Add memory usage
          if (resource.maxmem !== undefined && resource.mem !== undefined) {
            totalMemory += resource.maxmem;
            usedMemory += resource.mem;
          }
        } else if (resource.type === 'storage') {
          // Add storage usage
          if (resource.maxdisk !== undefined && resource.disk !== undefined) {
            totalStorage += resource.maxdisk;
            usedStorage += resource.disk;
          }
        }
      }

      // Calculate average CPU usage
      const avgCpu = nodeCount > 0 ? totalCpu / nodeCount : 0;

      return {
        cpu: avgCpu,
        memory: {
          total: totalMemory,
          used: usedMemory,
          free: totalMemory - usedMemory,
        },
        storage: {
          total: totalStorage,
          used: usedStorage,
          free: totalStorage - usedStorage,
        },
      };
    } catch (error) {
      logger.error('Failed to get cluster resources', error);
      this.emit(ClusterManagerEvent.ERROR, error);
      throw new ProxmoxApiError('Failed to get cluster resources', { cause: error });
    }
  }

  /**
   * Get cluster status
   */
  async getClusterStatus(): Promise<any> {
    try {
      logger.info('Getting cluster status');

      const response = await this.api.get('/cluster/status');
      return response.data.data;
    } catch (error) {
      logger.error('Failed to get cluster status', error);
      this.emit(ClusterManagerEvent.ERROR, error);
      throw new ProxmoxApiError('Failed to get cluster status', { cause: error });
    }
  }

  /**
   * Get node tasks
   */
  async getNodeTasks(node: string, options: {
    start?: number;
    limit?: number;
    vmid?: string;
    running?: boolean;
  } = {}): Promise<any[]> {
    try {
      logger.info(`Getting tasks for node ${node}`);

      // Build query parameters
      const params: Record<string, any> = {};
      
      if (options.start !== undefined) {
        params.start = options.start;
      }
      
      if (options.limit !== undefined) {
        params.limit = options.limit;
      }
      
      if (options.vmid !== undefined) {
        params.vmid = options.vmid;
      }
      
      if (options.running !== undefined) {
        params.running = options.running ? 1 : 0;
      }

      // Make API request
      const response = await this.api.get(`/nodes/${node}/tasks`, {
        params,
      });

      return response.data.data;
    } catch (error) {
      logger.error(`Failed to get tasks for node ${node}`, error);
      this.emit(ClusterManagerEvent.ERROR, error);
      throw new ProxmoxApiError(`Failed to get tasks for node ${node}`, { cause: error });
    }
  }
}
