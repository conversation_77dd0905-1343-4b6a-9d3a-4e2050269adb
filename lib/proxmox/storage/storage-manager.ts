/**
 * Storage Manager
 *
 * This module provides functionality for managing Proxmox storage.
 */

import { EventEmitter } from 'events';
import { ProxmoxApi } from '../api/proxmox-api';
import { StorageInfo, StorageType } from '../types';
import { ProxmoxStorageError } from '../utils/error-handler';
import logger from '../utils/logger';

/**
 * Storage manager events
 */
export enum StorageManagerEvent {
  STORAGE_CREATED = 'storage:created',
  STORAGE_UPDATED = 'storage:updated',
  STORAGE_DELETED = 'storage:deleted',
  ERROR = 'error',
}

/**
 * Storage configuration
 */
export interface StorageConfig {
  /**
   * Storage name
   */
  name: string;

  /**
   * Storage type
   */
  type: StorageType;

  /**
   * Storage path (for directory storage)
   */
  path?: string;

  /**
   * Storage server (for NFS, CIFS, etc.)
   */
  server?: string;

  /**
   * Storage export (for NFS)
   */
  export?: string;

  /**
   * Storage share (for CIFS)
   */
  share?: string;

  /**
   * Storage username (for CIFS)
   */
  username?: string;

  /**
   * Storage password (for CIFS)
   */
  password?: string;

  /**
   * Storage domain (for CIFS)
   */
  domain?: string;

  /**
   * Storage pool (for LVM, ZFS)
   */
  pool?: string;

  /**
   * Storage content types
   */
  content?: Array<'images' | 'rootdir' | 'vztmpl' | 'backup' | 'iso'>;

  /**
   * Whether to enable this storage
   */
  enabled?: boolean;

  /**
   * Whether to mount this storage on boot
   */
  mountOnBoot?: boolean;

  /**
   * Additional options
   */
  options?: Record<string, any>;
}

/**
 * Storage volume
 */
export interface StorageVolume {
  /**
   * Volume ID
   */
  id: string;

  /**
   * Volume name
   */
  name: string;

  /**
   * Volume format
   */
  format: string;

  /**
   * Volume size in bytes
   */
  size: number;

  /**
   * Volume used space in bytes
   */
  used?: number;

  /**
   * Volume content type
   */
  content: string;

  /**
   * Volume owner VM ID
   */
  vmid?: string;
}

/**
 * Storage manager class
 */
export class StorageManager extends EventEmitter {
  private api: ProxmoxApi;
  private initialized: boolean = false;

  /**
   * Create a new StorageManager instance
   */
  constructor(api: ProxmoxApi) {
    super();
    this.api = api;
  }

  /**
   * Initialize the storage manager
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Nothing to initialize for now
      this.initialized = true;
      logger.info('Storage manager initialized');
    } catch (error) {
      logger.error('Failed to initialize storage manager', error);
      throw error;
    }
  }

  /**
   * List all storage
   */
  async listStorage(): Promise<StorageInfo[]> {
    try {
      logger.info('Listing storage');

      const response = await this.api.get('/storage');
      const storage = response.data.data;

      return storage.map((s: any) => {
        // Map storage type
        let type: StorageType;
        switch (s.type) {
          case 'dir':
            type = StorageType.DIR;
            break;
          case 'lvm':
            type = StorageType.LVM;
            break;
          case 'zfs':
            type = StorageType.ZFS;
            break;
          case 'cifs':
            type = StorageType.CIFS;
            break;
          case 'nfs':
            type = StorageType.NFS;
            break;
          case 'iscsi':
            type = StorageType.ISCSI;
            break;
          case 'glusterfs':
            type = StorageType.GLUSTERFS;
            break;
          default:
            type = s.type as StorageType;
        }

        // Create storage info object
        return {
          id: s.storage,
          name: s.storage,
          type,
          path: s.path,
          total: s.total,
          used: s.used,
          available: s.avail,
        };
      });
    } catch (error) {
      logger.error('Failed to list storage', error);
      this.emit(StorageManagerEvent.ERROR, error);
      throw new ProxmoxStorageError('Failed to list storage', { cause: error });
    }
  }

  /**
   * Get storage information
   */
  async getStorage(storageId: string): Promise<StorageInfo> {
    try {
      logger.info(`Getting storage ${storageId}`);

      const storage = await this.listStorage();
      const storageInfo = storage.find(s => s.id === storageId || s.name === storageId);

      if (!storageInfo) {
        throw new ProxmoxStorageError(`Storage ${storageId} not found`);
      }

      return storageInfo;
    } catch (error) {
      logger.error(`Failed to get storage ${storageId}`, error);
      this.emit(StorageManagerEvent.ERROR, error);
      throw new ProxmoxStorageError(`Failed to get storage ${storageId}`, { cause: error });
    }
  }

  /**
   * Create storage
   */
  async createStorage(config: StorageConfig): Promise<StorageInfo> {
    try {
      logger.info(`Creating storage ${config.name}`);

      // Prepare storage configuration
      const storageConfig: Record<string, any> = {
        storage: config.name,
        type: config.type.toLowerCase(),
      };

      // Add type-specific configuration
      switch (config.type) {
        case StorageType.DIR:
          if (!config.path) {
            throw new ProxmoxStorageError('Path is required for directory storage');
          }
          storageConfig.path = config.path;
          break;
        case StorageType.NFS:
          if (!config.server || !config.export) {
            throw new ProxmoxStorageError('Server and export are required for NFS storage');
          }
          storageConfig.server = config.server;
          storageConfig.export = config.export;
          break;
        case StorageType.CIFS:
          if (!config.server || !config.share) {
            throw new ProxmoxStorageError('Server and share are required for CIFS storage');
          }
          storageConfig.server = config.server;
          storageConfig.share = config.share;
          if (config.username) storageConfig.username = config.username;
          if (config.password) storageConfig.password = config.password;
          if (config.domain) storageConfig.domain = config.domain;
          break;
        case StorageType.LVM:
        case StorageType.ZFS:
          if (!config.pool) {
            throw new ProxmoxStorageError(`Pool is required for ${config.type} storage`);
          }
          storageConfig.pool = config.pool;
          break;
      }

      // Add content types
      if (config.content && config.content.length > 0) {
        storageConfig.content = config.content.join(',');
      }

      // Add other options
      if (config.enabled !== undefined) {
        storageConfig.disable = config.enabled ? 0 : 1;
      }

      if (config.mountOnBoot !== undefined) {
        storageConfig.shared = config.mountOnBoot ? 1 : 0;
      }

      // Add additional options
      if (config.options) {
        Object.assign(storageConfig, config.options);
      }

      // Create storage
      await this.api.post('/storage', storageConfig);

      // Get storage info
      const storageInfo = await this.getStorage(config.name);
      this.emit(StorageManagerEvent.STORAGE_CREATED, storageInfo);
      return storageInfo;
    } catch (error) {
      logger.error(`Failed to create storage ${config.name}`, error);
      this.emit(StorageManagerEvent.ERROR, error);
      throw new ProxmoxStorageError(`Failed to create storage ${config.name}`, { cause: error });
    }
  }

  /**
   * Update storage
   */
  async updateStorage(storageId: string, config: Partial<StorageConfig>): Promise<StorageInfo> {
    try {
      logger.info(`Updating storage ${storageId}`);

      // Prepare storage configuration
      const storageConfig: Record<string, any> = {};

      // Add content types
      if (config.content && config.content.length > 0) {
        storageConfig.content = config.content.join(',');
      }

      // Add other options
      if (config.enabled !== undefined) {
        storageConfig.disable = config.enabled ? 0 : 1;
      }

      if (config.mountOnBoot !== undefined) {
        storageConfig.shared = config.mountOnBoot ? 1 : 0;
      }

      // Add additional options
      if (config.options) {
        Object.assign(storageConfig, config.options);
      }

      // Update storage
      await this.api.put(`/storage/${storageId}`, storageConfig);

      // Get storage info
      const storageInfo = await this.getStorage(storageId);
      this.emit(StorageManagerEvent.STORAGE_UPDATED, storageInfo);
      return storageInfo;
    } catch (error) {
      logger.error(`Failed to update storage ${storageId}`, error);
      this.emit(StorageManagerEvent.ERROR, error);
      throw new ProxmoxStorageError(`Failed to update storage ${storageId}`, { cause: error });
    }
  }

  /**
   * Delete storage
   */
  async deleteStorage(storageId: string): Promise<void> {
    try {
      logger.info(`Deleting storage ${storageId}`);

      // Get storage info before deleting
      const storageInfo = await this.getStorage(storageId);

      // Delete storage
      await this.api.delete(`/storage/${storageId}`);

      this.emit(StorageManagerEvent.STORAGE_DELETED, storageInfo);
    } catch (error) {
      logger.error(`Failed to delete storage ${storageId}`, error);
      this.emit(StorageManagerEvent.ERROR, error);
      throw new ProxmoxStorageError(`Failed to delete storage ${storageId}`, { cause: error });
    }
  }

  /**
   * List storage volumes
   */
  async listVolumes(node: string, storage: string, content?: string): Promise<StorageVolume[]> {
    try {
      logger.info(`Listing volumes for storage ${storage} on node ${node}`);

      // Build query parameters
      const params: Record<string, any> = {};
      
      if (content) {
        params.content = content;
      }

      // Make API request
      const response = await this.api.get(`/nodes/${node}/storage/${storage}/content`, {
        params,
      });

      const volumes = response.data.data;

      return volumes.map((v: any) => ({
        id: v.volid,
        name: v.volid.split('/').pop(),
        format: v.format,
        size: v.size,
        used: v.used,
        content: v.content,
        vmid: v.vmid,
      }));
    } catch (error) {
      logger.error(`Failed to list volumes for storage ${storage} on node ${node}`, error);
      this.emit(StorageManagerEvent.ERROR, error);
      throw new ProxmoxStorageError(`Failed to list volumes for storage ${storage} on node ${node}`, { cause: error });
    }
  }

  /**
   * Delete storage volume
   */
  async deleteVolume(node: string, storage: string, volume: string): Promise<void> {
    try {
      logger.info(`Deleting volume ${volume} from storage ${storage} on node ${node}`);

      await this.api.delete(`/nodes/${node}/storage/${storage}/content/${volume}`);
    } catch (error) {
      logger.error(`Failed to delete volume ${volume} from storage ${storage} on node ${node}`, error);
      this.emit(StorageManagerEvent.ERROR, error);
      throw new ProxmoxStorageError(`Failed to delete volume ${volume} from storage ${storage} on node ${node}`, { cause: error });
    }
  }
}
