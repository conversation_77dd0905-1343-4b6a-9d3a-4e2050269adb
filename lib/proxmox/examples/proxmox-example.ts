/**
 * Proxmox API Example
 *
 * This file demonstrates how to use the Proxmox API library.
 */

import { ProxmoxClient } from '../api/proxmox-client';
import { VMStatus } from '../types';
import logger from '../utils/logger';

/**
 * Example function to demonstrate the Proxmox API library
 */
async function proxmoxExample() {
  try {
    // Create a Proxmox client
    const client = new ProxmoxClient({
      apiUrl: process.env.PROXMOX_API_URL || 'https://your-proxmox-server:8006',
      credentials: {
        username: process.env.PROXMOX_USERNAME || 'root',
        password: process.env.PROXMOX_PASSWORD,
        token: process.env.PROXMOX_TOKEN,
        tokenId: process.env.PROXMOX_TOKEN_ID,
        realm: process.env.PROXMOX_REALM || 'pam',
      },
      verifySSL: process.env.PROXMOX_VERIFY_SSL !== 'false',
    });

    // Initialize the client
    await client.initialize();
    logger.info('Proxmox client initialized');

    // Get cluster status
    const clusterStatus = await client.cluster.getClusterStatus();
    logger.info('Cluster status:', clusterStatus);

    // List nodes
    const nodes = await client.cluster.listNodes();
    logger.info(`Found ${nodes.length} nodes`);

    if (nodes.length === 0) {
      logger.error('No nodes found');
      return;
    }

    // Get the first node
    const node = nodes[0];
    logger.info(`Using node ${node.name}`);

    // List VMs
    const vms = await client.vm.listVMs(node.name);
    logger.info(`Found ${vms.length} VMs on node ${node.name}`);

    // Create a new VM
    const newVm = await client.vm.createVM({
      node: node.name,
      vmid: 100,
      name: 'test-vm',
      description: 'Test VM created by Proxmox API',
      cores: 1,
      memory: 512,
      disks: [
        {
          storage: 'local',
          size: 8,
          format: 'qcow2',
        },
      ],
      networks: [
        {
          model: 'virtio',
          bridge: 'vmbr0',
        },
      ],
      onBoot: true,
      start: true,
    });

    logger.info(`Created VM ${newVm.name} with ID ${newVm.id}`);

    // Wait for VM to start
    logger.info(`Waiting for VM ${newVm.id} to start...`);
    let vmInfo = await client.vm.getVM(node.name, newVm.id);
    
    while (vmInfo.status !== VMStatus.RUNNING) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      vmInfo = await client.vm.getVM(node.name, newVm.id);
    }
    
    logger.info(`VM ${newVm.id} is now running`);

    // Create a snapshot
    const snapshot = await client.backup.createSnapshot(node.name, newVm.id, {
      name: 'test-snapshot',
      description: 'Test snapshot created by Proxmox API',
      vmstate: true,
    });

    logger.info(`Created snapshot ${snapshot.name} for VM ${newVm.id}`);

    // Stop the VM
    await client.vm.stopVM(node.name, newVm.id);
    logger.info(`Stopped VM ${newVm.id}`);

    // Wait for VM to stop
    logger.info(`Waiting for VM ${newVm.id} to stop...`);
    vmInfo = await client.vm.getVM(node.name, newVm.id);
    
    while (vmInfo.status !== VMStatus.STOPPED) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      vmInfo = await client.vm.getVM(node.name, newVm.id);
    }
    
    logger.info(`VM ${newVm.id} is now stopped`);

    // Rollback to snapshot
    await client.backup.rollbackSnapshot(node.name, newVm.id, 'test-snapshot');
    logger.info(`Rolled back VM ${newVm.id} to snapshot test-snapshot`);

    // Start the VM
    await client.vm.startVM(node.name, newVm.id);
    logger.info(`Started VM ${newVm.id}`);

    // Wait for VM to start
    logger.info(`Waiting for VM ${newVm.id} to start...`);
    vmInfo = await client.vm.getVM(node.name, newVm.id);
    
    while (vmInfo.status !== VMStatus.RUNNING) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      vmInfo = await client.vm.getVM(node.name, newVm.id);
    }
    
    logger.info(`VM ${newVm.id} is now running`);

    // Delete the snapshot
    await client.backup.deleteSnapshot(node.name, newVm.id, 'test-snapshot');
    logger.info(`Deleted snapshot test-snapshot for VM ${newVm.id}`);

    // Stop and delete the VM
    await client.vm.stopVM(node.name, newVm.id);
    logger.info(`Stopped VM ${newVm.id}`);

    // Wait for VM to stop
    logger.info(`Waiting for VM ${newVm.id} to stop...`);
    vmInfo = await client.vm.getVM(node.name, newVm.id);
    
    while (vmInfo.status !== VMStatus.STOPPED) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      vmInfo = await client.vm.getVM(node.name, newVm.id);
    }
    
    logger.info(`VM ${newVm.id} is now stopped`);

    // Delete the VM
    await client.vm.deleteVM(node.name, newVm.id);
    logger.info(`Deleted VM ${newVm.id}`);

    // Logout
    await client.logout();
    logger.info('Logged out from Proxmox API');
  } catch (error) {
    logger.error('Proxmox example error:', error);
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  proxmoxExample().catch(error => {
    logger.error('Proxmox example failed:', error);
    process.exit(1);
  });
}

export default proxmoxExample;
