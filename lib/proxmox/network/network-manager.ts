/**
 * Network Manager
 *
 * This module provides functionality for managing Proxmox networks.
 */

import { EventEmitter } from 'events';
import { ProxmoxApi } from '../api/proxmox-api';
import { ProxmoxNetworkError } from '../utils/error-handler';
import logger from '../utils/logger';

/**
 * Network manager events
 */
export enum NetworkManagerEvent {
  NETWORK_CREATED = 'network:created',
  NETWORK_UPDATED = 'network:updated',
  NETWORK_DELETED = 'network:deleted',
  ERROR = 'error',
}

/**
 * Network interface type
 */
export enum NetworkInterfaceType {
  BRIDGE = 'bridge',
  BOND = 'bond',
  VLAN = 'vlan',
  OVS = 'OVSBridge',
  OVS_BOND = 'OVSBond',
  OVS_INTPORT = 'OVSIntPort',
  OVS_PORT = 'OVSPort',
}

/**
 * Network interface information
 */
export interface NetworkInterface {
  /**
   * Interface name
   */
  name: string;

  /**
   * Interface type
   */
  type: NetworkInterfaceType;

  /**
   * Interface IP address
   */
  address?: string;

  /**
   * Interface netmask
   */
  netmask?: string;

  /**
   * Interface gateway
   */
  gateway?: string;

  /**
   * Interface bridge ports
   */
  bridge_ports?: string[];

  /**
   * Interface VLAN ID
   */
  vlan_id?: number;

  /**
   * Interface VLAN raw device
   */
  vlan_raw_device?: string;

  /**
   * Interface bond slaves
   */
  bond_slaves?: string[];

  /**
   * Interface bond mode
   */
  bond_mode?: string;

  /**
   * Interface OVS options
   */
  ovs_options?: string;

  /**
   * Interface OVS tag
   */
  ovs_tag?: number;

  /**
   * Interface OVS bridge
   */
  ovs_bridge?: string;

  /**
   * Interface active
   */
  active?: boolean;

  /**
   * Interface autostart
   */
  autostart?: boolean;

  /**
   * Interface comments
   */
  comments?: string;
}

/**
 * Network interface configuration
 */
export interface NetworkInterfaceConfig {
  /**
   * Interface name
   */
  name: string;

  /**
   * Interface type
   */
  type: NetworkInterfaceType;

  /**
   * Interface IP address
   */
  address?: string;

  /**
   * Interface netmask
   */
  netmask?: string;

  /**
   * Interface gateway
   */
  gateway?: string;

  /**
   * Interface bridge ports
   */
  bridge_ports?: string[];

  /**
   * Interface VLAN ID
   */
  vlan_id?: number;

  /**
   * Interface VLAN raw device
   */
  vlan_raw_device?: string;

  /**
   * Interface bond slaves
   */
  bond_slaves?: string[];

  /**
   * Interface bond mode
   */
  bond_mode?: string;

  /**
   * Interface OVS options
   */
  ovs_options?: string;

  /**
   * Interface OVS tag
   */
  ovs_tag?: number;

  /**
   * Interface OVS bridge
   */
  ovs_bridge?: string;

  /**
   * Interface autostart
   */
  autostart?: boolean;

  /**
   * Interface comments
   */
  comments?: string;
}

/**
 * Network manager class
 */
export class NetworkManager extends EventEmitter {
  private api: ProxmoxApi;
  private initialized: boolean = false;

  /**
   * Create a new NetworkManager instance
   */
  constructor(api: ProxmoxApi) {
    super();
    this.api = api;
  }

  /**
   * Initialize the network manager
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Nothing to initialize for now
      this.initialized = true;
      logger.info('Network manager initialized');
    } catch (error) {
      logger.error('Failed to initialize network manager', error);
      throw error;
    }
  }

  /**
   * List network interfaces on a node
   */
  async listInterfaces(node: string): Promise<NetworkInterface[]> {
    try {
      logger.info(`Listing network interfaces on node ${node}`);

      const response = await this.api.get(`/nodes/${node}/network`);
      const interfaces = response.data.data;

      return interfaces.map((iface: any) => {
        // Map interface type
        let type: NetworkInterfaceType;
        switch (iface.type) {
          case 'bridge':
            type = NetworkInterfaceType.BRIDGE;
            break;
          case 'bond':
            type = NetworkInterfaceType.BOND;
            break;
          case 'vlan':
            type = NetworkInterfaceType.VLAN;
            break;
          case 'OVSBridge':
            type = NetworkInterfaceType.OVS;
            break;
          case 'OVSBond':
            type = NetworkInterfaceType.OVS_BOND;
            break;
          case 'OVSIntPort':
            type = NetworkInterfaceType.OVS_INTPORT;
            break;
          case 'OVSPort':
            type = NetworkInterfaceType.OVS_PORT;
            break;
          default:
            type = iface.type as NetworkInterfaceType;
        }

        // Create network interface object
        const networkInterface: NetworkInterface = {
          name: iface.iface,
          type,
          address: iface.address,
          netmask: iface.netmask,
          gateway: iface.gateway,
          active: iface.active,
          autostart: iface.autostart,
          comments: iface.comments,
        };

        // Add type-specific properties
        if (type === NetworkInterfaceType.BRIDGE && iface.bridge_ports) {
          networkInterface.bridge_ports = iface.bridge_ports.split(' ');
        }

        if (type === NetworkInterfaceType.VLAN) {
          networkInterface.vlan_id = iface.vlan_id;
          networkInterface.vlan_raw_device = iface.vlan_raw_device;
        }

        if (type === NetworkInterfaceType.BOND && iface.slaves) {
          networkInterface.bond_slaves = iface.slaves.split(' ');
          networkInterface.bond_mode = iface.bond_mode;
        }

        if (
          type === NetworkInterfaceType.OVS ||
          type === NetworkInterfaceType.OVS_BOND ||
          type === NetworkInterfaceType.OVS_INTPORT ||
          type === NetworkInterfaceType.OVS_PORT
        ) {
          networkInterface.ovs_options = iface.ovs_options;
          networkInterface.ovs_tag = iface.ovs_tag;
          networkInterface.ovs_bridge = iface.ovs_bridge;
        }

        return networkInterface;
      });
    } catch (error) {
      logger.error(`Failed to list network interfaces on node ${node}`, error);
      this.emit(NetworkManagerEvent.ERROR, error);
      throw new ProxmoxNetworkError(`Failed to list network interfaces on node ${node}`, { cause: error });
    }
  }

  /**
   * Get network interface information
   */
  async getInterface(node: string, iface: string): Promise<NetworkInterface> {
    try {
      logger.info(`Getting network interface ${iface} on node ${node}`);

      const interfaces = await this.listInterfaces(node);
      const networkInterface = interfaces.find(i => i.name === iface);

      if (!networkInterface) {
        throw new ProxmoxNetworkError(`Network interface ${iface} not found on node ${node}`);
      }

      return networkInterface;
    } catch (error) {
      logger.error(`Failed to get network interface ${iface} on node ${node}`, error);
      this.emit(NetworkManagerEvent.ERROR, error);
      throw new ProxmoxNetworkError(`Failed to get network interface ${iface} on node ${node}`, { cause: error });
    }
  }

  /**
   * Create network interface
   */
  async createInterface(node: string, config: NetworkInterfaceConfig): Promise<NetworkInterface> {
    try {
      logger.info(`Creating network interface ${config.name} on node ${node}`);

      // Prepare network interface configuration
      const ifaceConfig: Record<string, any> = {
        iface: config.name,
        type: config.type,
      };

      // Add IP configuration
      if (config.address) {
        ifaceConfig.address = config.address;
      }

      if (config.netmask) {
        ifaceConfig.netmask = config.netmask;
      }

      if (config.gateway) {
        ifaceConfig.gateway = config.gateway;
      }

      // Add type-specific configuration
      switch (config.type) {
        case NetworkInterfaceType.BRIDGE:
          if (config.bridge_ports && config.bridge_ports.length > 0) {
            ifaceConfig.bridge_ports = config.bridge_ports.join(' ');
          }
          break;
        case NetworkInterfaceType.VLAN:
          if (config.vlan_id === undefined) {
            throw new ProxmoxNetworkError('VLAN ID is required for VLAN interfaces');
          }
          ifaceConfig.vlan_id = config.vlan_id;

          if (!config.vlan_raw_device) {
            throw new ProxmoxNetworkError('VLAN raw device is required for VLAN interfaces');
          }
          ifaceConfig.vlan_raw_device = config.vlan_raw_device;
          break;
        case NetworkInterfaceType.BOND:
          if (!config.bond_slaves || config.bond_slaves.length === 0) {
            throw new ProxmoxNetworkError('Bond slaves are required for bond interfaces');
          }
          ifaceConfig.slaves = config.bond_slaves.join(' ');

          if (config.bond_mode) {
            ifaceConfig.bond_mode = config.bond_mode;
          }
          break;
        case NetworkInterfaceType.OVS:
        case NetworkInterfaceType.OVS_BOND:
        case NetworkInterfaceType.OVS_INTPORT:
        case NetworkInterfaceType.OVS_PORT:
          if (config.ovs_options) {
            ifaceConfig.ovs_options = config.ovs_options;
          }

          if (config.ovs_tag) {
            ifaceConfig.ovs_tag = config.ovs_tag;
          }

          if (config.ovs_bridge) {
            ifaceConfig.ovs_bridge = config.ovs_bridge;
          }
          break;
      }

      // Add other options
      if (config.autostart !== undefined) {
        ifaceConfig.autostart = config.autostart ? 1 : 0;
      }

      if (config.comments) {
        ifaceConfig.comments = config.comments;
      }

      // Create network interface
      await this.api.post(`/nodes/${node}/network`, ifaceConfig);

      // Apply network configuration
      await this.applyNetworkConfig(node);

      // Get network interface
      const networkInterface = await this.getInterface(node, config.name);
      this.emit(NetworkManagerEvent.NETWORK_CREATED, networkInterface);
      return networkInterface;
    } catch (error) {
      logger.error(`Failed to create network interface ${config.name} on node ${node}`, error);
      this.emit(NetworkManagerEvent.ERROR, error);
      throw new ProxmoxNetworkError(`Failed to create network interface ${config.name} on node ${node}`, { cause: error });
    }
  }

  /**
   * Update network interface
   */
  async updateInterface(node: string, iface: string, config: Partial<NetworkInterfaceConfig>): Promise<NetworkInterface> {
    try {
      logger.info(`Updating network interface ${iface} on node ${node}`);

      // Get current interface configuration
      const currentInterface = await this.getInterface(node, iface);

      // Prepare network interface configuration
      const ifaceConfig: Record<string, any> = {
        iface,
      };

      // Add IP configuration
      if (config.address !== undefined) {
        ifaceConfig.address = config.address;
      }

      if (config.netmask !== undefined) {
        ifaceConfig.netmask = config.netmask;
      }

      if (config.gateway !== undefined) {
        ifaceConfig.gateway = config.gateway;
      }

      // Add type-specific configuration
      if (currentInterface.type === NetworkInterfaceType.BRIDGE && config.bridge_ports !== undefined) {
        ifaceConfig.bridge_ports = config.bridge_ports.join(' ');
      }

      if (currentInterface.type === NetworkInterfaceType.VLAN) {
        if (config.vlan_id !== undefined) {
          ifaceConfig.vlan_id = config.vlan_id;
        }

        if (config.vlan_raw_device !== undefined) {
          ifaceConfig.vlan_raw_device = config.vlan_raw_device;
        }
      }

      if (currentInterface.type === NetworkInterfaceType.BOND) {
        if (config.bond_slaves !== undefined) {
          ifaceConfig.slaves = config.bond_slaves.join(' ');
        }

        if (config.bond_mode !== undefined) {
          ifaceConfig.bond_mode = config.bond_mode;
        }
      }

      if (
        currentInterface.type === NetworkInterfaceType.OVS ||
        currentInterface.type === NetworkInterfaceType.OVS_BOND ||
        currentInterface.type === NetworkInterfaceType.OVS_INTPORT ||
        currentInterface.type === NetworkInterfaceType.OVS_PORT
      ) {
        if (config.ovs_options !== undefined) {
          ifaceConfig.ovs_options = config.ovs_options;
        }

        if (config.ovs_tag !== undefined) {
          ifaceConfig.ovs_tag = config.ovs_tag;
        }

        if (config.ovs_bridge !== undefined) {
          ifaceConfig.ovs_bridge = config.ovs_bridge;
        }
      }

      // Add other options
      if (config.autostart !== undefined) {
        ifaceConfig.autostart = config.autostart ? 1 : 0;
      }

      if (config.comments !== undefined) {
        ifaceConfig.comments = config.comments;
      }

      // Update network interface
      await this.api.put(`/nodes/${node}/network/${iface}`, ifaceConfig);

      // Apply network configuration
      await this.applyNetworkConfig(node);

      // Get updated network interface
      const networkInterface = await this.getInterface(node, iface);
      this.emit(NetworkManagerEvent.NETWORK_UPDATED, networkInterface);
      return networkInterface;
    } catch (error) {
      logger.error(`Failed to update network interface ${iface} on node ${node}`, error);
      this.emit(NetworkManagerEvent.ERROR, error);
      throw new ProxmoxNetworkError(`Failed to update network interface ${iface} on node ${node}`, { cause: error });
    }
  }

  /**
   * Delete network interface
   */
  async deleteInterface(node: string, iface: string): Promise<void> {
    try {
      logger.info(`Deleting network interface ${iface} on node ${node}`);

      // Get interface info before deleting
      const networkInterface = await this.getInterface(node, iface);

      // Delete network interface
      await this.api.delete(`/nodes/${node}/network/${iface}`);

      // Apply network configuration
      await this.applyNetworkConfig(node);

      this.emit(NetworkManagerEvent.NETWORK_DELETED, networkInterface);
    } catch (error) {
      logger.error(`Failed to delete network interface ${iface} on node ${node}`, error);
      this.emit(NetworkManagerEvent.ERROR, error);
      throw new ProxmoxNetworkError(`Failed to delete network interface ${iface} on node ${node}`, { cause: error });
    }
  }

  /**
   * Apply network configuration
   */
  private async applyNetworkConfig(node: string): Promise<void> {
    try {
      logger.info(`Applying network configuration on node ${node}`);

      await this.api.put(`/nodes/${node}/network`);
    } catch (error) {
      logger.error(`Failed to apply network configuration on node ${node}`, error);
      throw new ProxmoxNetworkError(`Failed to apply network configuration on node ${node}`, { cause: error });
    }
  }
}
