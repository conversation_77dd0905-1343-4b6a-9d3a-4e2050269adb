/**
 * Task Manager
 *
 * This module provides functionality for managing Proxmox tasks.
 */

import { EventEmitter } from 'events';
import { ProxmoxApi } from '../api/proxmox-api';
import { TaskInfo, TaskStatus } from '../types';
import { ProxmoxTaskError } from '../utils/error-handler';
import logger from '../utils/logger';

/**
 * Task manager events
 */
export enum TaskManagerEvent {
  TASK_STARTED = 'task:started',
  TASK_COMPLETED = 'task:completed',
  TASK_FAILED = 'task:failed',
  ERROR = 'error',
}

/**
 * Task filter options
 */
export interface TaskFilterOptions {
  /**
   * Start index
   */
  start?: number;

  /**
   * Limit
   */
  limit?: number;

  /**
   * VM ID
   */
  vmid?: string;

  /**
   * Only running tasks
   */
  running?: boolean;

  /**
   * Task type
   */
  type?: string;

  /**
   * Task errors only
   */
  errors?: boolean;

  /**
   * Task source
   */
  source?: string;

  /**
   * Task user
   */
  userfilter?: string;

  /**
   * Since timestamp
   */
  since?: number;

  /**
   * Until timestamp
   */
  until?: number;
}

/**
 * Task log entry
 */
export interface TaskLogEntry {
  /**
   * Log entry number
   */
  n: number;

  /**
   * Log entry timestamp
   */
  t: number;

  /**
   * Log entry level
   */
  level: string;

  /**
   * Log entry message
   */
  message: string;
}

/**
 * Task manager class
 */
export class TaskManager extends EventEmitter {
  private api: ProxmoxApi;
  private initialized: boolean = false;
  private taskCache: Map<string, TaskStatus> = new Map();
  private taskMonitorInterval: NodeJS.Timeout | null = null;

  /**
   * Create a new TaskManager instance
   */
  constructor(api: ProxmoxApi) {
    super();
    this.api = api;
  }

  /**
   * Initialize the task manager
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Nothing to initialize for now
      this.initialized = true;
      logger.info('Task manager initialized');
    } catch (error) {
      logger.error('Failed to initialize task manager', error);
      throw error;
    }
  }

  /**
   * List tasks
   */
  async listTasks(node: string, options: TaskFilterOptions = {}): Promise<TaskInfo[]> {
    try {
      logger.info(`Listing tasks on node ${node}`);

      // Build query parameters
      const params: Record<string, any> = {};
      
      if (options.start !== undefined) {
        params.start = options.start;
      }
      
      if (options.limit !== undefined) {
        params.limit = options.limit;
      }
      
      if (options.vmid !== undefined) {
        params.vmid = options.vmid;
      }
      
      if (options.running !== undefined) {
        params.running = options.running ? 1 : 0;
      }
      
      if (options.type !== undefined) {
        params.type = options.type;
      }
      
      if (options.errors !== undefined) {
        params.errors = options.errors ? 1 : 0;
      }
      
      if (options.source !== undefined) {
        params.source = options.source;
      }
      
      if (options.userfilter !== undefined) {
        params.userfilter = options.userfilter;
      }
      
      if (options.since !== undefined) {
        params.since = options.since;
      }
      
      if (options.until !== undefined) {
        params.until = options.until;
      }

      // Make API request
      const response = await this.api.get(`/nodes/${node}/tasks`, {
        params,
      });

      const tasks = response.data.data;

      return tasks.map((task: any) => {
        // Map task status
        let status: TaskStatus;
        if (task.status === 'running') {
          status = TaskStatus.RUNNING;
        } else if (task.status === 'stopped' && task.exitstatus === 'OK') {
          status = TaskStatus.COMPLETED;
        } else if (task.status === 'stopped') {
          status = TaskStatus.FAILED;
        } else {
          status = TaskStatus.UNKNOWN;
        }

        // Check if status has changed
        const previousStatus = this.taskCache.get(task.upid);
        if (previousStatus && previousStatus !== status) {
          if (status === TaskStatus.COMPLETED) {
            this.emit(TaskManagerEvent.TASK_COMPLETED, {
              id: task.upid,
              type: task.type,
              status,
              node,
            });
          } else if (status === TaskStatus.FAILED) {
            this.emit(TaskManagerEvent.TASK_FAILED, {
              id: task.upid,
              type: task.type,
              status,
              node,
              error: task.exitstatus,
            });
          }
        } else if (!previousStatus && status === TaskStatus.RUNNING) {
          this.emit(TaskManagerEvent.TASK_STARTED, {
            id: task.upid,
            type: task.type,
            status,
            node,
          });
        }

        // Update task cache
        this.taskCache.set(task.upid, status);

        // Create task info object
        return {
          id: task.upid,
          type: task.type,
          status,
          startTime: new Date(task.starttime * 1000),
          endTime: task.endtime ? new Date(task.endtime * 1000) : undefined,
          node: task.node,
          user: task.user,
          description: task.desc,
        };
      });
    } catch (error) {
      logger.error(`Failed to list tasks on node ${node}`, error);
      this.emit(TaskManagerEvent.ERROR, error);
      throw new ProxmoxTaskError(`Failed to list tasks on node ${node}`, { cause: error });
    }
  }

  /**
   * Get task status
   */
  async getTaskStatus(node: string, taskId: string): Promise<TaskInfo> {
    try {
      logger.info(`Getting task status for ${taskId} on node ${node}`);

      const response = await this.api.get(`/nodes/${node}/tasks/${taskId}/status`);
      const task = response.data.data;

      // Map task status
      let status: TaskStatus;
      if (task.status === 'running') {
        status = TaskStatus.RUNNING;
      } else if (task.status === 'stopped' && task.exitstatus === 'OK') {
        status = TaskStatus.COMPLETED;
      } else if (task.status === 'stopped') {
        status = TaskStatus.FAILED;
      } else {
        status = TaskStatus.UNKNOWN;
      }

      // Check if status has changed
      const previousStatus = this.taskCache.get(taskId);
      if (previousStatus && previousStatus !== status) {
        if (status === TaskStatus.COMPLETED) {
          this.emit(TaskManagerEvent.TASK_COMPLETED, {
            id: taskId,
            type: task.type,
            status,
            node,
          });
        } else if (status === TaskStatus.FAILED) {
          this.emit(TaskManagerEvent.TASK_FAILED, {
            id: taskId,
            type: task.type,
            status,
            node,
            error: task.exitstatus,
          });
        }
      } else if (!previousStatus && status === TaskStatus.RUNNING) {
        this.emit(TaskManagerEvent.TASK_STARTED, {
          id: taskId,
          type: task.type,
          status,
          node,
        });
      }

      // Update task cache
      this.taskCache.set(taskId, status);

      // Create task info object
      return {
        id: taskId,
        type: task.type,
        status,
        startTime: new Date(task.starttime * 1000),
        endTime: task.endtime ? new Date(task.endtime * 1000) : undefined,
        node,
        user: task.user,
        description: task.desc,
      };
    } catch (error) {
      logger.error(`Failed to get task status for ${taskId} on node ${node}`, error);
      this.emit(TaskManagerEvent.ERROR, error);
      throw new ProxmoxTaskError(`Failed to get task status for ${taskId} on node ${node}`, { cause: error });
    }
  }

  /**
   * Get task log
   */
  async getTaskLog(node: string, taskId: string, options: {
    start?: number;
    limit?: number;
    download?: boolean;
  } = {}): Promise<TaskLogEntry[]> {
    try {
      logger.info(`Getting task log for ${taskId} on node ${node}`);

      // Build query parameters
      const params: Record<string, any> = {};
      
      if (options.start !== undefined) {
        params.start = options.start;
      }
      
      if (options.limit !== undefined) {
        params.limit = options.limit;
      }
      
      if (options.download !== undefined) {
        params.download = options.download ? 1 : 0;
      }

      // Make API request
      const response = await this.api.get(`/nodes/${node}/tasks/${taskId}/log`, {
        params,
      });

      return response.data.data;
    } catch (error) {
      logger.error(`Failed to get task log for ${taskId} on node ${node}`, error);
      this.emit(TaskManagerEvent.ERROR, error);
      throw new ProxmoxTaskError(`Failed to get task log for ${taskId} on node ${node}`, { cause: error });
    }
  }

  /**
   * Stop a task
   */
  async stopTask(node: string, taskId: string): Promise<void> {
    try {
      logger.info(`Stopping task ${taskId} on node ${node}`);

      await this.api.delete(`/nodes/${node}/tasks/${taskId}`);
    } catch (error) {
      logger.error(`Failed to stop task ${taskId} on node ${node}`, error);
      this.emit(TaskManagerEvent.ERROR, error);
      throw new ProxmoxTaskError(`Failed to stop task ${taskId} on node ${node}`, { cause: error });
    }
  }

  /**
   * Start task monitoring
   */
  startTaskMonitoring(node: string, interval: number = 5000): void {
    if (this.taskMonitorInterval) {
      this.stopTaskMonitoring();
    }

    logger.info(`Starting task monitoring on node ${node} with interval ${interval}ms`);

    this.taskMonitorInterval = setInterval(async () => {
      try {
        await this.listTasks(node, { running: true });
      } catch (error) {
        logger.error(`Task monitoring error on node ${node}`, error);
        this.emit(TaskManagerEvent.ERROR, error);
      }
    }, interval);
  }

  /**
   * Stop task monitoring
   */
  stopTaskMonitoring(): void {
    if (this.taskMonitorInterval) {
      clearInterval(this.taskMonitorInterval);
      this.taskMonitorInterval = null;
      logger.info('Stopped task monitoring');
    }
  }

  /**
   * Wait for a task to complete
   */
  async waitForTask(node: string, taskId: string, timeout: number = 60000): Promise<TaskInfo> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const taskInfo = await this.getTaskStatus(node, taskId);
        
        if (taskInfo.status === TaskStatus.COMPLETED || taskInfo.status === TaskStatus.FAILED) {
          return taskInfo;
        }
        
        // Wait before checking again
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        logger.warn(`Failed to check task ${taskId} status`, error);
        // Continue waiting
      }
    }
    
    throw new ProxmoxTaskError(`Task ${taskId} timed out after ${timeout}ms`);
  }
}
