/**
 * Proxmox Client
 *
 * This module provides a high-level client for interacting with the Proxmox API.
 * It includes methods for managing VMs, clusters, storage, networks, backups, and tasks.
 */

import { ProxmoxApi, ProxmoxApiOptions } from './proxmox-api';
import { VMManager } from '../vm/vm-manager';
import { ClusterManager } from '../cluster/cluster-manager';
import { StorageManager } from '../storage/storage-manager';
import { NetworkManager } from '../network/network-manager';
import { BackupManager } from '../backup/backup-manager';
import { TaskManager } from '../tasks/task-manager';
import logger from '../utils/logger';

/**
 * Proxmox client options
 */
export interface ProxmoxClientOptions extends ProxmoxApiOptions {
  /**
   * Whether to automatically initialize the client
   */
  autoInitialize?: boolean;
}

/**
 * Proxmox client class
 */
export class ProxmoxClient {
  /**
   * Low-level API client
   */
  readonly api: ProxmoxApi;

  /**
   * VM manager
   */
  readonly vm: VMManager;

  /**
   * Cluster manager
   */
  readonly cluster: ClusterManager;

  /**
   * Storage manager
   */
  readonly storage: StorageManager;

  /**
   * Network manager
   */
  readonly network: NetworkManager;

  /**
   * Backup manager
   */
  readonly backup: BackupManager;

  /**
   * Task manager
   */
  readonly task: TaskManager;

  private readonly options: ProxmoxClientOptions;
  private initialized: boolean = false;
  private initPromise: Promise<void> | null = null;

  /**
   * Create a new ProxmoxClient instance
   */
  constructor(options: ProxmoxClientOptions) {
    this.options = {
      autoInitialize: true,
      ...options,
    };

    // Create API client
    this.api = new ProxmoxApi({
      ...options,
      autoInitialize: false, // We'll initialize it ourselves
    });

    // Create managers
    this.vm = new VMManager(this.api);
    this.cluster = new ClusterManager(this.api);
    this.storage = new StorageManager(this.api);
    this.network = new NetworkManager(this.api);
    this.backup = new BackupManager(this.api);
    this.task = new TaskManager(this.api);

    // Auto-initialize if enabled
    if (this.options.autoInitialize) {
      this.initialize();
    }
  }

  /**
   * Initialize the client
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = (async () => {
      try {
        // Initialize API client
        await this.api.initialize();

        // Initialize managers
        await Promise.all([
          this.vm.initialize(),
          this.cluster.initialize(),
          this.storage.initialize(),
          this.network.initialize(),
          this.backup.initialize(),
          this.task.initialize(),
        ]);

        this.initialized = true;
        logger.info('Proxmox client initialized');
      } catch (error) {
        logger.error('Failed to initialize Proxmox client', error);
        throw error;
      } finally {
        this.initPromise = null;
      }
    })();

    return this.initPromise;
  }

  /**
   * Check if the client is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    return this.api.isAuthenticated();
  }

  /**
   * Logout from the Proxmox API
   */
  async logout(): Promise<void> {
    await this.api.logout();
    this.initialized = false;
  }
}
