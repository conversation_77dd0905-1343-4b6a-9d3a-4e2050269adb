/**
 * Proxmox API Client
 *
 * A comprehensive client for interacting with the Proxmox API
 * with built-in error handling, retries, and type safety.
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import https from 'https';
import { createNamespacedLogger } from '@/lib/logger';
import { Server } from '../../../types/server';
import { ProxmoxTicket } from '../../../types/proxmox';

// Create a logger for this module
const logger = createNamespacedLogger('proxmox-client');

// Constants
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // ms
const REQUEST_TIMEOUT = 30000; // ms

export interface ProxmoxClientOptions {
  server: Server;
  ticket?: ProxmoxTicket;
  timeout?: number;
  maxRetries?: number;
  retryDelay?: number;
  disableSSLVerification?: boolean;
}

export class ProxmoxClient {
  private axiosInstance: AxiosInstance;
  private server: Server;
  private ticket?: ProxmoxTicket;
  private maxRetries: number;
  private retryDelay: number;

  /**
   * Create a new Proxmox API client
   */
  constructor(options: ProxmoxClientOptions) {
    this.server = options.server;
    this.ticket = options.ticket;
    this.maxRetries = options.maxRetries || MAX_RETRIES;
    this.retryDelay = options.retryDelay || RETRY_DELAY;

    // Create axios instance with custom config
    this.axiosInstance = axios.create({
      baseURL: `${this.server.url}/api2/json`,
      timeout: options.timeout || REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
      // Disable SSL verification if requested
      httpsAgent: options.disableSSLVerification
        ? new https.Agent({ rejectUnauthorized: false })
        : undefined,
    });

    // Add request interceptor for authentication
    this.axiosInstance.interceptors.request.use((config) => {
      // Add authentication headers if ticket is available
      if (this.ticket) {
        // Set cookie header with ticket
        config.headers['Cookie'] = `PVEAuthCookie=${this.ticket.ticket}`;

        // Set CSRF token header for non-GET requests
        if (config.method !== 'get' && this.ticket.csrfToken) {
          config.headers['CSRFPreventionToken'] = this.ticket.csrfToken;
        }
      }

      return config;
    });

    // Add response interceptor for logging
    this.axiosInstance.interceptors.response.use(
      (response) => {
        logger.debug('Proxmox API response', {
          url: response.config.url,
          status: response.status,
          data: response.data
        });
        return response;
      },
      (error) => {
        logger.error('Proxmox API error', {
          url: error.config?.url,
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Set the authentication ticket
   */
  setTicket(ticket: ProxmoxTicket): void {
    this.ticket = ticket;
    logger.debug('Set authentication ticket', {
      hasTicket: !!ticket.ticket,
      hasCSRF: !!ticket.csrfToken
    });
  }

  /**
   * Make a request to the Proxmox API with retry logic
   */
  async request<T = any>(
    method: string,
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig,
    retryCount = 0
  ): Promise<T> {
    try {
      const requestConfig: AxiosRequestConfig = {
        method,
        url: endpoint,
        ...config,
      };

      // Add data for non-GET requests
      if (method.toLowerCase() !== 'get' && data) {
        requestConfig.data = data;
      }
      // Add params for GET requests
      else if (method.toLowerCase() === 'get' && data) {
        requestConfig.params = data;
      }

      logger.debug('Making Proxmox API request', {
        method,
        endpoint,
        retryCount
      });

      const response: AxiosResponse = await this.axiosInstance.request(requestConfig);

      // Handle Proxmox error responses
      if (response.data && response.data.data && response.data.data.errors) {
        const error = new Error(
          `Proxmox API error: ${JSON.stringify(response.data.data.errors)}`
        );
        throw error;
      }

      // Return the data property if it exists
      return response.data?.data || response.data;

    } catch (error: any) {
      // Check if we should retry
      if (
        retryCount < this.maxRetries &&
        (
          // Network errors
          error.code === 'ECONNABORTED' ||
          error.code === 'ETIMEDOUT' ||
          // Rate limiting or temporary server errors
          (error.response && (error.response.status >= 429 || error.response.status >= 500))
        )
      ) {
        logger.warn(`Retrying Proxmox API request (${retryCount + 1}/${this.maxRetries})`, {
          method,
          endpoint,
          error: error.message
        });

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));

        // Retry the request
        return this.request(method, endpoint, data, config, retryCount + 1);
      }

      // Authentication errors - special handling
      if (error.response && error.response.status === 401) {
        logger.error('Authentication failed for Proxmox API request', {
          method,
          endpoint,
          status: error.response.status
        });
        throw new Error('Authentication failed. Please log in again.');
      }

      // Rethrow the error
      logger.error('Proxmox API request failed', {
        method,
        endpoint,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });

      throw error;
    }
  }

  /**
   * Get a list of nodes in the cluster
   */
  async getNodes() {
    return this.request<any[]>('get', '/nodes');
  }

  /**
   * Get a list of VMs on a specific node
   */
  async getVMs(node: string) {
    return this.request<any[]>('get', `/nodes/${node}/qemu`);
  }

  /**
   * Get detailed information about a VM
   */
  async getVM(node: string, vmid: string) {
    return this.request<any>('get', `/nodes/${node}/qemu/${vmid}/status/current`);
  }

  /**
   * Get VM by ID (searches across all nodes)
   */
  async getVm(vmid: string) {
    try {
      // Get all nodes
      const nodes = await this.getNodes();

      // Check each node for the VM
      for (const node of nodes) {
        try {
          const vm = await this.getVM(node.node, vmid);
          if (vm) {
            return { ...vm, node: node.node };
          }
        } catch (error) {
          // VM not found on this node, continue to next node
          continue;
        }
      }

      // VM not found on any node
      return null;
    } catch (error) {
      logger.error(`Error finding VM ${vmid}:`, { error });
      throw error;
    }
  }

  /**
   * Get VM configuration
   */
  async getVMConfig(node: string, vmid: string) {
    return this.request<any>('get', `/nodes/${node}/qemu/${vmid}/config`);
  }

  /**
   * Start a VM
   */
  async startVM(node: string, vmid: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/status/start`);
  }

  /**
   * Stop a VM
   */
  async stopVm(node: string, vmid: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/status/stop`);
  }

  /**
   * Suspend a VM
   */
  async suspendVm(node: string, vmid: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/status/suspend`);
  }

  /**
   * Shutdown a VM (graceful stop)
   */
  async shutdownVM(node: string, vmid: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/status/shutdown`);
  }

  /**
   * Reset a VM
   */
  async resetVM(node: string, vmid: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/status/reset`);
  }

  /**
   * Suspend a VM
   */
  async suspendVM(node: string, vmid: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/status/suspend`);
  }

  /**
   * Resume a VM
   */
  async resumeVM(node: string, vmid: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/status/resume`);
  }

  /**
   * Get VNC connection details for a VM
   */
  async getVNCProxy(node: string, vmid: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/vncproxy`, {
      websocket: 1,
    });
  }

  /**
   * Get SPICE connection details for a VM
   */
  async getSPICEProxy(node: string, vmid: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/spiceproxy`);
  }

  /**
   * Create a new VM
   */
  async createVM(node: string, vmConfig: any) {
    return this.request<any>('post', `/nodes/${node}/qemu`, vmConfig);
  }

  /**
   * Update VM configuration
   */
  async updateVMConfig(node: string, vmid: string, vmConfig: any) {
    return this.request<any>('put', `/nodes/${node}/qemu/${vmid}/config`, vmConfig);
  }

  /**
   * Delete a VM
   */
  async deleteVM(node: string, vmid: string, purge = true) {
    return this.request<any>('delete', `/nodes/${node}/qemu/${vmid}`, {
      purge,
    });
  }

  /**
   * Clone a VM
   */
  async cloneVM(node: string, vmid: string, cloneParams: any) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/clone`, cloneParams);
  }

  /**
   * Get VM snapshots
   */
  async getSnapshots(node: string, vmid: string) {
    return this.request<any[]>('get', `/nodes/${node}/qemu/${vmid}/snapshot`);
  }

  /**
   * Create a VM snapshot
   */
  async createSnapshot(node: string, vmid: string, snapshotName: string, description?: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/snapshot`, {
      snapname: snapshotName,
      description: description || `Snapshot created at ${new Date().toISOString()}`,
    });
  }

  /**
   * Restore a VM snapshot
   */
  async restoreSnapshot(node: string, vmid: string, snapshotName: string) {
    return this.request<any>('post', `/nodes/${node}/qemu/${vmid}/snapshot/${snapshotName}/rollback`);
  }

  /**
   * Delete a VM snapshot
   */
  async deleteSnapshot(node: string, vmid: string, snapshotName: string) {
    return this.request<any>('delete', `/nodes/${node}/qemu/${vmid}/snapshot/${snapshotName}`);
  }

  /**
   * Get VM backup configuration
   */
  async getBackupConfig() {
    return this.request<any>('get', '/cluster/backup');
  }

  /**
   * Get storage information
   */
  async getStorage(node: string) {
    return this.request<any[]>('get', `/nodes/${node}/storage`);
  }

  /**
   * Get storage content
   */
  async getStorageContent(node: string, storage: string) {
    return this.request<any[]>('get', `/nodes/${node}/storage/${storage}/content`);
  }

  /**
   * Get cluster resources
   */
  async getResources(resourceType?: string) {
    const params: any = {};
    if (resourceType) {
      params.type = resourceType;
    }
    return this.request<any[]>('get', '/cluster/resources', params);
  }

  /**
   * Get cluster status
   */
  async getClusterStatus() {
    return this.request<any>('get', '/cluster/status');
  }

  /**
   * Get node status
   */
  async getNodeStatus(node: string) {
    return this.request<any>('get', `/nodes/${node}/status`);
  }

  /**
   * Get node tasks
   */
  async getNodeTasks(node: string, limit = 50) {
    return this.request<any[]>('get', `/nodes/${node}/tasks`, {
      limit,
    });
  }

  /**
   * Get task status
   */
  async getTaskStatus(node: string, upid: string) {
    return this.request<any>('get', `/nodes/${node}/tasks/${upid}/status`);
  }

  /**
   * Get task log
   */
  async getTaskLog(node: string, upid: string, limit = 500) {
    return this.request<any[]>('get', `/nodes/${node}/tasks/${upid}/log`, {
      limit,
    });
  }

  /**
   * Execute a command on a VM using qemu-guest-agent
   */
  async executeCommand(node: string, vmId: string, options: { command: string, startTimeout: number }) {
    try {
      // This requires qemu-guest-agent to be installed on the VM
      const result = await this.request<any>(
        'post',
        `/nodes/${node}/qemu/${vmId}/agent/exec`,
        {
          command: options.command,
          timeout: options.startTimeout
        }
      );

      // Wait for the command to complete
      const pid = result.pid;
      let commandResult = '';

      if (pid) {
        // Poll for command completion
        let completed = false;
        let attempts = 0;
        const maxAttempts = Math.max(30, options.startTimeout); // At least 30 seconds or the specified timeout

        while (!completed && attempts < maxAttempts) {
          const status = await this.request<any>(
            'get',
            `/nodes/${node}/qemu/${vmId}/agent/exec-status`,
            { pid }
          );

          if (status.exited) {
            completed = true;
            commandResult = status.out || '';

            // Check for error in command execution
            if (status.exitcode !== 0) {
              throw new Error(`Command exited with non-zero status: ${status.exitcode}. Error: ${status.err || 'Unknown error'}`);
            }
          } else {
            // Wait a second before polling again
            await new Promise(resolve => setTimeout(resolve, 1000));
            attempts++;
          }
        }

        if (!completed) {
          throw new Error(`Command execution timed out after ${maxAttempts} seconds`);
        }
      } else {
        throw new Error('Failed to get PID for command execution');
      }

      return commandResult;
    } catch (error) {
      logger.error('Failed to execute command on VM', { error, node, vmId, command: options.command });
      throw error;
    }
  }
}

/**
 * Create a Proxmox client with the given server and ticket
 */
export function createProxmoxClient(server: Server, ticket?: ProxmoxTicket): ProxmoxClient {
  return new ProxmoxClient({
    server,
    ticket,
    disableSSLVerification: true, // For development - should be configurable in production
  });
}
