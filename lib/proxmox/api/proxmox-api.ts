/**
 * Proxmox API
 *
 * This module provides a low-level client for interacting with the Proxmox API.
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ProxmoxAuth, AuthOptions } from '../auth';
import { ProxmoxApiError, handleProxmoxError } from '../utils/error-handler';
import logger from '../utils/logger';

/**
 * Proxmox API options
 */
export interface ProxmoxApiOptions extends AuthOptions {
  /**
   * Base path for API requests
   */
  basePath?: string;

  /**
   * Default request timeout in milliseconds
   */
  timeout?: number;

  /**
   * Whether to verify SSL certificates
   */
  verifySSL?: boolean;

  /**
   * Whether to automatically initialize the API client
   */
  autoInitialize?: boolean;
}

/**
 * Proxmox API class
 */
export class ProxmoxApi {
  private auth: ProxmoxAuth;
  private client: AxiosInstance;
  private apiUrl: string;
  private basePath: string;
  private timeout: number;
  private verifySSL: boolean;
  private initialized: boolean = false;
  private initPromise: Promise<void> | null = null;

  /**
   * Create a new ProxmoxApi instance
   */
  constructor(options: ProxmoxApiOptions) {
    this.apiUrl = options.apiUrl;
    this.basePath = options.basePath || '/api2/json';
    this.timeout = options.timeout || 30000;
    this.verifySSL = options.verifySSL !== false;

    // Create authentication service
    this.auth = new ProxmoxAuth(options);

    // Create axios instance
    this.client = axios.create({
      baseURL: `${this.apiUrl}${this.basePath}`,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Configure SSL verification
    if (!this.verifySSL) {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    }

    // Auto-initialize if enabled
    if (options.autoInitialize !== false) {
      this.initialize();
    }
  }

  /**
   * Initialize the API client
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = (async () => {
      try {
        // Authenticate with the API
        await this.auth.authenticate();
        this.initialized = true;
        logger.info('Proxmox API client initialized');
      } catch (error) {
        logger.error('Failed to initialize Proxmox API client', error);
        throw handleProxmoxError(error);
      } finally {
        this.initPromise = null;
      }
    })();

    return this.initPromise;
  }

  /**
   * Ensure the API client is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * Make a request to the Proxmox API
   */
  async request<T = any>(
    method: string,
    path: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    await this.ensureInitialized();

    try {
      // Get authentication headers
      const authHeaders = await this.auth.getAuthHeaders();

      // Merge headers with config
      const requestConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...authHeaders,
          ...config?.headers,
        },
      };

      // Make the request
      return await this.client.request<T>({
        method,
        url: path,
        data,
        ...requestConfig,
      });
    } catch (error) {
      logger.error(`API request failed: ${method} ${path}`, error);
      throw handleProxmoxError(error);
    }
  }

  /**
   * Make a GET request to the Proxmox API
   */
  async get<T = any>(path: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>('GET', path, undefined, config);
  }

  /**
   * Make a POST request to the Proxmox API
   */
  async post<T = any>(path: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>('POST', path, data, config);
  }

  /**
   * Make a PUT request to the Proxmox API
   */
  async put<T = any>(path: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>('PUT', path, data, config);
  }

  /**
   * Make a DELETE request to the Proxmox API
   */
  async delete<T = any>(path: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>('DELETE', path, undefined, config);
  }

  /**
   * Check if the API client is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    return this.auth.isAuthenticated();
  }

  /**
   * Logout from the Proxmox API
   */
  async logout(): Promise<void> {
    await this.auth.logout();
    this.initialized = false;
  }
}
