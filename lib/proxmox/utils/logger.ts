/**
 * Proxmox API Logger
 *
 * This module provides logging functionality for the Proxmox API.
 */

/**
 * Log levels
 */
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}

/**
 * Logger options
 */
export interface LoggerOptions {
  /**
   * Minimum log level to display
   */
  level?: LogLevel;

  /**
   * Whether to include timestamps in log messages
   */
  timestamps?: boolean;

  /**
   * Custom log handler
   */
  handler?: (level: LogLevel, message: string, ...args: any[]) => void;
}

/**
 * Logger class
 */
class Logger {
  private level: LogLevel;
  private timestamps: boolean;
  private handler?: (level: LogLevel, message: string, ...args: any[]) => void;

  /**
   * Create a new Logger instance
   */
  constructor(options: LoggerOptions = {}) {
    this.level = options.level || LogLevel.INFO;
    this.timestamps = options.timestamps !== false;
    this.handler = options.handler;
  }

  /**
   * Set the log level
   */
  setLevel(level: LogLevel): void {
    this.level = level;
  }

  /**
   * Set the log handler
   */
  setHandler(handler: (level: LogLevel, message: string, ...args: any[]) => void): void {
    this.handler = handler;
  }

  /**
   * Log a debug message
   */
  debug(message: string, ...args: any[]): void {
    this.log(LogLevel.DEBUG, message, ...args);
  }

  /**
   * Log an info message
   */
  info(message: string, ...args: any[]): void {
    this.log(LogLevel.INFO, message, ...args);
  }

  /**
   * Log a warning message
   */
  warn(message: string, ...args: any[]): void {
    this.log(LogLevel.WARN, message, ...args);
  }

  /**
   * Log an error message
   */
  error(message: string, ...args: any[]): void {
    this.log(LogLevel.ERROR, message, ...args);
  }

  /**
   * Log a message with the specified level
   */
  private log(level: LogLevel, message: string, ...args: any[]): void {
    // Check if the log level is enabled
    if (!this.isLevelEnabled(level)) {
      return;
    }

    // Format the message
    const formattedMessage = this.formatMessage(level, message);

    // Use the custom handler if provided
    if (this.handler) {
      this.handler(level, formattedMessage, ...args);
      return;
    }

    // Otherwise, use the console
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage, ...args);
        break;
      case LogLevel.INFO:
        console.info(formattedMessage, ...args);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage, ...args);
        break;
      case LogLevel.ERROR:
        console.error(formattedMessage, ...args);
        break;
    }
  }

  /**
   * Check if the specified log level is enabled
   */
  private isLevelEnabled(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR];
    const currentLevelIndex = levels.indexOf(this.level);
    const targetLevelIndex = levels.indexOf(level);
    return targetLevelIndex >= currentLevelIndex;
  }

  /**
   * Format a log message
   */
  private formatMessage(level: LogLevel, message: string): string {
    const prefix = this.timestamps ? `[${new Date().toISOString()}] [${level.toUpperCase()}]` : `[${level.toUpperCase()}]`;
    return `${prefix} ${message}`;
  }
}

// Create a default logger instance
const logger = new Logger();

// Export the default logger
export default logger;
