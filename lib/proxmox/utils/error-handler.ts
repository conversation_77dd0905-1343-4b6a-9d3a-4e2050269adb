/**
 * Proxmox API Error Handler
 *
 * This module provides error handling functionality for the Proxmox API.
 */

/**
 * Proxmox API error
 */
export class ProxmoxApiError extends Error {
  /**
   * Error code
   */
  code?: string;

  /**
   * HTTP status code
   */
  status?: number;

  /**
   * Original error
   */
  cause?: Error;

  /**
   * Additional error details
   */
  details?: Record<string, any>;

  /**
   * Create a new ProxmoxApiError
   */
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message);
    this.name = 'ProxmoxApiError';
    
    if (options) {
      this.code = options.code;
      this.status = options.status;
      this.details = options.details;
      
      if (options.cause instanceof Error) {
        this.cause = options.cause;
      } else if (options.cause) {
        this.cause = new Error(String(options.cause));
      }
    }
  }
}

/**
 * Authentication error
 */
export class ProxmoxAuthError extends ProxmoxApiError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, options);
    this.name = 'ProxmoxAuthError';
  }
}

/**
 * VM operation error
 */
export class ProxmoxVMError extends ProxmoxApiError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, options);
    this.name = 'ProxmoxVMError';
  }
}

/**
 * Storage operation error
 */
export class ProxmoxStorageError extends ProxmoxApiError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, options);
    this.name = 'ProxmoxStorageError';
  }
}

/**
 * Network operation error
 */
export class ProxmoxNetworkError extends ProxmoxApiError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, options);
    this.name = 'ProxmoxNetworkError';
  }
}

/**
 * Backup operation error
 */
export class ProxmoxBackupError extends ProxmoxApiError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, options);
    this.name = 'ProxmoxBackupError';
  }
}

/**
 * Task operation error
 */
export class ProxmoxTaskError extends ProxmoxApiError {
  constructor(
    message: string,
    options?: {
      code?: string;
      status?: number;
      cause?: Error | unknown;
      details?: Record<string, any>;
    }
  ) {
    super(message, options);
    this.name = 'ProxmoxTaskError';
  }
}

/**
 * Handle Proxmox API errors
 */
export function handleProxmoxError(error: any): ProxmoxApiError {
  if (error instanceof ProxmoxApiError) {
    return error;
  }

  // Handle axios errors
  if (error.isAxiosError) {
    const status = error.response?.status;
    const data = error.response?.data;
    
    let message = error.message;
    let code: string | undefined;
    let details: Record<string, any> | undefined;
    
    if (data) {
      if (data.message) {
        message = data.message;
      }
      
      if (data.code) {
        code = data.code;
      }
      
      details = data;
    }
    
    return new ProxmoxApiError(message, {
      code,
      status,
      cause: error,
      details,
    });
  }

  // Handle other errors
  return new ProxmoxApiError(
    error.message || 'Unknown Proxmox API error',
    { cause: error }
  );
}
