/**
 * Proxmox Authentication Module
 *
 * This module provides authentication functionality for the Proxmox API.
 * It supports both token-based and username/password authentication methods.
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { ProxmoxCredentials } from './types';
import { ProxmoxApiError } from './utils/error-handler';
import logger from './utils/logger';

/**
 * Authentication token response
 */
interface AuthTokenResponse {
  /**
   * Authentication ticket
   */
  ticket: string;

  /**
   * CSRF prevention token
   */
  CSRFPreventionToken: string;

  /**
   * Token expiration time
   */
  username: string;
}

/**
 * Authentication options
 */
export interface AuthOptions {
  /**
   * API URL
   */
  apiUrl: string;

  /**
   * Authentication credentials
   */
  credentials: ProxmoxCredentials;

  /**
   * Request timeout in milliseconds
   */
  timeout?: number;

  /**
   * Whether to verify SSL certificates
   */
  verifySSL?: boolean;
}

/**
 * Authentication result
 */
export interface AuthResult {
  /**
   * Authentication ticket
   */
  ticket?: string;

  /**
   * CSRF prevention token
   */
  csrfToken?: string;

  /**
   * API token
   */
  apiToken?: string;

  /**
   * Authentication method used
   */
  method: 'ticket' | 'token';
}

/**
 * Proxmox authentication service
 */
export class ProxmoxAuth {
  private apiUrl: string;
  private credentials: ProxmoxCredentials;
  private timeout: number;
  private verifySSL: boolean;
  private authResult: AuthResult | null = null;
  private client: AxiosInstance;

  /**
   * Create a new ProxmoxAuth instance
   */
  constructor(options: AuthOptions) {
    this.apiUrl = options.apiUrl;
    this.credentials = options.credentials;
    this.timeout = options.timeout || 30000;
    this.verifySSL = options.verifySSL !== false;

    // Create axios instance
    this.client = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
      validateStatus: (status) => status < 500, // Don't throw on 4xx errors
    });

    // Configure SSL verification
    if (!this.verifySSL) {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    }
  }

  /**
   * Authenticate with the Proxmox API
   */
  async authenticate(): Promise<AuthResult> {
    // If we already have authentication, return it
    if (this.authResult) {
      return this.authResult;
    }

    // Try token-based authentication first if token is provided
    if (this.credentials.token && this.credentials.tokenId) {
      try {
        logger.info('Authenticating with token');
        return await this.authenticateWithToken();
      } catch (error) {
        logger.warn('Token authentication failed, falling back to username/password', error);
      }
    }

    // Fall back to username/password authentication
    if (this.credentials.username && this.credentials.password) {
      try {
        logger.info('Authenticating with username/password');
        return await this.authenticateWithPassword();
      } catch (error) {
        logger.error('Username/password authentication failed', error);
        throw new ProxmoxApiError('Authentication failed', { cause: error });
      }
    }

    // No valid authentication method
    throw new ProxmoxApiError('No valid authentication credentials provided');
  }

  /**
   * Authenticate with username and password
   */
  private async authenticateWithPassword(): Promise<AuthResult> {
    try {
      const response = await this.client.post<AuthTokenResponse>('/api2/json/access/ticket', {
        username: `${this.credentials.username}@${this.credentials.realm || 'pam'}`,
        password: this.credentials.password,
      });

      if (response.status !== 200 || !response.data) {
        throw new ProxmoxApiError(`Authentication failed with status ${response.status}`);
      }

      const data = response.data.data;
      
      this.authResult = {
        ticket: data.ticket,
        csrfToken: data.CSRFPreventionToken,
        method: 'ticket',
      };

      return this.authResult;
    } catch (error) {
      logger.error('Password authentication failed', error);
      throw new ProxmoxApiError('Password authentication failed', { cause: error });
    }
  }

  /**
   * Authenticate with API token
   */
  private async authenticateWithToken(): Promise<AuthResult> {
    // For token-based authentication, we don't need to make an API call
    // We just need to set the token in the headers for future requests
    this.authResult = {
      apiToken: this.credentials.token,
      method: 'token',
    };

    // Verify that the token works by making a test API call
    try {
      const config: AxiosRequestConfig = {
        headers: {
          'Authorization': `PVEAPIToken=${this.credentials.tokenId}=${this.credentials.token}`,
        },
      };

      const response = await this.client.get('/api2/json/access/ticket', config);

      if (response.status !== 200) {
        throw new ProxmoxApiError(`Token verification failed with status ${response.status}`);
      }

      return this.authResult;
    } catch (error) {
      this.authResult = null;
      logger.error('Token authentication failed', error);
      throw new ProxmoxApiError('Token authentication failed', { cause: error });
    }
  }

  /**
   * Get authentication headers for API requests
   */
  async getAuthHeaders(): Promise<Record<string, string>> {
    const auth = await this.authenticate();

    if (auth.method === 'token') {
      return {
        'Authorization': `PVEAPIToken=${this.credentials.tokenId}=${this.credentials.token}`,
      };
    } else {
      return {
        'Cookie': `PVEAuthCookie=${auth.ticket}`,
        'CSRFPreventionToken': auth.csrfToken || '',
      };
    }
  }

  /**
   * Check if authentication is valid
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      await this.authenticate();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Logout from the Proxmox API (only for ticket-based authentication)
   */
  async logout(): Promise<void> {
    if (!this.authResult || this.authResult.method !== 'ticket') {
      return;
    }

    try {
      const headers = await this.getAuthHeaders();
      await this.client.delete('/api2/json/access/ticket', { headers });
      this.authResult = null;
    } catch (error) {
      logger.warn('Logout failed', error);
    }
  }
}
