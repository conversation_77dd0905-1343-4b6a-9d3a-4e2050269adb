/**
 * AI Desktop Automation Types
 * 
 * Type definitions for AI-powered desktop automation, including actions,
 * sessions, screenshots, and automation workflows.
 */

// Re-export types from desktop-vm for convenience
export type {
  AutomationActionType,
  AutomationActionStatus,
  AutomationAction,
  AutomationSession,
  ScreenshotData,
  MouseAction,
  KeyboardAction,
  AutomationPrompt,
  UseAiDesktopAutomationResult,
  ExecuteAutomationRequest,
  ExecuteAutomationResponse,
  TakeScreenshotRequest,
  TakeScreenshotResponse,
  ExecuteMouseActionRequest,
  ExecuteMouseActionResponse,
  ExecuteKeyboardActionRequest,
  ExecuteKeyboardActionResponse,
  StartAutomationSessionRequest,
  StartAutomationSessionResponse,
  StopAutomationSessionRequest,
  StopAutomationSessionResponse,
  GetSessionHistoryRequest,
  GetSessionHistoryResponse,
  ExportSessionRequest,
  ExportSessionResponse,
  ImportSessionRequest,
  ImportSessionResponse,
} from '../containerization/desktop-vm/types';

// Additional AI-specific types

export interface AiAutomationAgent {
  id: string;
  name: string;
  description: string;
  capabilities: string[];
  model: string;
  version: string;
  status: 'active' | 'inactive' | 'error';
  lastUsed?: string;
}

export interface AiAutomationTask {
  id: string;
  sessionId: string;
  vmId: string;
  prompt: string;
  status: 'pending' | 'analyzing' | 'executing' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  actions: AutomationAction[];
  screenshots: ScreenshotData[];
  error?: string;
  confidence?: number;
  metadata?: Record<string, any>;
}

export interface AiAutomationWorkflow {
  id: string;
  name: string;
  description: string;
  steps: AiAutomationStep[];
  triggers: AiAutomationTrigger[];
  status: 'active' | 'inactive' | 'draft';
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  tags: string[];
}

export interface AiAutomationStep {
  id: string;
  type: 'prompt' | 'action' | 'condition' | 'loop' | 'wait';
  name: string;
  description?: string;
  config: Record<string, any>;
  nextSteps: string[];
  conditions?: AiAutomationCondition[];
}

export interface AiAutomationTrigger {
  id: string;
  type: 'schedule' | 'event' | 'webhook' | 'manual';
  config: Record<string, any>;
  enabled: boolean;
}

export interface AiAutomationCondition {
  id: string;
  type: 'screenshot' | 'element' | 'text' | 'time' | 'custom';
  operator: 'equals' | 'contains' | 'exists' | 'not_exists' | 'greater' | 'less';
  value: any;
  timeout?: number;
}

export interface ScreenAnalysis {
  id: string;
  screenshotId: string;
  timestamp: string;
  elements: DetectedElement[];
  text: DetectedText[];
  regions: DetectedRegion[];
  confidence: number;
  processingTime: number;
}

export interface DetectedElement {
  id: string;
  type: 'button' | 'input' | 'link' | 'image' | 'text' | 'menu' | 'window' | 'dialog';
  bounds: ElementBounds;
  text?: string;
  attributes?: Record<string, string>;
  confidence: number;
  clickable: boolean;
  visible: boolean;
}

export interface DetectedText {
  id: string;
  text: string;
  bounds: ElementBounds;
  confidence: number;
  language?: string;
  fontSize?: number;
  fontFamily?: string;
}

export interface DetectedRegion {
  id: string;
  type: 'content' | 'navigation' | 'sidebar' | 'header' | 'footer' | 'modal';
  bounds: ElementBounds;
  confidence: number;
  elements: string[]; // IDs of elements in this region
}

export interface ElementBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface AutomationPlan {
  id: string;
  prompt: string;
  vmId: string;
  steps: PlannedStep[];
  estimatedDuration: number;
  confidence: number;
  risks: string[];
  alternatives: AlternativePlan[];
  createdAt: string;
}

export interface PlannedStep {
  id: string;
  type: AutomationActionType;
  description: string;
  parameters: Record<string, any>;
  expectedOutcome: string;
  confidence: number;
  dependencies: string[];
  alternatives: string[];
}

export interface AlternativePlan {
  id: string;
  description: string;
  steps: PlannedStep[];
  confidence: number;
  reason: string;
}

export interface AutomationMetrics {
  sessionId: string;
  vmId: string;
  totalActions: number;
  successfulActions: number;
  failedActions: number;
  averageActionTime: number;
  totalDuration: number;
  screenshotCount: number;
  errorRate: number;
  confidenceScore: number;
  userSatisfaction?: number;
}

export interface AutomationInsights {
  id: string;
  sessionId: string;
  type: 'performance' | 'accuracy' | 'efficiency' | 'error' | 'suggestion';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendations: string[];
  data: Record<string, any>;
  createdAt: string;
}

export interface AutomationTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  prompt: string;
  expectedActions: PlannedStep[];
  requirements: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  popularity: number;
  rating: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface AutomationLibrary {
  id: string;
  name: string;
  description: string;
  functions: AutomationFunction[];
  version: string;
  author: string;
  license: string;
  dependencies: string[];
  documentation: string;
}

export interface AutomationFunction {
  id: string;
  name: string;
  description: string;
  parameters: FunctionParameter[];
  returnType: string;
  examples: FunctionExample[];
  category: string;
  complexity: 'simple' | 'moderate' | 'complex';
}

export interface FunctionParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  defaultValue?: any;
  validation?: string;
}

export interface FunctionExample {
  id: string;
  title: string;
  description: string;
  code: string;
  input: Record<string, any>;
  output: any;
}

export interface AutomationSchedule {
  id: string;
  name: string;
  description: string;
  workflowId: string;
  vmId: string;
  schedule: ScheduleConfig;
  enabled: boolean;
  lastRun?: string;
  nextRun?: string;
  runCount: number;
  successCount: number;
  failureCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ScheduleConfig {
  type: 'once' | 'recurring' | 'cron';
  startTime?: string;
  endTime?: string;
  interval?: number;
  intervalUnit?: 'minutes' | 'hours' | 'days' | 'weeks' | 'months';
  cronExpression?: string;
  timezone?: string;
  maxRuns?: number;
}

export interface AutomationNotification {
  id: string;
  type: 'success' | 'failure' | 'warning' | 'info';
  title: string;
  message: string;
  sessionId?: string;
  workflowId?: string;
  vmId: string;
  timestamp: string;
  read: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  id: string;
  label: string;
  action: string;
  parameters?: Record<string, any>;
}

export interface AutomationSettings {
  vmId: string;
  defaultTimeout: number;
  screenshotInterval: number;
  maxRetries: number;
  retryDelay: number;
  enableLogging: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  enableScreenshots: boolean;
  screenshotQuality: number;
  enableRecording: boolean;
  recordingFormat: 'mp4' | 'webm';
  enableNotifications: boolean;
  notificationChannels: string[];
  aiModel: string;
  aiTemperature: number;
  aiMaxTokens: number;
  customPrompts: Record<string, string>;
}

export interface AutomationReport {
  id: string;
  title: string;
  description: string;
  period: {
    start: string;
    end: string;
  };
  vmIds: string[];
  metrics: AutomationMetrics[];
  insights: AutomationInsights[];
  recommendations: string[];
  charts: ReportChart[];
  generatedAt: string;
  generatedBy: string;
}

export interface ReportChart {
  id: string;
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  title: string;
  description: string;
  data: any[];
  config: Record<string, any>;
}

// Event types for real-time updates
export interface AutomationEvent {
  id: string;
  type: string;
  timestamp: string;
  data: any;
}

export interface SessionUpdatedEvent extends AutomationEvent {
  type: 'session_updated';
  data: {
    session: AutomationSession;
  };
}

export interface ActionExecutedEvent extends AutomationEvent {
  type: 'action_executed';
  data: {
    action: AutomationAction;
    sessionId: string;
  };
}

export interface ScreenshotCapturedEvent extends AutomationEvent {
  type: 'screenshot_captured';
  data: {
    screenshot: ScreenshotData;
    sessionId: string;
  };
}

export interface AutomationErrorEvent extends AutomationEvent {
  type: 'automation_error';
  data: {
    error: string;
    sessionId: string;
    actionId?: string;
  };
}

export interface TaskCompletedEvent extends AutomationEvent {
  type: 'task_completed';
  data: {
    task: AiAutomationTask;
  };
}

export interface WorkflowExecutedEvent extends AutomationEvent {
  type: 'workflow_executed';
  data: {
    workflow: AiAutomationWorkflow;
    sessionId: string;
    status: 'success' | 'failure';
  };
}

export type AutomationEventType = 
  | SessionUpdatedEvent
  | ActionExecutedEvent
  | ScreenshotCapturedEvent
  | AutomationErrorEvent
  | TaskCompletedEvent
  | WorkflowExecutedEvent;
