/**
 * Session Recorder Service
 * 
 * Service for recording and playing back automation sessions,
 * including screen recordings and action sequences.
 */

import { EventEmitter } from 'events';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { logger } from '@/lib/utils/logger';
import { 
  AutomationSession, 
  AutomationAction 
} from './types';

const execAsync = promisify(exec);

export interface SessionRecorderOptions {
  recordingDir?: string;
  videoFormat?: 'mp4' | 'webm' | 'avi';
  videoQuality?: 'low' | 'medium' | 'high';
  frameRate?: number;
  enableAudio?: boolean;
  maxRecordingDuration?: number; // in seconds
  autoCleanup?: boolean;
  retentionDays?: number;
}

export interface RecordingSession {
  id: string;
  sessionId: string;
  vmId: string;
  startTime: string;
  endTime?: string;
  status: 'recording' | 'stopped' | 'processing' | 'completed' | 'failed';
  videoPath?: string;
  actionsPath?: string;
  duration?: number;
  fileSize?: number;
  metadata?: Record<string, any>;
}

export class SessionRecorderService extends EventEmitter {
  private options: Required<SessionRecorderOptions>;
  private activeRecordings: Map<string, RecordingSession> = new Map();
  private recordingProcesses: Map<string, any> = new Map();
  private cleanupInterval?: NodeJS.Timeout;

  constructor(options: SessionRecorderOptions = {}) {
    super();
    
    this.options = {
      recordingDir: options.recordingDir || '/tmp/session-recordings',
      videoFormat: options.videoFormat || 'mp4',
      videoQuality: options.videoQuality || 'medium',
      frameRate: options.frameRate || 30,
      enableAudio: options.enableAudio ?? false,
      maxRecordingDuration: options.maxRecordingDuration || 3600, // 1 hour
      autoCleanup: options.autoCleanup ?? true,
      retentionDays: options.retentionDays || 7,
    };

    this.initialize();
  }

  /**
   * Initialize the session recorder
   */
  private async initialize(): Promise<void> {
    try {
      // Create recording directory if it doesn't exist
      await fs.mkdir(this.options.recordingDir, { recursive: true });
      
      // Start cleanup interval if auto cleanup is enabled
      if (this.options.autoCleanup) {
        this.startCleanupInterval();
      }
      
      logger.info('Session recorder service initialized');
    } catch (error: any) {
      logger.error(`Failed to initialize session recorder: ${error.message}`);
      throw error;
    }
  }

  /**
   * Start recording a session
   */
  async startRecording(sessionId: string, vmId: string): Promise<RecordingSession> {
    logger.info(`Starting recording for session ${sessionId} on VM ${vmId}`);

    try {
      // Check if already recording
      if (this.activeRecordings.has(sessionId)) {
        throw new Error(`Recording already active for session ${sessionId}`);
      }

      const recordingId = `recording-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const startTime = new Date().toISOString();
      
      const recording: RecordingSession = {
        id: recordingId,
        sessionId,
        vmId,
        startTime,
        status: 'recording',
        metadata: {
          format: this.options.videoFormat,
          quality: this.options.videoQuality,
          frameRate: this.options.frameRate,
          enableAudio: this.options.enableAudio,
        },
      };

      // Create recording directory
      const recordingDir = path.join(this.options.recordingDir, sessionId);
      await fs.mkdir(recordingDir, { recursive: true });

      // Set file paths
      recording.videoPath = path.join(recordingDir, `video.${this.options.videoFormat}`);
      recording.actionsPath = path.join(recordingDir, 'actions.json');

      // Start video recording
      await this.startVideoRecording(recording);

      // Add to active recordings
      this.activeRecordings.set(sessionId, recording);

      this.emit('recordingStarted', sessionId);
      
      logger.info(`Recording started for session ${sessionId}: ${recordingId}`);
      
      return recording;
    } catch (error: any) {
      logger.error(`Failed to start recording for session ${sessionId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Stop recording a session
   */
  async stopRecording(sessionId: string): Promise<RecordingSession> {
    logger.info(`Stopping recording for session ${sessionId}`);

    try {
      const recording = this.activeRecordings.get(sessionId);
      if (!recording) {
        throw new Error(`No active recording found for session ${sessionId}`);
      }

      recording.status = 'processing';
      recording.endTime = new Date().toISOString();

      // Stop video recording
      await this.stopVideoRecording(recording);

      // Calculate duration
      const startTime = new Date(recording.startTime).getTime();
      const endTime = new Date(recording.endTime).getTime();
      recording.duration = Math.round((endTime - startTime) / 1000);

      // Get file size
      if (recording.videoPath) {
        try {
          const stats = await fs.stat(recording.videoPath);
          recording.fileSize = stats.size;
        } catch (error) {
          logger.warn(`Failed to get video file size: ${error}`);
        }
      }

      recording.status = 'completed';

      // Remove from active recordings
      this.activeRecordings.delete(sessionId);

      this.emit('recordingStopped', sessionId);
      
      logger.info(`Recording stopped for session ${sessionId}`);
      
      return recording;
    } catch (error: any) {
      logger.error(`Failed to stop recording for session ${sessionId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Record an action during a session
   */
  async recordAction(sessionId: string, action: AutomationAction): Promise<void> {
    try {
      const recording = this.activeRecordings.get(sessionId);
      if (!recording || recording.status !== 'recording') {
        return; // No active recording
      }

      if (!recording.actionsPath) {
        return;
      }

      // Load existing actions
      let actions: AutomationAction[] = [];
      try {
        const actionsContent = await fs.readFile(recording.actionsPath, 'utf-8');
        actions = JSON.parse(actionsContent);
      } catch {
        // File doesn't exist yet, start with empty array
      }

      // Add new action
      actions.push(action);

      // Save updated actions
      await fs.writeFile(recording.actionsPath, JSON.stringify(actions, null, 2));
      
      logger.debug(`Action recorded for session ${sessionId}: ${action.type}`);
    } catch (error: any) {
      logger.error(`Failed to record action for session ${sessionId}: ${error.message}`);
    }
  }

  /**
   * Get recording for a session
   */
  async getRecording(sessionId: string): Promise<RecordingSession | null> {
    try {
      // Check active recordings first
      const activeRecording = this.activeRecordings.get(sessionId);
      if (activeRecording) {
        return activeRecording;
      }

      // Load from disk
      const recordingDir = path.join(this.options.recordingDir, sessionId);
      const metadataPath = path.join(recordingDir, 'metadata.json');

      try {
        const metadataContent = await fs.readFile(metadataPath, 'utf-8');
        const recording: RecordingSession = JSON.parse(metadataContent);
        return recording;
      } catch {
        return null;
      }
    } catch (error: any) {
      logger.error(`Failed to get recording for session ${sessionId}: ${error.message}`);
      return null;
    }
  }

  /**
   * List all recordings
   */
  async listRecordings(): Promise<RecordingSession[]> {
    try {
      const recordings: RecordingSession[] = [];

      // Add active recordings
      for (const recording of this.activeRecordings.values()) {
        recordings.push(recording);
      }

      // Load completed recordings from disk
      const sessionDirs = await fs.readdir(this.options.recordingDir);
      
      for (const sessionDir of sessionDirs) {
        const sessionPath = path.join(this.options.recordingDir, sessionDir);
        const stat = await fs.stat(sessionPath);
        
        if (stat.isDirectory()) {
          const metadataPath = path.join(sessionPath, 'metadata.json');
          
          try {
            const metadataContent = await fs.readFile(metadataPath, 'utf-8');
            const recording: RecordingSession = JSON.parse(metadataContent);
            
            // Don't add if already in active recordings
            if (!this.activeRecordings.has(recording.sessionId)) {
              recordings.push(recording);
            }
          } catch {
            // Skip invalid metadata files
          }
        }
      }

      // Sort by start time (newest first)
      recordings.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime());
      
      return recordings;
    } catch (error: any) {
      logger.error(`Failed to list recordings: ${error.message}`);
      return [];
    }
  }

  /**
   * Delete a recording
   */
  async deleteRecording(sessionId: string): Promise<boolean> {
    try {
      // Stop recording if active
      if (this.activeRecordings.has(sessionId)) {
        await this.stopRecording(sessionId);
      }

      // Delete recording directory
      const recordingDir = path.join(this.options.recordingDir, sessionId);
      
      try {
        await fs.rm(recordingDir, { recursive: true, force: true });
        logger.info(`Recording deleted for session ${sessionId}`);
        return true;
      } catch {
        return false;
      }
    } catch (error: any) {
      logger.error(`Failed to delete recording for session ${sessionId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Start video recording using ffmpeg
   */
  private async startVideoRecording(recording: RecordingSession): Promise<void> {
    try {
      // Build ffmpeg command for screen recording
      const command = this.buildFfmpegCommand(recording);
      
      logger.debug(`Starting video recording with command: ${command}`);
      
      // For now, we'll simulate the recording process
      // In a real implementation, this would start ffmpeg to record the VNC display
      
      // Simulate recording process
      const process = {
        pid: Math.floor(Math.random() * 10000),
        command,
        startTime: Date.now(),
      };
      
      this.recordingProcesses.set(recording.sessionId, process);
      
      // In a real implementation, you would execute:
      // const child = spawn('ffmpeg', ffmpegArgs);
      // this.recordingProcesses.set(recording.sessionId, child);
      
      logger.debug(`Video recording process started for session ${recording.sessionId}`);
    } catch (error: any) {
      logger.error(`Failed to start video recording: ${error.message}`);
      throw error;
    }
  }

  /**
   * Stop video recording
   */
  private async stopVideoRecording(recording: RecordingSession): Promise<void> {
    try {
      const process = this.recordingProcesses.get(recording.sessionId);
      if (!process) {
        return;
      }

      // In a real implementation, you would:
      // process.kill('SIGTERM');
      // await new Promise(resolve => process.on('exit', resolve));
      
      // Simulate stopping the process
      await this.delay(1000);
      
      this.recordingProcesses.delete(recording.sessionId);
      
      // Save recording metadata
      await this.saveRecordingMetadata(recording);
      
      logger.debug(`Video recording stopped for session ${recording.sessionId}`);
    } catch (error: any) {
      logger.error(`Failed to stop video recording: ${error.message}`);
      throw error;
    }
  }

  /**
   * Build ffmpeg command for recording
   */
  private buildFfmpegCommand(recording: RecordingSession): string {
    const { videoPath, vmId } = recording;
    const { videoFormat, videoQuality, frameRate, enableAudio } = this.options;
    
    // Base command
    let command = 'ffmpeg';
    
    // Input source (VNC display)
    command += ` -f x11grab -r ${frameRate} -s 1920x1080 -i :1`;
    
    // Audio input (if enabled)
    if (enableAudio) {
      command += ' -f pulse -i default';
    }
    
    // Video codec and quality settings
    switch (videoQuality) {
      case 'low':
        command += ' -c:v libx264 -preset ultrafast -crf 28';
        break;
      case 'medium':
        command += ' -c:v libx264 -preset fast -crf 23';
        break;
      case 'high':
        command += ' -c:v libx264 -preset slow -crf 18';
        break;
    }
    
    // Audio codec (if enabled)
    if (enableAudio) {
      command += ' -c:a aac -b:a 128k';
    }
    
    // Output format
    command += ` -f ${videoFormat}`;
    
    // Maximum duration
    command += ` -t ${this.options.maxRecordingDuration}`;
    
    // Output file
    command += ` "${videoPath}"`;
    
    return command;
  }

  /**
   * Save recording metadata
   */
  private async saveRecordingMetadata(recording: RecordingSession): Promise<void> {
    try {
      const recordingDir = path.dirname(recording.videoPath || '');
      const metadataPath = path.join(recordingDir, 'metadata.json');
      
      await fs.writeFile(metadataPath, JSON.stringify(recording, null, 2));
      
      logger.debug(`Recording metadata saved for session ${recording.sessionId}`);
    } catch (error: any) {
      logger.error(`Failed to save recording metadata: ${error.message}`);
    }
  }

  /**
   * Start cleanup interval for old recordings
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldRecordings();
    }, 24 * 60 * 60 * 1000); // Run daily
  }

  /**
   * Cleanup old recordings based on retention policy
   */
  private async cleanupOldRecordings(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.options.retentionDays);
      
      const recordings = await this.listRecordings();
      
      for (const recording of recordings) {
        if (new Date(recording.startTime) < cutoffDate) {
          await this.deleteRecording(recording.sessionId);
        }
      }
      
      logger.debug('Recording cleanup completed');
    } catch (error: any) {
      logger.error(`Failed to cleanup old recordings: ${error.message}`);
    }
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    // Stop all active recordings
    for (const sessionId of this.activeRecordings.keys()) {
      this.stopRecording(sessionId).catch(err => 
        logger.error(`Failed to stop recording ${sessionId} during cleanup: ${err.message}`)
      );
    }

    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.activeRecordings.clear();
    this.recordingProcesses.clear();
    this.removeAllListeners();
  }
}
