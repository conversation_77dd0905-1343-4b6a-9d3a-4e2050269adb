/**
 * Screenshot Service
 * 
 * Service for capturing, storing, and managing screenshots from desktop VMs
 * for AI automation and analysis purposes.
 */

import { EventEmitter } from 'events';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { logger } from '@/lib/utils/logger';
import { 
  ScreenshotData, 
  TakeScreenshotRequest 
} from './types';

const execAsync = promisify(exec);

export interface ScreenshotServiceOptions {
  defaultFormat?: 'png' | 'jpeg';
  defaultQuality?: number;
  maxWidth?: number;
  maxHeight?: number;
  enableCompression?: boolean;
  storageDir?: string;
  retentionDays?: number;
  enableCache?: boolean;
  cacheSize?: number;
}

export class ScreenshotService extends EventEmitter {
  private options: Required<ScreenshotServiceOptions>;
  private screenshotCache: Map<string, ScreenshotData> = new Map();
  private cleanupInterval?: NodeJS.Timeout;

  constructor(options: ScreenshotServiceOptions = {}) {
    super();
    
    this.options = {
      defaultFormat: options.defaultFormat || 'png',
      defaultQuality: options.defaultQuality || 90,
      maxWidth: options.maxWidth || 1920,
      maxHeight: options.maxHeight || 1080,
      enableCompression: options.enableCompression ?? true,
      storageDir: options.storageDir || '/tmp/screenshots',
      retentionDays: options.retentionDays || 7,
      enableCache: options.enableCache ?? true,
      cacheSize: options.cacheSize || 100,
    };

    this.initialize();
  }

  /**
   * Initialize the screenshot service
   */
  private async initialize(): Promise<void> {
    try {
      // Create storage directory if it doesn't exist
      await fs.mkdir(this.options.storageDir, { recursive: true });
      
      // Start cleanup interval
      this.startCleanupInterval();
      
      logger.info('Screenshot service initialized');
    } catch (error: any) {
      logger.error(`Failed to initialize screenshot service: ${error.message}`);
      throw error;
    }
  }

  /**
   * Take a screenshot from a desktop VM
   */
  async takeScreenshot(request: TakeScreenshotRequest): Promise<ScreenshotData> {
    const { vmId, format = this.options.defaultFormat, quality = this.options.defaultQuality } = request;
    
    logger.debug(`Taking screenshot for VM ${vmId}`);

    try {
      // Generate screenshot ID
      const screenshotId = `screenshot-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const timestamp = new Date().toISOString();
      
      // Capture screenshot from VM
      const { imageData, width, height } = await this.captureVmScreen(vmId, format, quality);
      
      // Create data URL
      const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';
      const dataUrl = `data:${mimeType};base64,${imageData.toString('base64')}`;
      
      // Create screenshot data
      const screenshot: ScreenshotData = {
        id: screenshotId,
        vmId,
        timestamp,
        width,
        height,
        format,
        dataUrl,
        size: imageData.length,
        metadata: {
          quality,
          compressed: this.options.enableCompression,
          captureMethod: 'vnc',
        },
      };

      // Save to storage
      await this.saveScreenshot(screenshot, imageData);
      
      // Add to cache
      if (this.options.enableCache) {
        this.addToCache(screenshot);
      }
      
      this.emit('screenshotCaptured', screenshot);
      
      logger.debug(`Screenshot captured for VM ${vmId}: ${screenshotId}`);
      
      return screenshot;
    } catch (error: any) {
      logger.error(`Failed to take screenshot for VM ${vmId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a screenshot by ID
   */
  async getScreenshot(vmId: string, screenshotId: string): Promise<ScreenshotData | null> {
    try {
      // Check cache first
      if (this.options.enableCache) {
        const cached = this.screenshotCache.get(screenshotId);
        if (cached && cached.vmId === vmId) {
          return cached;
        }
      }

      // Load from storage
      const screenshot = await this.loadScreenshot(vmId, screenshotId);
      
      if (screenshot && this.options.enableCache) {
        this.addToCache(screenshot);
      }
      
      return screenshot;
    } catch (error: any) {
      logger.error(`Failed to get screenshot ${screenshotId}: ${error.message}`);
      return null;
    }
  }

  /**
   * List screenshots for a VM
   */
  async listScreenshots(vmId: string, options: { limit?: number; offset?: number } = {}): Promise<ScreenshotData[]> {
    const { limit = 50, offset = 0 } = options;
    
    try {
      const vmDir = path.join(this.options.storageDir, vmId);
      
      // Check if directory exists
      try {
        await fs.access(vmDir);
      } catch {
        return [];
      }

      // Read directory contents
      const files = await fs.readdir(vmDir);
      const metadataFiles = files.filter(file => file.endsWith('.json'));
      
      // Sort by creation time (newest first)
      metadataFiles.sort((a, b) => {
        const timeA = parseInt(a.split('-')[1]) || 0;
        const timeB = parseInt(b.split('-')[1]) || 0;
        return timeB - timeA;
      });

      // Apply pagination
      const paginatedFiles = metadataFiles.slice(offset, offset + limit);
      
      // Load screenshot metadata
      const screenshots: ScreenshotData[] = [];
      
      for (const file of paginatedFiles) {
        try {
          const metadataPath = path.join(vmDir, file);
          const metadataContent = await fs.readFile(metadataPath, 'utf-8');
          const screenshot: ScreenshotData = JSON.parse(metadataContent);
          screenshots.push(screenshot);
        } catch (error: any) {
          logger.warn(`Failed to load screenshot metadata ${file}: ${error.message}`);
        }
      }
      
      return screenshots;
    } catch (error: any) {
      logger.error(`Failed to list screenshots for VM ${vmId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Delete a screenshot
   */
  async deleteScreenshot(vmId: string, screenshotId: string): Promise<boolean> {
    try {
      const vmDir = path.join(this.options.storageDir, vmId);
      const metadataPath = path.join(vmDir, `${screenshotId}.json`);
      const imagePath = path.join(vmDir, `${screenshotId}.png`); // Try PNG first
      const jpegPath = path.join(vmDir, `${screenshotId}.jpeg`); // Then JPEG
      
      // Remove from cache
      this.screenshotCache.delete(screenshotId);
      
      // Delete files
      let deleted = false;
      
      try {
        await fs.unlink(metadataPath);
        deleted = true;
      } catch {
        // Metadata file might not exist
      }
      
      try {
        await fs.unlink(imagePath);
        deleted = true;
      } catch {
        // Try JPEG
        try {
          await fs.unlink(jpegPath);
          deleted = true;
        } catch {
          // Neither format exists
        }
      }
      
      if (deleted) {
        logger.debug(`Screenshot deleted: ${screenshotId} for VM ${vmId}`);
      }
      
      return deleted;
    } catch (error: any) {
      logger.error(`Failed to delete screenshot ${screenshotId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Delete all screenshots for a VM
   */
  async deleteAllScreenshots(vmId: string): Promise<number> {
    try {
      const vmDir = path.join(this.options.storageDir, vmId);
      
      // Check if directory exists
      try {
        await fs.access(vmDir);
      } catch {
        return 0;
      }

      // Remove from cache
      for (const [id, screenshot] of this.screenshotCache.entries()) {
        if (screenshot.vmId === vmId) {
          this.screenshotCache.delete(id);
        }
      }

      // Delete directory and all contents
      const files = await fs.readdir(vmDir);
      let deletedCount = 0;
      
      for (const file of files) {
        try {
          await fs.unlink(path.join(vmDir, file));
          if (file.endsWith('.json')) {
            deletedCount++;
          }
        } catch (error: any) {
          logger.warn(`Failed to delete file ${file}: ${error.message}`);
        }
      }
      
      // Remove directory if empty
      try {
        await fs.rmdir(vmDir);
      } catch {
        // Directory might not be empty
      }
      
      logger.debug(`${deletedCount} screenshots deleted for VM ${vmId}`);
      
      return deletedCount;
    } catch (error: any) {
      logger.error(`Failed to delete screenshots for VM ${vmId}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Capture screen from VM using VNC
   */
  private async captureVmScreen(vmId: string, format: 'png' | 'jpeg', quality: number): Promise<{
    imageData: Buffer;
    width: number;
    height: number;
  }> {
    try {
      // For now, we'll simulate screenshot capture
      // In a real implementation, this would connect to the VM's VNC server
      // and capture the screen using a library like node-vnc or by executing
      // vncsnapshot or similar tools
      
      // Simulate screenshot data
      const width = 1920;
      const height = 1080;
      
      // Create a simple test image (in real implementation, this would be actual screen capture)
      const canvas = await this.createTestImage(width, height, vmId);
      const imageData = Buffer.from(canvas, 'base64');
      
      return {
        imageData,
        width,
        height,
      };
    } catch (error: any) {
      logger.error(`Failed to capture VM screen: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a test image (placeholder for real screenshot)
   */
  private async createTestImage(width: number, height: number, vmId: string): Promise<string> {
    // This is a placeholder that creates a simple test image
    // In a real implementation, this would be replaced with actual VNC screen capture
    
    const timestamp = new Date().toISOString();
    const text = `Desktop VM: ${vmId}\nTimestamp: ${timestamp}\nResolution: ${width}x${height}`;
    
    // Return a base64 encoded placeholder image
    // This would be replaced with actual screenshot data
    return Buffer.from(`Test screenshot for ${vmId} at ${timestamp}`).toString('base64');
  }

  /**
   * Save screenshot to storage
   */
  private async saveScreenshot(screenshot: ScreenshotData, imageData: Buffer): Promise<void> {
    try {
      const vmDir = path.join(this.options.storageDir, screenshot.vmId);
      await fs.mkdir(vmDir, { recursive: true });
      
      // Save image file
      const imageExtension = screenshot.format === 'png' ? 'png' : 'jpeg';
      const imagePath = path.join(vmDir, `${screenshot.id}.${imageExtension}`);
      await fs.writeFile(imagePath, imageData);
      
      // Save metadata
      const metadataPath = path.join(vmDir, `${screenshot.id}.json`);
      const metadata = { ...screenshot };
      delete (metadata as any).dataUrl; // Don't store data URL in metadata file
      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));
      
      logger.debug(`Screenshot saved: ${screenshot.id}`);
    } catch (error: any) {
      logger.error(`Failed to save screenshot: ${error.message}`);
      throw error;
    }
  }

  /**
   * Load screenshot from storage
   */
  private async loadScreenshot(vmId: string, screenshotId: string): Promise<ScreenshotData | null> {
    try {
      const vmDir = path.join(this.options.storageDir, vmId);
      const metadataPath = path.join(vmDir, `${screenshotId}.json`);
      
      // Load metadata
      const metadataContent = await fs.readFile(metadataPath, 'utf-8');
      const metadata: ScreenshotData = JSON.parse(metadataContent);
      
      // Load image data
      const imageExtension = metadata.format === 'png' ? 'png' : 'jpeg';
      const imagePath = path.join(vmDir, `${screenshotId}.${imageExtension}`);
      const imageData = await fs.readFile(imagePath);
      
      // Create data URL
      const mimeType = metadata.format === 'png' ? 'image/png' : 'image/jpeg';
      const dataUrl = `data:${mimeType};base64,${imageData.toString('base64')}`;
      
      return {
        ...metadata,
        dataUrl,
      };
    } catch (error: any) {
      logger.error(`Failed to load screenshot ${screenshotId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Add screenshot to cache
   */
  private addToCache(screenshot: ScreenshotData): void {
    // Remove oldest entries if cache is full
    if (this.screenshotCache.size >= this.options.cacheSize) {
      const oldestKey = this.screenshotCache.keys().next().value;
      if (oldestKey) {
        this.screenshotCache.delete(oldestKey);
      }
    }
    
    this.screenshotCache.set(screenshot.id, screenshot);
  }

  /**
   * Start cleanup interval for old screenshots
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldScreenshots();
    }, 24 * 60 * 60 * 1000); // Run daily
  }

  /**
   * Cleanup old screenshots based on retention policy
   */
  private async cleanupOldScreenshots(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.options.retentionDays);
      
      const vmDirs = await fs.readdir(this.options.storageDir);
      
      for (const vmDir of vmDirs) {
        const vmPath = path.join(this.options.storageDir, vmDir);
        const stat = await fs.stat(vmPath);
        
        if (stat.isDirectory()) {
          const files = await fs.readdir(vmPath);
          
          for (const file of files) {
            if (file.endsWith('.json')) {
              try {
                const filePath = path.join(vmPath, file);
                const fileContent = await fs.readFile(filePath, 'utf-8');
                const metadata: ScreenshotData = JSON.parse(fileContent);
                
                if (new Date(metadata.timestamp) < cutoffDate) {
                  await this.deleteScreenshot(metadata.vmId, metadata.id);
                }
              } catch (error: any) {
                logger.warn(`Failed to process file ${file} during cleanup: ${error.message}`);
              }
            }
          }
        }
      }
      
      logger.debug('Screenshot cleanup completed');
    } catch (error: any) {
      logger.error(`Failed to cleanup old screenshots: ${error.message}`);
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.screenshotCache.clear();
    this.removeAllListeners();
  }
}
