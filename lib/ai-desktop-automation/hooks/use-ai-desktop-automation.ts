/**
 * AI Desktop Automation Hook
 * 
 * React hook for AI-powered desktop automation including screenshot capture,
 * input automation, session recording, and AI agent interactions.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  AutomationSession, 
  AutomationAction, 
  ScreenshotData,
  UseAiDesktopAutomationResult,
  ExecuteAutomationRequest,
  ExecuteAutomationResponse,
  TakeScreenshotRequest,
  TakeScreenshotResponse,
  ExecuteMouseActionRequest,
  ExecuteMouseActionResponse,
  ExecuteKeyboardActionRequest,
  ExecuteKeyboardActionResponse,
  StartAutomationSessionRequest,
  StartAutomationSessionResponse,
  StopAutomationSessionRequest,
  StopAutomationSessionResponse,
  GetSessionHistoryRequest,
  GetSessionHistoryResponse,
  ExportSessionRequest,
  ExportSessionResponse,
  ImportSessionRequest,
  ImportSessionResponse
} from '../types';

export function useAiDesktopAutomation(vmId?: string): UseAiDesktopAutomationResult {
  // State
  const [isAutomationActive, setIsAutomationActive] = useState(false);
  const [currentSession, setCurrentSession] = useState<AutomationSession | undefined>();
  const [automationError, setAutomationError] = useState<string | undefined>();

  // Refs
  const abortController = useRef<AbortController>();
  const eventSource = useRef<EventSource>();

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortController.current) {
        abortController.current.abort();
      }
      if (eventSource.current) {
        eventSource.current.close();
      }
    };
  }, []);

  // Setup event source for real-time updates
  const setupEventSource = useCallback((sessionId: string) => {
    if (eventSource.current) {
      eventSource.current.close();
    }

    eventSource.current = new EventSource(`/api/desktop-vm/automation/events?sessionId=${sessionId}`);
    
    eventSource.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
          case 'session_updated':
            setCurrentSession(data.session);
            break;
          case 'action_executed':
            setCurrentSession(prev => prev ? {
              ...prev,
              actions: [...prev.actions, data.action]
            } : prev);
            break;
          case 'automation_error':
            setAutomationError(data.error);
            break;
        }
      } catch (error) {
        console.error('Failed to parse event source data:', error);
      }
    };

    eventSource.current.onerror = (error) => {
      console.error('Event source error:', error);
      setAutomationError('Connection to automation service lost');
    };
  }, []);

  // Start automation session
  const startAutomation = useCallback(async (): Promise<AutomationSession> => {
    if (!vmId) {
      throw new Error('No VM ID provided');
    }

    setAutomationError(undefined);

    try {
      const request: StartAutomationSessionRequest = { 
        vmId,
        metadata: {
          startedAt: new Date().toISOString(),
          userAgent: navigator.userAgent,
        }
      };
      
      const response = await fetch('/api/desktop-vm/automation/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start automation session');
      }

      const data: StartAutomationSessionResponse = await response.json();
      
      setCurrentSession(data.session);
      setIsAutomationActive(true);
      
      // Setup real-time updates
      setupEventSource(data.session.id);

      return data.session;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId, setupEventSource]);

  // Stop automation session
  const stopAutomation = useCallback(async (): Promise<AutomationSession> => {
    if (!vmId || !currentSession) {
      throw new Error('No active automation session');
    }

    try {
      const request: StopAutomationSessionRequest = { 
        vmId,
        sessionId: currentSession.id
      };
      
      const response = await fetch('/api/desktop-vm/automation/stop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to stop automation session');
      }

      const data: StopAutomationSessionResponse = await response.json();
      
      setCurrentSession(data.session);
      setIsAutomationActive(false);
      
      // Close event source
      if (eventSource.current) {
        eventSource.current.close();
      }

      return data.session;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId, currentSession]);

  // Execute automation prompt
  const executeAutomationPrompt = useCallback(async (prompt: string): Promise<AutomationAction[]> => {
    if (!vmId) {
      throw new Error('No VM ID provided');
    }

    setAutomationError(undefined);

    try {
      const request: ExecuteAutomationRequest = { 
        vmId,
        prompt,
        sessionId: currentSession?.id
      };
      
      const response = await fetch('/api/desktop-vm/automation/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to execute automation prompt');
      }

      const data: ExecuteAutomationResponse = await response.json();
      
      // Update current session if we have one
      if (currentSession) {
        setCurrentSession(prev => prev ? {
          ...prev,
          actions: [...prev.actions, ...data.actions]
        } : prev);
      }

      return data.actions;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId, currentSession]);

  // Take screenshot
  const takeScreenshot = useCallback(async (): Promise<ScreenshotData> => {
    if (!vmId) {
      throw new Error('No VM ID provided');
    }

    try {
      const request: TakeScreenshotRequest = { 
        vmId,
        format: 'png',
        quality: 90
      };
      
      const response = await fetch('/api/desktop-vm/screenshot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to take screenshot');
      }

      const data: TakeScreenshotResponse = await response.json();
      return data.screenshot;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId]);

  // Execute mouse action
  const executeMouseAction = useCallback(async (
    action: string, 
    x: number, 
    y: number
  ): Promise<AutomationAction> => {
    if (!vmId) {
      throw new Error('No VM ID provided');
    }

    try {
      const request: ExecuteMouseActionRequest = { 
        vmId,
        action: {
          type: action as any,
          x,
          y,
          button: 'left'
        },
        sessionId: currentSession?.id
      };
      
      const response = await fetch('/api/desktop-vm/automation/mouse', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to execute mouse action');
      }

      const data: ExecuteMouseActionResponse = await response.json();
      
      // Update current session if we have one
      if (currentSession) {
        setCurrentSession(prev => prev ? {
          ...prev,
          actions: [...prev.actions, data.action]
        } : prev);
      }

      return data.action;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId, currentSession]);

  // Execute keyboard action
  const executeKeyboardAction = useCallback(async (
    text: string, 
    keys?: string[]
  ): Promise<AutomationAction> => {
    if (!vmId) {
      throw new Error('No VM ID provided');
    }

    try {
      const request: ExecuteKeyboardActionRequest = { 
        vmId,
        action: {
          type: keys ? 'combination' : 'type',
          text: keys ? undefined : text,
          keys: keys || undefined
        },
        sessionId: currentSession?.id
      };
      
      const response = await fetch('/api/desktop-vm/automation/keyboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to execute keyboard action');
      }

      const data: ExecuteKeyboardActionResponse = await response.json();
      
      // Update current session if we have one
      if (currentSession) {
        setCurrentSession(prev => prev ? {
          ...prev,
          actions: [...prev.actions, data.action]
        } : prev);
      }

      return data.action;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId, currentSession]);

  // Start session recording
  const startSessionRecording = useCallback(async (): Promise<void> => {
    if (!vmId || !currentSession) {
      throw new Error('No active automation session');
    }

    try {
      const response = await fetch(`/api/desktop-vm/automation/recording/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          vmId,
          sessionId: currentSession.id 
        }),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start session recording');
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId, currentSession]);

  // Stop session recording
  const stopSessionRecording = useCallback(async (): Promise<void> => {
    if (!vmId || !currentSession) {
      throw new Error('No active automation session');
    }

    try {
      const response = await fetch(`/api/desktop-vm/automation/recording/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          vmId,
          sessionId: currentSession.id 
        }),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to stop session recording');
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId, currentSession]);

  // Get session history
  const getSessionHistory = useCallback(async (): Promise<AutomationAction[]> => {
    if (!vmId) {
      throw new Error('No VM ID provided');
    }

    try {
      const request: GetSessionHistoryRequest = { 
        vmId,
        sessionId: currentSession?.id,
        limit: 100
      };
      
      const params = new URLSearchParams();
      params.append('vmId', request.vmId);
      if (request.sessionId) params.append('sessionId', request.sessionId);
      if (request.limit) params.append('limit', request.limit.toString());
      
      const response = await fetch(`/api/desktop-vm/automation/history?${params}`, {
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get session history');
      }

      const data: GetSessionHistoryResponse = await response.json();
      return data.actions;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId, currentSession]);

  // Export session
  const exportSession = useCallback(async (sessionId: string): Promise<any> => {
    try {
      const request: ExportSessionRequest = { 
        sessionId,
        format: 'json'
      };
      
      const response = await fetch('/api/desktop-vm/automation/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to export session');
      }

      const data: ExportSessionResponse = await response.json();
      return data.data;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, []);

  // Import session
  const importSession = useCallback(async (sessionData: any): Promise<AutomationSession> => {
    if (!vmId) {
      throw new Error('No VM ID provided');
    }

    try {
      const request: ImportSessionRequest = { 
        vmId,
        sessionData,
        format: 'json'
      };
      
      const response = await fetch('/api/desktop-vm/automation/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to import session');
      }

      const data: ImportSessionResponse = await response.json();
      return data.session;
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        setAutomationError(error.message);
        throw error;
      }
      throw error;
    }
  }, [vmId]);

  // Initialize abort controller
  useEffect(() => {
    abortController.current = new AbortController();
    
    return () => {
      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, []);

  return {
    isAutomationActive,
    currentSession,
    automationError,
    startAutomation,
    stopAutomation,
    executeAutomationPrompt,
    takeScreenshot,
    executeMouseAction,
    executeKeyboardAction,
    startSessionRecording,
    stopSessionRecording,
    getSessionHistory,
    exportSession,
    importSession,
  };
}
