/**
 * Desktop Automation Agent
 * 
 * AI agent for automated desktop interactions using computer vision,
 * natural language processing, and action execution.
 */

import { EventEmitter } from 'events';
import { openai } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';
import { logger } from '@/lib/utils/logger';
import { ScreenshotService } from './screenshot-service';
import { InputAutomationService } from './input-automation';
import { SessionRecorderService } from './session-recorder';
import { 
  AutomationSession, 
  AutomationAction, 
  ScreenshotData,
  ExecuteAutomationRequest,
  ExecuteAutomationResponse,
  StartAutomationSessionRequest,
  StartAutomationSessionResponse,
  StopAutomationSessionRequest,
  StopAutomationSessionResponse,
  GetSessionHistoryRequest,
  GetSessionHistoryResponse,
  AiAutomationTask,
  AutomationPlan,
  PlannedStep
} from './types';

export interface DesktopAutomationAgentOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  enableScreenshots?: boolean;
  screenshotInterval?: number;
  maxRetries?: number;
  retryDelay?: number;
  enableLogging?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

export class DesktopAutomationAgent extends EventEmitter {
  private options: Required<DesktopAutomationAgentOptions>;
  private screenshotService: ScreenshotService;
  private inputAutomationService: InputAutomationService;
  private sessionRecorderService: SessionRecorderService;
  private activeSessions: Map<string, AutomationSession> = new Map();
  private activeTasks: Map<string, AiAutomationTask> = new Map();

  constructor(options: DesktopAutomationAgentOptions = {}) {
    super();
    
    this.options = {
      model: options.model || 'gpt-4o',
      temperature: options.temperature ?? 0.7,
      maxTokens: options.maxTokens || 4000,
      enableScreenshots: options.enableScreenshots ?? true,
      screenshotInterval: options.screenshotInterval || 5000,
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 1000,
      enableLogging: options.enableLogging ?? true,
      logLevel: options.logLevel || 'info',
    };

    // Initialize services
    this.screenshotService = new ScreenshotService();
    this.inputAutomationService = new InputAutomationService();
    this.sessionRecorderService = new SessionRecorderService();

    this.setupEventHandlers();
  }

  /**
   * Execute an automation prompt
   */
  async executeAutomationPrompt(request: ExecuteAutomationRequest): Promise<ExecuteAutomationResponse> {
    const { vmId, prompt, sessionId } = request;
    
    logger.info(`Executing automation prompt for VM ${vmId}: ${prompt}`);

    try {
      // Get or create session
      let session = sessionId ? this.activeSessions.get(sessionId) : undefined;
      if (!session) {
        session = await this.createAutomationSession(vmId);
      }

      // Create automation task
      const task: AiAutomationTask = {
        id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        sessionId: session.id,
        vmId,
        prompt,
        status: 'analyzing',
        startTime: new Date().toISOString(),
        actions: [],
        screenshots: [],
      };

      this.activeTasks.set(task.id, task);
      this.emit('taskStarted', task);

      // Take initial screenshot
      let currentScreenshot: ScreenshotData | undefined;
      if (this.options.enableScreenshots) {
        currentScreenshot = await this.screenshotService.takeScreenshot({ vmId });
        task.screenshots.push(currentScreenshot);
      }

      // Generate automation plan
      const plan = await this.generateAutomationPlan(prompt, currentScreenshot);
      task.status = 'executing';
      this.emit('taskUpdated', task);

      // Execute planned actions
      const actions: AutomationAction[] = [];
      
      for (const step of plan.steps) {
        try {
          const action = await this.executeAutomationStep(vmId, step, session.id);
          actions.push(action);
          task.actions.push(action);
          
          // Take screenshot after action if enabled
          if (this.options.enableScreenshots) {
            const screenshot = await this.screenshotService.takeScreenshot({ vmId });
            task.screenshots.push(screenshot);
            currentScreenshot = screenshot;
          }

          // Wait between actions
          await this.delay(this.options.retryDelay);
        } catch (error: any) {
          logger.error(`Failed to execute automation step: ${error.message}`);
          
          const failedAction: AutomationAction = {
            id: `action-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: step.type,
            status: 'failed',
            timestamp: new Date().toISOString(),
            description: step.description,
            parameters: step.parameters,
            error: error.message,
          };
          
          actions.push(failedAction);
          task.actions.push(failedAction);
        }
      }

      // Update session
      session.actions.push(...actions);
      this.activeSessions.set(session.id, session);

      // Complete task
      task.status = 'completed';
      task.endTime = new Date().toISOString();
      task.confidence = this.calculateTaskConfidence(task);
      this.emit('taskCompleted', task);

      logger.info(`Automation completed for VM ${vmId}: ${actions.length} actions executed`);

      return {
        actions,
        sessionId: session.id,
      };
    } catch (error: any) {
      logger.error(`Failed to execute automation prompt: ${error.message}`);
      throw error;
    }
  }

  /**
   * Start an automation session
   */
  async startAutomationSession(request: StartAutomationSessionRequest): Promise<AutomationSession> {
    const { vmId, metadata } = request;
    
    logger.info(`Starting automation session for VM ${vmId}`);

    const session = await this.createAutomationSession(vmId, metadata);
    
    // Start session recording if enabled
    await this.sessionRecorderService.startRecording(session.id, vmId);
    
    this.emit('sessionStarted', session);
    
    return session;
  }

  /**
   * Stop an automation session
   */
  async stopAutomationSession(request: StopAutomationSessionRequest): Promise<AutomationSession> {
    const { vmId, sessionId } = request;
    
    logger.info(`Stopping automation session ${sessionId} for VM ${vmId}`);

    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error(`Automation session ${sessionId} not found`);
    }

    // Update session status
    session.status = 'completed';
    session.endTime = new Date().toISOString();
    
    // Stop session recording
    await this.sessionRecorderService.stopRecording(sessionId);
    
    // Remove from active sessions
    this.activeSessions.delete(sessionId);
    
    this.emit('sessionEnded', session);
    
    return session;
  }

  /**
   * Get session history
   */
  async getSessionHistory(request: GetSessionHistoryRequest): Promise<AutomationAction[]> {
    const { vmId, sessionId, limit = 100, offset = 0 } = request;
    
    if (sessionId) {
      const session = this.activeSessions.get(sessionId);
      if (session) {
        return session.actions.slice(offset, offset + limit);
      }
    }

    // For now, return actions from all active sessions for the VM
    const allActions: AutomationAction[] = [];
    
    for (const session of this.activeSessions.values()) {
      if (session.vmId === vmId) {
        allActions.push(...session.actions);
      }
    }

    // Sort by timestamp and apply pagination
    allActions.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    return allActions.slice(offset, offset + limit);
  }

  /**
   * Generate automation plan using AI
   */
  private async generateAutomationPlan(prompt: string, screenshot?: ScreenshotData): Promise<AutomationPlan> {
    const systemPrompt = `You are an expert desktop automation agent. Your task is to analyze the user's request and current screen state, then generate a detailed plan of actions to accomplish the goal.

Available action types:
- mouse_click: Click at specific coordinates
- mouse_double_click: Double-click at specific coordinates  
- mouse_right_click: Right-click at specific coordinates
- keyboard_type: Type text
- keyboard_key: Press specific keys or key combinations
- wait: Wait for a specified duration
- screenshot: Take a screenshot for analysis

When analyzing screenshots, identify clickable elements, text fields, buttons, and other interactive components. Provide precise coordinates and clear descriptions for each action.

Generate a step-by-step plan that is:
1. Specific and actionable
2. Includes precise coordinates when needed
3. Handles potential errors or variations
4. Includes verification steps
5. Is efficient and direct`;

    const userPrompt = `User request: ${prompt}

${screenshot ? `Current screen analysis: The screen shows a ${screenshot.width}x${screenshot.height} desktop. Please analyze the screenshot and plan actions accordingly.` : 'No screenshot available - plan actions based on the request.'}

Generate a detailed automation plan with specific steps.`;

    try {
      const result = await streamText({
        model: openai(this.options.model),
        system: systemPrompt,
        prompt: userPrompt,
        temperature: this.options.temperature,
        maxTokens: this.options.maxTokens,
        tools: {
          generatePlan: tool({
            description: 'Generate a detailed automation plan',
            parameters: z.object({
              steps: z.array(z.object({
                type: z.enum(['mouse_click', 'mouse_double_click', 'mouse_right_click', 'keyboard_type', 'keyboard_key', 'wait', 'screenshot']),
                description: z.string(),
                parameters: z.record(z.any()),
                confidence: z.number().min(0).max(1),
              })),
              estimatedDuration: z.number(),
              confidence: z.number().min(0).max(1),
              risks: z.array(z.string()),
            }),
            execute: async ({ steps, estimatedDuration, confidence, risks }) => {
              return { steps, estimatedDuration, confidence, risks };
            },
          }),
        },
      });

      // Extract the plan from the AI response
      const planData = result.toolResults?.[0]?.result as any;
      
      if (!planData) {
        throw new Error('Failed to generate automation plan');
      }

      const plan: AutomationPlan = {
        id: `plan-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        prompt,
        vmId: '', // Will be set by caller
        steps: planData.steps.map((step: any, index: number) => ({
          id: `step-${index}`,
          type: step.type,
          description: step.description,
          parameters: step.parameters,
          expectedOutcome: `Step ${index + 1} completed`,
          confidence: step.confidence,
          dependencies: index > 0 ? [`step-${index - 1}`] : [],
          alternatives: [],
        })),
        estimatedDuration: planData.estimatedDuration,
        confidence: planData.confidence,
        risks: planData.risks,
        alternatives: [],
        createdAt: new Date().toISOString(),
      };

      return plan;
    } catch (error: any) {
      logger.error(`Failed to generate automation plan: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute a single automation step
   */
  private async executeAutomationStep(
    vmId: string, 
    step: PlannedStep, 
    sessionId: string
  ): Promise<AutomationAction> {
    const action: AutomationAction = {
      id: `action-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: step.type,
      status: 'executing',
      timestamp: new Date().toISOString(),
      description: step.description,
      parameters: step.parameters,
    };

    try {
      const startTime = Date.now();

      switch (step.type) {
        case 'mouse_click':
        case 'mouse_double_click':
        case 'mouse_right_click':
          await this.inputAutomationService.executeMouseAction(vmId, {
            type: step.type.replace('mouse_', '') as any,
            x: step.parameters.x,
            y: step.parameters.y,
            button: step.parameters.button || 'left',
          });
          break;

        case 'keyboard_type':
          await this.inputAutomationService.executeKeyboardAction(vmId, {
            type: 'type',
            text: step.parameters.text,
          });
          break;

        case 'keyboard_key':
          await this.inputAutomationService.executeKeyboardAction(vmId, {
            type: 'key',
            keys: step.parameters.keys,
          });
          break;

        case 'wait':
          await this.delay(step.parameters.duration || 1000);
          break;

        case 'screenshot':
          const screenshot = await this.screenshotService.takeScreenshot({ vmId });
          action.result = { screenshotId: screenshot.id };
          break;

        default:
          throw new Error(`Unsupported action type: ${step.type}`);
      }

      action.status = 'completed';
      action.duration = Date.now() - startTime;
      
      logger.debug(`Automation step completed: ${step.description}`);
      
      return action;
    } catch (error: any) {
      action.status = 'failed';
      action.error = error.message;
      action.duration = Date.now() - Date.parse(action.timestamp);
      
      logger.error(`Automation step failed: ${step.description} - ${error.message}`);
      
      return action;
    }
  }

  /**
   * Create a new automation session
   */
  private async createAutomationSession(vmId: string, metadata?: Record<string, any>): Promise<AutomationSession> {
    const session: AutomationSession = {
      id: `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      vmId,
      startTime: new Date().toISOString(),
      status: 'active',
      actions: [],
      metadata,
    };

    this.activeSessions.set(session.id, session);
    
    return session;
  }

  /**
   * Calculate task confidence based on action success rate
   */
  private calculateTaskConfidence(task: AiAutomationTask): number {
    if (task.actions.length === 0) return 0;
    
    const successfulActions = task.actions.filter(action => action.status === 'completed').length;
    return successfulActions / task.actions.length;
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Handle screenshot service events
    this.screenshotService.on('screenshotCaptured', (screenshot: ScreenshotData) => {
      this.emit('screenshotCaptured', screenshot);
    });

    // Handle input automation events
    this.inputAutomationService.on('actionExecuted', (action: AutomationAction) => {
      this.emit('actionExecuted', action);
    });

    // Handle session recorder events
    this.sessionRecorderService.on('recordingStarted', (sessionId: string) => {
      this.emit('recordingStarted', sessionId);
    });

    this.sessionRecorderService.on('recordingStopped', (sessionId: string) => {
      this.emit('recordingStopped', sessionId);
    });
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
