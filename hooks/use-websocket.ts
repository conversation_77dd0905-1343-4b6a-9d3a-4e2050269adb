/**
 * WebSocket Hook for Real-time Communication
 */

import { useState, useEffect, useRef, useCallback } from 'react'

export interface WebSocketMessage {
  type: string
  data?: any
  timestamp?: string
}

export interface UseWebSocketOptions {
  reconnectAttempts?: number
  reconnectInterval?: number
  heartbeatInterval?: number
  onOpen?: () => void
  onClose?: () => void
  onError?: (error: Event) => void
  onMessage?: (message: MessageEvent) => void
}

export function useWebSocket(url: string, options: UseWebSocketOptions = {}) {
  const {
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    heartbeatInterval = 30000,
    onOpen,
    onClose,
    onError,
    onMessage
  } = options

  const [isConnected, setIsConnected] = useState(false)
  const [lastMessage, setLastMessage] = useState<MessageEvent | null>(null)
  const [connectionState, setConnectionState] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [error, setError] = useState<string | null>(null)

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectCountRef = useRef(0)
  const shouldConnectRef = useRef(false)

  /**
   * Send message through WebSocket
   */
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      try {
        const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
        wsRef.current.send(messageStr)
        return true
      } catch (err) {
        console.error('[useWebSocket] Error sending message:', err)
        setError(err instanceof Error ? err.message : 'Failed to send message')
        return false
      }
    } else {
      console.warn('[useWebSocket] WebSocket is not connected')
      setError('WebSocket is not connected')
      return false
    }
  }, [])

  /**
   * Start heartbeat to keep connection alive
   */
  const startHeartbeat = useCallback(() => {
    if (heartbeatTimeoutRef.current) {
      clearInterval(heartbeatTimeoutRef.current)
    }

    heartbeatTimeoutRef.current = setInterval(() => {
      sendMessage({ type: 'ping' })
    }, heartbeatInterval)
  }, [heartbeatInterval, sendMessage])

  /**
   * Stop heartbeat
   */
  const stopHeartbeat = useCallback(() => {
    if (heartbeatTimeoutRef.current) {
      clearInterval(heartbeatTimeoutRef.current)
      heartbeatTimeoutRef.current = null
    }
  }, [])

  /**
   * Connect to WebSocket
   */
  const connect = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      return // Already connected
    }

    shouldConnectRef.current = true
    setConnectionState('connecting')
    setError(null)

    try {
      wsRef.current = new WebSocket(url)

      wsRef.current.onopen = () => {
        console.log('[useWebSocket] Connected to:', url)
        setIsConnected(true)
        setConnectionState('connected')
        setError(null)
        reconnectCountRef.current = 0
        
        startHeartbeat()
        onOpen?.()
      }

      wsRef.current.onclose = (event) => {
        console.log('[useWebSocket] Disconnected from:', url, event.code, event.reason)
        setIsConnected(false)
        setConnectionState('disconnected')
        
        stopHeartbeat()
        onClose?.()

        // Attempt reconnection if it was not a clean close and we should still be connected
        if (shouldConnectRef.current && event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
          scheduleReconnect()
        }
      }

      wsRef.current.onerror = (error) => {
        console.error('[useWebSocket] Error:', error)
        setConnectionState('error')
        setError('WebSocket connection error')
        onError?.(error)
      }

      wsRef.current.onmessage = (event) => {
        setLastMessage(event)
        onMessage?.(event)

        // Handle pong responses
        try {
          const message = JSON.parse(event.data)
          if (message.type === 'pong') {
            // Heartbeat response received
            return
          }
        } catch {
          // Not JSON, ignore
        }
      }

    } catch (err) {
      console.error('[useWebSocket] Failed to create WebSocket:', err)
      setError(err instanceof Error ? err.message : 'Failed to create WebSocket')
      setConnectionState('error')
    }
  }, [url, onOpen, onClose, onError, onMessage, reconnectAttempts, startHeartbeat, stopHeartbeat])

  /**
   * Schedule reconnection attempt
   */
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    reconnectCountRef.current++
    const delay = reconnectInterval * Math.pow(1.5, reconnectCountRef.current - 1) // Exponential backoff

    console.log(`[useWebSocket] Scheduling reconnect attempt ${reconnectCountRef.current}/${reconnectAttempts} in ${delay}ms`)

    reconnectTimeoutRef.current = setTimeout(() => {
      if (shouldConnectRef.current) {
        connect()
      }
    }, delay)
  }, [reconnectInterval, reconnectAttempts, connect])

  /**
   * Disconnect from WebSocket
   */
  const disconnect = useCallback(() => {
    shouldConnectRef.current = false
    
    // Clear reconnection timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    // Stop heartbeat
    stopHeartbeat()

    // Close WebSocket connection
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect')
      wsRef.current = null
    }

    setIsConnected(false)
    setConnectionState('disconnected')
    setError(null)
    reconnectCountRef.current = 0
  }, [stopHeartbeat])

  /**
   * Reconnect manually
   */
  const reconnect = useCallback(() => {
    disconnect()
    setTimeout(() => {
      connect()
    }, 100)
  }, [disconnect, connect])

  /**
   * Get connection status
   */
  const getReadyState = useCallback(() => {
    return wsRef.current?.readyState ?? WebSocket.CLOSED
  }, [])

  /**
   * Get connection status string
   */
  const getReadyStateString = useCallback(() => {
    const state = getReadyState()
    switch (state) {
      case WebSocket.CONNECTING: return 'CONNECTING'
      case WebSocket.OPEN: return 'OPEN'
      case WebSocket.CLOSING: return 'CLOSING'
      case WebSocket.CLOSED: return 'CLOSED'
      default: return 'UNKNOWN'
    }
  }, [getReadyState])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    // State
    isConnected,
    lastMessage,
    connectionState,
    error,
    readyState: getReadyState(),
    readyStateString: getReadyStateString(),

    // Actions
    connect,
    disconnect,
    reconnect,
    sendMessage,

    // Utilities
    clearError: () => setError(null)
  }
}
