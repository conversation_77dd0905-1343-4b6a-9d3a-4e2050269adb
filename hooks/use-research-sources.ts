/**
 * Research Sources React Hooks
 * 
 * Provides React hooks for managing research sources and analyses
 */

import { useCallback } from 'react';
import { useSession } from 'next-auth/react';
import useSWR, { mutate } from 'swr';

// Types
interface ResearchSource {
  id: string;
  projectId: string;
  type: 'web' | 'academic' | 'news' | 'social' | 'documentation';
  url: string;
  title: string;
  description: string;
  author?: string;
  publishedAt?: string;
  accessedAt: string;
  credibilityScore: number;
  biasScore: number;
  relevanceScore: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  metadata: Record<string, any>;
  content: {
    raw: string;
    processed: string;
    summary?: string;
    keyPoints?: string[];
  };
}

interface AnalysisResult {
  id: string;
  sourceId: string;
  projectId: string;
  type: 'summary' | 'insights' | 'sentiment' | 'bias' | 'credibility' | 'entities' | 'relationships';
  model: string;
  result: any;
  confidence: number;
  createdAt: string;
  metadata: {
    processingTime: number;
    tokensUsed: number;
    cost?: number;
    [key: string]: any;
  };
}

// API helper functions
const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch');
  }
  return response.json();
};

/**
 * Hook for managing research sources
 */
export function useResearchSources(projectId: string | null) {
  const { data: session } = useSession();
  
  const { data, error, isLoading } = useSWR(
    session && projectId ? `/api/deep-research/projects/${projectId}/sources` : null,
    fetcher
  );

  const getSourcesByType = useCallback((type: string) => {
    return data?.sources?.filter((source: ResearchSource) => source.type === type) || [];
  }, [data]);

  const getSourcesByCredibility = useCallback((minScore: number = 0.7) => {
    return data?.sources?.filter((source: ResearchSource) => source.credibilityScore >= minScore) || [];
  }, [data]);

  const getSourcesByRelevance = useCallback((minScore: number = 0.7) => {
    return data?.sources?.filter((source: ResearchSource) => source.relevanceScore >= minScore) || [];
  }, [data]);

  return {
    sources: data?.sources || [],
    total: data?.total || 0,
    isLoading,
    error,
    getSourcesByType,
    getSourcesByCredibility,
    getSourcesByRelevance,
    refresh: () => mutate(`/api/deep-research/projects/${projectId}/sources`)
  };
}

/**
 * Hook for managing research analyses
 */
export function useResearchAnalyses(projectId: string | null) {
  const { data: session } = useSession();
  
  const { data, error, isLoading } = useSWR(
    session && projectId ? `/api/deep-research/projects/${projectId}/analyses` : null,
    fetcher
  );

  const getAnalysesByType = useCallback((type: string) => {
    return data?.analyses?.filter((analysis: AnalysisResult) => analysis.type === type) || [];
  }, [data]);

  const getLatestAnalyses = useCallback((limit: number = 10) => {
    return data?.analyses
      ?.sort((a: AnalysisResult, b: AnalysisResult) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )
      ?.slice(0, limit) || [];
  }, [data]);

  const getHighConfidenceAnalyses = useCallback((minConfidence: number = 0.8) => {
    return data?.analyses?.filter((analysis: AnalysisResult) => analysis.confidence >= minConfidence) || [];
  }, [data]);

  return {
    analyses: data?.analyses || [],
    total: data?.total || 0,
    isLoading,
    error,
    getAnalysesByType,
    getLatestAnalyses,
    getHighConfidenceAnalyses,
    refresh: () => mutate(`/api/deep-research/projects/${projectId}/analyses`)
  };
}

/**
 * Hook for research insights and summaries
 */
export function useResearchInsights(projectId: string | null) {
  const { analyses } = useResearchAnalyses(projectId);

  const summaries = analyses.filter((analysis: AnalysisResult) => analysis.type === 'summary');
  const insights = analyses.filter((analysis: AnalysisResult) => analysis.type === 'insights');
  const biasAnalyses = analyses.filter((analysis: AnalysisResult) => analysis.type === 'bias');
  const credibilityAnalyses = analyses.filter((analysis: AnalysisResult) => analysis.type === 'credibility');
  const entityAnalyses = analyses.filter((analysis: AnalysisResult) => analysis.type === 'entities');

  const getOverallBiasScore = useCallback(() => {
    if (biasAnalyses.length === 0) return null;
    
    const totalScore = biasAnalyses.reduce((sum: number, analysis: AnalysisResult) => {
      return sum + (analysis.result?.overallBiasScore || 0);
    }, 0);
    
    return totalScore / biasAnalyses.length;
  }, [biasAnalyses]);

  const getOverallCredibilityScore = useCallback(() => {
    if (credibilityAnalyses.length === 0) return null;
    
    const totalScore = credibilityAnalyses.reduce((sum: number, analysis: AnalysisResult) => {
      return sum + (analysis.result?.overallCredibility || 0);
    }, 0);
    
    return totalScore / credibilityAnalyses.length;
  }, [credibilityAnalyses]);

  const getTopEntities = useCallback((limit: number = 10) => {
    const allEntities: any[] = [];
    
    entityAnalyses.forEach((analysis: AnalysisResult) => {
      if (analysis.result?.people) allEntities.push(...analysis.result.people);
      if (analysis.result?.organizations) allEntities.push(...analysis.result.organizations);
      if (analysis.result?.concepts) allEntities.push(...analysis.result.concepts);
      if (analysis.result?.locations) allEntities.push(...analysis.result.locations);
    });

    // Group by name and calculate average confidence
    const entityMap = new Map();
    allEntities.forEach(entity => {
      const key = entity.name?.toLowerCase();
      if (key) {
        if (entityMap.has(key)) {
          const existing = entityMap.get(key);
          existing.confidence = (existing.confidence + entity.confidence) / 2;
          existing.count++;
        } else {
          entityMap.set(key, { ...entity, count: 1 });
        }
      }
    });

    return Array.from(entityMap.values())
      .sort((a, b) => b.confidence * b.count - a.confidence * a.count)
      .slice(0, limit);
  }, [entityAnalyses]);

  const getKeyInsights = useCallback(() => {
    const allInsights: string[] = [];
    
    insights.forEach((analysis: AnalysisResult) => {
      if (analysis.result?.insights) {
        allInsights.push(...analysis.result.insights);
      }
    });

    return allInsights;
  }, [insights]);

  return {
    summaries,
    insights,
    biasAnalyses,
    credibilityAnalyses,
    entityAnalyses,
    getOverallBiasScore,
    getOverallCredibilityScore,
    getTopEntities,
    getKeyInsights
  };
}

/**
 * Hook for research statistics and metrics
 */
export function useResearchStats(projectId: string | null) {
  const { sources } = useResearchSources(projectId);
  const { analyses } = useResearchAnalyses(projectId);

  const sourceStats = {
    total: sources.length,
    byType: sources.reduce((acc: Record<string, number>, source: ResearchSource) => {
      acc[source.type] = (acc[source.type] || 0) + 1;
      return acc;
    }, {}),
    averageCredibility: sources.length > 0 
      ? sources.reduce((sum: number, source: ResearchSource) => sum + source.credibilityScore, 0) / sources.length
      : 0,
    averageRelevance: sources.length > 0
      ? sources.reduce((sum: number, source: ResearchSource) => sum + source.relevanceScore, 0) / sources.length
      : 0,
    averageBias: sources.length > 0
      ? sources.reduce((sum: number, source: ResearchSource) => sum + source.biasScore, 0) / sources.length
      : 0
  };

  const analysisStats = {
    total: analyses.length,
    byType: analyses.reduce((acc: Record<string, number>, analysis: AnalysisResult) => {
      acc[analysis.type] = (acc[analysis.type] || 0) + 1;
      return acc;
    }, {}),
    averageConfidence: analyses.length > 0
      ? analyses.reduce((sum: number, analysis: AnalysisResult) => sum + analysis.confidence, 0) / analyses.length
      : 0,
    totalProcessingTime: analyses.reduce((sum: number, analysis: AnalysisResult) => 
      sum + (analysis.metadata?.processingTime || 0), 0),
    totalTokensUsed: analyses.reduce((sum: number, analysis: AnalysisResult) => 
      sum + (analysis.metadata?.tokensUsed || 0), 0)
  };

  return {
    sourceStats,
    analysisStats
  };
}
