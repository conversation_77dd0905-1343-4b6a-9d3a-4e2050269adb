/**
 * React Hooks for File Synchronization
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { useWebSocket } from './use-websocket'

// Types
export interface SyncStats {
  totalFiles: number
  syncedFiles: number
  conflictedFiles: number
  pendingFiles: number
  lastSyncTime: Date | null
  totalSize: number
}

export interface SyncResult {
  success: boolean
  operation: string
  direction: string
  filesProcessed: number
  filesCreated: number
  filesUpdated: number
  filesDeleted: number
  conflictsFound: number
  conflictsResolved: number
  bytesTransferred: number
  duration: number
  errors: any[]
  warnings: string[]
  sessionId: string
}

export interface SyncProgress {
  sessionId: string
  phase: string
  progress: number
  currentFile?: string
  filesProcessed: number
  totalFiles: number
  bytesTransferred: number
  totalBytes: number
  estimatedTimeRemaining?: number
  errors: any[]
}

export interface ConflictFile {
  id: string
  path: string
  conflictData: any
  syncStatus: string
}

export interface SyncConfig {
  autoSync?: boolean
  syncInterval?: number
  conflictResolution?: string
  excludePatterns?: string[]
  includePatterns?: string[]
  maxFileSize?: number
  enableWatchers?: boolean
  batchSize?: number
  retryAttempts?: number
  retryDelay?: number
}

export interface SyncOptions {
  direction?: 'nodebox_to_db' | 'db_to_nodebox' | 'bidirectional'
  force?: boolean
  dryRun?: boolean
  includePatterns?: string[]
  excludePatterns?: string[]
  conflictResolution?: string
}

/**
 * Main file sync hook
 */
export function useFileSync(projectId: string) {
  const [stats, setStats] = useState<SyncStats | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastSync, setLastSync] = useState<Date | null>(null)
  const [config, setConfig] = useState<SyncConfig | null>(null)

  // WebSocket connection for real-time updates
  const { 
    isConnected, 
    lastMessage, 
    sendMessage,
    connect,
    disconnect 
  } = useWebSocket(`ws://localhost:3001/api/file-sync/ws`)

  // Subscribe to project events on connection
  useEffect(() => {
    if (isConnected && projectId) {
      sendMessage({
        type: 'subscribe',
        projectId
      })
    }
  }, [isConnected, projectId, sendMessage])

  // Handle WebSocket messages
  useEffect(() => {
    if (lastMessage) {
      const message = JSON.parse(lastMessage.data)
      
      switch (message.type) {
        case 'sync-complete':
          setLastSync(new Date())
          refreshStats()
          break
        case 'sync-error':
          setError(message.data.message)
          break
        case 'file-change':
          // Optionally refresh stats on file changes
          if (config?.autoSync) {
            refreshStats()
          }
          break
      }
    }
  }, [lastMessage, config?.autoSync])

  /**
   * Refresh sync statistics
   */
  const refreshStats = useCallback(async () => {
    if (!projectId) return

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/file-sync?projectId=${projectId}&action=stats`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch sync stats')
      }

      setStats(data.stats)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [projectId])

  /**
   * Perform full synchronization
   */
  const fullSync = useCallback(async (options: SyncOptions = {}) => {
    if (!projectId) throw new Error('Project ID is required')

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/file-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          type: 'full',
          options
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Full sync failed')
      }

      setLastSync(new Date())
      await refreshStats()
      
      return data.result as SyncResult
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Unknown error'
      setError(error)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [projectId, refreshStats])

  /**
   * Perform incremental synchronization
   */
  const incrementalSync = useCallback(async (options: SyncOptions = {}) => {
    if (!projectId) throw new Error('Project ID is required')

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/file-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          type: 'incremental',
          options
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Incremental sync failed')
      }

      setLastSync(new Date())
      await refreshStats()
      
      return data.result as SyncResult
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Unknown error'
      setError(error)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [projectId, refreshStats])

  /**
   * Sync specific files
   */
  const syncFiles = useCallback(async (filePaths: string[], options: SyncOptions = {}) => {
    if (!projectId) throw new Error('Project ID is required')
    if (!filePaths.length) throw new Error('File paths are required')

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/file-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId,
          type: 'files',
          filePaths,
          options
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'File sync failed')
      }

      setLastSync(new Date())
      await refreshStats()
      
      return data.result as SyncResult
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Unknown error'
      setError(error)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [projectId, refreshStats])

  /**
   * Get sync configuration
   */
  const getConfig = useCallback(async () => {
    if (!projectId) return

    try {
      const response = await fetch(`/api/file-sync?projectId=${projectId}&action=config`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch config')
      }

      setConfig(data.config)
      return data.config as SyncConfig
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }, [projectId])

  /**
   * Update sync configuration
   */
  const updateConfig = useCallback(async (newConfig: Partial<SyncConfig>) => {
    if (!projectId) throw new Error('Project ID is required')

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/file-sync', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update-config',
          projectId,
          config: newConfig
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update config')
      }

      setConfig(data.config)
      return data.config as SyncConfig
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Unknown error'
      setError(error)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [projectId])

  /**
   * Start file watching
   */
  const startWatching = useCallback(async () => {
    if (!projectId) throw new Error('Project ID is required')

    try {
      const response = await fetch('/api/file-sync', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'start-watching',
          projectId
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to start watching')
      }

      return true
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      throw err
    }
  }, [projectId])

  /**
   * Stop file watching
   */
  const stopWatching = useCallback(async () => {
    if (!projectId) throw new Error('Project ID is required')

    try {
      const response = await fetch('/api/file-sync', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'stop-watching',
          projectId
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to stop watching')
      }

      return true
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      throw err
    }
  }, [projectId])

  // Initialize on mount
  useEffect(() => {
    if (projectId) {
      refreshStats()
      getConfig()
      connect()
    }

    return () => {
      if (projectId) {
        sendMessage({
          type: 'unsubscribe',
          projectId
        })
        disconnect()
      }
    }
  }, [projectId])

  return {
    // State
    stats,
    isLoading,
    error,
    lastSync,
    config,
    isConnected,

    // Actions
    fullSync,
    incrementalSync,
    syncFiles,
    refreshStats,
    getConfig,
    updateConfig,
    startWatching,
    stopWatching,

    // Utilities
    clearError: () => setError(null)
  }
}
