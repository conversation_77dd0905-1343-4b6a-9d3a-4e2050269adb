/**
 * React Hook for File Conflict Resolution
 */

import { useState, useCallback, useEffect } from 'react'

export interface ConflictFile {
  id: string
  projectId: string
  path: string
  filename: string
  directory?: string
  content: string
  contentHash: string
  syncStatus: string
  conflictData: {
    type: 'content' | 'metadata' | 'both'
    nodeboxContent: string
    databaseContent: string
    nodeboxHash: string
    databaseHash: string
    nodeboxModified: string
    databaseModified: string
    resolution?: string
    resolvedContent?: string
    resolvedBy?: string
    resolvedAt?: string
  }
  createdAt: string
  updatedAt: string
}

export interface ConflictResolutionOptions {
  strategy: 'timestamp_based' | 'user_choice' | 'nodebox_wins' | 'database_wins' | 'merge_content' | 'create_backup'
  userChoice?: 'nodebox' | 'database' | 'merge'
  mergeStrategy?: 'line_by_line' | 'block_based' | 'semantic'
  createBackup?: boolean
  backupSuffix?: string
}

export interface ConflictSummary {
  type: string
  nodeboxSize: number
  databaseSize: number
  timeDifference: number
  hasContentChanges: boolean
  hasMetadataChanges: boolean
  recommendedStrategy: string
  canAutoResolve: boolean
}

export function useConflictResolution(projectId: string) {
  const [conflicts, setConflicts] = useState<ConflictFile[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [resolvingFiles, setResolvingFiles] = useState<Set<string>>(new Set())

  /**
   * Fetch conflicts for the project
   */
  const fetchConflicts = useCallback(async () => {
    if (!projectId) return

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/file-sync?projectId=${projectId}&action=conflicts`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch conflicts')
      }

      setConflicts(data.conflicts || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [projectId])

  /**
   * Resolve a specific conflict
   */
  const resolveConflict = useCallback(async (
    filePath: string,
    options: ConflictResolutionOptions
  ) => {
    if (!projectId) throw new Error('Project ID is required')

    try {
      setResolvingFiles(prev => new Set(prev).add(filePath))
      setError(null)

      const response = await fetch('/api/file-sync', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'resolve-conflict',
          projectId,
          filePath,
          ...options
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to resolve conflict')
      }

      // Remove resolved conflict from list
      setConflicts(prev => prev.filter(conflict => conflict.path !== filePath))

      return true
    } catch (err) {
      const error = err instanceof Error ? err.message : 'Unknown error'
      setError(error)
      throw err
    } finally {
      setResolvingFiles(prev => {
        const newSet = new Set(prev)
        newSet.delete(filePath)
        return newSet
      })
    }
  }, [projectId])

  /**
   * Resolve multiple conflicts with the same strategy
   */
  const resolveMultipleConflicts = useCallback(async (
    filePaths: string[],
    options: ConflictResolutionOptions
  ) => {
    const results = []
    const errors = []

    for (const filePath of filePaths) {
      try {
        await resolveConflict(filePath, options)
        results.push({ filePath, success: true })
      } catch (err) {
        errors.push({ 
          filePath, 
          error: err instanceof Error ? err.message : 'Unknown error' 
        })
      }
    }

    if (errors.length > 0) {
      setError(`Failed to resolve ${errors.length} conflicts`)
    }

    return { results, errors }
  }, [resolveConflict])

  /**
   * Auto-resolve conflicts that can be safely resolved
   */
  const autoResolveConflicts = useCallback(async () => {
    const autoResolvableConflicts = conflicts.filter(conflict => {
      const summary = getConflictSummary(conflict)
      return summary.canAutoResolve
    })

    if (autoResolvableConflicts.length === 0) {
      return { resolved: 0, errors: [] }
    }

    const results = []
    const errors = []

    for (const conflict of autoResolvableConflicts) {
      try {
        const summary = getConflictSummary(conflict)
        await resolveConflict(conflict.path, {
          strategy: summary.recommendedStrategy as any
        })
        results.push(conflict.path)
      } catch (err) {
        errors.push({
          filePath: conflict.path,
          error: err instanceof Error ? err.message : 'Unknown error'
        })
      }
    }

    return { resolved: results.length, errors }
  }, [conflicts, resolveConflict])

  /**
   * Get conflict summary for display
   */
  const getConflictSummary = useCallback((conflict: ConflictFile): ConflictSummary => {
    const conflictData = conflict.conflictData
    const nodeboxModified = new Date(conflictData.nodeboxModified)
    const databaseModified = new Date(conflictData.databaseModified)
    const timeDifference = Math.abs(nodeboxModified.getTime() - databaseModified.getTime())

    const hasContentChanges = conflictData.nodeboxContent !== conflictData.databaseContent
    const hasMetadataChanges = conflictData.type === 'metadata' || conflictData.type === 'both'

    // Determine recommended strategy
    let recommendedStrategy = 'user_choice'
    let canAutoResolve = false

    if (!hasContentChanges) {
      recommendedStrategy = 'timestamp_based'
      canAutoResolve = true
    } else if (timeDifference > 60000) { // 1 minute
      recommendedStrategy = 'timestamp_based'
      canAutoResolve = true
    } else if (isContentMergeable(conflictData.nodeboxContent, conflictData.databaseContent)) {
      recommendedStrategy = 'merge_content'
      canAutoResolve = true
    }

    return {
      type: conflictData.type,
      nodeboxSize: conflictData.nodeboxContent.length,
      databaseSize: conflictData.databaseContent.length,
      timeDifference,
      hasContentChanges,
      hasMetadataChanges,
      recommendedStrategy,
      canAutoResolve
    }
  }, [])

  /**
   * Check if content can be automatically merged
   */
  const isContentMergeable = useCallback((nodeboxContent: string, databaseContent: string): boolean => {
    // Simple heuristic - if one version is a subset of the other, it's mergeable
    return nodeboxContent.includes(databaseContent) || databaseContent.includes(nodeboxContent)
  }, [])

  /**
   * Get diff between nodebox and database versions
   */
  const getDiff = useCallback((conflict: ConflictFile) => {
    const { nodeboxContent, databaseContent } = conflict.conflictData
    
    // Simple line-by-line diff
    const nodeboxLines = nodeboxContent.split('\n')
    const databaseLines = databaseContent.split('\n')
    
    const diff = []
    const maxLines = Math.max(nodeboxLines.length, databaseLines.length)
    
    for (let i = 0; i < maxLines; i++) {
      const nodeboxLine = nodeboxLines[i] || ''
      const databaseLine = databaseLines[i] || ''
      
      if (nodeboxLine === databaseLine) {
        diff.push({ type: 'equal', line: nodeboxLine, lineNumber: i + 1 })
      } else {
        if (nodeboxLine) {
          diff.push({ type: 'removed', line: nodeboxLine, lineNumber: i + 1 })
        }
        if (databaseLine) {
          diff.push({ type: 'added', line: databaseLine, lineNumber: i + 1 })
        }
      }
    }
    
    return diff
  }, [])

  /**
   * Preview merge result
   */
  const previewMerge = useCallback((conflict: ConflictFile, strategy: 'line_by_line' | 'block_based' = 'line_by_line'): string => {
    const { nodeboxContent, databaseContent } = conflict.conflictData
    
    if (strategy === 'line_by_line') {
      const nodeboxLines = nodeboxContent.split('\n')
      const databaseLines = databaseContent.split('\n')
      const mergedLines: string[] = []
      
      const maxLines = Math.max(nodeboxLines.length, databaseLines.length)
      
      for (let i = 0; i < maxLines; i++) {
        const nodeboxLine = nodeboxLines[i] || ''
        const databaseLine = databaseLines[i] || ''
        
        if (nodeboxLine === databaseLine) {
          mergedLines.push(nodeboxLine)
        } else {
          // Add conflict markers
          mergedLines.push('<<<<<<< Nodebox')
          mergedLines.push(nodeboxLine)
          mergedLines.push('=======')
          mergedLines.push(databaseLine)
          mergedLines.push('>>>>>>> Database')
        }
      }
      
      return mergedLines.join('\n')
    }
    
    // For block-based, return a simple concatenation with markers
    return `<<<<<<< Nodebox\n${nodeboxContent}\n=======\n${databaseContent}\n>>>>>>> Database`
  }, [])

  /**
   * Get conflicts by type
   */
  const getConflictsByType = useCallback(() => {
    const byType = {
      content: conflicts.filter(c => c.conflictData.type === 'content'),
      metadata: conflicts.filter(c => c.conflictData.type === 'metadata'),
      both: conflicts.filter(c => c.conflictData.type === 'both')
    }
    
    return byType
  }, [conflicts])

  /**
   * Get auto-resolvable conflicts
   */
  const getAutoResolvableConflicts = useCallback(() => {
    return conflicts.filter(conflict => {
      const summary = getConflictSummary(conflict)
      return summary.canAutoResolve
    })
  }, [conflicts, getConflictSummary])

  // Load conflicts on mount and when projectId changes
  useEffect(() => {
    if (projectId) {
      fetchConflicts()
    }
  }, [projectId, fetchConflicts])

  return {
    // State
    conflicts,
    isLoading,
    error,
    resolvingFiles,

    // Actions
    fetchConflicts,
    resolveConflict,
    resolveMultipleConflicts,
    autoResolveConflicts,

    // Utilities
    getConflictSummary,
    getDiff,
    previewMerge,
    getConflictsByType,
    getAutoResolvableConflicts,
    clearError: () => setError(null),

    // Computed values
    hasConflicts: conflicts.length > 0,
    conflictCount: conflicts.length,
    autoResolvableCount: getAutoResolvableConflicts().length
  }
}
