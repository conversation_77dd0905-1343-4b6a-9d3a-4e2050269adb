/**
 * Deep Research React Hooks
 * 
 * Provides React hooks for interacting with the deep research API
 */

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import useSWR, { mutate } from 'swr';

// Types
interface ResearchProject {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'active' | 'completed' | 'archived';
  tags: string[];
  createdAt: string;
  updatedAt: string;
  userId: string;
  settings: {
    autoAnalysis: boolean;
    sourceTypes: string[];
    analysisTypes: string[];
    maxSources: number;
    qualityThreshold: number;
    biasDetection: boolean;
    factChecking: boolean;
    realTimeUpdates: boolean;
  };
  metadata: {
    totalSources: number;
    totalAnalyses: number;
    lastActivity: string;
  };
}

interface ResearchSession {
  id: string;
  projectId: string;
  query: string;
  status: 'running' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  results: {
    sourcesFound: number;
    sourcesProcessed: number;
    analysesCompleted: number;
    entitiesExtracted: number;
    relationshipsFound: number;
  };
  metadata: any;
}

interface ResearchOptions {
  sources: {
    web?: {
      enabled: boolean;
      maxResults?: number;
      domains?: string[];
      excludeDomains?: string[];
    };
    academic?: {
      enabled: boolean;
      maxResults?: number;
      databases?: ('arxiv' | 'pubmed' | 'scholar')[];
      dateRange?: {
        from: string;
        to: string;
      };
    };
    news?: {
      enabled: boolean;
      maxResults?: number;
      sources?: string[];
      languages?: string[];
    };
    social?: {
      enabled: boolean;
      platforms?: string[];
      maxResults?: number;
    };
  };
  analysis?: {
    summarize?: boolean;
    extractEntities?: boolean;
    detectBias?: boolean;
    checkCredibility?: boolean;
    findRelationships?: boolean;
  };
}

// API helper functions
const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch');
  }
  return response.json();
};

/**
 * Hook for managing research projects
 */
export function useResearchProjects() {
  const { data: session } = useSession();
  const { data, error, isLoading } = useSWR(
    session ? '/api/deep-research/projects' : null,
    fetcher
  );

  const createProject = useCallback(async (
    title: string,
    description: string,
    settings?: Partial<ResearchProject['settings']>
  ) => {
    const response = await fetch('/api/deep-research/projects', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ title, description, settings })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create project');
    }

    const result = await response.json();
    mutate('/api/deep-research/projects');
    return result.project;
  }, []);

  const updateProject = useCallback(async (
    projectId: string,
    updates: Partial<ResearchProject>
  ) => {
    const response = await fetch(`/api/deep-research/projects/${projectId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update project');
    }

    const result = await response.json();
    mutate('/api/deep-research/projects');
    mutate(`/api/deep-research/projects/${projectId}`);
    return result.project;
  }, []);

  const deleteProject = useCallback(async (projectId: string) => {
    const response = await fetch(`/api/deep-research/projects/${projectId}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete project');
    }

    mutate('/api/deep-research/projects');
  }, []);

  return {
    projects: data?.projects || [],
    isLoading,
    error,
    createProject,
    updateProject,
    deleteProject,
    refresh: () => mutate('/api/deep-research/projects')
  };
}

/**
 * Hook for managing a specific research project
 */
export function useResearchProject(projectId: string | null) {
  const { data: session } = useSession();
  const { data, error, isLoading } = useSWR(
    session && projectId ? `/api/deep-research/projects/${projectId}` : null,
    fetcher
  );

  return {
    project: data?.project,
    stats: data?.stats,
    isLoading,
    error,
    refresh: () => mutate(`/api/deep-research/projects/${projectId}`)
  };
}

/**
 * Hook for starting and monitoring research sessions
 */
export function useResearchSession() {
  const [currentSession, setCurrentSession] = useState<ResearchSession | null>(null);
  const [isStarting, setIsStarting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const startResearch = useCallback(async (
    query: string,
    options: ResearchOptions,
    projectId?: string
  ) => {
    setIsStarting(true);
    setError(null);

    try {
      const response = await fetch('/api/deep-research/research', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, projectId, options })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start research');
      }

      const result = await response.json();
      setCurrentSession(result.session);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    } finally {
      setIsStarting(false);
    }
  }, []);

  const getSessionStatus = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/deep-research/research/${sessionId}`);
      
      if (!response.ok) {
        throw new Error('Failed to get session status');
      }

      const result = await response.json();
      setCurrentSession(result.session);
      return result.session;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    }
  }, []);

  const cancelSession = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/deep-research/research/${sessionId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to cancel session');
      }

      setCurrentSession(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    }
  }, []);

  return {
    currentSession,
    isStarting,
    error,
    startResearch,
    getSessionStatus,
    cancelSession,
    clearError: () => setError(null)
  };
}

/**
 * Hook for polling research session status
 */
export function useResearchSessionPolling(sessionId: string | null, interval = 5000) {
  const [session, setSession] = useState<ResearchSession | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  useEffect(() => {
    if (!sessionId) return;

    setIsPolling(true);
    
    const pollStatus = async () => {
      try {
        const response = await fetch(`/api/deep-research/research/${sessionId}`);
        if (response.ok) {
          const result = await response.json();
          setSession(result.session);
          
          // Stop polling if session is completed or failed
          if (result.session.status === 'completed' || result.session.status === 'failed') {
            setIsPolling(false);
            return;
          }
        }
      } catch (error) {
        console.error('Error polling session status:', error);
      }
    };

    // Initial poll
    pollStatus();

    // Set up interval polling
    const intervalId = setInterval(pollStatus, interval);

    return () => {
      clearInterval(intervalId);
      setIsPolling(false);
    };
  }, [sessionId, interval]);

  return { session, isPolling };
}
