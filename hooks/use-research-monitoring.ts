/**
 * Research Monitoring React Hook
 * 
 * Provides real-time monitoring and progress tracking for research sessions
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';

// Types
interface ResearchProgress {
  sessionId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: {
    currentPhase: 'collection' | 'analysis' | 'synthesis' | 'completed';
    sourcesFound: number;
    sourcesProcessed: number;
    analysesCompleted: number;
    entitiesExtracted: number;
    relationshipsFound: number;
    estimatedTimeRemaining?: number;
  };
  metrics: {
    startTime: string;
    elapsedTime: number;
    averageSourceProcessingTime: number;
    averageAnalysisTime: number;
  };
  errors: Array<{
    timestamp: string;
    type: 'source_collection' | 'analysis' | 'synthesis' | 'system';
    message: string;
    details?: any;
  }>;
  warnings: Array<{
    timestamp: string;
    type: 'rate_limit' | 'quality' | 'timeout' | 'api_limit';
    message: string;
  }>;
}

interface ResearchEvent {
  type: 'progress_update' | 'source_collected' | 'analysis_complete' | 'error' | 'warning' | 'completed';
  timestamp: string;
  sessionId: string;
  data: any;
}

/**
 * Hook for monitoring research session progress
 */
export function useResearchMonitoring(sessionId: string | null) {
  const { data: session } = useSession();
  const [progress, setProgress] = useState<ResearchProgress | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [events, setEvents] = useState<ResearchEvent[]>([]);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  // Connect to WebSocket for real-time updates
  const connectWebSocket = useCallback(() => {
    if (!session?.user?.id || !sessionId) return;

    try {
      // In a real implementation, this would be a WebSocket connection
      // For now, we'll simulate with polling
      setIsConnected(true);
      
      // Simulate WebSocket connection
      console.log(`Connecting to research monitoring for session: ${sessionId}`);
      
      // Reset reconnect attempts on successful connection
      setReconnectAttempts(0);
    } catch (error) {
      console.error('Failed to connect to research monitoring:', error);
      setIsConnected(false);
      
      // Attempt to reconnect
      if (reconnectAttempts < 5) {
        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
        reconnectTimeoutRef.current = setTimeout(() => {
          setReconnectAttempts(prev => prev + 1);
          connectWebSocket();
        }, delay);
      }
    }
  }, [session?.user?.id, sessionId, reconnectAttempts]);

  // Disconnect WebSocket
  const disconnectWebSocket = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setIsConnected(false);
    setReconnectAttempts(0);
  }, []);

  // Poll for progress updates (fallback when WebSocket is not available)
  const pollProgress = useCallback(async () => {
    if (!sessionId || !session?.user?.id) return;

    try {
      const response = await fetch(`/api/deep-research/research/${sessionId}`);
      if (response.ok) {
        const data = await response.json();
        const researchSession = data.session;
        
        // Convert session data to progress format
        const newProgress: ResearchProgress = {
          sessionId: researchSession.id,
          status: researchSession.status,
          progress: {
            currentPhase: determineCurrentPhase(researchSession),
            sourcesFound: researchSession.results.sourcesFound,
            sourcesProcessed: researchSession.results.sourcesProcessed,
            analysesCompleted: researchSession.results.analysesCompleted,
            entitiesExtracted: researchSession.results.entitiesExtracted,
            relationshipsFound: researchSession.results.relationshipsFound,
            estimatedTimeRemaining: calculateEstimatedTime(researchSession)
          },
          metrics: {
            startTime: researchSession.startedAt,
            elapsedTime: Date.now() - new Date(researchSession.startedAt).getTime(),
            averageSourceProcessingTime: calculateAverageProcessingTime(researchSession, 'source'),
            averageAnalysisTime: calculateAverageProcessingTime(researchSession, 'analysis')
          },
          errors: researchSession.metadata?.errors || [],
          warnings: researchSession.metadata?.warnings || []
        };

        setProgress(newProgress);

        // Add progress event
        const event: ResearchEvent = {
          type: 'progress_update',
          timestamp: new Date().toISOString(),
          sessionId: researchSession.id,
          data: newProgress
        };
        
        setEvents(prev => [...prev.slice(-99), event]); // Keep last 100 events
      }
    } catch (error) {
      console.error('Error polling research progress:', error);
    }
  }, [sessionId, session?.user?.id]);

  // Start monitoring
  const startMonitoring = useCallback(() => {
    if (!sessionId) return;

    connectWebSocket();
    
    // Start polling as fallback
    const pollInterval = setInterval(pollProgress, 2000); // Poll every 2 seconds
    
    return () => {
      clearInterval(pollInterval);
      disconnectWebSocket();
    };
  }, [sessionId, connectWebSocket, pollProgress, disconnectWebSocket]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    disconnectWebSocket();
  }, [disconnectWebSocket]);

  // Get progress percentage
  const getProgressPercentage = useCallback((): number => {
    if (!progress) return 0;

    const { sourcesFound, sourcesProcessed, analysesCompleted } = progress.progress;
    
    if (sourcesFound === 0) return 0;
    
    // Weight different phases
    const collectionProgress = (sourcesProcessed / sourcesFound) * 0.4;
    const analysisProgress = (analysesCompleted / sourcesProcessed) * 0.5;
    const synthesisProgress = progress.status === 'completed' ? 0.1 : 0;
    
    return Math.min(100, (collectionProgress + analysisProgress + synthesisProgress) * 100);
  }, [progress]);

  // Get current phase description
  const getCurrentPhaseDescription = useCallback((): string => {
    if (!progress) return 'Initializing...';

    switch (progress.progress.currentPhase) {
      case 'collection':
        return `Collecting sources (${progress.progress.sourcesProcessed}/${progress.progress.sourcesFound})`;
      case 'analysis':
        return `Analyzing content (${progress.progress.analysesCompleted} analyses completed)`;
      case 'synthesis':
        return 'Synthesizing insights...';
      case 'completed':
        return 'Research completed';
      default:
        return 'Processing...';
    }
  }, [progress]);

  // Get estimated time remaining
  const getEstimatedTimeRemaining = useCallback((): string | null => {
    if (!progress?.progress.estimatedTimeRemaining) return null;

    const minutes = Math.ceil(progress.progress.estimatedTimeRemaining / 60000);
    
    if (minutes < 1) return 'Less than 1 minute';
    if (minutes === 1) return '1 minute';
    if (minutes < 60) return `${minutes} minutes`;
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 1 && remainingMinutes === 0) return '1 hour';
    if (remainingMinutes === 0) return `${hours} hours`;
    
    return `${hours}h ${remainingMinutes}m`;
  }, [progress]);

  // Effect to start/stop monitoring based on sessionId
  useEffect(() => {
    if (sessionId) {
      const cleanup = startMonitoring();
      return cleanup;
    } else {
      stopMonitoring();
    }
  }, [sessionId, startMonitoring, stopMonitoring]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnectWebSocket();
    };
  }, [disconnectWebSocket]);

  return {
    progress,
    events,
    isConnected,
    isMonitoring: !!sessionId && isConnected,
    getProgressPercentage,
    getCurrentPhaseDescription,
    getEstimatedTimeRemaining,
    startMonitoring,
    stopMonitoring,
    reconnectAttempts
  };
}

// Helper functions
function determineCurrentPhase(session: any): 'collection' | 'analysis' | 'synthesis' | 'completed' {
  if (session.status === 'completed') return 'completed';
  
  const { sourcesFound, sourcesProcessed, analysesCompleted } = session.results;
  
  if (sourcesProcessed < sourcesFound) return 'collection';
  if (analysesCompleted < sourcesProcessed) return 'analysis';
  
  return 'synthesis';
}

function calculateEstimatedTime(session: any): number | undefined {
  const { sourcesFound, sourcesProcessed, analysesCompleted } = session.results;
  const elapsedTime = Date.now() - new Date(session.startedAt).getTime();
  
  if (sourcesProcessed === 0) return undefined;
  
  const avgTimePerSource = elapsedTime / sourcesProcessed;
  const remainingSources = sourcesFound - sourcesProcessed;
  const remainingAnalyses = sourcesProcessed - analysesCompleted;
  
  return (remainingSources + remainingAnalyses) * avgTimePerSource;
}

function calculateAverageProcessingTime(session: any, type: 'source' | 'analysis'): number {
  const elapsedTime = Date.now() - new Date(session.startedAt).getTime();
  
  if (type === 'source') {
    return session.results.sourcesProcessed > 0 
      ? elapsedTime / session.results.sourcesProcessed 
      : 0;
  } else {
    return session.results.analysesCompleted > 0 
      ? elapsedTime / session.results.analysesCompleted 
      : 0;
  }
}
