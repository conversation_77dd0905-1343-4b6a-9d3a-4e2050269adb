"use client"

import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import type { DeploymentRequest, DeploymentResponse } from "@/types/ai-features"

/**
 * Hook to create and execute deployment plans
 */
export function useDeploymentAssistant() {
  const [isCreating, setIsCreating] = useState(false)
  const [isExecuting, setIsExecuting] = useState(false)
  const [deployment, setDeployment] = useState<DeploymentResponse["deployment"] | null>(null)

  // Create a new deployment plan
  const createDeploymentPlan = async (request: DeploymentRequest) => {
    setIsCreating(true)
    try {
      const response = await fetch("/api/ai/deployment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to create deployment plan")
      }

      const data = await response.json()
      setDeployment(data.data.deployment)
      return data.data.deployment
    } catch (error) {
      toast({
        title: "Deployment Plan Error",
        description: error instanceof Error ? error.message : "Failed to create deployment plan",
        variant: "destructive",
      })
      throw error
    } finally {
      setIsCreating(false)
    }
  }

  // Execute a deployment plan
  const executeDeployment = async ({
    vmId,
    serverId,
  }: {
    vmId: string
    serverId: string
  }) => {
    if (!deployment) {
      throw new Error("No deployment plan to execute")
    }

    setIsExecuting(true)
    try {
      const response = await fetch("/api/ai/deployment", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          deployment,
          vmId,
          serverId,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to execute deployment")
      }

      const data = await response.json()
      setDeployment(data.data.deployment)
      return data.data.deployment
    } catch (error) {
      toast({
        title: "Deployment Execution Error",
        description: error instanceof Error ? error.message : "Failed to execute deployment",
        variant: "destructive",
      })
      throw error
    } finally {
      setIsExecuting(false)
    }
  }

  // Execute a single deployment step
  const executeDeploymentStepMutation = useMutation({
    mutationFn: async ({
      stepId,
      vmId,
      serverId,
    }: {
      stepId: string
      vmId: string
      serverId: string
    }) => {
      if (!deployment) {
        throw new Error("No deployment plan")
      }

      const step = deployment.steps.find((s) => s.id === stepId)
      if (!step) {
        throw new Error(`Step with ID ${stepId} not found`)
      }

      const response = await fetch("/api/ai/deployment/step", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          step,
          vmId,
          serverId,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to execute deployment step")
      }

      const data = await response.json()
      return data.data.step
    },
    onSuccess: (updatedStep) => {
      if (deployment) {
        // Update the step in the deployment
        const updatedSteps = deployment.steps.map((step) =>
          step.id === updatedStep.id ? updatedStep : step
        )
        setDeployment({
          ...deployment,
          steps: updatedSteps,
        })
      }

      toast({
        title: "Step Executed",
        description: `Step "${updatedStep.name}" has been executed ${
          updatedStep.status === "completed" ? "successfully" : "with errors"
        }.`,
        variant: updatedStep.status === "completed" ? "default" : "destructive",
      })
    },
    onError: (error) => {
      toast({
        title: "Step Execution Failed",
        description: error instanceof Error ? error.message : "Failed to execute step",
        variant: "destructive",
      })
    },
  })

  return {
    isCreating,
    isExecuting,
    deployment,
    createDeploymentPlan,
    executeDeployment,
    executeDeploymentStep: executeDeploymentStepMutation,
    clearDeployment: () => setDeployment(null),
  }
}
