"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import type { NLSearchRequest, NLSearchResponse } from "@/types/ai-features"

/**
 * Hook to search VMs using natural language
 */
export function useNaturalLanguageSearch() {
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<NLSearchResponse["results"] | null>(null)
  const [isSearching, setIsSearching] = useState(false)

  const search = async (query: string) => {
    if (!query.trim()) {
      return
    }

    setIsSearching(true)
    setSearchQuery(query)

    try {
      const response = await fetch("/api/ai/search", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to search")
      }

      const data = await response.json()
      setSearchResults(data.data)
      return data.data
    } catch (error) {
      toast({
        title: "Search Error",
        description: error instanceof Error ? error.message : "Failed to search",
        variant: "destructive",
      })
      throw error
    } finally {
      setIsSearching(false)
    }
  }

  return {
    searchQuery,
    searchResults,
    isSearching,
    search,
    clearSearch: () => {
      setSearchQuery("")
      setSearchResults(null)
    },
  }
}

/**
 * Hook to get search history
 */
export function useSearchHistory() {
  return useQuery({
    queryKey: ["search-history"],
    queryFn: async () => {
      const response = await fetch("/api/user/search-history")
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch search history")
      }
      const data = await response.json()
      return data.data
    },
  })
}

/**
 * Hook to get search suggestions based on user behavior
 */
export function useSearchSuggestions() {
  return useQuery({
    queryKey: ["search-suggestions"],
    queryFn: async () => {
      const response = await fetch("/api/user/search-suggestions")
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch search suggestions")
      }
      const data = await response.json()
      return data.data
    },
  })
}
