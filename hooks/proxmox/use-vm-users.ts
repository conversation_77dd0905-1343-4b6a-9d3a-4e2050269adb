"use client"

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"

export interface VMUserAssignment {
  id: string
  vmId: string
  userId: string
  role: "ADMIN" | "OPERATOR" | "VIEWER"
  permissions?: Record<string, any>
  createdAt: string
  updatedAt: string
  user?: {
    id: string
    name?: string
    email: string
    image?: string
  }
}

export interface VMUserAssignmentInput {
  userId: string
  role: "ADMIN" | "OPERATOR" | "VIEWER"
  permissions?: Record<string, any>
}

export interface VMUserAssignmentUpdateInput {
  role?: "ADMIN" | "OPERATOR" | "VIEWER"
  permissions?: Record<string, any>
}

/**
 * Hook to fetch all users assigned to a VM
 */
export function useVMUsers(vmId: string) {
  return useQuery({
    queryKey: ["vm-users", vmId],
    queryFn: async () => {
      const response = await fetch(`/api/vms/${vmId}/users`)
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch VM users")
      }
      const data = await response.json()
      return data.data as VMUserAssignment[]
    },
    enabled: !!vmId,
  })
}

/**
 * Hook to fetch a specific user assignment for a VM
 */
export function useVMUserAssignment(vmId: string, userId: string) {
  return useQuery({
    queryKey: ["vm-user", vmId, userId],
    queryFn: async () => {
      const response = await fetch(`/api/vms/${vmId}/users/${userId}`)
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch user assignment")
      }
      const data = await response.json()
      return data.data as VMUserAssignment
    },
    enabled: !!vmId && !!userId,
  })
}

/**
 * Hook to assign a user to a VM
 */
export function useAssignUserToVM(vmId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (input: VMUserAssignmentInput) => {
      const response = await fetch(`/api/vms/${vmId}/users`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(input),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to assign user to VM")
      }

      const data = await response.json()
      return data.data as VMUserAssignment
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["vm-users", vmId] })
      toast({
        title: "User assigned",
        description: `${data.user?.name || data.user?.email || "User"} has been assigned to the VM.`,
      })
    },
    onError: (error) => {
      toast({
        title: "Failed to assign user",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to update a user assignment for a VM
 */
export function useUpdateVMUserAssignment(vmId: string, userId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (input: VMUserAssignmentUpdateInput) => {
      const response = await fetch(`/api/vms/${vmId}/users/${userId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(input),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to update user assignment")
      }

      const data = await response.json()
      return data.data as VMUserAssignment
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["vm-users", vmId] })
      queryClient.invalidateQueries({ queryKey: ["vm-user", vmId, userId] })
      toast({
        title: "User assignment updated",
        description: `${data.user?.name || data.user?.email || "User"}'s role has been updated.`,
      })
    },
    onError: (error) => {
      toast({
        title: "Failed to update user assignment",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to remove a user assignment from a VM
 */
export function useRemoveUserFromVM(vmId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await fetch(`/api/vms/${vmId}/users/${userId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to remove user from VM")
      }

      return { userId }
    },
    onSuccess: (_, userId) => {
      queryClient.invalidateQueries({ queryKey: ["vm-users", vmId] })
      queryClient.removeQueries({ queryKey: ["vm-user", vmId, userId] })
      toast({
        title: "User removed",
        description: "User has been removed from the VM.",
      })
    },
    onError: (error) => {
      toast({
        title: "Failed to remove user",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    },
  })
}
