"use client"

import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { useEffect, useState } from "react"
import { getProxmoxClient } from "@/lib/api-client"
import { toast } from "@/hooks/use-toast"
import { useServerContext } from "@/providers/server-provider"
import { ProxmoxServerInfo, ProxmoxVM } from "@/types/proxmox"
import { z } from "zod"

async function fetchVirtualMachines(serverInfo: any): Promise<ProxmoxVM[]> {
  if (!serverInfo) return []

  try {
    console.log("Fetching VMs with server info:", {
      url: serverInfo.url,
      hasTicket: !!serverInfo.ticket,
      ticketStart: serverInfo.ticket ? serverInfo.ticket.substring(0, 10) + '...' : null
    })

    // Use the general API proxy to avoid CORS issues
    const response = await fetch(`/api/proxmox?path=cluster/resources`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Proxmox-URL": serverInfo.url,
        "X-Proxmox-Ticket": serverInfo.ticket,
        "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error("VM fetch error:", {
        status: response.status,
        statusText: response.statusText,
        errorText
      })
      throw new Error(`Failed to fetch virtual machines: ${response.status} ${response.statusText}`)
    }

    const responseData = await response.json()
    console.log("VM response:", responseData)

    if (!responseData.data) {
      console.error("Invalid VM response:", responseData)
      return []
    }

    return responseData.data
      .filter((resource: any) => resource.type === "qemu")
      .map((vm: any) => ({
        vmid: vm.vmid,
        name: vm.name || `VM ${vm.vmid}`,
        status: vm.status,
        node: vm.node || '',
        cpu: vm.cpu || 0,
        maxcpu: vm.maxcpu || 1,
        mem: vm.mem || 0,
        maxmem: vm.maxmem || 1024,
        disk: vm.disk || 0,
        maxdisk: vm.maxdisk || 0,
        uptime: vm.uptime || 0,
      }))
  } catch (error) {
    console.error("Failed to fetch virtual machines:", error)
    return []
  }
}

/**
 * VM creation schema based on the latest Proxmox VE API (7.x)
 * Reference: https://pve.proxmox.com/pve-docs/api-viewer/index.html#/nodes/{node}/qemu
 */
const createVMSchema = z.object({
  // Required parameters
  name: z.string().min(1, "VM name is required"),
  node: z.string().min(1, "Node is required"),

  // VM resources
  cores: z.number().int().min(1).max(128).default(2),
  sockets: z.number().int().min(1).max(4).default(1),
  memory: z.number().int().min(512).max(1048576).default(2048),

  // Storage configuration
  storage: z.string().min(1, "Storage is required"),
  diskSize: z.number().min(1).max(10000).default(32),
  diskFormat: z.enum(["raw", "qcow2", "vmdk"]).default("qcow2"),

  // Network configuration
  networkBridge: z.string().min(1, "Network bridge is required").default("vmbr0"),
  networkModel: z.enum(["virtio", "e1000", "rtl8139", "vmxnet3"]).default("virtio"),

  // OS configuration
  osType: z.enum(["l26", "win10", "win11", "other"]).default("l26"),
  bootDisk: z.string().default("scsi0"),
  bootOrder: z.string().optional(),

  // Cloud-init configuration
  useCloudinit: z.boolean().default(false),
  ipConfig: z.string().optional(), // Format: ip=********/24,gw=********
  ipAddress: z.string().optional(),
  gateway: z.string().optional(),
  netmask: z.string().optional(),
  hostname: z.string().optional(),
  password: z.string().optional(),
  sshKeys: z.string().optional(),

  // Marketplace/template options
  useMarketplaceImage: z.boolean().default(false),
  marketplaceImage: z.string().optional(),
  clone: z.string().optional(), // VMID to clone from
  full: z.boolean().default(true), // Full clone (not linked)

  // Behavior
  startAfterCreation: z.boolean().default(true),
  onBoot: z.boolean().default(true),
  protection: z.boolean().default(false),

  // Advanced
  cpu: z.string().default("host"), // CPU type
  kvm: z.boolean().default(true), // Enable KVM hardware virtualization
  agent: z.enum(["1", "0"]).default("1"), // Enable QEMU Guest Agent
  bios: z.enum(["seabios", "ovmf"]).default("seabios"),
  description: z.string().optional(),
  tags: z.string().optional(),
})

export type CreateVMParams = z.infer<typeof createVMSchema>

/**
 * SSH connection schema for secure VM access
 * Supports both password and key-based authentication
 */
const sshConnectionSchema = z.object({
  host: z.string().min(1, "Host is required"),
  port: z.number().int().min(1).max(65535).default(22),
  username: z.string().min(1, "Username is required").default("root"),
  authMethod: z.enum(["password", "privateKey", "privateKeyFile"]).default("password"),
  password: z.string().optional(),
  privateKey: z.string().optional(),
  privateKeyPath: z.string().optional(),
  passphrase: z.string().optional(),
  readyTimeout: z.number().int().min(1000).max(300000).default(30000), // 30 seconds
  keepaliveInterval: z.number().int().min(1000).max(300000).default(10000), // 10 seconds
})

export type SSHConnectionParams = z.infer<typeof sshConnectionSchema>

// Helper functions for VM creation
async function createVM(requestData: Record<string, any>) {
  const { node, ...data } = requestData;

  try {
    const response = await fetch(`/api/proxmox?path=nodes/${node}/qemu`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('VM creation error:', {
        status: response.status,
        statusText: response.statusText,
        errorText,
      });
      throw new Error(`Failed to create VM: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();

    // If startAfterCreation is true, start the VM
    if (data.startAfterCreation && responseData.data?.vmid) {
      await controlVirtualMachine(responseData.data.vmid.toString(), 'start');
    }

    return responseData;
  } catch (error) {
    console.error('Failed to create VM:', error);
    throw error;
  }
}

async function createVMFromMarketplaceImage(params: CreateVMParams) {
  // In a real implementation, this would download and process a marketplace template
  // For now, we'll simulate the process with a standard VM creation

  try {
    console.log(`Creating VM from marketplace image: ${params.marketplaceImage}`);

    // First, we would download or locate the marketplace template
    // Then create a VM using that template

    // For demonstration, we'll create a standard VM with some preset values
    const requestData: Record<string, any> = {
      node: params.node,
      name: params.name,
      cores: params.cores,
      sockets: params.sockets || 1,
      memory: params.memory,
      ostype: params.osType,
      cpu: 'host',
      kvm: 1,
      agent: '1',
      startAfterCreation: params.startAfterCreation,
    };

    // Add storage configuration - in a real implementation, this would use the marketplace image
    requestData[`scsi0`] = `${params.storage}:${params.diskSize},format=qcow2`;

    // Add network configuration
    requestData[`net0`] = `model=virtio,bridge=${params.networkBridge || 'vmbr0'}`;

    // Add description if provided
    if (params.description) {
      requestData.description = `Created from marketplace image: ${params.marketplaceImage}\n${params.description}`;
    } else {
      requestData.description = `Created from marketplace image: ${params.marketplaceImage}`;
    }

    return createVM(requestData);
  } catch (error) {
    console.error(`Failed to create VM from marketplace image:`, error);
    throw error;
  }
}

async function createVMFromClone(params: {
  source: string
  target: string
  name: string
  full: boolean
  description?: string
  storage?: string
  startAfterCreation?: boolean
}) {
  try {
    // First, find the node for the source VM
    const response = await fetch(`/api/proxmox?path=cluster/resources&type=vm`);

    if (!response.ok) {
      throw new Error(`Failed to get VM list: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const sourceVM = data.data.find((vm: any) => vm.vmid.toString() === params.source);

    if (!sourceVM) {
      throw new Error(`Source VM with ID ${params.source} not found`);
    }

    const sourceNode = sourceVM.node;

    // Prepare the clone request
    const requestData: Record<string, any> = {
      newid: params.source, // This will be auto-assigned if not provided
      name: params.name,
      full: params.full ? 1 : 0,
      target: params.target !== sourceNode ? params.target : undefined,
    };

    // Add storage target if provided and different from source
    if (params.storage) {
      requestData.storage = params.storage;
    }

    // Add description if provided
    if (params.description) {
      requestData.description = params.description;
    }

    // Make the clone request
    const cloneResponse = await fetch(`/api/proxmox?path=nodes/${sourceNode}/qemu/${params.source}/clone`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!cloneResponse.ok) {
      const errorText = await cloneResponse.text();
      throw new Error(`Failed to clone VM: ${cloneResponse.status} ${cloneResponse.statusText} - ${errorText}`);
    }

    const cloneData = await cloneResponse.json();

    // If startAfterCreation is true, start the VM
    if (params.startAfterCreation && cloneData.data?.vmid) {
      // Determine which node the VM is now on
      const targetNode = params.target || sourceNode;

      // Start the VM
      await fetch(`/api/proxmox?path=nodes/${targetNode}/qemu/${cloneData.data.vmid}/status/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    return cloneData;
  } catch (error) {
    console.error('Failed to clone VM:', error);
    throw error;
  }
}

async function controlVirtualMachine(vmId: string, action: string, options?: Record<string, any>) {
  try {
    // First, find the node for this VM
    const response = await fetch(`/api/proxmox?path=cluster/resources&type=vm`);

    if (!response.ok) {
      throw new Error(`Failed to get VM list: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const vm = data.data.find((vm: any) => vm.vmid.toString() === vmId);

    if (!vm) {
      throw new Error(`VM with ID ${vmId} not found`);
    }

    const node = vm.node;

    // Prepare the request URL and body
    const url = `/api/proxmox?path=nodes/${node}/qemu/${vmId}/status/${action}`;

    // Make the request
    const actionResponse = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: options ? JSON.stringify(options) : undefined,
    });

    if (!actionResponse.ok) {
      const errorText = await actionResponse.text();
      throw new Error(`Failed to ${action} VM: ${actionResponse.status} ${actionResponse.statusText} - ${errorText}`);
    }

    return await actionResponse.json();
  } catch (error) {
    console.error(`Failed to ${action} VM:`, error);
    throw error;
  }
}

export function useVirtualMachines() {
  const { serverInfo } = useServerContext()
  const queryClient = useQueryClient()
  const [isActionLoading, setIsActionLoading] = useState(false)

  const { data = [], isLoading, error, refetch } = useQuery({
    queryKey: ["virtualMachines", serverInfo?.url],
    queryFn: () => fetchVirtualMachines(serverInfo),
    enabled: !!serverInfo?.url && !!serverInfo?.ticket && serverInfo.ticket !== "placeholder",
    refetchInterval: 30000, // Refetch every 30 seconds
    retry: 3,
    retryDelay: 1000,
  })

  // If we have serverInfo but the query is disabled due to missing ticket, refetch when ticket becomes available
  useEffect(() => {
    if (serverInfo?.ticket && serverInfo.ticket !== "placeholder" && error) {
      console.log("Ticket is now available, refetching VMs")
      refetch()
    }
  }, [serverInfo?.ticket, error, refetch])

  /**
   * Mutation for creating a VM using the Proxmox API
   * Supports multiple creation methods:
   * 1. Standard VM creation
   * 2. Marketplace/template-based creation
   * 3. Clone from existing VM
   * 4. Cloud-init enabled VMs
   */
  const createVMMutation = useMutation({
    mutationFn: async (params: CreateVMParams) => {
      if (!serverInfo) throw new Error("Server information is not available")

      // Validate the parameters
      const validatedParams = createVMSchema.parse(params)

      // Prepare the request based on the creation method
      if (validatedParams.useMarketplaceImage && validatedParams.marketplaceImage) {
        // Marketplace image-based VM creation
        return createVMFromMarketplaceImage(validatedParams)
      } else if (validatedParams.clone) {
        // Clone-based VM creation
        return createVMFromClone({
          source: validatedParams.clone,
          target: validatedParams.node,
          name: validatedParams.name,
          full: validatedParams.full,
          description: validatedParams.description,
          storage: validatedParams.storage,
          startAfterCreation: validatedParams.startAfterCreation
        })
      } else {
        // Standard VM creation
        const requestData: Record<string, any> = {
          node: validatedParams.node,
          name: validatedParams.name,
          cores: validatedParams.cores,
          sockets: validatedParams.sockets,
          memory: validatedParams.memory,
          ostype: validatedParams.osType,
          cpu: validatedParams.cpu,
          kvm: validatedParams.kvm ? 1 : 0,
          agent: validatedParams.agent,
          bios: validatedParams.bios,
          onboot: validatedParams.onBoot ? 1 : 0,
          protection: validatedParams.protection ? 1 : 0,
          startAfterCreation: validatedParams.startAfterCreation,
        }

        // Add storage configuration
        requestData[`scsi0`] = `${validatedParams.storage}:${validatedParams.diskSize},format=${validatedParams.diskFormat}`

        // Add network configuration
        requestData[`net0`] = `model=${validatedParams.networkModel},bridge=${validatedParams.networkBridge}`

        // Add boot configuration
        if (validatedParams.bootOrder) {
          requestData.boot = validatedParams.bootOrder
        } else {
          requestData.boot = `order=${validatedParams.bootDisk}`
        }

        // Add cloud-init configuration if enabled
        if (validatedParams.useCloudinit) {
          // Add cloud-init drive
          requestData[`ide2`] = `${validatedParams.storage}:cloudinit`

          // Add IP configuration
          if (validatedParams.ipConfig) {
            requestData[`ipconfig0`] = validatedParams.ipConfig
          } else if (validatedParams.ipAddress && validatedParams.netmask && validatedParams.gateway) {
            requestData[`ipconfig0`] = `ip=${validatedParams.ipAddress}/${validatedParams.netmask},gw=${validatedParams.gateway}`
          }

          // Add hostname if provided
          if (validatedParams.hostname) {
            requestData.hostname = validatedParams.hostname
          }

          // Add password if provided
          if (validatedParams.password) {
            requestData.cipassword = validatedParams.password
          }

          // Add SSH keys if provided
          if (validatedParams.sshKeys) {
            requestData.sshkeys = encodeURIComponent(validatedParams.sshKeys)
          }
        }

        // Add description and tags if provided
        if (validatedParams.description) {
          requestData.description = validatedParams.description
        }

        if (validatedParams.tags) {
          requestData.tags = validatedParams.tags
        }

        return createVM(requestData)
      }
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Virtual machine created successfully",
      })

      // Invalidate queries to refresh VM list
      queryClient.invalidateQueries({ queryKey: ["virtualMachines", serverInfo?.url] })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create VM",
        variant: "destructive",
      })
    },
  })

  /**
   * Mutation for connecting to a VM via SSH
   * Uses WebSocket-based SSH client for secure terminal access
   */
  const connectToVMMutation = useMutation({
    mutationFn: async (params: SSHConnectionParams) => {
      // Validate the parameters
      const validatedParams = sshConnectionSchema.parse(params)

      // In a production implementation, this would establish a WebSocket connection
      // to a backend SSH proxy service that handles the actual SSH connection
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:"
      const wsUrl = `${protocol}//${window.location.host}/api/ssh/connect`

      // For now, we'll simulate the connection process
      // In a real implementation, this would be handled by a WebSocket connection
      console.log(`Establishing SSH connection to ${validatedParams.host}:${validatedParams.port} as ${validatedParams.username}`)

      // Simulate connection delay
      await new Promise(resolve => setTimeout(resolve, 500))

      return {
        success: true,
        connectionParams: validatedParams,
        connectionUrl: wsUrl,
      }
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "SSH connection established",
      })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to establish SSH connection",
        variant: "destructive",
      })
    },
  })

  /**
   * VM control mutations for basic actions (start, stop, reset)
   */
  const vmControlMutation = useMutation({
    mutationFn: async ({ vmId, action, options }: { vmId: string; action: string; options?: Record<string, any> }) => {
      setIsActionLoading(true);
      try {
        return await controlVirtualMachine(vmId, action, options);
      } finally {
        setIsActionLoading(false);
      }
    },
    onSuccess: (_, { action, vmId }) => {
      // Map action to user-friendly text
      const actionTextMap: Record<string, string> = {
        start: "starting",
        stop: "stopping",
        reset: "resetting",
        shutdown: "shutting down",
        suspend: "suspending",
        resume: "resuming"
      };

      const actionText = actionTextMap[action] || String(action);

      toast({
        title: "Success",
        description: `VM ${vmId} is ${actionText}`,
      });

      // Invalidate queries to refresh VM list
      queryClient.invalidateQueries({ queryKey: ["virtualMachines", serverInfo?.url] });
    },
    onError: (error, { action }) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to ${action} VM`,
        variant: "destructive",
      });
    },
  });

  // Helper functions for VM control
  const startVm = (vmId: string) => vmControlMutation.mutate({ vmId, action: "start" });
  const stopVm = (vmId: string, force = false) => vmControlMutation.mutate({
    vmId,
    action: "stop",
    options: force ? { forceStop: 1 } : undefined
  });
  const resetVm = (vmId: string) => vmControlMutation.mutate({ vmId, action: "reset" });
  const shutdownVm = (vmId: string, timeout = 60) => vmControlMutation.mutate({
    vmId,
    action: "shutdown",
    options: { timeout }
  });

  return {
    vms: data,
    isLoading,
    error,
    // VM creation
    createVM: (params: CreateVMParams) => createVMMutation.mutate(params),
    isCreatingVM: createVMMutation.isPending,
    // VM control
    startVm,
    stopVm,
    resetVm,
    shutdownVm,
    controlVm: (vmId: string, action: string, options?: Record<string, any>) =>
      vmControlMutation.mutate({ vmId, action, options }),
    isActionLoading,
    // SSH connection
    connectToVM: (params: SSHConnectionParams) => connectToVMMutation.mutate(params),
    isConnecting: connectToVMMutation.isPending,
    // Refetch
    refetch,
  }
}

// Hook for a single VM's details
export function useVirtualMachine(vmId: string) {
  const { serverInfo } = useServerContext()
  const queryClient = useQueryClient()

  const {
    data: vm,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["vm", vmId],
    queryFn: async () => {
      // Use the server info from context
      if (!serverInfo) {
        throw new Error("No server information available")
      }

      // Pass the authentication headers directly
      const headers = {
        'X-Proxmox-URL': serverInfo.url,
        'X-Proxmox-Ticket': serverInfo.ticket,
        'X-Proxmox-CSRFToken': serverInfo.CSRFPreventionToken
      }

      // Make the API request directly
      const response = await fetch(`/api/proxmox?path=cluster/resources&type=vm`, {
        headers
      })

      if (!response.ok) {
        throw new Error(`Failed to get VM list: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      const vm = data.data.find((vm: any) => vm.vmid.toString() === vmId)

      if (!vm) {
        throw new Error(`VM with ID ${vmId} not found`)
      }

      // Get detailed VM status
      const node = vm.node
      const statusResponse = await fetch(`/api/proxmox?path=nodes/${node}/qemu/${vmId}/status/current`, {
        headers
      })

      if (!statusResponse.ok) {
        throw new Error(`Failed to get VM status: ${statusResponse.status} ${statusResponse.statusText}`)
      }

      const statusData = await statusResponse.json()
      return statusData.data || null
    },
    refetchInterval: 5000, // Refetch every 5 seconds
  })

  /**
   * VM control schema for validating VM action parameters
   * Based on Proxmox API: https://pve.proxmox.com/pve-docs/api-viewer/index.html#/nodes/{node}/qemu/{vmid}/status
   */
  const vmControlSchema = z.object({
    action: z.enum(["start", "stop", "reset", "shutdown", "suspend", "resume"]),
    timeout: z.number().int().min(0).max(600).optional(), // Timeout in seconds (for stop/shutdown)
    force: z.boolean().optional(), // Force stop if true
    skiplock: z.boolean().optional(), // Ignore locks if true
    statedir: z.string().optional(), // Path to store the VM state when suspending
  })

  type VMControlParams = z.infer<typeof vmControlSchema>

  /**
   * Mutation for controlling this specific VM with advanced options
   * Supports multiple control actions: start, stop, reset, shutdown, suspend, resume
   * Reference: https://pve.proxmox.com/pve-docs/api-viewer/index.html#/nodes/{node}/qemu/{vmid}/status
   */
  const vmActionMutation = useMutation({
    mutationFn: async (params: string | VMControlParams) => {
      // Handle simple string actions for backward compatibility
      const actionParams = typeof params === "string"
        ? { action: params as "start" | "stop" | "reset" | "shutdown" | "suspend" | "resume" }
        : params

      // Validate the parameters
      const validatedParams = vmControlSchema.parse(actionParams)

      // Prepare the request data
      const requestData: Record<string, any> = {}

      // Add optional parameters if provided
      if (validatedParams.timeout !== undefined) {
        requestData.timeout = validatedParams.timeout
      }

      // Handle force parameter based on the action
      if (validatedParams.force !== undefined) {
        if (validatedParams.action === 'stop') {
          // For stop action, use forceStop parameter
          requestData.forceStop = validatedParams.force ? 1 : 0
        } else {
          // For other actions, use force parameter
          requestData.force = validatedParams.force ? 1 : 0
        }
      }

      if (validatedParams.skiplock !== undefined) {
        requestData.skiplock = validatedParams.skiplock ? 1 : 0
      }

      if (validatedParams.statedir !== undefined && validatedParams.action === "suspend") {
        requestData.statedir = validatedParams.statedir
      }

      // Execute the VM control action
      if (!serverInfo) {
        throw new Error("No server information available")
      }

      // Pass the authentication headers directly
      const headers = {
        'X-Proxmox-URL': serverInfo.url,
        'X-Proxmox-Ticket': serverInfo.ticket,
        'X-Proxmox-CSRF': serverInfo.CSRFPreventionToken,
        'Content-Type': 'application/json'
      }

      // First, find the node for this VM
      const vmResponse = await fetch(`/api/proxmox?path=cluster/resources&type=vm`, {
        headers
      })

      if (!vmResponse.ok) {
        throw new Error(`Failed to get VM list: ${vmResponse.status} ${vmResponse.statusText}`)
      }

      const vmData = await vmResponse.json()
      const vm = vmData.data.find((vm: any) => vm.vmid.toString() === vmId)

      if (!vm) {
        throw new Error(`VM with ID ${vmId} not found`)
      }

      const node = vm.node

      // Log the request data for debugging
      console.log(`VM control action: ${validatedParams.action}`, {
        requestData,
        endpoint: `/api/proxmox?path=nodes/${node}/qemu/${vmId}/status/${validatedParams.action}`
      })

      // Make the control request
      const actionResponse = await fetch(`/api/proxmox?path=nodes/${node}/qemu/${vmId}/status/${validatedParams.action}`, {
        method: 'POST',
        headers: {
          ...headers,
          'Content-Type': 'application/json',
          // Ensure the CSRF token is included in the headers
          'X-Proxmox-CSRFToken': serverInfo.CSRFPreventionToken
        },
        // Always send a valid JSON body, even if empty
        body: JSON.stringify(Object.keys(requestData).length > 0 ? requestData : {})
      })

      if (!actionResponse.ok) {
        const errorText = await actionResponse.text()
        throw new Error(`Failed to ${validatedParams.action} VM: ${actionResponse.status} ${actionResponse.statusText} - ${errorText}`)
      }

      return await actionResponse.json()
    },
    onSuccess: (_, params) => {
      // Extract action from params (string or object)
      const action = typeof params === "string" ? params : params.action

      // Map action to user-friendly text
      const actionTextMap: Record<string, string> = {
        start: "starting",
        stop: "stopping",
        reset: "resetting",
        shutdown: "shutting down",
        suspend: "suspending",
        resume: "resuming"
      }

      const actionText = actionTextMap[action] || String(action)

      toast({
        title: "Success",
        description: `VM is ${actionText}`,
      })

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["vm", vmId] })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to control VM",
        variant: "destructive",
      })
    },
  })

  /**
   * Mutation for connecting to this specific VM via SSH
   * Includes VM and server context for enhanced functionality
   */
  const sshConnectionMutation = useMutation({
    mutationFn: async (params: SSHConnectionParams) => {
      // Validate the parameters
      const validatedParams = sshConnectionSchema.parse(params)

      // In a production implementation, this would establish a WebSocket connection
      // to a backend SSH proxy service that handles the actual SSH connection
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:"
      const wsUrl = `${protocol}//${window.location.host}/api/ssh/connect`

      // For now, we'll simulate the connection process
      // In a real implementation, this would be handled by a WebSocket connection
      console.log(`Establishing SSH connection to VM ${vmId} (${validatedParams.host}:${validatedParams.port}) as ${validatedParams.username}`)

      // Simulate connection delay
      await new Promise(resolve => setTimeout(resolve, 500))

      return {
        success: true,
        connectionParams: validatedParams,
        connectionUrl: wsUrl,
        vmId,
      }
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "SSH connection established",
      })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to establish SSH connection",
        variant: "destructive",
      })
    },
  })

  /**
   * Mutation for executing commands in a VM using the QEMU guest agent
   * Allows running commands inside the VM via the guest agent
   * Reference: https://pve.proxmox.com/pve-docs/api-viewer/index.html#/nodes/{node}/qemu/{vmid}/agent/exec
   */
  const executeCommandMutation = useMutation({
    mutationFn: async ({ command, args = [] }: { command: string, args?: string[] }) => {
      if (!serverInfo) {
        throw new Error("No server information available")
      }
      
      if (!vm) {
        throw new Error("VM information not available")
      }
      
      const node = vm.node || vm.nodename
      
      const response = await fetch(`/api/proxmox?path=nodes/${node}/qemu/${vmId}/agent/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Proxmox-URL': serverInfo.url,
          'X-Proxmox-Ticket': serverInfo.ticket,
          'X-Proxmox-CSRFToken': serverInfo.CSRFPreventionToken
        },
        body: JSON.stringify({
          command,
          args
        })
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to execute command: ${response.status} ${response.statusText} - ${errorText}`)
      }
      
      return await response.json()
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Command executed successfully",
      })
      queryClient.invalidateQueries({ queryKey: ["vm", vmId] })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to execute command",
        variant: "destructive",
      })
    }
  })

  /**
   * Mutation for writing files to a VM using the QEMU guest agent
   * Allows creating or modifying files inside the VM
   */
  const writeFileMutation = useMutation({
    mutationFn: async ({ path, content }: { path: string, content: string }) => {
      if (!serverInfo || !vm) {
        throw new Error("Server or VM information not available")
      }
      
      const node = vm.node || vm.nodename
      
      const response = await fetch(`/api/proxmox?path=nodes/${node}/qemu/${vmId}/agent/file-write`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Proxmox-URL': serverInfo.url,
          'X-Proxmox-Ticket': serverInfo.ticket,
          'X-Proxmox-CSRFToken': serverInfo.CSRFPreventionToken
        },
        body: JSON.stringify({
          file: path,
          content: btoa(content) // Base64 encode the content
        })
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to write file: ${response.status} ${response.statusText} - ${errorText}`)
      }
      
      return await response.json()
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "File written successfully",
      })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to write file",
        variant: "destructive",
      })
    }
  })

  /**
   * Mutation for reading files from a VM using the QEMU guest agent
   * Allows retrieving file content from inside the VM
   */
  const readFileMutation = useMutation({
    mutationFn: async (path: string) => {
      if (!serverInfo || !vm) {
        throw new Error("Server or VM information not available")
      }
      
      const node = vm.node || vm.nodename
      
      const response = await fetch(`/api/proxmox?path=nodes/${node}/qemu/${vmId}/agent/file-read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Proxmox-URL': serverInfo.url,
          'X-Proxmox-Ticket': serverInfo.ticket,
          'X-Proxmox-CSRFToken': serverInfo.CSRFPreventionToken
        },
        body: JSON.stringify({
          file: path
        })
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to read file: ${response.status} ${response.statusText} - ${errorText}`)
      }
      
      const result = await response.json()
      // Decode the base64 content
      return {
        ...result.data,
        content: atob(result.data.content || '')
      }
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "File read successfully",
      })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to read file",
        variant: "destructive",
      })
    }
  })

  /**
   * Helper functions for VM actions with advanced options
   */
  // Basic VM control actions
  const startVm = () => vmActionMutation.mutate("start")
  const stopVm = (force = false) => {
    if (force) {
      // For force stop, use the stop action
      return vmActionMutation.mutate("stop")
    } else {
      // For graceful shutdown, use the shutdown action with a timeout
      return vmActionMutation.mutate({ action: "shutdown", timeout: 60 })
    }
  }
  const resetVm = () => vmActionMutation.mutate("reset")

  // Advanced VM control actions
  const shutdownVm = (timeout = 60) => vmActionMutation.mutate({ action: "shutdown", timeout })
  const suspendVm = (statedir?: string) => vmActionMutation.mutate({ action: "suspend", statedir })
  const resumeVm = () => vmActionMutation.mutate("resume")

  // SSH connection
  const connectToVm = (params: SSHConnectionParams) => sshConnectionMutation.mutate(params)

  //

  return {
    vm,
    isLoading,
    error,
    refetch,
    // Basic VM control actions
    startVm,
    stopVm,
    resetVm,
    // Advanced VM control actions
    shutdownVm,
    suspendVm,
    resumeVm,
    // Direct access to the mutation for custom control options
    controlVm: (params: VMControlParams) => vmActionMutation.mutate(params),
    // SSH connection
    connectToVm,
    //Commands
    executeCommand: (params: { command: string, args?: string[] }) => executeCommandMutation.mutate(params),
    //Files
    writeFile: (params: { path: string, content: string }) => writeFileMutation.mutate(params),
    readFile: (path: string) => readFileMutation.mutate(path),

    // Loading states
    isActionLoading: vmActionMutation.isPending,
    isConnecting: sshConnectionMutation.isPending,
  }
}
