"use client"

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { useEffect } from "react"
import { getProxmoxClient } from "@/lib/api-client"
import { toast } from "@/hooks/use-toast"
import { useServerContext } from "@/providers/server-provider"
import { ProxmoxContainer } from "@/types/proxmox"

export function useContainers() {
  const { serverInfo } = useServerContext()

  const { data = [], isLoading, error, refetch } = useQuery({
    queryKey: ["containers", serverInfo?.url],
    queryFn: () => fetchContainers(serverInfo),
    enabled: !!serverInfo?.url && !!serverInfo?.ticket && serverInfo.ticket !== "placeholder",
    refetchInterval: 30000, // Refetch every 30 seconds
    retry: 3,
    retryDelay: 1000,
  })

  // If we have serverInfo but the query is disabled due to missing ticket, refetch when ticket becomes available
  useEffect(() => {
    if (serverInfo?.ticket && serverInfo.ticket !== "placeholder" && error) {
      console.log("Ticket is now available, refetching containers")
      refetch()
    }
  }, [serverInfo?.ticket, error, refetch])

  return {
    containers: data,
    isLoading,
    error,
  }
}

// Hook for a single container's details
export function useContainer(serverId: string, ctId: string) {
  const client = getProxmoxClient(serverId)
  const queryClient = useQueryClient()

  const {
    data: container,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["container", serverId, ctId],
    queryFn: async () => {
      const response = await client.getContainerStatus(ctId)
      return response.data || null
    },
    refetchInterval: 5000, // Refetch every 5 seconds
  })

  // Mutation for controlling this specific container
  const containerActionMutation = useMutation({
    mutationFn: async (action: "start" | "stop" | "restart") => {
      return client.controlContainer(ctId, action)
    },
    onSuccess: (data, action) => {
      const actionText = action === "start" ? "starting" : action === "stop" ? "stopping" : "restarting"

      toast({
        title: "Success",
        description: `Container is ${actionText}`,
      })

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["container", serverId, ctId] })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to control container",
        variant: "destructive",
      })
    },
  })

  // Helper functions for container actions
  const startContainer = () => containerActionMutation.mutate("start")
  const stopContainer = () => containerActionMutation.mutate("stop")
  const restartContainer = () => containerActionMutation.mutate("restart")

  return {
    container,
    isLoading,
    error,
    refetch,
    startContainer,
    stopContainer,
    restartContainer,
    isActionLoading: containerActionMutation.isPending,
  }
}

async function fetchContainers(serverInfo: any): Promise<ProxmoxContainer[]> {
  if (!serverInfo) return []

  try {
    console.log("Fetching containers with server info:", {
      url: serverInfo.url,
      hasTicket: !!serverInfo.ticket
    })

    // Use the general API proxy to avoid CORS issues
    const response = await fetch(`/api/proxmox?path=cluster/resources`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Proxmox-URL": serverInfo.url,
        "X-Proxmox-Ticket": serverInfo.ticket,
        "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Container fetch error:", {
        status: response.status,
        statusText: response.statusText,
        errorText
      })
      throw new Error(`Failed to fetch containers: ${response.status} ${response.statusText}`)
    }

    const responseData = await response.json()
    console.log("Container response:", responseData)

    if (!responseData.data) {
      console.error("Invalid container response:", responseData)
      return []
    }

    return responseData.data
      .filter((resource: any) => resource.type === "lxc")
      .map((container: any) => ({
        vmid: container.vmid,
        name: container.name || `Container ${container.vmid}`,
        status: container.status,
        cpu: container.cpu || 0,
        maxcpu: container.maxcpu || 1,
        mem: container.mem || 0,
        maxmem: container.maxmem || 1024,
        disk: container.disk || 0,
        maxdisk: container.maxdisk || 0,
        uptime: container.uptime || 0,
      }))
  } catch (error) {
    console.error("Failed to fetch containers:", error)
    return []
  }
}
