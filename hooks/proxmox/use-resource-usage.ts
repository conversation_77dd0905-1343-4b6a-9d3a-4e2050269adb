"use client"

import { useState, useEffect } from "react"
import { useQuery } from "@tanstack/react-query"
import { getProxmoxClient } from "@/lib/api-client"

export function useResourceUsage(serverId: string, resourceType: "vm" | "container", resourceId: string) {
  const client = getProxmoxClient(serverId)

  // State to store historical data
  const [history, setHistory] = useState({
    cpu: [] as { time: string; value: number }[],
    memory: [] as { time: string; value: number }[],
    diskRead: [] as { time: string; value: number }[],
    diskWrite: [] as { time: string; value: number }[],
    netIn: [] as { time: string; value: number }[],
    netOut: [] as { time: string; value: number }[],
  })

  // Query for current resource usage
  const {
    data: resourceData,
    isLoading,
    error,
  } = useQuery({
    queryKey: [resourceType, serverId, resourceId, "status"],
    queryFn: async () => {
      if (resourceType === "vm") {
        const response = await client.getVirtualMachineStatus(resourceId)
        return response.data
      } else {
        const response = await client.getContainerStatus(resourceId)
        return response.data
      }
    },
    refetchInterval: 5000, // Refetch every 5 seconds
  })

  // Update historical data when new data arrives
  useEffect(() => {
    if (resourceData) {
      const now = new Date()
      const time = now.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })

      setHistory((prev) => {
        // Keep only the last 19 data points and add the new one (for a total of 20)
        const cpu = [...prev.cpu.slice(-19), { time, value: (resourceData.cpu || 0) * 100 }]
        const memory = [
          ...prev.memory.slice(-19),
          {
            time,
            value: resourceData.mem && resourceData.maxmem ? (resourceData.mem / resourceData.maxmem) * 100 : 0,
          },
        ]
        const diskRead = [...prev.diskRead.slice(-19), { time, value: resourceData.diskread || 0 }]
        const diskWrite = [...prev.diskWrite.slice(-19), { time, value: resourceData.diskwrite || 0 }]
        const netIn = [...prev.netIn.slice(-19), { time, value: resourceData.netin || 0 }]
        const netOut = [...prev.netOut.slice(-19), { time, value: resourceData.netout || 0 }]

        return {
          cpu,
          memory,
          diskRead,
          diskWrite,
          netIn,
          netOut,
        }
      })
    }
  }, [resourceData])

  return {
    resourceData,
    history,
    isLoading,
    error,
  }
}
