"use client"

import { useState, useEffect, useCallback } from "react"
import { toast } from "sonner"
import { DomainStatus } from "@/lib/generated/prisma"

export type EmailDomain = {
  id: string
  domain: string
  dkimEnabled: boolean
  spfEnabled: boolean
  dmarcEnabled: boolean
  status: DomainStatus
  createdAt: Date
  updatedAt: Date
}

export type CreateEmailDomainData = {
  domain: string
  dkimEnabled?: boolean
  spfEnabled?: boolean
  dmarcEnabled?: boolean
}

export type UpdateEmailDomainData = {
  dkimEnabled?: boolean
  spfEnabled?: boolean
  dmarcEnabled?: boolean
  status?: DomainStatus
}

export function useEmailDomains() {
  const [domains, setDomains] = useState<EmailDomain[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch all email domains
  const fetchDomains = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch("/api/email/domains")
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch email domains")
      }
      
      const data = await response.json()
      setDomains(data.domains)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to load email domains")
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Create a new email domain
  const createDomain = useCallback(async (domainData: CreateEmailDomainData) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch("/api/email/domains", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(domainData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to create email domain")
      }
      
      const data = await response.json()
      setDomains((prev) => [...prev, data.domain])
      toast.success("Email domain created successfully")
      return data.domain
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to create email domain")
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Update an existing email domain
  const updateDomain = useCallback(async (id: string, domainData: UpdateEmailDomainData) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/email/domains/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(domainData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update email domain")
      }
      
      const data = await response.json()
      setDomains((prev) => 
        prev.map((domain) => (domain.id === id ? data.domain : domain))
      )
      toast.success("Email domain updated successfully")
      return data.domain
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to update email domain")
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Delete an email domain
  const deleteDomain = useCallback(async (id: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/email/domains/${id}`, {
        method: "DELETE",
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to delete email domain")
      }
      
      setDomains((prev) => prev.filter((domain) => domain.id !== id))
      toast.success("Email domain deleted successfully")
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to delete email domain")
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load domains on component mount
  useEffect(() => {
    fetchDomains()
  }, [fetchDomains])

  return {
    domains,
    isLoading,
    error,
    fetchDomains,
    createDomain,
    updateDomain,
    deleteDomain,
  }
}
