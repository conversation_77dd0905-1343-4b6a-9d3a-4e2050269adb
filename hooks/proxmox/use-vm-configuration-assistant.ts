"use client"

import { useState } from "react"
import { useQuery, useMutation } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import type { VMConfigurationRequest, VMConfigurationResponse } from "@/types/ai-features"

/**
 * Hook to generate VM configuration recommendations
 */
export function useVMConfigurationAssistant() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [configuration, setConfiguration] = useState<VMConfigurationResponse | null>(null)

  const generateConfiguration = async (request: VMConfigurationRequest) => {
    setIsGenerating(true)
    try {
      const response = await fetch("/api/ai/vm-configuration", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to generate VM configuration")
      }

      const data = await response.json()
      setConfiguration(data.data)
      return data.data
    } catch (error) {
      toast({
        title: "Configuration Error",
        description: error instanceof Error ? error.message : "Failed to generate VM configuration",
        variant: "destructive",
      })
      throw error
    } finally {
      setIsGenerating(false)
    }
  }

  return {
    isGenerating,
    configuration,
    generateConfiguration,
    clearConfiguration: () => setConfiguration(null),
  }
}

/**
 * Hook to validate VM configuration against server constraints
 */
export function useVMConfigurationValidator(serverId: string) {
  return useQuery({
    queryKey: ["server-constraints", serverId],
    queryFn: async () => {
      const response = await fetch(`/api/servers/${serverId}/constraints`)
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch server constraints")
      }
      const data = await response.json()
      return data.data
    },
    enabled: !!serverId,
  })
}

/**
 * Hook to apply VM configuration
 */
export function useApplyVMConfiguration(serverId: string) {
  return useMutation({
    mutationFn: async (configuration: VMConfigurationResponse["configuration"]) => {
      if (!configuration) {
        throw new Error("No configuration provided")
      }

      const response = await fetch(`/api/servers/${serverId}/vms`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: configuration.osType,
          specs: {
            cpu: configuration.cpu,
            memory: configuration.memory,
            disk: configuration.storage,
            os: configuration.osType,
            osVersion: configuration.osVersion,
          },
          networkConfiguration: configuration.networkConfiguration,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to create VM")
      }

      const data = await response.json()
      return data.data
    },
    onSuccess: (data) => {
      toast({
        title: "VM Created",
        description: `VM "${data.name}" has been created successfully.`,
      })
    },
    onError: (error) => {
      toast({
        title: "VM Creation Failed",
        description: error instanceof Error ? error.message : "Failed to create VM",
        variant: "destructive",
      })
    },
  })
}
