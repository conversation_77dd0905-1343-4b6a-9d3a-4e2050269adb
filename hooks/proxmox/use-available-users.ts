"use client"

import { useQuery } from "@tanstack/react-query"

export interface User {
  id: string
  name?: string
  email: string
  image?: string
  role: string
}

/**
 * Hook to fetch all available users for assignment
 */
export function useAvailableUsers() {
  return useQuery({
    queryKey: ["available-users"],
    queryFn: async () => {
      const response = await fetch("/api/users")
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch available users")
      }
      const data = await response.json()
      return data.data as User[]
    },
  })
}

/**
 * Hook to fetch users available for server assignment
 * (excludes users already assigned to the server)
 */
export function useAvailableUsersForServer(serverId: string) {
  return useQuery({
    queryKey: ["available-users-server", serverId],
    queryFn: async () => {
      const response = await fetch(`/api/servers/${serverId}/available-users`)
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch available users")
      }
      const data = await response.json()
      return data.data as User[]
    },
    enabled: !!serverId,
  })
}

/**
 * Hook to fetch users available for VM assignment
 * (excludes users already assigned to the VM)
 */
export function useAvailableUsersForVM(vmId: string) {
  return useQuery({
    queryKey: ["available-users-vm", vmId],
    queryFn: async () => {
      const response = await fetch(`/api/vms/${vmId}/available-users`)
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch available users")
      }
      const data = await response.json()
      return data.data as User[]
    },
    enabled: !!vmId,
  })
}
