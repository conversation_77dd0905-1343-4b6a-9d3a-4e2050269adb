"use client"

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"

export interface ServerUserAssignment {
  id: string
  serverId: string
  userId: string
  role: "ADMIN" | "MANAGER" | "OPERATOR" | "VIEWER"
  permissions?: Record<string, any>
  createdAt: string
  updatedAt: string
  user?: {
    id: string
    name?: string
    email: string
    image?: string
  }
}

export interface ServerUserAssignmentInput {
  userId: string
  role: "ADMIN" | "MANAGER" | "OPERATOR" | "VIEWER"
  permissions?: Record<string, any>
}

export interface ServerUserAssignmentUpdateInput {
  role?: "ADMIN" | "MANAGER" | "OPERATOR" | "VIEWER"
  permissions?: Record<string, any>
}

/**
 * Hook to fetch all users assigned to a server
 */
export function useServerUsers(serverId: string) {
  return useQuery({
    queryKey: ["server-users", serverId],
    queryFn: async () => {
      const response = await fetch(`/api/servers/${serverId}/users`)
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch server users")
      }
      const data = await response.json()
      return data.data as ServerUserAssignment[]
    },
    enabled: !!serverId,
  })
}

/**
 * Hook to fetch a specific user assignment for a server
 */
export function useServerUserAssignment(serverId: string, userId: string) {
  return useQuery({
    queryKey: ["server-user", serverId, userId],
    queryFn: async () => {
      const response = await fetch(`/api/servers/${serverId}/users/${userId}`)
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch user assignment")
      }
      const data = await response.json()
      return data.data as ServerUserAssignment
    },
    enabled: !!serverId && !!userId,
  })
}

/**
 * Hook to assign a user to a server
 */
export function useAssignUserToServer(serverId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (input: ServerUserAssignmentInput) => {
      const response = await fetch(`/api/servers/${serverId}/users`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(input),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to assign user to server")
      }

      const data = await response.json()
      return data.data as ServerUserAssignment
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["server-users", serverId] })
      toast({
        title: "User assigned",
        description: `${data.user?.name || data.user?.email || "User"} has been assigned to the server.`,
      })
    },
    onError: (error) => {
      toast({
        title: "Failed to assign user",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to update a user assignment for a server
 */
export function useUpdateServerUserAssignment(serverId: string, userId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (input: ServerUserAssignmentUpdateInput) => {
      const response = await fetch(`/api/servers/${serverId}/users/${userId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(input),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to update user assignment")
      }

      const data = await response.json()
      return data.data as ServerUserAssignment
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["server-users", serverId] })
      queryClient.invalidateQueries({ queryKey: ["server-user", serverId, userId] })
      toast({
        title: "User assignment updated",
        description: `${data.user?.name || data.user?.email || "User"}'s role has been updated.`,
      })
    },
    onError: (error) => {
      toast({
        title: "Failed to update user assignment",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to remove a user assignment from a server
 */
export function useRemoveUserFromServer(serverId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await fetch(`/api/servers/${serverId}/users/${userId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to remove user from server")
      }

      return { userId }
    },
    onSuccess: (_, userId) => {
      queryClient.invalidateQueries({ queryKey: ["server-users", serverId] })
      queryClient.removeQueries({ queryKey: ["server-user", serverId, userId] })
      toast({
        title: "User removed",
        description: "User has been removed from the server.",
      })
    },
    onError: (error) => {
      toast({
        title: "Failed to remove user",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    },
  })
}
