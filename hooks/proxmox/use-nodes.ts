"use client"

import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import { useServerContext } from "@/providers/server-provider"

export interface ProxmoxNode {
  id: string
  name: string
  status: string
  ip?: string
  cpu: number
  maxcpu: number
  mem: number
  maxmem: number
  disk: number
  maxdisk: number
  uptime: number
  ssl_fingerprint?: string
  level?: string
}

async function fetchNodes(serverInfo: any): Promise<ProxmoxNode[]> {
  if (!serverInfo) return []

  try {
    console.log("Fetching nodes with server info:", {
      url: serverInfo.url,
      hasTicket: !!serverInfo.ticket,
      ticketStart: serverInfo.ticket ? serverInfo.ticket.substring(0, 10) + '...' : null
    })
    
    // Use the general API proxy to avoid CORS issues
    const response = await fetch(`/api/proxmox?path=nodes`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Proxmox-URL": serverInfo.url,
        "X-Proxmox-Ticket": serverInfo.ticket,
        "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Nodes fetch error:", {
        status: response.status,
        statusText: response.statusText,
        errorText
      })
      throw new Error(`Failed to fetch nodes: ${response.status} ${response.statusText}`)
    }

    const responseData = await response.json()
    console.log("Nodes response:", responseData)
    
    if (!responseData.data) {
      console.error("Invalid nodes response:", responseData)
      return []
    }

    // Map node data
    return responseData.data.map((node: any) => ({
      id: node.id || node.node,
      name: node.node,
      status: node.status || 'unknown',
      ip: node.ip,
      cpu: node.cpu || 0,
      maxcpu: node.maxcpu || 1,
      mem: node.mem || 0,
      maxmem: node.maxmem || 1,
      disk: node.disk || 0,
      maxdisk: node.maxdisk || 1,
      uptime: node.uptime || 0,
      ssl_fingerprint: node.ssl_fingerprint,
      level: node.level
    }))
  } catch (error) {
    console.error("Failed to fetch nodes:", error)
    return []
  }
}

export function useNodes() {
  const { serverInfo } = useServerContext()

  const { data = [], isLoading, error } = useQuery({
    queryKey: ["nodes", serverInfo?.url],
    queryFn: () => fetchNodes(serverInfo),
    enabled: !!serverInfo?.ticket,
    refetchInterval: 30000, // Refetch every 30 seconds
    retry: 2,
    retryDelay: 1000,
  })

  return {
    nodes: data,
    isLoading,
    error,
  }
}

// Hook for a single node's details
export function useNodeDetails(nodeId: string) {
  const { serverInfo } = useServerContext()
  const queryClient = useQueryClient()

  const { data, isLoading, error } = useQuery({
    queryKey: ["nodeDetails", serverInfo?.url, nodeId],
    queryFn: async () => {
      if (!serverInfo) return null
      
      const response = await fetch(`/api/proxmox?path=nodes/${nodeId}/status`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch node details: ${response.status}`)
      }

      const responseData = await response.json()
      return responseData.data || null
    },
    enabled: !!serverInfo?.ticket && !!nodeId,
  })

  // Mutation for node actions (e.g., start/stop/reboot)
  const nodeMutation = useMutation({
    mutationFn: async ({ action, params }: { action: string, params?: Record<string, any> }) => {
      if (!serverInfo) throw new Error("Server info not available")
      
      const response = await fetch(`/api/proxmox?path=nodes/${nodeId}/${action}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
        body: JSON.stringify(params || {}),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `Failed to perform action: ${action}`)
      }

      return await response.json()
    },
    onSuccess: (_data, variables) => {
      toast({
        title: "Success",
        description: `Node ${variables.action} action completed`,
      })
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["nodes"] })
      queryClient.invalidateQueries({ queryKey: ["nodeDetails", serverInfo?.url, nodeId] })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to perform node action",
        variant: "destructive",
      })
    },
  })

  return {
    node: data,
    isLoading,
    error,
    performAction: (action: string, params?: Record<string, any>) => 
      nodeMutation.mutate({ action, params }),
    isActionLoading: nodeMutation.isPending,
  }
} 