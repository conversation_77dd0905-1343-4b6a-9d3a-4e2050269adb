"use client"

import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import { useServerContext } from "@/providers/server-provider"

export interface ProxmoxStorage {
  storage: string
  type: string
  content: string
  active: boolean
  enabled: boolean
  shared: boolean
  used: number
  total: number
  used_fraction: number
  avail: number
  plugintype: string
}

async function fetchStorage(serverInfo: any): Promise<ProxmoxStorage[]> {
  if (!serverInfo) return []

  try {
    console.log("Fetching storage with server info:", {
      url: serverInfo.url,
      hasTicket: !!serverInfo.ticket,
      ticketStart: serverInfo.ticket ? serverInfo.ticket.substring(0, 10) + '...' : null
    })
    
    // Use the general API proxy to avoid CORS issues
    const response = await fetch(`/api/proxmox?path=storage`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Proxmox-URL": serverInfo.url,
        "X-Proxmox-Ticket": serverInfo.ticket,
        "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Storage fetch error:", {
        status: response.status,
        statusText: response.statusText,
        errorText
      })
      throw new Error(`Failed to fetch storage: ${response.status} ${response.statusText}`)
    }

    const responseData = await response.json()
    console.log("Storage response:", responseData)
    
    if (!responseData.data) {
      console.error("Invalid storage response:", responseData)
      return []
    }

    return responseData.data
  } catch (error) {
    console.error("Failed to fetch storage:", error)
    return []
  }
}

export function useStorage() {
  const { serverInfo } = useServerContext()

  const { data = [], isLoading, error } = useQuery({
    queryKey: ["storage", serverInfo?.url],
    queryFn: () => fetchStorage(serverInfo),
    enabled: !!serverInfo?.ticket,
    refetchInterval: 30000, // Refetch every 30 seconds
    retry: 2,
    retryDelay: 1000,
  })

  return {
    storages: data,
    isLoading,
    error,
  }
}

// Hook for a single storage's details
export function useStorageDetails(storageId: string) {
  const { serverInfo } = useServerContext()
  const queryClient = useQueryClient()

  const { data, isLoading, error } = useQuery({
    queryKey: ["storageDetails", serverInfo?.url, storageId],
    queryFn: async () => {
      if (!serverInfo) return null
      
      const response = await fetch(`/api/proxmox?path=storage/${storageId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch storage details: ${response.status}`)
      }

      const responseData = await response.json()
      return responseData.data || null
    },
    enabled: !!serverInfo?.ticket && !!storageId,
  })

  // Mutation for storage actions (e.g., disable/enable)
  const storageMutation = useMutation({
    mutationFn: async ({ action, params }: { action: string, params?: Record<string, any> }) => {
      if (!serverInfo) throw new Error("Server info not available")
      
      const response = await fetch(`/api/proxmox?path=storage/${storageId}/${action}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
        body: JSON.stringify(params || {}),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `Failed to perform action: ${action}`)
      }

      return await response.json()
    },
    onSuccess: (_data, variables) => {
      toast({
        title: "Success",
        description: `Storage ${variables.action} action completed`,
      })
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["storage"] })
      queryClient.invalidateQueries({ queryKey: ["storageDetails", serverInfo?.url, storageId] })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to perform storage action",
        variant: "destructive",
      })
    },
  })

  return {
    storage: data,
    isLoading,
    error,
    performAction: (action: string, params?: Record<string, any>) => 
      storageMutation.mutate({ action, params }),
    isActionLoading: storageMutation.isPending,
  }
} 