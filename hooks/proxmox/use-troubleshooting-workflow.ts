"use client"

import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import type {
  TroubleshootingRequest,
  TroubleshootingResponse,
  DiagnosticStep,
  RemediationStep,
} from "@/types/ai-features"

/**
 * Hook to create and manage troubleshooting workflows
 */
export function useTroubleshootingWorkflow() {
  const [isCreating, setIsCreating] = useState(false)
  const [workflow, setWorkflow] = useState<TroubleshootingResponse["workflow"] | null>(null)
  const [diagnosticResults, setDiagnosticResults] = useState<DiagnosticStep[]>([])
  const [remediationResults, setRemediationResults] = useState<RemediationStep[]>([])

  // Create a new troubleshooting workflow
  const createWorkflow = async (request: TroubleshootingRequest) => {
    setIsCreating(true)
    try {
      const response = await fetch("/api/ai/troubleshooting", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to create troubleshooting workflow")
      }

      const data = await response.json()
      setWorkflow(data.data.workflow)
      setDiagnosticResults([])
      setRemediationResults([])
      return data.data.workflow
    } catch (error) {
      toast({
        title: "Workflow Creation Error",
        description: error instanceof Error ? error.message : "Failed to create troubleshooting workflow",
        variant: "destructive",
      })
      throw error
    } finally {
      setIsCreating(false)
    }
  }

  // Execute diagnostic steps
  const executeDiagnosticSteps = useMutation({
    mutationFn: async ({
      steps,
      vmId,
      serverId,
    }: {
      steps: DiagnosticStep[]
      vmId: string
      serverId: string
    }) => {
      const response = await fetch("/api/ai/troubleshooting", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "executeDiagnosticStep",
          steps,
          vmId,
          serverId,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to execute diagnostic steps")
      }

      const data = await response.json()
      return data.data.steps
    },
    onSuccess: (data) => {
      setDiagnosticResults((prev) => [...prev, ...data])
      toast({
        title: "Diagnostic Steps Executed",
        description: "Diagnostic steps have been executed successfully.",
      })
    },
    onError: (error) => {
      toast({
        title: "Diagnostic Execution Failed",
        description: error instanceof Error ? error.message : "Failed to execute diagnostic steps",
        variant: "destructive",
      })
    },
  })

  // Update workflow with diagnostic results
  const updateWithDiagnosis = useMutation({
    mutationFn: async () => {
      if (!workflow) {
        throw new Error("No active workflow")
      }

      const response = await fetch("/api/ai/troubleshooting", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "updateWithDiagnosis",
          workflow,
          steps: diagnosticResults,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to update workflow with diagnosis")
      }

      const data = await response.json()
      return data.data.workflow
    },
    onSuccess: (data) => {
      setWorkflow(data)
      toast({
        title: "Workflow Updated",
        description: "Workflow has been updated with diagnostic results.",
      })
    },
    onError: (error) => {
      toast({
        title: "Workflow Update Failed",
        description: error instanceof Error ? error.message : "Failed to update workflow",
        variant: "destructive",
      })
    },
  })

  // Execute remediation steps
  const executeRemediationSteps = useMutation({
    mutationFn: async ({
      steps,
      vmId,
      serverId,
    }: {
      steps: RemediationStep[]
      vmId: string
      serverId: string
    }) => {
      const response = await fetch("/api/ai/troubleshooting", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "executeRemediationStep",
          steps,
          vmId,
          serverId,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to execute remediation steps")
      }

      const data = await response.json()
      return data.data.steps
    },
    onSuccess: (data) => {
      setRemediationResults((prev) => [...prev, ...data])
      toast({
        title: "Remediation Steps Executed",
        description: "Remediation steps have been executed successfully.",
      })
    },
    onError: (error) => {
      toast({
        title: "Remediation Execution Failed",
        description: error instanceof Error ? error.message : "Failed to execute remediation steps",
        variant: "destructive",
      })
    },
  })

  // Finalize workflow
  const finalizeWorkflow = useMutation({
    mutationFn: async ({ successful }: { successful: boolean }) => {
      if (!workflow) {
        throw new Error("No active workflow")
      }

      const response = await fetch("/api/ai/troubleshooting", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "finalizeWorkflow",
          workflow,
          steps: remediationResults,
          successful,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to finalize workflow")
      }

      const data = await response.json()
      return data.data.workflow
    },
    onSuccess: (data) => {
      setWorkflow(data)
      toast({
        title: "Workflow Finalized",
        description: `Troubleshooting workflow has been ${
          data.status === "resolved" ? "resolved" : "marked as failed"
        }.`,
      })
    },
    onError: (error) => {
      toast({
        title: "Workflow Finalization Failed",
        description: error instanceof Error ? error.message : "Failed to finalize workflow",
        variant: "destructive",
      })
    },
  })

  return {
    isCreating,
    workflow,
    diagnosticResults,
    remediationResults,
    createWorkflow,
    executeDiagnosticSteps,
    updateWithDiagnosis,
    executeRemediationSteps,
    finalizeWorkflow,
    clearWorkflow: () => {
      setWorkflow(null)
      setDiagnosticResults([])
      setRemediationResults([])
    },
  }
}
