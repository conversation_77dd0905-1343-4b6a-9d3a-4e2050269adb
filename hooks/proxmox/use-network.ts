"use client"

import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import { useServerContext } from "@/providers/server-provider"

export interface ProxmoxNetworkInterface {
  iface: string
  type: string
  active: boolean
  autostart?: boolean
  address?: string
  netmask?: string
  gateway?: string
  cidr?: string
  bridge_ports?: string
  slaves?: string
  bond_mode?: string
  mtu?: number
  vlan_raw_device?: string
  vlan_id?: number
  comments?: string
}

async function fetchNetworkInterfaces(serverInfo: any): Promise<ProxmoxNetworkInterface[]> {
  if (!serverInfo) return []

  try {
    console.log("Fetching network interfaces with server info:", {
      url: serverInfo.url,
      hasTicket: !!serverInfo.ticket,
      ticketStart: serverInfo.ticket ? serverInfo.ticket.substring(0, 10) + '...' : null
    })
    
    // Use the general API proxy to avoid CORS issues
    const response = await fetch(`/api/proxmox?path=nodes/localhost/network`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Proxmox-URL": serverInfo.url,
        "X-Proxmox-Ticket": serverInfo.ticket,
        "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Network fetch error:", {
        status: response.status,
        statusText: response.statusText,
        errorText
      })
      throw new Error(`Failed to fetch network interfaces: ${response.status} ${response.statusText}`)
    }

    const responseData = await response.json()
    console.log("Network response:", responseData)
    
    if (!responseData.data) {
      console.error("Invalid network response:", responseData)
      return []
    }

    return responseData.data
  } catch (error) {
    console.error("Failed to fetch network interfaces:", error)
    return []
  }
}

export function useNetwork() {
  const { serverInfo } = useServerContext()

  const { data = [], isLoading, error } = useQuery({
    queryKey: ["network", serverInfo?.url],
    queryFn: () => fetchNetworkInterfaces(serverInfo),
    enabled: !!serverInfo?.ticket,
    refetchInterval: 30000, // Refetch every 30 seconds
    retry: 2,
    retryDelay: 1000,
  })

  return {
    networks: data,
    isLoading,
    error,
  }
}

// Hook for a single network interface's details
export function useNetworkInterface(interfaceId: string) {
  const { serverInfo } = useServerContext()
  const queryClient = useQueryClient()

  const { data, isLoading, error } = useQuery({
    queryKey: ["networkInterface", serverInfo?.url, interfaceId],
    queryFn: async () => {
      if (!serverInfo) return null
      
      const response = await fetch(`/api/proxmox?path=nodes/localhost/network/${interfaceId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch network interface details: ${response.status}`)
      }

      const responseData = await response.json()
      return responseData.data || null
    },
    enabled: !!serverInfo?.ticket && !!interfaceId,
  })

  // Mutation for network interface actions (e.g., create/update/delete)
  const networkInterfaceMutation = useMutation({
    mutationFn: async ({ action, params }: { action: string, params?: Record<string, any> }) => {
      if (!serverInfo) throw new Error("Server info not available")
      
      const response = await fetch(`/api/proxmox?path=nodes/localhost/network/${interfaceId}/${action}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
        body: JSON.stringify(params || {}),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `Failed to perform action: ${action}`)
      }

      return await response.json()
    },
    onSuccess: (_data, variables) => {
      toast({
        title: "Success",
        description: `Network interface ${variables.action} action completed`,
      })
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["network"] })
      queryClient.invalidateQueries({ queryKey: ["networkInterface", serverInfo?.url, interfaceId] })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to perform network interface action",
        variant: "destructive",
      })
    },
  })

  return {
    networkInterface: data,
    isLoading,
    error,
    performAction: (action: string, params?: Record<string, any>) => 
      networkInterfaceMutation.mutate({ action, params }),
    isActionLoading: networkInterfaceMutation.isPending,
  }
} 