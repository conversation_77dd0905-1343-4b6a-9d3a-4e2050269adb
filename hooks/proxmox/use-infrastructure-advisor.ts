"use client"

import { useState } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import type {
  InfrastructureAnalysisRequest,
  InfrastructureAnalysisResponse,
  OptimizationRecommendation,
} from "@/types/ai-features"

/**
 * Hook to analyze infrastructure
 */
export function useInfrastructureAnalysis() {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState<InfrastructureAnalysisResponse | null>(null)

  const analyzeInfrastructure = async (request: InfrastructureAnalysisRequest) => {
    setIsAnalyzing(true)
    try {
      const response = await fetch("/api/ai/infrastructure-advisor", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to analyze infrastructure")
      }

      const data = await response.json()
      setAnalysis(data.data)
      return data.data
    } catch (error) {
      toast({
        title: "Analysis Error",
        description: error instanceof Error ? error.message : "Failed to analyze infrastructure",
        variant: "destructive",
      })
      throw error
    } finally {
      setIsAnalyzing(false)
    }
  }

  return {
    isAnalyzing,
    analysis,
    analyzeInfrastructure,
    clearAnalysis: () => setAnalysis(null),
  }
}

/**
 * Hook to apply an optimization recommendation
 */
export function useApplyOptimization() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      recommendation,
      vmId,
      serverId,
    }: {
      recommendation: OptimizationRecommendation
      vmId: string
      serverId: string
    }) => {
      const response = await fetch("/api/ai/infrastructure-advisor", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          recommendation,
          vmId,
          serverId,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to apply optimization")
      }

      const data = await response.json()
      return data.data
    },
    onSuccess: (data) => {
      toast({
        title: "Optimization Applied",
        description: data.message || "Optimization has been applied successfully.",
      })
      // Invalidate VM queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["vm"] })
    },
    onError: (error) => {
      toast({
        title: "Optimization Failed",
        description: error instanceof Error ? error.message : "Failed to apply optimization",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to get resource usage history for a VM
 */
export function useVMResourceHistory(vmId: string, timeframe: "day" | "week" | "month" = "week") {
  return useQuery({
    queryKey: ["vm-resource-history", vmId, timeframe],
    queryFn: async () => {
      const response = await fetch(`/api/vms/${vmId}/resource-history?timeframe=${timeframe}`)
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to fetch VM resource history")
      }
      const data = await response.json()
      return data.data
    },
    enabled: !!vmId,
  })
}
