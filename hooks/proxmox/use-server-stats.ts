"use client"

import { useQuery } from "@tanstack/react-query"
import { useEffect } from "react"
import { ProxmoxNode } from "@/types/proxmox"
import { useServerContext } from "@/providers/server-provider"


async function fetchNodeStats(serverInfo: any): Promise<ProxmoxNode[]> {
  if (!serverInfo) return []

  try {
    console.log("Fetching nodes with server info:", {
      url: serverInfo.url,
      hasTicket: !!serverInfo.ticket
    })

    // Use the general API proxy to avoid CORS issues
    const response = await fetch(`/api/proxmox?path=nodes`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Proxmox-URL": serverInfo.url,
        "X-Proxmox-Ticket": serverInfo.ticket,
        "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Node fetch error:", {
        status: response.status,
        statusText: response.statusText,
        errorText
      })
      throw new Error(`Failed to fetch node statistics: ${response.status} ${response.statusText}`)
    }

    const responseData = await response.json()
    console.log("Node response:", responseData)

    if (!responseData.data) {
      console.error("Invalid node response:", responseData)
      return []
    }

    return responseData.data.map((node: any) => ({
      node: node.node,
      status: node.status,
      cpu: node.cpu || 0,
      maxcpu: node.maxcpu || 1,
      mem: node.mem || 0,
      maxmem: node.maxmem || 1024,
      disk: node.disk || 0,
      maxdisk: node.maxdisk || 0,
      uptime: node.uptime || 0,
    }))
  } catch (error) {
    console.error("Failed to fetch node statistics:", error)
    return []
  }
}

export function useServerStats() {
  const { serverInfo } = useServerContext()

  const { data = [], isLoading, error, refetch } = useQuery({
    queryKey: ["nodeStats", serverInfo?.url],
    queryFn: () => fetchNodeStats(serverInfo),
    enabled: !!serverInfo?.url && !!serverInfo?.ticket && serverInfo.ticket !== "placeholder",
    refetchInterval: 60000, // Refetch every minute
    retry: 3,
    retryDelay: 1000,
  })

  // If we have serverInfo but the query is disabled due to missing ticket, refetch when ticket becomes available
  useEffect(() => {
    if (serverInfo?.ticket && serverInfo.ticket !== "placeholder" && error) {
      console.log("Ticket is now available, refetching node stats")
      refetch()
    }
  }, [serverInfo?.ticket, error, refetch])

  return {
    nodeStats: data,
    isLoading,
    error,
  }
}

export function useServerStatsById(serverId: string) {
  const { serverInfo } = useServerContext()

  const { data = [], isLoading, error, refetch } = useQuery({
    queryKey: ["nodeStats", serverInfo?.url, serverId],
    queryFn: async () => {
      const allNodeStats = await fetchNodeStats(serverInfo)
      return allNodeStats.filter(node => node.node === serverId)
    },
    enabled: !!serverInfo?.url && !!serverInfo?.ticket && serverInfo.ticket !== "placeholder" && !!serverId,
    refetchInterval: 60000, // Refetch every minute
    retry: 3,
    retryDelay: 1000,
  })

  useEffect(() => {
    if (serverInfo?.ticket && serverInfo.ticket !== "placeholder" && error) {
      console.log("Ticket is now available, refetching node stats for server:", serverId)
      refetch()
    }
  }, [serverInfo?.ticket, error, refetch, serverId])

  return {
    nodeStats: data,
    isLoading,
    error,
  }
}
