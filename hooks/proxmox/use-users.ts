"use client"

import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import { useServerContext } from "@/providers/server-provider"

export interface ProxmoxUser {
  userid: string
  firstname?: string
  lastname?: string
  email?: string
  enable: boolean
  expire?: number
  groups?: string[]
  realm?: string
  comment?: string
}

async function fetchUsers(serverInfo: any): Promise<ProxmoxUser[]> {
  if (!serverInfo) return []

  try {
    console.log("Fetching users with server info:", {
      url: serverInfo.url,
      hasTicket: !!serverInfo.ticket,
      ticketStart: serverInfo.ticket ? serverInfo.ticket.substring(0, 10) + '...' : null
    })
    
    // Use the general API proxy to avoid CORS issues
    const response = await fetch(`/api/proxmox?path=access/users`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Proxmox-URL": serverInfo.url,
        "X-Proxmox-Ticket": serverInfo.ticket,
        "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Users fetch error:", {
        status: response.status,
        statusText: response.statusText,
        errorText
      })
      throw new Error(`Failed to fetch users: ${response.status} ${response.statusText}`)
    }

    const responseData = await response.json()
    console.log("Users response:", responseData)
    
    if (!responseData.data) {
      console.error("Invalid users response:", responseData)
      return []
    }

    // Map user data
    return responseData.data.map((user: any) => ({
      userid: user.userid,
      firstname: user.firstname,
      lastname: user.lastname,
      email: user.email,
      enable: user.enable !== 0,
      expire: user.expire,
      groups: user.groups ? user.groups.split(',') : [],
      realm: user.realm || user.userid.split('@')[1],
      comment: user.comment
    }))
  } catch (error) {
    console.error("Failed to fetch users:", error)
    return []
  }
}

export function useUsers() {
  const { serverInfo } = useServerContext()

  const { data = [], isLoading, error } = useQuery({
    queryKey: ["users", serverInfo?.url],
    queryFn: () => fetchUsers(serverInfo),
    enabled: !!serverInfo?.ticket,
    refetchInterval: 60000, // Refetch every minute
    retry: 2,
    retryDelay: 1000,
  })

  return {
    users: data,
    isLoading,
    error,
  }
}

export function useUserActions() {
  const { serverInfo } = useServerContext()
  const queryClient = useQueryClient()

  // Mutation for user operations (create, update, delete)
  const userMutation = useMutation({
    mutationFn: async ({ 
      action, 
      userid, 
      userData 
    }: { 
      action: 'create' | 'update' | 'delete'
      userid?: string
      userData?: Partial<ProxmoxUser> & { password?: string }
    }) => {
      if (!serverInfo) throw new Error("Server info not available")
      
      let url = '/api/proxmox?path=access/users'
      let method = 'POST'
      
      if (action === 'update' && userid) {
        url = `/api/proxmox?path=access/users/${encodeURIComponent(userid)}`
        method = 'PUT'
      } else if (action === 'delete' && userid) {
        url = `/api/proxmox?path=access/users/${encodeURIComponent(userid)}`
        method = 'DELETE'
      }
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
        body: action !== 'delete' ? JSON.stringify(userData || {}) : undefined,
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Failed to ${action} user`)
      }

      return await response.json()
    },
    onSuccess: (_data, variables) => {
      const actionText = variables.action === 'create' 
        ? 'created' 
        : variables.action === 'update' 
        ? 'updated' 
        : 'deleted'
      
      toast({
        title: "Success",
        description: `User ${variables.userid || ''} ${actionText} successfully`,
      })
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["users"] })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to perform user operation",
        variant: "destructive",
      })
    },
  })

  return {
    createUser: (userData: Partial<ProxmoxUser> & { password: string }) => 
      userMutation.mutate({ action: 'create', userData }),
    updateUser: (userid: string, userData: Partial<ProxmoxUser>) => 
      userMutation.mutate({ action: 'update', userid, userData }),
    deleteUser: (userid: string) => 
      userMutation.mutate({ action: 'delete', userid }),
    isActionLoading: userMutation.isPending,
  }
} 