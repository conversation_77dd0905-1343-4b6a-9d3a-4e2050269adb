"use client"

import { useState, useEffect, useCallback } from "react"
import { toast } from "@/hooks/use-toast"
import { getServerById } from "@/lib/server-storage"

export function useVmActions(serverId?: string, vmId?: string) {
  const [vmStatus, setVmStatus] = useState<string | null>(null)
  const [vmDetails, setVmDetails] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch VM status if serverId and vmId are provided
  useEffect(() => {
    if (!serverId || !vmId) return

    let isMounted = true
    const server = getServerById(serverId)
    if (!server) return

    async function fetchVmStatus() {
      try {
        setError(null)

        // First find which node the VM is on
        const response = await fetch(`/api/proxmox/vms/${vmId}/status`)

        if (!response.ok) {
          throw new Error("Failed to fetch VM status")
        }

        const data = await response.json()

        if (isMounted) {
          setVmStatus(data.data.status)
          setVmDetails(data.data)
        }
      } catch (err) {
        console.error("Error fetching VM status:", err)
        if (isMounted) {
          setError("Failed to fetch VM status")
        }
      }
    }

    fetchVmStatus()

    // Set up polling for updates
    const intervalId = setInterval(fetchVmStatus, 5000) // Update every 5 seconds

    return () => {
      isMounted = false
      clearInterval(intervalId)
    }
  }, [serverId, vmId])

  const executeVmAction = useCallback(
    async (actionServerId: string, actionVmId: string, action: string, successMessage: string) => {
      setIsLoading(true)
      setError(null)

      try {
        const server = getServerById(actionServerId)
        if (!server) {
          throw new Error("Server not found")
        }

        const response = await fetch(`/api/proxmox/${actionServerId}/vms/${actionVmId}/status`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ action }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || `Failed to ${action} VM`)
        }

        toast({
          title: "Success",
          description: successMessage,
        })

        // Update VM status after action
        if (actionServerId === serverId && actionVmId === vmId) {
          // Set temporary status based on action
          if (action === "start") setVmStatus("starting")
          else if (action === "stop") setVmStatus("stopping")
          else if (action === "reset") setVmStatus("resetting")
        }
      } catch (err) {
        console.error(`Error executing ${action}:`, err)
        const errorMessage = err instanceof Error ? err.message : `Failed to ${action} VM`
        setError(errorMessage)
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    },
    [serverId, vmId],
  )

  const startVm = useCallback(
    (actionServerId: string, actionVmId: string) => {
      return executeVmAction(actionServerId, actionVmId, "start", "VM is starting")
    },
    [executeVmAction],
  )

  const stopVm = useCallback(
    (actionServerId: string, actionVmId: string) => {
      return executeVmAction(actionServerId, actionVmId, "stop", "VM is stopping")
    },
    [executeVmAction],
  )

  const resetVm = useCallback(
    (actionServerId: string, actionVmId: string) => {
      return executeVmAction(actionServerId, actionVmId, "reset", "VM is resetting")
    },
    [executeVmAction],
  )

  return {
    startVm,
    stopVm,
    resetVm,
    vmStatus,
    vmDetails,
    isLoading,
    error,
  }
}
