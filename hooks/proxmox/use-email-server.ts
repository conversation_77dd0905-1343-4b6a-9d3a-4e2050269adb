"use client"

import { useState, useEffect, useCallback } from "react"
import { toast } from "sonner"

export type EmailServerStatus = {
  mailServer: {
    status: "running" | "stopped" | "error"
    version: string
  }
  services: {
    imap: {
      status: "running" | "stopped" | "error"
      port: number
    }
    imaps: {
      status: "running" | "stopped" | "error"
      port: number
    }
    pop3: {
      status: "running" | "stopped" | "error"
      port: number
    }
    pop3s: {
      status: "running" | "stopped" | "error"
      port: number
    }
    smtp: {
      status: "running" | "stopped" | "error"
      port: number
    }
    smtps: {
      status: "running" | "stopped" | "error"
      port: number
    }
  }
  security: {
    spamFilter: {
      status: "running" | "stopped" | "error"
      version: string
    }
    antivirus: {
      status: "running" | "stopped" | "error"
      version: string
      lastUpdate: string
    }
    firewall: {
      status: "running" | "stopped" | "error"
    }
  }
  storage: {
    totalSpace: number // in bytes
    usedSpace: number // in bytes
    mailboxes: number
  }
}

export type EmailServerConfig = {
  hostname: string
  domain: string
  ipAddress: string
  maxMessageSize: number // in MB
  defaultQuota: number // in MB
  relayHost?: string
  enableImap: boolean
  enablePop3: boolean
  enableSpamFilter: boolean
  enableAntivirus: boolean
  enableFirewall: boolean
  enableSSL: boolean
  sslCertificate?: string
  sslKey?: string
}

export function useEmailServer() {
  const [serverStatus, setServerStatus] = useState<EmailServerStatus | null>(null)
  const [serverConfig, setServerConfig] = useState<EmailServerConfig | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch server status
  const fetchServerStatus = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch("/api/email/server/status")
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch server status")
      }
      
      const data = await response.json()
      setServerStatus(data.status)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to load server status")
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Fetch server configuration
  const fetchServerConfig = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch("/api/email/server/config")
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch server configuration")
      }
      
      const data = await response.json()
      setServerConfig(data.config)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to load server configuration")
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Update server configuration
  const updateServerConfig = useCallback(async (config: Partial<EmailServerConfig>) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch("/api/email/server/config", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(config),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update server configuration")
      }
      
      const data = await response.json()
      setServerConfig(data.config)
      toast.success("Server configuration updated successfully")
      return data.config
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to update server configuration")
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Restart mail server
  const restartServer = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch("/api/email/server/restart", {
        method: "POST",
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to restart server")
      }
      
      toast.success("Mail server restarting...")
      
      // Poll for server status
      const checkStatus = async () => {
        try {
          const statusResponse = await fetch("/api/email/server/status")
          if (statusResponse.ok) {
            const statusData = await statusResponse.json()
            setServerStatus(statusData.status)
            
            if (statusData.status.mailServer.status === "running") {
              toast.success("Mail server restarted successfully")
              return true
            }
          }
          return false
        } catch (error) {
          return false
        }
      }
      
      // Poll every 2 seconds for up to 30 seconds
      let attempts = 0
      const maxAttempts = 15
      
      const poll = setInterval(async () => {
        attempts++
        const success = await checkStatus()
        
        if (success || attempts >= maxAttempts) {
          clearInterval(poll)
          setIsLoading(false)
          
          if (!success && attempts >= maxAttempts) {
            toast.error("Server restart is taking longer than expected")
          }
        }
      }, 2000)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to restart server")
      setIsLoading(false)
    }
  }, [])

  // Load server status and config on component mount
  useEffect(() => {
    fetchServerStatus()
    fetchServerConfig()
  }, [fetchServerStatus, fetchServerConfig])

  return {
    serverStatus,
    serverConfig,
    isLoading,
    error,
    fetchServerStatus,
    fetchServerConfig,
    updateServerConfig,
    restartServer,
  }
}
