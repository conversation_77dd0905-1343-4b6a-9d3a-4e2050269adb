"use client"

import { useState, useCallback } from "react"
import { useQuery, useMutation } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import type {
  ImapAssistantSession,
  ImapAssistantMessage,
  ImapAssistantRequest,
  ImapAssistantAction,
} from "@/types/imap-assistant"
import type { ImapMigrationConfig } from "@/types/alternative-migrations"

/**
 * Hook to create and manage an IMAP Assistant session
 */
export function useImapAssistant(initialConfig?: Partial<ImapMigrationConfig>) {
  const [session, setSession] = useState<ImapAssistantSession | null>(null)
  const [messages, setMessages] = useState<ImapAssistantMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [inputValue, setInputValue] = useState("")

  // Create a new session
  const createSession = useQuery({
    queryKey: ["imap-assistant-session"],
    queryFn: async () => {
      const response = await fetch("/api/ai/imap-assistant/session")
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to create IMAP Assistant session")
      }
      const data = await response.json()
      return data.data as ImapAssistantSession
    },
    enabled: false,
    onSuccess: (data) => {
      setSession(data)
      setMessages(data.messages.filter((msg) => msg.role !== "system"))
    },
    onError: (error) => {
      toast({
        title: "Session Creation Error",
        description: error instanceof Error ? error.message : "Failed to create IMAP Assistant session",
        variant: "destructive",
      })
    },
  })

  // Send a message to the assistant
  const sendMessage = useMutation({
    mutationFn: async (request: ImapAssistantRequest) => {
      const response = await fetch("/api/ai/imap-assistant", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...request,
          sessionId: session?.id,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to send message to IMAP Assistant")
      }

      const data = await response.json()
      return data.data
    },
    onSuccess: (data) => {
      if (data.message) {
        setMessages((prev) => [...prev, data.message])
      }
    },
    onError: (error) => {
      toast({
        title: "Message Error",
        description: error instanceof Error ? error.message : "Failed to send message to IMAP Assistant",
        variant: "destructive",
      })
    },
  })

  // Initialize the session
  const initializeSession = useCallback(async () => {
    if (!session) {
      await createSession.refetch()
    }
  }, [session, createSession])

  // Send a user message
  const sendUserMessage = useCallback(
    async (message: string, config?: ImapMigrationConfig) => {
      if (!message.trim()) return

      // Add user message to the list immediately
      const userMessage: ImapAssistantMessage = {
        id: Date.now().toString(),
        role: "user",
        content: message,
        timestamp: Date.now(),
      }
      setMessages((prev) => [...prev, userMessage])
      setInputValue("")
      setIsLoading(true)

      try {
        await sendMessage.mutateAsync({
          message,
          migrationConfig: config || session?.migrationConfig,
        })
      } finally {
        setIsLoading(false)
      }
    },
    [session, sendMessage]
  )

  // Perform an action
  const performAction = useCallback(
    async (action: ImapAssistantAction, config?: ImapMigrationConfig) => {
      setIsLoading(true)

      try {
        await sendMessage.mutateAsync({
          message: "",
          action,
          migrationConfig: config || session?.migrationConfig,
        })
      } finally {
        setIsLoading(false)
      }
    },
    [session, sendMessage]
  )

  // Update the migration configuration
  const updateConfig = useCallback(
    (config: Partial<ImapMigrationConfig>) => {
      if (session) {
        setSession({
          ...session,
          migrationConfig: {
            ...session.migrationConfig,
            ...config,
            sourceConfig: {
              ...session.migrationConfig.sourceConfig,
              ...(config.sourceConfig || {}),
            },
            serverConfig: {
              ...session.migrationConfig.serverConfig,
              ...(config.serverConfig || {}),
            },
            destinationConfig: {
              ...session.migrationConfig.destinationConfig,
              ...(config.destinationConfig || {}),
            },
            migrationPlan: {
              ...session.migrationConfig.migrationPlan,
              ...(config.migrationPlan || {}),
            },
          },
        })
      }
    },
    [session]
  )

  return {
    session,
    messages,
    isLoading,
    inputValue,
    setInputValue,
    initializeSession,
    sendUserMessage,
    performAction,
    updateConfig,
  }
}

/**
 * Hook to validate IMAP source configuration
 */
export function useValidateSourceConfig() {
  return useMutation({
    mutationFn: async (config: ImapMigrationConfig["sourceConfig"]) => {
      const response = await fetch("/api/ai/imap-assistant", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: {
            type: "VALIDATE_SOURCE_CONFIG",
            config,
          },
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to validate source configuration")
      }

      const data = await response.json()
      return data.data.result
    },
    onError: (error) => {
      toast({
        title: "Validation Error",
        description: error instanceof Error ? error.message : "Failed to validate source configuration",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to validate IMAP server configuration
 */
export function useValidateServerConfig() {
  return useMutation({
    mutationFn: async (config: ImapMigrationConfig["serverConfig"]) => {
      const response = await fetch("/api/ai/imap-assistant", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: {
            type: "VALIDATE_SERVER_CONFIG",
            config,
          },
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to validate server configuration")
      }

      const data = await response.json()
      return data.data.result
    },
    onError: (error) => {
      toast({
        title: "Validation Error",
        description: error instanceof Error ? error.message : "Failed to validate server configuration",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to get server configuration recommendations
 */
export function useServerRecommendations() {
  return useMutation({
    mutationFn: async (domain: string) => {
      const response = await fetch("/api/ai/imap-assistant", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: {
            type: "RECOMMEND_SERVER_CONFIG",
            domain,
          },
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to get server recommendations")
      }

      const data = await response.json()
      return data.data.result
    },
    onError: (error) => {
      toast({
        title: "Recommendation Error",
        description: error instanceof Error ? error.message : "Failed to get server recommendations",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to get migration plan recommendations
 */
export function useMigrationPlanRecommendations() {
  return useMutation({
    mutationFn: async (accounts: ImapMigrationConfig["sourceConfig"]["emailAccounts"]) => {
      const response = await fetch("/api/ai/imap-assistant", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: {
            type: "RECOMMEND_MIGRATION_PLAN",
            accounts,
          },
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to get migration plan recommendations")
      }

      const data = await response.json()
      return data.data.result
    },
    onError: (error) => {
      toast({
        title: "Recommendation Error",
        description: error instanceof Error ? error.message : "Failed to get migration plan recommendations",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook to get explanations for migration steps
 */
export function useStepExplanation() {
  return useMutation({
    mutationFn: async (step: string) => {
      const response = await fetch("/api/ai/imap-assistant", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: {
            type: "EXPLAIN_STEP",
            step,
          },
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || "Failed to get step explanation")
      }

      const data = await response.json()
      return data.data.result
    },
    onError: (error) => {
      toast({
        title: "Explanation Error",
        description: error instanceof Error ? error.message : "Failed to get step explanation",
        variant: "destructive",
      })
    },
  })
}
