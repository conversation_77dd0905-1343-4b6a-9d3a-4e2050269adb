"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useState, type <PERSON>actN<PERSON> } from "react"
import type { Server } from "@/types/server"

interface ServerContextType {
  selectedServer: Server | null
  setSelectedServer: (server: Server | null) => void
}

const ServerContext = createContext<ServerContextType | undefined>(undefined)

export function ServerProvider({ children }: { children: ReactNode }) {
  const [selectedServer, setSelectedServer] = useState<Server | null>(null)

  return <ServerContext.Provider value={{ selectedServer, setSelectedServer }}>{children}</ServerContext.Provider>
}

export function useServerContext() {
  const context = useContext(ServerContext)

  if (context === undefined) {
    throw new Error("useServerContext must be used within a ServerProvider")
  }

  return context
}
