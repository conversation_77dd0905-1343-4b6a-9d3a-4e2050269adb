"use client"

import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"
import { useServerContext } from "@/providers/server-provider"

export interface ProxmoxBackup {
  id: string
  name: string
  type: string
  size: number
  created: string
  status: string
  vmid?: string
  volid?: string
  storage?: string
  notes?: string
}

export interface ProxmoxBackupJob {
  id: string
  name: string
  schedule: string
  target: string
  retention: string
  lastRun: string
  enabled: boolean
  compress?: string
  mode?: string
  notes?: string
}

async function fetchBackups(serverInfo: any): Promise<ProxmoxBackup[]> {
  if (!serverInfo) return []

  try {
    console.log("Fetching backups with server info:", {
      url: serverInfo.url,
      hasTicket: !!serverInfo.ticket,
      ticketStart: serverInfo.ticket ? serverInfo.ticket.substring(0, 10) + '...' : null
    })
    
    // Use the general API proxy to avoid CORS issues
    const response = await fetch(`/api/proxmox?path=nodes/localhost/storage/local/content`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Proxmox-URL": serverInfo.url,
        "X-Proxmox-Ticket": serverInfo.ticket,
        "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Backups fetch error:", {
        status: response.status,
        statusText: response.statusText,
        errorText
      })
      throw new Error(`Failed to fetch backups: ${response.status} ${response.statusText}`)
    }

    const responseData = await response.json()
    console.log("Backups response:", responseData)
    
    if (!responseData.data) {
      console.error("Invalid backups response:", responseData)
      return []
    }

    // Filter and map backup content
    return responseData.data
      .filter((item: any) => item.content === 'backup' || item.content === 'iso')
      .map((backup: any, index: number) => ({
        id: backup.volid || `backup-${index}`,
        name: backup.volid?.split('/').pop() || `Backup ${index}`,
        type: backup.content,
        size: backup.size || 0,
        created: backup.ctime ? new Date(backup.ctime * 1000).toISOString() : new Date().toISOString(),
        status: "success", // Mock status since the API doesn't provide this
        vmid: backup.vmid?.toString(),
        volid: backup.volid,
        storage: backup.storage,
        notes: backup.notes
      }))
  } catch (error) {
    console.error("Failed to fetch backups:", error)
    return []
  }
}

async function fetchBackupJobs(serverInfo: any): Promise<ProxmoxBackupJob[]> {
  if (!serverInfo) return []

  try {
    // Proxmox doesn't have a direct API for backup jobs schedules in a nice format,
    // so we'll mock this data or construct it from vzdum configs
    const response = await fetch(`/api/proxmox?path=cluster/backup`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Proxmox-URL": serverInfo.url,
        "X-Proxmox-Ticket": serverInfo.ticket,
        "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
      },
    })

    if (!response.ok) {
      console.error("Backup jobs fetch error:", response.status)
      // Return mock data if the API doesn't work or isn't available yet
      return getMockBackupJobs();
    }

    const responseData = await response.json()
    console.log("Backup jobs response:", responseData)
    
    if (!responseData.data) {
      return getMockBackupJobs();
    }

    // Map the backup jobs from the API
    return responseData.data.map((job: any, index: number) => ({
      id: job.id || `job-${index}`,
      name: job.id || `Backup Job ${index}`,
      schedule: job.schedule || 'daily',
      target: job.storage || 'local',
      retention: `${job.maxfiles || 1} versions`,
      lastRun: job.lastrun || new Date().toISOString(),
      enabled: job.enabled !== 0,
      compress: job.compress || 'zstd',
      mode: job.mode || 'snapshot',
      notes: job.comment
    }))
  } catch (error) {
    console.error("Failed to fetch backup jobs:", error)
    return getMockBackupJobs();
  }
}

// Mock data for testing until the API is fully implemented
function getMockBackupJobs(): ProxmoxBackupJob[] {
  return [
    {
      id: "job-1",
      name: "Daily VM Backup",
      schedule: "0 0 * * *",
      target: "local",
      retention: "7 versions",
      lastRun: new Date(Date.now() - 86400000).toISOString(),
      enabled: true,
      compress: "zstd",
      mode: "snapshot",
      notes: "Daily backup of all VMs"
    },
    {
      id: "job-2",
      name: "Weekly Full Backup",
      schedule: "0 0 * * 0",
      target: "backup-nfs",
      retention: "4 versions",
      lastRun: new Date(Date.now() - 86400000 * 3).toISOString(),
      enabled: true,
      compress: "zstd",
      mode: "stop",
      notes: "Weekly full backup to NFS storage"
    }
  ]
}

export function useBackups() {
  const { serverInfo } = useServerContext()

  const { 
    data: backups = [], 
    isLoading: isLoadingBackups, 
    error: backupsError 
  } = useQuery({
    queryKey: ["backups", serverInfo?.url],
    queryFn: () => fetchBackups(serverInfo),
    enabled: !!serverInfo?.ticket,
    refetchInterval: 60000, // Refresh every minute
    retry: 2,
    retryDelay: 1000,
  })

  const { 
    data: backupJobs = [], 
    isLoading: isLoadingJobs, 
    error: jobsError 
  } = useQuery({
    queryKey: ["backupJobs", serverInfo?.url],
    queryFn: () => fetchBackupJobs(serverInfo),
    enabled: !!serverInfo?.ticket,
    refetchInterval: 60000, // Refresh every minute
    retry: 2,
    retryDelay: 1000,
  })

  return {
    backups,
    backupJobs,
    isLoading: isLoadingBackups || isLoadingJobs,
    backupsError,
    jobsError,
  }
}

export function useBackupActions() {
  const { serverInfo } = useServerContext()
  const queryClient = useQueryClient()

  // Mutation for backup operations (create, delete, restore)
  const backupMutation = useMutation({
    mutationFn: async ({ action, id, params }: { action: string, id: string, params?: Record<string, any> }) => {
      if (!serverInfo) throw new Error("Server info not available")
      
      const path = action === 'create' 
        ? 'nodes/localhost/vzdump'
        : `nodes/localhost/storage/local/content/${id}`;
      
      const method = action === 'delete' ? 'DELETE' : 'POST';
      
      const response = await fetch(`/api/proxmox?path=${path}`, {
        method,
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
        body: JSON.stringify(params || {}),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Failed to ${action} backup`)
      }

      return await response.json()
    },
    onSuccess: (_data, variables) => {
      toast({
        title: "Success",
        description: `Backup ${variables.action} operation completed`,
      })
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["backups"] })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to perform backup operation",
        variant: "destructive",
      })
    },
  })

  return {
    performBackupAction: (action: string, id: string, params?: Record<string, any>) =>
      backupMutation.mutate({ action, id, params }),
    isActionLoading: backupMutation.isPending,
  }
}