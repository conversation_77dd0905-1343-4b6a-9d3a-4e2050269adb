import { useState, useEffect } from "react"
import { useServerContext } from "@/providers/server-provider"
import { apiClient } from "@/lib/api-client"

interface VirtualMachine {
  vmid: number
  name: string
  status: string
  cpu: number
  mem: number
  maxmem: number
  uptime: number
  cpus: number
  memory: number
  disk: number
  node: string
  ostype: string
  diskread: number
  diskwrite: number
  netout: number
  netin: number
  maxdisk: number
}

export function useVirtualMachine(vmId: string) {
  const [vm, setVM] = useState<VirtualMachine | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const { serverInfo } = useServerContext()

  useEffect(() => {
    async function fetchVM() {
      if (!serverInfo || !vmId) {
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        // First, fetch the VM resources to get the node name
        const resourcesResponse = await fetch(`/api/proxmox?path=cluster/resources`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "X-Proxmox-URL": serverInfo.url,
            "X-Proxmox-Ticket": serverInfo.ticket,
            "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
          },
        })

        if (!resourcesResponse.ok) {
          throw new Error(`Failed to fetch VM resources: ${resourcesResponse.statusText}`)
        }

        const resourcesData = await resourcesResponse.json()
        const vmResource = resourcesData.data.find(
          (resource: any) => resource.type === "qemu" && resource.vmid.toString() === vmId
        )

        if (!vmResource) {
          throw new Error(`VM with ID ${vmId} not found`)
        }

        const nodeName = vmResource.node

        // Fetch the detailed VM status
        const statusResponse = await fetch(`/api/proxmox?path=nodes/${nodeName}/qemu/${vmId}/status/current`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "X-Proxmox-URL": serverInfo.url,
            "X-Proxmox-Ticket": serverInfo.ticket,
            "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
          },
        })

        if (!statusResponse.ok) {
          throw new Error(`Failed to fetch VM status: ${statusResponse.statusText}`)
        }

        const statusData = await statusResponse.json()
        
        // Fetch the VM config for additional details
        const configResponse = await fetch(`/api/proxmox?path=nodes/${nodeName}/qemu/${vmId}/config`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "X-Proxmox-URL": serverInfo.url,
            "X-Proxmox-Ticket": serverInfo.ticket,
            "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
          },
        })

        if (!configResponse.ok) {
          throw new Error(`Failed to fetch VM config: ${configResponse.statusText}`)
        }

        const configData = await configResponse.json()
        
        // Parse memory in MB
        const memoryMB = parseInt(configData.data.memory) || 1024

        // Combine resource and status data to create the VM object
        const vmData: VirtualMachine = {
          vmid: parseInt(vmId),
          name: vmResource.name || `VM ${vmId}`,
          status: statusData.data.status || "unknown",
          cpu: statusData.data.cpu || 0,
          mem: statusData.data.mem || 0,
          maxmem: statusData.data.maxmem || 0,
          uptime: statusData.data.uptime || 0,
          cpus: statusData.data.cpus || parseInt(configData.data.sockets) || 1,
          memory: memoryMB * 1024 * 1024, // Convert to bytes
          disk: statusData.data.disk || 0,
          maxdisk: statusData.data.maxdisk || 0,
          diskread: statusData.data.diskread || 0,
          diskwrite: statusData.data.diskwrite || 0,
          netin: statusData.data.netin || 0,
          netout: statusData.data.netout || 0,
          node: nodeName,
          ostype: configData.data.ostype || "other",
        }
        
        setVM(vmData)
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch VM data'))
        console.error('Error fetching VM:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchVM()

    // Set up polling to refresh VM data
    const intervalId = setInterval(fetchVM, 10000) // Refresh every 10 seconds
    
    return () => {
      clearInterval(intervalId) // Clean up on unmount
    }
  }, [vmId, serverInfo])

  // Function to start the VM
  const startVM = async () => {
    if (!serverInfo || !vmId || !vm) return
    
    try {
      const nodeName = vm.node
      const response = await fetch(`/api/proxmox?path=nodes/${nodeName}/qemu/${vmId}/status/start`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
      })
      
      if (!response.ok) {
        throw new Error(`Failed to start VM: ${response.statusText}`)
      }
      
      // Update VM status locally
      setVM(prev => prev ? { ...prev, status: "starting" } : null)
    } catch (err) {
      console.error('Error starting VM:', err)
      throw err
    }
  }
  
  // Function to stop the VM
  const stopVM = async () => {
    if (!serverInfo || !vmId || !vm) return
    
    try {
      const nodeName = vm.node
      const response = await fetch(`/api/proxmox?path=nodes/${nodeName}/qemu/${vmId}/status/stop`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
      })
      
      if (!response.ok) {
        throw new Error(`Failed to stop VM: ${response.statusText}`)
      }
      
      // Update VM status locally
      setVM(prev => prev ? { ...prev, status: "stopping" } : null)
    } catch (err) {
      console.error('Error stopping VM:', err)
      throw err
    }
  }
  
  // Function to restart the VM
  const restartVM = async () => {
    if (!serverInfo || !vmId || !vm) return
    
    try {
      const nodeName = vm.node
      const response = await fetch(`/api/proxmox?path=nodes/${nodeName}/qemu/${vmId}/status/reset`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
      })
      
      if (!response.ok) {
        throw new Error(`Failed to restart VM: ${response.statusText}`)
      }
      
      // Update VM status locally
      setVM(prev => prev ? { ...prev, status: "resetting" } : null)
    } catch (err) {
      console.error('Error restarting VM:', err)
      throw err
    }
  }

  return { vm, isLoading, error, startVM, stopVM, restartVM }
} 