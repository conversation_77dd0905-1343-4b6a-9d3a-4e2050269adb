"use client"

import { useState, useEffect } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { getProxmoxClient } from "@/lib/api-client"
import { toast } from "@/hooks/use-toast"
import { useVirtualMachines } from "@/hooks/use-virtual-machines"
import { useServerContext } from "@/providers/server-provider"
import type { MigrationConfig } from "@/types/email-migration"

export type MigrationSource = "imap" | "gmail" | "office365" | "yahoo" | "zoho" | "exchange" | "pop3" | "cpanel"

export interface MigrationProgress {
  totalEmails: number
  processedEmails: number
  failedEmails: number
  bytesTransferred?: number
  totalBytes?: number
  currentFolder?: string
  currentEmail?: string
  startTime: Date
  endTime?: Date
  estimatedTimeRemaining?: number
  speed?: number
  isRunning?: boolean
  isCompleted?: boolean
  logs?: string[]
  errors?: string[]
}

export function useEmailMigration() {
  const [migrationId, setMigrationId] = useState<string | null>(null)
  const queryClient = useQueryClient()
  const { serverInfo } = useServerContext()
  const { vms, startVm } = useVirtualMachines()

  // Test cPanel connection
  const testConnectionMutation = useMutation({
    mutationFn: async (sourceConfig: any) => {
      const client = getProxmoxClient("")
      return client.testCpanelConnection(sourceConfig)
    },
    onSuccess: () => {
      toast({
        title: "Connection successful",
        description: "Successfully connected to cPanel server",
      })
    },
    onError: (error) => {
      toast({
        title: "Connection failed",
        description: error instanceof Error ? error.message : "Failed to connect to cPanel server",
        variant: "destructive",
      })
    },
  })

  // Fetch domains
  const fetchDomainsMutation = useMutation({
    mutationFn: async (sourceConfig: any) => {
      const client = getProxmoxClient("")
      return client.fetchDomains(sourceConfig)
    },
    onError: (error) => {
      toast({
        title: "Error fetching domains",
        description: error instanceof Error ? error.message : "Failed to fetch domains from cPanel server",
        variant: "destructive",
      })
    },
  })

  // Start migration
  const startMigrationMutation = useMutation({
    mutationFn: async (config: MigrationConfig) => {
      // First, check if the VM exists and is running
      const vmId = parseInt(config.destinationConfig.vmId)
      const vm = vms.find(vm => vm.vmid === vmId)

      if (!vm) {
        throw new Error(`VM with ID ${vmId} not found`)
      }

      // Start VM if it's not running
      if (vm.status !== 'running') {
        await startVm(vmId.toString())

        // Wait for VM to start (30 seconds)
        await new Promise(resolve => setTimeout(resolve, 30000))
      }

      // Now start the migration
      const client = getProxmoxClient(config.destinationConfig.serverId)
      return client.startMigration(config)
    },
    onSuccess: (data) => {
      setMigrationId(data.data.migrationId)
      toast({
        title: "Migration started",
        description: "Email migration process has been started",
      })

      // Invalidate queries to refresh data
      if (data.data.migrationId) {
        queryClient.invalidateQueries({ queryKey: ["migrationStatus", data.data.migrationId] })
      }
    },
    onError: (error) => {
      toast({
        title: "Migration failed",
        description: error instanceof Error ? error.message : "Failed to start migration process",
        variant: "destructive",
      })
    },
  })

  // Get migration status
  const { data: migrationStatus, refetch: refetchStatus } = useQuery({
    queryKey: ["migrationStatus", migrationId],
    queryFn: async () => {
      if (!migrationId) return null

      const client = getProxmoxClient("")
      const result = await client.getMigrationStatus(migrationId)
      return result.data
    },
    enabled: !!migrationId,
    refetchInterval: migrationId ? 2000 : false,
  })

  // Abort migration
  const abortMigrationMutation = useMutation({
    mutationFn: async () => {
      if (!migrationId) throw new Error("Migration ID not found")

      const client = getProxmoxClient("")
      return client.abortMigration(migrationId)
    },
    onSuccess: () => {
      toast({
        title: "Migration aborted",
        description: "Email migration has been aborted",
      })
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to abort migration process",
        variant: "destructive",
      })
    },
  })

  // Verify migration
  const verifyMigrationMutation = useMutation({
    mutationFn: async (config: MigrationConfig) => {
      const client = getProxmoxClient(config.destinationConfig.serverId)
      return client.verifyMigration(config)
    },
    onSuccess: () => {
      toast({
        title: "Verification completed",
        description: "Email migration has been verified",
      })
    },
    onError: (error) => {
      toast({
        title: "Verification failed",
        description: error instanceof Error ? error.message : "Failed to verify migration",
        variant: "destructive",
      })
    },
  })

  return {
    // Test connection
    testConnection: testConnectionMutation.mutate,
    isTestingConnection: testConnectionMutation.isPending,

    // Fetch domains
    fetchDomains: fetchDomainsMutation.mutate,
    domains: fetchDomainsMutation.data || [],
    isFetchingDomains: fetchDomainsMutation.isPending,

    // Start migration
    startMigration: startMigrationMutation.mutate,
    isStartingMigration: startMigrationMutation.isPending,

    // Migration status
    migrationId,
    migrationStatus,
    refetchStatus,

    // Abort migration
    abortMigration: abortMigrationMutation.mutate,
    isAbortingMigration: abortMigrationMutation.isPending,

    // Verify migration
    verifyMigration: verifyMigrationMutation.mutate,
    isVerifyingMigration: verifyMigrationMutation.isPending,
    verificationResults: verifyMigrationMutation.data?.data,
  }
}
