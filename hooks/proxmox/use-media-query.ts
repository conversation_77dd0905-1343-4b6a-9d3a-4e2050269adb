"use client"

import { useState, useEffect } from "react"

export function useMediaQuery(query: string): boolean {
  // Initialize with null to indicate it hasn't been measured yet
  const [matches, setMatches] = useState<boolean | null>(null)

  // Return true for desktop queries when rendering on the server or during initial hydration
  // This prevents layout shift when the component first renders
  if (matches === null) {
    // If it's a min-width query (desktop), default to true to prevent mobile flash
    // If it's a max-width query (mobile), default to false
    return query.includes('min-width') ? true : false
  }

  useEffect(() => {
    // Create the media query list
    const media = window.matchMedia(query)

    // Set initial value
    setMatches(media.matches)

    // Define listener function
    const listener = () => setMatches(media.matches)

    // Add listener
    media.addEventListener("change", listener)

    // Clean up
    return () => media.removeEventListener("change", listener)
  }, [query]) // Only re-run if the query changes

  return matches
}
