import { useState, useEffect } from "react"
import { useServerContext } from "@/providers/server-provider"

interface Container {
  id: number
  name: string
  status: string
  cpu: number
  mem: number
  maxmem: number
  uptime: number
  cpus: number
  memory: number
  disk: number
  maxdisk: number
  node: string
  template: string
  diskread: number
  diskwrite: number
  netout: number
  netin: number
}

export function useContainer(containerId: string) {
  const [container, setContainer] = useState<Container | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const { serverInfo } = useServerContext()

  useEffect(() => {
    async function fetchContainer() {
      if (!serverInfo || !containerId) {
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        // First, fetch the container resources to get the node name
        const resourcesResponse = await fetch(`/api/proxmox?path=cluster/resources`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "X-Proxmox-URL": serverInfo.url,
            "X-Proxmox-Ticket": serverInfo.ticket,
            "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
          },
        })

        if (!resourcesResponse.ok) {
          throw new Error(`Failed to fetch container resources: ${resourcesResponse.statusText}`)
        }

        const resourcesData = await resourcesResponse.json()
        const containerResource = resourcesData.data.find(
          (resource: any) => resource.type === "lxc" && resource.vmid.toString() === containerId
        )

        if (!containerResource) {
          throw new Error(`Container with ID ${containerId} not found`)
        }

        const nodeName = containerResource.node

        // Fetch the detailed container status
        const statusResponse = await fetch(`/api/proxmox?path=nodes/${nodeName}/lxc/${containerId}/status/current`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "X-Proxmox-URL": serverInfo.url,
            "X-Proxmox-Ticket": serverInfo.ticket,
            "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
          },
        })

        if (!statusResponse.ok) {
          throw new Error(`Failed to fetch container status: ${statusResponse.statusText}`)
        }

        const statusData = await statusResponse.json()
        
        // Fetch the container config for additional details
        const configResponse = await fetch(`/api/proxmox?path=nodes/${nodeName}/lxc/${containerId}/config`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "X-Proxmox-URL": serverInfo.url,
            "X-Proxmox-Ticket": serverInfo.ticket,
            "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
          },
        })

        if (!configResponse.ok) {
          throw new Error(`Failed to fetch container config: ${configResponse.statusText}`)
        }

        const configData = await configResponse.json()
        
        // Parse memory in MB
        const memoryMB = parseInt(configData.data.memory) || 512

        // Determine the OS template
        let template = "Custom";
        if (configData.data.ostemplate) {
          const templateParts = configData.data.ostemplate.split(':');
          if (templateParts.length > 1) {
            template = templateParts[1].split('-')[0];
          }
        }

        // Combine resource and status data to create the container object
        const containerData: Container = {
          id: parseInt(containerId),
          name: containerResource.name || `CT-${containerId}`,
          status: statusData.data.status || "unknown",
          cpu: statusData.data.cpu || 0,
          mem: statusData.data.mem || 0,
          maxmem: statusData.data.maxmem || 0,
          uptime: statusData.data.uptime || 0,
          cpus: statusData.data.cpus || parseInt(configData.data.cores) || 1,
          memory: memoryMB * 1024 * 1024, // Convert to bytes
          disk: statusData.data.disk || 0,
          maxdisk: statusData.data.maxdisk || 0,
          diskread: statusData.data.diskread || 0,
          diskwrite: statusData.data.diskwrite || 0,
          netin: statusData.data.netin || 0,
          netout: statusData.data.netout || 0,
          node: nodeName,
          template: template,
        }
        
        setContainer(containerData)
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch container data'))
        console.error('Error fetching container:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchContainer()
    
    // Set up polling to refresh container data
    const intervalId = setInterval(fetchContainer, 10000) // Refresh every 10 seconds
    
    return () => {
      clearInterval(intervalId) // Clean up on unmount
    }
  }, [containerId, serverInfo])

  // Function to start the container
  const startContainer = async () => {
    if (!serverInfo || !containerId || !container) return
    
    try {
      const nodeName = container.node
      const response = await fetch(`/api/proxmox?path=nodes/${nodeName}/lxc/${containerId}/status/start`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
      })
      
      if (!response.ok) {
        throw new Error(`Failed to start container: ${response.statusText}`)
      }
      
      // Update container status locally
      setContainer(prev => prev ? { ...prev, status: "starting" } : null)
    } catch (err) {
      console.error('Error starting container:', err)
      throw err
    }
  }
  
  // Function to stop the container
  const stopContainer = async () => {
    if (!serverInfo || !containerId || !container) return
    
    try {
      const nodeName = container.node
      const response = await fetch(`/api/proxmox?path=nodes/${nodeName}/lxc/${containerId}/status/stop`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
      })
      
      if (!response.ok) {
        throw new Error(`Failed to stop container: ${response.statusText}`)
      }
      
      // Update container status locally
      setContainer(prev => prev ? { ...prev, status: "stopping" } : null)
    } catch (err) {
      console.error('Error stopping container:', err)
      throw err
    }
  }
  
  // Function to restart the container
  const restartContainer = async () => {
    if (!serverInfo || !containerId || !container) return
    
    try {
      const nodeName = container.node
      const response = await fetch(`/api/proxmox?path=nodes/${nodeName}/lxc/${containerId}/status/restart`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Proxmox-URL": serverInfo.url,
          "X-Proxmox-Ticket": serverInfo.ticket,
          "X-Proxmox-CSRFToken": serverInfo.CSRFPreventionToken,
        },
      })
      
      if (!response.ok) {
        throw new Error(`Failed to restart container: ${response.statusText}`)
      }
      
      // Update container status locally
      setContainer(prev => prev ? { ...prev, status: "restarting" } : null)
    } catch (err) {
      console.error('Error restarting container:', err)
      throw err
    }
  }

  return { container, isLoading, error, startContainer, stopContainer, restartContainer }
} 