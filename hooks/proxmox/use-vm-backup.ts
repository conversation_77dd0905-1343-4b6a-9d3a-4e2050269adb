"use client"

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { getProxmoxClient } from "@/lib/api-client"
import { toast } from "@/hooks/use-toast"

export function useVMBackups(serverId: string, nodeId: string, vmId: string) {
  const client = getProxmoxClient(serverId)
  const queryClient = useQueryClient()

  // Query for fetching backups
  const {
    data: backups = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["vm-backups", serverId, nodeId, vmId],
    queryFn: async () => {
      const response = await client.get(`/nodes/${nodeId}/storage/local/content?content=backup&vmid=${vmId}`)
      return response.data || []
    },
  })

  // Mutation for creating a backup
  const createBackupMutation = useMutation({
    mutationFn: async (options: {
      mode: "snapshot" | "suspend" | "stop"
      compress?: boolean
      description?: string
    }) => {
      return client.post(`/nodes/${nodeId}/qemu/${vmId}/backup`, options)
    },
    onSuccess: () => {
      toast({
        title: "Backup started",
        description: "The backup process has been started",
      })

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["vm-backups", serverId, nodeId, vmId] })
    },
    onError: (error) => {
      toast({
        title: "Backup failed",
        description: error instanceof Error ? error.message : "Failed to start backup",
        variant: "destructive",
      })
    },
  })

  // Mutation for restoring a backup
  const restoreBackupMutation = useMutation({
    mutationFn: async (volid: string) => {
      return client.post(`/nodes/${nodeId}/qemu/${vmId}/snapshot/rollback`, { snapname: volid })
    },
    onSuccess: () => {
      toast({
        title: "Restore started",
        description: "The restore process has been started",
      })

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["vm", serverId, vmId] })
    },
    onError: (error) => {
      toast({
        title: "Restore failed",
        description: error instanceof Error ? error.message : "Failed to restore backup",
        variant: "destructive",
      })
    },
  })

  // Mutation for deleting a backup
  const deleteBackupMutation = useMutation({
    mutationFn: async (volid: string) => {
      return client.delete(`/nodes/${nodeId}/storage/local/content/${volid}`)
    },
    onSuccess: () => {
      toast({
        title: "Backup deleted",
        description: "The backup has been deleted",
      })

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["vm-backups", serverId, nodeId, vmId] })
    },
    onError: (error) => {
      toast({
        title: "Delete failed",
        description: error instanceof Error ? error.message : "Failed to delete backup",
        variant: "destructive",
      })
    },
  })

  return {
    backups,
    isLoading,
    error,
    refetch,
    createBackup: createBackupMutation.mutate,
    isCreatingBackup: createBackupMutation.isPending,
    restoreBackup: restoreBackupMutation.mutate,
    isRestoringBackup: restoreBackupMutation.isPending,
    deleteBackup: deleteBackupMutation.mutate,
    isDeletingBackup: deleteBackupMutation.isPending,
  }
}
