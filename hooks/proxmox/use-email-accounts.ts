"use client"

import { useState, useEffect, useCallback } from "react"
import { toast } from "sonner"
import { AccountStatus } from "@/lib/generated/prisma"

export type EmailAccount = {
  id: string
  email: string
  username: string
  domainId: string
  domain?: {
    domain: string
  }
  quota: number
  used: number
  status: AccountStatus
  createdAt: Date
  updatedAt: Date
}

export type CreateEmailAccountData = {
  email: string
  username: string
  password: string
  domainId: string
  quota?: number
}

export type UpdateEmailAccountData = {
  password?: string
  quota?: number
  status?: AccountStatus
}

export function useEmailAccounts() {
  const [accounts, setAccounts] = useState<EmailAccount[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch all email accounts
  const fetchAccounts = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch("/api/email/accounts")
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to fetch email accounts")
      }
      
      const data = await response.json()
      setAccounts(data.accounts)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to load email accounts")
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Create a new email account
  const createAccount = useCallback(async (accountData: CreateEmailAccountData) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch("/api/email/accounts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(accountData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to create email account")
      }
      
      const data = await response.json()
      setAccounts((prev) => [...prev, data.account])
      toast.success("Email account created successfully")
      return data.account
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to create email account")
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Update an existing email account
  const updateAccount = useCallback(async (id: string, accountData: UpdateEmailAccountData) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/email/accounts/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(accountData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update email account")
      }
      
      const data = await response.json()
      setAccounts((prev) => 
        prev.map((account) => (account.id === id ? data.account : account))
      )
      toast.success("Email account updated successfully")
      return data.account
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to update email account")
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Delete an email account
  const deleteAccount = useCallback(async (id: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/email/accounts/${id}`, {
        method: "DELETE",
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to delete email account")
      }
      
      setAccounts((prev) => prev.filter((account) => account.id !== id))
      toast.success("Email account deleted successfully")
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      toast.error("Failed to delete email account")
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load accounts on component mount
  useEffect(() => {
    fetchAccounts()
  }, [fetchAccounts])

  return {
    accounts,
    isLoading,
    error,
    fetchAccounts,
    createAccount,
    updateAccount,
    deleteAccount,
  }
}
