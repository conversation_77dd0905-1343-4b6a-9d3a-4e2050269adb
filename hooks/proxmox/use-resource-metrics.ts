"use client"

import { useQuery } from "@tanstack/react-query"
import { useServerContext } from "@/providers/server-provider"
import { useState, useEffect } from "react"

interface MetricHistory {
  time: string
  value: number
}

interface ResourceMetrics {
  cpu?: {
    current: number
    trend: number
    history: MetricHistory[]
    loadavg?: number[]
  }
  memory?: {
    current: number
    trend: number
    history: MetricHistory[]
    details?: {
      total: number
      free: number
      used: number
      cached: number
      buffers: number
      swap: {
        total: number
        free: number
        used: number
      }
    }
  }
  network?: {
    in: {
      current: number
      peak: number
      total: number
      trend: number
      history: MetricHistory[]
    }
    out: {
      current: number
      peak: number
      total: number
      trend: number
      history: MetricHistory[]
    }
    combined?: {
      history: MetricHistory[]
    }
    connections?: number
  }
  disk?: {
    read: {
      current: number
      peak: number
      total: number
      trend: number
      history: MetricHistory[]
    }
    write: {
      current: number
      peak: number
      total: number
      trend: number
      history: MetricHistory[]
    }
    usage: {
      total: number
      used: number
      free: number
      percent: number
    }
  }
}

async function fetchResourceMetrics(serverInfo: any, timeRange: string, nodeId: string): Promise<ResourceMetrics> {
  if (!serverInfo) return {}

  try {
    console.log("Fetching resource metrics with server info:", {
      url: serverInfo.url,
      hasTicket: !!serverInfo.ticket,
      timeRange,
      nodeId
    })
    
    // This would normally make an actual API call
    // For now, we'll generate mock data instead

    // Generate mock data based on the time range
    const dataPoints = 
      timeRange === "hour" ? 60 : 
      timeRange === "day" ? 24 : 
      timeRange === "week" ? 7 : 30
    
    const history: MetricHistory[] = generateMockHistory(dataPoints)
    const memHistory: MetricHistory[] = generateMockHistory(dataPoints, 60, 95)
    const netInHistory: MetricHistory[] = generateMockHistory(dataPoints, 0, 12)
    const netOutHistory: MetricHistory[] = generateMockHistory(dataPoints, 0, 8)
    
    const combinedNetHistory: MetricHistory[] = netInHistory.map((item, index) => ({
      time: item.time,
      value: item.value + netOutHistory[index].value
    }))

    // Mock CPU metrics
    const cpuCurrent = Math.round(history[history.length - 1].value * 10) / 10
    const cpuPrevious = history[history.length - 2].value
    const cpuTrend = Math.round((cpuCurrent - cpuPrevious) * 10) / 10
    
    // Mock memory metrics
    const memCurrent = Math.round(memHistory[memHistory.length - 1].value * 10) / 10
    const memPrevious = memHistory[memHistory.length - 2].value
    const memTrend = Math.round((memCurrent - memPrevious) * 10) / 10
    
    // Mock network metrics
    const netInCurrent = Math.round(netInHistory[netInHistory.length - 1].value * 10) / 10
    const netInPrevious = netInHistory[netInHistory.length - 2].value
    const netInTrend = Math.round((netInCurrent - netInPrevious) * 10) / 10
    
    const netOutCurrent = Math.round(netOutHistory[netOutHistory.length - 1].value * 10) / 10
    const netOutPrevious = netOutHistory[netOutHistory.length - 2].value
    const netOutTrend = Math.round((netOutCurrent - netOutPrevious) * 10) / 10

    // Return a mock response
    return {
      cpu: {
        current: cpuCurrent,
        trend: cpuTrend,
        history,
        loadavg: [Math.random() * 4, Math.random() * 3, Math.random() * 2]
      },
      memory: {
        current: memCurrent,
        trend: memTrend,
        history: memHistory,
        details: {
          total: 32,
          free: 32 * (1 - memCurrent / 100),
          used: 32 * (memCurrent / 100),
          cached: 3.2,
          buffers: 1.8,
          swap: {
            total: 16,
            free: 15.2,
            used: 0.8
          }
        }
      },
      network: {
        in: {
          current: netInCurrent,
          peak: Math.max(...netInHistory.map(h => h.value)),
          total: 1200,
          trend: netInTrend,
          history: netInHistory
        },
        out: {
          current: netOutCurrent,
          peak: Math.max(...netOutHistory.map(h => h.value)),
          total: 840,
          trend: netOutTrend,
          history: netOutHistory
        },
        combined: {
          history: combinedNetHistory
        },
        connections: Math.floor(Math.random() * 200) + 50
      },
      disk: {
        read: {
          current: 2.3,
          peak: 15.7,
          total: 450,
          trend: -0.2,
          history: generateMockHistory(dataPoints, 0, 8)
        },
        write: {
          current: 1.8,
          peak: 12.4,
          total: 380,
          trend: 0.3,
          history: generateMockHistory(dataPoints, 0, 6)
        },
        usage: {
          total: 500,
          used: 325,
          free: 175,
          percent: 65
        }
      }
    }
  } catch (error) {
    console.error("Failed to fetch resource metrics:", error)
    return {}
  }
}

function generateMockHistory(count: number, min = 10, max = 85): MetricHistory[] {
  const history: MetricHistory[] = []
  let lastValue = Math.random() * (max - min) + min
  
  for (let i = 0; i < count; i++) {
    // Create a somewhat realistic fluctuation
    const change = (Math.random() - 0.5) * 10
    lastValue = Math.max(min, Math.min(max, lastValue + change))
    
    const date = new Date()
    date.setMinutes(date.getMinutes() - (count - i))
    
    history.push({
      time: date.toISOString(),
      value: lastValue
    })
  }
  
  return history
}

export function useResourceMetrics(timeRange = 'hour', nodeId = 'all') {
  const { serverInfo } = useServerContext()
  const [metrics, setMetrics] = useState<ResourceMetrics>({})
  
  const { data, isLoading, error } = useQuery({
    queryKey: ["resourceMetrics", serverInfo?.url, timeRange, nodeId],
    queryFn: () => fetchResourceMetrics(serverInfo, timeRange, nodeId),
    enabled: !!serverInfo?.ticket,
    refetchInterval: 15000, // Refetch every 15 seconds
    retry: 2,
    retryDelay: 1000,
  })
  
  useEffect(() => {
    if (data) {
      setMetrics(data)
    }
  }, [data])

  return {
    metrics,
    isLoading,
    error,
  }
} 