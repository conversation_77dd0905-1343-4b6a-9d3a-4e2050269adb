"use client"

import { useState, useEffect, useCallback } from "react"
import { toast } from "@/hooks/use-toast"
import { getServerById } from "@/lib/server-storage"

export function useContainerActions(serverId?: string, ctId?: string) {
  const [containerStatus, setContainerStatus] = useState<string | null>(null)
  const [containerDetails, setContainerDetails] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch container status if serverId and ctId are provided
  useEffect(() => {
    if (!serverId || !ctId) return

    let isMounted = true
    const server = getServerById(serverId)
    if (!server) return

    async function fetchContainerStatus() {
      try {
        setError(null)

        const response = await fetch(`/api/proxmox/containers/${ctId}/status`)

        if (!response.ok) {
          throw new Error("Failed to fetch container status")
        }

        const data = await response.json()

        if (isMounted) {
          setContainerStatus(data.data.status)
          setContainerDetails(data.data)
        }
      } catch (err) {
        console.error("Error fetching container status:", err)
        if (isMounted) {
          setError("Failed to fetch container status")
        }
      }
    }

    fetchContainerStatus()

    // Set up polling for updates
    const intervalId = setInterval(fetchContainerStatus, 5000) // Update every 5 seconds

    return () => {
      isMounted = false
      clearInterval(intervalId)
    }
  }, [serverId, ctId])

  const executeContainerAction = useCallback(
    async (actionServerId: string, actionCtId: string, action: string, successMessage: string) => {
      setIsLoading(true)
      setError(null)

      try {
        const server = getServerById(actionServerId)
        if (!server) {
          throw new Error("Server not found")
        }

        const response = await fetch(`/api/proxmox/${actionServerId}/containers/${actionCtId}/status`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ action }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || `Failed to ${action} container`)
        }

        toast({
          title: "Success",
          description: successMessage,
        })

        // Update container status after action
        if (actionServerId === serverId && actionCtId === ctId) {
          // Set temporary status based on action
          if (action === "start") setContainerStatus("starting")
          else if (action === "stop") setContainerStatus("stopping")
          else if (action === "restart") setContainerStatus("restarting")
        }
      } catch (err) {
        console.error(`Error executing ${action}:`, err)
        const errorMessage = err instanceof Error ? err.message : `Failed to ${action} container`
        setError(errorMessage)
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    },
    [serverId, ctId],
  )

  const startContainer = useCallback(
    (actionServerId: string, actionCtId: string) => {
      return executeContainerAction(actionServerId, actionCtId, "start", "Container is starting")
    },
    [executeContainerAction],
  )

  const stopContainer = useCallback(
    (actionServerId: string, actionCtId: string) => {
      return executeContainerAction(actionServerId, actionCtId, "stop", "Container is stopping")
    },
    [executeContainerAction],
  )

  const restartContainer = useCallback(
    (actionServerId: string, actionCtId: string) => {
      return executeContainerAction(actionServerId, actionCtId, "restart", "Container is restarting")
    },
    [executeContainerAction],
  )

  return {
    startContainer,
    stopContainer,
    restartContainer,
    containerStatus,
    containerDetails,
    isLoading,
    error,
  }
}
