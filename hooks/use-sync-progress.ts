/**
 * React Hook for Sync Progress Monitoring
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { useWebSocket } from './use-websocket'

export interface SyncProgress {
  sessionId: string
  phase: 'initializing' | 'scanning' | 'comparing' | 'syncing' | 'resolving_conflicts' | 'finalizing' | 'completed' | 'failed'
  progress: number // 0.0 to 1.0
  currentFile?: string
  filesProcessed: number
  totalFiles: number
  bytesTransferred: number
  totalBytes: number
  estimatedTimeRemaining?: number
  errors: any[]
}

export interface SyncSession {
  id: string
  projectId: string
  sessionType: 'full_sync' | 'incremental' | 'conflict_resolution' | 'manual'
  trigger: 'user_action' | 'auto_sync' | 'file_watcher' | 'api_call'
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  startedAt: Date
  completedAt?: Date
  progress: number
  statistics: {
    filesScanned: number
    filesCreated: number
    filesUpdated: number
    filesDeleted: number
    conflictsFound: number
    conflictsResolved: number
    bytesTransferred: number
    duration?: number
  }
  errorMessage?: string
  metadata?: Record<string, any>
}

export interface SyncEvent {
  type: 'sync-start' | 'sync-progress' | 'sync-complete' | 'sync-error' | 'file-change' | 'conflict-detected' | 'conflict-resolved'
  projectId: string
  data: any
  timestamp: string
}

export function useSyncProgress(projectId: string) {
  const [currentSession, setCurrentSession] = useState<SyncSession | null>(null)
  const [progress, setProgress] = useState<SyncProgress | null>(null)
  const [recentSessions, setRecentSessions] = useState<SyncSession[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [events, setEvents] = useState<SyncEvent[]>([])

  // Track active sync operations
  const activeSyncRef = useRef<string | null>(null)
  const startTimeRef = useRef<Date | null>(null)

  // WebSocket connection for real-time updates
  const { isConnected, lastMessage, sendMessage, connect } = useWebSocket(
    `ws://localhost:3001/api/file-sync/ws`
  )

  // Subscribe to project events
  useEffect(() => {
    if (isConnected && projectId) {
      sendMessage({
        type: 'subscribe',
        projectId
      })
    }
  }, [isConnected, projectId, sendMessage])

  // Handle WebSocket messages
  useEffect(() => {
    if (lastMessage) {
      try {
        const event: SyncEvent = JSON.parse(lastMessage.data)
        
        // Only process events for our project
        if (event.projectId !== projectId) return

        // Add to events list
        setEvents(prev => [event, ...prev.slice(0, 99)]) // Keep last 100 events

        switch (event.type) {
          case 'sync-start':
            handleSyncStart(event.data)
            break
          
          case 'sync-progress':
            handleSyncProgress(event.data)
            break
          
          case 'sync-complete':
            handleSyncComplete(event.data)
            break
          
          case 'sync-error':
            handleSyncError(event.data)
            break
          
          case 'file-change':
            handleFileChange(event.data)
            break
          
          case 'conflict-detected':
            handleConflictDetected(event.data)
            break
          
          case 'conflict-resolved':
            handleConflictResolved(event.data)
            break
        }
      } catch (err) {
        console.error('[useSyncProgress] Error parsing WebSocket message:', err)
      }
    }
  }, [lastMessage, projectId])

  /**
   * Handle sync start event
   */
  const handleSyncStart = useCallback((session: SyncSession) => {
    setCurrentSession(session)
    setProgress({
      sessionId: session.id,
      phase: 'initializing',
      progress: 0,
      filesProcessed: 0,
      totalFiles: 0,
      bytesTransferred: 0,
      totalBytes: 0,
      errors: []
    })
    activeSyncRef.current = session.id
    startTimeRef.current = new Date()
    setError(null)
  }, [])

  /**
   * Handle sync progress event
   */
  const handleSyncProgress = useCallback((progressData: SyncProgress) => {
    setProgress(prev => {
      if (!prev || prev.sessionId !== progressData.sessionId) {
        return progressData
      }

      // Calculate estimated time remaining
      let estimatedTimeRemaining: number | undefined
      if (startTimeRef.current && progressData.progress > 0) {
        const elapsed = Date.now() - startTimeRef.current.getTime()
        const totalEstimated = elapsed / progressData.progress
        estimatedTimeRemaining = totalEstimated - elapsed
      }

      return {
        ...progressData,
        estimatedTimeRemaining
      }
    })

    // Update current session progress
    setCurrentSession(prev => {
      if (!prev || prev.id !== progressData.sessionId) return prev
      return {
        ...prev,
        progress: progressData.progress
      }
    })
  }, [])

  /**
   * Handle sync complete event
   */
  const handleSyncComplete = useCallback((result: any) => {
    setCurrentSession(prev => {
      if (!prev) return null
      
      const completedSession: SyncSession = {
        ...prev,
        status: 'completed',
        completedAt: new Date(),
        progress: 1.0,
        statistics: {
          ...prev.statistics,
          ...result,
          duration: startTimeRef.current ? Date.now() - startTimeRef.current.getTime() : undefined
        }
      }

      // Add to recent sessions
      setRecentSessions(prevSessions => [completedSession, ...prevSessions.slice(0, 9)])
      
      return completedSession
    })

    setProgress(prev => {
      if (!prev) return null
      return {
        ...prev,
        phase: 'completed',
        progress: 1.0
      }
    })

    activeSyncRef.current = null
    startTimeRef.current = null
  }, [])

  /**
   * Handle sync error event
   */
  const handleSyncError = useCallback((errorData: any) => {
    setError(errorData.message || 'Sync operation failed')
    
    setCurrentSession(prev => {
      if (!prev) return null
      
      const failedSession: SyncSession = {
        ...prev,
        status: 'failed',
        completedAt: new Date(),
        errorMessage: errorData.message
      }

      // Add to recent sessions
      setRecentSessions(prevSessions => [failedSession, ...prevSessions.slice(0, 9)])
      
      return failedSession
    })

    setProgress(prev => {
      if (!prev) return null
      return {
        ...prev,
        phase: 'failed',
        errors: [...prev.errors, errorData]
      }
    })

    activeSyncRef.current = null
    startTimeRef.current = null
  }, [])

  /**
   * Handle file change event
   */
  const handleFileChange = useCallback((fileChangeData: any) => {
    // Update progress if sync is active
    if (activeSyncRef.current && progress) {
      setProgress(prev => {
        if (!prev) return null
        return {
          ...prev,
          currentFile: fileChangeData.path,
          filesProcessed: prev.filesProcessed + 1
        }
      })
    }
  }, [progress])

  /**
   * Handle conflict detected event
   */
  const handleConflictDetected = useCallback((conflictData: any) => {
    setProgress(prev => {
      if (!prev) return null
      return {
        ...prev,
        phase: 'resolving_conflicts'
      }
    })
  }, [])

  /**
   * Handle conflict resolved event
   */
  const handleConflictResolved = useCallback((conflictData: any) => {
    // Update session statistics
    setCurrentSession(prev => {
      if (!prev) return null
      return {
        ...prev,
        statistics: {
          ...prev.statistics,
          conflictsResolved: prev.statistics.conflictsResolved + 1
        }
      }
    })
  }, [])

  /**
   * Fetch recent sync sessions
   */
  const fetchRecentSessions = useCallback(async (limit: number = 10) => {
    if (!projectId) return

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/file-sync?projectId=${projectId}&action=sessions&limit=${limit}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch sync sessions')
      }

      setRecentSessions(data.sessions || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [projectId])

  /**
   * Get sync statistics
   */
  const getSyncStatistics = useCallback(() => {
    if (!recentSessions.length) return null

    const completedSessions = recentSessions.filter(s => s.status === 'completed')
    const failedSessions = recentSessions.filter(s => s.status === 'failed')

    const totalFiles = completedSessions.reduce((sum, s) => sum + s.statistics.filesScanned, 0)
    const totalBytes = completedSessions.reduce((sum, s) => sum + s.statistics.bytesTransferred, 0)
    const totalDuration = completedSessions.reduce((sum, s) => sum + (s.statistics.duration || 0), 0)

    return {
      totalSessions: recentSessions.length,
      completedSessions: completedSessions.length,
      failedSessions: failedSessions.length,
      successRate: recentSessions.length > 0 ? completedSessions.length / recentSessions.length : 0,
      totalFiles,
      totalBytes,
      averageDuration: completedSessions.length > 0 ? totalDuration / completedSessions.length : 0,
      lastSyncTime: recentSessions[0]?.completedAt
    }
  }, [recentSessions])

  /**
   * Check if sync is currently active
   */
  const isSyncActive = useCallback(() => {
    return activeSyncRef.current !== null && currentSession?.status === 'running'
  }, [currentSession])

  /**
   * Get formatted progress text
   */
  const getProgressText = useCallback(() => {
    if (!progress) return 'No sync in progress'

    const percentage = Math.round(progress.progress * 100)
    const phase = progress.phase.replace('_', ' ').toUpperCase()

    if (progress.currentFile) {
      return `${phase}: ${progress.currentFile} (${percentage}%)`
    }

    if (progress.filesProcessed > 0 && progress.totalFiles > 0) {
      return `${phase}: ${progress.filesProcessed}/${progress.totalFiles} files (${percentage}%)`
    }

    return `${phase}: ${percentage}%`
  }, [progress])

  /**
   * Get estimated time remaining text
   */
  const getTimeRemainingText = useCallback(() => {
    if (!progress?.estimatedTimeRemaining) return null

    const seconds = Math.round(progress.estimatedTimeRemaining / 1000)
    if (seconds < 60) return `${seconds}s remaining`
    
    const minutes = Math.round(seconds / 60)
    if (minutes < 60) return `${minutes}m remaining`
    
    const hours = Math.round(minutes / 60)
    return `${hours}h remaining`
  }, [progress])

  // Initialize on mount
  useEffect(() => {
    if (projectId) {
      fetchRecentSessions()
      connect()
    }
  }, [projectId, fetchRecentSessions, connect])

  return {
    // State
    currentSession,
    progress,
    recentSessions,
    events,
    isLoading,
    error,
    isConnected,

    // Actions
    fetchRecentSessions,

    // Utilities
    getSyncStatistics,
    isSyncActive,
    getProgressText,
    getTimeRemainingText,
    clearError: () => setError(null),
    clearEvents: () => setEvents([]),

    // Computed values
    hasActiveSync: isSyncActive(),
    progressPercentage: progress ? Math.round(progress.progress * 100) : 0,
    statistics: getSyncStatistics()
  }
}
