/**
 * Virtual Machines Hook
 * 
 * This hook provides functionality for managing virtual machines.
 */

import { useState, useEffect } from 'react';
import { toast } from '@/components/ui/use-toast';

export interface VirtualMachine {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'error';
  type: 'microvm' | 'lxc' | 'lxd';
  createdAt: string;
  updatedAt: string;
  ipAddress?: string;
  cpuCount: number;
  memoryMb: number;
  diskSizeGb: number;
}

export function useVirtualMachines() {
  const [vms, setVms] = useState<VirtualMachine[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchVirtualMachines = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch MicroVMs
      const microVmResponse = await fetch('/api/containerization/microvm');
      
      if (!microVmResponse.ok) {
        throw new Error('Failed to fetch MicroVMs');
      }
      
      const microVmData = await microVmResponse.json();
      
      // Transform MicroVM data to VirtualMachine format
      const microVms: VirtualMachine[] = (microVmData.microvms || []).map((vm: any) => ({
        id: vm.id,
        name: vm.name,
        status: vm.state === 'RUNNING' ? 'running' : vm.state === 'STOPPED' ? 'stopped' : 'error',
        type: 'microvm',
        createdAt: vm.createdAt || new Date().toISOString(),
        updatedAt: vm.updatedAt || new Date().toISOString(),
        ipAddress: vm.ipAddress,
        cpuCount: vm.vcpuCount || 2,
        memoryMb: vm.memSizeMib || 1024,
        diskSizeGb: 10, // Default value
      }));
      
      setVms(microVms);
    } catch (err) {
      console.error('Error fetching virtual machines:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      toast({
        title: 'Error',
        description: 'Failed to fetch virtual machines',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getVirtualMachine = async (id: string) => {
    try {
      const response = await fetch(`/api/containerization/microvm/${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch virtual machine');
      }
      
      const data = await response.json();
      
      return {
        id: data.id,
        name: data.name,
        status: data.state === 'RUNNING' ? 'running' : data.state === 'STOPPED' ? 'stopped' : 'error',
        type: 'microvm',
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt || new Date().toISOString(),
        ipAddress: data.ipAddress,
        cpuCount: data.vcpuCount || 2,
        memoryMb: data.memSizeMib || 1024,
        diskSizeGb: 10, // Default value
      } as VirtualMachine;
    } catch (err) {
      console.error(`Error fetching virtual machine ${id}:`, err);
      toast({
        title: 'Error',
        description: 'Failed to fetch virtual machine',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const startVirtualMachine = async (id: string) => {
    try {
      const response = await fetch(`/api/containerization/microvm/${id}/start`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error('Failed to start virtual machine');
      }
      
      toast({
        title: 'Success',
        description: 'Virtual machine started successfully',
      });
      
      await fetchVirtualMachines();
    } catch (err) {
      console.error(`Error starting virtual machine ${id}:`, err);
      toast({
        title: 'Error',
        description: 'Failed to start virtual machine',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const stopVirtualMachine = async (id: string) => {
    try {
      const response = await fetch(`/api/containerization/microvm/${id}/stop`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error('Failed to stop virtual machine');
      }
      
      toast({
        title: 'Success',
        description: 'Virtual machine stopped successfully',
      });
      
      await fetchVirtualMachines();
    } catch (err) {
      console.error(`Error stopping virtual machine ${id}:`, err);
      toast({
        title: 'Error',
        description: 'Failed to stop virtual machine',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const restartVirtualMachine = async (id: string) => {
    try {
      const response = await fetch(`/api/containerization/microvm/${id}/restart`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error('Failed to restart virtual machine');
      }
      
      toast({
        title: 'Success',
        description: 'Virtual machine restarted successfully',
      });
      
      await fetchVirtualMachines();
    } catch (err) {
      console.error(`Error restarting virtual machine ${id}:`, err);
      toast({
        title: 'Error',
        description: 'Failed to restart virtual machine',
        variant: 'destructive',
      });
      throw err;
    }
  };

  const deleteVirtualMachine = async (id: string) => {
    try {
      const response = await fetch(`/api/containerization/microvm/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete virtual machine');
      }
      
      toast({
        title: 'Success',
        description: 'Virtual machine deleted successfully',
      });
      
      await fetchVirtualMachines();
    } catch (err) {
      console.error(`Error deleting virtual machine ${id}:`, err);
      toast({
        title: 'Error',
        description: 'Failed to delete virtual machine',
        variant: 'destructive',
      });
      throw err;
    }
  };

  useEffect(() => {
    fetchVirtualMachines();
  }, []);

  return {
    vms,
    loading,
    error,
    fetchVirtualMachines,
    getVirtualMachine,
    startVirtualMachine,
    stopVirtualMachine,
    restartVirtualMachine,
    deleteVirtualMachine,
  };
}
