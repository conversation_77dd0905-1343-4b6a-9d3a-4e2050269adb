/**
 * Research Templates React Hook
 * 
 * Provides React hooks for managing research templates
 */

import { useCallback } from 'react';
import { useSession } from 'next-auth/react';
import useSWR from 'swr';

// Types
interface ResearchTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  options: {
    sources: {
      web?: {
        enabled: boolean;
        maxResults?: number;
        domains?: string[];
        excludeDomains?: string[];
      };
      academic?: {
        enabled: boolean;
        maxResults?: number;
        databases?: ('arxiv' | 'pubmed' | 'scholar')[];
        dateRange?: {
          from: string;
          to: string;
        };
      };
      news?: {
        enabled: boolean;
        maxResults?: number;
        sources?: string[];
        languages?: string[];
      };
      social?: {
        enabled: boolean;
        platforms?: string[];
        maxResults?: number;
      };
    };
    analysis?: {
      summarize?: boolean;
      extractEntities?: boolean;
      detectBias?: boolean;
      checkCredibility?: boolean;
      findRelationships?: boolean;
    };
  };
  settings: {
    autoAnalysis: boolean;
    sourceTypes: string[];
    analysisTypes: string[];
    maxSources: number;
    qualityThreshold: number;
    biasDetection: boolean;
    factChecking: boolean;
    realTimeUpdates?: boolean;
  };
}

// API helper functions
const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error('Failed to fetch');
  }
  return response.json();
};

/**
 * Hook for managing research templates
 */
export function useResearchTemplates(category?: string) {
  const { data: session } = useSession();
  
  const url = session 
    ? `/api/deep-research/templates${category ? `?category=${encodeURIComponent(category)}` : ''}`
    : null;
    
  const { data, error, isLoading } = useSWR(url, fetcher);

  const getTemplateById = useCallback((templateId: string): ResearchTemplate | undefined => {
    return data?.templates?.find((template: ResearchTemplate) => template.id === templateId);
  }, [data]);

  const getTemplatesByCategory = useCallback((categoryName: string): ResearchTemplate[] => {
    return data?.templates?.filter((template: ResearchTemplate) => 
      template.category.toLowerCase() === categoryName.toLowerCase()
    ) || [];
  }, [data]);

  const createProjectFromTemplate = useCallback(async (
    templateId: string,
    projectTitle: string,
    projectDescription?: string
  ) => {
    const template = getTemplateById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Create project with template settings
    const response = await fetch('/api/deep-research/projects', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: projectTitle,
        description: projectDescription || template.description,
        settings: template.settings
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create project from template');
    }

    return response.json();
  }, [getTemplateById]);

  const startResearchFromTemplate = useCallback(async (
    templateId: string,
    query: string,
    projectId?: string,
    customizations?: Partial<ResearchTemplate['options']>
  ) => {
    const template = getTemplateById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Merge template options with customizations
    const options = {
      ...template.options,
      ...customizations,
      sources: {
        ...template.options.sources,
        ...customizations?.sources
      },
      analysis: {
        ...template.options.analysis,
        ...customizations?.analysis
      }
    };

    // Start research session
    const response = await fetch('/api/deep-research/research', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query,
        projectId,
        options
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to start research from template');
    }

    return response.json();
  }, [getTemplateById]);

  return {
    templates: data?.templates || [],
    categories: data?.categories || [],
    isLoading,
    error,
    getTemplateById,
    getTemplatesByCategory,
    createProjectFromTemplate,
    startResearchFromTemplate
  };
}

/**
 * Hook for template recommendations based on query
 */
export function useTemplateRecommendations(query: string) {
  const { templates } = useResearchTemplates();

  const getRecommendations = useCallback((maxRecommendations = 3): ResearchTemplate[] => {
    if (!query || !templates.length) return [];

    const queryLower = query.toLowerCase();
    const scored = templates.map((template: ResearchTemplate) => {
      let score = 0;
      
      // Score based on category keywords
      const categoryKeywords: Record<string, string[]> = {
        'Academic': ['research', 'study', 'analysis', 'literature', 'paper', 'academic', 'scientific'],
        'Business': ['market', 'business', 'company', 'industry', 'financial', 'economic', 'revenue'],
        'Technology': ['tech', 'software', 'ai', 'machine learning', 'programming', 'computer', 'digital'],
        'Health': ['health', 'medical', 'disease', 'treatment', 'medicine', 'clinical', 'patient'],
        'General': ['overview', 'summary', 'general', 'basic', 'introduction'],
        'Research': ['comprehensive', 'detailed', 'thorough', 'complete', 'extensive']
      };

      const keywords = categoryKeywords[template.category] || [];
      keywords.forEach(keyword => {
        if (queryLower.includes(keyword)) {
          score += 2;
        }
      });

      // Score based on template name and description
      if (template.name.toLowerCase().includes(queryLower)) {
        score += 3;
      }
      
      if (template.description.toLowerCase().includes(queryLower)) {
        score += 1;
      }

      // Boost score for certain patterns
      if (queryLower.includes('quick') || queryLower.includes('fast')) {
        if (template.id === 'quick-overview') score += 5;
      }
      
      if (queryLower.includes('comprehensive') || queryLower.includes('detailed')) {
        if (template.id === 'comprehensive-analysis') score += 5;
      }

      if (queryLower.includes('academic') || queryLower.includes('literature')) {
        if (template.id === 'academic-literature-review') score += 5;
      }

      return { template, score };
    });

    return scored
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, maxRecommendations)
      .map(item => item.template);
  }, [query, templates]);

  return {
    recommendations: getRecommendations(),
    getRecommendations
  };
}

/**
 * Hook for customizing template options
 */
export function useTemplateCustomization() {
  const customizeTemplate = useCallback((
    template: ResearchTemplate,
    customizations: {
      sources?: Partial<ResearchTemplate['options']['sources']>;
      analysis?: Partial<ResearchTemplate['options']['analysis']>;
      settings?: Partial<ResearchTemplate['settings']>;
    }
  ): ResearchTemplate => {
    return {
      ...template,
      options: {
        ...template.options,
        sources: {
          ...template.options.sources,
          ...customizations.sources
        },
        analysis: {
          ...template.options.analysis,
          ...customizations.analysis
        }
      },
      settings: {
        ...template.settings,
        ...customizations.settings
      }
    };
  }, []);

  const validateTemplate = useCallback((template: ResearchTemplate): {
    isValid: boolean;
    errors: string[];
  } => {
    const errors: string[] = [];

    // Check if at least one source is enabled
    const sourcesEnabled = Object.values(template.options.sources).some(source => source?.enabled);
    if (!sourcesEnabled) {
      errors.push('At least one source type must be enabled');
    }

    // Check if at least one analysis type is enabled
    const analysisEnabled = Object.values(template.options.analysis || {}).some(analysis => analysis);
    if (!analysisEnabled) {
      errors.push('At least one analysis type must be enabled');
    }

    // Check max sources limit
    if (template.settings.maxSources < 1 || template.settings.maxSources > 100) {
      errors.push('Max sources must be between 1 and 100');
    }

    // Check quality threshold
    if (template.settings.qualityThreshold < 0 || template.settings.qualityThreshold > 1) {
      errors.push('Quality threshold must be between 0 and 1');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, []);

  return {
    customizeTemplate,
    validateTemplate
  };
}
