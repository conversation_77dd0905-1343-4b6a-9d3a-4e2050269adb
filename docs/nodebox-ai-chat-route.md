# Nodebox AI Chat Route Documentation

This document explains the new `/api/nodebox-ai` route that provides comprehensive AI chat capabilities with Nodebox tool integration for development workflows.

## Overview

The Nodebox AI chat route is a specialized API endpoint that enables AI agents to directly interact with Nodebox development environments through natural language conversations. It provides server-side tool execution for file operations, project management, and development commands.

## API Endpoint

```
POST /api/nodebox-ai
```

### Request Format

```typescript
{
  "messages": Message[], // Chat messages in AI SDK format
  "projectId": string    // Optional project identifier
}
```

### Response Format

The endpoint returns a streaming response compatible with the AI SDK's `streamText` function, providing real-time tool execution results and AI responses.

## Available Tools

### File Operations

#### `read_file_nodebox`
Read the contents of a file from the active Nodebox instance.

**Parameters:**
- `path` (string): The file path to read (e.g., "/src/App.tsx", "/package.json")

**Example:**
```typescript
// AI prompt: "Read the package.json file"
// Tool call: read_file_nodebox({ path: "/package.json" })
// Result: { path: "/package.json", content: "...", success: true }
```

#### `write_file_nodebox`
Write content to a file in the active Nodebox instance (creates or updates).

**Parameters:**
- `path` (string): The file path to write to
- `content` (string): The content to write to the file

**Example:**
```typescript
// AI prompt: "Create a React component called Button"
// Tool call: write_file_nodebox({ 
//   path: "/src/components/Button.tsx", 
//   content: "import React from 'react'..." 
// })
// Result: { path: "/src/components/Button.tsx", bytesWritten: 245, success: true }
```

#### `create_file_nodebox`
Create a new file in the active Nodebox instance.

**Parameters:**
- `path` (string): The file path to create
- `content` (string, optional): The initial content for the file

#### `create_directory_nodebox`
Create a new directory in the active Nodebox instance.

**Parameters:**
- `path` (string): The directory path to create

#### `delete_file_nodebox`
Delete a file from the active Nodebox instance.

**Parameters:**
- `path` (string): The file path to delete

#### `list_files_nodebox`
List all files and directories in the active Nodebox instance.

**Parameters:**
- `path` (string, optional): The directory path to list (defaults to root "/")

**Example:**
```typescript
// AI prompt: "Show me all files in the project"
// Tool call: list_files_nodebox({ path: "/" })
// Result: { 
//   path: "/", 
//   files: [
//     { name: "package.json", path: "/package.json", type: "file", size: 1024 },
//     { name: "src", path: "/src", type: "directory" }
//   ], 
//   count: 2, 
//   success: true 
// }
```

### Project Management

#### `create_project_nodebox`
Create a new Nodebox project from a template.

**Parameters:**
- `name` (string): The name for the new project
- `template` (enum): The project template to use
- `projectId` (string, optional): Optional project ID for organization

**Available Templates:**
- `react` - Modern React application with TypeScript
- `nextjs` - Full-stack Next.js application
- `express` - Node.js Express server
- `vanilla-js` - Simple HTML/CSS/JavaScript
- `typescript` - Pure TypeScript project
- `research-dashboard` - Data visualization dashboard
- `data-analysis` - Data processing and analysis tools
- `custom` - Flexible custom project setup

**Example:**
```typescript
// AI prompt: "Create a new React project called 'my-dashboard'"
// Tool call: create_project_nodebox({ 
//   name: "my-dashboard", 
//   template: "react",
//   projectId: "user-123-project" 
// })
// Result: { 
//   name: "my-dashboard", 
//   template: "react", 
//   instanceId: "instance_123", 
//   projectId: "user-123-project",
//   created: true, 
//   success: true 
// }
```

#### `get_project_info_nodebox`
Get detailed information about the active Nodebox project.

**Parameters:** None

**Example:**
```typescript
// AI prompt: "Tell me about this project"
// Tool call: get_project_info_nodebox({})
// Result: {
//   instance: {
//     id: "instance_123",
//     name: "my-dashboard",
//     template: "react",
//     status: "ready",
//     createdAt: "2024-01-01T00:00:00Z"
//   },
//   fileCount: 15,
//   processCount: 2,
//   previewCount: 1,
//   success: true
// }
```

### Command Execution

#### `run_command_nodebox`
Run a command in the active Nodebox instance terminal.

**Parameters:**
- `command` (string): The command to run (e.g., "npm", "node", "tsc")
- `args` (string[], optional): Command arguments

**Example:**
```typescript
// AI prompt: "Install lodash as a dependency"
// Tool call: run_command_nodebox({ 
//   command: "npm", 
//   args: ["install", "lodash"] 
// })
// Result: { 
//   command: "npm", 
//   args: ["install", "lodash"], 
//   processId: "proc_123", 
//   status: "completed", 
//   success: true 
// }
```

### Development Tools

#### `search_documentation`
Search through development documentation and help resources.

**Parameters:**
- `query` (string): The search query
- `category` (enum, optional): Documentation category

#### `generate_code_snippet`
Generate a code snippet based on specific requirements.

**Parameters:**
- `language` (string): Programming language
- `description` (string): Description of what the code should do
- `framework` (string, optional): Framework context
- `style` (enum, optional): Code style preference

#### `validate_code`
Validate code syntax and provide suggestions.

**Parameters:**
- `code` (string): The code to validate
- `language` (string): The programming language
- `strict` (boolean, optional): Enable strict validation

## Usage Examples

### Basic Integration

```typescript
import { AgenticChatInterface } from '@/components/agentic-chatbot/agentic-chat-interface';

<AgenticChatInterface
  projectId="my-project-123"
  apiEndpoint="/api/nodebox-ai"
  systemPrompt="You are an AI development assistant with Nodebox capabilities..."
  enableTools={true}
  maxSteps={15}
/>
```

### Complete Development Environment

```typescript
import { NodeboxAIChatExample } from '@/examples/nodebox-ai-chat-example';

<NodeboxAIChatExample projectId="my-project-123" />
```

## Conversation Examples

### Creating a React Component

**User:** "Create a React component called UserCard that displays user information with name, email, and avatar"

**AI Response:**
1. Uses `write_file_nodebox` to create `/src/components/UserCard.tsx`
2. Generates TypeScript React component with proper interfaces
3. Returns success confirmation with file details
4. Suggests next steps like styling or testing

### Setting Up a Project

**User:** "Create a new Next.js project and set up Tailwind CSS"

**AI Response:**
1. Uses `create_project_nodebox` with Next.js template
2. Uses `run_command_nodebox` to install Tailwind CSS
3. Uses `write_file_nodebox` to configure Tailwind config files
4. Uses `write_file_nodebox` to update CSS imports
5. Provides complete setup confirmation

### File Management

**User:** "Show me all TypeScript files in the project and read the main App component"

**AI Response:**
1. Uses `list_files_nodebox` to get all files
2. Filters and displays TypeScript files (.ts, .tsx)
3. Uses `read_file_nodebox` to show App.tsx content
4. Provides organized file structure overview

## Error Handling

The route includes comprehensive error handling:

### Tool Execution Errors
- **File not found**: Clear error message with suggestions
- **Permission errors**: Explanation of access requirements
- **Invalid arguments**: Correct parameter format examples
- **Network issues**: Retry suggestions and fallback options

### Project State Errors
- **No active project**: Prompts to create a project first
- **Invalid template**: Lists available templates
- **Command failures**: Detailed error output and troubleshooting

### Example Error Response
```typescript
{
  error: "File not found: /src/NonExistent.tsx",
  message: "The file you're trying to read doesn't exist. Use list_files_nodebox to see available files.",
  suggestions: [
    "Check the file path for typos",
    "Use list_files_nodebox to browse the project structure",
    "Create the file first with create_file_nodebox"
  ],
  success: false
}
```

## Performance Considerations

### Tool Execution
- **Concurrent operations**: Tools execute sequentially for consistency
- **Timeout handling**: 60-second maximum duration for streaming
- **Memory management**: Efficient file content handling
- **Caching**: Template configurations cached for performance

### Streaming Response
- **Real-time feedback**: Tool results stream as they complete
- **Progress indicators**: Step-by-step execution visibility
- **Error recovery**: Graceful handling of partial failures
- **Resource cleanup**: Automatic cleanup of failed operations

## Security Considerations

### Input Validation
- **Path sanitization**: Prevents directory traversal attacks
- **Content filtering**: Validates file content for safety
- **Command restrictions**: Whitelist of allowed commands
- **Size limits**: Maximum file size and content length limits

### Project Isolation
- **Sandboxed execution**: Each project runs in isolation
- **Resource limits**: CPU and memory constraints
- **Network restrictions**: Controlled external access
- **File system boundaries**: Restricted to project directories

## Integration with Frontend

### Agentic Chat Interface
The route integrates seamlessly with the enhanced agentic chat interface:

```typescript
// The interface automatically routes Nodebox tools to this endpoint
<AgenticChatInterface
  projectId="my-project"
  apiEndpoint="/api/nodebox-ai"  // Use the Nodebox AI route
  enableTools={true}
/>
```

### Real-time Synchronization
- **File changes**: Automatically sync with code editor
- **Project updates**: Refresh file browser and project status
- **Command output**: Stream to terminal interface
- **Error states**: Propagate across all components

## Best Practices

### Effective Prompting
1. **Be specific** about file paths and operations
2. **Provide context** about the project and goals
3. **Ask for explanations** when learning new concepts
4. **Request step-by-step** guidance for complex tasks

### Project Organization
1. **Use templates** for consistent project structure
2. **Create directories** before adding files
3. **Follow naming conventions** for better maintainability
4. **Document changes** with clear descriptions

### Performance Optimization
1. **Batch operations** when possible
2. **Use specific paths** instead of broad searches
3. **Limit large file operations** to prevent timeouts
4. **Monitor tool usage** to avoid rate limits

This route provides a powerful foundation for AI-driven development workflows, enabling natural language interaction with complex development environments.
