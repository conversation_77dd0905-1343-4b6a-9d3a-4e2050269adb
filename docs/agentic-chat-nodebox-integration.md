# Agentic Chat + Nodebox Integration Guide

This guide explains how to use the enhanced agentic chat interface with Nodebox tool calling capabilities for seamless AI-powered development workflows.

## Overview

The enhanced agentic chat interface now supports direct integration with Nodebox development environments, allowing AI agents to:

- **Read and write files** in real-time
- **Create and manage projects** from templates
- **Execute terminal commands** and monitor processes
- **Navigate file systems** and manage directory structures
- **Provide instant feedback** with rich tool call visualization

## Quick Start

### Basic Integration

```tsx
import { AgenticChatInterface } from '@/components/agentic-chatbot/agentic-chat-interface';

<AgenticChatInterface
  projectId="my-project-123"
  apiEndpoint="/api/chat"
  systemPrompt="You are an AI development assistant with Nodebox capabilities..."
  enableTools={true}
  maxSteps={10}
/>
```

### Complete Development Environment

```tsx
import { EnhancedAgenticChatWithNodebox } from '@/examples/enhanced-agentic-chat-with-nodebox';

<EnhancedAgenticChatWithNodebox projectId="my-project-123" />
```

## Available Nodebox Tools

The AI agent has access to 9 powerful Nodebox tools:

### File Operations

#### `read_file_nodebox`
Read the contents of any file in the project.

**Example prompts:**
- "Read the package.json file"
- "Show me the contents of /src/App.tsx"
- "What's in the README file?"

#### `write_file_nodebox`
Write content to existing or new files.

**Example prompts:**
- "Create a React component called Button in /src/components/Button.tsx"
- "Update the README with installation instructions"
- "Add a new utility function to /src/utils/helpers.ts"

#### `create_file_nodebox`
Create new files with optional initial content.

**Example prompts:**
- "Create an empty TypeScript file at /src/types/user.ts"
- "Make a new CSS file for the header component"

#### `delete_file_nodebox`
Remove files from the project.

**Example prompts:**
- "Delete the unused component at /src/components/Old.tsx"
- "Remove the test file that's no longer needed"

#### `list_files_nodebox`
List all files and directories in the project.

**Example prompts:**
- "Show me all files in the project"
- "List everything in the /src directory"
- "What files are in the components folder?"

### Directory Operations

#### `create_directory_nodebox`
Create new directories for organizing code.

**Example prompts:**
- "Create a hooks directory in /src"
- "Make a new folder for API utilities"

### Project Management

#### `create_project_nodebox`
Create new projects from built-in templates.

**Example prompts:**
- "Create a new React project called 'my-app'"
- "Set up a Next.js application with TypeScript"
- "Initialize an Express server project"

**Available templates:**
- `react` - Modern React with TypeScript
- `nextjs` - Full-stack Next.js application
- `express` - Node.js Express server
- `typescript` - Pure TypeScript project
- `vanilla-js` - Simple HTML/CSS/JS
- `research-dashboard` - Data visualization
- `data-analysis` - Data processing tools

#### `get_project_info_nodebox`
Get detailed information about the current project.

**Example prompts:**
- "Tell me about this project"
- "What's the current project status?"
- "Show me project details"

### Command Execution

#### `run_command_nodebox`
Execute terminal commands in the project environment.

**Example prompts:**
- "Install lodash as a dependency"
- "Run the development server"
- "Build the project for production"
- "Run the test suite"

## Enhanced Tool Call Visualization

The interface provides rich visualization for Nodebox tool calls:

### Visual Indicators
- **📦 Nodebox tools** are highlighted with special badges
- **🔧 Regular tools** use standard styling
- **Success/failure states** are clearly indicated with colors and icons

### Detailed Results Display
- **File operations** show paths, content previews, and byte counts
- **Command execution** displays command details and process IDs
- **Project creation** shows template information and instance details
- **File listings** present organized directory structures
- **Error messages** are clearly highlighted with troubleshooting info

## Example Conversations

### Creating a React Component

**User:** "Create a React component called UserCard in /src/components/UserCard.tsx that displays user information"

**AI Response:**
1. Uses `create_file_nodebox` tool
2. Writes TypeScript React component code
3. Shows success confirmation with file path
4. Provides code preview in tool result

### Setting Up a Project

**User:** "Create a new Next.js project and install Tailwind CSS"

**AI Response:**
1. Uses `create_project_nodebox` with Next.js template
2. Uses `run_command_nodebox` to install Tailwind
3. Uses `write_file_nodebox` to configure Tailwind
4. Shows complete setup status

### File Management

**User:** "Show me all TypeScript files in the project and read the main App component"

**AI Response:**
1. Uses `list_files_nodebox` to get all files
2. Filters and displays TypeScript files
3. Uses `read_file_nodebox` to show App.tsx content
4. Provides organized file structure view

## Error Handling

The system provides comprehensive error handling:

### Tool Call Errors
- **Network issues** are caught and reported
- **File not found** errors provide helpful suggestions
- **Permission errors** explain access requirements
- **Invalid arguments** show correct parameter formats

### Recovery Mechanisms
- **Automatic retries** for transient failures
- **Fallback suggestions** when tools fail
- **Clear error messages** with actionable advice
- **Graceful degradation** when Nodebox is unavailable

## Integration with Development Workflow

### Real-time Synchronization
- **File changes** appear instantly in code editor
- **Project updates** refresh file browser automatically
- **Command output** streams to terminal interface
- **Error states** propagate across all components

### Collaborative Development
- **AI and human** can work on same files simultaneously
- **Change tracking** shows who made what modifications
- **Conflict resolution** handles overlapping edits
- **Version awareness** maintains file consistency

## Best Practices

### Effective Prompting
1. **Be specific** about file paths and operations
2. **Provide context** about what you're trying to achieve
3. **Ask for explanations** when you want to understand the code
4. **Request multiple steps** for complex workflows

### Project Organization
1. **Use templates** for consistent project structure
2. **Create directories** before adding files
3. **Follow naming conventions** for better organization
4. **Document changes** with clear commit messages

### Performance Optimization
1. **Batch operations** when possible
2. **Use specific paths** instead of broad searches
3. **Limit file content** requests for large files
4. **Monitor tool call frequency** to avoid rate limits

## Troubleshooting

### Common Issues

**Tool calls not working:**
- Ensure `projectId` is provided to the chat interface
- Check that Nodebox store is properly initialized
- Verify network connectivity for API calls

**File operations failing:**
- Confirm file paths are correct and accessible
- Check permissions for file system operations
- Ensure Nodebox instance is active and ready

**Commands not executing:**
- Verify command syntax and arguments
- Check that required dependencies are installed
- Monitor process status and error output

### Debug Mode
Enable detailed logging by setting:
```tsx
<AgenticChatInterface
  projectId="my-project"
  // ... other props
  onToolCallStart={(toolName, args) => console.log('Tool started:', toolName, args)}
  onToolCallComplete={(toolName, result) => console.log('Tool completed:', toolName, result)}
/>
```

## Advanced Usage

### Custom System Prompts
Tailor the AI's behavior for specific use cases:

```tsx
const customPrompt = `You are a React specialist AI assistant. Focus on:
- Creating reusable, well-typed components
- Following React best practices
- Using modern hooks and patterns
- Implementing proper error boundaries
- Writing comprehensive tests

Use Nodebox tools to create, modify, and test React applications.`;

<AgenticChatInterface
  systemPrompt={customPrompt}
  projectId="react-project"
  // ... other props
/>
```

### Tool Call Monitoring
Track tool usage and performance:

```tsx
const [toolCallStats, setToolCallStats] = useState({});

const handleToolCallComplete = (toolName: string, result: any) => {
  setToolCallStats(prev => ({
    ...prev,
    [toolName]: (prev[toolName] || 0) + 1
  }));
};
```

### Integration with External APIs
Combine Nodebox tools with external services:

```tsx
// Example: AI can use Nodebox tools + external APIs
const systemPrompt = `You can use Nodebox tools for file operations and also:
- Fetch data from external APIs
- Generate images with DALL-E
- Search documentation with vector search
- Deploy projects to cloud platforms

Always explain which tools you're using and why.`;
```

This integration creates a powerful AI-powered development environment where natural language commands translate directly into code changes, project management, and development workflows.
