# Nodebox Store Guide

A comprehensive guide to using the new Nodebox store for managing Nodebox runtime instances and their actions in your AI Node.js workspace.

## Overview

The Nodebox store is a Zustand-based state management solution that provides centralized control over Nodebox instances, file operations, terminal commands, previews, and settings. It's designed for easy integration with React components and offers both simple and advanced usage patterns.

## Quick Start

### Basic Integration

```tsx
import { useNodeboxIntegration } from '@/lib/stores/nodebox-store';

function MyComponent({ projectId }: { projectId: string }) {
  const {
    activeInstance,
    isLoading,
    error,
    createApp,
    quickRunCommand
  } = useNodeboxIntegration(projectId);

  const handleCreateApp = () => {
    createApp('nextjs', 'My Next.js App');
  };

  return (
    <div>
      {activeInstance ? (
        <button onClick={() => quickRunCommand('npm', ['run', 'dev'])}>
          Start Dev Server
        </button>
      ) : (
        <button onClick={handleCreateApp}>
          Create App
        </button>
      )}
    </div>
  );
}
```

## Store Architecture

### Core State

- **instances**: Array of all Nodebox instances
- **activeInstanceId**: ID of the currently active instance
- **isLoading**: Loading state for async operations
- **error**: Current error state
- **fileSystem**: File system state per instance
- **processes**: Running processes per instance
- **previews**: Preview URLs per instance
- **settings**: Global and per-instance settings

### Available Templates

- **React App**: Modern React with TypeScript
- **Next.js App**: Full-stack Next.js application
- **Express Server**: Node.js Express API server
- **Vanilla JavaScript**: Simple HTML/CSS/JS project
- **TypeScript Project**: Pure TypeScript development
- **Research Dashboard**: Data visualization with Recharts
- **Data Analysis**: Node.js data processing tools
- **Custom Project**: Flexible custom template

## Hooks and Selectors

### Main Integration Hook

```tsx
const nodeboxIntegration = useNodeboxIntegration(projectId);
```

Returns:
- `activeInstance`: Current active instance
- `isLoading`: Loading state
- `error`: Error state
- `createApp(template, name)`: Quick app creation
- `quickRunCommand(command, args)`: Run command in active instance
- `quickGetPreview()`: Get preview for active instance
- `quickReadFile(path)`: Read file from active instance
- `quickWriteFile(path, content)`: Write file to active instance
- All store actions

### Individual Selectors

```tsx
// Instance management
const instances = useNodeboxInstances();
const activeInstance = useActiveNodeboxInstance();
const isLoading = useNodeboxLoading();
const error = useNodeboxError();

// Templates and settings
const templates = useNodeboxTemplates();
const globalSettings = useNodeboxGlobalSettings();

// File system
const files = useNodeboxFileSystem(instanceId);
const activeFile = useActiveNodeboxFile();

// Terminal and processes
const processes = useNodeboxProcesses(instanceId);
const terminalOutput = useNodeboxTerminalOutput(processId);

// Previews
const previews = useNodeboxPreviews(instanceId);
const activePreview = useActiveNodeboxPreview();

// All actions
const actions = useNodeboxActions();
```

## Core Actions

### Instance Management

```tsx
const actions = useNodeboxActions();

// Initialize runtime
await actions.initializeRuntime(clientMode, baseUrl);

// Create instance
const instance = await actions.createInstance({
  name: 'My App',
  template: 'nextjs',
  projectId: 'project-123'
});

// Create from template
const instance = await actions.createFromTemplate('react', 'My React App', 'project-123');

// Destroy instance
await actions.destroyInstance(instanceId);

// Set active instance
actions.setActiveInstance(instanceId);

// Refresh instances
await actions.refreshInstances();
```

### File Operations

```tsx
// Load file system
await actions.loadFileSystem(instanceId);

// Read file
const content = await actions.readFile(instanceId, 'package.json');

// Write file
await actions.writeFile(instanceId, 'README.md', '# My Project');

// Create file
await actions.createFile(instanceId, 'src/utils.ts', 'export const utils = {};');

// Delete file
await actions.deleteFile(instanceId, 'old-file.js');

// Create directory
await actions.createDirectory(instanceId, 'src/components');

// Set active file
actions.setActiveFile(instanceId, 'src/App.tsx', content);
```

### Terminal Operations

```tsx
// Run command
const process = await actions.runCommand(instanceId, 'npm', ['install', 'lodash']);

// Kill process
await actions.killProcess(instanceId, processId);

// Set active process
actions.setActiveProcess(instanceId, processId);

// Manage terminal output
actions.appendTerminalOutput(processId, 'Command output...');
actions.clearTerminalOutput(processId);
```

### Preview Management

```tsx
// Get preview
const preview = await actions.getPreview(instanceId, processId);

// Refresh previews
await actions.refreshPreviews(instanceId);

// Set active preview
actions.setActivePreview(instanceId, previewId);
```

### Settings Management

```tsx
// Update global settings
actions.updateGlobalSettings({
  autoSave: true,
  enableHotReload: true,
  memoryLimit: 1024
});

// Update instance settings
actions.updateInstanceSettings(instanceId, {
  autoPreview: true,
  timeoutMs: 60000
});
```

## Advanced Usage

### Custom Instance Configuration

```tsx
const customInstance = await actions.createInstance({
  name: 'Custom App',
  description: 'A custom configured instance',
  template: 'custom',
  projectId: 'my-project',
  environment: {
    NODE_ENV: 'development',
    API_URL: 'http://localhost:3001'
  },
  settings: {
    autoSave: true,
    autoPreview: false,
    memoryLimit: 512,
    timeoutMs: 30000,
    allowNetworking: true,
    enableHotReload: true
  }
});
```

### Error Handling

```tsx
const { error, clearError } = useNodeboxError();

useEffect(() => {
  if (error) {
    console.error('Nodebox error:', error.message);
    // Handle error appropriately
    if (error.code === 'FILESYSTEM_ERROR') {
      // Handle file system errors
    }
  }
}, [error]);

// Clear error when needed
const handleClearError = () => {
  clearError();
};
```

### Cleanup

```tsx
useEffect(() => {
  return () => {
    // Cleanup on unmount
    actions.cleanup();
  };
}, []);
```

## Integration with AI Node.js Workspace

The store is designed to integrate seamlessly with the AI Node.js workspace layout:

```tsx
// In your workspace component
const {
  activeInstance,
  isLoading,
  error,
  createFromTemplate
} = useNodeboxIntegration(projectId);

// Handle AI generation completion
const handleGenerationComplete = async (appInfo) => {
  const instance = await createFromTemplate(
    appInfo.type, 
    appInfo.name, 
    projectId
  );
  
  if (instance) {
    // Switch to workspace tab
    // Show success message
  }
};
```

## Best Practices

1. **Use the integration hook** for simple use cases
2. **Use individual selectors** for fine-grained control
3. **Handle errors gracefully** with proper error boundaries
4. **Clean up resources** when components unmount
5. **Leverage templates** for quick project setup
6. **Use settings** to customize behavior per instance
7. **Monitor loading states** for better UX

## Performance Considerations

- The store uses Zustand with Immer for efficient updates
- Selectors are optimized to prevent unnecessary re-renders
- File operations are mocked for development (replace with real API calls)
- Settings are persisted automatically

## Migration from Old Hook

Replace the old `useNodebox` hook:

```tsx
// Old way
const { activeInstance, createFromTemplate } = useNodebox({ 
  autoConnect: true, 
  clientMode: true 
});

// New way
const { activeInstance, createFromTemplate } = useNodeboxIntegration(projectId);
```

The new store provides the same functionality with additional features and better state management.

## AI Agent Tool Integration

The Nodebox store now includes comprehensive AI agent tool calling support for file operations and project management.

### Available Agent Tools

```typescript
import { nodeboxTools, nodeboxToolExecutors } from '@/lib/agents/nodebox-tools';

// All available tools
const tools = [
  'read_file_nodebox',           // Read file contents
  'write_file_nodebox',          // Write file contents
  'create_file_nodebox',         // Create new file
  'create_directory_nodebox',    // Create new directory
  'delete_file_nodebox',         // Delete file
  'list_files_nodebox',          // List files and directories
  'run_command_nodebox',         // Run terminal commands
  'create_project_nodebox',      // Create new project from template
  'get_project_info_nodebox'     // Get project information
];
```

### Tool Usage Examples

```typescript
// Read a file
const fileContent = await nodeboxToolExecutors.readFile({
  path: '/src/App.tsx'
});

// Write a file
await nodeboxToolExecutors.writeFile({
  path: '/src/components/Button.tsx',
  content: 'export default function Button() { return <button>Click me</button>; }'
});

// Create a project
await nodeboxToolExecutors.createProject({
  name: 'My React App',
  template: 'react',
  projectId: 'project-123'
});

// Run commands
await nodeboxToolExecutors.runCommand({
  command: 'npm',
  args: ['install', 'lodash']
});
```

### Integration with AI SDK

```typescript
import { convertToAISDKTools } from '@/lib/agents/nodebox-tools';

// Convert to AI SDK format
const aiTools = convertToAISDKTools(nodeboxTools);

// Use with AI SDK
const result = await generateText({
  model: openai('gpt-4'),
  tools: aiTools,
  prompt: 'Create a React component in /src/components/Header.tsx'
});
```

## Enhanced Components

### Nodebox Code Editor Panel

```typescript
import { NodeboxCodeEditorPanel } from '@/components/nodebox-code-editor-panel';

<NodeboxCodeEditorPanel projectId="my-project" />
```

Features:
- Integrated file browser with Nodebox file system
- Monaco editor with syntax highlighting
- Real-time file synchronization
- Save, run, and build actions
- Status indicators and error handling

### Nodebox File Browser

```typescript
import { NodeboxFileBrowser } from '@/components/nodebox-file-browser';

<NodeboxFileBrowser
  projectId="my-project"
  onFileSelect={(file) => console.log('Selected:', file)}
  showActions={true}
/>
```

Features:
- Tree view of Nodebox file system
- Context menu actions (create, delete, copy)
- Search functionality
- File type icons and metadata
- Drag and drop support (planned)

### AI Agent Integration Example

```typescript
import { AIAgentNodeboxIntegration } from '@/examples/ai-agent-nodebox-integration';

<AIAgentNodeboxIntegration projectId="my-project" />
```

Features:
- Chat interface with AI agent
- Real-time tool calling
- File operations through natural language
- Project management commands
- Integrated code editor and file browser

## File System Synchronization

The enhanced store automatically synchronizes between:

1. **Nodebox Store** - Primary file system state
2. **File Store** - Editor integration
3. **Open Files Store** - Tab management
4. **Monaco Editor** - Code editing

```typescript
// Automatic synchronization flow
nodeboxStore.writeFile()
  → fileStore.updateFileContent()
  → openFilesStore (auto-sync)
  → monaco editor (auto-update)
```

## Error Handling and Recovery

```typescript
// Comprehensive error handling
try {
  await nodeboxToolExecutors.writeFile({ path, content });
} catch (error) {
  if (error.code === 'FILESYSTEM_ERROR') {
    // Handle file system errors
  } else if (error.code === 'PERMISSION_ERROR') {
    // Handle permission errors
  }
}

// Error recovery
const { error, clearError } = useNodeboxError();
if (error) {
  // Display error to user
  // Provide recovery options
  clearError(); // Clear when resolved
}
```

## Real-time Updates

The system supports real-time updates through:

- **File System Watching** - Detect external file changes
- **Process Monitoring** - Track command execution
- **Preview Updates** - Live reload for development servers
- **Collaborative Editing** - Multi-user support (planned)

## Performance Optimizations

- **Lazy Loading** - Files loaded on demand
- **Caching** - Intelligent file content caching
- **Debounced Saves** - Prevent excessive write operations
- **Virtual Scrolling** - Handle large file trees
- **Code Splitting** - Modular component loading
