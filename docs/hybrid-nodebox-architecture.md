# Hybrid Nodebox-Native Environment Architecture

## Overview

This document outlines the implementation strategy for a hybrid architecture that combines Nodebox's browser-based runtime with temporary native execution environments for CLI commands that require system-level access.

## Architecture Components

### 1. Temporary Command Execution Environment (TCEE)

**Technology Stack:**
- **Docker Containers**: Lightweight, isolated, and ephemeral
- **Base Images**: Node.js Alpine images with common CLI tools pre-installed
- **Orchestration**: Docker Compose or Kubernetes for container management
- **Security**: Non-root user, read-only filesystem except for workspace

**Container Specifications:**
```dockerfile
FROM node:18-alpine
RUN addgroup -g 1001 -S nodejs && adduser -S nodebox -u 1001 -G nodejs
RUN apk add --no-cache git curl
WORKDIR /workspace
USER nodebox
EXPOSE 3001
```

**Lifecycle Management:**
- **Creation**: On-demand container spawning per project/session
- **Timeout**: 30-minute idle timeout with configurable extension
- **Cleanup**: Automatic container destruction and resource cleanup
- **Scaling**: Horizontal scaling based on demand

### 2. File Synchronization Middleware

**Sync Strategy:**
```typescript
interface SyncConfig {
  include: string[]     // ['**/*.{ts,tsx,js,jsx,json,md}']
  exclude: string[]     // ['node_modules/**', '.next/**', 'dist/**']
  priority: string[]    // ['package.json', 'components.json', '*.config.*']
  bidirectional: boolean
  conflictResolution: 'nodebox' | 'container' | 'merge' | 'prompt'
}
```

**Sync Operations:**
- **Initial Sync**: Full project sync from Nodebox to container
- **Incremental Sync**: Real-time bidirectional file changes
- **Selective Sync**: Only sync files that matter for CLI operations
- **Conflict Resolution**: Intelligent merge strategies with user prompts

**Performance Optimizations:**
- **Debouncing**: 500ms delay for file change batching
- **Compression**: gzip compression for file transfers
- **Delta Sync**: Only transfer changed portions of files
- **Caching**: File hash-based change detection

### 3. Real-time WebSocket File Monitoring

**WebSocket Architecture:**
```typescript
interface FileChangeEvent {
  type: 'create' | 'update' | 'delete' | 'rename'
  path: string
  content?: string
  metadata: {
    size: number
    modified: Date
    checksum: string
  }
  source: 'nodebox' | 'container'
}
```

**Monitoring Features:**
- **File Watchers**: Native file system watchers in both environments
- **Change Detection**: Hash-based change detection with metadata
- **Priority Queue**: Critical files (configs) get immediate sync
- **Batch Processing**: Non-critical files batched for efficiency
- **Status Updates**: Real-time sync status in UI

## Integration with Existing Nodebox AI Tools

### Phase 1: Hybrid Tool Architecture

**Enhanced Tool Structure:**
```typescript
interface HybridTool {
  name: string
  mode: 'nodebox-only' | 'container-only' | 'hybrid'
  fallback?: 'nodebox' | 'container'
  requirements: {
    cli?: string[]
    packages?: string[]
    systemAccess?: boolean
  }
}
```

**Tool Routing Logic:**
1. **Analyze Command**: Determine if command requires native CLI
2. **Environment Selection**: Choose Nodebox or container based on requirements
3. **Execution**: Run command in appropriate environment
4. **Sync Results**: Sync any file changes back to Nodebox
5. **Response**: Return unified response to user

### Phase 2: Enhanced Shadcn Integration

**Shadcn CLI Wrapper:**
```typescript
const shadcnTool = {
  name: 'shadcn_cli_native',
  mode: 'container-only',
  requirements: {
    cli: ['npx', 'shadcn'],
    systemAccess: true
  },
  execute: async ({ command, components }) => {
    // 1. Ensure container is running
    // 2. Sync current project state
    // 3. Execute actual CLI command
    // 4. Sync results back to Nodebox
    // 5. Return success/failure status
  }
}
```

## Security Considerations

### Container Security
- **Isolated Network**: No external network access except for package registries
- **Resource Limits**: CPU, memory, and disk space constraints
- **Non-root Execution**: All commands run as non-privileged user
- **Read-only Filesystem**: Base filesystem is read-only
- **Audit Logging**: All commands and file changes logged

### File Sync Security
- **Path Validation**: Prevent directory traversal attacks
- **Content Filtering**: Scan for malicious content
- **Size Limits**: Prevent large file attacks
- **Rate Limiting**: Prevent sync flooding

### WebSocket Security
- **Authentication**: JWT-based authentication for WebSocket connections
- **Encryption**: TLS encryption for all communications
- **Origin Validation**: Validate WebSocket origin headers
- **Message Validation**: Validate all incoming messages

## Performance Optimization

### Latency Minimization
- **Connection Pooling**: Reuse WebSocket connections
- **Compression**: gzip compression for file transfers
- **Caching**: Aggressive caching of unchanged files
- **Predictive Sync**: Pre-sync likely-to-change files

### Resource Management
- **Container Pooling**: Reuse containers when possible
- **Memory Management**: Efficient memory usage in sync operations
- **Disk Management**: Cleanup temporary files aggressively
- **Network Optimization**: Minimize network round trips

## Implementation Phases

### Phase 1: Foundation (Week 1-2)
- [ ] Docker container setup and orchestration
- [ ] Basic WebSocket file monitoring
- [ ] Simple bidirectional file sync
- [ ] Security framework implementation

### Phase 2: Integration (Week 3-4)
- [ ] Hybrid tool architecture
- [ ] Enhanced Shadcn CLI integration
- [ ] Conflict resolution strategies
- [ ] Performance optimizations

### Phase 3: Production (Week 5-6)
- [ ] Comprehensive testing
- [ ] Error handling and recovery
- [ ] Monitoring and logging
- [ ] Documentation and deployment

## Deployment Considerations

### Infrastructure Requirements
- **Container Runtime**: Docker or Podman
- **Orchestration**: Docker Compose (development) or Kubernetes (production)
- **Storage**: Persistent volumes for project data
- **Networking**: Internal network for container communication

### Scaling Strategy
- **Horizontal Scaling**: Multiple container instances
- **Load Balancing**: Distribute containers across nodes
- **Auto-scaling**: Scale based on demand
- **Resource Monitoring**: Monitor CPU, memory, and disk usage

## Alternative Approaches

### Option 1: WebAssembly (WASM)
- **Pros**: Native performance in browser, no containers needed
- **Cons**: Limited CLI tool support, complex toolchain

### Option 2: Server-Side Execution
- **Pros**: Simpler architecture, centralized management
- **Cons**: Latency issues, scaling challenges

### Option 3: Progressive Web App (PWA) with Service Workers
- **Pros**: Offline capability, better performance
- **Cons**: Still limited by browser security model

## Conclusion

The hybrid architecture provides the best balance of functionality, security, and performance. It enables full CLI compatibility while maintaining Nodebox's excellent development experience. The phased implementation approach allows for gradual migration and risk mitigation.
