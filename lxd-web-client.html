<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LXD Web Client</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    h1, h2, h3 {
      color: #333;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .server-info {
      background-color: #f9f9f9;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    
    .instances {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .instance-card {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .instance-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .instance-name {
      font-weight: bold;
      font-size: 1.2em;
    }
    
    .instance-status {
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 0.8em;
      font-weight: bold;
    }
    
    .status-running {
      background-color: #d4edda;
      color: #155724;
    }
    
    .status-stopped {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    .instance-details {
      margin-bottom: 15px;
    }
    
    .instance-actions {
      display: flex;
      gap: 10px;
    }
    
    button {
      padding: 8px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    
    .btn-start {
      background-color: #28a745;
      color: white;
    }
    
    .btn-stop {
      background-color: #dc3545;
      color: white;
    }
    
    .btn-console {
      background-color: #007bff;
      color: white;
    }
    
    .console-container {
      margin-top: 20px;
      display: none;
    }
    
    .console {
      background-color: #000;
      color: #fff;
      font-family: monospace;
      padding: 10px;
      border-radius: 5px;
      height: 400px;
      overflow-y: auto;
      white-space: pre-wrap;
    }
    
    .console-input {
      display: flex;
      margin-top: 10px;
    }
    
    .console-input input {
      flex-grow: 1;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px 0 0 4px;
      font-family: monospace;
    }
    
    .console-input button {
      border-radius: 0 4px 4px 0;
    }
    
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: #fff;
      animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>LXD Web Client</h1>
      <button id="refresh-btn">Refresh</button>
    </div>
    
    <div class="server-info" id="server-info">
      <h2>Server Information</h2>
      <p>Loading...</p>
    </div>
    
    <h2>Instances</h2>
    <div class="instances" id="instances">
      <p>Loading instances...</p>
    </div>
    
    <div class="console-container" id="console-container">
      <h3 id="console-title">Console</h3>
      <div class="console" id="console"></div>
      <div class="console-input">
        <input type="text" id="console-input" placeholder="Enter command...">
        <button id="console-send">Send</button>
      </div>
    </div>
  </div>
  
  <script>
    // Configuration
    const API_URL = 'http://localhost:3000/api';
    
    // DOM elements
    const serverInfoEl = document.getElementById('server-info');
    const instancesEl = document.getElementById('instances');
    const refreshBtn = document.getElementById('refresh-btn');
    const consoleContainerEl = document.getElementById('console-container');
    const consoleTitleEl = document.getElementById('console-title');
    const consoleEl = document.getElementById('console');
    const consoleInputEl = document.getElementById('console-input');
    const consoleSendBtn = document.getElementById('console-send');
    
    // WebSocket connection
    let ws = null;
    let currentInstance = null;
    
    // Fetch server information
    async function fetchServerInfo() {
      try {
        const response = await fetch(`${API_URL}/server`);
        const data = await response.json();
        
        const serverVersion = data.metadata.environment.server_version;
        const serverName = data.metadata.environment.server_name;
        const auth = data.metadata.auth;
        
        serverInfoEl.innerHTML = `
          <h2>Server Information</h2>
          <p><strong>Server:</strong> ${data.metadata.environment.server} ${serverVersion}</p>
          <p><strong>Name:</strong> ${serverName}</p>
          <p><strong>Authentication:</strong> ${auth}</p>
        `;
      } catch (error) {
        serverInfoEl.innerHTML = `
          <h2>Server Information</h2>
          <p class="error">Error: ${error.message}</p>
        `;
      }
    }
    
    // Fetch instances
    async function fetchInstances() {
      try {
        const response = await fetch(`${API_URL}/instances`);
        const instances = await response.json();
        
        if (instances.length === 0) {
          instancesEl.innerHTML = '<p>No instances found.</p>';
          return;
        }
        
        instancesEl.innerHTML = instances.map(instance => `
          <div class="instance-card">
            <div class="instance-header">
              <div class="instance-name">${instance.name}</div>
              <div class="instance-status ${instance.status.toLowerCase() === 'running' ? 'status-running' : 'status-stopped'}">
                ${instance.status}
              </div>
            </div>
            <div class="instance-details">
              <p><strong>Type:</strong> ${instance.type}</p>
              <p><strong>IP:</strong> ${instance.ip.length > 0 ? instance.ip.join(', ') : 'N/A'}</p>
            </div>
            <div class="instance-actions">
              ${instance.status.toLowerCase() === 'running' 
                ? `<button class="btn-stop" data-instance="${instance.name}">Stop</button>` 
                : `<button class="btn-start" data-instance="${instance.name}">Start</button>`}
              <button class="btn-console" data-instance="${instance.name}">Console</button>
            </div>
          </div>
        `).join('');
        
        // Add event listeners
        document.querySelectorAll('.btn-start').forEach(btn => {
          btn.addEventListener('click', startInstance);
        });
        
        document.querySelectorAll('.btn-stop').forEach(btn => {
          btn.addEventListener('click', stopInstance);
        });
        
        document.querySelectorAll('.btn-console').forEach(btn => {
          btn.addEventListener('click', openConsole);
        });
      } catch (error) {
        instancesEl.innerHTML = `<p class="error">Error: ${error.message}</p>`;
      }
    }
    
    // Start an instance
    async function startInstance(event) {
      const instanceName = event.target.dataset.instance;
      event.target.disabled = true;
      event.target.innerHTML = '<div class="loading"></div>';
      
      try {
        const response = await fetch(`${API_URL}/instances/${instanceName}/start`, {
          method: 'POST'
        });
        
        await response.json();
        await fetchInstances();
      } catch (error) {
        alert(`Failed to start instance: ${error.message}`);
        event.target.disabled = false;
        event.target.textContent = 'Start';
      }
    }
    
    // Stop an instance
    async function stopInstance(event) {
      const instanceName = event.target.dataset.instance;
      event.target.disabled = true;
      event.target.innerHTML = '<div class="loading"></div>';
      
      try {
        const response = await fetch(`${API_URL}/instances/${instanceName}/stop`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ force: false })
        });
        
        await response.json();
        await fetchInstances();
      } catch (error) {
        alert(`Failed to stop instance: ${error.message}`);
        event.target.disabled = false;
        event.target.textContent = 'Stop';
      }
    }
    
    // Open console for an instance
    async function openConsole(event) {
      const instanceName = event.target.dataset.instance;
      currentInstance = instanceName;
      
      consoleTitleEl.textContent = `Console: ${instanceName}`;
      consoleEl.textContent = '';
      consoleContainerEl.style.display = 'block';
      
      // Scroll to console
      consoleContainerEl.scrollIntoView({ behavior: 'smooth' });
      
      // Execute a command to get a shell
      try {
        const response = await fetch(`${API_URL}/instances/${instanceName}/exec`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            command: ['/bin/sh'],
            interactive: true
          })
        });
        
        const data = await response.json();
        
        // Connect to WebSocket
        connectWebSocket(data.operation, data.metadata);
      } catch (error) {
        consoleEl.textContent = `Error: ${error.message}`;
      }
    }
    
    // Connect to WebSocket for interactive console
    function connectWebSocket(operation, metadata) {
      const operationId = operation.split('/').pop();
      const wsUrl = `ws://localhost:3000/ws?operation=${operationId}&control=${metadata.fds.control}&stdin=${metadata.fds[0]}&stdout=${metadata.fds[1]}`;
      
      ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        consoleEl.textContent += 'Connected to console.\n';
      };
      
      ws.onmessage = (event) => {
        consoleEl.textContent += event.data;
        consoleEl.scrollTop = consoleEl.scrollHeight;
      };
      
      ws.onclose = () => {
        consoleEl.textContent += '\nConnection closed.\n';
      };
      
      ws.onerror = (error) => {
        consoleEl.textContent += `\nError: ${error.message}\n`;
      };
    }
    
    // Send command to console
    function sendCommand() {
      if (!ws || ws.readyState !== WebSocket.OPEN) {
        alert('Console not connected');
        return;
      }
      
      const command = consoleInputEl.value;
      ws.send(command + '\n');
      consoleInputEl.value = '';
    }
    
    // Event listeners
    refreshBtn.addEventListener('click', () => {
      fetchServerInfo();
      fetchInstances();
    });
    
    consoleSendBtn.addEventListener('click', sendCommand);
    
    consoleInputEl.addEventListener('keypress', (event) => {
      if (event.key === 'Enter') {
        sendCommand();
      }
    });
    
    // Initialize
    fetchServerInfo();
    fetchInstances();
  </script>
</body>
</html>
