/**
 * Simple LXD REST API client
 * 
 * This script demonstrates how to connect to the LXD REST API using client certificate authentication.
 */

const fs = require('fs');
const https = require('https');
const path = require('path');

// Configuration
const config = {
  apiUrl: 'https://localhost:8443',
  clientCertPath: path.join(process.env.HOME, '.config', 'lxd', 'client.crt'),
  clientKeyPath: path.join(process.env.HOME, '.config', 'lxd', 'client.key'),
  verifySSL: false
};

// Create HTTPS agent with client certificate
const httpsAgent = new https.Agent({
  rejectUnauthorized: config.verifySSL,
  cert: fs.readFileSync(config.clientCertPath),
  key: fs.readFileSync(config.clientKeyPath)
});

/**
 * Make a request to the LXD API
 */
async function request(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, config.apiUrl);
    
    const options = {
      method,
      agent: httpsAgent,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * Get server information
 */
async function getServerInfo() {
  return request('GET', '/1.0');
}

/**
 * List instances
 */
async function listInstances() {
  return request('GET', '/1.0/instances');
}

/**
 * Get instance information
 */
async function getInstanceInfo(name) {
  return request('GET', `/1.0/instances/${name}`);
}

/**
 * Get instance state
 */
async function getInstanceState(name) {
  return request('GET', `/1.0/instances/${name}/state`);
}

/**
 * Start an instance
 */
async function startInstance(name) {
  return request('PUT', `/1.0/instances/${name}/state`, { action: 'start' });
}

/**
 * Stop an instance
 */
async function stopInstance(name) {
  return request('PUT', `/1.0/instances/${name}/state`, { action: 'stop' });
}

/**
 * Execute a command in an instance
 */
async function executeCommand(name, command, env = {}, cwd = '/') {
  const data = {
    command: Array.isArray(command) ? command : ['/bin/sh', '-c', command],
    environment: env,
    'wait-for-websocket': false,
    'record-output': true,
    interactive: false,
    width: 80,
    height: 25,
    cwd
  };
  
  return request('POST', `/1.0/instances/${name}/exec`, data);
}

/**
 * Main function
 */
async function main() {
  try {
    // Get server information
    console.log('Getting server information...');
    const serverInfo = await getServerInfo();
    console.log(`Server: ${serverInfo.metadata.environment.server} ${serverInfo.metadata.environment.server_version}`);
    console.log(`Authentication: ${serverInfo.metadata.auth}`);
    
    // List instances
    console.log('\nListing instances...');
    const instances = await listInstances();
    console.log(`Found ${instances.metadata.length} instances:`);
    
    // Process each instance
    for (const instanceUrl of instances.metadata) {
      const instanceName = instanceUrl.split('/').pop();
      console.log(`\nGetting information for instance: ${instanceName}`);
      
      // Get instance information
      const instanceInfo = await getInstanceInfo(instanceName);
      console.log(`Type: ${instanceInfo.metadata.type}`);
      console.log(`Status: ${instanceInfo.metadata.status}`);
      
      // Get instance state
      const instanceState = await getInstanceState(instanceName);
      console.log(`Status code: ${instanceState.metadata.status_code}`);
      
      // If the instance is stopped, start it
      if (instanceState.metadata.status === 'Stopped') {
        console.log(`Starting instance: ${instanceName}`);
        const startResult = await startInstance(instanceName);
        console.log(`Start operation: ${startResult.operation}`);
        
        // Wait for the instance to start
        console.log('Waiting for instance to start...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Get updated state
        const updatedState = await getInstanceState(instanceName);
        console.log(`Updated status: ${updatedState.metadata.status}`);
        
        // If the instance started successfully, execute a command
        if (updatedState.metadata.status === 'Running') {
          console.log(`Executing command in instance: ${instanceName}`);
          const execResult = await executeCommand(instanceName, ['echo', 'Hello from LXD REST API!']);
          console.log(`Exec operation: ${execResult.operation}`);
          
          // Wait for the command to complete
          console.log('Waiting for command to complete...');
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Stop the instance
          console.log(`Stopping instance: ${instanceName}`);
          const stopResult = await stopInstance(instanceName);
          console.log(`Stop operation: ${stopResult.operation}`);
        }
      }
    }
    
    console.log('\nDone!');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Run the main function
main();
