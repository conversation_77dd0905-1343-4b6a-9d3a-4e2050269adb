/**
 * LXD REST API Test
 * 
 * This script tests the LXD REST API.
 */

const fs = require('fs');
const https = require('https');
const path = require('path');

// Configuration
const config = {
  apiUrl: 'https://localhost:8443',
  clientCertPath: path.join(process.env.HOME, '.config', 'lxd', 'client.crt'),
  clientKeyPath: path.join(process.env.HOME, '.config', 'lxd', 'client.key'),
  verifySSL: false
};

// Create HTTPS agent with client certificate
const httpsAgent = new https.Agent({
  rejectUnauthorized: config.verifySSL,
  cert: fs.readFileSync(config.clientCertPath),
  key: fs.readFileSync(config.clientKeyPath)
});

/**
 * Make a request to the LXD API
 */
async function request(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, config.apiUrl);
    
    const options = {
      method,
      agent: httpsAgent,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * Run tests
 */
async function runTests() {
  try {
    // Test 1: Get server information
    console.log('Test 1: Get server information');
    const serverInfo = await request('GET', '/1.0');
    console.log(`✅ Server: ${serverInfo.metadata.environment.server} ${serverInfo.metadata.environment.server_version}`);
    console.log(`✅ Authentication: ${serverInfo.metadata.auth}`);
    
    // Test 2: List instances
    console.log('\nTest 2: List instances');
    const instances = await request('GET', '/1.0/instances');
    console.log(`✅ Found ${instances.metadata.length} instances`);
    
    // Test 3: Get instance information
    console.log('\nTest 3: Get instance information');
    const instanceName = 'guacamole-privileged';
    const instanceInfo = await request('GET', `/1.0/instances/${instanceName}`);
    console.log(`✅ Instance type: ${instanceInfo.metadata.type}`);
    console.log(`✅ Instance status: ${instanceInfo.metadata.status}`);
    
    // Test 4: Get instance state
    console.log('\nTest 4: Get instance state');
    const instanceState = await request('GET', `/1.0/instances/${instanceName}/state`);
    console.log(`✅ Instance status code: ${instanceState.metadata.status_code}`);
    
    console.log('\nAll tests passed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the tests
runTests();
