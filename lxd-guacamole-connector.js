/**
 * LXD Guacamole Connector
 * 
 * This script demonstrates how to connect Guacamole to LXD using the REST API.
 * It creates a simple web server that can be used as a bridge between Guacamole and LXD.
 */

const fs = require('fs');
const https = require('https');
const http = require('http');
const path = require('path');
const express = require('express');
const bodyParser = require('body-parser');
const WebSocket = require('ws');

// Configuration
const config = {
  // LXD configuration
  lxd: {
    apiUrl: 'https://localhost:8443',
    clientCertPath: path.join(process.env.HOME, '.config', 'lxd', 'client.crt'),
    clientKeyPath: path.join(process.env.HOME, '.config', 'lxd', 'client.key'),
    verifySSL: false
  },
  // Server configuration
  server: {
    port: 3000,
    host: 'localhost'
  }
};

// Create Express app
const app = express();
app.use(bodyParser.json());

// Create HTTPS agent with client certificate
const httpsAgent = new https.Agent({
  rejectUnauthorized: config.lxd.verifySSL,
  cert: fs.readFileSync(config.lxd.clientCertPath),
  key: fs.readFileSync(config.lxd.clientKeyPath)
});

/**
 * Make a request to the LXD API
 */
async function lxdRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, config.lxd.apiUrl);
    
    const options = {
      method,
      agent: httpsAgent,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * Wait for an operation to complete
 */
async function waitForOperation(operationUrl, timeout = 30000) {
  return new Promise((resolve, reject) => {
    const url = new URL(`${operationUrl}/wait`, config.lxd.apiUrl);
    
    const options = {
      method: 'GET',
      agent: httpsAgent
    };
    
    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(timeout, () => {
      req.abort();
      reject(new Error(`Operation timed out after ${timeout}ms`));
    });
    
    req.end();
  });
}

// API routes

// Get server information
app.get('/api/server', async (req, res) => {
  try {
    const serverInfo = await lxdRequest('GET', '/1.0');
    res.json(serverInfo);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// List instances
app.get('/api/instances', async (req, res) => {
  try {
    const instances = await lxdRequest('GET', '/1.0/instances');
    
    // Get detailed information for each instance
    const instancesData = await Promise.all(
      instances.metadata.map(async (instanceUrl) => {
        const instanceName = instanceUrl.split('/').pop();
        const instanceInfo = await lxdRequest('GET', `/1.0/instances/${instanceName}`);
        const instanceState = await lxdRequest('GET', `/1.0/instances/${instanceName}/state`);
        
        return {
          name: instanceName,
          type: instanceInfo.metadata.type,
          status: instanceState.metadata.status,
          statusCode: instanceState.metadata.status_code,
          ip: instanceState.metadata.network ? 
            Object.values(instanceState.metadata.network)
              .flatMap(iface => iface.addresses || [])
              .filter(addr => addr.family === 'inet')
              .map(addr => addr.address) : 
            []
        };
      })
    );
    
    res.json(instancesData);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get instance information
app.get('/api/instances/:name', async (req, res) => {
  try {
    const instanceInfo = await lxdRequest('GET', `/1.0/instances/${req.params.name}`);
    const instanceState = await lxdRequest('GET', `/1.0/instances/${req.params.name}/state`);
    
    res.json({
      info: instanceInfo.metadata,
      state: instanceState.metadata
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Start an instance
app.post('/api/instances/:name/start', async (req, res) => {
  try {
    const startResult = await lxdRequest('PUT', `/1.0/instances/${req.params.name}/state`, { action: 'start' });
    
    // Wait for the operation to complete
    const operationResult = await waitForOperation(startResult.operation);
    
    res.json(operationResult);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Stop an instance
app.post('/api/instances/:name/stop', async (req, res) => {
  try {
    const stopResult = await lxdRequest('PUT', `/1.0/instances/${req.params.name}/state`, { 
      action: 'stop',
      force: req.body.force || false,
      timeout: req.body.timeout || 30
    });
    
    // Wait for the operation to complete
    const operationResult = await waitForOperation(stopResult.operation);
    
    res.json(operationResult);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Execute a command in an instance
app.post('/api/instances/:name/exec', async (req, res) => {
  try {
    const { command, env, cwd, interactive } = req.body;
    
    const data = {
      command: Array.isArray(command) ? command : ['/bin/sh', '-c', command],
      environment: env || {},
      'wait-for-websocket': interactive || false,
      'record-output': !interactive,
      interactive: !!interactive,
      width: req.body.width || 80,
      height: req.body.height || 25,
      cwd: cwd || '/'
    };
    
    const execResult = await lxdRequest('POST', `/1.0/instances/${req.params.name}/exec`, data);
    
    if (interactive) {
      // Return the operation URL for the client to connect to
      res.json({
        operation: execResult.operation,
        metadata: execResult.metadata
      });
    } else {
      // Wait for the operation to complete
      const operationResult = await waitForOperation(execResult.operation);
      
      res.json(operationResult);
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Create a WebSocket server for interactive console
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws, req) => {
  const url = new URL(req.url, `http://${req.headers.host}`);
  const operationId = url.searchParams.get('operation');
  const controlSecret = url.searchParams.get('control');
  const stdinSecret = url.searchParams.get('stdin');
  const stdoutSecret = url.searchParams.get('stdout');
  
  if (!operationId || !controlSecret || !stdinSecret || !stdoutSecret) {
    ws.close(1008, 'Missing required parameters');
    return;
  }
  
  // Connect to LXD WebSocket
  const lxdWs = new WebSocket(`${config.lxd.apiUrl.replace('https:', 'wss:')}/1.0/operations/${operationId}/websocket?secret=${stdoutSecret}`, {
    agent: httpsAgent,
    rejectUnauthorized: config.lxd.verifySSL
  });
  
  lxdWs.on('open', () => {
    console.log(`Connected to LXD WebSocket for operation ${operationId}`);
  });
  
  lxdWs.on('message', (data) => {
    ws.send(data);
  });
  
  lxdWs.on('close', (code, reason) => {
    console.log(`LXD WebSocket closed: ${code} ${reason}`);
    ws.close(code, reason);
  });
  
  lxdWs.on('error', (error) => {
    console.error('LXD WebSocket error:', error);
    ws.close(1011, error.message);
  });
  
  ws.on('message', (data) => {
    lxdWs.send(data);
  });
  
  ws.on('close', (code, reason) => {
    console.log(`Client WebSocket closed: ${code} ${reason}`);
    lxdWs.close(code, reason);
  });
});

// Start the server
server.listen(config.server.port, config.server.host, () => {
  console.log(`LXD Guacamole Connector listening at http://${config.server.host}:${config.server.port}`);
});
