{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine,STOPPED,,,CONTAINER,0 --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:13:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc config show guacamole-alpine,STOPPED,,,CONTAINER,0\nError: Failed to fetch instance \"guacamole-alpine,STOPPED,,,CONTAINER,0\" in project \"default\": Instance not found\n","service":"app-gen","timestamp":"2025-05-18 14:13:29"}
{"level":"error","message":"API Error:","metadata":{"details":"Unexpected end of JSON input","message":"Failed to get container info: guacamole-alpine,STOPPED,,,CONTAINER,0","status":500},"service":"app-gen","timestamp":"2025-05-18 14:13:29"}
{"level":"error","message":"LXC REST API Error: No response received","service":"app-gen","timestamp":"2025-05-18 14:17:04"}
{"level":"error","message":"Failed to ping LXC REST API","metadata":{"code":"ECONNREFUSED","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:8080/api/v1","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.9.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/ping","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"","name":"AggregateError","stack":"AggregateError: \n    at AxiosError.from (webpack-internal:///(rsc)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/core/AxiosError.js:97:14)\n    at RedirectableRequest.handleRequestError (webpack-internal:///(rsc)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/adapters/http.js:650:75)\n    at RedirectableRequest.emit (node:events:519:35)\n    at eventHandlers.<computed> (webpack-internal:///(rsc)/./node_modules/.pnpm/follow-redirects@1.15.9_debug@4.4.0/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:507:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:518:5)\n    at Socket.emit (node:events:507:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (webpack-internal:///(rsc)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/core/Axios.js:57:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async LxcRestClient.ping (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-rest-client.ts:96:30)\n    at async LxcRestClient.initialize (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-rest-client.ts:77:13)\n    at async LxcRestClient.ensureInitialized (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-rest-client.ts:89:13)\n    at async LxcRestClient.listContainers (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-rest-client.ts:106:9)\n    at async LxcApi.listContainers (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-api.ts:46:20)\n    at async ContainerManager.initialize (webpack-internal:///(rsc)/./lib/containerization/lxc/core/container-manager.ts:72:32)"},"service":"app-gen","timestamp":"2025-05-18 14:17:04"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine,STOPPED,,,CONTAINER,0 --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:17:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine,STOPPED,,,CONTAINER,0 --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:17:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc config show guacamole-alpine,STOPPED,,,CONTAINER,0\nError: Failed to fetch instance \"guacamole-alpine,STOPPED,,,CONTAINER,0\" in project \"default\": Instance not found\n","service":"app-gen","timestamp":"2025-05-18 14:17:05"}
{"level":"error","message":"API Error:","metadata":{"details":"Unexpected end of JSON input","message":"Failed to get container info: guacamole-alpine,STOPPED,,,CONTAINER,0","status":500},"service":"app-gen","timestamp":"2025-05-18 14:17:05"}
{"level":"error","message":"LXC REST API Error: No response received","service":"app-gen","timestamp":"2025-05-18 14:34:02"}
{"level":"error","message":"Failed to ping LXC REST API","metadata":{"code":"ECONNREFUSED","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:8080/api/v1","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.9.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/ping","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"","name":"AggregateError","stack":"AggregateError: \n    at AxiosError.from (webpack-internal:///(rsc)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/core/AxiosError.js:97:14)\n    at RedirectableRequest.handleRequestError (webpack-internal:///(rsc)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/adapters/http.js:650:75)\n    at RedirectableRequest.emit (node:events:519:35)\n    at eventHandlers.<computed> (webpack-internal:///(rsc)/./node_modules/.pnpm/follow-redirects@1.15.9_debug@4.4.0/node_modules/follow-redirects/index.js:49:24)\n    at ClientRequest.emit (node:events:507:28)\n    at emitErrorEvent (node:_http_client:104:11)\n    at Socket.socketErrorListener (node:_http_client:518:5)\n    at Socket.emit (node:events:507:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (webpack-internal:///(rsc)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/core/Axios.js:57:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async LxcRestClient.ping (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-rest-client.ts:96:30)\n    at async LxcRestClient.initialize (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-rest-client.ts:77:13)\n    at async LxcRestClient.ensureInitialized (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-rest-client.ts:89:13)\n    at async LxcRestClient.listImages (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-rest-client.ts:235:9)\n    at async LxcApi.listTemplates (webpack-internal:///(rsc)/./lib/containerization/lxc/api/lxc-api.ts:211:20)\n    at async TemplateManager.initialize (webpack-internal:///(rsc)/./lib/containerization/lxc/core/template-manager.ts:47:31)"},"service":"app-gen","timestamp":"2025-05-18 14:34:02"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine,STOPPED,,,CONTAINER,0 --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:34:03"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine,STOPPED,,,CONTAINER,0 --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:34:03"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc config show guacamole-alpine,STOPPED,,,CONTAINER,0\nError: Failed to fetch instance \"guacamole-alpine,STOPPED,,,CONTAINER,0\" in project \"default\": Instance not found\n","service":"app-gen","timestamp":"2025-05-18 14:34:03"}
{"level":"error","message":"API Error:","metadata":{"details":"Unexpected end of JSON input","message":"Failed to get container info: guacamole-alpine,STOPPED,,,CONTAINER,0","status":500},"service":"app-gen","timestamp":"2025-05-18 14:34:03"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:37"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:37"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:37"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:40"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:40"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:40"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:40"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:40"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 14:37:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:47"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:47"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:48"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:48"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:50"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:51"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:51"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:51"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 14:37:54"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:57"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:57"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:57"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:58"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:59"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:59"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:59"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:59"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:37:59"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 14:38:03"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:04"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:04"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:04"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:07"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 14:38:08"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:10"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:10"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:10"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:10"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:11"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:11"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:11"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:11"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:13"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:13"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:13"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:13"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:13"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:38:13"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 14:39:37"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:40"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:40"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:41"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:41"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:42"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:43"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:43"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 14:39:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:50"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:50"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:50"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:50"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:50"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:50"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:39:51"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 14:40:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:08"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:08"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:08"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:08"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:40:09"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 14:41:10"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:10"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:10"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:11"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:11"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:11"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:11"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:11"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:13"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:13"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:13"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 14:41:54"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:54"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:54"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:55"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:56"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:57"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:57"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:57"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:57"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:57"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 14:41:57"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 16:02:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:47"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:47"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:47"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:47"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:47"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:47"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:47"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:48"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:48"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:48"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:48"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:50"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:02:50"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 16:03:02"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:03"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:03"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:04"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:04"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:04"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:04"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:05"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:06"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:07"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:08"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 16:03:12"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc create [object Object] container-1\nError: unknown command \"create\" for \"lxc\"\n","service":"app-gen","timestamp":"2025-05-18 16:03:13"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:13"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc create [object Object] container-1\nError: unknown command \"create\" for \"lxc\"\n","service":"app-gen","timestamp":"2025-05-18 16:03:14"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:14"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:14"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc create [object Object] container-1\nError: unknown command \"create\" for \"lxc\"\n","service":"app-gen","timestamp":"2025-05-18 16:03:14"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:14"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:15"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:15"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:16"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:16"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:16"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:16"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 16:03:19"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:20"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:20"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 16:03:20"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:21"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:21"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:22"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:22"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:23"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:23"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:24"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 16:03:24"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 17:01:26"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:26"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:27"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 17:01:27"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:27"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:28"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:28"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:28"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:30"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:30"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:01:31"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 17:03:25"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:26"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:26"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:27"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:27"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:27"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:27"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:27"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:28"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:28"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:28"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:30"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:30"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:30"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:03:30"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 17:04:29"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:30"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:30"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:31"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:31"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:31"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:31"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:31"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:31"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:32"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:32"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:32"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:32"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:33"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:33"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:33"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:33"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:33"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:33"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:34"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:34"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 17:04:37"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc create [object Object] container-1\nError: unknown command \"create\" for \"lxc\"\n","service":"app-gen","timestamp":"2025-05-18 17:04:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:38"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc create [object Object] container-1\nError: unknown command \"create\" for \"lxc\"\n","service":"app-gen","timestamp":"2025-05-18 17:04:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc create [object Object] container-1\nError: unknown command \"create\" for \"lxc\"\n","service":"app-gen","timestamp":"2025-05-18 17:04:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:39"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:40"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:40"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:41"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:41"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:42"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:42"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 17:04:44"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:45"}
{"level":"error","message":"Failed to start LXC REST API server","metadata":{"address":"::1","code":"EADDRINUSE","errno":-98,"port":8080,"syscall":"listen"},"service":"app-gen","timestamp":"2025-05-18 17:04:45"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-alpine --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-privileged --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:46"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-simple --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:48"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:48"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info guacamole-test --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:48"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:49"}
{"level":"error","message":"Error executing LXC command: Command failed: lxc info test-container --state\nError: unknown flag: --state\n","service":"app-gen","timestamp":"2025-05-18 17:04:49"}
