/**
 * Enhanced Agentic Chat with Nodebox Integration Example
 * 
 * Demonstrates how to use the enhanced agentic chat interface
 * with Nodebox tool calling capabilities for file operations
 */

"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Bot,
  Code,
  FileText,
  Terminal,
  Settings,
  Play,
  Folder,
  Zap,
  MessageSquare
} from 'lucide-react';
import { AgenticChatInterface } from '@/components/agentic-chatbot/agentic-chat-interface';
import { NodeboxCodeEditorPanel } from '@/components/nodebox-code-editor-panel';
import { NodeboxFileBrowser } from '@/components/nodebox-file-browser';
import { useNodeboxIntegration } from '@/lib/stores/nodebox-store';

interface EnhancedAgenticChatWithNodeboxProps {
  projectId: string;
}

export function EnhancedAgenticChatWithNodebox({ projectId }: EnhancedAgenticChatWithNodeboxProps) {
  const [activeTab, setActiveTab] = useState('chat');
  
  // Nodebox integration for status display
  const {
    activeInstance,
    isLoading,
    error
  } = useNodeboxIntegration(projectId);

  // Example prompts for users to try
  const examplePrompts = [
    {
      title: "Create a React Component",
      prompt: "Create a React component called Button in /src/components/Button.tsx with TypeScript. Make it accept props for variant (primary, secondary) and size (small, medium, large).",
      icon: <Code className="h-4 w-4" />,
      category: "Development"
    },
    {
      title: "Read Package.json",
      prompt: "Read the package.json file and tell me what dependencies are installed in this project.",
      icon: <FileText className="h-4 w-4" />,
      category: "File Operations"
    },
    {
      title: "Create Project Structure",
      prompt: "Create a new React project with a proper folder structure including components, hooks, utils, and types directories.",
      icon: <Folder className="h-4 w-4" />,
      category: "Project Setup"
    },
    {
      title: "Run Development Server",
      prompt: "Start the development server for this project and show me the output.",
      icon: <Play className="h-4 w-4" />,
      category: "Commands"
    },
    {
      title: "List All Files",
      prompt: "Show me all the files in this project and organize them by directory.",
      icon: <FileText className="h-4 w-4" />,
      category: "File Operations"
    },
    {
      title: "Install Dependencies",
      prompt: "Install lodash and @types/lodash as dependencies for this project.",
      icon: <Terminal className="h-4 w-4" />,
      category: "Commands"
    }
  ];

  const handleExamplePrompt = (prompt: string) => {
    // This would trigger the chat interface with the example prompt
    // For now, we'll just show how it would work
    console.log('Example prompt selected:', prompt);
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <div className="border-b p-4 bg-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Bot className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-semibold">AI Agent + Nodebox Development Environment</h1>
            </div>
            
            {activeInstance && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="gap-1">
                  <Zap className="h-3 w-3" />
                  {activeInstance.config.name}
                </Badge>
                <Badge variant="secondary">
                  {activeInstance.config.template}
                </Badge>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            {isLoading && (
              <Badge variant="outline" className="gap-1">
                <div className="h-2 w-2 bg-yellow-500 rounded-full animate-pulse" />
                Loading
              </Badge>
            )}
            {error && (
              <Badge variant="destructive" className="gap-1">
                <div className="h-2 w-2 bg-red-500 rounded-full" />
                Error
              </Badge>
            )}
            {activeInstance && !isLoading && !error && (
              <Badge variant="default" className="gap-1">
                <div className="h-2 w-2 bg-green-500 rounded-full" />
                Ready
              </Badge>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        {/* Main Content Area */}
        <div className="flex-1 flex">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            {/* Tab Navigation */}
            <div className="border-b bg-muted/30">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="chat" className="gap-2">
                  <MessageSquare className="h-4 w-4" />
                  AI Chat
                </TabsTrigger>
                <TabsTrigger value="editor" className="gap-2">
                  <Code className="h-4 w-4" />
                  Code Editor
                </TabsTrigger>
                <TabsTrigger value="files" className="gap-2">
                  <Folder className="h-4 w-4" />
                  File Browser
                </TabsTrigger>
                <TabsTrigger value="examples" className="gap-2">
                  <Zap className="h-4 w-4" />
                  Examples
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Tab Content */}
            <div className="flex-1 min-h-0">
              <TabsContent value="chat" className="h-full m-0">
                <AgenticChatInterface
                  projectId={projectId}
                  apiEndpoint="/api/chat"
                  systemPrompt={`You are an AI development assistant with access to a Nodebox development environment. You can:

1. **File Operations**: Read, write, create, and delete files using Nodebox tools
2. **Project Management**: Create new projects from templates (React, Next.js, Express, etc.)
3. **Command Execution**: Run npm commands, build processes, and development servers
4. **Code Generation**: Create components, utilities, and complete applications

Available Nodebox Tools:
- read_file_nodebox: Read file contents
- write_file_nodebox: Write content to files
- create_file_nodebox: Create new files
- create_directory_nodebox: Create directories
- delete_file_nodebox: Delete files
- list_files_nodebox: List files and directories
- run_command_nodebox: Execute terminal commands
- create_project_nodebox: Create projects from templates
- get_project_info_nodebox: Get project information

Always use these tools when the user asks for file operations, project setup, or command execution. Provide clear feedback about what you're doing and the results.`}
                  maxSteps={10}
                  enableTools={true}
                  enableContextManager={true}
                  className="h-full"
                />
              </TabsContent>

              <TabsContent value="editor" className="h-full m-0">
                <NodeboxCodeEditorPanel projectId={projectId} />
              </TabsContent>

              <TabsContent value="files" className="h-full m-0">
                <NodeboxFileBrowser 
                  projectId={projectId}
                  showActions={true}
                  className="h-full"
                />
              </TabsContent>

              <TabsContent value="examples" className="h-full m-0">
                <div className="h-full p-6">
                  <div className="max-w-4xl mx-auto">
                    <div className="mb-6">
                      <h2 className="text-2xl font-bold mb-2">Example Prompts</h2>
                      <p className="text-muted-foreground">
                        Try these example prompts to see how the AI agent can help with development tasks using Nodebox tools.
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {examplePrompts.map((example, index) => (
                        <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-sm flex items-center gap-2">
                              {example.icon}
                              {example.title}
                              <Badge variant="outline" className="ml-auto text-xs">
                                {example.category}
                              </Badge>
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <p className="text-sm text-muted-foreground mb-3">
                              {example.prompt}
                            </p>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleExamplePrompt(example.prompt)}
                              className="w-full"
                            >
                              Try This Prompt
                            </Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    <Separator className="my-8" />

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Available Nodebox Tools</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <h4 className="font-medium mb-2 flex items-center gap-1">
                            <FileText className="h-4 w-4" />
                            File Operations
                          </h4>
                          <ul className="space-y-1 text-muted-foreground">
                            <li>• read_file_nodebox</li>
                            <li>• write_file_nodebox</li>
                            <li>• create_file_nodebox</li>
                            <li>• delete_file_nodebox</li>
                            <li>• list_files_nodebox</li>
                          </ul>
                        </div>
                        
                        <div>
                          <h4 className="font-medium mb-2 flex items-center gap-1">
                            <Terminal className="h-4 w-4" />
                            Commands & Projects
                          </h4>
                          <ul className="space-y-1 text-muted-foreground">
                            <li>• run_command_nodebox</li>
                            <li>• create_project_nodebox</li>
                            <li>• get_project_info_nodebox</li>
                            <li>• create_directory_nodebox</li>
                          </ul>
                        </div>
                        
                        <div>
                          <h4 className="font-medium mb-2 flex items-center gap-1">
                            <Settings className="h-4 w-4" />
                            Templates Available
                          </h4>
                          <ul className="space-y-1 text-muted-foreground">
                            <li>• React App</li>
                            <li>• Next.js App</li>
                            <li>• Express Server</li>
                            <li>• TypeScript Project</li>
                            <li>• Vanilla JavaScript</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
