/**
 * Nodebox Store Test Component
 * 
 * Simple test component to verify that the Nodebox store hooks
 * don't cause infinite loops and work correctly
 */

"use client";

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw,
  Play,
  AlertTriangle
} from 'lucide-react';
import { 
  useNodeboxActions, 
  useNodeboxIntegration,
  useActiveNodeboxInstance,
  useNodeboxLoading,
  useNodeboxError
} from '@/lib/stores/nodebox-store';

export function NodeboxStoreTest() {
  const [renderCount, setRenderCount] = useState(0);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [isRunningTests, setIsRunningTests] = useState(false);

  // Test individual hooks
  const actions = useNodeboxActions();
  const activeInstance = useActiveNodeboxInstance();
  const isLoading = useNodeboxLoading();
  const error = useNodeboxError();
  const integration = useNodeboxIntegration('test-project');

  // Track render count to detect infinite loops
  useEffect(() => {
    setRenderCount(prev => prev + 1);
  });

  // Run tests
  const runTests = async () => {
    setIsRunningTests(true);
    const results: Record<string, boolean> = {};

    try {
      // Test 1: Actions hook returns stable object
      const actions1 = useNodeboxActions();
      const actions2 = useNodeboxActions();
      results.actionsStability = typeof actions1 === 'object' && typeof actions2 === 'object';

      // Test 2: Integration hook returns stable object
      results.integrationStability = typeof integration === 'object';

      // Test 3: No infinite loops (render count should be reasonable)
      results.noInfiniteLoops = renderCount < 10;

      // Test 4: All required actions are present
      const requiredActions = [
        'initializeRuntime',
        'createInstance',
        'destroyInstance',
        'readFile',
        'writeFile',
        'runCommand'
      ];
      results.allActionsPresent = requiredActions.every(action => 
        typeof actions[action as keyof typeof actions] === 'function'
      );

      // Test 5: Integration provides quick actions
      const quickActions = [
        'createApp',
        'quickRunCommand',
        'quickReadFile',
        'quickWriteFile'
      ];
      results.quickActionsPresent = quickActions.every(action =>
        typeof integration[action as keyof typeof integration] === 'function'
      );

      setTestResults(results);
    } catch (error) {
      console.error('Test error:', error);
      setTestResults({ error: false });
    } finally {
      setIsRunningTests(false);
    }
  };

  const getTestStatus = (testName: string) => {
    if (isRunningTests) return 'running';
    if (!(testName in testResults)) return 'pending';
    return testResults[testName] ? 'passed' : 'failed';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running': return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      default: return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'running': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    }
  };

  const tests = [
    {
      name: 'actionsStability',
      title: 'Actions Hook Stability',
      description: 'useNodeboxActions returns stable object references'
    },
    {
      name: 'integrationStability',
      title: 'Integration Hook Stability',
      description: 'useNodeboxIntegration returns stable object references'
    },
    {
      name: 'noInfiniteLoops',
      title: 'No Infinite Loops',
      description: 'Component renders a reasonable number of times'
    },
    {
      name: 'allActionsPresent',
      title: 'All Actions Present',
      description: 'All required action functions are available'
    },
    {
      name: 'quickActionsPresent',
      title: 'Quick Actions Present',
      description: 'Integration provides convenience functions'
    }
  ];

  const allTestsPassed = Object.keys(testResults).length > 0 && 
    Object.values(testResults).every(result => result === true);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Nodebox Store Test Suite</h1>
        <p className="text-muted-foreground">
          Testing Nodebox store hooks for stability and infinite loop prevention
        </p>
      </div>

      {/* Render Count Monitor */}
      <Alert className={renderCount > 10 ? 'border-red-200 bg-red-50' : 'border-blue-200 bg-blue-50'}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Render Count:</strong> {renderCount} 
          {renderCount > 10 && (
            <span className="text-red-600 ml-2">
              ⚠️ High render count detected - possible infinite loop!
            </span>
          )}
        </AlertDescription>
      </Alert>

      {/* Test Controls */}
      <div className="flex justify-center gap-4">
        <Button 
          onClick={runTests} 
          disabled={isRunningTests}
          className="gap-2"
        >
          {isRunningTests ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Play className="h-4 w-4" />
          )}
          Run Tests
        </Button>
        
        <Button 
          variant="outline" 
          onClick={() => window.location.reload()}
          className="gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Reset
        </Button>
      </div>

      {/* Test Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {tests.map((test) => {
          const status = getTestStatus(test.name);
          return (
            <Card key={test.name} className="relative">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  {getStatusIcon(status)}
                  {test.title}
                  <Badge className={`ml-auto text-xs ${getStatusColor(status)}`}>
                    {status}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">
                  {test.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Overall Status */}
      {Object.keys(testResults).length > 0 && (
        <Alert className={allTestsPassed ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          {allTestsPassed ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <XCircle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription>
            <strong>Overall Status:</strong> {allTestsPassed ? 'All tests passed! ✅' : 'Some tests failed ❌'}
          </AlertDescription>
        </Alert>
      )}

      {/* Debug Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Debug Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div><strong>Active Instance:</strong> {activeInstance?.id || 'None'}</div>
          <div><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</div>
          <div><strong>Error:</strong> {error?.message || 'None'}</div>
          <div><strong>Actions Available:</strong> {Object.keys(actions).length}</div>
          <div><strong>Integration Methods:</strong> {Object.keys(integration).length}</div>
        </CardContent>
      </Card>
    </div>
  );
}
