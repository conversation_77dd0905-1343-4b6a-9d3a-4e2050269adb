/**
 * Nodebox AI Chat Example
 * 
 * Example component demonstrating how to use the new Nodebox AI chat route
 * with comprehensive file operations and project management capabilities
 */

"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Bot,
  Code,
  FileText,
  Terminal,
  Play,
  Folder,
  Zap,
  MessageSquare,
  Sparkles,
  Rocket
} from 'lucide-react';
import { AgenticChatInterface } from '@/components/agentic-chatbot/agentic-chat-interface';
import { NodeboxCodeEditorPanel } from '@/components/nodebox-code-editor-panel';
import { NodeboxFileBrowser } from '@/components/nodebox-file-browser';

interface NodeboxAIChatExampleProps {
  projectId?: string;
}

export function NodeboxAIChatExample({ projectId = 'demo-project' }: NodeboxAIChatExampleProps) {
  const [activeTab, setActiveTab] = useState('chat');

  // Example prompts specifically designed for the Nodebox AI route
  const examplePrompts = [
    {
      title: "Create React App",
      prompt: "Create a new React project called 'my-dashboard' with TypeScript. Include a Header component with navigation and a main Dashboard component.",
      icon: <Rocket className="h-4 w-4" />,
      category: "Project Setup",
      difficulty: "Beginner"
    },
    {
      title: "Build Component Library",
      prompt: "Create a component library with Button, Card, and Modal components. Each should be TypeScript-based with proper props interfaces and styling.",
      icon: <Code className="h-4 w-4" />,
      category: "Development",
      difficulty: "Intermediate"
    },
    {
      title: "Setup Express API",
      prompt: "Create an Express server with TypeScript. Add routes for user management (GET, POST, PUT, DELETE) and include proper error handling middleware.",
      icon: <Terminal className="h-4 w-4" />,
      category: "Backend",
      difficulty: "Intermediate"
    },
    {
      title: "Add Testing Setup",
      prompt: "Set up Jest and React Testing Library for the project. Create example tests for components and add test scripts to package.json.",
      icon: <Zap className="h-4 w-4" />,
      category: "Testing",
      difficulty: "Advanced"
    },
    {
      title: "File Organization",
      prompt: "Analyze the current project structure and reorganize files into a better folder structure. Create proper directories for components, hooks, utils, and types.",
      icon: <Folder className="h-4 w-4" />,
      category: "Organization",
      difficulty: "Beginner"
    },
    {
      title: "Add Documentation",
      prompt: "Create comprehensive documentation for the project including README.md, API documentation, and component documentation with examples.",
      icon: <FileText className="h-4 w-4" />,
      category: "Documentation",
      difficulty: "Beginner"
    }
  ];

  const quickActions = [
    {
      title: "List Files",
      prompt: "Show me all files in this project organized by directory",
      icon: <FileText className="h-3 w-3" />
    },
    {
      title: "Project Info",
      prompt: "Tell me about this project - what type it is, how many files, and current status",
      icon: <Folder className="h-3 w-3" />
    },
    {
      title: "Install Dependencies",
      prompt: "Install lodash and @types/lodash as dependencies",
      icon: <Terminal className="h-3 w-3" />
    },
    {
      title: "Run Dev Server",
      prompt: "Start the development server for this project",
      icon: <Play className="h-3 w-3" />
    }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'Advanced': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <div className="border-b p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Bot className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-semibold">Nodebox AI Development Assistant</h1>
            </div>
            
            <Badge variant="outline" className="gap-1 bg-white/50">
              <Sparkles className="h-3 w-3" />
              Enhanced with Tool Calling
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="gap-1">
              <Zap className="h-3 w-3" />
              Project: {projectId}
            </Badge>
          </div>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          {/* Tab Navigation */}
          <div className="border-b bg-muted/30">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="chat" className="gap-2">
                <MessageSquare className="h-4 w-4" />
                AI Chat
              </TabsTrigger>
              <TabsTrigger value="examples" className="gap-2">
                <Sparkles className="h-4 w-4" />
                Examples
              </TabsTrigger>
              <TabsTrigger value="editor" className="gap-2">
                <Code className="h-4 w-4" />
                Code Editor
              </TabsTrigger>
              <TabsTrigger value="files" className="gap-2">
                <Folder className="h-4 w-4" />
                File Browser
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Tab Content */}
          <div className="flex-1 min-h-0">
            <TabsContent value="chat" className="h-full m-0">
              <AgenticChatInterface
                projectId={projectId}
                apiEndpoint="/api/nodebox-ai"
                systemPrompt="You are an AI development assistant with comprehensive Nodebox capabilities. Use the available tools to help users with file operations, project management, and development tasks."
                maxSteps={15}
                enableTools={true}
                enableContextManager={true}
                className="h-full"
              />
            </TabsContent>

            <TabsContent value="examples" className="h-full m-0">
              <div className="h-full p-6 overflow-auto">
                <div className="max-w-6xl mx-auto space-y-8">
                  {/* Quick Actions */}
                  <div>
                    <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      Quick Actions
                    </h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {quickActions.map((action, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          className="h-auto p-3 flex flex-col items-center gap-2 text-center"
                          onClick={() => {
                            setActiveTab('chat');
                            // In a real implementation, this would trigger the chat with the prompt
                            console.log('Quick action:', action.prompt);
                          }}
                        >
                          {action.icon}
                          <span className="text-xs">{action.title}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  {/* Example Prompts */}
                  <div>
                    <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Sparkles className="h-5 w-5" />
                      Example Development Tasks
                    </h2>
                    <p className="text-muted-foreground mb-6">
                      Try these comprehensive examples to see how the AI can help with complex development workflows using Nodebox tools.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {examplePrompts.map((example, index) => (
                        <Card key={index} className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-sm flex items-center gap-2">
                              {example.icon}
                              {example.title}
                            </CardTitle>
                            <div className="flex gap-2">
                              <Badge variant="outline" className="text-xs">
                                {example.category}
                              </Badge>
                              <Badge className={`text-xs ${getDifficultyColor(example.difficulty)}`}>
                                {example.difficulty}
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                              {example.prompt}
                            </p>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setActiveTab('chat');
                                // In a real implementation, this would trigger the chat with the prompt
                                console.log('Example prompt:', example.prompt);
                              }}
                              className="w-full"
                            >
                              Try This Example
                            </Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  {/* Tool Information */}
                  <div>
                    <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                      <Terminal className="h-5 w-5" />
                      Available Nodebox Tools
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-3">
                        <h3 className="font-medium text-sm flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          File Operations
                        </h3>
                        <ul className="space-y-1 text-sm text-muted-foreground">
                          <li>• read_file_nodebox - Read file contents</li>
                          <li>• write_file_nodebox - Write/update files</li>
                          <li>• create_file_nodebox - Create new files</li>
                          <li>• delete_file_nodebox - Remove files</li>
                          <li>• list_files_nodebox - Browse directories</li>
                          <li>• create_directory_nodebox - Make folders</li>
                        </ul>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="font-medium text-sm flex items-center gap-2">
                          <Terminal className="h-4 w-4" />
                          Project & Commands
                        </h3>
                        <ul className="space-y-1 text-sm text-muted-foreground">
                          <li>• create_project_nodebox - New projects</li>
                          <li>• get_project_info_nodebox - Project details</li>
                          <li>• run_command_nodebox - Execute commands</li>
                          <li>• search_documentation - Find docs</li>
                          <li>• generate_code_snippet - Code generation</li>
                          <li>• validate_code - Code quality checks</li>
                        </ul>
                      </div>
                      
                      <div className="space-y-3">
                        <h3 className="font-medium text-sm flex items-center gap-2">
                          <Code className="h-4 w-4" />
                          Templates Available
                        </h3>
                        <ul className="space-y-1 text-sm text-muted-foreground">
                          <li>• React - Modern React + TypeScript</li>
                          <li>• Next.js - Full-stack application</li>
                          <li>• Express - Node.js server</li>
                          <li>• TypeScript - Pure TS project</li>
                          <li>• Vanilla JS - HTML/CSS/JS</li>
                          <li>• Research Dashboard - Data viz</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="editor" className="h-full m-0">
              <NodeboxCodeEditorPanel projectId={projectId} />
            </TabsContent>

            <TabsContent value="files" className="h-full m-0">
              <NodeboxFileBrowser 
                projectId={projectId}
                showActions={true}
                className="h-full"
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}
