/**
 * Nodebox Store Usage Examples
 * 
 * This file demonstrates various ways to use the new Nodebox store
 * in your React components and applications.
 */

"use client";

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  useNodeboxIntegration,
  useActiveNodeboxInstance,
  useNodeboxLoading,
  useNodeboxError,
  useNodeboxActions,
  useNodeboxInstances,
  useNodeboxTemplates
} from '@/lib/stores/nodebox-store';
import { ProjectTemplate } from '@/lib/nodebox-runtime/api/nodebox-types';

// Example 1: Simple integration hook usage
export function SimpleNodeboxExample({ projectId }: { projectId: string }) {
  const { 
    activeInstance, 
    isLoading, 
    error, 
    createApp,
    quickRunCommand 
  } = useNodeboxIntegration(projectId);

  const handleCreateReactApp = () => {
    createApp('react', 'My React App');
  };

  const handleRunDevServer = () => {
    quickRunCommand('npm', ['run', 'dev']);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Simple Nodebox Integration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading && <Badge>Loading...</Badge>}
        {error && <Badge variant="destructive">Error: {error.message}</Badge>}
        
        {activeInstance ? (
          <div className="space-y-2">
            <p>Active: {activeInstance.config.name}</p>
            <Button onClick={handleRunDevServer}>Start Dev Server</Button>
          </div>
        ) : (
          <Button onClick={handleCreateReactApp}>Create React App</Button>
        )}
      </CardContent>
    </Card>
  );
}

// Example 2: Using individual selector hooks
export function DetailedNodeboxExample() {
  const instances = useNodeboxInstances();
  const activeInstance = useActiveNodeboxInstance();
  const isLoading = useNodeboxLoading();
  const error = useNodeboxError();
  const templates = useNodeboxTemplates();
  const actions = useNodeboxActions();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Detailed Nodebox Management</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium">Instances ({instances.length})</h4>
          {instances.map(instance => (
            <div key={instance.id} className="flex items-center justify-between p-2 border rounded">
              <span>{instance.config.name}</span>
              <Button 
                size="sm" 
                variant={activeInstance?.id === instance.id ? "default" : "outline"}
                onClick={() => actions.setActiveInstance(instance.id)}
              >
                {activeInstance?.id === instance.id ? "Active" : "Select"}
              </Button>
            </div>
          ))}
        </div>

        <div>
          <h4 className="font-medium">Available Templates</h4>
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(templates).map(([key, template]) => (
              <Button
                key={key}
                variant="outline"
                size="sm"
                onClick={() => actions.createFromTemplate(key as ProjectTemplate, template.name)}
              >
                {template.name}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Example 3: File operations
export function FileOperationsExample() {
  const activeInstance = useActiveNodeboxInstance();
  const actions = useNodeboxActions();
  const [fileContent, setFileContent] = useState('');

  const handleReadPackageJson = async () => {
    if (!activeInstance) return;
    const content = await actions.readFile(activeInstance.id, 'package.json');
    setFileContent(content || 'File not found');
  };

  const handleWriteReadme = async () => {
    if (!activeInstance) return;
    const content = `# ${activeInstance.config.name}\n\nThis is a generated README file.`;
    await actions.writeFile(activeInstance.id, 'README.md', content);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>File Operations</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {activeInstance ? (
          <>
            <div className="flex gap-2">
              <Button onClick={handleReadPackageJson}>Read package.json</Button>
              <Button onClick={handleWriteReadme}>Write README.md</Button>
            </div>
            
            {fileContent && (
              <div className="p-2 bg-muted rounded">
                <pre className="text-xs overflow-auto">{fileContent}</pre>
              </div>
            )}
          </>
        ) : (
          <p className="text-muted-foreground">No active instance</p>
        )}
      </CardContent>
    </Card>
  );
}

// Example 4: Terminal operations
export function TerminalExample() {
  const activeInstance = useActiveNodeboxInstance();
  const actions = useNodeboxActions();
  const [command, setCommand] = useState('npm run build');
  const [output, setOutput] = useState<string[]>([]);

  const handleRunCommand = async () => {
    if (!activeInstance || !command.trim()) return;
    
    const [cmd, ...args] = command.split(' ');
    const process = await actions.runCommand(activeInstance.id, cmd, args);
    
    if (process) {
      setOutput(prev => [...prev, `$ ${command}`, 'Command started...']);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Terminal Operations</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {activeInstance ? (
          <>
            <div className="flex gap-2">
              <input
                type="text"
                value={command}
                onChange={(e) => setCommand(e.target.value)}
                className="flex-1 px-2 py-1 border rounded"
                placeholder="Enter command..."
              />
              <Button onClick={handleRunCommand}>Run</Button>
            </div>
            
            <div className="h-32 p-2 bg-black text-green-400 rounded font-mono text-xs overflow-auto">
              {output.map((line, i) => (
                <div key={i}>{line}</div>
              ))}
            </div>
          </>
        ) : (
          <p className="text-muted-foreground">No active instance</p>
        )}
      </CardContent>
    </Card>
  );
}

// Example 5: Settings management
export function SettingsExample() {
  const actions = useNodeboxActions();
  const activeInstance = useActiveNodeboxInstance();

  const handleUpdateGlobalSettings = () => {
    actions.updateGlobalSettings({
      autoSave: true,
      enableHotReload: true,
      memoryLimit: 1024
    });
  };

  const handleUpdateInstanceSettings = () => {
    if (!activeInstance) return;
    actions.updateInstanceSettings(activeInstance.id, {
      autoPreview: true,
      timeoutMs: 60000
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Settings Management</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={handleUpdateGlobalSettings}>
          Update Global Settings
        </Button>
        
        {activeInstance && (
          <Button onClick={handleUpdateInstanceSettings}>
            Update Instance Settings
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

// Example 6: Complete workspace integration
export function WorkspaceIntegrationExample({ projectId }: { projectId: string }) {
  const {
    activeInstance,
    isLoading,
    error,
    createApp,
    quickRunCommand,
    quickGetPreview,
    quickReadFile,
    quickWriteFile
  } = useNodeboxIntegration(projectId);

  // Auto-create a default app if none exists
  useEffect(() => {
    if (!activeInstance && !isLoading && !error) {
      createApp('nextjs', `Project ${projectId}`);
    }
  }, [activeInstance, isLoading, error, createApp, projectId]);

  const handleQuickStart = async () => {
    if (!activeInstance) return;
    
    // Start the development server
    await quickRunCommand('npm', ['run', 'dev']);
    
    // Get the preview URL
    await quickGetPreview();
    
    // Read the main file
    const content = await quickReadFile('pages/index.tsx');
    console.log('Main file content:', content);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Complete Workspace Integration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading && <Badge>Setting up workspace...</Badge>}
        {error && <Badge variant="destructive">Error: {error.message}</Badge>}
        
        {activeInstance && (
          <div className="space-y-2">
            <p>Workspace ready: {activeInstance.config.name}</p>
            <Button onClick={handleQuickStart}>Quick Start Development</Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Main example component that showcases all examples
export function NodeboxStoreExamples() {
  const projectId = "example-project";

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-3xl font-bold">Nodebox Store Usage Examples</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SimpleNodeboxExample projectId={projectId} />
        <DetailedNodeboxExample />
        <FileOperationsExample />
        <TerminalExample />
        <SettingsExample />
        <WorkspaceIntegrationExample projectId={projectId} />
      </div>
    </div>
  );
}
