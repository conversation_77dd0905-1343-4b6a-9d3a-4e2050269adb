/**
 * AI Agent Nodebox Integration Example
 * 
 * Demonstrates how to integrate AI agents with Nodebox file operations
 * using the enhanced store and tool calling system
 */

"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Bot,
  Code,
  FileText,
  Play,
  Loader2,
  CheckCircle,
  AlertCircle,
  Terminal
} from 'lucide-react';
import { useNodeboxIntegration } from '@/lib/stores/nodebox-store';
import { nodeboxTools, nodeboxToolExecutors } from '@/lib/agents/nodebox-tools';
import { NodeboxCodeEditorPanel } from '@/components/nodebox-code-editor-panel';
import { NodeboxFileBrowser } from '@/components/nodebox-file-browser';

interface AIAgentNodeboxIntegrationProps {
  projectId: string;
}

interface AgentMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  toolCalls?: Array<{
    tool: string;
    params: any;
    result: any;
    success: boolean;
  }>;
}

export function AIAgentNodeboxIntegration({ projectId }: AIAgentNodeboxIntegrationProps) {
  // Nodebox integration
  const {
    activeInstance,
    isLoading,
    error,
    createApp
  } = useNodeboxIntegration(projectId);

  // Chat state
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [input, setInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Example AI agent responses with tool calls
  const simulateAIResponse = async (userMessage: string): Promise<AgentMessage> => {
    setIsProcessing(true);
    
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const messageId = `msg_${Date.now()}`;
    let response: AgentMessage = {
      id: messageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      toolCalls: []
    };

    try {
      // Simple command parsing for demonstration
      if (userMessage.toLowerCase().includes('create') && userMessage.toLowerCase().includes('react')) {
        // Create React app
        const result = await nodeboxToolExecutors.createProject({
          name: 'My React App',
          template: 'react',
          projectId
        });
        
        response.content = 'I\'ve created a new React application for you! The project includes all the necessary files and dependencies.';
        response.toolCalls = [{
          tool: 'create_project_nodebox',
          params: { name: 'My React App', template: 'react', projectId },
          result,
          success: true
        }];

      } else if (userMessage.toLowerCase().includes('read') && userMessage.includes('/')) {
        // Read file
        const pathMatch = userMessage.match(/\/[^\s]+/);
        if (pathMatch) {
          const filePath = pathMatch[0];
          try {
            const result = await nodeboxToolExecutors.readFile({ path: filePath });
            response.content = `Here's the content of ${filePath}:\n\n\`\`\`\n${result.content}\n\`\`\``;
            response.toolCalls = [{
              tool: 'read_file_nodebox',
              params: { path: filePath },
              result,
              success: true
            }];
          } catch (error) {
            response.content = `I couldn't read the file ${filePath}. It might not exist or there was an error accessing it.`;
            response.toolCalls = [{
              tool: 'read_file_nodebox',
              params: { path: filePath },
              result: { error: error instanceof Error ? error.message : String(error) },
              success: false
            }];
          }
        }

      } else if (userMessage.toLowerCase().includes('write') || userMessage.toLowerCase().includes('create file')) {
        // Create/write file
        const pathMatch = userMessage.match(/\/[^\s]+/);
        if (pathMatch) {
          const filePath = pathMatch[0];
          const content = `// Generated file: ${filePath}
// Created by AI Agent

export default function Component() {
  return (
    <div>
      <h1>Hello from ${filePath}!</h1>
      <p>This file was created by an AI agent.</p>
    </div>
  );
}`;

          try {
            const result = await nodeboxToolExecutors.writeFile({ path: filePath, content });
            response.content = `I've created the file ${filePath} with some starter content. You can now edit it in the code editor.`;
            response.toolCalls = [{
              tool: 'write_file_nodebox',
              params: { path: filePath, content },
              result,
              success: true
            }];
          } catch (error) {
            response.content = `I couldn't create the file ${filePath}. There was an error: ${error instanceof Error ? error.message : String(error)}`;
            response.toolCalls = [{
              tool: 'write_file_nodebox',
              params: { path: filePath, content },
              result: { error: error instanceof Error ? error.message : String(error) },
              success: false
            }];
          }
        }

      } else if (userMessage.toLowerCase().includes('list files')) {
        // List files
        try {
          const result = await nodeboxToolExecutors.listFiles({ path: '/' });
          const fileList = result.files.map((f: any) => `- ${f.name} (${f.type})`).join('\n');
          response.content = `Here are the files in your project:\n\n${fileList}`;
          response.toolCalls = [{
            tool: 'list_files_nodebox',
            params: { path: '/' },
            result,
            success: true
          }];
        } catch (error) {
          response.content = `I couldn't list the files. Error: ${error instanceof Error ? error.message : String(error)}`;
          response.toolCalls = [{
            tool: 'list_files_nodebox',
            params: { path: '/' },
            result: { error: error instanceof Error ? error.message : String(error) },
            success: false
          }];
        }

      } else if (userMessage.toLowerCase().includes('run') && userMessage.toLowerCase().includes('dev')) {
        // Run dev server
        try {
          const result = await nodeboxToolExecutors.runCommand({ command: 'npm', args: ['run', 'dev'] });
          response.content = 'I\'ve started the development server for you! You should see it running in the terminal.';
          response.toolCalls = [{
            tool: 'run_command_nodebox',
            params: { command: 'npm', args: ['run', 'dev'] },
            result,
            success: true
          }];
        } catch (error) {
          response.content = `I couldn't start the dev server. Error: ${error instanceof Error ? error.message : String(error)}`;
          response.toolCalls = [{
            tool: 'run_command_nodebox',
            params: { command: 'npm', args: ['run', 'dev'] },
            result: { error: error instanceof Error ? error.message : String(error) },
            success: false
          }];
        }

      } else if (userMessage.toLowerCase().includes('project info')) {
        // Get project info
        try {
          const result = await nodeboxToolExecutors.getProjectInfo({});
          response.content = `Here's information about your current project:

**Instance:** ${result.instance.name}
**Template:** ${result.instance.template}
**Status:** ${result.instance.status}
**Files:** ${result.fileCount}
**Processes:** ${result.processCount}
**Previews:** ${result.previewCount}
**Created:** ${new Date(result.instance.createdAt).toLocaleString()}`;
          
          response.toolCalls = [{
            tool: 'get_project_info_nodebox',
            params: {},
            result,
            success: true
          }];
        } catch (error) {
          response.content = `I couldn't get project information. Error: ${error instanceof Error ? error.message : String(error)}`;
          response.toolCalls = [{
            tool: 'get_project_info_nodebox',
            params: {},
            result: { error: error instanceof Error ? error.message : String(error) },
            success: false
          }];
        }

      } else {
        // Default response with available commands
        response.content = `I can help you with your Nodebox project! Here are some things you can ask me to do:

🚀 **Project Management:**
- "Create a React app"
- "Create a Next.js app"
- "Get project info"

📁 **File Operations:**
- "List files"
- "Read /src/App.tsx"
- "Create file /src/components/Button.tsx"
- "Write to /README.md"

⚡ **Development:**
- "Run dev server"
- "Run build"
- "Install dependencies"

Just ask me naturally, and I'll use the appropriate tools to help you!`;
      }

    } catch (error) {
      response.content = `I encountered an error: ${error instanceof Error ? error.message : String(error)}`;
    }

    setIsProcessing(false);
    return response;
  };

  // Handle sending message
  const handleSendMessage = async () => {
    if (!input.trim()) return;

    const userMessage: AgentMessage = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');

    // Get AI response
    const aiResponse = await simulateAIResponse(input);
    setMessages(prev => [...prev, aiResponse]);
  };

  // Handle quick actions
  const handleQuickAction = async (action: string) => {
    setInput(action);
    await handleSendMessage();
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bot className="h-6 w-6 text-primary" />
            <h1 className="text-xl font-semibold">AI Agent + Nodebox Integration</h1>
          </div>
          
          {activeInstance && (
            <Badge variant="outline" className="gap-1">
              <CheckCircle className="h-3 w-3" />
              {activeInstance.config.name}
            </Badge>
          )}
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Chat Panel */}
        <div className="w-1/3 border-r flex flex-col">
          <div className="p-4 border-b">
            <h2 className="font-medium flex items-center gap-2">
              <Bot className="h-4 w-4" />
              AI Assistant
            </h2>
          </div>

          {/* Messages */}
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              {messages.map(message => (
                <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[80%] rounded-lg p-3 ${
                    message.role === 'user' 
                      ? 'bg-primary text-primary-foreground' 
                      : 'bg-muted'
                  }`}>
                    <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                    
                    {/* Tool calls */}
                    {message.toolCalls && message.toolCalls.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {message.toolCalls.map((call, index) => (
                          <div key={index} className="text-xs opacity-75 flex items-center gap-1">
                            {call.success ? (
                              <CheckCircle className="h-3 w-3 text-green-500" />
                            ) : (
                              <AlertCircle className="h-3 w-3 text-red-500" />
                            )}
                            <code>{call.tool}</code>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    <div className="text-xs opacity-50 mt-1">
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
              
              {isProcessing && (
                <div className="flex justify-start">
                  <div className="bg-muted rounded-lg p-3 flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm">AI is thinking...</span>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Input */}
          <div className="p-4 border-t space-y-2">
            {/* Quick Actions */}
            <div className="flex flex-wrap gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction('Create a React app')}
                disabled={isProcessing}
              >
                <Code className="h-3 w-3 mr-1" />
                Create React App
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction('List files')}
                disabled={isProcessing}
              >
                <FileText className="h-3 w-3 mr-1" />
                List Files
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction('Run dev server')}
                disabled={isProcessing}
              >
                <Play className="h-3 w-3 mr-1" />
                Run Dev
              </Button>
            </div>

            <div className="flex gap-2">
              <Textarea
                placeholder="Ask me to help with your project..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                className="min-h-[60px]"
                disabled={isProcessing}
              />
              <Button 
                onClick={handleSendMessage} 
                disabled={!input.trim() || isProcessing}
                className="self-end"
              >
                {isProcessing ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  'Send'
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* File Browser */}
        <div className="w-1/4 border-r">
          <NodeboxFileBrowser projectId={projectId} />
        </div>

        {/* Code Editor */}
        <div className="flex-1">
          <NodeboxCodeEditorPanel projectId={projectId} />
        </div>
      </div>
    </div>
  );
}
