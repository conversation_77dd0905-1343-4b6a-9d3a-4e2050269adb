# Desktop VM with Guacamole Integration
# 
# Complete setup with ultra-lightweight Desktop VM + Guacamole web interface

version: '3.8'

services:
  # Ultra-lightweight Desktop VM
  desktop-vm:
    build:
      context: .
      dockerfile: Dockerfile.minimal
    container_name: desktop-vm-guac
    hostname: desktop-vm
    restart: unless-stopped
    environment:
      - DISPLAY=:1
      - VNC_PORT=5901
      - RESOLUTION=1280x720
      - USER=desktop
      - TZ=UTC
    ports:
      - "5901:5901"   # VNC port (for direct access)
    volumes:
      - desktop_home:/home/<USER>
    networks:
      - guacamole-network
    security_opt:
      - seccomp:unconfined
    cap_add:
      - SYS_ADMIN
    shm_size: 256m
    mem_limit: 512m
    cpus: 1.0
    healthcheck:
      test: ["CMD", "pgrep", "Xvfb"]
      interval: 30s
      timeout: 5s
      retries: 2
      start_period: 10s

  # Guacamole Database
  guacamole-db:
    image: postgres:15-alpine
    container_name: guacamole-db
    hostname: guacamole-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: guacamole_db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: guacamole_pass
    volumes:
      - guacamole_db:/var/lib/postgresql/data
      - ./init/initdb.sql:/docker-entrypoint-initdb.d/initdb.sql:ro
    networks:
      - guacamole-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U guacamole_user -d guacamole_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Guacamole Daemon (guacd)
  guacd:
    image: guacamole/guacd:latest
    container_name: guacd
    hostname: guacd
    restart: unless-stopped
    networks:
      - guacamole-network
    volumes:
      - guacamole_drive:/drive
      - guacamole_record:/record
    healthcheck:
      test: ["CMD", "nc", "-z", "127.0.0.1", "4822"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Guacamole Web Application
  guacamole:
    image: guacamole/guacamole:latest
    container_name: guacamole
    hostname: guacamole
    restart: unless-stopped
    environment:
      # Database configuration
      POSTGRES_DATABASE: guacamole_db
      POSTGRES_HOSTNAME: guacamole-db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: guacamole_pass
      # Guacd configuration
      GUACD_HOSTNAME: guacd
      GUACD_PORT: 4822
      # Optional: LDAP, SAML, etc.
      GUACAMOLE_HOME: /etc/guacamole
    ports:
      - "8080:8080"   # Guacamole web interface
    volumes:
      - guacamole_config:/etc/guacamole
    networks:
      - guacamole-network
    depends_on:
      guacamole-db:
        condition: service_healthy
      guacd:
        condition: service_healthy
      desktop-vm:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/guacamole/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx Reverse Proxy (Optional - for custom domain/SSL)
  nginx:
    image: nginx:alpine
    container_name: guacamole-nginx
    hostname: nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - guacamole-network
    depends_on:
      - guacamole
    profiles:
      - nginx  # Only start with --profile nginx

volumes:
  desktop_home:
    driver: local
  guacamole_db:
    driver: local
  guacamole_config:
    driver: local
  guacamole_drive:
    driver: local
  guacamole_record:
    driver: local

networks:
  guacamole-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: br-guacamole
