# Docker ignore file for Desktop VM
# Excludes unnecessary files from Docker build context

# Documentation
README.md
*.md
docs/

# Git
.git/
.gitignore
.gitattributes

# Docker files
docker-compose.yml
docker-compose.*.yml
Dockerfile.*
.dockerignore

# Build scripts and tools
build.sh
*.sh
Makefile

# Environment and configuration
.env
.env.*
*.env

# Logs
*.log
logs/
log/

# Temporary files
tmp/
temp/
.tmp/

# Cache directories
.cache/
cache/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if any)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# Volume directories (will be mounted)
volumes/
data/

# SSL certificates
*.pem
*.key
*.crt
*.cert

# Archives
*.tar
*.tar.gz
*.tgz
*.zip
*.rar

# Test files
test/
tests/
*_test.go
*_test.py
*.test

# Coverage reports
coverage/
*.cover
.coverage

# Nginx config (will be mounted)
nginx/

# Database init files (will be mounted)
init/

# Runtime files
*.pid
*.sock
