# Ultra-Minimal Desktop VM
# 
# Optimized for fastest possible startup time
# Based on Alpine Linux with absolute minimal components

FROM alpine:3.18

# Environment variables
ENV DISPLAY=:1 \
    VNC_PORT=5901 \
    NOVNC_PORT=6080 \
    RESOLUTION=1024x768 \
    USER=desktop \
    HOME=/home/<USER>

# Install only essential packages in single layer
RUN apk add --no-cache \
    # Core system
    bash \
    dbus \
    # X11 minimal
    xvfb \
    x11vnc \
    # Minimal window manager (lighter than XFCE)
    openbox \
    # Essential tools
    xterm \
    xsetroot \
    netcat-openbsd \
    procps \
    # Python for websockify
    python3 \
    py3-pip \
    # Cleanup
    && rm -rf /var/cache/apk/* /tmp/* \
    # Install websockify for web VNC access
    && pip3 install --no-cache-dir websockify

# Create user
RUN adduser -D -s /bin/bash $USER \
    && echo "$USER:$USER" | chpasswd

# Setup minimal environment
USER $USER
WORKDIR $HOME

# Create minimal directories and openbox config
RUN mkdir -p .vnc Desktop .config/openbox && \
    echo '<?xml version="1.0" encoding="UTF-8"?>' > .config/openbox/rc.xml && \
    echo '<openbox_config xmlns="http://openbox.org/3.4/rc">' >> .config/openbox/rc.xml && \
    echo '  <theme><name>Clearlooks</name></theme>' >> .config/openbox/rc.xml && \
    echo '  <desktops><number>1</number></desktops>' >> .config/openbox/rc.xml && \
    echo '</openbox_config>' >> .config/openbox/rc.xml && \
    chown -R $USER:$USER .config

# Create startup script with VNC and WebSocket support
USER root
RUN echo '#!/bin/bash' > /start.sh && \
    echo 'set -e' >> /start.sh && \
    echo 'echo "🚀 Starting minimal desktop with WebSocket VNC..."' >> /start.sh && \
    echo 'dbus-daemon --system --fork 2>/dev/null || true' >> /start.sh && \
    echo 'echo "🖥️  Starting Xvfb on $DISPLAY..."' >> /start.sh && \
    echo 'Xvfb $DISPLAY -screen 0 ${RESOLUTION}x24 -ac +extension GLX +render -noreset &' >> /start.sh && \
    echo 'sleep 3' >> /start.sh && \
    echo 'if ! pgrep Xvfb > /dev/null; then echo "❌ Failed to start Xvfb"; exit 1; fi' >> /start.sh && \
    echo 'echo "✅ Xvfb started successfully"' >> /start.sh && \
    echo 'echo "🔗 Starting x11vnc on port $VNC_PORT..."' >> /start.sh && \
    echo 'x11vnc -display $DISPLAY -nopw -forever -shared -rfbport $VNC_PORT &' >> /start.sh && \
    echo 'sleep 3' >> /start.sh && \
    echo 'if ! pgrep x11vnc > /dev/null; then echo "❌ Failed to start x11vnc"; exit 1; fi' >> /start.sh && \
    echo 'echo "✅ x11vnc started successfully"' >> /start.sh && \
    echo 'echo "🌐 Starting websockify on port ${WEBSOCKET_PORT:-6080}..."' >> /start.sh && \
    echo 'websockify ${WEBSOCKET_PORT:-6080} localhost:$VNC_PORT &' >> /start.sh && \
    echo 'sleep 3' >> /start.sh && \
    echo 'if ! pgrep websockify > /dev/null; then echo "❌ Failed to start websockify"; exit 1; fi' >> /start.sh && \
    echo 'echo "✅ websockify started successfully"' >> /start.sh && \
    echo 'echo "🪟 Starting desktop environment..."' >> /start.sh && \
    echo 'export HOME=/home/<USER>' >> /start.sh && \
    echo 'export USER=desktop' >> /start.sh && \
    echo 'cd /home/<USER>' >> /start.sh && \
    echo 'DISPLAY=$DISPLAY xsetroot -solid "#2E3440" &' >> /start.sh && \
    echo 'sleep 1' >> /start.sh && \
    echo 'DISPLAY=$DISPLAY openbox &' >> /start.sh && \
    echo 'sleep 2' >> /start.sh && \
    echo 'DISPLAY=$DISPLAY xterm -geometry 80x24+100+100 -title "Desktop VM Terminal" -bg black -fg green &' >> /start.sh && \
    echo 'DISPLAY=$DISPLAY xterm -geometry 60x20+300+200 -title "Welcome" -bg blue -fg white -e "echo Welcome to Desktop VM!; echo Right-click for menu; bash" &' >> /start.sh && \
    echo 'sleep 1' >> /start.sh && \
    echo 'echo "🎉 Desktop ready!"' >> /start.sh && \
    echo 'echo "   VNC Port: $VNC_PORT"' >> /start.sh && \
    echo 'echo "   WebSocket Port: ${WEBSOCKET_PORT:-6080}"' >> /start.sh && \
    echo 'echo "   Resolution: $RESOLUTION"' >> /start.sh && \
    echo 'tail -f /dev/null' >> /start.sh && \
    chmod +x /start.sh

# Expose ports
EXPOSE 5901 6080

# Ultra-fast health check
HEALTHCHECK --interval=15s --timeout=3s --start-period=5s --retries=1 \
    CMD pgrep Xvfb > /dev/null

# Switch to desktop user and start
USER $USER
CMD ["/start.sh"]
