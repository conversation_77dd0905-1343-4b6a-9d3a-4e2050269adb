#!/bin/bash

# Guacamole Desktop VM Setup Script
# 
# Sets up the complete Guacamole + Desktop VM environment

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
PROJECT_NAME="desktop-vm-guacamole"
COMPOSE_FILE="docker-compose.guacamole.yml"

# Show usage
usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     Initial setup and build"
    echo "  start     Start all services"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  logs      Show logs"
    echo "  status    Show status"
    echo "  clean     Clean up everything"
    echo "  rebuild   Rebuild and restart"
    echo ""
}

# Check prerequisites
check_prereqs() {
    if ! command -v docker &> /dev/null; then
        error "Docker not found"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null || ! docker compose version &> /dev/null; then
        if ! command -v docker-compose &> /dev/null; then
            error "Docker Compose not found (tried both 'docker compose' and 'docker-compose')"
            exit 1
        else
            DOCKER_COMPOSE="docker-compose"
        fi
    else
        DOCKER_COMPOSE="docker compose"
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker not running"
        exit 1
    fi
}

# Initial setup
setup() {
    log "Setting up Guacamole Desktop VM environment..."
    
    # Create necessary directories
    mkdir -p init nginx/ssl
    
    # Build the minimal desktop VM image
    log "Building ultra-minimal Desktop VM image..."
    docker build -f Dockerfile.minimal -t desktop-vm-minimal:latest .
    
    # Download Guacamole database schema if not exists
    if [ ! -f "init/guacamole-schema.sql" ]; then
        log "Downloading Guacamole database schema..."
        curl -L -o init/guacamole-schema.sql \
            "https://raw.githubusercontent.com/apache/guacamole-server/master/src/protocols/vnc/guacamole-auth-jdbc-postgresql-1.5.3.sql" \
            || warning "Failed to download schema, using minimal schema"
    fi
    
    success "Setup completed successfully!"
}

# Start services
start() {
    log "Starting Guacamole Desktop VM services..."
    
    # Start all services
    $DOCKER_COMPOSE -f $COMPOSE_FILE up -d

    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 10

    # Check if services are running
    if $DOCKER_COMPOSE -f $COMPOSE_FILE ps | grep -q "Up"; then
        success "Services started successfully!"
        show_access_info
    else
        error "Some services failed to start"
        $DOCKER_COMPOSE -f $COMPOSE_FILE logs
        exit 1
    fi
}

# Stop services
stop() {
    log "Stopping services..."
    $DOCKER_COMPOSE -f $COMPOSE_FILE down
    success "Services stopped"
}

# Restart services
restart() {
    log "Restarting services..."
    stop
    sleep 2
    start
}

# Show logs
logs() {
    $DOCKER_COMPOSE -f $COMPOSE_FILE logs -f
}

# Show status
status() {
    echo "=== Service Status ==="
    $DOCKER_COMPOSE -f $COMPOSE_FILE ps

    echo ""
    echo "=== Resource Usage ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

    echo ""
    echo "=== Health Checks ==="
    $DOCKER_COMPOSE -f $COMPOSE_FILE ps --format "table {{.Name}}\t{{.Status}}"
}

# Clean up
clean() {
    log "Cleaning up..."
    $DOCKER_COMPOSE -f $COMPOSE_FILE down -v --remove-orphans
    docker rmi desktop-vm-minimal:latest 2>/dev/null || true
    docker system prune -f
    success "Cleanup completed"
}

# Rebuild
rebuild() {
    log "Rebuilding..."
    stop
    $DOCKER_COMPOSE -f $COMPOSE_FILE build --no-cache
    start
}

# Show access information
show_access_info() {
    echo ""
    echo "🌐 Guacamole Desktop VM Access:"
    echo "   Web Interface:  http://localhost:8080/guacamole/"
    echo "   Username:       guacadmin"
    echo "   Password:       guacadmin"
    echo ""
    echo "🖥️  Direct VNC Access:"
    echo "   VNC Client:     vnc://localhost:5901"
    echo "   No password required"
    echo ""
    echo "🔧 Management:"
    echo "   Nginx Proxy:    http://localhost (optional)"
    echo "   Database:       PostgreSQL on internal network"
    echo ""
    echo "📊 Monitoring:"
    echo "   Status:         ./setup-guacamole.sh status"
    echo "   Logs:           ./setup-guacamole.sh logs"
    echo ""
}

# Test connection
test_connection() {
    log "Testing Guacamole connection..."
    
    # Wait for Guacamole to be ready
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/guacamole/ >/dev/null 2>&1; then
            success "Guacamole is accessible!"
            break
        else
            log "Attempt $attempt/$max_attempts - waiting for Guacamole..."
            sleep 2
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        error "Guacamole failed to become accessible"
        return 1
    fi
    
    # Test VNC connection
    if nc -z localhost 5901 2>/dev/null; then
        success "VNC server is accessible!"
    else
        warning "VNC server is not accessible"
    fi
}

# Quick start
quick_start() {
    log "Quick start - setting up everything..."
    
    check_prereqs
    setup
    start
    test_connection
    
    success "Guacamole Desktop VM is ready!"
    show_access_info
}

# Main function
main() {
    case "${1:-}" in
        setup)
            check_prereqs
            setup
            ;;
        start)
            check_prereqs
            start
            ;;
        stop)
            stop
            ;;
        restart)
            restart
            ;;
        logs)
            logs
            ;;
        status)
            status
            ;;
        clean)
            clean
            ;;
        rebuild)
            check_prereqs
            rebuild
            ;;
        test)
            test_connection
            ;;
        quick)
            quick_start
            ;;
        *)
            usage
            ;;
    esac
}

main "$@"
