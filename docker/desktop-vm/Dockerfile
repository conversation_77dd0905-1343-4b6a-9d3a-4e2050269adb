# Lightweight Desktop VM Docker Image
#
# Optimized for fast startup and minimal resource usage
# Uses Alpine Linux with minimal XFCE components

FROM alpine:3.18

# Set up environment variables
ENV DISPLAY=:1
ENV VNC_PORT=5901
ENV NOVNC_PORT=6080
ENV RESOLUTION=1280x720
ENV VNC_PASSWORD=vncpassword
ENV USER=desktop
ENV HOME=/home/<USER>
ENV TZ=UTC

# Install minimal system dependencies
RUN apk update && apk add --no-cache \
    # Core system
    bash \
    curl \
    wget \
    ca-certificates \
    tzdata \
    # X11 and VNC (minimal)
    xvfb \
    x11vnc \
    dbus \
    # Minimal XFCE components
    xfce4 \
    xfce4-terminal \
    thunar \
    mousepad \
    # Essential tools
    firefox \
    git \
    nano \
    # Node.js (minimal)
    nodejs \
    npm \
    # Python (minimal)
    python3 \
    py3-pip \
    # Networking
    net-tools \
    # Cleanup
    && rm -rf /var/cache/apk/* \
    # Install websockify via pip
    && pip3 install --no-cache-dir websockify

# Create desktop user (Alpine style)
RUN addgroup -g 1000 $USER \
    && adduser -D -s /bin/bash -u 1000 -G $USER $USER \
    && echo "$USER:$USER" | chpasswd \
    && mkdir -p /etc/sudoers.d \
    && echo "$USER ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/$USER

# Set up minimal VNC configuration
USER $USER
WORKDIR $HOME

# Create essential directories
RUN mkdir -p $HOME/.vnc $HOME/Desktop $HOME/shared

# Create minimal VNC startup script
RUN echo '#!/bin/bash\nexport DISPLAY=:1\nstartxfce4 &' > $HOME/.vnc/xstartup \
    && chmod +x $HOME/.vnc/xstartup

# Switch back to root for startup script
USER root

# Create startup scripts directory
RUN mkdir -p /opt/desktop-vm

# Create lightweight startup script
RUN echo '#!/bin/bash\n\
echo "Starting lightweight desktop VM..."\n\
\n\
# Start D-Bus\n\
dbus-daemon --system --fork || true\n\
\n\
# Start Xvfb\n\
Xvfb $DISPLAY -screen 0 ${RESOLUTION}x24 -ac &\n\
sleep 1\n\
\n\
# Start x11vnc\n\
x11vnc -display $DISPLAY -nopw -forever -shared -rfbport $VNC_PORT &\n\
sleep 1\n\
\n\
# Start NoVNC\n\
websockify $NOVNC_PORT localhost:$VNC_PORT &\n\
sleep 1\n\
\n\
# Start XFCE as user\n\
su - $USER -c "DISPLAY=$DISPLAY startxfce4" &\n\
\n\
echo "Desktop VM ready on VNC port $VNC_PORT and NoVNC port $NOVNC_PORT"\n\
\n\
# Keep container running\n\
tail -f /dev/null\n\
' > /opt/desktop-vm/start.sh \
    && chmod +x /opt/desktop-vm/start.sh

# Simple health check
RUN echo '#!/bin/bash\npgrep -f "Xvfb.*$DISPLAY" > /dev/null' > /opt/desktop-vm/health.sh \
    && chmod +x /opt/desktop-vm/health.sh

# Set ownership
RUN chown -R $USER:$USER /home/<USER>

# Expose ports
EXPOSE $VNC_PORT $NOVNC_PORT

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=2 \
    CMD /opt/desktop-vm/health.sh

# Start the desktop environment
CMD ["/opt/desktop-vm/start.sh"]
