-- Guacamole Database Initialization
-- This script sets up the initial database schema and default connection

-- Download and apply the official Guacamole schema
-- Note: In production, you should download the actual schema from:
-- https://raw.githubusercontent.com/apache/guacamole-server/master/src/protocols/vnc/guacamole-auth-jdbc-postgresql-1.5.3.sql

-- For now, we'll create a minimal schema that works with our setup
-- This is a simplified version - in production use the official schema

-- Create the database schema
CREATE SCHEMA IF NOT EXISTS guacamole;

-- Users table
CREATE TABLE IF NOT EXISTS guacamole_user (
    user_id SERIAL PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(128) UNIQUE NOT NULL,
    password_hash BYTEA NOT NULL,
    password_salt BYTEA,
    password_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    disabled BOOLEAN DEFAULT FALSE,
    expired BOOLEAN DEFAULT FALSE,
    access_window_start TIME,
    access_window_end TIME,
    valid_from DATE,
    valid_until DATE,
    timezone VARCHAR(64),
    full_name <PERSON><PERSON><PERSON><PERSON>(256),
    email_address VARCHAR(256),
    organization VARCHAR(256),
    organizational_role VARCHAR(256)
);

-- Connections table
CREATE TABLE IF NOT EXISTS guacamole_connection (
    connection_id SERIAL PRIMARY KEY,
    connection_name VARCHAR(128) NOT NULL,
    parent_id INTEGER REFERENCES guacamole_connection(connection_id) ON DELETE CASCADE,
    protocol VARCHAR(32) NOT NULL,
    proxy_hostname VARCHAR(512),
    proxy_port INTEGER,
    proxy_encryption_method VARCHAR(4),
    max_connections INTEGER,
    max_connections_per_user INTEGER,
    connection_weight INTEGER,
    failover_only BOOLEAN DEFAULT FALSE
);

-- Connection parameters table
CREATE TABLE IF NOT EXISTS guacamole_connection_parameter (
    connection_id INTEGER NOT NULL REFERENCES guacamole_connection(connection_id) ON DELETE CASCADE,
    parameter_name VARCHAR(128) NOT NULL,
    parameter_value VARCHAR(4096),
    PRIMARY KEY (connection_id, parameter_name)
);

-- User permissions table
CREATE TABLE IF NOT EXISTS guacamole_connection_permission (
    user_id INTEGER NOT NULL REFERENCES guacamole_user(user_id) ON DELETE CASCADE,
    connection_id INTEGER NOT NULL REFERENCES guacamole_connection(connection_id) ON DELETE CASCADE,
    permission VARCHAR(10) NOT NULL,
    PRIMARY KEY (user_id, connection_id, permission)
);

-- Insert default admin user (username: guacadmin, password: guacadmin)
-- Password hash for 'guacadmin' with salt
INSERT INTO guacamole_user (username, password_hash, password_salt) 
VALUES (
    'guacadmin',
    decode('CA458A7D494E3BE824F5E1E175A1556C0F8EEF2C2D7DF3633BEC4A29C4411960', 'hex'),
    decode('FE24ADC5E11E2B25288D1704ABE67A79E342ECC26064CE69C5B3177795A82264', 'hex')
) ON CONFLICT (username) DO NOTHING;

-- Insert default Desktop VM connection
INSERT INTO guacamole_connection (connection_name, protocol) 
VALUES ('Desktop VM', 'vnc')
ON CONFLICT DO NOTHING;

-- Get the connection ID for the Desktop VM
DO $$
DECLARE
    conn_id INTEGER;
    user_id INTEGER;
BEGIN
    -- Get connection ID
    SELECT connection_id INTO conn_id FROM guacamole_connection WHERE connection_name = 'Desktop VM';
    
    -- Get user ID
    SELECT guacamole_user.user_id INTO user_id FROM guacamole_user WHERE username = 'guacadmin';
    
    -- Insert connection parameters for Desktop VM
    INSERT INTO guacamole_connection_parameter (connection_id, parameter_name, parameter_value) VALUES
    (conn_id, 'hostname', 'desktop-vm'),
    (conn_id, 'port', '5901'),
    (conn_id, 'password', ''),
    (conn_id, 'width', '1280'),
    (conn_id, 'height', '720'),
    (conn_id, 'color-depth', '24'),
    (conn_id, 'cursor', 'remote'),
    (conn_id, 'autoretry', '5'),
    (conn_id, 'read-only', 'false'),
    (conn_id, 'swap-red-blue', 'false'),
    (conn_id, 'dest-host', 'desktop-vm'),
    (conn_id, 'dest-port', '5901')
    ON CONFLICT (connection_id, parameter_name) DO NOTHING;
    
    -- Grant admin user permission to use the connection
    INSERT INTO guacamole_connection_permission (user_id, connection_id, permission) VALUES
    (user_id, conn_id, 'READ'),
    (user_id, conn_id, 'UPDATE'),
    (user_id, conn_id, 'DELETE'),
    (user_id, conn_id, 'ADMINISTER')
    ON CONFLICT (user_id, connection_id, permission) DO NOTHING;
END $$;
