#!/bin/bash

# Lightweight Desktop VM Build Script
# 
# Optimized for fast startup and minimal resource usage

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# Configuration
PROJECT_NAME="desktop-vm-light"
COMPOSE_FILE="docker-compose.lightweight.yml"

log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage
usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build     Build lightweight image"
    echo "  start     Start desktop VM only"
    echo "  start-all Start with Guacamole"
    echo "  stop      Stop all services"
    echo "  logs      Show logs"
    echo "  clean     Clean up"
    echo "  status    Show status"
    echo ""
}

# Check prerequisites
check_prereqs() {
    if ! command -v docker &> /dev/null; then
        error "Docker not found"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker not running"
        exit 1
    fi
}

# Build image
build() {
    log "Building lightweight desktop VM image..."
    docker build -t ${PROJECT_NAME}:latest .
    success "Image built successfully"
}

# Start desktop VM only
start() {
    log "Starting lightweight desktop VM..."
    docker-compose -f $COMPOSE_FILE up -d desktop-vm
    
    # Wait for startup
    log "Waiting for desktop to be ready..."
    sleep 5
    
    # Check if running
    if docker-compose -f $COMPOSE_FILE ps desktop-vm | grep -q "Up"; then
        success "Desktop VM started successfully!"
        show_access_info
    else
        error "Failed to start desktop VM"
        exit 1
    fi
}

# Start with Guacamole
start_all() {
    log "Starting desktop VM with Guacamole..."
    docker-compose -f $COMPOSE_FILE --profile guacamole up -d
    
    log "Waiting for services to be ready..."
    sleep 10
    
    success "All services started!"
    show_access_info_full
}

# Stop services
stop() {
    log "Stopping services..."
    docker-compose -f $COMPOSE_FILE down
    success "Services stopped"
}

# Show logs
logs() {
    docker-compose -f $COMPOSE_FILE logs -f desktop-vm
}

# Clean up
clean() {
    log "Cleaning up..."
    docker-compose -f $COMPOSE_FILE down -v --remove-orphans
    docker rmi ${PROJECT_NAME}:latest 2>/dev/null || true
    docker system prune -f
    success "Cleanup complete"
}

# Show status
status() {
    echo "=== Service Status ==="
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    echo "=== Resource Usage ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
}

# Show access information
show_access_info() {
    echo ""
    echo "🖥️  Desktop VM Access:"
    echo "   VNC Client:  vnc://localhost:5901"
    echo "   NoVNC Web:   http://localhost:6080"
    echo "   Password:    vncpassword"
    echo ""
}

show_access_info_full() {
    show_access_info
    echo "🌐 Guacamole:"
    echo "   Web UI:      http://localhost:8080/guacamole"
    echo "   Username:    guacadmin"
    echo "   Password:    guacadmin"
    echo ""
}

# Quick setup
quick_setup() {
    log "Quick setup for lightweight desktop VM..."
    
    # Create minimal volume directory
    mkdir -p volumes/desktop_home
    chmod 755 volumes/desktop_home
    
    # Create minimal .env
    cat > .env << EOF
DESKTOP_VM_RESOLUTION=1280x720
DESKTOP_VM_VNC_PASSWORD=vncpassword
DESKTOP_VM_USER=desktop
TZ=UTC
EOF
    
    success "Quick setup complete"
}

# Main function
main() {
    case "${1:-}" in
        build)
            check_prereqs
            build
            ;;
        start)
            check_prereqs
            start
            ;;
        start-all)
            check_prereqs
            start_all
            ;;
        stop)
            stop
            ;;
        logs)
            logs
            ;;
        clean)
            clean
            ;;
        status)
            status
            ;;
        setup)
            quick_setup
            ;;
        quick)
            check_prereqs
            quick_setup
            build
            start
            ;;
        *)
            usage
            ;;
    esac
}

main "$@"
