#!/bin/bash

# Desktop VM Optimization Script
# 
# Applies all performance optimizations for fastest startup

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[OPTIMIZE]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# System optimization
optimize_system() {
    log "Optimizing system for Docker performance..."
    
    # Check if running as root for system optimizations
    if [[ $EUID -eq 0 ]]; then
        # Increase file descriptor limits
        echo "* soft nofile 65536" >> /etc/security/limits.conf
        echo "* hard nofile 65536" >> /etc/security/limits.conf
        
        # Optimize kernel parameters
        cat >> /etc/sysctl.conf << EOF
# Docker optimizations
vm.max_map_count=262144
net.core.somaxconn=65535
net.ipv4.ip_local_port_range=1024 65535
net.ipv4.tcp_tw_reuse=1
net.ipv4.tcp_fin_timeout=30
EOF
        
        # Apply sysctl changes
        sysctl -p
        
        success "System optimizations applied"
    else
        warning "Run as root for system-level optimizations"
    fi
}

# Docker optimization
optimize_docker() {
    log "Optimizing Docker configuration..."
    
    # Create Docker daemon configuration
    local docker_config="/etc/docker/daemon.json"
    
    if [[ $EUID -eq 0 ]]; then
        cat > $docker_config << EOF
{
  "storage-driver": "overlay2",
  "storage-opts": [
    "overlay2.override_kernel_check=true"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "default-runtime": "runc",
  "runtimes": {
    "runc": {
      "path": "runc"
    }
  },
  "experimental": false,
  "features": {
    "buildkit": true
  },
  "builder": {
    "gc": {
      "enabled": true,
      "defaultKeepStorage": "20GB"
    }
  }
}
EOF
        
        # Restart Docker to apply changes
        systemctl restart docker
        success "Docker optimizations applied"
    else
        warning "Run as root to optimize Docker daemon"
    fi
}

# Build optimization
optimize_build() {
    log "Optimizing build process..."
    
    # Enable BuildKit
    export DOCKER_BUILDKIT=1
    export BUILDKIT_PROGRESS=plain
    
    # Create optimized .dockerignore
    cat > .dockerignore << EOF
# Optimization: Exclude everything not needed for build
**
!Dockerfile*
!scripts/
!*.sh
EOF
    
    # Create build cache directory
    mkdir -p .build-cache
    
    success "Build optimizations configured"
}

# Image optimization
optimize_image() {
    log "Building optimized images..."
    
    # Build minimal image with all optimizations
    docker build \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --cache-from desktop-vm-minimal:latest \
        -f Dockerfile.minimal \
        -t desktop-vm-minimal:optimized \
        .
    
    # Build lightweight image
    docker build \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --cache-from desktop-vm-light:latest \
        -f Dockerfile \
        -t desktop-vm-light:optimized \
        .
    
    success "Optimized images built"
}

# Runtime optimization
optimize_runtime() {
    log "Creating optimized runtime configurations..."
    
    # Create optimized compose file
    cat > docker-compose.optimized.yml << EOF
version: '3.8'

services:
  desktop-vm-ultra:
    image: desktop-vm-minimal:optimized
    container_name: desktop-vm-ultra
    restart: "no"  # Faster startup without restart policy
    environment:
      - DISPLAY=:1
      - VNC_PORT=5901
      - NOVNC_PORT=6080
      - RESOLUTION=1024x768  # Minimal resolution
      - USER=desktop
    ports:
      - "5901:5901"
      - "6080:6080"
    volumes:
      - type: tmpfs
        target: /tmp
        tmpfs:
          size: 100M
      - type: tmpfs
        target: /var/tmp
        tmpfs:
          size: 50M
    networks:
      - desktop-net
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_ADMIN  # Minimal required capability
    read_only: false  # Set to true if possible
    shm_size: 128m     # Minimal shared memory
    mem_limit: 256m    # Strict memory limit
    memswap_limit: 256m
    cpus: 0.5          # CPU limit
    pids_limit: 100    # Process limit
    ulimits:
      nofile:
        soft: 1024
        hard: 1024
    healthcheck:
      test: ["CMD", "pgrep", "Xvfb"]
      interval: 10s
      timeout: 2s
      retries: 1
      start_period: 3s

networks:
  desktop-net:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: br-desktop
      com.docker.network.driver.mtu: 1500
EOF
    
    success "Optimized runtime configuration created"
}

# Performance testing
test_performance() {
    log "Testing optimized performance..."
    
    # Test minimal image
    log "Testing ultra-minimal configuration..."
    local start_time=$(date +%s.%N)
    
    docker run -d \
        --name perf-test \
        -p 5901:5901 \
        --memory=256m \
        --cpus=0.5 \
        desktop-vm-minimal:optimized > /dev/null
    
    # Wait for readiness
    local ready=false
    local timeout=30
    local elapsed=0
    
    while [ $elapsed -lt $timeout ] && [ "$ready" = false ]; do
        if docker exec perf-test pgrep Xvfb > /dev/null 2>&1; then
            ready=true
            local end_time=$(date +%s.%N)
            local startup_time=$(echo "$end_time - $start_time" | bc -l)
            success "Startup time: $(printf "%.2f" $startup_time) seconds"
        else
            sleep 0.1
            elapsed=$((elapsed + 1))
        fi
    done
    
    if [ "$ready" = false ]; then
        error "Performance test failed - container didn't start in time"
    else
        # Show resource usage
        log "Resource usage:"
        docker stats perf-test --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
    fi
    
    # Cleanup
    docker stop perf-test > /dev/null 2>&1
    docker rm perf-test > /dev/null 2>&1
}

# Create startup script
create_startup_script() {
    log "Creating optimized startup script..."
    
    cat > start-optimized.sh << 'EOF'
#!/bin/bash

# Ultra-fast startup script
echo "🚀 Starting optimized desktop VM..."

# Start with optimized settings
docker run -d \
    --name desktop-vm-fast \
    -p 5901:5901 \
    -p 6080:6080 \
    --memory=256m \
    --cpus=0.5 \
    --shm-size=128m \
    --tmpfs /tmp:rw,noexec,nosuid,size=100m \
    --tmpfs /var/tmp:rw,noexec,nosuid,size=50m \
    --security-opt no-new-privileges:true \
    --cap-drop ALL \
    --cap-add SYS_ADMIN \
    --pids-limit 100 \
    -e RESOLUTION=1024x768 \
    desktop-vm-minimal:optimized

echo "⏱️  Waiting for desktop to be ready..."

# Wait for VNC to be available
start_time=$(date +%s)
while ! nc -z localhost 5901 2>/dev/null; do
    sleep 0.1
    current_time=$(date +%s)
    if [ $((current_time - start_time)) -gt 30 ]; then
        echo "❌ Timeout waiting for desktop"
        exit 1
    fi
done

end_time=$(date +%s)
startup_time=$((end_time - start_time))

echo "✅ Desktop ready in ${startup_time} seconds!"
echo "🖥️  VNC: vnc://localhost:5901"
echo "🌐 Web: http://localhost:6080"
echo "🛑 Stop: docker stop desktop-vm-fast"
EOF
    
    chmod +x start-optimized.sh
    success "Optimized startup script created: ./start-optimized.sh"
}

# Cleanup optimization
cleanup_optimization() {
    log "Cleaning up for optimization..."
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove unused networks
    docker network prune -f
    
    # Remove build cache (keep recent)
    docker builder prune --keep-storage 1GB -f
    
    success "Cleanup completed"
}

# Main optimization function
run_optimization() {
    log "Starting comprehensive optimization..."
    
    # Check prerequisites
    if ! command -v docker &> /dev/null; then
        error "Docker not found"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        warning "bc calculator not found (needed for timing)"
    fi
    
    # Run optimizations
    optimize_build
    optimize_image
    optimize_runtime
    create_startup_script
    cleanup_optimization
    
    # Test performance
    if command -v bc &> /dev/null; then
        test_performance
    fi
    
    success "Optimization complete!"
    echo ""
    echo "🚀 Quick start: ./start-optimized.sh"
    echo "📊 Benchmark: ./benchmark.sh quick"
    echo "🔧 Advanced: docker-compose -f docker-compose.optimized.yml up"
}

# Usage
usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  all        Run all optimizations"
    echo "  system     Optimize system (requires root)"
    echo "  docker     Optimize Docker (requires root)"
    echo "  build      Optimize build process"
    echo "  image      Build optimized images"
    echo "  runtime    Create optimized runtime configs"
    echo "  test       Test performance"
    echo "  clean      Cleanup for optimization"
    echo ""
}

# Main
case "${1:-all}" in
    all)
        run_optimization
        ;;
    system)
        optimize_system
        ;;
    docker)
        optimize_docker
        ;;
    build)
        optimize_build
        ;;
    image)
        optimize_image
        ;;
    runtime)
        optimize_runtime
        ;;
    test)
        test_performance
        ;;
    clean)
        cleanup_optimization
        ;;
    *)
        usage
        ;;
esac
