# Lightweight Desktop VM Docker Compose
# 
# Minimal configuration for fast startup and low resource usage

version: '3.8'

services:
  # Lightweight Desktop VM
  desktop-vm:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: desktop-vm-light
    hostname: desktop-vm
    restart: unless-stopped
    environment:
      - DISPLAY=:1
      - VNC_PORT=5901
      - NOVNC_PORT=6080
      - RESOLUTION=1280x720
      - VNC_PASSWORD=vncpassword
      - USER=desktop
      - TZ=UTC
    ports:
      - "5901:5901"   # VNC port
      - "6080:6080"   # NoVNC web interface
    volumes:
      - desktop_home:/home/<USER>
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    networks:
      - desktop-network
    security_opt:
      - seccomp:unconfined
    cap_add:
      - SYS_ADMIN
    shm_size: 256m
    mem_limit: 1g
    cpus: 1.0
    healthcheck:
      test: ["CMD", "/opt/desktop-vm/health.sh"]
      interval: 30s
      timeout: 5s
      retries: 2
      start_period: 10s

  # Optional: Minimal Guacamole (only if web interface needed)
  guacamole-light:
    image: guacamole/guacamole:latest
    container_name: guacamole-light
    hostname: guacamole
    restart: unless-stopped
    environment:
      GUACD_HOSTNAME: guacd-light
      GUACD_PORT: 4822
      GUACAMOLE_HOME: /etc/guacamole
    ports:
      - "8080:8080"
    depends_on:
      - guacd-light
      - desktop-vm
    networks:
      - desktop-network
    mem_limit: 512m
    cpus: 0.5
    profiles:
      - guacamole  # Only start with --profile guacamole

  # Minimal Guacamole Daemon
  guacd-light:
    image: guacamole/guacd:latest
    container_name: guacd-light
    hostname: guacd
    restart: unless-stopped
    networks:
      - desktop-network
    mem_limit: 256m
    cpus: 0.25
    profiles:
      - guacamole  # Only start with --profile guacamole

volumes:
  desktop_home:
    driver: local

networks:
  desktop-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: br-desktop
