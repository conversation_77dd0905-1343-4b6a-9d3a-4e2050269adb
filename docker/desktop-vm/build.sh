#!/bin/bash

# Desktop VM Docker Build Script
# 
# This script builds and manages the Desktop VM Docker environment
# with all necessary components and configurations.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="desktop-vm"
DOCKER_COMPOSE_FILE="docker-compose.yml"
DOCKERFILE="Dockerfile"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build     Build the Desktop VM Docker image"
    echo "  up        Start the Desktop VM environment"
    echo "  down      Stop the Desktop VM environment"
    echo "  restart   Restart the Desktop VM environment"
    echo "  logs      Show logs from all services"
    echo "  status    Show status of all services"
    echo "  clean     Clean up Docker resources"
    echo "  setup     Initial setup (create directories, download files)"
    echo "  help      Show this help message"
    echo ""
    echo "Options:"
    echo "  --force   Force rebuild/recreate containers"
    echo "  --no-cache Build without using Docker cache"
    echo "  --pull    Pull latest base images before building"
    echo ""
    echo "Examples:"
    echo "  $0 setup                    # Initial setup"
    echo "  $0 build --no-cache         # Build without cache"
    echo "  $0 up                       # Start services"
    echo "  $0 logs desktop-vm          # Show logs for desktop-vm service"
    echo "  $0 down                     # Stop all services"
}

# Function to check prerequisites
check_prerequisites() {
    log_step "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check available disk space (at least 10GB)
    available_space=$(df . | awk 'NR==2 {print $4}')
    required_space=10485760  # 10GB in KB
    
    if [[ $available_space -lt $required_space ]]; then
        log_warning "Low disk space. At least 10GB is recommended for Desktop VM."
    fi
    
    log_success "Prerequisites check passed"
}

# Function to setup initial environment
setup_environment() {
    log_step "Setting up Desktop VM environment..."
    
    # Create necessary directories
    log_info "Creating volume directories..."
    mkdir -p volumes/desktop_home
    mkdir -p volumes/shared
    mkdir -p volumes/guacamole_drive
    mkdir -p volumes/guacamole_record
    mkdir -p nginx/conf.d
    
    # Set proper permissions
    chmod 755 volumes/desktop_home
    chmod 755 volumes/shared
    chmod 755 volumes/guacamole_drive
    chmod 755 volumes/guacamole_record
    
    # Download Guacamole database schema if not exists
    if [ ! -f "init/01-initdb.sql" ]; then
        log_info "Downloading Guacamole database schema..."
        mkdir -p init
        # The schema is already created in our init file
    fi
    
    # Create environment file
    if [ ! -f ".env" ]; then
        log_info "Creating environment configuration..."
        cat > .env << EOF
# Desktop VM Environment Configuration

# Desktop VM Settings
DESKTOP_VM_RESOLUTION=1920x1080
DESKTOP_VM_VNC_PASSWORD=vncpassword
DESKTOP_VM_USER=desktop

# Guacamole Settings
GUACAMOLE_DB_NAME=guacamole_db
GUACAMOLE_DB_USER=guacamole_user
GUACAMOLE_DB_PASSWORD=guacamole_pass

# Redis Settings
REDIS_PASSWORD=redis_password

# Network Settings
DESKTOP_VM_VNC_PORT=5901
DESKTOP_VM_NOVNC_PORT=6080
GUACAMOLE_PORT=8080
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# Timezone
TZ=UTC
EOF
    fi
    
    log_success "Environment setup completed"
}

# Function to build Docker image
build_image() {
    local force_rebuild=$1
    local no_cache=$2
    local pull_images=$3
    
    log_step "Building Desktop VM Docker image..."
    
    # Build arguments
    build_args=""
    if [[ "$no_cache" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi
    if [[ "$pull_images" == "true" ]]; then
        build_args="$build_args --pull"
    fi
    
    # Build the image
    log_info "Building desktop-vm image..."
    docker build $build_args -t ${PROJECT_NAME}:latest -f $DOCKERFILE .
    
    log_success "Docker image built successfully"
}

# Function to start services
start_services() {
    local force_recreate=$1
    
    log_step "Starting Desktop VM services..."
    
    # Compose arguments
    compose_args="up -d"
    if [[ "$force_recreate" == "true" ]]; then
        compose_args="up -d --force-recreate"
    fi
    
    # Start services
    docker-compose $compose_args
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 10
    
    # Check service health
    check_service_health
    
    log_success "Desktop VM services started successfully"
    show_access_info
}

# Function to stop services
stop_services() {
    log_step "Stopping Desktop VM services..."
    
    docker-compose down
    
    log_success "Desktop VM services stopped"
}

# Function to restart services
restart_services() {
    log_step "Restarting Desktop VM services..."
    
    stop_services
    sleep 5
    start_services
    
    log_success "Desktop VM services restarted"
}

# Function to show logs
show_logs() {
    local service=$1
    
    if [[ -n "$service" ]]; then
        log_info "Showing logs for service: $service"
        docker-compose logs -f $service
    else
        log_info "Showing logs for all services"
        docker-compose logs -f
    fi
}

# Function to show service status
show_status() {
    log_step "Checking Desktop VM service status..."
    
    echo ""
    echo "=== Docker Compose Services ==="
    docker-compose ps
    
    echo ""
    echo "=== Service Health Status ==="
    check_service_health
    
    echo ""
    echo "=== Resource Usage ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Function to check service health
check_service_health() {
    local services=("desktop-vm" "guacamole" "postgres" "guacd")
    
    for service in "${services[@]}"; do
        if docker-compose ps $service | grep -q "Up"; then
            if docker-compose exec -T $service echo "Health check" &> /dev/null; then
                log_success "$service is healthy"
            else
                log_warning "$service is running but may not be ready"
            fi
        else
            log_error "$service is not running"
        fi
    done
}

# Function to clean up Docker resources
cleanup() {
    log_step "Cleaning up Desktop VM Docker resources..."
    
    # Stop and remove containers
    docker-compose down --remove-orphans
    
    # Remove images
    read -p "Remove Desktop VM Docker images? (y/N): " remove_images
    if [[ $remove_images =~ ^[Yy]$ ]]; then
        docker rmi ${PROJECT_NAME}:latest 2>/dev/null || true
        docker image prune -f
    fi
    
    # Remove volumes
    read -p "Remove Docker volumes (this will delete all data)? (y/N): " remove_volumes
    if [[ $remove_volumes =~ ^[Yy]$ ]]; then
        docker-compose down -v
        docker volume prune -f
    fi
    
    # Remove networks
    docker network prune -f
    
    log_success "Cleanup completed"
}

# Function to show access information
show_access_info() {
    echo ""
    echo "=== Desktop VM Access Information ==="
    echo ""
    echo "🖥️  Desktop VM Access:"
    echo "   VNC Client:     vnc://localhost:5901"
    echo "   NoVNC Web:      http://localhost:6080"
    echo "   VNC Password:   vncpassword"
    echo ""
    echo "🌐 Guacamole Web Interface:"
    echo "   URL:            http://localhost:8080/guacamole"
    echo "   Username:       guacadmin"
    echo "   Password:       guacadmin"
    echo ""
    echo "🔧 Nginx Reverse Proxy:"
    echo "   Main URL:       http://localhost"
    echo "   Guacamole:      http://localhost/guacamole/"
    echo "   NoVNC:          http://localhost/novnc/"
    echo ""
    echo "📊 Monitoring:"
    echo "   Service Status: $0 status"
    echo "   Service Logs:   $0 logs [service-name]"
    echo ""
}

# Main function
main() {
    local command=$1
    shift
    
    # Parse options
    local force_rebuild=false
    local no_cache=false
    local pull_images=false
    local force_recreate=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force_rebuild=true
                force_recreate=true
                shift
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            --pull)
                pull_images=true
                shift
                ;;
            *)
                break
                ;;
        esac
    done
    
    # Execute command
    case $command in
        setup)
            check_prerequisites
            setup_environment
            ;;
        build)
            check_prerequisites
            build_image $force_rebuild $no_cache $pull_images
            ;;
        up)
            check_prerequisites
            start_services $force_recreate
            ;;
        down)
            stop_services
            ;;
        restart)
            check_prerequisites
            restart_services
            ;;
        logs)
            show_logs $1
            ;;
        status)
            show_status
            ;;
        clean)
            cleanup
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            if [[ -z "$command" ]]; then
                show_usage
            else
                log_error "Unknown command: $command"
                show_usage
                exit 1
            fi
            ;;
    esac
}

# Run main function with all arguments
main "$@"
