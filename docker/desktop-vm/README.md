# Desktop VM Docker Environment

A comprehensive Docker-based desktop virtual machine environment with XFCE desktop, VNC access, Apache Guacamole web interface, and AI automation capabilities.

## 🏗️ Architecture

The Desktop VM environment consists of several interconnected services:

- **Desktop VM Container**: Ubuntu 22.04 with XFCE desktop environment
- **Apache Guacamole**: Web-based remote desktop gateway
- **PostgreSQL**: Database for Guacamole configuration
- **Guacd**: Guacamole proxy daemon
- **Nginx**: Reverse proxy with WebSocket support
- **Redis**: Session management (optional)

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 10GB free disk space
- 4GB+ RAM recommended

### 1. Initial Setup

```bash
# Clone or navigate to the desktop-vm directory
cd docker/desktop-vm

# Run initial setup
./build.sh setup
```

### 2. Build and Start

```bash
# Build the Docker image
./build.sh build

# Start all services
./build.sh up
```

### 3. Access the Desktop

Once all services are running, you can access the desktop through multiple methods:

#### Web Browser (Recommended)
- **Guacamole Interface**: http://localhost:8080/guacamole
  - Username: `guacadmin`
  - Password: `guacadmin`
  - Click on "Desktop VM" connection

#### Direct VNC Access
- **NoVNC Web Client**: http://localhost:6080
- **VNC Client**: `vnc://localhost:5901`
  - Password: `vncpassword`

#### Nginx Reverse Proxy
- **Main Interface**: http://localhost
- **Guacamole**: http://localhost/guacamole/
- **NoVNC**: http://localhost/novnc/

## 🛠️ Management Commands

### Build Script Usage

```bash
./build.sh [COMMAND] [OPTIONS]
```

#### Commands

- `setup` - Initial environment setup
- `build` - Build Docker images
- `up` - Start all services
- `down` - Stop all services
- `restart` - Restart all services
- `logs [service]` - Show service logs
- `status` - Show service status
- `clean` - Clean up Docker resources
- `help` - Show help message

#### Options

- `--force` - Force rebuild/recreate
- `--no-cache` - Build without cache
- `--pull` - Pull latest base images

#### Examples

```bash
# Initial setup and build
./build.sh setup
./build.sh build --no-cache

# Start services
./build.sh up

# Check status
./build.sh status

# View logs
./build.sh logs desktop-vm
./build.sh logs guacamole

# Restart services
./build.sh restart

# Stop services
./build.sh down

# Clean up everything
./build.sh clean
```

### Docker Compose Commands

You can also use Docker Compose directly:

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f desktop-vm

# Scale services (if needed)
docker-compose up -d --scale desktop-vm=2
```

## 🔧 Configuration

### Environment Variables

Edit the `.env` file to customize settings:

```bash
# Desktop VM Settings
DESKTOP_VM_RESOLUTION=1920x1080
DESKTOP_VM_VNC_PASSWORD=vncpassword
DESKTOP_VM_USER=desktop

# Guacamole Settings
GUACAMOLE_DB_NAME=guacamole_db
GUACAMOLE_DB_USER=guacamole_user
GUACAMOLE_DB_PASSWORD=guacamole_pass

# Network Settings
DESKTOP_VM_VNC_PORT=5901
DESKTOP_VM_NOVNC_PORT=6080
GUACAMOLE_PORT=8080
```

### Volume Mounts

- `volumes/desktop_home` - User home directory
- `volumes/shared` - Shared files between host and VM
- `volumes/guacamole_drive` - Guacamole file sharing
- `volumes/guacamole_record` - Session recordings

### Network Configuration

The services communicate through a custom Docker network (`desktop-network`) with subnet `**********/16`.

## 🖥️ Desktop Environment

### Installed Software

The Desktop VM comes with:

#### Desktop Environment
- XFCE 4.16 (lightweight desktop)
- Thunar file manager
- XFCE Terminal
- System utilities

#### Web Browsers
- Firefox ESR
- Chromium

#### Development Tools
- Node.js 18 LTS
- Python 3.10
- Git
- Vim/Nano editors
- Build tools

#### Productivity
- LibreOffice Suite
- Text editors
- Calculator
- System monitor

#### Media Tools
- VLC Media Player
- GIMP Image Editor
- Image viewer

### User Account

- **Username**: `desktop`
- **Password**: `desktop`
- **Home Directory**: `/home/<USER>
- **Sudo Access**: Yes

## 🔒 Security Considerations

### Default Credentials

⚠️ **Change default passwords in production!**

- VNC Password: `vncpassword`
- Guacamole Admin: `guacadmin` / `guacadmin`
- Desktop User: `desktop` / `desktop`

### Network Security

- Services are bound to localhost by default
- Use reverse proxy for external access
- Consider VPN or SSH tunneling for remote access
- Enable HTTPS in production

### Firewall Configuration

If exposing services externally, configure firewall rules:

```bash
# Allow only necessary ports
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 5901/tcp   # Block direct VNC access
ufw deny 6080/tcp   # Block direct NoVNC access
```

## 🐛 Troubleshooting

### Common Issues

#### Services Won't Start

```bash
# Check Docker daemon
sudo systemctl status docker

# Check logs
./build.sh logs

# Restart services
./build.sh restart
```

#### Desktop Not Loading

```bash
# Check desktop-vm logs
./build.sh logs desktop-vm

# Restart desktop-vm service
docker-compose restart desktop-vm
```

#### Connection Refused

```bash
# Check if services are running
./build.sh status

# Check port bindings
docker-compose ps

# Check firewall
sudo ufw status
```

#### Performance Issues

```bash
# Check resource usage
docker stats

# Adjust resolution in .env file
DESKTOP_VM_RESOLUTION=1366x768

# Restart with new settings
./build.sh restart
```

### Health Checks

All services include health checks:

```bash
# Check health status
docker-compose ps

# Manual health check
docker-compose exec desktop-vm /opt/desktop-vm/scripts/health-check.sh
```

### Log Locations

- Desktop VM: `docker-compose logs desktop-vm`
- Guacamole: `docker-compose logs guacamole`
- Database: `docker-compose logs postgres`
- Nginx: `docker-compose logs nginx`

## 🔄 Updates and Maintenance

### Updating Images

```bash
# Pull latest base images
./build.sh build --pull

# Rebuild without cache
./build.sh build --no-cache --force
```

### Backup and Restore

#### Backup

```bash
# Backup volumes
docker run --rm -v desktop-vm_desktop_home:/data -v $(pwd):/backup ubuntu tar czf /backup/desktop_home_backup.tar.gz -C /data .

# Backup database
docker-compose exec postgres pg_dump -U guacamole_user guacamole_db > guacamole_backup.sql
```

#### Restore

```bash
# Restore volumes
docker run --rm -v desktop-vm_desktop_home:/data -v $(pwd):/backup ubuntu tar xzf /backup/desktop_home_backup.tar.gz -C /data

# Restore database
docker-compose exec -T postgres psql -U guacamole_user guacamole_db < guacamole_backup.sql
```

## 📊 Monitoring

### Resource Monitoring

```bash
# Real-time stats
docker stats

# Service status
./build.sh status

# Detailed container info
docker-compose exec desktop-vm htop
```

### Log Monitoring

```bash
# Follow all logs
./build.sh logs

# Follow specific service
./build.sh logs desktop-vm

# Search logs
docker-compose logs desktop-vm | grep ERROR
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:

1. Check the troubleshooting section
2. Review Docker and service logs
3. Search existing issues
4. Create a new issue with detailed information

---

**Note**: This is a development/testing environment. For production use, implement proper security measures, change default passwords, and configure appropriate access controls.
