#!/bin/bash

# Desktop VM Performance Benchmark
# 
# Compares startup times and resource usage between different configurations

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[BENCHMARK]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Benchmark configuration
CONFIGS=(
    "minimal:Dockerfile.minimal:Ultra-Minimal (Openbox)"
    "lightweight:Dockerfile:Lightweight (XFCE)"
)

# Results storage
declare -A BUILD_TIMES
declare -A STARTUP_TIMES
declare -A IMAGE_SIZES
declare -A MEMORY_USAGE

# Cleanup function
cleanup() {
    log "Cleaning up test containers..."
    docker stop desktop-vm-test 2>/dev/null || true
    docker rm desktop-vm-test 2>/dev/null || true
    docker rmi desktop-vm-test 2>/dev/null || true
}

# Measure build time
measure_build_time() {
    local config=$1
    local dockerfile=$2
    local description=$3
    
    log "Building $description..."
    
    # Clean up previous builds
    docker rmi desktop-vm-test 2>/dev/null || true
    
    # Measure build time
    local start_time=$(date +%s.%N)
    docker build -f $dockerfile -t desktop-vm-test . > /dev/null 2>&1
    local end_time=$(date +%s.%N)
    
    local build_time=$(echo "$end_time - $start_time" | bc -l)
    BUILD_TIMES[$config]=$(printf "%.2f" $build_time)
    
    # Get image size
    local size=$(docker images desktop-vm-test --format "{{.Size}}")
    IMAGE_SIZES[$config]=$size
    
    success "$description built in ${BUILD_TIMES[$config]}s (Size: $size)"
}

# Measure startup time
measure_startup_time() {
    local config=$1
    local description=$2
    
    log "Testing startup time for $description..."
    
    # Start container
    local start_time=$(date +%s.%N)
    docker run -d --name desktop-vm-test \
        -p 5901:5901 \
        -e DISPLAY=:1 \
        desktop-vm-test > /dev/null
    
    # Wait for VNC to be ready
    local ready=false
    local timeout=60
    local elapsed=0
    
    while [ $elapsed -lt $timeout ] && [ "$ready" = false ]; do
        if docker exec desktop-vm-test pgrep Xvfb > /dev/null 2>&1; then
            ready=true
            local end_time=$(date +%s.%N)
            local startup_time=$(echo "$end_time - $start_time" | bc -l)
            STARTUP_TIMES[$config]=$(printf "%.2f" $startup_time)
        else
            sleep 0.5
            elapsed=$((elapsed + 1))
        fi
    done
    
    if [ "$ready" = true ]; then
        success "$description ready in ${STARTUP_TIMES[$config]}s"
        
        # Measure memory usage after startup
        sleep 2
        local memory=$(docker stats desktop-vm-test --no-stream --format "{{.MemUsage}}" | cut -d'/' -f1)
        MEMORY_USAGE[$config]=$memory
        
        log "$description memory usage: $memory"
    else
        error "$description failed to start within ${timeout}s"
        STARTUP_TIMES[$config]="FAILED"
        MEMORY_USAGE[$config]="N/A"
    fi
    
    # Stop and remove container
    docker stop desktop-vm-test > /dev/null 2>&1
    docker rm desktop-vm-test > /dev/null 2>&1
}

# Test VNC connectivity
test_vnc_connectivity() {
    local config=$1
    local description=$2
    
    log "Testing VNC connectivity for $description..."
    
    # Start container
    docker run -d --name desktop-vm-test \
        -p 5901:5901 \
        -e DISPLAY=:1 \
        desktop-vm-test > /dev/null
    
    # Wait for startup
    sleep 5
    
    # Test VNC connection (requires vncviewer or similar)
    if command -v nc &> /dev/null; then
        if nc -z localhost 5901; then
            success "$description VNC port is accessible"
        else
            warning "$description VNC port is not accessible"
        fi
    else
        warning "netcat not available, skipping VNC connectivity test"
    fi
    
    # Stop and remove container
    docker stop desktop-vm-test > /dev/null 2>&1
    docker rm desktop-vm-test > /dev/null 2>&1
}

# Generate report
generate_report() {
    echo ""
    echo "=========================================="
    echo "         DESKTOP VM BENCHMARK REPORT"
    echo "=========================================="
    echo ""
    
    printf "%-20s %-15s %-15s %-15s %-15s\n" "Configuration" "Build Time" "Startup Time" "Image Size" "Memory Usage"
    printf "%-20s %-15s %-15s %-15s %-15s\n" "-------------" "----------" "------------" "----------" "------------"
    
    for config_line in "${CONFIGS[@]}"; do
        IFS=':' read -r config dockerfile description <<< "$config_line"
        printf "%-20s %-15s %-15s %-15s %-15s\n" \
            "$description" \
            "${BUILD_TIMES[$config]}s" \
            "${STARTUP_TIMES[$config]}s" \
            "${IMAGE_SIZES[$config]}" \
            "${MEMORY_USAGE[$config]}"
    done
    
    echo ""
    echo "=========================================="
    echo ""
    
    # Find fastest configuration
    local fastest_config=""
    local fastest_time=999999
    
    for config_line in "${CONFIGS[@]}"; do
        IFS=':' read -r config dockerfile description <<< "$config_line"
        if [[ "${STARTUP_TIMES[$config]}" != "FAILED" ]]; then
            local time=$(echo "${STARTUP_TIMES[$config]}" | bc -l)
            if (( $(echo "$time < $fastest_time" | bc -l) )); then
                fastest_time=$time
                fastest_config=$description
            fi
        fi
    done
    
    if [ -n "$fastest_config" ]; then
        success "Fastest configuration: $fastest_config (${fastest_time}s startup)"
    fi
    
    echo ""
    echo "Recommendations:"
    echo "- For fastest startup: Use minimal configuration"
    echo "- For full features: Use lightweight configuration"
    echo "- For production: Consider resource limits and security"
    echo ""
}

# Main benchmark function
run_benchmark() {
    log "Starting Desktop VM benchmark..."
    
    # Check prerequisites
    if ! command -v docker &> /dev/null; then
        error "Docker not found"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        error "bc calculator not found (required for timing)"
        exit 1
    fi
    
    # Cleanup any existing test containers
    cleanup
    
    # Run benchmarks for each configuration
    for config_line in "${CONFIGS[@]}"; do
        IFS=':' read -r config dockerfile description <<< "$config_line"
        
        if [ -f "$dockerfile" ]; then
            echo ""
            log "Benchmarking: $description"
            echo "----------------------------------------"
            
            measure_build_time "$config" "$dockerfile" "$description"
            measure_startup_time "$config" "$description"
            test_vnc_connectivity "$config" "$description"
            
            # Cleanup between tests
            cleanup
        else
            warning "Dockerfile not found: $dockerfile"
        fi
    done
    
    # Generate final report
    generate_report
}

# Quick test function
quick_test() {
    local dockerfile=${1:-Dockerfile.minimal}
    local description=${2:-"Quick Test"}
    
    log "Quick test with $dockerfile..."
    
    cleanup
    
    # Build
    log "Building..."
    docker build -f $dockerfile -t desktop-vm-test . > /dev/null
    
    # Start
    log "Starting..."
    docker run -d --name desktop-vm-test -p 5901:5901 desktop-vm-test > /dev/null
    
    # Wait and test
    sleep 5
    if docker exec desktop-vm-test pgrep Xvfb > /dev/null 2>&1; then
        success "Desktop is running!"
        log "Access via VNC: vnc://localhost:5901"
        log "Stop with: docker stop desktop-vm-test"
    else
        error "Desktop failed to start"
        docker logs desktop-vm-test
    fi
}

# Usage
usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  benchmark    Run full benchmark comparison"
    echo "  quick        Quick test with minimal config"
    echo "  test [file]  Test specific Dockerfile"
    echo "  clean        Clean up test containers"
    echo ""
}

# Main
case "${1:-}" in
    benchmark)
        run_benchmark
        ;;
    quick)
        quick_test "Dockerfile.minimal" "Minimal"
        ;;
    test)
        quick_test "${2:-Dockerfile}" "Custom"
        ;;
    clean)
        cleanup
        ;;
    *)
        usage
        ;;
esac
