# Desktop VM Docker Compose Configuration
# 
# This compose file sets up a complete desktop VM environment with
# Guacamole for web-based access and supporting services.

version: '3.8'

services:
  # Desktop VM Container
  desktop-vm:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: desktop-vm
    hostname: desktop-vm
    restart: unless-stopped
    environment:
      - DISPLAY=:1
      - VNC_PORT=5901
      - NOVNC_PORT=6080
      - RESOLUTION=1920x1080
      - VNC_PASSWORD=vncpassword
      - USER=desktop
      - TZ=UTC
    ports:
      - "5901:5901"   # VNC port
      - "6080:6080"   # NoVNC web interface
    volumes:
      - desktop_home:/home/<USER>
      - desktop_shared:/shared
      - /dev/shm:/dev/shm:rw
    networks:
      - desktop-network
    security_opt:
      - seccomp:unconfined
    cap_add:
      - SYS_ADMIN
    devices:
      - /dev/dri:/dev/dri
    healthcheck:
      test: ["CMD", "/opt/desktop-vm/scripts/health-check.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Guacamole Daemon
  guacd:
    image: guacamole/guacd:latest
    container_name: guacd
    hostname: guacd
    restart: unless-stopped
    volumes:
      - guacamole_drive:/drive:rw
      - guacamole_record:/record:rw
    networks:
      - desktop-network
    healthcheck:
      test: ["CMD-SHELL", "netstat -ln | grep :4822 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database for Guacamole
  postgres:
    image: postgres:15
    container_name: guacamole-postgres
    hostname: postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: guacamole_db
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: guacamole_pass
      POSTGRES_INITDB_ARGS: "--encoding=UTF8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d:ro
    networks:
      - desktop-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U guacamole_user -d guacamole_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Guacamole Web Application
  guacamole:
    image: guacamole/guacamole:latest
    container_name: guacamole
    hostname: guacamole
    restart: unless-stopped
    environment:
      GUACD_HOSTNAME: guacd
      GUACD_PORT: 4822
      POSTGRES_DATABASE: guacamole_db
      POSTGRES_HOSTNAME: postgres
      POSTGRES_PORT: 5432
      POSTGRES_USER: guacamole_user
      POSTGRES_PASSWORD: guacamole_pass
      POSTGRES_AUTO_CREATE_ACCOUNTS: "true"
      GUACAMOLE_HOME: /etc/guacamole
    ports:
      - "8080:8080"   # Guacamole web interface
    volumes:
      - guacamole_config:/etc/guacamole
      - guacamole_extensions:/etc/guacamole/extensions
      - guacamole_lib:/etc/guacamole/lib
    depends_on:
      - guacd
      - postgres
      - desktop-vm
    networks:
      - desktop-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/guacamole/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis for session management (optional)
  redis:
    image: redis:7-alpine
    container_name: guacamole-redis
    hostname: redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    networks:
      - desktop-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: desktop-vm-nginx
    hostname: nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - guacamole
      - desktop-vm
    networks:
      - desktop-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  # Desktop VM volumes
  desktop_home:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/desktop_home
  
  desktop_shared:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/shared

  # Guacamole volumes
  guacamole_drive:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/guacamole_drive

  guacamole_record:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/guacamole_record

  guacamole_config:
    driver: local

  guacamole_extensions:
    driver: local

  guacamole_lib:
    driver: local

  # Database volumes
  postgres_data:
    driver: local

  redis_data:
    driver: local

  # Nginx volumes
  nginx_logs:
    driver: local

networks:
  desktop-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
