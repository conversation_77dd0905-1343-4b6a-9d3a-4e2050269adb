"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { AdminVMTable } from "@/components/admin/admin-vm-table"
import { AdminTemplateTable } from "@/components/admin/admin-template-table"
import { AdminOrdersTable } from "@/components/admin/admin-orders-table"
import { useToast } from "@/components/ui/use-toast"
import { 
  Plus, 
  Search, 
  RefreshCw, 
  Server, 
  Package,
  ShoppingCart,
  Loader2,
  Filter
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Mock VM data (same as user dashboard but with additional admin fields)
const mockVMs = [
  {
    id: "vm-101",
    name: "web-server-1",
    description: "Primary web server",
    status: "running",
    template: "Standard Web Server",
    region: "us-east",
    ipAddress: "**********",
    cpu: { cores: 4, usage: 0.35 },
    memory: { total: 8 * 1024 * 1024 * 1024, used: 3 * 1024 * 1024 * 1024 },
    storage: { total: 100 * 1024 * 1024 * 1024, used: 45 * 1024 * 1024 * 1024 },
    bandwidth: { total: 2 * 1024 * 1024 * 1024 * 1024, used: 500 * 1024 * 1024 * 1024 },
    os: "Ubuntu 20.04 LTS",
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    price: 25,
    backups: true,
    containers: 3,
    owner: "<EMAIL>",
    node: "node-1",
    host: "proxmox-1"
  },
  {
    id: "vm-102",
    name: "db-server-1",
    description: "Primary database server",
    status: "running",
    template: "Database Server",
    region: "us-east",
    ipAddress: "**********",
    cpu: { cores: 4, usage: 0.65 },
    memory: { total: 16 * 1024 * 1024 * 1024, used: 12 * 1024 * 1024 * 1024 },
    storage: { total: 200 * 1024 * 1024 * 1024, used: 120 * 1024 * 1024 * 1024 },
    bandwidth: { total: 3 * 1024 * 1024 * 1024 * 1024, used: 800 * 1024 * 1024 * 1024 },
    os: "Ubuntu 20.04 LTS",
    createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000), // 25 days ago
    price: 40,
    backups: true,
    containers: 1,
    owner: "<EMAIL>",
    node: "node-1",
    host: "proxmox-1"
  },
  {
    id: "vm-103",
    name: "app-server-1",
    description: "Application server",
    status: "stopped",
    template: "Basic Development Server",
    region: "us-west",
    ipAddress: "**********",
    cpu: { cores: 2, usage: 0 },
    memory: { total: 4 * 1024 * 1024 * 1024, used: 0 },
    storage: { total: 50 * 1024 * 1024 * 1024, used: 15 * 1024 * 1024 * 1024 },
    bandwidth: { total: 1 * 1024 * 1024 * 1024 * 1024, used: 0 },
    os: "Ubuntu 22.04 LTS",
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
    price: 10,
    backups: false,
    containers: 0,
    owner: "<EMAIL>",
    node: "node-2",
    host: "proxmox-2"
  },
  {
    id: "vm-104",
    name: "container-host-1",
    description: "LXD container host",
    status: "running",
    template: "LXD Container Host",
    region: "eu-central",
    ipAddress: "**********",
    cpu: { cores: 8, usage: 0.45 },
    memory: { total: 16 * 1024 * 1024 * 1024, used: 10 * 1024 * 1024 * 1024 },
    storage: { total: 300 * 1024 * 1024 * 1024, used: 180 * 1024 * 1024 * 1024 },
    bandwidth: { total: 4 * 1024 * 1024 * 1024 * 1024, used: 1.2 * 1024 * 1024 * 1024 * 1024 },
    os: "Ubuntu 20.04 LTS",
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
    price: 60,
    backups: true,
    containers: 5,
    owner: "<EMAIL>",
    node: "node-3",
    host: "proxmox-3"
  }
];

// Mock VM templates
const mockTemplates = [
  {
    id: "template-1",
    name: "Basic Development Server",
    description: "Perfect for small projects and development work",
    icon: "server",
    specs: {
      cpu: 2,
      memory: 4,
      storage: 50,
      bandwidth: "1 TB"
    },
    price: 10,
    popular: false,
    category: "development",
    os: "Ubuntu 20.04 LTS",
    createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
    active: true,
    creator: "<EMAIL>"
  },
  {
    id: "template-2",
    name: "Standard Web Server",
    description: "Ideal for hosting websites and web applications",
    icon: "globe",
    specs: {
      cpu: 4,
      memory: 8,
      storage: 100,
      bandwidth: "2 TB"
    },
    price: 25,
    popular: true,
    category: "web",
    os: "Ubuntu 20.04 LTS",
    createdAt: new Date(Date.now() - 85 * 24 * 60 * 60 * 1000),
    active: true,
    creator: "<EMAIL>"
  },
  {
    id: "template-3",
    name: "Database Server",
    description: "Optimized for database workloads with fast storage",
    icon: "database",
    specs: {
      cpu: 4,
      memory: 16,
      storage: 200,
      bandwidth: "3 TB"
    },
    price: 40,
    popular: false,
    category: "database",
    os: "Ubuntu 20.04 LTS",
    createdAt: new Date(Date.now() - 80 * 24 * 60 * 60 * 1000),
    active: true,
    creator: "<EMAIL>"
  },
  {
    id: "template-4",
    name: "AI Development Environment",
    description: "Configured for machine learning and AI development",
    icon: "brain",
    specs: {
      cpu: 8,
      memory: 32,
      storage: 500,
      bandwidth: "5 TB"
    },
    price: 80,
    popular: true,
    category: "ai",
    os: "Ubuntu 22.04 LTS",
    createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
    active: true,
    creator: "<EMAIL>"
  },
  {
    id: "template-5",
    name: "LXD Container Host",
    description: "Designed to run multiple LXD containers efficiently",
    icon: "box",
    specs: {
      cpu: 8,
      memory: 16,
      storage: 300,
      bandwidth: "4 TB"
    },
    price: 60,
    popular: false,
    category: "container",
    os: "Ubuntu 20.04 LTS",
    createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
    active: true,
    creator: "<EMAIL>"
  },
  {
    id: "template-6",
    name: "High Performance Computing",
    description: "For compute-intensive workloads and batch processing",
    icon: "cpu",
    specs: {
      cpu: 16,
      memory: 64,
      storage: 1000,
      bandwidth: "10 TB"
    },
    price: 150,
    popular: false,
    category: "compute",
    os: "Ubuntu 22.04 LTS",
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    active: true,
    creator: "<EMAIL>"
  }
];

// Mock VM orders
const mockOrders = [
  {
    id: "order-1",
    vmId: "vm-101",
    vmName: "web-server-1",
    template: "Standard Web Server",
    user: "<EMAIL>",
    status: "completed",
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    completedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000 + 10 * 60 * 1000),
    price: 25,
    region: "us-east"
  },
  {
    id: "order-2",
    vmId: "vm-102",
    vmName: "db-server-1",
    template: "Database Server",
    user: "<EMAIL>",
    status: "completed",
    createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
    completedAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000 + 15 * 60 * 1000),
    price: 40,
    region: "us-east"
  },
  {
    id: "order-3",
    vmId: "vm-103",
    vmName: "app-server-1",
    template: "Basic Development Server",
    user: "<EMAIL>",
    status: "completed",
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    completedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000 + 8 * 60 * 1000),
    price: 10,
    region: "us-west"
  },
  {
    id: "order-4",
    vmId: "vm-104",
    vmName: "container-host-1",
    template: "LXD Container Host",
    user: "<EMAIL>",
    status: "completed",
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    completedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000 + 12 * 60 * 1000),
    price: 60,
    region: "eu-central"
  },
  {
    id: "order-5",
    vmId: null,
    vmName: "new-vm-1",
    template: "AI Development Environment",
    user: "<EMAIL>",
    status: "pending",
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    completedAt: null,
    price: 80,
    region: "us-east"
  }
];

export default function AdminInventoryPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("vms");
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  
  // Handle refresh
  const handleRefresh = async () => {
    setIsLoading(true);
    
    try {
      // In a real application, this would fetch updated data from an API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Refreshed",
        description: "Inventory data has been updated",
      });
    } catch (error) {
      console.error("Error refreshing inventory data:", error);
      toast({
        title: "Error",
        description: "Failed to refresh inventory data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">VM Inventory Management</h1>
          <p className="text-muted-foreground">
            Manage virtual machines, templates, and orders
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          <Button onClick={() => router.push("/admin")}>
            Back to Dashboard
          </Button>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="vms">
            <Server className="mr-2 h-4 w-4" />
            Virtual Machines
          </TabsTrigger>
          <TabsTrigger value="templates">
            <Package className="mr-2 h-4 w-4" />
            Templates
          </TabsTrigger>
          <TabsTrigger value="orders">
            <ShoppingCart className="mr-2 h-4 w-4" />
            Orders
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="vms">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Virtual Machines</CardTitle>
                <div className="flex gap-2">
                  <div className="relative w-[300px]">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search VMs..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create VM
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <AdminVMTable 
                vms={mockVMs} 
                searchTerm={searchTerm}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="templates">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>VM Templates</CardTitle>
                <div className="flex gap-2">
                  <div className="relative w-[300px]">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search templates..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Template
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <AdminTemplateTable 
                templates={mockTemplates} 
                searchTerm={searchTerm}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>VM Orders</CardTitle>
                <div className="flex gap-2">
                  <div className="relative w-[300px]">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search orders..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <AdminOrdersTable 
                orders={mockOrders} 
                searchTerm={searchTerm}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
