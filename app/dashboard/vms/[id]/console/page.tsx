"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { DashboardHeader } from "@/components/dashboard/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb } from "@/components/ui/breadcrumb"
import { useVirtualMachine } from "@/hooks/use-virtual-machines"
import { VMVNC } from "@/components/vm/vm-vnc"
import { Skeleton } from "@/components/ui/skeleton"
import { useServerContext } from "@/providers/server-provider"
import { ArrowLeft, Monitor, RefreshCw } from "lucide-react"

export default function VMConsolePage() {
  const params = useParams()
  const router = useRouter()
  // We don't need serverInfo for now
  const vmId = params.id as string
  const { vm, isLoading, error, refetch } = useVirtualMachine(vmId)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Handle refresh button click
  const handleRefresh = async () => {
    setIsRefreshing(true)
    await refetch()
    setTimeout(() => setIsRefreshing(false), 500) // Minimum spinner time for UX
  }

  // Handle back button click
  const handleBack = () => {
    router.push(`/proxmox-dashboard/vms/${vmId}`)
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-4">
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/proxmox-dashboard" },
            { label: "Virtual Machines", href: "/proxmox-dashboard/vms" },
            { label: `VM ${vmId}`, href: `/proxmox-dashboard/vms/${vmId}` },
            { label: "Console", href: `/proxmox-dashboard/vms/${vmId}/console` },
          ]}
        />
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-40" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-9 w-28" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
        <Skeleton className="h-[600px] w-full" />
      </div>
    )
  }

  // Error state
  if (error || !vm) {
    return (
      <div className="flex-1 space-y-6 p-4">
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/proxmox-dashboard" },
            { label: "Virtual Machines", href: "/proxmox-dashboard/vms" },
            { label: `VM ${vmId}`, href: `/proxmox-dashboard/vms/${vmId}` },
            { label: "Console", href: `/proxmox-dashboard/vms/${vmId}/console` },
          ]}
        />
        <div className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Error Loading VM</h2>
          <p className="text-muted-foreground mb-6">Failed to load VM details</p>
          <Button asChild>
            <a href="/proxmox-dashboard/vms">Back to VM List</a>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4">
      <Breadcrumb
        items={[
          { label: "Dashboard", href: "/proxmox-dashboard" },
          { label: "Virtual Machines", href: "/proxmox-dashboard/vms" },
          { label: vm.name, href: `/proxmox-dashboard/vms/${vmId}` },
          { label: "Console", href: `/proxmox-dashboard/vms/${vmId}/console` },
        ]}
      />

      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <DashboardHeader
          heading={`${vm.name} Console`}
          text={`VM ID: ${vmId} • Status: ${vm.status} • Node: ${vm.node}`}
        />

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to VM
          </Button>
          <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Monitor className="mr-2 h-5 w-5" />
            VNC Console
          </CardTitle>
          <CardDescription>
            Access the virtual machine console via VNC
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="min-h-[600px]">
            <VMVNC
              serverId=""
              vmId={vmId}
              vmName={vm.name}
              status={vm.status}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Keyboard Shortcuts</CardTitle>
          <CardDescription>
            Useful keyboard shortcuts for the VNC console
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-medium">System Shortcuts</h3>
              <div className="border rounded-md divide-y">
                <div className="flex justify-between p-2">
                  <span className="font-mono">Ctrl+Alt+Del</span>
                  <span>Send Ctrl+Alt+Del</span>
                </div>
                <div className="flex justify-between p-2">
                  <span className="font-mono">Alt+Tab</span>
                  <span>Switch applications</span>
                </div>
                <div className="flex justify-between p-2">
                  <span className="font-mono">Ctrl+Esc</span>
                  <span>Start menu (Windows)</span>
                </div>
                <div className="flex justify-between p-2">
                  <span className="font-mono">Alt+F4</span>
                  <span>Close window</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium">noVNC Shortcuts</h3>
              <div className="border rounded-md divide-y">
                <div className="flex justify-between p-2">
                  <span className="font-mono">Ctrl+Alt+F</span>
                  <span>Toggle fullscreen</span>
                </div>
                <div className="flex justify-between p-2">
                  <span className="font-mono">Ctrl+Alt+C</span>
                  <span>Send Ctrl+Alt+Del</span>
                </div>
                <div className="flex justify-between p-2">
                  <span className="font-mono">Ctrl+Alt+S</span>
                  <span>Toggle settings</span>
                </div>
                <div className="flex justify-between p-2">
                  <span className="font-mono">Ctrl+Alt+D</span>
                  <span>Disconnect</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
