"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { DashboardHeader } from "@/components/layout/dashboard-header"
import { VMUsersTable } from "@/components/vm/vm-users-table"
import { Button } from "@/components/ui/button"
import { Icon } from "@/components/ui/icon"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useVMUserAssignment } from "@/hooks/use-vm-users"

export default function VMUsersPage() {
  const params = useParams<{ id: string }>()
  const [vm, setVM] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isOwner, setIsOwner] = useState(false)
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  
  // Get current user's assignment to check if they're an admin
  const { data: userAssignment } = useVMUserAssignment(
    params.id,
    currentUserId || ""
  )

  // Check if current user is an admin
  const isAdmin = userAssignment?.role === "ADMIN"

  // Load VM details
  useEffect(() => {
    const loadVM = async () => {
      try {
        // Get VM details
        const response = await fetch(`/api/vms/${params.id}`)
        if (!response.ok) {
          throw new Error("Failed to fetch VM")
        }
        const data = await response.json()
        setVM(data.data)
        
        // Get current user ID
        const sessionResponse = await fetch('/api/auth/session')
        const sessionData = await sessionResponse.json()
        const userId = sessionData.user?.id
        setCurrentUserId(userId)
        
        // Check if current user is the owner
        if (data.data && userId) {
          setIsOwner(data.data.userId === userId)
        }
      } catch (error) {
        console.error("Error loading VM:", error)
      } finally {
        setIsLoading(false)
      }
    }
    
    loadVM()
  }, [params.id])

  if (isLoading) {
    return (
      <div className="space-y-6 p-6 pb-16">
        <div className="flex flex-col gap-2">
          <Skeleton className="h-10 w-[250px]" />
          <Skeleton className="h-4 w-[350px]" />
        </div>
        <Skeleton className="h-[400px] w-full rounded-md" />
      </div>
    )
  }

  if (!vm) {
    return (
      <div className="space-y-6 p-6 pb-16">
        <DashboardHeader
          title="VM Not Found"
          description="The virtual machine you're looking for doesn't exist or you don't have access to it."
          icon="mdi:server-off"
        />
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <Icon name="mdi:server-off" size="xl" className="text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">VM Not Found</h3>
            <p className="text-sm text-muted-foreground mt-2">
              The virtual machine you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button className="mt-4" asChild>
              <a href="/proxmox-dashboard/vms">
                <Icon name="mdi:arrow-left" size="sm" className="mr-2" />
                Back to VMs
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6 pb-16">
      <DashboardHeader
        title={`${vm.name} Users`}
        description="Manage user access and permissions for this virtual machine"
        icon="mdi:account-multiple"
        actions={
          <Button variant="outline" asChild>
            <a href={`/proxmox-dashboard/vms/${params.id}`}>
              <Icon name="mdi:arrow-left" size="sm" className="mr-2" />
              Back to VM
            </a>
          </Button>
        }
      />

      <Tabs defaultValue="users" className="space-y-6">
        <TabsList>
          <TabsTrigger value="users">
            <Icon name="mdi:account-multiple" size="sm" className="mr-2" />
            Users
          </TabsTrigger>
          <TabsTrigger value="permissions">
            <Icon name="mdi:shield-key" size="sm" className="mr-2" />
            Permissions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          <VMUsersTable
            vmId={params.id}
            isOwner={isOwner}
            isAdmin={isAdmin}
          />
        </TabsContent>

        <TabsContent value="permissions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Role Permissions</CardTitle>
              <CardDescription>
                Understand what each role can do on this virtual machine
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <Icon name="mdi:shield" size="sm" className="text-primary" />
                    Admin
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Admins have full control over the VM. They can manage the VM, users, and VM settings.
                  </p>
                  <ul className="text-sm text-muted-foreground list-disc list-inside ml-4 space-y-1">
                    <li>Start, stop, and restart the VM</li>
                    <li>Modify VM settings</li>
                    <li>Add, modify, and remove users</li>
                    <li>Access the VM console</li>
                    <li>View VM performance and logs</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <Icon name="mdi:account-wrench" size="sm" className="text-primary" />
                    Operator
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Operators can operate the VM but cannot modify user access.
                  </p>
                  <ul className="text-sm text-muted-foreground list-disc list-inside ml-4 space-y-1">
                    <li>Start, stop, and restart the VM</li>
                    <li>Modify some VM settings</li>
                    <li>Access the VM console</li>
                    <li>View VM performance and logs</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <Icon name="mdi:account-eye" size="sm" className="text-primary" />
                    Viewer
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Viewers have read-only access to the VM.
                  </p>
                  <ul className="text-sm text-muted-foreground list-disc list-inside ml-4 space-y-1">
                    <li>View VM details</li>
                    <li>View VM performance and logs</li>
                    <li>Cannot start, stop, or modify the VM</li>
                    <li>Cannot access the VM console</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
