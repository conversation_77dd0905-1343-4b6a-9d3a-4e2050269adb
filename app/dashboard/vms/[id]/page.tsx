"use client"

import { notFound } from "next/navigation"
import { useParams } from "next/navigation"
import { DashboardHeader } from "@/components/dashboard/header"
import { Breadcrumb } from "@/components/ui/breadcrumb"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { useVirtualMachine } from "@/hooks/use-virtual-machines"
import { VMStatusBadge } from "@/components/vm/vm-status-badge"
import { VMTerminal } from "@/components/vm/vm-terminal"
import { VMVNC } from "@/components/vm/vm-vnc"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { Play, Square, RefreshCw, Monitor } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useState } from "react"
import { useServerContext } from "@/providers/server-provider"
import { toast } from "sonner"
import { formatBytes, formatDiskSize, formatMemory, formatUptime } from "@/lib/format-utils"

export default function VMDetailPage() {
  const params = useParams<{ id: string }>()
  const vmId = params.id as string
  const { serverInfo } = useServerContext()

  const {
    vm, isLoading,
    error,
    startVm,
    stopVm,
    resetVm,
    isActionLoading: vmActionLoading
  } = useVirtualMachine(vmId)
  const [isActionLoading, setIsActionLoading] = useState(false)

  if (error) {
    notFound()
  }

  const handleStart = () => {
    if (isActionLoading || vmActionLoading) return

    setIsActionLoading(true)
    try {
      startVm()
      toast.success("VM start command sent successfully")
    } catch (err) {
      toast.error(`Failed to start VM: ${err instanceof Error ? err.message : "Unknown error"}`)
    } finally {
      setIsActionLoading(false)
    }
  }

  const handleStop = () => {
    if (isActionLoading || vmActionLoading) return

    setIsActionLoading(true)
    try {
      stopVm(false) // graceful shutdown
      toast.success("VM shutdown command sent successfully")
    } catch (err) {
      toast.error(`Failed to shutdown VM: ${err instanceof Error ? err.message : "Unknown error"}`)
    } finally {
      setIsActionLoading(false)
    }
  }

  const handleRestart = () => {
    if (isActionLoading || vmActionLoading) return

    setIsActionLoading(true)
    try {
      resetVm()
      toast.success("VM reset command sent successfully")
    } catch (err) {
      toast.error(`Failed to reset VM: ${err instanceof Error ? err.message : "Unknown error"}`)
    } finally {
      setIsActionLoading(false)
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-4">
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/proxmox-dashboard" },
            { label: "Virtual Machines", href: "/proxmox-dashboard/vms" },
            { label: `VM ${vmId}`, href: `/proxmox-dashboard/vms/${vmId}` },
          ]}
        />
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-40" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-9 w-28" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
        <div className="grid gap-4 p-4 border rounded-lg shadow-sm">
          <Skeleton className="h-6 w-40" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-[200px] w-full" />
          </div>
        </div>
        <Skeleton className="h-[400px] w-full" />
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4">
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/proxmox-dashboard" },
            { label: "Virtual Machines", href: "/proxmox-dashboard/vms" },
            { label: `VM ${vmId}`, href: `/proxmox-dashboard/vms/${vmId}` },
          ]}
        />
        <div className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Error Loading VM</h2>
          <p className="text-muted-foreground mb-6">Failed to load VM details</p>
          <Button asChild>
            <a href="/proxmox-dashboard/vms">Back to VM List</a>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4">
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/proxmox-dashboard" },
            { label: "Virtual Machines", href: "/proxmox-dashboard/vms" },
            { label: vm?.name || `VM ${vmId}`, href: `/proxmox-dashboard/vms/${vmId}` },
          ]}
        />

        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <DashboardHeader
            heading={vm?.name || `VM ${vmId}`}
            text={`Virtual Machine ID: ${vmId}`}
          />

          <div className="flex items-center gap-2">
            {isLoading ? (
              <div className="h-9 w-28 animate-pulse rounded bg-muted"></div>
            ) : (
              vm && <VMStatusBadge status={vm.status} />
            )}

            <div className="flex gap-2">
              {vm?.status === "running" ? (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-amber-500"
                    onClick={handleStop}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Square className="mr-2 h-4 w-4" />
                    )}
                    Stop
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRestart}
                    disabled={isActionLoading}
                  >
                    {isActionLoading ? (
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="mr-2 h-4 w-4" />
                    )}
                    Restart
                  </Button>
                </div>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  className="text-green-500"
                  onClick={handleStart}
                  disabled={isActionLoading || vm?.status === "starting"}
                >
                  {isActionLoading ? (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Play className="mr-2 h-4 w-4" />
                  )}
                  Start
                </Button>
              )}
            </div>
          </div>
        </div>

        <div className="grid gap-4 p-4 border rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold">VM Specifications</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">Hardware</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">CPUs</span>
                  <span>{isLoading ? "-" : vm?.cpus || 1}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Memory</span>
                  <span>{isLoading ? "-" : formatMemory(vm?.memory || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Disk</span>
                  <span>{isLoading ? "-" : formatDiskSize(vm?.disk || 0)}</span>
                </div>
              </div>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">Configuration</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Node</span>
                  <span>{isLoading ? "-" : vm?.node || "unknown"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">OS Type</span>
                  <span>{isLoading ? "-" : vm?.ostype || "unknown"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Uptime</span>
                  <span>{isLoading ? "-" : formatUptime(vm?.uptime || 0)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Tabs defaultValue="resources" className="mt-6">
          <TabsList>
            <TabsTrigger value="resources">Resources</TabsTrigger>
            <TabsTrigger value="network">Network</TabsTrigger>
            <TabsTrigger value="disks">Disks</TabsTrigger>
            <TabsTrigger value="console">Console</TabsTrigger>
            <TabsTrigger value="backups">Backups</TabsTrigger>
          </TabsList>
          <TabsContent value="resources">
            <div className="grid gap-4 p-4 border rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold">Resource Usage</h2>
              <div className="grid gap-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">CPU Usage</h3>
                  <div className="w-full h-4 bg-gray-200 rounded-full overflow-hidden">
                    <div className="bg-blue-500 h-full" style={{ width: `${isLoading ? 0 : vm?.cpu || 0}%` }}></div>
                  </div>
                  <p className="text-sm text-right mt-1">{isLoading ? "Loading..." : `${vm?.cpu || 0}%`}</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Memory Usage</h3>
                  <div className="w-full h-4 bg-gray-200 rounded-full overflow-hidden">
                    <div className="bg-purple-500 h-full" style={{ width: `${isLoading ? 0 : (vm?.mem && vm?.maxmem) ? (vm.mem / vm.maxmem * 100) : 0}%` }}></div>
                  </div>
                  <p className="text-sm text-right mt-1">
                    {isLoading ? "Loading..." : (vm?.mem && vm?.maxmem)
                      ? `${formatMemory(vm.mem)} / ${formatMemory(vm.maxmem)} (${(vm.mem / vm.maxmem * 100).toFixed(1)}%)`
                      : "N/A"}
                  </p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Network Traffic</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Incoming</h4>
                      <div className="flex items-center">
                        <Icons.arrowRight className="h-4 w-4 text-green-500 mr-2" />
                        <span>{isLoading ? "Loading..." : formatBytes(vm?.netin || 0) + "/s"}</span>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">Outgoing</h4>
                      <div className="flex items-center">
                        <Icons.arrowRight className="h-4 w-4 text-blue-500 mr-2" />
                        <span>{isLoading ? "Loading..." : formatBytes(vm?.netout || 0) + "/s"}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Disk I/O</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Read</h4>
                      <div className="flex items-center">
                        <Icons.post className="h-4 w-4 text-green-500 mr-2" />
                        <span>{isLoading ? "Loading..." : formatBytes(vm?.diskread || 0) + "/s"}</span>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">Write</h4>
                      <div className="flex items-center">
                        <Icons.post className="h-4 w-4 text-blue-500 mr-2" />
                        <span>{isLoading ? "Loading..." : formatBytes(vm?.diskwrite || 0) + "/s"}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="network">
            <div className="grid gap-4 p-4 border rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold">Network Interfaces</h2>
              <div className="space-y-2">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">net0</h3>
                    <Badge variant="outline" className="bg-green-100 text-green-800">Connected</Badge>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Model</span>
                      <span>virtio</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">MAC Address</span>
                      <span>52:54:00:12:34:56</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Bridge</span>
                      <span>vmbr0</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="disks">
            <div className="grid gap-4 p-4 border rounded-lg shadow-sm">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Disk Storage</h2>
                <Button size="sm" variant="outline">
                  <Icons.add className="mr-2 h-4 w-4" />
                  Add Disk
                </Button>
              </div>
              <div className="space-y-2">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">scsi0</h3>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Primary</Badge>
                      <Button size="icon" variant="outline" className="h-8 w-8 text-destructive">
                        <Icons.trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Storage</span>
                      <span>local-lvm</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Size</span>
                      <span>{isLoading ? "Loading..." : formatDiskSize(vm?.maxdisk || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Used</span>
                      <span>
                        {isLoading ? "Loading..." :
                         (vm?.disk !== undefined && vm?.maxdisk !== undefined) ?
                         `${formatDiskSize(vm.disk)} (${(vm.disk / vm.maxdisk * 100).toFixed(1)}%)` : "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="console">
            <div className="grid gap-4 p-4 border rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold">Console Access</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <VMVNC
                  serverId=""
                  vmId={vmId}
                  vmName={vm?.name || `VM ${vmId}`}
                  status={vm?.status || "stopped"}
                />
                <VMTerminal
                  serverId=""
                  vmId={vmId}
                  vmName={vm?.name || `VM ${vmId}`}
                  ipAddress={vm?.ipAddress || ""}
                  status={vm?.status || "stopped"}
                />
              </div>

              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-4">About VNC Console</h3>
                  <p className="text-muted-foreground mb-4">
                    Access your virtual machine's graphical console directly from your web browser using VNC (Virtual Network Computing).
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Icons.check className="h-4 w-4 text-green-500 mr-2" />
                      <span>No additional software required</span>
                    </div>
                    <div className="flex items-center">
                      <Icons.check className="h-4 w-4 text-green-500 mr-2" />
                      <span>Secure connection via WebSockets</span>
                    </div>
                    <div className="flex items-center">
                      <Icons.check className="h-4 w-4 text-green-500 mr-2" />
                      <span>Full keyboard and mouse support</span>
                    </div>
                    <div className="flex items-center">
                      <Icons.check className="h-4 w-4 text-green-500 mr-2" />
                      <span>Special key combinations (Ctrl+Alt+Del, etc.)</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-4">SSH Instructions</h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">From Terminal</h4>
                      <div className="bg-muted p-2 rounded-md font-mono text-sm overflow-x-auto">
                        ssh root@{vm?.ipAddress || "your-vm-ip"}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">Using Key Authentication</h4>
                      <div className="bg-muted p-2 rounded-md font-mono text-sm overflow-x-auto">
                        ssh -i /path/to/private_key root@{vm?.ipAddress || "your-vm-ip"}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">Using Custom Port</h4>
                      <div className="bg-muted p-2 rounded-md font-mono text-sm overflow-x-auto">
                        ssh -p 22 root@{vm?.ipAddress || "your-vm-ip"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4 flex justify-end">
                <Button asChild>
                  <a href={`/proxmox-dashboard/vms/${vmId}/console`} target="_blank" rel="noopener noreferrer">
                    <Monitor className="mr-2 h-4 w-4" />
                    Open Console in New Window
                  </a>
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="backups">
            <div className="grid gap-4 p-4 border rounded-lg shadow-sm">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Backup History</h2>
                <Button size="sm" variant="outline">
                  <Icons.add className="mr-2 h-4 w-4" />
                  Create Backup
                </Button>
              </div>
              <div className="space-y-2">
                {isLoading ? (
                  <div className="p-4 border rounded-lg h-20 animate-pulse bg-muted"></div>
                ) : (
                  <div className="p-4 border rounded-lg space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Backup - 2023-10-24</h3>
                        <p className="text-sm text-muted-foreground">Size: 4.2 GB</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline">
                          <Icons.restore className="mr-2 h-4 w-4" />
                          Restore
                        </Button>
                        <Button size="sm" variant="outline" className="text-destructive">
                          <Icons.trash className="mr-2 h-4 w-4" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
  )
}

