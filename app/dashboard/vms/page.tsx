"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { UserVMCard } from "@/components/dashboard/user-vm-card"
import { UserVMDetails } from "@/components/dashboard/user-vm-details"
import { VMUsageStats } from "@/components/dashboard/vm-usage-stats"
import { VMActivityLog } from "@/components/dashboard/vm-activity-log"
import { useToast } from "@/components/ui/use-toast"
import {
  Plus,
  Search,
  RefreshCw,
  Server,
  Activity,
  Loader2,
  Filter
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { vmService } from "@/lib/services/vm-service"

export default function UserVMDashboardPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [vms, setVMs] = useState<any[]>([]);
  const [activityLog, setActivityLog] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedVM, setSelectedVM] = useState<string | null>(null);
  const [selectedVMData, setSelectedVMData] = useState<any | null>(null);

  // Fetch VMs on component mount
  useEffect(() => {
    fetchVMs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch VMs from API
  const fetchVMs = async () => {
    setIsLoading(true);

    try {
      // Fetch VMs from API
      const userVMs = await vmService.getUserVMs();
      setVMs(userVMs);

      // Fetch activity log for all VMs
      const allActivities: any[] = [];
      for (const vm of userVMs) {
        try {
          const activities = await vmService.getVMActivityLog(vm.id);
          allActivities.push(...activities);
        } catch (error) {
          console.error(`Error fetching activity log for VM ${vm.id}:`, error);
        }
      }

      // Sort activities by timestamp (newest first)
      allActivities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      setActivityLog(allActivities);

      toast({
        title: "VMs Loaded",
        description: `Successfully loaded ${userVMs.length} virtual machines`,
      });
    } catch (error: any) {
      console.error("Error fetching VMs:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to load VMs. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Filter VMs based on search and status
  const filteredVMs = vms.filter(vm => {
    const matchesSearch = vm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vm.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vm.ipAddress.includes(searchTerm);

    const matchesStatus = statusFilter === "all" || vm.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Handle VM selection
  const handleSelectVM = async (vmId: string) => {
    setIsLoading(true);

    try {
      // Fetch VM details from API
      const vm = await vmService.getVMById(vmId);

      // Fetch VM activity log
      const activities = await vmService.getVMActivityLog(vmId);

      setSelectedVM(vmId);
      setSelectedVMData(vm);

      // Update activity log with VM-specific activities
      const updatedActivityLog = [...activityLog];
      for (const activity of activities) {
        if (!updatedActivityLog.some(a => a.id === activity.id)) {
          updatedActivityLog.push(activity);
        }
      }

      // Sort activities by timestamp (newest first)
      updatedActivityLog.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      setActivityLog(updatedActivityLog);
    } catch (error: any) {
      console.error(`Error fetching VM ${vmId}:`, error);
      toast({
        title: "Error",
        description: error.message || "Failed to load VM details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle VM action
  const handleVMAction = async (action: string, vmId: string) => {
    setIsLoading(true);

    try {
      // Call appropriate API based on action
      switch (action) {
        case "start":
          await vmService.startVM(vmId);
          break;
        case "stop":
          await vmService.stopVM(vmId);
          break;
        case "restart":
          await vmService.restartVM(vmId);
          break;
        case "delete":
          await vmService.deleteVM(vmId);
          break;
        case "console":
          // Open console in new window
          window.open(`/console/${vmId}`, '_blank');
          setIsLoading(false);
          return;
        default:
          toast({
            title: "Not Implemented",
            description: `The ${action} action is not implemented yet.`,
          });
          setIsLoading(false);
          return;
      }

      // Refresh VM data
      await fetchVMs();

      // If a VM was selected and it was the one we performed an action on, refresh its data
      if (selectedVM === vmId) {
        try {
          const updatedVM = await vmService.getVMById(vmId);
          setSelectedVMData(updatedVM);
        } catch (error) {
          // If the VM was deleted, clear the selection
          if (action === "delete") {
            setSelectedVM(null);
            setSelectedVMData(null);
          }
        }
      }

      toast({
        title: "Success",
        description: `VM ${action} operation completed successfully`,
      });
    } catch (error: any) {
      console.error(`Error performing ${action} on VM ${vmId}:`, error);
      toast({
        title: "Error",
        description: error.message || `Failed to ${action} VM. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    await fetchVMs();
  };

  // If a VM is selected, show its details
  if (selectedVM && selectedVMData) {
    return (
      <div className="container mx-auto py-6">
        <UserVMDetails
          vm={selectedVMData}
          activityLog={activityLog.filter(activity => activity.vmId === selectedVM)}
          onBack={() => {
            setSelectedVM(null);
            setSelectedVMData(null);
          }}
          onAction={handleVMAction}
          isLoading={isLoading}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">My Virtual Machines</h1>
          <p className="text-muted-foreground">
            Manage your virtual machines and containers
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          <Button onClick={() => router.push("/marketplace")}>
            <Plus className="mr-2 h-4 w-4" />
            New VM
          </Button>
        </div>
      </div>

      {isLoading && vms.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
          <h3 className="text-lg font-medium">Loading your virtual machines...</h3>
          <p className="text-muted-foreground">Please wait while we fetch your VM data</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <VMUsageStats vms={vms} />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>Virtual Machines</CardTitle>
                    <div className="flex gap-2">
                      <div className="relative w-[200px]">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search VMs..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-8"
                        />
                      </div>
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-[130px]">
                          <Filter className="h-4 w-4 mr-2" />
                          <SelectValue placeholder="Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="running">Running</SelectItem>
                          <SelectItem value="stopped">Stopped</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {filteredVMs.length === 0 ? (
                        <div className="text-center py-8 border rounded-md">
                          <Server className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                          <h3 className="text-lg font-medium">No VMs found</h3>
                          <p className="text-muted-foreground mb-4">
                            {searchTerm || statusFilter !== "all"
                              ? "Try adjusting your search or filter criteria"
                              : "You don't have any virtual machines yet"}
                          </p>
                          {!searchTerm && statusFilter === "all" && (
                            <Button onClick={() => router.push("/marketplace")}>
                              <Plus className="mr-2 h-4 w-4" />
                              Create Your First VM
                            </Button>
                          )}
                        </div>
                      ) : (
                        filteredVMs.map(vm => (
                          <UserVMCard
                            key={vm.id}
                            vm={vm}
                            onSelect={() => handleSelectVM(vm.id)}
                            onAction={(action) => handleVMAction(action, vm.id)}
                            isLoading={isLoading}
                          />
                        ))
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>
                    Recent actions performed on your VMs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <VMActivityLog activities={activityLog.slice(0, 10)} vms={vms} />
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
