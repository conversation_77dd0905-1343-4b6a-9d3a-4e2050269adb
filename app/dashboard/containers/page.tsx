"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Box, Container, Plus, RefreshCw } from "lucide-react"
import Link from "next/link"
import { DashboardHeader } from "@/components/dashboard/header"
import { DashboardShell } from "@/components/dashboard/shell"

export default function ContainerDashboardPage() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Container Management"
        text="Manage your LXC and LXD containers from a single dashboard."
      >
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            New Container
          </Button>
        </div>
      </DashboardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="lxc">LXC Containers</TabsTrigger>
          <TabsTrigger value="lxd">LXD Containers</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  LXC Containers
                </CardTitle>
                <Box className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  Linux Containers (LXC)
                </p>
                <Button asChild className="mt-4 w-full" variant="outline" size="sm">
                  <Link href="/dashboard/containers/lxc">
                    Manage LXC Containers
                  </Link>
                </Button>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  LXD Containers
                </CardTitle>
                <Container className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-muted-foreground">
                  LXD System Containers
                </p>
                <Button asChild className="mt-4 w-full" variant="outline" size="sm">
                  <Link href="/dashboard/containers/lxd">
                    Manage LXD Containers
                  </Link>
                </Button>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Resources
                </CardTitle>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  className="h-4 w-4 text-muted-foreground"
                >
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                </svg>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0 / 0</div>
                <p className="text-xs text-muted-foreground">
                  CPU: 0 cores, Memory: 0 GB
                </p>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid gap-4 md:grid-cols-1">
            <Card>
              <CardHeader>
                <CardTitle>Container Management</CardTitle>
                <CardDescription>
                  Manage your containers and resources from a single dashboard
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border rounded-lg p-4">
                      <h3 className="font-medium flex items-center">
                        <Box className="h-4 w-4 mr-2" />
                        LXC Container Management
                      </h3>
                      <p className="text-sm text-muted-foreground mt-2">
                        Create and manage lightweight Linux containers with LXC. Perfect for development environments and isolated workloads.
                      </p>
                      <Button asChild className="mt-4" variant="outline" size="sm">
                        <Link href="/dashboard/containers/lxc">
                          Manage LXC Containers
                        </Link>
                      </Button>
                    </div>
                    
                    <div className="border rounded-lg p-4">
                      <h3 className="font-medium flex items-center">
                        <Container className="h-4 w-4 mr-2" />
                        LXD Container Management
                      </h3>
                      <p className="text-sm text-muted-foreground mt-2">
                        Manage system containers with LXD. Ideal for running full system environments with init systems and multiple processes.
                      </p>
                      <Button asChild className="mt-4" variant="outline" size="sm">
                        <Link href="/dashboard/containers/lxd">
                          Manage LXD Containers
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="lxc" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>LXC Containers</CardTitle>
              <CardDescription>
                Manage your LXC containers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Box className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                <h3 className="text-lg font-medium">No LXC Containers</h3>
                <p className="text-muted-foreground mb-4">
                  You don't have any LXC containers yet
                </p>
                <Button asChild>
                  <Link href="/dashboard/containers/lxc">
                    <Plus className="mr-2 h-4 w-4" />
                    Manage LXC Containers
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="lxd" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>LXD Containers</CardTitle>
              <CardDescription>
                Manage your LXD containers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Container className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                <h3 className="text-lg font-medium">No LXD Containers</h3>
                <p className="text-muted-foreground mb-4">
                  You don't have any LXD containers yet
                </p>
                <Button asChild>
                  <Link href="/dashboard/containers/lxd">
                    <Plus className="mr-2 h-4 w-4" />
                    Manage LXD Containers
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}
