import { Dash<PERSON>Header } from "@/components/dashboard/header"
import { DashboardShell } from "@/components/dashboard/shell"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export default function LxcContainersLoading() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="LXC Containers"
        text="Manage your Linux Containers (LXC) for lightweight virtualization."
      />
      
      <div className="grid gap-4">
        <Card>
          <CardHeader className="gap-2">
            <Skeleton className="h-5 w-40" />
            <Skeleton className="h-4 w-full" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
