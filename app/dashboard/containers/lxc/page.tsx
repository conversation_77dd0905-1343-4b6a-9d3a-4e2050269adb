"use client"

import { <PERSON><PERSON><PERSON>ead<PERSON> } from "@/components/dashboard/header"
import { DashboardShell } from "@/components/dashboard/shell"
import { LxcContainerManager } from "@/components/lxc/lxc-container-manager"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function LxcContainersPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="LXC Containers"
        text="Manage your Linux Containers (LXC) for lightweight virtualization."
      >
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/containers">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Containers
          </Link>
        </Button>
      </DashboardHeader>
      
      <div className="grid gap-4">
        <LxcContainerManager />
      </div>
    </DashboardShell>
  )
}
