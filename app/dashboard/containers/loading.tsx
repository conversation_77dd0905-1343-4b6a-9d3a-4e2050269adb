import { Dash<PERSON>Header } from "@/components/dashboard/header"
import { DashboardShell } from "@/components/dashboard/shell"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export default function ContainersLoading() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Container Management"
        text="Manage your LXC and LXD containers from a single dashboard."
      />
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="gap-2">
            <Skeleton className="h-5 w-1/2" />
          </CardHeader>
          <CardContent className="h-10">
            <Skeleton className="h-8 w-full" />
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="gap-2">
            <Skeleton className="h-5 w-1/2" />
          </CardHeader>
          <CardContent className="h-10">
            <Skeleton className="h-8 w-full" />
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="gap-2">
            <Skeleton className="h-5 w-1/2" />
          </CardHeader>
          <CardContent className="h-10">
            <Skeleton className="h-8 w-full" />
          </CardContent>
        </Card>
      </div>
      
      <div className="grid gap-4 md:grid-cols-1">
        <Card>
          <CardHeader className="gap-2">
            <Skeleton className="h-5 w-40" />
            <Skeleton className="h-4 w-full" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardShell>
  )
}
