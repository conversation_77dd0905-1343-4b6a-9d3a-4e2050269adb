"use client"

import { <PERSON><PERSON><PERSON>ead<PERSON> } from "@/components/dashboard/header"
import { DashboardShell } from "@/components/dashboard/shell"
import { LxdContainerManager } from "@/components/lxd/lxd-container-manager"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function LxdContainersPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="LXD Containers"
        text="Manage your LXD system containers for full system environments."
      >
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/containers">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Containers
          </Link>
        </Button>
      </DashboardHeader>
      
      <div className="grid gap-4">
        <LxdContainerManager />
      </div>
    </DashboardShell>
  )
}
