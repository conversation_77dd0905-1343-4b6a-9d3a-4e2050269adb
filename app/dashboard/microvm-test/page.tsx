"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/components/dashboard/layout"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bs<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Server, HardDrive, AlertTriangle } from "lucide-react"

interface VMImage {
  name: string;
  description: string;
  type: 'kernel' | 'rootfs';
  path: string;
  size: number;
  lastModified: string | Date;
}

interface LXCImage {
  name: string;
  path: string;
  description: string;
  size: number;
  type: 'alpine' | 'ubuntu' | 'other';
  format: 'tar.gz' | 'img' | 'qcow2' | 'other';
}

export default function MicroVmTestPage() {
  const [kernelImages, setKernelImages] = useState<VMImage[]>([]);
  const [rootfsImages, setRootfsImages] = useState<VMImage[]>([]);
  const [lxcImages, setLxcImages] = useState<LXCImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedKernel, setSelectedKernel] = useState<VMImage | null>(null);
  const [selectedRootfs, setSelectedRootfs] = useState<VMImage | null>(null);
  const [selectedLxcImage, setSelectedLxcImage] = useState<LXCImage | null>(null);
  const [isCreatingVM, setIsCreatingVM] = useState(false);
  const [isCreatingLxcVM, setIsCreatingLxcVM] = useState(false);
  const [activeTab, setActiveTab] = useState("microvm")

  const { toast } = useToast();

  // Fetch available images
  useEffect(() => {
    const fetchImages = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await fetch('/api/containerization/microvm/images');
        
        if (!response.ok) {
          throw new Error(`Failed to fetch images: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Format dates to be displayed properly
        const processedKernelImages = data.kernelImages.map((img: VMImage) => ({
          ...img,
          lastModified: new Date(img.lastModified),
        }));
        
        const processedRootfsImages = data.rootfsImages.map((img: VMImage) => ({
          ...img,
          lastModified: new Date(img.lastModified),
        }));
        
        setKernelImages(processedKernelImages);
        setRootfsImages(processedRootfsImages);
        setLxcImages(data.lxcImages || []);
        
        // Set defaults
        if (processedKernelImages.length > 0) {
          setSelectedKernel(processedKernelImages[0]);
        }
        
        if (processedRootfsImages.length > 0) {
          setSelectedRootfs(processedRootfsImages[0]);
        }
        
        if (data.lxcImages && data.lxcImages.length > 0) {
          setSelectedLxcImage(data.lxcImages[0]);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch images');
        toast({
          variant: "destructive",
          title: "Error",
          description: `Failed to fetch VM images: ${err.message || 'Unknown error'}`
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchImages();
  }, [toast]);
  
  // Create VM function
  const createVM = async () => {
    if (!selectedKernel || !selectedRootfs) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select both kernel and rootfs images"
      });
      return;
    }
    
    setIsCreatingVM(true);
    
    try {
      const response = await fetch('/api/containerization/microvm/create-from-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `test-vm-${Date.now().toString().slice(-6)}`,
          kernelImagePath: selectedKernel.path,
          rootfsImagePath: selectedRootfs.path,
          memSizeMib: 2048,
          vcpuCount: 2,
          networkEnabled: true
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create VM: ${response.status}`);
      }
      
      const data = await response.json();
      
      toast({
        title: "Success",
        description: `VM created successfully: ${data.name} (${data.id})`,
      });
    } catch (err: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: `Failed to create VM: ${err.message || 'Unknown error'}`
      });
    } finally {
      setIsCreatingVM(false);
    }
  };
  
  // Create VM from LXC image function
  const createVmFromLxc = async () => {
    if (!selectedLxcImage) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select an LXC image"
      });
      return;
    }
    
    setIsCreatingLxcVM(true);
    
    try {
      const response = await fetch('/api/containerization/microvm/create-with-lxc', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `lxc-vm-${Date.now().toString().slice(-6)}`,
          imageType: selectedLxcImage.type,
          memSizeMib: 2048,
          vcpuCount: 2,
          networkEnabled: true
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create VM from LXC image: ${response.status}`);
      }
      
      const data = await response.json();
      
      toast({
        title: "Success",
        description: `VM created from LXC image successfully: ${data.name} (${data.id})`,
      });
    } catch (err: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: `Failed to create VM from LXC image: ${err.message || 'Unknown error'}`
      });
    } finally {
      setIsCreatingLxcVM(false);
    }
  };

  return (
    <DashboardLayout>
      <DashboardShell>
        <DashboardHeader
          heading="MicroVM Test"
          text="Test MicroVM image loading and VM creation"
        />

        <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="microvm">MicroVM</TabsTrigger>
            <TabsTrigger value="lxc">LXC Container</TabsTrigger>
          </TabsList>
          
          <TabsContent value="microvm" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* Kernel Images */}
              <Card>
                <CardHeader>
                  <CardTitle>Kernel Images</CardTitle>
                  <CardDescription>Available kernel images for MicroVMs</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {isLoading ? (
                    <div className="flex items-center justify-center p-4">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : error ? (
                    <div className="flex items-center rounded-md bg-destructive/10 p-4">
                      <AlertTriangle className="h-5 w-5 text-destructive mr-2" />
                      <p className="text-sm text-destructive">{error}</p>
                    </div>
                  ) : kernelImages.length === 0 ? (
                    <div className="text-center p-4">
                      <p className="text-muted-foreground">No kernel images found</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {kernelImages.map((image) => (
                        <div 
                          key={image.path}
                          className={`
                            flex justify-between items-center p-2 rounded-md
                            ${selectedKernel?.path === image.path 
                              ? 'bg-primary/10 border border-primary/30' 
                              : 'hover:bg-muted cursor-pointer'}
                          `}
                          onClick={() => setSelectedKernel(image)}
                        >
                          <div className="flex items-center">
                            <Server className="h-4 w-4 mr-2 text-muted-foreground" />
                            <div>
                              <p className="font-medium text-sm">{image.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {(image.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Rootfs Images */}
              <Card>
                <CardHeader>
                  <CardTitle>Rootfs Images</CardTitle>
                  <CardDescription>Available root filesystem images</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {isLoading ? (
                    <div className="flex items-center justify-center p-4">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : error ? (
                    <div className="flex items-center rounded-md bg-destructive/10 p-4">
                      <AlertTriangle className="h-5 w-5 text-destructive mr-2" />
                      <p className="text-sm text-destructive">{error}</p>
                    </div>
                  ) : rootfsImages.length === 0 ? (
                    <div className="text-center p-4">
                      <p className="text-muted-foreground">No rootfs images found</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {rootfsImages.map((image) => (
                        <div 
                          key={image.path}
                          className={`
                            flex justify-between items-center p-2 rounded-md
                            ${selectedRootfs?.path === image.path 
                              ? 'bg-primary/10 border border-primary/30' 
                              : 'hover:bg-muted cursor-pointer'}
                          `}
                          onClick={() => setSelectedRootfs(image)}
                        >
                          <div className="flex items-center">
                            <HardDrive className="h-4 w-4 mr-2 text-muted-foreground" />
                            <div>
                              <p className="font-medium text-sm">{image.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {(image.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Create VM Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Create MicroVM</CardTitle>
                  <CardDescription>Create a new MicroVM with selected images</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedKernel && (
                      <div className="rounded-md bg-muted p-2">
                        <p className="font-medium text-sm">Selected Kernel:</p>
                        <p className="text-xs text-muted-foreground">{selectedKernel.name}</p>
                        <p className="text-xs text-muted-foreground">{selectedKernel.path}</p>
                      </div>
                    )}
                    
                    {selectedRootfs && (
                      <div className="rounded-md bg-muted p-2">
                        <p className="font-medium text-sm">Selected Rootfs:</p>
                        <p className="text-xs text-muted-foreground">{selectedRootfs.name}</p>
                        <p className="text-xs text-muted-foreground">{selectedRootfs.path}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    onClick={createVM} 
                    disabled={isCreatingVM || !selectedKernel || !selectedRootfs}
                    className="w-full"
                  >
                    {isCreatingVM ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : "Create MicroVM"}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="lxc" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>LXC Images</CardTitle>
                  <CardDescription>Available LXC container images</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {isLoading ? (
                    <div className="flex items-center justify-center p-4">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : error ? (
                    <div className="flex items-center rounded-md bg-destructive/10 p-4">
                      <AlertTriangle className="h-5 w-5 text-destructive mr-2" />
                      <p className="text-sm text-destructive">{error}</p>
                    </div>
                  ) : lxcImages.length === 0 ? (
                    <div className="text-center p-4">
                      <p className="text-muted-foreground">No LXC images found</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {lxcImages.map((image) => (
                        <div 
                          key={image.path}
                          className={`
                            flex justify-between items-center p-2 rounded-md
                            ${selectedLxcImage?.path === image.path 
                              ? 'bg-primary/10 border border-primary/30' 
                              : 'hover:bg-muted cursor-pointer'}
                          `}
                          onClick={() => setSelectedLxcImage(image)}
                        >
                          <div className="flex items-center">
                            <Server className="h-4 w-4 mr-2 text-muted-foreground" />
                            <div>
                              <p className="font-medium text-sm">{image.name}</p>
                              <p className="text-xs text-muted-foreground">{image.description}</p>
                              <p className="text-xs text-muted-foreground">
                                {image.type} | {image.format}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
                <CardFooter>
                  <Button 
                    onClick={createVmFromLxc}
                    disabled={!selectedLxcImage || isCreatingLxcVM}
                    className="w-full"
                  >
                    {isCreatingLxcVM ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : "Create LXC VM"}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DashboardShell>
    </DashboardLayout>
  )
}