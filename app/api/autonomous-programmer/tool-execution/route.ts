/**
 * API Route for Autonomous Programmer Tool Execution
 *
 * This route allows direct execution of tools from the client side.
 * It integrates with the app-dev services to provide a comprehensive
 * suite of tools for code generation, analysis, and project management.
 */

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { toolRegistry } from "@/lib/ai-agents/tools";
import { z } from "zod";

// Schema for tool execution request
const toolExecutionSchema = z.object({
  toolName: z.string(),
  args: z.record(z.any()),
});

export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();

    // Validate request
    const result = toolExecutionSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid request", details: result.error.format() },
        { status: 400 }
      );
    }

    const { toolName, args } = result.data;

    // Check if tool exists
    const tool = toolRegistry[toolName as keyof typeof toolRegistry];
    if (!tool) {
      return NextResponse.json(
        { error: `Tool '${toolName}' not found` },
        { status: 404 }
      );
    }

    // Execute tool
    try {
      console.log(`Executing tool ${toolName} with args:`, JSON.stringify(args));

      // Record start time for execution timing
      const startTime = Date.now();

      // For long-running operations, update progress to 0%
      if (['implementFeature', 'buildKnowledgeGraph', 'queryKnowledgeGraph'].includes(toolName)) {
        // Update progress to 0%
        await fetch(`${req.nextUrl.origin}/api/autonomous-programmer/tool-progress`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            toolCallId: args.toolCallId || 'unknown',
            toolName,
            projectId: args.projectId,
            progress: 0,
            status: 'running',
            statusMessage: `Starting ${toolName}...`,
          }),
        });
      }

      // Execute the tool
      const toolResult = await tool.execute(args);

      // Calculate execution time
      const executionTime = Date.now() - startTime;

      // Log successful execution
      console.log(`Tool ${toolName} executed successfully in ${executionTime}ms`);

      // For long-running operations, update progress to 100%
      if (['implementFeature', 'buildKnowledgeGraph', 'queryKnowledgeGraph'].includes(toolName)) {
        // Update progress to 100%
        await fetch(`${req.nextUrl.origin}/api/autonomous-programmer/tool-progress`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            toolCallId: args.toolCallId || 'unknown',
            toolName,
            projectId: args.projectId,
            progress: 100,
            status: 'completed',
            statusMessage: `${toolName} completed successfully`,
            result: toolResult,
          }),
        });
      }

      // Record tool execution in history
      try {
        await fetch(`${req.nextUrl.origin}/api/autonomous-programmer/tool-history`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            toolName,
            args,
            result: toolResult,
            status: 'success',
            executionTime,
            projectId: args.projectId,
          }),
        });
      } catch (historyError) {
        // Log error but don't fail the request
        console.error('Error recording tool execution history:', historyError);
      }

      // Return the result
      return NextResponse.json({
        success: true,
        result: toolResult,
        toolName,
        executionTime,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      // Log detailed error information
      console.error(`Error executing tool ${toolName}:`, error);
      console.error(`Tool args:`, JSON.stringify(args));

      // For long-running operations, update progress to failed
      if (['implementFeature', 'buildKnowledgeGraph', 'queryKnowledgeGraph'].includes(toolName)) {
        try {
          // Update progress to failed
          await fetch(`${req.nextUrl.origin}/api/autonomous-programmer/tool-progress`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              toolCallId: args.toolCallId || 'unknown',
              toolName,
              projectId: args.projectId,
              progress: 0,
              status: 'failed',
              statusMessage: `${toolName} failed: ${error.message}`,
            }),
          });
        } catch (progressError) {
          // Log error but don't fail the request
          console.error('Error updating tool progress:', progressError);
        }
      }

      // Record failed tool execution in history
      try {
        await fetch(`${req.nextUrl.origin}/api/autonomous-programmer/tool-history`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            toolName,
            args,
            result: { error: error.message },
            status: 'failure',
            executionTime: 0,
            projectId: args.projectId,
          }),
        });
      } catch (historyError) {
        // Log error but don't fail the request
        console.error('Error recording tool execution history:', historyError);
      }

      // Return structured error response
      return NextResponse.json(
        {
          success: false,
          error: "Tool execution failed",
          message: error.message,
          toolName,
          timestamp: new Date().toISOString(),
          // Include stack trace in development environment only
          ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Error in tool execution route:", error);
    return NextResponse.json(
      { error: "Internal server error", message: error.message },
      { status: 500 }
    );
  }
}
