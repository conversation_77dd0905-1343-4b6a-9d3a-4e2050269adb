/**
 * API Route for Autonomous Programmer Tool Progress
 * 
 * This route allows tracking the progress of long-running tool executions.
 */

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

// In-memory store for tool execution progress
// In a production environment, this would be stored in a database or Redis
const toolProgressStore: Record<string, {
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  statusMessage: string;
  result: any;
  lastUpdated: Date;
}> = {};

export async function GET(req: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const toolCallId = searchParams.get('toolCallId');
    const toolName = searchParams.get('toolName');
    const projectId = searchParams.get('projectId');

    if (!toolCallId || !toolName || !projectId) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Check if project exists and belongs to user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Generate a unique key for the tool execution
    const progressKey = `${projectId}:${toolName}:${toolCallId}`;

    // Get progress from store or return default values
    const progress = toolProgressStore[progressKey] || {
      progress: 0,
      status: 'pending' as const,
      statusMessage: 'Waiting to start...',
      result: null,
      lastUpdated: new Date()
    };

    return NextResponse.json(progress);
  } catch (error: any) {
    console.error("Error in tool progress route:", error);
    return NextResponse.json(
      { error: "Internal server error", message: error.message },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { toolCallId, toolName, projectId, progress, status, statusMessage, result } = body;

    if (!toolCallId || !toolName || !projectId) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Check if project exists and belongs to user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Generate a unique key for the tool execution
    const progressKey = `${projectId}:${toolName}:${toolCallId}`;

    // Update progress in store
    toolProgressStore[progressKey] = {
      progress: progress || 0,
      status: status || 'running',
      statusMessage: statusMessage || 'Processing...',
      result: result || null,
      lastUpdated: new Date()
    };

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error in tool progress route:", error);
    return NextResponse.json(
      { error: "Internal server error", message: error.message },
      { status: 500 }
    );
  }
}

// Clean up old progress entries (older than 1 hour)
setInterval(() => {
  const now = new Date();
  Object.keys(toolProgressStore).forEach(key => {
    const entry = toolProgressStore[key];
    const ageInMs = now.getTime() - entry.lastUpdated.getTime();
    
    // Remove entries older than 1 hour
    if (ageInMs > 60 * 60 * 1000) {
      delete toolProgressStore[key];
    }
  });
}, 15 * 60 * 1000); // Run every 15 minutes
