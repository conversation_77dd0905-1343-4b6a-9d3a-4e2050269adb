/**
 * API Route for Autonomous Programmer Tool Execution History
 * 
 * This route allows retrieving the history of tool executions for a project.
 */

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

export async function GET(req: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const projectId = searchParams.get('projectId');
    const limit = parseInt(searchParams.get('limit') || '50', 10);
    const toolName = searchParams.get('toolName');

    if (!projectId) {
      return NextResponse.json(
        { error: "Project ID is required" },
        { status: 400 }
      );
    }

    // Check if project exists and belongs to user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Build query for tool executions
    const whereClause: any = {
      projectId,
    };

    // Add tool name filter if provided
    if (toolName) {
      whereClause.toolName = toolName;
    }

    // Query tool executions from database
    const toolExecutions = await db.toolExecution.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });

    // Format the response
    const history = toolExecutions.map(execution => ({
      id: execution.id,
      toolName: execution.toolName,
      args: execution.args,
      result: execution.result,
      status: execution.status,
      timestamp: execution.createdAt,
      executionTime: execution.executionTime,
    }));

    return NextResponse.json({ history });
  } catch (error: any) {
    console.error("Error in tool history route:", error);
    return NextResponse.json(
      { error: "Internal server error", message: error.message },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { toolName, args, result, status, executionTime, projectId } = body;

    if (!toolName || !projectId) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Check if project exists and belongs to user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Create tool execution record
    const toolExecution = await db.toolExecution.create({
      data: {
        toolName,
        args,
        result,
        status: status || 'success',
        executionTime,
        projectId,
        userId: session.user.id,
      },
    });

    return NextResponse.json({ success: true, id: toolExecution.id });
  } catch (error: any) {
    console.error("Error in tool history route:", error);
    return NextResponse.json(
      { error: "Internal server error", message: error.message },
      { status: 500 }
    );
  }
}
