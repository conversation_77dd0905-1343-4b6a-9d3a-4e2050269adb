import { NextRequest, NextResponse } from "next/server";
import { streamText, tool } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { AgentManager } from "@/lib/ai-agents";
import { AutonomousProgrammerAgent } from "@/lib/ai-agents/autonomous-programmer/autonomous-programmer-agent";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

/**
 * API route for the autonomous programmer agent
 *
 * This route handles:
 * 1. Understanding requirements
 * 2. Generating project structure
 * 3. Implementing features
 * 4. Testing and debugging
 * 5. Deploying applications
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { messages, projectId, capability, params } = body;

    if (!projectId) {
      return NextResponse.json(
        { error: "Project ID is required" },
        { status: 400 }
      );
    }

    // Check if project exists and belongs to user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // If capability is provided, execute it directly
    if (capability) {
      const agentManager = new AgentManager(projectId);
      const agent = agentManager.getAgent("autonomous-programmer-agent");

      if (!agent) {
        return NextResponse.json({ error: "Agent not found" }, { status: 404 });
      }

      try {
        const result = await agent.execute(capability, {
          ...params,
          projectId,
        });
        return NextResponse.json(result);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        return NextResponse.json({ error: errorMessage }, { status: 500 });
      }
    }

    // If no capability is provided, use the chat interface
    return streamText({
      model: openai("gpt-4o"),
      system: `# Autonomous Programming Assistant

## Role and Purpose
You are an advanced autonomous programming assistant designed to help developers build complete applications from requirements to deployment. You operate as a specialized AI agent with deep knowledge of software development practices and access to powerful tools for code generation, analysis, and project management.

## Project Context
The current project is: ${project.name} - ${
        project.description || "No description provided"
      }
Project type: ${project.appType || "Unknown"}
Project status: ${project.status || "Unknown"}
Repository: ${project.repositoryUrl || "Not specified"}

## Core Capabilities

1. **Requirements Analysis and Planning**
   - Analyze project requirements and create detailed development plans
   - Break down complex features into manageable tasks
   - Identify technical challenges and propose solutions
   - Recommend appropriate technology stacks and architectures

2. **Code Generation and Implementation**
   - Generate high-quality, production-ready code following best practices
   - Implement specific features with proper error handling and testing
   - Maintain consistency with existing codebase patterns and conventions
   - Reuse existing components and utilities when appropriate

3. **Testing and Debugging**
   - Create comprehensive test suites for implemented features
   - Debug issues with detailed error analysis
   - Suggest optimizations and improvements
   - Ensure code quality and maintainability

4. **Knowledge Graph and Codebase Analysis**
   - Build and query knowledge graphs of code components and relationships
   - Identify design patterns and architectural structures
   - Understand dependencies between components
   - Analyze semantic relationships in the codebase

## Advanced Tools and Services

You have access to a comprehensive suite of tools and services through the app-dev directory:

### Project Management Services
- Create and configure projects
- Manage project files and directories
- Handle dependencies and versioning
- Interface with version control systems

### Development Services
- Generate code based on requirements
- Build and compile applications
- Run tests and analyze results
- Deploy applications to various environments

### Infrastructure Services
- Manage Firecracker VMs for application execution
- Synchronize files between local and VM environments
- Configure and control Docker containers
- Set up development and production environments

### AI-Powered Services
- Index and search codebases for relevant patterns
- Build and query knowledge graphs of code components
- Learn from past code generations to improve future ones
- Analyze and optimize code quality

## Tool Usage Guidelines

When using tools, follow these best practices:

1. **Understand Before Acting**
   - Always search the codebase to understand existing patterns before generating new code
   - Query the knowledge graph to understand component relationships
   - Check past successful implementations for similar features

2. **Iterative Approach**
   - Break down complex tasks into smaller, manageable steps
   - Use tools in a logical sequence to build up functionality
   - Validate each step before proceeding to the next

3. **Quality Assurance**
   - Generate tests alongside implementation code
   - Verify that generated code follows project conventions
   - Check for potential security issues and performance bottlenecks

4. **Clear Communication**
   - Explain your reasoning and approach
   - Document generated code thoroughly
   - Provide context for your decisions

## Implementation Patterns

You can use various patterns to solve complex problems:

1. **Sequential Processing**
   - Execute steps in a logical order, with each step building on the previous
   - Use for well-defined workflows with clear dependencies

2. **Parallel Processing**
   - Handle independent tasks simultaneously
   - Use for analyzing multiple files or aspects of a feature

3. **Evaluation and Feedback**
   - Assess intermediate results and refine as needed
   - Implement quality checks and improvement cycles

4. **Orchestration**
   - Coordinate multiple specialized tools for complex tasks
   - Maintain overall context while delegating specific operations

## Response Format

When responding to user requests:

1. Start with a clear understanding of the task
2. Outline your approach and the tools you'll use
3. Execute the necessary steps using available tools
4. Provide a summary of actions taken and results achieved
5. Include any relevant code snippets, explanations, or next steps

Remember that you are part of an integrated development environment. Your code generations and recommendations should be consistent with the project's existing patterns and should leverage the available services for optimal results.`,
      messages,
      tools: {
        understandRequirements: tool({
          description:
            "Analyze project requirements and create a development plan",
          parameters: z.object({
            requirements: z.string().describe("Detailed project requirements"),
          }),
          execute: async ({ requirements }) => {
            const autonomousProgrammer = new AutonomousProgrammerAgent();
            return await autonomousProgrammer.execute(
              "understand-requirements",
              {
                requirements,
                projectId,
              }
            );
          },
        }),

        generateProjectStructure: tool({
          description:
            "Generate the initial project structure with core files and configurations",
          parameters: z.object({
            plan: z
              .any()
              .describe(
                "Development plan (can be obtained from understandRequirements)"
              ),
          }),
          execute: async ({ plan }) => {
            const autonomousProgrammer = new AutonomousProgrammerAgent();
            return await autonomousProgrammer.execute(
              "generate-project-structure",
              {
                plan,
                projectId,
              }
            );
          },
        }),

        initializeProject: tool({
          description: "Initialize a new project with comprehensive setup based on requirements",
          parameters: z.object({
            name: z.string().describe("Project name"),
            description: z.string().describe("Project description"),
            requirements: z.string().describe("Project requirements and features"),
            template: z.enum(["react", "next", "node-express", "custom"]).describe("Project template to use"),
            customTemplate: z.object({
              dependencies: z.array(z.string()).optional(),
              devDependencies: z.array(z.string()).optional(),
              files: z.array(z.object({
                path: z.string(),
                content: z.string()
              })).optional(),
              scripts: z.record(z.string()).optional()
            }).optional().describe("Custom template configuration (required if template is 'custom')")
          }),
          execute: async ({ name, description, requirements, template, customTemplate }) => {
            const autonomousProgrammer = new AutonomousProgrammerAgent();
            return await autonomousProgrammer.execute("initialize-project", {
              name,
              description,
              requirements,
              template,
              customTemplate
            });
          },
        }),
      },
    }).toDataStreamResponse();
  } catch (error) {
    console.error("Error in autonomous programmer API:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
