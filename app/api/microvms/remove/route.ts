import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { logger } from '@/lib/logger';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

// Remove a MicroVM
export async function POST(request: Request) {
  try {
    // Check authentication if configured
    let userEmail = 'anonymous';
    let session: any = null;
    
    try {
      session = await getServerSession(authOptions);
      if (session && typeof session === 'object' && 'user' in session && session.user && typeof session.user === 'object' && 'email' in session.user) {
        userEmail = session.user.email as string;
      }
    } catch (authError) {
      logger.warn('Authentication error or not configured:', authError);
    }
    
    // Parse request body
    const body = await request.json();
    const { vmId } = body;

    if (!vmId) {
      return NextResponse.json({ 
        error: 'Missing required parameter: vmId'
      }, { status: 400 });
    }
    
    logger.info(`Removing MicroVM with ID: ${vmId}`);

    // Get the MicroVM and delete it
    try {
      const microvm = microVmManager.getMicroVm(vmId);
      
      if (!microvm) {
        return NextResponse.json({ 
          error: `MicroVM with ID '${vmId}' not found`
        }, { status: 404 });
      }
      
      // Stop the VM if it's running
      const vmInfo = await microvm.getInfo();
      if (vmInfo.state.toLowerCase() === 'running') {
        await microvm.stop();
      }
      
      // Delete the VM
      await microVmManager.deleteMicroVm(vmId);
      
      return NextResponse.json({
        success: true,
        message: `MicroVM '${vmId}' removed successfully`,
      });
    } catch (error) {
      throw error;
    }
  } catch (error) {
    logger.error('Error removing MicroVM:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json({ error: error.message }, { status: 404 });
      }
      
      return NextResponse.json({ 
        error: 'Failed to remove MicroVM',
        message: error.message,
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      error: 'Failed to remove MicroVM',
      message: 'Unknown error occurred',
    }, { status: 500 });
  }
} 