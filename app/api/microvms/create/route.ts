import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { logger } from '@/lib/logger';
import { findKernelImage, findRootfsImage } from '@/lib/containerization/microvm/utils';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

// Create a new MicroVM
export async function POST(request: Request) {
  try {
    // Check authentication if configured (might be optional in dev environment)
    let userEmail = 'anonymous';
    let session: any = null;
    
    try {
      session = await getServerSession(authOptions);
      if (session && typeof session === 'object' && 'user' in session && session.user && typeof session.user === 'object' && 'email' in session.user) {
        userEmail = session.user.email as string;
      }
    } catch (authError) {
      logger.warn('Authentication error or not configured:', authError);
      // Continue without authentication
    }
    
    // Parse request body
    const body = await request.json();
    const {
      name,
      template = 'nextjs',
      memSizeMib = 2048,
      vcpuCount = 2,
      networkEnabled = true,
      projectId = 'default-project',
    } = body;

    // Generate a unique name if not provided
    const vmName = name || `micro-vm-${Date.now().toString().slice(-6)}`;
    
    logger.info(`Creating MicroVM '${vmName}' with template '${template}'`);

    // Find kernel and rootfs images
    const kernelPath = findKernelImage() || '/path/to/default-vmlinux.bin';
    const rootfsPath = findRootfsImage() || '/path/to/default-rootfs.ext4';

    // Create options object with mandatory kernel and rootfs properties
    const options = {
      name: vmName,
      memSizeMib,
      vcpuCount,
      networkEnabled,
      kernel: {
        path: kernelPath,
        bootArgs: "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp",
      },
      rootfs: {
        path: rootfsPath,
        readOnly: false,
      },
      metadata: {
        annotations: {
          projectId: projectId?.toString(),
          createdBy: userEmail,
          createdAt: new Date().toISOString(),
          template,
        }
      }
    };

    // Create the MicroVM
    const microvm = await microVmManager.createMicroVm(options);
    
    // Start the MicroVM
    await microvm.start();
    
    // Get VM info
    const vmInfo = await microvm.getInfo();
    
    return NextResponse.json({
      id: vmInfo.id,
      name: vmInfo.name,
      status: vmInfo.state,
      template,
      resources: {
        memSizeMib,
        vcpuCount,
      },
      message: `MicroVM '${vmName}' created and started successfully`,
    }, { status: 201 });
  } catch (error) {
    logger.error('Error creating MicroVM:', error);
    
    if (error instanceof Error) {
      // Handle specific error types
      if (error.name === 'AlreadyExistsError') {
        return NextResponse.json({ error: error.message }, { status: 409 });
      }
      
      if (error.name === 'ResourceExhaustionError') {
        return NextResponse.json({ error: error.message }, { status: 507 });
      }
      
      if (error.message.includes('ENOENT')) {
        return NextResponse.json({ 
          error: 'Server configuration error: A required file was not found',
          details: error.message
        }, { status: 500 });
      }
    }
    
    // Generic error
    return NextResponse.json({ 
      error: 'Failed to create MicroVM',
      message: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
} 