/**
 * MicroVM API Routes - Project Synchronization
 * 
 * This file handles API routes for microVM project synchronization.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '@/lib/logger';
import { MicroVmManager } from '@/lib/containerization/microvm/core/microvm-manager';
import { MicroVmState } from '@/lib/containerization/microvm/models';
import { ContainerizationError, NotFoundError } from '@/lib/containerization/shared/error-handling';
import { retry } from '@/lib/containerization/shared/retry';
import path from 'path';
import fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Initialize the microVM manager
const microVmManager = new MicroVmManager();

/**
 * Handle API errors
 * @param res Response object
 * @param error Error to handle
 */
function handleApiError(res: NextApiResponse, error: any): void {
  logger.error('MicroVM sync API error:', error);
  
  if (error instanceof NotFoundError) {
    res.status(404).json({ error: error.message });
  } else if (error instanceof ContainerizationError) {
    res.status(error.status || 500).json({ error: error.message });
  } else {
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Synchronize files to a microVM
 * @param req Request object
 * @param res Response object
 */
async function syncToMicroVm(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const {
      sourceDir,
      destDir,
      exclude,
      delete: deleteExtraFiles,
      recursive,
      preservePermissions,
      preserveTimestamps,
      compress,
    } = req.body;
    
    if (!sourceDir || typeof sourceDir !== 'string') {
      res.status(400).json({ error: 'Missing or invalid source directory' });
      return;
    }
    
    if (!destDir || typeof destDir !== 'string') {
      res.status(400).json({ error: 'Missing or invalid destination directory' });
      return;
    }
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    if (microVm.getState() !== MicroVmState.RUNNING) {
      res.status(400).json({ error: `MicroVM is not running (current state: ${microVm.getState()})` });
      return;
    }
    
    // Ensure the destination directory exists
    await retry(
      () => microVm.executeCommand(`mkdir -p "${destDir}"`),
      {
        maxAttempts: 3,
        context: 'MicroVM directory creation',
      }
    );
    
    // In a real implementation, this would use a proper file transfer mechanism
    // For this example, we'll simulate it with a simple command
    
    // Build rsync-like command for synchronization
    const excludeArgs = (exclude || [])
      .map((pattern: string) => `--exclude="${pattern}"`)
      .join(' ');
    
    const deleteArg = deleteExtraFiles ? '--delete' : '';
    const recursiveArg = recursive !== false ? '-r' : '';
    const preserveArg = preservePermissions !== false ? '-p' : '';
    const timestampArg = preserveTimestamps !== false ? '-t' : '';
    const compressArg = compress !== false ? '-z' : '';
    
    // In a real implementation, this would be a proper rsync or similar command
    // For this example, we'll simulate it with a simple command
    const syncCommand = `echo "Simulating sync from ${sourceDir} to ${destDir} with options: ${excludeArgs} ${deleteArg} ${recursiveArg} ${preserveArg} ${timestampArg} ${compressArg}"`;
    
    const output = await retry(
      () => microVm.executeCommand(syncCommand),
      {
        maxAttempts: 3,
        context: 'MicroVM sync command',
      }
    );
    
    logger.info(`Sync to microVM output: ${output}`);
    
    // In a real implementation, parse the output to get statistics
    const filesTransferred = 10; // Simulated value
    const bytesTransferred = 1024 * 1024; // Simulated value
    
    res.status(200).json({
      success: true,
      filesTransferred,
      bytesTransferred,
      timeTaken: 1000, // Simulated value
    });
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Synchronize files from a microVM
 * @param req Request object
 * @param res Response object
 */
async function syncFromMicroVm(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const {
      sourceDir,
      destDir,
      exclude,
      delete: deleteExtraFiles,
      recursive,
      preservePermissions,
      preserveTimestamps,
      compress,
    } = req.body;
    
    if (!sourceDir || typeof sourceDir !== 'string') {
      res.status(400).json({ error: 'Missing or invalid source directory' });
      return;
    }
    
    if (!destDir || typeof destDir !== 'string') {
      res.status(400).json({ error: 'Missing or invalid destination directory' });
      return;
    }
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    if (microVm.getState() !== MicroVmState.RUNNING) {
      res.status(400).json({ error: `MicroVM is not running (current state: ${microVm.getState()})` });
      return;
    }
    
    // In a real implementation, this would use a proper file transfer mechanism
    // For this example, we'll simulate it with a simple command
    const syncCommand = `echo "Simulating sync from ${sourceDir} to ${destDir}"`;
    
    const output = await retry(
      () => microVm.executeCommand(syncCommand),
      {
        maxAttempts: 3,
        context: 'MicroVM sync command',
      }
    );
    
    logger.info(`Sync from microVM output: ${output}`);
    
    // In a real implementation, parse the output to get statistics
    const filesTransferred = 5; // Simulated value
    const bytesTransferred = 512 * 1024; // Simulated value
    
    res.status(200).json({
      success: true,
      filesTransferred,
      bytesTransferred,
      timeTaken: 500, // Simulated value
    });
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Handle microVM sync API requests
 * @param req Request object
 * @param res Response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Only allow POST requests
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    res.status(405).json({ error: `Method ${req.method} not allowed` });
    return;
  }
  
  // Check the sync direction
  const { direction } = req.body;
  
  if (direction === 'from') {
    await syncFromMicroVm(req, res);
  } else {
    await syncToMicroVm(req, res);
  }
}
