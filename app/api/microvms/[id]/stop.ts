/**
 * MicroVM API Routes - Stop MicroVM
 * 
 * This file handles API routes for stopping a microVM.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { stopMicroVm } from '@/lib/containerization/microvm/api/handlers';

/**
 * Handle microVM stop API requests
 * @param req Request object
 * @param res Response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Check the HTTP method
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    res.status(405).json({ error: `Method ${req.method} not allowed` });
    return;
  }
  
  // Stop the microVM
  await stopMicroVm(req, res);
}
