/**
 * MicroVM API Routes - File System
 * 
 * This file handles API routes for microVM file system operations.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '@/lib/logger';
import { MicroVmManager } from '@/lib/containerization/microvm/core/microvm-manager';
import { MicroVmState } from '@/lib/containerization/microvm/models';
import { ContainerizationError, NotFoundError } from '@/lib/containerization/shared/error-handling';
import { retry } from '@/lib/containerization/shared/retry';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import { Readable } from 'stream';

// Initialize the microVM manager
const microVmManager = new MicroVmManager();

/**
 * Handle API errors
 * @param res Response object
 * @param error Error to handle
 */
function handleApiError(res: NextApiResponse, error: any): void {
  logger.error('MicroVM file system API error:', error);
  
  if (error instanceof NotFoundError) {
    res.status(404).json({ error: error.message });
  } else if (error instanceof ContainerizationError) {
    res.status(error.status || 500).json({ error: error.message });
  } else {
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * List files in a directory
 * @param req Request object
 * @param res Response object
 */
async function listFiles(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const { path: dirPath = '/' } = req.query;
    
    if (typeof dirPath !== 'string') {
      res.status(400).json({ error: 'Invalid directory path' });
      return;
    }
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    if (microVm.getState() !== MicroVmState.RUNNING) {
      res.status(400).json({ error: `MicroVM is not running (current state: ${microVm.getState()})` });
      return;
    }
    
    // Execute ls command with detailed output
    const output = await retry(
      () => microVm.executeCommand(`ls -la --time-style=long-iso "${dirPath}"`),
      {
        maxAttempts: 3,
        context: 'MicroVM file listing',
      }
    );
    
    // Parse the output
    const lines = output.trim().split('\n');
    
    // Skip the first line (total) and parse each line
    const files = lines.slice(1).map(line => {
      // Example line: drwxr-xr-x 2 <USER> <GROUP> 4096 2023-01-01 12:00 directory_name
      const parts = line.trim().split(/\s+/);
      
      // Need at least 8 parts for a valid line
      if (parts.length < 8) {
        return null;
      }
      
      const permissions = parts[0];
      const isDirectory = permissions.startsWith('d');
      const owner = parts[2];
      const group = parts[3];
      const size = parseInt(parts[4], 10);
      
      // Date and time are parts[5] and parts[6]
      const dateStr = `${parts[5]} ${parts[6]}`;
      const modifiedTime = new Date(dateStr);
      
      // The name is everything after the date and time
      const name = parts.slice(7).join(' ');
      
      return {
        name,
        path: `${dirPath}${dirPath.endsWith('/') ? '' : '/'}${name}`,
        isDirectory,
        size,
        modifiedTime,
        permissions,
        owner,
        group,
      };
    }).filter(Boolean);
    
    res.status(200).json(files);
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Read a file
 * @param req Request object
 * @param res Response object
 */
async function readFile(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const { path: filePath } = req.query;
    
    if (!filePath || typeof filePath !== 'string') {
      res.status(400).json({ error: 'Missing or invalid file path' });
      return;
    }
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    if (microVm.getState() !== MicroVmState.RUNNING) {
      res.status(400).json({ error: `MicroVM is not running (current state: ${microVm.getState()})` });
      return;
    }
    
    // Check if the file exists
    const checkResult = await retry(
      () => microVm.executeCommand(`test -f "${filePath}" && echo "File exists"`),
      {
        maxAttempts: 3,
        context: 'MicroVM file check',
      }
    );
    
    if (!checkResult.includes('File exists')) {
      res.status(404).json({ error: `File not found: ${filePath}` });
      return;
    }
    
    // Read the file content
    const content = await retry(
      () => microVm.executeCommand(`cat "${filePath}"`),
      {
        maxAttempts: 3,
        context: 'MicroVM file read',
      }
    );
    
    // Set the content type based on the file extension
    const ext = path.extname(filePath).toLowerCase();
    
    switch (ext) {
      case '.json':
        res.setHeader('Content-Type', 'application/json');
        break;
      case '.txt':
        res.setHeader('Content-Type', 'text/plain');
        break;
      case '.html':
        res.setHeader('Content-Type', 'text/html');
        break;
      case '.css':
        res.setHeader('Content-Type', 'text/css');
        break;
      case '.js':
        res.setHeader('Content-Type', 'application/javascript');
        break;
      default:
        res.setHeader('Content-Type', 'application/octet-stream');
    }
    
    // Set the content disposition
    res.setHeader('Content-Disposition', `inline; filename="${path.basename(filePath)}"`);
    
    res.status(200).send(content);
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Write a file
 * @param req Request object
 * @param res Response object
 */
async function writeFile(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const { path: filePath } = req.query;
    
    if (!filePath || typeof filePath !== 'string') {
      res.status(400).json({ error: 'Missing or invalid file path' });
      return;
    }
    
    const { content } = req.body;
    
    if (!content || typeof content !== 'string') {
      res.status(400).json({ error: 'Missing or invalid file content' });
      return;
    }
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    if (microVm.getState() !== MicroVmState.RUNNING) {
      res.status(400).json({ error: `MicroVM is not running (current state: ${microVm.getState()})` });
      return;
    }
    
    // Create a temporary file with the content
    const tempFile = `/tmp/microvm-file-${Date.now()}`;
    
    await retry(
      () => microVm.executeCommand(`cat > "${tempFile}" << 'EOF'\n${content}\nEOF`),
      {
        maxAttempts: 3,
        context: 'MicroVM file write',
      }
    );
    
    // Move the temporary file to the destination
    await retry(
      () => microVm.executeCommand(`mv "${tempFile}" "${filePath}"`),
      {
        maxAttempts: 3,
        context: 'MicroVM file move',
      }
    );
    
    res.status(200).json({ success: true, path: filePath });
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Delete a file or directory
 * @param req Request object
 * @param res Response object
 */
async function deleteFileOrDirectory(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      res.status(400).json({ error: 'Missing or invalid microVM ID' });
      return;
    }
    
    const { path: targetPath } = req.query;
    
    if (!targetPath || typeof targetPath !== 'string') {
      res.status(400).json({ error: 'Missing or invalid path' });
      return;
    }
    
    const { recursive } = req.query;
    const isRecursive = recursive === 'true';
    
    const microVm = microVmManager.getMicroVm(id);
    
    if (!microVm) {
      res.status(404).json({ error: `MicroVM with ID '${id}' not found` });
      return;
    }
    
    if (microVm.getState() !== MicroVmState.RUNNING) {
      res.status(400).json({ error: `MicroVM is not running (current state: ${microVm.getState()})` });
      return;
    }
    
    // Check if the path exists
    const checkResult = await retry(
      () => microVm.executeCommand(`test -e "${targetPath}" && echo "Path exists"`),
      {
        maxAttempts: 3,
        context: 'MicroVM path check',
      }
    );
    
    if (!checkResult.includes('Path exists')) {
      res.status(404).json({ error: `Path not found: ${targetPath}` });
      return;
    }
    
    // Check if it's a directory
    const isDirResult = await retry(
      () => microVm.executeCommand(`test -d "${targetPath}" && echo "Is directory"`),
      {
        maxAttempts: 3,
        context: 'MicroVM directory check',
      }
    );
    
    const isDirectory = isDirResult.includes('Is directory');
    
    // Delete the file or directory
    if (isDirectory) {
      if (isRecursive) {
        await retry(
          () => microVm.executeCommand(`rm -rf "${targetPath}"`),
          {
            maxAttempts: 3,
            context: 'MicroVM recursive directory delete',
          }
        );
      } else {
        await retry(
          () => microVm.executeCommand(`rmdir "${targetPath}"`),
          {
            maxAttempts: 3,
            context: 'MicroVM directory delete',
          }
        );
      }
    } else {
      await retry(
        () => microVm.executeCommand(`rm "${targetPath}"`),
        {
          maxAttempts: 3,
          context: 'MicroVM file delete',
        }
      );
    }
    
    res.status(200).json({ success: true, path: targetPath });
  } catch (error) {
    handleApiError(res, error);
  }
}

/**
 * Handle microVM file system API requests
 * @param req Request object
 * @param res Response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Check the HTTP method
  switch (req.method) {
    case 'GET':
      // Check if we're reading a file or listing a directory
      if (req.query.action === 'read') {
        await readFile(req, res);
      } else {
        await listFiles(req, res);
      }
      break;
    
    case 'POST':
    case 'PUT':
      // Write a file
      await writeFile(req, res);
      break;
    
    case 'DELETE':
      // Delete a file or directory
      await deleteFileOrDirectory(req, res);
      break;
    
    default:
      // Method not allowed
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
      res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}

// Configure API to handle large file uploads
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
};
