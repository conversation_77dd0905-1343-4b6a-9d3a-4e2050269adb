/**
 * MicroVM API Routes - Individual MicroVM
 * 
 * This file handles API routes for individual microVM operations.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getMicroVm, deleteMicroVm } from '@/lib/containerization/microvm/api/handlers';

/**
 * Handle individual microVM API requests
 * @param req Request object
 * @param res Response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Check the HTTP method
  switch (req.method) {
    case 'GET':
      // Get a microVM by ID
      await getMicroVm(req, res);
      break;
    
    case 'DELETE':
      // Delete a microVM
      await deleteMicroVm(req, res);
      break;
    
    default:
      // Method not allowed
      res.setHeader('Allow', ['GET', 'DELETE']);
      res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}
