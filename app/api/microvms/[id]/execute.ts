/**
 * MicroVM API Routes - Execute Command
 * 
 * This file handles API routes for executing commands in a microVM.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { executeCommand } from '@/lib/containerization/microvm/api/handlers';

/**
 * Handle microVM command execution API requests
 * @param req Request object
 * @param res Response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Check the HTTP method
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    res.status(405).json({ error: `Method ${req.method} not allowed` });
    return;
  }
  
  // Execute the command
  await executeCommand(req, res);
}
