/**
 * MicroVM API Routes - Terminal WebSocket
 * 
 * This file handles WebSocket connections for microVM terminal interaction.
 */

import { NextApiRequest } from 'next';
import { Server as SocketIOServer } from 'socket.io';
import { logger } from '@/lib/logger';
import { MicroVmManager } from '@/lib/containerization/microvm/core/microvm-manager';
import { MicroVmState } from '@/lib/containerization/microvm/models';
import { retry } from '@/lib/containerization/shared/retry';

// Initialize the microVM manager
const microVmManager = new MicroVmManager();

// Terminal session data
interface TerminalSession {
  id: string;
  microVmId: string;
  socketId: string;
  createdAt: Date;
  lastActivity: Date;
}

// Store active terminal sessions
const activeSessions: Map<string, TerminalSession> = new Map();

// Clean up inactive sessions every 5 minutes
const CLEANUP_INTERVAL = 5 * 60 * 1000;
const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes

setInterval(() => {
  const now = Date.now();
  
  for (const [sessionId, session] of activeSessions.entries()) {
    if (now - session.lastActivity.getTime() > SESSION_TIMEOUT) {
      logger.info(`Cleaning up inactive terminal session: ${sessionId}`);
      activeSessions.delete(sessionId);
    }
  }
}, CLEANUP_INTERVAL);

/**
 * Handle WebSocket connections for microVM terminal interaction
 * @param req Request object
 * @param res Response object
 */
export default function handler(req: NextApiRequest, res: any): void {
  // Check if socket.io server is already initialized
  if (res.socket.server.io) {
    logger.debug('Socket.io already initialized');
    res.end();
    return;
  }
  
  // Get the microVM ID from the request
  const { id: microVmId } = req.query;
  
  if (!microVmId || typeof microVmId !== 'string') {
    logger.error('Missing or invalid microVM ID');
    res.status(400).json({ error: 'Missing or invalid microVM ID' });
    return;
  }
  
  // Initialize socket.io server
  const io = new SocketIOServer(res.socket.server, {
    path: `/api/microvms/${microVmId}/terminal`,
    addTrailingSlash: false,
  });
  
  res.socket.server.io = io;
  
  // Handle socket.io connections
  io.on('connection', (socket) => {
    logger.info(`New terminal connection for microVM: ${microVmId}`);
    
    // Generate a unique session ID
    const sessionId = `term-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // Create a new terminal session
    const session: TerminalSession = {
      id: sessionId,
      microVmId,
      socketId: socket.id,
      createdAt: new Date(),
      lastActivity: new Date(),
    };
    
    // Store the session
    activeSessions.set(sessionId, session);
    
    // Send the session ID to the client
    socket.emit('session', { id: sessionId });
    
    // Handle terminal input
    socket.on('input', async (data: { input: string }) => {
      try {
        // Update last activity
        session.lastActivity = new Date();
        
        // Get the microVM
        const microVm = microVmManager.getMicroVm(microVmId);
        
        if (!microVm) {
          socket.emit('error', { message: `MicroVM with ID '${microVmId}' not found` });
          return;
        }
        
        if (microVm.getState() !== MicroVmState.RUNNING) {
          socket.emit('error', { message: `MicroVM is not running (current state: ${microVm.getState()})` });
          return;
        }
        
        // Execute the command
        const output = await retry(
          () => microVm.executeCommand(data.input),
          {
            maxAttempts: 3,
            context: 'MicroVM terminal command execution',
          }
        );
        
        // Send the output to the client
        socket.emit('output', { output });
      } catch (error) {
        logger.error('Failed to execute terminal command:', error);
        socket.emit('error', { message: error instanceof Error ? error.message : String(error) });
      }
    });
    
    // Handle terminal resize
    socket.on('resize', async (data: { rows: number; cols: number }) => {
      try {
        // Update last activity
        session.lastActivity = new Date();
        
        // Get the microVM
        const microVm = microVmManager.getMicroVm(microVmId);
        
        if (!microVm) {
          socket.emit('error', { message: `MicroVM with ID '${microVmId}' not found` });
          return;
        }
        
        if (microVm.getState() !== MicroVmState.RUNNING) {
          socket.emit('error', { message: `MicroVM is not running (current state: ${microVm.getState()})` });
          return;
        }
        
        // Set terminal dimensions
        await microVm.executeCommand(`export LINES=${data.rows} COLUMNS=${data.cols}`);
        
        // Acknowledge the resize
        socket.emit('resize_ack', { rows: data.rows, cols: data.cols });
      } catch (error) {
        logger.error('Failed to resize terminal:', error);
        socket.emit('error', { message: error instanceof Error ? error.message : String(error) });
      }
    });
    
    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`Terminal connection closed for microVM: ${microVmId}`);
      
      // Remove the session
      activeSessions.delete(sessionId);
    });
  });
  
  logger.info(`Socket.io initialized for microVM: ${microVmId}`);
  res.end();
}

// Disable body parsing, we'll handle it ourselves with socket.io
export const config = {
  api: {
    bodyParser: false,
  },
};
