import { NextRequest, NextResponse } from 'next/server';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { spawn } from 'child_process';

const execAsync = promisify(exec);

// Check if this is a WebSocket request
function isWebSocketRequest(req: NextRequest) {
  const { headers } = req;
  return (
    headers.get('connection')?.toLowerCase().includes('upgrade') &&
    headers.get('upgrade')?.toLowerCase() === 'websocket'
  );
}

/**
 * Terminal API endpoint for VMs
 * 
 * Handles both WebSocket connections and POST requests for command execution
 */
export async function GET(
  req: NextRequest, 
  { params }: { params: { id: string } }
) {
  const { id } = params;
  
  // Check if this is a WebSocket request
  if (isWebSocketRequest(req)) {
    try {
      // For WebSocket connections, we try to forward to the containerization API
      const url = new URL(req.url);
      const forwardUrl = `${url.protocol}//${url.host}/api/containerization/microvm/${id}/terminal`;
      
      return NextResponse.redirect(forwardUrl, 307);
    } catch (error) {
      console.error('Terminal WebSocket connection error:', error);
      return NextResponse.json(
        { 
          error: 'Failed to establish WebSocket terminal connection',
          details: error instanceof Error ? error.message : String(error)
        },
        { status: 500 }
      );
    }
  }
  
  // For regular GET requests, return status
  return NextResponse.json({ status: 'Terminal API ready', vmId: id });
}

/**
 * Terminal API endpoint for command execution
 * 
 * Handles POST requests for executing commands
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  let body;
  
  try {
    body = await req.json();
  } catch (error) {
    return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
  }
  
  const { command } = body;
  
  if (!command) {
    return NextResponse.json({ error: 'Command is required' }, { status: 400 });
  }

  try {
    // Check if this is a directly created VM
    const vmDir = `/tmp/microvms/${id}`;
    const metadataPath = path.join(vmDir, 'metadata', 'vm.json');
    
    // If it's a direct VM, we'll handle it here
    if (fs.existsSync(metadataPath)) {
      // Read the VM metadata
      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf-8'));
      const socketPath = metadata.socketPath;
      
      // Check if VM is running
      if (!fs.existsSync(socketPath)) {
        return NextResponse.json(
          { error: 'VM is not running (socket not found)' },
          { status: 400 }
        );
      }
      
      // Execute the command
      try {
        const { stdout, stderr } = await execAsync(command, {
          timeout: 10000, // 10-second timeout
        });
        
        return NextResponse.json({
          success: true,
          stdout,
          stderr
        });
      } catch (error: any) {
        return NextResponse.json({
          success: false,
          error: error.message,
          stdout: error.stdout || '',
          stderr: error.stderr || ''
        });
      }
    }
    
    // Not a direct VM - try to connect to the VM's shell via API
    try {
      // Execute command via the containerization API
      const response = await fetch(`http://localhost:3082/api/containerization/microvm/${id}/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          command,
          options: {
            timeout: 10000
          }
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        return NextResponse.json({
          success: true,
          stdout: data.stdout || '',
          stderr: data.stderr || ''
        });
      } else {
        // If the containerization API fails, fall back to direct command execution
        const { stdout, stderr } = await execAsync(command, {
          timeout: 10000,
        });
        
        return NextResponse.json({
          success: true,
          stdout,
          stderr
        });
      }
    } catch (error: any) {
      // If all methods fail, return the error
      return NextResponse.json({
        success: false,
        error: error.message,
        stdout: error.stdout || '',
        stderr: error.stderr || ''
      });
    }
  } catch (error) {
    console.error('Terminal command execution error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to execute terminal command',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
} 