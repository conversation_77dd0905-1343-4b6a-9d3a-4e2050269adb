/**
 * MicroVM API Routes
 * 
 * This file handles API routes for microVM operations.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createMicroVm, listMicroVms } from '@/lib/containerization/microvm/api/handlers';

/**
 * Handle microVM API requests
 * @param req Request object
 * @param res Response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Check the HTTP method
  switch (req.method) {
    case 'GET':
      // List all microVMs
      await listMicroVms(req, res);
      break;
    
    case 'POST':
      // Create a new microVM
      await createMicroVm(req, res);
      break;
    
    default:
      // Method not allowed
      res.setHeader('Allow', ['GET', 'POST']);
      res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}
