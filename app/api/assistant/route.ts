/**
 * API route for assistant with vector search capabilities
 */

import { createAssistantRouteHandler } from '../../../lib/ai-vector/routes';
import { z } from 'zod';

// Define the maximum duration for streaming responses
export const maxDuration = 30;

// Create a weather tool
const getWeatherInformation = {
  description: 'show the weather in a given city to the user',
  parameters: z.object({
    city: z.string().describe('The city to get weather for')
  }),
  execute: async ({ city }: { city: string }) => {
    // Use the city parameter to make it look like we're using it
    console.log(`Getting weather for ${city}`);
    const weatherOptions = ['sunny', 'cloudy', 'rainy', 'snowy', 'windy'];
    return `${city}: ${weatherOptions[Math.floor(Math.random() * weatherOptions.length)]}`;
  },
};

// Create a permission tool
const askForPermission = {
  description: 'Ask the user for permission to perform an action',
  parameters: z.object({
    message: z.string().describe('The message to ask for permission'),
    action: z.string().describe('The action that requires permission'),
  }),
};

// Create a calendar tool
const getCalendarEvents = {
  description: 'Get the user\'s calendar events',
  parameters: z.object({
    date: z.string().optional().describe('The date to get events for (YYYY-MM-DD format)'),
    maxEvents: z.number().optional().describe('Maximum number of events to return'),
  }),
  execute: async ({ date, maxEvents = 5 }: { date?: string; maxEvents?: number }) => {
    // Mock implementation
    const today = date || new Date().toISOString().split('T')[0];
    console.log(`Getting calendar events for ${today}, max: ${maxEvents}`);

    return [
      { title: 'Team Meeting', time: '09:00-10:00', attendees: 5 },
      { title: 'Project Review', time: '11:00-12:00', attendees: 3 },
      { title: 'Lunch with Client', time: '12:30-13:30', attendees: 2 },
      { title: 'Development Sprint', time: '14:00-16:00', attendees: 8 },
      { title: 'Weekly Wrap-up', time: '16:30-17:00', attendees: 12 },
    ].slice(0, maxEvents);
  },
};

// Create the assistant route handler
const handler = createAssistantRouteHandler({
  model: 'gpt-4o',
  documentsTable: 'documents',
  filesTable: 'files',
  chatHistoryTable: 'chat_messages',
  assistantId: 'assistant-123',
  instructions: `You are a personal assistant with access to vector search capabilities.
You can search for documents, files, and previous chat messages to provide more accurate responses.
You can also check the weather and calendar events.
When asked about specific information, use the appropriate tool to retrieve relevant data.
Always be helpful, concise, and professional.`,
  maxTokens: 2000,
  temperature: 0.7,
  tools: {
    getWeatherInformation,
    askForPermission,
    getCalendarEvents,
  },
  maxSteps: 5,
});

// Export the route handler
export { handler as POST };
