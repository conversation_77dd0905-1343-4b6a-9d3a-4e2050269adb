/**
 * Deep Research Project Analyses API Routes
 * 
 * Handles project analyses and insights
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../auth/[...nextauth]/route';
import { deepResearchService } from '@/lib/deep-research';

/**
 * GET /api/deep-research/projects/[projectId]/analyses
 * Get all analyses for a research project
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const project = await deepResearchService.getProject(params.projectId);
    
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Check if user owns the project
    if (project.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const analysisType = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get analyses from database adapter
    const analyses = await deepResearchService.getProjectAnalyses(params.projectId);
    
    // Filter by type if specified
    const filteredAnalyses = analysisType 
      ? analyses.filter(analysis => analysis.type === analysisType)
      : analyses;

    // Apply pagination
    const paginatedAnalyses = filteredAnalyses.slice(offset, offset + limit);

    return NextResponse.json({
      analyses: paginatedAnalyses,
      total: filteredAnalyses.length,
      offset,
      limit
    });
  } catch (error) {
    console.error('Error fetching project analyses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project analyses' },
      { status: 500 }
    );
  }
}
