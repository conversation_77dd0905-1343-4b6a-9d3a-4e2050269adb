/**
 * Deep Research Project Sources API Routes
 * 
 * Handles project sources and analyses
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../auth/[...nextauth]/route';
import { deepResearchService } from '@/lib/deep-research';

/**
 * GET /api/deep-research/projects/[projectId]/sources
 * Get all sources for a research project
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const project = await deepResearchService.getProject(params.projectId);
    
    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Check if user owns the project
    if (project.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const sourceType = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get sources from database adapter
    const sources = await deepResearchService.getProjectSources(params.projectId);
    
    // Filter by type if specified
    const filteredSources = sourceType 
      ? sources.filter(source => source.type === sourceType)
      : sources;

    // Apply pagination
    const paginatedSources = filteredSources.slice(offset, offset + limit);

    return NextResponse.json({
      sources: paginatedSources,
      total: filteredSources.length,
      offset,
      limit
    });
  } catch (error) {
    console.error('Error fetching project sources:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project sources' },
      { status: 500 }
    );
  }
}
