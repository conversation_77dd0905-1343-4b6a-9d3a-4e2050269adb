/**
 * Deep Research Projects API Routes
 * 
 * Handles CRUD operations for research projects
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { deepResearchService } from '@/lib/deep-research';
import { z } from 'zod';

// Request validation schemas
const createProjectSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().min(1).max(1000),
  settings: z.object({
    autoAnalysis: z.boolean().optional(),
    sourceTypes: z.array(z.string()).optional(),
    analysisTypes: z.array(z.string()).optional(),
    maxSources: z.number().min(1).max(100).optional(),
    qualityThreshold: z.number().min(0).max(1).optional(),
    biasDetection: z.boolean().optional(),
    factChecking: z.boolean().optional(),
    realTimeUpdates: z.boolean().optional()
  }).optional()
});

const updateProjectSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().min(1).max(1000).optional(),
  status: z.enum(['draft', 'active', 'completed', 'archived']).optional(),
  tags: z.array(z.string()).optional(),
  settings: z.object({
    autoAnalysis: z.boolean().optional(),
    sourceTypes: z.array(z.string()).optional(),
    analysisTypes: z.array(z.string()).optional(),
    maxSources: z.number().min(1).max(100).optional(),
    qualityThreshold: z.number().min(0).max(1).optional(),
    biasDetection: z.boolean().optional(),
    factChecking: z.boolean().optional(),
    realTimeUpdates: z.boolean().optional()
  }).optional()
});

/**
 * GET /api/deep-research/projects
 * List all research projects for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let projects;
    
    if (status) {
      projects = await deepResearchService.getProjectsByStatus(
        status as any,
        session.user.id
      );
    } else if (search) {
      projects = await deepResearchService.searchProjects(
        search,
        session.user.id
      );
    } else {
      projects = await deepResearchService.listProjects(session.user.id);
    }

    return NextResponse.json({ projects });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/deep-research/projects
 * Create a new research project
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createProjectSchema.parse(body);

    const project = await deepResearchService.createProject(
      validatedData.title,
      validatedData.description,
      validatedData.settings || {},
      session.user.id
    );

    return NextResponse.json({ project }, { status: 201 });
  } catch (error) {
    console.error('Error creating project:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}
