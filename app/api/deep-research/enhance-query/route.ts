/**
 * AI Query Enhancement API Route
 * 
 * Uses AI to enhance and improve research queries
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { openai } from '@ai-sdk/openai';
import { generateText } from 'ai';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { query } = await request.json();

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query is required and must be a string' },
        { status: 400 }
      );
    }

    const { text } = await generateText({
      model: openai('gpt-4'),
      messages: [
        {
          role: 'system',
          content: `You are an expert research assistant. Your task is to enhance research queries to make them more effective for comprehensive research. 

Guidelines:
1. Make queries more specific and focused
2. Add relevant keywords and synonyms
3. Suggest alternative phrasings
4. Include related concepts and domains
5. Consider different perspectives and angles
6. Ensure the query is suitable for academic, web, and news sources

Respond with a JSON object containing:
- enhancedQuery: The improved version of the query
- suggestions: Array of alternative query suggestions
- keywords: Array of important keywords to focus on
- reasoning: Brief explanation of the enhancements made`
        },
        {
          role: 'user',
          content: `Please enhance this research query: "${query}"`
        }
      ],
      temperature: 0.7,
      maxTokens: 1000
    });

    try {
      const response = JSON.parse(text);
      return NextResponse.json(response);
    } catch (parseError) {
      // If JSON parsing fails, return a structured response
      return NextResponse.json({
        enhancedQuery: text,
        suggestions: [],
        keywords: [],
        reasoning: 'AI provided enhanced query but in non-JSON format'
      });
    }

  } catch (error) {
    console.error('Error enhancing query:', error);
    return NextResponse.json(
      { error: 'Failed to enhance query' },
      { status: 500 }
    );
  }
}
