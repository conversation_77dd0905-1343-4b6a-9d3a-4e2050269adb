/**
 * Deep Research Session Status API Routes
 * 
 * Handles individual research session operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';
import { deepResearchService } from '@/lib/deep-research';

/**
 * GET /api/deep-research/research/[sessionId]
 * Get research session status and progress
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const researchSession = deepResearchService.getSessionStatus(params.sessionId);
    
    if (!researchSession) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // Verify user owns the project associated with this session
    const project = await deepResearchService.getProject(researchSession.projectId);
    if (!project || project.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    return NextResponse.json({ session: researchSession });
  } catch (error) {
    console.error('Error fetching research session:', error);
    return NextResponse.json(
      { error: 'Failed to fetch research session' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/deep-research/research/[sessionId]
 * Cancel a running research session
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const researchSession = deepResearchService.getSessionStatus(params.sessionId);
    
    if (!researchSession) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    // Verify user owns the project associated with this session
    const project = await deepResearchService.getProject(researchSession.projectId);
    if (!project || project.userId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Cancel the session (this would need to be implemented in the research engine)
    // For now, we'll just return success
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error cancelling research session:', error);
    return NextResponse.json(
      { error: 'Failed to cancel research session' },
      { status: 500 }
    );
  }
}
