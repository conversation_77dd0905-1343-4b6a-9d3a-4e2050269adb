/**
 * Deep Research Session API Routes
 * 
 * Handles research session operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { deepResearchService } from '@/lib/deep-research';
import { z } from 'zod';

// Request validation schema
const startResearchSchema = z.object({
  query: z.string().min(1).max(500),
  projectId: z.string().optional(),
  options: z.object({
    sources: z.object({
      web: z.object({
        enabled: z.boolean(),
        maxResults: z.number().min(1).max(50).optional(),
        domains: z.array(z.string()).optional(),
        excludeDomains: z.array(z.string()).optional()
      }).optional(),
      academic: z.object({
        enabled: z.boolean(),
        maxResults: z.number().min(1).max(30).optional(),
        databases: z.array(z.enum(['arxiv', 'pubmed', 'scholar'])).optional(),
        dateRange: z.object({
          from: z.string().datetime(),
          to: z.string().datetime()
        }).optional()
      }).optional(),
      news: z.object({
        enabled: z.boolean(),
        maxResults: z.number().min(1).max(30).optional(),
        sources: z.array(z.string()).optional(),
        languages: z.array(z.string()).optional()
      }).optional(),
      social: z.object({
        enabled: z.boolean(),
        platforms: z.array(z.string()).optional(),
        maxResults: z.number().min(1).max(20).optional()
      }).optional()
    }),
    analysis: z.object({
      summarize: z.boolean().optional(),
      extractEntities: z.boolean().optional(),
      detectBias: z.boolean().optional(),
      checkCredibility: z.boolean().optional(),
      findRelationships: z.boolean().optional()
    }).optional()
  })
});

/**
 * POST /api/deep-research/research
 * Start a new research session
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = startResearchSchema.parse(body);

    // Create project if not provided
    let projectId = validatedData.projectId;
    if (!projectId) {
      const project = await deepResearchService.createProject(
        `Research: ${validatedData.query}`,
        `Research session for query: ${validatedData.query}`,
        {
          autoAnalysis: true,
          sourceTypes: Object.keys(validatedData.options.sources).filter(
            key => validatedData.options.sources[key as keyof typeof validatedData.options.sources]?.enabled
          ),
          maxSources: 30,
          qualityThreshold: 0.7
        },
        session.user.id
      );
      projectId = project.id;
    } else {
      // Verify user owns the project
      const project = await deepResearchService.getProject(projectId);
      if (!project || project.userId !== session.user.id) {
        return NextResponse.json({ error: 'Project not found or access denied' }, { status: 403 });
      }
    }

    // Convert options to the expected format
    const researchOptions = {
      query: validatedData.query,
      sources: validatedData.options.sources,
      analysis: validatedData.options.analysis || {
        summarize: true,
        extractEntities: true,
        detectBias: true,
        checkCredibility: true,
        findRelationships: true
      }
    };

    // Start research session
    const researchSession = await deepResearchService.startResearchSession(
      {
        query: validatedData.query,
        projectId,
        sourceTypes: Object.keys(validatedData.options.sources).filter(
          key => validatedData.options.sources[key as keyof typeof validatedData.options.sources]?.enabled
        ),
        maxSources: Object.values(validatedData.options.sources)
          .filter(source => source?.enabled)
          .reduce((sum, source) => sum + (source?.maxResults || 10), 0),
        analysisTypes: Object.keys(validatedData.options.analysis || {}).filter(
          key => validatedData.options.analysis?.[key as keyof typeof validatedData.options.analysis]
        ),
        realTime: false,
        deepAnalysis: true
      },
      researchOptions
    );

    return NextResponse.json({ 
      session: researchSession,
      projectId 
    }, { status: 201 });

  } catch (error) {
    console.error('Error starting research session:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to start research session' },
      { status: 500 }
    );
  }
}
