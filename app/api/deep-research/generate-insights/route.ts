/**
 * AI Insights Generation API Route
 * 
 * Generates custom insights based on research data and user queries
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { messages } = await request.json();

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      );
    }

    const result = await streamText({
      model: openai('gpt-4'),
      messages: [
        {
          role: 'system',
          content: `You are an expert research analyst with deep knowledge across multiple domains. You help researchers understand their data by providing insightful analysis, identifying patterns, and generating actionable recommendations.

Your capabilities include:
1. Analyzing research findings and identifying key themes
2. Detecting patterns and trends in data
3. Providing strategic recommendations
4. Identifying research gaps and opportunities
5. Synthesizing complex information into clear insights
6. Offering different perspectives and interpretations

When responding:
- Be specific and evidence-based
- Provide actionable insights
- Consider multiple perspectives
- Highlight important patterns or anomalies
- Suggest next steps or areas for further investigation
- Use clear, professional language
- Structure your response logically

If you need more context about the research data, ask specific questions.`
        },
        ...messages
      ],
      temperature: 0.7,
      maxTokens: 2000
    });

    return result.toAIStreamResponse();

  } catch (error) {
    console.error('Error generating insights:', error);
    return NextResponse.json(
      { error: 'Failed to generate insights' },
      { status: 500 }
    );
  }
}
