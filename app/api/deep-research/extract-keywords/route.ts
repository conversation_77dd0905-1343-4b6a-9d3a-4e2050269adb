/**
 * AI Keyword Extraction API Route
 * 
 * Extracts relevant keywords from research queries
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { openai } from '@ai-sdk/openai';
import { generateText } from 'ai';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { query } = await request.json();

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query is required and must be a string' },
        { status: 400 }
      );
    }

    const { text } = await generateText({
      model: openai('gpt-4'),
      messages: [
        {
          role: 'system',
          content: `You are an expert at extracting relevant keywords from research queries. 

Extract the most important keywords and phrases that would be useful for:
1. Database searches
2. Academic paper searches
3. Web searches
4. News searches

Focus on:
- Core concepts and topics
- Technical terms
- Related fields and domains
- Alternative terminology
- Synonyms and variations

Respond with a JSON object containing:
- keywords: Array of 5-15 most relevant keywords/phrases
- categories: Object grouping keywords by type (concepts, techniques, domains, etc.)
- synonyms: Object mapping main keywords to their synonyms

Keep keywords concise but meaningful.`
        },
        {
          role: 'user',
          content: `Extract keywords from this research query: "${query}"`
        }
      ],
      temperature: 0.3,
      maxTokens: 800
    });

    try {
      const response = JSON.parse(text);
      
      // Ensure the response has the expected structure
      if (!response.keywords || !Array.isArray(response.keywords)) {
        throw new Error('Invalid response structure');
      }

      return NextResponse.json(response);
    } catch (parseError) {
      // Fallback: extract basic keywords using simple text processing
      const words = query.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 3)
        .filter(word => !['this', 'that', 'with', 'from', 'they', 'have', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well', 'were'].includes(word));

      const fallbackKeywords = [...new Set(words)].slice(0, 10);

      return NextResponse.json({
        keywords: fallbackKeywords,
        categories: {
          concepts: fallbackKeywords.slice(0, 5),
          terms: fallbackKeywords.slice(5)
        },
        synonyms: {}
      });
    }

  } catch (error) {
    console.error('Error extracting keywords:', error);
    return NextResponse.json(
      { error: 'Failed to extract keywords' },
      { status: 500 }
    );
  }
}
