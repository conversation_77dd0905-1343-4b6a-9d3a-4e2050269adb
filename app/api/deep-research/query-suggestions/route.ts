/**
 * AI Query Suggestions API Route
 * 
 * Generates intelligent query suggestions and improvements
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { openai } from '@ai-sdk/openai';
import { generateText } from 'ai';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { query } = await request.json();

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query is required and must be a string' },
        { status: 400 }
      );
    }

    const { text } = await generateText({
      model: openai('gpt-4'),
      messages: [
        {
          role: 'system',
          content: `You are an expert research assistant specializing in query optimization. Generate intelligent suggestions to improve research queries.

For each suggestion, provide:
1. Enhanced query text
2. Type of improvement (enhancement, alternative, refinement)
3. Brief reasoning for the suggestion

Types:
- enhancement: Makes the query more comprehensive or specific
- alternative: Provides a different angle or perspective
- refinement: Focuses or narrows the scope

Respond with a JSON object containing an array of suggestions, each with:
- id: unique identifier
- text: the suggested query
- type: enhancement|alternative|refinement
- reasoning: explanation of why this suggestion is valuable

Provide 3-5 high-quality suggestions.`
        },
        {
          role: 'user',
          content: `Generate suggestions for this research query: "${query}"`
        }
      ],
      temperature: 0.8,
      maxTokens: 1500
    });

    try {
      const response = JSON.parse(text);
      
      // Ensure the response has the expected structure
      if (!response.suggestions || !Array.isArray(response.suggestions)) {
        throw new Error('Invalid response structure');
      }

      // Add IDs if missing
      response.suggestions = response.suggestions.map((suggestion: any, index: number) => ({
        id: suggestion.id || `suggestion-${index + 1}`,
        text: suggestion.text || '',
        type: suggestion.type || 'enhancement',
        reasoning: suggestion.reasoning || 'AI-generated suggestion'
      }));

      return NextResponse.json(response);
    } catch (parseError) {
      // Fallback: create structured suggestions from unstructured text
      const fallbackSuggestions = [
        {
          id: 'suggestion-1',
          text: `${query} - comprehensive analysis`,
          type: 'enhancement',
          reasoning: 'Added comprehensive analysis focus'
        },
        {
          id: 'suggestion-2',
          text: `Recent developments in ${query}`,
          type: 'alternative',
          reasoning: 'Focus on recent developments'
        },
        {
          id: 'suggestion-3',
          text: `${query} - challenges and opportunities`,
          type: 'refinement',
          reasoning: 'Refined to focus on challenges and opportunities'
        }
      ];

      return NextResponse.json({ suggestions: fallbackSuggestions });
    }

  } catch (error) {
    console.error('Error generating query suggestions:', error);
    return NextResponse.json(
      { error: 'Failed to generate query suggestions' },
      { status: 500 }
    );
  }
}
