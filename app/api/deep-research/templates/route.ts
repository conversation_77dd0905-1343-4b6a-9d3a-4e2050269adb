/**
 * Deep Research Templates API Routes
 * 
 * Provides predefined research templates and configurations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';

// Predefined research templates
const researchTemplates = [
  {
    id: 'academic-literature-review',
    name: 'Academic Literature Review',
    description: 'Comprehensive academic research with focus on peer-reviewed sources',
    icon: '🎓',
    category: 'Academic',
    options: {
      sources: {
        academic: {
          enabled: true,
          maxResults: 25,
          databases: ['arxiv', 'pubmed']
        },
        web: {
          enabled: true,
          maxResults: 10,
          domains: ['scholar.google.com', '.edu', '.org']
        },
        news: {
          enabled: false
        }
      },
      analysis: {
        summarize: true,
        extractEntities: true,
        detectBias: true,
        checkCredibility: true,
        findRelationships: true
      }
    },
    settings: {
      autoAnalysis: true,
      sourceTypes: ['academic', 'web'],
      analysisTypes: ['summary', 'insights', 'entities', 'relationships'],
      maxSources: 35,
      qualityThreshold: 0.8,
      biasDetection: true,
      factChecking: true
    }
  },
  {
    id: 'market-research',
    name: 'Market Research',
    description: 'Business and market analysis with news and web sources',
    icon: '📊',
    category: 'Business',
    options: {
      sources: {
        web: {
          enabled: true,
          maxResults: 20,
          domains: ['bloomberg.com', 'reuters.com', 'wsj.com', 'forbes.com']
        },
        news: {
          enabled: true,
          maxResults: 15,
          sources: ['reuters.com', 'bloomberg.com', 'cnbc.com']
        },
        academic: {
          enabled: false
        }
      },
      analysis: {
        summarize: true,
        extractEntities: true,
        detectBias: true,
        checkCredibility: true,
        findRelationships: false
      }
    },
    settings: {
      autoAnalysis: true,
      sourceTypes: ['web', 'news'],
      analysisTypes: ['summary', 'insights', 'bias', 'credibility'],
      maxSources: 35,
      qualityThreshold: 0.7,
      biasDetection: true,
      factChecking: true
    }
  },
  {
    id: 'technology-trends',
    name: 'Technology Trends',
    description: 'Tech industry analysis with academic and news sources',
    icon: '💻',
    category: 'Technology',
    options: {
      sources: {
        web: {
          enabled: true,
          maxResults: 15,
          domains: ['techcrunch.com', 'wired.com', 'arstechnica.com', 'ieee.org']
        },
        academic: {
          enabled: true,
          maxResults: 10,
          databases: ['arxiv']
        },
        news: {
          enabled: true,
          maxResults: 10,
          sources: ['techcrunch.com', 'theverge.com']
        }
      },
      analysis: {
        summarize: true,
        extractEntities: true,
        detectBias: false,
        checkCredibility: true,
        findRelationships: true
      }
    },
    settings: {
      autoAnalysis: true,
      sourceTypes: ['web', 'academic', 'news'],
      analysisTypes: ['summary', 'insights', 'entities', 'relationships'],
      maxSources: 35,
      qualityThreshold: 0.7,
      biasDetection: false,
      factChecking: true
    }
  },
  {
    id: 'health-medical',
    name: 'Health & Medical Research',
    description: 'Medical and health research with PubMed focus',
    icon: '🏥',
    category: 'Health',
    options: {
      sources: {
        academic: {
          enabled: true,
          maxResults: 20,
          databases: ['pubmed']
        },
        web: {
          enabled: true,
          maxResults: 10,
          domains: ['nih.gov', 'who.int', 'cdc.gov', 'mayoclinic.org']
        },
        news: {
          enabled: true,
          maxResults: 5,
          sources: ['reuters.com', 'bbc.com']
        }
      },
      analysis: {
        summarize: true,
        extractEntities: true,
        detectBias: true,
        checkCredibility: true,
        findRelationships: true
      }
    },
    settings: {
      autoAnalysis: true,
      sourceTypes: ['academic', 'web', 'news'],
      analysisTypes: ['summary', 'insights', 'entities', 'credibility'],
      maxSources: 35,
      qualityThreshold: 0.9,
      biasDetection: true,
      factChecking: true
    }
  },
  {
    id: 'quick-overview',
    name: 'Quick Overview',
    description: 'Fast research for quick insights and summaries',
    icon: '⚡',
    category: 'General',
    options: {
      sources: {
        web: {
          enabled: true,
          maxResults: 10
        },
        news: {
          enabled: true,
          maxResults: 5
        },
        academic: {
          enabled: false
        }
      },
      analysis: {
        summarize: true,
        extractEntities: false,
        detectBias: false,
        checkCredibility: true,
        findRelationships: false
      }
    },
    settings: {
      autoAnalysis: true,
      sourceTypes: ['web', 'news'],
      analysisTypes: ['summary', 'insights'],
      maxSources: 15,
      qualityThreshold: 0.6,
      biasDetection: false,
      factChecking: false
    }
  },
  {
    id: 'comprehensive-analysis',
    name: 'Comprehensive Analysis',
    description: 'Deep research with all sources and analysis types',
    icon: '🔬',
    category: 'Research',
    options: {
      sources: {
        web: {
          enabled: true,
          maxResults: 20
        },
        academic: {
          enabled: true,
          maxResults: 15,
          databases: ['arxiv', 'pubmed']
        },
        news: {
          enabled: true,
          maxResults: 10
        }
      },
      analysis: {
        summarize: true,
        extractEntities: true,
        detectBias: true,
        checkCredibility: true,
        findRelationships: true
      }
    },
    settings: {
      autoAnalysis: true,
      sourceTypes: ['web', 'academic', 'news'],
      analysisTypes: ['summary', 'insights', 'bias', 'credibility', 'entities', 'relationships'],
      maxSources: 45,
      qualityThreshold: 0.7,
      biasDetection: true,
      factChecking: true
    }
  }
];

/**
 * GET /api/deep-research/templates
 * Get all available research templates
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    let filteredTemplates = researchTemplates;
    
    if (category) {
      filteredTemplates = researchTemplates.filter(
        template => template.category.toLowerCase() === category.toLowerCase()
      );
    }

    const categories = [...new Set(researchTemplates.map(t => t.category))];

    return NextResponse.json({
      templates: filteredTemplates,
      categories
    });
  } catch (error) {
    console.error('Error fetching research templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch research templates' },
      { status: 500 }
    );
  }
}
