import { NextRequest, NextResponse } from "next/server";
import { streamText, tool } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";

/**
 * Simplified API route for the autonomous programmer agent
 * This version doesn't require authentication or a real project ID
 */
export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { messages, projectId } = body;

    // Use the chat interface with tools
    return streamText({
      model: openai("gpt-4o"),
      system: `# Autonomous Programming Assistant

## Role and Purpose
You are an advanced autonomous programming assistant designed to help developers build complete applications from requirements to deployment. You operate as a specialized AI agent with deep knowledge of software development practices and access to powerful tools for code generation, analysis, and project management.

## Project Context
The current project is a demo project for showcasing your capabilities.

## Core Capabilities

1. **Requirements Analysis and Planning**
   - Analyze project requirements and create detailed development plans
   - Break down complex features into manageable tasks
   - Identify technical challenges and propose solutions
   - Recommend appropriate technology stacks and architectures

2. **Code Generation and Implementation**
   - Generate high-quality, production-ready code following best practices
   - Implement specific features with proper error handling and testing
   - Maintain consistency with existing codebase patterns and conventions
   - Reuse existing components and utilities when appropriate

3. **Testing and Debugging**
   - Create comprehensive test suites for implemented features
   - Debug issues with detailed error analysis
   - Suggest optimizations and improvements
   - Ensure code quality and maintainability

## Response Format

When responding to user requests:

1. Start with a clear understanding of the task
2. Outline your approach and the tools you'll use
3. Execute the necessary steps using available tools
4. Provide a summary of actions taken and results achieved
5. Include any relevant code snippets, explanations, or next steps

Remember that you are part of an integrated development environment. Your code generations and recommendations should be consistent with the project's existing patterns and should leverage the available services for optimal results.`,
      messages,
      tools: {
        generateFile: tool({
          description: "Generate a file with the given content",
          parameters: z.object({
            filePath: z.string().describe("Path to the file to generate"),
            content: z.string().describe("Content of the file"),
            description: z.string().optional().describe("Description of the file"),
          }),
          execute: async ({ filePath, content, description }) => {
            console.log(`Generated file: ${filePath}`);
            return {
              success: true,
              filePath,
              message: `File ${filePath} generated successfully`,
            };
          },
        }),

        analyzeCode: tool({
          description: "Analyze code for issues and suggestions",
          parameters: z.object({
            code: z.string().describe("Code to analyze"),
            language: z.string().describe("Programming language of the code"),
          }),
          execute: async ({ code, language }) => {
            // Simple code analysis
            const issues = [];
            const suggestions = [];
            
            // Check for common issues based on language
            if (language === "javascript" || language === "typescript") {
              if (code.includes("var ")) {
                issues.push({
                  type: "style",
                  message: "Use 'const' or 'let' instead of 'var'",
                  line: code.split("\n").findIndex(line => line.includes("var ")) + 1,
                });
              }
              
              if (code.includes("function(") && !code.includes("=>")) {
                suggestions.push({
                  type: "modernization",
                  message: "Consider using arrow functions for cleaner syntax",
                  line: code.split("\n").findIndex(line => line.includes("function(")) + 1,
                });
              }
            }
            
            return {
              issues,
              suggestions,
              summary: "Code analysis completed successfully."
            };
          },
        }),

        searchCodebase: tool({
          description: "Search the codebase for relevant code",
          parameters: z.object({
            query: z.string().describe("Search query"),
          }),
          execute: async ({ query }) => {
            // Mock codebase search
            return {
              results: [
                {
                  file: "src/components/Button.tsx",
                  content: "export const Button = ({ children, ...props }) => {\n  return <button {...props}>{children}</button>;\n};",
                  relevance: 0.95,
                },
                {
                  file: "src/utils/helpers.ts",
                  content: "export const formatDate = (date: Date) => {\n  return date.toLocaleDateString();\n};",
                  relevance: 0.85,
                },
              ],
              summary: `Found 2 results for "${query}"`
            };
          },
        }),
        
        implementFeature: tool({
          description: "Implement a specific feature in the project",
          parameters: z.object({
            feature: z.string().describe("Feature description"),
            language: z.string().optional().describe("Programming language"),
            framework: z.string().optional().describe("Framework to use"),
          }),
          execute: async ({ feature, language = "typescript", framework = "react" }) => {
            console.log(`Implementing feature: ${feature} using ${language} and ${framework}`);
            
            // This would normally generate code based on the feature description
            return {
              success: true,
              files: [
                {
                  path: `src/features/${feature.toLowerCase().replace(/\s+/g, '-')}.${language === "typescript" ? "tsx" : "jsx"}`,
                  content: `// Feature implementation for: ${feature}\n// Using ${framework} with ${language}\n\n// Implementation would go here`
                }
              ],
              message: `Feature "${feature}" implemented successfully`
            };
          },
        }),
      },
    }).toDataStreamResponse();
  } catch (error) {
    console.error("Error in simple autonomous programmer API:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
