/**
 * WebSocket API for Real-time File Sync Events
 */

import { NextRequest } from 'next/server'
import { WebSocketServer, WebSocket } from 'ws'
import { FileSyncManager, createDefaultSyncConfig } from '@/lib/file-sync'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// WebSocket server instance
let wss: WebSocketServer | null = null
const clients = new Map<string, Set<WebSocket>>()
const syncManagers = new Map<string, FileSyncManager>()

interface WebSocketMessage {
  type: 'subscribe' | 'unsubscribe' | 'ping' | 'sync-trigger'
  projectId?: string
  data?: any
}

interface SyncEvent {
  type: 'sync-start' | 'sync-progress' | 'sync-complete' | 'sync-error' | 'file-change' | 'conflict-detected' | 'conflict-resolved'
  projectId: string
  data: any
  timestamp: string
}

/**
 * Initialize WebSocket server
 */
function initializeWebSocketServer(): WebSocketServer {
  if (wss) return wss

  wss = new WebSocketServer({ 
    port: 3001,
    path: '/api/file-sync/ws'
  })

  wss.on('connection', (ws: WebSocket, request) => {
    console.log('[file-sync-ws] New WebSocket connection')

    // Send welcome message
    ws.send(JSON.stringify({
      type: 'connected',
      message: 'File sync WebSocket connected',
      timestamp: new Date().toISOString()
    }))

    // Handle messages
    ws.on('message', async (data) => {
      try {
        const message: WebSocketMessage = JSON.parse(data.toString())
        await handleWebSocketMessage(ws, message)
      } catch (error) {
        console.error('[file-sync-ws] Error handling message:', error)
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Invalid message format',
          timestamp: new Date().toISOString()
        }))
      }
    })

    // Handle connection close
    ws.on('close', () => {
      console.log('[file-sync-ws] WebSocket connection closed')
      // Remove client from all project subscriptions
      for (const [projectId, projectClients] of clients.entries()) {
        projectClients.delete(ws)
        if (projectClients.size === 0) {
          clients.delete(projectId)
        }
      }
    })

    // Handle errors
    ws.on('error', (error) => {
      console.error('[file-sync-ws] WebSocket error:', error)
    })
  })

  console.log('[file-sync-ws] WebSocket server initialized on port 3001')
  return wss
}

/**
 * Handle WebSocket messages
 */
async function handleWebSocketMessage(ws: WebSocket, message: WebSocketMessage): Promise<void> {
  switch (message.type) {
    case 'subscribe':
      if (!message.projectId) {
        ws.send(JSON.stringify({
          type: 'error',
          message: 'projectId is required for subscription',
          timestamp: new Date().toISOString()
        }))
        return
      }

      await subscribeToProject(ws, message.projectId)
      break

    case 'unsubscribe':
      if (!message.projectId) {
        ws.send(JSON.stringify({
          type: 'error',
          message: 'projectId is required for unsubscription',
          timestamp: new Date().toISOString()
        }))
        return
      }

      unsubscribeFromProject(ws, message.projectId)
      break

    case 'ping':
      ws.send(JSON.stringify({
        type: 'pong',
        timestamp: new Date().toISOString()
      }))
      break

    case 'sync-trigger':
      if (!message.projectId) {
        ws.send(JSON.stringify({
          type: 'error',
          message: 'projectId is required for sync trigger',
          timestamp: new Date().toISOString()
        }))
        return
      }

      await triggerSync(message.projectId, message.data)
      break

    default:
      ws.send(JSON.stringify({
        type: 'error',
        message: `Unknown message type: ${message.type}`,
        timestamp: new Date().toISOString()
      }))
  }
}

/**
 * Subscribe client to project sync events
 */
async function subscribeToProject(ws: WebSocket, projectId: string): Promise<void> {
  try {
    // Add client to project subscription
    if (!clients.has(projectId)) {
      clients.set(projectId, new Set())
    }
    clients.get(projectId)!.add(ws)

    // Get or create sync manager for project
    let syncManager = syncManagers.get(projectId)
    if (!syncManager) {
      const config = createDefaultSyncConfig(projectId)
      syncManager = new FileSyncManager(config, prisma)
      await syncManager.initialize()
      
      // Setup event listeners
      setupSyncManagerEvents(syncManager, projectId)
      syncManagers.set(projectId, syncManager)
    }

    ws.send(JSON.stringify({
      type: 'subscribed',
      projectId,
      message: `Subscribed to sync events for project: ${projectId}`,
      timestamp: new Date().toISOString()
    }))

    console.log(`[file-sync-ws] Client subscribed to project: ${projectId}`)
  } catch (error) {
    console.error(`[file-sync-ws] Error subscribing to project ${projectId}:`, error)
    ws.send(JSON.stringify({
      type: 'error',
      message: `Failed to subscribe to project: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    }))
  }
}

/**
 * Unsubscribe client from project sync events
 */
function unsubscribeFromProject(ws: WebSocket, projectId: string): void {
  const projectClients = clients.get(projectId)
  if (projectClients) {
    projectClients.delete(ws)
    if (projectClients.size === 0) {
      clients.delete(projectId)
      
      // Optionally shutdown sync manager if no clients
      const syncManager = syncManagers.get(projectId)
      if (syncManager) {
        syncManager.shutdown().catch(console.error)
        syncManagers.delete(projectId)
      }
    }
  }

  ws.send(JSON.stringify({
    type: 'unsubscribed',
    projectId,
    message: `Unsubscribed from sync events for project: ${projectId}`,
    timestamp: new Date().toISOString()
  }))

  console.log(`[file-sync-ws] Client unsubscribed from project: ${projectId}`)
}

/**
 * Setup event listeners for sync manager
 */
function setupSyncManagerEvents(syncManager: FileSyncManager, projectId: string): void {
  syncManager.on('sync-start', (session) => {
    broadcastToProject(projectId, {
      type: 'sync-start',
      projectId,
      data: session,
      timestamp: new Date().toISOString()
    })
  })

  syncManager.on('sync-progress', (progress) => {
    broadcastToProject(projectId, {
      type: 'sync-progress',
      projectId,
      data: progress,
      timestamp: new Date().toISOString()
    })
  })

  syncManager.on('sync-complete', (result) => {
    broadcastToProject(projectId, {
      type: 'sync-complete',
      projectId,
      data: result,
      timestamp: new Date().toISOString()
    })
  })

  syncManager.on('sync-error', (error) => {
    broadcastToProject(projectId, {
      type: 'sync-error',
      projectId,
      data: error,
      timestamp: new Date().toISOString()
    })
  })

  syncManager.on('file-change', (event) => {
    broadcastToProject(projectId, {
      type: 'file-change',
      projectId,
      data: event,
      timestamp: new Date().toISOString()
    })
  })

  syncManager.on('conflict-detected', (conflict) => {
    broadcastToProject(projectId, {
      type: 'conflict-detected',
      projectId,
      data: conflict,
      timestamp: new Date().toISOString()
    })
  })

  syncManager.on('conflict-resolved', (conflict) => {
    broadcastToProject(projectId, {
      type: 'conflict-resolved',
      projectId,
      data: conflict,
      timestamp: new Date().toISOString()
    })
  })
}

/**
 * Broadcast event to all clients subscribed to a project
 */
function broadcastToProject(projectId: string, event: SyncEvent): void {
  const projectClients = clients.get(projectId)
  if (!projectClients) return

  const message = JSON.stringify(event)
  
  projectClients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(message)
    } else {
      // Remove closed connections
      projectClients.delete(client)
    }
  })

  console.log(`[file-sync-ws] Broadcasted ${event.type} to ${projectClients.size} clients for project: ${projectId}`)
}

/**
 * Trigger sync operation
 */
async function triggerSync(projectId: string, data: any): Promise<void> {
  try {
    const syncManager = syncManagers.get(projectId)
    if (!syncManager) {
      throw new Error(`No sync manager found for project: ${projectId}`)
    }

    const syncType = data?.type || 'incremental'
    const options = data?.options || {}

    let result
    switch (syncType) {
      case 'full':
        result = await syncManager.fullSync(options)
        break
      case 'incremental':
        result = await syncManager.incrementalSync(options)
        break
      case 'files':
        if (!data?.filePaths) {
          throw new Error('filePaths is required for files sync')
        }
        result = await syncManager.syncFiles(data.filePaths, options)
        break
      default:
        throw new Error(`Unknown sync type: ${syncType}`)
    }

    console.log(`[file-sync-ws] Triggered ${syncType} sync for project: ${projectId}`)
  } catch (error) {
    console.error(`[file-sync-ws] Error triggering sync for project ${projectId}:`, error)
    
    broadcastToProject(projectId, {
      type: 'sync-error',
      projectId,
      data: {
        type: 'trigger_error',
        message: error instanceof Error ? error.message : 'Unknown error',
        retryable: true
      },
      timestamp: new Date().toISOString()
    })
  }
}

/**
 * HTTP handler for WebSocket upgrade
 */
export async function GET(request: NextRequest) {
  // Initialize WebSocket server if not already done
  if (!wss) {
    initializeWebSocketServer()
  }

  // Return WebSocket connection info
  return new Response(JSON.stringify({
    message: 'File Sync WebSocket server is running',
    endpoint: 'ws://localhost:3001/api/file-sync/ws',
    status: 'active',
    connectedClients: Array.from(clients.entries()).map(([projectId, clients]) => ({
      projectId,
      clientCount: clients.size
    }))
  }), {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// Cleanup on process exit
process.on('SIGTERM', async () => {
  console.log('[file-sync-ws] Shutting down WebSocket server...')
  
  // Shutdown all sync managers
  for (const [projectId, syncManager] of syncManagers.entries()) {
    try {
      await syncManager.shutdown()
    } catch (error) {
      console.error(`[file-sync-ws] Error shutting down sync manager for ${projectId}:`, error)
    }
  }
  
  // Close WebSocket server
  if (wss) {
    wss.close()
  }
  
  // Disconnect from database
  await prisma.$disconnect()
})
