/**
 * File Synchronization API Routes
 */

import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { FileSyncManager, createDefaultSyncConfig } from '@/lib/file-sync'
import { z } from 'zod'

const prisma = new PrismaClient()

// Request validation schemas
const SyncRequestSchema = z.object({
  projectId: z.string().min(1),
  type: z.enum(['full', 'incremental', 'files']),
  options: z.object({
    direction: z.enum(['nodebox_to_db', 'db_to_nodebox', 'bidirectional']).optional(),
    force: z.boolean().optional(),
    dryRun: z.boolean().optional(),
    includePatterns: z.array(z.string()).optional(),
    excludePatterns: z.array(z.string()).optional(),
    conflictResolution: z.enum(['timestamp_based', 'user_choice', 'nodebox_wins', 'database_wins', 'merge_content', 'create_backup']).optional(),
  }).optional(),
  filePaths: z.array(z.string()).optional()
})

const ConflictResolutionSchema = z.object({
  projectId: z.string().min(1),
  filePath: z.string().min(1),
  strategy: z.enum(['timestamp_based', 'user_choice', 'nodebox_wins', 'database_wins', 'merge_content', 'create_backup']),
  userChoice: z.enum(['nodebox', 'database', 'merge']).optional(),
  mergeStrategy: z.enum(['line_by_line', 'block_based', 'semantic']).optional(),
  createBackup: z.boolean().optional(),
  backupSuffix: z.string().optional()
})

const ConfigUpdateSchema = z.object({
  projectId: z.string().min(1),
  config: z.object({
    autoSync: z.boolean().optional(),
    syncInterval: z.number().min(1000).optional(),
    conflictResolution: z.enum(['timestamp_based', 'user_choice', 'nodebox_wins', 'database_wins', 'merge_content', 'create_backup']).optional(),
    excludePatterns: z.array(z.string()).optional(),
    includePatterns: z.array(z.string()).optional(),
    maxFileSize: z.number().min(1).optional(),
    enableWatchers: z.boolean().optional(),
    batchSize: z.number().min(1).optional(),
    retryAttempts: z.number().min(0).optional(),
    retryDelay: z.number().min(100).optional()
  })
})

// Global sync managers cache
const syncManagers = new Map<string, FileSyncManager>()

/**
 * Get or create sync manager for project
 */
async function getSyncManager(projectId: string): Promise<FileSyncManager> {
  if (syncManagers.has(projectId)) {
    return syncManagers.get(projectId)!
  }

  const config = createDefaultSyncConfig(projectId)
  const manager = new FileSyncManager(config, prisma)
  
  await manager.initialize()
  syncManagers.set(projectId, manager)
  
  return manager
}

/**
 * POST /api/file-sync - Perform synchronization operations
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { projectId, type, options = {}, filePaths } = SyncRequestSchema.parse(body)

    const syncManager = await getSyncManager(projectId)
    let result

    switch (type) {
      case 'full':
        result = await syncManager.fullSync(options)
        break
      
      case 'incremental':
        result = await syncManager.incrementalSync(options)
        break
      
      case 'files':
        if (!filePaths || filePaths.length === 0) {
          return NextResponse.json(
            { error: 'filePaths is required for files sync type' },
            { status: 400 }
          )
        }
        result = await syncManager.syncFiles(filePaths, options)
        break
      
      default:
        return NextResponse.json(
          { error: `Unknown sync type: ${type}` },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      result,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[file-sync] POST error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        error: 'Sync operation failed', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/file-sync - Get sync information
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const action = searchParams.get('action') || 'stats'

    if (!projectId) {
      return NextResponse.json(
        { error: 'projectId is required' },
        { status: 400 }
      )
    }

    const syncManager = await getSyncManager(projectId)

    switch (action) {
      case 'stats':
        const stats = await syncManager.getSyncStats()
        return NextResponse.json({ success: true, stats })

      case 'history':
        const limit = parseInt(searchParams.get('limit') || '50')
        const history = await syncManager.getSyncHistory(limit)
        return NextResponse.json({ success: true, history })

      case 'sessions':
        const sessionLimit = parseInt(searchParams.get('limit') || '50')
        const sessions = await syncManager.getSyncSessions(sessionLimit)
        return NextResponse.json({ success: true, sessions })

      case 'conflicts':
        const conflicts = await syncManager.getConflictedFiles()
        return NextResponse.json({ success: true, conflicts })

      case 'config':
        const config = syncManager.getConfig()
        return NextResponse.json({ success: true, config })

      case 'health':
        const health = await syncManager.healthCheck()
        return NextResponse.json({ success: true, health })

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('[file-sync] GET error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get sync information', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/file-sync - Update configuration or resolve conflicts
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const action = body.action

    switch (action) {
      case 'resolve-conflict':
        const conflictData = ConflictResolutionSchema.parse(body)
        const syncManager = await getSyncManager(conflictData.projectId)
        
        await syncManager.resolveConflict(conflictData.filePath, {
          strategy: conflictData.strategy,
          userChoice: conflictData.userChoice,
          mergeStrategy: conflictData.mergeStrategy,
          createBackup: conflictData.createBackup,
          backupSuffix: conflictData.backupSuffix
        })

        return NextResponse.json({
          success: true,
          message: 'Conflict resolved successfully'
        })

      case 'update-config':
        const configData = ConfigUpdateSchema.parse(body)
        const configManager = await getSyncManager(configData.projectId)
        
        configManager.updateConfig(configData.config)

        return NextResponse.json({
          success: true,
          message: 'Configuration updated successfully',
          config: configManager.getConfig()
        })

      case 'start-watching':
        const watchProjectId = body.projectId
        if (!watchProjectId) {
          return NextResponse.json(
            { error: 'projectId is required' },
            { status: 400 }
          )
        }
        
        const watchManager = await getSyncManager(watchProjectId)
        await watchManager.startWatching()

        return NextResponse.json({
          success: true,
          message: 'File watching started'
        })

      case 'stop-watching':
        const stopProjectId = body.projectId
        if (!stopProjectId) {
          return NextResponse.json(
            { error: 'projectId is required' },
            { status: 400 }
          )
        }
        
        const stopManager = await getSyncManager(stopProjectId)
        await stopManager.stopWatching()

        return NextResponse.json({
          success: true,
          message: 'File watching stopped'
        })

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('[file-sync] PUT error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        error: 'Update operation failed', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/file-sync - Cleanup operations
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const action = searchParams.get('action') || 'cleanup'

    if (!projectId) {
      return NextResponse.json(
        { error: 'projectId is required' },
        { status: 400 }
      )
    }

    const syncManager = await getSyncManager(projectId)

    switch (action) {
      case 'cleanup':
        const olderThanDays = parseInt(searchParams.get('olderThanDays') || '30')
        const olderThan = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000)
        
        const cleanupResult = await syncManager.cleanup(olderThan)
        
        return NextResponse.json({
          success: true,
          message: 'Cleanup completed',
          result: cleanupResult
        })

      case 'shutdown':
        await syncManager.shutdown()
        syncManagers.delete(projectId)
        
        return NextResponse.json({
          success: true,
          message: 'Sync manager shutdown completed'
        })

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('[file-sync] DELETE error:', error)
    return NextResponse.json(
      { 
        error: 'Delete operation failed', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}
