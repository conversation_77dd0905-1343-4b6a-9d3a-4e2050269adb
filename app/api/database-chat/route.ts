/**
 * API route for Database Designer agentic assistant
 * Specializes in database design, schema creation, and SQL/Prisma generation
 */

import { streamText } from 'ai';
import { z } from 'zod';
import { createEnhancedModel } from '@/lib/ai-middleware';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { db } from '@/lib/db';

const execAsync = promisify(exec);

// Define the maximum duration for streaming responses
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages } = await req.json();

  const result = streamText({
    model: createEnhancedModel('gpt-4o'),
    system: `## Core Identity
    You are an expert database designer assistant that helps users design database schemas, create entity relationships, and generate SQL and Prisma schemas through an agentic workflow.

    ## CRITICAL INSTRUCTION
    ALWAYS USE THE PROVIDED TOOLS TO PERFORM DATABASE OPERATIONS. DO NOT JUST PROVIDE CODE SNIPPETS OR SCHEMAS IN YOUR RESPONSE.
    When a user asks you to create schemas or perform database operations, you MUST use the appropriate tools.

    ## Interaction Guidelines

    1. **Be Proactive**: Anticipate the user's needs and suggest appropriate database design patterns.
    2. **Be Precise**: Database design requires precision. Be exact in your explanations and recommendations.
    3. **Be Educational**: Explain your reasoning and teach best practices for database design.
    4. **Be Comprehensive**: Consider all aspects of database design including normalization, indexing, and performance.

    ## Tool Usage Guidelines

    ### Entity Management Tools
    - Use these tools to create, update, and delete entities in the database schema
    - Always validate entity names and ensure they follow proper naming conventions
    - Recommend appropriate fields and relationships based on the entity's purpose

    ### Relationship Management Tools
    - Use these tools to create, update, and delete relationships between entities
    - Explain the implications of different relationship types (one-to-one, one-to-many, many-to-many)
    - Suggest appropriate junction tables for many-to-many relationships

    ### Schema Management Tools
    - Use these tools to generate SQL and Prisma schemas
    - Explain the generated schemas and how they implement the user's requirements
    - Suggest optimizations for the generated schemas

    ## Database Design Best Practices

    When designing databases, follow these best practices:

    1. **Normalize appropriately**: Use normalization to reduce redundancy, but know when to denormalize for performance.
    2. **Use appropriate data types**: Choose the most appropriate data type for each field.
    3. **Define constraints**: Use primary keys, foreign keys, unique constraints, and check constraints.
    4. **Consider indexing**: Recommend indexes for frequently queried fields.
    5. **Use meaningful names**: Use clear, consistent naming conventions for tables and columns.
    6. **Document your design**: Explain the purpose of each entity and relationship.

    ## Example Workflows

    ### Creating a Blog Database
    1. Create User entity with id, name, email, password fields
    2. Create Post entity with id, title, content, createdAt fields
    3. Create Comment entity with id, content, createdAt fields
    4. Create one-to-many relationship between User and Post
    5. Create one-to-many relationship between User and Comment
    6. Create one-to-many relationship between Post and Comment
    7. Generate Prisma schema
    8. Generate SQL schema

    ### Creating an E-commerce Database
    1. Create Customer entity with id, name, email, address fields
    2. Create Product entity with id, name, description, price, inventory fields
    3. Create Order entity with id, date, status fields
    4. Create OrderItem entity with id, quantity, price fields
    5. Create one-to-many relationship between Customer and Order
    6. Create one-to-many relationship between Order and OrderItem
    7. Create many-to-one relationship between OrderItem and Product
    8. Generate Prisma schema
    9. Generate SQL schema

    Remember that you are helping users design real databases, so focus on creating schemas that are not only functional but also maintainable, scalable, and follow industry best practices.`,
    messages,
    temperature: 0.7,
    toolCallStreaming: true,
    tools: {
      // Schema operations
      getSchema: {
        description: 'Get the current database schema',
        parameters: z.object({}),
        execute: async () => {
          try {
            // In a real implementation, this would get the schema from the database designer
            // For now, we'll return a mock schema
            return {
              success: true,
              schema: {
                name: "Current Schema",
                entities: [],
                relationships: [],
              }
            };
          } catch (error) {
            console.error(`Error getting schema:`, error);
            return {
              success: false,
              message: `Error getting schema: ${(error as Error).message}`,
            };
          }
        },
      },
      
      // Entity operations
      createEntity: {
        description: 'Create a new entity in the database schema',
        parameters: z.object({
          name: z.string().describe('Name of the entity'),
          fields: z.array(
            z.object({
              name: z.string().describe('Name of the field'),
              type: z.string().describe('Type of the field'),
              required: z.boolean().optional().describe('Whether the field is required'),
              unique: z.boolean().optional().describe('Whether the field is unique'),
              description: z.string().optional().describe('Description of the field'),
              default: z.string().optional().describe('Default value for the field'),
            })
          ).optional().describe('Fields of the entity'),
          description: z.string().optional().describe('Description of the entity'),
          tableName: z.string().optional().describe('Custom table name for the entity'),
        }),
        execute: async ({ name, fields, description, tableName }) => {
          try {
            // In a real implementation, this would create an entity in the database designer
            // For now, we'll return a mock response
            return {
              success: true,
              entityId: `entity-${Date.now()}`,
              name,
              fields: fields || [],
              description,
              tableName,
              message: `Entity "${name}" has been created.`,
            };
          } catch (error) {
            console.error(`Error creating entity ${name}:`, error);
            return {
              success: false,
              message: `Error creating entity: ${(error as Error).message}`,
            };
          }
        },
      },
      
      // Relationship operations
      createRelationship: {
        description: 'Create a relationship between entities',
        parameters: z.object({
          sourceEntity: z.string().describe('ID of the source entity'),
          targetEntity: z.string().describe('ID of the target entity'),
          type: z.enum(['one-to-one', 'one-to-many', 'many-to-one', 'many-to-many']).describe('Type of relationship'),
          sourceField: z.string().describe('Field in the source entity'),
          targetField: z.string().describe('Field in the target entity'),
          description: z.string().optional().describe('Description of the relationship'),
          junctionTable: z.string().optional().describe('Junction table name for many-to-many relationships'),
        }),
        execute: async ({ sourceEntity, targetEntity, type, sourceField, targetField, description, junctionTable }) => {
          try {
            // In a real implementation, this would create a relationship in the database designer
            // For now, we'll return a mock response
            return {
              success: true,
              relationshipId: `relationship-${Date.now()}`,
              sourceEntity,
              targetEntity,
              type,
              sourceField,
              targetField,
              description,
              junctionTable,
              message: `Relationship between entities has been created.`,
            };
          } catch (error) {
            console.error(`Error creating relationship:`, error);
            return {
              success: false,
              message: `Error creating relationship: ${(error as Error).message}`,
            };
          }
        },
      },
      
      // Schema generation
      generatePrismaSchema: {
        description: 'Generate a Prisma schema from the current database schema',
        parameters: z.object({}),
        execute: async () => {
          try {
            // In a real implementation, this would generate a Prisma schema from the database designer
            // For now, we'll return a mock schema
            const prismaSchema = `
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// This is a mock schema. In a real implementation, this would be generated from the database designer.
model User {
  id        String   @id @default(uuid())
  name      String
  email     String   @unique
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  posts     Post[]
  comments  Comment[]
}

model Post {
  id        String   @id @default(uuid())
  title     String
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  author    User     @relation(fields: [authorId], references: [id])
  authorId  String
  comments  Comment[]
}

model Comment {
  id        String   @id @default(uuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  author    User     @relation(fields: [authorId], references: [id])
  authorId  String
  post      Post     @relation(fields: [postId], references: [id])
  postId    String
}
`;
            
            return {
              success: true,
              prismaSchema,
              message: `Prisma schema has been generated.`,
            };
          } catch (error) {
            console.error(`Error generating Prisma schema:`, error);
            return {
              success: false,
              message: `Error generating Prisma schema: ${(error as Error).message}`,
            };
          }
        },
      },
      
      generateSqlSchema: {
        description: 'Generate a SQL schema from the current database schema',
        parameters: z.object({
          dialect: z.enum(['postgresql', 'mysql', 'sqlite', 'sqlserver']).optional().describe('SQL dialect to use'),
        }),
        execute: async ({ dialect = 'postgresql' }) => {
          try {
            // In a real implementation, this would generate a SQL schema from the database designer
            // For now, we'll return a mock schema
            const sqlSchema = `
-- This is a mock schema. In a real implementation, this would be generated from the database designer.
CREATE TABLE "User" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "name" TEXT NOT NULL,
  "email" TEXT NOT NULL UNIQUE,
  "password" TEXT NOT NULL,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL
);

CREATE TABLE "Post" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "title" TEXT NOT NULL,
  "content" TEXT NOT NULL,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL,
  "authorId" UUID NOT NULL REFERENCES "User"("id") ON DELETE CASCADE
);

CREATE TABLE "Comment" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "content" TEXT NOT NULL,
  "createdAt" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP NOT NULL,
  "authorId" UUID NOT NULL REFERENCES "User"("id") ON DELETE CASCADE,
  "postId" UUID NOT NULL REFERENCES "Post"("id") ON DELETE CASCADE
);

CREATE INDEX "Post_authorId_idx" ON "Post"("authorId");
CREATE INDEX "Comment_authorId_idx" ON "Comment"("authorId");
CREATE INDEX "Comment_postId_idx" ON "Comment"("postId");
`;
            
            return {
              success: true,
              sqlSchema,
              dialect,
              message: `SQL schema has been generated for ${dialect}.`,
            };
          } catch (error) {
            console.error(`Error generating SQL schema:`, error);
            return {
              success: false,
              message: `Error generating SQL schema: ${(error as Error).message}`,
            };
          }
        },
      },
    },
    maxSteps: 10,
  });

  return result.toResponse();
}
