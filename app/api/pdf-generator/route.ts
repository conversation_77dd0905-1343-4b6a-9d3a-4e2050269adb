/**
 * PDF Generator API Route
 * 
 * Main API endpoint for PDF generation functionality
 * Integrates with the existing API structure and AI tool system
 */

import { NextRequest, NextResponse } from 'next/server';
import { handlePDFGeneratorRoute } from '@/lib/pdf-generator/api/routes';

/**
 * Handle GET requests
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action');

  // Handle actions that don't require request body
  switch (action) {
    case 'get-templates':
    case 'get-status':
      return handlePDFGeneratorRoute(request, action);
    
    default:
      return NextResponse.json(
        { 
          error: 'Invalid action for GET request',
          availableActions: ['get-templates', 'get-status'],
          message: 'Use POST for document operations'
        },
        { status: 400 }
      );
  }
}

/**
 * Handle POST requests
 */
export async function POST(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action') || 'generate-pdf';

  try {
    return await handlePDFGeneratorRoute(request, action);
  } catch (error) {
    console.error('PDF Generator API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message,
        action 
      },
      { status: 500 }
    );
  }
}

/**
 * Handle OPTIONS requests for CORS
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
