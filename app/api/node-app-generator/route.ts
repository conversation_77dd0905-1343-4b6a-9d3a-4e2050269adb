import { NextRequest, NextResponse } from "next/server";
import { streamText, tool } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";

/**
 * Demo API route for the node app generator
 * This is a simplified version that doesn't require authentication or a real project ID
 */
export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { messages, projectId, context } = body;

    // Use the chat interface with tools
    return streamText({
      model: openai("gpt-4o"),
      system: `# Node.js Application Generator

## Role and Purpose
You are a specialized AI assistant designed to help developers build complete Node.js applications. You have deep knowledge of modern JavaScript/TypeScript development practices and can generate high-quality code for various types of Node.js applications.

## Core Capabilities

1. **Application Architecture**
   - Design scalable and maintainable application architectures
   - Implement best practices for Node.js applications
   - Create appropriate folder structures and module organization
   - Set up proper configuration management

2. **API Development**
   - Design and implement RESTful APIs using Express.js
   - Create GraphQL APIs using Apollo Server
   - Implement proper middleware for authentication, validation, etc.
   - Set up error handling and logging

3. **Database Integration**
   - Integrate with SQL databases (PostgreSQL, MySQL) using Prisma or Sequelize
   - Integrate with NoSQL databases (MongoDB, Firebase) using appropriate ODMs
   - Implement data models and relationships
   - Set up database migrations and seeding

4. **Authentication and Authorization**
   - Implement JWT-based authentication
   - Set up OAuth 2.0 integration
   - Create role-based access control systems
   - Implement secure password handling

5. **Testing and Quality Assurance**
   - Generate unit tests using Jest or Mocha
   - Create integration tests for APIs
   - Set up CI/CD configurations
   - Implement code quality tools (ESLint, Prettier)

## Response Format

When responding to user requests:

1. Start with a clear understanding of the requirements
2. Outline the architecture and approach you'll take
3. Generate the necessary code files with detailed explanations
4. Provide guidance on how to run and test the application
5. Suggest next steps or improvements

Remember to follow modern JavaScript/TypeScript best practices, use async/await for asynchronous operations, and implement proper error handling throughout the application.`,
      messages,
      tools: {
        generateFile: tool({
          description: "Generate a file with the given content",
          parameters: z.object({
            filePath: z.string().describe("Path to the file to generate"),
            content: z.string().describe("Content of the file"),
            description: z.string().optional().describe("Description of the file"),
          }),
          execute: async ({ filePath, content, description }) => {
            // In a real implementation, this would create a file in the project
            // For the demo, we just return success
            console.log(`Generated file: ${filePath}`);
            return {
              success: true,
              filePath,
              message: `File ${filePath} generated successfully`,
            };
          },
        }),

        analyzeCode: tool({
          description: "Analyze code for issues and suggestions",
          parameters: z.object({
            code: z.string().describe("Code to analyze"),
            language: z.string().describe("Programming language of the code"),
          }),
          execute: async ({ code, language }) => {
            // In a real implementation, this would analyze the code
            // For the demo, we just return a mock analysis
            return {
              issues: [
                {
                  type: "style",
                  message: "Consider using const instead of let for variables that are not reassigned",
                  line: 5,
                },
                {
                  type: "performance",
                  message: "This loop could be optimized using a more efficient algorithm",
                  line: 12,
                },
              ],
              suggestions: [
                {
                  type: "refactor",
                  message: "Extract this logic into a separate function for better readability",
                  line: 8,
                },
                {
                  type: "improvement",
                  message: "Add error handling for this async operation",
                  line: 15,
                },
              ],
            };
          },
        }),

        searchNpmPackages: tool({
          description: "Search for npm packages",
          parameters: z.object({
            query: z.string().describe("Search query"),
            limit: z.number().optional().describe("Maximum number of results to return"),
          }),
          execute: async ({ query, limit = 5 }) => {
            // In a real implementation, this would search npm
            // For the demo, we just return mock results
            return {
              results: [
                {
                  name: "express",
                  version: "4.18.2",
                  description: "Fast, unopinionated, minimalist web framework",
                  downloads: 20000000,
                  repository: "https://github.com/expressjs/express",
                },
                {
                  name: "mongoose",
                  version: "7.5.0",
                  description: "MongoDB object modeling designed to work in an asynchronous environment",
                  downloads: 10000000,
                  repository: "https://github.com/Automattic/mongoose",
                },
                {
                  name: "prisma",
                  version: "5.2.0",
                  description: "Next-generation ORM for Node.js & TypeScript",
                  downloads: 5000000,
                  repository: "https://github.com/prisma/prisma",
                },
              ].slice(0, limit),
            };
          },
        }),

        generateProjectStructure: tool({
          description: "Generate a complete project structure for a Node.js application",
          parameters: z.object({
            appType: z.string().describe("Type of application (e.g., 'rest-api', 'graphql-api', 'fullstack')"),
            features: z.array(z.string()).describe("List of features to include"),
            database: z.string().optional().describe("Database to use (e.g., 'postgres', 'mongodb')"),
            auth: z.boolean().optional().describe("Whether to include authentication"),
          }),
          execute: async ({ appType, features, database, auth }) => {
            // In a real implementation, this would generate a complete project structure
            // For the demo, we just return a mock structure
            return {
              success: true,
              structure: {
                root: [
                  "package.json",
                  "tsconfig.json",
                  ".env",
                  ".gitignore",
                  "README.md",
                ],
                src: [
                  "index.ts",
                  "app.ts",
                  "config.ts",
                ],
                "src/routes": [
                  "index.ts",
                  "auth.routes.ts",
                  "user.routes.ts",
                ],
                "src/controllers": [
                  "auth.controller.ts",
                  "user.controller.ts",
                ],
                "src/models": [
                  "user.model.ts",
                ],
                "src/middleware": [
                  "auth.middleware.ts",
                  "error.middleware.ts",
                ],
                "src/services": [
                  "auth.service.ts",
                  "user.service.ts",
                ],
                "src/utils": [
                  "logger.ts",
                  "validation.ts",
                ],
                "src/types": [
                  "index.ts",
                ],
                "tests": [
                  "setup.ts",
                  "auth.test.ts",
                  "user.test.ts",
                ],
              },
            };
          },
        }),
      },
    }).toDataStreamResponse();
  } catch (error) {
    console.error("Error in node app generator API:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
