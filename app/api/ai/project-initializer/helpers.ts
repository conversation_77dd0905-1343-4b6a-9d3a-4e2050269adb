// Helper functions for project initialization

// Project types
export const PROJECT_TYPES = ["web", "mobile", "backend", "fullstack", "api"] as const
export type ProjectType = typeof PROJECT_TYPES[number]

// Technology stacks by project type
export const TECHNOLOGY_STACKS = {
  web: {
    frontend: ["React", "Next.js", "Vue.js", "Angular", "Svelte"],
    styling: ["Tailwind CSS", "CSS Modules", "Styled Components", "Material UI", "Chakra UI"],
    state: ["Redux", "Zustand", "Context API", "MobX", "Recoil"],
  },
  mobile: {
    frameworks: ["React Native", "Flutter", "Ionic", "Expo", "Native Android/iOS"],
    state: ["Redux", "MobX", "Context API", "Zustand"],
  },
  backend: {
    runtime: ["Node.js", "Python", "Java", "Go", "Ruby", "PHP", "C#"],
    framework: ["Express", "NestJS", "Django", "Flask", "Spring Boot", "Laravel", "ASP.NET"],
    database: ["PostgreSQL", "MongoDB", "MySQL", "Redis", "SQLite", "Prisma ORM"],
  },
  fullstack: {
    frontend: ["React", "Next.js", "Vue.js", "Angular", "Svelte"],
    backend: ["Node.js", "Python", "Java", "Go"],
    database: ["PostgreSQL", "MongoDB", "MySQL", "Redis", "SQLite", "Prisma ORM"],
  },
  api: {
    runtime: ["Node.js", "Python", "Java", "Go", "Ruby", "PHP", "C#"],
    framework: ["Express", "NestJS", "Django REST", "FastAPI", "Spring Boot", "Laravel", "ASP.NET"],
    database: ["PostgreSQL", "MongoDB", "MySQL", "Redis", "SQLite", "Prisma ORM"],
  },
}

/**
 * Determine project type based on requirements
 */
export function determineProjectType(requirements: string): ProjectType {
  const lowerReq = requirements.toLowerCase()
  
  if (lowerReq.includes("mobile") || lowerReq.includes("android") || lowerReq.includes("ios") || lowerReq.includes("app store")) {
    return "mobile"
  } else if (lowerReq.includes("api") || lowerReq.includes("endpoint") || lowerReq.includes("rest") || lowerReq.includes("graphql")) {
    return "api"
  } else if (lowerReq.includes("backend") || lowerReq.includes("server") || lowerReq.includes("database")) {
    return "backend"
  } else if (lowerReq.includes("fullstack") || (lowerReq.includes("frontend") && lowerReq.includes("backend"))) {
    return "fullstack"
  } else {
    return "web" // Default to web
  }
}

/**
 * Calculate project complexity on a scale of 1-10
 */
export function calculateComplexity(requirements: string): number {
  // Simple algorithm to estimate complexity on a scale of 1-10
  const complexityFactors = [
    { term: "authentication", weight: 1 },
    { term: "authorization", weight: 1 },
    { term: "payment", weight: 2 },
    { term: "real-time", weight: 2 },
    { term: "chat", weight: 1.5 },
    { term: "video", weight: 2 },
    { term: "audio", weight: 1.5 },
    { term: "machine learning", weight: 3 },
    { term: "ai", weight: 2.5 },
    { term: "blockchain", weight: 3 },
    { term: "social network", weight: 2 },
    { term: "e-commerce", weight: 2 },
    { term: "dashboard", weight: 1 },
    { term: "analytics", weight: 1.5 },
    { term: "integration", weight: 1 },
    { term: "third-party", weight: 1 },
    { term: "api", weight: 0.5 },
    { term: "database", weight: 0.5 },
  ]
  
  const lowerReq = requirements.toLowerCase()
  let complexity = 1 // Base complexity
  
  complexityFactors.forEach(factor => {
    if (lowerReq.includes(factor.term)) {
      complexity += factor.weight
    }
  })
  
  // Cap at 10
  return Math.min(Math.round(complexity), 10)
}

/**
 * Estimate project time in days
 */
export function estimateTime(requirements: string): number {
  // Estimate time based on complexity
  const complexity = calculateComplexity(requirements)
  
  // Simple formula: 5 days base + 5 days per complexity point
  return 5 + (complexity * 5)
}

/**
 * Extract key components from requirements
 */
export function extractKeyComponents(requirements: string, projectType: string): string[] {
  const components = new Set<string>()
  const lowerReq = requirements.toLowerCase()
  
  // Common components for all project types
  if (lowerReq.includes("auth") || lowerReq.includes("login") || lowerReq.includes("register")) {
    components.add("User Authentication")
  }
  
  if (lowerReq.includes("database") || lowerReq.includes("data") || lowerReq.includes("storage")) {
    components.add("Data Storage")
  }
  
  if (lowerReq.includes("api") || lowerReq.includes("integration") || lowerReq.includes("third-party")) {
    components.add("API Integration")
  }
  
  // Project type specific components
  if (projectType === "web" || projectType === "fullstack" || projectType === "mobile") {
    components.add("User Interface")
  }
  
  if (projectType === "backend" || projectType === "api" || projectType === "fullstack") {
    components.add("Server Architecture")
    components.add("Data Models")
  }
  
  if (lowerReq.includes("payment") || lowerReq.includes("subscription") || lowerReq.includes("billing")) {
    components.add("Payment Processing")
  }
  
  if (lowerReq.includes("real-time") || lowerReq.includes("live") || lowerReq.includes("socket")) {
    components.add("Real-time Communication")
  }
  
  if (lowerReq.includes("file") || lowerReq.includes("upload") || lowerReq.includes("image")) {
    components.add("File Management")
  }
  
  return Array.from(components)
}

/**
 * Identify potential challenges for the project
 */
export function identifyChallenges(requirements: string, projectType: string): string[] {
  const challenges = new Set<string>()
  const lowerReq = requirements.toLowerCase()
  
  // Common challenges
  if (lowerReq.includes("scale") || lowerReq.includes("high traffic") || lowerReq.includes("performance")) {
    challenges.add("Scalability and Performance")
  }
  
  if (lowerReq.includes("secure") || lowerReq.includes("privacy") || lowerReq.includes("sensitive")) {
    challenges.add("Security Implementation")
  }
  
  // Project type specific challenges
  if (projectType === "mobile") {
    challenges.add("Cross-platform Compatibility")
    challenges.add("App Store Approval Process")
  }
  
  if (projectType === "web" || projectType === "fullstack") {
    challenges.add("Browser Compatibility")
    challenges.add("Responsive Design")
  }
  
  if (projectType === "backend" || projectType === "api" || projectType === "fullstack") {
    challenges.add("Database Design and Optimization")
    challenges.add("API Security and Rate Limiting")
  }
  
  if (lowerReq.includes("payment") || lowerReq.includes("subscription")) {
    challenges.add("Payment Gateway Integration")
    challenges.add("Handling Financial Transactions Securely")
  }
  
  if (lowerReq.includes("real-time") || lowerReq.includes("live")) {
    challenges.add("Real-time Data Synchronization")
  }
  
  return Array.from(challenges).slice(0, 5) // Limit to top 5 challenges
}

/**
 * Generate recommendations for the project
 */
export function generateRecommendations(_requirements: string, projectType: string): string[] {
  const recommendations = new Set<string>()
  
  // Common recommendations
  recommendations.add("Start with a minimum viable product (MVP)")
  recommendations.add("Implement automated testing early")
  
  // Project type specific recommendations
  if (projectType === "web" || projectType === "fullstack") {
    recommendations.add("Use a component-based architecture")
    recommendations.add("Implement responsive design from the start")
  }
  
  if (projectType === "mobile") {
    recommendations.add("Consider a cross-platform framework to reduce development time")
    recommendations.add("Plan for app store review processes")
  }
  
  if (projectType === "backend" || projectType === "api" || projectType === "fullstack") {
    recommendations.add("Design a clear API structure with documentation")
    recommendations.add("Implement proper error handling and logging")
  }
  
  recommendations.add("Set up continuous integration/deployment")
  recommendations.add("Consider serverless architecture for scalability")
  
  return Array.from(recommendations).slice(0, 5) // Limit to top 5 recommendations
}
