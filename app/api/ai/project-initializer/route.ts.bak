import { openai } from "@ai-sdk/openai"
import { createDataStreamResponse, streamText, tool } from "ai"
import { z } from "zod"
import { db } from "@/lib/db"
import { getServerSession } from "next-auth"
import { authOptions } from "../../auth/[...nextauth]/route"
import { researchService } from "@/lib/research"
import {
  PROJECT_TYPES,
  determineProjectType,
  calculateComplexity,
  estimateTime,
  extractKeyComponents,
  identifyChallenges,
  generateRecommendations
} from "./helpers"
import {
  suggestTechnologiesForProject,
  generateFileStructureForProject,
  generateConfigFiles,
  createProjectFiles
} from "./file-helpers"

// Allow streaming responses up to 120 seconds
export const maxDuration = 120

export async function POST(req: Request) {
  // Verify user authentication
  const session = await getServerSession(authOptions)
  if (!session?.user) {
    return new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
      headers: { "Content-Type": "application/json" },
    })
  }

  const { messages, projectId } = await req.json()

  // If projectId is provided, fetch existing project details
  let existingProject = null
  if (projectId) {
    existingProject = await db.project.findUnique({
      where: { id: projectId },
      include: { files: true },
    })

    if (!existingProject) {
      return new Response(JSON.stringify({ error: "Project not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      })
    }

    // Verify project ownership
    if (existingProject.userId !== session.user.id) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 403,
        headers: { "Content-Type": "application/json" },
      })
    }
  }

  return createDataStreamResponse({
    execute: async (dataStream) => {
      const result = streamText({
        model: openai("gpt-4o"),
        messages,
        system: `You are an expert AI project initializer specialized in helping users create software projects.
        You analyze user requirements and help set up project structures with appropriate technologies.

        When a user describes a project they want to build:
        1. Help them refine their idea and suggest the best approach
        2. Recommend appropriate technologies based on their requirements
        3. Create a project structure with necessary files and configurations
        4. Provide guidance on next steps for development

        ${existingProject ? `You are working with an existing project: ${existingProject.name} - ${existingProject.description}` : ""}

        Always be helpful, concise, and focus on guiding the user through the project initialization process.`,
        tools: {
          analyzeRequirements: tool({
            description: "Analyze project requirements and provide insights",
            parameters: z.object({
              requirements: z.string().describe("The project requirements to analyze"),
            }),
            execute: async ({ requirements }) => {
              // Analyze the requirements to determine project type, complexity, etc.
              const projectType = determineProjectType(requirements)

              return {
                projectType,
                complexity: calculateComplexity(requirements),
                estimatedTimeInDays: estimateTime(requirements),
                keyComponents: extractKeyComponents(requirements, projectType),
                potentialChallenges: identifyChallenges(requirements, projectType),
                recommendations: generateRecommendations(requirements, projectType),
              }
            },
          }),

          suggestTechnologies: tool({
            description: "Suggest appropriate technologies for a project",
            parameters: z.object({
              projectType: z.enum(PROJECT_TYPES).describe("The type of project"),
              requirements: z.string().describe("The project requirements"),
            }),
            execute: async ({ projectType, requirements }) => {
              // Get technology suggestions based on project type and requirements
              const suggestions = suggestTechnologiesForProject(projectType, requirements)

              return {
                suggestedTechnologies: suggestions,
                reasoning: `Based on your ${projectType} project requirements, these technologies would provide a solid foundation.`,
              }
            },
          }),

          researchTechnology: tool({
            description: "Research a specific technology to get up-to-date information",
            parameters: z.object({
              technology: z.string().describe("The technology to research"),
              context: z.string().describe("The context in which the technology will be used"),
            }),
            execute: async ({ technology, context }) => {
              // Use web search to get up-to-date information about the technology
              const searchQuery = `${technology} latest features best practices for ${context} development`
              const searchResults = await researchService.performWebSearch({ query: searchQuery })

              return {
                technology,
                information: searchResults,
                recommendations: `Based on current information, here are recommendations for using ${technology} in your project.`,
              }
            },
          }),

          initializeProject: tool({
            description: "Initialize a new project with the specified details",
            parameters: z.object({
              name: z.string().describe("The name of the project"),
              description: z.string().describe("A detailed description of the project"),
              type: z.enum(PROJECT_TYPES).describe("The type of project"),
              technologies: z.array(z.string()).describe("List of technologies to use"),
              features: z.array(z.string()).optional().describe("List of key features to implement"),
            }),
            execute: async ({ name, description, type, technologies, features }) => {
              // Create a new project in the database
              const project = await db.project.create({
                data: {
                  name,
                  description,
                  appType: type,
                  technologies: JSON.stringify(technologies),
                  userId: session.user.id as string,
                  status: "planning",
                },
              })

              // Log activity
              await db.activity.create({
                data: {
                  userId: session.user.id as string,
                  type: "project_created",
                  description: `Project "${name}" created`,
                  projectId: project.id,
                  metadata: {
                    projectType: type,
                    technologies,
                    features,
                  },
                },
              })

              return {
                success: true,
                projectId: project.id,
                message: `Project "${name}" initialized successfully with ${technologies.length} technologies.`,
              }
            },
          }),

          generateFileStructure: tool({
            description: "Generate a file structure for a project",
            parameters: z.object({
              projectId: z.string().describe("The ID of the project"),
              projectType: z.enum(PROJECT_TYPES).describe("The type of project"),
              technologies: z.array(z.string()).describe("List of technologies used in the project"),
            }),
            execute: async ({ projectId, projectType, technologies }) => {
              // Generate file structure based on project type and technologies
              const fileStructure = generateFileStructureForProject(projectType, technologies)

              // Create the files in the database
              const createdFiles = await createProjectFiles(projectId, fileStructure)

              return {
                success: true,
                fileCount: createdFiles.length,
                fileStructure,
                message: `Generated ${createdFiles.length} files for the project.`,
              }
            },
          }),

          generateConfigFiles: tool({
            description: "Generate configuration files for the project",
            parameters: z.object({
              projectId: z.string().describe("The ID of the project"),
              technologies: z.array(z.string()).describe("List of technologies used in the project"),
            }),
            execute: async ({ projectId, technologies }) => {
              // Generate configuration files based on technologies
              const configFiles = generateConfigFiles(technologies)

              // Create the config files in the database
              const createdFiles = await createProjectFiles(projectId, configFiles)

              return {
                success: true,
                fileCount: createdFiles.length,
                configFiles,
                message: `Generated ${createdFiles.length} configuration files for the project.`,
              }
            },
          }),

          createProjectTasks: tool({
            description: "Create initial tasks for the project",
            parameters: z.object({
              projectId: z.string().describe("The ID of the project"),
              tasks: z.array(
                z.object({
                  title: z.string().describe("Task title"),
                  description: z.string().describe("Task description"),
                  priority: z.enum(["low", "medium", "high", "critical"]).describe("Task priority"),
                })
              ).describe("List of tasks to create"),
            }),
            execute: async ({ projectId, tasks }) => {
              // Create tasks in the database
              const createdTasks = await Promise.all(
                tasks.map(task =>
                  db.projectTask.create({
                    data: {
                      projectId,
                      title: task.title,
                      description: task.description,
                      priority: task.priority,
                      status: "todo",
                    },
                  })
                )
              )

              return {
                success: true,
                taskCount: createdTasks.length,
                tasks: createdTasks.map(task => ({ id: task.id, title: task.title })),
                message: `Created ${createdTasks.length} tasks for the project.`,
              }
            },
          }),

          updateProjectStatus: tool({
            description: "Update the project status",
            parameters: z.object({
              projectId: z.string().describe("The ID of the project"),
              status: z.enum(["planning", "generating", "testing", "ready", "deployed"] as const).describe("The new status"),
            }),
            execute: async ({ projectId, status }) => {
              // Update project status
              await db.project.update({
                where: { id: projectId },
                data: { status },
              })

              return {
                success: true,
                status,
                message: `Project status updated to "${status}".`,
              }
            },
          }),
        },
        maxSteps: 10, // Enable multi-step tool calls
      })

      result.mergeIntoDataStream(dataStream)
    },
  })
}

// Helper functions

// Helper functions are now imported from './helpers.ts'

  // Common challenges
  if (lowerReq.includes("scale") || lowerReq.includes("high traffic") || lowerReq.includes("performance")) {
    challenges.add("Scalability and Performance")
  }

  if (lowerReq.includes("secure") || lowerReq.includes("privacy") || lowerReq.includes("sensitive")) {
    challenges.add("Security Implementation")
  }

  // Project type specific challenges
  if (projectType === "mobile") {
    challenges.add("Cross-platform Compatibility")
    challenges.add("App Store Approval Process")
  }

  if (projectType === "web" || projectType === "fullstack") {
    challenges.add("Browser Compatibility")
    challenges.add("Responsive Design")
  }

  if (projectType === "backend" || projectType === "api" || projectType === "fullstack") {
    challenges.add("Database Design and Optimization")
    challenges.add("API Security and Rate Limiting")
  }

  if (lowerReq.includes("payment") || lowerReq.includes("subscription")) {
    challenges.add("Payment Gateway Integration")
    challenges.add("Handling Financial Transactions Securely")
  }

  if (lowerReq.includes("real-time") || lowerReq.includes("live")) {
    challenges.add("Real-time Data Synchronization")
  }

  return Array.from(challenges).slice(0, 5) // Limit to top 5 challenges
}

function generateRecommendations(_requirements: string, projectType: string): string[] {
  const recommendations = new Set<string>()

  // Common recommendations
  recommendations.add("Start with a minimum viable product (MVP)")
  recommendations.add("Implement automated testing early")

  // Project type specific recommendations
  if (projectType === "web" || projectType === "fullstack") {
    recommendations.add("Use a component-based architecture")
    recommendations.add("Implement responsive design from the start")
  }

  if (projectType === "mobile") {
    recommendations.add("Consider a cross-platform framework to reduce development time")
    recommendations.add("Plan for app store review processes")
  }

  if (projectType === "backend" || projectType === "api" || projectType === "fullstack") {
    recommendations.add("Design a clear API structure with documentation")
    recommendations.add("Implement proper error handling and logging")
  }

  recommendations.add("Set up continuous integration/deployment")
  recommendations.add("Consider serverless architecture for scalability")

  return Array.from(recommendations).slice(0, 5) // Limit to top 5 recommendations
}

function suggestTechnologiesForProject(projectType: string, requirements: string): string[] {
  const lowerReq = requirements.toLowerCase()
  const suggestions: string[] = []

  // Get technology options based on project type
  // const techOptions = TECHNOLOGY_STACKS[projectType as keyof typeof TECHNOLOGY_STACKS]
  // Not used directly, but helps with type checking

  // Add technologies based on project type
  if (projectType === "web") {
    // Select frontend framework
    if (lowerReq.includes("react") || lowerReq.includes("next")) {
      suggestions.push("Next.js")
    } else if (lowerReq.includes("vue")) {
      suggestions.push("Vue.js")
    } else if (lowerReq.includes("angular")) {
      suggestions.push("Angular")
    } else if (lowerReq.includes("svelte")) {
      suggestions.push("Svelte")
    } else {
      suggestions.push("Next.js") // Default to Next.js
    }

    // Add styling
    if (lowerReq.includes("tailwind")) {
      suggestions.push("Tailwind CSS")
    } else if (lowerReq.includes("material")) {
      suggestions.push("Material UI")
    } else if (lowerReq.includes("chakra")) {
      suggestions.push("Chakra UI")
    } else {
      suggestions.push("Tailwind CSS") // Default to Tailwind
    }

    // Add state management if needed
    if (lowerReq.includes("state") || lowerReq.includes("complex")) {
      if (lowerReq.includes("redux")) {
        suggestions.push("Redux")
      } else if (lowerReq.includes("zustand")) {
        suggestions.push("Zustand")
      } else {
        suggestions.push("Zustand") // Default to Zustand
      }
    }
  } else if (projectType === "mobile") {
    // Select mobile framework
    if (lowerReq.includes("react native") || lowerReq.includes("react")) {
      suggestions.push("React Native")
    } else if (lowerReq.includes("flutter")) {
      suggestions.push("Flutter")
    } else if (lowerReq.includes("ionic")) {
      suggestions.push("Ionic")
    } else if (lowerReq.includes("expo")) {
      suggestions.push("Expo")
    } else {
      suggestions.push("React Native") // Default to React Native
    }

    // Add state management if needed
    if (lowerReq.includes("state") || lowerReq.includes("complex")) {
      suggestions.push("Redux")
    }
  } else if (projectType === "backend" || projectType === "api") {
    // Select backend runtime
    if (lowerReq.includes("node") || lowerReq.includes("javascript") || lowerReq.includes("typescript")) {
      suggestions.push("Node.js")

      // Select Node.js framework
      if (lowerReq.includes("express")) {
        suggestions.push("Express")
      } else if (lowerReq.includes("nest")) {
        suggestions.push("NestJS")
      } else {
        suggestions.push("Express") // Default to Express
      }
    } else if (lowerReq.includes("python")) {
      suggestions.push("Python")

      // Select Python framework
      if (lowerReq.includes("django")) {
        suggestions.push("Django")
      } else if (lowerReq.includes("flask")) {
        suggestions.push("Flask")
      } else if (lowerReq.includes("fastapi")) {
        suggestions.push("FastAPI")
      } else {
        suggestions.push("FastAPI") // Default to FastAPI
      }
    } else {
      suggestions.push("Node.js")
      suggestions.push("Express") // Default to Node.js + Express
    }

    // Select database
    if (lowerReq.includes("sql") || lowerReq.includes("relational")) {
      suggestions.push("PostgreSQL")
      suggestions.push("Prisma ORM")
    } else if (lowerReq.includes("mongo") || lowerReq.includes("nosql")) {
      suggestions.push("MongoDB")
    } else {
      suggestions.push("PostgreSQL")
      suggestions.push("Prisma ORM") // Default to PostgreSQL + Prisma
    }
  } else if (projectType === "fullstack") {
    // Frontend
    if (lowerReq.includes("react") || lowerReq.includes("next")) {
      suggestions.push("Next.js")
    } else if (lowerReq.includes("vue")) {
      suggestions.push("Vue.js")
    } else {
      suggestions.push("Next.js") // Default to Next.js
    }

    // Styling
    suggestions.push("Tailwind CSS")

    // Backend
    suggestions.push("Node.js")
    suggestions.push("Express")

    // Database
    if (lowerReq.includes("sql") || lowerReq.includes("relational")) {
      suggestions.push("PostgreSQL")
      suggestions.push("Prisma ORM")
    } else if (lowerReq.includes("mongo") || lowerReq.includes("nosql")) {
      suggestions.push("MongoDB")
    } else {
      suggestions.push("PostgreSQL")
      suggestions.push("Prisma ORM") // Default to PostgreSQL + Prisma
    }
  }

  // Add TypeScript for all projects unless specifically mentioned not to
  if (!lowerReq.includes("no typescript") && !lowerReq.includes("without typescript")) {
    suggestions.push("TypeScript")
  }

  // Add testing framework
  suggestions.push("Jest")

  return suggestions
}

function generateFileStructureForProject(projectType: string, technologies: string[]): any {
  // Base file structure by project type
  const fileStructures: Record<string, any> = {
    web: {
      "src/": {
        "components/": ["Header.tsx", "Footer.tsx", "Layout.tsx"],
        "pages/": ["index.tsx", "about.tsx"],
        "styles/": ["globals.css"],
        "utils/": ["api.ts", "helpers.ts"],
        "hooks/": ["useAuth.ts"],
        "context/": ["AuthContext.tsx"],
      },
      "public/": ["favicon.ico"],
      "README.md": "# Web Project\n\nThis is a web application built with modern technologies.",
    },
    mobile: {
      "src/": {
        "components/": ["Header.tsx", "Footer.tsx"],
        "screens/": ["Home.tsx", "Profile.tsx", "Settings.tsx"],
        "navigation/": ["AppNavigator.tsx"],
        "services/": ["api.ts"],
        "hooks/": ["useAuth.ts"],
        "context/": ["AuthContext.tsx"],
        "assets/": {
          "images/": ["logo.png"],
          "fonts/": [],
        },
      },
      "README.md": "# Mobile App\n\nThis is a mobile application built with modern technologies.",
    },
    backend: {
      "src/": {
        "controllers/": ["userController.ts", "authController.ts"],
        "models/": ["userModel.ts"],
        "routes/": ["userRoutes.ts", "authRoutes.ts"],
        "middleware/": ["auth.ts", "error.ts", "logger.ts"],
        "services/": ["userService.ts"],
        "utils/": ["helpers.ts", "validators.ts"],
        "config/": ["database.ts", "env.ts"],
      },
      "tests/": ["user.test.ts", "auth.test.ts"],
      "README.md": "# Backend API\n\nThis is a backend API built with modern technologies.",
    },
    fullstack: {
      "client/": {
        "src/": {
          "components/": ["Header.tsx", "Footer.tsx", "Layout.tsx"],
          "pages/": ["index.tsx", "about.tsx"],
          "styles/": ["globals.css"],
          "utils/": ["api.ts", "helpers.ts"],
          "hooks/": ["useAuth.ts"],
          "context/": ["AuthContext.tsx"],
        },
        "public/": ["favicon.ico"],
      },
      "server/": {
        "src/": {
          "controllers/": ["userController.ts", "authController.ts"],
          "models/": ["userModel.ts"],
          "routes/": ["userRoutes.ts", "authRoutes.ts"],
          "middleware/": ["auth.ts", "error.ts", "logger.ts"],
          "services/": ["userService.ts"],
          "utils/": ["helpers.ts", "validators.ts"],
          "config/": ["database.ts", "env.ts"],
        },
        "tests/": ["user.test.ts", "auth.test.ts"],
      },
      "README.md": "# Fullstack Application\n\nThis is a fullstack application with client and server components.",
    },
    api: {
      "src/": {
        "controllers/": ["userController.ts", "authController.ts"],
        "models/": ["userModel.ts"],
        "routes/": ["userRoutes.ts", "authRoutes.ts"],
        "middleware/": ["auth.ts", "error.ts", "logger.ts"],
        "services/": ["userService.ts"],
        "utils/": ["helpers.ts", "validators.ts"],
        "config/": ["database.ts", "env.ts"],
      },
      "tests/": ["user.test.ts", "auth.test.ts"],
      "README.md": "# API Service\n\nThis is an API service built with modern technologies.",
    },
  }

  // Customize file structure based on technologies
  const structure = JSON.parse(JSON.stringify(fileStructures[projectType])) // Deep clone

  // Add technology-specific files
  if (technologies.includes("Next.js")) {
    structure["next.config.js"] = "/** @type {import('next').NextConfig} */\nmodule.exports = {\n  reactStrictMode: true,\n};"
    structure["src/pages/_app.tsx"] = "import '../styles/globals.css';\nimport type { AppProps } from 'next/app';\n\nexport default function App({ Component, pageProps }: AppProps) {\n  return <Component {...pageProps} />;\n}"
    structure["src/pages/index.tsx"] = "export default function Home() {\n  return (\n    <div>\n      <h1>Welcome to the application</h1>\n    </div>\n  );\n}"
  }

  if (technologies.includes("Express")) {
    structure["src/app.ts"] = "import express from 'express';\nimport cors from 'cors';\nimport { userRoutes } from './routes/userRoutes';\nimport { authRoutes } from './routes/authRoutes';\n\nconst app = express();\n\napp.use(cors());\napp.use(express.json());\n\napp.use('/api/users', userRoutes);\napp.use('/api/auth', authRoutes);\n\nexport default app;"
    structure["src/server.ts"] = "import app from './app';\n\nconst PORT = process.env.PORT || 3000;\n\napp.listen(PORT, () => {\n  console.log(`Server running on port ${PORT}`);\n});"
  }

  if (technologies.includes("Prisma ORM")) {
    structure["prisma/"] = {
      "schema.prisma": "generator client {\n  provider = \"prisma-client-js\"\n}\n\ndatasource db {\n  provider = \"postgresql\"\n  url      = env(\"DATABASE_URL\")\n}\n\nmodel User {\n  id        String   @id @default(uuid())\n  email     String   @unique\n  name      String?\n  password  String\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n}",
    }
    structure["src/lib/prisma.ts"] = "import { PrismaClient } from '@prisma/client';\n\nconst prisma = new PrismaClient();\n\nexport default prisma;"
  }

  if (technologies.includes("React Native")) {
    structure["App.tsx"] = "import React from 'react';\nimport { NavigationContainer } from '@react-navigation/native';\nimport AppNavigator from './src/navigation/AppNavigator';\n\nexport default function App() {\n  return (\n    <NavigationContainer>\n      <AppNavigator />\n    </NavigationContainer>\n  );\n}"
  }

  if (technologies.includes("TypeScript")) {
    structure["tsconfig.json"] = "{\n  \"compilerOptions\": {\n    \"target\": \"es2017\",\n    \"module\": \"commonjs\",\n    \"lib\": [\"dom\", \"es6\", \"es2017\", \"esnext.asynciterable\"],\n    \"skipLibCheck\": true,\n    \"sourceMap\": true,\n    \"outDir\": \"./dist\",\n    \"moduleResolution\": \"node\",\n    \"removeComments\": true,\n    \"noImplicitAny\": true,\n    \"strictNullChecks\": true,\n    \"strictFunctionTypes\": true,\n    \"noImplicitThis\": true,\n    \"noUnusedLocals\": true,\n    \"noUnusedParameters\": true,\n    \"noImplicitReturns\": true,\n    \"noFallthroughCasesInSwitch\": true,\n    \"allowSyntheticDefaultImports\": true,\n    \"esModuleInterop\": true,\n    \"emitDecoratorMetadata\": true,\n    \"experimentalDecorators\": true,\n    \"resolveJsonModule\": true,\n    \"baseUrl\": \".\"\n  },\n  \"exclude\": [\"node_modules\"],\n  \"include\": [\"./src/**/*.ts\"]\n}"
  }

  return structure
}

function generateConfigFiles(technologies: string[]): any {
  const configFiles: Record<string, string> = {}

  // Package.json based on technologies
  const dependencies: Record<string, string> = {}
  const devDependencies: Record<string, string> = {}

  // Add common dependencies
  if (technologies.includes("TypeScript")) {
    devDependencies["typescript"] = "^5.0.0"
    devDependencies["@types/node"] = "^18.0.0"
  }

  if (technologies.includes("Jest")) {
    devDependencies["jest"] = "^29.0.0"
    devDependencies["ts-jest"] = "^29.0.0"
    devDependencies["@types/jest"] = "^29.0.0"
  }

  // Add technology-specific dependencies
  if (technologies.includes("Next.js")) {
    dependencies["next"] = "^13.0.0"
    dependencies["react"] = "^18.0.0"
    dependencies["react-dom"] = "^18.0.0"
    devDependencies["@types/react"] = "^18.0.0"
    devDependencies["@types/react-dom"] = "^18.0.0"

    if (technologies.includes("Tailwind CSS")) {
      devDependencies["tailwindcss"] = "^3.0.0"
      devDependencies["postcss"] = "^8.0.0"
      devDependencies["autoprefixer"] = "^10.0.0"
    }
  }

  if (technologies.includes("Express")) {
    dependencies["express"] = "^4.0.0"
    dependencies["cors"] = "^2.0.0"
    dependencies["dotenv"] = "^16.0.0"
    devDependencies["@types/express"] = "^4.0.0"
    devDependencies["@types/cors"] = "^2.0.0"
    devDependencies["nodemon"] = "^2.0.0"
    devDependencies["ts-node"] = "^10.0.0"
  }

  if (technologies.includes("Prisma ORM")) {
    dependencies["@prisma/client"] = "^4.0.0"
    devDependencies["prisma"] = "^4.0.0"
  }

  if (technologies.includes("PostgreSQL")) {
    dependencies["pg"] = "^8.0.0"
    devDependencies["@types/pg"] = "^8.0.0"
  }

  if (technologies.includes("MongoDB")) {
    dependencies["mongoose"] = "^7.0.0"
    devDependencies["@types/mongoose"] = "^5.0.0"
  }

  if (technologies.includes("React Native")) {
    dependencies["react-native"] = "^0.70.0"
    dependencies["@react-navigation/native"] = "^6.0.0"
    dependencies["@react-navigation/native-stack"] = "^6.0.0"
    dependencies["react-native-screens"] = "^3.0.0"
    dependencies["react-native-safe-area-context"] = "^4.0.0"
  }

  // Create package.json
  const packageJson = {
    name: "project-name",
    version: "0.1.0",
    private: true,
    scripts: {
      start: technologies.includes("Next.js") ? "next start" : "node dist/server.js",
      dev: technologies.includes("Next.js") ? "next dev" : "nodemon src/server.ts",
      build: technologies.includes("Next.js") ? "next build" : "tsc",
      test: "jest",
    },
    dependencies,
    devDependencies,
  }

  configFiles["package.json"] = JSON.stringify(packageJson, null, 2)

  // Add other config files based on technologies
  if (technologies.includes("Tailwind CSS")) {
    configFiles["tailwind.config.js"] = "/** @type {import('tailwindcss').Config} */\nmodule.exports = {\n  content: [\n    './src/pages/**/*.{js,ts,jsx,tsx}',\n    './src/components/**/*.{js,ts,jsx,tsx}',\n  ],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n}"
    configFiles["postcss.config.js"] = "module.exports = {\n  plugins: {\n    tailwindcss: {},\n    autoprefixer: {},\n  },\n}"
  }

  if (technologies.includes("Jest")) {
    configFiles["jest.config.js"] = "/** @type {import('ts-jest').JestConfigWithTsJest} */\nmodule.exports = {\n  preset: 'ts-jest',\n  testEnvironment: 'node',\n  testMatch: ['**/*.test.ts'],\n};"
  }

  if (technologies.includes("ESLint")) {
    configFiles[".eslintrc.js"] = "module.exports = {\n  parser: '@typescript-eslint/parser',\n  extends: [\n    'eslint:recommended',\n    'plugin:@typescript-eslint/recommended',\n  ],\n  parserOptions: {\n    ecmaVersion: 2020,\n    sourceType: 'module',\n  },\n  rules: {},\n};"
  }

  // Add .env file
  configFiles[".env.example"] = technologies.includes("Prisma ORM")
    ? "# Database\nDATABASE_URL=postgresql://user:password@localhost:5432/dbname\n\n# Server\nPORT=3000\nNODE_ENV=development\n\n# Auth\nJWT_SECRET=your_jwt_secret"
    : "# Server\nPORT=3000\nNODE_ENV=development\n\n# Auth\nJWT_SECRET=your_jwt_secret"

  // Add .gitignore
  configFiles[".gitignore"] = "# dependencies\nnode_modules\n.pnp\n.pnp.js\n\n# testing\ncoverage\n\n# production\nbuild\ndist\n\n# misc\n.DS_Store\n.env\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n\n# IDE\n.idea\n.vscode"

  return configFiles
}

async function createProjectFiles(projectId: string, fileStructure: any, basePath: string = ""): Promise<any[]> {
  const createdFiles: any[] = []

  // Process the file structure recursively
  for (const [key, value] of Object.entries(fileStructure)) {
    const path = `${basePath}${key}`

    if (typeof value === "string") {
      // It's a file with content
      const file = await db.generatedFile.create({
        data: {
          projectId,
          path,
          content: value,
          language: getLanguageFromPath(path),
          type: "file",
        },
      })
      createdFiles.push(file)
    } else if (Array.isArray(value)) {
      // It's a directory with file names only
      for (const fileName of value) {
        const filePath = `${path}${fileName}`
        const file = await db.generatedFile.create({
          data: {
            projectId,
            path: filePath,
            content: "", // Empty content for now
            language: getLanguageFromPath(filePath),
            type: "file",
          },
        })
        createdFiles.push(file)
      }
    } else if (typeof value === "object") {
      // It's a directory with nested structure
      // Create directory entry
      await db.generatedFile.create({
        data: {
          projectId,
          path,
          content: "",
          language: "plaintext",
          type: "directory",
        },
      })

      // Process nested files
      const nestedFiles = await createProjectFiles(projectId, value, path)
      createdFiles.push(...nestedFiles)
    }
  }

  return createdFiles
}

function getLanguageFromPath(path: string): string {
  const extension = path.split(".").pop()?.toLowerCase() || ""

  const languageMap: Record<string, string> = {
    "js": "javascript",
    "jsx": "javascript",
    "ts": "typescript",
    "tsx": "typescript",
    "json": "json",
    "md": "markdown",
    "css": "css",
    "html": "html",
    "py": "python",
    "java": "java",
    "go": "go",
    "rb": "ruby",
    "php": "php",
    "cs": "csharp",
    "prisma": "prisma",
  }

  return languageMap[extension] || "plaintext"
}