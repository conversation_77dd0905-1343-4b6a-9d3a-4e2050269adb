import { openai } from "@ai-sdk/openai"
import { createDataStreamResponse, streamText, tool } from "ai"
import { z } from "zod"
import { db } from "@/lib/db"
import { getServerSession } from "next-auth"
import { authOptions } from "../../auth/[...nextauth]/route"
import { researchService } from "@/lib/research"
import { extractTextFromPdf } from "@/lib/research/utils/pdf"
import {
  PROJECT_TYPES,
  determineProjectType,
  calculateComplexity,
  estimateTime,
  extractKeyComponents,
  identifyChallenges,
  generateRecommendations
} from "./helpers"
import {
  suggestTechnologiesForProject,
  generateFileStructureForProject,
  generateConfigFiles,
  createProjectFiles
} from "./file-helpers"

// Allow streaming responses up to 120 seconds
export const maxDuration = 120

export async function POST(req: Request) {
  // Verify user authentication
  const session = await getServerSession(authOptions)
  if (!session?.user) {
    return new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
      headers: { "Content-Type": "application/json" },
    })
  }

  const { messages, projectId } = await req.json()

  // If projectId is provided, fetch existing project details
  let existingProject = null
  if (projectId) {
    existingProject = await db.project.findUnique({
      where: { id: projectId },
      include: { files: true },
    })

    if (!existingProject) {
      return new Response(JSON.stringify({ error: "Project not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      })
    }

    // Verify project ownership
    if (existingProject.userId !== session.user.id) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 403,
        headers: { "Content-Type": "application/json" },
      })
    }
  }

  return createDataStreamResponse({
    execute: async (dataStream) => {
      const result = streamText({
        model: openai("gpt-4o"),
        messages,
        system: `You are an expert AI project initializer specialized in helping users create software projects.
        You analyze user requirements and help set up project structures with appropriate technologies.

        When a user describes a project they want to build:
        1. Help them refine their idea and suggest the best approach
        2. Recommend appropriate technologies based on their requirements
        3. Create a project structure with necessary files and configurations
        4. Provide guidance on next steps for development

        ${existingProject ? `You are working with an existing project: ${existingProject.name} - ${existingProject.description}` : ""}

        Always be helpful, concise, and focus on guiding the user through the project initialization process.`,
        tools: {
          analyzeRequirements: tool({
            description: "Analyze project requirements and provide insights",
            parameters: z.object({
              requirements: z.string().describe("The project requirements to analyze"),
            }),
            execute: async ({ requirements }) => {
              // Analyze the requirements to determine project type, complexity, etc.
              const projectType = determineProjectType(requirements)

              return {
                projectType,
                complexity: calculateComplexity(requirements),
                estimatedTimeInDays: estimateTime(requirements),
                keyComponents: extractKeyComponents(requirements, projectType),
                potentialChallenges: identifyChallenges(requirements, projectType),
                recommendations: generateRecommendations(requirements, projectType),
              }
            },
          }),

          suggestTechnologies: tool({
            description: "Suggest appropriate technologies for a project",
            parameters: z.object({
              projectType: z.enum(PROJECT_TYPES).describe("The type of project"),
              requirements: z.string().describe("The project requirements"),
            }),
            execute: async ({ projectType, requirements }) => {
              // Get technology suggestions based on project type and requirements
              const suggestions = suggestTechnologiesForProject(projectType, requirements)

              return {
                suggestedTechnologies: suggestions,
                reasoning: `Based on your ${projectType} project requirements, these technologies would provide a solid foundation.`,
              }
            },
          }),

          researchTechnology: tool({
            description: "Research a specific technology to get up-to-date information",
            parameters: z.object({
              technology: z.string().describe("The technology to research"),
              context: z.string().describe("The context in which the technology will be used"),
            }),
            execute: async ({ technology, context }) => {
              // Use web search to get up-to-date information about the technology
              const searchQuery = `${technology} latest features best practices for ${context} development`
              const searchResults = await researchService.performWebSearch({ query: searchQuery })

              return {
                technology,
                information: searchResults,
                recommendations: `Based on current information, here are recommendations for using ${technology} in your project.`,
              }
            },
          }),

          initializeProject: tool({
            description: "Initialize a new project with the specified details",
            parameters: z.object({
              name: z.string().describe("The name of the project"),
              description: z.string().describe("A detailed description of the project"),
              type: z.enum(PROJECT_TYPES).describe("The type of project"),
              technologies: z.array(z.string()).describe("List of technologies to use"),
              features: z.array(z.string()).optional().describe("List of key features to implement"),
            }),
            execute: async ({ name, description, type, technologies, features }) => {
              // Create a new project in the database
              const project = await db.project.create({
                data: {
                  name,
                  description,
                  appType: type,
                  technologies: JSON.stringify(technologies),
                  userId: session.user.id as string,
                  status: "planning",
                },
              })

              // Log activity
              await db.activity.create({
                data: {
                  userId: session.user.id as string,
                  type: "project_created",
                  description: `Project "${name}" created`,
                  projectId: project.id,
                  metadata: {
                    projectType: type,
                    technologies,
                    features,
                  },
                },
              })

              return {
                success: true,
                projectId: project.id,
                message: `Project "${name}" initialized successfully with ${technologies.length} technologies.`,
              }
            },
          }),

          generateFileStructure: tool({
            description: "Generate a file structure for a project",
            parameters: z.object({
              projectId: z.string().describe("The ID of the project"),
              projectType: z.enum(PROJECT_TYPES).describe("The type of project"),
              technologies: z.array(z.string()).describe("List of technologies used in the project"),
            }),
            execute: async ({ projectId, projectType, technologies }) => {
              // Generate file structure based on project type and technologies
              const fileStructure = generateFileStructureForProject(projectType, technologies)

              // Create the files in the database
              const createdFiles = await createProjectFiles(projectId, fileStructure)

              return {
                success: true,
                fileCount: createdFiles.length,
                fileStructure,
                message: `Generated ${createdFiles.length} files for the project.`,
              }
            },
          }),

          generateConfigFiles: tool({
            description: "Generate configuration files for the project",
            parameters: z.object({
              projectId: z.string().describe("The ID of the project"),
              technologies: z.array(z.string()).describe("List of technologies used in the project"),
            }),
            execute: async ({ projectId, technologies }) => {
              // Generate configuration files based on technologies
              const configFiles = generateConfigFiles(technologies)

              // Create the config files in the database
              const createdFiles = await createProjectFiles(projectId, configFiles)

              return {
                success: true,
                fileCount: createdFiles.length,
                configFiles,
                message: `Generated ${createdFiles.length} configuration files for the project.`,
              }
            },
          }),

          createProjectTasks: tool({
            description: "Create initial tasks for the project",
            parameters: z.object({
              projectId: z.string().describe("The ID of the project"),
              tasks: z.array(
                z.object({
                  title: z.string().describe("Task title"),
                  description: z.string().describe("Task description"),
                  priority: z.enum(["low", "medium", "high", "critical"]).describe("Task priority"),
                })
              ).describe("List of tasks to create"),
            }),
            execute: async ({ projectId, tasks }) => {
              // Create tasks in the database
              const createdTasks = await Promise.all(
                tasks.map(task =>
                  db.projectTask.create({
                    data: {
                      projectId,
                      title: task.title,
                      description: task.description,
                      priority: task.priority,
                      status: "todo",
                    },
                  })
                )
              )

              return {
                success: true,
                taskCount: createdTasks.length,
                tasks: createdTasks.map(task => ({ id: task.id, title: task.title })),
                message: `Created ${createdTasks.length} tasks for the project.`,
              }
            },
          }),

          updateProjectStatus: tool({
            description: "Update the project status",
            parameters: z.object({
              projectId: z.string().describe("The ID of the project"),
              status: z.enum(["planning", "generating", "testing", "ready", "deployed"] as const).describe("The new status"),
            }),
            execute: async ({ projectId, status }) => {
              // Update project status
              await db.project.update({
                where: { id: projectId },
                data: { status },
              })

              return {
                success: true,
                status,
                message: `Project status updated to "${status}".`,
              }
            },
          }),

          analyzePdfRequirements: tool({
            description: "Analyze project requirements from PDF content",
            parameters: z.object({
              pdfText: z.string().describe("The extracted text content from the PDF"),
              pdfFilename: z.string().describe("The name of the PDF file"),
            }),
            execute: async ({ pdfText, pdfFilename }) => {
              try {
                // Call the requirements extraction API
                const response = await fetch(new URL('/api/pdf-processor/extract-requirements', req.url), {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ pdfText }),
                });

                if (!response.ok) {
                  throw new Error('Failed to extract requirements from PDF');
                }

                const data = await response.json();

                if (!data.success || !data.requirements) {
                  throw new Error('Invalid response from requirements extraction API');
                }

                const { projectType, features, technologies, description, complexity } = data.requirements;

                // Log the PDF analysis
                await db.activity.create({
                  data: {
                    userId: session.user.id as string,
                    type: "pdf_analyzed",
                    description: `PDF "${pdfFilename}" analyzed for project requirements`,
                    metadata: {
                      pdfFilename,
                      extractedRequirements: data.requirements,
                    },
                  },
                });

                return {
                  success: true,
                  projectType,
                  features,
                  technologies,
                  description,
                  complexity,
                  message: `Successfully extracted requirements from "${pdfFilename}".`,
                };
              } catch (error: any) {
                console.error('Error analyzing PDF requirements:', error);
                return {
                  success: false,
                  error: error.message || 'Failed to analyze PDF requirements',
                  message: `Failed to extract requirements from "${pdfFilename}".`,
                };
              }
            },
          }),
        },
        maxSteps: 10, // Enable multi-step tool calls
      })

      result.mergeIntoDataStream(dataStream)
    },
  })
}
