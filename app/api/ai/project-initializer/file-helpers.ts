import { db } from "@/lib/db"
import { ProjectType, TECHNOLOGY_STACKS } from "./helpers"

/**
 * Suggest technologies for a project based on type and requirements
 */
export function suggestTechnologiesForProject(projectType: ProjectType, requirements: string): Record<string, string[]> {
  const lowerReq = requirements.toLowerCase()
  const suggestions: Record<string, string[]> = {}
  
  // Get technology options based on project type
  // Not used directly, but helps with type checking
  
  // Web project suggestions
  if (projectType === "web") {
    // Frontend framework
    if (lowerReq.includes("react") || lowerReq.includes("next")) {
      suggestions.frontend = ["Next.js"]
    } else if (lowerReq.includes("vue")) {
      suggestions.frontend = ["Vue.js", "Nuxt.js"]
    } else if (lowerReq.includes("angular")) {
      suggestions.frontend = ["Angular"]
    } else if (lowerReq.includes("svelte")) {
      suggestions.frontend = ["Svelte", "SvelteKit"]
    } else {
      suggestions.frontend = ["Next.js"] // Default to Next.js
    }
    
    // Styling
    if (lowerReq.includes("tailwind")) {
      suggestions.styling = ["Tailwind CSS"]
    } else if (lowerReq.includes("material")) {
      suggestions.styling = ["Material UI"]
    } else if (lowerReq.includes("chakra")) {
      suggestions.styling = ["Chakra UI"]
    } else {
      suggestions.styling = ["Tailwind CSS"] // Default to Tailwind
    }
    
    // State management
    if (lowerReq.includes("redux")) {
      suggestions.state = ["Redux Toolkit"]
    } else if (lowerReq.includes("zustand")) {
      suggestions.state = ["Zustand"]
    } else if (lowerReq.includes("mobx")) {
      suggestions.state = ["MobX"]
    } else {
      suggestions.state = ["Zustand"] // Default to Zustand
    }
  }
  
  // Mobile project suggestions
  else if (projectType === "mobile") {
    // Framework
    if (lowerReq.includes("react native") || lowerReq.includes("react-native")) {
      suggestions.framework = ["React Native", "Expo"]
    } else if (lowerReq.includes("flutter")) {
      suggestions.framework = ["Flutter"]
    } else if (lowerReq.includes("native")) {
      suggestions.framework = ["Native Android/iOS"]
    } else {
      suggestions.framework = ["React Native", "Expo"] // Default to React Native
    }
    
    // State management
    if (lowerReq.includes("redux")) {
      suggestions.state = ["Redux Toolkit"]
    } else {
      suggestions.state = ["Context API", "Zustand"]
    }
  }
  
  // Backend project suggestions
  else if (projectType === "backend" || projectType === "api") {
    // Runtime
    if (lowerReq.includes("node") || lowerReq.includes("javascript") || lowerReq.includes("typescript")) {
      suggestions.runtime = ["Node.js"]
    } else if (lowerReq.includes("python")) {
      suggestions.runtime = ["Python"]
    } else if (lowerReq.includes("java")) {
      suggestions.runtime = ["Java"]
    } else if (lowerReq.includes("go")) {
      suggestions.runtime = ["Go"]
    } else {
      suggestions.runtime = ["Node.js"] // Default to Node.js
    }
    
    // Framework
    if (suggestions.runtime[0] === "Node.js") {
      if (lowerReq.includes("express")) {
        suggestions.framework = ["Express"]
      } else if (lowerReq.includes("nest")) {
        suggestions.framework = ["NestJS"]
      } else {
        suggestions.framework = ["NestJS"] // Default to NestJS for Node
      }
    } else if (suggestions.runtime[0] === "Python") {
      if (lowerReq.includes("django")) {
        suggestions.framework = ["Django", "Django REST Framework"]
      } else if (lowerReq.includes("flask")) {
        suggestions.framework = ["Flask"]
      } else if (lowerReq.includes("fastapi")) {
        suggestions.framework = ["FastAPI"]
      } else {
        suggestions.framework = ["FastAPI"] // Default to FastAPI for Python
      }
    }
    
    // Database
    if (lowerReq.includes("sql") || lowerReq.includes("relational")) {
      suggestions.database = ["PostgreSQL", "Prisma ORM"]
    } else if (lowerReq.includes("mongo") || lowerReq.includes("nosql")) {
      suggestions.database = ["MongoDB"]
    } else if (lowerReq.includes("redis") || lowerReq.includes("cache")) {
      suggestions.database = ["Redis", "PostgreSQL"]
    } else {
      suggestions.database = ["PostgreSQL", "Prisma ORM"] // Default to PostgreSQL
    }
  }
  
  // Fullstack project suggestions
  else if (projectType === "fullstack") {
    // Frontend
    if (lowerReq.includes("react") || lowerReq.includes("next")) {
      suggestions.frontend = ["Next.js"]
    } else if (lowerReq.includes("vue")) {
      suggestions.frontend = ["Nuxt.js"]
    } else {
      suggestions.frontend = ["Next.js"] // Default to Next.js
    }
    
    // Backend
    if (lowerReq.includes("python")) {
      suggestions.backend = ["Python", "FastAPI"]
    } else {
      suggestions.backend = ["Node.js", "NestJS"] // Default to Node.js
    }
    
    // Database
    if (lowerReq.includes("mongo") || lowerReq.includes("nosql")) {
      suggestions.database = ["MongoDB"]
    } else {
      suggestions.database = ["PostgreSQL", "Prisma ORM"] // Default to PostgreSQL
    }
    
    // Styling
    suggestions.styling = ["Tailwind CSS"]
  }
  
  return suggestions
}

/**
 * Generate file structure for a project
 */
export function generateFileStructureForProject(projectType: ProjectType, technologies: string[]): Array<{ path: string, content: string }> {
  const files: Array<{ path: string, content: string }> = []
  
  // Common files for all projects
  files.push({
    path: "README.md",
    content: `# Project\n\nThis project was generated with the App Generator.\n\n## Technologies\n\n${technologies.join(", ")}\n\n## Getting Started\n\n1. Clone the repository\n2. Install dependencies\n3. Run the development server\n`,
  })
  
  files.push({
    path: ".gitignore",
    content: `# Dependencies\nnode_modules\n.pnp\n.pnp.js\n\n# Testing\ncoverage\n\n# Production\nbuild\ndist\nout\n\n# Misc\n.DS_Store\n*.pem\n\n# Debug\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n\n# Local env files\n.env*.local\n.env\n\n# Vercel\n.vercel\n`,
  })
  
  // Project type specific files
  if (projectType === "web" || projectType === "fullstack") {
    // Check for Next.js
    if (technologies.includes("Next.js")) {
      files.push({
        path: "next.config.js",
        content: `/** @type {import('next').NextConfig} */\nconst nextConfig = {\n  reactStrictMode: true,\n}\n\nmodule.exports = nextConfig\n`,
      })
      
      files.push({
        path: "tsconfig.json",
        content: `{\n  "compilerOptions": {\n    "target": "es5",\n    "lib": ["dom", "dom.iterable", "esnext"],\n    "allowJs": true,\n    "skipLibCheck": true,\n    "strict": true,\n    "forceConsistentCasingInFileNames": true,\n    "noEmit": true,\n    "esModuleInterop": true,\n    "module": "esnext",\n    "moduleResolution": "node",\n    "resolveJsonModule": true,\n    "isolatedModules": true,\n    "jsx": "preserve",\n    "incremental": true,\n    "plugins": [\n      {\n        "name": "next"\n      }\n    ],\n    "paths": {\n      "@/*": ["./*"]\n    }\n  },\n  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],\n  "exclude": ["node_modules"]\n}\n`,
      })
      
      files.push({
        path: "app/page.tsx",
        content: `export default function Home() {\n  return (\n    <main className="flex min-h-screen flex-col items-center justify-between p-24">\n      <h1 className="text-4xl font-bold">Welcome to your new project!</h1>\n      <p>Get started by editing app/page.tsx</p>\n    </main>\n  )\n}\n`,
      })
      
      files.push({
        path: "app/layout.tsx",
        content: `import "./globals.css"\n\nexport const metadata = {\n  title: "My Project",\n  description: "Created with App Generator",\n}\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html lang="en">\n      <body>{children}</body>\n    </html>\n  )\n}\n`,
      })
      
      files.push({
        path: "app/globals.css",
        content: `@tailwind base;\n@tailwind components;\n@tailwind utilities;\n`,
      })
      
      // Add Tailwind config if using Tailwind
      if (technologies.includes("Tailwind CSS")) {
        files.push({
          path: "tailwind.config.js",
          content: `/** @type {import('tailwindcss').Config} */\nmodule.exports = {\n  content: [\n    "./pages/**/*.{js,ts,jsx,tsx,mdx}",\n    "./components/**/*.{js,ts,jsx,tsx,mdx}",\n    "./app/**/*.{js,ts,jsx,tsx,mdx}",\n  ],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n}\n`,
        })
        
        files.push({
          path: "postcss.config.js",
          content: `module.exports = {\n  plugins: {\n    tailwindcss: {},\n    autoprefixer: {},\n  },\n}\n`,
        })
      }
    }
  }
  
  // Backend specific files
  if (projectType === "backend" || projectType === "api" || projectType === "fullstack") {
    if (technologies.includes("Node.js") || technologies.includes("Express") || technologies.includes("NestJS")) {
      files.push({
        path: "package.json",
        content: `{\n  "name": "project",\n  "version": "0.1.0",\n  "private": true,\n  "scripts": {\n    "dev": "next dev",\n    "build": "next build",\n    "start": "next start",\n    "lint": "next lint"\n  },\n  "dependencies": {\n    ${technologies.includes("Next.js") ? `"next": "latest",\n    "react": "latest",\n    "react-dom": "latest",` : ""}\n    ${technologies.includes("Express") ? `"express": "^4.18.2",` : ""}\n    ${technologies.includes("NestJS") ? `"@nestjs/core": "^10.0.0",\n    "@nestjs/common": "^10.0.0",` : ""}\n    ${technologies.includes("Prisma ORM") ? `"prisma": "^5.0.0",\n    "@prisma/client": "^5.0.0",` : ""}\n    "typescript": "^5.0.0"\n  },\n  "devDependencies": {\n    ${technologies.includes("Next.js") ? `"@types/react": "latest",\n    "@types/react-dom": "latest",` : ""}\n    ${technologies.includes("Express") ? `"@types/express": "^4.17.17",` : ""}\n    ${technologies.includes("Tailwind CSS") ? `"tailwindcss": "^3.3.0",\n    "autoprefixer": "^10.4.14",\n    "postcss": "^8.4.27",` : ""}\n    "@types/node": "^20.0.0",\n    "typescript": "^5.0.0"\n  }\n}\n`,
      })
      
      // Add Prisma schema if using Prisma
      if (technologies.includes("Prisma ORM")) {
        files.push({
          path: "prisma/schema.prisma",
          content: `// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\ngenerator client {\n  provider = "prisma-client-js"\n}\n\ndatasource db {\n  provider = "postgresql"\n  url      = env("DATABASE_URL")\n}\n\n// Define your models here\n`,
        })
        
        files.push({
          path: ".env",
          content: `DATABASE_URL="postgresql://postgres:postgres@localhost:5432/mydb?schema=public"\n`,
        })
      }
    }
  }
  
  // Mobile specific files
  if (projectType === "mobile") {
    if (technologies.includes("React Native") || technologies.includes("Expo")) {
      files.push({
        path: "App.tsx",
        content: `import React from 'react';\nimport { StyleSheet, Text, View } from 'react-native';\n\nexport default function App() {\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Welcome to your new mobile app!</Text>\n      <Text>Get started by editing App.tsx</Text>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 16,\n  },\n});\n`,
      })
      
      files.push({
        path: "package.json",
        content: `{\n  "name": "mobile-app",\n  "version": "1.0.0",\n  "main": "node_modules/expo/AppEntry.js",\n  "scripts": {\n    "start": "expo start",\n    "android": "expo start --android",\n    "ios": "expo start --ios",\n    "web": "expo start --web"\n  },\n  "dependencies": {\n    "expo": "~48.0.18",\n    "expo-status-bar": "~1.4.4",\n    "react": "18.2.0",\n    "react-native": "0.71.8"\n  },\n  "devDependencies": {\n    "@babel/core": "^7.20.0",\n    "@types/react": "~18.0.14",\n    "typescript": "^4.9.4"\n  },\n  "private": true\n}\n`,
      })
      
      files.push({
        path: "tsconfig.json",
        content: `{\n  "extends": "expo/tsconfig.base",\n  "compilerOptions": {\n    "strict": true\n  }\n}\n`,
      })
    }
  }
  
  return files
}

/**
 * Generate configuration files for a project
 */
export function generateConfigFiles(technologies: string[]): Array<{ path: string, content: string }> {
  const files: Array<{ path: string, content: string }> = []
  
  // ESLint configuration
  files.push({
    path: ".eslintrc.json",
    content: `{\n  "extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended"],\n  "parser": "@typescript-eslint/parser",\n  "plugins": ["@typescript-eslint"],\n  "root": true\n}\n`,
  })
  
  // Add Docker configuration if needed
  files.push({
    path: "Dockerfile",
    content: `FROM node:18-alpine AS base\n\nFROM base AS deps\nWORKDIR /app\nCOPY package.json package-lock.json* pnpm-lock.yaml* ./\nRUN npm ci\n\nFROM base AS builder\nWORKDIR /app\nCOPY --from=deps /app/node_modules ./node_modules\nCOPY . .\nRUN npm run build\n\nFROM base AS runner\nWORKDIR /app\nENV NODE_ENV production\nCOPY --from=builder /app/public ./public\nCOPY --from=builder /app/.next/standalone ./\nCOPY --from=builder /app/.next/static ./.next/static\nEXPOSE 3000\nENV PORT 3000\nCMD ["node", "server.js"]\n`,
  })
  
  files.push({
    path: ".dockerignore",
    content: `node_modules\n.next\nout\nbuild\n.DS_Store\n*.pem\n.env*.local\n.env\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n`,
  })
  
  // Add GitHub Actions workflow
  files.push({
    path: ".github/workflows/ci.yml",
    content: `name: CI\n\non:\n  push:\n    branches: [ main ]\n  pull_request:\n    branches: [ main ]\n\njobs:\n  build:\n    runs-on: ubuntu-latest\n\n    steps:\n    - uses: actions/checkout@v3\n    - name: Use Node.js\n      uses: actions/setup-node@v3\n      with:\n        node-version: '18.x'\n        cache: 'npm'\n    - run: npm ci\n    - run: npm run build --if-present\n    - run: npm test --if-present\n`,
  })
  
  return files
}

/**
 * Create project files in the database
 */
export async function createProjectFiles(projectId: string, files: Array<{ path: string, content: string }>) {
  const createdFiles = await Promise.all(
    files.map(file => 
      db.projectFile.create({
        data: {
          projectId,
          path: file.path,
          content: file.content,
          type: determineFileType(file.path),
        },
      })
    )
  )
  
  return createdFiles
}

/**
 * Determine file type based on file extension
 */
function determineFileType(filePath: string): string {
  const extension = filePath.split('.').pop()?.toLowerCase() || ''
  
  switch (extension) {
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
      return 'code'
    case 'json':
    case 'yml':
    case 'yaml':
    case 'toml':
      return 'config'
    case 'md':
      return 'documentation'
    case 'css':
    case 'scss':
    case 'sass':
    case 'less':
      return 'style'
    case 'html':
      return 'markup'
    case 'svg':
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
      return 'asset'
    default:
      return 'other'
  }
}
