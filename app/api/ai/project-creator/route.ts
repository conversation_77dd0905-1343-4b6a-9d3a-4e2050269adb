import { openai } from "@ai-sdk/openai"
import { createDataStreamResponse, streamText, tool } from "ai"
import { z } from "zod"

// Allow streaming responses up to 60 seconds
export const maxDuration = 60

// Constants for human-in-the-loop approval
const APPROVAL = {
  YES: "Yes, confirmed.",
  NO: "No, denied.",
}

export async function POST(req: Request) {
  const { messages } = await req.json()

  // Check if the last message contains a tool result that needs processing
  const lastMessage = messages[messages.length - 1]
  let processedMessages = [...messages]

  if (lastMessage?.role === "assistant" && lastMessage.parts) {
    const processedParts = await Promise.all(
      lastMessage.parts.map(async (part: any) => {
        if (part.type !== "tool-invocation") return part

        const toolInvocation = part.toolInvocation
        if (toolInvocation.toolName !== "createProject" || toolInvocation.state !== "result") {
          return part
        }

        // Process the tool result based on user confirmation
        let result
        if (toolInvocation.result === APPROVAL.YES) {
          // Execute the actual project creation
          result = await executeProjectCreation(toolInvocation.args)
        } else if (toolInvocation.result === APPROVAL.NO) {
          result = "Project creation cancelled by user."
        } else {
          return part
        }

        // Return updated tool invocation with the actual result
        return {
          ...part,
          toolInvocation: {
            ...toolInvocation,
            result,
          },
        }
      })
    )

    // Update the last message with processed parts
    processedMessages = [
      ...processedMessages.slice(0, -1),
      { ...lastMessage, parts: processedParts },
    ]
  }

  return createDataStreamResponse({
    execute: async (dataStream) => {
      const result = streamText({
        model: openai("gpt-4o"),
        messages: processedMessages,
        system: `You are an expert AI assistant specialized in helping users create software projects.
        You can help users define project requirements, suggest technologies, and create project structures.
        When a user describes a project they want to build, help them refine their idea and suggest the best approach.
        If the user provides enough details about a project, offer to create it for them using the createProject tool.
        You can also analyze project requirements, suggest technologies, generate file structures, and provide architecture diagrams.
        Always be helpful, concise, and focus on guiding the user through the project creation process.`,
        tools: {
          createProject: tool({
            description: "Create a new software project based on user requirements",
            parameters: z.object({
              name: z.string().describe("The name of the project"),
              type: z.enum(["web", "mobile", "backend", "fullstack", "api"] as const).describe("The type of project"),
              description: z.string().describe("A detailed description of the project"),
              technologies: z
                .array(z.string())
                .optional()
                .describe("List of technologies to use in the project"),
              features: z
                .array(z.string())
                .optional()
                .describe("List of key features to implement in the project"),
            }),
            // No execute function - we want human-in-the-loop confirmation
          }),
          suggestTechnologies: tool({
            description: "Suggest appropriate technologies for a project",
            parameters: z.object({
              projectType: z.enum(["web", "mobile", "backend", "fullstack", "api"] as const).describe("The type of project"),
              requirements: z.string().describe("The project requirements"),
            }),
            execute: async ({ projectType, requirements }) => {
              // This would typically call a more sophisticated function
              // For demo purposes, we'll return some static suggestions
              const techStacks = {
                web: ["React", "Next.js", "Tailwind CSS", "TypeScript"],
                mobile: ["React Native", "Flutter", "Swift", "Kotlin"],
                backend: ["Node.js", "Express", "PostgreSQL", "Prisma"],
                fullstack: ["Next.js", "TypeScript", "Prisma", "PostgreSQL"],
                api: ["Node.js", "Express", "Swagger", "MongoDB"],
              }

              return {
                suggestedTechnologies: techStacks[projectType],
                reasoning: `Based on your ${projectType} project requirements: "${requirements}", these technologies would provide a solid foundation.`
              }
            },
          }),
          analyzeRequirements: tool({
            description: "Analyze project requirements and provide insights",
            parameters: z.object({
              requirements: z.string().describe("The project requirements to analyze"),
            }),
            execute: async ({ requirements }) => {
              // In a real implementation, this would use a more sophisticated analysis
              // For demo purposes, we'll simulate an analysis
              return {
                complexity: Math.floor(Math.random() * 5) + 1,
                estimatedTimeInDays: Math.floor(Math.random() * 30) + 5,
                keyComponents: [
                  "User Authentication",
                  "Data Storage",
                  "API Integration",
                  "User Interface",
                  "Business Logic",
                ].slice(0, Math.floor(Math.random() * 5) + 1),
                potentialChallenges: [
                  "Scalability concerns",
                  "Security implementation",
                  "Performance optimization",
                  "Cross-platform compatibility",
                  "Third-party API integration",
                ].slice(0, Math.floor(Math.random() * 3) + 1),
                recommendations: [
                  "Start with a minimum viable product (MVP)",
                  "Implement automated testing early",
                  "Use a component-based architecture",
                  "Consider serverless deployment",
                  "Implement continuous integration",
                ].slice(0, Math.floor(Math.random() * 3) + 1),
              }
            },
          }),
          generateFileStructure: tool({
            description: "Generate a file structure for a project",
            parameters: z.object({
              projectType: z.enum(["web", "mobile", "backend", "fullstack", "api"] as const).describe("The type of project"),
              technologies: z.array(z.string()).describe("List of technologies used in the project"),
            }),
            execute: async ({ projectType, technologies }) => {
              // Generate a file structure based on project type and technologies
              const fileStructures = {
                web: {
                  "src/": {
                    "components/": ["Header.tsx", "Footer.tsx", "Layout.tsx"],
                    "pages/": ["index.tsx", "about.tsx", "contact.tsx"],
                    "styles/": ["globals.css", "Home.module.css"],
                    "utils/": ["api.ts", "helpers.ts"],
                    "hooks/": ["useAuth.ts", "useFetch.ts"],
                    "context/": ["AuthContext.tsx"],
                  },
                  "public/": ["favicon.ico", "logo.svg"],
                  "package.json": {},
                  "tsconfig.json": {},
                  "README.md": {},
                },
                mobile: {
                  "src/": {
                    "components/": ["Header.tsx", "Footer.tsx", "Layout.tsx"],
                    "screens/": ["Home.tsx", "Profile.tsx", "Settings.tsx"],
                    "navigation/": ["AppNavigator.tsx", "AuthNavigator.tsx"],
                    "services/": ["api.ts", "storage.ts"],
                    "hooks/": ["useAuth.ts", "useFetch.ts"],
                    "context/": ["AuthContext.tsx"],
                    "assets/": {
                      "images/": ["logo.png"],
                      "fonts/": ["Roboto-Regular.ttf"],
                    },
                  },
                  "app.json": {},
                  "package.json": {},
                  "tsconfig.json": {},
                  "README.md": {},
                },
                backend: {
                  "src/": {
                    "controllers/": ["userController.ts", "authController.ts"],
                    "models/": ["userModel.ts", "postModel.ts"],
                    "routes/": ["userRoutes.ts", "authRoutes.ts"],
                    "middleware/": ["auth.ts", "error.ts", "logger.ts"],
                    "services/": ["userService.ts", "emailService.ts"],
                    "utils/": ["helpers.ts", "validators.ts"],
                    "config/": ["database.ts", "env.ts"],
                  },
                  "tests/": ["user.test.ts", "auth.test.ts"],
                  "package.json": {},
                  "tsconfig.json": {},
                  "README.md": {},
                },
                fullstack: {
                  "client/": {
                    "src/": {
                      "components/": ["Header.tsx", "Footer.tsx", "Layout.tsx"],
                      "pages/": ["index.tsx", "about.tsx", "contact.tsx"],
                      "styles/": ["globals.css", "Home.module.css"],
                      "utils/": ["api.ts", "helpers.ts"],
                      "hooks/": ["useAuth.ts", "useFetch.ts"],
                      "context/": ["AuthContext.tsx"],
                    },
                    "public/": ["favicon.ico", "logo.svg"],
                    "package.json": {},
                    "tsconfig.json": {},
                  },
                  "server/": {
                    "src/": {
                      "controllers/": ["userController.ts", "authController.ts"],
                      "models/": ["userModel.ts", "postModel.ts"],
                      "routes/": ["userRoutes.ts", "authRoutes.ts"],
                      "middleware/": ["auth.ts", "error.ts", "logger.ts"],
                      "services/": ["userService.ts", "emailService.ts"],
                      "utils/": ["helpers.ts", "validators.ts"],
                      "config/": ["database.ts", "env.ts"],
                    },
                    "tests/": ["user.test.ts", "auth.test.ts"],
                    "package.json": {},
                    "tsconfig.json": {},
                  },
                  "package.json": {},
                  "README.md": {},
                },
                api: {
                  "src/": {
                    "controllers/": ["userController.ts", "authController.ts"],
                    "models/": ["userModel.ts", "postModel.ts"],
                    "routes/": ["userRoutes.ts", "authRoutes.ts"],
                    "middleware/": ["auth.ts", "error.ts", "logger.ts"],
                    "services/": ["userService.ts", "emailService.ts"],
                    "utils/": ["helpers.ts", "validators.ts"],
                    "config/": ["database.ts", "env.ts"],
                  },
                  "tests/": ["user.test.ts", "auth.test.ts"],
                  "package.json": {},
                  "tsconfig.json": {},
                  "README.md": {},
                },
              }

              return {
                fileStructure: fileStructures[projectType],
                explanation: `This file structure is optimized for a ${projectType} project using ${technologies.join(", ")}.`,
              }
            },
          }),
          generateArchitectureDiagram: tool({
            description: "Generate an architecture diagram for a project",
            parameters: z.object({
              projectType: z.enum(["web", "mobile", "backend", "fullstack", "api"] as const).describe("The type of project"),
              technologies: z.array(z.string()).describe("List of technologies used in the project"),
            }),
            execute: async ({ projectType, technologies }) => {
              // In a real implementation, this would generate a diagram
              // For demo purposes, we'll return a text-based representation
              const architectureDiagrams = {
                web: `
Client (Browser)
    │
    ├── React/Next.js Frontend
    │   ├── Components
    │   ├── Pages/Routes
    │   ├── State Management
    │   └── API Client
    │
    └── External Services
        ├── Authentication
        └── APIs
                `,
                mobile: `
Mobile Device
    │
    ├── React Native/Flutter App
    │   ├── Screens
    │   ├── Components
    │   ├── Navigation
    │   └── State Management
    │
    └── External Services
        ├── Authentication
        ├── Push Notifications
        └── APIs
                `,
                backend: `
Client Requests
    │
    ├── API Gateway
    │   │
    │   ├── Routes
    │   │   ├── Auth Routes
    │   │   ├── User Routes
    │   │   └── Resource Routes
    │   │
    │   ├── Controllers
    │   │   ├── Auth Controller
    │   │   ├── User Controller
    │   │   └── Resource Controller
    │   │
    │   └── Middleware
    │       ├── Authentication
    │       ├── Validation
    │       └── Error Handling
    │
    ├── Services
    │   ├── Business Logic
    │   └── External API Integration
    │
    └── Data Layer
        ├── Models
        ├── Database (PostgreSQL/MongoDB)
        └── Caching (Redis)
                `,
                fullstack: `
Client (Browser/Mobile)
    │
    ├── Frontend (React/Next.js)
    │   ├── Components
    │   ├── Pages/Routes
    │   ├── State Management
    │   └── API Client
    │
    ├── Backend (Node.js/Express)
    │   ├── Routes
    │   ├── Controllers
    │   ├── Services
    │   └── Middleware
    │
    ├── Database (PostgreSQL/MongoDB)
    │
    └── External Services
        ├── Authentication
        ├── Storage
        └── Third-party APIs
                `,
                api: `
Client Requests
    │
    ├── API Gateway
    │   │
    │   ├── Routes
    │   │   ├── Auth Routes
    │   │   ├── User Routes
    │   │   └── Resource Routes
    │   │
    │   ├── Controllers
    │   │   ├── Auth Controller
    │   │   ├── User Controller
    │   │   └── Resource Controller
    │   │
    │   └── Middleware
    │       ├── Authentication
    │       ├── Validation
    │       └── Error Handling
    │
    ├── Services
    │   ├── Business Logic
    │   └── External API Integration
    │
    └── Data Layer
        ├── Models
        ├── Database (PostgreSQL/MongoDB)
        └── Caching (Redis)
                `,
              }

              return {
                diagram: architectureDiagrams[projectType],
                explanation: `This architecture diagram represents a ${projectType} project using ${technologies.join(", ")}.`,
              }
            },
          }),
          generateDatabaseSchema: tool({
            description: "Generate a database schema for a project",
            parameters: z.object({
              projectType: z.enum(["web", "mobile", "backend", "fullstack", "api"] as const).describe("The type of project"),
              entities: z.array(z.string()).describe("List of entities in the project"),
            }),
            execute: async ({ projectType, entities }) => {
              // In a real implementation, this would generate a schema based on the entities
              // For demo purposes, we'll return a simple schema
              const entitySchemas = entities.map(entity => {
                const lowercaseEntity = entity.toLowerCase();

                // Common fields for all entities
                const commonFields = {
                  id: "UUID (Primary Key)",
                  createdAt: "DateTime",
                  updatedAt: "DateTime",
                };

                // Entity-specific fields
                const specificFields: Record<string, any> = {};

                if (lowercaseEntity.includes("user")) {
                  specificFields.email = "String (Unique)";
                  specificFields.password = "String (Hashed)";
                  specificFields.name = "String";
                  specificFields.role = "Enum (USER, ADMIN)";
                } else if (lowercaseEntity.includes("product")) {
                  specificFields.name = "String";
                  specificFields.description = "String";
                  specificFields.price = "Decimal";
                  specificFields.inventory = "Integer";
                  specificFields.categoryId = "UUID (Foreign Key)";
                } else if (lowercaseEntity.includes("order")) {
                  specificFields.userId = "UUID (Foreign Key)";
                  specificFields.status = "Enum (PENDING, COMPLETED, CANCELLED)";
                  specificFields.total = "Decimal";
                } else if (lowercaseEntity.includes("post")) {
                  specificFields.title = "String";
                  specificFields.content = "Text";
                  specificFields.authorId = "UUID (Foreign Key)";
                  specificFields.published = "Boolean";
                } else if (lowercaseEntity.includes("comment")) {
                  specificFields.content = "Text";
                  specificFields.authorId = "UUID (Foreign Key)";
                  specificFields.postId = "UUID (Foreign Key)";
                } else {
                  specificFields.name = "String";
                  specificFields.description = "String";
                }

                return {
                  entity,
                  fields: { ...commonFields, ...specificFields },
                };
              });

              return {
                schemas: entitySchemas,
                explanation: `This database schema is designed for a ${projectType} project with the following entities: ${entities.join(", ")}.`,
              }
            },
          }),
        },
        maxSteps: 5, // Enable multi-step tool calls
      })

      result.mergeIntoDataStream(dataStream)
    },
  })
}

// Function to simulate project creation
async function executeProjectCreation(args: any) {
  // In a real application, this would create the actual project
  // For demo purposes, we'll just return a success message
  const featuresText = args.features && args.features.length > 0
    ? `\n  - Features: ${args.features.join(", ")}`
    : "";

  return `Project "${args.name}" created successfully with the following details:
  - Type: ${args.type}
  - Description: ${args.description}
  - Technologies: ${args.technologies ? args.technologies.join(", ") : "Not specified"}${featuresText}

  Your project has been initialized and is ready for development. You can now:
  1. View the project details in the Projects dashboard
  2. Add team members to collaborate
  3. Start implementing features
  4. Set up deployment pipelines`
}
