/**
 * API route for MicroVM Dev Bot
 * Specializes in VM operations and command execution using AI SDK tools
 */

import { NextRequest } from 'next/server';
import { Message } from 'ai';
import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { createNextJsProject, startNextJsDevServer } from '@/components/dev-bot/tools/nextjs-helper';

// Define the maximum duration for streaming responses (2 minutes)
export const maxDuration = 120;

// Define schemas for tool arguments
const runCommandArgsSchema = z.object({
  command: z.string().describe("The command to run in the VM"),
  vmId: z.string().optional().describe("The ID of the VM to run the command in")
});

const filesystemArgsSchema = z.object({
  operation: z.enum(["read", "write", "list", "create", "delete", "exists"])
    .describe("The operation to perform"),
  path: z.string().describe("The file path"),
  content: z.string().optional().describe("Content for write operations"),
  vmId: z.string().optional().describe("The ID of the VM")
});

const createNextJsProjectArgsSchema = z.object({
  projectName: z.string().describe("Name of the Next.js project"),
  template: z.string().optional().describe("Template to use (e.g., 'app', 'default', or an example name)"),
  typescript: z.boolean().optional().describe("Whether to use TypeScript (default: true)"),
  tailwind: z.boolean().optional().describe("Whether to use Tailwind CSS (default: true)"),
  eslint: z.boolean().optional().describe("Whether to use ESLint (default: true)"),
  appDir: z.boolean().optional().describe("Whether to use the App Router (default: true)"),
  vmId: z.string().optional().describe("The ID of the VM")
});

const startNextJsDevServerArgsSchema = z.object({
  projectName: z.string().describe("Name of the Next.js project"),
  port: z.number().optional().describe("Port to run the development server on (default: 3000)"),
  vmId: z.string().optional().describe("The ID of the VM")
});

// Define the tools for MicroVM operations
const microVmTools = {
  run_command: {
    description: "Execute a command in the MicroVM environment",
    parameters: runCommandArgsSchema,
    execute: async ({ command, vmId }: z.infer<typeof runCommandArgsSchema>) => {
      try {
        const response = await fetch("/api/microvm/command", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ vmId, command }),
        });
        
        if (!response.ok) {
          throw new Error(`Failed to execute command: ${response.status}`);
        }
        
        const result = await response.json();
        return {
          result: result.result,
          error: result.error
        };
      } catch (error) {
        console.error("Error executing command:", error);
        return { error: error instanceof Error ? error.message : "Unknown error" };
      }
    }
  },
  filesystem: {
    description: "Perform filesystem operations in the MicroVM",
    parameters: filesystemArgsSchema,
    execute: async ({ operation, path, content, vmId }: z.infer<typeof filesystemArgsSchema>) => {
      try {
        const response = await fetch("/api/microvm/filesystem", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ vmId, operation, path, content }),
        });
        
        if (!response.ok) {
          throw new Error(`Failed to perform filesystem operation: ${response.status}`);
        }
        
        return await response.json();
      } catch (error) {
        console.error("Error performing filesystem operation:", error);
        return { error: error instanceof Error ? error.message : "Unknown error" };
      }
    }
  },
  create_nextjs_project: {
    description: "Create a new Next.js project in the MicroVM",
    parameters: createNextJsProjectArgsSchema,
    execute: async (args: z.infer<typeof createNextJsProjectArgsSchema>) => {
      try {
        return await createNextJsProject(args);
      } catch (error) {
        console.error("Error creating Next.js project:", error);
        return { error: error instanceof Error ? error.message : "Unknown error" };
      }
    }
  },
  start_nextjs_dev_server: {
    description: "Start a Next.js development server for a project",
    parameters: startNextJsDevServerArgsSchema,
    execute: async (args: z.infer<typeof startNextJsDevServerArgsSchema>) => {
      try {
        return await startNextJsDevServer(args);
      } catch (error) {
        console.error("Error starting Next.js dev server:", error);
        return { error: error instanceof Error ? error.message : "Unknown error" };
      }
    }
  }
};

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();
    const { messages } = body;

    // Get vmId from URL query params
    const url = new URL(req.url);
    const vmId = url.searchParams.get('vmId');

    // Create the system prompt with VM context
    const systemPrompt = `You are an AI assistant running in a MicroVM environment${vmId ? ` with ID: ${vmId}` : ''}. 
    You can help with coding tasks, system administration, and other tasks within this isolated virtual machine environment.
    You have access to tools that can interact with the filesystem and run commands in the VM.
    When using tools, always include the vmId parameter with value "${vmId}" to ensure operations happen in the correct VM.

    ## Environment Info:
    - MicroVM ID: ${vmId || 'Not specified'}
    - The filesystem is isolated within the VM
    - You can create and run Next.js applications
    - You can execute terminal commands 
    - The default workspace directory is /app

    ## Next.js App Creation:
    You can create and run Next.js applications with the following tools:
    1. create_nextjs_project - Creates a new Next.js project with options for TypeScript, Tailwind, etc.
    2. start_nextjs_dev_server - Starts the development server for a Next.js project
    
    When creating a Next.js project, first check if any dependencies need to be installed.
    After starting the dev server, let the user know they can view the app in the preview panel.

    ## Best Practices:
    1. When editing files, first check if they exist
    2. When installing packages, check if they're already installed
    3. Use proper error handling in your commands
    4. Give clear explanations of what you're doing`;

    // Create a streaming response
    const result = streamText({
      model: openai('gpt-4o'),
      system: systemPrompt,
      messages: messages as Message[],
      tools: microVmTools,
      maxSteps: 15, // Allow up to 15 steps for multi-step tool calls
      toolChoice: 'auto', // Let the model decide when to use tools
      toolCallStreaming: true, // Stream tool calls as they happen
    });

    // Return the streaming response with enhanced error handling
    return result.toDataStreamResponse({
      getErrorMessage: (error) => {
        console.error('Error in MicroVM DevBot route:', error);

        // Provide more helpful error messages based on error type
        if (error instanceof Error) {
          if (error.message.includes('rate limit')) {
            return 'Rate limit exceeded. Please try again in a moment.';
          } else if (error.message.includes('timeout')) {
            return 'The request timed out. Please try a simpler query or try again later.';
          }
          return `Error: ${error.message}`;
        }

        return 'An unexpected error occurred. Please try again.';
      },
      sendUsage: true, // Send token usage information to the client
    });
  } catch (error: any) {
    console.error('Error in MicroVM DevBot route:', error);
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
} 