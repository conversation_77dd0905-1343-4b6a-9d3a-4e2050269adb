import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/auth/[...nextauth]/route"
import { createActivity, ActivityType } from "@/lib/activity"
import { db } from "@/lib/db"
import { z } from "zod"

// Validation schema for activity creation
const activitySchema = z.object({
  type: z.string(),
  description: z.string(),
  metadata: z.record(z.any()).optional(),
  projectId: z.string().optional(),
})

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await req.json()

    // Validate input
    const result = activitySchema.safeParse(body)

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          errors: result.error.flatten().fieldErrors
        },
        { status: 400 }
      )
    }

    const { type, description, metadata, projectId } = result.data

    // Create activity
    const activity = await createActivity({
      userId: session.user.id,
      type: type as ActivityType,
      description,
      metadata,
      projectId,
    })

    return NextResponse.json(
      {
        success: true,
        activity
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Error creating activity:", error)
    return NextResponse.json(
      {
        success: false,
        message: "An error occurred while creating the activity"
      },
      { status: 500 }
    )
  }
}

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams
    const projectId = searchParams.get("projectId")
    const limit = parseInt(searchParams.get("limit") || "10", 10)

    // Get activities
    let activities

    if (projectId) {
      // Get project activities
      activities = await db.activity.findMany({
        where: {
          projectId,
          userId: session.user.id, // Ensure user can only see their own activities
        },
        orderBy: { timestamp: "desc" },
        take: limit,
      })
    } else {
      // Get user activities
      activities = await db.activity.findMany({
        where: { userId: session.user.id },
        orderBy: { timestamp: "desc" },
        take: limit,
      })
    }

    return NextResponse.json(
      {
        success: true,
        activities
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Error fetching activities:", error)
    return NextResponse.json(
      {
        success: false,
        message: "An error occurred while fetching activities"
      },
      { status: 500 }
    )
  }
}
