/**
 * API route for agentic code generation
 *
 * This route also supports agent pattern tools for demonstrating different agent patterns.
 */

import { streamText } from 'ai';
import { z } from 'zod';
import { createEnhancedModel } from '@/lib/ai-middleware';
import { openai } from '@ai-sdk/openai';
import { agentPatternTools } from '@/lib/tools/agent-patterns';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';

// Define the maximum duration for streaming responses
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages } = await req.json();

  const result = streamText({
    model: createEnhancedModel('gpt-4o'),
    system: `## Core Identity
    You are an expert full-stack developer assistant that helps users generate code for their applications through an agentic workflow.

    ## Expertise and Capabilities
    - You specialize in React, Next.js, TypeScript, and modern web development practices
    - You have deep knowledge of UI/UX design patterns and accessibility standards
    - You can generate complete, production-ready code implementations
    - You can create and modify files in a project structure
    - You can provide explanations and documentation for the code you generate

    ## Code Generation Guidelines
    1. Use modern React patterns with functional components and hooks
    2. Follow TypeScript best practices with proper type definitions
    3. Organize code with clear separation of concerns
    4. Implement responsive designs that work on all devices
    5. Use semantic HTML and follow accessibility guidelines
    6. Provide clear comments for complex logic
    7. When generating files, ensure they are properly structured with all necessary imports
    8. Prefer named exports over default exports for better refactoring support
    9. Use consistent naming conventions (camelCase for variables, PascalCase for components)
    10. Implement proper error handling and loading states

    ## File Structure and Organization
    - Use a modular approach with components, hooks, and utilities in separate files
    - Follow the Next.js App Router conventions for routing and data fetching
    - Organize components by feature or domain when appropriate
    - Keep related code close together
    - Use index files to simplify imports

    ## Styling Approach
    - Use Tailwind CSS for styling unless otherwise specified
    - Implement responsive designs that work on all screen sizes
    - Follow a consistent color scheme and design system
    - Use CSS variables for theming when appropriate

    ## Tool Usage Guidelines

    ### generateFile Tool
    - Use this tool to create new files with complete implementations
    - Always include proper imports, exports, and type definitions
    - Structure the file according to best practices for its type
    - Include helpful comments for complex logic
    - Example usage:
      - Creating React components with proper props and types
      - Implementing utility functions with documentation
      - Setting up configuration files with appropriate options

    ### updateFile Tool
    - Use this tool to modify existing files
    - Preserve the existing structure and style of the file
    - Make minimal changes necessary to implement the requested feature
    - Ensure backward compatibility when possible
    - Example usage:
      - Adding new methods to existing classes
      - Updating component props or state
      - Fixing bugs in existing code

    ### listFiles Tool
    - Use this tool to explore the project structure
    - Understand the organization before making changes
    - Identify related files that might need updates
    - Example usage:
      - Finding all components in a specific directory
      - Understanding the project's organization
      - Locating configuration files

    ### searchCode Tool
    - Use this tool to find relevant code in the project
    - Look for patterns, implementations, or usage examples
    - Understand the existing codebase before making changes
    - Example usage:
      - Finding how similar components are implemented
      - Locating usages of specific functions or components
      - Understanding the project's coding style

    ### previewCode Tool
    - Use this tool to generate visual previews of components
    - Help users understand how the code will look when rendered
    - Provide context for design decisions
    - Example usage:
      - Showing how a component will appear with different props
      - Demonstrating responsive behavior
      - Visualizing layout and styling

    ## Agent Capabilities

    As an agentic code generator, you have the ability to:

    1. **Break down complex tasks** into smaller, manageable steps
    2. **Use tools iteratively** to solve problems across multiple steps
    3. **Maintain context** throughout a multi-step interaction
    4. **Adapt your approach** based on intermediate results
    5. **Provide structured answers** with explanations of your process

    ### Multi-Step Problem Solving

    When faced with a complex code generation task:

    1. First, understand the requirements thoroughly
    2. Plan your approach by breaking down the task into logical steps
    3. Use the appropriate tools in each step to gather information or generate code
    4. Evaluate intermediate results and adjust your approach as needed
    5. Provide a comprehensive solution with explanations of your process

    ### Agent Patterns

    You can use different patterns depending on the task:

    - **Sequential Processing**: Execute steps in a predefined order for well-defined tasks
    - **Routing**: Make decisions about which path to take based on context
    - **Evaluation-Optimization**: Assess intermediate results and improve them iteratively

    ### Structured Output

    When providing your final solution:

    1. Organize your response with clear sections
    2. Explain your reasoning and approach
    3. Provide the generated code files with proper context
    4. Include any necessary setup or usage instructions
    5. Suggest next steps or improvements

    Remember that you are helping users build real applications, so focus on generating code that is not only functional but also maintainable, scalable, and follows industry best practices.`,
    messages,
    temperature: 0.7,
    toolCallStreaming: true,
    tools: {
      generateFile: {
        description: 'Generate a new file with the specified content',
        parameters: z.object({
          filePath: z.string().describe('The path of the file to generate'),
          content: z.string().describe('The content of the file'),
          description: z.string().optional().describe('A brief description of what the file does'),
        }),
        execute: async ({ filePath, content, description }) => {
          try {
            // Ensure the directory exists
            const directory = path.dirname(filePath);
            await fs.mkdir(directory, { recursive: true });

            // Write the file
            await fs.writeFile(filePath, content, 'utf8');

            console.log(`File generated: ${filePath}`);

            // Get file stats
            const stats = await fs.stat(filePath);

            return {
              success: true,
              filePath,
              message: `File ${filePath} has been generated.`,
              description: description || 'File generated successfully',
              size: stats.size,
              created: stats.birthtime,
            };
          } catch (error) {
            console.error(`Error generating file ${filePath}:`, error);
            return {
              success: false,
              filePath,
              message: `Error generating file: ${(error as Error).message}`,
            };
          }
        },
      },
      updateFile: {
        description: 'Update an existing file with new content',
        parameters: z.object({
          filePath: z.string().describe('The path of the file to update'),
          content: z.string().describe('The new content of the file'),
          description: z.string().optional().describe('A brief description of the changes made'),
        }),
        execute: async ({ filePath, content, description }) => {
          try {
            // Check if file exists
            try {
              await fs.access(filePath);
            } catch {
              return {
                success: false,
                filePath,
                message: `File ${filePath} does not exist.`,
              };
            }

            // Read the original content for comparison
            const originalContent = await fs.readFile(filePath, 'utf8');

            // Write the updated content
            await fs.writeFile(filePath, content, 'utf8');

            console.log(`File updated: ${filePath}`);

            // Calculate diff stats
            const additions = content.split('\n').length - originalContent.split('\n').length;
            const changePercentage = Math.round(
              (Math.abs(content.length - originalContent.length) / originalContent.length) * 100
            );

            return {
              success: true,
              filePath,
              message: `File ${filePath} has been updated.`,
              description: description || 'File updated successfully',
              changeStats: {
                lineChanges: additions > 0 ? `+${additions}` : additions,
                changePercentage: `${changePercentage}%`,
              },
            };
          } catch (error) {
            console.error(`Error updating file ${filePath}:`, error);
            return {
              success: false,
              filePath,
              message: `Error updating file: ${(error as Error).message}`,
            };
          }
        },
      },
      listFiles: {
        description: 'List files in a directory',
        parameters: z.object({
          directory: z.string().describe('The directory to list files from'),
        }),
        execute: async ({ directory }) => {
          try {
            // Ensure the directory exists
            try {
              await fs.access(directory);
            } catch {
              // If directory doesn't exist, create it
              await fs.mkdir(directory, { recursive: true });
            }

            // Read directory contents
            const entries = await fs.readdir(directory, { withFileTypes: true });

            // Map entries to file objects
            const files = await Promise.all(
              entries.map(async (entry) => {
                const entryPath = path.join(directory, entry.name);
                const stats = await fs.stat(entryPath);

                return {
                  name: entry.name,
                  path: entryPath,
                  type: entry.isDirectory() ? 'directory' : 'file',
                  size: entry.isFile() ? stats.size : null,
                  modified: stats.mtime,
                };
              })
            );

            console.log(`Listed ${files.length} files in ${directory}`);

            return {
              directory,
              files,
              count: files.length,
            };
          } catch (error) {
            console.error(`Error listing files in ${directory}:`, error);
            return {
              success: false,
              directory,
              message: `Error listing files: ${(error as Error).message}`,
              files: [],
            };
          }
        },
      },
      searchCode: {
        description: 'Search for code in the codebase',
        parameters: z.object({
          query: z.string().describe('The search query'),
          fileType: z.string().optional().describe('Filter by file type (e.g., tsx, ts, js)'),
          directory: z.string().optional().describe('Directory to search in (defaults to current directory)'),
        }),
        execute: async ({ query, fileType, directory = '.' }) => {
          try {
            console.log(`Searching for "${query}"${fileType ? ` in ${fileType} files` : ''} in ${directory}`);

            // Build the grep command
            let grepCommand = `grep -n -r "${query}" ${directory}`;

            // Add file type filter if specified
            if (fileType) {
              grepCommand += ` --include="*.${fileType}"`;
            }

            // Execute the grep command
            const results = await new Promise<any[]>((resolve, reject) => {
              exec(grepCommand, { maxBuffer: 1024 * 1024 }, (error, stdout, stderr) => {
                if (error && error.code !== 1) {
                  // grep returns code 1 when no matches are found, which isn't an error for us
                  return reject(error);
                }

                if (stderr) {
                  console.error('Grep stderr:', stderr);
                }

                // Parse the results
                const lines = stdout.split('\n').filter(Boolean);
                const searchResults = lines.map(line => {
                  // Format: filePath:lineNumber:matchedText
                  const [filePath, lineNumberStr, ...rest] = line.split(':');
                  const matchedText = rest.join(':');

                  return {
                    filePath,
                    lineNumber: parseInt(lineNumberStr, 10),
                    snippet: matchedText.trim(),
                  };
                });

                resolve(searchResults);
              });
            });

            console.log(`Found ${results.length} matches for "${query}"`);

            return {
              query,
              fileType,
              directory,
              count: results.length,
              results: results.slice(0, 10), // Limit to 10 results to avoid overwhelming the response
            };
          } catch (error) {
            console.error(`Error searching for "${query}":`, error);
            return {
              success: false,
              query,
              message: `Error searching code: ${(error as Error).message}`,
              results: [],
            };
          }
        },
      },
      previewCode: {
        description: 'Generate a preview of what the code will look like when rendered',
        parameters: z.object({
          code: z.string().describe('The code to preview'),
          type: z.enum(['component', 'page', 'layout']).describe('The type of code to preview'),
        }),
        // This tool doesn't have an execute function because it will be handled client-side
      },

      // Code optimization tool for improving code based on feedback
      optimizeCode: {
        description: 'Optimize code based on evaluation feedback',
        parameters: z.object({
          filePath: z.string().describe('The path of the file to optimize'),
          originalCode: z.string().describe('The original code to optimize'),
          feedback: z.array(z.string()).describe('Feedback points to address in the optimization'),
          focusAreas: z.array(z.string()).optional().describe('Specific areas to focus on (e.g., "performance", "readability")'),
        }),
        execute: async ({ filePath, originalCode, feedback, focusAreas }) => {
          // Log the optimization request
          console.log(`Optimizing ${filePath} (${originalCode.length} chars) based on ${feedback.length} feedback points`);
          if (focusAreas) {
            console.log(`Focus areas: ${focusAreas.join(', ')}`);
          }

          // In a real implementation, you would run actual code optimization
          // For now, we'll just return a success message
          return {
            filePath,
            success: true,
            message: `Code optimized successfully addressing ${feedback.length} feedback points`,
            optimizationSummary: `Improved ${focusAreas ? focusAreas.join(', ') : 'general code quality'}`,
          };
        },
      },

      // Evaluation tool for code quality assessment
      evaluateCode: {
        description: 'Evaluate the quality of generated code',
        parameters: z.object({
          filePath: z.string().describe('The path of the file to evaluate'),
          code: z.string().describe('The code to evaluate'),
          criteria: z.array(z.string()).describe('Specific criteria to evaluate (e.g., "performance", "readability", "security")'),
        }),
        execute: async ({ filePath, code, criteria }) => {
          try {
            console.log(`Evaluating ${filePath} (${code.length} chars) for ${criteria.join(', ')}`);

            // Determine file extension
            const extension = path.extname(filePath).toLowerCase();

            // Create a temporary file for analysis if it doesn't exist
            let tempFilePath = filePath;
            let fileExists = false;

            try {
              await fs.access(filePath);
              fileExists = true;
            } catch {
              // File doesn't exist, create a temporary file
              const tempDir = path.join(process.cwd(), 'temp');
              await fs.mkdir(tempDir, { recursive: true });
              tempFilePath = path.join(tempDir, path.basename(filePath));
              await fs.writeFile(tempFilePath, code, 'utf8');
            }

            // Initialize results object
            const results = {
              filePath,
              overallScore: 0,
              criteriaScores: [] as Array<{name: string; score: number; feedback: string}>,
              suggestions: [] as string[],
              errors: [] as string[],
            };

            // Perform different checks based on file type and criteria
            for (const criterion of criteria) {
              let score = 0;
              let feedback = '';

              switch (criterion.toLowerCase()) {
                case 'syntax':
                  // Check syntax based on file type
                  if (['.js', '.jsx', '.ts', '.tsx'].includes(extension)) {
                    try {
                      // Use Node to check syntax
                      await new Promise<void>((resolve) => {
                        exec(`node --check ${tempFilePath}`, (error) => {
                          if (error) {
                            score = 3;
                            feedback = `Syntax errors found: ${error.message}`;
                            results.errors.push(error.message);
                          } else {
                            score = 10;
                            feedback = 'No syntax errors found';
                          }
                          resolve();
                        });
                      });
                    } catch (error) {
                      score = 3;
                      feedback = `Error checking syntax: ${(error as Error).message}`;
                    }
                  } else {
                    score = 7;
                    feedback = 'Syntax check not available for this file type';
                  }
                  break;

                case 'complexity':
                  // Count lines, nested blocks, and function length
                  const lines = code.split('\n');
                  const lineCount = lines.length;
                  const functionCount = lines.filter((line: string) =>
                    line.includes('function') || line.includes('=>')
                  ).length;

                  if (lineCount > 300) {
                    score = 4;
                    feedback = 'File is too long, consider breaking it into smaller modules';
                    results.suggestions.push('Break this file into smaller modules');
                  } else if (lineCount > 150) {
                    score = 7;
                    feedback = 'File length is acceptable but could be more concise';
                    results.suggestions.push('Consider refactoring to reduce file length');
                  } else {
                    score = 9;
                    feedback = 'File has good length and complexity';
                  }
                  break;

                case 'readability':
                  // Check for comments, consistent spacing, and naming
                  const commentLines = code.split('\n').filter((line: string) =>
                    line.trim().startsWith('//') || line.includes('/*') || line.includes('*/')
                  ).length;
                  const commentRatio = commentLines / code.split('\n').length;

                  if (commentRatio < 0.05) {
                    score = 5;
                    feedback = 'Code lacks sufficient comments';
                    results.suggestions.push('Add more comments to explain complex logic');
                  } else if (commentRatio > 0.3) {
                    score = 7;
                    feedback = 'Good comment ratio, but ensure comments add value rather than stating the obvious';
                  } else {
                    score = 9;
                    feedback = 'Good balance of code and comments';
                  }
                  break;

                default:
                  score = 7;
                  feedback = `${criterion} evaluation is not specifically implemented, but the code appears satisfactory`;
              }

              results.criteriaScores.push({
                name: criterion,
                score,
                feedback,
              });
            }

            // Calculate overall score as average of criteria scores
            results.overallScore = results.criteriaScores.reduce(
              (sum, item) => sum + item.score,
              0
            ) / results.criteriaScores.length;

            // Add general suggestions based on file type
            if (['.js', '.jsx', '.ts', '.tsx'].includes(extension)) {
              if (!code.includes('try') && !code.includes('catch')) {
                results.suggestions.push('Consider adding error handling with try/catch blocks');
              }

              if (extension.includes('ts') && !code.includes(': ')) {
                results.suggestions.push('Add more TypeScript type annotations for better type safety');
              }
            }

            // Clean up temporary file if we created one
            if (!fileExists) {
              try {
                await fs.unlink(tempFilePath);
              } catch (error) {
                console.error(`Error removing temporary file: ${error}`);
              }
            }

            return results;
          } catch (error) {
            console.error(`Error evaluating code:`, error);
            return {
              filePath,
              success: false,
              message: `Error evaluating code: ${(error as Error).message}`,
              overallScore: 0,
              criteriaScores: [],
              suggestions: [],
              errors: [(error as Error).message],
            };
          }
        },
      },

      // Structured answer tool for providing final solutions
      provideSolution: {
        description: 'Provide a structured solution to the code generation task',
        parameters: z.object({
          summary: z.string().describe('A brief summary of the solution'),
          approach: z.string().describe('The approach taken to solve the problem'),
          files: z.array(
            z.object({
              filePath: z.string().describe('The path of the file'),
              purpose: z.string().describe('The purpose of this file in the solution'),
              dependencies: z.array(z.string()).describe('Other files or libraries this file depends on'),
            })
          ).describe('The files generated as part of the solution'),
          setupInstructions: z.string().optional().describe('Instructions for setting up and running the code'),
          nextSteps: z.array(z.string()).optional().describe('Suggested next steps or improvements'),
        }),
        // No execute function - invoking it will provide the structured solution
      },
    },
    maxSteps: 10, // Allow up to 10 steps for multi-step tool calls
    onStepFinish: ({ toolCalls, finishReason }) => {
      // Log each completed step for debugging
      console.log(`Step completed: ${finishReason}`);
      console.log(`Tool calls: ${toolCalls?.length || 0}`);

      // You could implement additional logic here:
      // - Save intermediate results to a database
      // - Track token usage for billing
      // - Implement custom logging or monitoring
      // - Apply additional validation or security checks
    }
  });

  return result.toDataStreamResponse({
    // Provide custom error handling
    getErrorMessage: (error) => {
      if (error == null) {
        return 'An unknown error occurred';
      }

      if (typeof error === 'string') {
        return error;
      }

      if (error instanceof Error) {
        return `Error: ${error.message}`;
      }

      return JSON.stringify(error);
    },
  });
}
