import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ProxmoxClient } from '@/lib/proxmox/client';
import { db } from '@/lib/db';

// Initialize Proxmox client
const proxmoxClient = new ProxmoxClient({
  host: process.env.PROXMOX_HOST || 'localhost',
  port: parseInt(process.env.PROXMOX_PORT || '8006'),
  username: process.env.PROXMOX_USERNAME || 'root',
  password: process.env.PROXMOX_PASSWORD || '',
  realm: process.env.PROXMOX_REALM || 'pam',
});

// POST /api/vms/[id]/start - Start VM
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const vmId = params.id;

    // Get VM from database
    const vm = await db.virtualMachine.findFirst({
      where: { vmId },
    });

    if (!vm) {
      return NextResponse.json({ error: 'VM not found' }, { status: 404 });
    }

    // Check if user is owner or admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { id: true, role: true },
    });

    if (user?.id !== vm.ownerId && user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Start VM in Proxmox
    await proxmoxClient.startVM(vmId);

    // Create activity log entry
    await db.vmActivity.create({
      data: {
        vmId,
        action: 'start',
        userId: user?.id as string,
        details: `VM started by ${session.user?.email}`,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error(`Error starting VM ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to start VM' },
      { status: 500 }
    );
  }
}
