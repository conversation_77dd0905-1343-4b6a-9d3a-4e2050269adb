import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ProxmoxClient } from '@/lib/proxmox/client';
import { db } from '@/lib/db';

// Initialize Proxmox client
const proxmoxClient = new ProxmoxClient({
  host: process.env.PROXMOX_HOST || 'localhost',
  port: parseInt(process.env.PROXMOX_PORT || '8006'),
  username: process.env.PROXMOX_USERNAME || 'root',
  password: process.env.PROXMOX_PASSWORD || '',
  realm: process.env.PROXMOX_REALM || 'pam',
});

// GET /api/vms/[id] - Get VM by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const vmId = params.id;

    // Get VM from database
    const vm = await db.virtualMachine.findFirst({
      where: { vmId },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        template: true,
      },
    });

    if (!vm) {
      return NextResponse.json({ error: 'VM not found' }, { status: 404 });
    }

    // Check if user is owner or admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { id: true, role: true },
    });

    if (user?.id !== vm.ownerId && user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get VM from Proxmox
    const proxmoxVM = await proxmoxClient.getVM(vmId);

    // Calculate bandwidth usage (mock data for now)
    const bandwidthTotal = vm.template?.specs.bandwidth === '1 TB' 
      ? 1 * 1024 * 1024 * 1024 * 1024 
      : vm.template?.specs.bandwidth === '2 TB'
      ? 2 * 1024 * 1024 * 1024 * 1024
      : vm.template?.specs.bandwidth === '3 TB'
      ? 3 * 1024 * 1024 * 1024 * 1024
      : vm.template?.specs.bandwidth === '4 TB'
      ? 4 * 1024 * 1024 * 1024 * 1024
      : vm.template?.specs.bandwidth === '5 TB'
      ? 5 * 1024 * 1024 * 1024 * 1024
      : 1 * 1024 * 1024 * 1024 * 1024;
    
    const bandwidthUsed = Math.random() * 0.7 * bandwidthTotal;

    // Combine database data with Proxmox data
    const combinedVM = {
      id: vm.vmId,
      name: vm.name,
      description: vm.description,
      status: proxmoxVM?.status,
      template: vm.template?.name || '',
      region: vm.region,
      ipAddress: proxmoxVM?.config?.networks?.[0]?.ipv4 || '',
      cpu: { 
        cores: proxmoxVM?.config?.cpu?.cores || vm.template?.specs.cpu || 1, 
        usage: proxmoxVM?.cpuUsage || 0 
      },
      memory: { 
        total: (proxmoxVM?.config?.memory || vm.template?.specs.memory || 1) * 1024 * 1024 * 1024, 
        used: proxmoxVM?.memoryUsage || 0 
      },
      storage: { 
        total: (proxmoxVM?.config?.disks?.[0]?.size || vm.template?.specs.storage || 10) * 1024 * 1024 * 1024, 
        used: proxmoxVM?.diskUsage || 0 
      },
      bandwidth: {
        total: bandwidthTotal,
        used: bandwidthUsed
      },
      os: vm.os,
      createdAt: vm.createdAt,
      price: vm.price,
      backups: vm.backups,
      containers: 0, // This will be updated by the containers endpoint
      owner: vm.owner?.email || '',
      node: proxmoxVM?.node || '',
      host: proxmoxVM?.node || '',
    };

    return NextResponse.json(combinedVM);
  } catch (error: any) {
    console.error(`Error fetching VM ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch VM' },
      { status: 500 }
    );
  }
}

// DELETE /api/vms/[id] - Delete VM
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const vmId = params.id;

    // Get VM from database
    const vm = await db.virtualMachine.findFirst({
      where: { vmId },
      include: {
        owner: {
          select: {
            id: true,
            email: true,
          },
        },
      },
    });

    if (!vm) {
      return NextResponse.json({ error: 'VM not found' }, { status: 404 });
    }

    // Check if user is owner or admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { id: true, role: true },
    });

    if (user?.id !== vm.ownerId && user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Delete VM from Proxmox
    await proxmoxClient.deleteVM(vmId);

    // Delete VM from database
    await db.virtualMachine.delete({
      where: { id: vm.id },
    });

    // Create activity log entry
    await db.vmActivity.create({
      data: {
        vmId,
        action: 'delete',
        userId: user?.id as string,
        details: `VM deleted by ${session.user?.email}`,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error(`Error deleting VM ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete VM' },
      { status: 500 }
    );
  }
}
