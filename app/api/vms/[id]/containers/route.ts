import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ProxmoxClient } from '@/lib/proxmox/client';
import { LxdClient } from '@/lib/containerization/lxd/api/lxd-client';
import { db } from '@/lib/db';

// Initialize Proxmox client
const proxmoxClient = new ProxmoxClient({
  host: process.env.PROXMOX_HOST || 'localhost',
  port: parseInt(process.env.PROXMOX_PORT || '8006'),
  username: process.env.PROXMOX_USERNAME || 'root',
  password: process.env.PROXMOX_PASSWORD || '',
  realm: process.env.PROXMOX_REALM || 'pam',
});

// GET /api/vms/[id]/containers - Get VM containers
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const vmId = params.id;

    // Get VM from database
    const vm = await db.virtualMachine.findFirst({
      where: { vmId },
    });

    if (!vm) {
      return NextResponse.json({ error: 'VM not found' }, { status: 404 });
    }

    // Check if user is owner or admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { id: true, role: true },
    });

    if (user?.id !== vm.ownerId && user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get VM from Proxmox
    const proxmoxVM = await proxmoxClient.getVM(vmId);
    
    // Get VM IP address
    const vmIp = proxmoxVM?.config?.networks?.[0]?.ipv4;
    
    if (!vmIp) {
      return NextResponse.json({ error: 'VM has no IP address' }, { status: 400 });
    }
    
    // In a real implementation, you would establish an SSH connection to the VM
    // and then connect to the LXD API on that VM
    // For now, we'll use a custom LXD client for the VM
    
    const lxdClient = new LxdClient({
      apiUrl: `https://${vmIp}:8443`,
      socketPath: '/var/lib/lxd/unix.socket',
      verifySSL: false,
    });
    
    try {
      // Get containers from LXD
      const containers = await lxdClient.listContainers();
      
      // Get container details
      const containerDetails = await Promise.all(
        containers.map(async (container) => {
          try {
            const state = await lxdClient.getContainerState(container.name);
            const info = await lxdClient.getContainer(container.name);
            
            // Calculate resource usage
            const cpuUsage = state.cpu.usage ? state.cpu.usage / 100 : 0;
            const memoryTotal = info.config['limits.memory'] 
              ? parseInt(info.config['limits.memory'].replace(/[^0-9]/g, '')) * 1024 * 1024 
              : 1024 * 1024 * 1024;
            const memoryUsed = state.memory.usage || 0;
            const diskTotal = info.config['limits.disk'] 
              ? parseInt(info.config['limits.disk'].replace(/[^0-9]/g, '')) * 1024 * 1024 
              : 20 * 1024 * 1024 * 1024;
            const diskUsed = state.disk?.usage?.root?.usage || 0;
            
            return {
              id: container.name,
              name: container.name,
              status: state.status,
              host: vmId,
              ipAddress: Object.values(state.network?.eth0?.addresses || {})[0]?.address || '',
              cpu: { 
                usage: cpuUsage, 
                cores: info.config['limits.cpu'] ? parseInt(info.config['limits.cpu']) : 1 
              },
              memory: { 
                used: memoryUsed, 
                total: memoryTotal 
              },
              disk: { 
                used: diskUsed, 
                total: diskTotal 
              },
              uptime: state.processes ? state.processes : 0,
            };
          } catch (error) {
            console.error(`Error fetching details for container ${container.name}:`, error);
            return {
              id: container.name,
              name: container.name,
              status: 'unknown',
              host: vmId,
              ipAddress: '',
              cpu: { usage: 0, cores: 1 },
              memory: { used: 0, total: 1024 * 1024 * 1024 },
              disk: { used: 0, total: 20 * 1024 * 1024 * 1024 },
              uptime: 0,
            };
          }
        })
      );
      
      return NextResponse.json(containerDetails);
    } catch (error: any) {
      console.error(`Error connecting to LXD on VM ${vmId}:`, error);
      
      // If we can't connect to LXD, return an empty array
      return NextResponse.json([]);
    }
  } catch (error: any) {
    console.error(`Error fetching containers for VM ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch containers' },
      { status: 500 }
    );
  }
}
