import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

// GET /api/vms/[id]/activity - Get VM activity log
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const vmId = params.id;

    // Get VM from database
    const vm = await db.virtualMachine.findFirst({
      where: { vmId },
    });

    if (!vm) {
      return NextResponse.json({ error: 'VM not found' }, { status: 404 });
    }

    // Check if user is owner or admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { id: true, role: true },
    });

    if (user?.id !== vm.ownerId && user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get activity log from database
    const activityLog = await db.vmActivity.findMany({
      where: { vmId },
      orderBy: { timestamp: 'desc' },
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
    });

    // Format activity log
    const formattedActivityLog = activityLog.map((activity) => ({
      id: activity.id,
      vmId: activity.vmId,
      action: activity.action,
      timestamp: activity.timestamp,
      user: activity.user?.email || '',
      details: activity.details,
    }));

    return NextResponse.json(formattedActivityLog);
  } catch (error: any) {
    console.error(`Error fetching activity log for VM ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch activity log' },
      { status: 500 }
    );
  }
}
