import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

// GET /api/vms/templates - Get all VM templates
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(req.url);
    const active = url.searchParams.get('active');
    const category = url.searchParams.get('category');

    // Build query
    const query: any = {};
    
    // Filter by active status if specified
    if (active === 'true') {
      query.active = true;
    } else if (active === 'false') {
      query.active = false;
    }
    
    // Filter by category if specified
    if (category && category !== 'all') {
      query.category = category;
    }

    // Get templates from database
    const templates = await db.vmTemplate.findMany({
      where: query,
      orderBy: { popular: 'desc' },
    });

    return NextResponse.json(templates);
  } catch (error: any) {
    console.error('Error fetching VM templates:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch VM templates' },
      { status: 500 }
    );
  }
}

// POST /api/vms/templates - Create a new VM template (admin only)
export async function POST(req: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const body = await req.json();
    const {
      name,
      description,
      icon,
      specs,
      price,
      popular,
      category,
      os,
      active,
    } = body;

    // Validate required fields
    if (!name || !description || !icon || !specs || !price || !category || !os) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create template in database
    const template = await db.vmTemplate.create({
      data: {
        name,
        description,
        icon,
        specs,
        price,
        popular: popular || false,
        category,
        os,
        active: active !== undefined ? active : true,
        creator: session.user?.email as string,
      },
    });

    return NextResponse.json(template);
  } catch (error: any) {
    console.error('Error creating VM template:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create VM template' },
      { status: 500 }
    );
  }
}
