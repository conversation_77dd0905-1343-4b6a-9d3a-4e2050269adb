import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ProxmoxClient } from '@/lib/proxmox/client';
import { db } from '@/lib/db';

// Initialize Proxmox client
const proxmoxClient = new ProxmoxClient({
  host: process.env.PROXMOX_HOST || 'localhost',
  port: parseInt(process.env.PROXMOX_PORT || '8006'),
  username: process.env.PROXMOX_USERNAME || 'root',
  password: process.env.PROXMOX_PASSWORD || '',
  realm: process.env.PROXMOX_REALM || 'pam',
});

// POST /api/vms/purchase - Purchase a new VM
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { id: true, email: true },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Parse request body
    const body = await req.json();
    const {
      templateId,
      vmName,
      region,
      cpu,
      memory,
      storage,
      operatingSystem,
      backups,
      autoStart,
      paymentMethod,
    } = body;

    // Validate required fields
    if (!templateId || !vmName || !region || !cpu || !memory || !storage || !operatingSystem || !paymentMethod) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get template from database
    const template = await db.vmTemplate.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Calculate price based on resources
    let price = template.price;
    if (cpu > template.specs.cpu) {
      price += (cpu - template.specs.cpu) * 5;
    }
    if (memory > template.specs.memory) {
      price += (memory - template.specs.memory) * 2;
    }
    if (storage > template.specs.storage) {
      price += (storage - template.specs.storage) * 0.1;
    }
    if (backups) {
      price += Math.ceil(price * 0.2); // 20% extra for backups
    }
    price = Math.round(price * 100) / 100;

    // Create order in database
    const order = await db.vmOrder.create({
      data: {
        vmName,
        templateId,
        userId: user.id,
        status: 'pending',
        price,
        region,
        paymentMethod,
        config: {
          cpu,
          memory,
          storage,
          operatingSystem,
          backups,
          autoStart,
        },
      },
    });

    // In a real application, you would process the payment here
    // For now, we'll just simulate a successful payment

    // Update order status to processing
    await db.vmOrder.update({
      where: { id: order.id },
      data: { status: 'processing' },
    });

    // Create VM in Proxmox (in a real application, this would be done by a background job)
    try {
      // Select a node based on region
      const nodes = await proxmoxClient.getNodes();
      const node = nodes.find(n => n.name.includes(region.toLowerCase())) || nodes[0];

      if (!node) {
        throw new Error('No suitable node found for VM creation');
      }

      // Create VM configuration
      const vmConfig = {
        name: vmName,
        node: node.name,
        description: `VM created from template ${template.name} for user ${user.email}`,
        cpu: { cores: cpu },
        memory: memory * 1024, // Convert GB to MB
        disks: [{ size: storage, storage: 'local-lvm' }],
        onBoot: autoStart,
      };

      // Create VM in Proxmox
      const newVM = await proxmoxClient.createVM(vmConfig);

      // Create VM record in database
      const vmRecord = await db.virtualMachine.create({
        data: {
          vmId: newVM.id,
          name: vmName,
          description: `Created from template ${template.name}`,
          templateId,
          ownerId: user.id,
          region,
          price,
          backups,
          os: operatingSystem,
        },
      });

      // Update order with VM ID and status
      await db.vmOrder.update({
        where: { id: order.id },
        data: {
          vmId: newVM.id,
          status: 'completed',
          completedAt: new Date(),
        },
      });

      // Create activity log entry
      await db.vmActivity.create({
        data: {
          vmId: newVM.id,
          action: 'create',
          userId: user.id,
          details: `VM created from template ${template.name}`,
        },
      });

      // Start VM if autoStart is true
      if (autoStart) {
        await proxmoxClient.startVM(newVM.id);
        
        // Create activity log entry for start
        await db.vmActivity.create({
          data: {
            vmId: newVM.id,
            action: 'start',
            userId: user.id,
            details: 'VM started automatically after creation',
          },
        });
      }

      // Return the updated order
      const updatedOrder = await db.vmOrder.findUnique({
        where: { id: order.id },
        include: {
          template: {
            select: {
              name: true,
            },
          },
          user: {
            select: {
              email: true,
            },
          },
        },
      });

      return NextResponse.json({
        id: updatedOrder?.id,
        vmId: updatedOrder?.vmId,
        vmName: updatedOrder?.vmName,
        template: updatedOrder?.template?.name,
        user: updatedOrder?.user?.email,
        status: updatedOrder?.status,
        createdAt: updatedOrder?.createdAt,
        completedAt: updatedOrder?.completedAt,
        price: updatedOrder?.price,
        region: updatedOrder?.region,
      });
    } catch (error: any) {
      // Update order status to failed
      await db.vmOrder.update({
        where: { id: order.id },
        data: {
          status: 'failed',
          error: error.message,
        },
      });

      console.error('Error creating VM:', error);
      return NextResponse.json(
        { error: error.message || 'Failed to create VM' },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error processing VM purchase:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process VM purchase' },
      { status: 500 }
    );
  }
}
