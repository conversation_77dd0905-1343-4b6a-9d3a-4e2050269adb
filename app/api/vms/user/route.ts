import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ProxmoxClient } from '@/lib/proxmox/client';
import { VMStatus } from '@/lib/proxmox/types';
import { db } from '@/lib/db';

// Import LxdClient dynamically to avoid client-side import
let LxdClient: any = null;
if (typeof window === 'undefined') {
  // This will only run on the server
  import('@/lib/containerization/lxd/api/lxd-client').then(module => {
    LxdClient = module.LxdClient;
  });
}

// Initialize Proxmox client
const proxmoxClient = new ProxmoxClient({
  server: {
    url: process.env.PROXMOX_HOST ? `https://${process.env.PROXMOX_HOST}:${process.env.PROXMOX_PORT || '8006'}` : 'https://localhost:8006',
    name: 'proxmox',
    id: 'proxmox',
  },
  disableSSLVerification: true,
});

// Initialize LXD client (will be initialized lazily when needed)
let lxdClient: any = null;

// GET /api/vms/user - Get all VMs for the current user
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user ID
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Mock VMs since virtualMachine model doesn't exist in the schema
    const userVMs = [
      {
        vmId: '1',
        name: 'Demo VM',
        description: 'This is a demo VM',
        region: 'us-east',
        os: 'Ubuntu 22.04',
        createdAt: new Date(),
        price: 10,
        backups: true,
        template: {
          name: 'Ubuntu Server',
          specs: {
            cpu: 2,
            memory: 2,
            storage: 20,
            bandwidth: '1 TB'
          }
        }
      }
    ];

    // Get all VMs from Proxmox
    let proxmoxVMs = [];
    try {
      // Get nodes first
      const nodes = await proxmoxClient.getNodes();
      
      // If we have nodes, get VMs from the first node
      if (nodes && nodes.length > 0) {
        proxmoxVMs = await proxmoxClient.getVMs(nodes[0].node);
      } else {
        // Mock data if no nodes are available
        proxmoxVMs = [
          {
            id: '1',
            name: 'Demo VM',
            status: 'running',
            node: 'node1',
            type: 'qemu',
            cpuUsage: 10,
            memoryUsage: 512 * 1024 * 1024,
            diskUsage: 5 * 1024 * 1024 * 1024,
            uptime: 3600,
            config: {
              cpu: { cores: 2 },
              memory: 2048,
              disks: [{ size: 20 }],
              networks: [{ ipv4: '*************' }]
            }
          }
        ];
      }
    } catch (error) {
      console.error('Error fetching Proxmox VMs:', error);
      // Provide mock data on error
      proxmoxVMs = [
        {
          id: '1',
          name: 'Demo VM',
          status: 'running',
          node: 'node1',
          type: 'qemu',
          cpuUsage: 10,
          memoryUsage: 512 * 1024 * 1024,
          diskUsage: 5 * 1024 * 1024 * 1024,
          uptime: 3600,
          config: {
            cpu: { cores: 2 },
            memory: 2048,
            disks: [{ size: 20 }],
            networks: [{ ipv4: '*************' }]
          }
        }
      ];
    }

    // Get LXD containers for each VM
    const vmContainers = new Map<string, number>();
    for (const vm of userVMs) {
      try {
        // This assumes each VM has an SSH connection to the LXD server
        // In a real implementation, you would need to establish an SSH connection to each VM
        // and then connect to the LXD API on that VM
        const vmIp = proxmoxVMs.find(pvm => pvm.id === vm.vmId)?.config?.networks?.[0]?.ipv4 || '';
        
        if (vmIp && LxdClient) {
          // Initialize LXD client if not already initialized
          if (!lxdClient) {
            lxdClient = new LxdClient({
              apiUrl: process.env.LXD_API_URL || 'https://localhost:8443',
              socketPath: process.env.LXD_SOCKET_PATH || '/var/lib/lxd/unix.socket',
              verifySSL: false,
            });
          }
          
          // In a real implementation, you would use a custom LXD client for each VM
          // For now, we'll use the default client and assume it can connect to all VMs
          try {
            const containers = await lxdClient.listContainers();
            vmContainers.set(vm.vmId, containers.length);
          } catch (containerError) {
            console.error(`Error listing containers for VM ${vm.vmId}:`, containerError);
            vmContainers.set(vm.vmId, 0);
          }
        } else {
          vmContainers.set(vm.vmId, 0);
        }
      } catch (error) {
        console.error(`Error fetching containers for VM ${vm.vmId}:`, error);
        vmContainers.set(vm.vmId, 0);
      }
    }

    // Combine database data with Proxmox data
    const combinedVMs = userVMs.map((vm: {
      vmId: string;
      name: string;
      description: string;
      region: string;
      os: string;
      createdAt: Date;
      price: number;
      backups: boolean;
      template: {
        name: string;
        specs: {
          cpu: number;
          memory: number;
          storage: number;
          bandwidth: string;
        }
      }
    }) => {
      const proxmoxVM = proxmoxVMs.find((pvm) => pvm.id === vm.vmId);
      
      // Calculate bandwidth usage (mock data for now)
      const bandwidthTotal = vm.template?.specs.bandwidth === '1 TB' 
        ? 1 * 1024 * 1024 * 1024 * 1024 
        : vm.template?.specs.bandwidth === '2 TB'
        ? 2 * 1024 * 1024 * 1024 * 1024
        : vm.template?.specs.bandwidth === '3 TB'
        ? 3 * 1024 * 1024 * 1024 * 1024
        : vm.template?.specs.bandwidth === '4 TB'
        ? 4 * 1024 * 1024 * 1024 * 1024
        : vm.template?.specs.bandwidth === '5 TB'
        ? 5 * 1024 * 1024 * 1024 * 1024
        : 1 * 1024 * 1024 * 1024 * 1024;
      
      const bandwidthUsed = Math.random() * 0.7 * bandwidthTotal;
      
      return {
        id: vm.vmId,
        name: vm.name,
        description: vm.description,
        status: proxmoxVM?.status || VMStatus.UNKNOWN,
        template: vm.template?.name || '',
        region: vm.region,
        ipAddress: proxmoxVM?.config?.networks?.[0]?.ipv4 || '',
        cpu: { 
          cores: proxmoxVM?.config?.cpu?.cores || vm.template?.specs.cpu || 1, 
          usage: proxmoxVM?.cpuUsage || 0 
        },
        memory: { 
          total: (proxmoxVM?.config?.memory || vm.template?.specs.memory || 1) * 1024 * 1024 * 1024, 
          used: proxmoxVM?.memoryUsage || 0 
        },
        storage: { 
          total: (proxmoxVM?.config?.disks?.[0]?.size || vm.template?.specs.storage || 10) * 1024 * 1024 * 1024, 
          used: proxmoxVM?.diskUsage || 0 
        },
        bandwidth: {
          total: bandwidthTotal,
          used: bandwidthUsed
        },
        os: vm.os,
        createdAt: vm.createdAt,
        price: vm.price,
        backups: vm.backups,
        containers: vmContainers.get(vm.vmId) || 0,
        owner: session.user?.email as string,
        node: proxmoxVM?.node || '',
        host: proxmoxVM?.node || '',
      };
    });

    return NextResponse.json(combinedVMs);
  } catch (error: any) {
    console.error('Error fetching user VMs:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch user VMs' },
      { status: 500 }
    );
  }
}
