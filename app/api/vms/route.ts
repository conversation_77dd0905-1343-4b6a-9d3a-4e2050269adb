import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ProxmoxClient } from '@/lib/proxmox/client';
import { VMStatus } from '@/lib/proxmox/types';
import { db } from '@/lib/db';

// Initialize Proxmox client
const proxmoxClient = new ProxmoxClient({
  host: process.env.PROXMOX_HOST || 'localhost',
  port: parseInt(process.env.PROXMOX_PORT || '8006'),
  username: process.env.PROXMOX_USERNAME || 'root',
  password: process.env.PROXMOX_PASSWORD || '',
  realm: process.env.PROXMOX_REALM || 'pam',
});

// GET /api/vms - Get all VMs for admin
export async function GET(req: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get all VMs from Proxmox
    const vms = await proxmoxClient.getVMs();

    // Get VM details from database
    const vmDetails = await db.virtualMachine.findMany({
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        template: true,
      },
    });

    // Combine Proxmox data with database data
    const combinedVMs = vms.map((vm) => {
      const details = vmDetails.find((detail) => detail.vmId === vm.id);
      return {
        id: vm.id,
        name: vm.name,
        status: vm.status,
        node: vm.node,
        type: vm.type,
        cpuUsage: vm.cpuUsage,
        memoryUsage: vm.memoryUsage,
        diskUsage: vm.diskUsage,
        uptime: vm.uptime,
        description: details?.description || '',
        owner: details?.owner || null,
        template: details?.template || null,
        region: details?.region || '',
        price: details?.price || 0,
        backups: details?.backups || false,
        createdAt: details?.createdAt || new Date(),
      };
    });

    return NextResponse.json(combinedVMs);
  } catch (error: any) {
    console.error('Error fetching VMs:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch VMs' },
      { status: 500 }
    );
  }
}

// POST /api/vms - Create a new VM (admin only)
export async function POST(req: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await db.user.findUnique({
      where: { email: session.user?.email as string },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const body = await req.json();
    const {
      name,
      description,
      templateId,
      userId,
      region,
      cpu,
      memory,
      storage,
      operatingSystem,
      backups,
      autoStart,
    } = body;

    // Validate required fields
    if (!name || !templateId || !userId || !region || !cpu || !memory || !storage || !operatingSystem) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get template from database
    const template = await db.vmTemplate.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Calculate price based on resources
    let price = template.price;
    if (cpu > template.specs.cpu) {
      price += (cpu - template.specs.cpu) * 5;
    }
    if (memory > template.specs.memory) {
      price += (memory - template.specs.memory) * 2;
    }
    if (storage > template.specs.storage) {
      price += (storage - template.specs.storage) * 0.1;
    }
    if (backups) {
      price += Math.ceil(price * 0.2); // 20% extra for backups
    }
    price = Math.round(price * 100) / 100;

    // Create VM in Proxmox
    const vmConfig = {
      name,
      description,
      cpu: { cores: cpu },
      memory: memory * 1024, // Convert GB to MB
      disks: [{ size: storage, storage: 'local-lvm' }],
      onBoot: autoStart,
    };

    const newVM = await proxmoxClient.createVM(vmConfig);

    // Create VM record in database
    const vmRecord = await db.virtualMachine.create({
      data: {
        vmId: newVM.id,
        name,
        description,
        templateId,
        ownerId: userId,
        region,
        price,
        backups,
        os: operatingSystem,
      },
    });

    return NextResponse.json(vmRecord);
  } catch (error: any) {
    console.error('Error creating VM:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create VM' },
      { status: 500 }
    );
  }
}
