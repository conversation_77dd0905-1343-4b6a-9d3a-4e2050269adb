/**
 * Nodebox Instance Management API Route
 * 
 * GET /api/nodebox/[instanceId] - Get instance details
 * DELETE /api/nodebox/[instanceId] - Destroy instance
 */

import { NextRequest, NextResponse } from 'next/server';
import { NodeboxManager } from '@/lib/nodebox-runtime/core/nodebox-manager';
import { NodeboxError } from '@/lib/nodebox-runtime/api/nodebox-types';

// Global manager instance
let globalManager: NodeboxManager | null = null;

function getManager(): NodeboxManager {
  if (!globalManager) {
    globalManager = new NodeboxManager({
      maxInstances: 20,
      autoCleanup: true,
      cleanupInterval: 300000 // 5 minutes
    });
  }
  return globalManager;
}

interface RouteParams {
  params: {
    instanceId: string;
  };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { instanceId } = params;

    if (!instanceId) {
      return NextResponse.json(
        { error: 'Instance ID is required' },
        { status: 400 }
      );
    }

    const manager = getManager();
    const instance = manager.getInstance(instanceId);

    if (!instance) {
      return NextResponse.json(
        { error: `Instance ${instanceId} not found` },
        { status: 404 }
      );
    }

    // Update activity timestamp
    manager.updateActivity(instanceId);

    // Return instance info (without sensitive runtime details)
    const responseData = {
      id: instance.id,
      config: instance.config,
      status: instance.status,
      createdAt: instance.createdAt,
      lastActivity: instance.lastActivity,
      error: instance.error
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Error getting Nodebox instance:', error);

    if (error instanceof NodeboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code,
          details: error.details
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to get Nodebox instance',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { instanceId } = params;

    if (!instanceId) {
      return NextResponse.json(
        { error: 'Instance ID is required' },
        { status: 400 }
      );
    }

    const manager = getManager();
    const success = await manager.destroyInstance(instanceId);

    if (!success) {
      return NextResponse.json(
        { error: `Instance ${instanceId} not found or could not be destroyed` },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Instance ${instanceId} destroyed successfully`
    });

  } catch (error) {
    console.error('Error destroying Nodebox instance:', error);

    if (error instanceof NodeboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code,
          details: error.details
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to destroy Nodebox instance',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { instanceId } = params;
    const body = await request.json();

    if (!instanceId) {
      return NextResponse.json(
        { error: 'Instance ID is required' },
        { status: 400 }
      );
    }

    const manager = getManager();
    const instance = manager.getInstance(instanceId);

    if (!instance) {
      return NextResponse.json(
        { error: `Instance ${instanceId} not found` },
        { status: 404 }
      );
    }

    // Update activity timestamp
    manager.updateActivity(instanceId);

    // Handle different update operations
    if (body.action) {
      switch (body.action) {
        case 'updateSettings':
          if (body.settings) {
            instance.config.settings = {
              ...instance.config.settings,
              ...body.settings
            };
          }
          break;

        case 'updateEnvironment':
          if (body.environment) {
            instance.config.environment = {
              ...instance.config.environment,
              ...body.environment
            };
          }
          break;

        case 'updateStatus':
          if (body.status) {
            instance.status = body.status;
          }
          break;

        default:
          return NextResponse.json(
            { error: `Unknown action: ${body.action}` },
            { status: 400 }
          );
      }
    }

    // Update last activity
    instance.lastActivity = new Date();

    // Return updated instance info
    const responseData = {
      id: instance.id,
      config: instance.config,
      status: instance.status,
      createdAt: instance.createdAt,
      lastActivity: instance.lastActivity,
      error: instance.error
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Error updating Nodebox instance:', error);

    if (error instanceof NodeboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code,
          details: error.details
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to update Nodebox instance',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
