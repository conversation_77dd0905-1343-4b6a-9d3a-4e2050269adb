/**
 * Nodebox List Instances API Route
 * 
 * GET /api/nodebox/list - List all instances
 */

import { NextRequest, NextResponse } from 'next/server';
import { NodeboxManager } from '@/lib/nodebox-runtime/core/nodebox-manager';
import { NodeboxError } from '@/lib/nodebox-runtime/api/nodebox-types';

// Global manager instance
let globalManager: NodeboxManager | null = null;

function getManager(): NodeboxManager {
  if (!globalManager) {
    globalManager = new NodeboxManager({
      maxInstances: 20,
      autoCleanup: true,
      cleanupInterval: 300000 // 5 minutes
    });
  }
  return globalManager;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const status = searchParams.get('status');
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    const manager = getManager();
    let instances = manager.listInstances();

    // Filter by project ID if provided
    if (projectId) {
      instances = instances.filter(instance => 
        instance.config.projectId === projectId
      );
    }

    // Filter by status if provided
    if (status) {
      instances = instances.filter(instance => 
        instance.status === status
      );
    }

    // Apply pagination if provided
    const limitNum = limit ? parseInt(limit, 10) : undefined;
    const offsetNum = offset ? parseInt(offset, 10) : 0;

    if (limitNum) {
      instances = instances.slice(offsetNum, offsetNum + limitNum);
    } else if (offsetNum > 0) {
      instances = instances.slice(offsetNum);
    }

    // Return instance info (without sensitive runtime details)
    const responseData = instances.map(instance => ({
      id: instance.id,
      config: instance.config,
      status: instance.status,
      createdAt: instance.createdAt,
      lastActivity: instance.lastActivity,
      error: instance.error
    }));

    // Get manager statistics
    const stats = manager.getStats();

    return NextResponse.json({
      instances: responseData,
      stats,
      pagination: {
        total: manager.listInstances().length,
        offset: offsetNum,
        limit: limitNum,
        hasMore: limitNum ? (offsetNum + limitNum) < manager.listInstances().length : false
      }
    });

  } catch (error) {
    console.error('Error listing Nodebox instances:', error);

    if (error instanceof NodeboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code,
          details: error.details
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to list Nodebox instances',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const status = searchParams.get('status');
    const force = searchParams.get('force') === 'true';

    const manager = getManager();
    let instances = manager.listInstances();

    // Filter instances to delete
    if (projectId) {
      instances = instances.filter(instance => 
        instance.config.projectId === projectId
      );
    }

    if (status) {
      instances = instances.filter(instance => 
        instance.status === status
      );
    }

    // Safety check - don't delete all instances unless force is true
    if (!projectId && !status && !force) {
      return NextResponse.json(
        { 
          error: 'Cannot delete all instances without force=true parameter',
          hint: 'Add ?force=true to delete all instances, or use projectId/status filters'
        },
        { status: 400 }
      );
    }

    // Delete instances
    const deletionResults = await Promise.allSettled(
      instances.map(instance => manager.destroyInstance(instance.id))
    );

    // Count successful deletions
    const successCount = deletionResults.filter(result => 
      result.status === 'fulfilled' && result.value === true
    ).length;

    const failureCount = deletionResults.length - successCount;

    return NextResponse.json({
      success: true,
      message: `Deleted ${successCount} instances`,
      details: {
        total: instances.length,
        successful: successCount,
        failed: failureCount
      }
    });

  } catch (error) {
    console.error('Error bulk deleting Nodebox instances:', error);

    if (error instanceof NodeboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code,
          details: error.details
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to delete Nodebox instances',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
