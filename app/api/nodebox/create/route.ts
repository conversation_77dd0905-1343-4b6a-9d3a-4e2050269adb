/**
 * Nodebox Create Instance API Route
 * 
 * POST /api/nodebox/create
 */

import { NextRequest, NextResponse } from 'next/server';
import { NodeboxManager } from '@/lib/nodebox-runtime/core/nodebox-manager';
import { getTemplate } from '@/lib/nodebox-runtime/utils/project-templates';
import {
  NodeboxConfig,
  NodeboxError,
  ProjectTemplate
} from '@/lib/nodebox-runtime/api/nodebox-types';

// Global manager instance (in a real app, you might want to use a singleton pattern or dependency injection)
let globalManager: NodeboxManager | null = null;

function getManager(): NodeboxManager {
  if (!globalManager) {
    globalManager = new NodeboxManager({
      maxInstances: 20,
      autoCleanup: true,
      cleanupInterval: 300000 // 5 minutes
    });
  }
  return globalManager;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Generate instance ID
    const instanceId = `nodebox_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create config
    const config: NodeboxConfig = {
      instanceId,
      name: body.name,
      description: body.description || `Nodebox instance: ${body.name}`,
      template: body.template as ProjectTemplate || 'vanilla-js',
      projectId: body.projectId,
      environment: body.environment || {},
      settings: {
        autoSave: true,
        autoPreview: true,
        memoryLimit: 512,
        timeoutMs: 30000,
        allowNetworking: false,
        enableHotReload: true,
        ...body.settings
      }
    };

    // Get manager and create instance
    const manager = getManager();
    const instance = await manager.createInstance(config);

    // Initialize with template if specified
    if (config.template && config.template !== 'custom') {
      try {
        const template = getTemplate(config.template);
        if (template && template.files) {
          await instance.filesystemManager.initializeProject(template.files);
          
          // Start development server if template has a start command
          if (template.startCommand) {
            try {
              const process = await instance.shellManager.executeNpmCommand('run', [template.startCommand]);
              console.log(`Started development server for instance ${instanceId}:`, process.id);
            } catch (startError) {
              console.warn(`Failed to start development server for instance ${instanceId}:`, startError);
              // Don't fail the instance creation if we can't start the dev server
            }
          }
        }
      } catch (templateError) {
        console.error(`Error initializing template for instance ${instanceId}:`, templateError);
        // Continue without template initialization
      }
    }

    // Return instance info (without sensitive runtime details)
    const responseData = {
      id: instance.id,
      config: instance.config,
      status: instance.status,
      createdAt: instance.createdAt,
      lastActivity: instance.lastActivity
    };

    return NextResponse.json(responseData, { status: 201 });

  } catch (error) {
    console.error('Error creating Nodebox instance:', error);

    if (error instanceof NodeboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code,
          details: error.details
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to create Nodebox instance',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  try {
    const manager = getManager();
    const stats = manager.getStats();
    
    return NextResponse.json({
      status: 'healthy',
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting Nodebox health status:', error);
    
    return NextResponse.json(
      { 
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
