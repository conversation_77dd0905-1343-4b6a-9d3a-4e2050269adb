import { openai } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Code generation tools
const codeGenerationTools = {
  generateComponent: tool({
    description: 'Generate a React component based on specifications',
    parameters: z.object({
      componentName: z.string().describe('Name of the component'),
      props: z.array(z.object({
        name: z.string(),
        type: z.string(),
        required: z.boolean(),
        description: z.string().optional(),
      })).describe('Component props'),
      features: z.array(z.string()).describe('List of features the component should have'),
      styling: z.enum(['tailwind', 'css-modules', 'styled-components', 'emotion']).describe('Styling approach'),
      typescript: z.boolean().describe('Whether to use TypeScript'),
    }),
    execute: async ({ componentName, props, features, styling, typescript }) => {
      // This would typically call a code generation service
      // For now, we'll return a structured response
      return {
        componentName,
        generatedCode: `// Generated ${typescript ? 'TypeScript' : 'JavaScript'} component
// Component: ${componentName}
// Styling: ${styling}
// Features: ${features.join(', ')}

${typescript ? 'interface Props {' : 'const Props = {'}
${props.map(prop => `  ${prop.name}${prop.required ? '' : '?'}: ${prop.type};`).join('\n')}
${typescript ? '}' : '};'}

export ${typescript ? `const ${componentName}: React.FC<Props> = (` : `function ${componentName}(`}props${typescript ? ')' : ')'} => {
  return (
    <div className="component-container">
      {/* Component implementation */}
      <h1>Generated ${componentName}</h1>
      {/* Features: ${features.join(', ')} */}
    </div>
  );
};`,
        files: [
          {
            path: `components/${componentName}.${typescript ? 'tsx' : 'jsx'}`,
            content: '// Component code would be here',
          }
        ],
        dependencies: styling === 'styled-components' ? ['styled-components'] : [],
      };
    },
  }),

  analyzeCode: tool({
    description: 'Analyze code for issues, improvements, and suggestions',
    parameters: z.object({
      code: z.string().describe('Code to analyze'),
      language: z.string().describe('Programming language'),
      analysisType: z.enum(['bugs', 'performance', 'security', 'style', 'all']).describe('Type of analysis'),
    }),
    execute: async ({ code, language, analysisType }) => {
      // This would typically use a code analysis service
      return {
        language,
        analysisType,
        issues: [
          {
            type: 'warning',
            line: 1,
            message: 'Consider adding error handling',
            suggestion: 'Wrap in try-catch block',
          }
        ],
        metrics: {
          complexity: 'medium',
          maintainability: 'good',
          performance: 'good',
        },
        suggestions: [
          'Add TypeScript types for better type safety',
          'Consider extracting reusable logic into custom hooks',
          'Add unit tests for better coverage',
        ],
      };
    },
  }),

  generateTests: tool({
    description: 'Generate unit tests for given code',
    parameters: z.object({
      code: z.string().describe('Code to generate tests for'),
      testFramework: z.enum(['jest', 'vitest', 'mocha', 'cypress']).describe('Testing framework'),
      testType: z.enum(['unit', 'integration', 'e2e']).describe('Type of tests'),
    }),
    execute: async ({ code, testFramework, testType }) => {
      return {
        testFramework,
        testType,
        testCode: `// Generated ${testType} tests using ${testFramework}
describe('Component Tests', () => {
  test('should render correctly', () => {
    // Test implementation
    expect(true).toBe(true);
  });
  
  test('should handle user interactions', () => {
    // Test implementation
    expect(true).toBe(true);
  });
});`,
        coverage: {
          statements: 85,
          branches: 80,
          functions: 90,
          lines: 85,
        },
      };
    },
  }),

  explainCode: tool({
    description: 'Explain how code works with detailed breakdown',
    parameters: z.object({
      code: z.string().describe('Code to explain'),
      language: z.string().describe('Programming language'),
      level: z.enum(['beginner', 'intermediate', 'advanced']).describe('Explanation level'),
    }),
    execute: async ({ code, language, level }) => {
      return {
        language,
        level,
        explanation: {
          overview: 'This code demonstrates...',
          breakdown: [
            {
              section: 'Imports and Dependencies',
              explanation: 'The code imports necessary modules...',
              codeSnippet: '// Import section',
            },
            {
              section: 'Main Logic',
              explanation: 'The main functionality...',
              codeSnippet: '// Main logic section',
            },
          ],
          concepts: [
            'React Hooks',
            'State Management',
            'Event Handling',
          ],
          resources: [
            'https://react.dev/learn',
            'https://developer.mozilla.org/en-US/docs/Web/JavaScript',
          ],
        },
      };
    },
  }),

  refactorCode: tool({
    description: 'Refactor code for better maintainability and performance',
    parameters: z.object({
      code: z.string().describe('Code to refactor'),
      goals: z.array(z.string()).describe('Refactoring goals (e.g., performance, readability, modularity)'),
      preserveAPI: z.boolean().describe('Whether to preserve the existing API'),
    }),
    execute: async ({ code, goals, preserveAPI }) => {
      return {
        originalCode: code,
        refactoredCode: `// Refactored code
// Goals: ${goals.join(', ')}
// API preserved: ${preserveAPI}

// Improved implementation here`,
        changes: [
          {
            type: 'improvement',
            description: 'Extracted reusable logic into custom hook',
            impact: 'Better code reusability',
          },
          {
            type: 'optimization',
            description: 'Memoized expensive calculations',
            impact: 'Improved performance',
          },
        ],
        metrics: {
          linesReduced: 15,
          complexityReduction: '20%',
          performanceGain: '15%',
        },
      };
    },
  }),
};

export async function POST(req: Request) {
  try {
    const { messages, systemPrompt, enableTools = true } = await req.json();

    const result = streamText({
      model: openai('gpt-4o'),
      system: systemPrompt || `You are an expert software developer and AI assistant specialized in:
- Code generation and component creation
- Code analysis and debugging
- Test generation and quality assurance
- Code explanation and documentation
- Refactoring and optimization

You have access to powerful tools for code generation, analysis, testing, and refactoring. 
Use these tools when appropriate to provide comprehensive assistance.

Always provide clear, well-documented, and production-ready code.
Consider best practices, performance, accessibility, and maintainability.
When generating React components, use modern patterns like hooks and functional components.
Prefer TypeScript when possible for better type safety.`,
      messages,
      tools: enableTools ? codeGenerationTools : {},
      maxSteps: 5,
      toolCallStreaming: true,
      onFinish: ({ text, toolCalls, toolResults, usage, finishReason }) => {
        console.log('Chat completed:', {
          textLength: text.length,
          toolCallsCount: toolCalls.length,
          usage,
          finishReason,
        });
      },
    });

    return result.toDataStreamResponse({
      getErrorMessage: (error) => {
        console.error('Chat error:', error);
        
        if (error instanceof Error) {
          return `Error: ${error.message}`;
        }
        
        if (typeof error === 'string') {
          return error;
        }
        
        return 'An unexpected error occurred. Please try again.';
      },
    });
  } catch (error) {
    console.error('API route error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}
