/**
 * Cognitive Agent API Route
 * 
 * This file implements a NextJS API route handler for a cognitive agent using the AI SDK.
 * It supports streaming responses, memory persistence with Weaviate, and middleware enhancements.
 */

import { NextRequest } from 'next/server';
import { streamText, generateText, wrapLanguageModel } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { BaseCognitiveAgent } from '@/lib/ai-sdk-agents/base-cognitive-agent';
import { createProductionModelRegistry } from '@/lib/ai-sdk-agents/provider-management/model-registry';
import { WeaviatePersistence } from '@/lib/ai-sdk-agents/memory/persistence/weaviate-persistence';
import { VectorMemoryStore } from '@/lib/ai-sdk-agents/memory/vector-store';
import { MemoryManager, ImportanceStrategy } from '@/lib/ai-sdk-agents/memory/memory-manager';
import { 
  createLoggingMiddleware, 
  createThinkingMiddleware, 
  createGuardrailMiddleware,
  createPIIPolicy,
  createProfanityPolicy
} from '@/lib/ai-sdk-agents/middleware';

// Define the maximum duration for streaming responses (2 minutes)
export const maxDuration = 120;

// Request validation schema
const requestSchema = z.object({
  messages: z.array(
    z.object({
      role: z.enum(['user', 'assistant', 'system']),
      content: z.string(),
      id: z.string().optional(),
    })
  ),
  context: z.record(z.any()).optional(),
  sessionId: z.string().optional(),
  stream: z.boolean().optional().default(true),
});

// Memory persistence configuration
const WEAVIATE_CONFIG = {
  host: process.env.WEAVIATE_HOST || 'localhost',
  port: process.env.WEAVIATE_PORT ? parseInt(process.env.WEAVIATE_PORT, 10) : 8080,
  https: process.env.WEAVIATE_HTTPS === 'true',
  apiKey: process.env.WEAVIATE_API_KEY,
  className: 'CognitiveAgentMemory',
};

/**
 * Create a cognitive agent with memory persistence
 */
async function createAgent(sessionId: string) {
  // Create a model registry with enhanced models
  const modelRegistry = createProductionModelRegistry();
  
  // Create a thinking middleware enhanced model
  const enhancedModel = wrapLanguageModel({
    model: openai('gpt-4o'),
    middleware: [
      createLoggingMiddleware({
        logParams: true,
        logResults: true,
      }),
      createThinkingMiddleware({
        includeThinkingInOutput: false,
      }),
      createGuardrailMiddleware({
        policies: [createPIIPolicy(), createProfanityPolicy()],
        violationAction: 'sanitize',
      }),
    ],
  });
  
  // Register the enhanced model
  modelRegistry.registerLanguageModel('enhanced-gpt-4o', enhancedModel);
  
  // Create Weaviate persistence adapter
  const persistence = new WeaviatePersistence({
    connection: {
      host: WEAVIATE_CONFIG.host,
      port: WEAVIATE_CONFIG.port,
      https: WEAVIATE_CONFIG.https,
      apiKey: WEAVIATE_CONFIG.apiKey,
    },
    className: `${WEAVIATE_CONFIG.className}_${sessionId}`,
    createClass: true,
    logger: {
      debug: console.debug,
      info: console.info,
      warn: console.warn,
      error: console.error,
    },
  });
  
  // Initialize the persistence adapter
  await persistence.initialize();
  
  // Create a vector memory store with Weaviate persistence
  const vectorStore = new VectorMemoryStore({
    modelRegistry,
    embeddingModelId: 'text-embedding-ada-002',
    logger: {
      debug: console.debug,
      info: console.info,
      warn: console.warn,
      error: console.error,
    },
  });
  
  // Create a memory manager
  const memoryManager = new MemoryManager({
    modelRegistry,
    embeddingModelId: 'text-embedding-ada-002',
    importanceStrategy: ImportanceStrategy.LENGTH_BASED,
    logger: {
      debug: console.debug,
      info: console.info,
      warn: console.warn,
      error: console.error,
    },
    vectorStoreOptions: {
      similarityAlgorithm: 'cosine',
    },
  });
  
  // Create the cognitive agent
  const agent = new BaseCognitiveAgent({
    id: `cognitive-agent-${sessionId}`,
    name: 'Cognitive Agent',
    description: 'An intelligent cognitive agent with advanced memory capabilities',
    icon: '🧠',
    capabilities: [
      {
        id: 'answer-question',
        name: 'Answer Question',
        description: 'Answer a question based on knowledge and reasoning',
        parameters: {
          question: {
            type: 'string',
            description: 'Question to answer',
            required: true,
          },
          context: {
            type: 'string',
            description: 'Additional context for the question',
            required: false,
          },
        },
        examples: [
          'What is the capital of France?',
          'How does machine learning work?',
        ],
      },
      {
        id: 'analyze-text',
        name: 'Analyze Text',
        description: 'Analyze text and provide insights',
        parameters: {
          text: {
            type: 'string',
            description: 'Text to analyze',
            required: true,
          },
        },
        examples: [
          'Analyze this article about climate change',
          'Provide insights on this research paper',
        ],
      },
    ],
    systemPrompt: `You are an intelligent cognitive agent with the ability to think, decide, and observe.
You have access to advanced memory systems with vector-based retrieval capabilities.
Use these capabilities to provide thoughtful and insightful responses based on your memories and reasoning.
Always be helpful, accurate, and concise in your responses.`,
    model: 'enhanced-gpt-4o',
    maxSteps: 5,
    modelRegistry,
    memoryOptions: {
      embeddingModelId: 'text-embedding-ada-002',
      maxItems: 1000,
      defaultTtl: 30 * 24 * 60 * 60 * 1000, // 30 days
      importanceStrategy: 'length_based',
      vectorStoreOptions: {
        similarityAlgorithm: 'cosine',
      },
    },
  });
  
  return agent;
}

/**
 * Store conversation history as memories
 */
async function storeConversationMemories(agent: BaseCognitiveAgent, messages: any[]) {
  // Store the last few messages as memories
  const recentMessages = messages.slice(-5);
  
  for (const message of recentMessages) {
    await agent.remember({
      content: message.content,
      type: message.role === 'user' ? 'episodic' : 'semantic',
      importance: 0.7,
      metadata: {
        role: message.role,
        timestamp: new Date().toISOString(),
        tags: ['conversation'],
      },
    });
  }
}

/**
 * POST handler for the cognitive agent API
 */
export async function POST(req: NextRequest) {
  try {
    // Parse and validate request
    const body = await req.json();
    const validationResult = requestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid request', 
          details: validationResult.error.format() 
        }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    const { messages, context, sessionId = 'default', stream = true } = validationResult.data;
    
    // Create or get the cognitive agent
    const agent = await createAgent(sessionId);
    
    // Store conversation history as memories
    await storeConversationMemories(agent, messages);
    
    // Extract the last user message
    const lastUserMessage = messages
      .filter(m => m.role === 'user')
      .pop();
      
    if (!lastUserMessage) {
      return new Response(
        JSON.stringify({ error: 'No user message found' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Process the request based on streaming preference
    if (stream) {
      // Use streaming response
      return streamText({
        model: openai('gpt-4o'),
        system: `You are an intelligent cognitive agent with the ability to think, decide, and observe.
You have access to advanced memory systems with vector-based retrieval capabilities.
Use these capabilities to provide thoughtful and insightful responses based on your memories and reasoning.

Previous conversation:
${messages.slice(0, -1).map(m => `${m.role}: ${m.content}`).join('\n')}`,
        prompt: lastUserMessage.content,
        temperature: 0.7,
        maxTokens: 2000,
      });
    } else {
      // Use non-streaming response with the cognitive agent's think and decide capabilities
      const thoughts = await agent.think(lastUserMessage.content);
      const decision = await agent.decide(lastUserMessage.content);
      
      // Retrieve relevant memories
      const memories = await agent.recall(lastUserMessage.content, { limit: 5 });
      
      // Generate the final response
      const response = await generateText({
        model: openai('gpt-4o'),
        system: `You are an intelligent cognitive agent.
Based on the following thoughts, decision, and memories, generate a response to the user's message.

Thoughts:
${thoughts.map(t => `- ${t.content}`).join('\n')}

Decision:
${decision.content}

Relevant memories:
${memories.map(m => `- ${m.content}`).join('\n')}`,
        prompt: `User message: ${lastUserMessage.content}

Generate a helpful response:`,
      });
      
      return new Response(
        JSON.stringify({
          response: response.text,
          thoughts,
          decision,
          memories,
        }),
        { headers: { 'Content-Type': 'application/json' } }
      );
    }
  } catch (error) {
    console.error('Error in cognitive agent API:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
