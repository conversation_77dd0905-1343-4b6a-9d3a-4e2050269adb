/**
 * Streaming Cognitive Agent API Route
 * 
 * This file implements a NextJS API route handler for a cognitive agent with streaming capabilities.
 * It uses the AI SDK's createDataStreamResponse to stream the agent's thinking, decision making,
 * and final response to the client.
 */

import { NextRequest } from 'next/server';
import { streamText, createDataStreamResponse } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { BaseCognitiveAgent } from '@/lib/ai-sdk-agents/base-cognitive-agent';
import { createProductionModelRegistry } from '@/lib/ai-sdk-agents/provider-management/model-registry';
import { WeaviatePersistence } from '@/lib/ai-sdk-agents/memory/persistence/weaviate-persistence';
import { MemoryManager, ImportanceStrategy } from '@/lib/ai-sdk-agents/memory/memory-manager';
import { 
  createLoggingMiddleware, 
  createThinkingMiddleware, 
  createGuardrailMiddleware,
  createPIIPolicy,
  createProfanityPolicy
} from '@/lib/ai-sdk-agents/middleware';

// Define the maximum duration for streaming responses (2 minutes)
export const maxDuration = 120;

// Request validation schema
const requestSchema = z.object({
  messages: z.array(
    z.object({
      role: z.enum(['user', 'assistant', 'system']),
      content: z.string(),
      id: z.string().optional(),
    })
  ),
  context: z.record(z.any()).optional(),
  sessionId: z.string().optional(),
  capability: z.string().optional(),
  params: z.record(z.any()).optional(),
});

// Memory persistence configuration
const WEAVIATE_CONFIG = {
  host: process.env.WEAVIATE_HOST || 'localhost',
  port: process.env.WEAVIATE_PORT ? parseInt(process.env.WEAVIATE_PORT, 10) : 8080,
  https: process.env.WEAVIATE_HTTPS === 'true',
  apiKey: process.env.WEAVIATE_API_KEY,
  className: 'CognitiveAgentMemory',
};

// Agent cache to avoid recreating agents for the same session
const agentCache = new Map<string, {
  agent: BaseCognitiveAgent;
  lastAccessed: number;
}>();

// Clean up old agents every hour
setInterval(() => {
  const now = Date.now();
  for (const [sessionId, entry] of agentCache.entries()) {
    // Remove agents not accessed in the last hour
    if (now - entry.lastAccessed > 60 * 60 * 1000) {
      agentCache.delete(sessionId);
    }
  }
}, 60 * 60 * 1000);

/**
 * Get or create a cognitive agent with memory persistence
 */
async function getAgent(sessionId: string) {
  // Check if we have a cached agent
  const cachedEntry = agentCache.get(sessionId);
  if (cachedEntry) {
    cachedEntry.lastAccessed = Date.now();
    return cachedEntry.agent;
  }
  
  // Create a model registry with enhanced models
  const modelRegistry = createProductionModelRegistry();
  
  // Create Weaviate persistence adapter
  const persistence = new WeaviatePersistence({
    connection: {
      host: WEAVIATE_CONFIG.host,
      port: WEAVIATE_CONFIG.port,
      https: WEAVIATE_CONFIG.https,
      apiKey: WEAVIATE_CONFIG.apiKey,
    },
    className: `${WEAVIATE_CONFIG.className}_${sessionId}`,
    createClass: true,
    logger: {
      debug: console.debug,
      info: console.info,
      warn: console.warn,
      error: console.error,
    },
  });
  
  // Initialize the persistence adapter
  await persistence.initialize();
  
  // Create a memory manager
  const memoryManager = new MemoryManager({
    modelRegistry,
    embeddingModelId: 'text-embedding-ada-002',
    importanceStrategy: ImportanceStrategy.LENGTH_BASED,
    logger: {
      debug: console.debug,
      info: console.info,
      warn: console.warn,
      error: console.error,
    },
    vectorStoreOptions: {
      similarityAlgorithm: 'cosine',
    },
  });
  
  // Create the cognitive agent
  const agent = new BaseCognitiveAgent({
    id: `cognitive-agent-${sessionId}`,
    name: 'Cognitive Agent',
    description: 'An intelligent cognitive agent with advanced memory capabilities',
    icon: '🧠',
    capabilities: [
      {
        id: 'answer-question',
        name: 'Answer Question',
        description: 'Answer a question based on knowledge and reasoning',
        parameters: {
          question: {
            type: 'string',
            description: 'Question to answer',
            required: true,
          },
          context: {
            type: 'string',
            description: 'Additional context for the question',
            required: false,
          },
        },
        examples: [
          'What is the capital of France?',
          'How does machine learning work?',
        ],
      },
      {
        id: 'analyze-text',
        name: 'Analyze Text',
        description: 'Analyze text and provide insights',
        parameters: {
          text: {
            type: 'string',
            description: 'Text to analyze',
            required: true,
          },
        },
        examples: [
          'Analyze this article about climate change',
          'Provide insights on this research paper',
        ],
      },
    ],
    systemPrompt: `You are an intelligent cognitive agent with the ability to think, decide, and observe.
You have access to advanced memory systems with vector-based retrieval capabilities.
Use these capabilities to provide thoughtful and insightful responses based on your memories and reasoning.
Always be helpful, accurate, and concise in your responses.`,
    model: 'gpt-4o',
    maxSteps: 5,
    modelRegistry,
    memoryOptions: {
      embeddingModelId: 'text-embedding-ada-002',
      maxItems: 1000,
      defaultTtl: 30 * 24 * 60 * 60 * 1000, // 30 days
      importanceStrategy: 'length_based',
      vectorStoreOptions: {
        similarityAlgorithm: 'cosine',
      },
    },
  });
  
  // Cache the agent
  agentCache.set(sessionId, {
    agent,
    lastAccessed: Date.now(),
  });
  
  return agent;
}

/**
 * Store conversation history as memories
 */
async function storeConversationMemories(agent: BaseCognitiveAgent, messages: any[]) {
  // Store the last few messages as memories
  const recentMessages = messages.slice(-5);
  
  for (const message of recentMessages) {
    await agent.remember({
      content: message.content,
      type: message.role === 'user' ? 'episodic' : 'semantic',
      importance: 0.7,
      metadata: {
        role: message.role,
        timestamp: new Date().toISOString(),
        tags: ['conversation'],
      },
    });
  }
}

/**
 * POST handler for the streaming cognitive agent API
 */
export async function POST(req: NextRequest) {
  try {
    // Parse and validate request
    const body = await req.json();
    const validationResult = requestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid request', 
          details: validationResult.error.format() 
        }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    const { messages, context, sessionId = 'default', capability, params = {} } = validationResult.data;
    
    // Get or create the cognitive agent
    const agent = await getAgent(sessionId);
    
    // Store conversation history as memories
    await storeConversationMemories(agent, messages);
    
    // Extract the last user message
    const lastUserMessage = messages
      .filter(m => m.role === 'user')
      .pop();
      
    if (!lastUserMessage) {
      return new Response(
        JSON.stringify({ error: 'No user message found' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Create a streaming response
    return createDataStreamResponse({
      execute: async (dataStream) => {
        try {
          // If a specific capability is requested, execute it
          if (capability) {
            dataStream.write({ type: 'thinking', content: `Executing capability: ${capability}` });
            
            const result = await agent.execute(capability, {
              ...params,
              input: lastUserMessage.content,
            });
            
            dataStream.write({ type: 'result', content: result.result });
            
            // Stream the thoughts
            for (const thought of result.thoughts) {
              dataStream.write({ type: 'thought', content: thought.content, thoughtType: thought.type });
            }
            
            // Stream the decision
            dataStream.write({ type: 'decision', content: result.decision.content, confidence: result.decision.confidence });
            
            // Stream the final result
            dataStream.write({ type: 'response', content: result.result });
          } else {
            // Otherwise, use the think-decide-respond flow
            dataStream.write({ type: 'thinking', content: 'Thinking about your message...' });
            
            // Retrieve relevant memories
            const memories = await agent.recall(lastUserMessage.content, { limit: 5 });
            dataStream.write({ 
              type: 'memories', 
              content: memories.map(m => m.content),
              count: memories.length
            });
            
            // Generate thoughts
            const thoughts = await agent.think(lastUserMessage.content, {
              onThought: (thought) => {
                dataStream.write({ type: 'thought', content: thought.content, thoughtType: thought.type });
              }
            });
            
            // Make a decision
            dataStream.write({ type: 'deciding', content: 'Making a decision based on thoughts and memories...' });
            const decision = await agent.decide(lastUserMessage.content);
            dataStream.write({ type: 'decision', content: decision.content, confidence: decision.confidence });
            
            // Generate the final response using streamText
            const streamResult = streamText({
              model: openai('gpt-4o'),
              system: `You are an intelligent cognitive agent.
Based on the following thoughts, decision, and memories, generate a response to the user's message.

Thoughts:
${thoughts.map(t => `- ${t.content}`).join('\n')}

Decision:
${decision.content}

Relevant memories:
${memories.map(m => `- ${m.content}`).join('\n')}`,
              prompt: `User message: ${lastUserMessage.content}

Generate a helpful response:`,
            });
            
            // Stream the response chunks
            for await (const chunk of streamResult.toDataStream()) {
              if (chunk.type === 'text') {
                dataStream.write({ type: 'response', content: chunk.text });
              }
            }
          }
        } catch (error) {
          console.error('Error in cognitive agent stream:', error);
          dataStream.write({ 
            type: 'error', 
            content: error instanceof Error ? error.message : 'Unknown error' 
          });
        }
      }
    });
  } catch (error) {
    console.error('Error in cognitive agent API:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
