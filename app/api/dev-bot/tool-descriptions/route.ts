/**
 * API route for getting tool descriptions
 * This endpoint provides descriptions for tools without exposing Node.js modules to the client
 */

import { NextResponse } from 'next/server';
import { appDevTools } from '@/lib/app-dev/tools';

export async function GET(request: Request) {
  try {
    // Get the tool name from the query string
    const url = new URL(request.url);
    const toolName = url.searchParams.get('tool');

    if (toolName) {
      // Return description for a specific tool
      const tool = appDevTools[toolName as keyof typeof appDevTools];
      if (tool) {
        return NextResponse.json({
          success: true,
          description: tool.description || 'No description available'
        });
      } else {
        return NextResponse.json(
          {
            success: false,
            error: `Tool '${toolName}' not found`
          },
          { status: 404 }
        );
      }
    } else {
      // Return descriptions for all tools
      const descriptions: Record<string, string> = {};
      
      Object.entries(appDevTools).forEach(([name, tool]) => {
        descriptions[name] = tool.description || 'No description available';
      });
      
      return NextResponse.json({
        success: true,
        descriptions
      });
    }
  } catch (error: any) {
    console.error('Error fetching tool descriptions:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch tool descriptions',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
