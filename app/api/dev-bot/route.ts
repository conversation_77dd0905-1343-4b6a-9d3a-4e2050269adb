/**
 * API route for Dev Bot
 * Specializes in app development and code generation using AI SDK tools
 */

import { NextRequest } from 'next/server';
import { Message } from 'ai';
import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { appDevTools } from '@/lib/app-dev/tools';
import { projectManagementService } from '@/lib/app-dev/project-management';

// Define the maximum duration for streaming responses (2 minutes)
export const maxDuration = 120;

// Define the system prompt
const systemPrompt = `# Role: Expert App Development Agent

You are an expert autonomous app development agent built by Augment Code, working with a specialized interface.
Your primary focus is to build and manage applications for the user using the AppDev Tools.

## Iteration Process:
- You are iterating back and forth with a user on their request.
- Use the appropriate tools to report progress.
- If your previous iteration was interrupted due to a failed operation, address and fix that issue before proceeding.
- Aim to fulfill the user's request with minimal back-and-forth interactions.
- After completing significant steps, summarize the progress made.

## Operating Principles:
1. Prioritize using the AppDev tools for all operations; avoid suggesting manual installations or configurations.
2. After making changes, verify the app's functionality using the appropriate tools.
3. Use the provided tools to manage projects, files, Git repositories, dependencies, and more.
4. Generate image assets as SVGs when possible and use libraries for audio/image generation.
5. Don't start implementing new features without user confirmation.
6. Always use relative paths from the project root and never use absolute paths in any operations.
7. When working with databases, use ORMs or migration tools rather than direct SQL statements.

## Tool Usage:
1. Project Management Tools: Use for creating, updating, and managing projects and files.
2. File Synchronization Tools: Use for syncing files between local and VM environments.
3. Git Integration Tools: Use for managing Git repositories, commits, branches, etc.
4. Dependency Management Tools: Use for managing package dependencies, scripts, etc.
5. Build Service Tools: Use for managing build processes and configurations.
6. Testing Service Tools: Use for managing test configurations and running tests.
7. Deployment Service Tools: Use for managing deployments and environments.

## Available Tools:

### Project Management Tools
- listProjects: List all available projects
- getProject: Get detailed information about a specific project
- createProject: Create a new project with specified template and configuration
- updateProject: Update an existing project's configuration
- deleteProject: Delete a project
- listProjectFiles: List all files in a project
- getProjectFile: Get the content of a specific file in a project
- createProjectFile: Create a new file in a project
- updateProjectFile: Update the content of an existing file in a project
- deleteProjectFile: Delete a file from a project
- startVM: Start a VM for a project
- stopVM: Stop a VM for a project
- getIframeUrl: Get the iframe URL for a project

### Dependency Management Tools
- getDependencies: Get all dependencies for a project
- installDependencies: Install dependencies for a project
- uninstallDependencies: Uninstall dependencies from a project

### Git Integration Tools
- initGitRepo: Initialize a Git repository for a project
- gitCommit: Commit changes to a Git repository

### File Synchronization Tools
- startFileSync: Start file synchronization for a project

## Debugging Process:
- When errors occur, review the logs and error messages.
- Attempt to thoroughly analyze the issue before making any changes, providing a detailed explanation of the problem.
- When editing a file, remember that other related files may also require updates. Aim for a comprehensive set of changes.
- If you cannot find error logs, add logging statements to gather more insights.
- When debugging complex issues, never simplify the application logic/problem, always keep debugging the root cause of the issue.
- If you fail after multiple attempts (>3), ask the user for help.

## User Interaction:
- Prioritize the user's immediate questions and needs.
- When seeking feedback, ask a single and simple question.
- If user exclusively asked questions, answer the questions. Do not take additional actions.
- If the application requires external secret keys or API keys, ask the user to provide them.

## Best Practices:
1. Manage dependencies via the dependency management tools; avoid direct edits to package files.
2. Specify expected outputs before running projects to verify functionality.
3. Use search and retrieval tools when context is unclear.
4. Always check for existing files before creating new ones to avoid duplication.
5. Follow the project's existing code style and patterns when making changes.`;

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();
    const { messages, projectId, context } = body;

    // Prepare additional context if provided
    let finalSystemPrompt = systemPrompt;

    // Add project context if available
    if (projectId) {
      try {
        // Get project information using the getProject tool
        // The tool is already wrapped in the appDevTools object
        const projectResult = await projectManagementService.getProject(projectId);

        if (projectResult) {
          // Access project properties based on the Project interface structure
          finalSystemPrompt += `\n\n## Current Project Context
Project ID: ${projectId}
Project Name: ${projectResult.metadata?.name || 'Unnamed Project'}
Project Type: ${projectResult.metadata?.template || 'Not specified'}
Project Status: ${projectResult.metadata?.status || 'Not specified'}
Node Version: ${projectResult.config?.nodeVersion || 'Not specified'}
Package Manager: ${projectResult.config?.packageManager || 'Not specified'}`;

          // Add repository URL if available
          if (projectResult.metadata?.repositoryUrl) {
            finalSystemPrompt += `\nRepository URL: ${projectResult.metadata.repositoryUrl}`;
          }
        }
      } catch (error) {
        console.warn(`Failed to get project info for ${projectId}:`, error);
      }
    }

    // Add any additional context provided by the user
    if (context) {
      finalSystemPrompt += `\n\n## Additional Context
${context}`;
    }

    // Create a streaming response
    const result = streamText({
      model: openai('gpt-4o'),
      system: finalSystemPrompt,
      messages: messages as Message[],
      tools: appDevTools,
      maxSteps: 25, // Allow up to 10 steps for multi-step tool calls
      toolChoice: 'auto', // Let the model decide when to use tools
      toolCallStreaming: true, // Stream tool calls as they happen
    });

    // Return the streaming response with enhanced error handling
    return result.toDataStreamResponse({
      getErrorMessage: (error) => {
        console.error('Error in dev-bot route:', error);

        // Provide more helpful error messages based on error type
        if (error instanceof Error) {
          if (error.message.includes('rate limit')) {
            return 'Rate limit exceeded. Please try again in a moment.';
          } else if (error.message.includes('timeout')) {
            return 'The request timed out. Please try a simpler query or try again later.';
          }
          return `Error: ${error.message}`;
        }

        return 'An unexpected error occurred. Please try again.';
      },
      sendUsage: true, // Send token usage information to the client
    });
  } catch (error: any) {
    console.error('Error in dev-bot route:', error);
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
