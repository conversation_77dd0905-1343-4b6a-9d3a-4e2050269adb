/**
 * API route for getting available dev-bot tools
 * This endpoint provides information about available tools without exposing Node.js modules to the client
 */

import { NextResponse } from 'next/server';
import { appDevTools } from '@/lib/app-dev/tools';

export async function GET() {
  try {
    // Extract just the tool names and descriptions to avoid sending Node.js modules to the client
    const toolsInfo = Object.entries(appDevTools).map(([name, tool]) => ({
      name,
      description: tool.description || 'No description available'
    }));

    return NextResponse.json({
      success: true,
      tools: toolsInfo
    });
  } catch (error: any) {
    console.error('Error fetching tools:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch tools',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
