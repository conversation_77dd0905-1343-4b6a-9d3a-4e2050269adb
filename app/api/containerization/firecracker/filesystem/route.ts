import { NextRequest, NextResponse } from 'next/server';
import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * API endpoint for handling file system operations in direct VMs
 */
export async function POST(req: NextRequest) {
  let body;
  
  try {
    body = await req.json();
  } catch (error) {
    return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
  }
  
  const { vmId, operation, path: filePath, content } = body;
  
  if (!vmId) {
    return NextResponse.json({ error: 'VM ID is required' }, { status: 400 });
  }
  
  if (!operation) {
    return NextResponse.json({ error: 'Operation is required' }, { status: 400 });
  }
  
  // Check if the VM metadata exists
  const vmDir = `/tmp/microvms/${vmId}`;
  const metadataPath = path.join(vmDir, 'metadata', 'vm.json');
  
  if (!fs.existsSync(metadataPath)) {
    console.error(`VM metadata not found at ${metadataPath}`);
    // Still proceed with operations since file system operations might be valid
    // even without metadata if we're accessing the host filesystem 
  }
  
  try {
    // Handle different operations
    switch (operation) {
      case 'list':
        return await listFiles(vmId, filePath);
      case 'read':
        return await readFile(vmId, filePath);
      case 'write':
        return await writeFile(vmId, filePath, content);
      case 'delete':
        return await deleteFile(vmId, filePath);
      case 'mkdir':
        return await createDirectory(vmId, filePath);
      default:
        return NextResponse.json({ error: 'Invalid operation' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in file operation:', error);
    return NextResponse.json({ 
      error: 'Failed to perform file operation',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * List files in a directory
 */
async function listFiles(vmId: string, dirPath: string) {
  if (!dirPath) {
    dirPath = '/';  // Default to root if not specified
  }
  
  try {
    // Ensure the directory exists
    if (!fs.existsSync(dirPath)) {
      console.warn(`Directory ${dirPath} does not exist`);
      return NextResponse.json({ contents: [] });
    }
    
    // Use fs.readdirSync instead of executing ls command for better stability
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    const contents = items.map(item => {
      const itemPath = path.join(dirPath, item.name).replace(/\\/g, '/');
      const stats = fs.statSync(itemPath);
      
      return {
        path: item.isDirectory() ? `${itemPath}/` : itemPath,
        name: item.name,
        type: item.isDirectory() ? 'directory' : 'file',
        size: stats.size,
        modified: stats.mtime.toLocaleString(),
        permissions: getPermissionsString(stats.mode),
        owner: stats.uid.toString(),
        group: stats.gid.toString(),
      };
    });
    
    // Sort directories first, then files
    contents.sort((a, b) => {
      if (a.type === 'directory' && b.type !== 'directory') return -1;
      if (a.type !== 'directory' && b.type === 'directory') return 1;
      return a.name.localeCompare(b.name);
    });
    
    return NextResponse.json({ contents });
  } catch (error) {
    console.error('Error listing files:', error);
    return NextResponse.json({ 
      error: 'Failed to list files',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * Read file content
 */
async function readFile(vmId: string, filePath: string) {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }
    
    // Check if it's a directory
    const stats = fs.statSync(filePath);
    if (stats.isDirectory()) {
      return NextResponse.json({ error: 'Cannot read a directory as a file' }, { status: 400 });
    }
    
    // Read file content
    const content = fs.readFileSync(filePath, 'utf-8');
    
    return NextResponse.json({ content });
  } catch (error) {
    console.error('Error reading file:', error);
    return NextResponse.json({ 
      error: 'Failed to read file',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * Write file content
 */
async function writeFile(vmId: string, filePath: string, content: string) {
  try {
    // Create directory if it doesn't exist
    const dirname = path.dirname(filePath);
    if (!fs.existsSync(dirname)) {
      fs.mkdirSync(dirname, { recursive: true });
    }
    
    // Write file content
    fs.writeFileSync(filePath, content, 'utf-8');
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error writing file:', error);
    return NextResponse.json({ 
      error: 'Failed to write file',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * Delete file or directory
 */
async function deleteFile(vmId: string, filePath: string) {
  try {
    // Check if path exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }
    
    // Check if it's a directory or file
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Delete directory recursively
      fs.rmdirSync(filePath, { recursive: true });
    } else {
      // Delete file
      fs.unlinkSync(filePath);
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting file:', error);
    return NextResponse.json({ 
      error: 'Failed to delete file',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * Create directory
 */
async function createDirectory(vmId: string, dirPath: string) {
  try {
    // Create directory recursively
    fs.mkdirSync(dirPath, { recursive: true });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error creating directory:', error);
    return NextResponse.json({ 
      error: 'Failed to create directory',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * Helper function to convert mode to permissions string
 */
function getPermissionsString(mode: number): string {
  const permissions = [];
  
  // Owner permissions
  permissions.push(mode & 0o400 ? 'r' : '-');
  permissions.push(mode & 0o200 ? 'w' : '-');
  permissions.push(mode & 0o100 ? 'x' : '-');
  
  // Group permissions
  permissions.push(mode & 0o040 ? 'r' : '-');
  permissions.push(mode & 0o020 ? 'w' : '-');
  permissions.push(mode & 0o010 ? 'x' : '-');
  
  // Others permissions
  permissions.push(mode & 0o004 ? 'r' : '-');
  permissions.push(mode & 0o002 ? 'w' : '-');
  permissions.push(mode & 0o001 ? 'x' : '-');
  
  // Determine the file type
  const fileType = (mode & 0o170000) === 0o040000 ? 'd' : '-';
  
  return fileType + permissions.join('');
} 