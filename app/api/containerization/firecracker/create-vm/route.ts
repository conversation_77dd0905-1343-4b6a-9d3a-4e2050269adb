import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { logger } from '@/lib/logger';
import { findKernelImage, findRootfsImage } from '@/lib/containerization/microvm/utils';

// Initialize the MicroVM manager with Firecracker backend
const microVmManager = new MicroVmManager();

// Create a new MicroVM with Firecracker
export async function POST(request: Request) {
  try {
    // Check authentication
    let session: any = null;
    
    try {
      session = await getServerSession(authOptions);
      if (!session || !('user' in session) || !session.user || !('email' in session.user)) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
    } catch (authError) {
      logger.warn('Authentication error:', authError);
      return NextResponse.json({ error: 'Authentication error' }, { status: 401 });
    }
    
    // Parse request body
    const body = await request.json();
    const {
      name,
      template = 'nextjs',
      memSizeMib = 2048,
      vcpuCount = 2,
      networkEnabled = true,
      projectId = 'default-project',
    } = body;

    // Generate a unique name if not provided
    const vmName = name || `fc-vm-${Date.now().toString().slice(-6)}`;
    
    logger.info(`Creating Firecracker MicroVM '${vmName}' with template '${template}'`);

    // Find kernel and rootfs images
    const kernelPath = findKernelImage() || '/path/to/default-vmlinux.bin';
    const rootfsPath = findRootfsImage() || '/path/to/default-rootfs.ext4';

    // Create the MicroVM using the Firecracker backend
    // Note: Some properties like networkEnabled and backend are not included
    // as they are not part of the MicroVmCreationOptions type
    const microvm = await microVmManager.createMicroVm({
      name: vmName,
      memSizeMib,
      vcpuCount,
      kernel: {
        path: kernelPath,
        bootArgs: "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp",
      },
      rootfs: {
        path: rootfsPath,
        readOnly: false,
      },
      labels: {
        projectId: projectId?.toString() || 'default',
        createdBy: session.user.email as string || 'unknown',
        createdAt: new Date().toISOString(),
        template,
        networkEnabled: networkEnabled.toString(),
      }
    });
    
    // Start the MicroVM
    await microvm.start();
    
    // Get VM info
    const vmInfo = await microvm.getInfo();
    
    return NextResponse.json({
      vmId: vmInfo.id,
      name: vmInfo.name,
      status: vmInfo.state,
      template,
      resources: {
        memSizeMib,
        vcpuCount,
      },
      networkInterfaces: vmInfo.networkInterfaces || [],
      message: `Firecracker MicroVM '${vmName}' created and started successfully`,
    }, { status: 201 });
  } catch (error) {
    logger.error('Error creating Firecracker MicroVM:', error);
    
    if (error instanceof Error) {
      // Handle specific error types
      if (error.name === 'AlreadyExistsError') {
        return NextResponse.json({ error: error.message }, { status: 409 });
      }
      
      if (error.name === 'ResourceExhaustionError') {
        return NextResponse.json({ error: error.message }, { status: 507 });
      }
      
      if (error.message.includes('ENOENT')) {
        return NextResponse.json({ 
          error: 'Server configuration error: A required file was not found',
          details: error.message
        }, { status: 500 });
      }
    }
    
    // Generic error
    return NextResponse.json({ 
      error: 'Failed to create Firecracker MicroVM',
      message: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
} 