import { NextRequest, NextResponse } from "next/server";
import * as fs from "fs";
import * as path from "path";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json({ error: "VM ID is required" }, { status: 400 });
    }

    // Check if this is a directly created VM
    // Look for metadata in the VM directory
    const vmDir = `/tmp/microvms/${id}`;
    const metadataPath = path.join(vmDir, "metadata", "vm.json");

    // If metadata file exists, this is a direct VM
    if (fs.existsSync(metadataPath)) {
      try {
        const metadata = JSON.parse(fs.readFileSync(metadataPath, "utf-8"));
        const socketPath = metadata.socketPath;

        // Check if the VM is running by checking if the socket file exists
        let status = "unknown";
        if (fs.existsSync(socketPath)) {
          try {
            // Try to get machine config via API to confirm VM is running
            const curl = `curl --unix-socket ${socketPath} -X GET http://localhost/machine-config`;
            await execAsync(curl);
            status = "running";
          } catch (error) {
            status = "error";
          }
        } else {
          status = "stopped";
        }

        // Get basic resource usage (just mock data for now)
        const resources = {
          cpu: Math.floor(Math.random() * 20), // 0-20% CPU usage
          memory: Math.floor(metadata.memSizeMib * 0.6), // 60% of allocated memory
          disk: 0, // No disk usage tracking for direct VMs
        };

        return NextResponse.json({
          id,
          name: metadata.name,
          status,
          resources,
          metadata,
        });
      } catch (error) {
        return NextResponse.json(
          {
            error: "Failed to read VM metadata",
            details: error instanceof Error ? error.message : String(error),
          },
          { status: 500 }
        );
      }
    }

    // If not a direct VM, try to get status from MicroVM Manager API
    const manangerEndpoint = `/api/containerization/microvm/${id}`;
    try {
      const apiUrl = new URL(manangerEndpoint, request.url);
      const response = await fetch(apiUrl.toString());
      
      if (response.ok) {
        const data = await response.json();
        return NextResponse.json(data);
      } else {
        return NextResponse.json(
          { error: "Failed to get VM status from manager" },
          { status: 404 }
        );
      }
    } catch (error) {
      return NextResponse.json(
        { 
          error: "Failed to get VM status", 
          details: error instanceof Error ? error.message : String(error) 
        },
        { status: 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { 
        error: "Failed to process request", 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
} 