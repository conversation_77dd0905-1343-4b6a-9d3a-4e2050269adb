import { NextRequest, NextResponse } from "next/server";
import * as fs from "fs";
import * as path from "path";
import { exec, spawn } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string; action: string } }
) {
  try {
    const { id, action } = params;

    if (!id) {
      return NextResponse.json({ error: "VM ID is required" }, { status: 400 });
    }

    if (!["start", "stop", "restart"].includes(action)) {
      return NextResponse.json(
        { error: "Invalid action. Must be start, stop, or restart" },
        { status: 400 }
      );
    }

    // Get VM metadata
    const vmDir = `/tmp/microvms/${id}`;
    const metadataPath = path.join(vmDir, "metadata", "vm.json");

    if (!fs.existsSync(metadataPath)) {
      return NextResponse.json(
        { error: "VM metadata not found" },
        { status: 404 }
      );
    }

    // Read VM metadata
    const metadata = JSON.parse(fs.readFileSync(metadataPath, "utf-8"));
    const socketPath = metadata.socketPath;

    // Check if VM is running
    const isRunning = fs.existsSync(socketPath);

    switch (action) {
      case "start":
        if (isRunning) {
          return NextResponse.json({ message: "VM is already running" });
        }
        return await startVm(metadata);

      case "stop":
        if (!isRunning) {
          return NextResponse.json({ message: "VM is already stopped" });
        }
        return await stopVm(metadata);

      case "restart":
        if (isRunning) {
          await stopVm(metadata);
          // Wait briefly to ensure VM is stopped
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
        return await startVm(metadata);

      default:
        return NextResponse.json(
          { error: "Unsupported action" },
          { status: 400 }
        );
    }
  } catch (error) {
    return NextResponse.json(
      { 
        error: "Failed to process request", 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

/**
 * Start a VM
 */
async function startVm(metadata: any): Promise<NextResponse> {
  try {
    // Find firecracker binary
    const firecrackerPath = await findFirecrackerBinary();
    if (!firecrackerPath) {
      return NextResponse.json(
        { error: "Firecracker binary not found" },
        { status: 500 }
      );
    }

    // Ensure socket directory exists
    const socketPath = metadata.socketPath;
    const socketDir = path.dirname(socketPath);
    if (!fs.existsSync(socketDir)) {
      fs.mkdirSync(socketDir, { recursive: true });
    }

    // Remove socket file if it exists
    if (fs.existsSync(socketPath)) {
      fs.unlinkSync(socketPath);
    }

    // Start Firecracker process
    const fcProcess = spawn(firecrackerPath, ["--api-sock", socketPath], {
      detached: true,
      stdio: "ignore",
    });

    fcProcess.unref();

    // Wait for socket to be created
    await waitForSocket(socketPath);

    // Configure the VM
    await configureVm(socketPath, {
      kernelPath: metadata.kernelPath,
      bootArgs: metadata.bootArgs || "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp",
      rootfsPath: metadata.rootfsPath,
      memSizeMib: metadata.memSizeMib,
      vcpuCount: metadata.vcpuCount,
    });

    // Update status in metadata
    metadata.status = "running";
    metadata.startedAt = new Date().toISOString();
    fs.writeFileSync(
      path.join(path.dirname(socketPath), "metadata", "vm.json"),
      JSON.stringify(metadata, null, 2)
    );

    return NextResponse.json({
      success: true,
      message: "VM started successfully",
      id: metadata.id,
    });
  } catch (error) {
    return NextResponse.json(
      { 
        error: "Failed to start VM", 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

/**
 * Stop a VM
 */
async function stopVm(metadata: any): Promise<NextResponse> {
  try {
    const socketPath = metadata.socketPath;

    if (!fs.existsSync(socketPath)) {
      return NextResponse.json({ 
        message: "VM is not running or socket not found" 
      });
    }

    try {
      // Send action to shut down
      await execAsync(`curl --unix-socket ${socketPath} -X PUT http://localhost/actions -H "Content-Type: application/json" -d '{"action_type": "SendCtrlAltDel"}'`);
    } catch (error) {
      // If API call fails, try to terminate more forcefully
      try {
        // Find and kill Firecracker process
        const { stdout } = await execAsync(`lsof -n -U ${socketPath} | grep firecracker | awk '{print $2}'`);
        const pid = stdout.trim();
        
        if (pid) {
          await execAsync(`kill -TERM ${pid}`);
        }
      } catch (killError) {
        // If kill fails, just remove the socket
        console.error(`Failed to kill Firecracker process: ${killError}`);
      }
    }

    // Remove socket file
    if (fs.existsSync(socketPath)) {
      fs.unlinkSync(socketPath);
    }

    // Update status in metadata
    metadata.status = "stopped";
    metadata.stoppedAt = new Date().toISOString();
    fs.writeFileSync(
      path.join(path.dirname(socketPath), "metadata", "vm.json"),
      JSON.stringify(metadata, null, 2)
    );

    return NextResponse.json({
      success: true,
      message: "VM stopped successfully",
      id: metadata.id,
    });
  } catch (error) {
    return NextResponse.json(
      { 
        error: "Failed to stop VM", 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

/**
 * Find the Firecracker binary
 */
async function findFirecrackerBinary(): Promise<string | null> {
  const commonPaths = [
    "/usr/bin/firecracker",
    "/usr/local/bin/firecracker",
    "/opt/firecracker/firecracker",
  ];

  for (const path of commonPaths) {
    if (fs.existsSync(path)) {
      return path;
    }
  }

  try {
    // Try to find it in PATH
    const { stdout } = await execAsync("which firecracker");
    const path = stdout.trim();
    if (path && fs.existsSync(path)) {
      return path;
    }
  } catch (error) {
    // Ignore error
  }

  return null;
}

/**
 * Wait for socket file to be created
 */
async function waitForSocket(socketPath: string, timeoutMs: number = 5000): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeoutMs) {
    if (fs.existsSync(socketPath)) {
      // Wait a bit more to ensure Firecracker is ready
      await new Promise(resolve => setTimeout(resolve, 200));
      return;
    }
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  throw new Error(`Timed out waiting for socket: ${socketPath}`);
}

/**
 * Configure the VM through the Firecracker API
 */
async function configureVm(
  socketPath: string,
  config: {
    kernelPath: string;
    bootArgs: string;
    rootfsPath: string;
    memSizeMib: number;
    vcpuCount: number;
  }
): Promise<void> {
  // Helper function to send PUT requests
  const putRequest = async (path: string, body: any): Promise<void> => {
    const curl = `curl --unix-socket ${socketPath} -X PUT http://localhost${path} \
      -H "Content-Type: application/json" \
      -d '${JSON.stringify(body)}'`;

    try {
      await execAsync(curl);
    } catch (error) {
      console.error(`Error sending request to ${path}:`, error);
      throw new Error(`Failed to send request to ${path}`);
    }
  };

  // 1. Set boot source
  await putRequest("/boot-source", {
    kernel_image_path: config.kernelPath,
    boot_args: config.bootArgs,
  });

  // 2. Set rootfs drive
  await putRequest("/drives/rootfs", {
    drive_id: "rootfs",
    path_on_host: config.rootfsPath,
    is_root_device: true,
    is_read_only: false,
  });

  // 3. Set machine config
  await putRequest("/machine-config", {
    vcpu_count: config.vcpuCount,
    mem_size_mib: config.memSizeMib,
  });

  // 4. Start the VM
  await putRequest("/actions", { action_type: "InstanceStart" });
} 