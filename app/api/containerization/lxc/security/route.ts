import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { SecurityConfig, SecurityProfileType } from '@/lib/containerization/lxc/models/container-extended';
import { SecurityManager } from '@/lib/containerization/lxc/core/security-manager';

// Create SSH key schema
const createSshKeySchema = z.object({
  name: z.string().min(1).max(100),
  publicKey: z.string().min(1)
});

// Apply security config schema
const applySecurityConfigSchema = z.object({
  containerId: z.string(),
  profile: z.enum([
    SecurityProfileType.UNPRIVILEGED,
    SecurityProfileType.PRIVILEGED,
    SecurityProfileType.RESTRICTED,
    SecurityProfileType.CUSTOM
  ]),
  appArmor: z.string().optional(),
  seccomp: z.string().optional(),
  idmap: z.object({
    uidMap: z.array(z.string()).optional(),
    gidMap: z.array(z.string()).optional()
  }).optional(),
  capabilities: z.array(z.string()).optional(),
  sshKeys: z.array(z.string()).optional()
});

/**
 * GET handler for security operations
 * - List SSH keys: GET /api/containerization/lxc/security/ssh-keys
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse URL
    const url = new URL(request.url);
    const path = url.pathname.split('/');
    const resource = path[path.length - 1];

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Create security manager
    const lxcManager = containerManager.getLxcManager();
    const securityManager = new SecurityManager(lxcManager.api);

    if (resource === 'ssh-keys') {
      // Get SSH keys for user
      const sshKeys = await securityManager.getSshKeysForUser(session.user.id);
      return NextResponse.json({ sshKeys });
    }

    return NextResponse.json({ error: 'Invalid resource' }, { status: 400 });
  } catch (error: any) {
    console.error('Error in LXC security API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for security operations
 * - Create SSH key: POST /api/containerization/lxc/security/ssh-keys
 * - Apply security config: POST /api/containerization/lxc/security/apply
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse URL
    const url = new URL(request.url);
    const path = url.pathname.split('/');
    const resource = path[path.length - 1];

    // Parse request body
    const body = await request.json();

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Create security manager
    const lxcManager = containerManager.getLxcManager();
    const securityManager = new SecurityManager(lxcManager.api);

    if (resource === 'ssh-keys') {
      // Validate request body
      const validationResult = createSshKeySchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: 'Invalid request body', details: validationResult.error.format() },
          { status: 400 }
        );
      }

      // Create SSH key
      const sshKey = await securityManager.addSshKey(
        session.user.id,
        validationResult.data.name,
        validationResult.data.publicKey
      );

      return NextResponse.json({ sshKey }, { status: 201 });
    } else if (resource === 'apply') {
      // Validate request body
      const validationResult = applySecurityConfigSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: 'Invalid request body', details: validationResult.error.format() },
          { status: 400 }
        );
      }

      // Get container
      const container = await prisma.lxcContainer.findUnique({
        where: { id: validationResult.data.containerId }
      });

      if (!container) {
        return NextResponse.json({ error: 'Container not found' }, { status: 404 });
      }

      // Check if user has permission to modify container
      if (container.userId !== session.user.id && session.user.role !== 'admin') {
        return NextResponse.json({ error: 'Unauthorized to modify this container' }, { status: 403 });
      }

      // Get SSH keys if provided
      let sshKeys = [];
      if (validationResult.data.sshKeys && validationResult.data.sshKeys.length > 0) {
        const keys = await prisma.lxcAccessLog.findMany({
          where: { id: { in: validationResult.data.sshKeys } }
        });
        
        // This is a placeholder - in a real implementation, you would fetch the actual SSH keys
        sshKeys = validationResult.data.sshKeys.map(id => ({
          id,
          name: `Key ${id}`,
          publicKey: 'ssh-rsa AAAAB3NzaC1yc2E...',
          userId: session.user.id,
          createdAt: new Date()
        }));
      }

      // Create security config
      const securityConfig: SecurityConfig = {
        profile: validationResult.data.profile,
        appArmor: validationResult.data.appArmor,
        seccomp: validationResult.data.seccomp,
        idmap: validationResult.data.idmap,
        capabilities: validationResult.data.capabilities,
        sshKeys
      };

      // Apply security config
      await securityManager.applySecurityConfig(container.name, securityConfig);

      // Update container in database
      await prisma.lxcContainer.update({
        where: { id: container.id },
        data: {
          securityProfile: validationResult.data.profile
        }
      });

      // Log access
      await prisma.lxcAccessLog.create({
        data: {
          containerId: container.id,
          userId: session.user.id,
          action: 'security-change',
          details: { securityConfig },
          timestamp: new Date()
        }
      });

      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Invalid resource' }, { status: 400 });
  } catch (error: any) {
    console.error('Error in LXC security API (POST):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * DELETE handler for security operations
 * - Delete SSH key: DELETE /api/containerization/lxc/security/ssh-keys/:id
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse URL
    const url = new URL(request.url);
    const path = url.pathname.split('/');
    const resource = path[path.length - 2];
    const id = path[path.length - 1];

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Create security manager
    const lxcManager = containerManager.getLxcManager();
    const securityManager = new SecurityManager(lxcManager.api);

    if (resource === 'ssh-keys') {
      // Delete SSH key
      await securityManager.removeSshKey(id);
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Invalid resource' }, { status: 400 });
  } catch (error: any) {
    console.error('Error in LXC security API (DELETE):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
