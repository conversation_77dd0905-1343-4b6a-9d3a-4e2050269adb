import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { SecurityManager } from '@/lib/containerization/lxc/core/security-manager';

/**
 * GET handler for SSH key operations
 * - Get SSH key: GET /api/containerization/lxc/security/ssh-keys/:id
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Create security manager
    const lxcManager = containerManager.getLxcManager();
    const securityManager = new SecurityManager(lxcManager.api);

    // Get SSH keys for user
    const sshKeys = await securityManager.getSshKeysForUser(session.user.id);
    
    // Find the requested SSH key
    const sshKey = sshKeys.find(key => key.id === id);
    
    if (!sshKey) {
      return NextResponse.json({ error: 'SSH key not found' }, { status: 404 });
    }

    return NextResponse.json({ sshKey });
  } catch (error: any) {
    console.error('Error in LXC SSH key API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * DELETE handler for SSH key operations
 * - Delete SSH key: DELETE /api/containerization/lxc/security/ssh-keys/:id
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Create security manager
    const lxcManager = containerManager.getLxcManager();
    const securityManager = new SecurityManager(lxcManager.api);

    // Get SSH keys for user
    const sshKeys = await securityManager.getSshKeysForUser(session.user.id);
    
    // Find the requested SSH key
    const sshKey = sshKeys.find(key => key.id === id);
    
    if (!sshKey) {
      return NextResponse.json({ error: 'SSH key not found' }, { status: 404 });
    }

    // Check if the SSH key belongs to the user
    if (sshKey.userId !== session.user.id && session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized to delete this SSH key' }, { status: 403 });
    }

    // Delete SSH key
    await securityManager.removeSshKey(id);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in LXC SSH key API (DELETE):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
