/**
 * API routes for LXC container management
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { ContainerConfig, CreateContainerOptions, NetworkType } from '@/lib/containerization/lxc/models/container';
import { z } from 'zod';

// Validation schema for creating a container
const createContainerSchema = z.object({
  name: z.string().min(1, "Container name is required"),
  template: z.string().min(1, "Template name is required"),
  release: z.string().optional(),
  arch: z.string().optional(),
  variant: z.string().optional(),
  projectId: z.string().optional().describe("Project ID to associate with this container"),
  config: z.object({
    autostart: z.boolean().optional(),
    networks: z.array(
      z.object({
        name: z.string(),
        type: z.enum([
          NetworkType.VETH,
          NetworkType.MACVLAN,
          NetworkType.IPVLAN,
          NetworkType.PHYS,
          NetworkType.EMPTY,
          NetworkType.NONE
        ]),
        link: z.string().optional(),
        flags: z.string().optional(),
        hwaddr: z.string().optional(),
        ipv4: z.string().optional(),
        ipv4Gateway: z.string().optional(),
        ipv6: z.string().optional(),
        ipv6Gateway: z.string().optional(),
        mtu: z.number().optional(),
      })
    ).optional(),
    resources: z.object({
      cpu: z.object({
        shares: z.number().optional(),
        quota: z.number().optional(),
        period: z.number().optional(),
        cores: z.string().optional(),
      }).optional(),
      memory: z.object({
        limit: z.number().optional(),
        reservation: z.number().optional(),
        swap: z.number().optional(),
        swappiness: z.number().optional(),
      }).optional(),
      disk: z.object({
        iops: z.number().optional(),
        bps: z.number().optional(),
      }).optional(),
    }).optional(),
    environment: z.record(z.string()).optional(),
  }).optional(),
});

/**
 * GET handler for container operations
 * - List containers: GET /api/containerization/lxc/containers
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('projectId');

    // Ensure container manager is initialized
    await initializeContainerManager();

    // List containers
    const containers = containerManager.listContainers();

    // If containers is undefined or not an array, return an empty array
    if (!containers || !Array.isArray(containers)) {
      console.warn('Container manager returned invalid containers list');
      return NextResponse.json({ containers: [] });
    }

    // Map containers to a simpler format for the API
    const containerList = containers.map(container => ({
      id: container.id,
      name: container.name,
      state: container.state,
      template: container.config.template.name,
      release: container.config.template.release,
      arch: container.config.template.arch,
      variant: container.config.template.variant,
      autostart: container.config.autostart,
      projectId: container.config.environment?.PROJECT_ID || null,
    }));

    // Filter by projectId if provided
    const filteredContainers = projectId
      ? containerList.filter(container => container.projectId === projectId)
      : containerList;

    return NextResponse.json({ containers: filteredContainers });
  } catch (error: any) {
    console.error('Error in LXC containers API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for container operations
 * - Create container: POST /api/containerization/lxc/containers
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = createContainerSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Create container options
    const { name, template, release, arch, variant, config, projectId } = validationResult.data;
    
    const options: CreateContainerOptions = {
      name,
      template,
      release,
      arch,
      variant,
      config: config ? { ...config } : undefined
    };

    // Add projectId to environment if provided
    if (projectId) {
      if (!options.config) options.config = {};
      if (!options.config.environment) options.config.environment = {};
      options.config.environment.PROJECT_ID = projectId;
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Create container
    const container = await containerManager.createContainer(options);

    return NextResponse.json({
      id: container.id,
      name: container.name,
      state: container.state,
      template: container.config.template.name,
      release: container.config.template.release,
      arch: container.config.template.arch,
      variant: container.config.template.variant,
      autostart: container.config.autostart,
      projectId: container.config.environment?.PROJECT_ID || null,
    }, { status: 201 });
  } catch (error: any) {
    console.error('Error in LXC containers API (POST):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
