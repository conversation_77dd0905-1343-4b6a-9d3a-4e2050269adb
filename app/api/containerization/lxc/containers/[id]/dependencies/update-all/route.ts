/**
 * API routes for updating all LXC container dependencies
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';

// Validation schema for updating all dependencies
const updateAllDependenciesSchema = z.object({
  projectId: z.string().optional(),
  path: z.string().default('/app'),
});

/**
 * POST handler for updating all dependencies
 * - Update all dependencies: POST /api/containerization/lxc/containers/:id/dependencies/update-all
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = updateAllDependenciesSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: projectPath } = validationResult.data;

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Update all dependencies
    const command = ['npm', 'update'];
    
    const result = await containerOps.executeCommand(container.name, command, { cwd: projectPath });
    
    if (result.exitCode !== 0) {
      throw new Error(`Failed to update dependencies: ${result.stderr}`);
    }

    return NextResponse.json({ 
      success: true,
      output: result.stdout
    });
  } catch (error: any) {
    console.error('Error in LXC dependencies update-all API (POST):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
