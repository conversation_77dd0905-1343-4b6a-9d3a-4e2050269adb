/**
 * API routes for updating LXC container dependencies
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';

// Validation schema for updating dependencies
const updateDependencySchema = z.object({
  projectId: z.string().optional(),
  path: z.string().default('/app'),
  name: z.string().min(1, "Package name is required"),
  type: z.enum(['dependency', 'devDependency']).default('dependency'),
});

/**
 * POST handler for updating dependencies
 * - Update dependency: POST /api/containerization/lxc/containers/:id/dependencies/update
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = updateDependencySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: projectPath, name, type } = validationResult.data;

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Update dependency
    const saveFlag = type === 'dependency' ? '--save' : '--save-dev';
    const command = ['npm', 'update', name, saveFlag];
    
    const result = await containerOps.executeCommand(container.name, command, { cwd: projectPath });
    
    if (result.exitCode !== 0) {
      throw new Error(`Failed to update dependency: ${result.stderr}`);
    }

    return NextResponse.json({ 
      success: true,
      output: result.stdout
    });
  } catch (error: any) {
    console.error('Error in LXC dependencies update API (POST):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
