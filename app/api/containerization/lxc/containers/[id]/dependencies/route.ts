/**
 * API routes for LXC container dependencies
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';

// Validation schema for dependencies
const installDependencySchema = z.object({
  projectId: z.string().optional(),
  path: z.string().default('/app'),
  name: z.string().min(1, "Package name is required"),
  version: z.string().optional(),
  type: z.enum(['dependency', 'devDependency']).default('dependency'),
});

const removeDependencySchema = z.object({
  projectId: z.string().optional(),
  path: z.string().default('/app'),
  name: z.string().min(1, "Package name is required"),
  type: z.enum(['dependency', 'devDependency']).default('dependency'),
});

/**
 * GET handler for dependencies
 * - Get dependencies: GET /api/containerization/lxc/containers/:id/dependencies?path=/app
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const projectPath = url.searchParams.get('path') || '/app';

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Check if package.json exists
    try {
      const packageJsonPath = `${projectPath}/package.json`;
      const packageJsonContent = await containerOps.readFile(container.name, packageJsonPath);
      const packageJson = JSON.parse(packageJsonContent);

      // Get installed dependencies
      const command = ['npm', 'list', '--json', '--depth=0'];
      const result = await containerOps.executeCommand(container.name, command, { cwd: projectPath });
      
      if (result.exitCode !== 0) {
        throw new Error(`Failed to get dependencies: ${result.stderr}`);
      }

      const npmList = JSON.parse(result.stdout);
      
      // Get outdated dependencies
      const outdatedCommand = ['npm', 'outdated', '--json'];
      const outdatedResult = await containerOps.executeCommand(container.name, outdatedCommand, { cwd: projectPath });
      
      // Parse outdated dependencies (npm outdated returns non-zero exit code when there are outdated packages)
      let outdated = {};
      if (outdatedResult.stdout) {
        try {
          outdated = JSON.parse(outdatedResult.stdout);
        } catch (e) {
          console.error('Failed to parse outdated dependencies:', e);
        }
      }

      // Format dependencies
      const dependencies = formatDependencies(packageJson, npmList, outdated);
      
      return NextResponse.json({ dependencies });
    } catch (error) {
      // If package.json doesn't exist or npm list fails
      return NextResponse.json({ dependencies: [] });
    }
  } catch (error: any) {
    console.error('Error in LXC dependencies API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for dependencies
 * - Install dependency: POST /api/containerization/lxc/containers/:id/dependencies
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = installDependencySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: projectPath, name, version, type } = validationResult.data;

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Install dependency
    const saveFlag = type === 'dependency' ? '--save' : '--save-dev';
    const versionStr = version ? `@${version}` : '';
    const command = ['npm', 'install', `${name}${versionStr}`, saveFlag];
    
    const result = await containerOps.executeCommand(container.name, command, { cwd: projectPath });
    
    if (result.exitCode !== 0) {
      throw new Error(`Failed to install dependency: ${result.stderr}`);
    }

    return NextResponse.json({ 
      success: true,
      output: result.stdout
    });
  } catch (error: any) {
    console.error('Error in LXC dependencies API (POST):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * DELETE handler for dependencies
 * - Remove dependency: DELETE /api/containerization/lxc/containers/:id/dependencies
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = removeDependencySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: projectPath, name, type } = validationResult.data;

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Remove dependency
    const saveFlag = type === 'dependency' ? '--save' : '--save-dev';
    const command = ['npm', 'uninstall', name, saveFlag];
    
    const result = await containerOps.executeCommand(container.name, command, { cwd: projectPath });
    
    if (result.exitCode !== 0) {
      throw new Error(`Failed to remove dependency: ${result.stderr}`);
    }

    return NextResponse.json({ 
      success: true,
      output: result.stdout
    });
  } catch (error: any) {
    console.error('Error in LXC dependencies API (DELETE):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * Format dependencies
 */
function formatDependencies(packageJson: any, npmList: any, outdated: any) {
  const dependencies: any[] = [];
  const deps = packageJson.dependencies || {};
  const devDeps = packageJson.devDependencies || {};
  const installedDeps = npmList.dependencies || {};

  // Process regular dependencies
  Object.keys(deps).forEach(name => {
    const installed = installedDeps[name];
    const outdatedInfo = outdated[name];
    
    dependencies.push({
      name,
      version: deps[name],
      installedVersion: installed ? installed.version : undefined,
      latest: outdatedInfo ? outdatedInfo.latest : undefined,
      outdated: !!outdatedInfo,
      type: 'dependency'
    });
  });

  // Process dev dependencies
  Object.keys(devDeps).forEach(name => {
    const installed = installedDeps[name];
    const outdatedInfo = outdated[name];
    
    dependencies.push({
      name,
      version: devDeps[name],
      installedVersion: installed ? installed.version : undefined,
      latest: outdatedInfo ? outdatedInfo.latest : undefined,
      outdated: !!outdatedInfo,
      type: 'devDependency'
    });
  });

  return dependencies;
}
