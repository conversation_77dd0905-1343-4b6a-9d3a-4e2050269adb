/**
 * LXC Container Exec API Route
 */

import { NextRequest, NextResponse } from 'next/server';
import { executeCommand } from '../../../controller';

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * POST /api/containerization/lxc/containers/[id]/exec
 * Execute command in container
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  return executeCommand(params.id, request);
}
