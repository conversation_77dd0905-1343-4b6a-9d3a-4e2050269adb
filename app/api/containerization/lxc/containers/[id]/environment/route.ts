/**
 * API routes for LXC container environment variables
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';

// Validation schema for environment variables
const environmentVariableSchema = z.object({
  key: z.string().min(1, "Key is required"),
  value: z.string(),
  isSecret: z.boolean().optional(),
  description: z.string().optional(),
});

const environmentSchema = z.object({
  projectId: z.string().optional(),
  path: z.string().default('/.env'),
  variables: z.array(environmentVariableSchema),
});

/**
 * GET handler for environment variables
 * - Get environment variables: GET /api/containerization/lxc/containers/:id/environment?path=/.env
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const envPath = url.searchParams.get('path') || '/.env';

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    try {
      // Read environment file
      const content = await containerOps.readFile(container.name, envPath);
      
      // Parse environment variables
      const variables = parseEnvFile(content);
      
      return NextResponse.json({ variables });
    } catch (error) {
      // If file doesn't exist, return empty array
      return NextResponse.json({ variables: [] });
    }
  } catch (error: any) {
    console.error('Error in LXC environment API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * PUT handler for environment variables
 * - Update environment variables: PUT /api/containerization/lxc/containers/:id/environment
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = environmentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: envPath, variables } = validationResult.data;

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Generate environment file content
    const content = generateEnvFile(variables);

    // Write environment file
    await containerOps.writeFile(container.name, envPath, content);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in LXC environment API (PUT):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * Parse environment file content into variables
 */
function parseEnvFile(content: string) {
  const variables: any[] = [];
  const lines = content.split('\n');

  for (const line of lines) {
    // Skip comments and empty lines
    if (line.trim().startsWith('#') || !line.trim()) continue;

    // Parse key=value format
    const match = line.match(/^([^=]+)=(.*)$/);
    if (match) {
      const [, key, value] = match;
      variables.push({
        key: key.trim(),
        value: value.trim(),
        isSecret: key.toLowerCase().includes('secret') || 
                 key.toLowerCase().includes('password') || 
                 key.toLowerCase().includes('token')
      });
    }
  }

  return variables;
}

/**
 * Generate environment file content from variables
 */
function generateEnvFile(variables: any[]) {
  return variables.map(v => `${v.key}=${v.value}`).join('\n');
}
