/**
 * API route for starting an LXC container
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';

/**
 * PUT handler for starting a container
 * - Start container: PUT /api/containerization/lxc/containers/:id/start
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Start container
    await containerManager.startContainer(id);

    return NextResponse.json({
      id: container.id,
      name: container.name,
      state: 'RUNNING',
    });
  } catch (error: any) {
    console.error(`Error in LXC container API (PUT ${params.id}/start):`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
