/**
 * API routes for LXC container directory operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';

// Validation schema for directory operations
const directorySchema = z.object({
  path: z.string().min(1, "Path is required"),
  mode: z.string().optional(),
});

/**
 * PUT handler for directory operations
 * - Create directory: PUT /api/containerization/lxc/containers/:id/files/directory
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = directorySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: dirPath, mode } = validationResult.data;

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Create directory
    await containerOps.createDirectory(container.name, dirPath, mode);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in LXC directory API (PUT):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
