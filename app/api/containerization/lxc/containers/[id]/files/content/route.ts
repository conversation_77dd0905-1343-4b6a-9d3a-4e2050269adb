/**
 * API routes for LXC container file content operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';

// Validation schema for file content operations
const fileContentSchema = z.object({
  path: z.string().min(1, "Path is required"),
  content: z.string(),
});

/**
 * GET handler for file content operations
 * - Get file content: GET /api/containerization/lxc/containers/:id/files/content?path=/path/to/file
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const filePath = url.searchParams.get('path');

    if (!filePath) {
      return NextResponse.json({ error: 'Path is required' }, { status: 400 });
    }

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Read file content
    const content = await containerOps.readFile(container.name, filePath);

    return NextResponse.json({ content });
  } catch (error: any) {
    console.error('Error in LXC file content API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * PUT handler for file content operations
 * - Write file content: PUT /api/containerization/lxc/containers/:id/files/content
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = fileContentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: filePath, content } = validationResult.data;

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Write file content
    await containerOps.writeFile(container.name, filePath, content);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in LXC file content API (PUT):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
