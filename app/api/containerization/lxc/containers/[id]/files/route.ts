/**
 * API routes for LXC container file operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';
import path from 'path';

// Validation schema for file operations
const fileOperationSchema = z.object({
  path: z.string().min(1, "Path is required"),
  recursive: z.boolean().optional().default(false),
});

/**
 * GET handler for file operations
 * - List files: GET /api/containerization/lxc/containers/:id/files?path=/path/to/dir
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const filePath = url.searchParams.get('path') || '/';

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // List files
    const files = await containerOps.listFiles(container.name, filePath);

    return NextResponse.json({ files });
  } catch (error: any) {
    console.error('Error in LXC files API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * DELETE handler for file operations
 * - Delete file: DELETE /api/containerization/lxc/containers/:id/files
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = fileOperationSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: filePath, recursive } = validationResult.data;

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Delete file or directory
    await containerOps.deleteFile(container.name, filePath, recursive);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in LXC files API (DELETE):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
