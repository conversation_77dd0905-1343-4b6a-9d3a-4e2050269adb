/**
 * API route for container terminal access
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';

/**
 * GET handler for container terminal
 * - Get terminal URL: GET /api/containerization/lxc/containers/{id}/terminal
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Check if container is running
    if (!container.isRunning()) {
      return NextResponse.json({ error: 'Container is not running' }, { status: 400 });
    }

    // For now, return a placeholder URL
    // In a real implementation, this would return a URL to a terminal service
    // like ttyd, wetty, or a custom terminal implementation
    const terminalUrl = `/terminal/${id}`;

    return NextResponse.json({ url: terminalUrl });
  } catch (error: any) {
    console.error('Error in container terminal API:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
