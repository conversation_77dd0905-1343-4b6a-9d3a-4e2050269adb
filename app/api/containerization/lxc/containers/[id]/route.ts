/**
 * API routes for individual LXC container operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';

/**
 * GET handler for individual container operations
 * - Get container details: GET /api/containerization/lxc/containers/:id
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get container info
    const info = await containerManager.getContainerInfo(id);

    return NextResponse.json({
      id: container.id,
      name: container.name,
      state: container.state,
      template: container.config.template.name,
      release: container.config.template.release,
      arch: container.config.template.arch,
      variant: container.config.template.variant,
      autostart: container.config.autostart,
      networks: container.config.networks,
      resources: container.config.resources,
      environment: container.config.environment,
      info: {
        pid: info.pid,
        ip: info.ip,
        memory: info.memory,
        cpu: info.cpu,
        network: info.network,
        created: info.created,
      },
    });
  } catch (error: any) {
    console.error(`Error in LXC container API (GET ${params.id}):`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * DELETE handler for individual container operations
 * - Delete container: DELETE /api/containerization/lxc/containers/:id
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Delete container
    await containerManager.deleteContainer(id);

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error(`Error in LXC container API (DELETE ${params.id}):`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
