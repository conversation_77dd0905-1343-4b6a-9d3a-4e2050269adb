/**
 * API routes for LXC container project sync
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// Validation schema for sync operations
const syncSchema = z.object({
  projectId: z.string().min(1, "Project ID is required"),
  direction: z.enum(['toContainer', 'fromContainer']),
  paths: z.array(z.string()).min(1, "At least one path is required"),
  ignorePatterns: z.array(z.string()).optional(),
});

/**
 * GET handler for sync operations
 * - Get sync status: GET /api/containerization/lxc/containers/:id/sync?projectId=123&path=/app
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const projectId = url.searchParams.get('projectId');
    const containerPath = url.searchParams.get('path') || '/app';

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    // Get project
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    });

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Get local project path
    const localProjectPath = path.join(process.env.PROJECTS_DIR || '/tmp/projects', projectId);

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Get sync items
    const syncItems = await getSyncItems(containerOps, container.name, localProjectPath, containerPath);

    // Get last sync time
    const lastSync = await prisma.projectSync.findFirst({
      where: {
        projectId,
        containerId: id
      },
      orderBy: {
        syncedAt: 'desc'
      }
    });

    return NextResponse.json({
      items: syncItems,
      lastSync: lastSync?.syncedAt
    });
  } catch (error: any) {
    console.error('Error in LXC sync API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for sync operations
 * - Sync files: POST /api/containerization/lxc/containers/:id/sync
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = syncSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { projectId, direction, paths, ignorePatterns } = validationResult.data;

    // Get project
    const project = await prisma.project.findUnique({
      where: { id: projectId }
    });

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Get local project path
    const localProjectPath = path.join(process.env.PROJECTS_DIR || '/tmp/projects', projectId);
    const containerPath = '/app';

    // Get container operations
    const containerOps = containerManager.getContainerOperations();

    // Sync files
    if (direction === 'toContainer') {
      await syncToContainer(containerOps, container.name, localProjectPath, containerPath, paths, ignorePatterns);
    } else {
      await syncFromContainer(containerOps, container.name, localProjectPath, containerPath, paths, ignorePatterns);
    }

    // Record sync
    await prisma.projectSync.create({
      data: {
        projectId,
        containerId: id,
        direction,
        syncedAt: new Date(),
        syncedBy: session.user.id
      }
    });

    // Get updated sync items
    const syncItems = await getSyncItems(containerOps, container.name, localProjectPath, containerPath);

    return NextResponse.json({
      success: true,
      items: syncItems
    });
  } catch (error: any) {
    console.error('Error in LXC sync API (POST):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * Get sync items
 */
async function getSyncItems(containerOps: any, containerName: string, localPath: string, containerPath: string) {
  // Get local files
  const localFiles = await getFilesRecursive(localPath);
  
  // Get container files
  const containerFiles = await getContainerFilesRecursive(containerOps, containerName, containerPath);
  
  // Compare files
  const syncItems = [];
  
  // Check local files
  for (const file of localFiles) {
    const relativePath = path.relative(localPath, file.path);
    const containerFilePath = path.join(containerPath, relativePath);
    
    const containerFile = containerFiles.find(f => f.path === containerFilePath);
    
    if (!containerFile) {
      // File exists locally but not in container
      syncItems.push({
        path: relativePath,
        type: file.type,
        status: 'new',
        lastModified: file.lastModified,
        size: file.size
      });
    } else if (file.type === 'file' && containerFile.type === 'file') {
      // File exists in both places, check if modified
      const localContent = await readFile(file.path, 'utf8');
      const containerContent = await containerOps.readFile(containerName, containerFile.path);
      
      if (localContent !== containerContent) {
        syncItems.push({
          path: relativePath,
          type: file.type,
          status: 'modified',
          lastModified: file.lastModified,
          size: file.size
        });
      } else {
        syncItems.push({
          path: relativePath,
          type: file.type,
          status: 'unchanged',
          lastModified: file.lastModified,
          size: file.size
        });
      }
    } else {
      // Directory exists in both places
      syncItems.push({
        path: relativePath,
        type: file.type,
        status: 'unchanged',
        lastModified: file.lastModified
      });
    }
  }
  
  // Check container files that don't exist locally
  for (const file of containerFiles) {
    const relativePath = path.relative(containerPath, file.path);
    const localFilePath = path.join(localPath, relativePath);
    
    const localFile = localFiles.find(f => f.path === localFilePath);
    
    if (!localFile) {
      // File exists in container but not locally
      syncItems.push({
        path: relativePath,
        type: file.type,
        status: 'deleted',
        lastModified: file.lastModified,
        size: file.size
      });
    }
  }
  
  return syncItems;
}

/**
 * Sync files to container
 */
async function syncToContainer(containerOps: any, containerName: string, localPath: string, containerPath: string, paths: string[], ignorePatterns: string[] = []) {
  for (const relativePath of paths) {
    const localFilePath = path.join(localPath, relativePath);
    const containerFilePath = path.join(containerPath, relativePath);
    
    // Check if path should be ignored
    if (shouldIgnore(relativePath, ignorePatterns)) {
      continue;
    }
    
    // Check if path exists locally
    try {
      const stats = await stat(localFilePath);
      
      if (stats.isDirectory()) {
        // Create directory in container
        await containerOps.createDirectory(containerName, containerFilePath);
        
        // Recursively sync directory contents
        const files = await readdir(localFilePath);
        
        for (const file of files) {
          const childRelativePath = path.join(relativePath, file);
          
          // Skip ignored files
          if (shouldIgnore(childRelativePath, ignorePatterns)) {
            continue;
          }
          
          await syncToContainer(containerOps, containerName, localPath, containerPath, [childRelativePath], ignorePatterns);
        }
      } else {
        // Copy file to container
        const content = await readFile(localFilePath, 'utf8');
        await containerOps.writeFile(containerName, containerFilePath, content);
      }
    } catch (error) {
      console.error(`Error syncing ${relativePath} to container:`, error);
    }
  }
}

/**
 * Sync files from container
 */
async function syncFromContainer(containerOps: any, containerName: string, localPath: string, containerPath: string, paths: string[], ignorePatterns: string[] = []) {
  for (const relativePath of paths) {
    const localFilePath = path.join(localPath, relativePath);
    const containerFilePath = path.join(containerPath, relativePath);
    
    // Check if path should be ignored
    if (shouldIgnore(relativePath, ignorePatterns)) {
      continue;
    }
    
    try {
      // Get file info from container
      const files = await containerOps.listFiles(containerName, path.dirname(containerFilePath));
      const file = files.find(f => f.path === containerFilePath);
      
      if (!file) {
        console.error(`File not found in container: ${containerFilePath}`);
        continue;
      }
      
      if (file.type === 'directory') {
        // Create directory locally
        await mkdir(localFilePath, { recursive: true });
        
        // Recursively sync directory contents
        const containerDirFiles = await containerOps.listFiles(containerName, containerFilePath);
        
        for (const childFile of containerDirFiles) {
          const childRelativePath = path.relative(containerPath, childFile.path);
          
          // Skip ignored files
          if (shouldIgnore(childRelativePath, ignorePatterns)) {
            continue;
          }
          
          await syncFromContainer(containerOps, containerName, localPath, containerPath, [childRelativePath], ignorePatterns);
        }
      } else {
        // Create parent directory if it doesn't exist
        await mkdir(path.dirname(localFilePath), { recursive: true });
        
        // Copy file from container
        const content = await containerOps.readFile(containerName, containerFilePath);
        await writeFile(localFilePath, content);
      }
    } catch (error) {
      console.error(`Error syncing ${relativePath} from container:`, error);
    }
  }
}

/**
 * Get files recursively
 */
async function getFilesRecursive(dir: string) {
  const files: any[] = [];
  
  try {
    const entries = await readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const entryPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        files.push({
          path: entryPath,
          type: 'directory',
          lastModified: (await stat(entryPath)).mtime.toISOString()
        });
        
        const childFiles = await getFilesRecursive(entryPath);
        files.push(...childFiles);
      } else {
        const stats = await stat(entryPath);
        
        files.push({
          path: entryPath,
          type: 'file',
          lastModified: stats.mtime.toISOString(),
          size: stats.size
        });
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error);
  }
  
  return files;
}

/**
 * Get container files recursively
 */
async function getContainerFilesRecursive(containerOps: any, containerName: string, dir: string) {
  const files: any[] = [];
  
  try {
    const entries = await containerOps.listFiles(containerName, dir);
    
    for (const entry of entries) {
      if (entry.type === 'directory') {
        files.push(entry);
        
        const childFiles = await getContainerFilesRecursive(containerOps, containerName, entry.path);
        files.push(...childFiles);
      } else {
        files.push(entry);
      }
    }
  } catch (error) {
    console.error(`Error reading container directory ${dir}:`, error);
  }
  
  return files;
}

/**
 * Check if path should be ignored
 */
function shouldIgnore(filePath: string, ignorePatterns: string[]) {
  if (!ignorePatterns || ignorePatterns.length === 0) {
    return false;
  }
  
  return ignorePatterns.some(pattern => {
    // Simple glob pattern matching
    if (pattern.includes('*')) {
      const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
      return regex.test(filePath);
    }
    
    // Direct path matching
    return filePath === pattern || filePath.startsWith(pattern + '/');
  });
}
