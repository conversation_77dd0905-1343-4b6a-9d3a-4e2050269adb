/**
 * API route for executing commands in an LXC container
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';

// Validation schema for executing a command
const executeCommandSchema = z.object({
  command: z.array(z.string()).min(1, "Command is required"),
  env: z.record(z.string()).optional(),
  cwd: z.string().optional(),
  user: z.string().optional(),
  timeout: z.number().optional(),
});

/**
 * POST handler for executing a command in a container
 * - Execute command: POST /api/containerization/lxc/containers/:id/execute
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { id } = params;

    // Get container
    const container = containerManager.getContainer(id);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = executeCommandSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { command, env, cwd, user, timeout } = validationResult.data;

    // Execute command
    const result = await containerManager.getContainerOperations().executeCommand(
      container.name,
      command,
      { env, cwd, user, timeout }
    );

    return NextResponse.json({
      stdout: result.stdout,
      stderr: result.stderr,
      exitCode: result.exitCode,
    });
  } catch (error: any) {
    console.error(`Error in LXC container API (POST ${params.id}/execute):`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
