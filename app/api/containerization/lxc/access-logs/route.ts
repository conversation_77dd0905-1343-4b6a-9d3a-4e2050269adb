import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { AccessActionType } from '@/lib/containerization/lxc/models/access-log';

// Query access logs schema
const queryAccessLogsSchema = z.object({
  containerId: z.string().optional(),
  userId: z.string().optional(),
  action: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  success: z.boolean().optional(),
  limit: z.number().optional(),
  offset: z.number().optional(),
  sortBy: z.enum(['timestamp', 'action', 'userId']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

/**
 * GET handler for access logs operations
 * - List access logs: GET /api/containerization/lxc/access-logs
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams: Record<string, any> = {};
    url.searchParams.forEach((value, key) => {
      // Handle boolean values
      if (value === 'true') queryParams[key] = true;
      else if (value === 'false') queryParams[key] = false;
      // Handle numeric values
      else if (!isNaN(Number(value))) queryParams[key] = Number(value);
      // Handle string values
      else queryParams[key] = value;
    });

    // Validate query parameters
    const validationResult = queryAccessLogsSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Build query
    const query: any = {};
    
    if (validationResult.data.containerId) {
      query.containerId = validationResult.data.containerId;
    }
    
    if (validationResult.data.userId) {
      query.userId = validationResult.data.userId;
    }
    
    if (validationResult.data.action) {
      query.action = validationResult.data.action;
    }
    
    if (validationResult.data.startDate || validationResult.data.endDate) {
      query.timestamp = {};
      
      if (validationResult.data.startDate) {
        query.timestamp.gte = new Date(validationResult.data.startDate);
      }
      
      if (validationResult.data.endDate) {
        query.timestamp.lte = new Date(validationResult.data.endDate);
      }
    }

    // Check if user is admin
    const isAdmin = session.user.role === 'admin';
    
    // If not admin, restrict to user's own logs or containers they own
    if (!isAdmin) {
      // Get containers owned by the user
      const userContainers = await prisma.lxcContainer.findMany({
        where: { userId: session.user.id },
        select: { id: true }
      });
      
      const containerIds = userContainers.map(container => container.id);
      
      // User can see their own access logs or logs for containers they own
      query.OR = [
        { userId: session.user.id },
        { containerId: { in: containerIds } }
      ];
    }

    // Get access logs
    const accessLogs = await prisma.lxcAccessLog.findMany({
      where: query,
      orderBy: validationResult.data.sortBy === 'action' ? { action: validationResult.data.sortOrder || 'desc' } :
               validationResult.data.sortBy === 'userId' ? { userId: validationResult.data.sortOrder || 'asc' } :
               { timestamp: validationResult.data.sortOrder || 'desc' },
      take: validationResult.data.limit || 100,
      skip: validationResult.data.offset || 0,
      include: {
        container: {
          select: {
            name: true,
            state: true
          }
        },
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({ accessLogs });
  } catch (error: any) {
    console.error('Error in LXC access logs API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for access logs operations
 * - Create access log: POST /api/containerization/lxc/access-logs
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const createAccessLogSchema = z.object({
      containerId: z.string(),
      action: z.string(),
      ipAddress: z.string().optional(),
      userAgent: z.string().optional(),
      details: z.record(z.any()).optional(),
      success: z.boolean().default(true),
      errorMessage: z.string().optional()
    });

    const validationResult = createAccessLogSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Check if container exists
    const container = await prisma.lxcContainer.findUnique({
      where: { id: validationResult.data.containerId }
    });

    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Create access log
    const accessLog = await prisma.lxcAccessLog.create({
      data: {
        containerId: validationResult.data.containerId,
        userId: session.user.id,
        action: validationResult.data.action,
        ipAddress: validationResult.data.ipAddress,
        userAgent: validationResult.data.userAgent,
        details: validationResult.data.details || {},
        timestamp: new Date()
      }
    });

    return NextResponse.json({ accessLog }, { status: 201 });
  } catch (error: any) {
    console.error('Error in LXC access logs API (POST):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
