/**
 * API route for restoring an LXC container snapshot
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';

/**
 * PUT handler for restoring a snapshot
 * - Restore snapshot: PUT /api/containerization/lxc/snapshots/:containerId/:snapshotId/restore
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { containerId: string; snapshotId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { containerId, snapshotId } = params;

    // Get container
    const container = containerManager.getContainer(containerId);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get snapshot manager
    const snapshotManager = containerManager.getSnapshotManager();

    // Get snapshot
    const snapshot = snapshotManager.getSnapshot(snapshotId);
    if (!snapshot) {
      return NextResponse.json({ error: 'Snapshot not found' }, { status: 404 });
    }

    // Restore snapshot
    await snapshotManager.restoreSnapshot(snapshotId);

    return NextResponse.json({
      success: true,
      message: `Snapshot ${snapshot.name} restored successfully`,
    });
  } catch (error: any) {
    console.error(`Error in LXC snapshots API (PUT ${params.containerId}/${params.snapshotId}/restore):`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
