/**
 * API routes for LXC container snapshots
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';

// Validation schema for creating a snapshot
const createSnapshotSchema = z.object({
  name: z.string().min(1, "Snapshot name is required"),
  stateful: z.boolean().optional(),
  comment: z.string().optional(),
});

/**
 * GET handler for snapshot operations
 * - List snapshots: GET /api/containerization/lxc/snapshots/:containerId
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { containerId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { containerId } = params;

    // Get container
    const container = containerManager.getContainer(containerId);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get snapshot manager
    const snapshotManager = containerManager.getSnapshotManager();

    // List snapshots
    const snapshots = await snapshotManager.listContainerSnapshots(container.name);

    // Map snapshots to a simpler format for the API
    const snapshotList = snapshots.map(snapshot => ({
      id: snapshot.id,
      name: snapshot.name,
      containerId: snapshot.containerId,
      createdAt: snapshot.createdAt,
      stateful: snapshot.stateful,
      comment: snapshot.comment,
      size: snapshot.size,
    }));

    return NextResponse.json({ snapshots: snapshotList });
  } catch (error: any) {
    console.error(`Error in LXC snapshots API (GET ${params.containerId}):`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for snapshot operations
 * - Create snapshot: POST /api/containerization/lxc/snapshots/:containerId
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { containerId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    

    // Ensure container manager is initialized
    await initializeContainerManager();

    const { containerId } = params;

    // Get container
    const container = containerManager.getContainer(containerId);
    if (!container) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = createSnapshotSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { name, stateful, comment } = validationResult.data;

    // Get snapshot manager
    const snapshotManager = containerManager.getSnapshotManager();

    // Create snapshot
    const snapshot = await snapshotManager.createSnapshot(container.name, {
      name,
      stateful,
      comment,
    });

    return NextResponse.json({
      id: snapshot.id,
      name: snapshot.name,
      containerId: snapshot.containerId,
      createdAt: snapshot.createdAt,
      stateful: snapshot.stateful,
      comment: snapshot.comment,
    }, { status: 201 });
  } catch (error: any) {
    console.error(`Error in LXC snapshots API (POST ${params.containerId}):`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
