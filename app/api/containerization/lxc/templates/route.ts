/**
 * API routes for LXC templates
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { CreateTemplateOptions, TemplateQueryOptions, TemplateType } from '@/lib/containerization/lxc/models/template-extended';

// Validation schema for downloading a template
const downloadTemplateSchema = z.object({
  name: z.string().min(1, "Template name is required"),
  distribution: z.string().min(1, "Distribution is required"),
  release: z.string().min(1, "Release is required"),
  arch: z.string().optional(),
  variant: z.string().optional(),
});

// Create template schema
const createTemplateSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  distribution: z.string().min(1),
  release: z.string().min(1),
  arch: z.string().optional(),
  variant: z.string().optional(),
  defaultConfig: z.record(z.any()).optional(),
  packages: z.array(z.object({
    name: z.string(),
    version: z.string().optional(),
    required: z.boolean()
  })).optional(),
  security: z.object({
    profile: z.string(),
    appArmor: z.string().optional(),
    seccomp: z.string().optional()
  }).optional(),
  resources: z.object({
    cpu: z.object({
      shares: z.number().optional(),
      quota: z.number().optional(),
      period: z.number().optional(),
      cores: z.string().optional()
    }).optional(),
    memory: z.object({
      limit: z.number().optional(),
      reservation: z.number().optional(),
      swap: z.number().optional(),
      swappiness: z.number().optional()
    }).optional(),
    disk: z.object({
      limit: z.number().optional(),
      iops: z.number().optional(),
      bps: z.number().optional()
    }).optional()
  }).optional(),
  isPublic: z.boolean().optional(),
  tags: z.array(z.string()).optional()
});

// Update template schema
const updateTemplateSchema = createTemplateSchema.partial();

// Query templates schema
const queryTemplatesSchema = z.object({
  distribution: z.string().optional(),
  release: z.string().optional(),
  arch: z.string().optional(),
  variant: z.string().optional(),
  isPublic: z.boolean().optional(),
  isVerified: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  limit: z.number().optional(),
  offset: z.number().optional(),
  sortBy: z.enum(['name', 'createdAt', 'usageCount']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

/**
 * GET handler for template operations
 * - List templates: GET /api/containerization/lxc/templates
 * - Get template by ID: GET /api/containerization/lxc/templates?id=<id>
 * - Query templates: GET /api/containerization/lxc/templates?distribution=<distribution>&release=<release>...
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Get template manager
    const templateManager = containerManager.getTemplateManager();

    // Parse query parameters
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    // If ID is provided, get specific template
    if (id) {
      // Try to get from extended templates first
      try {
        const extendedTemplate = await templateManager.getExtendedTemplate(id);
        if (extendedTemplate) {
          return NextResponse.json({ template: extendedTemplate });
        }
      } catch (error) {
        console.error('Error getting extended template:', error);
      }

      // Fall back to legacy template
      const template = templateManager.getTemplate(id);
      if (!template) {
        return NextResponse.json({ error: 'Template not found' }, { status: 404 });
      }

      return NextResponse.json({
        template: {
          id: template.id,
          name: template.name,
          type: template.type,
          release: template.release,
          arch: template.arch,
          variant: template.variant,
          createdAt: template.createdAt,
          size: template.size,
        }
      });
    }

    // Check if we should use extended templates
    const useExtended = url.searchParams.get('extended') === 'true';

    if (useExtended) {
      // Parse query parameters for extended templates
      const queryParams: Record<string, any> = {};
      url.searchParams.forEach((value, key) => {
        if (key !== 'extended') {
          // Handle boolean values
          if (value === 'true') queryParams[key] = true;
          else if (value === 'false') queryParams[key] = false;
          // Handle numeric values
          else if (!isNaN(Number(value))) queryParams[key] = Number(value);
          // Handle array values (comma-separated)
          else if (key === 'tags' && value.includes(',')) queryParams[key] = value.split(',');
          // Handle string values
          else queryParams[key] = value;
        }
      });

      // Validate query parameters
      const validationResult = queryTemplatesSchema.safeParse(queryParams);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: 'Invalid query parameters', details: validationResult.error.format() },
          { status: 400 }
        );
      }

      // Get extended templates
      const extendedTemplates = await templateManager.getExtendedTemplates(validationResult.data);
      return NextResponse.json({ templates: extendedTemplates });
    }

    // List legacy templates
    const templates = templateManager.listTemplates();

    // Map templates to a simpler format for the API
    const templateList = templates.map(template => ({
      id: template.id,
      name: template.name,
      type: template.type,
      release: template.release,
      arch: template.arch,
      variant: template.variant,
      createdAt: template.createdAt,
      size: template.size,
    }));

    return NextResponse.json({ templates: templateList });
  } catch (error: any) {
    console.error('Error in LXC templates API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for template operations
 * - Download template: POST /api/containerization/lxc/templates
 * - Create extended template: POST /api/containerization/lxc/templates?extended=true
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Parse request body
    const body = await request.json();

    // Parse query parameters
    const url = new URL(request.url);
    const useExtended = url.searchParams.get('extended') === 'true';

    // Get template manager
    const templateManager = containerManager.getTemplateManager();

    if (useExtended) {
      // Validate request body for extended template
      const validationResult = createTemplateSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: 'Invalid request body', details: validationResult.error.format() },
          { status: 400 }
        );
      }

      // Create extended template options
      const templateOptions: CreateTemplateOptions = {
        ...validationResult.data,
        createdBy: session.user.id
      };

      // Create extended template
      const template = await templateManager.createExtendedTemplate(templateOptions);

      return NextResponse.json({ template }, { status: 201 });
    } else {
      // Validate request body for legacy template
      const validationResult = downloadTemplateSchema.safeParse(body);
      if (!validationResult.success) {
        return NextResponse.json(
          { error: 'Invalid request body', details: validationResult.error.format() },
          { status: 400 }
        );
      }

      const { name, distribution, release, arch, variant } = validationResult.data;

      // Download template
      const template = await templateManager.downloadTemplate({
        name,
        distribution,
        release,
        arch,
        variant,
      });

      return NextResponse.json({
        id: template.id,
        name: template.name,
        type: template.type,
        release: template.release,
        arch: template.arch,
        variant: template.variant,
        createdAt: template.createdAt,
        size: template.size,
      }, { status: 201 });
    }
  } catch (error: any) {
    console.error('Error in LXC templates API (POST):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * PUT handler for template operations
 * - Update extended template: PUT /api/containerization/lxc/templates/:id
 */
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Parse request body
    const body = await request.json();

    // Parse URL to get template ID
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    if (!id) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    // Validate request body
    const validationResult = updateTemplateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Get template manager
    const templateManager = containerManager.getTemplateManager();

    // Update extended template
    const template = await templateManager.updateExtendedTemplate(id, validationResult.data);

    return NextResponse.json({ template });
  } catch (error: any) {
    console.error('Error in LXC templates API (PUT):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * DELETE handler for template operations
 * - Delete template: DELETE /api/containerization/lxc/templates/:id
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Parse URL to get template ID
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    if (!id) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    // Get template manager
    const templateManager = containerManager.getTemplateManager();

    // Try to delete extended template first
    try {
      await templateManager.deleteExtendedTemplate(id);
      return NextResponse.json({ success: true });
    } catch (error) {
      console.error('Error deleting extended template:', error);

      // Fall back to legacy template
      try {
        await templateManager.deleteTemplate(id);
        return NextResponse.json({ success: true });
      } catch (error) {
        throw new Error(`Template with ID ${id} not found or could not be deleted`);
      }
    }
  } catch (error: any) {
    console.error('Error in LXC templates API (DELETE):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
