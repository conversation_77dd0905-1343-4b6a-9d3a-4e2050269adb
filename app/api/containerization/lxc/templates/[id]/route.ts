import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import containerManager, { initializeContainerManager } from '@/lib/containerization/lxc';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

// Update template schema
const updateTemplateSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  distribution: z.string().min(1).optional(),
  release: z.string().min(1).optional(),
  arch: z.string().optional(),
  variant: z.string().optional(),
  defaultConfig: z.record(z.any()).optional(),
  packages: z.array(z.object({
    name: z.string(),
    version: z.string().optional(),
    required: z.boolean()
  })).optional(),
  security: z.object({
    profile: z.string(),
    appArmor: z.string().optional(),
    seccomp: z.string().optional()
  }).optional(),
  resources: z.object({
    cpu: z.object({
      shares: z.number().optional(),
      quota: z.number().optional(),
      period: z.number().optional(),
      cores: z.string().optional()
    }).optional(),
    memory: z.object({
      limit: z.number().optional(),
      reservation: z.number().optional(),
      swap: z.number().optional(),
      swappiness: z.number().optional()
    }).optional(),
    disk: z.object({
      limit: z.number().optional(),
      iops: z.number().optional(),
      bps: z.number().optional()
    }).optional()
  }).optional(),
  isPublic: z.boolean().optional(),
  tags: z.array(z.string()).optional()
});

/**
 * GET handler for template operations
 * - Get template by ID: GET /api/containerization/lxc/templates/:id
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Get template manager
    const templateManager = containerManager.getTemplateManager();

    // Try to get from extended templates first
    try {
      const extendedTemplate = await templateManager.getExtendedTemplate(id);
      if (extendedTemplate) {
        return NextResponse.json({ template: extendedTemplate });
      }
    } catch (error) {
      console.error('Error getting extended template:', error);
    }

    // Fall back to legacy template
    const template = templateManager.getTemplate(id);
    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    return NextResponse.json({
      template: {
        id: template.id,
        name: template.name,
        type: template.type,
        release: template.release,
        arch: template.arch,
        variant: template.variant,
        createdAt: template.createdAt,
        size: template.size,
      }
    });
  } catch (error: any) {
    console.error('Error in LXC templates API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * PUT handler for template operations
 * - Update extended template: PUT /api/containerization/lxc/templates/:id
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = updateTemplateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Get template manager
    const templateManager = containerManager.getTemplateManager();

    // Update extended template
    const template = await templateManager.updateExtendedTemplate(id, validationResult.data);

    return NextResponse.json({ template });
  } catch (error: any) {
    console.error('Error in LXC templates API (PUT):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * DELETE handler for template operations
 * - Delete template: DELETE /api/containerization/lxc/templates/:id
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Ensure container manager is initialized
    await initializeContainerManager();

    // Get template manager
    const templateManager = containerManager.getTemplateManager();

    // Try to delete extended template first
    try {
      await templateManager.deleteExtendedTemplate(id);
      return NextResponse.json({ success: true });
    } catch (error) {
      console.error('Error deleting extended template:', error);
      
      // Fall back to legacy template
      try {
        await templateManager.deleteTemplate(id);
        return NextResponse.json({ success: true });
      } catch (error) {
        throw new Error(`Template with ID ${id} not found or could not be deleted`);
      }
    }
  } catch (error: any) {
    console.error('Error in LXC templates API (DELETE):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
