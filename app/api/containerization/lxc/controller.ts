/**
 * LXC API Controller
 *
 * This module provides a controller for the LXC REST API.
 */

import { NextRequest, NextResponse } from 'next/server';
import { LxcRestService } from '@/lib/containerization/lxc/api/lxc-rest-service';
import { LxcRestClient } from '@/lib/containerization/lxc/api/lxc-rest-client';
import { logger } from '@/lib/logger';

// Initialize LXC REST service
const lxcRestService = LxcRestService.getInstance({
  port: 8080,
  host: 'localhost',
  autoStart: true
});

// Initialize LXC REST client
const lxcRestClient = new LxcRestClient({
  baseUrl: lxcRestService.getUrl(),
  apiVersion: 'v1'
});

/**
 * Handle errors
 */
function handleError(error: any) {
  logger.error('LXC API error:', error);
  
  return NextResponse.json(
    {
      error: error.message || 'Internal Server Error',
      details: error.details || {}
    },
    { status: error.status || 500 }
  );
}

/**
 * List containers
 */
export async function listContainers() {
  try {
    const containers = await lxcRestClient.listContainers();
    return NextResponse.json({ containers });
  } catch (error) {
    return handleError(error);
  }
}

/**
 * Get container info
 */
export async function getContainerInfo(id: string) {
  try {
    const container = await lxcRestClient.getContainerInfo(id);
    return NextResponse.json({ container });
  } catch (error) {
    return handleError(error);
  }
}

/**
 * Create container
 */
export async function createContainer(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, config } = body;
    
    if (!name) {
      return NextResponse.json(
        { error: 'Container name is required' },
        { status: 400 }
      );
    }
    
    await lxcRestClient.createContainer(name, config || {});
    
    return NextResponse.json(
      { message: `Container ${name} created successfully` },
      { status: 201 }
    );
  } catch (error) {
    return handleError(error);
  }
}

/**
 * Update container state
 */
export async function updateContainerState(id: string, request: NextRequest) {
  try {
    const body = await request.json();
    const { action, force } = body;
    
    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }
    
    switch (action) {
      case 'start':
        await lxcRestClient.startContainer(id);
        break;
      case 'stop':
        await lxcRestClient.stopContainer(id, force);
        break;
      default:
        return NextResponse.json(
          { error: `Invalid action: ${action}` },
          { status: 400 }
        );
    }
    
    return NextResponse.json(
      { message: `Container ${id} ${action}ed successfully` }
    );
  } catch (error) {
    return handleError(error);
  }
}

/**
 * Execute command in container
 */
export async function executeCommand(id: string, request: NextRequest) {
  try {
    const body = await request.json();
    const { command, environment, workingDirectory, user, timeout } = body;
    
    if (!command || !Array.isArray(command)) {
      return NextResponse.json(
        { error: 'Command is required and must be an array' },
        { status: 400 }
      );
    }
    
    const result = await lxcRestClient.executeCommand(id, command, {
      env: environment,
      cwd: workingDirectory,
      user,
      timeout
    });
    
    return NextResponse.json(result);
  } catch (error) {
    return handleError(error);
  }
}

/**
 * List container files
 */
export async function listContainerFiles(id: string, request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path') || '/';
    
    const files = await lxcRestClient.getContainerFiles(id, path);
    
    return NextResponse.json({ files });
  } catch (error) {
    return handleError(error);
  }
}

/**
 * Get container file content
 */
export async function getContainerFileContent(id: string, request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');
    
    if (!path) {
      return NextResponse.json(
        { error: 'Path is required' },
        { status: 400 }
      );
    }
    
    const content = await lxcRestClient.getContainerFileContent(id, path);
    
    return NextResponse.json({ content });
  } catch (error) {
    return handleError(error);
  }
}

/**
 * Put container file content
 */
export async function putContainerFileContent(id: string, request: NextRequest) {
  try {
    const body = await request.json();
    const { path, content } = body;
    
    if (!path) {
      return NextResponse.json(
        { error: 'Path is required' },
        { status: 400 }
      );
    }
    
    await lxcRestClient.putContainerFileContent(id, path, content || '');
    
    return NextResponse.json(
      { message: `File ${path} updated successfully` }
    );
  } catch (error) {
    return handleError(error);
  }
}

/**
 * List images
 */
export async function listImages() {
  try {
    const images = await lxcRestClient.listImages();
    return NextResponse.json({ images });
  } catch (error) {
    return handleError(error);
  }
}

/**
 * Get image info
 */
export async function getImageInfo(id: string) {
  try {
    const image = await lxcRestClient.getImageInfo(id);
    return NextResponse.json({ image });
  } catch (error) {
    return handleError(error);
  }
}
