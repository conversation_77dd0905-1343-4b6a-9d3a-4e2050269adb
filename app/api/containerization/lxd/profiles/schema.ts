import { z } from 'zod';

// Schema for profile creation
export const createProfileSchema = z.object({
  name: z.string().min(1, "Profile name is required"),
  description: z.string().optional(),
  config: z.record(z.string()).optional(),
  devices: z.record(z.record(z.string())).optional(),
});

export type CreateProfileInput = z.infer<typeof createProfileSchema>;

// Schema for profile update
export const updateProfileSchema = z.object({
  description: z.string().optional(),
  config: z.record(z.string()).optional(),
  devices: z.record(z.record(z.string())).optional(),
});

export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;