import { z } from 'zod';

// Schema for image creation
export const createImageSchema = z.object({
  source: z.object({
    type: z.enum(['url', 'file', 'fingerprint']),
    url: z.string().url().optional(),
    file: z.string().optional(),
    fingerprint: z.string().optional(),
    server: z.string().optional(),
    protocol: z.enum(['lxd', 'simplestreams']).optional(),
  }),
  aliases: z.array(z.string()).optional(),
  public: z.boolean().optional(),
  auto_update: z.boolean().optional(),
  properties: z.record(z.string()).optional(),
});

export type CreateImageInput = z.infer<typeof createImageSchema>;

// Schema for image update
export const updateImageSchema = z.object({
  aliases: z.array(z.string()).optional(),
  public: z.boolean().optional(),
  auto_update: z.boolean().optional(),
  properties: z.record(z.string()).optional(),
});

export type UpdateImageInput = z.infer<typeof updateImageSchema>;