import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';
import { z } from 'zod';
import fs from 'fs';
import path from 'path';

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

// Validation schema for image import
const importImageSchema = z.object({
  sourcePath: z.string().min(1, "Source path is required"),
  aliases: z.array(z.string()).optional(),
  description: z.string().optional(),
});

/**
 * POST /api/containerization/lxd/images/import
 * Imports a local image file into LXD
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // Validate request body against schema
    const result = importImageSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }
    
    const { sourcePath, aliases = [], description = '' } = result.data;
    
    // Check if the file exists
    if (!fs.existsSync(sourcePath)) {
      return NextResponse.json(
        { error: `Image file not found at path: ${sourcePath}` },
        { status: 404 }
      );
    }

    // Import the image
    try {
      const imageManager = lxdClient.getImageManager();
      const fingerprint = await imageManager.importImage(sourcePath, aliases);
      
      // Add description as an alias if provided
      if (description && !aliases.includes(description)) {
        await imageManager.addImageAlias(fingerprint, description);
      }
      
      return NextResponse.json({
        success: true,
        fingerprint,
        aliases,
        message: `Image imported successfully with fingerprint: ${fingerprint}`
      }, { status: 201 });
    } catch (error: any) {
      console.error('Error importing image:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in image import API:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
