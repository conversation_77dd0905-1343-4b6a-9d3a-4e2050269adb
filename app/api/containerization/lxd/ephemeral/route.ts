import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';

// Create a new instance of LxdClient and PrismaClient
const lxdClient = new LxdClient();
const prisma = new PrismaClient();

// Validation schema for ephemeral container creation
const createEphemeralContainerSchema = z.object({
  name: z.string().optional(),
  image: z.string().optional(),
  imagePath: z.string().optional(),
  profiles: z.array(z.string()).optional(),
  config: z.record(z.string()).optional(),
  projectId: z.string().optional(),
  expiresInMinutes: z.number().optional(),
});

/**
 * POST /api/containerization/lxd/ephemeral
 * Creates a new ephemeral LXD container
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // Validate request body against schema
    const result = createEphemeralContainerSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }

    const {
      name = `ephemeral-${Date.now().toString().slice(-6)}`,
      image = 'ubuntu:22.04',
      imagePath,
      profiles = ['default'],
      config = {},
      projectId,
      expiresInMinutes = 60
    } = result.data;

    // If imagePath is provided, import the image first
    let imageToUse = image;
    if (imagePath) {
      try {
        // Import the image
        const imageManager = lxdClient.getImageManager();
        const alias = `ephemeral-image-${Date.now().toString().slice(-6)}`;
        const fingerprint = await imageManager.importImage(imagePath, [alias]);
        imageToUse = alias; // Use the alias for the container creation
      } catch (error: any) {
        console.error('Error importing image:', error);
        return NextResponse.json({ error: `Failed to import image: ${error.message}` }, { status: 500 });
      }
    }

    // Create the ephemeral container
    const container = await lxdClient.createContainer({
      name,
      image: imageToUse,
      profiles,
      ephemeral: true, // This is what makes it ephemeral
      stateful: false,
      config: {
        ...config,
        // Add any additional configuration needed for ephemeral containers
        'boot.autostart': 'false', // Don't autostart ephemeral containers
      }
    });

    // Start the container
    await lxdClient.startContainer(container.id);

    // If projectId is provided, associate the container with the project
    if (projectId && session.user?.id) {
      try {
        await prisma.project.update({
          where: { id: projectId },
          data: {
            containers: {
              create: {
                containerId: container.id,
                containerType: 'lxd-ephemeral',
                createdBy: session.user.id
              }
            }
          }
        });

        // Add projectId to the container
        container.projectId = projectId;
      } catch (error) {
        console.error('Error associating container with project:', error);
        // Continue even if association fails
      }
    }

    // If expiresInMinutes is provided, set up auto-deletion
    if (expiresInMinutes > 0) {
      // This is just a placeholder - in a real implementation, you would set up
      // a scheduled task or use a background worker to delete the container
      // after the specified time
      console.log(`Container ${container.id} will expire in ${expiresInMinutes} minutes`);
      
      // In a production environment, you would use a proper task scheduler
      // For now, we'll just log it
    }

    return NextResponse.json({
      id: container.id,
      name: container.name,
      state: 'running',
      ephemeral: true,
      projectId: container.projectId || null,
      expiresAt: expiresInMinutes > 0 
        ? new Date(Date.now() + expiresInMinutes * 60 * 1000).toISOString()
        : null
    }, { status: 201 });
  } catch (error: any) {
    console.error('Error creating ephemeral container:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
