import { z } from 'zod';

// Schema for container creation
export const createContainerSchema = z.object({
  name: z.string().min(1, "Container name is required"),
  image: z.string().min(1, "Image name/fingerprint is required"),
  profiles: z.array(z.string()).optional(),
  ephemeral: z.boolean().optional(),
  stateful: z.boolean().optional(),
  config: z.record(z.string()).optional(),
  devices: z.record(z.record(z.string())).optional(),
  description: z.string().optional(),
  autostart: z.boolean().optional(),
  projectId: z.string().optional(),
});

export type CreateContainerInput = z.infer<typeof createContainerSchema>;

// Schema for container update
export const updateContainerSchema = z.object({
  description: z.string().optional(),
  config: z.record(z.string()).optional(),
  devices: z.record(z.record(z.string())).optional(),
  profiles: z.array(z.string()).optional(),
  autostart: z.boolean().optional(),
});

export type UpdateContainerInput = z.infer<typeof updateContainerSchema>;

// Schema for container stop
export const stopContainerSchema = z.object({
  force: z.boolean().optional(),
  timeout: z.number().optional(),
});

export type StopContainerInput = z.infer<typeof stopContainerSchema>;

// Schema for container restart
export const restartContainerSchema = z.object({
  force: z.boolean().optional(),
  timeout: z.number().optional(),
});

export type RestartContainerInput = z.infer<typeof restartContainerSchema>;

// Schema for command execution
export const executeCommandSchema = z.object({
  command: z.array(z.string()).min(1, "Command is required"),
  env: z.record(z.string()).optional(),
  cwd: z.string().optional(),
  user: z.string().optional(),
  group: z.string().optional(),
  timeout: z.number().optional(),
});

export type ExecuteCommandInput = z.infer<typeof executeCommandSchema>;