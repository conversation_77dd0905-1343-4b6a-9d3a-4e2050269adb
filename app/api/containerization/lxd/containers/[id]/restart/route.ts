import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';
import { restartContainerSchema } from '../../schema';

interface RouteParams {
  params: {
    id: string;
  };
}

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * POST /api/containerization/lxd/containers/[id]/restart
 * Restarts a specific LXD container
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      await lxdClient.getContainerInfo(id);
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    const body = await request.json();

    // Validate request body against schema
    const result = restartContainerSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }

    const { force = false } = result.data;

    // Restart container
    try {
      await lxdClient.restartContainer(id, force);

      return NextResponse.json(
        { message: `Container ${id} restarted successfully` },
        { status: 200 }
      );
    } catch (error: any) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error(`Error restarting container ${params.id}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}