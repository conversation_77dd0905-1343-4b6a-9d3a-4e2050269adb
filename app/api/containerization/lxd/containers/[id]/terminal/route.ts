/**
 * API route for LXD container terminal access
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * GET handler for container terminal
 * - Get terminal URL: GET /api/containerization/lxd/containers/{id}/terminal
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      const container = await lxdClient.getContainerInfo(id);
      
      // Check if container is running
      if (container.state.status !== 'Running') {
        return NextResponse.json({ error: 'Container is not running' }, { status: 400 });
      }
      
      // For now, return a placeholder URL
      // In a real implementation, this would return a URL to a terminal service
      // like ttyd, wetty, or a custom terminal implementation
      const terminalUrl = `/terminal/lxd/${id}`;
      
      return NextResponse.json({ url: terminalUrl });
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }
  } catch (error: any) {
    console.error('Error in LXD container terminal API:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
