import { NextRequest, NextResponse } from 'next/server';
import { LxdClient } from '@/lib/containerization/lxd';
import { executeCommandSchema } from '../schema';

interface RouteParams {
  params: {
    id: string;
  };
}

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * POST /api/containerization/lxd/containers/[id]/execute
 * Executes a command in a specific LXD container
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const body = await request.json();
    
    // Validate request body against schema
    const result = executeCommandSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }
    
    const { command, env = {}, cwd, user, group, timeout } = result.data;
    
    const commandResult = await lxdClient.executeCommand(id, command, {
      env,
      cwd,
      user,
      group,
      timeout
    });
    
    return NextResponse.json({ result: commandResult }, { status: 200 });
  } catch (error: any) {
    console.error(`Error executing command in container ${params.id}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}