/**
 * API route for executing commands in an LXD container
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';
import { z } from 'zod';

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

// Validation schema for command execution
const execCommandSchema = z.object({
  command: z.string().min(1, "Command is required"),
  env: z.record(z.string()).optional(),
  cwd: z.string().optional(),
  user: z.string().optional(),
  timeout: z.number().optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * POST handler for executing commands
 * - Execute command: POST /api/containerization/lxd/containers/{id}/exec
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      const container = await lxdClient.getContainerInfo(id);
      
      // Check if container is running
      if (container.state.status !== 'Running') {
        return NextResponse.json({ error: 'Container is not running' }, { status: 400 });
      }
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = execCommandSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { command, env, cwd, user, timeout } = validationResult.data;

    // Execute command
    try {
      // If command is a string, split it into an array
      const commandArray = typeof command === 'string' ? command.split(' ') : command;
      
      // Execute command
      const result = await lxdClient.executeCommand(id, commandArray, {
        env,
        cwd,
        user,
        timeout
      });
      
      return NextResponse.json({
        stdout: result.stdout,
        stderr: result.stderr,
        exitCode: result.exitCode
      });
    } catch (error: any) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in LXD exec API:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
