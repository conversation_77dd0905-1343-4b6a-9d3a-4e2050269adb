/**
 * API routes for LXD container file operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';
import { z } from 'zod';
import path from 'path';

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

// Validation schema for file operations
const fileOperationSchema = z.object({
  path: z.string().min(1, "Path is required"),
  recursive: z.boolean().optional().default(false),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET handler for file operations
 * - List files: GET /api/containerization/lxd/containers/:id/files?path=/path/to/dir
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      await lxdClient.getContainerInfo(id);
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const filePath = url.searchParams.get('path') || '/';

    // List files
    try {
      // Execute command to list files
      const result = await lxdClient.executeCommand(id, ['ls', '-la', filePath]);
      
      // Parse ls output to get file list
      const files = parseLsOutput(result.stdout, filePath);

      return NextResponse.json({ files });
    } catch (error: any) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in LXD files API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * DELETE handler for file operations
 * - Delete file: DELETE /api/containerization/lxd/containers/:id/files
 */
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      await lxdClient.getContainerInfo(id);
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = fileOperationSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: filePath, recursive } = validationResult.data;

    // Delete file or directory
    try {
      const command = recursive ? ['rm', '-rf', filePath] : ['rm', filePath];
      await lxdClient.executeCommand(id, command);
      return NextResponse.json({ success: true });
    } catch (error: any) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in LXD files API (DELETE):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * Parse ls output to get file list
 */
function parseLsOutput(output: string, basePath: string): any[] {
  const lines = output.split('\n');
  const files: any[] = [];

  // Skip the first line (total)
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;

    // Parse ls -la output
    // Format: permissions links owner group size month day time name
    const parts = line.split(/\s+/);
    if (parts.length < 9) continue;

    const permissions = parts[0];
    const isDirectory = permissions.startsWith('d');
    const owner = parts[2];
    const group = parts[3];
    const size = parseInt(parts[4], 10);
    
    // Get file name (might contain spaces)
    const name = parts.slice(8).join(' ');
    
    // Skip . and ..
    if (name === '.' || name === '..') continue;

    files.push({
      name,
      path: path.join(basePath, name),
      type: isDirectory ? 'directory' : 'file',
      size: isDirectory ? undefined : size,
      permissions,
      owner,
      group
    });
  }

  return files;
}
