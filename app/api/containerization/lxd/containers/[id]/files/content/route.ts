/**
 * API routes for LXD container file content operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';
import { z } from 'zod';
import path from 'path';

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

// Validation schema for file content operations
const fileContentSchema = z.object({
  path: z.string().min(1, "Path is required"),
  content: z.string(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET handler for file content operations
 * - Read file content: GET /api/containerization/lxd/containers/:id/files/content?path=/path/to/file
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      await lxdClient.getContainerInfo(id);
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const filePath = url.searchParams.get('path');

    if (!filePath) {
      return NextResponse.json({ error: 'Path is required' }, { status: 400 });
    }

    // Read file content
    try {
      // Execute command to read file
      const result = await lxdClient.executeCommand(id, ['cat', filePath]);
      return NextResponse.json({ content: result.stdout });
    } catch (error: any) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in LXD file content API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * PUT handler for file content operations
 * - Write file content: PUT /api/containerization/lxd/containers/:id/files/content
 */
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      await lxdClient.getContainerInfo(id);
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = fileContentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: filePath, content } = validationResult.data;

    // Write file content
    try {
      // Create a temporary file with the content
      const tempFile = `/tmp/lxd-file-${Date.now()}`;
      
      // Write content to temp file
      await lxdClient.executeCommand(id, ['bash', '-c', `cat > ${tempFile} << 'EOF'
${content}
EOF`]);
      
      // Create directory if it doesn't exist
      const dirPath = path.dirname(filePath);
      await lxdClient.executeCommand(id, ['mkdir', '-p', dirPath]);
      
      // Move temp file to destination
      await lxdClient.executeCommand(id, ['mv', tempFile, filePath]);
      
      return NextResponse.json({ success: true });
    } catch (error: any) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in LXD file content API (PUT):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
