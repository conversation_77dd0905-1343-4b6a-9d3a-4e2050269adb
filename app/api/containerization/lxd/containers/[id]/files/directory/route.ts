/**
 * API routes for LXD container directory operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';
import { z } from 'zod';

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

// Validation schema for directory operations
const directorySchema = z.object({
  path: z.string().min(1, "Path is required"),
  mode: z.string().optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * PUT handler for directory operations
 * - Create directory: PUT /api/containerization/lxd/containers/:id/files/directory
 */
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      await lxdClient.getContainerInfo(id);
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = directorySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { path: dirPath, mode } = validationResult.data;

    // Create directory
    try {
      // Create directory with mode if specified
      const command = mode 
        ? ['mkdir', '-p', '-m', mode, dirPath]
        : ['mkdir', '-p', dirPath];
        
      await lxdClient.executeCommand(id, command);
      
      return NextResponse.json({ success: true });
    } catch (error: any) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in LXD directory API (PUT):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
