import { NextRequest, NextResponse } from 'next/server';
import { LxdClient } from '@/lib/containerization/lxd';

interface RouteParams {
  params: {
    id: string;
  };
}

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * GET /api/containerization/lxd/containers/[id]
 * Retrieves details of a specific LXD container
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const container = await lxdClient.getContainerInfo(id);
    
    if (!container) {
      return NextResponse.json(
        { error: `Container with ID ${id} not found` },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ container }, { status: 200 });
  } catch (error: any) {
    console.error(`Error getting container ${params.id}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * DELETE /api/containerization/lxd/containers/[id]
 * Deletes a specific LXD container
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    await lxdClient.deleteContainer(id);
    
    return NextResponse.json(
      { message: `Container ${id} deleted successfully` },
      { status: 200 }
    );
  } catch (error: any) {
    console.error(`Error deleting container ${params.id}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}