import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';

interface RouteParams {
  params: {
    id: string;
  };
}

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * POST /api/containerization/lxd/containers/[id]/start
 * Starts a specific LXD container
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      await lxdClient.getContainerInfo(id);
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Start container
    try {
      await lxdClient.startContainer(id);
      return NextResponse.json(
        { message: `Container ${id} started successfully` },
        { status: 200 }
      );
    } catch (error: any) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error(`Error starting container ${params.id}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}