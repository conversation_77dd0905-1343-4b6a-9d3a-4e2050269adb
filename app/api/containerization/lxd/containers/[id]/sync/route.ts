/**
 * API routes for LXD container file synchronization
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';
import { z } from 'zod';
import path from 'path';
import fs from 'fs/promises';
import { PrismaClient } from '@prisma/client';

// Create a new instance of LxdClient and PrismaClient
const lxdClient = new LxdClient();
const prisma = new PrismaClient();

// Validation schema for sync operations
const syncSchema = z.object({
  projectId: z.string().min(1, "Project ID is required"),
  direction: z.enum(['toContainer', 'fromContainer']),
  paths: z.array(z.string()).min(1, "At least one path is required"),
  ignorePatterns: z.array(z.string()).optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET handler for sync operations
 * - Get sync status: GET /api/containerization/lxd/containers/:id/sync?projectId=123&path=/app
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      await lxdClient.getContainerInfo(id);
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const projectId = url.searchParams.get('projectId');
    const containerPath = url.searchParams.get('path') || '/app';

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    // Get local project path
    const localProjectPath = path.join(process.env.PROJECTS_DIR || '/tmp/projects', projectId);

    // Get sync items
    const syncItems = await getSyncItems(id, localProjectPath, containerPath);

    // Get last sync time
    const lastSync = await prisma.projectSync.findFirst({
      where: {
        projectId,
        containerId: id
      },
      orderBy: {
        syncedAt: 'desc'
      }
    });

    return NextResponse.json({
      items: syncItems,
      lastSync: lastSync?.syncedAt
    });
  } catch (error: any) {
    console.error('Error in LXD sync API (GET):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for sync operations
 * - Sync files: POST /api/containerization/lxd/containers/:id/sync
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get container
    try {
      await lxdClient.getContainerInfo(id);
    } catch (error) {
      return NextResponse.json({ error: 'Container not found' }, { status: 404 });
    }

    // Parse request body
    const body = await request.json();

    // Validate request body
    const validationResult = syncSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { projectId, direction, paths, ignorePatterns } = validationResult.data;

    // Get local project path
    const localProjectPath = path.join(process.env.PROJECTS_DIR || '/tmp/projects', projectId);
    const containerPath = '/app';

    // Sync files
    if (direction === 'toContainer') {
      await syncToContainer(id, localProjectPath, containerPath, paths, ignorePatterns);
    } else {
      await syncFromContainer(id, localProjectPath, containerPath, paths, ignorePatterns);
    }

    // Record sync
    await prisma.projectSync.create({
      data: {
        projectId,
        containerId: id,
        direction,
        syncedAt: new Date(),
        syncedBy: session.user.id
      }
    });

    // Get updated sync items
    const syncItems = await getSyncItems(id, localProjectPath, containerPath);

    return NextResponse.json({
      success: true,
      items: syncItems
    });
  } catch (error: any) {
    console.error('Error in LXD sync API (POST):', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * Get sync items
 */
async function getSyncItems(containerId: string, localPath: string, containerPath: string) {
  // Get local files
  const localFiles = await getFilesRecursive(localPath);
  
  // Get container files
  const containerFiles = await getContainerFilesRecursive(containerId, containerPath);
  
  // Compare files
  const syncItems = [];
  
  // Process local files
  for (const localFile of localFiles) {
    const relativePath = path.relative(localPath, localFile.path);
    const containerFile = containerFiles.find(f => 
      path.relative(containerPath, f.path) === relativePath
    );
    
    if (!containerFile) {
      // File exists locally but not in container
      syncItems.push({
        path: relativePath,
        type: localFile.type,
        status: 'new',
        lastModified: localFile.lastModified,
        size: localFile.size
      });
    } else if (localFile.type === 'file' && containerFile.type === 'file') {
      // File exists in both places, check if modified
      const localDate = new Date(localFile.lastModified);
      const containerDate = new Date(containerFile.lastModified);
      
      // Consider modified if local file is newer or sizes differ
      const isModified = localDate > containerDate || localFile.size !== containerFile.size;
      
      syncItems.push({
        path: relativePath,
        type: 'file',
        status: isModified ? 'modified' : 'unchanged',
        lastModified: localFile.lastModified,
        size: localFile.size
      });
    } else {
      // Directory exists in both places
      syncItems.push({
        path: relativePath,
        type: 'directory',
        status: 'unchanged',
        lastModified: localFile.lastModified
      });
    }
  }
  
  // Process container files that don't exist locally
  for (const containerFile of containerFiles) {
    const relativePath = path.relative(containerPath, containerFile.path);
    const localFile = localFiles.find(f => 
      path.relative(localPath, f.path) === relativePath
    );
    
    if (!localFile) {
      // File exists in container but not locally
      syncItems.push({
        path: relativePath,
        type: containerFile.type,
        status: 'deleted',
        lastModified: containerFile.lastModified,
        size: containerFile.size
      });
    }
  }
  
  return syncItems;
}

// Helper functions for file synchronization
async function syncToContainer(containerId: string, localPath: string, containerPath: string, paths: string[], ignorePatterns?: string[]) {
  for (const relativePath of paths) {
    const localFilePath = path.join(localPath, relativePath);
    const containerFilePath = path.join(containerPath, relativePath);
    
    // Skip ignored files
    if (shouldIgnore(relativePath, ignorePatterns)) {
      continue;
    }
    
    // Check if path exists locally
    try {
      const stats = await fs.stat(localFilePath);
      
      if (stats.isDirectory()) {
        // Create directory in container
        await lxdClient.executeCommand(containerId, ['mkdir', '-p', containerFilePath]);
        
        // Recursively sync directory contents
        const files = await fs.readdir(localFilePath);
        
        for (const file of files) {
          const childRelativePath = path.join(relativePath, file);
          
          // Skip ignored files
          if (shouldIgnore(childRelativePath, ignorePatterns)) {
            continue;
          }
          
          await syncToContainer(containerId, localPath, containerPath, [childRelativePath], ignorePatterns);
        }
      } else {
        // Copy file to container
        const content = await fs.readFile(localFilePath, 'utf8');
        
        // Create a temporary file with the content
        const tempFile = `/tmp/lxd-file-${Date.now()}`;
        
        // Write content to temp file
        await lxdClient.executeCommand(containerId, ['bash', '-c', `cat > ${tempFile} << 'EOF'
${content}
EOF`]);
        
        // Create directory if it doesn't exist
        const dirPath = path.dirname(containerFilePath);
        await lxdClient.executeCommand(containerId, ['mkdir', '-p', dirPath]);
        
        // Move temp file to destination
        await lxdClient.executeCommand(containerId, ['mv', tempFile, containerFilePath]);
      }
    } catch (error) {
      console.error(`Error syncing to container: ${error}`);
      throw error;
    }
  }
}

async function syncFromContainer(containerId: string, localPath: string, containerPath: string, paths: string[], ignorePatterns?: string[]) {
  for (const relativePath of paths) {
    const localFilePath = path.join(localPath, relativePath);
    const containerFilePath = path.join(containerPath, relativePath);
    
    // Skip ignored files
    if (shouldIgnore(relativePath, ignorePatterns)) {
      continue;
    }
    
    try {
      // Check if path exists in container
      const lsResult = await lxdClient.executeCommand(containerId, ['ls', '-la', containerFilePath]);
      const isDirectory = lsResult.stdout.split('\n')[0].startsWith('d');
      
      if (isDirectory) {
        // Create directory locally
        await fs.mkdir(localFilePath, { recursive: true });
        
        // Recursively sync directory contents
        const lsResult = await lxdClient.executeCommand(containerId, ['ls', '-1', containerFilePath]);
        const files = lsResult.stdout.split('\n').filter(f => f && f !== '.' && f !== '..');
        
        for (const file of files) {
          const childRelativePath = path.join(relativePath, file);
          
          // Skip ignored files
          if (shouldIgnore(childRelativePath, ignorePatterns)) {
            continue;
          }
          
          await syncFromContainer(containerId, localPath, containerPath, [childRelativePath], ignorePatterns);
        }
      } else {
        // Create directory if it doesn't exist
        const dirPath = path.dirname(localFilePath);
        await fs.mkdir(dirPath, { recursive: true });
        
        // Copy file from container
        const catResult = await lxdClient.executeCommand(containerId, ['cat', containerFilePath]);
        await fs.writeFile(localFilePath, catResult.stdout);
      }
    } catch (error) {
      console.error(`Error syncing from container: ${error}`);
      throw error;
    }
  }
}

/**
 * Get files recursively
 */
async function getFilesRecursive(dir: string) {
  const files: any[] = [];
  
  try {
    const entries = await fs.readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const entryPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        files.push({
          path: entryPath,
          type: 'directory',
          lastModified: (await fs.stat(entryPath)).mtime.toISOString()
        });
        
        const childFiles = await getFilesRecursive(entryPath);
        files.push(...childFiles);
      } else {
        const stats = await fs.stat(entryPath);
        
        files.push({
          path: entryPath,
          type: 'file',
          lastModified: stats.mtime.toISOString(),
          size: stats.size
        });
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error);
  }
  
  return files;
}

/**
 * Get container files recursively
 */
async function getContainerFilesRecursive(containerId: string, dir: string) {
  const files: any[] = [];
  
  try {
    // List files in directory
    const lsResult = await lxdClient.executeCommand(containerId, ['ls', '-la', dir]);
    const lsLines = lsResult.stdout.split('\n');
    
    // Skip the first line (total) and parse each line
    for (let i = 1; i < lsLines.length; i++) {
      const line = lsLines[i].trim();
      if (!line) continue;
      
      // Parse ls -la output
      const parts = line.split(/\s+/);
      if (parts.length < 9) continue;
      
      const permissions = parts[0];
      const isDirectory = permissions.startsWith('d');
      const size = parseInt(parts[4], 10);
      
      // Get file name (might contain spaces)
      const name = parts.slice(8).join(' ');
      
      // Skip . and ..
      if (name === '.' || name === '..') continue;
      
      const filePath = path.join(dir, name);
      
      if (isDirectory) {
        files.push({
          path: filePath,
          type: 'directory',
          lastModified: new Date().toISOString() // We don't have accurate modification time
        });
        
        // Recursively get files in subdirectory
        const childFiles = await getContainerFilesRecursive(containerId, filePath);
        files.push(...childFiles);
      } else {
        files.push({
          path: filePath,
          type: 'file',
          lastModified: new Date().toISOString(), // We don't have accurate modification time
          size
        });
      }
    }
  } catch (error) {
    console.error(`Error reading container directory ${dir}:`, error);
  }
  
  return files;
}

/**
 * Check if a path should be ignored
 */
function shouldIgnore(relativePath: string, ignorePatterns?: string[]): boolean {
  if (!ignorePatterns || ignorePatterns.length === 0) {
    return false;
  }
  
  return ignorePatterns.some(pattern => {
    if (pattern.endsWith('/')) {
      // Directory pattern
      return relativePath.startsWith(pattern) || relativePath.includes(`/${pattern}`);
    } else {
      // File or pattern
      return relativePath === pattern || 
             relativePath.endsWith(`/${pattern}`) || 
             relativePath.includes(`/${pattern}/`);
    }
  });
}
