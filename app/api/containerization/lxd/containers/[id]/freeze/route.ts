import { NextRequest, NextResponse } from 'next/server';
import { LxdClient } from '@/lib/containerization/lxd';

interface RouteParams {
  params: {
    id: string;
  };
}

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * POST /api/containerization/lxd/containers/[id]/freeze
 * Freezes a specific LXD container
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    await lxdClient.freezeContainer(id);
    
    return NextResponse.json(
      { message: `Container ${id} frozen successfully` },
      { status: 200 }
    );
  } catch (error: any) {
    console.error(`Error freezing container ${params.id}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}