import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { LxdClient } from '@/lib/containerization/lxd';
import { createContainerSchema } from './schema';
import { PrismaClient } from '@prisma/client';

// Create a new instance of LxdClient and PrismaClient
const lxdClient = new LxdClient();
const prisma = new PrismaClient();

/**
 * GET /api/containerization/lxd/containers
 * Retrieves a list of all LXD containers
 * - List containers: GET /api/containerization/lxd/containers
 * - List containers for project: GET /api/containerization/lxd/containers?projectId=123
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const projectId = url.searchParams.get('projectId');

    // Get containers
    const containers = await lxdClient.listContainers();

    // If projectId is provided, filter containers by project ID
    if (projectId) {
      // Get container-project associations
      const containerProjects = await prisma.containerProject.findMany({
        where: {
          projectId,
          containerType: 'lxd'
        }
      });

      // Get container IDs associated with the project
      const containerIds = containerProjects.map(cp => cp.containerId);

      // Filter containers by ID
      const filteredContainers = containers.filter(container =>
        containerIds.includes(container.id)
      );

      return NextResponse.json({ containers: filteredContainers }, { status: 200 });
    }

    return NextResponse.json({ containers }, { status: 200 });
  } catch (error: any) {
    console.error('Error listing containers:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST /api/containerization/lxd/containers
 * Creates a new LXD container
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // Validate request body against schema
    const result = createContainerSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }

    const {
      name,
      image,
      profiles = ['default'],
      ephemeral = false,
      stateful = false,
      config = {},
      projectId
    } = result.data;

    const container = await lxdClient.createContainer({
      name,
      image,
      profiles,
      ephemeral,
      stateful,
      config
    });

    // If projectId is provided, associate the container with the project
    if (projectId && session.user?.id) {
      try {
        await prisma.project.update({
          where: { id: projectId },
          data: {
            containers: {
              create: {
                containerId: container.id,
                containerType: 'lxd',
                createdBy: session.user.id
              }
            }
          }
        });

        // Add projectId to the response
        container.projectId = projectId;
      } catch (error) {
        console.error('Error associating container with project:', error);
        // Continue even if association fails
      }
    }

    return NextResponse.json({ container }, { status: 201 });
  } catch (error: any) {
    console.error('Error creating container:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}