import { NextRequest, NextResponse } from 'next/server';
import { LxdClient, NetworkType } from '@/lib/containerization/lxd';
import { createNetworkSchema } from './schema';

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * GET /api/containerization/lxd/networks
 * Retrieves a list of all LXD networks
 */
export async function GET(_request: NextRequest) {
  try {
    const networks = await lxdClient.listNetworks();
    return NextResponse.json({ networks });
  } catch (error: any) {
    console.error('Error listing networks:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST /api/containerization/lxd/networks
 * Creates a new LXD network
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body against schema
    const result = createNetworkSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }

    // Extract data from the validated request
    const { name, description, type, config } = result.data;

    // Create the network with proper type conversion
    await lxdClient.createNetwork({
      name,
      description,
      type: type ? type as NetworkType : NetworkType.BRIDGE, // Default to bridge if not specified
      config
    });

    return NextResponse.json({
      message: "Network created successfully",
      network: name
    });
  } catch (error: any) {
    console.error('Error creating network:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}