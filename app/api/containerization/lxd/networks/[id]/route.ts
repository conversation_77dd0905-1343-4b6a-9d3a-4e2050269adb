import { NextRequest, NextResponse } from 'next/server';
import { LxdClient, NetworkType } from '@/lib/containerization/lxd';
import { updateNetworkSchema, createNetworkSchema } from '../schema';

interface RouteParams {
  params: {
    id: string;
  };
}

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * GET /api/containerization/lxd/networks/[id]
 *
 * Retrieves details of a specific LXD network
 *
 * @param {NextRequest} _request - The request object (unused)
 * @param {RouteParams} params - The route parameters containing the network ID
 *
 * @returns {NextResponse} - JSON response with network details or error
 *
 * @response {200} - Successfully retrieved network details
 *   {
 *     "network": {
 *       "name": "string",
 *       "description": "string",
 *       "type": "bridge|macvlan|sriov|ovn|physical",
 *       "config": { [key: string]: string },
 *       "managed": boolean,
 *       "status": "string",
 *       "locations": string[],
 *       ...
 *     }
 *   }
 *
 * @response {404} - Network not found
 *   {
 *     "error": "Network not found",
 *     "networkId": "string"
 *   }
 *
 * @response {500} - Server error
 *   {
 *     "error": "Error message"
 *   }
 */
export async function GET(_request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;

    try {
      // Get network details
      const networkInfo = await lxdClient.getNetworkInfo(id);

      // Return network details
      return NextResponse.json({
        network: networkInfo
      });
    } catch (error: any) {
      // Check if the error is a "not found" error
      if (error.message.includes('not found') || error.message.includes('No such') || error.message.includes('does not exist')) {
        return NextResponse.json({
          error: 'Network not found',
          networkId: id
        }, { status: 404 });
      }

      // Re-throw for general error handling
      throw error;
    }
  } catch (error: any) {
    console.error(`Error getting network ${params.id}:`, error);
    return NextResponse.json({
      error: error.message || 'An unexpected error occurred'
    }, { status: 500 });
  }
}

/**
 * PATCH /api/containerization/lxd/networks/[id]
 *
 * Partially updates a specific LXD network
 *
 * @param {NextRequest} request - The request object containing the update data
 * @param {RouteParams} params - The route parameters containing the network ID
 *
 * @returns {NextResponse} - JSON response with success message or error
 *
 * @requestBody {
 *   "description": "string", // Optional
 *   "config": { // Optional
 *     [key: string]: string
 *   }
 * }
 *
 * @response {200} - Network updated successfully
 *   {
 *     "message": "Network updated successfully",
 *     "networkId": "string"
 *   }
 *
 * @response {400} - Invalid request body
 *   {
 *     "error": "Invalid request body",
 *     "details": { ... }
 *   }
 *
 * @response {404} - Network not found
 *   {
 *     "error": "Network not found",
 *     "networkId": "string"
 *   }
 *
 * @response {500} - Server error
 *   {
 *     "error": "Error message"
 *   }
 */
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    // Validate request body against schema
    const result = updateNetworkSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }

    try {
      // Update the network
      await lxdClient.updateNetwork(id, result.data);

      return NextResponse.json({
        message: "Network updated successfully",
        networkId: id
      });
    } catch (error: any) {
      // Check if the error is a "not found" error
      if (error.message.includes('not found') || error.message.includes('No such') || error.message.includes('does not exist')) {
        return NextResponse.json({
          error: 'Network not found',
          networkId: id
        }, { status: 404 });
      }

      // Re-throw for general error handling
      throw error;
    }
  } catch (error: any) {
    console.error(`Error updating network ${params.id}:`, error);
    return NextResponse.json({
      error: error.message || 'An unexpected error occurred'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/containerization/lxd/networks/[id]
 *
 * Deletes a specific LXD network
 *
 * @param {NextRequest} _request - The request object (unused)
 * @param {RouteParams} params - The route parameters containing the network ID
 *
 * @returns {NextResponse} - JSON response with success message or error
 *
 * @response {200} - Network deleted successfully
 *   {
 *     "message": "Network deleted successfully",
 *     "networkId": "string"
 *   }
 *
 * @response {404} - Network not found
 *   {
 *     "error": "Network not found",
 *     "networkId": "string"
 *   }
 *
 * @response {409} - Network in use
 *   {
 *     "error": "Network is in use by containers",
 *     "networkId": "string"
 *   }
 *
 * @response {500} - Server error
 *   {
 *     "error": "Error message"
 *   }
 */
export async function DELETE(_request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;

    try {
      // Delete the network
      await lxdClient.deleteNetwork(id);

      return NextResponse.json({
        message: "Network deleted successfully",
        networkId: id
      });
    } catch (error: any) {
      // Check if the error is a "not found" error
      if (error.message.includes('not found') || error.message.includes('No such') || error.message.includes('does not exist')) {
        return NextResponse.json({
          error: 'Network not found',
          networkId: id
        }, { status: 404 });
      }

      // Check if the error is a "network in use" error
      if (error.message.includes('in use') || error.message.includes('being used')) {
        return NextResponse.json({
          error: 'Network is in use by containers',
          networkId: id
        }, { status: 409 });
      }

      // Re-throw for general error handling
      throw error;
    }
  } catch (error: any) {
    console.error(`Error deleting network ${params.id}:`, error);
    return NextResponse.json({
      error: error.message || 'An unexpected error occurred'
    }, { status: 500 });
  }
}

/**
 * PUT /api/containerization/lxd/networks/[id]
 *
 * Completely replaces a specific LXD network configuration
 * Note: This operation may not be supported for all network types
 *
 * @param {NextRequest} request - The request object containing the network configuration
 * @param {RouteParams} params - The route parameters containing the network ID
 *
 * @returns {NextResponse} - JSON response with success message or error
 *
 * @requestBody {
 *   "description": "string",
 *   "type": "bridge|macvlan|sriov|ovn|physical",
 *   "config": {
 *     [key: string]: string
 *   }
 * }
 *
 * @response {200} - Network replaced successfully
 *   {
 *     "message": "Network replaced successfully",
 *     "networkId": "string"
 *   }
 *
 * @response {400} - Invalid request body
 *   {
 *     "error": "Invalid request body",
 *     "details": { ... }
 *   }
 *
 * @response {404} - Network not found
 *   {
 *     "error": "Network not found",
 *     "networkId": "string"
 *   }
 *
 * @response {409} - Network in use or operation not supported
 *   {
 *     "error": "Network is in use or operation not supported",
 *     "networkId": "string"
 *   }
 *
 * @response {500} - Server error
 *   {
 *     "error": "Error message"
 *   }
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    // Validate request body against schema
    // We use the createNetworkSchema but without the name field requirement
    // since the name is already in the URL
    const result = createNetworkSchema.omit({ name: true }).safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }

    try {
      // First check if the network exists
      try {
        await lxdClient.getNetworkInfo(id);
      } catch (error: any) {
        if (error.message.includes('not found') || error.message.includes('No such') || error.message.includes('does not exist')) {
          return NextResponse.json({
            error: 'Network not found',
            networkId: id
          }, { status: 404 });
        }
        throw error;
      }

      // For a PUT operation, we need to:
      // 1. Delete the existing network
      // 2. Create a new network with the same name but new configuration

      try {
        // Delete the existing network
        await lxdClient.deleteNetwork(id);
      } catch (error: any) {
        // If the network is in use, we can't replace it
        if (error.message.includes('in use') || error.message.includes('being used')) {
          return NextResponse.json({
            error: 'Network is in use and cannot be replaced',
            networkId: id
          }, { status: 409 });
        }
        throw error;
      }

      // Create a new network with the same name but new configuration
      const { description, type, config } = result.data;
      await lxdClient.createNetwork({
        name: id,
        description,
        type: type as NetworkType,
        config
      });

      return NextResponse.json({
        message: "Network replaced successfully",
        networkId: id
      });
    } catch (error: any) {
      // Re-throw for general error handling
      throw error;
    }
  } catch (error: any) {
    console.error(`Error replacing network ${params.id}:`, error);
    return NextResponse.json({
      error: error.message || 'An unexpected error occurred'
    }, { status: 500 });
  }
}