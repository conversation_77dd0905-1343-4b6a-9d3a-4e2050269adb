import { z } from 'zod';

// Schema for network creation
export const createNetworkSchema = z.object({
  name: z.string().min(1, "Network name is required"),
  description: z.string().optional(),
  type: z.enum(['bridge', 'macvlan', 'sriov', 'ovn', 'physical']).optional(),
  config: z.record(z.string()).optional(),
});

export type CreateNetworkInput = z.infer<typeof createNetworkSchema>;

// Schema for network update
export const updateNetworkSchema = z.object({
  description: z.string().optional(),
  config: z.record(z.string()).optional(),
});

export type UpdateNetworkInput = z.infer<typeof updateNetworkSchema>;