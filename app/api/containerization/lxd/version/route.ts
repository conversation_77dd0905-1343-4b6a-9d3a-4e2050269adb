import { NextRequest, NextResponse } from 'next/server';
import { LxdClient } from '@/lib/containerization/lxd';

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * GET /api/containerization/lxd/version
 * Retrieves the LXD version
 */
export async function GET(request: NextRequest) {
  try {
    const version = await lxdClient.getVersion();
    return NextResponse.json({ version }, { status: 200 });
  } catch (error: any) {
    console.error('Error getting LXD version:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}