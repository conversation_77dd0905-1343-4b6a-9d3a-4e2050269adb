import { NextRequest, NextResponse } from 'next/server';
import { LxdClient } from '@/lib/containerization/lxd';
import { createSnapshotSchema } from '../schema';

interface RouteParams {
  params: {
    containerId: string;
  };
}

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * GET /api/containerization/lxd/snapshots/[containerId]
 * Retrieves all snapshots for a specific LXD container
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { containerId } = params;
    const snapshots = await lxdClient.listSnapshots(containerId);
    
    return NextResponse.json({ snapshots }, { status: 200 });
  } catch (error: any) {
    console.error(`Error listing snapshots for container ${params.containerId}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST /api/containerization/lxd/snapshots/[containerId]
 * Creates a new snapshot for a specific LXD container
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { containerId } = params;
    const body = await request.json();
    
    // Validate request body against schema
    const result = createSnapshotSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid request body', details: result.error.format() },
        { status: 400 }
      );
    }
    
    const { name, stateful = false } = result.data;
    
    await lxdClient.createSnapshot(containerId, name, stateful);
    
    return NextResponse.json(
      { message: `Snapshot ${name} created for container ${containerId}` },
      { status: 201 }
    );
  } catch (error: any) {
    console.error(`Error creating snapshot for container ${params.containerId}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}