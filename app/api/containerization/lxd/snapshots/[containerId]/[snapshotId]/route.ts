import { NextRequest, NextResponse } from 'next/server';
import { LxdClient } from '@/lib/containerization/lxd';

interface RouteParams {
  params: {
    containerId: string;
    snapshotId: string;
  };
}

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * DELETE /api/containerization/lxd/snapshots/[containerId]/[snapshotId]
 * Deletes a specific snapshot
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { containerId, snapshotId } = params;
    await lxdClient.deleteSnapshot(containerId, snapshotId);
    
    return NextResponse.json(
      { message: `Snapshot ${snapshotId} deleted successfully` },
      { status: 200 }
    );
  } catch (error: any) {
    console.error(`Error deleting snapshot ${params.snapshotId} for container ${params.containerId}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}