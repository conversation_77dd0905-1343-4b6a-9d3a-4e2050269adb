import { NextRequest, NextResponse } from 'next/server';
import { LxdClient } from '@/lib/containerization/lxd';

interface RouteParams {
  params: {
    containerId: string;
    snapshotId: string;
  };
}

// Create a new instance of LxdClient
const lxdClient = new LxdClient();

/**
 * POST /api/containerization/lxd/snapshots/[containerId]/[snapshotId]/restore
 * Restores a container to a specific snapshot
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { containerId, snapshotId } = params;
    await lxdClient.restoreSnapshot(containerId, snapshotId);
    
    return NextResponse.json(
      { message: `Container ${containerId} restored to snapshot ${snapshotId} successfully` },
      { status: 200 }
    );
  } catch (error: any) {
    console.error(`Error restoring snapshot ${params.snapshotId} for container ${params.containerId}:`, error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}