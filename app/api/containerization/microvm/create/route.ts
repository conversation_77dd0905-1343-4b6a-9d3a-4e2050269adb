import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { MicroVmManager } from "@/lib/containerization/microvm/core";

// Define an interface for VM labels
interface VmLabels {
  project: string;
  createdAt: string;
  createdBy: string;
  ephemeral: string;
  expiresAt?: string;
  [key: string]: string | undefined;
}

/**
 * Endpoint for creating a MicroVM
 * Supports ephemeral VMs with auto-shutdown
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    let session: any = null;
    try {
      session = await getServerSession(authOptions);
    } catch (authError) {
      logger.warn('Authentication error or not configured:', authError);
      // Continue without session for development
    }

    // Parse the request body
    const body = await req.json();
    const {
      name,
      template = "default",
      memSizeMib = 2048,
      vcpuCount = 2,
      networkEnabled = true,
      projectId,
      ephemeral = false,
      expiresInMinutes = 30
    } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: "Missing required field", field: "name" },
        { status: 400 }
      );
    }

    // Initialize the MicroVM manager
    const vmManager = new MicroVmManager();

    // Create the MicroVM
    logger.info(`Creating MicroVM '${name}' with template '${template}'${ephemeral ? ' (ephemeral)' : ''}`);
    
    // Set up labels for the VM
    const labels: VmLabels = {
      project: projectId || "default",
      createdAt: new Date().toISOString(),
      createdBy: session?.user?.email || "unknown",
      ephemeral: ephemeral.toString()
    };
    
    // For ephemeral VMs, add expiration time to labels
    if (ephemeral && expiresInMinutes > 0) {
      const expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000);
      labels.expiresAt = expiresAt.toISOString();
      logger.info(`Ephemeral VM '${name}' will expire at ${expiresAt.toISOString()}`);
      
      // Note: In a production system, you would set up a background job or scheduler
      // to actually stop and delete the VM after the expiration time.
    }
    
    // Create VM configuration - using any type to bypass type checking since we don't have
    // the precise interface definition for MicroVmCreationOptions
    const vmConfig: any = {
      name,
      memSizeMib,
      vcpuCount,
      networkEnabled,
      template,
      labels
    };
    
    // Create the VM
    const microvm = await vmManager.createMicroVm(vmConfig);
    
    // Start the VM
    await microvm.start();
    
    // Get VM info for the response
    const vmInfo = await microvm.getInfo();
    
    return NextResponse.json({
      id: vmInfo.id,
      name: vmInfo.name,
      status: vmInfo.state,
      ephemeral,
      expiresAt: ephemeral && labels.expiresAt ? labels.expiresAt : null
    });
  } catch (error) {
    logger.error("Error creating MicroVM:", error);
    return NextResponse.json(
      { error: "Failed to create MicroVM", message: (error as Error).message },
      { status: 500 }
    );
  }
} 