import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { loadImagesForMicroVM, getImageByPath } from '@/lib/containerization/microvm/image-loader';
import { logger } from '@/lib/logger';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

// Create a new MicroVM from a selected image
export async function POST(request: Request) {
  // Check authentication if needed
  let userEmail = 'anonymous';
  try {
    const session = await getServerSession(authOptions);
    if (session && typeof session === 'object' && 'user' in session && 
        session.user && typeof session.user === 'object' && 'email' in session.user) {
      userEmail = session.user.email as string;
    }
  } catch (error) {
    logger.warn('Authentication error or not configured:', error);
  }
  
  try {
    // Parse request body
    const body = await request.json();
    const {
      name,
      kernelImagePath, // Optional: specific kernel image path
      rootfsImagePath, // Optional: specific rootfs image path
      preferredRootfsType, // Optional: preferred type of rootfs (e.g., 'alpine', 'ubuntu')
      memSizeMib = 2048,
      vcpuCount = 2,
      networkEnabled = true,
      projectId = 'default-project',
    } = body;

    // Generate a unique name if not provided
    const vmName = name || `microvm-${Date.now().toString().slice(-6)}`;
    
    logger.info(`Creating MicroVM '${vmName}' with ${preferredRootfsType || 'default'} image`);

    // Load the appropriate images
    let kernelPath: string;
    let rootfsPath: string;

    if (kernelImagePath && rootfsImagePath) {
      // Use the specified images
      logger.info(`Using specified images: Kernel=${kernelImagePath}, Rootfs=${rootfsImagePath}`);
      
      // Verify the images exist
      const kernelImage = await getImageByPath(kernelImagePath);
      const rootfsImage = await getImageByPath(rootfsImagePath);
      
      if (!kernelImage) {
        return NextResponse.json({ error: `Kernel image not found: ${kernelImagePath}` }, { status: 400 });
      }
      
      if (!rootfsImage) {
        return NextResponse.json({ error: `Rootfs image not found: ${rootfsImagePath}` }, { status: 400 });
      }
      
      kernelPath = kernelImagePath;
      rootfsPath = rootfsImagePath;
    } else {
      // Auto-select the best images
      try {
        const result = await loadImagesForMicroVM(preferredRootfsType);
        kernelPath = result.kernelPath;
        rootfsPath = result.rootfsPath;
      } catch (error: any) {
        return NextResponse.json({ error: `Failed to load VM images: ${error.message}` }, { status: 500 });
      }
    }

    // Create the MicroVM
    const microvm = await microVmManager.createMicroVm({
      name: vmName,
      memSizeMib,
      vcpuCount,
      kernel: {
        path: kernelPath,
        bootArgs: "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp",
      },
      rootfs: {
        path: rootfsPath,
        readOnly: false,
      },
      labels: {
        projectId: projectId?.toString() || 'default',
        createdBy: userEmail,
        createdAt: new Date().toISOString(),
        imageType: preferredRootfsType || 'unknown',
      }
    });
    
    // Start the MicroVM
    await microvm.start();
    
    // Get VM info
    const vmInfo = await microvm.getInfo();
    
    return NextResponse.json({
      id: vmInfo.id,
      name: vmInfo.name,
      status: vmInfo.state,
      resources: {
        memSizeMib,
        vcpuCount,
      },
      imageInfo: {
        kernel: kernelPath,
        rootfs: rootfsPath,
        type: preferredRootfsType || 'unknown',
      },
      networkInterfaces: vmInfo.networkInterfaces || [],
      message: `MicroVM '${vmName}' created and started successfully`,
    }, { status: 201 });
    
  } catch (error: any) {
    logger.error('Error creating MicroVM:', error);
    
    return NextResponse.json({ 
      error: 'Failed to create MicroVM', 
      message: error.message || 'Unknown error' 
    }, { status: 500 });
  }
} 