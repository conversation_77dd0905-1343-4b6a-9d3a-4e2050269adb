import { NextResponse } from 'next/server';
import { getAllAvailableImages, getLxcImages } from '@/lib/containerization/microvm/image-loader';

/**
 * GET handler to list all available VM images
 */
export async function GET() {
  try {
    // Get all available VM images
    const { kernelImages, rootfsImages } = await getAllAvailableImages();
    
    // Get LXC images from resources directory
    const lxcImages = await getLxcImages();
    
    return NextResponse.json({
      kernelImages,
      rootfsImages,
      lxcImages,
    }, { status: 200 });
  } catch (error: any) {
    return NextResponse.json({ 
      error: 'Failed to get available VM images', 
      message: error.message || 'Unknown error' 
    }, { status: 500 });
  }
} 