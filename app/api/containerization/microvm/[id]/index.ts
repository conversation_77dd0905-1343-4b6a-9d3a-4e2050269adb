/**
 * MicroVM API Routes for a specific MicroVM
 * 
 * This file provides API routes for managing a specific MicroVM.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const { id } = req.query;
  
  if (!id || Array.isArray(id)) {
    return res.status(400).json({ error: 'Invalid MicroVM ID' });
  }

  switch (req.method) {
    case 'GET':
      return handleGet(id, req, res);
    case 'DELETE':
      return handleDelete(id, req, res);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

/**
 * Handle GET requests
 * @param id MicroVM ID
 * @param req Request
 * @param res Response
 */
async function handleGet(id: string, req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get MicroVM
    const microvm = await microVmManager.getMicroVm(id);
    
    if (!microvm) {
      return res.status(404).json({ error: 'MicroVM not found' });
    }
    
    // Get MicroVM info
    const info = await microvm.getInfo();
    
    return res.status(200).json({
      id: info.id,
      name: info.name,
      state: info.state,
      createdAt: info.metadata?.createdAt,
      updatedAt: info.metadata?.updatedAt,
      projectId: info.metadata?.annotations?.projectId || null,
      memSizeMib: info.config?.memSizeMib,
      vcpuCount: info.config?.vcpuCount,
      drives: info.drives,
      networkInterfaces: info.networkInterfaces,
    });
  } catch (error) {
    console.error(`Error getting MicroVM ${id}:`, error);
    return res.status(500).json({ error: 'Failed to get MicroVM' });
  }
}

/**
 * Handle DELETE requests
 * @param id MicroVM ID
 * @param req Request
 * @param res Response
 */
async function handleDelete(id: string, req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get MicroVM
    const microvm = await microVmManager.getMicroVm(id);
    
    if (!microvm) {
      return res.status(404).json({ error: 'MicroVM not found' });
    }
    
    // Delete MicroVM
    await microVmManager.deleteMicroVm(id);
    
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error(`Error deleting MicroVM ${id}:`, error);
    return res.status(500).json({ error: 'Failed to delete MicroVM' });
  }
}
