/**
 * MicroVM API Route for starting a MicroVM
 * 
 * This file provides an API route for starting a specific MicroVM.
 */

import { NextResponse } from 'next/server';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { Session } from 'next-auth';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  // Check authentication
  const session = await getServerSession(authOptions) as Session | null;
  
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = params;
  
  try {
    // Get MicroVM
    const microvm = await microVmManager.getMicroVm(id);
    
    if (!microvm) {
      return NextResponse.json({ error: 'MicroVM not found' }, { status: 404 });
    }
    
    // Start the MicroVM
    await microvm.start();
    
    // Get updated MicroVM info
    const info = await microvm.getInfo();
    
    return NextResponse.json({
      id: info.id,
      name: info.name,
      status: info.state,
      message: `MicroVM '${info.name}' started successfully`,
    });
  } catch (error) {
    console.error(`Error starting MicroVM '${id}':`, error);
    
    // Handle specific error types
    if (error instanceof Error && error.name === 'InvalidStateError') {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ 
      error: 'Failed to start MicroVM',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 