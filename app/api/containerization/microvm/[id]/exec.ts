/**
 * MicroVM API Route for executing commands in a MicroVM
 * 
 * This file provides an API route for executing commands in a specific MicroVM.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const { id } = req.query;
  
  if (!id || Array.isArray(id)) {
    return res.status(400).json({ error: 'Invalid MicroVM ID' });
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { command, timeout } = req.body;
    
    if (!command) {
      return res.status(400).json({ error: 'Command is required' });
    }
    
    // Get MicroVM
    const microvm = await microVmManager.getMicroVm(id);
    
    if (!microvm) {
      return res.status(404).json({ error: 'MicroVM not found' });
    }
    
    // Check if MicroVM is running
    const info = await microvm.getInfo();
    
    if (info.state !== 'RUNNING') {
      return res.status(400).json({ error: 'MicroVM is not running' });
    }
    
    // Execute command
    const result = await microvm.executeCommand(command, timeout);
    
    return res.status(200).json({
      stdout: result.stdout,
      stderr: result.stderr,
      exitCode: result.exitCode,
    });
  } catch (error) {
    console.error(`Error executing command in MicroVM ${id}:`, error);
    return res.status(500).json({ error: 'Failed to execute command' });
  }
}
