/**
 * MicroVM API Route for terminal WebSocket connection
 * 
 * This file provides an API route for connecting to a MicroVM terminal via WebSocket.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Server } from 'socket.io';
import { createServer } from 'http';
import { parse } from 'url';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const { id } = req.query;
  
  if (!id || Array.isArray(id)) {
    return res.status(400).json({ error: 'Invalid MicroVM ID' });
  }

  // For GET requests, return WebSocket URL
  if (req.method === 'GET') {
    const protocol = req.headers['x-forwarded-proto'] || 'http';
    const host = req.headers.host;
    const wsProtocol = protocol === 'https' ? 'wss' : 'ws';
    
    return res.status(200).json({
      url: `${wsProtocol}://${host}/api/containerization/microvm/${id}/terminal`,
    });
  }

  // For WebSocket connections
  if (req.method === 'POST' && req.headers.upgrade === 'websocket') {
    try {
      // Get MicroVM
      const microvm = await microVmManager.getMicroVm(id as string);
      
      if (!microvm) {
        return res.status(404).json({ error: 'MicroVM not found' });
      }
      
      // Check if MicroVM is running
      const info = await microvm.getInfo();
      
      if (info.state !== 'RUNNING') {
        return res.status(400).json({ error: 'MicroVM is not running' });
      }
      
      // Create WebSocket server
      const server = createServer();
      const io = new Server(server);
      
      // Handle WebSocket connections
      io.on('connection', (socket) => {
        console.log(`WebSocket connection established for MicroVM ${id}`);
        
        // Handle terminal input
        socket.on('data', async (data) => {
          try {
            // Execute command
            const result = await microvm.executeCommand(data);
            
            // Send output back to client
            socket.emit('data', result.stdout);
            
            if (result.stderr) {
              socket.emit('error', result.stderr);
            }
          } catch (error) {
            console.error(`Error executing command in MicroVM ${id}:`, error);
            socket.emit('error', error.message);
          }
        });
        
        // Handle disconnection
        socket.on('disconnect', () => {
          console.log(`WebSocket connection closed for MicroVM ${id}`);
        });
      });
      
      // Start WebSocket server
      server.listen(0, () => {
        const port = (server.address() as any).port;
        console.log(`WebSocket server listening on port ${port} for MicroVM ${id}`);
      });
      
      // Upgrade connection to WebSocket
      const { socket, head } = res.socket;
      const pathname = parse(req.url).pathname;
      
      io.engine.handleUpgrade(req, socket, head, (ws) => {
        io.engine.emit('connection', ws, req);
      });
      
      return res.end();
    } catch (error) {
      console.error(`Error establishing WebSocket connection for MicroVM ${id}:`, error);
      return res.status(500).json({ error: 'Failed to establish WebSocket connection' });
    }
  }

  return res.status(405).json({ error: 'Method not allowed' });
}
