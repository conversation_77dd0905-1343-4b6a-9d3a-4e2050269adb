/**
 * MicroVM API Route for stopping a MicroVM
 * 
 * This file provides an API route for stopping a specific MicroVM.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const { id } = req.query;
  
  if (!id || Array.isArray(id)) {
    return res.status(400).json({ error: 'Invalid MicroVM ID' });
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get MicroVM
    const microvm = await microVmManager.getMicroVm(id);
    
    if (!microvm) {
      return res.status(404).json({ error: 'MicroVM not found' });
    }
    
    // Stop MicroVM
    await microvm.stop();
    
    // Get updated MicroVM info
    const info = await microvm.getInfo();
    
    return res.status(200).json({
      id: info.id,
      name: info.name,
      state: info.state,
      updatedAt: info.metadata?.updatedAt,
    });
  } catch (error) {
    console.error(`Error stopping MicroVM ${id}:`, error);
    return res.status(500).json({ error: 'Failed to stop MicroVM' });
  }
}
