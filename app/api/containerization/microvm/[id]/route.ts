/**
 * MicroVM API Routes for a specific MicroVM
 * 
 * This file provides API routes for managing a specific MicroVM.
 * GET: Get MicroVM details
 * DELETE: Delete a MicroVM
 */

import { NextResponse } from 'next/server';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { Session } from 'next-auth';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

// Get MicroVM details
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  // Check authentication
  const session = await getServerSession(authOptions) as Session | null;
  
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = params;
  
  try {
    // Get MicroVM
    const microvm = await microVmManager.getMicroVm(id);
    
    if (!microvm) {
      return NextResponse.json({ error: 'MicroVM not found' }, { status: 404 });
    }
    
    // Get MicroVM info
    const info = await microvm.getInfo();
    
    // Format the response
    const formattedInfo = {
      id: info.id,
      name: info.name,
      status: info.state,
      template: info.labels?.template || 'unknown',
      resources: {
        memSizeMib: info.memSizeMib || 0,
        vcpuCount: info.vcpuCount || 0,
      },
      networkInterfaces: info.networkInterfaces || [],
      labels: info.labels || {},
      annotations: info.annotations || {},
      createdAt: info.annotations?.createdAt || null,
    };
    
    return NextResponse.json(formattedInfo);
  } catch (error) {
    console.error(`Error getting MicroVM '${id}':`, error);
    
    return NextResponse.json({ 
      error: 'Failed to get MicroVM',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Delete a MicroVM
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  // Check authentication
  const session = await getServerSession(authOptions) as Session | null;
  
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = params;
  
  try {
    // Get MicroVM
    const microvm = await microVmManager.getMicroVm(id);
    
    if (!microvm) {
      return NextResponse.json({ error: 'MicroVM not found' }, { status: 404 });
    }
    
    // Delete MicroVM
    await microVmManager.deleteMicroVm(id);
    
    return NextResponse.json({
      success: true,
      id,
      message: `MicroVM ${id} deleted successfully`
    });
  } catch (error) {
    console.error(`Error deleting MicroVM '${id}':`, error);
    
    // Handle specific error types
    if (error instanceof Error && error.name === 'InvalidStateError') {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ 
      error: 'Failed to delete MicroVM',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 