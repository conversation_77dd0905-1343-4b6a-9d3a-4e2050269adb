/**
 * MicroVM API Route for fetching metrics from a MicroVM
 * 
 * This file provides an API route for getting metrics from a specific MicroVM.
 */

import { NextResponse } from 'next/server';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { Session } from 'next-auth';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  // Check authentication
  const session = await getServerSession(authOptions) as Session | null;
  
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = params;
  
  try {
    // Get MicroVM
    const microvm = await microVmManager.getMicroVm(id);
    
    if (!microvm) {
      return NextResponse.json({ error: 'MicroVM not found' }, { status: 404 });
    }
    
    // Get metrics
    const metrics = await microvm.getMetrics();
    
    return NextResponse.json(metrics);
  } catch (error) {
    console.error(`Error getting metrics for MicroVM '${id}':`, error);
    
    // Handle specific error types
    if (error instanceof Error && error.name === 'InvalidStateError') {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ 
      error: 'Failed to get MicroVM metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 