/**
 * MicroVM API Routes
 *
 * This file provides API routes for managing MicroVMs.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { MicroVmCreationOptions } from '@/lib/containerization/microvm/models';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);

  if (!session?.user) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  switch (req.method) {
    case 'GET':
      return handleGet(req, res);
    case 'POST':
      return handlePost(req, res);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

/**
 * Handle GET requests
 * @param req Request
 * @param res Response
 */
async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { projectId } = req.query;

    // Get all MicroVMs
    const microvms = await microVmManager.listMicroVms();

    // Filter by project ID if provided
    const filteredMicrovms = projectId
      ? microvms.filter(vm => vm.getInfo().then(info => info.metadata?.annotations?.projectId === projectId))
      : microvms;

    // Get info for each MicroVM
    const microVmInfoPromises = filteredMicrovms.map(vm => vm.getInfo());
    const microVmInfos = await Promise.all(microVmInfoPromises);

    return res.status(200).json({
      microvms: microVmInfos.map(info => ({
        id: info.id,
        name: info.name,
        state: info.state,
        createdAt: info.metadata?.createdAt,
        updatedAt: info.metadata?.updatedAt,
        projectId: info.metadata?.annotations?.projectId || null,
      })),
    });
  } catch (error) {
    console.error('Error getting MicroVMs:', error);
    return res.status(500).json({ error: 'Failed to get MicroVMs' });
  }
}

/**
 * Handle POST requests
 * @param req Request
 * @param res Response
 */
async function handlePost(req: NextApiRequest, res: NextApiResponse) {
  try {
    const {
      name,
      memSizeMib = 1024,
      vcpuCount = 2,
      rootfsPath,
      kernelImagePath,
      networkInterfaces,
      projectId,
      diskSizeGb = 5,
    } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }

    // Use provided paths or default to the ones we set up
    const rootfs = rootfsPath || '/var/lib/firecracker/rootfs/bionic.rootfs.ext4';
    const kernel = kernelImagePath || '/var/lib/firecracker/kernels/vmlinux.bin';

    // Create MicroVM creation options
    const options: MicroVmCreationOptions = {
      name,
      memSizeMib,
      vcpuCount,
      rootfs: {
        path: rootfs,
        readOnly: false,
      },
      kernel: {
        path: kernel,
        bootArgs: 'console=ttyS0 reboot=k panic=1 pci=off',
      },
      diskSizeGb: diskSizeGb,
      networkInterfaces: networkInterfaces || [],
      annotations: {
        projectId: projectId || null,
      },
    };

    // Create MicroVM
    const microvm = await microVmManager.createMicroVm(options);

    // Get MicroVM info
    const info = await microvm.getInfo();

    return res.status(201).json({
      id: info.id,
      name: info.name,
      state: info.state,
      createdAt: info.metadata?.createdAt,
      updatedAt: info.metadata?.updatedAt,
      projectId: info.metadata?.annotations?.projectId || null,
    });
  } catch (error) {
    console.error('Error creating MicroVM:', error);
    return res.status(500).json({
      error: 'Failed to create MicroVM',
      details: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}
