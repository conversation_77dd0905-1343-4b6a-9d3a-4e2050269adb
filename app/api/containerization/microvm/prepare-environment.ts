/**
 * MicroVM Environment Preparation API
 * 
 * This file provides an API route for preparing the environment for MicroVMs.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import util from 'util';

const execAsync = util.promisify(exec);

// Directories that need to exist
const requiredDirs = [
  '/tmp/microvms',
  '/tmp/firecracker-sockets',
  '/var/lib/firecracker',
  '/var/lib/firecracker/kernels',
  '/var/lib/firecracker/rootfs',
];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  
  if (!session?.user) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Create required directories
    for (const dir of requiredDirs) {
      await createDirWithPermissions(dir);
    }

    // Check if kernel and rootfs files exist
    const kernelPath = '/var/lib/firecracker/kernels/vmlinux.bin';
    const rootfsPath = '/var/lib/firecracker/rootfs/bionic.rootfs.ext4';

    const kernelExists = await fileExists(kernelPath);
    const rootfsExists = await fileExists(rootfsPath);

    // Return success with status of kernel and rootfs files
    return res.status(200).json({
      success: true,
      kernelExists,
      rootfsExists,
      message: 'Environment prepared successfully',
    });
  } catch (error) {
    console.error('Error preparing environment:', error);
    return res.status(500).json({
      error: 'Failed to prepare environment',
      details: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Create a directory with proper permissions
 * @param dirPath Directory path
 */
async function createDirWithPermissions(dirPath: string): Promise<void> {
  try {
    // Check if directory exists
    try {
      await fs.promises.access(dirPath, fs.constants.F_OK);
      console.log(`Directory ${dirPath} already exists`);
    } catch (error) {
      // Directory doesn't exist, create it
      console.log(`Creating directory ${dirPath}`);
      await fs.promises.mkdir(dirPath, { recursive: true });
    }

    // Set permissions (777 for development, should be more restrictive in production)
    await fs.promises.chmod(dirPath, 0o777);
    
    // If this is running as root, change ownership to the current user
    if (process.getuid && process.getuid() === 0) {
      const userId = parseInt(process.env.SUDO_UID || '1000', 10);
      const groupId = parseInt(process.env.SUDO_GID || '1000', 10);
      
      await fs.promises.chown(dirPath, userId, groupId);
    }
  } catch (error) {
    console.error(`Error creating directory ${dirPath}:`, error);
    
    // Try with sudo if permission denied
    if (error instanceof Error && error.message.includes('permission denied')) {
      try {
        await execAsync(`sudo mkdir -p ${dirPath} && sudo chmod 777 ${dirPath}`);
        console.log(`Created directory ${dirPath} with sudo`);
      } catch (sudoError) {
        console.error(`Error creating directory ${dirPath} with sudo:`, sudoError);
        throw sudoError;
      }
    } else {
      throw error;
    }
  }
}

/**
 * Check if a file exists
 * @param filePath File path
 * @returns True if file exists, false otherwise
 */
async function fileExists(filePath: string): Promise<boolean> {
  try {
    await fs.promises.access(filePath, fs.constants.F_OK);
    return true;
  } catch (error) {
    return false;
  }
}
