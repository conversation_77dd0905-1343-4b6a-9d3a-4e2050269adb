import type { NextApiRequest, NextApiResponse } from 'next';
import { MicroVmManager } from '@/lib/containerization/microvm/core/microvm-manager';
import { MicroVmCreationOptions } from '@/lib/containerization/microvm/models';
import { logger } from '@/lib/logger';
import path from 'path';
import os from 'os';

// TODO: Replace these with actual paths to your prepared kernel and AI-ready rootfs
const DEFAULT_KERNEL_PATH = process.env.DEFAULT_KERNEL_PATH || '/path/to/your/default-vmlinux.bin';
const DEFAULT_AI_ROOTFS_PATH = process.env.DEFAULT_AI_ROOTFS_PATH || '/path/to/your/ai-rootfs.ext4';
const FIRECRACKER_BINARY_PATH = process.env.FIRECRACKER_BINARY_PATH || '/usr/local/bin/firecracker'; // Or wherever it's installed

const baseMicroVmDir = path.join(os.tmpdir(), 'app-gen-microvms');

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const { name: requestedName } = req.body;
    const vmName = requestedName || `ai-builder-vm-${Date.now().toString().slice(-6)}`;

    logger.info(`Received request to create MicroVM: ${vmName}`);

    // Ensure essential paths are configured
    if (DEFAULT_KERNEL_PATH === '/path/to/your/default-vmlinux.bin' || DEFAULT_AI_ROOTFS_PATH === '/path/to/your/ai-rootfs.ext4') {
        logger.error('Critical: DEFAULT_KERNEL_PATH or DEFAULT_AI_ROOTFS_PATH is not configured in environment variables or has default placeholder values.');
        return res.status(500).json({ message: 'Server configuration error: Kernel or RootFS path not set.' });
    }
    if (FIRECRACKER_BINARY_PATH === '/usr/local/bin/firecracker' && process.env.NODE_ENV === 'production') {
        // In production, we should be more strict about the firecracker path being explicitly set.
        // For development, the default might be okay if the user knows where it is.
         logger.warn('FIRECRACKER_BINARY_PATH is using a default value. Ensure this is correct for your environment.');
    }


    const microVmManager = new MicroVmManager({
      baseDir: baseMicroVmDir,
      firecrackerBinaryPath: FIRECRACKER_BINARY_PATH,
      defaultKernelPath: DEFAULT_KERNEL_PATH,
      defaultRootfsPath: DEFAULT_AI_ROOTFS_PATH,
    });

    const creationOptions: MicroVmCreationOptions = {
      name: vmName,
      memSizeMib: process.env.DEFAULT_VM_MEM_MIB ? parseInt(process.env.DEFAULT_VM_MEM_MIB) : 2048, // e.g., 2GB
      vcpuCount: process.env.DEFAULT_VM_VCPU_COUNT ? parseInt(process.env.DEFAULT_VM_VCPU_COUNT) : 2,
      kernel: {
        path: DEFAULT_KERNEL_PATH,
        // bootArgs: "console=ttyS0 reboot=k panic=1 pci=off quiet random.trust_cpu=on" // Common minimal boot args
        bootArgs: "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp", // Added ip=dhcp for networking
      },
      rootfs: {
        path: DEFAULT_AI_ROOTFS_PATH,
        readOnly: false, // Important for AI to install tools, write code. Manager will handle overlay.
      },
      // Basic network interface, manager will assign if not specified
      // networkInterfaces: [{ id: 'eth0', hostDevName: 'tap0' }], // Example if you want specific config
      // seccompEnabled: true, // Recommended for security
      // jailerEnabled: true, // Recommended for security, requires more setup for paths
    };

    logger.info('Creating MicroVM with options:', creationOptions);
    const microvm = await microVmManager.createMicroVm(creationOptions);

    // The MicroVmManager's createMicroVm already starts the Firecracker *process*.
    // The MicroVm instance then needs to be configured and the VM started.
    // The `microvm.start()` method in the provided `microvm.ts` should handle calling `configure()` if needed.
    
    logger.info(`Attempting to start MicroVM: ${microvm.getId()}`);
    await microvm.start(); // This should configure and then start the VM instance.

    const vmInfo = await microvm.getInfo();

    logger.info(`MicroVM '${vmInfo.name}' created and started successfully with ID: ${vmInfo.id}, State: ${vmInfo.state}`);

    return res.status(201).json({
      id: vmInfo.id,
      name: vmInfo.name,
      status: vmInfo.state, // Should be RUNNING
      message: 'MicroVM created successfully',
    });

  } catch (error: any) {
    logger.error('Error creating MicroVM:', error);
    let errorMessage = 'Failed to create MicroVM.';
    if (error instanceof Error) {
        errorMessage = error.message;
    } else if (typeof error === 'string') {
        errorMessage = error;
    }
    
    // Check for specific error types if your error classes have a unique property
    if (error.name === 'AlreadyExistsError') {
        return res.status(409).json({ message: errorMessage });
    }
    if (error.message && error.message.includes('ENOENT')) { // File not found
        return res.status(500).json({ message: `Server configuration error: A required file was not found. Details: ${errorMessage}`});
    }

    return res.status(500).json({ message: errorMessage, details: error.stack });
  }
} 