# MicroVM API Routes

This directory contains API routes for managing MicroVMs (lightweight virtual machines) using the Next.js App Router.

## Endpoints

### `/api/containerization/microvm`
- **GET**: List all MicroVMs
  - Query parameters:
    - `projectId`: Filter by project ID
    - `status`: Filter by VM status
    - `template`: Filter by template type
  - Returns an array of MicroVM objects

- **POST**: Create a new MicroVM
  - Body parameters:
    - `name`: (optional) Name for the MicroVM
    - `template`: (optional) Template to use ('nodejs', 'python', 'full-stack', 'minimal')
    - `memSizeMib`: (optional) Memory size in MiB
    - `vcpuCount`: (optional) Number of virtual CPUs
    - `networkEnabled`: (optional) Whether networking is enabled
    - `projectId`: (optional) ID of the project this MicroVM belongs to
  - Returns the created MicroVM object

### `/api/containerization/microvm/[id]`
- **GET**: Get details for a specific MicroVM
  - Returns the MicroVM object

- **DELETE**: Delete a specific MicroVM
  - Returns a success message

### `/api/containerization/microvm/[id]/start`
- **POST**: Start a specific MicroVM
  - Returns the updated MicroVM object

### `/api/containerization/microvm/[id]/stop`
- **POST**: Stop a specific MicroVM
  - Returns the updated MicroVM object

### `/api/containerization/microvm/[id]/restart`
- **POST**: Restart a specific MicroVM
  - Returns the updated MicroVM object

### `/api/containerization/microvm/[id]/metrics`
- **GET**: Get metrics for a specific MicroVM
  - Returns a metrics object containing:
    - `cpuUsage`: CPU usage percentage
    - `memoryUsage`: Memory usage in bytes
    - `networkRx`: Network receive in bytes
    - `networkTx`: Network transmit in bytes
    - `diskRead`: Disk read in bytes
    - `diskWrite`: Disk write in bytes
    - `uptime`: Uptime in seconds

## Authentication

All endpoints require authentication using Next Auth. The user must be logged in to access these endpoints.

## Error Handling

All endpoints include proper error handling and will return:
- `401` for unauthorized access
- `404` for MicroVMs that don't exist
- `400` for invalid operations
- `500` for server errors

## Examples

### Creating a MicroVM

```js
const response = await fetch('/api/containerization/microvm', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'my-nodejs-vm',
    template: 'nodejs',
    memSizeMib: 2048,
    vcpuCount: 2,
    projectId: 'project-123',
  }),
});

const data = await response.json();
```

### Listing MicroVMs

```js
const response = await fetch('/api/containerization/microvm');
const vms = await response.json();
```

### Managing a MicroVM

```js
// Starting a MicroVM
await fetch(`/api/containerization/microvm/${vmId}/start`, { method: 'POST' });

// Stopping a MicroVM
await fetch(`/api/containerization/microvm/${vmId}/stop`, { method: 'POST' });

// Restarting a MicroVM
await fetch(`/api/containerization/microvm/${vmId}/restart`, { method: 'POST' });

// Deleting a MicroVM
await fetch(`/api/containerization/microvm/${vmId}`, { method: 'DELETE' });

// Getting metrics
const response = await fetch(`/api/containerization/microvm/${vmId}/metrics`);
const metrics = await response.json();
``` 