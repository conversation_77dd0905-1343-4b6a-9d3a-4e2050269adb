import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { findBestLxcImage, getLxcImages } from '@/lib/containerization/microvm/image-loader';
import { logger } from '@/lib/logger';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { v4 as uuidv4 } from 'uuid';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

// Create a new MicroVM using an LXC image
export async function POST(request: Request) {
  // Check authentication if needed
  let userEmail = 'anonymous';
  try {
    const session = await getServerSession(authOptions);
    if (session && typeof session === 'object' && 'user' in session && 
        session.user && typeof session.user === 'object' && 'email' in session.user) {
      userEmail = session.user.email as string;
    }
  } catch (error) {
    logger.warn('Authentication error or not configured:', error);
  }
  
  try {
    // Parse request body
    const body = await request.json();
    const {
      name,
      imageType = 'ubuntu', // 'alpine' or 'ubuntu'
      memSizeMib = 2048,
      vcpuCount = 2,
      networkEnabled = true,
      projectId = 'default-project',
    } = body;

    // Generate a unique name if not provided
    const vmName = name || `lxc-vm-${Date.now().toString().slice(-6)}`;
    
    logger.info(`Creating MicroVM '${vmName}' with ${imageType} image`);

    // Find the best LXC image for the specified type
    const lxcImages = await getLxcImages();
    const selectedImage = findBestLxcImage(lxcImages, imageType as any);
    
    if (!selectedImage) {
      return NextResponse.json({ error: `No ${imageType} LXC image found` }, { status: 400 });
    }
    
    // Create a temporary working directory for VM files
    const vmId = uuidv4();
    const workDir = path.join(os.tmpdir(), `lxc-microvm-${vmId}`);
    await fs.promises.mkdir(workDir, { recursive: true });
    
    logger.info(`Using LXC image: ${selectedImage.name} (${selectedImage.path})`);
    logger.info(`Working directory: ${workDir}`);
    
    try {
      // Process the image based on its format
      let rootfsPath = '';
      
      if (selectedImage.format === 'tar.gz') {
        // For tar.gz format (like Alpine), create an ext4 image and extract the tar.gz into it
        rootfsPath = path.join(workDir, 'rootfs.ext4');
        
        // Create an empty ext4 image
        logger.info('Creating ext4 image from tar.gz...');
        await execAsync(`dd if=/dev/zero of=${rootfsPath} bs=1M count=2048`);
        await execAsync(`mkfs.ext4 ${rootfsPath}`);
        
        // Mount the image, extract the tar.gz, and unmount
        const mountPoint = path.join(workDir, 'mnt');
        await fs.promises.mkdir(mountPoint, { recursive: true });
        
        await execAsync(`mount -o loop ${rootfsPath} ${mountPoint}`);
        await execAsync(`tar -xzf ${selectedImage.path} -C ${mountPoint}`);
        await execAsync(`umount ${mountPoint}`);
        
        logger.info('Ext4 image created successfully from tar.gz');
      } else if (selectedImage.format === 'img') {
        // For img format (like Ubuntu cloud images), use it directly
        rootfsPath = selectedImage.path;
        logger.info('Using img format directly');
      } else {
        throw new Error(`Unsupported image format: ${selectedImage.format}`);
      }
      
      // Find or create a kernel image
      // For LXC images, we might need to extract the kernel from the image or use a default one
      const kernelPath = '/boot/vmlinuz-' + (await execAsync('uname -r')).stdout.trim();
      if (!fs.existsSync(kernelPath)) {
        throw new Error(`Kernel not found at ${kernelPath}`);
      }
      
      // Create the MicroVM
      const microvm = await microVmManager.createMicroVm({
        name: vmName,
        memSizeMib,
        vcpuCount,
        kernel: {
          path: kernelPath,
          bootArgs: "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp",
        },
        rootfs: {
          path: rootfsPath,
          readOnly: false,
        },
        labels: {
          projectId: projectId?.toString() || 'default',
          createdBy: userEmail,
          createdAt: new Date().toISOString(),
          imageType: selectedImage.type,
          imageFormat: selectedImage.format,
        }
      });
      
      // Start the MicroVM
      await microvm.start();
      
      // Get VM info
      const vmInfo = await microvm.getInfo();
      
      return NextResponse.json({
        id: vmInfo.id,
        name: vmInfo.name,
        status: vmInfo.state,
        resources: {
          memSizeMib,
          vcpuCount,
        },
        imageInfo: {
          name: selectedImage.name,
          type: selectedImage.type,
          format: selectedImage.format,
        },
        networkInterfaces: vmInfo.networkInterfaces || [],
        message: `MicroVM '${vmName}' created and started successfully with ${imageType} image`,
      }, { status: 201 });
      
    } catch (error) {
      // Clean up the working directory if an error occurred
      try {
        await fs.promises.rmdir(workDir, { recursive: true });
      } catch (cleanupError) {
        logger.error('Failed to clean up working directory:', cleanupError);
      }
      throw error;
    }
    
  } catch (error: any) {
    logger.error('Error creating MicroVM with LXC image:', error);
    
    return NextResponse.json({ 
      error: 'Failed to create MicroVM with LXC image', 
      message: error.message || 'Unknown error' 
    }, { status: 500 });
  }
} 