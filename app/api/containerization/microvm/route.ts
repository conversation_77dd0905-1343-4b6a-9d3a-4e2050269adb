/**
 * MicroVM API Routes
 * 
 * This file provides the main API routes for MicroVMs.
 * GET: List all MicroVMs
 * POST: Create a new MicroVM
 */

import { NextResponse } from 'next/server';
import { MicroVmManager } from '@/lib/containerization/microvm/core';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// Initialize the MicroVM manager
const microVmManager = new MicroVmManager();

// List all MicroVMs
export async function GET(request: Request) {
  // Check authentication
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    // Get query parameters from URL
    const url = new URL(request.url);
    const projectId = url.searchParams.get('projectId');
    const status = url.searchParams.get('status');
    const template = url.searchParams.get('template');
    
    // List all MicroVMs
    const vms = await microVmManager.listMicroVms();
    
    // Apply filters if specified
    let filteredVms = vms;
    
    if (projectId) {
      filteredVms = filteredVms.filter(vm => 
        vm.metadata?.annotations?.projectId === projectId
      );
    }
    
    if (status) {
      filteredVms = filteredVms.filter(vm => 
        vm.state.toLowerCase() === status.toString().toLowerCase()
      );
    }
    
    if (template) {
      filteredVms = filteredVms.filter(vm => 
        vm.metadata?.annotations?.template === template
      );
    }

    // Format the response
    const formattedVms = filteredVms.map(vm => ({
      id: vm.id,
      name: vm.name,
      status: vm.state,
      template: vm.metadata?.annotations?.template || 'unknown',
      resources: {
        memSizeMib: vm.config?.memSizeMib || 0,
        vcpuCount: vm.config?.vcpuCount || 0,
      },
      networkInterfaces: vm.networkInterfaces || [],
      labels: vm.metadata?.labels || {},
      annotations: vm.metadata?.annotations || {},
      createdAt: vm.metadata?.createdAt || null,
    }));
    
    return NextResponse.json(formattedVms);
  } catch (error) {
    console.error('Error listing MicroVMs:', error);
    return NextResponse.json({ 
      error: 'Failed to list MicroVMs',
      message: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}

// Create a new MicroVM
export async function POST(request: Request) {
  // Check authentication
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    // Parse request body
    const body = await request.json();
    const {
      name,
      template = 'nodejs',
      memSizeMib = 2048,
      vcpuCount = 2,
      networkEnabled = true,
      projectId,
    } = body;

    // Generate a unique name if not provided
    const vmName = name || `ai-vm-${Date.now().toString().slice(-6)}`;
    
    console.log(`Creating MicroVM '${vmName}' with template '${template}'`);

    // Create the MicroVM
    const microvm = await microVmManager.createMicroVm({
      name: vmName,
      template,
      memSizeMib,
      vcpuCount,
      networkEnabled,
      metadata: {
        annotations: {
          projectId: projectId?.toString(),
          createdBy: session.user.email || 'unknown',
          createdAt: new Date().toISOString(),
        }
      }
    });
    
    // Start the MicroVM
    await microvm.start();
    
    // Get VM info
    const vmInfo = await microvm.getInfo();
    
    return NextResponse.json({
      id: vmInfo.id,
      name: vmInfo.name,
      status: vmInfo.state,
      template,
      resources: {
        memSizeMib: vmInfo.config?.memSizeMib || memSizeMib,
        vcpuCount: vmInfo.config?.vcpuCount || vcpuCount,
      },
      networkInterfaces: vmInfo.networkInterfaces || [],
      message: `MicroVM '${vmName}' created and started successfully`,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating MicroVM:', error);
    
    if (error instanceof Error) {
      // Handle specific error types
      if (error.name === 'AlreadyExistsError') {
        return NextResponse.json({ error: error.message }, { status: 409 });
      }
      
      if (error.name === 'ResourceExhaustionError') {
        return NextResponse.json({ error: error.message }, { status: 507 });
      }
      
      if (error.message.includes('ENOENT')) {
        return NextResponse.json({ 
          error: 'Server configuration error: A required file was not found',
          details: error.message
        }, { status: 500 });
      }
    }
    
    // Generic error
    return NextResponse.json({ 
      error: 'Failed to create MicroVM',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 