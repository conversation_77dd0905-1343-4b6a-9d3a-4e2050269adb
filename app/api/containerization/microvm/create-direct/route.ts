import { NextResponse } from 'next/server';
import * as fs from 'fs';
import * as path from 'path';
import { spawn } from 'child_process';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Simple console-based logger implementation
const logger = {
  info: (...args: any[]) => console.log('[INFO]', ...args),
  warn: (...args: any[]) => console.warn('[WARN]', ...args),
  error: (...args: any[]) => console.error('[ERROR]', ...args),
  debug: (...args: any[]) => console.log('[DEBUG]', ...args)
};

export async function POST(request: Request) {
  try {
    // Parse the request body
    const body = await request.json();

    // Extract parameters
    const { 
      name, 
      id,
      socketPath,
      kernelPath, 
      rootfsPath, 
      memSizeMib = 2048, 
      vcpuCount = 2,
      bootArgs = "console=ttyS0 reboot=k panic=1 pci=off quiet ip=dhcp"
    } = body;

    if (!name || !kernelPath || !rootfsPath) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // Ensure Firecracker binary exists
    const firecrackerPath = await findFirecrackerBinary();
    if (!firecrackerPath) {
      return NextResponse.json({ error: 'Firecracker binary not found' }, { status: 500 });
    }

    // Ensure kernel and rootfs exist
    if (!fs.existsSync(kernelPath)) {
      return NextResponse.json({ error: `Kernel not found: ${kernelPath}` }, { status: 400 });
    }

    if (!fs.existsSync(rootfsPath)) {
      return NextResponse.json({ error: `Rootfs not found: ${rootfsPath}` }, { status: 400 });
    }

    // Create socket directory if it doesn't exist
    const socketDir = path.dirname(socketPath);
    if (!fs.existsSync(socketDir)) {
      fs.mkdirSync(socketDir, { recursive: true });
    }

    // Remove socket file if it exists
    if (fs.existsSync(socketPath)) {
      fs.unlinkSync(socketPath);
    }

    // Start Firecracker process
    logger.info(`Starting Firecracker process with socket: ${socketPath}`);
    
    const fcProcess = spawn(firecrackerPath, ['--api-sock', socketPath], {
      detached: true, // Detach process so it keeps running
      stdio: 'ignore' // Don't capture stdout/stderr
    });
    
    // Unref the process to allow Node to exit while Firecracker runs
    fcProcess.unref();

    // Wait for socket to be created
    await waitForSocket(socketPath);

    // Configure the VM
    await configureVm(socketPath, {
      kernelPath,
      bootArgs,
      rootfsPath,
      memSizeMib,
      vcpuCount
    });

    // Record VM details for later reference
    const vmDetails = {
      id: id || name,
      name,
      socketPath,
      kernelPath,
      rootfsPath,
      memSizeMib,
      vcpuCount,
      createdAt: new Date().toISOString(),
      status: 'running'
    };
    
    // Save VM details to a file for tracking
    const metadataDir = path.join(socketDir, 'metadata');
    if (!fs.existsSync(metadataDir)) {
      fs.mkdirSync(metadataDir, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(metadataDir, 'vm.json'), 
      JSON.stringify(vmDetails, null, 2)
    );

    return NextResponse.json({ 
      success: true, 
      vmId: id || name,
      message: 'MicroVM created directly',
      details: vmDetails
    });
    
  } catch (error) {
    logger.error('Error creating direct MicroVM:', error);
    return NextResponse.json({ 
      error: 'Failed to create MicroVM', 
      details: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
}

/**
 * Find the Firecracker binary
 */
async function findFirecrackerBinary(): Promise<string | null> {
  const commonPaths = [
    '/usr/bin/firecracker',
    '/usr/local/bin/firecracker',
    '/opt/firecracker/firecracker'
  ];

  for (const path of commonPaths) {
    if (fs.existsSync(path)) {
      return path;
    }
  }

  try {
    // Try to find it in PATH
    const { stdout } = await execAsync('which firecracker');
    const path = stdout.trim();
    if (path && fs.existsSync(path)) {
      return path;
    }
  } catch (error) {
    // Ignore error
  }

  return null;
}

/**
 * Wait for socket file to be created
 */
async function waitForSocket(socketPath: string, timeoutMs: number = 5000): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeoutMs) {
    if (fs.existsSync(socketPath)) {
      // Wait a bit more to ensure Firecracker is ready
      await new Promise(resolve => setTimeout(resolve, 200));
      return;
    }
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  throw new Error(`Timed out waiting for socket: ${socketPath}`);
}

/**
 * Configure the VM through the Firecracker API
 */
async function configureVm(socketPath: string, config: {
  kernelPath: string;
  bootArgs: string;
  rootfsPath: string;
  memSizeMib: number;
  vcpuCount: number;
}): Promise<void> {
  // Helper function to send PUT requests
  const putRequest = async (path: string, body: any): Promise<void> => {
    const curl = `curl --unix-socket ${socketPath} -X PUT http://localhost${path} \
      -H "Content-Type: application/json" \
      -d '${JSON.stringify(body)}'`;
    
    try {
      await execAsync(curl);
    } catch (error) {
      logger.error(`Error sending request to ${path}:`, error);
      throw new Error(`Failed to send request to ${path}`);
    }
  };

  // 1. Set boot source
  await putRequest('/boot-source', {
    kernel_image_path: config.kernelPath,
    boot_args: config.bootArgs
  });

  // 2. Set rootfs drive
  await putRequest('/drives/rootfs', {
    drive_id: 'rootfs',
    path_on_host: config.rootfsPath,
    is_root_device: true,
    is_read_only: false
  });

  // 3. Set machine config
  await putRequest('/machine-config', {
    vcpu_count: config.vcpuCount,
    mem_size_mib: config.memSizeMib
  });

  // 4. Start the VM
  await putRequest('/actions', { action_type: 'InstanceStart' });
} 