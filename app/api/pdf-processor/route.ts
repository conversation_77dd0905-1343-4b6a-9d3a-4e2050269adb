/**
 * PDF Processor API Route
 * 
 * This file contains API routes for processing PDF content.
 */

import { NextRequest, NextResponse } from 'next/server';
import { extractTextFromPdf, extractTablesFromPdf } from '@/lib/research/utils/pdf';

/**
 * POST handler for PDF processing
 * - Process PDF: POST /api/pdf-processor
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { pdfContent, query, operation = 'extract-text' } = body;
    
    // Validate the PDF content
    if (!pdfContent) {
      return NextResponse.json(
        { error: 'PDF content is required' },
        { status: 400 }
      );
    }
    
    let result;
    
    // Process the PDF based on the operation
    switch (operation) {
      case 'extract-text':
        result = await extractTextFromPdf(pdfContent);
        break;
      case 'extract-tables':
        result = await extractTablesFromPdf(pdfContent);
        break;
      case 'search':
        if (!query) {
          return NextResponse.json(
            { error: 'Query is required for search operation' },
            { status: 400 }
          );
        }
        
        // Extract text first
        const text = await extractTextFromPdf(pdfContent);
        
        // Perform search
        const lines = text.split('\n');
        const matchingLines = lines.filter(line => 
          line.toLowerCase().includes(query.toLowerCase())
        );
        
        result = {
          query,
          matches: matchingLines.length,
          matchingText: matchingLines.join('\n'),
          summary: `Found ${matchingLines.length} matches for "${query}" in the PDF.`
        };
        break;
      default:
        return NextResponse.json(
          { error: `Unsupported operation: ${operation}` },
          { status: 400 }
        );
    }
    
    // Return the result
    return NextResponse.json({
      success: true,
      result,
      operation
    });
  } catch (error: any) {
    console.error('Error processing PDF:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process PDF' },
      { status: 500 }
    );
  }
}
