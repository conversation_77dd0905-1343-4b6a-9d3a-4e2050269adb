/**
 * PDF Requirements Extractor API Route
 * 
 * This file contains the API route for extracting project requirements from PDF content.
 */

import { NextRequest, NextResponse } from 'next/server';
import { openai } from "@ai-sdk/openai";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";

/**
 * POST handler for PDF requirements extraction
 * - Extract requirements: POST /api/pdf-processor/extract-requirements
 */
export async function POST(request: NextRequest) {
  try {
    // Verify user authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { pdfText } = body;
    
    // Validate the PDF content
    if (!pdfText) {
      return NextResponse.json(
        { error: 'PDF text is required' },
        { status: 400 }
      );
    }

    // Use OpenAI to extract project requirements from the PDF text
    const response = await openai.chat({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert project requirements analyzer. Your task is to extract structured project requirements from PDF text. 
          Analyze the text carefully and identify:
          1. Project type (web, mobile, backend, fullstack, api)
          2. Key features
          3. Technologies mentioned or implied
          4. Overall project description
          5. Complexity level (low, medium, high)
          
          Return the information in a structured JSON format with these fields:
          - projectType: string (one of: web, mobile, backend, fullstack, api)
          - features: string[] (list of key features)
          - technologies: string[] (list of technologies)
          - description: string (concise project description)
          - complexity: string (low, medium, or high)
          
          If the PDF doesn't contain enough information, make reasonable inferences based on the available content.`
        },
        {
          role: "user",
          content: `Extract project requirements from the following PDF text:\n\n${pdfText.substring(0, 15000)}`
        }
      ],
      temperature: 0.2,
      response_format: { type: "json_object" }
    });

    // Parse the response
    const requirements = JSON.parse(response.choices[0].message.content);
    
    // Return the extracted requirements
    return NextResponse.json({
      success: true,
      requirements
    });
  } catch (error: any) {
    console.error('Error extracting requirements from PDF:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to extract requirements from PDF' },
      { status: 500 }
    );
  }
}
