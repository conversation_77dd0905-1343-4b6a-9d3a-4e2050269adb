/**
 * File Synchronization API Routes
 * 
 * This file contains the API routes for the file synchronization service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { 
  fileSyncService, 
  FileSyncAlreadyActiveError,
  FileSyncNotActiveError,
  FileSyncError
} from '@/lib/app-dev/file-sync';
import { projectManagementService } from '@/lib/app-dev/project-management';

// Schema for starting synchronization
const startSyncSchema = z.object({
  projectId: z.string().min(1),
  autoReload: z.boolean().optional(),
  ignore: z.array(z.string()).optional()
});

/**
 * Handle GET requests
 * - List all synchronizations: GET /api/app-dev/file-sync
 * - Get synchronization status: GET /api/app-dev/file-sync?projectId={projectId}
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (projectId) {
      // Get synchronization status for a specific project
      const status = fileSyncService.getSyncStatus(projectId);
      
      if (!status) {
        return NextResponse.json(
          { error: `No active synchronization for project ${projectId}` },
          { status: 404 }
        );
      }
      
      return NextResponse.json(status);
    } else {
      // List all synchronizations
      const statuses = fileSyncService.listSyncStatus();
      return NextResponse.json(statuses);
    }
  } catch (error: any) {
    console.error('Error handling GET request:', error);
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests
 * - Start synchronization: POST /api/app-dev/file-sync
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = startSyncSchema.parse(body);
    const { projectId, autoReload, ignore } = validatedData;
    
    // Get project details
    const project = await projectManagementService.getProject(projectId);
    
    // Check if VM is running
    if (!project.metadata.vmId) {
      // Start VM if not running
      project.metadata.vmId = await projectManagementService.startVM(projectId);
    }
    
    // Get project directory
    const projectDir = projectManagementService.getProjectDir(projectId);
    
    // Start synchronization
    const status = await fileSyncService.startSync({
      projectId,
      sourceDir: projectDir,
      vmId: project.metadata.vmId,
      autoReload,
      ignore,
      onChange: (event) => {
        console.log(`File change detected: ${event.type} ${event.relativePath}`);
      },
      onSync: (event) => {
        console.log(`File synchronized: ${event.type} ${event.relativePath}`);
      },
      onError: (error, event) => {
        console.error(`Synchronization error${event ? ` for ${event.relativePath}` : ''}:`, error);
      }
    });
    
    return NextResponse.json(status, { status: 201 });
  } catch (error: any) {
    console.error('Error handling POST request:', error);
    
    if (error instanceof FileSyncAlreadyActiveError) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.format() },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle DELETE requests
 * - Stop synchronization: DELETE /api/app-dev/file-sync?projectId={projectId}
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Missing projectId parameter' },
        { status: 400 }
      );
    }
    
    // Stop synchronization
    await fileSyncService.stopSync(projectId);
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error handling DELETE request:', error);
    
    if (error instanceof FileSyncNotActiveError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
