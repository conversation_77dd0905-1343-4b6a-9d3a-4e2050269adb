/**
 * Build Service API Routes
 * 
 * This file contains the API routes for the build service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { 
  buildService, 
  BuildError,
  BuildFailedError,
  InvalidBuildConfigError,
  BuildInProgressError,
  BuildNotFoundError,
  BuildArtifactNotFoundError,
  BuildTemplateNotFoundError
} from '@/lib/app-dev/build-service';
import { projectManagementService } from '@/lib/app-dev/project-management';

// Schema for starting a build
const startBuildSchema = z.object({
  projectId: z.string().min(1),
  templateName: z.string().min(1),
  environment: z.string().optional(),
  mode: z.enum(['development', 'production']).optional(),
  watch: z.boolean().optional(),
  minify: z.boolean().optional(),
  sourceMaps: z.boolean().optional(),
  clean: z.boolean().optional(),
  env: z.record(z.string()).optional(),
  options: z.record(z.any()).optional()
});

// Schema for adding a build hook
const addBuildHookSchema = z.object({
  buildId: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  timing: z.enum(['before', 'after']),
  command: z.string().min(1),
  enabled: z.boolean().optional()
});

/**
 * Handle GET requests
 * - Get build templates: GET /api/app-dev/build-service/templates
 * - Get build template: GET /api/app-dev/build-service/templates/{templateName}
 * - Get build status: GET /api/app-dev/build-service/status/{buildId}
 * - Get build result: GET /api/app-dev/build-service/result/{buildId}
 * - Get build artifact: GET /api/app-dev/build-service/artifact/{buildId}/{artifactName}
 * - Analyze build: GET /api/app-dev/build-service/analyze/{buildId}
 */
export async function GET(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    
    // Get build templates
    if (pathname.includes('/templates') && !pathname.includes('/templates/')) {
      const templates = buildService.getTemplates();
      return NextResponse.json(templates);
    }
    
    // Get build template
    if (pathname.includes('/templates/')) {
      const templateName = pathname.split('/templates/')[1];
      
      const template = buildService.getTemplate(templateName);
      return NextResponse.json(template);
    }
    
    // Get build status
    if (pathname.includes('/status/')) {
      const buildId = pathname.split('/status/')[1];
      
      const status = buildService.getBuildStatus(buildId);
      return NextResponse.json(status);
    }
    
    // Get build result
    if (pathname.includes('/result/')) {
      const buildId = pathname.split('/result/')[1];
      
      const result = buildService.getBuildResult(buildId);
      return NextResponse.json(result);
    }
    
    // Get build artifact
    if (pathname.includes('/artifact/')) {
      const parts = pathname.split('/artifact/')[1].split('/');
      const buildId = parts[0];
      const artifactName = parts.slice(1).join('/');
      
      const content = await buildService.getBuildArtifactContent(buildId, artifactName);
      
      // Determine content type
      let contentType = 'application/octet-stream';
      if (artifactName.endsWith('.js')) {
        contentType = 'application/javascript';
      } else if (artifactName.endsWith('.css')) {
        contentType = 'text/css';
      } else if (artifactName.endsWith('.html')) {
        contentType = 'text/html';
      } else if (artifactName.endsWith('.json')) {
        contentType = 'application/json';
      } else if (artifactName.endsWith('.map')) {
        contentType = 'application/json';
      } else if (artifactName.endsWith('.svg')) {
        contentType = 'image/svg+xml';
      } else if (artifactName.endsWith('.png')) {
        contentType = 'image/png';
      } else if (artifactName.endsWith('.jpg') || artifactName.endsWith('.jpeg')) {
        contentType = 'image/jpeg';
      }
      
      /* FIXME: Might Cause issues with Build service
               This is because the content is being converted to a Buffer and then to a Blob.
      */
      return new NextResponse(new Blob([Buffer.from(content).buffer]), {
        headers: {
          'Content-Type': contentType
        }
      });
    }
    
    // Analyze build
    if (pathname.includes('/analyze/')) {
      const buildId = pathname.split('/analyze/')[1];
      
      const analysis = buildService.analyzeBuild(buildId);
      return NextResponse.json(analysis);
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling GET request:', error);
    
    if (error instanceof BuildTemplateNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof BuildNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof BuildArtifactNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests
 * - Start build: POST /api/app-dev/build-service/build
 * - Stop build: POST /api/app-dev/build-service/stop/{buildId}
 * - Stop build watcher: POST /api/app-dev/build-service/stop-watcher/{buildId}
 * - Add build hook: POST /api/app-dev/build-service/hook
 * - Clear build cache: POST /api/app-dev/build-service/clear-cache
 */
export async function POST(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    
    // Start build
    if (pathname.includes('/build')) {
      const body = await request.json();
      const { 
        projectId, 
        templateName, 
        environment, 
        mode, 
        watch, 
        minify, 
        sourceMaps, 
        clean, 
        env, 
        options 
      } = startBuildSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      // Create build config from template
      const buildConfig = buildService.createBuildConfig(templateName, {
        environment,
        mode,
        minify,
        sourceMaps,
        clean,
        env,
        options
      });
      
      // Start build
      const buildId = await buildService.startBuild(projectDir, buildConfig, { watch });
      
      return NextResponse.json({ buildId });
    }
    
    // Stop build
    if (pathname.includes('/stop/')) {
      const buildId = pathname.split('/stop/')[1];
      
      await buildService.stopBuild(buildId);
      
      return NextResponse.json({ success: true });
    }
    
    // Stop build watcher
    if (pathname.includes('/stop-watcher/')) {
      const buildId = pathname.split('/stop-watcher/')[1];
      
      await buildService.stopBuildWatcher(buildId);
      
      return NextResponse.json({ success: true });
    }
    
    // Add build hook
    if (pathname.includes('/hook')) {
      const body = await request.json();
      const { buildId, name, description, timing, command, enabled } = addBuildHookSchema.parse(body);
      
      buildService.addBuildHook(buildId, {
        name,
        description,
        timing,
        command,
        enabled: enabled !== false
      });
      
      return NextResponse.json({ success: true });
    }
    
    // Clear build cache
    if (pathname.includes('/clear-cache')) {
      await buildService.clearBuildCache();
      
      return NextResponse.json({ success: true });
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling POST request:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.format() },
        { status: 400 }
      );
    }
    
    if (error instanceof InvalidBuildConfigError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    if (error instanceof BuildInProgressError) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }
    
    if (error instanceof BuildNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof BuildTemplateNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof BuildFailedError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle DELETE requests
 * - Remove build hook: DELETE /api/app-dev/build-service/hook/{buildId}/{hookName}
 */
export async function DELETE(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    
    // Remove build hook
    if (pathname.includes('/hook/')) {
      const parts = pathname.split('/hook/')[1].split('/');
      const buildId = parts[0];
      const hookName = parts[1];
      
      buildService.removeBuildHook(buildId, hookName);
      
      return NextResponse.json({ success: true });
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling DELETE request:', error);
    
    if (error instanceof BuildNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
