/**
 * Git Integration API Routes
 * 
 * This file contains the API routes for the Git integration service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { 
  gitIntegrationService, 
  GitRepoNotFoundError,
  GitRepoExistsError,
  GitBranchNotFoundError,
  GitBranchExistsError,
  GitCommitNotFoundError,
  GitUncommittedChangesError,
  GitMergeConflictError
} from '@/lib/app-dev/git-integration';
import { projectManagementService } from '@/lib/app-dev/project-management';

// Schema for Git init
const initRepoSchema = z.object({
  projectId: z.string().min(1),
  initialBranch: z.string().optional(),
  bare: z.boolean().optional()
});

// Schema for Git add
const addFilesSchema = z.object({
  projectId: z.string().min(1),
  files: z.array(z.string()).optional(),
  all: z.boolean().optional()
});

// Schema for Git commit
const commitSchema = z.object({
  projectId: z.string().min(1),
  message: z.string().min(1),
  author: z.string().optional(),
  email: z.string().optional(),
  addAll: z.boolean().optional(),
  allowEmpty: z.boolean().optional()
});

// Schema for Git branch
const branchSchema = z.object({
  projectId: z.string().min(1),
  name: z.string().min(1),
  checkout: z.boolean().optional()
});

// Schema for Git checkout
const checkoutSchema = z.object({
  projectId: z.string().min(1),
  ref: z.string().min(1),
  createBranch: z.boolean().optional(),
  force: z.boolean().optional()
});

/**
 * Handle GET requests
 * - Get repository status: GET /api/app-dev/git-integration/status?projectId={projectId}
 * - Get commit history: GET /api/app-dev/git-integration/commits?projectId={projectId}&limit={limit}
 * - Get commit details: GET /api/app-dev/git-integration/commits/{commitHash}?projectId={projectId}
 * - List branches: GET /api/app-dev/git-integration/branches?projectId={projectId}
 * - Get diff: GET /api/app-dev/git-integration/diff?projectId={projectId}&filePath={filePath}
 */
export async function GET(request: NextRequest) {
  try {
    const { pathname, searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Missing projectId parameter' },
        { status: 400 }
      );
    }
    
    // Get project directory
    const projectDir = projectManagementService.getProjectDir(projectId);
    
    // Get repository status
    if (pathname.includes('/status')) {
      const status = await gitIntegrationService.getStatus(projectDir);
      return NextResponse.json(status);
    }
    
    // Get commit history
    if (pathname.includes('/commits') && !pathname.includes('/commits/')) {
      const limitParam = searchParams.get('limit');
      const limit = limitParam ? parseInt(limitParam, 10) : 10;
      
      const commits = await gitIntegrationService.getCommitHistory(projectDir, limit);
      return NextResponse.json(commits);
    }
    
    // Get commit details
    if (pathname.includes('/commits/')) {
      const commitHash = pathname.split('/commits/')[1];
      
      const commit = await gitIntegrationService.getCommitDetails(projectDir, commitHash);
      return NextResponse.json(commit);
    }
    
    // List branches
    if (pathname.includes('/branches')) {
      const branches = await gitIntegrationService.listBranches(projectDir);
      return NextResponse.json(branches);
    }
    
    // Get diff
    if (pathname.includes('/diff')) {
      const filePath = searchParams.get('filePath') || undefined;
      
      const diffs = await gitIntegrationService.getDiff(projectDir, filePath);
      return NextResponse.json(diffs);
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling GET request:', error);
    
    if (error instanceof GitRepoNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof GitCommitNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof GitBranchNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests
 * - Initialize repository: POST /api/app-dev/git-integration/init
 * - Add files: POST /api/app-dev/git-integration/add
 * - Commit changes: POST /api/app-dev/git-integration/commit
 * - Create branch: POST /api/app-dev/git-integration/branch
 * - Checkout: POST /api/app-dev/git-integration/checkout
 */
export async function POST(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    const body = await request.json();
    
    // Initialize repository
    if (pathname.includes('/init')) {
      const { projectId, initialBranch, bare } = initRepoSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const result = await gitIntegrationService.initRepo(projectDir, {
        initialBranch,
        bare
      });
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Add files
    if (pathname.includes('/add')) {
      const { projectId, files, all } = addFilesSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const result = await gitIntegrationService.addFiles(projectDir, {
        files,
        all
      });
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Commit changes
    if (pathname.includes('/commit')) {
      const { projectId, message, author, email, addAll, allowEmpty } = commitSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const result = await gitIntegrationService.commit(projectDir, {
        message,
        author,
        email,
        addAll,
        allowEmpty
      });
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Create branch
    if (pathname.includes('/branch')) {
      const { projectId, name, checkout } = branchSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const result = await gitIntegrationService.createBranch(projectDir, {
        name,
        checkout
      });
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Checkout
    if (pathname.includes('/checkout')) {
      const { projectId, ref, createBranch, force } = checkoutSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const result = await gitIntegrationService.checkout(projectDir, {
        ref,
        createBranch,
        force
      });
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling POST request:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.format() },
        { status: 400 }
      );
    }
    
    if (error instanceof GitRepoNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof GitRepoExistsError) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }
    
    if (error instanceof GitBranchExistsError) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }
    
    if (error instanceof GitUncommittedChangesError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    if (error instanceof GitMergeConflictError) {
      return NextResponse.json(
        { error: error.message, conflicts: error.conflicts },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
