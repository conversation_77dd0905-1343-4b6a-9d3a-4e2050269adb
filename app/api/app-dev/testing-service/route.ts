/**
 * Testing Service API Routes
 * 
 * This file contains the API routes for the testing service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { 
  testingService, 
  TestingError,
  TestsFailedError,
  InvalidTestConfigError,
  TestRunInProgressError,
  TestRunNotFoundError,
  TestTemplateNotFoundError,
  UnsupportedTestFrameworkError,
  TestDirectoryNotFoundError,
  NoTestsFoundError
} from '@/lib/app-dev/testing-service';
import { projectManagementService } from '@/lib/app-dev/project-management';

// Schema for starting a test run
const startTestRunSchema = z.object({
  projectId: z.string().min(1),
  templateName: z.string().min(1),
  testPattern: z.string().optional(),
  watch: z.boolean().optional(),
  coverage: z.boolean().optional(),
  updateSnapshots: z.boolean().optional(),
  env: z.record(z.string()).optional(),
  options: z.record(z.any()).optional()
});

// Schema for generating a test file
const generateTestFileSchema = z.object({
  projectId: z.string().min(1),
  filePath: z.string().min(1),
  framework: z.string().min(1),
  type: z.string().min(1)
});

/**
 * Handle GET requests
 * - Get test templates: GET /api/app-dev/testing-service/templates
 * - Get test template: GET /api/app-dev/testing-service/templates/{templateName}
 * - Get test run status: GET /api/app-dev/testing-service/status/{testRunId}
 * - Get test result: GET /api/app-dev/testing-service/result/{testRunId}
 */
export async function GET(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    
    // Get test templates
    if (pathname.includes('/templates') && !pathname.includes('/templates/')) {
      const templates = testingService.getTemplates();
      return NextResponse.json(templates);
    }
    
    // Get test template
    if (pathname.includes('/templates/')) {
      const templateName = pathname.split('/templates/')[1];
      
      const template = testingService.getTemplate(templateName);
      return NextResponse.json(template);
    }
    
    // Get test run status
    if (pathname.includes('/status/')) {
      const testRunId = pathname.split('/status/')[1];
      
      const status = testingService.getTestRunStatus(testRunId);
      return NextResponse.json(status);
    }
    
    // Get test result
    if (pathname.includes('/result/')) {
      const testRunId = pathname.split('/result/')[1];
      
      const result = testingService.getTestResult(testRunId);
      return NextResponse.json(result);
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling GET request:', error);
    
    if (error instanceof TestTemplateNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof TestRunNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests
 * - Start test run: POST /api/app-dev/testing-service/run
 * - Stop test run: POST /api/app-dev/testing-service/stop/{testRunId}
 * - Stop test watcher: POST /api/app-dev/testing-service/stop-watcher/{testRunId}
 * - Generate test file: POST /api/app-dev/testing-service/generate-test
 */
export async function POST(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    
    // Start test run
    if (pathname.includes('/run')) {
      const body = await request.json();
      const { 
        projectId, 
        templateName, 
        testPattern, 
        watch, 
        coverage, 
        updateSnapshots, 
        env, 
        options 
      } = startTestRunSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      // Create test config from template
      const testConfig = testingService.createTestConfig(templateName, {
        testPattern,
        coverage,
        env,
        options
      });
      
      // Start test run
      const testRunId = await testingService.startTestRun(projectDir, testConfig, {
        watch,
        updateSnapshots
      });
      
      return NextResponse.json({ testRunId });
    }
    
    // Stop test run
    if (pathname.includes('/stop/')) {
      const testRunId = pathname.split('/stop/')[1];
      
      await testingService.stopTestRun(testRunId);
      
      return NextResponse.json({ success: true });
    }
    
    // Stop test watcher
    if (pathname.includes('/stop-watcher/')) {
      const testRunId = pathname.split('/stop-watcher/')[1];
      
      await testingService.stopTestWatcher(testRunId);
      
      return NextResponse.json({ success: true });
    }
    
    // Generate test file
    if (pathname.includes('/generate-test')) {
      const body = await request.json();
      const { projectId, filePath, framework, type } = generateTestFileSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      // Generate test file
      const testFilePath = await testingService.generateTestFile(projectDir, filePath, framework, type);
      
      return NextResponse.json({ testFilePath });
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling POST request:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.format() },
        { status: 400 }
      );
    }
    
    if (error instanceof InvalidTestConfigError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    if (error instanceof TestRunInProgressError) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }
    
    if (error instanceof TestRunNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof TestTemplateNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof UnsupportedTestFrameworkError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    if (error instanceof TestDirectoryNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof NoTestsFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof TestsFailedError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
