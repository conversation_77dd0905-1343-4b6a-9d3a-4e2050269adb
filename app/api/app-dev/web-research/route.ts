/**
 * Web Research API Routes
 * 
 * This file contains API routes for the web research service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { webResearchService } from '@/lib/app-dev/web-research';
import { WebResearchOptions } from '@/lib/app-dev/web-research/types';

/**
 * GET handler for web research
 * - List all research: GET /api/app-dev/web-research
 * - Get research by ID: GET /api/app-dev/web-research?researchId=123
 */
export async function GET(request: NextRequest) {
  try {
    // Parse the URL to get any query parameters
    const url = new URL(request.url);
    const researchId = url.searchParams.get('researchId');

    // If researchId is provided, get that specific research
    if (researchId) {
      const research = await webResearchService.getResearch(researchId);
      return NextResponse.json(research);
    }

    // Otherwise, list all research
    const researchItems = await webResearchService.listResearch();
    return NextResponse.json(researchItems);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST handler for web research
 * - Perform research: POST /api/app-dev/web-research
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const options: WebResearchOptions = await request.json();

    // Validate the URL
    if (!options.url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    // Perform the research
    const research = await webResearchService.performResearch(options);
    return NextResponse.json(research);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for web research
 * - Delete research: DELETE /api/app-dev/web-research?researchId=123
 */
export async function DELETE(request: NextRequest) {
  try {
    // Parse the URL to get any query parameters
    const url = new URL(request.url);
    const researchId = url.searchParams.get('researchId');

    // Validate the research ID
    if (!researchId) {
      return NextResponse.json(
        { error: 'Research ID is required' },
        { status: 400 }
      );
    }

    // Delete the research
    const success = await webResearchService.deleteResearch(researchId);
    
    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: 'Research not found' },
        { status: 404 }
      );
    }
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
