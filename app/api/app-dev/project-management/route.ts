/**
 * Project Management API Routes
 * 
 * This file contains the API routes for the project management service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { 
  projectManagementService, 
  createProjectSchema, 
  updateProjectSchema,
  ProjectNotFoundError,
  ProjectAlreadyExistsError,
  ProjectFileNotFoundError,
  ProjectOperationError
} from '@/lib/app-dev/project-management';

/**
 * Handle GET requests
 * - List projects: GET /api/app-dev/project-management
 * - Get project: GET /api/app-dev/project-management?projectId={projectId}
 * - Get project files: GET /api/app-dev/project-management/files?projectId={projectId}
 * - Get project file: GET /api/app-dev/project-management/files?projectId={projectId}&filePath={filePath}
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    // Get project files
    if (request.url.includes('/files')) {
      if (!projectId) {
        return NextResponse.json(
          { error: 'Missing projectId parameter' },
          { status: 400 }
        );
      }
      
      const filePath = searchParams.get('filePath');
      
      if (filePath) {
        // Get specific file
        const file = await projectManagementService.getProjectFile(projectId, filePath);
        return NextResponse.json(file);
      } else {
        // List all files
        const files = await projectManagementService.getProjectFiles(projectId);
        return NextResponse.json(files);
      }
    }
    
    // Get specific project
    if (projectId) {
      const project = await projectManagementService.getProject(projectId);
      return NextResponse.json(project);
    }
    
    // List all projects
    const projects = await projectManagementService.listProjects();
    return NextResponse.json(projects);
  } catch (error: any) {
    console.error('Error handling GET request:', error);
    
    if (error instanceof ProjectNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof ProjectFileNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests
 * - Create project: POST /api/app-dev/project-management
 * - Create project file: POST /api/app-dev/project-management/files?projectId={projectId}
 * - Start VM: POST /api/app-dev/project-management/vm?projectId={projectId}&action=start
 * - Stop VM: POST /api/app-dev/project-management/vm?projectId={projectId}&action=stop
 */
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const body = await request.json();
    
    // VM operations
    if (request.url.includes('/vm')) {
      const projectId = searchParams.get('projectId');
      const action = searchParams.get('action');
      
      if (!projectId) {
        return NextResponse.json(
          { error: 'Missing projectId parameter' },
          { status: 400 }
        );
      }
      
      if (action === 'start') {
        const vmId = await projectManagementService.startVM(projectId);
        return NextResponse.json({ vmId });
      } else if (action === 'stop') {
        await projectManagementService.stopVM(projectId);
        return NextResponse.json({ success: true });
      } else {
        return NextResponse.json(
          { error: 'Invalid action parameter' },
          { status: 400 }
        );
      }
    }
    
    // File operations
    if (request.url.includes('/files')) {
      const projectId = searchParams.get('projectId');
      
      if (!projectId) {
        return NextResponse.json(
          { error: 'Missing projectId parameter' },
          { status: 400 }
        );
      }
      
      const { filePath, content, isDirectory } = body;
      
      if (!filePath) {
        return NextResponse.json(
          { error: 'Missing filePath parameter' },
          { status: 400 }
        );
      }
      
      const result = await projectManagementService.createProjectFile(
        projectId,
        filePath,
        content || '',
        isDirectory || false
      );
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Create project
    try {
      const validatedData = createProjectSchema.parse(body);
      const project = await projectManagementService.createProject(validatedData);
      return NextResponse.json(project, { status: 201 });
    } catch (error) {
      if (error instanceof ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: error.format() },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error: any) {
    console.error('Error handling POST request:', error);
    
    if (error instanceof ProjectAlreadyExistsError) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }
    
    if (error instanceof ProjectNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof ProjectOperationError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle PUT requests
 * - Update project: PUT /api/app-dev/project-management?projectId={projectId}
 * - Update project file: PUT /api/app-dev/project-management/files?projectId={projectId}
 */
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const body = await request.json();
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Missing projectId parameter' },
        { status: 400 }
      );
    }
    
    // File operations
    if (request.url.includes('/files')) {
      const { filePath, content } = body;
      
      if (!filePath) {
        return NextResponse.json(
          { error: 'Missing filePath parameter' },
          { status: 400 }
        );
      }
      
      const result = await projectManagementService.updateProjectFile(
        projectId,
        filePath,
        content || ''
      );
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Update project
    try {
      const validatedData = updateProjectSchema.parse(body);
      const project = await projectManagementService.updateProject(projectId, validatedData);
      return NextResponse.json(project);
    } catch (error) {
      if (error instanceof ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: error.format() },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error: any) {
    console.error('Error handling PUT request:', error);
    
    if (error instanceof ProjectNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof ProjectFileNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle DELETE requests
 * - Delete project: DELETE /api/app-dev/project-management?projectId={projectId}
 * - Delete project file: DELETE /api/app-dev/project-management/files?projectId={projectId}&filePath={filePath}
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json(
        { error: 'Missing projectId parameter' },
        { status: 400 }
      );
    }
    
    // File operations
    if (request.url.includes('/files')) {
      const filePath = searchParams.get('filePath');
      
      if (!filePath) {
        return NextResponse.json(
          { error: 'Missing filePath parameter' },
          { status: 400 }
        );
      }
      
      const result = await projectManagementService.deleteProjectFile(projectId, filePath);
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Delete project
    await projectManagementService.deleteProject(projectId);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error handling DELETE request:', error);
    
    if (error instanceof ProjectNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof ProjectFileNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
