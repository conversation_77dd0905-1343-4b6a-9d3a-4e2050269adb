/**
 * Container Management API Route
 * 
 * This file contains the API route for container management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { projectManagementService } from '@/lib/app-dev/project-management';

/**
 * GET handler for container management
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('projectId');
    const action = searchParams.get('action');
    
    // Validate parameters
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }
    
    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }
    
    // Handle actions
    switch (action) {
      case 'ip':
        // Get container IP address
        const ipAddress = await projectManagementService.getContainerIpAddress(projectId);
        return NextResponse.json({ ipAddress });
      
      default:
        return NextResponse.json({ error: `Invalid action: ${action}` }, { status: 400 });
    }
  } catch (error: any) {
    console.error('Container API error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

/**
 * POST handler for container management
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('projectId');
    const action = searchParams.get('action');
    
    // Validate parameters
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }
    
    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }
    
    // Handle actions
    switch (action) {
      case 'create':
        // Create container
        const createContainerId = await projectManagementService.createContainer(projectId);
        return NextResponse.json({ containerId: createContainerId });
      
      case 'start':
        // Start container
        const startContainerId = await projectManagementService.startContainer(projectId);
        return NextResponse.json({ containerId: startContainerId });
      
      case 'stop':
        // Stop container
        await projectManagementService.stopContainer(projectId);
        return NextResponse.json({ success: true });
      
      case 'delete':
        // Delete container
        await projectManagementService.deleteContainer(projectId);
        return NextResponse.json({ success: true });
      
      case 'execute':
        // Execute command in container
        const body = await request.json();
        const { command, options } = body;
        
        if (!command || !Array.isArray(command)) {
          return NextResponse.json({ error: 'Command is required and must be an array' }, { status: 400 });
        }
        
        const result = await projectManagementService.executeContainerCommand(
          projectId,
          command,
          options
        );
        
        return NextResponse.json(result);
      
      default:
        return NextResponse.json({ error: `Invalid action: ${action}` }, { status: 400 });
    }
  } catch (error: any) {
    console.error('Container API error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
