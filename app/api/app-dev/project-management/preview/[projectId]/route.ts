/**
 * Project Preview API Route
 *
 * This file contains the API route for previewing projects in an iframe.
 * It handles both VM and container-based projects.
 */

import { NextRequest, NextResponse } from 'next/server';
import { projectManagementService, ProjectNotFoundError } from '@/lib/app-dev/project-management';

/**
 * Handle GET requests to proxy to the VM or container
 * - Preview project: GET /api/app-dev/project-management/preview/{projectId}
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const projectId = params.projectId;

    // Get project
    const project = await projectManagementService.getProject(projectId);

    // Check if container exists and is running
    if (project.metadata.containerId) {
      // Get container IP address
      const ipAddress = await projectManagementService.getContainerIpAddress(projectId);

      if (ipAddress) {
        // Container is running, get port from project config
        const port = project.config.port || 3000;

        // Construct URL
        const containerUrl = `http://${ipAddress}:${port}`;

        // Redirect to container URL
        return NextResponse.redirect(containerUrl);
      } else {
        // Container exists but is not running, try to start it
        try {
          await projectManagementService.startContainer(projectId);

          // Wait a moment for the container to start
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Get container IP address again
          const ipAddress = await projectManagementService.getContainerIpAddress(projectId);

          if (ipAddress) {
            // Container is now running, get port from project config
            const port = project.config.port || 3000;

            // Construct URL
            const containerUrl = `http://${ipAddress}:${port}`;

            // Redirect to container URL
            return NextResponse.redirect(containerUrl);
          }
        } catch (error: any) {
          console.error(`Failed to start container: ${error.message}`);
          // Fall back to VM if container start fails
        }
      }
    }

    // Fall back to VM if container is not available or not running
    // Check if VM is running
    if (!project.metadata.vmId) {
      // Start VM if not running
      try {
        project.metadata.vmId = await projectManagementService.startVM(projectId);
      } catch (error: any) {
        return NextResponse.json(
          { error: `Failed to start environment: ${error.message}` },
          { status: 500 }
        );
      }
    }

    // VM functionality has been removed - use containers instead
    return NextResponse.json(
      { error: 'VM functionality has been removed. Please use containers instead.' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling preview request:', error);

    if (error instanceof ProjectNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle all other HTTP methods by proxying to the VM
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  return proxyToVM(request, params.projectId);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  return proxyToVM(request, params.projectId);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  return proxyToVM(request, params.projectId);
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  return proxyToVM(request, params.projectId);
}

export async function OPTIONS(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  return proxyToVM(request, params.projectId);
}

/**
 * Proxy a request to the VM or container
 */
async function proxyToVM(request: NextRequest, projectId: string) {
  try {
    // Get project
    const project = await projectManagementService.getProject(projectId);

    // Check if container exists and is running
    if (project.metadata.containerId) {
      // Get container IP address
      const ipAddress = await projectManagementService.getContainerIpAddress(projectId);

      if (ipAddress) {
        // Container is running, get port from project config
        const port = project.config.port || 3000;

        // Construct URL
        const containerUrl = `http://${ipAddress}:${port}`;

        // Redirect to container URL
        return NextResponse.redirect(containerUrl);
      }
    }

    // Fall back to VM if container is not available or not running
    // Check if VM is running
    if (!project.metadata.vmId) {
      return NextResponse.json(
        { error: 'No running environment available' },
        { status: 400 }
      );
    }

    // VM functionality has been removed - use containers instead
    return NextResponse.json(
      { error: 'VM functionality has been removed. Please use containers instead.' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error proxying to environment:', error);

    if (error instanceof ProjectNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
