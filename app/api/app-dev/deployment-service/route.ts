/**
 * Deployment Service API Routes
 * 
 * This file contains the API routes for the deployment service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { 
  deploymentService, 
  DeploymentError,
  DeploymentFailedError,
  InvalidDeploymentConfigError,
  DeploymentInProgressError,
  DeploymentNotFoundError,
  DeploymentTemplateNotFoundError,
  UnsupportedProviderError,
  UnsupportedEnvironmentError,
  RollbackFailedError,
  RollbackNotAllowedError,
  NoPreviousDeploymentError
} from '@/lib/app-dev/deployment-service';
import { projectManagementService } from '@/lib/app-dev/project-management';

// Schema for starting a deployment
const startDeploymentSchema = z.object({
  projectId: z.string().min(1),
  templateName: z.string().min(1),
  environment: z.string().optional(),
  buildBeforeDeploy: z.boolean().optional(),
  buildId: z.string().optional(),
  branch: z.string().optional(),
  commit: z.string().optional(),
  autoRollback: z.boolean().optional(),
  env: z.record(z.string()).optional(),
  options: z.record(z.any()).optional()
});

/**
 * Handle GET requests
 * - Get deployment templates: GET /api/app-dev/deployment-service/templates
 * - Get deployment template: GET /api/app-dev/deployment-service/templates/{templateName}
 * - Get deployment status: GET /api/app-dev/deployment-service/status/{deploymentId}
 * - Get deployment result: GET /api/app-dev/deployment-service/result/{deploymentId}
 * - Get deployment history: GET /api/app-dev/deployment-service/history/{environment}
 */
export async function GET(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    
    // Get deployment templates
    if (pathname.includes('/templates') && !pathname.includes('/templates/')) {
      const templates = deploymentService.getTemplates();
      return NextResponse.json(templates);
    }
    
    // Get deployment template
    if (pathname.includes('/templates/')) {
      const templateName = pathname.split('/templates/')[1];
      
      const template = deploymentService.getTemplate(templateName);
      return NextResponse.json(template);
    }
    
    // Get deployment status
    if (pathname.includes('/status/')) {
      const deploymentId = pathname.split('/status/')[1];
      
      const status = deploymentService.getDeploymentStatus(deploymentId);
      return NextResponse.json(status);
    }
    
    // Get deployment result
    if (pathname.includes('/result/')) {
      const deploymentId = pathname.split('/result/')[1];
      
      const result = deploymentService.getDeploymentResult(deploymentId);
      return NextResponse.json(result);
    }
    
    // Get deployment history
    if (pathname.includes('/history/')) {
      const environment = pathname.split('/history/')[1];
      
      const history = deploymentService.getDeploymentHistory(environment);
      return NextResponse.json(history);
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling GET request:', error);
    
    if (error instanceof DeploymentTemplateNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof DeploymentNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests
 * - Start deployment: POST /api/app-dev/deployment-service/deploy
 * - Cancel deployment: POST /api/app-dev/deployment-service/cancel/{deploymentId}
 * - Rollback deployment: POST /api/app-dev/deployment-service/rollback/{deploymentId}
 */
export async function POST(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    
    // Start deployment
    if (pathname.includes('/deploy')) {
      const body = await request.json();
      const { 
        projectId, 
        templateName, 
        environment, 
        buildBeforeDeploy, 
        buildId, 
        branch, 
        commit, 
        autoRollback, 
        env, 
        options 
      } = startDeploymentSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      // Get project name
      const project = projectManagementService.getProject(projectId);
      
      // Create deployment config from template
      const deploymentConfig = deploymentService.createDeploymentConfig(templateName, project.name, {
        env: {
          NODE_ENV: environment || 'development',
          ...env
        }
      });
      
      // Override environment if provided
      if (environment) {
        deploymentConfig.environment = environment;
      }
      
      // Start deployment
      const deploymentId = await deploymentService.startDeployment(projectDir, deploymentConfig, {
        buildBeforeDeploy,
        buildId,
        branch,
        commit,
        autoRollback,
        env,
        options
      });
      
      return NextResponse.json({ deploymentId });
    }
    
    // Cancel deployment
    if (pathname.includes('/cancel/')) {
      const deploymentId = pathname.split('/cancel/')[1];
      
      await deploymentService.cancelDeployment(deploymentId);
      
      return NextResponse.json({ success: true });
    }
    
    // Rollback deployment
    if (pathname.includes('/rollback/')) {
      const deploymentId = pathname.split('/rollback/')[1];
      
      // Get project ID from query params
      const { searchParams } = new URL(request.url);
      const projectId = searchParams.get('projectId');
      
      if (!projectId) {
        return NextResponse.json(
          { error: 'Missing projectId parameter' },
          { status: 400 }
        );
      }
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      // Rollback deployment
      const rollbackDeploymentId = await deploymentService.rollbackDeployment(projectDir, deploymentId);
      
      return NextResponse.json({ rollbackDeploymentId });
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling POST request:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.format() },
        { status: 400 }
      );
    }
    
    if (error instanceof InvalidDeploymentConfigError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    if (error instanceof DeploymentInProgressError) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }
    
    if (error instanceof DeploymentNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof DeploymentTemplateNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof UnsupportedProviderError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    if (error instanceof UnsupportedEnvironmentError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    if (error instanceof RollbackNotAllowedError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    if (error instanceof NoPreviousDeploymentError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof DeploymentFailedError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
