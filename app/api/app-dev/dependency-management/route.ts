/**
 * Dependency Management API Routes
 * 
 * This file contains the API routes for the dependency management service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { 
  dependencyManagementService, 
  PackageJsonNotFoundError,
  InvalidPackageJsonError,
  DependencyNotFoundError,
  ScriptNotFoundError,
  PackageManagerNotFoundError
} from '@/lib/app-dev/dependency-management';
import { projectManagementService } from '@/lib/app-dev/project-management';

// Schema for installing dependencies
const installDependenciesSchema = z.object({
  projectId: z.string().min(1),
  packages: z.array(z.string()).optional(),
  dev: z.boolean().optional(),
  peer: z.boolean().optional(),
  optional: z.boolean().optional(),
  exact: z.boolean().optional(),
  packageManager: z.enum(['npm', 'yarn', 'pnpm']).optional()
});

// Schema for uninstalling dependencies
const uninstallDependenciesSchema = z.object({
  projectId: z.string().min(1),
  packages: z.array(z.string()),
  packageManager: z.enum(['npm', 'yarn', 'pnpm']).optional()
});

// Schema for updating dependencies
const updateDependenciesSchema = z.object({
  projectId: z.string().min(1),
  packages: z.array(z.string()).optional(),
  latest: z.boolean().optional(),
  packageManager: z.enum(['npm', 'yarn', 'pnpm']).optional()
});

// Schema for executing scripts
const executeScriptSchema = z.object({
  projectId: z.string().min(1),
  script: z.string().min(1),
  args: z.array(z.string()).optional(),
  env: z.record(z.string()).optional(),
  packageManager: z.enum(['npm', 'yarn', 'pnpm']).optional()
});

// Schema for adding scripts
const addScriptSchema = z.object({
  projectId: z.string().min(1),
  name: z.string().min(1),
  command: z.string().min(1)
});

// Schema for searching packages
const searchPackagesSchema = z.object({
  query: z.string().min(1),
  limit: z.number().min(1).max(100).optional()
});

/**
 * Handle GET requests
 * - Get package.json: GET /api/app-dev/dependency-management/package-json?projectId={projectId}
 * - Get dependencies: GET /api/app-dev/dependency-management/dependencies?projectId={projectId}
 * - Get scripts: GET /api/app-dev/dependency-management/scripts?projectId={projectId}
 * - Get package info: GET /api/app-dev/dependency-management/package-info?name={name}
 * - Search packages: GET /api/app-dev/dependency-management/search?query={query}&limit={limit}
 */
export async function GET(request: NextRequest) {
  try {
    const { pathname, searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    // Get package.json
    if (pathname.includes('/package-json')) {
      if (!projectId) {
        return NextResponse.json(
          { error: 'Missing projectId parameter' },
          { status: 400 }
        );
      }
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const packageJson = await dependencyManagementService.getPackageJson(projectDir);
      return NextResponse.json(packageJson);
    }
    
    // Get dependencies
    if (pathname.includes('/dependencies')) {
      if (!projectId) {
        return NextResponse.json(
          { error: 'Missing projectId parameter' },
          { status: 400 }
        );
      }
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const dependencies = await dependencyManagementService.getDependencies(projectDir);
      return NextResponse.json(dependencies);
    }
    
    // Get scripts
    if (pathname.includes('/scripts')) {
      if (!projectId) {
        return NextResponse.json(
          { error: 'Missing projectId parameter' },
          { status: 400 }
        );
      }
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const scripts = await dependencyManagementService.getScripts(projectDir);
      return NextResponse.json(scripts);
    }
    
    // Get package info
    if (pathname.includes('/package-info')) {
      const name = searchParams.get('name');
      
      if (!name) {
        return NextResponse.json(
          { error: 'Missing name parameter' },
          { status: 400 }
        );
      }
      
      const packageInfo = await dependencyManagementService.getPackageInfo(name);
      return NextResponse.json(packageInfo);
    }
    
    // Search packages
    if (pathname.includes('/search')) {
      const query = searchParams.get('query');
      const limitParam = searchParams.get('limit');
      
      if (!query) {
        return NextResponse.json(
          { error: 'Missing query parameter' },
          { status: 400 }
        );
      }
      
      const limit = limitParam ? parseInt(limitParam, 10) : 10;
      
      const results = await dependencyManagementService.searchPackages(query, limit);
      return NextResponse.json(results);
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling GET request:', error);
    
    if (error instanceof PackageJsonNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof InvalidPackageJsonError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests
 * - Install dependencies: POST /api/app-dev/dependency-management/install
 * - Uninstall dependencies: POST /api/app-dev/dependency-management/uninstall
 * - Update dependencies: POST /api/app-dev/dependency-management/update
 * - Execute script: POST /api/app-dev/dependency-management/execute-script
 * - Add script: POST /api/app-dev/dependency-management/add-script
 * - Search packages: POST /api/app-dev/dependency-management/search
 */
export async function POST(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    const body = await request.json();
    
    // Install dependencies
    if (pathname.includes('/install')) {
      const { projectId, packages, dev, peer, optional, exact, packageManager } = installDependenciesSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const result = await dependencyManagementService.installDependencies(projectDir, packages, {
        dev,
        peer,
        optional,
        exact,
        packageManager
      });
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Uninstall dependencies
    if (pathname.includes('/uninstall')) {
      const { projectId, packages, packageManager } = uninstallDependenciesSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const result = await dependencyManagementService.uninstallDependencies(projectDir, packages, {
        packageManager
      });
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Update dependencies
    if (pathname.includes('/update')) {
      const { projectId, packages, latest, packageManager } = updateDependenciesSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const result = await dependencyManagementService.updateDependencies(projectDir, packages, {
        latest,
        packageManager
      });
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Execute script
    if (pathname.includes('/execute-script')) {
      const { projectId, script, args, env, packageManager } = executeScriptSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      const result = await dependencyManagementService.executeScript(projectDir, script, {
        args,
        env,
        packageManager
      });
      
      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 400 }
        );
      }
      
      return NextResponse.json(result);
    }
    
    // Add script
    if (pathname.includes('/add-script')) {
      const { projectId, name, command } = addScriptSchema.parse(body);
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      await dependencyManagementService.addScript(projectDir, name, command);
      
      return NextResponse.json({ success: true });
    }
    
    // Search packages
    if (pathname.includes('/search')) {
      const { query, limit } = searchPackagesSchema.parse(body);
      
      const results = await dependencyManagementService.searchPackages(query, limit);
      
      return NextResponse.json(results);
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling POST request:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.format() },
        { status: 400 }
      );
    }
    
    if (error instanceof PackageJsonNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof InvalidPackageJsonError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    if (error instanceof ScriptNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof PackageManagerNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * Handle DELETE requests
 * - Remove script: DELETE /api/app-dev/dependency-management/script?projectId={projectId}&name={name}
 */
export async function DELETE(request: NextRequest) {
  try {
    const { pathname, searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    // Remove script
    if (pathname.includes('/script')) {
      if (!projectId) {
        return NextResponse.json(
          { error: 'Missing projectId parameter' },
          { status: 400 }
        );
      }
      
      const name = searchParams.get('name');
      
      if (!name) {
        return NextResponse.json(
          { error: 'Missing name parameter' },
          { status: 400 }
        );
      }
      
      // Get project directory
      const projectDir = projectManagementService.getProjectDir(projectId);
      
      await dependencyManagementService.removeScript(projectDir, name);
      
      return NextResponse.json({ success: true });
    }
    
    return NextResponse.json(
      { error: 'Invalid endpoint' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error handling DELETE request:', error);
    
    if (error instanceof ScriptNotFoundError) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
