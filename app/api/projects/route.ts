import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../auth/[...nextauth]/route"
import { db } from "@/lib/db"

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const projects = await db.project.findMany({
    where: {
      userId: session.user.id as string,
    },
    orderBy: {
      updatedAt: "desc",
    },
  })

  return NextResponse.json(projects)
}

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const body = await req.json()

  // Handle both simple description and structured project data
  if (typeof body === "object" && body !== null) {
    // Handle structured project data
    if (body.name && body.description) {
      const { name, description, type, template, technologies } = body

      const project = await db.project.create({
        data: {
          name,
          description,
          appType: type || "web",
          template,
          technologies: technologies ? JSON.stringify(technologies) : null,
          userId: session.user.id as string,
        },
      })

      return NextResponse.json(project)
    }

    // Handle simple description
    if (body.description) {
      const { description } = body

      // Extract a name from the description
      const name = description.split(".")[0].trim()

      // Determine app type from description
      let appType = "web"
      if (description.toLowerCase().includes("mobile")) {
        appType = "mobile"
      } else if (description.toLowerCase().includes("backend") || description.toLowerCase().includes("api")) {
        appType = "backend"
      } else if (description.toLowerCase().includes("fullstack")) {
        appType = "fullstack"
      }

      const project = await db.project.create({
        data: {
          name,
          description,
          appType,
          userId: session.user.id as string,
        },
      })

      return NextResponse.json(project)
    }
  }

  return NextResponse.json({ error: "Invalid project data" }, { status: 400 })
}
