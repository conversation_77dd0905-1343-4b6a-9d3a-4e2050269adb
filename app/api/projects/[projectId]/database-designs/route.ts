import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { db } from "@/lib/db"

// GET /api/projects/[projectId]/database-designs
export async function GET(
  req: NextRequest,
  { params }: { params: { projectId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const projectId = params.projectId

  try {
    // Check if project exists and belongs to the user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id as string,
      },
    })

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 })
    }

    // Get database designs for the project
    const designs = await db.databaseDesign.findMany({
      where: {
        projectId,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json(designs)
  } catch (error) {
    console.error("Error fetching database designs:", error)
    return NextResponse.json(
      { error: "Failed to fetch database designs" },
      { status: 500 }
    )
  }
}

// POST /api/projects/[projectId]/database-designs
export async function POST(
  req: NextRequest,
  { params }: { params: { projectId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const projectId = params.projectId
  const body = await req.json()

  try {
    // Check if project exists and belongs to the user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id as string,
      },
    })

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 })
    }

    // Create database design
    const design = await db.databaseDesign.create({
      data: {
        name: body.name,
        description: body.description,
        entities: body.entities || [],
        relationships: body.relationships || [],
        schema: body.schema,
        projectId,
      },
    })

    return NextResponse.json(design)
  } catch (error) {
    console.error("Error creating database design:", error)
    return NextResponse.json(
      { error: "Failed to create database design" },
      { status: 500 }
    )
  }
}
