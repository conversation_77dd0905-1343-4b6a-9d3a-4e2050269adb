import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../../auth/[...nextauth]/route"
import { WorkflowOrchestrator } from "@/lib/production/workflow-orchestrator"
import { db } from "@/lib/db"
import { logger } from "@/lib/utils/logger"
import path from "path"
import fs from "fs"

export async function POST(req: NextRequest, { params }: { params: { projectId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { projectId } = params
    const { workflow, config } = await req.json()

    if (!projectId) {
      return new NextResponse(JSON.stringify({ error: "Missing project ID" }), {
        status: 400,
      })
    }

    // Validate project ownership
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    })

    if (!project) {
      return new NextResponse(JSON.stringify({ error: "Project not found" }), {
        status: 404,
      })
    }

    // Get project directory
    const projectDir = path.join(process.cwd(), "tmp", "projects", projectId)
    if (!fs.existsSync(projectDir)) {
      return new NextResponse(JSON.stringify({ error: "Project directory not found" }), {
        status: 404,
      })
    }

    // Create task record
    const task = await db.task.create({
      data: {
        type: "production",
        status: "running",
        projectId,
        startedAt: new Date(),
      },
    })

    // Set up production workflow
    const orchestrator = new WorkflowOrchestrator()
    const result = await orchestrator.setupProductionWorkflow(project.appType as any, projectDir, config)

    // Update task status
    if (result.success) {
      await db.task.update({
        where: { id: task.id },
        data: {
          status: "completed",
          result: result.results,
          completedAt: new Date(),
        },
      })
    } else {
      await db.task.update({
        where: { id: task.id },
        data: {
          status: "failed",
          result: result.results,
          error: result.error,
          completedAt: new Date(),
        },
      })
    }

    return NextResponse.json({
      success: result.success,
      taskId: task.id,
      results: result.results,
    })
  } catch (error) {
    logger.error("Error setting up production workflow:", error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to set up production workflow",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}

export async function PUT(req: NextRequest, { params }: { params: { projectId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { projectId } = params
    const { steps, environment } = await req.json()

    if (!projectId || !steps || !Array.isArray(steps)) {
      return new NextResponse(JSON.stringify({ error: "Missing required fields" }), {
        status: 400,
      })
    }

    // Validate project ownership
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    })

    if (!project) {
      return new NextResponse(JSON.stringify({ error: "Project not found" }), {
        status: 404,
      })
    }

    // Get project directory
    const projectDir = path.join(process.cwd(), "tmp", "projects", projectId)
    if (!fs.existsSync(projectDir)) {
      return new NextResponse(JSON.stringify({ error: "Project directory not found" }), {
        status: 404,
      })
    }

    // Create task record
    const task = await db.task.create({
      data: {
        type: "workflow",
        status: "running",
        projectId,
        startedAt: new Date(),
      },
    })

    // Run production workflow
    const orchestrator = new WorkflowOrchestrator()
    const result = await orchestrator.runProductionWorkflow(
      project.appType as any,
      projectDir,
      steps as any[],
      environment || "development",
    )

    // Update task status
    if (result.success) {
      await db.task.update({
        where: { id: task.id },
        data: {
          status: "completed",
          result: result.results,
          completedAt: new Date(),
        },
      })
    } else {
      await db.task.update({
        where: { id: task.id },
        data: {
          status: "failed",
          result: result.results,
          error: result.error,
          completedAt: new Date(),
        },
      })
    }

    return NextResponse.json({
      success: result.success,
      taskId: task.id,
      results: result.results,
    })
  } catch (error) {
    logger.error("Error running production workflow:", error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to run production workflow",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
