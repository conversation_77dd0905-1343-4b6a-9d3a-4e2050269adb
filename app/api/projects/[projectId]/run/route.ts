import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../auth/[...nextauth]/route"
import { FirecrackerService } from "@/lib/firecracker/firecracker-service"
import { db } from "@/lib/db"
import { logger } from "@/lib/utils/logger"

export async function POST(req: NextRequest, { params }: { params: { projectId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  const { projectId } = params

  try {
    // Validate project ownership
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
      include: {
        tasks: {
          where: {
            type: "generate",
            status: "completed",
          },
          orderBy: {
            completedAt: "desc",
          },
          take: 1,
        },
      },
    })

    if (!project) {
      return new NextResponse(JSON.stringify({ error: "Project not found" }), {
        status: 404,
      })
    }

    if (project.tasks.length === 0 || !project.tasks[0].result?.sandboxId) {
      return new NextResponse(JSON.stringify({ error: "No generated code found for this project" }), {
        status: 400,
      })
    }

    const sandboxId = project.tasks[0].result.sandboxId as string

    // Get project type
    const projectType = project.appType as "react" | "next" | "node" | "express"

    // Run the project
    const firecrackerService = new FirecrackerService()
    const buildManager = firecrackerService.buildManager

    const result = await buildManager.runBuild(sandboxId, projectType)

    if (!result.success) {
      return new NextResponse(JSON.stringify({ error: result.error }), {
        status: 500,
      })
    }

    // Store the URL in the database
    await db.project.update({
      where: { id: projectId },
      data: {
        status: "running",
        deploymentUrl: result.url,
      },
    })

    return NextResponse.json({
      success: true,
      url: result.url,
    })
  } catch (error) {
    logger.error("Error running project:", error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to run project",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
