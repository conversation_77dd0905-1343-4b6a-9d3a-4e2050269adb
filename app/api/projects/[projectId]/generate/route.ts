import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../auth/[...nextauth]/route"
import { db } from "@/lib/db"
import { AIAgent } from "@/lib/services/ai-agent"

export async function POST(req: NextRequest, { params }: { params: { projectId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  const { projectId } = params

  const project = await db.project.findUnique({
    where: {
      id: projectId,
      userId: session.user.id,
    },
  })

  if (!project) {
    return new NextResponse(JSON.stringify({ error: "Project not found" }), {
      status: 404,
    })
  }

  try {
    const agent = new AIAgent(projectId)

    // Update project status
    await db.project.update({
      where: { id: projectId },
      data: { status: "building" },
    })

    // Start the analysis process
    const result = await agent.analyzeRequirements()

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error generating application:", error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to generate application",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
