import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { db } from "@/lib/db"

export async function GET(req: NextRequest, { params }: { params: { projectId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { projectId } = params

  // Verify project belongs to user
  const project = await db.project.findFirst({
    where: {
      id: projectId,
      userId: session.user.id as string,
    },
  })

  if (!project) {
    return NextResponse.json({ error: "Project not found" }, { status: 404 })
  }

  const files = await db.generatedFile.findMany({
    where: {
      projectId,
    },
    orderBy: {
      path: "asc",
    },
  })

  return NextResponse.json(files)
}

export async function POST(req: NextRequest, { params }: { params: { projectId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { projectId } = params

  // Verify project belongs to user
  const project = await db.project.findFirst({
    where: {
      id: projectId,
      userId: session.user.id as string,
    },
  })

  if (!project) {
    return NextResponse.json({ error: "Project not found" }, { status: 404 })
  }

  const body = await req.json()
  const { path, content, language } = body

  if (!path || !content || !language) {
    return NextResponse.json({ error: "Path, content, and language are required" }, { status: 400 })
  }

  // Check if file already exists
  const existingFile = await db.generatedFile.findFirst({
    where: {
      projectId,
      path,
    },
  })

  if (existingFile) {
    // Update existing file
    const updatedFile = await db.generatedFile.update({
      where: {
        id: existingFile.id,
      },
      data: {
        content,
        language,
      },
    })

    return NextResponse.json(updatedFile)
  } else {
    // Create new file
    const newFile = await db.generatedFile.create({
      data: {
        projectId,
        path,
        content,
        language,
      },
    })

    return NextResponse.json(newFile)
  }
}
