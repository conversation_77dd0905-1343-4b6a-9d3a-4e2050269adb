import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../../auth/[...nextauth]/route"
import { db } from "@/lib/db"

export async function GET(req: NextRequest, { params }: { params: { projectId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { projectId } = params
    const { searchParams } = new URL(req.url)
    const type = searchParams.get("type")
    const status = searchParams.get("status")
    const limit = Number.parseInt(searchParams.get("limit") || "50")

    // Validate project ownership
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    })

    if (!project) {
      return new NextResponse(JSON.stringify({ error: "Project not found" }), {
        status: 404,
      })
    }

    // Build query
    const query: any = {
      where: {
        projectId,
      },
      orderBy: {
        startedAt: "desc",
      },
      take: limit,
    }

    // Add type filter if provided
    if (type) {
      query.where.type = type
    }

    // Add status filter if provided
    if (status) {
      query.where.status = status
    }

    // Fetch tasks
    const tasks = await db.task.findMany(query)

    return NextResponse.json({
      tasks,
    })
  } catch (error) {
    return new NextResponse(
      JSON.stringify({
        error: "Failed to fetch tasks",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
