import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { db } from "@/lib/db"

// GET /api/projects/[projectId]/tasks/[taskId]
export async function GET(
  req: NextRequest,
  { params }: { params: { projectId: string; taskId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { projectId, taskId } = params

  try {
    // Check if project exists and belongs to the user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id as string,
      },
    })

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 })
    }

    // Get task
    const task = await db.projectTask.findUnique({
      where: {
        id: taskId,
        projectId,
      },
    })

    if (!task) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    return NextResponse.json(task)
  } catch (error) {
    console.error("Error fetching task:", error)
    return NextResponse.json(
      { error: "Failed to fetch task" },
      { status: 500 }
    )
  }
}

// PUT /api/projects/[projectId]/tasks/[taskId]
export async function PUT(
  req: NextRequest,
  { params }: { params: { projectId: string; taskId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { projectId, taskId } = params
  const body = await req.json()

  try {
    // Check if project exists and belongs to the user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id as string,
      },
    })

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 })
    }

    // Check if task exists
    const task = await db.projectTask.findUnique({
      where: {
        id: taskId,
        projectId,
      },
    })

    if (!task) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    // Update task
    const updatedTask = await db.projectTask.update({
      where: {
        id: taskId,
      },
      data: {
        title: body.title !== undefined ? body.title : undefined,
        description: body.description !== undefined ? body.description : undefined,
        status: body.status !== undefined ? body.status : undefined,
        priority: body.priority !== undefined ? body.priority : undefined,
        assignedTo: body.assignedTo !== undefined ? body.assignedTo : undefined,
        dueDate: body.dueDate !== undefined ? new Date(body.dueDate) : undefined,
        completedAt: body.status === "completed" && !task.completedAt ? new Date() : 
                    body.status !== "completed" && task.completedAt ? null : undefined,
      },
    })

    return NextResponse.json(updatedTask)
  } catch (error) {
    console.error("Error updating task:", error)
    return NextResponse.json(
      { error: "Failed to update task" },
      { status: 500 }
    )
  }
}

// DELETE /api/projects/[projectId]/tasks/[taskId]
export async function DELETE(
  req: NextRequest,
  { params }: { params: { projectId: string; taskId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { projectId, taskId } = params

  try {
    // Check if project exists and belongs to the user
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id as string,
      },
    })

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 })
    }

    // Check if task exists
    const task = await db.projectTask.findUnique({
      where: {
        id: taskId,
        projectId,
      },
    })

    if (!task) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    // Delete task
    await db.projectTask.delete({
      where: {
        id: taskId,
      },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting task:", error)
    return NextResponse.json(
      { error: "Failed to delete task" },
      { status: 500 }
    )
  }
}
