import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../auth/[...nextauth]/route"
import { FirecrackerService } from "@/lib/firecracker/firecracker-service"
import { db } from "@/lib/db"
import { logger } from "@/lib/utils/logger"
import fs from "fs"
import path from "path"
import { execSync } from "child_process"

export async function POST(req: NextRequest, { params }: { params: { projectId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  const { projectId } = params

  try {
    // Validate project ownership
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
      include: {
        tasks: {
          where: {
            type: "generate",
            status: "completed",
          },
          orderBy: {
            completedAt: "desc",
          },
          take: 1,
        },
      },
    })

    if (!project) {
      return new NextResponse(JSON.stringify({ error: "Project not found" }), {
        status: 404,
      })
    }

    if (project.tasks.length === 0 || !project.tasks[0].result?.sandboxId) {
      return new NextResponse(JSON.stringify({ error: "No generated code found for this project" }), {
        status: 400,
      })
    }

    const sandboxId = project.tasks[0].result.sandboxId as string

    // Create temporary directory for export
    const exportDir = path.join(process.cwd(), "tmp", "exports", projectId)
    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true })
    }

    // Export the project
    const firecrackerService = new FirecrackerService()
    const result = await firecrackerService.exportProject(sandboxId, exportDir)

    if (!result.success) {
      return new NextResponse(JSON.stringify({ error: result.error }), {
        status: 500,
      })
    }

    // Create a zip file
    const zipPath = path.join(process.cwd(), "tmp", "exports", `${projectId}.zip`)
    execSync(`cd ${exportDir} && zip -r ${zipPath} .`, {
      stdio: "ignore",
    })

    // Read the zip file
    const zipFile = fs.readFileSync(zipPath)

    // Clean up
    fs.rmSync(exportDir, { recursive: true, force: true })
    fs.unlinkSync(zipPath)

    // Return the zip file
    return new NextResponse(zipFile, {
      headers: {
        "Content-Type": "application/zip",
        "Content-Disposition": `attachment; filename="project-${projectId}.zip"`,
      },
    })
  } catch (error) {
    logger.error("Error exporting project:", error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to export project",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
