import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../auth/[...nextauth]/route"
import { db } from "@/lib/db"
import { logger } from "@/lib/utils/logger"
import { execSync } from "child_process"

export async function POST(req: NextRequest, { params }: { params: { projectId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  const { projectId } = params

  try {
    // Validate project ownership
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    })

    if (!project) {
      return new NextResponse(JSON.stringify({ error: "Project not found" }), {
        status: 404,
      })
    }

    if (project.status !== "running" || !project.deploymentUrl) {
      return new NextResponse(JSON.stringify({ error: "Project is not running" }), {
        status: 400,
      })
    }

    // Extract port from URL
    const url = new URL(project.deploymentUrl)
    const port = url.port

    if (port) {
      try {
        // Find and kill process running on that port
        execSync(`lsof -i :${port} | grep LISTEN | awk '{print $2}' | xargs kill -9`, {
          stdio: "ignore",
        })
      } catch (error) {
        logger.warn("Error killing process:", error)
        // Continue even if this fails
      }
    }

    // Update project status
    await db.project.update({
      where: { id: projectId },
      data: {
        status: "ready",
        deploymentUrl: null,
      },
    })

    return NextResponse.json({
      success: true,
    })
  } catch (error) {
    logger.error("Error stopping project:", error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to stop project",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
