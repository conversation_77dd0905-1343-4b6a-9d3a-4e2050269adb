/**
 * VibeKraft Sandbox Network Configuration API
 * 
 * API endpoints for network configuration and management within sandbox containers
 */

import { NextRequest, NextResponse } from 'next/server';
// Mock authentication for demo purposes
const mockAuth = {
  user: { id: 'demo-user', email: '<EMAIL>' }
};
import { SandboxManager } from '@/lib/vibekraft-sandbox/core/sandbox-manager';
import { NetworkManager } from '@/lib/vibekraft-sandbox/utils/network-manager';
import { z } from 'zod';

// Initialize managers
const sandboxManager = new SandboxManager();
const networkManager = new NetworkManager(sandboxManager.getDockerManager());

// Initialize manager on first use
let managerInitialized = false;
async function ensureManagerInitialized() {
  if (!managerInitialized) {
    await sandboxManager.initialize();
    managerInitialized = true;
  }
}

// Validation schemas
const networkConfigSchema = z.object({
  internetAccess: z.boolean().optional(),
  inboundAccess: z.boolean().optional(),
  dnsServers: z.array(z.string().ip()).optional(),
  allowedHosts: z.array(z.string()).optional(),
  blockedHosts: z.array(z.string()).optional(),
  portMappings: z.array(z.object({
    containerPort: z.number().min(1).max(65535),
    hostPort: z.number().min(1).max(65535).optional(),
    protocol: z.enum(['tcp', 'udp']).default('tcp'),
    description: z.string().optional(),
    public: z.boolean().default(false)
  })).optional(),
  firewallRules: z.array(z.object({
    type: z.enum(['allow', 'deny']),
    direction: z.enum(['inbound', 'outbound']),
    protocol: z.enum(['tcp', 'udp', 'icmp', 'all']),
    sourceIp: z.string().ip().optional(),
    sourcePort: z.number().min(1).max(65535).optional(),
    destinationIp: z.string().ip().optional(),
    destinationPort: z.number().min(1).max(65535).optional(),
    description: z.string().optional(),
    enabled: z.boolean().default(true),
    priority: z.number().min(1).max(100).default(50)
  })).optional(),
  bandwidth: z.object({
    uploadLimit: z.number().min(1).optional(),
    downloadLimit: z.number().min(1).optional()
  }).optional()
});

const portMappingSchema = z.object({
  containerPort: z.number().min(1).max(65535),
  hostPort: z.number().min(1).max(65535).optional(),
  protocol: z.enum(['tcp', 'udp']).default('tcp'),
  description: z.string().optional(),
  public: z.boolean().default(false)
});

const firewallRuleSchema = z.object({
  type: z.enum(['allow', 'deny']),
  direction: z.enum(['inbound', 'outbound']),
  protocol: z.enum(['tcp', 'udp', 'icmp', 'all']),
  sourceIp: z.string().ip().optional(),
  sourcePort: z.number().min(1).max(65535).optional(),
  destinationIp: z.string().ip().optional(),
  destinationPort: z.number().min(1).max(65535).optional(),
  description: z.string().optional(),
  enabled: z.boolean().default(true),
  priority: z.number().min(1).max(100).default(50)
});

const connectivityTestSchema = z.object({
  target: z.string().min(1),
  port: z.number().min(1).max(65535).optional(),
  timeout: z.number().min(1).max(30).default(5)
});

/**
 * GET /api/vibekraft-sandbox/sandboxes/[id]/network
 * Get network status and configuration
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { 
          error: 'Sandbox not running', 
          message: 'Sandbox must be running for network operations',
          currentState: status.state
        },
        { status: 409 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const operation = url.searchParams.get('operation') || 'status';

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    switch (operation) {
      case 'status': {
        // Get network status
        const networkStatus = await networkManager.getNetworkStatus(containerId);
        
        return NextResponse.json({
          sandboxId,
          operation: 'status',
          ...networkStatus,
          timestamp: new Date().toISOString()
        });
      }

      case 'test': {
        // Test connectivity
        const target = url.searchParams.get('target');
        const port = url.searchParams.get('port');

        if (!target) {
          return NextResponse.json(
            { error: 'Target parameter is required for connectivity test' },
            { status: 400 }
          );
        }

        const validation = connectivityTestSchema.safeParse({
          target,
          port: port ? parseInt(port) : undefined
        });

        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid test parameters', details: validation.error.format() },
            { status: 400 }
          );
        }

        const testResult = await networkManager.testConnectivity(
          containerId,
          validation.data.target,
          validation.data.port
        );

        return NextResponse.json({
          sandboxId,
          operation: 'test',
          target: validation.data.target,
          port: validation.data.port,
          ...testResult,
          timestamp: new Date().toISOString()
        });
      }

      default:
        return NextResponse.json(
          { error: 'Invalid operation', supportedOperations: ['status', 'test'] },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Network operation error:', error);
    return NextResponse.json(
      { error: 'Network operation failed', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vibekraft-sandbox/sandboxes/[id]/network
 * Configure network settings or perform network operations
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { 
          error: 'Sandbox not running', 
          message: 'Sandbox must be running for network operations',
          currentState: status.state
        },
        { status: 409 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { operation } = body;

    if (!operation) {
      return NextResponse.json({ error: 'Operation is required' }, { status: 400 });
    }

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    let result;

    switch (operation) {
      case 'configure': {
        const validation = networkConfigSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid request body', details: validation.error.format() },
            { status: 400 }
          );
        }

        result = await networkManager.configureNetwork(containerId, validation.data);
        break;
      }

      case 'add-port': {
        const validation = portMappingSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid request body', details: validation.error.format() },
            { status: 400 }
          );
        }

        result = await networkManager.addPortMapping(containerId, validation.data);
        break;
      }

      case 'add-firewall-rule': {
        const validation = firewallRuleSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid request body', details: validation.error.format() },
            { status: 400 }
          );
        }

        result = await networkManager.addFirewallRule(containerId, validation.data);
        break;
      }

      default:
        return NextResponse.json(
          { 
            error: 'Invalid operation', 
            supportedOperations: ['configure', 'add-port', 'add-firewall-rule'] 
          },
          { status: 400 }
        );
    }

    return NextResponse.json({
      sandboxId,
      ...result
    });

  } catch (error: any) {
    console.error('Network operation error:', error);
    return NextResponse.json(
      { error: 'Network operation failed', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/vibekraft-sandbox/sandboxes/[id]/network
 * Remove network configurations
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { 
          error: 'Sandbox not running', 
          message: 'Sandbox must be running for network operations',
          currentState: status.state
        },
        { status: 409 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const operation = url.searchParams.get('operation');
    const containerPort = url.searchParams.get('containerPort');
    const protocol = url.searchParams.get('protocol') as 'tcp' | 'udp' || 'tcp';

    if (!operation) {
      return NextResponse.json({ error: 'Operation parameter is required' }, { status: 400 });
    }

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    let result;

    switch (operation) {
      case 'remove-port': {
        if (!containerPort) {
          return NextResponse.json(
            { error: 'containerPort parameter is required for remove-port operation' },
            { status: 400 }
          );
        }

        result = await networkManager.removePortMapping(
          containerId,
          parseInt(containerPort),
          protocol
        );
        break;
      }

      default:
        return NextResponse.json(
          { error: 'Invalid operation', supportedOperations: ['remove-port'] },
          { status: 400 }
        );
    }

    return NextResponse.json({
      sandboxId,
      ...result
    });

  } catch (error: any) {
    console.error('Network operation error:', error);
    return NextResponse.json(
      { error: 'Network operation failed', message: error.message },
      { status: 500 }
    );
  }
}
