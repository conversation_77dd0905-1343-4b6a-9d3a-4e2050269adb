/**
 * VibeKraft Sandbox Individual API Routes
 * 
 * API endpoints for individual sandbox operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { SandboxManager } from '@/lib/vibekraft-sandbox/core/sandbox-manager';
import { z } from 'zod';

// Initialize sandbox manager
const sandboxManager = new SandboxManager({
  maxConcurrentSandboxes: 100,
  enableMonitoring: true,
  enableLogging: true,
  logLevel: 'info'
});

// Initialize manager on first use
let managerInitialized = false;
async function ensureManagerInitialized() {
  if (!managerInitialized) {
    await sandboxManager.initialize();
    managerInitialized = true;
  }
}

// Validation schemas
const updateSandboxSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  environment: z.record(z.string()).optional(),
  labels: z.record(z.string()).optional(),
  annotations: z.record(z.string()).optional()
});

/**
 * GET /api/vibekraft-sandbox/sandboxes/[id]
 * Get detailed information about a specific sandbox
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Get sandbox configuration
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json(
        { error: 'Sandbox not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (sandbox.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get sandbox status
    let status;
    try {
      status = await sandboxManager.getSandboxStatus(sandboxId);
    } catch (error) {
      // If we can't get status, create a basic one
      status = {
        id: sandboxId,
        state: 'unknown',
        health: { status: 'unknown', checks: [], lastChecked: new Date() },
        resources: {
          cpu: { usage: 0, cores: 0, throttled: false },
          memory: { usage: 0, limit: 0, percentage: 0, swap: 0 },
          disk: { usage: 0, limit: 0, percentage: 0, iops: 0 },
          network: { bytesIn: 0, bytesOut: 0, packetsIn: 0, packetsOut: 0, connections: 0 }
        },
        network: { interfaces: [], ports: [], connections: [] },
        processes: [],
        logs: []
      };
    }

    // Get recent operations
    const operations = sandboxManager.listOperations(sandboxId)
      .slice(-10) // Get last 10 operations
      .map(op => ({
        id: op.id,
        type: op.type,
        status: op.status,
        startedAt: op.startedAt,
        completedAt: op.completedAt,
        error: op.error
      }));

    return NextResponse.json({
      id: sandbox.id,
      name: sandbox.name,
      template: sandbox.template,
      projectId: sandbox.projectId,
      userId: sandbox.userId,
      
      // Current status
      state: status.state,
      health: status.health,
      
      // Configuration
      resources: sandbox.resources,
      network: sandbox.network,
      security: sandbox.security,
      environment: sandbox.environment,
      
      // Runtime information
      runtime: {
        resources: status.resources,
        network: status.network,
        processes: status.processes.slice(0, 20), // Limit to 20 processes
        uptime: sandbox.metadata.totalUptime
      },
      
      // Metadata
      metadata: sandbox.metadata,
      
      // Recent operations
      operations,
      
      // Recent logs (limited)
      logs: status.logs.slice(-50) // Last 50 log entries
    });

  } catch (error: any) {
    console.error('Error getting sandbox:', error);
    return NextResponse.json(
      { error: 'Failed to get sandbox', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/vibekraft-sandbox/sandboxes/[id]
 * Update sandbox configuration
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Get sandbox configuration
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json(
        { error: 'Sandbox not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (sandbox.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = updateSandboxSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request body', 
          details: validation.error.format() 
        },
        { status: 400 }
      );
    }

    const updates = validation.data;

    // Build update object
    const configUpdates: any = {
      metadata: {
        ...sandbox.metadata,
        updatedAt: new Date()
      }
    };

    if (updates.name) {
      configUpdates.name = updates.name;
    }

    if (updates.environment) {
      configUpdates.environment = {
        ...sandbox.environment,
        ...updates.environment
      };
    }

    if (updates.labels) {
      configUpdates.metadata.labels = {
        ...sandbox.metadata.labels,
        ...updates.labels
      };
    }

    if (updates.annotations) {
      configUpdates.metadata.annotations = {
        ...sandbox.metadata.annotations,
        ...updates.annotations
      };
    }

    // Update sandbox configuration
    await sandboxManager.updateSandboxConfig(sandboxId, configUpdates);

    // Get updated configuration
    const updatedSandbox = sandboxManager.getSandboxConfig(sandboxId);

    return NextResponse.json({
      id: updatedSandbox.id,
      name: updatedSandbox.name,
      template: updatedSandbox.template,
      projectId: updatedSandbox.projectId,
      environment: updatedSandbox.environment,
      metadata: updatedSandbox.metadata,
      message: 'Sandbox updated successfully'
    });

  } catch (error: any) {
    console.error('Error updating sandbox:', error);
    return NextResponse.json(
      { error: 'Failed to update sandbox', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/vibekraft-sandbox/sandboxes/[id]
 * Destroy a sandbox
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Get sandbox configuration
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json(
        { error: 'Sandbox not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (sandbox.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const force = url.searchParams.get('force') === 'true';

    // Check if sandbox is running and force is not specified
    try {
      const status = await sandboxManager.getSandboxStatus(sandboxId);
      if (status.state === 'running' && !force) {
        return NextResponse.json(
          { 
            error: 'Sandbox is running', 
            message: 'Stop the sandbox first or use force=true parameter' 
          },
          { status: 409 }
        );
      }
    } catch (error) {
      // If we can't get status, proceed with destruction
    }

    // Destroy sandbox
    await sandboxManager.destroySandbox(sandboxId);

    return NextResponse.json({
      id: sandboxId,
      message: 'Sandbox destroyed successfully'
    });

  } catch (error: any) {
    console.error('Error destroying sandbox:', error);
    return NextResponse.json(
      { error: 'Failed to destroy sandbox', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vibekraft-sandbox/sandboxes/[id]
 * Perform actions on a sandbox (start, stop, restart)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Get sandbox configuration
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json(
        { error: 'Sandbox not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (sandbox.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let result;
    switch (action) {
      case 'start':
        await sandboxManager.startSandbox(sandboxId);
        result = { action: 'start', status: 'started' };
        break;
      
      case 'stop':
        await sandboxManager.stopSandbox(sandboxId);
        result = { action: 'stop', status: 'stopped' };
        break;
      
      case 'restart':
        await sandboxManager.restartSandbox(sandboxId);
        result = { action: 'restart', status: 'restarted' };
        break;
      
      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    // Get updated status
    let status;
    try {
      status = await sandboxManager.getSandboxStatus(sandboxId);
    } catch (error) {
      status = { state: 'unknown' };
    }

    return NextResponse.json({
      id: sandboxId,
      name: sandbox.name,
      ...result,
      state: status.state,
      message: `Sandbox ${action} operation completed successfully`
    });

  } catch (error: any) {
    console.error('Error performing sandbox action:', error);
    return NextResponse.json(
      { error: 'Failed to perform action', message: error.message },
      { status: 500 }
    );
  }
}
