/**
 * VibeKraft Sandbox Package Management API
 * 
 * API endpoints for package management operations within sandbox containers
 */

import { NextRequest, NextResponse } from 'next/server';
// Mock authentication for demo purposes
const mockAuth = {
  user: { id: 'demo-user', email: '<EMAIL>' }
};
import { SandboxManager } from '@/lib/vibekraft-sandbox/core/sandbox-manager';
import { PackageManagerService } from '@/lib/vibekraft-sandbox/utils/package-manager';
import { z } from 'zod';

// Initialize managers
const sandboxManager = new SandboxManager();
const packageManagerService = new PackageManagerService(sandboxManager.getDockerManager());

// Initialize manager on first use
let managerInitialized = false;
async function ensureManagerInitialized() {
  if (!managerInitialized) {
    await sandboxManager.initialize();
    managerInitialized = true;
  }
}

// Validation schemas
const installPackagesSchema = z.object({
  packages: z.array(z.string()).min(1, 'At least one package is required'),
  manager: z.string().optional(),
  version: z.string().optional(),
  dev: z.boolean().default(false),
  global: z.boolean().default(false),
  save: z.boolean().default(true),
  exact: z.boolean().default(false),
  optional: z.boolean().default(false),
  workingDir: z.string().default('/workspace'),
  timeout: z.number().min(10).max(1800).default(600) // 10 minutes default, max 30 minutes
});

const uninstallPackagesSchema = z.object({
  packages: z.array(z.string()).min(1, 'At least one package is required'),
  manager: z.string().optional(),
  workingDir: z.string().default('/workspace')
});

const updatePackagesSchema = z.object({
  packages: z.array(z.string()).optional(), // If empty, update all
  manager: z.string().optional(),
  workingDir: z.string().default('/workspace')
});

const searchPackagesSchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  manager: z.string().min(1, 'Package manager is required'),
  limit: z.number().min(1).max(50).default(10),
  exact: z.boolean().default(false)
});

/**
 * GET /api/vibekraft-sandbox/sandboxes/[id]/packages
 * List installed packages or get package managers info
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { 
          error: 'Sandbox not running', 
          message: 'Sandbox must be running for package operations',
          currentState: status.state
        },
        { status: 409 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const operation = url.searchParams.get('operation') || 'list';
    const manager = url.searchParams.get('manager');
    const workingDir = url.searchParams.get('workingDir') || '/workspace';

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    switch (operation) {
      case 'managers': {
        // Get available package managers
        const managers = await packageManagerService.detectPackageManagers(containerId);
        
        return NextResponse.json({
          sandboxId,
          operation: 'managers',
          managers,
          timestamp: new Date().toISOString()
        });
      }

      case 'list': {
        // List installed packages
        const packageList = await packageManagerService.listPackages(containerId, manager || undefined, workingDir);
        
        return NextResponse.json({
          sandboxId,
          operation: 'list',
          ...packageList
        });
      }

      case 'search': {
        // Search for packages
        const query = url.searchParams.get('query');
        const limit = parseInt(url.searchParams.get('limit') || '10');
        const exact = url.searchParams.get('exact') === 'true';

        if (!query || !manager) {
          return NextResponse.json(
            { error: 'Query and manager parameters are required for search' },
            { status: 400 }
          );
        }

        const validation = searchPackagesSchema.safeParse({ query, manager, limit, exact });
        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid search parameters', details: validation.error.format() },
            { status: 400 }
          );
        }

        const searchResults = await packageManagerService.searchPackages(containerId, validation.data);
        
        return NextResponse.json({
          sandboxId,
          operation: 'search',
          query,
          manager,
          results: searchResults,
          totalResults: searchResults.length,
          timestamp: new Date().toISOString()
        });
      }

      default:
        return NextResponse.json(
          { error: 'Invalid operation', supportedOperations: ['managers', 'list', 'search'] },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Package operation error:', error);
    return NextResponse.json(
      { error: 'Package operation failed', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vibekraft-sandbox/sandboxes/[id]/packages
 * Install packages or perform package operations
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { 
          error: 'Sandbox not running', 
          message: 'Sandbox must be running for package operations',
          currentState: status.state
        },
        { status: 409 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { operation } = body;

    if (!operation) {
      return NextResponse.json({ error: 'Operation is required' }, { status: 400 });
    }

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    let result;

    switch (operation) {
      case 'install': {
        const validation = installPackagesSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid request body', details: validation.error.format() },
            { status: 400 }
          );
        }

        result = await packageManagerService.installPackages(containerId, validation.data);
        break;
      }

      case 'uninstall': {
        const validation = uninstallPackagesSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid request body', details: validation.error.format() },
            { status: 400 }
          );
        }

        result = await packageManagerService.uninstallPackages(
          containerId,
          validation.data.packages,
          validation.data.manager,
          validation.data.workingDir
        );
        break;
      }

      case 'update': {
        const validation = updatePackagesSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid request body', details: validation.error.format() },
            { status: 400 }
          );
        }

        result = await packageManagerService.updatePackages(
          containerId,
          validation.data.packages,
          validation.data.manager,
          validation.data.workingDir
        );
        break;
      }

      default:
        return NextResponse.json(
          { 
            error: 'Invalid operation', 
            supportedOperations: ['install', 'uninstall', 'update'] 
          },
          { status: 400 }
        );
    }

    return NextResponse.json({
      sandboxId,
      ...result
    });

  } catch (error: any) {
    console.error('Package operation error:', error);
    return NextResponse.json(
      { error: 'Package operation failed', message: error.message },
      { status: 500 }
    );
  }
}
