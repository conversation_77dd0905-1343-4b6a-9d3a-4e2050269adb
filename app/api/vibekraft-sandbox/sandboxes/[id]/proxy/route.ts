/**
 * VibeKraft Sandbox Application Preview/Proxy API
 * 
 * API endpoints for application preview and reverse proxy management within sandbox containers
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { SandboxManager } from '@/lib/vibekraft-sandbox/core/sandbox-manager';
import { ProxyManager } from '@/lib/vibekraft-sandbox/utils/proxy-manager';
import { z } from 'zod';

// Initialize managers
const sandboxManager = new SandboxManager();
const proxyManager = new ProxyManager(sandboxManager.getDockerManager());

// Initialize manager on first use
let managerInitialized = false;
async function ensureManagerInitialized() {
  if (!managerInitialized) {
    await sandboxManager.initialize();
    managerInitialized = true;
  }
}

// Validation schemas
const proxyRequestSchema = z.object({
  name: z.string().min(1, 'Name is required').max(50, 'Name too long'),
  containerPort: z.number().min(1).max(65535),
  protocol: z.enum(['http', 'https', 'ws', 'wss']).default('http'),
  path: z.string().default('/'),
  subdomain: z.string().optional(),
  customDomain: z.string().optional(),
  healthCheck: z.object({
    enabled: z.boolean().default(true),
    path: z.string().default('/health'),
    interval: z.number().min(5).max(300).default(30),
    timeout: z.number().min(1).max(60).default(5),
    retries: z.number().min(1).max(10).default(3)
  }).optional(),
  ssl: z.object({
    enabled: z.boolean().default(false),
    autoGenerate: z.boolean().default(true)
  }).optional(),
  auth: z.object({
    enabled: z.boolean().default(false),
    type: z.enum(['basic', 'bearer', 'custom']).default('basic'),
    credentials: z.record(z.string()).optional()
  }).optional(),
  rateLimit: z.object({
    enabled: z.boolean().default(false),
    requests: z.number().min(1).max(10000).default(100),
    window: z.number().min(1).max(3600).default(60)
  }).optional(),
  cors: z.object({
    enabled: z.boolean().default(true),
    origins: z.array(z.string()).default(['*']),
    methods: z.array(z.string()).default(['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']),
    headers: z.array(z.string()).default(['Content-Type', 'Authorization'])
  }).optional()
});

const proxyUpdateSchema = z.object({
  name: z.string().min(1).max(50).optional(),
  path: z.string().optional(),
  healthCheck: z.object({
    enabled: z.boolean().optional(),
    path: z.string().optional(),
    interval: z.number().min(5).max(300).optional(),
    timeout: z.number().min(1).max(60).optional(),
    retries: z.number().min(1).max(10).optional()
  }).optional(),
  auth: z.object({
    enabled: z.boolean().optional(),
    type: z.enum(['basic', 'bearer', 'custom']).optional(),
    credentials: z.record(z.string()).optional()
  }).optional(),
  rateLimit: z.object({
    enabled: z.boolean().optional(),
    requests: z.number().min(1).max(10000).optional(),
    window: z.number().min(1).max(3600).optional()
  }).optional(),
  cors: z.object({
    enabled: z.boolean().optional(),
    origins: z.array(z.string()).optional(),
    methods: z.array(z.string()).optional(),
    headers: z.array(z.string()).optional()
  }).optional()
});

/**
 * GET /api/vibekraft-sandbox/sandboxes/[id]/proxy
 * Get proxy targets, stats, or test proxy target
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { 
          error: 'Sandbox not running', 
          message: 'Sandbox must be running for proxy operations',
          currentState: status.state
        },
        { status: 409 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const operation = url.searchParams.get('operation') || 'list';
    const targetId = url.searchParams.get('targetId');

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    switch (operation) {
      case 'list': {
        // Get all proxy targets for this sandbox
        const targets = await proxyManager.getProxyTargets(sandboxId);
        
        return NextResponse.json({
          sandboxId,
          operation: 'list',
          targets,
          totalTargets: targets.length,
          timestamp: new Date().toISOString()
        });
      }

      case 'get': {
        // Get specific proxy target
        if (!targetId) {
          return NextResponse.json(
            { error: 'targetId parameter is required for get operation' },
            { status: 400 }
          );
        }

        const target = await proxyManager.getProxyTarget(targetId);
        if (!target) {
          return NextResponse.json({ error: 'Proxy target not found' }, { status: 404 });
        }

        return NextResponse.json({
          sandboxId,
          operation: 'get',
          target,
          timestamp: new Date().toISOString()
        });
      }

      case 'stats': {
        // Get proxy statistics
        const stats = await proxyManager.getProxyStats(sandboxId);
        
        return NextResponse.json({
          sandboxId,
          operation: 'stats',
          stats,
          timestamp: new Date().toISOString()
        });
      }

      case 'test': {
        // Test proxy target
        if (!targetId) {
          return NextResponse.json(
            { error: 'targetId parameter is required for test operation' },
            { status: 400 }
          );
        }

        const testResult = await proxyManager.testProxyTarget(containerId, targetId);
        
        return NextResponse.json({
          sandboxId,
          operation: 'test',
          targetId,
          ...testResult,
          timestamp: new Date().toISOString()
        });
      }

      default:
        return NextResponse.json(
          { error: 'Invalid operation', supportedOperations: ['list', 'get', 'stats', 'test'] },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Proxy operation error:', error);
    return NextResponse.json(
      { error: 'Proxy operation failed', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vibekraft-sandbox/sandboxes/[id]/proxy
 * Create proxy target or perform proxy operations
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { 
          error: 'Sandbox not running', 
          message: 'Sandbox must be running for proxy operations',
          currentState: status.state
        },
        { status: 409 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { operation } = body;

    if (!operation) {
      return NextResponse.json({ error: 'Operation is required' }, { status: 400 });
    }

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    let result;

    switch (operation) {
      case 'create': {
        const validation = proxyRequestSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid request body', details: validation.error.format() },
            { status: 400 }
          );
        }

        const target = await proxyManager.createProxyTarget(containerId, sandboxId, validation.data);
        
        result = {
          success: true,
          operation: 'create',
          target,
          timestamp: new Date()
        };
        break;
      }

      case 'update': {
        const { targetId, ...updateData } = body;
        
        if (!targetId) {
          return NextResponse.json(
            { error: 'targetId is required for update operation' },
            { status: 400 }
          );
        }

        const validation = proxyUpdateSchema.safeParse(updateData);
        if (!validation.success) {
          return NextResponse.json(
            { error: 'Invalid request body', details: validation.error.format() },
            { status: 400 }
          );
        }

        const target = await proxyManager.updateProxyTarget(containerId, targetId, validation.data);
        
        result = {
          success: true,
          operation: 'update',
          target,
          timestamp: new Date()
        };
        break;
      }

      case 'toggle': {
        const { targetId, enabled } = body;
        
        if (!targetId || typeof enabled !== 'boolean') {
          return NextResponse.json(
            { error: 'targetId and enabled (boolean) are required for toggle operation' },
            { status: 400 }
          );
        }

        const target = await proxyManager.toggleProxyTarget(containerId, targetId, enabled);
        
        result = {
          success: true,
          operation: 'toggle',
          target,
          timestamp: new Date()
        };
        break;
      }

      default:
        return NextResponse.json(
          { 
            error: 'Invalid operation', 
            supportedOperations: ['create', 'update', 'toggle'] 
          },
          { status: 400 }
        );
    }

    return NextResponse.json({
      sandboxId,
      ...result
    });

  } catch (error: any) {
    console.error('Proxy operation error:', error);
    return NextResponse.json(
      { error: 'Proxy operation failed', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/vibekraft-sandbox/sandboxes/[id]/proxy
 * Delete proxy target
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { 
          error: 'Sandbox not running', 
          message: 'Sandbox must be running for proxy operations',
          currentState: status.state
        },
        { status: 409 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const targetId = url.searchParams.get('targetId');

    if (!targetId) {
      return NextResponse.json(
        { error: 'targetId parameter is required' },
        { status: 400 }
      );
    }

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    // Delete proxy target
    await proxyManager.deleteProxyTarget(containerId, targetId);

    return NextResponse.json({
      sandboxId,
      success: true,
      operation: 'delete',
      targetId,
      message: 'Proxy target deleted successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('Proxy operation error:', error);
    return NextResponse.json(
      { error: 'Proxy operation failed', message: error.message },
      { status: 500 }
    );
  }
}
