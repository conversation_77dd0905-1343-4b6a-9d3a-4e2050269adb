/**
 * VibeKraft Sandbox Filesystem API
 * 
 * API endpoints for filesystem operations in sandboxes
 */

import { NextRequest, NextResponse } from 'next/server';
// Mock authentication for demo purposes
const mockAuth = {
  user: { id: 'demo-user', email: '<EMAIL>' }
};
import { SandboxManager } from '@/lib/vibekraft-sandbox/core/sandbox-manager';
import { FileSystemManager } from '@/lib/vibekraft-sandbox/utils/filesystem-utils';
import { z } from 'zod';

// Initialize sandbox manager
const sandboxManager = new SandboxManager();
const fileSystemManager = new FileSystemManager(sandboxManager.getDockerManager());

// Initialize manager on first use
let managerInitialized = false;
async function ensureManagerInitialized() {
  if (!managerInitialized) {
    await sandboxManager.initialize();
    managerInitialized = true;
  }
}

// Validation schemas
const listFilesSchema = z.object({
  path: z.string().default('/workspace'),
  recursive: z.boolean().default(false),
  includeHidden: z.boolean().default(false),
  sortBy: z.enum(['name', 'size', 'modified', 'type']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
});

const createFileSchema = z.object({
  path: z.string().min(1, 'Path is required'),
  content: z.string().default(''),
  encoding: z.enum(['utf8', 'base64']).default('utf8'),
  permissions: z.string().optional(),
  createDirectories: z.boolean().default(true)
});

const createDirectorySchema = z.object({
  path: z.string().min(1, 'Path is required'),
  permissions: z.string().optional(),
  recursive: z.boolean().default(true)
});

const moveFileSchema = z.object({
  source: z.string().min(1, 'Source path is required'),
  destination: z.string().min(1, 'Destination path is required'),
  overwrite: z.boolean().default(false)
});

const copyFileSchema = z.object({
  source: z.string().min(1, 'Source path is required'),
  destination: z.string().min(1, 'Destination path is required'),
  overwrite: z.boolean().default(false),
  recursive: z.boolean().default(false)
});

const deleteFileSchema = z.object({
  path: z.string().min(1, 'Path is required'),
  recursive: z.boolean().default(false),
  force: z.boolean().default(false)
});

const searchFilesSchema = z.object({
  query: z.string().min(1, 'Search query is required'),
  path: z.string().default('/workspace'),
  fileTypes: z.array(z.string()).optional(),
  maxResults: z.number().min(1).max(1000).default(100),
  includeContent: z.boolean().default(false)
});

/**
 * GET /api/vibekraft-sandbox/sandboxes/[id]/filesystem
 * List files and directories in the sandbox
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Parse query parameters
    const url = new URL(request.url);
    const operation = url.searchParams.get('operation') || 'list';
    const path = url.searchParams.get('path') || '/workspace';
    const recursive = url.searchParams.get('recursive') === 'true';
    const includeHidden = url.searchParams.get('includeHidden') === 'true';
    const sortBy = url.searchParams.get('sortBy') || 'name';
    const sortOrder = url.searchParams.get('sortOrder') || 'asc';

    const validation = listFilesSchema.safeParse({
      path,
      recursive,
      includeHidden,
      sortBy,
      sortOrder
    });
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: validation.error.format() },
        { status: 400 }
      );
    }

    // Use the already parsed parameters

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { error: 'Sandbox not running', currentState: status.state },
        { status: 409 }
      );
    }

    // For now, we'll use the sandboxId as containerId
    // In a real implementation, this would be retrieved from the sandbox manager
    const containerId = `vibekraft-${sandboxId}`;

    // Handle different operations
    switch (operation) {
      case 'list': {
        // Use FileSystemManager for enhanced directory listing
        const listing = await fileSystemManager.listDirectory(containerId, path);

        // Sort files if needed
        const sortedFiles = sortFiles(listing.items, sortBy, sortOrder);

        return NextResponse.json({
          sandboxId,
          operation: 'list',
          path: listing.path,
          files: sortedFiles,
          totalFiles: listing.totalItems,
          totalSize: listing.totalSize,
          permissions: listing.permissions,
          recursive,
          includeHidden
        });
      }

      case 'read': {
        // Read file content
        const filePath = url.searchParams.get('filePath');
        if (!filePath) {
          return NextResponse.json({ error: 'filePath parameter required for read operation' }, { status: 400 });
        }

        const fileContent = await fileSystemManager.readFile(containerId, filePath);

        return NextResponse.json({
          sandboxId,
          operation: 'read',
          ...fileContent
        });
      }

      default:
        return NextResponse.json(
          { error: 'Invalid operation', supportedOperations: ['list', 'read'] },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Error listing files:', error);
    return NextResponse.json(
      { error: 'Failed to list files', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vibekraft-sandbox/sandboxes/[id]/filesystem
 * Create files or directories
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { operation, ...data } = body;

    if (!operation) {
      return NextResponse.json({ error: 'Operation is required' }, { status: 400 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { error: 'Sandbox not running', currentState: status.state },
        { status: 409 }
      );
    }

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    let result;
    switch (operation) {
      case 'write':
        result = await fileSystemManager.writeFile(containerId, data);
        break;
      case 'mkdir':
        result = await fileSystemManager.createDirectory(containerId, data.path);
        break;
      case 'move':
        result = await fileSystemManager.move(containerId, data.source, data.destination);
        break;
      case 'copy':
        result = await fileSystemManager.copy(containerId, data.source, data.destination);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid operation', supportedOperations: ['write', 'mkdir', 'move', 'copy'] },
          { status: 400 }
        );
    }

    return NextResponse.json({
      sandboxId,
      ...result
    });

  } catch (error: any) {
    console.error('Error creating file/directory:', error);
    return NextResponse.json(
      { error: 'Failed to create file/directory', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/vibekraft-sandbox/sandboxes/[id]/filesystem
 * Move or copy files/directories
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { operation, ...data } = body;

    if (!operation || !['move', 'copy'].includes(operation)) {
      return NextResponse.json(
        { error: 'Operation must be either "move" or "copy"' },
        { status: 400 }
      );
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { error: 'Sandbox not running', currentState: status.state },
        { status: 409 }
      );
    }

    let result;
    if (operation === 'move') {
      result = await moveFile(sandboxManager, sandboxId, data);
    } else {
      result = await copyFile(sandboxManager, sandboxId, data);
    }

    return NextResponse.json({
      sandboxId,
      operation,
      source: data.source,
      destination: data.destination,
      message: `File ${operation}d successfully`,
      ...result
    });

  } catch (error: any) {
    console.error('Error moving/copying file:', error);
    return NextResponse.json(
      { error: 'Failed to move/copy file', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/vibekraft-sandbox/sandboxes/[id]/filesystem
 * Delete files or directories
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Verify sandbox ownership
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json({ error: 'Sandbox not found' }, { status: 404 });
    }

    if (sandbox.userId !== session.user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get query parameters for path and options
    const url = new URL(request.url);
    const path = url.searchParams.get('path');
    const recursive = url.searchParams.get('recursive') === 'true';

    if (!path) {
      return NextResponse.json({ error: 'Path parameter is required' }, { status: 400 });
    }

    // Check if sandbox is running
    const status = await sandboxManager.getSandboxStatus(sandboxId);
    if (status.state !== 'running') {
      return NextResponse.json(
        { error: 'Sandbox not running', currentState: status.state },
        { status: 409 }
      );
    }

    // Get container ID
    const containerId = `vibekraft-${sandboxId}`;

    // Use FileSystemManager for delete operation
    const result = await fileSystemManager.delete(containerId, path, recursive);

    return NextResponse.json({
      sandboxId,
      ...result
    });

  } catch (error: any) {
    console.error('Error deleting file:', error);
    return NextResponse.json(
      { error: 'Failed to delete file', message: error.message },
      { status: 500 }
    );
  }
}

// Helper functions
function parseLsOutput(output: string, basePath: string): any[] {
  const lines = output.split('\n').filter(line => line.trim());
  const files: any[] = [];

  for (const line of lines) {
    if (line.startsWith('total ') || line.trim() === '') continue;
    
    const parts = line.split(/\s+/);
    if (parts.length < 9) continue;

    const permissions = parts[0];
    const links = parseInt(parts[1]);
    const owner = parts[2];
    const group = parts[3];
    const size = parseInt(parts[4]);
    const date = parts[5];
    const time = parts[6];
    const name = parts.slice(8).join(' ');

    if (name === '.' || name === '..') continue;

    const isDirectory = permissions.startsWith('d');
    const isSymlink = permissions.startsWith('l');

    files.push({
      name,
      path: `${basePath}/${name}`.replace(/\/+/g, '/'),
      type: isDirectory ? 'directory' : isSymlink ? 'symlink' : 'file',
      size,
      permissions,
      owner,
      group,
      links,
      modified: `${date} ${time}`,
      isHidden: name.startsWith('.')
    });
  }

  return files;
}

function sortFiles(files: any[], sortBy: string, sortOrder: string): any[] {
  return files.sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'size':
        comparison = a.size - b.size;
        break;
      case 'modified':
        comparison = new Date(a.modified).getTime() - new Date(b.modified).getTime();
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
}

async function createFile(manager: SandboxManager, sandboxId: string, data: any) {
  const validation = createFileSchema.safeParse(data);
  if (!validation.success) {
    throw new Error(`Invalid file data: ${validation.error.message}`);
  }

  const { path, content, encoding, permissions, createDirectories } = validation.data;

  // Create directories if needed
  if (createDirectories) {
    const dir = path.substring(0, path.lastIndexOf('/'));
    if (dir) {
      await manager.executeCommand(sandboxId, {
        command: ['mkdir', '-p', dir],
        timeout: 30
      });
    }
  }

  // Create file with content
  const createCommand = encoding === 'base64' 
    ? ['bash', '-c', `echo '${content}' | base64 -d > '${path}'`]
    : ['bash', '-c', `cat > '${path}' << 'EOF'\n${content}\nEOF`];

  const result = await manager.executeCommand(sandboxId, {
    command: createCommand,
    timeout: 60
  });

  if (result.exitCode !== 0) {
    throw new Error(`Failed to create file: ${result.stderr}`);
  }

  // Set permissions if specified
  if (permissions) {
    await manager.executeCommand(sandboxId, {
      command: ['chmod', permissions, path],
      timeout: 30
    });
  }

  return { created: true };
}

async function createDirectory(manager: SandboxManager, sandboxId: string, data: any) {
  const validation = createDirectorySchema.safeParse(data);
  if (!validation.success) {
    throw new Error(`Invalid directory data: ${validation.error.message}`);
  }

  const { path, permissions, recursive } = validation.data;

  const mkdirCommand = [
    'mkdir',
    ...(recursive ? ['-p'] : []),
    path
  ];

  const result = await manager.executeCommand(sandboxId, {
    command: mkdirCommand,
    timeout: 30
  });

  if (result.exitCode !== 0) {
    throw new Error(`Failed to create directory: ${result.stderr}`);
  }

  // Set permissions if specified
  if (permissions) {
    await manager.executeCommand(sandboxId, {
      command: ['chmod', permissions, path],
      timeout: 30
    });
  }

  return { created: true };
}

async function moveFile(manager: SandboxManager, sandboxId: string, data: any) {
  const validation = moveFileSchema.safeParse(data);
  if (!validation.success) {
    throw new Error(`Invalid move data: ${validation.error.message}`);
  }

  const { source, destination, overwrite } = validation.data;

  const mvCommand = [
    'mv',
    ...(overwrite ? [] : ['-n']),
    source,
    destination
  ];

  const result = await manager.executeCommand(sandboxId, {
    command: mvCommand,
    timeout: 60
  });

  if (result.exitCode !== 0) {
    throw new Error(`Failed to move file: ${result.stderr}`);
  }

  return { moved: true };
}

async function copyFile(manager: SandboxManager, sandboxId: string, data: any) {
  const validation = copyFileSchema.safeParse(data);
  if (!validation.success) {
    throw new Error(`Invalid copy data: ${validation.error.message}`);
  }

  const { source, destination, overwrite, recursive } = validation.data;

  const cpCommand = [
    'cp',
    ...(recursive ? ['-r'] : []),
    ...(overwrite ? [] : ['-n']),
    source,
    destination
  ];

  const result = await manager.executeCommand(sandboxId, {
    command: cpCommand,
    timeout: 60
  });

  if (result.exitCode !== 0) {
    throw new Error(`Failed to copy file: ${result.stderr}`);
  }

  return { copied: true };
}
