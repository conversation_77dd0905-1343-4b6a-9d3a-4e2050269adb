/**
 * VibeKraft Sandbox Execute Command API
 * 
 * API endpoint for executing commands in sandboxes
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { SandboxManager } from '@/lib/vibekraft-sandbox/core/sandbox-manager';
import { ExecuteCommandRequest } from '@/lib/vibekraft-sandbox/types/sandbox';
import { z } from 'zod';

// Initialize sandbox manager
const sandboxManager = new SandboxManager({
  maxConcurrentSandboxes: 100,
  enableMonitoring: true,
  enableLogging: true,
  logLevel: 'info'
});

// Initialize manager on first use
let managerInitialized = false;
async function ensureManagerInitialized() {
  if (!managerInitialized) {
    await sandboxManager.initialize();
    managerInitialized = true;
  }
}

// Validation schema
const executeCommandSchema = z.object({
  command: z.array(z.string()).min(1, 'Command is required'),
  workingDir: z.string().optional(),
  environment: z.record(z.string()).optional(),
  user: z.string().optional(),
  timeout: z.number().min(1).max(300).optional(), // Max 5 minutes
  interactive: z.boolean().default(false)
});

/**
 * POST /api/vibekraft-sandbox/sandboxes/[id]/execute
 * Execute a command in the sandbox
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Get sandbox configuration
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json(
        { error: 'Sandbox not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (sandbox.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Check if sandbox is running
    let status;
    try {
      status = await sandboxManager.getSandboxStatus(sandboxId);
      if (status.state !== 'running') {
        return NextResponse.json(
          { 
            error: 'Sandbox not running', 
            message: 'Sandbox must be running to execute commands',
            currentState: status.state
          },
          { status: 409 }
        );
      }
    } catch (error) {
      return NextResponse.json(
        { error: 'Unable to determine sandbox status' },
        { status: 500 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = executeCommandSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request body', 
          details: validation.error.format() 
        },
        { status: 400 }
      );
    }

    const data = validation.data;

    // Security checks for command execution
    const securityChecks = performSecurityChecks(data.command, sandbox);
    if (!securityChecks.allowed) {
      return NextResponse.json(
        { 
          error: 'Command not allowed', 
          message: securityChecks.reason,
          blockedCommand: data.command.join(' ')
        },
        { status: 403 }
      );
    }

    // Build execute request
    const executeRequest: ExecuteCommandRequest = {
      command: data.command,
      workingDir: data.workingDir || '/workspace',
      environment: {
        ...sandbox.environment,
        ...data.environment
      },
      user: data.user || 'sandbox',
      timeout: data.timeout || 30,
      interactive: data.interactive
    };

    // Execute command
    const startTime = Date.now();
    let result;
    
    try {
      result = await sandboxManager.executeCommand(sandboxId, executeRequest);
    } catch (error: any) {
      return NextResponse.json(
        { 
          error: 'Command execution failed', 
          message: error.message,
          command: data.command.join(' '),
          duration: Date.now() - startTime
        },
        { status: 500 }
      );
    }

    // Log command execution for audit
    console.log(`Command executed in sandbox ${sandboxId}:`, {
      user: session.user.email,
      command: data.command.join(' '),
      exitCode: result.exitCode,
      duration: result.duration
    });

    return NextResponse.json({
      sandboxId,
      command: data.command.join(' '),
      exitCode: result.exitCode,
      stdout: result.stdout,
      stderr: result.stderr,
      duration: result.duration,
      timedOut: result.timedOut,
      workingDir: executeRequest.workingDir,
      user: executeRequest.user,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('Error executing command:', error);
    return NextResponse.json(
      { error: 'Failed to execute command', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET /api/vibekraft-sandbox/sandboxes/[id]/execute
 * Get command execution history
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    const sandboxId = params.id;

    // Get sandbox configuration
    let sandbox;
    try {
      sandbox = sandboxManager.getSandboxConfig(sandboxId);
    } catch (error) {
      return NextResponse.json(
        { error: 'Sandbox not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (sandbox.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const type = url.searchParams.get('type'); // 'execute' only for this endpoint

    // Get execution operations
    const operations = sandboxManager.listOperations(sandboxId)
      .filter(op => op.type === 'execute')
      .sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime()) // Most recent first
      .slice(offset, offset + limit)
      .map(op => ({
        id: op.id,
        type: op.type,
        status: op.status,
        startedAt: op.startedAt,
        completedAt: op.completedAt,
        duration: op.completedAt ? 
          op.completedAt.getTime() - op.startedAt.getTime() : null,
        result: op.result ? {
          exitCode: op.result.exitCode,
          stdout: op.result.stdout?.substring(0, 1000), // Truncate for list view
          stderr: op.result.stderr?.substring(0, 1000),
          timedOut: op.result.timedOut
        } : null,
        error: op.error
      }));

    const total = sandboxManager.listOperations(sandboxId)
      .filter(op => op.type === 'execute').length;

    return NextResponse.json({
      sandboxId,
      executions: operations,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    });

  } catch (error: any) {
    console.error('Error getting execution history:', error);
    return NextResponse.json(
      { error: 'Failed to get execution history', message: error.message },
      { status: 500 }
    );
  }
}

// Security check function
function performSecurityChecks(command: string[], sandbox: any): { allowed: boolean; reason?: string } {
  const commandStr = command.join(' ').toLowerCase();
  
  // Blocked commands for security
  const blockedCommands = [
    'rm -rf /',
    'dd if=',
    'mkfs',
    'fdisk',
    'mount',
    'umount',
    'chroot',
    'sudo su',
    'passwd',
    'useradd',
    'userdel',
    'usermod',
    'groupadd',
    'groupdel',
    'crontab',
    'systemctl',
    'service',
    'iptables',
    'netfilter',
    'tc ',
    'ip route',
    'ifconfig'
  ];

  // Check for blocked commands
  for (const blocked of blockedCommands) {
    if (commandStr.includes(blocked)) {
      return {
        allowed: false,
        reason: `Command contains blocked operation: ${blocked}`
      };
    }
  }

  // Check for dangerous file operations
  if (commandStr.includes('rm') && (commandStr.includes('-rf') || commandStr.includes('-r'))) {
    if (commandStr.includes('/etc') || commandStr.includes('/usr') || commandStr.includes('/var')) {
      return {
        allowed: false,
        reason: 'Dangerous file deletion operation detected'
      };
    }
  }

  // Check sudo usage if not allowed
  if (!sandbox.security.allowSudo && commandStr.startsWith('sudo')) {
    return {
      allowed: false,
      reason: 'Sudo access not allowed for this sandbox'
    };
  }

  // Check network operations if not allowed
  if (!sandbox.security.allowNetworkAccess) {
    const networkCommands = ['curl', 'wget', 'nc', 'netcat', 'telnet', 'ssh', 'scp', 'rsync'];
    for (const netCmd of networkCommands) {
      if (command[0] === netCmd || commandStr.includes(netCmd)) {
        return {
          allowed: false,
          reason: 'Network access not allowed for this sandbox'
        };
      }
    }
  }

  // Check for attempts to escape sandbox
  if (commandStr.includes('docker') || commandStr.includes('podman') || 
      commandStr.includes('lxc') || commandStr.includes('lxd')) {
    return {
      allowed: false,
      reason: 'Container operations not allowed within sandbox'
    };
  }

  // Check for kernel module operations
  if (commandStr.includes('modprobe') || commandStr.includes('insmod') || 
      commandStr.includes('rmmod') || commandStr.includes('lsmod')) {
    return {
      allowed: false,
      reason: 'Kernel module operations not allowed'
    };
  }

  return { allowed: true };
}
