/**
 * VibeKraft Sandbox API Routes
 * 
 * Main API endpoints for sandbox management
 */

import { NextRequest, NextResponse } from 'next/server';

// Mock authentication for demo purposes
const mockAuth = {
  user: { id: 'demo-user', email: '<EMAIL>' }
};

// Mock data store (in production, this would be a database)
const mockSandboxes = new Map();

// Initialize with some demo data
mockSandboxes.set('sandbox-001', {
  id: 'sandbox-001',
  name: 'Node.js Development',
  template: 'vibekraft/nodejs-minimal:latest',
  projectId: 'project-1',
  userId: 'demo-user',
  state: 'running',
  health: {
    status: 'healthy',
    checks: [],
    lastChecked: new Date().toISOString()
  },
  resources: {
    vcpuCount: 2,
    memSizeMib: 1024,
    diskSizeGb: 10
  },
  network: {
    ports: 3,
    isolated: false
  },
  createdAt: new Date(Date.now() - 86400000).toISOString(),
  lastAccessedAt: new Date(Date.now() - 3600000).toISOString(),
  labels: {
    'environment': 'development',
    'project': 'demo'
  },
  annotations: {
    'created-by': 'demo-user'
  }
});

mockSandboxes.set('sandbox-002', {
  id: 'sandbox-002',
  name: 'Python Data Science',
  template: 'vibekraft/python-minimal:latest',
  projectId: 'project-1',
  userId: 'demo-user',
  state: 'stopped',
  health: {
    status: 'unknown',
    checks: [],
    lastChecked: new Date().toISOString()
  },
  resources: {
    vcpuCount: 1,
    memSizeMib: 512,
    diskSizeGb: 5
  },
  network: {
    ports: 1,
    isolated: true
  },
  createdAt: new Date(Date.now() - 172800000).toISOString(),
  lastAccessedAt: new Date(Date.now() - 7200000).toISOString(),
  labels: {
    'environment': 'development',
    'type': 'data-science'
  },
  annotations: {
    'created-by': 'demo-user'
  }
});

// Simulate async operations
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Simple validation functions (replacing zod to avoid dependencies)
function validateCreateSandbox(data: any) {
  const errors: string[] = [];

  if (!data.name || typeof data.name !== 'string' || data.name.length === 0) {
    errors.push('Name is required');
  }

  if (!data.template || typeof data.template !== 'string') {
    errors.push('Template is required');
  }

  if (data.resources) {
    if (data.resources.vcpuCount && (data.resources.vcpuCount < 1 || data.resources.vcpuCount > 8)) {
      errors.push('CPU count must be between 1 and 8');
    }
    if (data.resources.memSizeMib && (data.resources.memSizeMib < 512 || data.resources.memSizeMib > 8192)) {
      errors.push('Memory must be between 512MB and 8GB');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * GET /api/vibekraft-sandbox/sandboxes
 * List all sandboxes for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    // Get query parameters
    const url = new URL(request.url);
    const projectId = url.searchParams.get('projectId');
    const status = url.searchParams.get('status');
    const template = url.searchParams.get('template');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // List sandboxes
    let sandboxes = await sandboxManager.listSandboxes();

    // Apply filters
    if (status) {
      // Filter by status would require getting status for each sandbox
      // This is a simplified implementation
    }

    if (template) {
      sandboxes = sandboxes.filter(s => s.template === template);
    }

    // Apply pagination
    const total = sandboxes.length;
    const paginatedSandboxes = sandboxes.slice(offset, offset + limit);

    // Get status for each sandbox
    const sandboxesWithStatus = await Promise.all(
      paginatedSandboxes.map(async (sandbox) => {
        try {
          const status = await sandboxManager.getSandboxStatus(sandbox.id);
          return {
            id: sandbox.id,
            name: sandbox.name,
            template: sandbox.template,
            projectId: sandbox.projectId,
            state: status.state,
            health: status.health,
            resources: {
              vcpuCount: sandbox.resources.vcpuCount,
              memSizeMib: sandbox.resources.memSizeMib,
              diskSizeGb: sandbox.resources.diskSizeGb
            },
            network: {
              ports: sandbox.network.ports.length,
              isolated: sandbox.network.isolated
            },
            createdAt: sandbox.metadata.createdAt,
            lastAccessedAt: sandbox.metadata.lastAccessedAt,
            labels: sandbox.metadata.labels,
            annotations: sandbox.metadata.annotations
          };
        } catch (error) {
          // If we can't get status, return basic info
          return {
            id: sandbox.id,
            name: sandbox.name,
            template: sandbox.template,
            projectId: sandbox.projectId,
            state: 'unknown',
            health: { status: 'unknown', checks: [], lastChecked: new Date() },
            resources: {
              vcpuCount: sandbox.resources.vcpuCount,
              memSizeMib: sandbox.resources.memSizeMib,
              diskSizeGb: sandbox.resources.diskSizeGb
            },
            network: {
              ports: sandbox.network.ports.length,
              isolated: sandbox.network.isolated
            },
            createdAt: sandbox.metadata.createdAt,
            lastAccessedAt: sandbox.metadata.lastAccessedAt,
            labels: sandbox.metadata.labels,
            annotations: sandbox.metadata.annotations
          };
        }
      })
    );

    return NextResponse.json({
      sandboxes: sandboxesWithStatus,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    });

  } catch (error: any) {
    console.error('Error listing sandboxes:', error);
    return NextResponse.json(
      { error: 'Failed to list sandboxes', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vibekraft-sandbox/sandboxes
 * Create a new sandbox
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    // Parse and validate request body
    const body = await request.json();
    const validation = createSandboxSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request body', 
          details: validation.error.format() 
        },
        { status: 400 }
      );
    }

    const data = validation.data;

    // Build sandbox configuration
    const sandboxConfig: Partial<SandboxConfig> = {
      name: data.name,
      template: data.template,
      projectId: data.projectId,
      userId: session.user.id!,
      
      resources: {
        vcpuCount: data.resources?.vcpuCount || 2,
        memSizeMib: data.resources?.memSizeMib || 2048,
        diskSizeGb: data.resources?.diskSizeGb || 10,
        diskType: data.resources?.diskType || 'ssd',
        // Set other resource defaults
        cpuShares: 1024,
        maxProcesses: 1024,
        maxOpenFiles: 1024,
        maxNetworkConnections: 100
      },
      
      network: {
        ports: data.network?.ports || [],
        isolated: data.network?.isolated || false,
        allowedHosts: data.network?.allowedHosts,
        blockedHosts: data.network?.blockedHosts
      },
      
      security: {
        readOnlyRootfs: data.security?.readOnlyRootfs || false,
        noNewPrivileges: data.security?.noNewPrivileges || true,
        capabilities: {
          drop: ['ALL'],
          add: []
        },
        runAsUser: 1000,
        runAsGroup: 1000,
        allowSudo: data.security?.allowSudo || false,
        allowNetworkAccess: data.security?.allowNetworkAccess || true,
        allowFileSystemAccess: data.security?.allowFileSystemAccess || true
      },
      
      environment: data.environment,
      
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        templateVersion: '1.0.0',
        templateSource: 'marketplace',
        labels: data.labels,
        annotations: data.annotations,
        totalUptime: 0,
        totalCpuTime: 0,
        totalMemoryUsage: 0,
        totalNetworkTraffic: 0
      }
    };

    // Create sandbox
    const sandboxId = await sandboxManager.createSandbox(sandboxConfig);

    // Get the created sandbox configuration
    const createdSandbox = sandboxManager.getSandboxConfig(sandboxId);

    return NextResponse.json({
      id: sandboxId,
      name: createdSandbox.name,
      template: createdSandbox.template,
      projectId: createdSandbox.projectId,
      state: 'creating',
      resources: {
        vcpuCount: createdSandbox.resources.vcpuCount,
        memSizeMib: createdSandbox.resources.memSizeMib,
        diskSizeGb: createdSandbox.resources.diskSizeGb
      },
      network: {
        ports: createdSandbox.network.ports.length,
        isolated: createdSandbox.network.isolated
      },
      createdAt: createdSandbox.metadata.createdAt,
      message: `Sandbox '${createdSandbox.name}' created successfully`
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error creating sandbox:', error);
    return NextResponse.json(
      { error: 'Failed to create sandbox', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/vibekraft-sandbox/sandboxes
 * Bulk operations on sandboxes
 */
export async function PATCH(request: NextRequest) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure manager is initialized
    await ensureManagerInitialized();

    // Parse request body
    const body = await request.json();
    const { operation, sandboxIds, ...operationData } = body;

    if (!operation || !Array.isArray(sandboxIds) || sandboxIds.length === 0) {
      return NextResponse.json(
        { error: 'Operation and sandboxIds are required' },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    // Perform bulk operation
    for (const sandboxId of sandboxIds) {
      try {
        // Verify ownership
        const sandbox = sandboxManager.getSandboxConfig(sandboxId);
        if (sandbox.userId !== session.user.id) {
          errors.push({ sandboxId, error: 'Access denied' });
          continue;
        }

        switch (operation) {
          case 'start':
            // Start operation would need to be implemented in SandboxManager
            results.push({ sandboxId, status: 'start requested' });
            break;

          case 'stop':
            // Stop operation would need to be implemented in SandboxManager
            results.push({ sandboxId, status: 'stop requested' });
            break;

          case 'restart':
            // Restart operation would need to be implemented in SandboxManager
            results.push({ sandboxId, status: 'restart requested' });
            break;

          case 'destroy':
            await sandboxManager.deleteSandbox(sandboxId);
            results.push({ sandboxId, status: 'destroyed' });
            break;

          default:
            errors.push({ sandboxId, error: `Unknown operation: ${operation}` });
        }
      } catch (error: any) {
        errors.push({ sandboxId, error: error.message });
      }
    }

    return NextResponse.json({
      operation,
      results,
      errors,
      summary: {
        total: sandboxIds.length,
        successful: results.length,
        failed: errors.length
      }
    });

  } catch (error: any) {
    console.error('Error performing bulk operation:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk operation', message: error.message },
      { status: 500 }
    );
  }
}
