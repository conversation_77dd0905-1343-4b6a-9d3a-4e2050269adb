/**
 * VibeKraft Sandbox Templates API
 * 
 * API endpoints for template management and marketplace
 */

import { NextRequest, NextResponse } from 'next/server';
// Mock authentication for demo purposes
const mockAuth = {
  user: { id: 'demo-user', email: '<EMAIL>' }
};
import { SandboxManager } from '@/lib/vibekraft-sandbox/core/sandbox-manager';
import { z } from 'zod';

// Initialize managers
const sandboxManager = new SandboxManager();
const templateManager = new TemplateManager({
  enableMarketplace: true,
  enableCustomTemplates: true
});

// Initialize managers on first use
let managersInitialized = false;
async function ensureManagersInitialized() {
  if (!managersInitialized) {
    await sandboxManager.initialize();
    await templateManager.initialize();
    managersInitialized = true;
  }
}

// Validation schemas
const templateSearchSchema = z.object({
  q: z.string().optional(), // Search query
  category: z.enum([
    'web-development',
    'mobile-development', 
    'data-science',
    'machine-learning',
    'devops',
    'education',
    'prototyping',
    'testing',
    'research',
    'other'
  ]).optional(),
  tags: z.string().optional(), // Comma-separated tags
  author: z.string().optional(),
  language: z.string().optional(),
  framework: z.string().optional(),
  minRating: z.number().min(0).max(5).optional(),
  sortBy: z.enum(['name', 'downloads', 'rating', 'updated', 'created']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});

const createTemplateSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().min(1).max(500),
  version: z.string().regex(/^\d+\.\d+\.\d+$/, 'Version must be in semver format'),
  category: z.enum([
    'web-development',
    'mobile-development',
    'data-science', 
    'machine-learning',
    'devops',
    'education',
    'prototyping',
    'testing',
    'research',
    'other'
  ]),
  tags: z.array(z.string()).default([]),
  
  // Template configuration
  config: z.object({
    baseImage: z.string().min(1),
    runtime: z.object({
      language: z.string().min(1),
      version: z.string().min(1),
      framework: z.string().optional(),
      frameworkVersion: z.string().optional(),
      packageManager: z.enum(['npm', 'yarn', 'pnpm', 'pip', 'poetry', 'cargo', 'go', 'maven', 'gradle']),
      startCommand: z.string().min(1),
      buildCommand: z.string().optional(),
      testCommand: z.string().optional()
    }),
    resources: z.object({
      min: z.object({
        vcpuCount: z.number().min(1).optional(),
        memSizeMib: z.number().min(512).optional(),
        diskSizeGb: z.number().min(1).optional()
      }).optional(),
      recommended: z.object({
        vcpuCount: z.number().min(1).optional(),
        memSizeMib: z.number().min(512).optional(),
        diskSizeGb: z.number().min(1).optional()
      }).optional(),
      max: z.object({
        vcpuCount: z.number().min(1).optional(),
        memSizeMib: z.number().min(512).optional(),
        diskSizeGb: z.number().min(1).optional()
      }).optional()
    }),
    network: z.object({
      ports: z.array(z.object({
        port: z.number().min(1).max(65535),
        protocol: z.enum(['tcp', 'udp']).default('tcp'),
        description: z.string(),
        required: z.boolean().default(false),
        public: z.boolean().default(false)
      })).default([]),
      internetAccess: z.boolean().default(true),
      inboundAccess: z.boolean().default(false)
    }),
    security: z.object({
      privileged: z.boolean().default(false),
      capabilities: z.array(z.string()).default([]),
      readOnlyRootfs: z.boolean().default(false),
      runAsRoot: z.boolean().default(false),
      sudoAccess: z.boolean().default(false)
    }),
    environment: z.object({
      required: z.array(z.object({
        name: z.string(),
        description: z.string(),
        type: z.enum(['string', 'number', 'boolean', 'url', 'email', 'password', 'json']).default('string'),
        required: z.boolean().default(true),
        defaultValue: z.string().optional()
      })).default([]),
      defaults: z.record(z.string()).default({})
    })
  }),
  
  // Files
  files: z.array(z.object({
    path: z.string().min(1),
    content: z.string(),
    type: z.enum(['text', 'binary']).default('text'),
    encoding: z.enum(['utf8', 'base64']).default('utf8'),
    permissions: z.string().optional(),
    template: z.boolean().default(false),
    variables: z.array(z.string()).default([])
  })).default([]),
  
  // Setup
  setup: z.object({
    preSetup: z.array(z.object({
      command: z.string(),
      description: z.string(),
      workingDir: z.string().optional(),
      timeout: z.number().optional(),
      continueOnError: z.boolean().default(false)
    })).default([]),
    postSetup: z.array(z.object({
      command: z.string(),
      description: z.string(),
      workingDir: z.string().optional(),
      timeout: z.number().optional(),
      continueOnError: z.boolean().default(false)
    })).default([])
  }).default({ preSetup: [], postSetup: [] }),
  
  // Customization
  customization: z.object({
    parameters: z.array(z.object({
      name: z.string(),
      displayName: z.string(),
      description: z.string(),
      type: z.enum(['string', 'number', 'boolean', 'select', 'multiselect', 'json']),
      required: z.boolean().default(false),
      defaultValue: z.any().optional(),
      options: z.array(z.object({
        value: z.any(),
        label: z.string(),
        description: z.string().optional()
      })).optional()
    })).default([]),
    features: z.array(z.object({
      id: z.string(),
      name: z.string(),
      description: z.string(),
      enabled: z.boolean().default(false),
      required: z.boolean().default(false),
      dependencies: z.array(z.string()).default([]),
      conflicts: z.array(z.string()).default([])
    })).default([]),
    addons: z.array(z.object({
      id: z.string(),
      name: z.string(),
      description: z.string(),
      version: z.string(),
      type: z.enum(['plugin', 'extension', 'integration', 'tool']),
      optional: z.boolean().default(true)
    })).default([])
  }).default({ parameters: [], features: [], addons: [] })
});

/**
 * GET /api/vibekraft-sandbox/templates
 * List and search templates
 */
export async function GET(request: NextRequest) {
  try {
    // Ensure managers are initialized
    await ensureManagersInitialized();

    // Parse and validate query parameters
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    // Convert tags string to array
    if (queryParams.tags) {
      queryParams.tags = queryParams.tags.split(',').map(tag => tag.trim());
    }
    
    // Convert numeric parameters
    if (queryParams.minRating) queryParams.minRating = parseFloat(queryParams.minRating);
    if (queryParams.limit) queryParams.limit = parseInt(queryParams.limit);
    if (queryParams.offset) queryParams.offset = parseInt(queryParams.offset);

    const validation = templateSearchSchema.safeParse(queryParams);
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters', 
          details: validation.error.format() 
        },
        { status: 400 }
      );
    }

    const searchParams = validation.data;

    // Perform search
    let templates;
    if (searchParams.q) {
      templates = await templateManager.searchTemplates(searchParams.q, {
        category: searchParams.category,
        tags: searchParams.tags,
        author: searchParams.author,
        language: searchParams.language,
        framework: searchParams.framework,
        minRating: searchParams.minRating,
        sortBy: searchParams.sortBy,
        sortOrder: searchParams.sortOrder,
        limit: searchParams.limit,
        offset: searchParams.offset
      });
    } else {
      templates = await templateManager.listTemplates({
        category: searchParams.category,
        tags: searchParams.tags,
        author: searchParams.author,
        language: searchParams.language,
        framework: searchParams.framework,
        minRating: searchParams.minRating,
        sortBy: searchParams.sortBy,
        sortOrder: searchParams.sortOrder,
        limit: searchParams.limit,
        offset: searchParams.offset
      });
    }

    // Format templates for API response
    const formattedTemplates = templates.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      version: template.version,
      author: template.metadata.author,
      category: template.metadata.category,
      tags: template.metadata.tags,
      rating: template.metadata.rating,
      downloads: template.metadata.downloads,
      createdAt: template.metadata.createdAt,
      updatedAt: template.metadata.updatedAt,
      
      // Runtime info
      runtime: {
        language: template.config.runtime.language,
        version: template.config.runtime.version,
        framework: template.config.runtime.framework
      },
      
      // Resource requirements
      resources: {
        recommended: template.config.resources.recommended
      },
      
      // Preview info
      preview: {
        icon: template.metadata.icon,
        screenshots: template.metadata.screenshots.slice(0, 3) // Limit screenshots
      }
    }));

    return NextResponse.json({
      templates: formattedTemplates,
      pagination: {
        limit: searchParams.limit,
        offset: searchParams.offset,
        hasMore: templates.length === searchParams.limit
      },
      filters: {
        category: searchParams.category,
        tags: searchParams.tags,
        language: searchParams.language,
        framework: searchParams.framework,
        minRating: searchParams.minRating
      }
    });

  } catch (error: any) {
    console.error('Error listing templates:', error);
    return NextResponse.json(
      { error: 'Failed to list templates', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vibekraft-sandbox/templates
 * Create a new custom template
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication (mock for demo)
    const session = mockAuth;
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Ensure managers are initialized
    await ensureManagersInitialized();

    // Parse and validate request body
    const body = await request.json();
    const validation = createTemplateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request body', 
          details: validation.error.format() 
        },
        { status: 400 }
      );
    }

    const templateData = validation.data;

    // Build template object
    const template: Omit<SandboxTemplate, 'id' | 'metadata'> = {
      name: templateData.name,
      description: templateData.description,
      version: templateData.version,
      config: templateData.config,
      files: templateData.files,
      setup: templateData.setup,
      customization: templateData.customization,
      metadata: {
        author: session.user.name || session.user.email || 'Unknown',
        authorEmail: session.user.email,
        category: templateData.category,
        tags: templateData.tags,
        createdAt: new Date(),
        updatedAt: new Date(),
        downloads: 0,
        rating: 0,
        reviews: 0,
        minVersion: '1.0.0',
        license: 'MIT',
        screenshots: []
      }
    };

    // Create template
    const templateId = await templateManager.createTemplate(template);

    // Get created template
    const createdTemplate = await templateManager.getTemplate(templateId);

    return NextResponse.json({
      id: templateId,
      name: createdTemplate!.name,
      description: createdTemplate!.description,
      version: createdTemplate!.version,
      category: createdTemplate!.metadata.category,
      author: createdTemplate!.metadata.author,
      createdAt: createdTemplate!.metadata.createdAt,
      message: `Template '${createdTemplate!.name}' created successfully`
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      { error: 'Failed to create template', message: error.message },
      { status: 500 }
    );
  }
}
