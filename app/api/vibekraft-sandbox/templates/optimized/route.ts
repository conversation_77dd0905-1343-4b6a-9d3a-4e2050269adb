/**
 * VibeKraft Optimized Templates API
 * 
 * API endpoints for managing optimized Docker templates
 */

import { NextRequest, NextResponse } from 'next/server';
import { SandboxManager } from '@/lib/vibekraft-sandbox/core/sandbox-manager';

// Production authentication - replace with your auth system
const getAuthenticatedUser = async () => {
  // TODO: Replace with real authentication
  return { id: 'demo-user', email: '<EMAIL>' };
};

// Global sandbox manager instance
let sandboxManager: SandboxManager | null = null;

async function getSandboxManager(): Promise<SandboxManager> {
  if (!sandboxManager) {
    sandboxManager = new SandboxManager({
      enableImageOptimization: true,
      autoOptimizeImages: true,
      maxConcurrentSandboxes: 50,
      defaultResourceLimits: {
        memory: 2 * 1024 * 1024 * 1024, // 2GB
        cpus: 2,
        diskQuota: 10 * 1024 * 1024 * 1024 // 10GB
      }
    });
    await sandboxManager.initialize();
  }
  return sandboxManager;
}

/**
 * GET /api/vibekraft-sandbox/templates/optimized
 * List all available optimized templates
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const manager = await getSandboxManager();
    
    // Get query parameters
    const url = new URL(request.url);
    const language = url.searchParams.get('language');
    const packageManager = url.searchParams.get('packageManager');
    const maxSize = url.searchParams.get('maxSize') as 'minimal' | 'standard' | 'full' | null;
    const includeAnalysis = url.searchParams.get('includeAnalysis') === 'true';

    // Get available templates
    let templates = manager.getAvailableTemplates();

    // Apply filters
    if (language) {
      templates = templates.filter(t => t.languages.includes(language));
    }
    if (packageManager) {
      templates = templates.filter(t => t.packageManagers.includes(packageManager));
    }
    if (maxSize) {
      const sizeOrder = { minimal: 0, standard: 1, full: 2 };
      templates = templates.filter(t => sizeOrder[t.size] <= sizeOrder[maxSize]);
    }

    // Include size analysis if requested
    let analysis = null;
    if (includeAnalysis) {
      try {
        analysis = await manager.getTemplateSizeComparison();
      } catch (error) {
        console.warn('Failed to get template size analysis:', error);
      }
    }

    return NextResponse.json({
      templates: templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        size: template.size,
        languages: template.languages,
        packageManagers: template.packageManagers,
        preInstalled: template.preInstalled,
        optimizedImage: template.optimizedImage,
        baseImage: template.baseImage
      })),
      analysis,
      filters: {
        language,
        packageManager,
        maxSize
      },
      total: templates.length
    });

  } catch (error: any) {
    console.error('Error listing optimized templates:', error);
    return NextResponse.json(
      { error: 'Failed to list templates', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vibekraft-sandbox/templates/optimized/build
 * Build optimized templates
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const manager = await getSandboxManager();
    const body = await request.json();
    const { templateIds, buildAll = false } = body;

    if (!buildAll && (!templateIds || !Array.isArray(templateIds))) {
      return NextResponse.json(
        { error: 'templateIds array is required when buildAll is false' },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    if (buildAll) {
      try {
        console.log('Building all optimized templates...');
        await manager.buildOptimizedTemplates();
        
        const templates = manager.getAvailableTemplates();
        for (const template of templates) {
          results.push({
            templateId: template.id,
            status: 'built',
            image: template.optimizedImage
          });
        }
      } catch (error: any) {
        errors.push({
          templateId: 'all',
          error: error.message
        });
      }
    } else {
      // Build specific templates
      for (const templateId of templateIds) {
        try {
          const templates = manager.getAvailableTemplates();
          const template = templates.find(t => t.id === templateId);
          
          if (!template) {
            errors.push({
              templateId,
              error: 'Template not found'
            });
            continue;
          }

          console.log(`Building template: ${template.name}`);
          // Note: Individual template building would need to be implemented
          // For now, we'll indicate it's queued
          results.push({
            templateId,
            status: 'queued',
            image: template.optimizedImage
          });
          
        } catch (error: any) {
          errors.push({
            templateId,
            error: error.message
          });
        }
      }
    }

    return NextResponse.json({
      operation: buildAll ? 'build-all' : 'build-selected',
      results,
      errors,
      summary: {
        total: buildAll ? manager.getAvailableTemplates().length : templateIds.length,
        successful: results.length,
        failed: errors.length
      }
    });

  } catch (error: any) {
    console.error('Error building templates:', error);
    return NextResponse.json(
      { error: 'Failed to build templates', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET /api/vibekraft-sandbox/templates/optimized/analysis
 * Get template size analysis and comparison
 */
export async function GET_ANALYSIS(request: NextRequest) {
  try {
    // Check authentication
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const manager = await getSandboxManager();
    
    // Get template size comparison
    const analysis = await manager.getTemplateSizeComparison();
    
    // Calculate statistics
    const builtTemplates = analysis.filter(t => typeof t.optimizedSize === 'number');
    const totalSize = builtTemplates.reduce((sum, t) => sum + (t.optimizedSize as number), 0);
    const averageSize = builtTemplates.length > 0 ? totalSize / builtTemplates.length : 0;
    const minSize = builtTemplates.length > 0 ? Math.min(...builtTemplates.map(t => t.optimizedSize as number)) : 0;
    const maxSize = builtTemplates.length > 0 ? Math.max(...builtTemplates.map(t => t.optimizedSize as number)) : 0;

    return NextResponse.json({
      templates: analysis,
      statistics: {
        totalTemplates: analysis.length,
        builtTemplates: builtTemplates.length,
        pendingTemplates: analysis.length - builtTemplates.length,
        totalSizeMB: Math.round(totalSize),
        averageSizeMB: Math.round(averageSize),
        minSizeMB: minSize,
        maxSizeMB: maxSize
      },
      recommendations: {
        mostOptimized: builtTemplates.length > 0 ? builtTemplates.reduce((min, t) => 
          (t.optimizedSize as number) < (min.optimizedSize as number) ? t : min
        ) : null,
        mostVersatile: analysis.find(t => t.languages.length > 2) || null
      }
    });

  } catch (error: any) {
    console.error('Error getting template analysis:', error);
    return NextResponse.json(
      { error: 'Failed to get template analysis', message: error.message },
      { status: 500 }
    );
  }
}
