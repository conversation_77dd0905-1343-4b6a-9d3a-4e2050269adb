import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { ProxmoxClient } from '@/lib/proxmox/api/proxmox-client';

// Initialize Proxmox client with environment variables
const getProxmoxClient = async () => {
  return new ProxmoxClient({
    apiUrl: process.env.PROXMOX_API_URL || '',
    credentials: {
      username: process.env.PROXMOX_USERNAME || '',
      password: process.env.PROXMOX_PASSWORD,
      token: process.env.PROXMOX_TOKEN,
      tokenId: process.env.PROXMOX_TOKEN_ID,
      realm: process.env.PROXMOX_REALM || 'pam',
    },
    verifySSL: process.env.PROXMOX_VERIFY_SSL !== 'false',
  });
};

// GET handler for fetching VM information
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const node = searchParams.get('node');
    const vmid = searchParams.get('vmid');

    if (!node) {
      return NextResponse.json({ error: 'Node parameter is required' }, { status: 400 });
    }

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    let data;
    
    if (vmid) {
      // Get specific VM
      data = await client.vm.getVM(node, vmid);
    } else {
      // List all VMs on the node
      data = await client.vm.listVMs(node);
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Proxmox VM API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching VM data from Proxmox' },
      { status: 500 }
    );
  }
}

// POST handler for VM creation
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { node, vmid, name, ...params } = body;

    if (!node || !vmid || !name) {
      return NextResponse.json({ error: 'Node, VMID, and name are required' }, { status: 400 });
    }

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    // Create VM
    const result = await client.vm.createVM({
      node,
      vmid: parseInt(vmid),
      name,
      ...params,
    });

    return NextResponse.json({ data: result });
  } catch (error: any) {
    console.error('Proxmox VM creation error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while creating the VM' },
      { status: 500 }
    );
  }
}

// PUT handler for VM operations (start, stop, restart)
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { action, node, vmid } = body;

    if (!action || !node || !vmid) {
      return NextResponse.json({ error: 'Action, node, and VMID are required' }, { status: 400 });
    }

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    let result;

    // Perform action
    switch (action) {
      case 'start':
        await client.vm.startVM(node, vmid);
        result = { success: true, action: 'start' };
        break;
      case 'stop':
        await client.vm.stopVM(node, vmid);
        result = { success: true, action: 'stop' };
        break;
      case 'restart':
        await client.vm.restartVM(node, vmid);
        result = { success: true, action: 'restart' };
        break;
      case 'update':
        const { config } = body;
        if (!config) {
          return NextResponse.json({ error: 'Config is required for update action' }, { status: 400 });
        }
        const updatedVM = await client.vm.updateVM(node, vmid, config);
        result = { success: true, action: 'update', vm: updatedVM };
        break;
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    return NextResponse.json({ data: result });
  } catch (error: any) {
    console.error('Proxmox VM operation error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while performing the VM operation' },
      { status: 500 }
    );
  }
}

// DELETE handler for VM deletion
export async function DELETE(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const node = searchParams.get('node');
    const vmid = searchParams.get('vmid');

    if (!node || !vmid) {
      return NextResponse.json({ error: 'Node and VMID parameters are required' }, { status: 400 });
    }

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    // Delete VM
    await client.vm.deleteVM(node, vmid);

    return NextResponse.json({ data: { success: true } });
  } catch (error: any) {
    console.error('Proxmox VM deletion error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while deleting the VM' },
      { status: 500 }
    );
  }
}
