import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { ProxmoxClient } from '@/lib/proxmox/api/proxmox-client';

// Initialize Proxmox client with environment variables
const getProxmoxClient = async () => {
  return new ProxmoxClient({
    apiUrl: process.env.PROXMOX_API_URL || '',
    credentials: {
      username: process.env.PROXMOX_USERNAME || '',
      password: process.env.PROXMOX_PASSWORD,
      token: process.env.PROXMOX_TOKEN,
      tokenId: process.env.PROXMOX_TOKEN_ID,
      realm: process.env.PROXMOX_REALM || 'pam',
    },
    verifySSL: process.env.PROXMOX_VERIFY_SSL !== 'false',
  });
};

// GET handler for fetching nodes
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const resource = searchParams.get('resource');
    const node = searchParams.get('node');
    const vmid = searchParams.get('vmid');

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    let data;
    
    // Handle different resource types
    switch (resource) {
      case 'nodes':
        data = await client.cluster.listNodes();
        break;
      case 'vms':
        if (!node) {
          return NextResponse.json({ error: 'Node parameter is required' }, { status: 400 });
        }
        data = await client.vm.listVMs(node);
        break;
      case 'vm':
        if (!node || !vmid) {
          return NextResponse.json({ error: 'Node and vmid parameters are required' }, { status: 400 });
        }
        data = await client.vm.getVM(node, vmid);
        break;
      case 'storage':
        data = await client.storage.listStorage();
        break;
      case 'volumes':
        if (!node || !searchParams.get('storage')) {
          return NextResponse.json({ error: 'Node and storage parameters are required' }, { status: 400 });
        }
        data = await client.storage.listVolumes(node, searchParams.get('storage')!);
        break;
      case 'network':
        if (!node) {
          return NextResponse.json({ error: 'Node parameter is required' }, { status: 400 });
        }
        data = await client.network.listInterfaces(node);
        break;
      case 'tasks':
        if (!node) {
          return NextResponse.json({ error: 'Node parameter is required' }, { status: 400 });
        }
        data = await client.task.listTasks(node);
        break;
      case 'cluster-status':
        data = await client.cluster.getClusterStatus();
        break;
      case 'cluster-resources':
        data = await client.cluster.getClusterResources();
        break;
      default:
        return NextResponse.json({ error: 'Invalid resource type' }, { status: 400 });
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Proxmox API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching data from Proxmox' },
      { status: 500 }
    );
  }
}

// POST handler for VM operations
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { action, node, vmid, ...params } = body;

    if (!action || !node) {
      return NextResponse.json({ error: 'Action and node are required' }, { status: 400 });
    }

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    let result;

    // Handle different actions
    switch (action) {
      case 'createVM':
        result = await client.vm.createVM({
          node,
          vmid: parseInt(vmid),
          ...params,
        });
        break;
      case 'startVM':
        if (!vmid) {
          return NextResponse.json({ error: 'VM ID is required' }, { status: 400 });
        }
        await client.vm.startVM(node, vmid);
        result = { success: true };
        break;
      case 'stopVM':
        if (!vmid) {
          return NextResponse.json({ error: 'VM ID is required' }, { status: 400 });
        }
        await client.vm.stopVM(node, vmid);
        result = { success: true };
        break;
      case 'restartVM':
        if (!vmid) {
          return NextResponse.json({ error: 'VM ID is required' }, { status: 400 });
        }
        await client.vm.restartVM(node, vmid);
        result = { success: true };
        break;
      case 'deleteVM':
        if (!vmid) {
          return NextResponse.json({ error: 'VM ID is required' }, { status: 400 });
        }
        await client.vm.deleteVM(node, vmid);
        result = { success: true };
        break;
      case 'createSnapshot':
        if (!vmid) {
          return NextResponse.json({ error: 'VM ID is required' }, { status: 400 });
        }
        result = await client.backup.createSnapshot(node, vmid, params);
        break;
      case 'createBackup':
        if (!vmid) {
          return NextResponse.json({ error: 'VM ID is required' }, { status: 400 });
        }
        result = await client.backup.createBackup(node, vmid, params);
        break;
      case 'createTemplate':
        result = await client.vm.createTemplate({
          node,
          vmid: parseInt(vmid),
          ...params,
        });
        break;
      case 'cloneVM':
        if (!vmid || !params.newVmId || !params.name) {
          return NextResponse.json({ error: 'VM ID, new VM ID, and name are required' }, { status: 400 });
        }
        result = await client.vm.cloneFromTemplate(
          node,
          vmid,
          parseInt(params.newVmId),
          params.name,
          {
            description: params.description,
            start: params.start,
            full: params.full,
          }
        );
        break;
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    return NextResponse.json({ data: result });
  } catch (error: any) {
    console.error('Proxmox API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while performing the operation' },
      { status: 500 }
    );
  }
}
