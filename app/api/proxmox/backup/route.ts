import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { ProxmoxClient } from '@/lib/proxmox/api/proxmox-client';

// Initialize Proxmox client with environment variables
const getProxmoxClient = async () => {
  return new ProxmoxClient({
    apiUrl: process.env.PROXMOX_API_URL || '',
    credentials: {
      username: process.env.PROXMOX_USERNAME || '',
      password: process.env.PROXMOX_PASSWORD,
      token: process.env.PROXMOX_TOKEN,
      tokenId: process.env.PROXMOX_TOKEN_ID,
      realm: process.env.PROXMOX_REALM || 'pam',
    },
    verifySSL: process.env.PROXMOX_VERIFY_SSL !== 'false',
  });
};

// GET handler for fetching backups and snapshots
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const node = searchParams.get('node');
    const vmid = searchParams.get('vmid');
    const type = searchParams.get('type') || 'snapshots'; // 'snapshots' or 'backups'

    if (!node || !vmid) {
      return NextResponse.json({ error: 'Node and VMID parameters are required' }, { status: 400 });
    }

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    let data;
    
    if (type === 'backups') {
      // List backups
      data = await client.backup.listBackups(node, vmid);
    } else {
      // List snapshots
      data = await client.backup.listSnapshots(node, vmid);
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Proxmox backup API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching backup data from Proxmox' },
      { status: 500 }
    );
  }
}

// POST handler for creating backups and snapshots
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { type, node, vmid, ...params } = body;

    if (!type || !node || !vmid) {
      return NextResponse.json({ error: 'Type, node, and VMID are required' }, { status: 400 });
    }

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    let result;

    if (type === 'backup') {
      // Create backup
      if (!params.storage) {
        return NextResponse.json({ error: 'Storage is required for backup' }, { status: 400 });
      }
      
      result = await client.backup.createBackup(node, vmid, params);
    } else if (type === 'snapshot') {
      // Create snapshot
      if (!params.name) {
        return NextResponse.json({ error: 'Name is required for snapshot' }, { status: 400 });
      }
      
      result = await client.backup.createSnapshot(node, vmid, params);
    } else {
      return NextResponse.json({ error: 'Invalid type' }, { status: 400 });
    }

    return NextResponse.json({ data: result });
  } catch (error: any) {
    console.error('Proxmox backup creation error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while creating the backup or snapshot' },
      { status: 500 }
    );
  }
}

// PUT handler for rollback to snapshot
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { action, node, vmid, snapshot } = body;

    if (action !== 'rollback' || !node || !vmid || !snapshot) {
      return NextResponse.json({ error: 'Action, node, VMID, and snapshot are required' }, { status: 400 });
    }

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    // Rollback to snapshot
    await client.backup.rollbackSnapshot(node, vmid, snapshot);

    return NextResponse.json({ data: { success: true } });
  } catch (error: any) {
    console.error('Proxmox snapshot rollback error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while rolling back to the snapshot' },
      { status: 500 }
    );
  }
}

// DELETE handler for deleting backups and snapshots
export async function DELETE(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const node = searchParams.get('node');
    const vmid = searchParams.get('vmid');
    const type = searchParams.get('type'); // 'snapshot' or 'backup'
    const id = searchParams.get('id'); // snapshot name or backup ID

    if (!node || !vmid || !type || !id) {
      return NextResponse.json({ error: 'Node, VMID, type, and ID parameters are required' }, { status: 400 });
    }

    // Initialize Proxmox client
    const client = await getProxmoxClient();
    await client.initialize();

    if (type === 'backup') {
      // Delete backup
      await client.backup.deleteBackup(node, vmid, id);
    } else if (type === 'snapshot') {
      // Delete snapshot
      await client.backup.deleteSnapshot(node, vmid, id);
    } else {
      return NextResponse.json({ error: 'Invalid type' }, { status: 400 });
    }

    return NextResponse.json({ data: { success: true } });
  } catch (error: any) {
    console.error('Proxmox backup deletion error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred while deleting the backup or snapshot' },
      { status: 500 }
    );
  }
}
