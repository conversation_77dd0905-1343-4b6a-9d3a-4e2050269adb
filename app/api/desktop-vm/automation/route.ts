/**
 * Desktop VM Automation API Routes
 * 
 * API endpoints for AI-powered desktop automation including
 * prompt execution, action automation, and session management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/utils/logger';
import { DesktopAutomationAgent } from '@/lib/ai-desktop-automation/desktop-automation-agent';
import { 
  ExecuteAutomationRequest, 
  ExecuteAutomationResponse,
  StartAutomationSessionRequest,
  StartAutomationSessionResponse,
  StopAutomationSessionRequest,
  StopAutomationSessionResponse,
  GetSessionHistoryRequest,
  GetSessionHistoryResponse
} from '@/lib/ai-desktop-automation/types';

// Global automation agent instance
let automationAgent: DesktopAutomationAgent;

// Initialize automation agent
function getAutomationAgent(): DesktopAutomationAgent {
  if (!automationAgent) {
    automationAgent = new DesktopAutomationAgent({
      model: process.env.AI_MODEL || 'gpt-4o',
      temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.AI_MAX_TOKENS || '4000'),
      enableScreenshots: process.env.ENABLE_SCREENSHOTS !== 'false',
      screenshotInterval: parseInt(process.env.SCREENSHOT_INTERVAL || '5000'),
      maxRetries: parseInt(process.env.MAX_RETRIES || '3'),
      retryDelay: parseInt(process.env.RETRY_DELAY || '1000'),
    });
  }
  return automationAgent;
}

/**
 * GET handler for automation operations
 * - Get session history: GET /api/desktop-vm/automation?vmId={vmId}&sessionId={sessionId}
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const vmId = searchParams.get('vmId');
    const sessionId = searchParams.get('sessionId');
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    if (!vmId) {
      return NextResponse.json(
        { error: 'VM ID is required' },
        { status: 400 }
      );
    }

    const agent = getAutomationAgent();
    
    // Get session history
    const requestData: GetSessionHistoryRequest = {
      vmId,
      sessionId: sessionId || undefined,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
    };

    const actions = await agent.getSessionHistory(requestData);
    
    const response: GetSessionHistoryResponse = {
      actions,
      total: actions.length
    };

    return NextResponse.json(response);
  } catch (error: any) {
    logger.error(`Error in automation GET handler: ${error.message}`);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST handler for automation operations
 * - Execute automation prompt: POST /api/desktop-vm/automation/execute
 * - Start automation session: POST /api/desktop-vm/automation/start
 * - Stop automation session: POST /api/desktop-vm/automation/stop
 */
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    const agent = getAutomationAgent();

    switch (action) {
      case 'execute': {
        const body: ExecuteAutomationRequest = await request.json();
        
        // Validate request
        if (!body.vmId) {
          return NextResponse.json(
            { error: 'VM ID is required' },
            { status: 400 }
          );
        }

        if (!body.prompt?.trim()) {
          return NextResponse.json(
            { error: 'Automation prompt is required' },
            { status: 400 }
          );
        }

        // Execute automation prompt
        const result = await agent.executeAutomationPrompt(body);
        
        logger.info(`Automation executed for VM ${body.vmId}: ${result.actions.length} actions`);
        
        const response: ExecuteAutomationResponse = result;
        return NextResponse.json(response);
      }

      case 'start': {
        const body: StartAutomationSessionRequest = await request.json();
        
        // Validate request
        if (!body.vmId) {
          return NextResponse.json(
            { error: 'VM ID is required' },
            { status: 400 }
          );
        }

        // Start automation session
        const session = await agent.startAutomationSession(body);
        
        logger.info(`Automation session started for VM ${body.vmId}: ${session.id}`);
        
        const response: StartAutomationSessionResponse = { session };
        return NextResponse.json(response);
      }

      case 'stop': {
        const body: StopAutomationSessionRequest = await request.json();
        
        // Validate request
        if (!body.vmId || !body.sessionId) {
          return NextResponse.json(
            { error: 'VM ID and session ID are required' },
            { status: 400 }
          );
        }

        // Stop automation session
        const session = await agent.stopAutomationSession(body);
        
        logger.info(`Automation session stopped for VM ${body.vmId}: ${body.sessionId}`);
        
        const response: StopAutomationSessionResponse = { session };
        return NextResponse.json(response);
      }

      default: {
        // Default to execute automation prompt
        const body: ExecuteAutomationRequest = await request.json();
        
        // Validate request
        if (!body.vmId) {
          return NextResponse.json(
            { error: 'VM ID is required' },
            { status: 400 }
          );
        }

        if (!body.prompt?.trim()) {
          return NextResponse.json(
            { error: 'Automation prompt is required' },
            { status: 400 }
          );
        }

        // Execute automation prompt
        const result = await agent.executeAutomationPrompt(body);
        
        logger.info(`Automation executed for VM ${body.vmId}: ${result.actions.length} actions`);
        
        const response: ExecuteAutomationResponse = result;
        return NextResponse.json(response);
      }
    }
  } catch (error: any) {
    logger.error(`Error in automation POST handler: ${error.message}`);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
