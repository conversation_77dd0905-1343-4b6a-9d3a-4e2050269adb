/**
 * Real Docker Desktop VM Stop API Route
 *
 * Direct Docker API integration to stop Desktop VM containers
 */

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface StopRequest {
  containerId: string;
  projectId: string;
}

interface StopResponse {
  success: boolean;
  message: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: StopRequest = await request.json();
    const { containerId, projectId } = body;

    // Validate input
    if (!containerId) {
      return NextResponse.json(
        { error: 'Missing required field: containerId' },
        { status: 400 }
      );
    }

    console.log(`Stopping Desktop VM container: ${containerId}`);

    // Check if container exists and is running
    try {
      const { stdout } = await execAsync(`docker inspect ${containerId} --format='{{.State.Status}}'`);
      const status = stdout.trim();

      if (status === 'exited') {
        return NextResponse.json({
          success: true,
          message: 'Container is already stopped'
        });
      }

      if (status !== 'running') {
        return NextResponse.json(
          { error: `Container is in unexpected state: ${status}` },
          { status: 400 }
        );
      }
    } catch (error) {
      return NextResponse.json(
        { error: `Container ${containerId} not found` },
        { status: 404 }
      );
    }

    // Stop the container gracefully
    try {
      await execAsync(`docker stop ${containerId}`);
      console.log(`Container ${containerId} stopped successfully`);
    } catch (error) {
      console.error(`Error stopping container ${containerId}:`, error);
      throw new Error(`Failed to stop container: ${error}`);
    }

    // Remove the container to clean up
    try {
      await execAsync(`docker rm ${containerId}`);
      console.log(`Container ${containerId} removed successfully`);
    } catch (error) {
      console.warn(`Warning: Failed to remove container ${containerId}:`, error);
      // Don't fail the request if removal fails
    }

    const response: StopResponse = {
      success: true,
      message: `Desktop VM container ${containerId} stopped successfully`
    };

    console.log('Desktop VM stopped successfully:', response);

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error stopping Desktop VM:', error);

    return NextResponse.json(
      {
        error: 'Failed to stop Desktop VM',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
