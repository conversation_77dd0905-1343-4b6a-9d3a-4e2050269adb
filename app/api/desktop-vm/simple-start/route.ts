/**
 * Simple Desktop VM Start API Route
 * 
 * Lightweight approach - just start the Desktop VM container with VNC
 * No Guacamole dependency - works immediately
 */

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface SimpleStartRequest {
  projectId: string;
}

interface SimpleStartResponse {
  containerId: string;
  vncPort: number;
  vncUrl: string;
  directAccess: {
    vnc: string;
    web: string;
  };
  resources: {
    cpu: string;
    memory: string;
    network: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: SimpleStartRequest = await request.json();
    const { projectId } = body;

    // Validate input
    if (!projectId) {
      return NextResponse.json(
        { error: 'Missing required field: projectId' },
        { status: 400 }
      );
    }

    // Generate unique container name
    const containerName = `desktop-vm-simple-${projectId}-${Date.now()}`;

    // Find available VNC port
    const vncPort = await findAvailablePort(5901);

    // Start the simple Desktop VM container
    const dockerCommand = [
      'docker run -d',
      `--name ${containerName}`,
      `--hostname desktop-vm-${projectId}`,
      `--restart unless-stopped`,
      `-p ${vncPort}:5901`,
      '--memory=256m',
      '--cpus=0.5',
      '--shm-size=128m',
      '--security-opt no-new-privileges:true',
      '--cap-drop ALL',
      '--cap-add SYS_ADMIN',
      '--pids-limit 100',
      '-e DISPLAY=:1',
      '-e RESOLUTION=1280x720',
      '-e VNC_PORT=5901',
      '-e USER=desktop',
      'desktop-vm-minimal:latest'
    ].join(' ');

    console.log('Starting simple Desktop VM container:', dockerCommand);

    const { stdout: containerId } = await execAsync(dockerCommand);
    const cleanContainerId = containerId.trim();

    // Wait for container to be ready
    await waitForContainer(cleanContainerId);

    // Get initial resource usage
    const resources = await getContainerResources(cleanContainerId);

    const response: SimpleStartResponse = {
      containerId: cleanContainerId,
      vncPort,
      vncUrl: `vnc://localhost:${vncPort}`,
      directAccess: {
        vnc: `vnc://localhost:${vncPort}`,
        web: `http://localhost:3000/desktop-vm/viewer?port=${vncPort}`
      },
      resources
    };

    console.log('Simple Desktop VM started successfully:', response);

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error starting simple Desktop VM:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to start simple Desktop VM',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to find available port
async function findAvailablePort(preferredPort: number): Promise<number> {
  for (let port = preferredPort; port < preferredPort + 100; port++) {
    try {
      await execAsync(`netstat -ln | grep :${port}`);
      // Port is in use, try next one
    } catch (error) {
      // Port is available
      return port;
    }
  }
  throw new Error(`No available ports found starting from ${preferredPort}`);
}

// Helper function to wait for container to be ready
async function waitForContainer(containerId: string, maxWaitTime = 30000): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    try {
      // Check if container is running
      const { stdout } = await execAsync(`docker inspect ${containerId} --format='{{.State.Status}}'`);
      
      if (stdout.trim() === 'running') {
        // Check if VNC is ready
        try {
          await execAsync(`docker exec ${containerId} pgrep Xvfb`);
          console.log('Simple Desktop VM is ready');
          return;
        } catch (error) {
          // VNC not ready yet, wait a bit more
        }
      }
    } catch (error) {
      // Container not ready yet
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error('Container failed to start within timeout period');
}

// Helper function to get container resource usage
async function getContainerResources(containerId: string): Promise<{
  cpu: string;
  memory: string;
  network: string;
}> {
  try {
    const { stdout } = await execAsync(
      `docker stats ${containerId} --no-stream --format "{{.CPUPerc}},{{.MemUsage}},{{.NetIO}}"`
    );
    
    const [cpu, memUsage, netIO] = stdout.trim().split(',');
    const memory = memUsage.split(' / ')[0]; // Get used memory only
    
    return {
      cpu: cpu || '0.00%',
      memory: memory || '0B',
      network: netIO || '0B / 0B'
    };
  } catch (error) {
    console.error('Error getting container resources:', error);
    return {
      cpu: '0.00%',
      memory: '0B',
      network: '0B / 0B'
    };
  }
}
