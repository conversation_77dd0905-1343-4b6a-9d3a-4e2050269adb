/**
 * Real Docker Desktop VM Start API Route
 *
 * Direct Docker API integration to start ultra-lightweight Desktop VM containers
 */

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface StartRequest {
  projectId: string;
  image?: string;
  ports?: {
    vnc: number;
    websocket: number;
  };
}

interface StartResponse {
  containerId: string;
  ports: {
    vnc: number;
    websocket: number;
  };
  websocketUrl: string;
  resources: {
    cpu: string;
    memory: string;
    network: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: StartRequest = await request.json();
    const { projectId, image = 'desktop-vm-minimal:latest', ports = { vnc: 5901, websocket: 6080 } } = body;

    // Validate input
    if (!projectId) {
      return NextResponse.json(
        { error: 'Missing required field: projectId' },
        { status: 400 }
      );
    }

    // Generate unique container name
    const containerName = `desktop-vm-${projectId}-${Date.now()}`;

    // Check if image exists
    try {
      await execAsync(`docker image inspect ${image}`);
    } catch (error) {
      return NextResponse.json(
        { error: `Image ${image} not found. Please build the image first using: docker build -f docker/desktop-vm/Dockerfile.minimal -t desktop-vm-minimal:latest docker/desktop-vm/` },
        { status: 404 }
      );
    }

    // Find available ports
    const vncPort = await findAvailablePort(ports.vnc);
    const websocketPort = await findAvailablePort(ports.websocket);

    // Start the container with optimized settings
    const dockerCommand = [
      'docker run -d',
      `--name ${containerName}`,
      `--hostname desktop-vm-${projectId}`,
      `--restart unless-stopped`,
      `-p ${vncPort}:5901`,
      `-p ${websocketPort}:6080`,
      '--memory=256m',
      '--cpus=0.5',
      '--shm-size=128m',
      '--tmpfs /tmp:rw,noexec,nosuid,size=100m',
      '--tmpfs /var/tmp:rw,noexec,nosuid,size=50m',
      '--security-opt no-new-privileges:true',
      '--cap-drop ALL',
      '--cap-add SYS_ADMIN',
      '--pids-limit 100',
      '-e DISPLAY=:1',
      '-e RESOLUTION=1280x720',
      '-e VNC_PORT=5901',
      '-e WEBSOCKET_PORT=6080',
      '-e USER=desktop',
      image
    ].join(' ');

    console.log('Starting Desktop VM container:', dockerCommand);

    const { stdout: containerId } = await execAsync(dockerCommand);
    const cleanContainerId = containerId.trim();

    // Wait for container to be ready
    await waitForContainer(cleanContainerId);

    // Get initial resource usage
    const resources = await getContainerResources(cleanContainerId);

    const response: StartResponse = {
      containerId: cleanContainerId,
      ports: {
        vnc: vncPort,
        websocket: websocketPort
      },
      websocketUrl: `ws://localhost:${websocketPort}`,
      resources
    };

    console.log('Desktop VM started successfully:', response);

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error starting Desktop VM:', error);

    return NextResponse.json(
      {
        error: 'Failed to start Desktop VM',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to find available port
async function findAvailablePort(preferredPort: number): Promise<number> {
  for (let port = preferredPort; port < preferredPort + 100; port++) {
    try {
      await execAsync(`netstat -ln | grep :${port}`);
      // Port is in use, try next one
    } catch (error) {
      // Port is available
      return port;
    }
  }
  throw new Error(`No available ports found starting from ${preferredPort}`);
}

// Helper function to wait for container to be ready
async function waitForContainer(containerId: string, maxWaitTime = 60000): Promise<void> {
  const startTime = Date.now();

  console.log('Waiting for container services to start...');

  while (Date.now() - startTime < maxWaitTime) {
    try {
      // Check if container is running
      const { stdout } = await execAsync(`docker inspect ${containerId} --format='{{.State.Status}}'`);

      if (stdout.trim() === 'running') {
        // Check if Xvfb is running
        try {
          await execAsync(`docker exec ${containerId} pgrep Xvfb`);
          console.log('✓ Xvfb is running');
        } catch (error) {
          console.log('⏳ Waiting for Xvfb...');
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }

        // Check if x11vnc is running
        try {
          await execAsync(`docker exec ${containerId} pgrep x11vnc`);
          console.log('✓ x11vnc is running');
        } catch (error) {
          console.log('⏳ Waiting for x11vnc...');
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }

        // Check if websockify is running
        try {
          await execAsync(`docker exec ${containerId} pgrep websockify`);
          console.log('✓ websockify is running');
        } catch (error) {
          console.log('⏳ Waiting for websockify...');
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }

        // Test WebSocket connection
        try {
          await execAsync(`docker exec ${containerId} nc -z localhost 6080`);
          console.log('✓ WebSocket port is accessible');
          console.log('Desktop VM is fully ready!');
          return;
        } catch (error) {
          console.log('⏳ Waiting for WebSocket port...');
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }
      }
    } catch (error) {
      console.log('⏳ Container not ready yet...');
    }

    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // If we get here, something failed - let's get diagnostic info
  try {
    const { stdout: logs } = await execAsync(`docker logs ${containerId} --tail 20`);
    console.error('Container logs:', logs);

    const { stdout: processes } = await execAsync(`docker exec ${containerId} ps aux`);
    console.error('Container processes:', processes);
  } catch (error) {
    console.error('Failed to get diagnostic info:', error);
  }

  throw new Error('Container failed to start all services within timeout period');
}

// Helper function to get container resource usage
async function getContainerResources(containerId: string): Promise<{
  cpu: string;
  memory: string;
  network: string;
}> {
  try {
    const { stdout } = await execAsync(
      `docker stats ${containerId} --no-stream --format "{{.CPUPerc}},{{.MemUsage}},{{.NetIO}}"`
    );

    const [cpu, memUsage, netIO] = stdout.trim().split(',');
    const memory = memUsage.split(' / ')[0]; // Get used memory only

    return {
      cpu: cpu || '0.00%',
      memory: memory || '0B',
      network: netIO || '0B / 0B'
    };
  } catch (error) {
    console.error('Error getting container resources:', error);
    return {
      cpu: '0.00%',
      memory: '0B',
      network: '0B / 0B'
    };
  }
}
