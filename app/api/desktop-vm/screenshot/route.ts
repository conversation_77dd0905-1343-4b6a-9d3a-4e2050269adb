/**
 * Desktop VM Screenshot API Route
 * 
 * API endpoint for capturing screenshots from desktop virtual machines
 * for AI automation and monitoring purposes.
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/utils/logger';
import { ScreenshotService } from '@/lib/ai-desktop-automation/screenshot-service';
import { 
  TakeScreenshotRequest, 
  TakeScreenshotResponse 
} from '@/lib/ai-desktop-automation/types';

// Global screenshot service instance
let screenshotService: ScreenshotService;

// Initialize screenshot service
function getScreenshotService(): ScreenshotService {
  if (!screenshotService) {
    screenshotService = new ScreenshotService({
      defaultFormat: (process.env.SCREENSHOT_FORMAT as 'png' | 'jpeg') || 'png',
      defaultQuality: parseInt(process.env.SCREENSHOT_QUALITY || '90'),
      maxWidth: parseInt(process.env.SCREENSHOT_MAX_WIDTH || '1920'),
      maxHeight: parseInt(process.env.SCREENSHOT_MAX_HEIGHT || '1080'),
      enableCompression: process.env.SCREENSHOT_ENABLE_COMPRESSION !== 'false',
      storageDir: process.env.SCREENSHOT_STORAGE_DIR || '/tmp/screenshots',
      retentionDays: parseInt(process.env.SCREENSHOT_RETENTION_DAYS || '7'),
    });
  }
  return screenshotService;
}

/**
 * POST handler for taking screenshots
 * - Take screenshot: POST /api/desktop-vm/screenshot
 */
export async function POST(request: NextRequest) {
  try {
    const body: TakeScreenshotRequest = await request.json();
    
    // Validate request
    if (!body.vmId) {
      return NextResponse.json(
        { error: 'VM ID is required' },
        { status: 400 }
      );
    }

    // Validate format if provided
    if (body.format && !['png', 'jpeg'].includes(body.format)) {
      return NextResponse.json(
        { error: 'Invalid format. Must be "png" or "jpeg"' },
        { status: 400 }
      );
    }

    // Validate quality if provided
    if (body.quality && (body.quality < 1 || body.quality > 100)) {
      return NextResponse.json(
        { error: 'Quality must be between 1 and 100' },
        { status: 400 }
      );
    }

    const service = getScreenshotService();
    
    // Take screenshot
    const screenshot = await service.takeScreenshot(body);
    
    logger.info(`Screenshot captured for VM ${body.vmId}: ${screenshot.id}`);
    
    const response: TakeScreenshotResponse = { screenshot };
    return NextResponse.json(response);
  } catch (error: any) {
    logger.error(`Error taking screenshot: ${error.message}`);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET handler for retrieving screenshots
 * - Get screenshot: GET /api/desktop-vm/screenshot?vmId={vmId}&screenshotId={screenshotId}
 * - List screenshots: GET /api/desktop-vm/screenshot?vmId={vmId}
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const vmId = searchParams.get('vmId');
    const screenshotId = searchParams.get('screenshotId');
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    if (!vmId) {
      return NextResponse.json(
        { error: 'VM ID is required' },
        { status: 400 }
      );
    }

    const service = getScreenshotService();

    if (screenshotId) {
      // Get specific screenshot
      const screenshot = await service.getScreenshot(vmId, screenshotId);
      
      if (!screenshot) {
        return NextResponse.json(
          { error: `Screenshot ${screenshotId} not found for VM ${vmId}` },
          { status: 404 }
        );
      }

      return NextResponse.json({ screenshot });
    } else {
      // List screenshots for VM
      const screenshots = await service.listScreenshots(vmId, {
        limit: limit ? parseInt(limit) : undefined,
        offset: offset ? parseInt(offset) : undefined,
      });

      return NextResponse.json({ 
        screenshots,
        total: screenshots.length
      });
    }
  } catch (error: any) {
    logger.error(`Error retrieving screenshots: ${error.message}`);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting screenshots
 * - Delete screenshot: DELETE /api/desktop-vm/screenshot?vmId={vmId}&screenshotId={screenshotId}
 * - Delete all screenshots for VM: DELETE /api/desktop-vm/screenshot?vmId={vmId}
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const vmId = searchParams.get('vmId');
    const screenshotId = searchParams.get('screenshotId');

    if (!vmId) {
      return NextResponse.json(
        { error: 'VM ID is required' },
        { status: 400 }
      );
    }

    const service = getScreenshotService();

    if (screenshotId) {
      // Delete specific screenshot
      const deleted = await service.deleteScreenshot(vmId, screenshotId);
      
      if (!deleted) {
        return NextResponse.json(
          { error: `Screenshot ${screenshotId} not found for VM ${vmId}` },
          { status: 404 }
        );
      }

      logger.info(`Screenshot deleted: ${screenshotId} for VM ${vmId}`);
      
      return NextResponse.json({ 
        success: true,
        message: `Screenshot ${screenshotId} deleted successfully`
      });
    } else {
      // Delete all screenshots for VM
      const deletedCount = await service.deleteAllScreenshots(vmId);
      
      logger.info(`${deletedCount} screenshots deleted for VM ${vmId}`);
      
      return NextResponse.json({ 
        success: true,
        message: `${deletedCount} screenshots deleted successfully`,
        deletedCount
      });
    }
  } catch (error: any) {
    logger.error(`Error deleting screenshots: ${error.message}`);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
