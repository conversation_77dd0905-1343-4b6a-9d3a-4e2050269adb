/**
 * Desktop VM API Routes
 * 
 * API endpoints for desktop virtual machine management including
 * creation, lifecycle operations, and configuration management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/noderunner/logger';
import { DesktopVmProvider } from '@/lib/containerization/desktop-vm/desktop-vm-provider';
import { 
  CreateDesktopVmRequest, 
  CreateDesktopVmResponse,
  DesktopVm 
} from '@/lib/containerization/desktop-vm/types';

// Global desktop VM provider instance
let desktopVmProvider: DesktopVmProvider;

// Initialize provider
function getDesktopVmProvider(): DesktopVmProvider {
  if (!desktopVmProvider) {
    desktopVmProvider = new DesktopVmProvider({
      baseDir: process.env.DESKTOP_VM_BASE_DIR || '/tmp/desktop-vms',
      maxVms: parseInt(process.env.DESKTOP_VM_MAX_VMS || '10'),
      enableMonitoring: process.env.DESKTOP_VM_ENABLE_MONITORING !== 'false',
      cleanupOnExit: process.env.DESKTOP_VM_CLEANUP_ON_EXIT !== 'false',
    });
  }
  return desktopVmProvider;
}

/**
 * GET handler for Desktop VM operations
 * - List all VMs: GET /api/desktop-vm
 * - Get VM by ID: GET /api/desktop-vm?vmId={vmId}
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const vmId = searchParams.get('vmId');

    const provider = getDesktopVmProvider();

    if (vmId) {
      // Get specific VM
      const vm = provider.getVm(vmId);
      if (!vm) {
        return NextResponse.json(
          { error: `Desktop VM ${vmId} not found` },
          { status: 404 }
        );
      }

      return NextResponse.json({ vm });
    } else {
      // List all VMs
      const vms = provider.getAllVms();
      return NextResponse.json({ vms });
    }
  } catch (error: any) {
    logger.error(`Error in desktop VM GET handler: ${error.message}`);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST handler for Desktop VM operations
 * - Create VM: POST /api/desktop-vm
 */
export async function POST(request: NextRequest) {
  try {
    const body: CreateDesktopVmRequest = await request.json();
    
    // Validate request
    if (!body.config) {
      return NextResponse.json(
        { error: 'VM configuration is required' },
        { status: 400 }
      );
    }

    if (!body.config.name?.trim()) {
      return NextResponse.json(
        { error: 'VM name is required' },
        { status: 400 }
      );
    }

    const provider = getDesktopVmProvider();
    
    // Create the desktop VM
    const vm = await provider.createDesktopVm(body.config);
    
    logger.info(`Desktop VM created: ${vm.id}`);
    
    const response: CreateDesktopVmResponse = { vm };
    return NextResponse.json(response);
  } catch (error: any) {
    logger.error(`Error creating desktop VM: ${error.message}`);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for Desktop VM operations
 * - Delete VM: DELETE /api/desktop-vm?vmId={vmId}
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const vmId = searchParams.get('vmId');

    if (!vmId) {
      return NextResponse.json(
        { error: 'VM ID is required' },
        { status: 400 }
      );
    }

    const provider = getDesktopVmProvider();
    
    // Check if VM exists
    const vm = provider.getVm(vmId);
    if (!vm) {
      return NextResponse.json(
        { error: `Desktop VM ${vmId} not found` },
        { status: 404 }
      );
    }

    // Delete the VM
    await provider.deleteVm(vmId);
    
    logger.info(`Desktop VM deleted: ${vmId}`);
    
    return NextResponse.json({ 
      success: true,
      message: `Desktop VM ${vmId} deleted successfully`
    });
  } catch (error: any) {
    logger.error(`Error deleting desktop VM: ${error.message}`);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
