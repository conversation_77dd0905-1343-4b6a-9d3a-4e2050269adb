/**
 * Desktop VM Status API Route
 * 
 * Real-time status checking for Desktop VM containers and Guacamole services
 */

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface StatusResponse {
  containers: {
    desktopVm?: {
      id: string;
      status: string;
      health: string;
      ports: {
        vnc: number;
        novnc?: number;
      };
      resources: {
        cpu: string;
        memory: string;
        network: string;
      };
    };
    guacamole?: {
      id: string;
      status: string;
      health: string;
      port: number;
    };
    guacd?: {
      id: string;
      status: string;
      health: string;
    };
    database?: {
      id: string;
      status: string;
      health: string;
    };
  };
  services: {
    guacamoleReady: boolean;
    desktopVmReady: boolean;
    overallStatus: 'healthy' | 'starting' | 'error' | 'stopped';
  };
}

export async function GET(request: NextRequest) {
  try {
    const response: StatusResponse = {
      containers: {},
      services: {
        guacamoleReady: false,
        desktopVmReady: false,
        overallStatus: 'stopped'
      }
    };

    // Check for Desktop VM containers
    try {
      const { stdout: containers } = await execAsync('docker ps --format "{{.ID}},{{.Names}},{{.Status}},{{.Ports}}" | grep -E "(desktop-vm|guacamole)"');
      
      const containerLines = containers.trim().split('\n').filter(line => line.length > 0);
      
      for (const line of containerLines) {
        const [id, name, status, ports] = line.split(',');
        
        if (name.includes('desktop-vm')) {
          // Get resource usage
          const resources = await getContainerResources(id);
          
          // Extract VNC port
          const vncPortMatch = ports.match(/(\d+)->5901/);
          const vncPort = vncPortMatch ? parseInt(vncPortMatch[1]) : 5901;
          
          response.containers.desktopVm = {
            id,
            status: status.includes('Up') ? 'running' : 'stopped',
            health: await getContainerHealth(id),
            ports: { vnc: vncPort },
            resources
          };
          
          response.services.desktopVmReady = status.includes('Up');
        }
        
        if (name.includes('guacamole') && !name.includes('guacd') && !name.includes('db')) {
          // Extract Guacamole port
          const guacPortMatch = ports.match(/(\d+)->8080/);
          const guacPort = guacPortMatch ? parseInt(guacPortMatch[1]) : 8080;
          
          response.containers.guacamole = {
            id,
            status: status.includes('Up') ? 'running' : 'stopped',
            health: await getContainerHealth(id),
            port: guacPort
          };
          
          response.services.guacamoleReady = status.includes('Up');
        }
        
        if (name.includes('guacd')) {
          response.containers.guacd = {
            id,
            status: status.includes('Up') ? 'running' : 'stopped',
            health: await getContainerHealth(id)
          };
        }
        
        if (name.includes('db')) {
          response.containers.database = {
            id,
            status: status.includes('Up') ? 'running' : 'stopped',
            health: await getContainerHealth(id)
          };
        }
      }
    } catch (error) {
      // No containers found or error - this is okay
      console.log('No Desktop VM containers found');
    }

    // Determine overall status
    if (response.services.desktopVmReady && response.services.guacamoleReady) {
      response.services.overallStatus = 'healthy';
    } else if (response.containers.desktopVm?.status === 'running' || 
               response.containers.guacamole?.status === 'running') {
      response.services.overallStatus = 'starting';
    } else if (Object.keys(response.containers).length > 0) {
      response.services.overallStatus = 'error';
    } else {
      response.services.overallStatus = 'stopped';
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error getting Desktop VM status:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get Desktop VM status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to get container health
async function getContainerHealth(containerId: string): Promise<string> {
  try {
    const { stdout } = await execAsync(`docker inspect ${containerId} --format='{{.State.Health.Status}}'`);
    const health = stdout.trim();
    return health === '<no value>' ? 'unknown' : health;
  } catch (error) {
    return 'unknown';
  }
}

// Helper function to get container resource usage
async function getContainerResources(containerId: string): Promise<{
  cpu: string;
  memory: string;
  network: string;
}> {
  try {
    const { stdout } = await execAsync(
      `docker stats ${containerId} --no-stream --format "{{.CPUPerc}},{{.MemUsage}},{{.NetIO}}"`
    );
    
    const [cpu, memUsage, netIO] = stdout.trim().split(',');
    const memory = memUsage.split(' / ')[0]; // Get used memory only
    
    return {
      cpu: cpu || '0.00%',
      memory: memory || '0B',
      network: netIO || '0B / 0B'
    };
  } catch (error) {
    console.error('Error getting container resources:', error);
    return {
      cpu: '0.00%',
      memory: '0B',
      network: '0B / 0B'
    };
  }
}
