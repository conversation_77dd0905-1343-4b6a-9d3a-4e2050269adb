/**
 * Browserless API Routes
 * 
 * This file contains API routes for the Browserless service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { browserlessService } from '@/lib/browserless';
import { BrowserOptions } from '@/lib/browserless/types';

/**
 * GET handler for Browserless
 * - Get content: GET /api/browserless?url=https://example.com&resultType=html
 */
export async function GET(request: NextRequest) {
  try {
    // Parse the URL to get query parameters
    const url = new URL(request.url);
    const targetUrl = url.searchParams.get('url');
    const resultType = url.searchParams.get('resultType') || 'html';
    const waitForSelector = url.searchParams.get('waitForSelector') || undefined;
    const timeout = url.searchParams.get('timeout') ? parseInt(url.searchParams.get('timeout')!, 10) : undefined;
    const stealth = url.searchParams.get('stealth') !== 'false';
    const javascript = url.searchParams.get('javascript') !== 'false';
    
    // Validate the URL
    if (!targetUrl) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }
    
    // Create options
    const options: BrowserOptions = {
      url: targetUrl,
      resultType: resultType as any,
      waitForSelector,
      timeout,
      stealth,
      javascript
    };
    
    // Get content
    const result = await browserlessService.getContent(options);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST handler for Browserless
 * - Get content: POST /api/browserless
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const options: BrowserOptions = await request.json();
    
    // Validate the URL
    if (!options.url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }
    
    // Get content
    const result = await browserlessService.getContent(options);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
