/**
 * Browserless Function API Route
 * 
 * This file contains API routes for executing functions with Browserless.
 */

import { NextRequest, NextResponse } from 'next/server';
import { browserlessService } from '@/lib/browserless';
import { FunctionOptions } from '@/lib/browserless/types';

/**
 * POST handler for Browserless Function
 * - Execute function: POST /api/browserless/function
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const options: FunctionOptions = await request.json();
    
    // Validate the URL
    if (!options.url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }
    
    // Validate the function
    if (!options.fn) {
      return NextResponse.json(
        { error: 'Function is required' },
        { status: 400 }
      );
    }
    
    // Execute function
    const result = await browserlessService.executeFunction(options);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
