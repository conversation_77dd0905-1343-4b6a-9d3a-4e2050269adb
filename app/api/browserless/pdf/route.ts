/**
 * Browserless PDF API Route
 * 
 * This file contains API routes for generating PDFs with Browserless.
 */

import { NextRequest, NextResponse } from 'next/server';
import { browserlessService } from '@/lib/browserless';
import { PdfOptions } from '@/lib/browserless/types';

/**
 * POST handler for Browserless PDF
 * - Generate PDF: POST /api/browserless/pdf
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const options: PdfOptions = await request.json();
    
    // Validate the URL
    if (!options.url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }
    
    // Generate PDF
    const result = await browserlessService.getPdf(options);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET handler for Browserless PDF
 * - Generate PDF: GET /api/browserless/pdf?url=https://example.com
 */
export async function GET(request: NextRequest) {
  try {
    // Parse the URL to get query parameters
    const url = new URL(request.url);
    const targetUrl = url.searchParams.get('url');
    const format = url.searchParams.get('format') as any || 'Letter';
    const printBackground = url.searchParams.get('printBackground') !== 'false';
    const waitForSelector = url.searchParams.get('waitForSelector') || undefined;
    const timeout = url.searchParams.get('timeout') ? parseInt(url.searchParams.get('timeout')!, 10) : undefined;
    const stealth = url.searchParams.get('stealth') !== 'false';
    const javascript = url.searchParams.get('javascript') !== 'false';
    
    // Validate the URL
    if (!targetUrl) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }
    
    // Create options
    const options: PdfOptions = {
      url: targetUrl,
      format,
      printBackground,
      waitForSelector,
      timeout,
      stealth,
      javascript
    };
    
    // Generate PDF
    const result = await browserlessService.getPdf(options);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
