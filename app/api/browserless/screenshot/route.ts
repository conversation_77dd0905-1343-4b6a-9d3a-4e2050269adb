/**
 * Browserless Screenshot API Route
 * 
 * This file contains API routes for taking screenshots with Browserless.
 */

import { NextRequest, NextResponse } from 'next/server';
import { browserlessService } from '@/lib/browserless';
import { ScreenshotOptions } from '@/lib/browserless/types';

/**
 * POST handler for Browserless Screenshot
 * - Take screenshot: POST /api/browserless/screenshot
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const options: ScreenshotOptions = await request.json();
    
    // Validate the URL
    if (!options.url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }
    
    // Take screenshot
    const result = await browserlessService.getScreenshot(options);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET handler for Browserless Screenshot
 * - Take screenshot: GET /api/browserless/screenshot?url=https://example.com
 */
export async function GET(request: NextRequest) {
  try {
    // Parse the URL to get query parameters
    const url = new URL(request.url);
    const targetUrl = url.searchParams.get('url');
    const type = (url.searchParams.get('type') || 'jpeg') as 'jpeg' | 'png';
    const quality = url.searchParams.get('quality') ? parseInt(url.searchParams.get('quality')!, 10) : 80;
    const fullPage = url.searchParams.get('fullPage') !== 'false';
    const waitForSelector = url.searchParams.get('waitForSelector') || undefined;
    const timeout = url.searchParams.get('timeout') ? parseInt(url.searchParams.get('timeout')!, 10) : undefined;
    const stealth = url.searchParams.get('stealth') !== 'false';
    const javascript = url.searchParams.get('javascript') !== 'false';
    
    // Validate the URL
    if (!targetUrl) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }
    
    // Create options
    const options: ScreenshotOptions = {
      url: targetUrl,
      type,
      quality,
      fullPage,
      waitForSelector,
      timeout,
      stealth,
      javascript
    };
    
    // Take screenshot
    const result = await browserlessService.getScreenshot(options);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
