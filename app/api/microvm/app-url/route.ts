import { NextRequest, NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";
import { type ExecOptions } from "child_process";

const execAsync = promisify(exec);

// Map of VM IDs to exposed ports
const VM_PORT_MAP: Record<string, number> = {};

// Maximum retry attempts for checking port availability
const MAX_RETRIES = 3;
// Timeout in milliseconds for exec commands
const COMMAND_TIMEOUT = 5000;

export async function GET(request: NextRequest) {
  try {
    // Get VM ID and port from the request
    const url = new URL(request.url);
    const vmId = url.searchParams.get("vmId");
    const port = Number(url.searchParams.get("port") || 3000);
    
    if (!vmId) {
      return NextResponse.json(
        { error: "VM ID is required" },
        { status: 400 }
      );
    }

    console.log(`Getting app URL for VM ${vmId} on port ${port}`);

    // Check if a port mapping exists for this VM
    let exposedPort = VM_PORT_MAP[vmId];
    let isNewPortMapping = false;

    // If no port mapping exists, create one
    if (!exposedPort) {
      try {
        // Generate a random port between 10000-60000
        exposedPort = Math.floor(Math.random() * 50000) + 10000;
        isNewPortMapping = true;

        // Try multiple VM detection approaches in sequence
        const vmType = await detectVmType(vmId);
        console.log(`Detected VM type: ${vmType}`);
        
        if (vmType === "firecracker") {
          // For Firecracker VMs, use direct port forward
          await setupFirecrackerPortForward(vmId, port, exposedPort);
          VM_PORT_MAP[vmId] = exposedPort;
        } 
        else if (vmType === "lxc") {
          // For LXC containers, use LXC proxy device
          await setupLxcPortForward(vmId, port, exposedPort);
          VM_PORT_MAP[vmId] = exposedPort;
        } 
        else if (vmType === "qemu") {
          // For QEMU VMs, use SSH tunnel or virsh port forwarding
          await setupQemuPortForward(vmId, port, exposedPort);
          VM_PORT_MAP[vmId] = exposedPort;
        }
        else {
          return NextResponse.json(
            { error: "VM type not detected or port forwarding not supported" },
            { status: 404 }
          );
        }
      } catch (error: any) {
        console.error("Error setting up port forwarding:", error);
        return NextResponse.json(
          { error: `Failed to set up port forwarding: ${error.message}` },
          { status: 500 }
        );
      }
    }

    // Check if the service is running on the forwarded port
    try {
      // Use more reliable method to check if port is accessible
      const isAvailable = await isPortAccessible(exposedPort);
      
      if (isAvailable) {
        return NextResponse.json({
          url: `http://localhost:${exposedPort}`,
          port: exposedPort,
          isNewMapping: isNewPortMapping
        });
      }

      // If we just created a port mapping and it's not responding, try a few more times
      if (isNewPortMapping) {
        for (let retry = 0; retry < MAX_RETRIES; retry++) {
          console.log(`Retrying port check (${retry + 1}/${MAX_RETRIES})...`);
          // Wait a second before retry
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          if (await isPortAccessible(exposedPort)) {
            return NextResponse.json({
              url: `http://localhost:${exposedPort}`,
              port: exposedPort,
              isNewMapping: true
            });
          }
        }
      }

      // If still not available, send error
      return NextResponse.json(
        { 
          error: "App is not running or not accessible",
          port: exposedPort
        },
        { status: 404 }
      );
    } catch (error: any) {
      console.error("Error checking port accessibility:", error);
      return NextResponse.json(
        { error: `Failed to check port accessibility: ${error.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Error getting app URL:", error);
    
    return NextResponse.json(
      { error: error.message || "Unknown error occurred" },
      { status: 500 }
    );
  }
}

/**
 * Try different approaches to detect VM type
 */
async function detectVmType(vmId: string): Promise<string> {
  try {
    // Try Firecracker first
    const options: ExecOptions = { timeout: COMMAND_TIMEOUT };
    const { stdout: fcOutput } = await execAsync(`curl -s --unix-socket /tmp/firecracker.socket http://localhost/machine-config 2>/dev/null || echo "not-found"`, options);
    
    if (!fcOutput.includes("not-found")) {
      return "firecracker";
    }
    
    // Try virsh for libvirt/QEMU
    const { stdout: virshOutput } = await execAsync(`sudo virsh list --all | grep ${vmId} || echo "not-found"`, options);
    
    if (!virshOutput.includes("not-found")) {
      return "qemu";
    }
    
    // Try LXC
    const { stdout: lxcOutput } = await execAsync(`sudo lxc-info -n ${vmId} 2>/dev/null || echo "not-found"`, options);
    
    if (!lxcOutput.includes("not-found")) {
      return "lxc";
    }
    
    // Default to unknown
    return "unknown";
  } catch (error) {
    console.error("Error detecting VM type:", error);
    return "unknown";
  }
}

/**
 * Setup port forwarding for Firecracker VMs
 */
async function setupFirecrackerPortForward(vmId: string, vmPort: number, hostPort: number): Promise<void> {
  try {
    // Try direct socat forwarding first
    await execAsync(`socat TCP-LISTEN:${hostPort},reuseaddr,fork TCP:localhost:${vmPort} & echo $! > /tmp/socat-${vmId}-${vmPort}.pid`, 
      { timeout: COMMAND_TIMEOUT });
    
    console.log(`Set up Firecracker port forward from localhost:${hostPort} to VM:${vmPort}`);
    return;
  } catch (error) {
    throw new Error(`Failed to set up Firecracker port forwarding: ${error}`);
  }
}

/**
 * Setup port forwarding for LXC containers
 */
async function setupLxcPortForward(vmId: string, vmPort: number, hostPort: number): Promise<void> {
  try {
    await execAsync(`sudo lxc-config device add ${vmId} port-${vmPort} proxy listen=tcp:0.0.0.0:${hostPort} connect=tcp:127.0.0.1:${vmPort}`,
      { timeout: COMMAND_TIMEOUT });
    
    console.log(`Set up LXC port forward from localhost:${hostPort} to container:${vmPort}`);
    return;
  } catch (error) {
    throw new Error(`Failed to set up LXC port forwarding: ${error}`);
  }
}

/**
 * Setup port forwarding for QEMU/libvirt VMs
 */
async function setupQemuPortForward(vmId: string, vmPort: number, hostPort: number): Promise<void> {
  try {
    // Try SSH tunnel if agent command is available
    try {
      await execAsync(`sudo virsh qemu-agent-command ${vmId} '{"execute":"guest-exec","arguments":{"path":"echo","arg":["hello"],"capture-output":true}}' 2>/dev/null`,
        { timeout: COMMAND_TIMEOUT });
      
      // Use SSH tunnel approach if agent is responsive
      await execAsync(`sudo ssh -o StrictHostKeyChecking=no -L ${hostPort}:localhost:${vmPort} -N -f root@$(virsh domifaddr ${vmId} | grep -oE '([0-9]{1,3}\\.){3}[0-9]{1,3}' | head -1)`,
        { timeout: COMMAND_TIMEOUT });
      
      console.log(`Set up QEMU SSH port forward from localhost:${hostPort} to VM:${vmPort}`);
      return;
    } catch {
      // Fall back to iptables/NAT if SSH doesn't work
      const { stdout: ipOutput } = await execAsync(`sudo virsh domifaddr ${vmId} | grep -oE '([0-9]{1,3}\\.){3}[0-9]{1,3}' | head -1`,
        { timeout: COMMAND_TIMEOUT });
      
      const vmIp = ipOutput.trim();
      if (vmIp) {
        await execAsync(`sudo iptables -t nat -A PREROUTING -p tcp --dport ${hostPort} -j DNAT --to ${vmIp}:${vmPort}`,
          { timeout: COMMAND_TIMEOUT });
        
        console.log(`Set up iptables port forward from localhost:${hostPort} to VM(${vmIp}):${vmPort}`);
        return;
      }
    }
    
    throw new Error("Failed to establish any port forwarding method");
  } catch (error) {
    throw new Error(`Failed to set up QEMU port forwarding: ${error}`);
  }
}

/**
 * Check if a port is accessible and responding
 */
async function isPortAccessible(port: number): Promise<boolean> {
  try {
    const options: ExecOptions = { timeout: COMMAND_TIMEOUT };
    
    // Try curl first with a quick timeout
    try {
      const { stdout } = await execAsync(`curl -s -o /dev/null -w "%{http_code}" http://localhost:${port} -m 2`, options);
      const statusCode = parseInt(stdout.trim());
      
      return statusCode >= 200 && statusCode < 600; // Any HTTP response is good
    } catch {
      // If curl fails, try a TCP connection check with netcat
      const { stdout } = await execAsync(`nc -z -w1 localhost ${port} && echo "success" || echo "failed"`, options);
      
      return stdout.trim() === "success";
    }
  } catch (error) {
    console.error("Error checking port accessibility:", error);
    return false;
  }
}