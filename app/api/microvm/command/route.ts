import { NextRequest, NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";
import { validateAndSanitizeCommand } from "@/lib/utils/security";

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    // Extract command and VM ID from request
    const { vmId, command } = await request.json();
    
    if (!command) {
      return NextResponse.json(
        { error: "Command is required" },
        { status: 400 }
      );
    }

    // Validate and sanitize the command for security
    const sanitizedCommand = validateAndSanitizeCommand(command);
    if (!sanitizedCommand) {
      return NextResponse.json(
        { error: "Invalid command" },
        { status: 400 }
      );
    }
    
    let commandToExecute = sanitizedCommand;
    
    // If a vmId is provided, execute the command in the VM context
    if (vmId) {
      // First check if we're using Firecracker VMs
      try {
        const checkVM = await execAsync(`sudo virsh list --all | grep ${vmId}`);
        
        if (checkVM.stdout) {
          // Use virsh to execute command in the VM
          commandToExecute = `sudo virsh qemu-agent-command ${vmId} '{"execute":"guest-exec", "arguments":{"path":"/bin/sh","arg":["-c","${sanitizedCommand}"],"capture-output":true}}'`;
        }
      } catch (error) {
        // Fall back to other VM technologies if virsh doesn't work
        try {
          // Check if this is a LXC container
          const checkLXC = await execAsync(`sudo lxc-info -n ${vmId} 2>/dev/null || echo "not-found"`);
          
          if (!checkLXC.stdout.includes("not-found")) {
            // Use LXC to execute command
            commandToExecute = `sudo lxc-attach -n ${vmId} -- /bin/sh -c "${sanitizedCommand}"`;
          }
        } catch (lxcError) {
          console.error("Error checking for LXC container:", lxcError);
        }
      }
    }
    
    // Execute the command
    const { stdout, stderr } = await execAsync(commandToExecute);
    
    return NextResponse.json({
      result: stdout,
      error: stderr || null,
    });
  } catch (error: any) {
    console.error("Error executing command:", error);
    
    return NextResponse.json(
      { error: error.message || "Unknown error occurred" },
      { status: 500 }
    );
  }
} 