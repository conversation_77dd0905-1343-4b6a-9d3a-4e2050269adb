import { NextRequest, NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";
import { type ExecOptions } from "child_process";
import path from "path";
import { validateAndSanitizeCommand } from "@/lib/utils/security";

const execAsync = promisify(exec);

// Timeout in milliseconds for exec commands
const COMMAND_TIMEOUT = 10000;

interface FilesystemRequestBody {
  vmId?: string;
  operation: "read" | "write" | "list" | "delete" | "exists";
  path: string;
  content?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: FilesystemRequestBody = await request.json();
    const { vmId, operation, path: filePath, content } = body;

    if (!vmId) {
      return NextResponse.json(
        { error: "VM ID is required" },
        { status: 400 }
      );
    }

    if (!filePath) {
      return NextResponse.json(
        { error: "Path is required" },
        { status: 400 }
      );
    }

    // Check if operation is valid
    if (!["read", "write", "list", "delete", "exists"].includes(operation)) {
      return NextResponse.json(
        { error: "Invalid operation" },
        { status: 400 }
      );
    }

    // Security check - ensure path is within allowed directories
    const normalizedPath = filePath.replace(/\/+/g, '/');
    if (!isAllowedPath(normalizedPath)) {
      return NextResponse.json(
        { error: "Access to this path is not allowed" },
        { status: 403 }
      );
    }

    // Execute the appropriate operation
    switch (operation) {
      case "read":
        return await readFile(vmId, normalizedPath);
      case "write":
        return await writeFile(vmId, normalizedPath, content || "");
      case "list":
        return await listDirectory(vmId, normalizedPath);
      case "delete":
        return await deleteItem(vmId, normalizedPath);
      case "exists":
        return await checkExists(vmId, normalizedPath);
      default:
        return NextResponse.json(
          { error: "Unsupported operation" },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error("Error in filesystem operation:", error);
    return NextResponse.json(
      { error: error.message || "Unknown error occurred" },
      { status: 500 }
    );
  }
}

// Check if path is allowed for security
function isAllowedPath(filePath: string): boolean {
  // Allow paths that start with /app, /tmp, or /home
  const allowedPrefixes = ['/app', '/tmp', '/home'];
  return allowedPrefixes.some(prefix => filePath.startsWith(prefix));
}

// Read file content
async function readFile(vmId: string, filePath: string) {
  try {
    const options: ExecOptions = { timeout: COMMAND_TIMEOUT };
    
    // First check if file exists
    const { stdout: existsOutput } = await execAsync(
      `test -f "${filePath}" && echo "exists" || echo "not exists"`,
      options
    );
    
    if (existsOutput.trim() !== "exists") {
      return NextResponse.json(
        { error: "File not found" },
        { status: 404 }
      );
    }
    
    // Check file size first to prevent loading huge files
    const { stdout: sizeOutput } = await execAsync(
      `stat -c %s "${filePath}"`,
      options
    );
    
    const fileSize = parseInt(sizeOutput.trim());
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB limit
    
    if (fileSize > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "File too large to load" },
        { status: 413 }
      );
    }
    
    // Read file content
    const { stdout: contentOutput } = await execAsync(
      `cat "${filePath}" | base64`,
      options
    );

    // Decode from base64
    const content = Buffer.from(contentOutput.trim(), 'base64').toString('utf-8');
    
    return NextResponse.json({
      content,
      size: fileSize,
      path: filePath
    });
  } catch (error: any) {
    console.error("Error reading file:", error);
    return NextResponse.json(
      { error: `Failed to read file: ${error.message}` },
      { status: 500 }
    );
  }
}

// Write file content
async function writeFile(vmId: string, filePath: string, content: string) {
  try {
    const options: ExecOptions = { timeout: COMMAND_TIMEOUT };
    
    // Make sure parent directory exists
    const dirPath = path.dirname(filePath);
    await execAsync(
      `mkdir -p "${dirPath}"`,
      options
    );
    
    // Encode content as base64 to handle special characters
    const base64Content = Buffer.from(content).toString('base64');
    
    // Write file
    await execAsync(
      `echo "${base64Content}" | base64 -d > "${filePath}"`,
      options
    );
    
    return NextResponse.json({
      success: true,
      path: filePath
    });
  } catch (error: any) {
    console.error("Error writing file:", error);
    return NextResponse.json(
      { error: `Failed to write file: ${error.message}` },
      { status: 500 }
    );
  }
}

// List directory contents
async function listDirectory(vmId: string, dirPath: string) {
  try {
    const options: ExecOptions = { timeout: COMMAND_TIMEOUT };
    
    // First check if directory exists
    const { stdout: existsOutput } = await execAsync(
      `test -d "${dirPath}" && echo "exists" || echo "not exists"`,
      options
    );
    
    if (existsOutput.trim() !== "exists") {
      return NextResponse.json(
        { error: "Directory not found" },
        { status: 404 }
      );
    }
    
    // List files with types
    const { stdout: lsOutput } = await execAsync(
      `find "${dirPath}" -maxdepth 1 -printf "%y,%p\\n" | grep -v "^$"`,
      options
    );
    
    // Parse the output
    const contents = lsOutput.trim().split('\n').filter(Boolean).map(line => {
      const [typeChar, itemPath] = line.split(',');
      const name = path.basename(itemPath);
      
      // Skip . and ..
      if (name === '.' || name === '..') {
        return null;
      }
      
      const type = typeChar === 'd' ? 'directory' : 'file';
      return {
        name,
        path: itemPath,
        type
      };
    }).filter(Boolean);
    
    return NextResponse.json({
      path: dirPath,
      contents
    });
  } catch (error: any) {
    console.error("Error listing directory:", error);
    return NextResponse.json(
      { error: `Failed to list directory: ${error.message}` },
      { status: 500 }
    );
  }
}

// Delete file or directory
async function deleteItem(vmId: string, itemPath: string) {
  try {
    const options: ExecOptions = { timeout: COMMAND_TIMEOUT };
    
    // First check if item exists
    const { stdout: existsOutput } = await execAsync(
      `test -e "${itemPath}" && echo "exists" || echo "not exists"`,
      options
    );
    
    if (existsOutput.trim() !== "exists") {
      return NextResponse.json(
        { error: "Item not found" },
        { status: 404 }
      );
    }
    
    // Check if it's a directory or file
    const { stdout: typeOutput } = await execAsync(
      `test -d "${itemPath}" && echo "directory" || echo "file"`,
      options
    );
    
    // Delete based on type
    if (typeOutput.trim() === "directory") {
      await execAsync(
        `rm -rf "${itemPath}"`,
        options
      );
    } else {
      await execAsync(
        `rm -f "${itemPath}"`,
        options
      );
    }
    
    return NextResponse.json({
      success: true,
      path: itemPath,
      type: typeOutput.trim()
    });
  } catch (error: any) {
    console.error("Error deleting item:", error);
    return NextResponse.json(
      { error: `Failed to delete item: ${error.message}` },
      { status: 500 }
    );
  }
}

// Check if file or directory exists
async function checkExists(vmId: string, itemPath: string) {
  try {
    const options: ExecOptions = { timeout: COMMAND_TIMEOUT };
    
    // Check if item exists
    const { stdout: existsOutput } = await execAsync(
      `test -e "${itemPath}" && echo "exists" || echo "not exists"`,
      options
    );
    
    if (existsOutput.trim() !== "exists") {
      return NextResponse.json({
        exists: false,
        path: itemPath
      });
    }
    
    // Check if it's a directory or file
    const { stdout: typeOutput } = await execAsync(
      `test -d "${itemPath}" && echo "directory" || echo "file"`,
      options
    );
    
    return NextResponse.json({
      exists: true,
      path: itemPath,
      type: typeOutput.trim()
    });
  } catch (error: any) {
    console.error("Error checking if item exists:", error);
    return NextResponse.json(
      { error: `Failed to check if item exists: ${error.message}` },
      { status: 500 }
    );
  }
} 