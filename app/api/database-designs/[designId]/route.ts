import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { db } from "@/lib/db"

// GET /api/database-designs/[designId]
export async function GET(
  req: NextRequest,
  { params }: { params: { designId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const designId = params.designId

  try {
    // Get database design
    const design = await db.databaseDesign.findUnique({
      where: {
        id: designId,
      },
      include: {
        project: true,
      },
    })

    if (!design) {
      return NextResponse.json({ error: "Database design not found" }, { status: 404 })
    }

    // Check if the design belongs to a project owned by the user
    if (design.project.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Remove project details from the response
    const { project, ...designData } = design
    return NextResponse.json(designData)
  } catch (error) {
    console.error("Error fetching database design:", error)
    return NextResponse.json(
      { error: "Failed to fetch database design" },
      { status: 500 }
    )
  }
}

// PUT /api/database-designs/[designId]
export async function PUT(
  req: NextRequest,
  { params }: { params: { designId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const designId = params.designId
  const body = await req.json()

  try {
    // Get database design
    const design = await db.databaseDesign.findUnique({
      where: {
        id: designId,
      },
      include: {
        project: true,
      },
    })

    if (!design) {
      return NextResponse.json({ error: "Database design not found" }, { status: 404 })
    }

    // Check if the design belongs to a project owned by the user
    if (design.project.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Update database design
    const updatedDesign = await db.databaseDesign.update({
      where: {
        id: designId,
      },
      data: {
        name: body.name !== undefined ? body.name : undefined,
        description: body.description !== undefined ? body.description : undefined,
        entities: body.entities !== undefined ? body.entities : undefined,
        relationships: body.relationships !== undefined ? body.relationships : undefined,
        schema: body.schema !== undefined ? body.schema : undefined,
      },
    })

    return NextResponse.json(updatedDesign)
  } catch (error) {
    console.error("Error updating database design:", error)
    return NextResponse.json(
      { error: "Failed to update database design" },
      { status: 500 }
    )
  }
}

// DELETE /api/database-designs/[designId]
export async function DELETE(
  req: NextRequest,
  { params }: { params: { designId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const designId = params.designId

  try {
    // Get database design
    const design = await db.databaseDesign.findUnique({
      where: {
        id: designId,
      },
      include: {
        project: true,
      },
    })

    if (!design) {
      return NextResponse.json({ error: "Database design not found" }, { status: 404 })
    }

    // Check if the design belongs to a project owned by the user
    if (design.project.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Delete database design
    await db.databaseDesign.delete({
      where: {
        id: designId,
      },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting database design:", error)
    return NextResponse.json(
      { error: "Failed to delete database design" },
      { status: 500 }
    )
  }
}
