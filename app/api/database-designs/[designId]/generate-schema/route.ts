import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { db } from "@/lib/db"

// Helper function to generate Prisma schema from entities and relationships
function generatePrismaSchema(entities: any[], relationships: any[]) {
  let schema = `generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

`;

  // Generate model definitions
  entities.forEach(entity => {
    schema += `model ${entity.name} {\n`;
    
    // Add ID field if not present
    if (!entity.fields.some(f => f.name === 'id')) {
      schema += `  id String @id @default(cuid())\n`;
    }
    
    // Add fields
    entity.fields.forEach(field => {
      if (field.name === 'id') {
        schema += `  id String @id @default(cuid())\n`;
      } else {
        const fieldType = field.type === 'string' ? 'String' : 
                         field.type === 'number' ? 'Int' :
                         field.type === 'boolean' ? 'Boolean' :
                         field.type === 'date' ? 'DateTime' :
                         field.type === 'float' ? 'Float' :
                         field.type === 'decimal' ? 'Decimal' :
                         field.type === 'json' ? 'Json' :
                         field.type === 'bigint' ? 'BigInt' :
                         field.type === 'bytes' ? 'Bytes' :
                         'String';
        
        let fieldDef = `  ${field.name} ${fieldType}`;
        
        // Add required/optional marker
        if (!field.required) {
          fieldDef += '?';
        }
        
        // Add unique attribute
        if (field.unique) {
          fieldDef += ' @unique';
        }
        
        schema += `${fieldDef}\n`;
      }
    });
    
    // Add timestamps
    if (!entity.fields.some(f => f.name === 'createdAt')) {
      schema += `  createdAt DateTime @default(now())\n`;
    }
    
    if (!entity.fields.some(f => f.name === 'updatedAt')) {
      schema += `  updatedAt DateTime @updatedAt\n`;
    }
    
    // Placeholder for relations (will be filled in later)
    schema += `  // Relations will be added here\n`;
    
    schema += `}\n\n`;
  });
  
  // Process relationships
  const relationFields = new Map();
  
  relationships.forEach(rel => {
    const { from, to, type, name } = rel;
    
    // Skip if either entity doesn't exist
    if (!entities.some(e => e.name === from) || !entities.some(e => e.name === to)) {
      return;
    }
    
    const relationName = name || `${from.toLowerCase()}To${to}`;
    
    if (type === 'one-to-one') {
      // Add fields to both sides
      if (!relationFields.has(from)) {
        relationFields.set(from, []);
      }
      if (!relationFields.has(to)) {
        relationFields.set(to, []);
      }
      
      relationFields.get(from).push(`  ${to.toLowerCase()} ${to}? @relation("${relationName}")\n`);
      relationFields.get(to).push(`  ${from.toLowerCase()} ${from}? @relation("${relationName}")\n`);
      
    } else if (type === 'one-to-many') {
      // Add fields to both sides
      if (!relationFields.has(from)) {
        relationFields.set(from, []);
      }
      if (!relationFields.has(to)) {
        relationFields.set(to, []);
      }
      
      relationFields.get(from).push(`  ${to.toLowerCase()}s ${to}[] @relation("${relationName}")\n`);
      relationFields.get(to).push(`  ${from.toLowerCase()} ${from} @relation("${relationName}", fields: [${from.toLowerCase()}Id], references: [id])\n`);
      relationFields.get(to).push(`  ${from.toLowerCase()}Id String\n`);
      
    } else if (type === 'many-to-one') {
      // Add fields to both sides
      if (!relationFields.has(from)) {
        relationFields.set(from, []);
      }
      if (!relationFields.has(to)) {
        relationFields.set(to, []);
      }
      
      relationFields.get(from).push(`  ${to.toLowerCase()} ${to} @relation("${relationName}", fields: [${to.toLowerCase()}Id], references: [id])\n`);
      relationFields.get(from).push(`  ${to.toLowerCase()}Id String\n`);
      relationFields.get(to).push(`  ${from.toLowerCase()}s ${from}[] @relation("${relationName}")\n`);
      
    } else if (type === 'many-to-many') {
      // Add fields to both sides
      if (!relationFields.has(from)) {
        relationFields.set(from, []);
      }
      if (!relationFields.has(to)) {
        relationFields.set(to, []);
      }
      
      relationFields.get(from).push(`  ${to.toLowerCase()}s ${to}[] @relation("${relationName}")\n`);
      relationFields.get(to).push(`  ${from.toLowerCase()}s ${from}[] @relation("${relationName}")\n`);
    }
  });
  
  // Add relation fields to schema
  let schemaWithRelations = '';
  schema.split('\n').forEach(line => {
    if (line.includes('// Relations will be added here')) {
      const modelName = line.split('model ')[1]?.split(' {')[0];
      if (modelName && relationFields.has(modelName)) {
        schemaWithRelations += relationFields.get(modelName).join('');
      }
    } else {
      schemaWithRelations += line + '\n';
    }
  });
  
  return schemaWithRelations;
}

// POST /api/database-designs/[designId]/generate-schema
export async function POST(
  req: NextRequest,
  { params }: { params: { designId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const designId = params.designId

  try {
    // Get database design
    const design = await db.databaseDesign.findUnique({
      where: {
        id: designId,
      },
      include: {
        project: true,
      },
    })

    if (!design) {
      return NextResponse.json({ error: "Database design not found" }, { status: 404 })
    }

    // Check if the design belongs to a project owned by the user
    if (design.project.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Generate Prisma schema
    const schema = generatePrismaSchema(design.entities as any[], design.relationships as any[])

    // Update the design with the generated schema
    const updatedDesign = await db.databaseDesign.update({
      where: {
        id: designId,
      },
      data: {
        schema,
      },
    })

    // Also save the schema as a file in the project
    await db.generatedFile.create({
      data: {
        projectId: design.projectId,
        path: "prisma/schema.prisma",
        content: schema,
        language: "prisma",
        size: schema.length,
        type: "file",
        isGenerated: true,
        description: `Generated from database design: ${design.name}`,
      },
    })

    return NextResponse.json({ schema })
  } catch (error) {
    console.error("Error generating schema:", error)
    return NextResponse.json(
      { error: "Failed to generate schema" },
      { status: 500 }
    )
  }
}
