/**
 * Research API Routes
 * 
 * This file contains API routes for the research service.
 */

import { NextRequest, NextResponse } from 'next/server';
import { researchService } from '@/lib/research';

/**
 * GET handler for research
 * - List all research: GET /api/research
 * - Get research by ID: GET /api/research?researchId=123
 */
export async function GET(request: NextRequest) {
  try {
    // Parse the URL to get any query parameters
    const url = new URL(request.url);
    const researchId = url.searchParams.get('researchId');

    // If researchId is provided, get that specific research
    if (researchId) {
      const research = await researchService.getResearch(researchId);
      return NextResponse.json(research);
    }

    // Otherwise, list all research
    const researchItems = await researchService.listResearch();
    return NextResponse.json(researchItems);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for research
 * - Delete research: DELETE /api/research?researchId=123
 */
export async function DELETE(request: NextRequest) {
  try {
    // Parse the URL to get any query parameters
    const url = new URL(request.url);
    const researchId = url.searchParams.get('researchId');

    // Validate the research ID
    if (!researchId) {
      return NextResponse.json(
        { error: 'Research ID is required' },
        { status: 400 }
      );
    }

    // Delete the research
    const success = await researchService.deleteResearch(researchId);
    
    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: 'Research not found' },
        { status: 404 }
      );
    }
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
