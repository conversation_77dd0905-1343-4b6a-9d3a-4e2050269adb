/**
 * Web Page Research API Route
 * 
 * This file contains API routes for researching web pages.
 */

import { NextRequest, NextResponse } from 'next/server';
import { researchService } from '@/lib/research';
import { WebPageResearchOptions } from '@/lib/research/types';

/**
 * POST handler for web page research
 * - Research web page: POST /api/research/web-page
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const options: WebPageResearchOptions = await request.json();

    // Validate the URL
    if (!options.url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    // Research the web page
    const result = await researchService.researchWebPage(options);
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
