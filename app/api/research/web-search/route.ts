/**
 * Web Search API Route
 * 
 * This file contains API routes for performing web searches.
 */

import { NextRequest, NextResponse } from 'next/server';
import { researchService } from '@/lib/research';
import { WebSearchOptions } from '@/lib/research/types';

/**
 * POST handler for web search
 * - Perform web search: POST /api/research/web-search
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const options: WebSearchOptions = await request.json();

    // Validate the query
    if (!options.query) {
      return NextResponse.json(
        { error: 'Query is required' },
        { status: 400 }
      );
    }

    // Perform the web search
    const result = await researchService.performWebSearch(options);
    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
