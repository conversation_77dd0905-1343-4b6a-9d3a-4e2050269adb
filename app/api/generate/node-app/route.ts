import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../auth/[...nextauth]/route"
import { NodeAppGenerator } from "@/lib/firecracker/node-app-generator"
import { db } from "@/lib/db"
import { logger } from "@/lib/utils/logger"

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { projectId, requirements, type } = await req.json()

    if (!projectId || !requirements || !type) {
      return new NextResponse(JSON.stringify({ error: "Missing required fields" }), {
        status: 400,
      })
    }

    // Validate project ownership
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        userId: session.user.id,
      },
    })

    if (!project) {
      return new NextResponse(JSON.stringify({ error: "Project not found" }), {
        status: 404,
      })
    }

    // Create task record
    const task = await db.task.create({
      data: {
        type: "generate",
        status: "running",
        projectId,
        startedAt: new Date(),
      },
    })

    // Generate application
    const generator = new NodeAppGenerator()
    let result

    switch (type) {
      case "react":
        result = await generator.generateReactApp(requirements)
        break
      case "next":
        result = await generator.generateNextApp(requirements)
        break
      case "node":
        result = await generator.generateNodeApp(requirements, "node")
        break
      case "express":
        result = await generator.generateNodeApp(requirements, "express")
        break
      default:
        return new NextResponse(JSON.stringify({ error: "Invalid application type" }), {
          status: 400,
        })
    }

    if (!result.success) {
      await db.task.update({
        where: { id: task.id },
        data: {
          status: "failed",
          error: result.error,
          completedAt: new Date(),
        },
      })

      return new NextResponse(JSON.stringify({ error: result.error }), {
        status: 500,
      })
    }

    // Save generated files to database
    if (result.files && result.files.length > 0) {
      await Promise.all(
        result.files.map((file) =>
          db.generatedFile.create({
            data: {
              projectId,
              path: file.path,
              content: file.content,
              language:
                file.path.endsWith(".ts") || file.path.endsWith(".tsx")
                  ? "typescript"
                  : file.path.endsWith(".js") || file.path.endsWith(".jsx")
                    ? "javascript"
                    : "plaintext",
            },
          }),
        ),
      )
    }

    // Update task status
    await db.task.update({
      where: { id: task.id },
      data: {
        status: "completed",
        result: {
          sandboxId: result.sandboxId,
          fileCount: result.files?.length || 0,
        },
        completedAt: new Date(),
      },
    })

    return NextResponse.json({
      success: true,
      taskId: task.id,
      sandboxId: result.sandboxId,
      fileCount: result.files?.length || 0,
    })
  } catch (error) {
    logger.error("Error generating Node.js application:", error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to generate application",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
