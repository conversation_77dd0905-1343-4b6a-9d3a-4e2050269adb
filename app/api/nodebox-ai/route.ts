/**
 * Nodebox AI Chat API Route
 * 
 * Dedicated API route for AI chat with comprehensive Nodebox tool integration
 * Handles file operations, project management, and development commands
 */

import { openai } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';
import { nodeboxToolHandlers } from '@/lib/api/nodebox-tool-handlers';
import { createAllEnhancedTools } from '@/lib/api/enhanced-nodebox-tools';

// Import real service implementations
import {
  DocumentationService,
  CodeGenerationService,
  CodeValidationService,
  TaskAnalysisService,
  OrchestrationService,
  ParallelProcessingService,
  CodeEvaluationService,
  // Removed unused imports: WebService, NodeboxRuntimeService, nodeboxInstanceProvider
} from '@/lib/nodebox-ai';

// Define the maximum duration for streaming responses
export const maxDuration = 60;

// Helper function to detect language from file path
function detectLanguageFromPath(filePath: string): string {
  const extension = filePath.split('.').pop()?.toLowerCase();

  const languageMap: { [key: string]: string } = {
    'ts': 'typescript',
    'tsx': 'typescript',
    'js': 'javascript',
    'jsx': 'javascript',
    'py': 'python',
    'css': 'css',
    'scss': 'scss',
    'sass': 'sass',
    'html': 'html',
    'json': 'json',
    'md': 'markdown',
    'yml': 'yaml',
    'yaml': 'yaml'
  };

  return languageMap[extension || ''] || 'javascript';
}

// Nodebox tool schemas with comprehensive validation and execute functions
const createNodeboxTools = (projectId?: string) => ({
  read_file_nodebox: tool({
    description: 'Read the contents of a file from the active Nodebox instance',
    parameters: z.object({
      path: z.string().describe('The file path to read (e.g., "/src/App.tsx", "/package.json")'),
    }),
    execute: async ({ path }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }
      return await nodeboxToolHandlers.readFile(projectId, path);
    },
  }),

  write_file_nodebox: tool({
    description: 'Write content to a file in the active Nodebox instance (creates or updates)',
    parameters: z.object({
      path: z.string().describe('The file path to write to (e.g., "/src/components/Button.tsx")'),
      content: z.string().describe('The content to write to the file'),
    }),
    execute: async ({ path, content }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }
      return await nodeboxToolHandlers.writeFile(projectId, path, content);
    },
  }),

  create_file_nodebox: tool({
    description: 'Create a new file in the active Nodebox instance',
    parameters: z.object({
      path: z.string().describe('The file path to create (e.g., "/src/utils/helpers.ts")'),
      content: z.string().optional().default('').describe('The initial content for the file'),
    }),
    execute: async ({ path, content }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }
      return await nodeboxToolHandlers.createFile(projectId, path, content);
    },
  }),

  create_directory_nodebox: tool({
    description: 'Create a new directory in the active Nodebox instance',
    parameters: z.object({
      path: z.string().describe('The directory path to create (e.g., "/src/components", "/docs")'),
    }),
    execute: async ({ path }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }
      return await nodeboxToolHandlers.createDirectory(projectId, path);
    },
  }),

  delete_file_nodebox: tool({
    description: 'Delete a file from the active Nodebox instance',
    parameters: z.object({
      path: z.string().describe('The file path to delete (e.g., "/src/unused.js")'),
    }),
    execute: async ({ path }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }
      return await nodeboxToolHandlers.deleteFile(projectId, path);
    },
  }),

  list_files_nodebox: tool({
    description: 'List all files and directories in the active Nodebox instance',
    parameters: z.object({
      path: z.string().optional().default('/').describe('The directory path to list (defaults to root "/")'),
    }),
    execute: async ({ path }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }
      return await nodeboxToolHandlers.listFiles(projectId, path);
    },
  }),

  run_command_nodebox: tool({
    description: 'Run a command in the active Nodebox instance terminal',
    parameters: z.object({
      command: z.string().describe('The command to run (e.g., "npm", "node", "tsc")'),
      args: z.array(z.string()).optional().default([]).describe('Command arguments (e.g., ["install", "lodash"])'),
    }),
    execute: async ({ command, args }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }
      return await nodeboxToolHandlers.runCommand(projectId, command, args);
    },
  }),

  create_project_nodebox: tool({
    description: 'Create a new Nodebox project from a template',
    parameters: z.object({
      name: z.string().describe('The name for the new project'),
      template: z.enum([
        'react',
        'nextjs',
        'express',
        'vanilla-js',
        'typescript',
        'research-dashboard',
        'data-analysis',
        'custom'
      ]).describe('The project template to use'),
      projectId: z.string().optional().describe('Optional project ID for organization'),
    }),
    execute: async ({ name, template, projectId: customProjectId }) => {
      const finalProjectId = customProjectId || projectId || `project_${Date.now()}`;
      return await nodeboxToolHandlers.createProject(name, template, finalProjectId);
    },
  }),

  get_project_info_nodebox: tool({
    description: 'Get detailed information about the active Nodebox project',
    parameters: z.object({}),
    execute: async () => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }
      return await nodeboxToolHandlers.getProjectInfo(projectId);
    },
  }),
});

// Additional development tools
const createDevelopmentTools = (projectId?: string) => ({
  get_current_time: tool({
    description: 'Get the current date and time',
    parameters: z.object({}),
    execute: async () => {
      return {
        timestamp: new Date().toISOString(),
        formatted: new Date().toLocaleString(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        success: true,
      };
    },
  }),

  search_documentation: tool({
    description: 'Search through development documentation and help resources',
    parameters: z.object({
      query: z.string().describe('The search query'),
      category: z.enum(['react', 'nextjs', 'typescript', 'nodejs', 'css', 'javascript', 'general']).optional(),
    }),
    execute: async ({ query, category }) => {
      const documentationService = DocumentationService.getInstance();

      try {
        const result = await documentationService.searchDocumentation({
          query,
          category: category as any,
          maxResults: 5,
          includeExamples: true
        });

        return result;
      } catch (error) {
        console.error('Documentation search error:', error);
        return {
          query,
          category: category || 'general',
          results: [],
          count: 0,
          success: false,
          sources: [],
          error: error instanceof Error ? error.message : 'Documentation search failed'
        };
      }
    },
  }),

  generate_code_snippet: tool({
    description: 'Generate a code snippet based on specific requirements',
    parameters: z.object({
      language: z.string().describe('Programming language (e.g., "typescript", "javascript", "css")'),
      description: z.string().describe('Description of what the code should do'),
      framework: z.string().optional().describe('Framework context (e.g., "react", "express", "nextjs")'),
      style: z.enum(['functional', 'class-based', 'hooks', 'minimal']).optional().describe('Code style preference'),
    }),
    execute: async ({ language, description, framework, style }) => {
      const codeGenerationService = CodeGenerationService.getInstance();

      try {
        const result = await codeGenerationService.generateCodeSnippet({
          language,
          description,
          framework,
          style,
          features: []
        });

        return {
          language: result.language,
          description: result.description,
          framework: result.framework,
          style: result.style,
          snippet: result.snippet,
          imports: result.imports,
          exports: result.exports,
          dependencies: result.dependencies,
          metadata: result.metadata,
          success: result.success,
        };
      } catch (error) {
        console.error('Code generation error:', error);
        return {
          language,
          description,
          framework,
          style,
          snippet: `// Error generating code: ${error instanceof Error ? error.message : 'Unknown error'}`,
          imports: [],
          exports: [],
          dependencies: [],
          success: false,
        };
      }
    },
  }),

  validate_code: tool({
    description: 'Validate code syntax and provide suggestions',
    parameters: z.object({
      code: z.string().describe('The code to validate'),
      language: z.string().describe('The programming language'),
      strict: z.boolean().optional().default(false).describe('Enable strict validation'),
    }),
    execute: async ({ code, language, strict }) => {
      const codeValidationService = CodeValidationService.getInstance();

      try {
        const result = await codeValidationService.validateCode({
          code,
          language,
          strict,
          checkSecurity: true,
          checkPerformance: true
        });

        return {
          language: result.language,
          valid: result.valid,
          issues: result.issues.map(issue => ({
            type: issue.type,
            message: issue.message,
            severity: issue.severity,
            line: issue.line,
            column: issue.column,
            rule: issue.rule,
            fixSuggestion: issue.fixSuggestion
          })),
          suggestions: result.suggestions,
          score: result.score,
          metrics: result.metrics,
          success: result.success,
        };
      } catch (error) {
        console.error('Code validation error:', error);
        return {
          language,
          valid: false,
          issues: [{
            type: 'error',
            message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            severity: 'high',
            rule: 'validation-error'
          }],
          suggestions: ['Fix validation errors and try again'],
          score: 0,
          success: false,
        };
      }
    },
  }),

  // Evaluator-Optimizer Pattern Tools
  evaluate_generated_code: tool({
    description: 'Evaluate generated code for quality, best practices, and project fit',
    parameters: z.object({
      filePath: z.string().describe('Path to the file to evaluate'),
      criteria: z.array(z.enum(['functionality', 'performance', 'security', 'maintainability', 'consistency'])).optional()
        .default(['functionality', 'maintainability', 'consistency'])
        .describe('Evaluation criteria to focus on'),
      strictMode: z.boolean().optional().default(false).describe('Enable strict evaluation mode'),
    }),
    execute: async ({ filePath, criteria, strictMode }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }

      try {
        const fileContent = await nodeboxToolHandlers.readFile(projectId, filePath);
        const codeEvaluationService = CodeEvaluationService.getInstance();

        // Detect language from file extension
        const language = detectLanguageFromPath(filePath);

        const result = await codeEvaluationService.evaluateCode(
          fileContent.content,
          language,
          criteria,
          strictMode
        );

        return {
          filePath,
          overallScore: result.overallScore,
          needsImprovement: result.needsImprovement,
          evaluation: result.evaluation,
          recommendations: result.recommendations,
          success: result.success,
          message: result.message
        };
      } catch (error) {
        throw new Error(`Failed to evaluate code: ${error instanceof Error ? error.message : String(error)}`);
      }
    },
  }),

  iterative_code_improvement: tool({
    description: 'Iteratively improve code based on evaluation feedback',
    parameters: z.object({
      filePath: z.string().describe('Path to the file to improve'),
      targetScore: z.number().min(70).max(100).optional().default(85).describe('Target quality score to achieve'),
      maxIterations: z.number().min(1).max(5).optional().default(3).describe('Maximum improvement iterations'),
      focusAreas: z.array(z.enum(['functionality', 'performance', 'security', 'maintainability', 'consistency'])).optional()
        .describe('Specific areas to focus improvements on'),
    }),
    execute: async ({ filePath, targetScore, maxIterations, focusAreas }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }

      try {
        const fileContent = await nodeboxToolHandlers.readFile(projectId, filePath);
        const codeEvaluationService = CodeEvaluationService.getInstance();
        const language = detectLanguageFromPath(filePath);

        const result = await codeEvaluationService.iterativelyImproveCode(
          fileContent.content,
          language,
          targetScore,
          maxIterations,
          focusAreas
        );

        // Write the improved code back to the file if successful
        if (result.success && result.iterations.length > 0) {
          const lastIteration = result.iterations[result.iterations.length - 1];
          if (lastIteration.codeChanges?.after) {
            await nodeboxToolHandlers.writeFile(
              projectId,
              filePath,
              lastIteration.codeChanges.after,
              {
                editMode: 'update',
                backupOnOverwrite: true,
                conflictResolution: 'replace'
              }
            );
          }
        }

        return {
          filePath,
          initialScore: result.initialScore,
          finalScore: result.finalScore,
          targetScore: result.targetScore,
          iterations: result.iterations,
          totalIterations: result.totalIterations,
          targetAchieved: result.targetAchieved,
          improvements: result.improvements,
          success: result.success,
          message: result.message
        };
      } catch (error) {
        throw new Error(`Failed to improve code iteratively: ${error instanceof Error ? error.message : String(error)}`);
      }
    },
  }),

  // Routing Pattern Tools
  analyze_task_complexity: tool({
    description: 'Analyze task complexity and determine the best approach for implementation',
    parameters: z.object({
      taskDescription: z.string().describe('Description of the task to analyze'),
      projectContext: z.string().optional().describe('Additional project context'),
    }),
    execute: async ({ taskDescription, projectContext }) => {
      const taskAnalysisService = TaskAnalysisService.getInstance();

      try {
        const result = await taskAnalysisService.analyzeTask(taskDescription, projectContext);

        return {
          taskDescription: result.taskDescription,
          complexity: result.complexity,
          taskType: result.taskType,
          recommendedApproach: result.recommendedApproach,
          estimatedSteps: result.estimatedSteps,
          estimatedTime: result.estimatedTime,
          recommendations: result.recommendations,
          toolsToUse: result.toolsToUse,
          dependencies: result.dependencies,
          riskFactors: result.riskFactors,
          success: result.success,
          message: result.message
        };
      } catch (error) {
        console.error('Task analysis error:', error);
        return {
          taskDescription,
          complexity: 'medium' as const,
          taskType: 'general' as const,
          recommendedApproach: 'sequential' as const,
          estimatedSteps: 3,
          estimatedTime: '10 minutes',
          recommendations: ['Analyze requirements', 'Implement solution', 'Validate results'],
          toolsToUse: ['write_file_nodebox', 'validate_code'],
          dependencies: [],
          riskFactors: ['Task analysis failed'],
          success: false,
          message: `Task analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
      }
    },
  }),

  // Orchestrator-Worker Pattern Tools
  orchestrate_complex_task: tool({
    description: 'Orchestrate a complex development task by breaking it into manageable subtasks',
    parameters: z.object({
      mainTask: z.string().describe('The main task to orchestrate'),
      requirements: z.array(z.string()).optional().describe('Specific requirements or constraints'),
      priority: z.enum(['low', 'medium', 'high']).optional().default('medium').describe('Task priority level'),
    }),
    execute: async ({ mainTask, requirements = [], priority }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }

      try {
        const orchestrationService = OrchestrationService.getInstance();

        const executionPlan = await orchestrationService.orchestrateComplexTask(
          mainTask,
          requirements,
          priority,
          projectId ? `Project: ${projectId}` : undefined
        );

        return {
          mainTask: executionPlan.mainTask,
          requirements: executionPlan.requirements,
          priority: executionPlan.priority,
          subtasks: executionPlan.subtasks,
          totalEstimatedTime: executionPlan.totalEstimatedTime,
          executionOrder: executionPlan.executionOrder,
          parallelizable: executionPlan.parallelizable,
          criticalPath: executionPlan.criticalPath,
          riskFactors: executionPlan.riskFactors,
          success: true,
          message: `Orchestration plan created for: ${mainTask}. ${executionPlan.subtasks.length} subtasks identified.`
        };
      } catch (error) {
        throw new Error(`Failed to orchestrate task: ${error instanceof Error ? error.message : String(error)}`);
      }
    },
  }),

  execute_subtask: tool({
    description: 'Execute a specific subtask as part of an orchestrated workflow',
    parameters: z.object({
      subtaskId: z.string().describe('ID of the subtask to execute'),
      subtaskName: z.string().describe('Name of the subtask'),
      context: z.object({
        mainTask: z.string(),
        previousResults: z.array(z.any()).optional(),
        requirements: z.array(z.string()).optional()
      }).describe('Context from the orchestration'),
    }),
    execute: async ({ subtaskId, subtaskName, context }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }

      try {
        const orchestrationService = OrchestrationService.getInstance();

        const result = await orchestrationService.executeSubtask(
          subtaskId,
          subtaskName,
          context,
          projectId
        );

        return {
          subtaskId: result.subtaskId,
          subtaskName: result.subtaskName,
          mainTask: result.mainTask,
          executionTime: result.executionTime,
          status: result.status,
          result: result.result,
          nextSteps: result.nextSteps,
          success: result.success,
          message: result.message
        };
      } catch (error) {
        throw new Error(`Failed to execute subtask: ${error instanceof Error ? error.message : String(error)}`);
      }
    },
  }),

  // Parallel Processing Pattern Tools
  identify_parallel_tasks: tool({
    description: 'Identify which parts of a complex task can be executed in parallel',
    parameters: z.object({
      mainTask: z.string().describe('The main task to analyze for parallelization'),
      subtasks: z.array(z.string()).optional().describe('List of subtasks if already identified'),
    }),
    execute: async ({ mainTask, subtasks = [] }) => {
      const parallelProcessingService = ParallelProcessingService.getInstance();

      try {
        const result = await parallelProcessingService.identifyParallelTasks(mainTask, subtasks);

        return {
          mainTask: result.mainTask,
          analysis: {
            independentTasks: result.independentTasks,
            dependentTasks: result.dependentTasks,
            parallelGroups: result.parallelGroups
          },
          optimizedExecution: result.optimizedExecution,
          timeReduction: result.timeReduction,
          parallelizationBenefit: result.parallelizationBenefit,
          recommendations: [
            'Execute independent file creation tasks in parallel',
            'Run styling and testing setup concurrently',
            'Maintain sequential order for dependent tasks'
          ],
          success: true,
          message: `Identified ${result.independentTasks.length} parallelizable tasks`
        };
      } catch (error) {
        console.error('Parallel task identification error:', error);
        return {
          mainTask,
          analysis: {
            independentTasks: [],
            dependentTasks: [mainTask],
            parallelGroups: []
          },
          optimizedExecution: {
            sequential: [mainTask],
            parallel: [],
            final: []
          },
          timeReduction: '0%',
          parallelizationBenefit: 'low' as const,
          recommendations: ['Task analysis failed - execute sequentially'],
          success: false,
          message: `Parallel task identification failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
      }
    },
  }),

  execute_parallel_tasks: tool({
    description: 'Execute multiple independent tasks in parallel for improved efficiency',
    parameters: z.object({
      taskGroup: z.object({
        groupId: z.string(),
        tasks: z.array(z.string()),
        context: z.any().optional()
      }).describe('Group of tasks to execute in parallel'),
      maxConcurrency: z.number().min(1).max(5).optional().default(3).describe('Maximum number of concurrent tasks'),
    }),
    execute: async ({ taskGroup, maxConcurrency }) => {
      if (!projectId) {
        throw new Error('No active project. Please create a project first.');
      }

      try {
        const parallelProcessingService = ParallelProcessingService.getInstance();

        const result = await parallelProcessingService.executeParallelTasks(
          taskGroup,
          maxConcurrency,
          projectId
        );

        return {
          groupId: result.groupId,
          tasksExecuted: result.tasksExecuted,
          maxConcurrency: result.maxConcurrency,
          executionResults: result.executionResults,
          totalExecutionTime: result.totalExecutionTime,
          averageTaskTime: result.averageTaskTime,
          successfulTasks: result.successfulTasks,
          failedTasks: result.failedTasks,
          parallelizationEfficiency: result.parallelizationEfficiency,
          success: result.success,
          message: result.message
        };
      } catch (error) {
        throw new Error(`Failed to execute parallel tasks: ${error instanceof Error ? error.message : String(error)}`);
      }
    },
  }),

  // Web Tools - Nodebox Compatible (using fetch API)
  scrape_webpage: tool({
    description: 'Fetch webpage content using Nodebox-compatible fetch API (limited to public APIs and CORS-enabled sites)',
    parameters: z.object({
      url: z.string().url().describe('The URL to fetch (must be CORS-enabled or a public API)'),
      method: z.enum(['GET', 'POST']).optional().default('GET').describe('HTTP method'),
      headers: z.record(z.string()).optional().describe('Custom headers'),
    }),
    execute: async ({ url, method, headers }) => {
      try {
        // Use Nodebox's fetch API (browser-based)
        const response = await fetch(url, {
          method,
          headers: {
            'User-Agent': 'Nodebox-Agent/1.0',
            ...headers
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type') || '';
        let content: string;

        if (contentType.includes('application/json')) {
          const jsonData = await response.json();
          content = JSON.stringify(jsonData, null, 2);
        } else {
          content = await response.text();
        }

        return {
          success: true,
          url,
          content: content.slice(0, 10000), // Limit content size
          status: response.status,
          statusText: response.statusText,
          contentType,
          timestamp: new Date().toISOString(),
          message: `Successfully fetched content from ${url}`,
          note: 'Limited to CORS-enabled sites and public APIs due to Nodebox browser constraints'
        };
      } catch (error) {
        console.error('Web fetch error:', error);
        return {
          success: false,
          url,
          content: '',
          status: 0,
          statusText: 'Error',
          contentType: '',
          timestamp: new Date().toISOString(),
          message: `Failed to fetch ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error',
          note: 'Nodebox runs in browser - only CORS-enabled sites and public APIs are accessible'
        };
      }
    },
  }),

  search_web: tool({
    description: 'Search for development resources and documentation using public APIs (Nodebox-compatible)',
    parameters: z.object({
      query: z.string().describe('Search query for development resources'),
      category: z.enum(['documentation', 'npm-packages', 'github-repos', 'stackoverflow']).optional().default('documentation').describe('Type of search'),
      maxResults: z.number().min(1).max(10).optional().default(5).describe('Maximum number of results'),
    }),
    execute: async ({ query, category, maxResults }) => {
      try {
        let results: any[] = [];
        let searchUrl = '';

        switch (category) {
          case 'npm-packages':
            searchUrl = `https://registry.npmjs.org/-/v1/search?text=${encodeURIComponent(query)}&size=${maxResults}`;
            break;
          case 'github-repos':
            // Note: GitHub API has CORS restrictions, this is a fallback
            results = [{
              title: 'GitHub Search',
              url: `https://github.com/search?q=${encodeURIComponent(query)}`,
              description: `Search GitHub for: ${query}`,
              note: 'Direct GitHub API access limited by CORS in browser environment'
            }];
            break;
          case 'stackoverflow':
            results = [{
              title: 'Stack Overflow Search',
              url: `https://stackoverflow.com/search?q=${encodeURIComponent(query)}`,
              description: `Search Stack Overflow for: ${query}`,
              note: 'Direct Stack Overflow API access limited by CORS in browser environment'
            }];
            break;
          default: // documentation
            results = [
              {
                title: 'MDN Web Docs',
                url: `https://developer.mozilla.org/en-US/search?q=${encodeURIComponent(query)}`,
                description: `Search MDN for: ${query}`
              },
              {
                title: 'React Documentation',
                url: `https://react.dev/search?q=${encodeURIComponent(query)}`,
                description: `Search React docs for: ${query}`
              },
              {
                title: 'Next.js Documentation',
                url: `https://nextjs.org/docs?search=${encodeURIComponent(query)}`,
                description: `Search Next.js docs for: ${query}`
              }
            ];
        }

        // Try to fetch NPM data if it's an npm search
        if (category === 'npm-packages' && searchUrl) {
          try {
            const response = await fetch(searchUrl);
            if (response.ok) {
              const data = await response.json();
              results = data.objects?.slice(0, maxResults).map((pkg: any) => ({
                title: pkg.package.name,
                url: `https://www.npmjs.com/package/${pkg.package.name}`,
                description: pkg.package.description || 'No description available',
                version: pkg.package.version,
                keywords: pkg.package.keywords || []
              })) || [];
            }
          } catch (fetchError) {
            console.warn('NPM search failed, using fallback');
            results = [{
              title: 'NPM Search',
              url: `https://www.npmjs.com/search?q=${encodeURIComponent(query)}`,
              description: `Search NPM for: ${query}`,
              note: 'Direct NPM API access failed, showing search link'
            }];
          }
        }

        return {
          success: true,
          query,
          category,
          results: results.slice(0, maxResults),
          totalResults: results.length,
          timestamp: new Date().toISOString(),
          message: `Found ${results.length} ${category} results for "${query}"`,
          note: 'Limited to CORS-enabled APIs and public search interfaces due to Nodebox browser constraints'
        };
      } catch (error) {
        console.error('Search error:', error);
        return {
          success: false,
          query,
          category,
          results: [],
          totalResults: 0,
          timestamp: new Date().toISOString(),
          message: `Failed to search for "${query}": ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error',
          note: 'Nodebox browser environment limits direct API access'
        };
      }
    },
  }),

  interact_with_webpage: tool({
    description: 'Limited webpage interaction using Nodebox fetch API (read-only access to public APIs)',
    parameters: z.object({
      url: z.string().url().describe('The URL to access (must be CORS-enabled)'),
      method: z.enum(['GET', 'POST']).optional().default('GET').describe('HTTP method'),
      data: z.record(z.any()).optional().describe('Data to send with POST requests'),
    }),
    execute: async ({ url, method, data }) => {
      try {
        const options: RequestInit = {
          method,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Nodebox-Agent/1.0'
          }
        };

        if (method === 'POST' && data) {
          options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type') || '';
        let content: any;

        if (contentType.includes('application/json')) {
          content = await response.json();
        } else {
          content = await response.text();
        }

        return {
          success: true,
          url,
          method,
          status: response.status,
          statusText: response.statusText,
          content: typeof content === 'string' ? content.slice(0, 5000) : content,
          contentType,
          timestamp: new Date().toISOString(),
          message: `Successfully ${method.toLowerCase()}ed ${url}`,
          note: 'Limited to CORS-enabled APIs - no DOM manipulation possible in Nodebox browser environment'
        };
      } catch (error) {
        console.error('Web interaction error:', error);
        return {
          success: false,
          url,
          method,
          status: 0,
          statusText: 'Error',
          content: '',
          contentType: '',
          timestamp: new Date().toISOString(),
          message: `Failed to access ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error',
          note: 'Nodebox browser environment limits web interactions to CORS-enabled APIs only'
        };
      }
    },
  }),

  take_screenshot: tool({
    description: 'Generate webpage preview links (screenshots not available in Nodebox browser environment)',
    parameters: z.object({
      url: z.string().url().describe('The URL to generate preview for'),
      description: z.string().optional().describe('Description of what to capture'),
    }),
    execute: async ({ url, description }) => {
      try {
        // Since we can't take actual screenshots in Nodebox, provide useful alternatives
        const previewServices = [
          {
            service: 'Web.dev Screenshot',
            url: `https://web.dev/screenshot/?url=${encodeURIComponent(url)}`,
            description: 'Generate screenshot using web.dev tools'
          },
          {
            service: 'URLBox Screenshot',
            url: `https://urlbox.io/screenshot?url=${encodeURIComponent(url)}`,
            description: 'Professional screenshot service'
          },
          {
            service: 'PageSpeed Insights',
            url: `https://pagespeed.web.dev/report?url=${encodeURIComponent(url)}`,
            description: 'Performance analysis with visual preview'
          }
        ];

        return {
          success: true,
          url,
          description: description || 'Website preview',
          previewServices,
          timestamp: new Date().toISOString(),
          message: `Generated preview options for ${url}`,
          note: 'Direct screenshot capture not available in Nodebox browser environment. Use external services or visit URL directly.',
          alternativeAction: `Visit ${url} directly in your browser for visual inspection`
        };
      } catch (error) {
        console.error('Screenshot generation error:', error);
        return {
          success: false,
          url,
          description: description || 'Website preview',
          previewServices: [],
          timestamp: new Date().toISOString(),
          message: `Failed to generate preview for ${url}`,
          error: error instanceof Error ? error.message : 'Unknown error',
          note: 'Screenshot functionality not available in Nodebox browser environment'
        };
      }
    },
  }),

  // Intelligent Nodebox Runtime Tools (Composing Real APIs)
  create_project_from_template: tool({
    description: 'Create a complete project from template using Nodebox filesystem and runtime',
    parameters: z.object({
      templateName: z.enum(['react-typescript', 'nextjs', 'nextjs-shadcn', 'express-api']).describe('Project template to use'),
      projectName: z.string().optional().describe('Custom project name'),
    }),
    execute: async ({ templateName, projectName }) => {
      console.log(`[create_project_from_template] Starting execution with:`, {
        templateName,
        projectName,
        projectId
      });

      // For create_project_from_template, we can create a new project if none exists
      let activeProjectId = projectId;

      if (!activeProjectId) {
        // Generate a new project ID for the template
        activeProjectId = `project-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        console.log(`[create_project_from_template] Creating new project with ID: ${activeProjectId}`);
      }

      try {
        // Define project templates for Nodebox filesystem
        const templates = {
          'react-typescript': {
            'package.json': JSON.stringify({
              name: projectName || 'react-typescript-app',
              version: '1.0.0',
              private: true,
              dependencies: {
                'react': '^18.2.0',
                'react-dom': '^18.2.0',
                'react-scripts': '5.0.1',
                'typescript': '^4.9.5',
                '@types/react': '^18.2.0',
                '@types/react-dom': '^18.2.0',
                '@types/node': '^16.18.0'
              },
              scripts: {
                'start': 'react-scripts start',
                'build': 'react-scripts build',
                'test': 'react-scripts test',
                'eject': 'react-scripts eject'
              },
              eslintConfig: {
                extends: ['react-app', 'react-app/jest']
              },
              browserslist: {
                production: ['>0.2%', 'not dead', 'not op_mini all'],
                development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version']
              }
            }, null, 2),
            'public/index.html': `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="React TypeScript App created with Nodebox" />
    <title>${projectName || 'React TypeScript App'}</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>`,
            'src/index.tsx': `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`,
            'src/App.tsx': `import React, { useState } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);

  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to ${projectName || 'React TypeScript App'}</h1>
        <p>
          Edit <code>src/App.tsx</code> and save to reload.
        </p>
        <button onClick={() => setCount(count + 1)}>
          Count: {count}
        </button>
      </header>
    </div>
  );
}

export default App;`,
            'src/App.css': `.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

button {
  background-color: #61dafb;
  border: none;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 5px;
  cursor: pointer;
  margin: 10px;
}

button:hover {
  background-color: #21a1c4;
}`,
            'src/index.css': `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}`,
            'tsconfig.json': JSON.stringify({
              compilerOptions: {
                target: 'es5',
                lib: ['dom', 'dom.iterable', 'es6'],
                allowJs: true,
                skipLibCheck: true,
                esModuleInterop: true,
                allowSyntheticDefaultImports: true,
                strict: true,
                forceConsistentCasingInFileNames: true,
                noFallthroughCasesInSwitch: true,
                module: 'esnext',
                moduleResolution: 'node',
                resolveJsonModule: true,
                isolatedModules: true,
                noEmit: true,
                jsx: 'react-jsx'
              },
              include: ['src']
            }, null, 2)
          },
          'nextjs': {
            'package.json': JSON.stringify({
              name: projectName || 'nextjs-app',
              version: '0.1.0',
              private: true,
              scripts: {
                dev: 'next dev',
                build: 'next build',
                start: 'next start',
                lint: 'next lint'
              },
              dependencies: {
                next: '14.0.0',
                react: '^18.2.0',
                'react-dom': '^18.2.0',
                typescript: '^5.0.0',
                '@types/node': '^20.0.0',
                '@types/react': '^18.2.0',
                '@types/react-dom': '^18.2.0'
              }
            }, null, 2),
            'app/layout.tsx': `import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: '${projectName || 'Next.js App'}',
  description: 'Generated by create next app',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}`,
            'app/page.tsx': `export default function Home() {
  return (
    <main style={{ padding: '2rem', textAlign: 'center' }}>
      <h1>Welcome to ${projectName || 'Next.js'}!</h1>
      <p>This is a Next.js application created with Nodebox.</p>
      <p>Edit <code>app/page.tsx</code> to get started.</p>
    </main>
  )
}`,
            'next.config.js': `/** @type {import('next').NextConfig} */
const nextConfig = {}

module.exports = nextConfig`,
            'tsconfig.json': JSON.stringify({
              compilerOptions: {
                target: 'es5',
                lib: ['dom', 'dom.iterable', 'es6'],
                allowJs: true,
                skipLibCheck: true,
                strict: true,
                noEmit: true,
                esModuleInterop: true,
                module: 'esnext',
                moduleResolution: 'bundler',
                resolveJsonModule: true,
                isolatedModules: true,
                jsx: 'preserve',
                incremental: true,
                plugins: [{ name: 'next' }],
                paths: { '@/*': ['./src/*'] }
              },
              include: ['next-env.d.ts', '**/*.ts', '**/*.tsx', '.next/types/**/*.ts'],
              exclude: ['node_modules']
            }, null, 2)
          },
          'nextjs-shadcn': {
            'package.json': JSON.stringify({
              name: projectName || 'nextjs-shadcn-app',
              version: '0.1.0',
              private: true,
              scripts: {
                dev: 'next dev',
                build: 'next build',
                start: 'next start',
                lint: 'next lint'
              },
              dependencies: {
                next: '14.0.0',
                react: '^18.2.0',
                'react-dom': '^18.2.0',
                typescript: '^5.0.0',
                '@types/node': '^20.0.0',
                '@types/react': '^18.2.0',
                '@types/react-dom': '^18.2.0',
                'tailwindcss': '^3.3.0',
                'autoprefixer': '^10.4.16',
                'postcss': '^8.4.31',
                'clsx': '^2.0.0',
                'tailwind-merge': '^2.0.0',
                'class-variance-authority': '^0.7.0',
                'lucide-react': '^0.294.0',
                '@radix-ui/react-accordion': '1.2.2',
                '@radix-ui/react-alert-dialog': '1.1.4',
                '@radix-ui/react-aspect-ratio': '1.1.1',
                '@radix-ui/react-avatar': '1.1.2',
                '@radix-ui/react-checkbox': '1.1.3',
                '@radix-ui/react-collapsible': '1.1.2',
                '@radix-ui/react-context-menu': '2.2.4',
                '@radix-ui/react-dialog': 'latest',
                '@radix-ui/react-dropdown-menu': '2.1.4',
                '@radix-ui/react-hover-card': '1.1.4',
                '@radix-ui/react-label': '2.1.1',
                '@radix-ui/react-menubar': '1.1.4',
                '@radix-ui/react-navigation-menu': '1.2.3',
                '@radix-ui/react-popover': '1.1.4',
                '@radix-ui/react-progress': '1.1.1',
                '@radix-ui/react-radio-group': '1.2.2',
                '@radix-ui/react-scroll-area': '1.2.2',
                '@radix-ui/react-select': '2.1.4',
                '@radix-ui/react-separator': '1.1.1',
                '@radix-ui/react-slider': '1.2.2',
                '@radix-ui/react-slot': '1.1.1',
                '@radix-ui/react-switch': '1.1.2',
                '@radix-ui/react-tabs': '1.1.2',
                '@radix-ui/react-toast': '1.2.4',
                '@radix-ui/react-toggle': '1.1.1',
                '@radix-ui/react-toggle-group': '1.1.1',
                '@radix-ui/react-tooltip': '1.1.6',
                'tailwindcss-animate': '^1.0.7'
              }
            }, null, 2),
            'components.json': JSON.stringify({
              "$schema": "https://ui.shadcn.com/schema.json",
              "style": "default",
              "rsc": true,
              "tsx": true,
              "tailwind": {
                "config": "tailwind.config.ts",
                "css": "app/globals.css",
                "baseColor": "slate",
                "cssVariables": true,
                "prefix": ""
              },
              "aliases": {
                "components": "@/components",
                "utils": "@/lib/utils",
                "ui": "@/components/ui",
                "lib": "@/lib",
                "hooks": "@/hooks"
              },
              "iconLibrary": "lucide"
            }, null, 2),
            'app/layout.tsx': `import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: '${projectName || 'Next.js Shadcn App'}',
  description: 'Generated by create next app with Shadcn/UI',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}`,
            'app/page.tsx': `import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function Home() {
  return (
    <main className="container mx-auto p-8">
      <div className="max-w-2xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Welcome to ${projectName || 'Next.js'}!</h1>
          <p className="text-muted-foreground">
            This is a Next.js application with Shadcn/UI components pre-configured.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>
              Your project is ready with Shadcn/UI components
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>Edit <code className="bg-muted px-2 py-1 rounded">app/page.tsx</code> to get started.</p>
            <div className="flex gap-2">
              <Button>Primary Button</Button>
              <Button variant="outline">Outline Button</Button>
              <Button variant="secondary">Secondary Button</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  )
}`,
            'app/globals.css': `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}`,
            'lib/utils.ts': `import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}`,
            'tailwind.config.ts': `import type { Config } from "tailwindcss"

const config: Config = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config

export default config`,
            'next.config.js': `/** @type {import('next').NextConfig} */
const nextConfig = {}

module.exports = nextConfig`,
            'tsconfig.json': JSON.stringify({
              compilerOptions: {
                target: 'es5',
                lib: ['dom', 'dom.iterable', 'es6'],
                allowJs: true,
                skipLibCheck: true,
                strict: true,
                noEmit: true,
                esModuleInterop: true,
                module: 'esnext',
                moduleResolution: 'bundler',
                resolveJsonModule: true,
                isolatedModules: true,
                jsx: 'preserve',
                incremental: true,
                plugins: [{ name: 'next' }],
                paths: { '@/*': ['./*'] }
              },
              include: ['next-env.d.ts', '**/*.ts', '**/*.tsx', '.next/types/**/*.ts'],
              exclude: ['node_modules']
            }, null, 2)
          },
          'express-api': {
            'package.json': JSON.stringify({
              name: projectName || 'express-api',
              version: '1.0.0',
              description: 'Express API created with Nodebox',
              main: 'server.js',
              scripts: {
                start: 'node server.js',
                dev: 'node server.js'
              },
              dependencies: {
                express: '^4.18.0',
                cors: '^2.8.5'
              }
            }, null, 2),
            'server.js': `const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to ${projectName || 'Express API'}!',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(\`🚀 Server running on port \${PORT}\`);
});`
          }
        };

        const template = templates[templateName];
        if (!template) {
          throw new Error(`Template '${templateName}' not found`);
        }

        console.log(`[create_project_from_template] Creating ${Object.keys(template).length} files for ${templateName}`);

        // Create files using Nodebox filesystem API through our tool handlers
        const filesCreated: string[] = [];
        for (const [filePath, content] of Object.entries(template)) {
          try {
            console.log(`[create_project_from_template] Creating file: ${filePath}`);
            await nodeboxToolHandlers.writeFile(activeProjectId, filePath, content, {
              editMode: 'create',
              backupOnOverwrite: false,
              conflictResolution: 'replace'
            });
            filesCreated.push(filePath);
          } catch (error) {
            console.error(`[create_project_from_template] Failed to create file ${filePath}:`, error);
            // Continue with other files
          }
        }

        console.log(`[create_project_from_template] Successfully created ${filesCreated.length} files`);

        return {
          success: true,
          projectId: activeProjectId,
          projectPath: '/',
          filesCreated,
          dependenciesInstalled: false, // Dependencies will be installed when running dev command
          templateUsed: templateName,
          projectName: projectName || templateName,
          message: `Successfully created ${templateName} project${projectName ? ` '${projectName}'` : ''} with ${filesCreated.length} files${!projectId ? ` (New project ID: ${activeProjectId})` : ''}`,
          nextSteps: [
            'Project files created successfully',
            !projectId ? `New project created with ID: ${activeProjectId}` : 'Project files updated',
            'Run the development server using the run_command tool',
            `For ${templateName === 'nextjs' || templateName === 'nextjs-shadcn' ? 'Next.js' : templateName === 'react-typescript' ? 'React' : 'Express'}: Use 'npm run ${templateName === 'express-api' ? 'start' : 'dev'}' command`,
            'Dependencies will be automatically installed by Nodebox when running commands'
          ],
          template: templateName,
          fileCount: filesCreated.length,
          isNewProject: !projectId
        };
      } catch (error) {
        console.error('[create_project_from_template] Error:', error);
        return {
          success: false,
          projectPath: '',
          filesCreated: [],
          dependenciesInstalled: false,
          templateUsed: templateName,
          message: `Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }


    },
  }),

  install_packages_intelligent: tool({
    description: 'Install packages using Nodebox-compatible package management (npm/yarn/pnpm)',
    parameters: z.object({
      packages: z.array(z.string()).describe('Packages to install'),
      dev: z.boolean().optional().default(false).describe('Install as dev dependencies'),
      packageManager: z.enum(['npm', 'yarn', 'pnpm']).optional().describe('Force specific package manager'),
    }),
    execute: async ({ packages, dev, packageManager }) => {
      if (!projectId) {
        return {
          success: false,
          packages,
          packageManager: packageManager || 'npm',
          dev,
          message: 'No active project. Please create a project first.',
          error: 'No project ID provided'
        };
      }

      try {
        // Use Nodebox-compatible package installation
        const pm = packageManager || 'npm';
        let command: string;
        let args: string[];

        switch (pm) {
          case 'yarn':
            command = 'yarn';
            args = ['add', ...packages];
            if (dev) args.push('--dev');
            break;
          case 'pnpm':
            command = 'pnpm';
            args = ['add', ...packages];
            if (dev) args.push('--save-dev');
            break;
          default: // npm
            command = 'npm';
            args = ['install', ...packages];
            if (dev) args.push('--save-dev');
        }

        console.log(`[install_packages_intelligent] Running: ${command} ${args.join(' ')}`);
        const result = await nodeboxToolHandlers.runCommand(projectId, command, args);

        return {
          success: result.success,
          packages,
          packageManager: pm,
          dev,
          processId: result.processId,
          status: result.status,
          command: `${command} ${args.join(' ')}`,
          message: result.success
            ? `Successfully installed ${packages.length} package${packages.length > 1 ? 's' : ''}: ${packages.join(', ')} using ${pm}`
            : `Failed to install packages using ${pm}`,
          note: 'Nodebox automatically manages dependencies and caching for optimal performance'
        };
      } catch (error) {
        console.error('Package installation error:', error);
        return {
          success: false,
          packages,
          packageManager: packageManager || 'npm',
          dev,
          command: `${packageManager || 'npm'} install ${packages.join(' ')}`,
          message: `Failed to install packages: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error',
          note: 'Package installation failed - check package names and network connectivity'
        };
      }
    },
  }),

  run_dev_server_with_preview: tool({
    description: 'Run development server using Nodebox shell and preview system',
    parameters: z.object({
      command: z.string().describe('Command to run (e.g., "npm run dev", "yarn start")'),
      port: z.number().optional().describe('Expected port for the development server'),
      environmentVariables: z.record(z.string()).optional().describe('Environment variables'),
    }),
    execute: async ({ command, port, environmentVariables }) => {
      if (!projectId) {
        return {
          success: false,
          command,
          port: port || 3000,
          message: 'No active project. Please create a project first.',
          error: 'No project ID provided'
        };
      }

      try {
        // Parse command into binary and args
        const commandParts = command.split(' ');
        const binary = commandParts[0];
        const args = commandParts.slice(1);

        console.log(`[run_dev_server_with_preview] Running: ${binary} ${args.join(' ')}`);

        // Use the basic run_command tool with environment variables
        const result = await nodeboxToolHandlers.runCommand(projectId, binary, args);

        // In Nodebox, the preview URL is typically generated automatically
        // when a development server starts on a port
        const expectedPort = port || 3000;
        const previewUrl = result.success ? `http://localhost:${expectedPort}` : undefined;

        return {
          success: result.success,
          command,
          binary,
          args,
          processId: result.processId,
          status: result.status,
          port: expectedPort,
          previewUrl,
          environmentVariables: environmentVariables || {},
          message: result.success
            ? `Development server started successfully. ${previewUrl ? `Preview should be available at ${previewUrl}` : 'Check Nodebox preview panel for the running application.'}`
            : `Failed to start development server`,
          note: 'Nodebox automatically manages preview URLs and port forwarding. Check the preview panel in your Nodebox interface.'
        };
      } catch (error) {
        console.error('Dev server error:', error);
        return {
          success: false,
          command,
          port: port || 3000,
          message: `Failed to start development server: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error',
          note: 'Development server failed to start. Check your package.json scripts and dependencies.'
        };
      }
    },
  }),

  get_available_templates: tool({
    description: 'Get list of available project templates for Nodebox',
    parameters: z.object({}),
    execute: async () => {
      try {
        // Return the templates that are actually available in our create_project_from_template tool
        const templates = [
          {
            name: 'react-typescript',
            description: 'React application with TypeScript, including React Scripts, CSS, and complete project structure',
            features: ['React 18', 'TypeScript', 'React Scripts', 'CSS styling', 'Hot reload'],
            files: ['package.json', 'public/index.html', 'src/index.tsx', 'src/App.tsx', 'src/App.css', 'src/index.css', 'tsconfig.json']
          },
          {
            name: 'nextjs',
            description: 'Next.js application with TypeScript and App Router',
            features: ['Next.js 14', 'TypeScript', 'App Router', 'Server-side rendering', 'API routes'],
            files: ['package.json', 'app/layout.tsx', 'app/page.tsx', 'next.config.js', 'tsconfig.json']
          },
          {
            name: 'nextjs-shadcn',
            description: 'Next.js application with Shadcn/UI pre-configured',
            features: ['Next.js 14', 'TypeScript', 'Tailwind CSS', 'Shadcn/UI', 'Pre-built components'],
            files: ['package.json', 'app/layout.tsx', 'app/page.tsx', 'components.json', 'tailwind.config.ts', 'lib/utils.ts', 'components/ui/*']
          },
          {
            name: 'express-api',
            description: 'Express.js API server with CORS support',
            features: ['Express.js', 'CORS middleware', 'JSON parsing', 'Health check endpoint'],
            files: ['package.json', 'server.js']
          }
        ];

        return {
          templates,
          count: templates.length,
          success: true,
          message: `Found ${templates.length} available Nodebox project templates`,
          note: 'These templates are optimized for Nodebox browser runtime environment'
        };
      } catch (error) {
        console.error('Template listing error:', error);
        return {
          templates: [],
          count: 0,
          success: false,
          message: `Failed to get templates: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
      }
    },
  }),

  // Shadcn UI Tools for Nodebox
  shadcn_init: tool({
    description: 'Initialize Shadcn/UI configuration in a Next.js project (Nodebox-compatible)',
    parameters: z.object({
      style: z.enum(['default', 'new-york']).optional().default('default').describe('UI style to use'),
      baseColor: z.enum(['slate', 'gray', 'zinc', 'neutral', 'stone']).optional().default('slate').describe('Base color for the theme'),
      cssVariables: z.boolean().optional().default(true).describe('Use CSS variables for theming'),
    }),
    execute: async ({ style, baseColor, cssVariables }) => {
      if (!projectId) {
        return {
          success: false,
          message: 'No active project. Please create a project first.',
          error: 'No project ID provided'
        };
      }

      try {
        console.log(`[shadcn_init] Initializing Shadcn/UI with style: ${style}, baseColor: ${baseColor}`);

        // Create components.json configuration
        const componentsConfig = {
          "$schema": "https://ui.shadcn.com/schema.json",
          "style": style,
          "rsc": true,
          "tsx": true,
          "tailwind": {
            "config": "tailwind.config.ts",
            "css": "app/globals.css",
            "baseColor": baseColor,
            "cssVariables": cssVariables,
            "prefix": ""
          },
          "aliases": {
            "components": "@/components",
            "utils": "@/lib/utils",
            "ui": "@/components/ui",
            "lib": "@/lib",
            "hooks": "@/hooks"
          },
          "iconLibrary": "lucide"
        };

        // Create lib/utils.ts
        const utilsContent = `import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}`;

        // Create basic Tailwind config
        const tailwindConfig = `import type { Config } from "tailwindcss"

const config: Config = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config

export default config`;

        // Create globals.css with Shadcn variables
        const globalsCss = `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}`;

        // Create files
        const filesCreated: string[] = [];

        // Create components.json
        await nodeboxToolHandlers.writeFile(projectId, 'components.json', JSON.stringify(componentsConfig, null, 2));
        filesCreated.push('components.json');

        // Create lib directory and utils.ts
        await nodeboxToolHandlers.createDirectory(projectId, 'lib');
        await nodeboxToolHandlers.writeFile(projectId, 'lib/utils.ts', utilsContent);
        filesCreated.push('lib/utils.ts');

        // Create/update tailwind.config.ts
        await nodeboxToolHandlers.writeFile(projectId, 'tailwind.config.ts', tailwindConfig);
        filesCreated.push('tailwind.config.ts');

        // Create/update globals.css
        await nodeboxToolHandlers.writeFile(projectId, 'app/globals.css', globalsCss);
        filesCreated.push('app/globals.css');

        // Create components/ui directory
        await nodeboxToolHandlers.createDirectory(projectId, 'components');
        await nodeboxToolHandlers.createDirectory(projectId, 'components/ui');
        filesCreated.push('components/ui/');

        return {
          success: true,
          style,
          baseColor,
          cssVariables,
          filesCreated,
          message: `Successfully initialized Shadcn/UI with ${style} style and ${baseColor} base color`,
          nextSteps: [
            'Shadcn/UI configuration is now ready',
            'Install required dependencies: npm install clsx tailwind-merge lucide-react',
            'Use shadcn_add_component tool to add UI components',
            'Components will be added to components/ui/ directory'
          ],
          note: 'Shadcn/UI initialized for Nodebox environment with pre-configured settings'
        };
      } catch (error) {
        console.error('[shadcn_init] Error:', error);
        return {
          success: false,
          style,
          baseColor,
          message: `Failed to initialize Shadcn/UI: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    },
  }),

  shadcn_add_component: tool({
    description: 'Add Shadcn/UI components to the project (Nodebox-compatible)',
    parameters: z.object({
      components: z.array(z.enum([
        'button', 'card', 'input', 'label', 'textarea', 'select', 'checkbox', 'radio-group',
        'switch', 'slider', 'progress', 'badge', 'avatar', 'alert', 'dialog', 'dropdown-menu',
        'popover', 'tooltip', 'tabs', 'accordion', 'collapsible', 'navigation-menu',
        'breadcrumb', 'pagination', 'table', 'form', 'sheet', 'toast', 'skeleton',
        'separator', 'scroll-area', 'resizable', 'toggle', 'toggle-group'
      ])).describe('List of Shadcn components to add'),
      overwrite: z.boolean().optional().default(false).describe('Overwrite existing components'),
    }),
    execute: async ({ components, overwrite }) => {
      if (!projectId) {
        return {
          success: false,
          message: 'No active project. Please create a project first.',
          error: 'No project ID provided'
        };
      }

      try {
        console.log(`[shadcn_add_component] Adding components: ${components.join(', ')}`);

        // Component templates (simplified versions for Nodebox)
        const componentTemplates: Record<string, string> = {
          button: `import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }`,

          card: `import * as React from "react"

import { cn } from "@/lib/utils"

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-lg border bg-card text-card-foreground shadow-sm",
      className
    )}
    {...props}
  />
))
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("flex flex-col space-y-1.5 p-6", className)} {...props} />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn("text-sm text-muted-foreground", className)} {...props} />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("flex items-center p-6 pt-0", className)} {...props} />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }`,

          input: `import * as React from "react"

import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }`,

          label: `import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const labelVariants = cva(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
)

const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
>(({ className, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants(), className)}
    {...props}
  />
))
Label.displayName = LabelPrimitive.Root.displayName

export { Label }`,

          badge: `import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }`
        };

        const filesCreated: string[] = [];
        const skippedComponents: string[] = [];

        for (const component of components) {
          const componentPath = `components/ui/${component}.tsx`;

          // Check if component exists and overwrite is false
          if (!overwrite) {
            try {
              await nodeboxToolHandlers.readFile(projectId, componentPath);
              skippedComponents.push(component);
              continue;
            } catch {
              // File doesn't exist, proceed with creation
            }
          }

          const template = componentTemplates[component];
          if (template) {
            await nodeboxToolHandlers.writeFile(projectId, componentPath, template);
            filesCreated.push(componentPath);
          } else {
            // Create a basic placeholder for components we don't have templates for
            const placeholderTemplate = `import * as React from "react"
import { cn } from "@/lib/utils"

// ${component.charAt(0).toUpperCase() + component.slice(1)} component
// This is a placeholder - implement according to your needs
export function ${component.charAt(0).toUpperCase() + component.slice(1)}({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("${component}", className)}
      {...props}
    />
  )
}`;
            await nodeboxToolHandlers.writeFile(projectId, componentPath, placeholderTemplate);
            filesCreated.push(componentPath);
          }
        }

        return {
          success: true,
          components,
          filesCreated,
          skippedComponents,
          overwrite,
          message: `Successfully added ${filesCreated.length} Shadcn components${skippedComponents.length > 0 ? `, skipped ${skippedComponents.length} existing components` : ''}`,
          nextSteps: [
            'Components have been added to components/ui/ directory',
            'Import and use components in your React components',
            'Install required dependencies if not already installed:',
            '  npm install @radix-ui/react-slot @radix-ui/react-label class-variance-authority',
            'Components are ready to use with proper TypeScript support'
          ],
          note: 'Components are optimized for Nodebox environment with essential functionality'
        };
      } catch (error) {
        console.error('[shadcn_add_component] Error:', error);
        return {
          success: false,
          components,
          filesCreated: [],
          message: `Failed to add Shadcn components: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    },
  }),

  // Hybrid Execution Tools
  shadcn_cli_native: tool({
    description: 'Execute native Shadcn CLI commands using hybrid execution environment',
    parameters: z.object({
      action: z.enum(['init', 'add', 'list', 'update', 'remove']).describe('Shadcn CLI action to perform'),
      components: z.array(z.string()).optional().describe('Components to add/update/remove'),
      options: z.object({
        style: z.enum(['default', 'new-york']).optional(),
        baseColor: z.enum(['slate', 'gray', 'zinc', 'neutral', 'stone']).optional(),
        cssVariables: z.boolean().optional(),
        overwrite: z.boolean().optional(),
        force: z.boolean().optional(),
      }).optional().describe('Additional options for the command'),
    }),
    execute: async ({ action, components, options = {} }) => {
      if (!projectId) {
        return {
          success: false,
          message: 'No active project. Please create a project first.',
          error: 'No project ID provided'
        };
      }

      try {
        // This would integrate with the HybridExecutor
        // For now, we'll simulate the behavior
        console.log(`[shadcn_cli_native] Executing ${action} with components:`, components);

        switch (action) {
          case 'init':
            // Simulate Shadcn init
            const initFiles = [
              'components.json',
              'lib/utils.ts',
              'tailwind.config.ts',
              'app/globals.css'
            ];

            for (const file of initFiles) {
              // Create the files using our existing tools
              if (file === 'components.json') {
                const componentsConfig = {
                  "$schema": "https://ui.shadcn.com/schema.json",
                  "style": options.style || "default",
                  "rsc": true,
                  "tsx": true,
                  "tailwind": {
                    "config": "tailwind.config.ts",
                    "css": "app/globals.css",
                    "baseColor": options.baseColor || "slate",
                    "cssVariables": options.cssVariables !== false,
                    "prefix": ""
                  },
                  "aliases": {
                    "components": "@/components",
                    "utils": "@/lib/utils",
                    "ui": "@/components/ui"
                  }
                };
                await nodeboxToolHandlers.writeFile(projectId, file, JSON.stringify(componentsConfig, null, 2));
              }
            }

            return {
              success: true,
              action,
              filesCreated: initFiles,
              message: `Successfully initialized Shadcn/UI with ${options.style || 'default'} style`,
              note: 'Using hybrid execution simulation - full native CLI integration coming soon'
            };

          case 'add':
            if (!components || components.length === 0) {
              throw new Error('No components specified for add action');
            }

            // Ensure directories exist first
            try {
              await nodeboxToolHandlers.createDirectory(projectId, 'components');
              await nodeboxToolHandlers.createDirectory(projectId, 'components/ui');
            } catch (error) {
              console.log('[shadcn_cli_native] Directory creation info:', error);
              // Continue - directories might already exist
            }

            const addedFiles: string[] = [];
            for (const component of components) {
              const componentPath = `components/ui/${component}.tsx`;
              // Create placeholder component file
              const componentContent = `// ${component} component - generated by Shadcn CLI simulation
import * as React from "react"
import { cn } from "@/lib/utils"

export function ${component.charAt(0).toUpperCase() + component.slice(1)}({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("${component}", className)}
      {...props}
    />
  )
}`;
              await nodeboxToolHandlers.writeFile(projectId, componentPath, componentContent);
              addedFiles.push(componentPath);
            }

            return {
              success: true,
              action,
              components,
              filesCreated: addedFiles,
              message: `Successfully added ${components.length} component(s): ${components.join(', ')}`,
              note: 'Using hybrid execution simulation - full native CLI integration coming soon'
            };

          case 'list':
            // Simulate listing available components
            const availableComponents = [
              'button', 'card', 'input', 'label', 'textarea', 'select',
              'checkbox', 'radio-group', 'switch', 'slider', 'progress',
              'badge', 'avatar', 'alert', 'dialog', 'dropdown-menu',
              'popover', 'tooltip', 'tabs', 'accordion', 'table'
            ];

            return {
              success: true,
              action,
              components: availableComponents,
              message: `Found ${availableComponents.length} available components`,
              note: 'Using hybrid execution simulation - full native CLI integration coming soon'
            };

          default:
            throw new Error(`Unsupported action: ${action}`);
        }
      } catch (error) {
        console.error('[shadcn_cli_native] Error:', error);
        return {
          success: false,
          action,
          components,
          message: `Failed to execute Shadcn CLI ${action}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error',
          note: 'This is a simulation - full hybrid execution environment coming soon'
        };
      }
    },
  }),

  hybrid_command_execute: tool({
    description: 'Execute any CLI command using hybrid execution environment (when available)',
    parameters: z.object({
      command: z.string().describe('Command to execute'),
      environment: z.enum(['auto', 'nodebox', 'container']).optional().default('auto').describe('Execution environment'),
      sync: z.boolean().optional().default(true).describe('Sync files after execution'),
      timeout: z.number().optional().default(300000).describe('Command timeout in milliseconds'),
    }),
    execute: async ({ command, environment, sync, timeout }) => {
      if (!projectId) {
        return {
          success: false,
          message: 'No active project. Please create a project first.',
          error: 'No project ID provided'
        };
      }

      try {
        console.log(`[hybrid_command_execute] Executing: ${command} in ${environment} environment`);

        // For now, route to existing Nodebox tools
        // In the future, this would use the HybridExecutor
        if (environment === 'nodebox' || environment === 'auto') {
          // Use existing Nodebox command execution
          const result = await nodeboxToolHandlers.runCommand(projectId, command);

          return {
            success: result.success,
            command,
            environment: 'nodebox',
            exitCode: result.success ? 0 : 1,
            stdout: result.success ? 'Command executed successfully' : '',
            stderr: result.success ? '' : 'Command failed',
            duration: 0,
            filesChanged: [],
            message: result.success
              ? `Successfully executed: ${command}`
              : `Failed to execute: ${command}`,
            note: 'Executed in Nodebox environment - hybrid execution coming soon'
          };
        } else {
          // Container execution simulation
          return {
            success: false,
            command,
            environment: 'container',
            exitCode: 1,
            stdout: '',
            stderr: 'Container execution not yet implemented',
            duration: 0,
            filesChanged: [],
            message: `Container execution not available yet for: ${command}`,
            note: 'Hybrid execution environment is in development'
          };
        }
      } catch (error) {
        console.error('[hybrid_command_execute] Error:', error);
        return {
          success: false,
          command,
          environment,
          exitCode: 1,
          stdout: '',
          stderr: error instanceof Error ? error.message : 'Unknown error',
          duration: 0,
          filesChanged: [],
          message: `Failed to execute command: ${error instanceof Error ? error.message : 'Unknown error'}`,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    },
  }),

  manage_shell_lifecycle: tool({
    description: 'Basic shell management for Nodebox (simplified for browser environment)',
    parameters: z.object({
      action: z.enum(['list', 'info']).describe('Action to perform'),
      processId: z.string().optional().describe('Process ID to get info about'),
    }),
    execute: async ({ action, processId }) => {
      try {
        switch (action) {
          case 'list':
            return {
              action,
              success: true,
              message: 'Shell management in Nodebox is handled automatically by the runtime',
              note: 'Nodebox manages shell processes automatically. Use run_command_nodebox to execute commands.',
              availableCommands: [
                'run_command_nodebox - Execute commands in Nodebox shell',
                'run_dev_server_with_preview - Start development servers',
                'install_packages_intelligent - Install npm packages'
              ]
            };

          case 'info':
            if (!processId) {
              throw new Error('Process ID required for info action');
            }
            return {
              action,
              processId,
              success: true,
              message: `Process info for ${processId}`,
              note: 'Detailed process information is managed internally by Nodebox runtime',
              suggestion: 'Use run_command_nodebox to check process status or run new commands'
            };

          default:
            throw new Error(`Unknown action: ${action}`);
        }
      } catch (error) {
        console.error('Shell management error:', error);
        return {
          action,
          success: false,
          message: `Failed to ${action}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          note: 'Nodebox handles shell lifecycle automatically in browser environment'
        };
      }
    },
  }),
});

// Combine all tools - this will be created dynamically in the POST function
export const createAllTools = (projectId?: string) => ({
  ...createNodeboxTools(projectId),
  ...createAllEnhancedTools(projectId || 'default'),
  ...createDevelopmentTools(projectId),
});

// Handle direct API actions (non-AI requests)
async function handleDirectAction(body: any) {
  const { action, projectId } = body;

  try {
    switch (action) {
      case 'get_project_info':
        return await handleGetProjectInfo(projectId);
      case 'get_filesystem_stats':
        return await handleGetFilesystemStats(projectId);
      default:
        return new Response(
          JSON.stringify({ error: `Unknown action: ${action}` }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
    }
  } catch (error) {
    console.error(`Error handling action ${action}:`, error);
    return new Response(
      JSON.stringify({
        error: `Failed to handle action: ${error instanceof Error ? error.message : String(error)}`
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

async function handleGetProjectInfo(projectId: string) {
  try {
    const result = await nodeboxToolHandlers.getProjectInfo(projectId);
    return new Response(
      JSON.stringify(result),
      { headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    throw new Error(`Failed to get project info: ${error instanceof Error ? error.message : String(error)}`);
  }
}

async function handleGetFilesystemStats(projectId: string) {
  try {
    // Get enhanced filesystem from the enhanced server store
    const { EnhancedServerNodeboxStore } = await import('@/lib/api/enhanced-server-nodebox-store');
    const filesystem = EnhancedServerNodeboxStore.getFilesystem(projectId);
    if (!filesystem) {
      return new Response(
        JSON.stringify({ entries: [] }),
        { headers: { 'Content-Type': 'application/json' } }
      );
    }

    const stats = filesystem.getStats();
    return new Response(
      JSON.stringify({ entries: stats.entries }),
      { headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    throw new Error(`Failed to get filesystem stats: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function POST(req: Request) {
  try {
    console.log('[Nodebox AI] Starting request processing...');

    const body = await req.json();

    // Handle direct API actions (non-AI requests)
    if (body.action) {
      return await handleDirectAction(body);
    }

    const { messages, projectId, instanceId, projectContext } = body;

    console.log(`[Nodebox AI] Processing request for project: ${projectId}`);
    console.log(`[Nodebox AI] Instance ID: ${instanceId}`);
    console.log(`[Nodebox AI] Project Context:`, projectContext);
    console.log(`[Nodebox AI] Messages count: ${messages?.length || 0}`);

    // Validate required parameters
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      console.error('[Nodebox AI] Invalid messages array');
      return new Response(
        JSON.stringify({
          error: 'Bad Request',
          message: 'Messages array is required and cannot be empty',
          timestamp: new Date().toISOString(),
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Create tools with projectId context - wrap in try-catch to catch tool creation errors
    console.log('[Nodebox AI] Creating tools...');
    let allTools;
    try {
      // For debugging, start with just a simple subset of tools
      const nodeboxTools = createNodeboxTools(projectId);
      console.log(`[Nodebox AI] Created ${Object.keys(nodeboxTools).length} nodebox tools`);

      // Test if enhanced tools are causing the issue
      let enhancedTools = {};
      try {
        enhancedTools = createAllEnhancedTools(projectId || 'default');
        console.log(`[Nodebox AI] Created ${Object.keys(enhancedTools).length} enhanced tools`);
      } catch (enhancedError) {
        console.error('[Nodebox AI] Error creating enhanced tools:', enhancedError);
        // Continue without enhanced tools for now
      }

      // Test if development tools are causing the issue
      let developmentTools = {};
      try {
        developmentTools = createDevelopmentTools(projectId);
        console.log(`[Nodebox AI] Created ${Object.keys(developmentTools).length} development tools`);
      } catch (devError) {
        console.error('[Nodebox AI] Error creating development tools:', devError);
        // Continue without development tools for now
      }

      allTools = {
        ...nodeboxTools,
        ...enhancedTools,
        ...developmentTools
      };

      console.log(`[Nodebox AI] Successfully created ${Object.keys(allTools).length} total tools`);
    } catch (toolError) {
      console.error('[Nodebox AI] Error creating tools:', toolError);
      return new Response(
        JSON.stringify({
          error: 'Tool Creation Error',
          message: `Failed to create tools: ${toolError instanceof Error ? toolError.message : 'Unknown error'}`,
          timestamp: new Date().toISOString(),
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Enhanced system prompt specifically for Nodebox development with agentic patterns
    const systemPrompt = `You are an advanced AI development assistant with comprehensive access to a Nodebox development environment. You specialize in modern web development and can directly manipulate files, run commands, and manage projects with advanced edit capabilities and intelligent agentic patterns.

## Your Core Capabilities:

### 🔧 Enhanced File Operations
- **read_file_nodebox**: Read any file in the project with metadata
- **write_file_nodebox**: Smart file creation/editing with conflict resolution and backup support
- **update_file_nodebox**: Update existing files with automatic backup creation
- **append_to_file_nodebox**: Append content to existing files
- **smart_merge_file_nodebox**: Intelligently merge new content with existing files
- **force_write_file_nodebox**: Force overwrite files with backup protection
- **create_directory_nodebox**: Create directories for project organization
- **delete_file_nodebox**: Remove unnecessary files
- **list_files_nodebox**: Explore project structure with rich metadata

### 🛡️ Advanced Edit Features
- **Conflict Resolution**: Automatically handles duplicate files by renaming or merging
- **Backup Management**: Creates backups before overwriting important files
- **Smart Merging**: Intelligently combines imports, functions, and content
- **Edit History**: Tracks all file modifications with timestamps
- **Path Normalization**: Prevents filesystem conflicts and ensures consistency

### 🧠 Context-Aware Generation
- **analyze_codebase_nodebox**: Analyze existing project patterns and conventions
- **generate_contextual_code_nodebox**: Generate code that follows project standards
- **generate_project_template_nodebox**: Create complete project templates from requirements
- **optimize_project_structure_nodebox**: Analyze and optimize project organization

### 🚀 Project Management
- **create_project_nodebox**: Create new projects from templates
- **get_project_info_nodebox**: Get detailed project information
- **run_command_nodebox**: Execute terminal commands (npm, build, test, etc.)

### 🤖 Agentic Patterns (NEW!)

#### Evaluator-Optimizer Pattern
- **evaluate_generated_code**: Evaluate code quality, best practices, and project fit
- **iterative_code_improvement**: Iteratively improve code based on evaluation feedback
- Use these to create feedback loops that automatically improve code quality

#### Routing Pattern
- **analyze_task_complexity**: Analyze task complexity and determine the best approach
- Automatically route simple tasks to direct implementation, complex tasks to orchestration

#### Orchestrator-Worker Pattern
- **orchestrate_complex_task**: Break complex tasks into manageable subtasks
- **execute_subtask**: Execute specific subtasks as part of orchestrated workflows
- Use for complex development tasks that require multiple coordinated steps

#### Parallel Processing Pattern
- **identify_parallel_tasks**: Identify which parts of a task can be executed in parallel
- **execute_parallel_tasks**: Execute multiple independent tasks concurrently for efficiency
- Use when tasks have independent components that can run simultaneously

### 📚 Available Templates
- **react**: Modern React app with TypeScript
- **nextjs**: Full-stack Next.js application
- **express**: Node.js Express server
- **typescript**: Pure TypeScript project
- **vanilla-js**: HTML/CSS/JavaScript project
- **research-dashboard**: Data visualization dashboard
- **data-analysis**: Data processing tools
- **custom**: Flexible custom setup

### 🛠️ Development Tools
- **search_documentation**: Find relevant documentation
- **generate_code_snippet**: Create code based on requirements
- **validate_code**: Check code quality and syntax

⚠️ **IMPORTANT**: DO NOT use get_current_time unless specifically asked for timestamps. Most development tasks don't need time information.

### 🌐 Web Tools (Browserless Integration)
- **scrape_webpage**: Extract content from any webpage with real browser automation
- **search_web**: Search Google, Bing, or DuckDuckGo with actual results
- **interact_with_webpage**: Click, type, scroll, and interact with web pages
- **take_screenshot**: Capture screenshots of any webpage

### 📦 Intelligent Nodebox Runtime Tools (Real API Composition)
- **create_project_from_template**: Create complete projects using real Nodebox APIs with templates
- **install_packages_intelligent**: Smart package installation with automatic package manager detection
- **run_dev_server_with_preview**: Start development servers with automatic preview management
- **get_available_templates**: List all available project templates
- **manage_shell_lifecycle**: Manage Nodebox shell processes (list, cleanup)

## Your Agentic Approach:

🚨 **CRITICAL: PREVENT INFINITE LOOPS**
- **NEVER call the same tool repeatedly without making progress**
- **NEVER call get_current_time unless specifically asked for timestamps**
- **Each tool call must have a clear purpose that advances the user's goal**
- **If a tool doesn't provide useful information, don't call it again**

1. **Analyze First**: Understand the user's request directly - don't use tools unless necessary
2. **Route Intelligently**: Simple tasks → direct implementation, Complex tasks → orchestration
3. **Be Purposeful**: Every tool call must directly contribute to solving the user's request
4. **Evaluate Continuously**: Use evaluation tools only when quality assessment is needed
5. **Iterate When Needed**: Use iterative improvement for code that doesn't meet quality thresholds
6. **Provide Transparency**: Always explain your reasoning and approach

## Pattern Selection Guidelines:
- **Single-Step**: Simple file creation, basic code generation
- **Sequential**: Multi-file projects, step-by-step implementations
- **Evaluator-Optimizer**: When code quality is critical, iterative refinement needed
- **Routing**: When task complexity varies, need to choose different approaches
- **Orchestrator-Worker**: Complex features, multi-component systems, full applications
- **Parallel Processing**: Independent file creation, concurrent component development, batch operations

## Current Context:
${projectId ? `- Active Project ID: ${projectId}` : '- No active project (suggest creating one with create_project_nodebox)'}
${instanceId ? `- Active Instance ID: ${instanceId}` : '- No active instance'}
${projectContext ? `- Active Instance: ${projectContext.activeInstance || 'None'}
- Template: ${projectContext.template || 'Unknown'}
- Instance Status: ${projectContext.status || 'Unknown'}` : ''}
- All Nodebox tools and agentic patterns are available
- File changes will be reflected in real-time in the user's interface
- You can execute commands and see their output
- Quality evaluation and iterative improvement are enabled
- Enhanced context awareness for better project understanding

## Code Quality Standards:
- Use TypeScript for type safety
- Follow modern React patterns (hooks, functional components)
- Implement proper error handling
- Write clean, readable, and maintainable code
- Include helpful comments for complex logic
- Follow consistent naming conventions
- Organize code with clear separation of concerns
- Aim for 85+ quality scores in evaluations

When users ask for development tasks, first analyze the complexity, choose the appropriate agentic pattern, and execute with continuous evaluation and improvement.`;

    console.log('[Nodebox AI] Creating streamText with OpenAI...');

    // Check if OpenAI API key is available
    if (!process.env.OPENAI_API_KEY) {
      console.error('[Nodebox AI] OPENAI_API_KEY not found in environment variables');
      return new Response(
        JSON.stringify({
          error: 'Configuration Error',
          message: 'OpenAI API key not configured',
          timestamp: new Date().toISOString(),
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Create all available tools
    const nodeboxTools = createNodeboxTools(projectId);
    const enhancedTools = createAllEnhancedTools(projectId || 'default');
    const developmentTools = createDevelopmentTools(projectId);

    // Combine all tools
    const allAvailableTools = {
      ...nodeboxTools,
      ...enhancedTools,
      ...developmentTools
    };

    console.log(`[Nodebox AI] Created ${Object.keys(allAvailableTools).length} total tools`);

    // Define core tools that should always be active (most commonly used)
    const coreToolNames = [
      'create_file_nodebox',
      'read_file_nodebox',
      'update_file_nodebox',
      'delete_file_nodebox',
      'list_files_nodebox',
      'run_command_nodebox',
      'install_package',
      'search_documentation',
      'generate_code_snippet',
      'create_project_from_template', // Re-enabled with simplified implementation
      'search_web', // Nodebox-compatible web search
      'scrape_webpage', // Nodebox-compatible web scraping
      'run_dev_server_with_preview', // Nodebox development server
      'get_available_templates', // Template information
      'shadcn_init', // Initialize Shadcn/UI configuration
      'shadcn_add_component', // Add Shadcn/UI components
      'shadcn_cli_native', // Native Shadcn CLI integration
      'hybrid_command_execute' // Hybrid command execution
    ];

    // Filter core tools that actually exist
    const activeTools = Object.fromEntries(
      Object.entries(allAvailableTools).filter(([name]) => coreToolNames.includes(name))
    );

    console.log(`[Nodebox AI] Using ${Object.keys(activeTools).length} active tools: ${Object.keys(activeTools).join(', ')}`);

    let result;
    try {
      result = streamText({
        model: openai('gpt-4o-mini'),
        messages: [
          {
            role: 'system',
            content: systemPrompt,
          },
          ...messages,
        ],
        // Use only core active tools to prevent overload
        tools: activeTools,
        // Be more conservative with tool usage
        toolChoice: 'auto',
        maxSteps: 10, // Reduced to prevent infinite loops
        temperature: 0.7,
        onStepFinish: (step) => {
          console.log('Nodebox AI Step finished:', {
            stepType: step.stepType,
            toolCalls: step.toolCalls?.length || 0,
            toolResults: step.toolResults?.length || 0,
            projectId,
          });
        },
      });
      console.log('[Nodebox AI] streamText created successfully');
    } catch (streamError) {
      console.error('[Nodebox AI] Error creating streamText:', streamError);
      return new Response(
        JSON.stringify({
          error: 'Stream Creation Error',
          message: `Failed to create AI stream: ${streamError instanceof Error ? streamError.message : 'Unknown error'}`,
          timestamp: new Date().toISOString(),
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    console.log('[Nodebox AI] Returning data stream response...');
    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Nodebox AI Chat API error:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
