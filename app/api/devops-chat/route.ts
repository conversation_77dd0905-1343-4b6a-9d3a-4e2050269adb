/**
 * API route for <PERSON><PERSON><PERSON> agentic assistant
 * Specializes in Docker, Firecracker, and general DevOps tasks
 */

import { streamText } from 'ai';
import { z } from 'zod';
import { createEnhancedModel } from '@/lib/ai-middleware';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { vmService } from '@/lib/devops/vm-service';

const execAsync = promisify(exec);

// Define the maximum duration for streaming responses
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages } = await req.json();

  const result = streamText({
    model: createEnhancedModel('gpt-4o'),
    system: `## Core Identity
    You are an expert DevOps engineer assistant that helps users with Docker, Firecracker, and general DevOps tasks through an agentic workflow.

    ## CRITICAL INSTRUCTION
    ALWAYS USE THE PROVIDED TOOLS TO PERFORM OPERATIONS. DO NOT JUST PROVIDE CODE SNIPPETS OR COMMANDS IN YOUR RESPONSE.
    When a user asks you to create configurations or perform operations, you MUST use the appropriate tools.

    ## Expertise and Capabilities
    - You specialize in Docker, containerization, and microservices architecture
    - You have deep knowledge of Firecracker microVMs and lightweight virtualization
    - You understand CI/CD pipelines, infrastructure as code, and automation
    - You can generate complete, production-ready configuration files
    - You can create and modify files in a project structure using the provided tools
    - You can execute commands safely in isolated environments
    - You can create, manage, and interact with virtual machines
    - You can provide explanations and documentation for the configurations you generate

    ## Configuration Generation Guidelines
    1. Use modern Docker best practices with multi-stage builds when appropriate
    2. Follow security best practices for containerization
    3. Organize configurations with clear separation of concerns
    4. Implement resource constraints and limits appropriately
    5. Use environment variables for configuration
    6. Provide clear comments for complex configurations
    7. When generating files, ensure they are properly structured with all necessary components
    8. Use consistent naming conventions
    9. Implement proper error handling and logging
    10. Consider performance optimization in your configurations

    ## File Structure and Organization
    - Use a modular approach with separate files for different services
    - Follow industry standard conventions for configuration files
    - Organize files by service or domain when appropriate
    - Keep related configurations close together
    - Use descriptive names for files and directories

    ## Tool Usage Guidelines

    ### IMPORTANT: ALWAYS USE TOOLS FOR OPERATIONS
    - When a user asks you to create configurations, ALWAYS use the generateFile tool
    - When a user asks you to execute commands, ALWAYS use the executeCommand tool
    - DO NOT just provide code snippets or commands in your response
    - ALWAYS generate complete, working implementations using the tools
    - After generating files or executing commands, explain what you've created and how it works

    ### generateFile Tool
    - Use this tool to create new configuration files with complete implementations
    - Always include proper syntax and required fields
    - Structure the file according to best practices for its type
    - Include helpful comments for complex configurations
    - REQUIRED USAGE:
      - When creating Dockerfiles
      - When creating docker-compose.yml files
      - When creating Firecracker configurations
      - When creating CI/CD pipeline configurations
      - When creating any configuration file requested by the user

    ### updateFile Tool
    - Use this tool to modify existing configuration files
    - Preserve the existing structure and style of the file
    - Make minimal changes necessary to implement the requested feature
    - Ensure backward compatibility when possible
    - REQUIRED USAGE:
      - When modifying existing configuration files
      - When adding features to existing configurations
      - When fixing issues in existing configurations

    ### executeCommand Tool
    - Use this tool to execute Docker, Firecracker, or other DevOps commands
    - Always validate commands before execution
    - Provide clear explanations of what the command does
    - Handle errors gracefully
    - REQUIRED USAGE:
      - When the user asks to run Docker commands
      - When the user asks to execute general DevOps operations
      - When you need to run system commands

    ### listFiles Tool
    - Use this tool to explore the project structure
    - Understand the organization before making changes
    - Identify related files that might need updates
    - REQUIRED USAGE:
      - Before making changes to understand the project structure
      - When you need to find related configuration files

    ### createVM Tool
    - Use this tool to create new Firecracker microVMs
    - Specify CPU, memory, and disk requirements
    - Configure networking options
    - REQUIRED USAGE:
      - When the user asks to create a new VM
      - When the user wants to set up a new environment

    ### listVMs Tool
    - Use this tool to list all VMs and their status
    - Get information about running VMs
    - REQUIRED USAGE:
      - When the user asks about existing VMs
      - Before performing operations on VMs

    ### startVM Tool
    - Use this tool to start a stopped VM
    - REQUIRED USAGE:
      - When the user asks to start a VM

    ### stopVM Tool
    - Use this tool to stop a running VM
    - REQUIRED USAGE:
      - When the user asks to stop a VM

    ### deleteVM Tool
    - Use this tool to delete a VM
    - REQUIRED USAGE:
      - When the user asks to delete or remove a VM

    ### executeVMCommand Tool
    - Use this tool to execute commands inside a VM
    - REQUIRED USAGE:
      - When the user asks to run commands in a VM
      - When you need to configure or check something inside a VM

    ### getVMStatus Tool
    - Use this tool to get detailed status of a VM
    - REQUIRED USAGE:
      - When the user asks about VM status
      - When you need detailed information about a VM

    ### createGuacamoleConnection Tool
    - Use this tool to create a remote desktop connection to a VM
    - Supports VNC, RDP, and SSH protocols
    - REQUIRED USAGE:
      - When the user asks to connect to a VM
      - When the user wants to access a VM's desktop or console

    ### getGuacamoleConnection Tool
    - Use this tool to get details about an existing Guacamole connection
    - REQUIRED USAGE:
      - When you need information about a specific connection

    ### listGuacamoleConnections Tool
    - Use this tool to list all active Guacamole connections
    - REQUIRED USAGE:
      - When the user asks about existing connections
      - Before creating a new connection to check for duplicates

    ### deleteGuacamoleConnection Tool
    - Use this tool to delete a Guacamole connection
    - REQUIRED USAGE:
      - When the user asks to disconnect from a VM
      - When cleaning up unused connections

    ## Agent Capabilities

    As an agentic DevOps assistant, you have the ability to:

    1. **Break down complex tasks** into smaller, manageable steps
    2. **Use tools iteratively** to solve problems across multiple steps
    3. **Maintain context** throughout a multi-step interaction
    4. **Adapt your approach** based on intermediate results
    5. **Provide structured answers** with explanations of your process

    ### Multi-Step Problem Solving

    When faced with a complex DevOps task:

    1. First, understand the requirements thoroughly
    2. Plan your approach by breaking down the task into logical steps
    3. Use the appropriate tools in each step to gather information or perform operations
    4. Evaluate intermediate results and adjust your approach as needed
    5. Provide a comprehensive solution with explanations of your process

    ### Structured Output

    When providing your final solution:

    1. Organize your response with clear sections
    2. Explain your reasoning and approach
    3. Provide the generated configuration files with proper context
    4. Include any necessary setup or usage instructions
    5. Suggest next steps or improvements

    Remember that you are helping users with real DevOps tasks, so focus on generating configurations and executing commands that are not only functional but also secure, maintainable, and follow industry best practices.`,
    messages,
    temperature: 0.7,
    toolCallStreaming: true,
    tools: {
      generateFile: {
        description: 'Generate a new file with the specified content',
        parameters: z.object({
          filePath: z.string().describe('The path of the file to generate'),
          content: z.string().describe('The content of the file'),
          description: z.string().optional().describe('A brief description of what the file does'),
        }),
        execute: async ({ filePath, content, description }) => {
          try {
            // Ensure the directory exists
            const directory = path.dirname(filePath);
            await fs.mkdir(directory, { recursive: true });

            // Write the file
            await fs.writeFile(filePath, content, 'utf8');

            console.log(`File generated: ${filePath}`);

            // Get file stats
            const stats = await fs.stat(filePath);

            return {
              success: true,
              filePath,
              message: `File ${filePath} has been generated.`,
              description: description || 'File generated successfully',
              size: stats.size,
              created: stats.birthtime,
            };
          } catch (error) {
            console.error(`Error generating file ${filePath}:`, error);
            return {
              success: false,
              filePath,
              message: `Error generating file: ${(error as Error).message}`,
            };
          }
        },
      },
      updateFile: {
        description: 'Update an existing file with new content',
        parameters: z.object({
          filePath: z.string().describe('The path of the file to update'),
          content: z.string().describe('The new content of the file'),
          description: z.string().optional().describe('A brief description of the changes made'),
        }),
        execute: async ({ filePath, content, description }) => {
          try {
            // Check if file exists
            try {
              await fs.access(filePath);
            } catch {
              return {
                success: false,
                filePath,
                message: `File ${filePath} does not exist.`,
              };
            }

            // Read the original content for comparison
            const originalContent = await fs.readFile(filePath, 'utf8');

            // Write the updated content
            await fs.writeFile(filePath, content, 'utf8');

            console.log(`File updated: ${filePath}`);

            // Calculate diff stats
            const additions = content.split('\n').length - originalContent.split('\n').length;
            const changePercentage = Math.round(
              (Math.abs(content.length - originalContent.length) / originalContent.length) * 100
            );

            return {
              success: true,
              filePath,
              message: `File ${filePath} has been updated.`,
              description: description || 'File updated successfully',
              changeStats: {
                lineChanges: additions > 0 ? `+${additions}` : additions,
                changePercentage: `${changePercentage}%`,
              },
            };
          } catch (error) {
            console.error(`Error updating file ${filePath}:`, error);
            return {
              success: false,
              filePath,
              message: `Error updating file: ${(error as Error).message}`,
            };
          }
        },
      },
      executeCommand: {
        description: 'Execute a Docker, Firecracker, or other DevOps command',
        parameters: z.object({
          command: z.string().describe('The command to execute'),
          workingDirectory: z.string().optional().describe('The working directory for the command'),
          timeout: z.number().optional().describe('Timeout in milliseconds'),
          description: z.string().optional().describe('A brief description of what the command does'),
        }),
        execute: async ({ command, workingDirectory, timeout, description }) => {
          try {
            // Validate command for security
            const forbiddenCommands = ['rm -rf', 'sudo', 'chmod 777', '> /dev/', '| sh', 'curl | bash'];
            if (forbiddenCommands.some(cmd => command.includes(cmd))) {
              return {
                success: false,
                command,
                message: 'Command contains potentially dangerous operations and was blocked for security.',
                output: '',
                error: 'Security validation failed',
              };
            }

            console.log(`Executing command: ${command}`);

            // Execute the command
            const { stdout, stderr } = await execAsync(command, {
              cwd: workingDirectory || process.cwd(),
              timeout: timeout || 30000,
            });

            return {
              success: true,
              command,
              message: `Command executed successfully.`,
              description: description || 'Command executed successfully',
              output: stdout,
              error: stderr || null,
            };
          } catch (error) {
            console.error(`Error executing command: ${command}`, error);
            return {
              success: false,
              command,
              message: `Error executing command: ${(error as Error).message}`,
              output: '',
              error: (error as Error).message,
            };
          }
        },
      },
      listFiles: {
        description: 'List files in a directory',
        parameters: z.object({
          directory: z.string().describe('The directory to list files from'),
        }),
        execute: async ({ directory }) => {
          try {
            // Ensure the directory exists
            try {
              await fs.access(directory);
            } catch {
              // If directory doesn't exist, create it
              await fs.mkdir(directory, { recursive: true });
            }

            // Read directory contents
            const entries = await fs.readdir(directory, { withFileTypes: true });

            // Map entries to file objects
            const files = await Promise.all(
              entries.map(async (entry) => {
                const entryPath = path.join(directory, entry.name);
                const stats = await fs.stat(entryPath);

                return {
                  name: entry.name,
                  path: entryPath,
                  type: entry.isDirectory() ? 'directory' : 'file',
                  size: entry.isFile() ? stats.size : null,
                  modified: stats.mtime,
                };
              })
            );

            console.log(`Listed ${files.length} files in ${directory}`);

            return {
              directory,
              files,
              count: files.length,
            };
          } catch (error) {
            console.error(`Error listing files in ${directory}:`, error);
            return {
              success: false,
              directory,
              message: `Error listing files: ${(error as Error).message}`,
              files: [],
            };
          }
        },
      },

      // VM Management Tools
      createVM: {
        description: 'Create a new Firecracker microVM',
        parameters: z.object({
          name: z.string().describe('Name of the VM'),
          description: z.string().optional().describe('Description of the VM'),
          vcpu: z.number().describe('Number of virtual CPUs'),
          memoryMb: z.number().describe('Memory in MB'),
          diskSizeGb: z.number().describe('Disk size in GB'),
          image: z.string().optional().describe('Base image to use'),
          networkConfig: z.object({
            enableInternet: z.boolean().optional().describe('Enable internet access'),
            forwardedPorts: z.array(
              z.object({
                hostPort: z.number().describe('Port on the host'),
                vmPort: z.number().describe('Port on the VM')
              })
            ).optional().describe('Ports to forward from host to VM')
          }).optional().describe('Network configuration')
        }),
        execute: async (params) => {
          try {
            console.log(`Creating VM: ${params.name}`);
            const vm = await vmService.createVM(params);

            return {
              success: true,
              vmId: vm.id,
              name: vm.name,
              status: vm.status,
              ip: vm.ip,
              message: `VM ${params.name} (${vm.id}) created successfully`,
            };
          } catch (error) {
            console.error(`Error creating VM:`, error);
            return {
              success: false,
              message: `Error creating VM: ${(error as Error).message}`,
            };
          }
        },
      },

      listVMs: {
        description: 'List all VMs',
        parameters: z.object({}),
        execute: async () => {
          try {
            console.log('Listing VMs');
            const vms = await vmService.listVMs();

            return {
              success: true,
              vms: vms.map(vm => ({
                id: vm.id,
                name: vm.name || vm.id,
                status: vm.status,
                ip: vm.ip,
                createdAt: vm.createdAt,
              })),
              count: vms.length,
            };
          } catch (error) {
            console.error(`Error listing VMs:`, error);
            return {
              success: false,
              message: `Error listing VMs: ${(error as Error).message}`,
              vms: [],
            };
          }
        },
      },

      startVM: {
        description: 'Start a VM',
        parameters: z.object({
          vmId: z.string().describe('ID of the VM to start'),
        }),
        execute: async ({ vmId }) => {
          try {
            console.log(`Starting VM: ${vmId}`);
            const vm = await vmService.startVM(vmId);

            return {
              success: true,
              vmId: vm.id,
              status: vm.status,
              message: `VM ${vmId} started successfully`,
            };
          } catch (error) {
            console.error(`Error starting VM:`, error);
            return {
              success: false,
              message: `Error starting VM: ${(error as Error).message}`,
            };
          }
        },
      },

      stopVM: {
        description: 'Stop a VM',
        parameters: z.object({
          vmId: z.string().describe('ID of the VM to stop'),
        }),
        execute: async ({ vmId }) => {
          try {
            console.log(`Stopping VM: ${vmId}`);
            const vm = await vmService.stopVM(vmId);

            return {
              success: true,
              vmId: vm.id,
              status: vm.status,
              message: `VM ${vmId} stopped successfully`,
            };
          } catch (error) {
            console.error(`Error stopping VM:`, error);
            return {
              success: false,
              message: `Error stopping VM: ${(error as Error).message}`,
            };
          }
        },
      },

      deleteVM: {
        description: 'Delete a VM',
        parameters: z.object({
          vmId: z.string().describe('ID of the VM to delete'),
        }),
        execute: async ({ vmId }) => {
          try {
            console.log(`Deleting VM: ${vmId}`);
            await vmService.deleteVM(vmId);

            return {
              success: true,
              vmId,
              message: `VM ${vmId} deleted successfully`,
            };
          } catch (error) {
            console.error(`Error deleting VM:`, error);
            return {
              success: false,
              message: `Error deleting VM: ${(error as Error).message}`,
            };
          }
        },
      },

      executeVMCommand: {
        description: 'Execute a command in a VM',
        parameters: z.object({
          vmId: z.string().describe('ID of the VM'),
          command: z.string().describe('Command to execute'),
          timeout: z.number().optional().describe('Timeout in milliseconds'),
        }),
        execute: async ({ vmId, command, timeout }) => {
          try {
            console.log(`Executing command in VM ${vmId}: ${command}`);
            const result = await vmService.executeVMCommand({ vmId, command, timeout });

            return {
              success: true,
              vmId,
              command,
              output: result.output,
              error: result.error,
              message: result.error
                ? `Command executed with errors`
                : `Command executed successfully`,
            };
          } catch (error) {
            console.error(`Error executing command in VM:`, error);
            return {
              success: false,
              message: `Error executing command in VM: ${(error as Error).message}`,
            };
          }
        },
      },

      getVMStatus: {
        description: 'Get detailed status of a VM',
        parameters: z.object({
          vmId: z.string().describe('ID of the VM'),
        }),
        execute: async ({ vmId }) => {
          try {
            console.log(`Getting status for VM: ${vmId}`);
            const status = await vmService.getVMStatus(vmId);

            return {
              success: true,
              vmId,
              status: status.status,
              uptime: status.uptime,
              cpuUsage: status.cpuUsage,
              memoryUsage: status.memoryUsage,
              diskUsage: status.diskUsage,
              networkStats: status.networkStats,
              message: `VM status retrieved successfully`,
            };
          } catch (error) {
            console.error(`Error getting VM status:`, error);
            return {
              success: false,
              message: `Error getting VM status: ${(error as Error).message}`,
            };
          }
        },
      },

      // Guacamole Connection Tools
      createGuacamoleConnection: {
        description: 'Create a Guacamole remote desktop connection to a VM',
        parameters: z.object({
          vmId: z.string().describe('ID of the VM to connect to'),
          protocol: z.enum(['vnc', 'rdp', 'ssh']).describe('Connection protocol'),
          username: z.string().optional().describe('Username for authentication'),
          password: z.string().optional().describe('Password for authentication'),
          port: z.number().optional().describe('Port number (defaults: VNC=5900, RDP=3389, SSH=22)'),
          width: z.number().optional().describe('Screen width in pixels'),
          height: z.number().optional().describe('Screen height in pixels'),
        }),
        execute: async (params) => {
          try {
            console.log(`Creating Guacamole connection to VM: ${params.vmId}`);
            const connection = await vmService.createGuacamoleConnection(params);

            return {
              success: true,
              connectionId: connection.connectionId,
              protocol: connection.protocol,
              webSocketUrl: connection.webSocketUrl,
              message: `Guacamole connection created successfully`,
              vmId: params.vmId,
            };
          } catch (error) {
            console.error(`Error creating Guacamole connection:`, error);
            return {
              success: false,
              message: `Error creating Guacamole connection: ${(error as Error).message}`,
            };
          }
        },
      },

      getGuacamoleConnection: {
        description: 'Get details about a Guacamole connection',
        parameters: z.object({
          connectionId: z.string().describe('ID of the Guacamole connection'),
        }),
        execute: async ({ connectionId }) => {
          try {
            console.log(`Getting Guacamole connection: ${connectionId}`);
            const connection = await vmService.getGuacamoleConnection(connectionId);

            return {
              success: true,
              connectionId: connection.connectionId,
              webSocketUrl: connection.webSocketUrl,
              status: connection.status,
              message: `Guacamole connection details retrieved successfully`,
            };
          } catch (error) {
            console.error(`Error getting Guacamole connection:`, error);
            return {
              success: false,
              message: `Error getting Guacamole connection: ${(error as Error).message}`,
            };
          }
        },
      },

      listGuacamoleConnections: {
        description: 'List all Guacamole connections',
        parameters: z.object({}),
        execute: async () => {
          try {
            console.log('Listing Guacamole connections');
            const connections = await vmService.listGuacamoleConnections();

            return {
              success: true,
              connections,
              count: connections.length,
              message: `Listed ${connections.length} Guacamole connections`,
            };
          } catch (error) {
            console.error(`Error listing Guacamole connections:`, error);
            return {
              success: false,
              message: `Error listing Guacamole connections: ${(error as Error).message}`,
              connections: [],
            };
          }
        },
      },

      deleteGuacamoleConnection: {
        description: 'Delete a Guacamole connection',
        parameters: z.object({
          connectionId: z.string().describe('ID of the Guacamole connection to delete'),
        }),
        execute: async ({ connectionId }) => {
          try {
            console.log(`Deleting Guacamole connection: ${connectionId}`);
            await vmService.deleteGuacamoleConnection(connectionId);

            return {
              success: true,
              connectionId,
              message: `Guacamole connection deleted successfully`,
            };
          } catch (error) {
            console.error(`Error deleting Guacamole connection:`, error);
            return {
              success: false,
              message: `Error deleting Guacamole connection: ${(error as Error).message}`,
            };
          }
        },
      },
    },
    maxSteps: 10, // Allow up to 10 steps for multi-step tool calls
  });

  return result.toDataStreamResponse({
    // Provide custom error handling
    getErrorMessage: (error) => {
      if (error == null) {
        return 'An unknown error occurred';
      }

      if (typeof error === 'string') {
        return error;
      }

      if (error instanceof Error) {
        return `Error: ${error.message}`;
      }

      return JSON.stringify(error);
    },
  });
}
