import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../../../auth/[...nextauth]/route"
import { EnhancedFirecrackerService } from "@/lib/firecracker/enhanced-firecracker-service"
import { logger } from "@/lib/utils/logger"

// Initialize Firecracker service
const firecrackerService = new EnhancedFirecrackerService()

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { id } = params

    // Resume instance
    const instance = await firecrackerService.resumeInstance(id)

    return NextResponse.json({
      success: true,
      instance: {
        id: instance.id,
        status: instance.status,
      },
    })
  } catch (error) {
    logger.error(`Error resuming Firecracker instance ${params.id}:`, error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to resume Firecracker instance",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
