import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../../../auth/[...nextauth]/route"
import { EnhancedFirecrackerService } from "@/lib/firecracker/enhanced-firecracker-service"
import { logger } from "@/lib/utils/logger"

// Initialize Firecracker service
const firecrackerService = new EnhancedFirecrackerService()

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { id } = params

    // Get instance
    const instance = firecrackerService.getInstance(id)

    if (!instance) {
      return new NextResponse(JSON.stringify({ error: "Instance not found" }), {
        status: 404,
      })
    }

    // List snapshots
    const snapshots = await firecrackerService.snapshotManager.listSnapshots(id)

    return NextResponse.json({
      success: true,
      snapshots,
    })
  } catch (error) {
    logger.error(`Error listing snapshots for Firecracker instance ${params.id}:`, error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to list snapshots",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { id } = params
    const { name } = await req.json()

    // Get instance
    const instance = firecrackerService.getInstance(id)

    if (!instance) {
      return new NextResponse(JSON.stringify({ error: "Instance not found" }), {
        status: 404,
      })
    }

    // Create snapshot
    const snapshotName = await firecrackerService.snapshotManager.createSnapshot(id, instance)

    return NextResponse.json({
      success: true,
      snapshot: {
        name: snapshotName,
        timestamp: new Date().toISOString(),
      },
    })
  } catch (error) {
    logger.error(`Error creating snapshot for Firecracker instance ${params.id}:`, error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to create snapshot",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
