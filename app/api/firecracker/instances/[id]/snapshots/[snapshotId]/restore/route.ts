import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../../../../auth/[...nextauth]/route"
import { EnhancedFirecrackerService } from "@/lib/firecracker/enhanced-firecracker-service"
import { logger } from "@/lib/utils/logger"

// Initialize Firecracker service
const firecrackerService = new EnhancedFirecrackerService()

export async function POST(req: NextRequest, { params }: { params: { id: string; snapshotId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { id, snapshotId } = params

    // Get instance
    const instance = firecrackerService.getInstance(id)

    if (!instance) {
      return new NextResponse(JSON.stringify({ error: "Instance not found" }), {
        status: 404,
      })
    }

    // Restore snapshot
    await firecrackerService.snapshotManager.restoreSnapshot(id, instance, snapshotId)

    return NextResponse.json({
      success: true,
    })
  } catch (error) {
    logger.error(`Error restoring snapshot ${params.snapshotId} for Firecracker instance ${params.id}:`, error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to restore snapshot",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
