import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../../../auth/[...nextauth]/route"
import { EnhancedFirecrackerService } from "@/lib/firecracker/enhanced-firecracker-service"
import { logger } from "@/lib/utils/logger"

// Initialize Firecracker service
const firecrackerService = new EnhancedFirecrackerService()

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { id } = params
    const { command, timeout } = await req.json()

    if (!command) {
      return new NextResponse(JSON.stringify({ error: "Missing command" }), {
        status: 400,
      })
    }

    // Execute command
    const result = await firecrackerService.executeCommand(id, command, timeout)

    return NextResponse.json({
      success: result.success,
      output: result.output,
      error: result.error,
    })
  } catch (error) {
    logger.error(`Error executing command in Firecracker instance ${params.id}:`, error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to execute command",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
