import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../../../auth/[...nextauth]/route"
import { EnhancedFirecrackerService } from "@/lib/firecracker/enhanced-firecracker-service"
import { logger } from "@/lib/utils/logger"

// Initialize Firecracker service
const firecrackerService = new EnhancedFirecrackerService()

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { id } = params
    const { force } = await req.json()

    // Stop instance
    await firecrackerService.stopInstance(id, force)

    return NextResponse.json({
      success: true,
    })
  } catch (error) {
    logger.error(`Error stopping Firecracker instance ${params.id}:`, error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to stop Firecracker instance",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
