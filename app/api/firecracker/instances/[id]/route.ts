import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../../auth/[...nextauth]/route"
import { EnhancedFirecrackerService } from "@/lib/firecracker/enhanced-firecracker-service"
import { logger } from "@/lib/utils/logger"

// Initialize Firecracker service
const firecrackerService = new EnhancedFirecrackerService()

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { id } = params

    // Get instance
    const instance = firecrackerService.getInstance(id)

    if (!instance) {
      return new NextResponse(JSON.stringify({ error: "Instance not found" }), {
        status: 404,
      })
    }

    return NextResponse.json({
      success: true,
      instance: {
        id: instance.id,
        status: instance.status,
        config: instance.config,
        startTime: instance.startTime,
        metrics: instance.metrics,
        error: instance.error,
      },
    })
  } catch (error) {
    logger.error(`Error getting Firecracker instance ${params.id}:`, error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to get Firecracker instance",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { id } = params

    // Delete instance
    await firecrackerService.deleteInstance(id)

    return NextResponse.json({
      success: true,
    })
  } catch (error) {
    logger.error(`Error deleting Firecracker instance ${params.id}:`, error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to delete Firecracker instance",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
