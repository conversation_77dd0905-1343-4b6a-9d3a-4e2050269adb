import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../auth/[...nextauth]/route"
import { EnhancedFirecrackerService } from "@/lib/firecracker/enhanced-firecracker-service"
import { logger } from "@/lib/utils/logger"

// Initialize Firecracker service
const firecrackerService = new EnhancedFirecrackerService()

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    // Get all instances
    const instances = firecrackerService.getInstances()

    return NextResponse.json({
      success: true,
      instances: instances.map((instance) => ({
        id: instance.id,
        status: instance.status,
        config: {
          vcpuCount: instance.config.vcpuCount,
          memSizeMib: instance.config.memSizeMib,
          rootDriveSizeMib: instance.config.rootDriveSizeMib,
          networkInterfaces: instance.config.networkInterfaces?.length || 0,
        },
        startTime: instance.startTime,
        metrics: instance.metrics,
      })),
    })
  } catch (error) {
    logger.error("Error getting Firecracker instances:", error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to get Firecracker instances",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    })
  }

  try {
    const { config } = await req.json()

    if (!config) {
      return new NextResponse(JSON.stringify({ error: "Missing configuration" }), {
        status: 400,
      })
    }

    // Create new instance
    const instance = await firecrackerService.createInstance(config)

    return NextResponse.json({
      success: true,
      instance: {
        id: instance.id,
        status: instance.status,
        config: {
          vcpuCount: instance.config.vcpuCount,
          memSizeMib: instance.config.memSizeMib,
          rootDriveSizeMib: instance.config.rootDriveSizeMib,
          networkInterfaces: instance.config.networkInterfaces?.length || 0,
        },
      },
    })
  } catch (error) {
    logger.error("Error creating Firecracker instance:", error)

    return new NextResponse(
      JSON.stringify({
        error: "Failed to create Firecracker instance",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      { status: 500 },
    )
  }
}
