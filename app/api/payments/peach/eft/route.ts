import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { logger } from '@/lib/logger'
import crypto from 'crypto'

// Mock Peach Payments EFT API client
class PeachEftClient {
  private entityId: string
  private apiKey: string
  private apiEndpoint: string
  
  constructor() {
    this.entityId = process.env.PEACH_ENTITY_ID || ''
    this.apiKey = process.env.PEACH_API_KEY || ''
    this.apiEndpoint = process.env.PEACH_API_ENDPOINT || 'https://test.peachpayments.com/v1'
  }
  
  async createEftPayment(params: any): Promise<any> {
    try {
      logger.info('Creating Peach EFT payment', { params })
      
      // In a real implementation, this would make an API call to Peach Payments
      // For now, we'll simulate a successful response
      const transactionId = `8a82944a${this.generateRandomHex(40)}`
      const paymentId = `8a8294${this.generateRandomHex(42)}`
      
      return {
        id: paymentId,
        result: {
          code: '000.100.110',
          description: 'Request successfully processed',
        },
        buildNumber: '********-111417.a78299f',
        timestamp: new Date().toISOString(),
        ndc: this.generateRandomHex(32),
        merchantTransactionId: params.merchantTransactionId,
        amount: params.amount,
        currency: params.currency,
        paymentType: 'EFT',
        paymentBrand: params.bank || 'FNB',
        customer: {
          email: params.customer.email,
          givenName: params.customer.givenName,
          surname: params.customer.surname,
        },
        customParameters: params.customParameters,
        bankAccount: {
          holder: params.bankAccount.holder,
          bank: params.bankAccount.bank,
          reference: params.bankAccount.reference,
        },
        resultDetails: {
          ConnectorTxID1: transactionId,
          ConnectorTxID3: this.generateRandomHex(32),
          ConnectorTxID2: this.generateRandomHex(32),
        },
      }
    } catch (error) {
      logger.error('Error creating Peach EFT payment', { error })
      throw error
    }
  }
  
  async createRecurringEftPayment(params: any): Promise<any> {
    try {
      logger.info('Creating Peach recurring EFT payment', { params })
      
      // Create initial payment
      const payment = await this.createEftPayment(params)
      
      // Create recurring registration
      const registrationId = `8a82944a${this.generateRandomHex(40)}`
      
      return {
        ...payment,
        registrationId,
        recurringType: 'REPEATED',
        recurringMode: 'AUTOMATIC',
      }
    } catch (error) {
      logger.error('Error creating Peach recurring EFT payment', { error })
      throw error
    }
  }
  
  private generateRandomHex(length: number): string {
    return crypto.randomBytes(Math.ceil(length / 2))
      .toString('hex')
      .slice(0, length)
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Parse the request body
    const body = await request.json()
    
    // Validate required fields
    if (!body.amount || !body.currency || !body.accountHolder || !body.bank || !body.reference) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      )
    }
    
    // Create a unique transaction reference
    const transactionRef = `AG-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    
    // Initialize Peach Payments client
    const peachClient = new PeachEftClient()
    
    // Prepare payment parameters
    const paymentParams = {
      merchantTransactionId: transactionRef,
      amount: body.amount.toFixed(2),
      currency: body.currency === 'R' ? 'ZAR' : body.currency,
      paymentType: 'EFT',
      bank: body.bank,
      bankAccount: {
        holder: body.accountHolder,
        bank: body.bank,
        reference: body.reference,
      },
      customer: {
        email: session.user.email,
        givenName: session.user.name?.split(' ')[0] || 'User',
        surname: session.user.name?.split(' ').slice(1).join(' ') || '',
      },
      customParameters: {
        SHOP_REFERENCE: body.planId || '',
        SUBSCRIPTION: body.isSubscription ? 'true' : 'false',
      },
      recurringType: body.isSubscription ? 'INITIAL' : null,
    }
    
    // Process the payment
    let result
    if (body.isSubscription) {
      result = await peachClient.createRecurringEftPayment(paymentParams)
    } else {
      result = await peachClient.createEftPayment(paymentParams)
    }
    
    // Create a payment record in the database
    await prisma.payment.create({
      data: {
        providerTransactionId: result.id,
        provider: 'peach',
        amount: body.amount,
        currency: body.currency === 'R' ? 'ZAR' : body.currency,
        status: 'pending', // EFT payments are initially pending
        reference: body.reference,
        metadata: {
          planId: body.planId,
          isSubscription: body.isSubscription,
          interval: body.interval,
          registrationId: result.registrationId,
          bank: body.bank,
          accountHolder: body.accountHolder,
        },
        userId: session.user.id,
      },
    })
    
    // If this is a subscription, create a subscription record
    if (body.isSubscription && body.planId) {
      // Get the plan from the database
      const plan = await prisma.plan.findUnique({
        where: { id: body.planId },
      })
      
      if (plan) {
        // Calculate the current period end date based on the interval
        const currentPeriodEnd = new Date()
        if (body.interval === 'monthly') {
          currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1)
        } else if (body.interval === 'quarterly') {
          currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 3)
        } else if (body.interval === 'yearly') {
          currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1)
        }
        
        // Create the subscription
        await prisma.subscription.create({
          data: {
            id: result.registrationId,
            provider: 'peach',
            status: 'pending', // EFT subscriptions start as pending
            planId: plan.id,
            userId: session.user.id,
            startDate: new Date(),
            currentPeriodEnd,
            metadata: {
              registrationId: result.registrationId,
              bank: body.bank,
              accountHolder: body.accountHolder,
              reference: body.reference,
            },
          },
        })
        
        // Create a pending invoice
        await prisma.invoice.create({
          data: {
            subscriptionId: result.registrationId,
            amount: body.amount,
            currency: body.currency === 'R' ? 'ZAR' : body.currency,
            status: 'unpaid', // Initially unpaid until EFT is confirmed
            dueDate: new Date(),
            userId: session.user.id,
            planId: plan.id,
          },
        })
      }
    }
    
    // Return the result
    return NextResponse.json({
      success: true,
      transactionId: result.id,
      status: 'pending',
      bankDetails: {
        accountName: 'SAGEDesigns pty ltd',
        accountNumber: '***********',
        bankName: 'FNB',
        branchCode: '250655',
        reference: body.reference,
      },
    })
    
  } catch (error) {
    logger.error('Error processing Peach EFT payment', { error })
    
    return NextResponse.json(
      { 
        message: error instanceof Error ? error.message : 'An unknown error occurred',
        success: false
      },
      { status: 500 }
    )
  }
}
