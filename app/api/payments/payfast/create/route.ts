import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { logger } from '@/lib/logger'
import crypto from 'crypto'

/**
 * Generate a signature for Payfast API requests
 * @param data Object containing the data to sign
 * @param passphrase Optional passphrase for additional security
 * @returns MD5 hash signature
 */
function generateSignature(data: Record<string, string>, passphrase: string | null = null): string {
  // Create parameter string
  let pfParamString = ""
  
  // Sort keys alphabetically
  const keys = Object.keys(data).sort()
  
  keys.forEach((key) => {
    if (key !== "signature") {
      pfParamString += `${key}=${encodeURIComponent(data[key]).replace(/%20/g, "+")}&`
    }
  })
  
  // Remove last ampersand
  pfParamString = pfParamString.slice(0, -1)
  
  // Add passphrase if provided
  if (passphrase !== null) {
    pfParamString += `&passphrase=${encodeURIComponent(passphrase).replace(/%20/g, "+")}`
  }
  
  // Generate signature
  return crypto.createHash("md5").update(pfParamString).digest("hex")
}

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Parse the request body
    const body = await request.json()
    
    // Validate required fields
    if (!body.planId || !body.returnUrl || !body.cancelUrl || !body.notifyUrl) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      )
    }
    
    // Get the plan from the database
    const plan = await prisma.plan.findUnique({
      where: { id: body.planId },
    })
    
    if (!plan) {
      return NextResponse.json(
        { message: 'Plan not found' },
        { status: 404 }
      )
    }
    
    // Get Payfast merchant details from environment variables
    const merchantId = process.env.PAYFAST_MERCHANT_ID
    const merchantKey = process.env.PAYFAST_MERCHANT_KEY
    const passphrase = process.env.PAYFAST_PASSPHRASE || null
    
    if (!merchantId || !merchantKey) {
      logger.error('Payfast credentials not configured')
      return NextResponse.json(
        { message: 'Payment provider not properly configured' },
        { status: 500 }
      )
    }
    
    // Create a unique transaction reference
    const transactionRef = `AG-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    
    // Determine subscription frequency
    let subscriptionFrequency = '3'
    if (plan.interval === 'monthly') {
      subscriptionFrequency = '3' // Monthly
    } else if (plan.interval === 'quarterly') {
      subscriptionFrequency = '4' // Quarterly
    } else if (plan.interval === 'yearly') {
      subscriptionFrequency = '6' // Annual
    }
    
    // Prepare Payfast data
    const payfastData: Record<string, string> = {
      // Merchant details
      merchant_id: merchantId,
      merchant_key: merchantKey,
      
      // Transaction details
      amount: plan.price.toFixed(2),
      item_name: `${plan.name} Subscription`,
      item_description: plan.description || '',
      custom_str1: plan.id,
      custom_str2: session.user.id,
      custom_str3: transactionRef,
      
      // Buyer details
      name_first: session.user.name?.split(' ')[0] || 'User',
      name_last: session.user.name?.split(' ').slice(1).join(' ') || '',
      email_address: session.user.email || '',
      
      // Subscription details
      subscription_type: subscriptionFrequency,
      billing_date: new Date().getDate().toString(),
      recurring_amount: plan.price.toFixed(2),
      frequency: subscriptionFrequency,
      cycles: '0', // Infinite until canceled
      
      // URLs
      return_url: body.returnUrl,
      cancel_url: body.cancelUrl,
      notify_url: body.notifyUrl,
    }
    
    // Generate signature
    payfastData.signature = generateSignature(payfastData, passphrase)
    
    // Create a payment record in the database
    await prisma.payment.create({
      data: {
        providerTransactionId: transactionRef,
        provider: 'payfast',
        amount: plan.price,
        currency: plan.currency,
        status: 'pending',
        reference: transactionRef,
        metadata: {
          planId: plan.id,
          returnUrl: body.returnUrl,
          cancelUrl: body.cancelUrl,
          notifyUrl: body.notifyUrl,
          isSubscription: true,
        },
        userId: session.user.id,
      },
    })
    
    // Return the Payfast data
    return NextResponse.json({
      success: true,
      formData: payfastData,
      transactionId: transactionRef,
    })
    
  } catch (error) {
    logger.error('Error creating Payfast payment', { error })
    
    return NextResponse.json(
      { 
        message: error instanceof Error ? error.message : 'An unknown error occurred',
        success: false
      },
      { status: 500 }
    )
  }
}
