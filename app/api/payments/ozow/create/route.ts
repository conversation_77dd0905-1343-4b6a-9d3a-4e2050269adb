import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { logger } from '@/lib/logger'
import crypto from 'crypto'

/**
 * Generate a hash for Ozow API requests
 * @param data Object containing the data to hash
 * @param privateKey Private key for signing
 * @returns SHA512 hash
 */
function generateHash(data: Record<string, string>, privateKey: string): string {
  // Create parameter string
  const values = Object.values(data).join('')
  const stringToHash = values + privateKey
  
  // Generate hash
  return crypto.createHash('sha512').update(stringToHash).digest('hex')
}

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Parse the request body
    const body = await request.json()
    
    // Validate required fields
    if (!body.amount || !body.currency || !body.bank || !body.reference) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      )
    }
    
    // Get Ozow API credentials from environment variables
    const siteCode = process.env.OZOW_SITE_CODE
    const privateKey = process.env.OZOW_PRIVATE_KEY
    const apiKey = process.env.OZOW_API_KEY
    
    if (!siteCode || !privateKey || !apiKey) {
      logger.error('Ozow credentials not configured')
      return NextResponse.json(
        { message: 'Payment provider not properly configured' },
        { status: 500 }
      )
    }
    
    // Create a unique transaction reference
    const transactionRef = body.reference || `AG-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    
    // Prepare Ozow API request data
    const ozowData: Record<string, string> = {
      SiteCode: siteCode,
      CountryCode: 'ZA',
      CurrencyCode: body.currency === 'R' ? 'ZAR' : body.currency,
      Amount: body.amount.toFixed(2),
      TransactionReference: transactionRef,
      BankReference: transactionRef,
      Customer: session.user.email || '',
      CancelUrl: body.cancelUrl,
      SuccessUrl: body.returnUrl,
      NotifyUrl: body.notifyUrl,
      IsTest: process.env.NODE_ENV === 'production' ? 'false' : 'true',
      BankId: body.bank,
    }
    
    // Add optional fields
    if (body.user?.name) {
      ozowData.Customer = body.user.name
    }
    
    if (body.user?.email) {
      ozowData.CustomerEmail = body.user.email
    }
    
    // Generate hash
    ozowData.HashCheck = generateHash(ozowData, privateKey)
    
    // In a real implementation, we would make an API call to Ozow
    // For now, we'll simulate the response
    
    // Generate a payment URL
    const baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://pay.ozow.com' 
      : 'https://pay.ozow.com/test'
    
    const queryParams = new URLSearchParams()
    Object.entries(ozowData).forEach(([key, value]) => {
      queryParams.append(key, value)
    })
    
    const paymentUrl = `${baseUrl}?${queryParams.toString()}`
    
    // Create a payment record in the database
    await prisma.payment.create({
      data: {
        providerTransactionId: transactionRef,
        provider: 'ozow',
        amount: body.amount,
        currency: body.currency === 'R' ? 'ZAR' : body.currency,
        status: 'pending',
        reference: transactionRef,
        metadata: {
          planId: body.metadata?.planId,
          userId: body.metadata?.userId || session.user.id,
          isSubscription: body.isSubscription,
          interval: body.interval,
          bank: body.bank,
          returnUrl: body.returnUrl,
          cancelUrl: body.cancelUrl,
          notifyUrl: body.notifyUrl,
        },
        userId: session.user.id,
      },
    })
    
    // If this is a subscription, create a subscription record
    if (body.isSubscription && body.metadata?.planId) {
      // Get the plan from the database
      const plan = await prisma.plan.findUnique({
        where: { id: body.metadata.planId },
      })
      
      if (plan) {
        // Generate a subscription ID
        const subscriptionId = `ozow_sub_${crypto.randomBytes(16).toString('hex')}`
        
        // Calculate the current period end date based on the interval
        const currentPeriodEnd = new Date()
        if (body.interval === 'monthly') {
          currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1)
        } else if (body.interval === 'quarterly') {
          currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 3)
        } else if (body.interval === 'yearly') {
          currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1)
        }
        
        // Create the subscription
        await prisma.subscription.create({
          data: {
            id: subscriptionId,
            provider: 'ozow',
            status: 'pending', // Ozow subscriptions start as pending
            planId: plan.id,
            userId: session.user.id,
            startDate: new Date(),
            currentPeriodEnd,
            metadata: {
              transactionRef,
              bank: body.bank,
            },
          },
        })
        
        // Create a pending invoice
        await prisma.invoice.create({
          data: {
            subscriptionId: subscriptionId,
            amount: body.amount,
            currency: body.currency === 'R' ? 'ZAR' : body.currency,
            status: 'unpaid', // Initially unpaid until payment is confirmed
            dueDate: new Date(),
            userId: session.user.id,
            planId: plan.id,
          },
        })
        
        // Update the payment with the subscription ID
        await prisma.payment.update({
          where: { providerTransactionId: transactionRef },
          data: {
            subscriptionId,
            metadata: {
              ...body.metadata,
              subscriptionId,
            },
          },
        })
      }
    }
    
    // Return the payment URL
    return NextResponse.json({
      success: true,
      paymentUrl,
      transactionId: transactionRef,
    })
    
  } catch (error) {
    logger.error('Error creating Ozow payment', { error })
    
    return NextResponse.json(
      { 
        message: error instanceof Error ? error.message : 'An unknown error occurred',
        success: false
      },
      { status: 500 }
    )
  }
}
