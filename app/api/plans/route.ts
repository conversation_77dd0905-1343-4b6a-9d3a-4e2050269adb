import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { logger } from '@/lib/logger'

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams
    const interval = searchParams.get('interval')
    
    // Build the query
    const query: any = {
      where: {
        active: true,
      },
      orderBy: {
        price: 'asc',
      },
    }
    
    // Add interval filter if provided
    if (interval) {
      query.where.interval = interval
    }
    
    // Get the plans from the database
    const plans = await prisma.plan.findMany(query)
    
    // Format the plans for the client
    const formattedPlans = plans.map(plan => ({
      id: plan.id,
      name: plan.name,
      description: plan.description,
      price: plan.price,
      currency: plan.currency,
      interval: plan.interval,
      features: plan.features as string[],
    }))
    
    return NextResponse.json({
      success: true,
      plans: formattedPlans,
    })
    
  } catch (error) {
    logger.error('Error fetching plans', { error })
    
    return NextResponse.json(
      { 
        message: error instanceof Error ? error.message : 'An unknown error occurred',
        success: false,
        plans: [],
      },
      { status: 500 }
    )
  }
}
