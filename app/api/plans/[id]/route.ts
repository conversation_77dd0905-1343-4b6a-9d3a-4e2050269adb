import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { logger } from '@/lib/logger'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const planId = params.id
    
    // Get the plan from the database
    const plan = await prisma.plan.findUnique({
      where: {
        id: planId,
      },
    })
    
    if (!plan) {
      return NextResponse.json(
        { message: 'Plan not found' },
        { status: 404 }
      )
    }
    
    // Format the plan for the client
    const formattedPlan = {
      id: plan.id,
      name: plan.name,
      description: plan.description,
      price: plan.price,
      currency: plan.currency,
      interval: plan.interval,
      features: plan.features as string[],
    }
    
    return NextResponse.json(formattedPlan)
    
  } catch (error) {
    logger.error('Error fetching plan', { error, planId: params.id })
    
    return NextResponse.json(
      { 
        message: error instanceof Error ? error.message : 'An unknown error occurred',
      },
      { status: 500 }
    )
  }
}

// For development and testing, we'll provide mock data for specific plan IDs
export async function generateStaticParams() {
  return [
    { id: 'starter-monthly' },
    { id: 'pro-monthly' },
    { id: 'enterprise-monthly' },
    { id: 'starter-yearly' },
    { id: 'pro-yearly' },
    { id: 'enterprise-yearly' },
  ]
}

// Mock data for development
const mockPlans = {
  'starter-monthly': {
    id: 'starter-monthly',
    name: 'Starter',
    description: 'Perfect for individuals and small projects',
    price: 99,
    currency: 'R',
    interval: 'monthly',
    features: [
      'Up to 5 projects',
      'Basic AI code generation',
      'Community support'
    ]
  },
  'pro-monthly': {
    id: 'pro-monthly',
    name: 'Pro',
    description: 'For professionals and growing teams',
    price: 299,
    currency: 'R',
    interval: 'monthly',
    features: [
      'Unlimited projects',
      'Advanced AI code generation',
      'Priority support',
      'Custom components',
      'API access'
    ]
  },
  'enterprise-monthly': {
    id: 'enterprise-monthly',
    name: 'Enterprise',
    description: 'For large organizations with custom needs',
    price: 999,
    currency: 'R',
    interval: 'monthly',
    features: [
      'Everything in Pro',
      'Dedicated account manager',
      'Custom integrations',
      'SLA guarantees',
      'On-premise deployment options'
    ]
  },
  'starter-yearly': {
    id: 'starter-yearly',
    name: 'Starter',
    description: 'Perfect for individuals and small projects',
    price: 990,
    currency: 'R',
    interval: 'yearly',
    features: [
      'Up to 5 projects',
      'Basic AI code generation',
      'Community support'
    ]
  },
  'pro-yearly': {
    id: 'pro-yearly',
    name: 'Pro',
    description: 'For professionals and growing teams',
    price: 2990,
    currency: 'R',
    interval: 'yearly',
    features: [
      'Unlimited projects',
      'Advanced AI code generation',
      'Priority support',
      'Custom components',
      'API access'
    ]
  },
  'enterprise-yearly': {
    id: 'enterprise-yearly',
    name: 'Enterprise',
    description: 'For large organizations with custom needs',
    price: 9990,
    currency: 'R',
    interval: 'yearly',
    features: [
      'Everything in Pro',
      'Dedicated account manager',
      'Custom integrations',
      'SLA guarantees',
      'On-premise deployment options'
    ]
  }
}
