/**
 * Agent Patterns API Route
 * 
 * This file implements a NextJS API route handler for the agent patterns functionality.
 * It uses the AI SDK's streamText function to generate responses with agent pattern tools.
 */

import { NextRequest } from 'next/server';
import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { agentPatternTools } from '@/lib/tools/agent-patterns';

// Define the maximum duration for streaming responses (2 minutes)
export const maxDuration = 120;

/**
 * POST handler for the agent patterns API
 */
export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();
    
    // Use streamText with the agent pattern tools
    return streamText({
      model: openai('gpt-4o'),
      messages: body.messages,
      tools: agentPatternTools,
      temperature: 0.7,
      maxTokens: 4000,
      maxSteps: 5, // Allow up to 5 steps for multi-step tool calls
    });
  } catch (error) {
    console.error('Error in agent patterns API:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
