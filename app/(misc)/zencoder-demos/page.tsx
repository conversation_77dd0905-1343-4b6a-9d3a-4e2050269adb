"use client"

import * as React from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, ArrowRight, MessageSquare, Terminal } from "lucide-react"

export default function ZencoderDemos() {
  return (
    <div className="flex flex-col min-h-screen bg-[#1A1A1A] text-white">
      <header className="border-b border-[#333333] p-4">
        <div className="container flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <a href="/">
                <ArrowLeft className="h-5 w-5" />
              </a>
            </Button>
            <h1 className="text-xl font-semibold">Zencoder Chat Input Demos</h1>
          </div>
        </div>
      </header>
      
      <main className="flex-1 container py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold mb-4">Zencoder Chat Input Components</h1>
            <p className="text-lg text-[#999999] max-w-2xl mx-auto">
              Two versions of the Zencoder chat input component with agent and context selection functionality.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="bg-[#1E1E1E] border-[#333333] text-white">
              <CardHeader>
                <CardTitle>Compact Chat Input</CardTitle>
                <CardDescription className="text-[#999999]">
                  A compact version that closely matches the reference image
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="aspect-video bg-[#1A1A1A] rounded-md flex items-center justify-center border border-[#333333]">
                  <Terminal className="h-12 w-12 text-[#333333]" />
                </div>
                <div className="mt-4 text-sm text-[#999999]">
                  <p>Features:</p>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Pre-selected agent and context</li>
                    <li>Compact dropdown selectors</li>
                    <li>Dark theme matching VSCode</li>
                    <li>Command palette support</li>
                  </ul>
                </div>
              </CardContent>
              <CardFooter>
                <Button asChild className="w-full">
                  <Link href="/zencoder-compact-demo">
                    View Demo
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
            
            <Card className="bg-[#1E1E1E] border-[#333333] text-white">
              <CardHeader>
                <CardTitle>Full Chat Interface</CardTitle>
                <CardDescription className="text-[#999999]">
                  A complete chat interface with agent and context selection
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="aspect-video bg-[#1A1A1A] rounded-md flex items-center justify-center border border-[#333333]">
                  <MessageSquare className="h-12 w-12 text-[#333333]" />
                </div>
                <div className="mt-4 text-sm text-[#999999]">
                  <p>Features:</p>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Complete chat interface</li>
                    <li>Message history display</li>
                    <li>Dynamic agent and context selection</li>
                    <li>Detailed instructions and examples</li>
                  </ul>
                </div>
              </CardContent>
              <CardFooter>
                <Button asChild className="w-full">
                  <Link href="/zencoder-chat-input-demo">
                    View Demo
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
          
          <div className="mt-12 text-center">
            <p className="text-[#999999] mb-4">
              Both components provide a modern, VSCode-inspired interface for interacting with AI agents.
            </p>
            <Button variant="outline" asChild>
              <Link href="/">
                Back to Home
              </Link>
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}
