'use client';

import { useState } from 'react';
import DevBot from '@/components/dev-bot';

export default function DevChatPage() {
  const [projectId, setProjectId] = useState<string | undefined>(undefined);
  
  return (
    <div className="flex flex-col h-screen bg-gray-100 dark:bg-gray-900">
      {/* Header */}
      <header className="py-4 px-6 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 rounded-md bg-orange-500 text-white">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 3L19 9L13 15V11H7V7H13V3Z" fill="currentColor" />
                <path d="M11 21L5 15L11 9V13H17V17H11V21Z" fill="currentColor" />
              </svg>
            </div>
            <h1 className="text-xl font-semibold text-gray-800 dark:text-white">App Generator</h1>
          </div>
          
          <div className="flex items-center">
            <select 
              className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={projectId || ''}
              onChange={(e) => setProjectId(e.target.value || undefined)}
            >
              <option value="">Select a project</option>
              <option value="project-1">Sample Project 1</option>
              <option value="project-2">Sample Project 2</option>
              <option value="project-3">E-commerce App</option>
              <option value="project-4">Blog Platform</option>
            </select>
            
            <button className="ml-3 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
              New Project
            </button>
          </div>
        </div>
      </header>
      
      {/* Main content */}
      <main className="flex-1 overflow-hidden p-4 md:p-6">
        <div className="max-w-7xl mx-auto h-full">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 h-full">
            <DevBot projectId={projectId} />
          </div>
        </div>
      </main>
      
      {/* Footer */}
      <footer className="py-3 px-6 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>App Generator © {new Date().getFullYear()}</p>
      </footer>
    </div>
  );
}
