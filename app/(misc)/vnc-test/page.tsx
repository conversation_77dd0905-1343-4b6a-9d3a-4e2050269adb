'use client';

import React, { useState } from 'react';
import { VMVncViewer } from '@/components/vm-interface/VMVncViewer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';

export default function VncTestPage() {
  const [vmId, setVmId] = useState('test-vm');
  const [viewOnly, setViewOnly] = useState(false);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleConnect = () => {
    setConnected(true);
    setError(null);
  };

  const handleDisconnect = () => {
    setConnected(false);
  };

  const handleError = (err: Error) => {
    setError(err.message);
    setConnected(false);
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">VNC Viewer Test</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>VNC Connection</CardTitle>
              <CardDescription>Configure your VNC connection</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="vmId">VM ID</Label>
                <Input
                  id="vmId"
                  value={vmId}
                  onChange={(e) => setVmId(e.target.value)}
                  placeholder="Enter VM ID"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="viewOnly"
                  checked={viewOnly}
                  onCheckedChange={setViewOnly}
                />
                <Label htmlFor="viewOnly">View Only</Label>
              </div>

              <div className="pt-2">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm">
                    {connected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                {error && (
                  <div className="mt-2 text-sm text-red-500">
                    Error: {error}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>VNC Viewer</CardTitle>
              <CardDescription>
                {viewOnly ? 'View only mode' : 'Interactive mode'}
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[600px]">
              <VMVncViewer
                vmId={vmId}
                viewOnly={viewOnly}
                onConnect={handleConnect}
                onDisconnect={handleDisconnect}
                onError={handleError}
                height="100%"
                width="100%"
                showControls={true}
                autoConnect={true}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
