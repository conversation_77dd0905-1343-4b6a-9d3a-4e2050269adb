"use client"

import { LxcContainerDetails } from "@/components/lxc/lxc-container-details"
import { useLxcContainer } from "@/hooks/use-lxc-container"

export default function LxcContainerDetailsPage({ params }: { params: { id: string } }) {
  const { id } = params
  
  const {
    container,
    loading,
    error,
    startContainer,
    stopContainer,
    restartContainer,
    deleteContainer,
  } = useLxcContainer(id)

  return (
    <LxcContainerDetails
      container={container}
      loading={loading}
      error={error}
      onStart={startContainer}
      onStop={stopContainer}
      onRestart={restartContainer}
      onDelete={deleteContainer}
    />
  )
}
