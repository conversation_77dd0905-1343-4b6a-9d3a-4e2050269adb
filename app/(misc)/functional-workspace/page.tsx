"use client"

import * as React from "react"
import { useState } from "react"
import <PERSON>raftCoder from "@/components/agent-interface/main"
import { cn } from "@/lib/utils"
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { ChevronLeft, Plus, FileCode } from "lucide-react"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"

// Define file types
interface CodeFile {
  id: string
  name: string
  content: string
  language: string
  active?: boolean
}

// Helper function to get language from file extension
const getLanguageFromFileName = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  
  switch (extension) {
    case 'js': return 'javascript'
    case 'jsx': return 'jsx'
    case 'ts': return 'typescript'
    case 'tsx': return 'tsx'
    case 'py': return 'python'
    case 'html': return 'html'
    case 'css': return 'css'
    case 'json': return 'json'
    default: return 'plaintext'
  }
}

export default function FunctionalWorkspace() {
  // State for files and active file
  const [files, setFiles] = useState<CodeFile[]>([])
  const [activeFileId, setActiveFileId] = useState<string>('')
  
  // Get active file
  const activeFile = files.find(f => f.id === activeFileId)
  
  // Handle file tab click
  const handleFileTabClick = (fileId: string) => {
    setActiveFileId(fileId)
  }
  
  // Handle file generation from agent
  const handleFileGenerated = (filePath: string, content: string, description?: string) => {
    console.log(`File generated: ${filePath}`, { description });
    
    // Create a new file
    const newFile: CodeFile = {
      id: `file-${Date.now()}`,
      name: filePath.split('/').pop() || 'new-file',
      content,
      language: getLanguageFromFileName(filePath),
      active: false
    }
    
    // Add the new file to the files array
    setFiles(prev => [...prev, newFile])
    
    // Set the new file as active
    setActiveFileId(newFile.id)
  }
  
  return (
    <div className="flex h-screen bg-[#1e1e1e] text-white overflow-hidden">
      {/* Chat Panel */}
      <div className="w-[350px] flex flex-col border-r border-[#333]">
        {/* Chat Header */}
        <div className="flex items-center p-3 border-b border-[#333]">
          <Button variant="ghost" size="icon" className="mr-2">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm font-medium">Autonomous Programmer</span>
          <div className="flex-1" />
          <Button variant="ghost" size="icon">
            <Plus className="h-4 w-4" />
          </Button>
          <span className="text-sm ml-2">New chat</span>
        </div>
        
        {/* Agent Enhanced Chat */}
        <div className="flex-1 flex flex-col">
          <KraftCoder
            className="flex-1 border-none shadow-none"
            initialAgentType="autonomous-programmer"
            projectId="functional-project" // This is just a placeholder
            onFileGenerated={handleFileGenerated}
            onVMOperation={(operation, args) => {
              console.log(`VM operation: ${operation}`, args);
            }}
          />
        </div>
      </div>
      
      {/* Code Panel */}
      <div className="flex-1 flex flex-col">
        {/* File Tabs */}
        <div className="flex items-center border-b border-[#333] bg-[#252525] overflow-x-auto">
          {files.length === 0 && (
            <div className="flex items-center h-9 px-4 text-sm text-gray-400">
              <FileCode className="h-4 w-4 mr-2" />
              No files yet - ask the agent to generate some code
            </div>
          )}
          
          {files.map(file => (
            <button
              key={file.id}
              className={cn(
                "flex items-center h-9 px-4 border-r border-[#333] text-sm",
                file.id === activeFileId ? "bg-[#1e1e1e] text-white" : "text-gray-400 hover:bg-[#2a2a2a]"
              )}
              onClick={() => handleFileTabClick(file.id)}
            >
              <span>{file.name}</span>
            </button>
          ))}
        </div>
        
        {/* Code Content */}
        <div className="flex-1 overflow-auto">
          {activeFile ? (
            <SyntaxHighlighter
              language={activeFile.language}
              style={vscDarkPlus}
              showLineNumbers={true}
              customStyle={{
                margin: 0,
                padding: '1rem',
                background: '#1e1e1e',
                fontSize: '14px',
                height: '100%',
                overflow: 'auto'
              }}
            >
              {activeFile.content}
            </SyntaxHighlighter>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-400">
              <FileCode className="h-16 w-16 mb-4" />
              <h3 className="text-xl font-medium mb-2">No file selected</h3>
              <p className="text-sm max-w-md text-center">
                Ask the autonomous programmer to generate code, and it will appear here.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
