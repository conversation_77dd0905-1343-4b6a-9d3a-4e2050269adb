"use client"

import * as React from "react"
import { useState } from "react"
import { CodeEditorWithAgent } from "@/components/code-editor-with-agent"

// Sample Python files
const pythonFiles = [
  {
    id: 'file1',
    name: 'loader.py',
    language: 'python',
    content: `class ClientTLSConfig:
    def __init__(
        self,
        tls_strategy: str,
        cert_file: str,
        key_file: str,
        ca_file: str,
        server_name: str,
    ):
        self.tls_strategy = tls_strategy
        self.cert_file = cert_file
        self.key_file = key_file
        self.ca_file = ca_file
        self.server_name = server_name

class ClientConfig:
    def __init__(
        self,
        tenant_id: str,
        tls_config: ClientTLSConfig,
        token: str,
        host_port: str = "localhost:7070",
        server_url: str = "https://app.dev.hatchet-tools.com",
        namespace: str = None,
    ):
        self.tenant_id = tenant_id
        self.tls_config = tls_config
        self.token = token
        self.host_port = host_port
        self.server_url = server_url
        self.namespace = namespace`,
    active: true
  },
  {
    id: 'file2',
    name: 'client.py',
    language: 'python',
    content: `import os
import yaml
from typing import Dict, Optional, Any

from .loader import ClientConfig, ClientTLSConfig

class ConfigLoader:
    def __init__(self, directory: str):
        self.directory = directory
        
    def load_client_config(self) -> ClientConfig:
        """
        Load client configuration from a YAML file or environment variables.
        
        Returns:
            ClientConfig: The client configuration object
        """
        # Try to load from YAML file
        config_path = os.path.join(self.directory, "config.yaml")
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f)
                
            # Extract TLS configuration
            tls_config = ClientTLSConfig(
                tls_strategy=config_data.get('tls_strategy', 'mutual'),
                cert_file=config_data.get('cert_file', ''),
                key_file=config_data.get('key_file', ''),
                ca_file=config_data.get('ca_file', ''),
                server_name=config_data.get('server_name', '')
            )
            
            # Create client config
            return ClientConfig(
                tenant_id=config_data.get('tenant_id', ''),
                tls_config=tls_config,
                token=config_data.get('token', ''),
                host_port=config_data.get('host_port', 'localhost:7070'),
                server_url=config_data.get('server_url', 'https://app.dev.hatchet-tools.com'),
                namespace=config_data.get('namespace')
            )
        
        # Fall back to environment variables
        tls_config = ClientTLSConfig(
            tls_strategy=os.environ.get('HATCHET_TLS_STRATEGY', 'mutual'),
            cert_file=os.environ.get('HATCHET_CERT_FILE', ''),
            key_file=os.environ.get('HATCHET_KEY_FILE', ''),
            ca_file=os.environ.get('HATCHET_CA_FILE', ''),
            server_name=os.environ.get('HATCHET_SERVER_NAME', '')
        )
        
        return ClientConfig(
            tenant_id=os.environ.get('HATCHET_TENANT_ID', ''),
            tls_config=tls_config,
            token=os.environ.get('HATCHET_TOKEN', ''),
            host_port=os.environ.get('HATCHET_HOST_PORT', 'localhost:7070'),
            server_url=os.environ.get('HATCHET_SERVER_URL', 'https://app.dev.hatchet-tools.com'),
            namespace=os.environ.get('HATCHET_NAMESPACE')
        )
        
    def load_tls_config(self, host_port: str) -> Dict[str, Any]:
        """
        Load TLS configuration from provided data or environment variables.
        
        Args:
            host_port: The host:port string for the connection
            
        Returns:
            Dict containing TLS configuration parameters
        """
        # Implementation details...
        pass`,
    active: false
  }
]

// Sample initial messages for Python demo
const pythonInitialMessages = [
  {
    id: '1',
    role: 'user',
    content: 'Can you tell me what functions we have on this page?'
  },
  {
    id: '2',
    role: 'assistant',
    content: "Based on the file you've shared (python-sdk/hatchet_sdk/loader.py), here are the functions present in this module:\n\n1. In the ConfigLoader class:\n   * `__init__(self, directory: str)`: Initializes the ConfigLoader with a directory path\n   * `load_client_config(self)` → ClientConfig: Loads client configuration from a YAML file or environment variables\n   * `load_tls_config(self, host_port: str)` → ClientTLSConfig: Loads TLS configuration from provided data or environment variables\n\nThe module also defines two classes:\n1. ClientTLSConfig: Stores TLS configuration parameters\n2. ClientConfig: Stores client configuration including TLS settings"
  }
]

export default function PythonCodeDemo() {
  return (
    <div className="h-screen w-full">
      <CodeEditorWithAgent 
        initialFiles={pythonFiles}
      />
    </div>
  )
}
