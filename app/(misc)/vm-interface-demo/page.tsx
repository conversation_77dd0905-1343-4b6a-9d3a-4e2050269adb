'use client';

import React, { useState, useEffect } from 'react';
import { useNodeRunner } from '@/hooks/useNodeRunner';
import NodeAppIframe from '@/components/NodeAppIframe';
import {
  VMStatusBadge,
  VMActionBar,
  VMList,
  VMCreateForm,
  VMConsole,
  VMResourceMonitor,
  VMFileExplorer,
  VM,
  VMCreateFormData
} from '@/components/vm-interface';

export default function VMInterfaceDemo() {
  const {
    vms,
    loading,
    error,
    listVMs,
    generateNodeApp,
    stopNodeApp,
    deleteNodeApp,
    getIframeUrl
  } = useNodeRunner();
  
  const [selectedVmId, setSelectedVmId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'preview' | 'console' | 'files' | 'resources'>('preview');
  const [showCreateForm, setShowCreateForm] = useState(false);
  
  // Load VMs on mount
  useEffect(() => {
    listVMs();
  }, [listVMs]);
  
  // Find the selected VM
  const selectedVM = vms.find(vm => vm.id === selectedVmId);
  
  // Handle VM creation
  const handleCreateVM = async (data: VMCreateFormData) => {
    try {
      const vm = await generateNodeApp(data);
      if (vm) {
        setSelectedVmId(vm.id);
        setShowCreateForm(false);
      }
    } catch (err) {
      console.error('Failed to create VM:', err);
    }
  };
  
  // Handle VM stop
  const handleStopVM = async (vm: VM) => {
    try {
      await stopNodeApp(vm.id);
      listVMs();
    } catch (err) {
      console.error('Failed to stop VM:', err);
    }
  };
  
  // Handle VM delete
  const handleDeleteVM = async (vm: VM) => {
    if (window.confirm(`Are you sure you want to delete ${vm.name || vm.id}?`)) {
      try {
        await deleteNodeApp(vm.id);
        if (selectedVmId === vm.id) {
          setSelectedVmId(null);
        }
        listVMs();
      } catch (err) {
        console.error('Failed to delete VM:', err);
      }
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">VM Interface Components Demo</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}
      
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Left sidebar */}
        <div className="w-full lg:w-1/3">
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Virtual Machines</h2>
              <button
                onClick={() => setShowCreateForm(!showCreateForm)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
              >
                {showCreateForm ? 'Cancel' : 'New VM'}
              </button>
            </div>
            
            {showCreateForm ? (
              <VMCreateForm
                onSubmit={handleCreateVM}
                onCancel={() => setShowCreateForm(false)}
                loading={loading}
                initialData={{
                  name: `app-${Date.now()}`,
                  template: 'express',
                  port: 3000
                }}
              />
            ) : (
              <VMList
                vms={vms.map(vm => ({
                  ...vm,
                  status: vm.status as any // Type conversion
                }))}
                onSelect={(vm) => setSelectedVmId(vm.id)}
                onStop={handleStopVM}
                onDelete={handleDeleteVM}
                selectedVmId={selectedVmId}
                loading={loading}
                emptyMessage="No virtual machines found. Create one to get started."
              />
            )}
            
            <button
              onClick={listVMs}
              disabled={loading}
              className="mt-4 w-full bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded"
            >
              {loading ? 'Refreshing...' : 'Refresh List'}
            </button>
          </div>
          
          {/* VM Actions */}
          {selectedVM && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium mb-3">VM Actions</h3>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">{selectedVM.name || selectedVM.id}</span>
                  <VMStatusBadge status={selectedVM.status as any} />
                </div>
              </div>
              
              <VMActionBar
                vmId={selectedVM.id}
                status={selectedVM.status as any}
                onStop={() => handleStopVM(selectedVM)}
                onDelete={() => handleDeleteVM(selectedVM)}
                onViewLogs={() => setActiveTab('console')}
                onViewFiles={() => setActiveTab('files')}
                loading={loading}
              />
            </div>
          )}
        </div>
        
        {/* Main content */}
        <div className="w-full lg:w-2/3">
          {!selectedVM ? (
            <div className="bg-white shadow rounded-lg p-12 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No VM Selected</h3>
              <p className="text-gray-500">
                Select a virtual machine from the list or create a new one to get started.
              </p>
            </div>
          ) : (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              {/* Tabs */}
              <div className="bg-gray-100 border-b border-gray-200 px-4 flex overflow-x-auto">
                {(['preview', 'console', 'files', 'resources'] as const).map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`px-4 py-3 text-sm font-medium border-b-2 ${
                      activeTab === tab
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                  </button>
                ))}
              </div>
              
              {/* Tab content */}
              <div className="p-4">
                {activeTab === 'preview' && (
                  <div>
                    <h3 className="text-lg font-medium mb-4">Application Preview</h3>
                    <NodeAppIframe
                      vmId={selectedVM.id}
                      height={500}
                      width="100%"
                      className="border border-gray-300 rounded-lg"
                    />
                  </div>
                )}
                
                {activeTab === 'console' && (
                  <div>
                    <h3 className="text-lg font-medium mb-4">Console Output</h3>
                    <VMConsole
                      vmId={selectedVM.id}
                      height={500}
                      showCommandInput={true}
                      onCommand={(cmd) => console.log('Command:', cmd)}
                    />
                  </div>
                )}
                
                {activeTab === 'files' && (
                  <div>
                    <h3 className="text-lg font-medium mb-4">File Explorer</h3>
                    <VMFileExplorer
                      vmId={selectedVM.id}
                      onFileOpen={(file) => console.log('Open file:', file)}
                    />
                  </div>
                )}
                
                {activeTab === 'resources' && (
                  <div>
                    <h3 className="text-lg font-medium mb-4">Resource Monitor</h3>
                    <VMResourceMonitor
                      vmId={selectedVM.id}
                      showDetailedView={true}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
