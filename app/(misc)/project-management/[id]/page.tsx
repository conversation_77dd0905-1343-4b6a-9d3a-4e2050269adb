import { Metadata } from "next"
import { notFound } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { db } from "@/lib/db"
import { ProjectDashboard } from "@/components/project-management/project-dashboard"

interface ProjectPageProps {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: ProjectPageProps): Promise<Metadata> {
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    return {
      title: "Project - Unauthorized",
    }
  }
  
  const project = await db.project.findUnique({
    where: {
      id: params.id,
      userId: session.user.id as string,
    },
  })
  
  if (!project) {
    return {
      title: "Project - Not Found",
    }
  }
  
  return {
    title: `${project.name} - Project Management`,
    description: project.description,
  }
}

export default async function ProjectManagementPage({ params }: ProjectPageProps) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold mb-4">Unauthorized</h1>
        <p className="text-muted-foreground mb-4">
          You need to be signed in to view this project.
        </p>
        <a
          href="/api/auth/signin"
          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        >
          Sign In
        </a>
      </div>
    )
  }
  
  // Check if project exists and belongs to the user
  const project = await db.project.findUnique({
    where: {
      id: params.id,
      userId: session.user.id as string,
    },
  })
  
  if (!project) {
    notFound()
  }
  
  return (
    <div className="container mx-auto py-6 h-[calc(100vh-4rem)]">
      <ProjectDashboard projectId={params.id} className="h-full" />
    </div>
  )
}
