import { Suspense } from "react"
import type { Metada<PERSON> } from "next"
import { getServerSession } from "next-auth"
import { authOptions } from "../../auth/[...nextauth]/route"
import { redirect } from "next/navigation"
import { db } from "@/lib/db"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { DashboardSkeleton } from "@/components/dashboard/dashboard-skeleton"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatDistanceToNow } from "date-fns"
import { ActivityIcon } from "lucide-react"

export const metadata: Metadata = {
  title: "Activity",
  description: "View your recent activity",
}

export default async function ActivityPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/auth/signin")
  }

  return (
    <DashboardShell>
      <DashboardHeader heading="Activity" text="View your recent activity" />
      <Suspense fallback={<DashboardSkeleton />}>
        <ActivityList userId={session.user.id} />
      </Suspense>
    </DashboardShell>
  )
}

async function ActivityList({ userId }: { userId: string }) {
  const activities = await db.activity.findMany({
    where: { userId },
    orderBy: { timestamp: "desc" },
    take: 50,
  })

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "project_created":
        return <ActivityIcon className="h-4 w-4 text-green-500" />
      case "project_updated":
        return <ActivityIcon className="h-4 w-4 text-blue-500" />
      case "deployment":
        return <ActivityIcon className="h-4 w-4 text-purple-500" />
      case "error":
        return <ActivityIcon className="h-4 w-4 text-red-500" />
      case "user_registered":
        return <ActivityIcon className="h-4 w-4 text-yellow-500" />
      default:
        return <ActivityIcon className="h-4 w-4 text-muted-foreground" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Activity History</CardTitle>
        <CardDescription>Your recent actions and events</CardDescription>
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <div className="flex h-[300px] flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
            <ActivityIcon className="h-10 w-10 text-muted-foreground" />
            <p className="mt-2 text-sm text-muted-foreground">No activity found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-4 rounded-md border p-4">
                <div className="mt-0.5">{getActivityIcon(activity.type)}</div>
                <div className="grid gap-1">
                  <p className="text-sm font-medium">{activity.description}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                  </p>
                  {activity.projectId && (
                    <p className="text-xs text-muted-foreground">
                      Project: {activity.projectId}
                    </p>
                  )}
                  {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                    <details className="mt-2">
                      <summary className="text-xs text-muted-foreground cursor-pointer">
                        Details
                      </summary>
                      <pre className="mt-2 rounded bg-muted p-2 text-xs overflow-auto max-h-[200px]">
                        {JSON.stringify(activity.metadata, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
