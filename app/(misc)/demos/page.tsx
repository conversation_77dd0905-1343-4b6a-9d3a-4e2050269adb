"use client"

import * as React from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowRight, Code, Terminal, FileCode, FolderTree } from "lucide-react"

export default function DemosPage() {
  return (
    <div className="container py-10">
      <div className="flex flex-col items-center justify-center text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Agent Interface Demos</h1>
        <p className="text-xl text-muted-foreground max-w-2xl">
          Explore different implementations of the agent interface with various features and capabilities.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="border-2 border-primary">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileCode className="h-5 w-5 mr-2" />
              VS Code Editor
            </CardTitle>
            <CardDescription>
              VS Code-like editor with file browser and agent
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This demo showcases a modular code editor with a VS Code-like layout, featuring a file browser column, tabs, and integrated agent chat.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/vs-code-editor" className="w-full">
              <Button className="w-full">
                Open Demo
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FolderTree className="h-5 w-5 mr-2" />
              Agent with File Explorer
            </CardTitle>
            <CardDescription>
              Agent interface with integrated file explorer
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This demo showcases the agent interface with a fully functional file explorer that allows you to browse, view, and edit files generated by the agent.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/agent-with-files" className="w-full">
              <Button variant="outline" className="w-full">
                Open Demo
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Terminal className="h-5 w-5 mr-2" />
              Functional Workspace
            </CardTitle>
            <CardDescription>
              A complete working environment with the autonomous programmer
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This workspace provides a fully functional environment where you can interact with the autonomous programmer agent to generate code and build applications.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/functional-workspace" className="w-full">
              <Button variant="outline" className="w-full">
                Open Workspace
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Code className="h-5 w-5 mr-2" />
              Code Chat Demos
            </CardTitle>
            <CardDescription>
              Various demos of the code chat interface
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Explore different demos of the code chat interface, including Python code analysis, recipe site generation, and more.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/code-chat-demos" className="w-full">
              <Button variant="outline" className="w-full">
                View Demos
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
