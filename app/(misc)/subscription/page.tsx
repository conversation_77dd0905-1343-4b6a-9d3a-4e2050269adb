"use client"

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { useSubscription } from '@/hooks/useSubscription'
import { CreditCard, Calendar, AlertCircle, CheckCircle, Clock, FileText, DownloadCloud, RefreshCw, XCircle } from 'lucide-react'
import { formatDate, formatCurrency } from '@/lib/utils'

export default function SubscriptionPage() {
  const { data: session, status: sessionStatus } = useSession()
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState('overview')
  const [isLoading, setIsLoading] = useState(true)
  const [invoices, setInvoices] = useState<any[]>([])
  const [isLoadingInvoices, setIsLoadingInvoices] = useState(false)
  const [isCanceling, setIsCanceling] = useState(false)
  const [isResuming, setIsResuming] = useState(false)
  
  const { 
    subscription, 
    error, 
    isLoading: isLoadingSubscription,
    fetchSubscription,
    cancelSubscription,
    resumeSubscription,
    getInvoices
  } = useSubscription()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/subscription')
    }
  }, [sessionStatus, router])

  // Fetch subscription data
  useEffect(() => {
    if (sessionStatus === 'authenticated') {
      fetchSubscription()
        .then(() => setIsLoading(false))
        .catch(() => setIsLoading(false))
    }
  }, [sessionStatus, fetchSubscription])

  // Fetch invoices when tab changes to invoices
  useEffect(() => {
    if (activeTab === 'invoices' && subscription && !invoices.length) {
      loadInvoices()
    }
  }, [activeTab, subscription])

  const loadInvoices = async () => {
    if (!subscription) return
    
    setIsLoadingInvoices(true)
    try {
      const data = await getInvoices()
      setInvoices(data)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load invoices. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoadingInvoices(false)
    }
  }

  const handleCancelSubscription = async () => {
    if (!subscription) return
    
    setIsCanceling(true)
    try {
      const success = await cancelSubscription()
      
      if (success) {
        toast({
          title: 'Subscription Canceled',
          description: 'Your subscription will end at the current billing period.',
          variant: 'default',
        })
      } else {
        throw new Error('Failed to cancel subscription')
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to cancel subscription. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsCanceling(false)
    }
  }

  const handleResumeSubscription = async () => {
    if (!subscription) return
    
    setIsResuming(true)
    try {
      const success = await resumeSubscription()
      
      if (success) {
        toast({
          title: 'Subscription Resumed',
          description: 'Your subscription has been successfully resumed.',
          variant: 'default',
        })
      } else {
        throw new Error('Failed to resume subscription')
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to resume subscription. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsResuming(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>
      case 'canceled':
        return <Badge variant="destructive">Canceled</Badge>
      case 'past_due':
        return <Badge variant="destructive">Past Due</Badge>
      case 'unpaid':
        return <Badge variant="destructive">Unpaid</Badge>
      case 'trialing':
        return <Badge variant="secondary">Trial</Badge>
      case 'paused':
        return <Badge variant="outline">Paused</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (sessionStatus === 'loading' || isLoading) {
    return (
      <div className="container max-w-4xl py-10">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-4 w-2/3" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!subscription) {
    return (
      <div className="container max-w-4xl py-10">
        <Card>
          <CardHeader>
            <CardTitle>No Active Subscription</CardTitle>
            <CardDescription>
              You don't have an active subscription at the moment.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>No subscription found</AlertTitle>
              <AlertDescription>
                Subscribe to a plan to access premium features.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/subscription/plans')}>
              View Plans
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="container max-w-4xl py-10">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Subscription Management</CardTitle>
              <CardDescription>
                Manage your subscription and billing information
              </CardDescription>
            </div>
            <div>{getStatusBadge(subscription.status)}</div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="invoices">Invoices</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4 pt-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Current Plan
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{subscription.planName}</div>
                    <p className="text-xs text-muted-foreground">
                      {formatCurrency(subscription.amount, subscription.currency)} / {subscription.interval}
                    </p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Billing Period
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>
                        Next billing date: {formatDate(subscription.currentPeriodEnd)}
                      </span>
                    </div>
                    {subscription.cancelAtPeriodEnd && (
                      <div className="mt-2 flex items-center text-amber-500">
                        <Clock className="mr-2 h-4 w-4" />
                        <span>Cancels on {formatDate(subscription.currentPeriodEnd)}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">
                    Payment Method
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <CreditCard className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>
                      {subscription.provider === 'polar' && 'Credit Card'}
                      {subscription.provider === 'payfast' && 'Payfast'}
                      {subscription.provider === 'peach' && 'Peach Payments'}
                      {subscription.provider === 'ozow' && 'Ozow Instant EFT'}
                    </span>
                  </div>
                </CardContent>
              </Card>
              
              <div className="flex flex-col space-y-2">
                {subscription.cancelAtPeriodEnd ? (
                  <Button 
                    onClick={handleResumeSubscription} 
                    disabled={isResuming}
                    className="w-full"
                  >
                    {isResuming ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Resuming Subscription...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Resume Subscription
                      </>
                    )}
                  </Button>
                ) : (
                  <Button 
                    variant="outline" 
                    onClick={handleCancelSubscription}
                    disabled={isCanceling}
                    className="w-full"
                  >
                    {isCanceling ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Canceling Subscription...
                      </>
                    ) : (
                      <>
                        <XCircle className="mr-2 h-4 w-4" />
                        Cancel Subscription
                      </>
                    )}
                  </Button>
                )}
                
                <Button 
                  variant="outline" 
                  onClick={() => router.push('/subscription/plans')}
                  className="w-full"
                >
                  Change Plan
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="invoices" className="pt-4">
              {isLoadingInvoices ? (
                <div className="space-y-2">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : invoices.length > 0 ? (
                <div className="space-y-4">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between border-b pb-4">
                      <div className="space-y-1">
                        <div className="font-medium">
                          {formatDate(invoice.createdAt)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {formatCurrency(invoice.amount, invoice.currency)}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {invoice.status === 'paid' ? (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            Paid
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                            {invoice.status}
                          </Badge>
                        )}
                        <Button variant="ghost" size="sm">
                          <DownloadCloud className="h-4 w-4" />
                          <span className="sr-only">Download</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert>
                  <FileText className="h-4 w-4" />
                  <AlertTitle>No invoices found</AlertTitle>
                  <AlertDescription>
                    You don't have any invoices yet.
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
