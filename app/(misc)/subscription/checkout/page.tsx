"use client"

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { Steps, Step } from "@/components/ui/steps"
import { PaymentProviderSelector } from '@/components/payment/PaymentProviderSelector'
import { PolarPaymentForm } from '@/components/payment/PolarPaymentForm'
import { PayfastPaymentForm } from '@/components/payment/PayfastPaymentForm'
import { PeachPaymentForm } from '@/components/payment/PeachPaymentForm'
import { OzowPaymentForm } from '@/components/payment/OzowPaymentForm'
import { SubscriptionPlan } from '@/components/payment/SubscriptionPlanSelector'
import { AlertCircle, ArrowLeft, CreditCard } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

export default function CheckoutPage() {
  const { data: session, status: sessionStatus } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const [plan, setPlan] = useState<SubscriptionPlan | null>(null)
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null)
  
  const planId = searchParams.get('plan')

  // Redirect to login if not authenticated
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/subscription/checkout')
    }
  }, [sessionStatus, router])

  // Fetch plan data
  useEffect(() => {
    if (sessionStatus === 'authenticated' && planId) {
      fetchPlan(planId)
    } else if (sessionStatus === 'authenticated' && !planId) {
      router.push('/subscription/plans')
    }
  }, [sessionStatus, planId])

  const fetchPlan = async (id: string) => {
    setIsLoading(true)
    try {
      // In a real implementation, this would fetch from your API
      const response = await fetch(`/api/plans/${id}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch plan')
      }
      
      const data = await response.json()
      setPlan(data)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load subscription plan. Please try again.',
        variant: 'destructive',
      })
      router.push('/subscription/plans')
    } finally {
      setIsLoading(false)
    }
  }

  const handleProviderSelect = (provider: string) => {
    setSelectedProvider(provider)
    setCurrentStep(2)
  }

  const handlePaymentSuccess = (transactionId: string) => {
    toast({
      title: 'Payment Successful',
      description: 'Your subscription has been activated.',
      variant: 'default',
    })
    router.push(`/subscription?success=true&txid=${transactionId}`)
  }

  const handlePaymentError = (error: Error) => {
    toast({
      title: 'Payment Failed',
      description: error.message || 'There was an error processing your payment.',
      variant: 'destructive',
    })
  }

  const handleBack = () => {
    if (currentStep === 2) {
      setCurrentStep(1)
      setSelectedProvider(null)
    } else {
      router.push('/subscription/plans')
    }
  }

  // Mock plan data for development
  const mockPlan: SubscriptionPlan = {
    id: 'pro-monthly',
    name: 'Pro Plan',
    price: 299,
    currency: 'R',
    interval: 'monthly',
    description: 'For professionals and growing teams',
    features: [
      'Unlimited projects',
      'Advanced AI code generation',
      'Priority support',
      'Custom components',
      'API access'
    ],
    popular: true
  }

  // Use mock data if plan is not loaded yet
  const displayPlan = plan || (planId === 'pro-monthly' ? mockPlan : null)

  if (sessionStatus === 'loading' || isLoading) {
    return (
      <div className="container max-w-4xl py-10">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-4 w-2/3" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!displayPlan) {
    return (
      <div className="container max-w-4xl py-10">
        <Card>
          <CardHeader>
            <CardTitle>Plan Not Found</CardTitle>
            <CardDescription>
              The subscription plan you selected could not be found.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                Please select a valid subscription plan.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/subscription/plans')}>
              View Plans
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="container max-w-4xl py-10">
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <Button variant="ghost" size="sm" onClick={handleBack} className="mr-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <CardTitle>Complete Your Subscription</CardTitle>
              <CardDescription>
                Subscribe to {displayPlan.name} - {formatCurrency(displayPlan.price, displayPlan.currency)}/{displayPlan.interval}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Steps currentStep={currentStep} className="mb-8">
            <Step title="Select Payment Method" />
            <Step title="Complete Payment" />
          </Steps>
          
          {currentStep === 1 && (
            <PaymentProviderSelector 
              onSelect={handleProviderSelect}
              preselected={selectedProvider as any}
            />
          )}
          
          {currentStep === 2 && selectedProvider === 'polar' && (
            <PolarPaymentForm 
              plan={displayPlan}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          )}
          
          {currentStep === 2 && selectedProvider === 'payfast' && (
            <PayfastPaymentForm 
              plan={displayPlan}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          )}
          
          {currentStep === 2 && selectedProvider === 'peach' && (
            <PeachPaymentForm 
              plan={displayPlan}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          )}
          
          {currentStep === 2 && selectedProvider === 'ozow' && (
            <OzowPaymentForm 
              plan={displayPlan}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          )}
        </CardContent>
        <CardFooter className="flex flex-col border-t pt-6">
          <div className="w-full rounded-md bg-muted p-4">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm font-medium">Order Summary</p>
                <p className="text-sm text-muted-foreground">{displayPlan.name} Subscription</p>
              </div>
              <p className="text-sm font-bold">
                {formatCurrency(displayPlan.price, displayPlan.currency)}/{displayPlan.interval.charAt(0)}
              </p>
            </div>
            <Separator className="my-4" />
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">Total</p>
              <p className="text-sm font-bold">
                {formatCurrency(displayPlan.price, displayPlan.currency)}/{displayPlan.interval.charAt(0)}
              </p>
            </div>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
