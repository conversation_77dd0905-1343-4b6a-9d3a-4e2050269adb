"use client"

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { SubscriptionPlanSelector } from '@/components/payment/SubscriptionPlanSelector'
import { useSubscription } from '@/hooks/useSubscription'
import { AlertCircle, CheckCircle, CreditCard } from 'lucide-react'

export default function PlansPage() {
  const { data: session, status: sessionStatus } = useSession()
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly')
  const [plans, setPlans] = useState<any[]>([])
  
  const { subscription, fetchSubscription } = useSubscription()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/subscription/plans')
    }
  }, [sessionStatus, router])

  // Fetch subscription data and plans
  useEffect(() => {
    if (sessionStatus === 'authenticated') {
      Promise.all([
        fetchSubscription(),
        fetchPlans()
      ])
        .then(() => setIsLoading(false))
        .catch(() => setIsLoading(false))
    }
  }, [sessionStatus])

  const fetchPlans = async () => {
    try {
      // In a real implementation, this would fetch from your API
      // For now, we'll use mock data
      setPlans(getMockPlans())
      return true
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load subscription plans. Please try again.',
        variant: 'destructive',
      })
      return false
    }
  }

  const handlePlanSelect = (plan: any) => {
    if (subscription && plan.id === subscription.planId) {
      router.push('/subscription')
      return
    }
    
    router.push(`/subscription/checkout?plan=${plan.id}`)
  }

  const getMockPlans = () => {
    const monthlyPlans = [
      {
        id: 'starter-monthly',
        name: 'Starter',
        price: 99,
        currency: 'R',
        interval: 'monthly',
        description: 'Perfect for individuals and small projects',
        features: [
          'Up to 5 projects',
          'Basic AI code generation',
          'Community support'
        ]
      },
      {
        id: 'pro-monthly',
        name: 'Pro',
        price: 299,
        currency: 'R',
        interval: 'monthly',
        description: 'For professionals and growing teams',
        features: [
          'Unlimited projects',
          'Advanced AI code generation',
          'Priority support',
          'Custom components',
          'API access'
        ],
        popular: true
      },
      {
        id: 'enterprise-monthly',
        name: 'Enterprise',
        price: 999,
        currency: 'R',
        interval: 'monthly',
        description: 'For large organizations with custom needs',
        features: [
          'Everything in Pro',
          'Dedicated account manager',
          'Custom integrations',
          'SLA guarantees',
          'On-premise deployment options'
        ]
      }
    ]
    
    const yearlyPlans = [
      {
        id: 'starter-yearly',
        name: 'Starter',
        price: 990,
        currency: 'R',
        interval: 'yearly',
        description: 'Perfect for individuals and small projects',
        features: [
          'Up to 5 projects',
          'Basic AI code generation',
          'Community support'
        ]
      },
      {
        id: 'pro-yearly',
        name: 'Pro',
        price: 2990,
        currency: 'R',
        interval: 'yearly',
        description: 'For professionals and growing teams',
        features: [
          'Unlimited projects',
          'Advanced AI code generation',
          'Priority support',
          'Custom components',
          'API access'
        ],
        popular: true
      },
      {
        id: 'enterprise-yearly',
        name: 'Enterprise',
        price: 9990,
        currency: 'R',
        interval: 'yearly',
        description: 'For large organizations with custom needs',
        features: [
          'Everything in Pro',
          'Dedicated account manager',
          'Custom integrations',
          'SLA guarantees',
          'On-premise deployment options'
        ]
      }
    ]
    
    return billingInterval === 'monthly' ? monthlyPlans : yearlyPlans
  }

  if (sessionStatus === 'loading' || isLoading) {
    return (
      <div className="container max-w-5xl py-10">
        <div className="space-y-4">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-10 w-40 mx-auto" />
          <div className="grid gap-6 md:grid-cols-3">
            <Skeleton className="h-96 w-full" />
            <Skeleton className="h-96 w-full" />
            <Skeleton className="h-96 w-full" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container max-w-5xl py-10">
      <div className="space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Choose Your Plan</h1>
          <p className="text-muted-foreground">
            Select the plan that best fits your needs
          </p>
        </div>
        
        {subscription && (
          <Alert className="max-w-md mx-auto">
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>You have an active subscription</AlertTitle>
            <AlertDescription>
              You're currently on the {subscription.planName} plan.
              {subscription.cancelAtPeriodEnd 
                ? " Your subscription will end on the next billing date." 
                : " You can change your plan below."}
            </AlertDescription>
          </Alert>
        )}
        
        <Tabs 
          defaultValue="monthly" 
          value={billingInterval}
          onValueChange={(value) => setBillingInterval(value as 'monthly' | 'yearly')}
          className="w-full"
        >
          <div className="flex justify-center">
            <TabsList className="grid w-64 grid-cols-2">
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
              <TabsTrigger value="yearly">Yearly (Save 15%)</TabsTrigger>
            </TabsList>
          </div>
          
          <TabsContent value="monthly" className="mt-6">
            <SubscriptionPlanSelector 
              plans={plans}
              onSelect={handlePlanSelect}
              preselected={subscription?.planId}
            />
          </TabsContent>
          
          <TabsContent value="yearly" className="mt-6">
            <SubscriptionPlanSelector 
              plans={plans}
              onSelect={handlePlanSelect}
              preselected={subscription?.planId}
            />
          </TabsContent>
        </Tabs>
        
        <div className="text-center mt-8">
          <p className="text-sm text-muted-foreground mb-2">
            All plans include a 14-day money-back guarantee
          </p>
          {subscription ? (
            <Button 
              variant="outline" 
              onClick={() => router.push('/subscription')}
            >
              Back to Subscription
            </Button>
          ) : (
            <Button 
              variant="outline" 
              onClick={() => router.push('/')}
            >
              Back to Home
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
