'use client';

import { useChat } from 'ai/react';
import { useState, useRef, useEffect } from 'react';

interface ChatbotProps {
  endpoint?: string;
  initialMessages?: any[];
  placeholder?: string;
  className?: string;
  maxSteps?: number;
}

export default function Chatbot({
  endpoint = '/api/chat',
  initialMessages = [],
  placeholder = 'Type your message...',
  className = '',
  maxSteps = 5,
}: ChatbotProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const { messages, input, handleInputChange, handleSubmit, isLoading, addMessage, addToolResult } = useChat({
    api: endpoint,
    initialMessages,
    maxSteps,
    onToolCall: async ({ toolCall }) => {
      // Handle client-side tools here
      if (toolCall.toolName === 'getClientInfo') {
        return {
          userAgent: navigator.userAgent,
          language: navigator.language,
          screenSize: `${window.innerWidth}x${window.innerHeight}`,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        };
      }
      return null;
    },
  });

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle file upload
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', files[0]);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('File upload failed');
      }

      const data = await response.json();

      // Add a user message about the uploaded file
      addMessage({
        id: crypto.randomUUID(),
        role: 'user',
        content: `I've uploaded a file: ${files[0].name}`,
      });

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Failed to upload file. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${
              message.role === 'user' ? 'justify-end' : 'justify-start'
            }`}
          >
            <div
              className={`max-w-3/4 rounded-lg p-3 ${
                message.role === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-800'
              }`}
            >
              {message.parts.map((part, i) => {
                switch (part.type) {
                  case 'text':
                    return <div key={`${message.id}-${i}`}>{part.text}</div>;

                  case 'tool-invocation':
                    switch (part.toolInvocation.state) {
                      case 'partial-call':
                        return (
                          <div key={`${message.id}-${i}`} className="text-xs italic">
                            Thinking...
                          </div>
                        );

                      case 'call':
                        return (
                          <div key={`${message.id}-${i}`} className="text-xs">
                            <div className="font-bold">Using tool: {part.toolInvocation.toolName}</div>
                            <div className="italic">
                              {JSON.stringify(part.toolInvocation.args, null, 2)}
                            </div>
                          </div>
                        );

                      case 'result':
                        return (
                          <div key={`${message.id}-${i}`} className="text-xs">
                            <div className="font-bold">Tool result:</div>
                            <pre className="whitespace-pre-wrap text-xs overflow-x-auto">
                              {typeof part.toolInvocation.result === 'string'
                                ? part.toolInvocation.result
                                : JSON.stringify(part.toolInvocation.result, null, 2)}
                            </pre>
                          </div>
                        );
                    }
                    break;

                  case 'step-start':
                    return i > 0 ? (
                      <div key={`${message.id}-${i}`} className="border-t border-gray-300 my-2"></div>
                    ) : null;
                }
              })}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <div className="border-t p-4">
        <form onSubmit={handleSubmit} className="flex items-center space-x-2">
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors"
          >
            {isUploading ? (
              <span className="animate-pulse">⏳</span>
            ) : (
              <span>📎</span>
            )}
          </button>
          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileUpload}
            className="hidden"
          />
          <input
            type="text"
            value={input}
            onChange={handleInputChange}
            placeholder={placeholder}
            disabled={isLoading || isUploading}
            className="flex-1 p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            disabled={isLoading || isUploading || !input.trim()}
            className="p-2 rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors disabled:opacity-50"
          >
            {isLoading ? (
              <span className="animate-pulse">...</span>
            ) : (
              <span>Send</span>
            )}
          </button>
        </form>
      </div>
    </div>
  );
}
