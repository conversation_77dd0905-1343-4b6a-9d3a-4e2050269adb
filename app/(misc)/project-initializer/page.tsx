import { ProjectInitializer } from '@/components/project-initializer'

export const metadata = {
  title: 'AI Project Initializer',
  description: 'Initialize your project with AI assistance',
}

export default function ProjectInitializerPage() {
  return (
    <div className="container py-10">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">AI Project Initializer</h1>
          <p className="text-muted-foreground">
            Describe your project idea and let AI help you set it up with the right technologies and structure
          </p>
        </div>
        
        <ProjectInitializer />
        
        <div className="mt-8 space-y-4">
          <h2 className="text-xl font-semibold">How it works</h2>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-2">
              <div className="font-medium">1. Describe your project</div>
              <p className="text-sm text-muted-foreground">
                Provide details about what you want to build, including features, technologies, and any specific requirements.
              </p>
            </div>
            <div className="space-y-2">
              <div className="font-medium">2. AI analyzes requirements</div>
              <p className="text-sm text-muted-foreground">
                Our AI will analyze your requirements, suggest appropriate technologies, and create a project structure.
              </p>
            </div>
            <div className="space-y-2">
              <div className="font-medium">3. Start developing</div>
              <p className="text-sm text-muted-foreground">
                Once your project is initialized, you can start developing with the generated file structure and configurations.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}