"use client"

import * as React from "react"
import { useState } from "react"
import { ZencoderChatInput } from "@/components/zencoder-chat-input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { 
  Code, 
  Server, 
  Database, 
  Terminal, 
  Sparkles,
  User,
  ArrowLeft
} from "lucide-react"

// Define message type
interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  agentType: 'coding-agent' | 'devops-agent' | 'database-agent' | 'autonomous-programmer' | 'default'
  context?: {
    type: 'codebase' | 'file' | 'folder' | 'none'
    path?: string
  }
  timestamp: Date
}

export default function ZencoderChatInputDemo() {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  // Handle sending a message
  const handleSendMessage = (
    content: string, 
    agentType: 'coding-agent' | 'devops-agent' | 'database-agent' | 'autonomous-programmer' | 'default',
    context: {
      type: 'codebase' | 'file' | 'folder' | 'none'
      path?: string
    }
  ) => {
    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content,
      role: 'user',
      agentType,
      context,
      timestamp: new Date()
    }
    
    setMessages(prev => [...prev, userMessage])
    
    // Simulate AI response
    setIsLoading(true)
    
    setTimeout(() => {
      // Generate response based on agent type
      let responseContent = ""
      
      switch (agentType) {
        case 'coding-agent':
          responseContent = `I'll help you with your coding question. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
          break
        case 'devops-agent':
          responseContent = `I'll assist with your DevOps needs. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
          break
        case 'database-agent':
          responseContent = `I'll help with your database question. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
          break
        case 'autonomous-programmer':
          responseContent = `I'll autonomously implement features for you. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
          break
        default:
          responseContent = `I'll help you with your question. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
      }
      
      // Add AI response
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        content: responseContent,
        role: 'assistant',
        agentType,
        context,
        timestamp: new Date()
      }
      
      setMessages(prev => [...prev, assistantMessage])
      setIsLoading(false)
    }, 1500)
  }
  
  // Get agent icon
  const getAgentIcon = (agentType: string) => {
    switch (agentType) {
      case 'coding-agent':
        return <Code className="h-4 w-4 text-blue-400" />
      case 'devops-agent':
        return <Server className="h-4 w-4 text-green-400" />
      case 'database-agent':
        return <Database className="h-4 w-4 text-amber-400" />
      case 'autonomous-programmer':
        return <Terminal className="h-4 w-4 text-purple-400" />
      default:
        return <Sparkles className="h-4 w-4 text-gray-400" />
    }
  }
  
  // Get context icon
  const getContextIcon = (contextType: string) => {
    switch (contextType) {
      case 'codebase':
        return <Code className="h-4 w-4 text-blue-400" />
      case 'file':
        return <Code className="h-4 w-4 text-green-400" />
      case 'folder':
        return <Code className="h-4 w-4 text-amber-400" />
      default:
        return null
    }
  }
  
  // Get agent name
  const getAgentName = (agentType: string) => {
    switch (agentType) {
      case 'coding-agent':
        return 'Coding Agent'
      case 'devops-agent':
        return 'DevOps Agent'
      case 'database-agent':
        return 'Database Agent'
      case 'autonomous-programmer':
        return 'Autonomous Programmer'
      default:
        return 'Zencoder'
    }
  }
  
  return (
    <div className="flex flex-col min-h-screen bg-[#1A1A1A] text-white">
      <header className="border-b border-[#333333] p-4">
        <div className="container flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <a href="/">
                <ArrowLeft className="h-5 w-5" />
              </a>
            </Button>
            <h1 className="text-xl font-semibold">Zencoder Chat Input Demo</h1>
          </div>
        </div>
      </header>
      
      <main className="flex-1 container py-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-[1fr_300px]">
          <Card className="bg-[#1E1E1E] border-[#333333] text-white">
            <CardHeader>
              <CardTitle>Chat Interface</CardTitle>
              <CardDescription className="text-[#999999]">
                Try the Zencoder chat input with agent and context selection
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[500px] p-4">
                <div className="space-y-4">
                  {messages.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <Sparkles className="h-12 w-12 text-[#333333] mb-4" />
                      <h3 className="text-lg font-medium mb-2">No messages yet</h3>
                      <p className="text-sm text-[#999999] max-w-md">
                        Start a conversation by typing a message below. Use / to select an agent and @ to select a context.
                      </p>
                    </div>
                  ) : (
                    messages.map(message => (
                      <div key={message.id} className="flex gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className={
                            message.role === 'user' 
                              ? "bg-[#333333] text-white" 
                              : "bg-[#252525] text-white"
                          }>
                            {message.role === 'user' ? (
                              <User className="h-4 w-4" />
                            ) : (
                              getAgentIcon(message.agentType)
                            )}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">
                              {message.role === 'user' ? 'You' : getAgentName(message.agentType)}
                            </span>
                            <span className="text-xs text-[#999999]">
                              {message.timestamp.toLocaleTimeString()}
                            </span>
                            {message.context && message.context.type !== 'none' && (
                              <Badge variant="outline" className="bg-[#252525] border-[#333333] text-xs">
                                {message.context.type === 'file' || message.context.type === 'folder' 
                                  ? message.context.path 
                                  : message.context.type}
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-[#E0E0E0]">
                            {message.content}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
            <CardFooter className="border-t border-[#333333] p-4">
              <ZencoderChatInput 
                onSendMessage={handleSendMessage}
                isLoading={isLoading}
              />
            </CardFooter>
          </Card>
          
          <Card className="bg-[#1E1E1E] border-[#333333] text-white">
            <CardHeader>
              <CardTitle>Instructions</CardTitle>
              <CardDescription className="text-[#999999]">
                How to use the Zencoder chat input
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <div>
                  <h3 className="font-medium mb-1">Agent Selection</h3>
                  <p className="text-[#999999] mb-2">
                    Type <code className="bg-[#252525] px-1 rounded">/</code> to select an agent:
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-blue-400" />
                      <span>Coding Agent</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Server className="h-4 w-4 text-green-400" />
                      <span>DevOps Agent</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Database className="h-4 w-4 text-amber-400" />
                      <span>Database Agent</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Terminal className="h-4 w-4 text-purple-400" />
                      <span>Autonomous Programmer</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-medium mb-1">Context Selection</h3>
                  <p className="text-[#999999] mb-2">
                    Type <code className="bg-[#252525] px-1 rounded">@</code> to select a context:
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-blue-400" />
                      <span>Codebase</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-green-400" />
                      <span>File (e.g., src/main.ts)</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-amber-400" />
                      <span>Folder (e.g., src/components)</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-medium mb-1">Keyboard Shortcuts</h3>
                  <ul className="space-y-2">
                    <li className="flex items-center justify-between">
                      <span>Send message</span>
                      <code className="bg-[#252525] px-1 rounded">Enter</code>
                    </li>
                    <li className="flex items-center justify-between">
                      <span>New line</span>
                      <code className="bg-[#252525] px-1 rounded">Shift + Enter</code>
                    </li>
                    <li className="flex items-center justify-between">
                      <span>Close menus</span>
                      <code className="bg-[#252525] px-1 rounded">Escape</code>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
