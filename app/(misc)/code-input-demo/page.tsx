"use client"

import * as React from "react"
import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CodeInputInterface } from "@/components/code-input-interface"
import { ScrollArea } from "@/components/ui/scroll-area"
import { MessageSquare, User } from "lucide-react"

// Define message types
type MessageRole = 'user' | 'assistant'

interface Message {
  id: string
  role: MessageRole
  content: string
}

export default function CodeInputDemo() {
  // State for messages
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  // Handle send message
  const handleSendMessage = async (message: string) => {
    // Create new user message
    const newUserMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: message
    }
    
    // Add user message to messages
    setMessages(prev => [...prev, newUserMessage])
    
    // Set loading state
    setIsLoading(true)
    
    try {
      // Simulate response delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Create assistant response
      const responseContent = `I'll help you with that request: "${message}"`
      
      // Add assistant message
      setMessages(prev => [...prev, {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: responseContent
      }])
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsLoading(false)
    }
  }
  
  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold mb-6">Code Input Interface Demo</h1>
      
      <Card className="bg-[#1e1e1e] border-[#333] text-white">
        <CardHeader>
          <CardTitle className="text-white">Chat Interface</CardTitle>
          <CardDescription className="text-gray-400">
            Try the code input interface with a dark theme
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="flex flex-col h-[500px]">
            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-6">
                {messages.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <MessageSquare className="h-12 w-12 text-[#333] mb-4" />
                    <h3 className="text-lg font-medium mb-2">No messages yet</h3>
                    <p className="text-sm text-gray-400 max-w-md">
                      Type a message below to start a conversation
                    </p>
                  </div>
                ) : (
                  messages.map(message => (
                    <div key={message.id} className="space-y-2">
                      {message.role === 'user' ? (
                        <>
                          <div className="flex items-center">
                            <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center mr-2">
                              <User className="h-3 w-3 text-white" />
                            </div>
                            <span className="font-medium text-sm">You</span>
                          </div>
                          <div className="bg-[#252525] rounded-lg p-3">
                            <p className="text-sm">{message.content}</p>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="flex items-center">
                            <div className="w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mr-2">
                              <MessageSquare className="h-3 w-3 text-white" />
                            </div>
                            <span className="font-medium text-sm">Coding Agent</span>
                          </div>
                          <div className="text-sm">
                            {message.content}
                          </div>
                        </>
                      )}
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
            
            {/* Input */}
            <div className="p-3 border-t border-[#333]">
              <CodeInputInterface
                onSendMessage={handleSendMessage}
                isLoading={isLoading}
                placeholder="New message"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
