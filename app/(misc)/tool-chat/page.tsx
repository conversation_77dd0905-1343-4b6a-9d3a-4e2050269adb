'use client';

import { ToolEnabledChatbot } from '@/components/chatbot/tool-enabled-chatbot';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function ToolChatPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">AI Tools & Chatbot</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="h-[calc(100vh-12rem)]">
            <CardHeader className="pb-2">
              <CardTitle>Tool-Enabled Chatbot</CardTitle>
              <CardDescription>
                Chat with an AI assistant that can use tools to help you with development tasks
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0 h-[calc(100%-5rem)]">
              <ToolEnabledChatbot
                apiEndpoint="/api/tool-chat"
                initialMessages={[
                  {
                    role: 'assistant',
                    content: "Hello! I'm your AI development assistant. I can help you with various tasks like managing projects, setting up environments, and deploying applications. What would you like to work on today?"
                  }
                ]}
              />
            </CardContent>
          </Card>
        </div>

        <div>
          <Tabs defaultValue="tools">
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="tools">Tool Explorer</TabsTrigger>
              <TabsTrigger value="info">Documentation</TabsTrigger>
            </TabsList>

            <TabsContent value="tools" className="mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Tool Explorer</CardTitle>
                  <CardDescription>
                    Directly execute tools to see how they work
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="p-4 bg-muted rounded-md">
                    <p className="text-sm">Tool Explorer will be available soon.</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="info" className="mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>About AI Tools</CardTitle>
                  <CardDescription>
                    How to use the tool-enabled chatbot
                  </CardDescription>
                </CardHeader>
                <CardContent className="prose prose-sm">
                  <h3>Available Tool Categories</h3>
                  <ul>
                    <li><strong>Project Management</strong> - Create and manage projects</li>
                    <li><strong>Firecracker VM</strong> - Control virtual machines</li>
                    <li><strong>Git Integration</strong> - Manage Git repositories</li>
                    <li><strong>Dependency Management</strong> - Install and update packages</li>
                    <li><strong>Build Service</strong> - Build applications</li>
                    <li><strong>Testing Service</strong> - Run tests</li>
                    <li><strong>Deployment Service</strong> - Deploy applications</li>
                    <li><strong>File Sync</strong> - Synchronize files</li>
                    <li><strong>Knowledge Graph</strong> - Analyze code</li>
                  </ul>

                  <h3>Example Prompts</h3>
                  <ul>
                    <li>"Create a new project called 'my-app'"</li>
                    <li>"Start a VM for my project"</li>
                    <li>"Install React and TypeScript in my project"</li>
                    <li>"Run tests for my project"</li>
                    <li>"Deploy my application to production"</li>
                    <li>"Analyze the code complexity of my project"</li>
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
