"use client"

import * as React from "react"
import { useEffect } from "react"
import { CodeEditorWithAgent } from "@/components/code-editor-with-agent"
import { useFileStore } from "@/lib/stores/file-store"

export default function VSCodeEditorPage() {
  // Get file store
  const { addFile } = useFileStore()
  
  // Add some sample files on component mount
  useEffect(() => {
    // Add sample files
    addFile('/package.json', `{
  "name": "sample-project",
  "version": "1.0.0",
  "description": "A sample project",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.2",
    "mongoose": "^7.5.0"
  },
  "devDependencies": {
    "jest": "^29.6.4",
    "nodemon": "^3.0.1"
  }
}`, 'json')

    addFile('/index.js', `const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/users', require('./routes/users'));
app.use('/api/posts', require('./routes/posts'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).send('Something broke!');
});

// Start server
app.listen(port, () => {
  console.log(\`Server running on port \${port}\`);
});`, 'javascript')

    addFile('/routes/users.js', `const express = require('express');
const router = express.Router();
const User = require('../models/User');

// Get all users
router.get('/', async (req, res) => {
  try {
    const users = await User.find();
    res.json(users);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

// Get one user
router.get('/:id', getUser, (req, res) => {
  res.json(res.user);
});

// Create a user
router.post('/', async (req, res) => {
  const user = new User({
    name: req.body.name,
    email: req.body.email,
    password: req.body.password
  });

  try {
    const newUser = await user.save();
    res.status(201).json(newUser);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
});

// Update a user
router.patch('/:id', getUser, async (req, res) => {
  if (req.body.name != null) {
    res.user.name = req.body.name;
  }
  if (req.body.email != null) {
    res.user.email = req.body.email;
  }
  if (req.body.password != null) {
    res.user.password = req.body.password;
  }

  try {
    const updatedUser = await res.user.save();
    res.json(updatedUser);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
});

// Delete a user
router.delete('/:id', getUser, async (req, res) => {
  try {
    await res.user.remove();
    res.json({ message: 'User deleted' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

// Middleware to get user by ID
async function getUser(req, res, next) {
  let user;
  try {
    user = await User.findById(req.params.id);
    if (user == null) {
      return res.status(404).json({ message: 'Cannot find user' });
    }
  } catch (err) {
    return res.status(500).json({ message: err.message });
  }

  res.user = user;
  next();
}

module.exports = router;`, 'javascript')

    addFile('/models/User.js', `const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  password: {
    type: String,
    required: true
  },
  date: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('User', userSchema);`, 'javascript')

    addFile('/README.md', `# Sample Express API

This is a sample Express.js API with MongoDB integration.

## Features

- RESTful API endpoints for users and posts
- MongoDB integration with Mongoose
- JWT authentication
- Error handling middleware

## Getting Started

1. Clone the repository
2. Install dependencies: \`npm install\`
3. Create a \`.env\` file with your MongoDB connection string
4. Start the server: \`npm start\`

## API Endpoints

### Users

- GET /api/users - Get all users
- GET /api/users/:id - Get a specific user
- POST /api/users - Create a new user
- PATCH /api/users/:id - Update a user
- DELETE /api/users/:id - Delete a user

### Posts

- GET /api/posts - Get all posts
- GET /api/posts/:id - Get a specific post
- POST /api/posts - Create a new post
- PATCH /api/posts/:id - Update a post
- DELETE /api/posts/:id - Delete a post

## License

MIT`, 'markdown')
  }, [addFile])
  
  return (
    <div className="h-screen">
      <CodeEditorWithAgent className="h-full" />
    </div>
  )
}
