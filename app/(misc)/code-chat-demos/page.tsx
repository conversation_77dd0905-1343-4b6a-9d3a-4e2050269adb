"use client"

import * as React from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"

export default function CodeChatDemos() {
  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-8">Code Chat Interface Demos</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="border-2 border-primary">
          <CardHeader>
            <CardTitle>Fixed Agent Demo</CardTitle>
            <CardDescription>
              A demo with a fully functional agent that can generate code (FIXED VERSION)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This demo showcases the agent with real functionality, capable of generating files and analyzing code.
              This version has been fixed to work properly with the demo API endpoints.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/fixed-agent-demo" className="w-full">
              <Button className="w-full">View Demo</Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Working Agent Demo</CardTitle>
            <CardDescription>
              A demo with a fully functional agent that can generate code
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This demo showcases the agent with real functionality, capable of generating files and analyzing code.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/working-agent-demo" className="w-full">
              <Button className="w-full">View Demo</Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Python Code Demo</CardTitle>
            <CardDescription>
              A demo showing Python code analysis with the agent chat interface
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This demo showcases the agent analyzing Python code from the Hatchet SDK, explaining functions and classes.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/python-code-demo" className="w-full">
              <Button className="w-full">View Demo</Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recipe Site Demo</CardTitle>
            <CardDescription>
              A demo showing JavaScript and HTML code for a recipe site
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This demo showcases the agent helping to build a modern, orange-themed recipe sharing site with Tailwind CSS.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/recipe-site-demo" className="w-full">
              <Button className="w-full">View Demo</Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Generic Code Editor Demo</CardTitle>
            <CardDescription>
              A demo with the basic code editor and agent chat interface
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This demo shows the generic implementation of the code editor with agent chat interface.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/code-editor-demo" className="w-full">
              <Button className="w-full">View Demo</Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
