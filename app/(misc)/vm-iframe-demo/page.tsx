'use client';

import React, { useState, useEffect } from 'react';
import { useNodeRunner } from '@/hooks/useNodeRunner';
import { VMIframeDemo } from '@/components/vm-interface';

export default function VMIframeDemoPage() {
  const {
    vms,
    loading,
    error,
    listVMs,
    generateNodeApp,
    stopNodeApp,
    deleteNodeApp,
  } = useNodeRunner();
  
  const [selectedVmId, setSelectedVmId] = useState<string | null>(null);
  const [creatingVM, setCreatingVM] = useState(false);
  
  // Load VMs on mount
  useEffect(() => {
    listVMs();
  }, [listVMs]);
  
  // Create a demo VM if none exists
  const createDemoVM = async () => {
    try {
      setCreatingVM(true);
      
      const vm = await generateNodeApp({
        name: `iframe-demo-${Date.now()}`,
        description: 'A demo application for the interactive iframe',
        template: 'express',
        dependencies: {
          express: '^4.18.2',
          'socket.io': '^4.7.2'
        },
        port: 3000
      });
      
      if (vm) {
        setSelectedVmId(vm.id);
      }
    } catch (err) {
      console.error('Failed to create VM:', err);
    } finally {
      setCreatingVM(false);
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Enhanced VM Iframe Components</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}
      
      {/* VM Selection */}
      <div className="bg-white shadow rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Select a Virtual Machine</h2>
        
        {loading ? (
          <div className="flex justify-center items-center h-20">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : vms.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">No virtual machines found.</p>
            <button
              onClick={createDemoVM}
              disabled={creatingVM}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              {creatingVM ? 'Creating...' : 'Create Demo VM'}
            </button>
          </div>
        ) : (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
              {vms.map(vm => (
                <div
                  key={vm.id}
                  onClick={() => setSelectedVmId(vm.id)}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedVmId === vm.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{vm.name || vm.id}</h3>
                      <p className="text-sm text-gray-500">{vm.description || 'No description'}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      vm.status === 'running' ? 'bg-green-100 text-green-800' :
                      vm.status === 'stopped' ? 'bg-gray-100 text-gray-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {vm.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="flex justify-between">
              <button
                onClick={listVMs}
                disabled={loading}
                className="text-blue-600 hover:text-blue-800"
              >
                Refresh List
              </button>
              
              <button
                onClick={createDemoVM}
                disabled={creatingVM}
                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
              >
                {creatingVM ? 'Creating...' : 'Create Another VM'}
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* VM Iframe Demo */}
      {selectedVmId ? (
        <div className="bg-white shadow rounded-lg p-6">
          <VMIframeDemo
            vmId={selectedVmId}
            interactive={true}
            height={600}
            width="100%"
          />
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg p-12 text-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No VM Selected</h3>
          <p className="text-gray-500">
            Select a virtual machine from the list or create a new one to get started.
          </p>
        </div>
      )}
      
      {/* Documentation */}
      <div className="bg-white shadow rounded-lg p-6 mt-8">
        <h2 className="text-xl font-semibold mb-4">Documentation</h2>
        
        <div className="prose max-w-none">
          <h3>Enhanced VM Iframe Components</h3>
          <p>
            These components provide an enhanced way to display and interact with Node.js applications
            running in Firecracker microVMs.
          </p>
          
          <h4>VMIframe</h4>
          <p>
            A basic iframe component with additional features like responsive sizing, error handling,
            loading states, and more.
          </p>
          <pre className="bg-gray-100 p-3 rounded">{`<VMIframe
  vmId="your-vm-id"
  height="600px"
  width="100%"
  showControls={true}
  showStatusOverlay={true}
  allowFullscreen={true}
  theme="system"
/>`}</pre>
          
          <h4>VMInteractiveIframe</h4>
          <p>
            An enhanced iframe component that adds two-way communication capabilities between the parent
            window and the iframe content.
          </p>
          <pre className="bg-gray-100 p-3 rounded">{`<VMInteractiveIframe
  vmId="your-vm-id"
  height="600px"
  width="100%"
  onMessage={(message) => console.log('Received message:', message)}
  onReady={() => console.log('Iframe is ready')}
  initialMessage={{ type: 'init', payload: { source: 'parent' } }}
  devTools={true}
/>`}</pre>
          
          <h4>Client-Side Integration</h4>
          <p>
            To enable communication between the parent window and the iframe content, add the following
            code to your Node.js application:
          </p>
          <pre className="bg-gray-100 p-3 rounded">{`// Set up message listener
window.addEventListener('message', (event) => {
  // Make sure the message is from the parent window
  if (event.source === window.parent) {
    const message = event.data;
    console.log('Received message:', message);
    
    // Handle the message
    // ...
    
    // Send a response
    window.parent.postMessage({
      type: 'response',
      payload: { received: true }
    }, '*');
  }
});

// Notify parent that we're ready
window.addEventListener('load', () => {
  window.parent.postMessage({ type: 'connect' }, '*');
});`}</pre>
        </div>
      </div>
    </div>
  );
}
