"use client"

import React, { useState } from "react"
import { TabContentGallery } from "@/components/tab-content-views/tab-content-gallery"
import { TabContentRenderer } from "@/components/tab-content-views/tab-content-renderer"
import { TabContentView } from "@/components/tab-content-views"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { X } from "lucide-react"

export default function TabContentLibraryPage() {
  const [selectedView, setSelectedView] = useState<TabContentView | null>(null)
  
  const handleSelectView = (view: TabContentView) => {
    setSelectedView(view)
  }
  
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Tab Content Library</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <TabContentGallery onSelect={handleSelectView} />
        </div>
        
        <div>
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle>Selected View</CardTitle>
              <CardDescription>
                Preview the selected tab content view
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedView ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {selectedView.icon}
                      <h3 className="font-medium">{selectedView.title}</h3>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setSelectedView(null)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="border rounded-md h-[400px] overflow-hidden">
                    <TabContentRenderer 
                      tabId={selectedView.id} 
                      props={selectedView.defaultProps || {}} 
                    />
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Description</h4>
                    <p className="text-sm text-muted-foreground">{selectedView.description}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Category</h4>
                    <div className="inline-block px-2 py-1 text-xs rounded-full bg-muted">
                      {selectedView.category}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  <p>Select a view from the gallery to preview it here</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
