'use client';

import React, { useState, useEffect } from 'react';
import { useNodeRunner } from '@/hooks/useNodeRunner';
import {
  VMFileExplorer,
  VMFileEditor,
  FileItem,
  VMStatusBadge,
  VMActionBar
} from '@/components/vm-interface';

export default function VMFileManagerDemo() {
  const {
    vms,
    loading,
    error,
    listVMs,
    generateNodeApp,
    stopNodeApp,
    deleteNodeApp,
  } = useNodeRunner();
  
  const [selectedVmId, setSelectedVmId] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [creatingVM, setCreatingVM] = useState(false);
  
  // Load VMs on mount
  useEffect(() => {
    listVMs();
  }, [listVMs]);
  
  // Create a demo VM if none exists
  const createDemoVM = async () => {
    try {
      setCreatingVM(true);
      
      const vm = await generateNodeApp({
        name: `file-demo-${Date.now()}`,
        description: 'A demo application for the file manager',
        template: 'express',
        dependencies: {
          express: '^4.18.2',
          'cors': '^2.8.5'
        },
        port: 3000
      });
      
      if (vm) {
        setSelectedVmId(vm.id);
      }
    } catch (err) {
      console.error('Failed to create VM:', err);
    } finally {
      setCreatingVM(false);
    }
  };
  
  // Handle file save
  const handleFileSave = async (file: FileItem, content: string) => {
    if (!selectedVmId) return;
    
    try {
      const response = await fetch(`/api/noderunner/files?vmId=${selectedVmId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          path: file.path,
          content,
        }),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to save file');
      }
    } catch (err) {
      console.error('Error saving file:', err);
      alert(`Failed to save file: ${err}`);
    }
  };
  
  // Get the selected VM
  const selectedVM = vms.find(vm => vm.id === selectedVmId);
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">VM File Manager Demo</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* VM Selection Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Virtual Machines</h2>
            
            {loading ? (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            ) : vms.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">No virtual machines found.</p>
                <button
                  onClick={createDemoVM}
                  disabled={creatingVM}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                >
                  {creatingVM ? 'Creating...' : 'Create Demo VM'}
                </button>
              </div>
            ) : (
              <div>
                <div className="space-y-2 mb-4">
                  {vms.map(vm => (
                    <div
                      key={vm.id}
                      onClick={() => {
                        setSelectedVmId(vm.id);
                        setSelectedFile(null);
                      }}
                      className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                        selectedVmId === vm.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{vm.name || vm.id}</h3>
                          <p className="text-sm text-gray-500">{vm.description || 'No description'}</p>
                        </div>
                        <VMStatusBadge status={vm.status as any} />
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-between">
                  <button
                    onClick={listVMs}
                    disabled={loading}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    Refresh
                  </button>
                  
                  <button
                    onClick={createDemoVM}
                    disabled={creatingVM}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
                  >
                    {creatingVM ? 'Creating...' : 'Create VM'}
                  </button>
                </div>
              </div>
            )}
          </div>
          
          {/* VM Actions */}
          {selectedVM && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium mb-3">VM Actions</h3>
              
              <VMActionBar
                vmId={selectedVM.id}
                status={selectedVM.status as any}
                onStop={() => stopNodeApp(selectedVM.id)}
                onDelete={() => {
                  if (window.confirm(`Are you sure you want to delete ${selectedVM.name || selectedVM.id}?`)) {
                    deleteNodeApp(selectedVM.id);
                    if (selectedVmId === selectedVM.id) {
                      setSelectedVmId(null);
                      setSelectedFile(null);
                    }
                  }
                }}
                loading={loading}
              />
            </div>
          )}
        </div>
        
        {/* Main Content Area */}
        <div className="lg:col-span-3">
          {!selectedVmId ? (
            <div className="bg-white shadow rounded-lg p-12 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No VM Selected</h3>
              <p className="text-gray-500">
                Select a virtual machine from the list or create a new one to get started.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* File Explorer */}
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="p-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium">File Explorer</h3>
                </div>
                <VMFileExplorer
                  vmId={selectedVmId}
                  onFileSelect={setSelectedFile}
                  onFileOpen={setSelectedFile}
                  readOnly={false}
                />
              </div>
              
              {/* File Editor */}
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="p-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium">File Editor</h3>
                </div>
                {selectedFile ? (
                  <VMFileEditor
                    vmId={selectedVmId}
                    file={selectedFile}
                    onSave={handleFileSave}
                    onClose={() => setSelectedFile(null)}
                    readOnly={false}
                    height={500}
                  />
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500">
                    Select a file to edit
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Documentation */}
      <div className="bg-white shadow rounded-lg p-6 mt-8">
        <h2 className="text-xl font-semibold mb-4">Documentation</h2>
        
        <div className="prose max-w-none">
          <h3>VM File Management</h3>
          <p>
            This demo showcases the file management capabilities of the VM interface components.
            You can browse, create, edit, rename, and delete files in the VM's filesystem.
          </p>
          
          <h4>File Explorer</h4>
          <p>
            The VMFileExplorer component provides a user interface for browsing and managing files in the VM.
            You can:
          </p>
          <ul>
            <li>Navigate through directories</li>
            <li>Create new files and directories</li>
            <li>Rename files and directories (right-click on a file)</li>
            <li>Delete files and directories (right-click on a file)</li>
            <li>Select files to edit</li>
          </ul>
          
          <h4>File Editor</h4>
          <p>
            The VMFileEditor component provides a simple text editor for editing files in the VM.
            You can:
          </p>
          <ul>
            <li>View and edit file content</li>
            <li>Save changes to the file</li>
            <li>See file metadata (size, modification date)</li>
          </ul>
          
          <h4>API Integration</h4>
          <p>
            The file management components use the following API endpoints:
          </p>
          <ul>
            <li><code>GET /api/noderunner/files?vmId={vmId}&path={path}</code> - List files in a directory</li>
            <li><code>GET /api/noderunner/files?vmId={vmId}&path={path}&action=content</code> - Get file content</li>
            <li><code>POST /api/noderunner/files?vmId={vmId}</code> - Create a file or directory</li>
            <li><code>PUT /api/noderunner/files?vmId={vmId}</code> - Update file content</li>
            <li><code>PUT /api/noderunner/files?vmId={vmId}&action=rename</code> - Rename a file or directory</li>
            <li><code>DELETE /api/noderunner/files?vmId={vmId}&path={path}</code> - Delete a file or directory</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
