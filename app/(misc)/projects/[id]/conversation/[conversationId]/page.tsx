import { notFound, redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { db } from "@/lib/db"
import { Conversation } from "@/components/conversation"
import { FileExplorer } from "@/components/file-explorer"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface ConversationPageProps {
  params: {
    id: string
    conversationId: string
  }
}

export default async function ConversationPage({ params }: ConversationPageProps) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/login")
  }

  const project = await db.project.findFirst({
    where: {
      id: params.id,
      userId: session.user.id as string,
    },
  })

  if (!project) {
    notFound()
  }

  const conversation = await db.conversation.findFirst({
    where: {
      id: params.conversationId,
      projectId: params.id,
    },
    include: {
      messages: {
        orderBy: {
          timestamp: "asc",
        },
      },
    },
  })

  if (!conversation) {
    notFound()
  }

  // Get all files
  const files = await db.generatedFile.findMany({
    where: {
      projectId: params.id,
    },
  })

  return (
    <div className="container py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">{project.name}</h1>
        <p className="text-muted-foreground">{project.description}</p>
      </div>

      <div className="h-[calc(100vh-200px)] border rounded-lg overflow-hidden">
        <Tabs defaultValue="conversation" className="h-full flex flex-col">
          <div className="border-b px-4">
            <TabsList>
              <TabsTrigger value="conversation">Conversation</TabsTrigger>
              <TabsTrigger value="files">Files</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="conversation" className="flex-1 p-0 m-0">
            <Conversation
              projectId={params.id}
              conversationId={params.conversationId}
              initialMessages={conversation.messages.map((msg) => ({
                role: msg.role,
                content: msg.content,
              }))}
            />
          </TabsContent>

          <TabsContent value="files" className="flex-1 p-0 m-0">
            <FileExplorer projectId={params.id} initialFiles={files} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
