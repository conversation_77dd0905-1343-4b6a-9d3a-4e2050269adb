import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../auth/[...nextauth]/route"
import { db } from "@/lib/db"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { ProjectDetails } from "@/components/dashboard/projects/project-details"
import { ProjectRequirements } from "@/components/dashboard/projects/project-requirements"
import { ProjectTasks } from "@/components/dashboard/projects/project-tasks"
import { ProjectCode } from "@/components/dashboard/projects/project-code"
import { ProjectDeployment } from "@/components/dashboard/projects/project-deployment"
import { DatabaseDesigner } from "@/components/database-designer"
import { ComponentLibrary } from "@/components/component-library"
import { ProductionWorkflow } from "@/components/production-workflow"
import { ProductionTasks } from "@/components/production-tasks"

export default async function ProjectPage({ params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/api/auth/signin")
  }

  const project = await db.project.findUnique({
    where: {
      id: params.id,
      userId: session.user.id,
    },
    include: {
      requirements: true,
    },
  })

  if (!project) {
    redirect("/dashboard")
  }

  const tasks = await db.task.findMany({
    where: {
      projectId: project.id,
      type: {
        in: ["production", "workflow", "generate", "build", "test", "deploy"],
      },
    },
    orderBy: {
      startedAt: "desc",
    },
  })

  return (
    <div className="container py-6 space-y-6">
      <ProjectDetails project={project} />

      <Tabs defaultValue="requirements">
        <TabsList className="grid grid-cols-7 w-full">
          <TabsTrigger value="requirements">Requirements</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="code">Code</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="production">Production</TabsTrigger>
          <TabsTrigger value="deployment">Deployment</TabsTrigger>
        </TabsList>

        <TabsContent value="requirements" className="mt-6">
          <ProjectRequirements project={project} />
        </TabsContent>

        <TabsContent value="tasks" className="mt-6">
          <ProjectTasks projectId={project.id} />
        </TabsContent>

        <TabsContent value="code" className="mt-6">
          <ProjectCode projectId={project.id} />
        </TabsContent>

        <TabsContent value="database" className="mt-6">
          <DatabaseDesigner projectId={project.id} />
        </TabsContent>

        <TabsContent value="components" className="mt-6">
          <ComponentLibrary projectId={project.id} />
        </TabsContent>

        <TabsContent value="production" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ProductionWorkflow projectId={project.id} projectType={project.appType as any} />
            <ProductionTasks projectId={project.id} initialTasks={tasks} />
          </div>
        </TabsContent>

        <TabsContent value="deployment" className="mt-6">
          <ProjectDeployment projectId={project.id} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
