import type { <PERSON>ada<PERSON> } from "next"
import { getServerSession } from "next-auth"
import { authOptions } from "../../../auth/[...nextauth]/route"
import { redirect } from "next/navigation"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { EnhancedProjectCreator } from "@/components/enhanced-project-creator"
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ProjectForm } from "@/components/dashboard/projects/project-form"
import { ProjectTemplates } from "@/components/dashboard/projects/project-templates"

export const metadata: Metadata = {
  title: "Create Project",
  description: "Create a new application project",
}

export default async function NewProjectPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/auth/signin")
  }

  return (
    <DashboardShell>
      <DashboardHeader heading="Create Project" text="Create a new application project" />
      <Tabs defaultValue="ai" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="ai">AI Assistant</TabsTrigger>
          <TabsTrigger value="form">Manual Setup</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>
        <TabsContent value="ai" className="mt-6">
          <EnhancedProjectCreator />
        </TabsContent>
        <TabsContent value="form" className="mt-6">
          <ProjectForm />
        </TabsContent>
        <TabsContent value="templates" className="mt-6">
          <ProjectTemplates />
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}
