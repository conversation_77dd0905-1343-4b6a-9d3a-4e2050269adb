"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardFooter, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  ArrowRight,
  Sparkles,
  Code,
  Zap,
  Layers,
  Palette,
  Bot,
  Check,
  MessageSquare,
  Laptop,
  Rocket,
  Star,
  CreditCard,
  Lock,
  Mail,
  User,
  X,
  ChevronDown,
  ChevronRight
} from "lucide-react"
import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionI<PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { EnhancedHeader } from "@/components/enhanced-header"

// Payment Modal Component
function PaymentModal({ plan = "Pro" }) {
  const [paymentMethod, setPaymentMethod] = useState("card")

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Subscribe to {plan}</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Subscribe to {plan} Plan</DialogTitle>
          <DialogDescription>
            Choose your preferred payment method to continue.
          </DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="card" className="w-full" onValueChange={setPaymentMethod}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="card">Credit Card</TabsTrigger>
            <TabsTrigger value="eft">EFT</TabsTrigger>
            <TabsTrigger value="polar">Polar</TabsTrigger>
          </TabsList>
          <TabsContent value="card" className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="card-number">Card Number</Label>
              <Input id="card-number" placeholder="1234 5678 9012 3456" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="expiry">Expiry Date</Label>
                <Input id="expiry" placeholder="MM/YY" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cvc">CVC</Label>
                <Input id="cvc" placeholder="123" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="name-on-card">Name on Card</Label>
              <Input id="name-on-card" placeholder="J. Smith" />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="save-card" />
              <label
                htmlFor="save-card"
                className="text-sm font-medium leading-none"
              >
                Save card for future payments
              </label>
            </div>
          </TabsContent>
          <TabsContent value="eft" className="space-y-4 py-4">
            <div className="rounded-md bg-muted p-4 space-y-3">
              <div className="space-y-1">
                <p className="text-sm font-medium">Bank Details</p>
                <p className="text-xs text-muted-foreground">Make payment to the following account:</p>
              </div>
              <div className="grid grid-cols-2 text-sm">
                <p className="text-muted-foreground">Bank:</p>
                <p>FNB</p>
                <p className="text-muted-foreground">Account Name:</p>
                <p>SAGEDesigns </p>
                <p className="text-muted-foreground">Account Number:</p>
                <p>***********</p>
                <p className="text-muted-foreground">Branch Code:</p>
                <p>250655</p>
                <p className="text-muted-foreground">Reference:</p>
                <p>AG-{Math.floor(Math.random() * 10000)}</p>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="proof-of-payment">Upload Proof of Payment</Label>
              <Input id="proof-of-payment" type="file" />
            </div>
          </TabsContent>
          <TabsContent value="polar" className="space-y-4 py-4">
            <div className="rounded-md bg-muted p-4 space-y-3">
              <div className="space-y-1">
                <p className="text-sm font-medium">Polar Payments</p>
                <p className="text-xs text-muted-foreground">
                  You'll be redirected to Polar to complete your payment securely.
                </p>
              </div>
              <div className="flex justify-center">
                <img
                  src="https://polarfs.com/wp-content/uploads/2023/05/Polar-Logo-Horizontal-Colour-1.png"
                  alt="Polar Payments"
                  className="h-12 object-contain"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
        <DialogFooter>
          <Button type="submit" className="w-full">
            {paymentMethod === "polar" ? "Continue to Polar" : "Complete Payment"}
            <Lock className="ml-2 h-4 w-4" />
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default function V0LandingPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <EnhancedHeader landingPage={true} />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-gradient-to-b from-background to-muted/30">
          <div className="container">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 xl:grid-cols-2">
              <div className="flex flex-col justify-center space-y-4">
                <Badge className="w-fit" variant="outline">Now in Public Beta</Badge>
                <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none bg-clip-text text-transparent bg-gradient-to-r from-primary to-indigo-500 font-kento">
                  Kraft Your Ideas into Reality.. While Vibing
                </h1>
                <p className="max-w-[600px] text-muted-foreground md:text-xl">
                  Describe your app idea in plain text. Get production-ready code in seconds. Deploy with one click.
                </p>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Button size="lg" className="group">
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>

                  <Link href="/agentic-code-generator">
                    <Button variant="outline" size="lg">
                      Try it Now
                    </Button>
                  </Link>

                </div>
                <div className="mt-6 flex items-center">
                  <div className="flex -space-x-2">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="inline-block h-8 w-8 rounded-full border-2 border-background bg-muted overflow-hidden">
                        <img
                          src={`https://i.pravatar.cc/100?img=${i + 10}`}
                          alt={`User ${i}`}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                  <span className="ml-2 text-sm text-muted-foreground">
                    Joined by 10,000+ developers
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-center">
                <div className="relative w-full h-full">
                  <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary/20 to-indigo-500/20 rounded-lg blur-3xl opacity-50" />
                  <div className="relative bg-background border rounded-lg shadow-lg p-6">
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-red-500"/>
                        <div className="h-3 w-3 rounded-full bg-yellow-500"/>
                        <div className="h-3 w-3 rounded-full bg-green-500"/>
                        <div className="ml-2 text-sm font-medium">AppGen Terminal</div>
                      </div>
                      <div className="h-[300px] bg-black rounded-md p-4 font-mono text-xs text-green-400 overflow-hidden">
                        <div className="typing-animation">
                          <p>$ appgen create "e-commerce app with product listings and cart"</p>
                          <p>Analyzing requirements...</p>
                          <p>Generating components...</p>
                          <p>Creating product listing page...</p>
                          <p>Building shopping cart functionality...</p>
                          <p>Setting up authentication...</p>
                          <p>Configuring API routes...</p>
                          <p>App created successfully!</p>
                          <p>$ appgen deploy</p>
                          <p>Deploying to production...</p>
                          <p>App deployed at: https://my-shop.appgen.dev</p>
                          <p className="animate-pulse">_</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-12 md:py-24 lg:py-32 bg-muted/40">
          <div className="container">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <Badge className="w-fit mx-auto" variant="secondary">Features</Badge>
                <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">Everything You Need</h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  AppGen provides all the tools you need to build and deploy applications quickly and efficiently.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
              <Card className="bg-background/60 backdrop-blur-sm">
                <CardContent className="flex flex-col items-center space-y-4 p-6">
                  <div className="rounded-full bg-primary/10 p-3">
                    <Bot className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">AI-Powered Generation</h3>
                  <p className="text-sm text-muted-foreground text-center">
                    Generate entire applications from simple text descriptions using advanced AI models.
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-background/60 backdrop-blur-sm">
                <CardContent className="flex flex-col items-center space-y-4 p-6">
                  <div className="rounded-full bg-primary/10 p-3">
                    <Code className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">Production-Ready Code</h3>
                  <p className="text-sm text-muted-foreground text-center">
                    Get clean, maintainable code that follows best practices and modern standards.
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-background/60 backdrop-blur-sm">
                <CardContent className="flex flex-col items-center space-y-4 p-6">
                  <div className="rounded-full bg-primary/10 p-3">
                    <Zap className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">One-Click Deployment</h3>
                  <p className="text-sm text-muted-foreground text-center">
                    Deploy your applications to production with a single click, no configuration needed.
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-background/60 backdrop-blur-sm">
                <CardContent className="flex flex-col items-center space-y-4 p-6">
                  <div className="rounded-full bg-primary/10 p-3">
                    <Layers className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">Component Library</h3>
                  <p className="text-sm text-muted-foreground text-center">
                    Access a rich library of pre-built components to customize your application.
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-background/60 backdrop-blur-sm">
                <CardContent className="flex flex-col items-center space-y-4 p-6">
                  <div className="rounded-full bg-primary/10 p-3">
                    <Palette className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">Customizable Themes</h3>
                  <p className="text-sm text-muted-foreground text-center">
                    Apply beautiful themes and customize the look and feel of your application.
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-background/60 backdrop-blur-sm">
                <CardContent className="flex flex-col items-center space-y-4 p-6">
                  <div className="rounded-full bg-primary/10 p-3">
                    <MessageSquare className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">Conversational UI</h3>
                  <p className="text-sm text-muted-foreground text-center">
                    Refine your application through natural conversation with our AI assistant.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section id="how-it-works" className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-background to-muted/30">
          <div className="container">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <Badge className="w-fit mx-auto" variant="outline">How It Works</Badge>
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">From Idea to Production</h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed">
                  AppGen streamlines the entire application development process with AI assistance at every step.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 py-12 md:grid-cols-3">
              <div className="flex flex-col items-center space-y-4 rounded-lg border p-6 shadow-sm bg-background/60 backdrop-blur-sm">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                  <span className="text-xl font-bold">1</span>
                </div>
                <h3 className="text-xl font-bold">Describe Your App</h3>
                <p className="text-center text-muted-foreground">
                  Tell our AI what you want to build using natural language. No technical jargon required.
                </p>
                <ArrowRight className="h-6 w-6 text-muted-foreground md:rotate-90 lg:rotate-0" />
              </div>
              <div className="flex flex-col items-center space-y-4 rounded-lg border p-6 shadow-sm bg-background/60 backdrop-blur-sm">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                  <span className="text-xl font-bold">2</span>
                </div>
                <h3 className="text-xl font-bold">AI Generates App</h3>
                <p className="text-center text-muted-foreground">
                  Our AI creates the entire application architecture, components, and functionality.
                </p>
                <ArrowRight className="h-6 w-6 text-muted-foreground md:rotate-90 lg:rotate-0" />
              </div>
              <div className="flex flex-col items-center space-y-4 rounded-lg border p-6 shadow-sm bg-background/60 backdrop-blur-sm">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                  <span className="text-xl font-bold">3</span>
                </div>
                <h3 className="text-xl font-bold">Deploy & Share</h3>
                <p className="text-center text-muted-foreground">
                  Deploy your application with a single click and share it with the world.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <Badge className="w-fit mx-auto" variant="secondary">Testimonials</Badge>
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Loved by Developers</h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed">
                  See what our users have to say about AppGen.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
              <Card className="bg-background/60 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                    ))}
                  </div>
                  <blockquote className="text-lg mb-6 italic">
                    "AppGen has completely transformed how I build applications. What used to take weeks now takes hours."
                  </blockquote>
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 rounded-full bg-muted overflow-hidden">
                      <img src="https://i.pravatar.cc/100?img=1" alt="Sarah Johnson" className="h-full w-full object-cover" />
                    </div>
                    <div>
                      <p className="font-semibold">Sarah Johnson</p>
                      <p className="text-sm text-muted-foreground">Frontend Developer, TechCorp</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-background/60 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                    ))}
                  </div>
                  <blockquote className="text-lg mb-6 italic">
                    "The AI assistance is mind-blowing. It understands exactly what I need and generates high-quality code."
                  </blockquote>
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 rounded-full bg-muted overflow-hidden">
                      <img src="https://i.pravatar.cc/100?img=2" alt="Michael Chen" className="h-full w-full object-cover" />
                    </div>
                    <div>
                      <p className="font-semibold">Michael Chen</p>
                      <p className="text-sm text-muted-foreground">Full Stack Developer, StartupX</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-background/60 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                    ))}
                  </div>
                  <blockquote className="text-lg mb-6 italic">
                    "I've tried many AI coding tools, but AppGen is in a league of its own. The deployment features are especially impressive."
                  </blockquote>
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 rounded-full bg-muted overflow-hidden">
                      <img src="https://i.pravatar.cc/100?img=3" alt="Emily Rodriguez" className="h-full w-full object-cover" />
                    </div>
                    <div>
                      <p className="font-semibold">Emily Rodriguez</p>
                      <p className="text-sm text-muted-foreground">DevOps Engineer, CloudSystems</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
        {/* Pricing Section */}
        <section id="pricing" className="py-16 bg-muted/50">
          <div className="container space-y-8">
            <div className="text-center space-y-4 max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold tracking-tight">Simple, transparent pricing</h2>
              <p className="text-muted-foreground">
                Choose the plan that's right for you and start building amazing applications today.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mt-8">
              {/* Starter Plan */}
              <Card className="flex flex-col">
                <CardHeader>
                  <CardTitle>Starter</CardTitle>
                  <CardDescription>Perfect for individuals and small projects</CardDescription>
                </CardHeader>
                <CardContent className="flex-1">
                  <div className="mb-4">
                    <span className="text-4xl font-bold">R99</span>
                    <span className="text-muted-foreground ml-1">/month</span>
                  </div>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Up to 5 projects</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Basic AI code generation</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Community support</span>
                    </li>
                  </ul>
                </CardContent>
                <CardFooter>
                  <PaymentModal plan="Starter" />
                </CardFooter>
              </Card>

              {/* Pro Plan */}
              <Card className="flex flex-col border-primary">
                <CardHeader className="bg-primary/5">
                  <div className="flex justify-between items-center">
                    <CardTitle>Pro</CardTitle>
                    <Badge>Popular</Badge>
                  </div>
                  <CardDescription>For professionals and growing teams</CardDescription>
                </CardHeader>
                <CardContent className="flex-1">
                  <div className="mb-4">
                    <span className="text-4xl font-bold">R299</span>
                    <span className="text-muted-foreground ml-1">/month</span>
                  </div>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Unlimited projects</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Advanced AI code generation</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Priority support</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Custom components</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>API access</span>
                    </li>
                  </ul>
                </CardContent>
                <CardFooter>
                  <PaymentModal plan="Pro" />
                </CardFooter>
              </Card>

              {/* Enterprise Plan */}
              <Card className="flex flex-col">
                <CardHeader>
                  <CardTitle>Enterprise</CardTitle>
                  <CardDescription>For large organizations with custom needs</CardDescription>
                </CardHeader>
                <CardContent className="flex-1">
                  <div className="mb-4">
                    <span className="text-4xl font-bold">Custom</span>
                  </div>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Everything in Pro</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Dedicated account manager</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>Custom integrations</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>SLA guarantees</span>
                    </li>
                    <li className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-primary" />
                      <span>On-premise deployment options</span>
                    </li>
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">Contact Sales</Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section id="faq" className="py-16">
          <div className="container space-y-8">
            <div className="text-center space-y-4 max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold tracking-tight">Frequently Asked Questions</h2>
              <p className="text-muted-foreground">
                Find answers to common questions about AppGen.
              </p>
            </div>

            <div className="max-w-3xl mx-auto mt-8">
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <AccordionTrigger>What is AppGen?</AccordionTrigger>
                  <AccordionContent>
                    AppGen is an AI-powered application generator that helps developers build web applications faster.
                    Using natural language, you can describe UI components, features, and functionality, and AppGen will
                    generate the code for you.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-2">
                  <AccordionTrigger>How does the pricing work?</AccordionTrigger>
                  <AccordionContent>
                    We offer three pricing tiers: Starter, Pro, and Enterprise. The Starter plan is perfect for individuals
                    and small projects, while the Pro plan offers more features for professionals and growing teams.
                    Enterprise plans are customized for large organizations with specific needs.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-3">
                  <AccordionTrigger>Can I cancel my subscription?</AccordionTrigger>
                  <AccordionContent>
                    Yes, you can cancel your subscription at any time. If you cancel, you'll continue to have access to your
                    plan until the end of your current billing period. We don't offer refunds for partial months.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-4">
                  <AccordionTrigger>What payment methods do you accept?</AccordionTrigger>
                  <AccordionContent>
                    We accept credit cards, EFT (Electronic Funds Transfer) for South African customers, and Polar payments.
                    For Enterprise plans, we also offer invoice-based payment options.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-5">
                  <AccordionTrigger>Do you offer a free trial?</AccordionTrigger>
                  <AccordionContent>
                    Yes, we offer a 14-day free trial on our Pro plan. No credit card is required to start your trial.
                    You'll get full access to all Pro features during your trial period.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-6">
                  <AccordionTrigger>How do I get support?</AccordionTrigger>
                  <AccordionContent>
                    Starter plan users have access to our community forums and documentation. Pro plan users get priority
                    email support. Enterprise customers receive dedicated support with an account manager and phone support.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-primary-foreground">
          <div className="container text-center space-y-6">
            <h2 className="text-3xl font-bold tracking-tight">Ready to start building?</h2>
            <p className="max-w-2xl mx-auto">
              Join thousands of developers who are already using AppGen to build amazing applications faster than ever before.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="#demo">See Demo</Link>
              </Button>
              <PaymentModal plan="Pro" />
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t py-6 md:py-10">
        <div className="container flex flex-col items-center justify-between gap-4 md:flex-row">
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} AppGen. All rights reserved.
          </p>
          <div className="flex gap-4">
            <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
              Terms
            </Link>
            <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
              Privacy
            </Link>
            <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
              Contact
            </Link>
          </div>
        </div>
      </footer>
    </div>
  )
}
