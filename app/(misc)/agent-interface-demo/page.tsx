"use client"

import * as React from "react"
import { useState } from "react"
import { AgentInterface } from "@/components/agent-interface"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  Code, 
  Server, 
  Database, 
  Terminal, 
  Sparkles 
} from "lucide-react"
import { AgentType } from "@/lib/stores/agent-store"

export default function AgentInterfaceDemo() {
  const [selectedAgentType, setSelectedAgentType] = useState<AgentType>('autonomous-programmer')
  
  return (
    <div className="container py-8">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Agent Interface Demo</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          A modern, VSCode-inspired interface for interacting with different AI agents. 
          Select an agent type below to see the specialized interface.
        </p>
      </div>
      
      <div className="mb-8 flex justify-center">
        <Tabs 
          value={selectedAgentType} 
          onValueChange={(value) => setSelectedAgentType(value as AgentType)}
          className="w-full max-w-2xl"
        >
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="autonomous-programmer" className="flex items-center gap-2">
              <Terminal className="h-4 w-4" />
              <span>Autonomous</span>
            </TabsTrigger>
            <TabsTrigger value="code-generator" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              <span>Code Gen</span>
            </TabsTrigger>
            <TabsTrigger value="devops" className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              <span>DevOps</span>
            </TabsTrigger>
            <TabsTrigger value="database" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              <span>Database</span>
            </TabsTrigger>
          </TabsList>
          
          <div className="border rounded-lg p-4 mb-4">
            <div className="flex items-center gap-3 mb-2">
              {selectedAgentType === 'autonomous-programmer' && (
                <>
                  <Terminal className="h-5 w-5 text-purple-500" />
                  <h2 className="text-xl font-semibold">Autonomous Programmer</h2>
                </>
              )}
              {selectedAgentType === 'code-generator' && (
                <>
                  <Code className="h-5 w-5 text-blue-500" />
                  <h2 className="text-xl font-semibold">Code Generator</h2>
                </>
              )}
              {selectedAgentType === 'devops' && (
                <>
                  <Server className="h-5 w-5 text-green-500" />
                  <h2 className="text-xl font-semibold">DevOps Assistant</h2>
                </>
              )}
              {selectedAgentType === 'database' && (
                <>
                  <Database className="h-5 w-5 text-amber-500" />
                  <h2 className="text-xl font-semibold">Database Designer</h2>
                </>
              )}
            </div>
            
            <p className="text-muted-foreground">
              {selectedAgentType === 'autonomous-programmer' 
                ? 'The Autonomous Programmer can implement features, search your codebase, and help you manage your project autonomously.'
                : selectedAgentType === 'code-generator'
                ? 'The Code Generator helps you create applications from natural language descriptions, generating all necessary files and code.'
                : selectedAgentType === 'devops'
                ? 'The DevOps Assistant helps you manage VMs, deploy applications, and set up infrastructure.'
                : 'The Database Designer helps you create database schemas, generate SQL, and optimize queries.'}
            </p>
          </div>
        </Tabs>
      </div>
      
      <div className="mb-4">
        <AgentInterface 
          initialAgentType={selectedAgentType}
          projectId="demo-project"
        />
      </div>
      
      <div className="text-center mt-8">
        <p className="text-sm text-muted-foreground mb-2">
          This interface is designed to work with the current theme system and can be integrated into any part of the application.
        </p>
        <Button variant="outline" onClick={() => window.history.back()}>
          Back to Home
        </Button>
      </div>
    </div>
  )
}
