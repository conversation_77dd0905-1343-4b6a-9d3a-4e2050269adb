import { FirecrackerDashboard } from "@/components/firecracker/dashboard"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { FirecrackerMetricsPanel } from "@/components/firecracker/metrics-panel"
import { FirecrackerSecurityPanel } from "@/components/firecracker/security-panel"
import { FirecrackerNetworkPanel } from "@/components/firecracker/network-panel"

export default function FirecrackerDashboardPage() {
  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-2">Firecracker Management</h1>
      <p className="text-muted-foreground mb-6">Monitor and manage your Firecracker microVM instances</p>

      <Tabs defaultValue="dashboard" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="network">Network</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <FirecrackerDashboard />
        </TabsContent>

        <TabsContent value="metrics">
          <FirecrackerMetricsPanel />
        </TabsContent>

        <TabsContent value="network">
          <FirecrackerNetworkPanel />
        </TabsContent>

        <TabsContent value="security">
          <FirecrackerSecurityPanel />
        </TabsContent>
      </Tabs>
    </div>
  )
}
