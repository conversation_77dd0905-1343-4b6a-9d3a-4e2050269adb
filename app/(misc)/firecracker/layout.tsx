import type React from "react"
import { FirecrackerNavigation } from "@/components/firecracker/navigation"

export default function FirecrackerLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-screen">
      <div className="w-64 border-r bg-gray-50/50">
        <FirecrackerNavigation />
      </div>
      <div className="flex-1">
        <main className="p-6">{children}</main>
      </div>
    </div>
  )
}
