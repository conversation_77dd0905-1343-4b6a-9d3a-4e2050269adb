"use client"

import * as React from "react"
import { useState } from "react"
import { AgentChatInput } from "@/components/agent-chat-input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { 
  Code, 
  Server, 
  Database, 
  Terminal, 
  Sparkles,
  User,
  ArrowLeft
} from "lucide-react"
import { AgentType } from "@/lib/stores/agent-store"
import { ContextInfo } from "@/components/context-selector"

// Define message type
interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  agentType: AgentType
  context?: ContextInfo
  timestamp: Date
}

export default function AgentChatInputDemo() {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [activeVariant, setActiveVariant] = useState<"default" | "compact" | "minimal">("default")
  
  // Handle sending a message
  const handleSendMessage = (
    content: string, 
    agentType: AgentType,
    context: ContextInfo
  ) => {
    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content,
      role: 'user',
      agentType,
      context,
      timestamp: new Date()
    }
    
    setMessages(prev => [...prev, userMessage])
    
    // Simulate AI response
    setIsLoading(true)
    
    setTimeout(() => {
      // Generate response based on agent type
      let responseContent = ""
      
      switch (agentType) {
        case 'code-generator':
          responseContent = `I'll help you with your coding question. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
          break
        case 'devops':
          responseContent = `I'll assist with your DevOps needs. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
          break
        case 'database':
          responseContent = `I'll help with your database question. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
          break
        case 'autonomous-programmer':
          responseContent = `I'll autonomously implement features for you. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
          break
        default:
          responseContent = `I'll help you with your question. ${context.type !== 'none' ? `I'll focus on ${context.type === 'file' ? context.path : context.type === 'folder' ? context.path : 'your entire codebase'}.` : ''}`
      }
      
      // Add AI response
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        content: responseContent,
        role: 'assistant',
        agentType,
        context,
        timestamp: new Date()
      }
      
      setMessages(prev => [...prev, assistantMessage])
      setIsLoading(false)
    }, 1500)
  }
  
  // Get agent icon
  const getAgentIcon = (agentType: AgentType) => {
    switch (agentType) {
      case 'code-generator':
        return <Code className="h-4 w-4 text-blue-500" />
      case 'devops':
        return <Server className="h-4 w-4 text-green-500" />
      case 'database':
        return <Database className="h-4 w-4 text-amber-500" />
      case 'autonomous-programmer':
        return <Terminal className="h-4 w-4 text-purple-500" />
      default:
        return <Sparkles className="h-4 w-4 text-gray-500" />
    }
  }
  
  // Get agent name
  const getAgentName = (agentType: AgentType) => {
    switch (agentType) {
      case 'code-generator':
        return 'Code Generator'
      case 'devops':
        return 'DevOps Assistant'
      case 'database':
        return 'Database Designer'
      case 'autonomous-programmer':
        return 'Autonomous Programmer'
      default:
        return 'AI Assistant'
    }
  }
  
  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b p-4">
        <div className="container flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <a href="/">
                <ArrowLeft className="h-5 w-5" />
              </a>
            </Button>
            <h1 className="text-xl font-semibold">Agent Chat Input Demo</h1>
          </div>
        </div>
      </header>
      
      <main className="flex-1 container py-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-[1fr_300px]">
          <Card>
            <CardHeader>
              <CardTitle>Chat Interface</CardTitle>
              <CardDescription>
                Try the agent chat input with different variants
              </CardDescription>
              <Tabs value={activeVariant} onValueChange={(v) => setActiveVariant(v as any)}>
                <TabsList>
                  <TabsTrigger value="default">Default</TabsTrigger>
                  <TabsTrigger value="compact">Compact</TabsTrigger>
                  <TabsTrigger value="minimal">Minimal</TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[400px] p-4">
                <div className="space-y-4">
                  {messages.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <Sparkles className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">No messages yet</h3>
                      <p className="text-sm text-muted-foreground max-w-md">
                        Start a conversation by typing a message below. Select an agent and context to get started.
                      </p>
                    </div>
                  ) : (
                    messages.map(message => (
                      <div key={message.id} className="flex gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className={
                            message.role === 'user' 
                              ? "bg-muted" 
                              : "bg-primary/10"
                          }>
                            {message.role === 'user' ? (
                              <User className="h-4 w-4" />
                            ) : (
                              getAgentIcon(message.agentType)
                            )}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">
                              {message.role === 'user' ? 'You' : getAgentName(message.agentType)}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {message.timestamp.toLocaleTimeString()}
                            </span>
                            {message.context && message.context.type !== 'none' && (
                              <Badge variant="outline" className="text-xs">
                                {message.context.type === 'file' || message.context.type === 'folder' 
                                  ? message.context.path 
                                  : message.context.type}
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm">
                            {message.content}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
            <CardFooter className="border-t p-4">
              <AgentChatInput 
                onSendMessage={handleSendMessage}
                isLoading={isLoading}
                variant={activeVariant}
              />
            </CardFooter>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Instructions</CardTitle>
              <CardDescription>
                How to use the agent chat input
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <div>
                  <h3 className="font-medium mb-1">Agent Selection</h3>
                  <p className="text-muted-foreground mb-2">
                    Select an agent to handle your request:
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-blue-500" />
                      <span>Code Generator</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Server className="h-4 w-4 text-green-500" />
                      <span>DevOps Assistant</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Database className="h-4 w-4 text-amber-500" />
                      <span>Database Designer</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Terminal className="h-4 w-4 text-purple-500" />
                      <span>Autonomous Programmer</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-medium mb-1">Context Selection</h3>
                  <p className="text-muted-foreground mb-2">
                    Select a context for your request:
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-blue-500" />
                      <span>Entire Codebase</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-green-500" />
                      <span>Specific File</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-amber-500" />
                      <span>Specific Folder</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-medium mb-1">Variants</h3>
                  <ul className="space-y-2">
                    <li className="flex items-center justify-between">
                      <span>Default</span>
                      <span className="text-muted-foreground">Full interface</span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span>Compact</span>
                      <span className="text-muted-foreground">Dropdown selectors</span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span>Minimal</span>
                      <span className="text-muted-foreground">Icon-only selectors</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
