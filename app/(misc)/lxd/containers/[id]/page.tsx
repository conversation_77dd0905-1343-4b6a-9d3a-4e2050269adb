"use client"

import { LxdContainerDetails } from "@/components/lxd/lxd-container-details"
import { useLxdContainer } from "@/lib/containerization/lxd/hooks/container-hooks"

export default function LxdContainerDetailsPage({ params }: { params: { id: string } }) {
  const { id } = params
  
  const {
    container,
    loading,
    error,
    startContainer,
    stopContainer,
    restartContainer,
    deleteContainer,
  } = useLxdContainer(id)

  return (
    <LxdContainerDetails
      container={container}
      loading={loading}
      error={error}
      onStart={startContainer}
      onStop={stopContainer}
      onRestart={restartContainer}
      onDelete={deleteContainer}
    />
  )
}
