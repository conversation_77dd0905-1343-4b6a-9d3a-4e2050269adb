'use client';

import React, { useState } from 'react';
import { useProjectManagement } from '@/lib/app-dev/project-management/hooks';
import { useFileSync } from '@/lib/app-dev/file-sync/hooks';
import { useGitIntegration } from '@/lib/app-dev/git-integration/hooks';
import { useDependencyManagement } from '@/lib/app-dev/dependency-management/hooks';
import { CreateProjectOptions, ProjectTemplateType } from '@/lib/app-dev/project-management';

export default function AppDevDemo() {
  const {
    projects,
    loading: projectsLoading,
    error: projectsError,
    createProject,
    deleteProject,
    startVM,
    stopVM,
    getIframeUrl
  } = useProjectManagement({ autoRefresh: true });

  const {
    syncStatuses,
    loading: syncLoading,
    error: syncError,
    startSync,
    stopSync
  } = useFileSync({ autoRefresh: true });

  const {
    repoStatus,
    commits,
    branches,
    loading: gitLoading,
    error: gitError,
    getStatus,
    initRepo,
    addFiles,
    commit,
    getCommitHistory,
    createBranch,
    listBranches,
    checkout
  } = useGitIntegration(selectedProjectId, { autoRefresh: true });

  const {
    packageJson,
    dependencies,
    scripts,
    loading: dependencyLoading,
    error: dependencyError,
    installDependencies,
    uninstallDependencies,
    updateDependencies,
    executeScript,
    addScript,
    removeScript,
    searchPackages
  } = useDependencyManagement(selectedProjectId, { autoRefresh: true });

  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState<CreateProjectOptions>({
    name: '',
    description: '',
    template: 'react' as ProjectTemplateType,
    initGitRepo: true
  });

  // State for Git operations
  const [showCommitForm, setShowCommitForm] = useState(false);
  const [commitMessage, setCommitMessage] = useState('');

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const project = await createProject(formData);
    if (project) {
      setShowCreateForm(false);
      setSelectedProjectId(project.metadata.id);
    }
  };

  // Handle project selection
  const handleSelectProject = (projectId: string) => {
    setSelectedProjectId(projectId);
  };

  // Handle project deletion
  const handleDeleteProject = async (projectId: string) => {
    if (confirm('Are you sure you want to delete this project?')) {
      await deleteProject(projectId);
      if (selectedProjectId === projectId) {
        setSelectedProjectId(null);
      }
    }
  };

  // Handle VM start
  const handleStartVM = async (projectId: string) => {
    await startVM(projectId);
  };

  // Handle VM stop
  const handleStopVM = async (projectId: string) => {
    await stopVM(projectId);
  };

  // Get selected project
  const selectedProject = projects.find(p => p.id === selectedProjectId);

  // Combine loading and error states
  const loading = projectsLoading || syncLoading || gitLoading || dependencyLoading;
  const error = projectsError || syncError || gitError || dependencyError;

  // Get sync status for selected project
  const selectedProjectSyncStatus = selectedProjectId
    ? syncStatuses.find(status => status.projectId === selectedProjectId)
    : null;

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">App Development Demo</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-4">
        <div className="w-full md:w-1/3">
          <div className="bg-white shadow rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Projects</h2>
              <button
                onClick={() => setShowCreateForm(true)}
                className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
              >
                New Project
              </button>
            </div>

            {loading && <p>Loading...</p>}

            {!loading && projects.length === 0 && (
              <p className="text-gray-500">No projects found. Create one to get started.</p>
            )}

            <ul className="space-y-2">
              {projects.map(project => (
                <li
                  key={project.id}
                  className={`p-3 rounded cursor-pointer ${
                    selectedProjectId === project.id
                      ? 'bg-blue-100 border border-blue-300'
                      : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
                  }`}
                  onClick={() => handleSelectProject(project.id)}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="font-medium">{project.name}</h3>
                      <p className="text-sm text-gray-500">{project.description || 'No description'}</p>
                      <div className="flex items-center mt-1">
                        <span className="text-xs bg-gray-200 px-2 py-1 rounded">{project.template}</span>
                        <span className={`text-xs ml-2 px-2 py-1 rounded ${
                          project.status === 'ready' ? 'bg-green-200' :
                          project.status === 'error' ? 'bg-red-200' :
                          'bg-yellow-200'
                        }`}>
                          {project.status}
                        </span>
                      </div>
                    </div>
                    <div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteProject(project.id);
                        }}
                        className="text-red-500 hover:text-red-700 text-sm"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>

          {showCreateForm && (
            <div className="mt-4 bg-white shadow rounded-lg p-4">
              <h2 className="text-xl font-semibold mb-4">Create New Project</h2>
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">Name</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full p-2 border rounded"
                    required
                    pattern="^[a-zA-Z0-9-_]+$"
                    title="Name can only contain letters, numbers, hyphens, and underscores"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">Description</label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="w-full p-2 border rounded"
                    rows={3}
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">Template</label>
                  <select
                    name="template"
                    value={formData.template}
                    onChange={handleInputChange}
                    className="w-full p-2 border rounded"
                  >
                    <option value="react">React</option>
                    <option value="next">Next.js</option>
                    <option value="express">Express</option>
                    <option value="node-basic">Node.js Basic</option>
                    <option value="fullstack-next">Fullstack Next.js</option>
                  </select>
                </div>

                <div className="mb-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="initGitRepo"
                      checked={formData.initGitRepo}
                      onChange={handleCheckboxChange}
                      className="mr-2"
                    />
                    <span className="text-sm">Initialize Git repository</span>
                  </label>
                </div>

                <div className="flex justify-end space-x-2">
                  <button
                    type="button"
                    onClick={() => setShowCreateForm(false)}
                    className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-100"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    disabled={loading}
                  >
                    {loading ? 'Creating...' : 'Create Project'}
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>

        <div className="w-full md:w-2/3">
          {selectedProject ? (
            <div className="bg-white shadow rounded-lg p-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">{selectedProject.name}</h2>
                <div className="space-x-2">
                  {selectedProject.vmId ? (
                    <button
                      onClick={() => handleStopVM(selectedProject.id)}
                      className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600"
                      disabled={loading}
                    >
                      Stop VM
                    </button>
                  ) : (
                    <button
                      onClick={() => handleStartVM(selectedProject.id)}
                      className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600"
                      disabled={loading}
                    >
                      Start VM
                    </button>
                  )}
                </div>
              </div>

              <div className="mb-4">
                <h3 className="font-medium mb-2">Project Details</h3>
                <div className="bg-gray-50 p-3 rounded border">
                  <p><strong>ID:</strong> {selectedProject.id}</p>
                  <p><strong>Template:</strong> {selectedProject.template}</p>
                  <p><strong>Status:</strong> {selectedProject.status}</p>
                  <p><strong>Created:</strong> {new Date(selectedProject.createdAt).toLocaleString()}</p>
                  <p><strong>Updated:</strong> {new Date(selectedProject.updatedAt).toLocaleString()}</p>
                  {selectedProject.vmId && (
                    <p><strong>VM ID:</strong> {selectedProject.vmId}</p>
                  )}
                </div>
              </div>

              {selectedProject.vmId && (
                <div className="mb-4">
                  <h3 className="font-medium mb-2">File Synchronization</h3>
                  <div className="bg-gray-50 p-3 rounded border">
                    {selectedProjectSyncStatus ? (
                      <>
                        <p>
                          <strong>Status:</strong>{' '}
                          <span className={`px-2 py-1 rounded text-xs ${selectedProjectSyncStatus.active ? 'bg-green-200' : 'bg-gray-200'}`}>
                            {selectedProjectSyncStatus.active ? 'Active' : 'Inactive'}
                          </span>
                        </p>
                        {selectedProjectSyncStatus.lastSyncTime && (
                          <p><strong>Last Sync:</strong> {new Date(selectedProjectSyncStatus.lastSyncTime).toLocaleString()}</p>
                        )}
                        <p><strong>Files Synced:</strong> {selectedProjectSyncStatus.filesSynced}</p>
                        {selectedProjectSyncStatus.lastError && (
                          <p className="text-red-500"><strong>Last Error:</strong> {selectedProjectSyncStatus.lastError}</p>
                        )}
                        <div className="mt-2">
                          {selectedProjectSyncStatus.active ? (
                            <button
                              onClick={() => stopSync(selectedProject.id)}
                              className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 text-sm"
                              disabled={loading}
                            >
                              Stop Sync
                            </button>
                          ) : (
                            <button
                              onClick={() => startSync(selectedProject.id, { autoReload: true })}
                              className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 text-sm"
                              disabled={loading}
                            >
                              Start Sync
                            </button>
                          )}
                        </div>
                      </>
                    ) : (
                      <>
                        <p>File synchronization is not active for this project.</p>
                        <div className="mt-2">
                          <button
                            onClick={() => startSync(selectedProject.id, { autoReload: true })}
                            className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 text-sm"
                            disabled={loading}
                          >
                            Start Sync
                          </button>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}

              <div className="mb-4">
                <h3 className="font-medium mb-2">Git Integration</h3>
                <div className="bg-gray-50 p-3 rounded border">
                  {repoStatus ? (
                    <>
                      <p>
                        <strong>Repository:</strong>{' '}
                        <span className={`px-2 py-1 rounded text-xs ${repoStatus.isRepo ? 'bg-green-200' : 'bg-gray-200'}`}>
                          {repoStatus.isRepo ? 'Initialized' : 'Not Initialized'}
                        </span>
                      </p>

                      {repoStatus.isRepo && (
                        <>
                          <p><strong>Current Branch:</strong> {repoStatus.currentBranch}</p>
                          <p>
                            <strong>Status:</strong>{' '}
                            <span className={`px-2 py-1 rounded text-xs ${repoStatus.hasChanges ? 'bg-yellow-200' : 'bg-green-200'}`}>
                              {repoStatus.hasChanges ? 'Changes' : 'Clean'}
                            </span>
                          </p>

                          {repoStatus.hasChanges && (
                            <div className="mt-2 space-y-2">
                              <div>
                                <p className="text-sm font-medium">Staged Changes: {repoStatus.staged.length}</p>
                                <p className="text-sm font-medium">Unstaged Changes: {repoStatus.unstaged.length}</p>
                                <p className="text-sm font-medium">Untracked Files: {repoStatus.untracked.length}</p>
                              </div>

                              <div className="flex space-x-2">
                                <button
                                  onClick={() => addFiles({ projectId: selectedProject.id, all: true })}
                                  className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 text-sm"
                                  disabled={loading}
                                >
                                  Stage All
                                </button>

                                <button
                                  onClick={() => {
                                    const message = prompt('Enter commit message:');
                                    if (message) {
                                      commit({
                                        projectId: selectedProject.id,
                                        message,
                                        addAll: true
                                      });
                                    }
                                  }}
                                  className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 text-sm"
                                  disabled={loading}
                                >
                                  Commit All
                                </button>
                              </div>
                            </div>
                          )}

                          {branches.length > 0 && (
                            <div className="mt-3">
                              <p className="text-sm font-medium">Branches:</p>
                              <div className="mt-1 space-y-1">
                                {branches.map(branch => (
                                  <div key={branch.name} className="flex items-center">
                                    <span className={`text-xs ${branch.current ? 'font-bold' : ''}`}>
                                      {branch.name} {branch.current && '(current)'}
                                    </span>
                                    {!branch.current && (
                                      <button
                                        onClick={() => checkout({ projectId: selectedProject.id, ref: branch.name })}
                                        className="ml-2 text-xs text-blue-500 hover:text-blue-700"
                                        disabled={loading}
                                      >
                                        Checkout
                                      </button>
                                    )}
                                  </div>
                                ))}
                              </div>

                              <button
                                onClick={() => {
                                  const branchName = prompt('Enter new branch name:');
                                  if (branchName) {
                                    createBranch({
                                      projectId: selectedProject.id,
                                      name: branchName,
                                      checkout: true
                                    });
                                  }
                                }}
                                className="mt-2 bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 text-sm"
                                disabled={loading}
                              >
                                New Branch
                              </button>
                            </div>
                          )}

                          {commits.length > 0 && (
                            <div className="mt-3">
                              <p className="text-sm font-medium">Recent Commits:</p>
                              <div className="mt-1 space-y-1 max-h-40 overflow-y-auto">
                                {commits.map(commit => (
                                  <div key={commit.hash} className="text-xs">
                                    <span className="font-mono">{commit.abbreviatedHash}</span>{' '}
                                    <span>{commit.message}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </>
                      )}

                      <div className="mt-3">
                        {!repoStatus.isRepo ? (
                          <button
                            onClick={() => initRepo({ projectId: selectedProject.id, initialBranch: 'main' })}
                            className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 text-sm"
                            disabled={loading}
                          >
                            Initialize Repository
                          </button>
                        ) : (
                          <div className="flex space-x-2">
                            <button
                              onClick={() => getCommitHistory()}
                              className="bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600 text-sm"
                              disabled={loading}
                            >
                              Refresh History
                            </button>

                            <button
                              onClick={() => listBranches()}
                              className="bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600 text-sm"
                              disabled={loading}
                            >
                              Refresh Branches
                            </button>
                          </div>
                        )}
                      </div>
                    </>
                  ) : (
                    <p>Loading Git status...</p>
                  )}
                </div>
              </div>

              <div className="mb-4">
                <h3 className="font-medium mb-2">Dependency Management</h3>
                <div className="bg-gray-50 p-3 rounded border">
                  {packageJson ? (
                    <>
                      <div className="mb-3">
                        <p><strong>Package Name:</strong> {packageJson.name}</p>
                        <p><strong>Version:</strong> {packageJson.version}</p>
                        {packageJson.description && (
                          <p><strong>Description:</strong> {packageJson.description}</p>
                        )}
                      </div>

                      <div className="mb-3">
                        <div className="flex justify-between items-center">
                          <p className="text-sm font-medium">Dependencies ({dependencies.filter(d => d.type === 'dependencies').length})</p>
                          <button
                            onClick={() => {
                              const packageName = prompt('Enter package name to install:');
                              if (packageName) {
                                installDependencies([packageName]);
                              }
                            }}
                            className="bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600 text-xs"
                            disabled={loading}
                          >
                            Add Dependency
                          </button>
                        </div>

                        <div className="mt-1 max-h-32 overflow-y-auto">
                          {dependencies.filter(d => d.type === 'dependencies').length > 0 ? (
                            <div className="space-y-1">
                              {dependencies
                                .filter(d => d.type === 'dependencies')
                                .map(dep => (
                                  <div key={dep.name} className="flex justify-between items-center text-xs">
                                    <span>
                                      <span className="font-medium">{dep.name}</span> {dep.version}
                                    </span>
                                    <div className="flex space-x-1">
                                      <button
                                        onClick={() => updateDependencies([dep.name], { latest: true })}
                                        className="text-blue-500 hover:text-blue-700"
                                        disabled={loading}
                                      >
                                        Update
                                      </button>
                                      <button
                                        onClick={() => uninstallDependencies([dep.name])}
                                        className="text-red-500 hover:text-red-700"
                                        disabled={loading}
                                      >
                                        Remove
                                      </button>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          ) : (
                            <p className="text-xs text-gray-500">No dependencies installed</p>
                          )}
                        </div>
                      </div>

                      <div className="mb-3">
                        <div className="flex justify-between items-center">
                          <p className="text-sm font-medium">Dev Dependencies ({dependencies.filter(d => d.type === 'devDependencies').length})</p>
                          <button
                            onClick={() => {
                              const packageName = prompt('Enter package name to install:');
                              if (packageName) {
                                installDependencies([packageName], { dev: true });
                              }
                            }}
                            className="bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 text-xs"
                            disabled={loading}
                          >
                            Add Dev Dependency
                          </button>
                        </div>

                        <div className="mt-1 max-h-32 overflow-y-auto">
                          {dependencies.filter(d => d.type === 'devDependencies').length > 0 ? (
                            <div className="space-y-1">
                              {dependencies
                                .filter(d => d.type === 'devDependencies')
                                .map(dep => (
                                  <div key={dep.name} className="flex justify-between items-center text-xs">
                                    <span>
                                      <span className="font-medium">{dep.name}</span> {dep.version}
                                    </span>
                                    <div className="flex space-x-1">
                                      <button
                                        onClick={() => updateDependencies([dep.name], { latest: true })}
                                        className="text-blue-500 hover:text-blue-700"
                                        disabled={loading}
                                      >
                                        Update
                                      </button>
                                      <button
                                        onClick={() => uninstallDependencies([dep.name])}
                                        className="text-red-500 hover:text-red-700"
                                        disabled={loading}
                                      >
                                        Remove
                                      </button>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          ) : (
                            <p className="text-xs text-gray-500">No dev dependencies installed</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center">
                          <p className="text-sm font-medium">Scripts ({Object.keys(scripts).length})</p>
                          <button
                            onClick={() => {
                              const name = prompt('Enter script name:');
                              if (name) {
                                const command = prompt('Enter script command:');
                                if (command) {
                                  addScript(name, command);
                                }
                              }
                            }}
                            className="bg-purple-500 text-white px-2 py-1 rounded hover:bg-purple-600 text-xs"
                            disabled={loading}
                          >
                            Add Script
                          </button>
                        </div>

                        <div className="mt-1 max-h-32 overflow-y-auto">
                          {Object.keys(scripts).length > 0 ? (
                            <div className="space-y-1">
                              {Object.entries(scripts).map(([name, command]) => (
                                <div key={name} className="flex justify-between items-center text-xs">
                                  <span>
                                    <span className="font-medium">{name}</span>: {command}
                                  </span>
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={() => executeScript(name)}
                                      className="text-green-500 hover:text-green-700"
                                      disabled={loading}
                                    >
                                      Run
                                    </button>
                                    <button
                                      onClick={() => removeScript(name)}
                                      className="text-red-500 hover:text-red-700"
                                      disabled={loading}
                                    >
                                      Remove
                                    </button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-xs text-gray-500">No scripts defined</p>
                          )}
                        </div>
                      </div>

                      <div className="mt-3 flex space-x-2">
                        <button
                          onClick={() => installDependencies()}
                          className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 text-sm"
                          disabled={loading}
                        >
                          Install All
                        </button>

                        <button
                          onClick={() => updateDependencies([], { latest: true })}
                          className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 text-sm"
                          disabled={loading}
                        >
                          Update All
                        </button>
                      </div>
                    </>
                  ) : (
                    <p>Loading package information...</p>
                  )}
                </div>
              </div>

              {selectedProject.vmId && (
                <div>
                  <h3 className="font-medium mb-2">Preview</h3>
                  <div className="border rounded overflow-hidden" style={{ height: '500px' }}>
                    <iframe
                      src={getIframeUrl(selectedProject.id)}
                      width="100%"
                      height="100%"
                      title={`Preview of ${selectedProject.name}`}
                      sandbox="allow-same-origin allow-scripts allow-forms"
                    />
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white shadow rounded-lg p-8 text-center">
              <h2 className="text-xl font-semibold mb-2">No Project Selected</h2>
              <p className="text-gray-500 mb-4">
                Select a project from the list or create a new one to get started.
              </p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Create New Project
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
