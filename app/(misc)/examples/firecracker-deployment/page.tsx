'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useDeploymentService } from '@/lib/app-dev/deployment-service/hooks';
import { DeploymentStatus } from '@/lib/app-dev/deployment-service/types';

export default function FirecrackerDeploymentExample() {
  const { toast } = useToast();
  const [projectId, setProjectId] = useState<string>('');
  const [deploymentId, setDeploymentId] = useState<string | null>(null);
  const [deploymentUrl, setDeploymentUrl] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('deploy');

  const {
    templates,
    deploymentStatus,
    deploymentResult,
    loading,
    error,
    getTemplates,
    startDeployment,
    getDeploymentStatus,
    getDeploymentResult,
    cancelDeployment,
    rollbackDeployment
  } = useDeploymentService({
    autoRefresh: true,
    refreshInterval: 2000
  });

  // Load templates on mount
  useEffect(() => {
    getTemplates();
  }, [getTemplates]);

  // Update deployment URL when result changes
  useEffect(() => {
    if (deploymentResult?.url) {
      setDeploymentUrl(deploymentResult.url);
    }
  }, [deploymentResult]);

  // Handle deployment
  const handleDeploy = async () => {
    try {
      if (!projectId) {
        toast({
          title: 'Error',
          description: 'Please enter a project ID',
          variant: 'destructive'
        });
        return;
      }

      const newDeploymentId = await startDeployment(projectId, 'firecracker-vm', {
        environment: 'development',
        buildBeforeDeploy: true,
        autoRollback: true,
        options: {
          framework: 'express' // or 'next' or 'basic'
        }
      });

      if (newDeploymentId) {
        setDeploymentId(newDeploymentId);
        toast({
          title: 'Deployment started',
          description: `Deployment ID: ${newDeploymentId}`
        });
      }
    } catch (err: any) {
      toast({
        title: 'Deployment failed',
        description: err.message,
        variant: 'destructive'
      });
    }
  };

  // Handle cancellation
  const handleCancel = async () => {
    if (!deploymentId) return;

    try {
      await cancelDeployment(deploymentId);
      toast({
        title: 'Deployment canceled',
        description: `Deployment ID: ${deploymentId}`
      });
    } catch (err: any) {
      toast({
        title: 'Failed to cancel deployment',
        description: err.message,
        variant: 'destructive'
      });
    }
  };

  // Handle rollback
  const handleRollback = async () => {
    if (!deploymentId || !projectId) return;

    try {
      const rollbackId = await rollbackDeployment(projectId, deploymentId);
      if (rollbackId) {
        setDeploymentId(rollbackId);
        toast({
          title: 'Rollback started',
          description: `Rollback ID: ${rollbackId}`
        });
      }
    } catch (err: any) {
      toast({
        title: 'Failed to rollback deployment',
        description: err.message,
        variant: 'destructive'
      });
    }
  };

  // Get status badge color
  const getStatusColor = (status?: DeploymentStatus) => {
    if (!status) return 'bg-gray-200';

    switch (status) {
      case 'success':
        return 'bg-green-500';
      case 'failed':
        return 'bg-red-500';
      case 'in_progress':
        return 'bg-blue-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'canceled':
        return 'bg-gray-500';
      case 'rollback_success':
        return 'bg-purple-500';
      case 'rollback_failed':
        return 'bg-red-500';
      case 'rollback_in_progress':
        return 'bg-blue-500';
      default:
        return 'bg-gray-200';
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Firecracker VM Deployment</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Deploy to Firecracker VM (Deprecated)</CardTitle>
          <CardDescription>
            VM functionality has been removed. Please use container-based deployment instead.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="projectId" className="text-right">
                Project ID
              </label>
              <input
                id="projectId"
                value={projectId}
                onChange={(e) => setProjectId(e.target.value)}
                className="col-span-3 px-3 py-2 border rounded"
                placeholder="Enter project ID"
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button onClick={handleDeploy} disabled={loading || !projectId}>
            {loading ? 'Deploying...' : 'Deploy to Firecracker VM'}
          </Button>
          {deploymentId && (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel} disabled={!deploymentStatus?.deploying}>
                Cancel
              </Button>
              <Button variant="outline" onClick={handleRollback} disabled={deploymentStatus?.deploying || deploymentStatus?.status !== 'success'}>
                Rollback
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>

      {deploymentId && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Deployment Status</CardTitle>
              <div className={`px-3 py-1 rounded text-white ${getStatusColor(deploymentStatus?.status)}`}>
                {deploymentStatus?.status || 'Unknown'}
              </div>
            </div>
            <CardDescription>
              Deployment ID: {deploymentId}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="deploy">Deployment</TabsTrigger>
                <TabsTrigger value="logs">Logs</TabsTrigger>
                <TabsTrigger value="preview">Preview</TabsTrigger>
              </TabsList>
              <TabsContent value="deploy">
                <div className="grid gap-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <span className="text-right font-medium">Status</span>
                    <span className="col-span-3">{deploymentStatus?.status || 'Unknown'}</span>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <span className="text-right font-medium">Progress</span>
                    <div className="col-span-3 w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-blue-600 h-2.5 rounded-full"
                        style={{ width: `${deploymentStatus?.progress || 0}%` }}
                      ></div>
                    </div>
                  </div>
                  {deploymentUrl && (
                    <div className="grid grid-cols-4 items-center gap-4">
                      <span className="text-right font-medium">URL</span>
                      <a
                        href={deploymentUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="col-span-3 text-blue-600 hover:underline"
                      >
                        {deploymentUrl}
                      </a>
                    </div>
                  )}
                </div>
              </TabsContent>
              <TabsContent value="logs">
                <pre className="bg-gray-100 p-4 rounded max-h-96 overflow-auto">
                  {deploymentResult?.logs?.join('\n') || 'No logs available'}
                </pre>
              </TabsContent>
              <TabsContent value="preview">
                {deploymentUrl ? (
                  <div className="border rounded h-96">
                    <iframe
                      src={deploymentUrl}
                      className="w-full h-full"
                      title="Deployment Preview"
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-96 bg-gray-100 rounded">
                    <p className="text-gray-500">No preview available</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {error && (
        <div className="mt-4 p-4 bg-red-100 text-red-800 rounded">
          {error}
        </div>
      )}
    </div>
  );
}
