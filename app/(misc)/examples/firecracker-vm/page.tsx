'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useFirecrackerVM } from '@/lib/app-dev/firecracker-service/hooks';
import { VMStatus } from '@/lib/app-dev/firecracker-service/types';

export default function FirecrackerVMExample() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('vms');
  const [commandInput, setCommandInput] = useState('');
  const [commandOutput, setCommandOutput] = useState<string | null>(null);
  const [snapshotName, setSnapshotName] = useState('');
  const [snapshotDescription, setSnapshotDescription] = useState('');
  
  const {
    vms,
    snapshots,
    selectedVM,
    loading,
    error,
    createVM,
    startVM,
    stopVM,
    deleteVM,
    executeCommand,
    createSnapshot,
    restoreSnapshot,
    selectVM,
    refreshVMs
  } = useFirecrackerVM({
    autoRefresh: true,
    refreshInterval: 5000
  });
  
  // Handle VM creation
  const handleCreateVM = async () => {
    try {
      await createVM({
        name: `vm-${Date.now()}`,
        memSizeMib: 1024,
        vcpuCount: 1,
        portMappings: {
          3000: 3000
        }
      }, {
        startAfterCreation: true
      });
      
      toast({
        title: 'VM created',
        description: 'The VM has been created and started successfully.'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to create VM: ${(error as Error).message}`,
        variant: 'destructive'
      });
    }
  };
  
  // Handle VM start
  const handleStartVM = async (vmId: string) => {
    try {
      await startVM(vmId);
      
      toast({
        title: 'VM started',
        description: 'The VM has been started successfully.'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to start VM: ${(error as Error).message}`,
        variant: 'destructive'
      });
    }
  };
  
  // Handle VM stop
  const handleStopVM = async (vmId: string) => {
    try {
      await stopVM(vmId);
      
      toast({
        title: 'VM stopped',
        description: 'The VM has been stopped successfully.'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to stop VM: ${(error as Error).message}`,
        variant: 'destructive'
      });
    }
  };
  
  // Handle VM delete
  const handleDeleteVM = async (vmId: string) => {
    try {
      await deleteVM(vmId);
      
      toast({
        title: 'VM deleted',
        description: 'The VM has been deleted successfully.'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to delete VM: ${(error as Error).message}`,
        variant: 'destructive'
      });
    }
  };
  
  // Handle command execution
  const handleExecuteCommand = async () => {
    if (!selectedVM || !commandInput) return;
    
    try {
      const result = await executeCommand(selectedVM.id, commandInput);
      
      setCommandOutput(result.success ? result.output || 'Command executed successfully' : result.error || 'Command failed');
    } catch (error) {
      setCommandOutput(`Error: ${(error as Error).message}`);
    }
  };
  
  // Handle snapshot creation
  const handleCreateSnapshot = async () => {
    if (!selectedVM || !snapshotName) return;
    
    try {
      await createSnapshot(selectedVM.id, snapshotName, snapshotDescription);
      
      toast({
        title: 'Snapshot created',
        description: 'The snapshot has been created successfully.'
      });
      
      setSnapshotName('');
      setSnapshotDescription('');
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to create snapshot: ${(error as Error).message}`,
        variant: 'destructive'
      });
    }
  };
  
  // Handle snapshot restore
  const handleRestoreSnapshot = async (snapshotId: string) => {
    if (!selectedVM) return;
    
    try {
      await restoreSnapshot(selectedVM.id, snapshotId);
      
      toast({
        title: 'Snapshot restored',
        description: 'The VM has been restored from the snapshot successfully.'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to restore snapshot: ${(error as Error).message}`,
        variant: 'destructive'
      });
    }
  };
  
  // Get status badge color
  const getStatusColor = (status?: VMStatus) => {
    if (!status) return 'bg-gray-200';
    
    switch (status) {
      case 'running':
        return 'bg-green-500';
      case 'stopped':
        return 'bg-red-500';
      case 'creating':
      case 'starting':
        return 'bg-blue-500';
      case 'stopping':
        return 'bg-yellow-500';
      case 'failed':
        return 'bg-red-700';
      default:
        return 'bg-gray-200';
    }
  };
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Firecracker VM Management</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>VM List</CardTitle>
              <CardDescription>
                Manage your Firecracker VMs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {vms.length === 0 ? (
                  <p className="text-gray-500">No VMs found</p>
                ) : (
                  vms.map(vm => (
                    <div 
                      key={vm.id} 
                      className={`p-3 border rounded cursor-pointer ${selectedVM?.id === vm.id ? 'border-blue-500 bg-blue-50' : ''}`}
                      onClick={() => selectVM(vm.id)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{vm.name}</p>
                          <p className="text-sm text-gray-500">{vm.id}</p>
                        </div>
                        <div className={`px-2 py-1 rounded text-white text-xs ${getStatusColor(vm.status)}`}>
                          {vm.status}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleCreateVM} disabled={loading}>
                Create VM
              </Button>
            </CardFooter>
          </Card>
        </div>
        
        <div className="md:col-span-2">
          {selectedVM ? (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>{selectedVM.name}</CardTitle>
                  <div className={`px-3 py-1 rounded text-white ${getStatusColor(selectedVM.status)}`}>
                    {selectedVM.status}
                  </div>
                </div>
                <CardDescription>
                  VM ID: {selectedVM.id}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="mb-4">
                    <TabsTrigger value="details">Details</TabsTrigger>
                    <TabsTrigger value="console">Console</TabsTrigger>
                    <TabsTrigger value="snapshots">Snapshots</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="details">
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium">Memory</p>
                          <p>{selectedVM.config.memSizeMib} MiB</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">vCPUs</p>
                          <p>{selectedVM.config.vcpuCount}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Created</p>
                          <p>{selectedVM.createdAt.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Updated</p>
                          <p>{selectedVM.updatedAt.toLocaleString()}</p>
                        </div>
                      </div>
                      
                      {selectedVM.metrics && (
                        <div className="mt-4">
                          <h3 className="text-lg font-medium mb-2">Metrics</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium">CPU Usage</p>
                              <p>{selectedVM.metrics.cpuUsage.toFixed(1)}%</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Memory Usage</p>
                              <p>{selectedVM.metrics.memoryUsage.toFixed(1)} MiB</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Network RX</p>
                              <p>{(selectedVM.metrics.networkRxBytes / 1024 / 1024).toFixed(2)} MB</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Network TX</p>
                              <p>{(selectedVM.metrics.networkTxBytes / 1024 / 1024).toFixed(2)} MB</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Uptime</p>
                              <p>{Math.floor(selectedVM.metrics.uptime / 3600)}h {Math.floor((selectedVM.metrics.uptime % 3600) / 60)}m</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="console">
                    <div className="space-y-4">
                      <div className="flex gap-2">
                        <input
                          type="text"
                          value={commandInput}
                          onChange={(e) => setCommandInput(e.target.value)}
                          placeholder="Enter command"
                          className="flex-1 px-3 py-2 border rounded"
                          onKeyDown={(e) => e.key === 'Enter' && handleExecuteCommand()}
                        />
                        <Button onClick={handleExecuteCommand} disabled={!commandInput || selectedVM.status !== 'running'}>
                          Execute
                        </Button>
                      </div>
                      
                      {commandOutput !== null && (
                        <div className="mt-4">
                          <h3 className="text-lg font-medium mb-2">Output</h3>
                          <pre className="bg-gray-100 p-4 rounded max-h-96 overflow-auto">
                            {commandOutput}
                          </pre>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="snapshots">
                    <div className="space-y-4">
                      <div className="grid gap-4">
                        <div>
                          <label htmlFor="snapshotName" className="block text-sm font-medium mb-1">
                            Snapshot Name
                          </label>
                          <input
                            id="snapshotName"
                            type="text"
                            value={snapshotName}
                            onChange={(e) => setSnapshotName(e.target.value)}
                            placeholder="Enter snapshot name"
                            className="w-full px-3 py-2 border rounded"
                          />
                        </div>
                        <div>
                          <label htmlFor="snapshotDescription" className="block text-sm font-medium mb-1">
                            Description (optional)
                          </label>
                          <input
                            id="snapshotDescription"
                            type="text"
                            value={snapshotDescription}
                            onChange={(e) => setSnapshotDescription(e.target.value)}
                            placeholder="Enter description"
                            className="w-full px-3 py-2 border rounded"
                          />
                        </div>
                        <Button 
                          onClick={handleCreateSnapshot} 
                          disabled={!snapshotName || selectedVM.status !== 'stopped'}
                        >
                          Create Snapshot
                        </Button>
                      </div>
                      
                      <div className="mt-4">
                        <h3 className="text-lg font-medium mb-2">Snapshots</h3>
                        <div className="space-y-2">
                          {snapshots.filter(s => s.vmId === selectedVM.id).length === 0 ? (
                            <p className="text-gray-500">No snapshots found</p>
                          ) : (
                            snapshots
                              .filter(s => s.vmId === selectedVM.id)
                              .map(snapshot => (
                                <div key={snapshot.id} className="p-3 border rounded">
                                  <div className="flex justify-between items-center">
                                    <div>
                                      <p className="font-medium">{snapshot.name}</p>
                                      <p className="text-sm text-gray-500">{snapshot.createdAt.toLocaleString()}</p>
                                      {snapshot.description && (
                                        <p className="text-sm">{snapshot.description}</p>
                                      )}
                                    </div>
                                    <Button 
                                      variant="outline" 
                                      onClick={() => handleRestoreSnapshot(snapshot.id)}
                                      disabled={selectedVM.status !== 'stopped'}
                                    >
                                      Restore
                                    </Button>
                                  </div>
                                </div>
                              ))
                          )}
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter className="flex justify-between">
                <div className="flex gap-2">
                  {selectedVM.status === 'stopped' && (
                    <Button onClick={() => handleStartVM(selectedVM.id)} disabled={loading}>
                      Start
                    </Button>
                  )}
                  {selectedVM.status === 'running' && (
                    <Button onClick={() => handleStopVM(selectedVM.id)} disabled={loading}>
                      Stop
                    </Button>
                  )}
                </div>
                <Button 
                  variant="destructive" 
                  onClick={() => handleDeleteVM(selectedVM.id)} 
                  disabled={loading}
                >
                  Delete
                </Button>
              </CardFooter>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <p className="text-gray-500">Select a VM to view details</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      
      {error && (
        <div className="mt-4 p-4 bg-red-100 text-red-800 rounded">
          {error}
        </div>
      )}
    </div>
  );
}
