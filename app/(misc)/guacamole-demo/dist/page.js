'use client';
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
exports.__esModule = true;
var react_1 = require("react");
var useNodeRunner_1 = require("@/hooks/useNodeRunner");
var vm_interface_1 = require("@/components/vm-interface");
function GuacamoleDemoPage() {
    var _this = this;
    var _a = useNodeRunner_1.useNodeRunner(), vms = _a.vms, loading = _a.loading, error = _a.error, listVMs = _a.listVMs, generateNodeApp = _a.generateNodeApp, stopNodeApp = _a.stopNodeApp, deleteNodeApp = _a.deleteNodeApp;
    var _b = react_1.useState(null), selectedVmId = _b[0], setSelectedVmId = _b[1];
    var _c = react_1.useState(null), guacamoleStatus = _c[0], setGuacamoleStatus = _c[1];
    var _d = react_1.useState(false), isSettingUp = _d[0], setIsSettingUp = _d[1];
    var _e = react_1.useState(false), isStarting = _e[0], setIsStarting = _e[1];
    var _f = react_1.useState(false), isStopping = _f[0], setIsStopping = _f[1];
    var _g = react_1.useState(null), setupError = _g[0], setSetupError = _g[1];
    // Load VMs on mount
    react_1.useEffect(function () {
        listVMs();
    }, [listVMs]);
    // Check Guacamole status when VM is selected
    react_1.useEffect(function () {
        if (selectedVmId) {
            checkGuacamoleStatus();
        }
        else {
            setGuacamoleStatus(null);
        }
    }, [selectedVmId]);
    // Check Guacamole status
    var checkGuacamoleStatus = function () { return __awaiter(_this, void 0, void 0, function () {
        var response, data, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!selectedVmId)
                        return [2 /*return*/];
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 4, , 5]);
                    return [4 /*yield*/, fetch("/api/noderunner/guacamole?vmId=" + selectedVmId)];
                case 2:
                    response = _a.sent();
                    if (!response.ok) {
                        throw new Error('Failed to check Guacamole status');
                    }
                    return [4 /*yield*/, response.json()];
                case 3:
                    data = _a.sent();
                    setGuacamoleStatus(data.status);
                    return [3 /*break*/, 5];
                case 4:
                    error_1 = _a.sent();
                    console.error('Error checking Guacamole status:', error_1);
                    setGuacamoleStatus(null);
                    return [3 /*break*/, 5];
                case 5: return [2 /*return*/];
            }
        });
    }); };
    // Setup Guacamole
    var setupGuacamole = function () { return __awaiter(_this, void 0, void 0, function () {
        var response, data, error_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!selectedVmId)
                        return [2 /*return*/];
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 6, 7, 8]);
                    setIsSettingUp(true);
                    setSetupError(null);
                    return [4 /*yield*/, fetch("/api/noderunner/guacamole?vmId=" + selectedVmId + "&action=setup", {
                            method: 'POST'
                        })];
                case 2:
                    response = _a.sent();
                    if (!!response.ok) return [3 /*break*/, 4];
                    return [4 /*yield*/, response.json()];
                case 3:
                    data = _a.sent();
                    throw new Error(data.error || 'Failed to setup Guacamole');
                case 4: return [4 /*yield*/, checkGuacamoleStatus()];
                case 5:
                    _a.sent();
                    return [3 /*break*/, 8];
                case 6:
                    error_2 = _a.sent();
                    console.error('Error setting up Guacamole:', error_2);
                    setSetupError(error_2.message);
                    return [3 /*break*/, 8];
                case 7:
                    setIsSettingUp(false);
                    return [7 /*endfinally*/];
                case 8: return [2 /*return*/];
            }
        });
    }); };
    // Start Guacamole services
    var startGuacamoleServices = function () { return __awaiter(_this, void 0, void 0, function () {
        var response, data, error_3;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!selectedVmId)
                        return [2 /*return*/];
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 6, 7, 8]);
                    setIsStarting(true);
                    setSetupError(null);
                    return [4 /*yield*/, fetch("/api/noderunner/guacamole?vmId=" + selectedVmId + "&action=start", {
                            method: 'POST'
                        })];
                case 2:
                    response = _a.sent();
                    if (!!response.ok) return [3 /*break*/, 4];
                    return [4 /*yield*/, response.json()];
                case 3:
                    data = _a.sent();
                    throw new Error(data.error || 'Failed to start Guacamole services');
                case 4: return [4 /*yield*/, checkGuacamoleStatus()];
                case 5:
                    _a.sent();
                    return [3 /*break*/, 8];
                case 6:
                    error_3 = _a.sent();
                    console.error('Error starting Guacamole services:', error_3);
                    setSetupError(error_3.message);
                    return [3 /*break*/, 8];
                case 7:
                    setIsStarting(false);
                    return [7 /*endfinally*/];
                case 8: return [2 /*return*/];
            }
        });
    }); };
    // Stop Guacamole services
    var stopGuacamoleServices = function () { return __awaiter(_this, void 0, void 0, function () {
        var response, data, error_4;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!selectedVmId)
                        return [2 /*return*/];
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 6, 7, 8]);
                    setIsStopping(true);
                    setSetupError(null);
                    return [4 /*yield*/, fetch("/api/noderunner/guacamole?vmId=" + selectedVmId + "&action=stop", {
                            method: 'POST'
                        })];
                case 2:
                    response = _a.sent();
                    if (!!response.ok) return [3 /*break*/, 4];
                    return [4 /*yield*/, response.json()];
                case 3:
                    data = _a.sent();
                    throw new Error(data.error || 'Failed to stop Guacamole services');
                case 4: return [4 /*yield*/, checkGuacamoleStatus()];
                case 5:
                    _a.sent();
                    return [3 /*break*/, 8];
                case 6:
                    error_4 = _a.sent();
                    console.error('Error stopping Guacamole services:', error_4);
                    setSetupError(error_4.message);
                    return [3 /*break*/, 8];
                case 7:
                    setIsStopping(false);
                    return [7 /*endfinally*/];
                case 8: return [2 /*return*/];
            }
        });
    }); };
    // Create a demo VM
    var createDemoVM = function () { return __awaiter(_this, void 0, void 0, function () {
        var vm, err_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    return [4 /*yield*/, generateNodeApp({
                            name: "guacamole-demo-" + Date.now(),
                            description: 'A demo VM for Guacamole',
                            template: 'express',
                            dependencies: {
                                express: '^4.18.2',
                                'cors': '^2.8.5'
                            },
                            port: 3000
                        })];
                case 1:
                    vm = _a.sent();
                    if (vm) {
                        setSelectedVmId(vm.id);
                    }
                    return [3 /*break*/, 3];
                case 2:
                    err_1 = _a.sent();
                    console.error('Failed to create VM:', err_1);
                    return [3 /*break*/, 3];
                case 3: return [2 /*return*/];
            }
        });
    }); };
    // Get the selected VM
    var selectedVM = vms.find(function (vm) { return vm.id === selectedVmId; });
    // Check if Guacamole is ready
    var isGuacamoleReady = guacamoleStatus && guacamoleStatus.guacd && guacamoleStatus.vnc && guacamoleStatus.websockify;
    return (react_1["default"].createElement("div", { className: "container mx-auto px-4 py-8" },
        react_1["default"].createElement("h1", { className: "text-3xl font-bold mb-6" }, "Guacamole Remote Desktop Demo"),
        error && (react_1["default"].createElement("div", { className: "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" },
            react_1["default"].createElement("p", null, error))),
        react_1["default"].createElement("div", { className: "grid grid-cols-1 lg:grid-cols-4 gap-6" },
            react_1["default"].createElement("div", { className: "lg:col-span-1" },
                react_1["default"].createElement("div", { className: "bg-white shadow rounded-lg p-6 mb-6" },
                    react_1["default"].createElement("h2", { className: "text-xl font-semibold mb-4" }, "Virtual Machines"),
                    loading ? (react_1["default"].createElement("div", { className: "flex justify-center items-center h-20" },
                        react_1["default"].createElement("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" }))) : vms.length === 0 ? (react_1["default"].createElement("div", { className: "text-center py-8" },
                        react_1["default"].createElement("p", { className: "text-gray-500 mb-4" }, "No virtual machines found."),
                        react_1["default"].createElement("button", { onClick: createDemoVM, className: "bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded" }, "Create Demo VM"))) : (react_1["default"].createElement("div", null,
                        react_1["default"].createElement(vm_interface_1.VMList, { vms: vms, selectedVmId: selectedVmId || '', onSelect: function (vm) { return setSelectedVmId(vm.id); } }),
                        react_1["default"].createElement("div", { className: "flex justify-between mt-4" },
                            react_1["default"].createElement("button", { onClick: listVMs, disabled: loading, className: "text-blue-600 hover:text-blue-800" }, "Refresh"),
                            react_1["default"].createElement("button", { onClick: createDemoVM, className: "bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm" }, "Create VM"))))),
                selectedVM && (react_1["default"].createElement("div", { className: "bg-white shadow rounded-lg p-6 mb-6" },
                    react_1["default"].createElement("h3", { className: "text-lg font-medium mb-3" }, "Guacamole Status"),
                    guacamoleStatus ? (react_1["default"].createElement("div", { className: "space-y-2" },
                        react_1["default"].createElement("div", { className: "flex justify-between items-center" },
                            react_1["default"].createElement("span", null, "guacd:"),
                            react_1["default"].createElement(vm_interface_1.VMStatusBadge, { status: guacamoleStatus.guacd ? 'running' : 'stopped' })),
                        react_1["default"].createElement("div", { className: "flex justify-between items-center" },
                            react_1["default"].createElement("span", null, "VNC Server:"),
                            react_1["default"].createElement(vm_interface_1.VMStatusBadge, { status: guacamoleStatus.vnc ? 'running' : 'stopped' })),
                        react_1["default"].createElement("div", { className: "flex justify-between items-center" },
                            react_1["default"].createElement("span", null, "WebSocket Proxy:"),
                            react_1["default"].createElement(vm_interface_1.VMStatusBadge, { status: guacamoleStatus.websockify ? 'running' : 'stopped' })),
                        react_1["default"].createElement("div", { className: "pt-4 space-y-2" },
                            !guacamoleStatus.guacd && (react_1["default"].createElement("button", { onClick: setupGuacamole, disabled: isSettingUp, className: "w-full bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm" }, isSettingUp ? 'Setting up...' : 'Setup Guacamole')),
                            guacamoleStatus.guacd && !isGuacamoleReady && (react_1["default"].createElement("button", { onClick: startGuacamoleServices, disabled: isStarting, className: "w-full bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm" }, isStarting ? 'Starting...' : 'Start Services')),
                            isGuacamoleReady && (react_1["default"].createElement("button", { onClick: stopGuacamoleServices, disabled: isStopping, className: "w-full bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm" }, isStopping ? 'Stopping...' : 'Stop Services')),
                            react_1["default"].createElement("button", { onClick: checkGuacamoleStatus, className: "w-full bg-gray-200 hover:bg-gray-300 text-gray-800 px-3 py-2 rounded text-sm" }, "Refresh Status")))) : (react_1["default"].createElement("div", { className: "text-center py-4" },
                        react_1["default"].createElement("p", { className: "text-gray-500" }, "Select a VM to check Guacamole status"))),
                    setupError && (react_1["default"].createElement("div", { className: "mt-4 bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded text-sm" },
                        react_1["default"].createElement("p", null, setupError))))),
                selectedVM && (react_1["default"].createElement("div", { className: "bg-white shadow rounded-lg p-6" },
                    react_1["default"].createElement("h3", { className: "text-lg font-medium mb-3" }, "VM Actions"),
                    react_1["default"].createElement(vm_interface_1.VMActionBar, { vmId: selectedVM.id, status: selectedVM.status, onStop: function () { return stopNodeApp(selectedVM.id); }, onDelete: function () {
                            if (window.confirm("Are you sure you want to delete " + (selectedVM.name || selectedVM.id) + "?")) {
                                deleteNodeApp(selectedVM.id);
                                if (selectedVmId === selectedVM.id) {
                                    setSelectedVmId(null);
                                }
                            }
                        }, loading: loading })))),
            react_1["default"].createElement("div", { className: "lg:col-span-3" }, !selectedVmId ? (react_1["default"].createElement("div", { className: "bg-white shadow rounded-lg p-12 text-center" },
                react_1["default"].createElement("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-16 w-16 mx-auto text-gray-400 mb-4", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor" },
                    react_1["default"].createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 1, d: "M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" })),
                react_1["default"].createElement("h3", { className: "text-lg font-medium text-gray-900 mb-2" }, "No VM Selected"),
                react_1["default"].createElement("p", { className: "text-gray-500" }, "Select a virtual machine from the list or create a new one to get started."))) : !isGuacamoleReady ? (react_1["default"].createElement("div", { className: "bg-white shadow rounded-lg p-12 text-center" },
                react_1["default"].createElement("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-16 w-16 mx-auto text-gray-400 mb-4", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor" },
                    react_1["default"].createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 1, d: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" }),
                    react_1["default"].createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 1, d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z" })),
                react_1["default"].createElement("h3", { className: "text-lg font-medium text-gray-900 mb-2" }, "Guacamole Not Ready"),
                react_1["default"].createElement("p", { className: "text-gray-500 mb-6" }, "Please setup and start Guacamole services to use the remote desktop."),
                !(guacamoleStatus === null || guacamoleStatus === void 0 ? void 0 : guacamoleStatus.guacd) ? (react_1["default"].createElement("button", { onClick: setupGuacamole, disabled: isSettingUp, className: "bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded" }, isSettingUp ? 'Setting up...' : 'Setup Guacamole')) : (react_1["default"].createElement("button", { onClick: startGuacamoleServices, disabled: isStarting, className: "bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded" }, isStarting ? 'Starting...' : 'Start Services')))) : (react_1["default"].createElement("div", { className: "bg-white shadow rounded-lg overflow-hidden" },
                react_1["default"].createElement("div", { className: "p-4 border-b border-gray-200" },
                    react_1["default"].createElement("h3", { className: "text-lg font-medium" }, "Guacamole Remote Desktop"),
                    react_1["default"].createElement("p", { className: "text-sm text-gray-500" },
                        "Connected to VM: ",
                        (selectedVM === null || selectedVM === void 0 ? void 0 : selectedVM.name) || selectedVmId)),
                react_1["default"].createElement(vm_interface_1.VMGuacamoleClient, { vmId: selectedVmId, protocol: "vnc", hostname: "localhost", port: 5901, password: "password", height: 600, autoConnect: true }))))),
        react_1["default"].createElement("div", { className: "bg-white shadow rounded-lg p-6 mt-8" },
            react_1["default"].createElement("h2", { className: "text-xl font-semibold mb-4" }, "Documentation"),
            react_1["default"].createElement("div", { className: "prose max-w-none" },
                react_1["default"].createElement("h3", null, "Apache Guacamole Integration"),
                react_1["default"].createElement("p", null, "This demo showcases the integration of Apache Guacamole with our VM interface. Apache Guacamole is a clientless remote desktop gateway that supports standard protocols like VNC, RDP, and SSH."),
                react_1["default"].createElement("h4", null, "How It Works"),
                react_1["default"].createElement("p", null, "The integration consists of several components:"),
                react_1["default"].createElement("ul", null,
                    react_1["default"].createElement("li", null, "A setup script that installs and configures Guacamole server (guacd) in the VM"),
                    react_1["default"].createElement("li", null, "A VNC server running in the VM to provide a graphical desktop"),
                    react_1["default"].createElement("li", null, "A WebSocket proxy to connect the browser to the VNC server"),
                    react_1["default"].createElement("li", null, "A Guacamole client component that renders the remote desktop in the browser")),
                react_1["default"].createElement("h4", null, "Getting Started"),
                react_1["default"].createElement("ol", null,
                    react_1["default"].createElement("li", null, "Select a VM from the list or create a new one"),
                    react_1["default"].createElement("li", null, "Click \"Setup Guacamole\" to install and configure Guacamole in the VM"),
                    react_1["default"].createElement("li", null, "Click \"Start Services\" to start the VNC server and WebSocket proxy"),
                    react_1["default"].createElement("li", null, "The remote desktop will appear in the main content area")),
                react_1["default"].createElement("h4", null, "Supported Protocols"),
                react_1["default"].createElement("p", null, "This implementation supports the following protocols:"),
                react_1["default"].createElement("ul", null,
                    react_1["default"].createElement("li", null, "VNC - Virtual Network Computing"),
                    react_1["default"].createElement("li", null, "RDP - Remote Desktop Protocol"),
                    react_1["default"].createElement("li", null, "SSH - Secure Shell"),
                    react_1["default"].createElement("li", null, "Telnet"))))));
}
exports["default"] = GuacamoleDemoPage;
