'use client';

import React, { useState, useEffect } from 'react';
import { useNodeRunner } from '@/hooks/useNodeRunner';
import {
  VMGuacamoleClient,
  VMStatusBadge,
  VMActionBar,
  VMList
} from '@/components/vm-interface';

export default function GuacamoleDemoPage() {
  const {
    vms,
    loading,
    error,
    listVMs,
    generateNodeApp,
    stopNodeApp,
    deleteNodeApp,
  } = useNodeRunner();

  const [selectedVmId, setSelectedVmId] = useState<string | null>(null);
  const [guacamoleStatus, setGuacamoleStatus] = useState<{ guacd: boolean; vnc: boolean; websockify: boolean } | null>(null);
  const [isSettingUp, setIsSettingUp] = useState(false);
  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [setupError, setSetupError] = useState<string | null>(null);

  // Load VMs on mount
  useEffect(() => {
    listVMs();
  }, [listVMs]);

  // Check Guacamole status when VM is selected
  useEffect(() => {
    if (selectedVmId) {
      checkGuacamoleStatus();
    } else {
      setGuacamoleStatus(null);
    }
  }, [selectedVmId]);

  // Check Guacamole status
  const checkGuacamoleStatus = async () => {
    if (!selectedVmId) return;

    try {
      const response = await fetch(`/api/noderunner/guacamole?vmId=${selectedVmId}`);

      if (!response.ok) {
        throw new Error('Failed to check Guacamole status');
      }

      const data = await response.json();
      setGuacamoleStatus(data.status);
    } catch (error) {
      console.error('Error checking Guacamole status:', error);
      setGuacamoleStatus(null);
    }
  };

  // Setup Guacamole
  const setupGuacamole = async () => {
    if (!selectedVmId) return;

    try {
      setIsSettingUp(true);
      setSetupError(null);

      const response = await fetch(`/api/noderunner/guacamole?vmId=${selectedVmId}&action=setup`, {
        method: 'POST',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to setup Guacamole');
      }

      await checkGuacamoleStatus();
    } catch (error: any) {
      console.error('Error setting up Guacamole:', error);
      setSetupError(error.message);
    } finally {
      setIsSettingUp(false);
    }
  };

  // Start Guacamole services
  const startGuacamoleServices = async () => {
    if (!selectedVmId) return;

    try {
      setIsStarting(true);
      setSetupError(null);

      const response = await fetch(`/api/noderunner/guacamole?vmId=${selectedVmId}&action=start`, {
        method: 'POST',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to start Guacamole services');
      }

      await checkGuacamoleStatus();
    } catch (error: any) {
      console.error('Error starting Guacamole services:', error);
      setSetupError(error.message);
    } finally {
      setIsStarting(false);
    }
  };

  // Stop Guacamole services
  const stopGuacamoleServices = async () => {
    if (!selectedVmId) return;

    try {
      setIsStopping(true);
      setSetupError(null);

      const response = await fetch(`/api/noderunner/guacamole?vmId=${selectedVmId}&action=stop`, {
        method: 'POST',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to stop Guacamole services');
      }

      await checkGuacamoleStatus();
    } catch (error: any) {
      console.error('Error stopping Guacamole services:', error);
      setSetupError(error.message);
    } finally {
      setIsStopping(false);
    }
  };

  // Create a demo VM
  const createDemoVM = async () => {
    try {
      const vm = await generateNodeApp({
        name: `guacamole-demo-${Date.now()}`,
        description: 'A demo VM for Guacamole',
        template: 'express',
        dependencies: {
          express: '^4.18.2',
          'cors': '^2.8.5'
        },
        port: 3000
      });

      if (vm) {
        setSelectedVmId(vm.id);
      }
    } catch (err) {
      console.error('Failed to create VM:', err);
    }
  };

  // Get the selected VM
  const selectedVM = vms.find(vm => vm.id === selectedVmId);

  // Check if Guacamole is ready
  const isGuacamoleReady = guacamoleStatus && guacamoleStatus.guacd && guacamoleStatus.vnc && guacamoleStatus.websockify;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Guacamole Remote Desktop Demo</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* VM Selection Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Virtual Machines</h2>

            {loading ? (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            ) : vms.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">No virtual machines found.</p>
                <button
                  onClick={createDemoVM}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                >
                  Create Demo VM
                </button>
              </div>
            ) : (
              <div>
                <VMList
                  vms={vms}
                  selectedVmId={selectedVmId || ''}
                  onSelect={(vm) => setSelectedVmId(vm.id)}
                />

                <div className="flex justify-between mt-4">
                  <button
                    onClick={listVMs}
                    disabled={loading}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    Refresh
                  </button>

                  <button
                    onClick={createDemoVM}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
                  >
                    Create VM
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Guacamole Status */}
          {selectedVM && (
            <div className="bg-white shadow rounded-lg p-6 mb-6">
              <h3 className="text-lg font-medium mb-3">Guacamole Status</h3>

              {guacamoleStatus ? (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span>guacd:</span>
                    <VMStatusBadge status={guacamoleStatus.guacd ? 'running' : 'stopped'} />
                  </div>
                  <div className="flex justify-between items-center">
                    <span>VNC Server:</span>
                    <VMStatusBadge status={guacamoleStatus.vnc ? 'running' : 'stopped'} />
                  </div>
                  <div className="flex justify-between items-center">
                    <span>WebSocket Proxy:</span>
                    <VMStatusBadge status={guacamoleStatus.websockify ? 'running' : 'stopped'} />
                  </div>

                  <div className="pt-4 space-y-2">
                    {!guacamoleStatus.guacd && (
                      <button
                        onClick={setupGuacamole}
                        disabled={isSettingUp}
                        className="w-full bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm"
                      >
                        {isSettingUp ? 'Setting up...' : 'Setup Guacamole'}
                      </button>
                    )}

                    {guacamoleStatus.guacd && !isGuacamoleReady && (
                      <button
                        onClick={startGuacamoleServices}
                        disabled={isStarting}
                        className="w-full bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm"
                      >
                        {isStarting ? 'Starting...' : 'Start Services'}
                      </button>
                    )}

                    {isGuacamoleReady && (
                      <button
                        onClick={stopGuacamoleServices}
                        disabled={isStopping}
                        className="w-full bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm"
                      >
                        {isStopping ? 'Stopping...' : 'Stop Services'}
                      </button>
                    )}

                    <button
                      onClick={checkGuacamoleStatus}
                      className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 px-3 py-2 rounded text-sm"
                    >
                      Refresh Status
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-500">Select a VM to check Guacamole status</p>
                </div>
              )}

              {setupError && (
                <div className="mt-4 bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded text-sm">
                  <p>{setupError}</p>
                </div>
              )}
            </div>
          )}

          {/* VM Actions */}
          {selectedVM && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium mb-3">VM Actions</h3>

              <VMActionBar
                vmId={selectedVM.id}
                status={selectedVM.status as any}
                onStop={() => stopNodeApp(selectedVM.id)}
                onDelete={() => {
                  if (window.confirm(`Are you sure you want to delete ${selectedVM.name || selectedVM.id}?`)) {
                    deleteNodeApp(selectedVM.id);
                    if (selectedVmId === selectedVM.id) {
                      setSelectedVmId(null);
                    }
                  }
                }}
                loading={loading}
              />
            </div>
          )}
        </div>

        {/* Main Content Area */}
        <div className="lg:col-span-3">
          {!selectedVmId ? (
            <div className="bg-white shadow rounded-lg p-12 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No VM Selected</h3>
              <p className="text-gray-500">
                Select a virtual machine from the list or create a new one to get started.
              </p>
            </div>
          ) : !isGuacamoleReady ? (
            <div className="bg-white shadow rounded-lg p-12 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Guacamole Not Ready</h3>
              <p className="text-gray-500 mb-6">
                Please setup and start Guacamole services to use the remote desktop.
              </p>

              {!guacamoleStatus?.guacd ? (
                <button
                  onClick={setupGuacamole}
                  disabled={isSettingUp}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
                >
                  {isSettingUp ? 'Setting up...' : 'Setup Guacamole'}
                </button>
              ) : (
                <button
                  onClick={startGuacamoleServices}
                  disabled={isStarting}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                >
                  {isStarting ? 'Starting...' : 'Start Services'}
                </button>
              )}
            </div>
          ) : (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-medium">Guacamole Remote Desktop</h3>
                <p className="text-sm text-gray-500">
                  Connected to VM: {selectedVM?.name || selectedVmId}
                </p>
              </div>

              <VMGuacamoleClient
                vmId={selectedVmId}
                protocol="vnc"
                hostname="localhost"
                port={5901}
                password="password"
                height={600}
                autoConnect={true}
              />
            </div>
          )}
        </div>
      </div>

      {/* Documentation */}
      <div className="bg-white shadow rounded-lg p-6 mt-8">
        <h2 className="text-xl font-semibold mb-4">Documentation</h2>

        <div className="prose max-w-none">
          <h3>Apache Guacamole Integration</h3>
          <p>
            This demo showcases the integration of Apache Guacamole with our VM interface.
            Apache Guacamole is a clientless remote desktop gateway that supports standard
            protocols like VNC, RDP, and SSH.
          </p>

          <h4>How It Works</h4>
          <p>
            The integration consists of several components:
          </p>
          <ul>
            <li>A setup script that installs and configures Guacamole server (guacd) in the VM</li>
            <li>A VNC server running in the VM to provide a graphical desktop</li>
            <li>A WebSocket proxy to connect the browser to the VNC server</li>
            <li>A Guacamole client component that renders the remote desktop in the browser</li>
          </ul>

          <h4>Getting Started</h4>
          <ol>
            <li>Select a VM from the list or create a new one</li>
            <li>Click "Setup Guacamole" to install and configure Guacamole in the VM</li>
            <li>Click "Start Services" to start the VNC server and WebSocket proxy</li>
            <li>The remote desktop will appear in the main content area</li>
          </ol>

          <h4>Supported Protocols</h4>
          <p>
            This implementation supports the following protocols:
          </p>
          <ul>
            <li>VNC - Virtual Network Computing</li>
            <li>RDP - Remote Desktop Protocol</li>
            <li>SSH - Secure Shell</li>
            <li>Telnet</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
