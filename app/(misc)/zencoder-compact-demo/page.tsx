"use client"

import * as React from "react"
import { useState } from "react"
import { ZencoderCompactInput } from "@/components/zencoder-compact-input"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"

export default function ZencoderCompactDemo() {
  const [isLoading, setIsLoading] = useState(false)
  
  // Handle sending a message
  const handleSendMessage = (
    content: string, 
    agentType: 'coding-agent' | 'devops-agent' | 'database-agent' | 'autonomous-programmer' | 'default',
    context: {
      type: 'codebase' | 'file' | 'folder' | 'none'
      path?: string
    }
  ) => {
    console.log('Message:', content)
    console.log('Agent:', agentType)
    console.log('Context:', context)
    
    // Simulate loading
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
    }, 1500)
  }
  
  return (
    <div className="flex flex-col min-h-screen bg-[#1A1A1A] text-white">
      <header className="border-b border-[#333333] p-4">
        <div className="container flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <a href="/">
                <ArrowLeft className="h-5 w-5" />
              </a>
            </Button>
            <h1 className="text-xl font-semibold">Zencoder Compact Input Demo</h1>
          </div>
        </div>
      </header>
      
      <main className="flex-1 container py-6">
        <div className="max-w-2xl mx-auto">
          <Card className="bg-[#1E1E1E] border-[#333333] text-white">
            <CardContent className="p-4">
              <div className="mb-4">
                <h2 className="text-lg font-semibold mb-2">Compact Chat Input</h2>
                <p className="text-sm text-[#999999]">
                  This version closely matches the UI shown in the reference image, with a compact design and pre-selected agent and context.
                </p>
              </div>
              
              <div className="border border-[#333333] rounded-md p-4 bg-[#1A1A1A]">
                <ZencoderCompactInput 
                  onSendMessage={handleSendMessage}
                  isLoading={isLoading}
                />
              </div>
              
              <div className="mt-6 space-y-4">
                <h3 className="text-sm font-medium">Features</h3>
                <ul className="text-sm text-[#999999] space-y-2">
                  <li>• Pre-selected agent and context with dropdown selection</li>
                  <li>• Dark theme matching VSCode-like interface</li>
                  <li>• Support for / and @ commands</li>
                  <li>• Keyboard shortcuts (Enter to send, Shift+Enter for new line)</li>
                  <li>• Auto-resizing textarea</li>
                  <li>• Loading state</li>
                </ul>
                
                <div className="pt-4 border-t border-[#333333]">
                  <h3 className="text-sm font-medium mb-2">Try it out</h3>
                  <p className="text-sm text-[#999999]">
                    Type a message and press Enter to send. Use / to select an agent and @ to select a context.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
