"use client"

import * as React from "react"
import { useState } from "react"
import { CodeEditorWithAgent } from "@/components/code-editor-with-agent"

// Sample recipe site files
const recipeSiteFiles = [
  {
    id: 'file1',
    name: 'app.js',
    language: 'javascript',
    content: `// Main application JavaScript for the homepage

document.addEventListener('DOMContentLoaded', function() {
  // Initialize the recipe grid
  displayRecipes(recipes);
  
  // Set up event listeners for filters
  setupFilters();
});

// Function to display recipes in the grid
function displayRecipes(recipesToShow) {
  const recipeGrid = document.getElementById('recipe-grid');
  recipeGrid.innerHTML = '';
  
  if (recipesToShow.length === 0) {
    recipeGrid.innerHTML = 
      '<div class="col-span-full text-center py-8">' +
        '<svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-orange-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">' +
          '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0.01-5.656 0M9 10h.01M15 10h.01M9 16h.01M15 16h.01M9 10h.01M15 10h.01M9 16h.01M15 16h.01M9 10h.01M15 10h.01M9 16h.01M15 16h.01M9 10h.01M15 10h.01M9 16h.01M15 16h.01" />' +
        '</svg>' +
        '<h3 class="text-xl font-bold text-gray-700 mb-2">No recipes found</h3>' +
        '<p class="text-gray-500">Try adjusting your filters or search terms</p>' +
      '</div>';
    return;
  }
  
  // Render each recipe card
  recipesToShow.forEach(recipe => {
    const recipeCard = createRecipeCard(recipe);
    recipeGrid.appendChild(recipeCard);
  });
}`,
    active: true
  },
  {
    id: 'file2',
    name: 'recipe.html',
    language: 'html',
    content: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Recipe Details</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100">
  <header class="bg-orange-500 text-white shadow-md">
    <div class="container mx-auto px-4 py-6">
      <div class="flex justify-between items-center">
        <a href="index.html" class="text-2xl font-bold">RecipeShare</a>
        <nav>
          <ul class="flex space-x-6">
            <li><a href="index.html" class="hover:text-orange-200">Home</a></li>
            <li><a href="#" class="hover:text-orange-200">Categories</a></li>
            <li><a href="#" class="hover:text-orange-200">Submit Recipe</a></li>
          </ul>
        </nav>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div id="recipe-details" class="p-6">
        <!-- Recipe content will be loaded here -->
      </div>
    </div>
  </main>

  <script src="recipes.js"></script>
  <script src="recipe-details.js"></script>
</body>
</html>`,
    active: false
  },
  {
    id: 'file3',
    name: 'index.html',
    language: 'html',
    content: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RecipeShare - Share Your Favorite Recipes</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100">
  <header class="bg-orange-500 text-white shadow-md">
    <div class="container mx-auto px-4 py-6">
      <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold">RecipeShare</h1>
        <nav>
          <ul class="flex space-x-6">
            <li><a href="#" class="hover:text-orange-200">Home</a></li>
            <li><a href="#" class="hover:text-orange-200">Categories</a></li>
            <li><a href="#" class="hover:text-orange-200">Submit Recipe</a></li>
          </ul>
        </nav>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-8">
    <div class="mb-8">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-800">Browse Recipes</h2>
        <div class="relative">
          <input type="text" id="search" placeholder="Search recipes..." class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-2.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>
      
      <div class="flex flex-wrap gap-4 mb-6">
        <button class="filter-btn active" data-category="all">All</button>
        <button class="filter-btn" data-category="breakfast">Breakfast</button>
        <button class="filter-btn" data-category="lunch">Lunch</button>
        <button class="filter-btn" data-category="dinner">Dinner</button>
        <button class="filter-btn" data-category="dessert">Dessert</button>
      </div>
    </div>

    <div id="recipe-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Recipe cards will be loaded here -->
    </div>
  </main>

  <script src="recipes.js"></script>
  <script src="app.js"></script>
</body>
</html>`,
    active: false
  },
  {
    id: 'file4',
    name: 'recipes.js',
    language: 'javascript',
    content: `// Sample recipe data
const recipes = [
  {
    id: 1,
    title: "Classic Pancakes",
    category: "breakfast",
    prepTime: 10,
    cookTime: 15,
    servings: 4,
    difficulty: "easy",
    image: "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=480&q=80",
    ingredients: [
      "1 cup all-purpose flour",
      "2 tablespoons sugar",
      "2 teaspoons baking powder",
      "1/2 teaspoon salt",
      "1 cup milk",
      "2 tablespoons melted butter",
      "1 large egg"
    ],
    instructions: [
      "In a large bowl, whisk together flour, sugar, baking powder, and salt.",
      "In another bowl, beat the milk, melted butter, and egg together.",
      "Pour the wet ingredients into the dry ingredients and stir until just combined (batter will be lumpy).",
      "Heat a lightly oiled griddle or frying pan over medium-high heat.",
      "Pour or scoop the batter onto the griddle, using approximately 1/4 cup for each pancake.",
      "Cook until bubbles form and the edges are dry, then flip and cook until browned on the other side.",
      "Serve hot with maple syrup and butter."
    ]
  },
  {
    id: 2,
    title: "Avocado Toast",
    category: "breakfast",
    prepTime: 5,
    cookTime: 5,
    servings: 1,
    difficulty: "easy",
    image: "https://images.unsplash.com/photo-1588137378633-dea1336ce1e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=480&q=80",
    ingredients: [
      "1 slice of bread (sourdough recommended)",
      "1/2 ripe avocado",
      "1/4 teaspoon salt",
      "1/4 teaspoon black pepper",
      "Red pepper flakes (optional)",
      "1 egg (optional)"
    ],
    instructions: [
      "Toast the bread until golden and firm.",
      "Remove the pit from the avocado and scoop the flesh into a bowl.",
      "Mash the avocado with a fork and season with salt and pepper.",
      "Spread the mashed avocado on top of the toast.",
      "If desired, top with a fried or poached egg.",
      "Sprinkle with red pepper flakes if you want some heat."
    ]
  }
];`,
    active: false
  },
  {
    id: 'file5',
    name: 'styles.css',
    language: 'css',
    content: `/* TailwindCSS 4.0 Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --color-primary: #f97316;
  --color-primary-dark: #ea580c;
  --color-primary-light: #fdba74;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.5;
  color: #1f2937;
}

.filter-btn {
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
}

.filter-btn:hover {
  background-color: #e5e7eb;
}

.filter-btn.active {
  background-color: var(--color-primary);
  color: white;
}`,
    active: false
  },
  {
    id: 'file6',
    name: 'recipe-details.js',
    language: 'javascript',
    content: `// Function to get URL parameters
function getUrlParameter(name) {
  name = name.replace(/[[]/, '\\[').replace(/[\\]]/, '\\]');
  const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
  const results = regex.exec(location.search);
  return results === null ? '' : decodeURIComponent(results[1].replace(/\\+/g, ' '));
}

// Function to load recipe details
function loadRecipeDetails() {
  const recipeId = parseInt(getUrlParameter('id'));
  const recipeDetails = document.getElementById('recipe-details');
  
  if (!recipeId) {
    recipeDetails.innerHTML = '<p class="text-center text-red-500">Recipe not found</p>';
    return;
  }
  
  const recipe = recipes.find(r => r.id === recipeId);
  
  if (!recipe) {
    recipeDetails.innerHTML = '<p class="text-center text-red-500">Recipe not found</p>';
    return;
  }
  
  // Create recipe detail HTML
  let html = \`
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-800 mb-2">\${recipe.title}</h1>
      <div class="flex items-center text-gray-600 mb-4">
        <span class="mr-4"><strong>Prep:</strong> \${recipe.prepTime} mins</span>
        <span class="mr-4"><strong>Cook:</strong> \${recipe.cookTime} mins</span>
        <span class="mr-4"><strong>Servings:</strong> \${recipe.servings}</span>
        <span><strong>Difficulty:</strong> \${recipe.difficulty}</span>
      </div>
      <div class="mb-6">
        <img src="\${recipe.image}" alt="\${recipe.title}" class="w-full h-64 object-cover rounded-lg">
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="md:col-span-1">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Ingredients</h2>
        <ul class="list-disc pl-5 space-y-2">
          \${recipe.ingredients.map(ingredient => \`<li>\${ingredient}</li>\`).join('')}
        </ul>
      </div>
      
      <div class="md:col-span-2">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Instructions</h2>
        <ol class="list-decimal pl-5 space-y-4">
          \${recipe.instructions.map(instruction => \`<li>\${instruction}</li>\`).join('')}
        </ol>
      </div>
    </div>
    
    <div class="mt-8 pt-6 border-t border-gray-200">
      <a href="index.html" class="inline-block bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
        &larr; Back to Recipes
      </a>
    </div>
  \`;
  
  recipeDetails.innerHTML = html;
}

// Load recipe details when the page loads
document.addEventListener('DOMContentLoaded', loadRecipeDetails);`,
    active: false
  }
]

// Sample initial messages for recipe site demo
const recipeSiteInitialMessages = [
  {
    id: '1',
    role: 'user',
    content: 'Create a modern, orange-themed recipe-sharing site with recipe listings, filtering, single-recipe views, and submission form, using Tailwindcss 4.0 and vanilla js.'
  },
  {
    id: '2',
    role: 'assistant',
    content: "I'll help you create a modern, orange-themed recipe-sharing site using Tailwind CSS 4.0 and vanilla JavaScript. Let's start by setting up the project structure and implementing the key features you mentioned: recipe listings, filtering, single-recipe views, and a submission form.\n\nFirst, let's check if we have any existing project structure to work with:"
  }
]

export default function RecipeSiteDemo() {
  return (
    <div className="h-screen w-full">
      <CodeEditorWithAgent 
        initialFiles={recipeSiteFiles}
      />
    </div>
  )
}
