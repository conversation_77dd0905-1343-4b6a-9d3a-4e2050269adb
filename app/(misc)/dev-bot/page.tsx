'use client';

import { useState, useRef, useEffect } from 'react';
import { useChat } from '@ai-sdk/react';
import { ChatMessages } from '@/components/dev-bot/ChatMessages';
import { ChatHeader } from '@/components/dev-bot/ChatHeader';
import { ChatSidebar } from '@/components/dev-bot/ChatSidebar';
import { ChatInput } from '@/components/dev-bot/ChatInput';
import { useTranscript } from '@/components/dev-bot/TranscriptContext';
import { useTools } from '@/components/dev-bot/ToolsContext';
import { toast } from 'sonner';

interface Tool {
  name: string;
  description: string;
}

export default function DevBotPage() {
  const { saveTranscript } = useTranscript();
  const { recordToolUsage } = useTools();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [selectedTools, setSelectedTools] = useState<Set<string>>(new Set());
  const [activeTool, setActiveTool] = useState<string | null>(null);
  const [availableTools, setAvailableTools] = useState<Tool[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const projectId = 'default';

  // Fetch available tools from the API
  useEffect(() => {
    const fetchTools = async () => {
      try {
        const response = await fetch('/api/dev-bot/tools');
        const data = await response.json();

        if (data.success && Array.isArray(data.tools)) {
          setAvailableTools(data.tools);
        } else {
          console.error('Failed to fetch tools:', data.error || 'Unknown error');
          toast.error('Failed to load tools');
        }
      } catch (error) {
        console.error('Error fetching tools:', error);
        toast.error('Failed to load tools');
      }
    };

    fetchTools();
  }, []);

  // Configure chat with tools
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    stop
  } = useChat({
    api: '/api/dev-bot',
    id: `dev-bot-${projectId}`,
    // @ts-ignore - The AI SDK types are not fully compatible with our implementation
    body: {
      projectId,
      selectedTools: Array.from(selectedTools),
    },
    onToolCall: async ({ toolCall }) => {
      // Update active tool state
      setActiveTool(toolCall.toolName);

      // Record tool usage for analytics
      recordToolUsage(toolCall.toolName);

      // For tools that need user interaction, just return null
      // They'll be handled by the UI
      return null;
    },
    onToolEnd: () => {
      // Clear active tool when tool execution ends
      setActiveTool(null);
    },
    onError: (error) => {
      console.error('Chat error:', error);
      toast.error('An error occurred: ' + error.message);
    },
    onFinish: (message) => {
      // Save the conversation to transcript when AI finishes responding
      try {
        // Convert message types if needed
        const allMessages = [...messages, message];
        saveTranscript(projectId, allMessages as any);
      } catch (error) {
        console.error('Error saving transcript:', error);
      }
    },
  });

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle tool selection
  const handleToolSelect = (toolName: string) => {
    setSelectedTools(prev => {
      const newSet = new Set(prev);
      if (newSet.has(toolName)) {
        newSet.delete(toolName);
      } else {
        newSet.add(toolName);
      }
      return newSet;
    });
  };

  // Handle form submission
  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!input.trim()) return;

    try {
      // Record tool usage for any selected tools
      selectedTools.forEach(tool => recordToolUsage(tool));

      // Call the AI SDK's handleSubmit
      handleSubmit(e);
    } catch (error) {
      console.error('Error submitting message:', error);
      toast.error('Failed to send message');
    }
  };

  // We could implement a handleToolResult function here if we needed
  // to handle client-side tools that required user interaction

  return (
    <div className="flex h-screen flex-col">
      <ChatHeader
        isSidebarOpen={isSidebarOpen}
        onToggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)}
      />

      <div className="flex flex-1 overflow-hidden">
        <ChatSidebar
          isOpen={isSidebarOpen}
          onClose={() => setIsSidebarOpen(false)}
          onToolSelect={handleToolSelect}
          selectedTools={selectedTools}
          projectId={projectId}
          tools={availableTools.map(tool => tool.name)}
        />

        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Chat messages */}
          <div className="flex-1 overflow-y-auto p-4">
            <ChatMessages
              messages={messages as any} // Type assertion to fix compatibility issues
              isLoading={isLoading}
              activeTool={activeTool}
              messagesEndRef={messagesEndRef as any} // Type assertion to fix compatibility issues
            />
          </div>

          {/* Chat input */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <ChatInput
              input={input}
              handleInputChange={handleInputChange}
              handleSubmit={handleFormSubmit}
              isLoading={isLoading}
              onStop={stop}
              attachedContext={null}
              onClearContext={() => {}}
              onAttachContext={() => {}}
            />

            {/* Selected tools display */}
            {selectedTools.size > 0 && (
              <div className="mt-2 flex flex-wrap gap-2">
                <span className="text-xs text-gray-500">Active tools:</span>
                {Array.from(selectedTools).map(tool => (
                  <span
                    key={tool}
                    className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full dark:bg-blue-900/30 dark:text-blue-300"
                  >
                    {tool}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
