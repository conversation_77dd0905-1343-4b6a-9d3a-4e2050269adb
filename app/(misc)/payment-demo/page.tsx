"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import { PaymentMethodSelector } from "@/components/payment"
import { useToast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"

export default function PaymentDemoPage() {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()

  const handleSuccess = () => {
    setOpen(false)
    toast({
      title: "Payment successful",
      description: "Your subscription has been activated.",
    })
  }

  const handleError = (error: string) => {
    toast({
      title: "Payment failed",
      description: error,
      variant: "destructive",
    })
  }

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-8">Payment Component Demo</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Payment Method Selector</CardTitle>
            <CardDescription>
              A modern, animated payment flow with multiple steps and payment methods
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              This component provides a complete payment flow with credit card, EFT, and Polar payment options.
              It includes form validation, animations, and success/error states.
            </p>
          </CardContent>
          <CardFooter>
            <Dialog open={open} onOpenChange={setOpen}>
              <DialogTrigger asChild>
                <Button>Open Payment Flow</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <PaymentMethodSelector 
                  plan="Pro"
                  amount="R299"
                  onSuccess={handleSuccess}
                  onError={handleError}
                  onCancel={() => setOpen(false)}
                />
              </DialogContent>
            </Dialog>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Features</CardTitle>
            <CardDescription>
              Key features of the payment component
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2">
              <li>Multi-step payment flow with smooth animations</li>
              <li>Support for multiple payment methods</li>
              <li>Form validation and error handling</li>
              <li>Success and error states with visual feedback</li>
              <li>Responsive design that works on all devices</li>
              <li>Customizable through props</li>
              <li>Built with Framer Motion for smooth transitions</li>
              <li>Follows modern UI design principles</li>
            </ul>
          </CardContent>
        </Card>
      </div>
      
      <Toaster />
    </div>
  )
}
