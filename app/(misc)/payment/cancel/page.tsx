"use client"

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { XCircle, ArrowRight, Home, CreditCard, RefreshCw } from 'lucide-react'
import Link from 'next/link'

export default function PaymentCancelPage() {
  const { data: session, status: sessionStatus } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const provider = searchParams.get('provider')
  const reason = searchParams.get('reason')
  const error = searchParams.get('error')
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/payment/cancel')
    }
  }, [sessionStatus, router])

  if (sessionStatus === 'loading') {
    return (
      <div className="container max-w-md py-10">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/2 mx-auto" />
            <Skeleton className="h-4 w-2/3 mx-auto" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container max-w-md py-10">
      <Card className="text-center">
        <CardHeader>
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-amber-100">
            <XCircle className="h-10 w-10 text-amber-600" />
          </div>
          <CardTitle className="text-2xl">Payment Cancelled</CardTitle>
          <CardDescription>
            Your payment was not completed
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertTitle>Transaction Cancelled</AlertTitle>
            <AlertDescription>
              {reason ? (
                reason
              ) : error ? (
                `Error: ${error}`
              ) : (
                'The payment process was cancelled before completion.'
              )}
            </AlertDescription>
          </Alert>
          
          <div className="rounded-lg border p-4 text-left">
            <div className="text-sm text-muted-foreground mb-2">What happened?</div>
            <p className="text-sm">
              {provider === 'payfast' && (
                'You cancelled the Payfast payment process before it was completed.'
              )}
              {provider === 'ozow' && (
                'You cancelled the Ozow Instant EFT payment process before it was completed.'
              )}
              {!provider && (
                'The payment process was cancelled before it could be completed. No charges were made to your account.'
              )}
            </p>
          </div>
          
          <div className="rounded-lg border p-4 text-left bg-muted/50">
            <div className="text-sm font-medium mb-2">What would you like to do next?</div>
            <ul className="text-sm space-y-1">
              <li>• Try the payment again</li>
              <li>• Choose a different payment method</li>
              <li>• Return to subscription plans</li>
              <li>• Contact support if you need assistance</li>
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button 
            className="w-full" 
            onClick={() => router.back()}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => router.push('/subscription/plans')}
          >
            <CreditCard className="mr-2 h-4 w-4" />
            View Subscription Plans
          </Button>
          
          <Button 
            variant="ghost" 
            className="w-full"
            asChild
          >
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Return to Home
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
