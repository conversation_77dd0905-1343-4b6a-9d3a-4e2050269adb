"use client"

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { CheckCircle, ArrowRight, Home, CreditCard } from 'lucide-react'
import { formatDate } from '@/lib/utils'
import Link from 'next/link'

export default function PaymentSuccessPage() {
  const { data: session, status: sessionStatus } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [isLoading, setIsLoading] = useState(true)
  const [paymentDetails, setPaymentDetails] = useState<any>(null)
  
  const provider = searchParams.get('provider')
  const txid = searchParams.get('txid')
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/payment/success')
    }
  }, [sessionStatus, router])

  // Fetch payment details
  useEffect(() => {
    if (sessionStatus === 'authenticated' && txid) {
      fetchPaymentDetails(txid)
    } else if (sessionStatus === 'authenticated' && !txid) {
      // If no transaction ID, use mock data
      setPaymentDetails({
        status: 'succeeded',
        provider: provider || 'unknown',
        date: new Date().toISOString(),
        subscription: {
          planName: 'Pro Plan',
          nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        }
      })
      setIsLoading(false)
    }
  }, [sessionStatus, txid, provider])

  const fetchPaymentDetails = async (transactionId: string) => {
    setIsLoading(true)
    try {
      // In a real implementation, this would fetch from your API
      // For now, we'll use mock data
      setTimeout(() => {
        setPaymentDetails({
          status: 'succeeded',
          provider: provider || 'unknown',
          date: new Date().toISOString(),
          subscription: {
            planName: 'Pro Plan',
            nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          }
        })
        setIsLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Error fetching payment details:', error)
      setIsLoading(false)
    }
  }

  if (sessionStatus === 'loading' || isLoading) {
    return (
      <div className="container max-w-md py-10">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/2 mx-auto" />
            <Skeleton className="h-4 w-2/3 mx-auto" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container max-w-md py-10">
      <Card className="text-center">
        <CardHeader>
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
          <CardTitle className="text-2xl">Payment Successful!</CardTitle>
          <CardDescription>
            Your payment has been processed successfully
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert className="bg-green-50 border-green-200">
            <AlertTitle className="text-green-800">Transaction Complete</AlertTitle>
            <AlertDescription className="text-green-700">
              {paymentDetails?.subscription ? (
                <>
                  Your subscription to {paymentDetails.subscription.planName} is now active.
                  <div className="mt-2 text-sm">
                    Next billing date: {formatDate(paymentDetails.subscription.nextBillingDate)}
                  </div>
                </>
              ) : (
                'Your payment has been processed successfully.'
              )}
            </AlertDescription>
          </Alert>
          
          <div className="rounded-lg border p-4 text-left">
            <div className="text-sm text-muted-foreground mb-2">Payment Details</div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-muted-foreground">Status:</div>
              <div className="font-medium text-green-600">Successful</div>
              
              <div className="text-muted-foreground">Date:</div>
              <div className="font-medium">{formatDate(paymentDetails?.date)}</div>
              
              <div className="text-muted-foreground">Provider:</div>
              <div className="font-medium capitalize">
                {paymentDetails?.provider === 'polar' && 'Polar Payments'}
                {paymentDetails?.provider === 'payfast' && 'Payfast'}
                {paymentDetails?.provider === 'peach' && 'Peach Payments'}
                {paymentDetails?.provider === 'ozow' && 'Ozow'}
                {!paymentDetails?.provider && 'Unknown'}
              </div>
              
              {txid && (
                <>
                  <div className="text-muted-foreground">Transaction ID:</div>
                  <div className="font-medium truncate">{txid}</div>
                </>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          {paymentDetails?.subscription ? (
            <Button 
              className="w-full" 
              onClick={() => router.push('/subscription')}
            >
              Manage Subscription
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button 
              className="w-full" 
              onClick={() => router.push('/subscription/plans')}
            >
              View Subscription Plans
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
          
          <Button 
            variant="outline" 
            className="w-full"
            asChild
          >
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Return to Home
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
