"use client"

import * as React from "react"
import { useState } from "react"
import { AgentEnhancedChat } from "@/components/agent-enhanced-chat"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  Code,
  Terminal,
  ArrowLeft,
  FileCode,
  FolderTree
} from "lucide-react"
import { AgentType } from "@/lib/stores/agent-store"

export default function AgentEnhancedChatDemo() {
  const [selectedAgentType, setSelectedAgentType] = useState<AgentType>('autonomous-programmer')
  const [generatedFiles, setGeneratedFiles] = useState<{
    filePath: string;
    content: string;
    description?: string;
  }[]>([])

  // Handle file generation
  const handleFileGenerated = (filePath: string, content: string, description?: string) => {
    setGeneratedFiles(prev => [...prev, { filePath, content, description }])
  }

  // Handle VM operation
  const handleVMOperation = (operation: string, args: any) => {
    console.log(`VM operation: ${operation}`, args)
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b p-4">
        <div className="container flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <a href="/">
                <ArrowLeft className="h-5 w-5" />
              </a>
            </Button>
            <h1 className="text-xl font-semibold">Agent Enhanced Chat Demo</h1>
          </div>
        </div>
      </header>

      <main className="flex-1 container py-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-[1fr_400px]">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Agent Selection</CardTitle>
                <CardDescription>
                  Select an agent type to see how the chat interface adapts
                </CardDescription>
                <Tabs
                  value={selectedAgentType}
                  onValueChange={(value) => setSelectedAgentType(value as AgentType)}
                  className="w-full"
                >
                  <TabsList className="grid grid-cols-2 mb-4">
                    <TabsTrigger value="autonomous-programmer" className="flex items-center gap-2">
                      <Terminal className="h-4 w-4" />
                      <span>Autonomous Programmer</span>
                    </TabsTrigger>
                    <TabsTrigger value="code-generator" className="flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      <span>Node App Generator</span>
                    </TabsTrigger>
                  </TabsList>

                  <div className="border rounded-lg p-4 mb-4">
                    <div className="flex items-center gap-3 mb-2">
                      {selectedAgentType === 'autonomous-programmer' ? (
                        <>
                          <Terminal className="h-5 w-5 text-purple-500" />
                          <h2 className="text-xl font-semibold">Autonomous Programmer</h2>
                        </>
                      ) : (
                        <>
                          <Code className="h-5 w-5 text-blue-500" />
                          <h2 className="text-xl font-semibold">Node App Generator</h2>
                        </>
                      )}
                    </div>

                    <p className="text-muted-foreground">
                      {selectedAgentType === 'autonomous-programmer'
                        ? 'The Autonomous Programmer can implement features, search your Node.js codebase, and help you manage your project autonomously.'
                        : 'The Node App Generator helps you create complete Node.js applications from natural language descriptions, generating routes, APIs, and all necessary files.'}
                    </p>
                  </div>
                </Tabs>
              </CardHeader>
              <CardContent className="p-0">
                <div className="border rounded-lg overflow-hidden h-[600px]">
                  <AgentEnhancedChat
                    initialAgentType={selectedAgentType}
                    projectId="demo-project"
                    onFileGenerated={handleFileGenerated}
                    onVMOperation={handleVMOperation}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileCode className="h-5 w-5" />
                  Generated Files
                </CardTitle>
                <CardDescription>
                  Files generated by the agent will appear here
                </CardDescription>
              </CardHeader>
              <CardContent>
                {generatedFiles.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-center border rounded-lg">
                    <FolderTree className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No files generated yet</h3>
                    <p className="text-sm text-muted-foreground max-w-md">
                      Ask the agent to generate some code and the files will appear here.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {generatedFiles.map((file, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <FileCode className="h-4 w-4 text-blue-500" />
                            <span className="font-medium">{file.filePath}</span>
                          </div>
                        </div>
                        {file.description && (
                          <p className="text-sm text-muted-foreground mb-2">{file.description}</p>
                        )}
                        <div className="bg-muted p-2 rounded-md">
                          <pre className="text-xs overflow-auto max-h-[200px]">
                            <code>{file.content.slice(0, 200)}...</code>
                          </pre>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Features</CardTitle>
                <CardDescription>
                  Key features of the enhanced agent chat
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    <span>Node.js application generation</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    <span>Express routes and API creation</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    <span>Complete project structure generation</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    <span>Autonomous feature implementation</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    <span>Context-aware code generation</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    <span>File generation visualization</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button variant="outline" onClick={() => window.history.back()} className="w-full">
                  Back to Home
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
