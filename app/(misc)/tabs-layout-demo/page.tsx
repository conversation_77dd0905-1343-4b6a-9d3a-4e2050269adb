"use client"

import { useState } from "react"
import { TabsLayoutDemo } from "@/components/ui/tabs-layout/demo"
import { AdvancedTabsLayoutDemo } from "@/components/ui/tabs-layout/advanced-demo"
import { EnhancedTabsLayoutDemo } from "@/components/ui/tabs-layout/enhanced-demo"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Toaster } from "@/components/ui/toaster"

export default function TabsLayoutDemoPage() {
  const [activeDemo, setActiveDemo] = useState<"basic" | "advanced" | "enhanced">("enhanced")

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">VS Code-like Tab and Panel Layout</h1>
      <p className="text-muted-foreground mb-8">
        A flexible tab and panel layout system inspired by VS Code. Features include draggable tabs,
        resizable panels, and VS Code-like styling.
      </p>

      <Tabs value={activeDemo} onValueChange={(value) => setActiveDemo(value as "basic" | "advanced" | "enhanced")} className="mb-8">
        <TabsList>
          <TabsTrigger value="basic">Basic Demo</TabsTrigger>
          <TabsTrigger value="advanced">Advanced Demo</TabsTrigger>
          <TabsTrigger value="enhanced">Enhanced Demo</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="mt-6">
          <TabsLayoutDemo />
        </TabsContent>

        <TabsContent value="advanced" className="mt-6">
          <AdvancedTabsLayoutDemo />
        </TabsContent>

        <TabsContent value="enhanced" className="mt-6">
          <EnhancedTabsLayoutDemo />
        </TabsContent>
      </Tabs>

      <div className="mt-8">
        <h2 className="text-xl font-bold mb-4">Features</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>Tabs with close buttons and dirty indicators</li>
          <li>Draggable tabs (between tab groups)</li>
          <li>Pinned tabs that cannot be closed or dragged</li>
          <li>Resizable panels in both horizontal and vertical directions</li>
          <li>VS Code-like styling with proper hover and active states</li>
          <li>Tab context menus with various actions (close, close others, save, pin, etc.)</li>
          <li>Maximizable panels that can be toggled between normal and maximized states</li>
          <li>Support for tab icons and custom content</li>
          <li>Responsive design that adapts to different screen sizes</li>
        </ul>
      </div>

      <Toaster />
    </div>
  )
}
