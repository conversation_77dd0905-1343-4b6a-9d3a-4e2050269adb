"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { VMPricingPlans } from "@/components/marketplace/vm-pricing-plans"
import { VMTemplateGrid } from "@/components/marketplace/vm-template-grid"
import { VMPurchaseForm } from "@/components/marketplace/vm-purchase-form"
import { useToast } from "@/components/ui/use-toast"
import {
  ShoppingCart,
  Server,
  Package,
  Cpu,
  HardDrive,
  Memory,
  RefreshCw,
  Loader2
} from "lucide-react"
import { vmService } from "@/lib/services/vm-service"

export default function MarketplacePage() {
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("templates");
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [showPurchaseForm, setShowPurchaseForm] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);
  const [pricingPlans, setPricingPlans] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch templates on component mount
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setIsLoading(true);
        const templates = await vmService.getTemplates();
        setTemplates(templates);

        // Create pricing plans from templates
        const plans = createPricingPlansFromTemplates(templates);
        setPricingPlans(plans);
      } catch (error) {
        console.error('Error fetching templates:', error);
        toast({
          title: "Error",
          description: "Failed to load VM templates. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplates();
  }, [toast]);

  // Create pricing plans from templates
  const createPricingPlansFromTemplates = (templates: any[]) => {
    // Find templates that match common configurations
    const basicTemplate = templates.find(t =>
      t.specs.cpu === 2 &&
      t.specs.memory === 4 &&
      t.specs.storage === 50
    ) || templates.find(t => t.category === 'development');

    const standardTemplate = templates.find(t =>
      t.specs.cpu === 4 &&
      t.specs.memory === 8 &&
      t.specs.storage === 100
    ) || templates.find(t => t.category === 'web');

    const premiumTemplate = templates.find(t =>
      t.specs.cpu === 8 &&
      t.specs.memory === 16 &&
      t.specs.storage >= 300
    ) || templates.find(t => t.category === 'container');

    return [
      {
        id: "basic",
        name: "Basic",
        description: "For individuals and small projects",
        price: basicTemplate?.price || 10,
        features: [
          `${basicTemplate?.specs.cpu || 2} CPU Cores`,
          `${basicTemplate?.specs.memory || 4} GB RAM`,
          `${basicTemplate?.specs.storage || 50} GB SSD Storage`,
          `${basicTemplate?.specs.bandwidth || "1 TB"} Bandwidth`,
          "24/7 Support"
        ],
        popular: false,
        templateId: basicTemplate?.id
      },
      {
        id: "standard",
        name: "Standard",
        description: "For growing applications and teams",
        price: standardTemplate?.price || 25,
        features: [
          `${standardTemplate?.specs.cpu || 4} CPU Cores`,
          `${standardTemplate?.specs.memory || 8} GB RAM`,
          `${standardTemplate?.specs.storage || 100} GB SSD Storage`,
          `${standardTemplate?.specs.bandwidth || "2 TB"} Bandwidth`,
          "24/7 Priority Support",
          "Daily Backups"
        ],
        popular: true,
        templateId: standardTemplate?.id
      },
      {
        id: "premium",
        name: "Premium",
        description: "For production workloads and enterprises",
        price: premiumTemplate?.price || 60,
        features: [
          `${premiumTemplate?.specs.cpu || 8} CPU Cores`,
          `${premiumTemplate?.specs.memory || 16} GB RAM`,
          `${premiumTemplate?.specs.storage || 300} GB SSD Storage`,
          `${premiumTemplate?.specs.bandwidth || "4 TB"} Bandwidth`,
          "24/7 Premium Support",
          "Hourly Backups",
          "High Availability",
          "Dedicated Resources"
        ],
        popular: false,
        templateId: premiumTemplate?.id
      }
    ];
  };

  const handleSelectTemplate = (template: any) => {
    setSelectedTemplate(template);
    setShowPurchaseForm(true);
  };

  const handlePurchase = async (formData: any) => {
    try {
      setIsLoading(true);

      // Prepare purchase data
      const purchaseData = {
        templateId: selectedTemplate.id,
        vmName: formData.vmName,
        region: formData.region,
        cpu: formData.cpu,
        memory: formData.memory,
        storage: formData.storage,
        operatingSystem: formData.operatingSystem,
        backups: formData.backups,
        autoStart: formData.autoStart,
        paymentMethod: formData.paymentMethod,
      };

      // Call API to purchase VM
      const result = await vmService.purchaseVM(purchaseData);

      toast({
        title: "VM Purchase Successful",
        description: `Your ${selectedTemplate.name} VM is being provisioned and will be ready shortly.`,
      });

      // Reset form and redirect to dashboard
      setShowPurchaseForm(false);
      setSelectedTemplate(null);

      // Redirect to user's VM dashboard
      router.push("/dashboard/vms");
    } catch (error: any) {
      console.error('Error purchasing VM:', error);
      toast({
        title: "Purchase Failed",
        description: error.message || "Failed to purchase VM. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelPurchase = () => {
    setShowPurchaseForm(false);
    setSelectedTemplate(null);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">VM Marketplace</h1>
          <p className="text-muted-foreground">
            Purchase and deploy virtual machines for your projects
          </p>
        </div>
        <Button variant="outline" onClick={() => router.push("/dashboard/vms")}>
          <Server className="mr-2 h-4 w-4" />
          My VMs
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-lg">Loading...</span>
        </div>
      ) : showPurchaseForm ? (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Purchase VM: {selectedTemplate.name}</CardTitle>
            <CardDescription>
              Configure your new virtual machine
            </CardDescription>
          </CardHeader>
          <CardContent>
            <VMPurchaseForm
              template={selectedTemplate}
              onSubmit={handlePurchase}
              onCancel={handleCancelPurchase}
              isLoading={isLoading}
            />
          </CardContent>
        </Card>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="templates">
              <Package className="mr-2 h-4 w-4" />
              VM Templates
            </TabsTrigger>
            <TabsTrigger value="plans">
              <ShoppingCart className="mr-2 h-4 w-4" />
              Pricing Plans
            </TabsTrigger>
          </TabsList>

          <TabsContent value="templates">
            <VMTemplateGrid
              templates={templates}
              onSelect={handleSelectTemplate}
            />
          </TabsContent>

          <TabsContent value="plans">
            <VMPricingPlans
              plans={pricingPlans}
              onSelect={handleSelectTemplate}
            />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
