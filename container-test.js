/**
 * Container Connectivity Test
 *
 * This script tests connectivity to the LXD container via SSH.
 */

const { Client } = require('ssh2');
const fs = require('fs');
const net = require('net');

// Configuration
const config = {
  container: {
    name: 'guacamole-privileged',
    ip: '*************'
  },
  ssh: {
    port: 22,
    username: 'root',
    password: 'password',
    readyTimeout: 5000
  },
  vnc: {
    port: 5901
  }
};

// Test results
const results = {
  ssh: {
    connected: false,
    error: null,
    output: null
  },
  vnc: {
    connected: false,
    error: null,
    version: null
  }
};

/**
 * Test SSH connectivity
 */
async function testSSH() {
  return new Promise((resolve, reject) => {
    console.log(`Testing SSH connectivity to ${config.container.ip}:${config.ssh.port}...`);

    const conn = new Client();

    conn.on('ready', () => {
      console.log('SSH connection established');
      results.ssh.connected = true;

      // Execute a command
      conn.exec('uname -a', (err, stream) => {
        if (err) {
          results.ssh.error = err.message;
          conn.end();
          resolve();
          return;
        }

        // Handle command output
        let output = '';

        stream.on('data', (data) => {
          output += data.toString();
        });

        stream.stderr.on('data', (data) => {
          console.error('STDERR:', data.toString());
        });

        stream.on('close', (code, signal) => {
          results.ssh.output = output;
          console.log('Command output:', output);
          console.log('Command exit code:', code);

          // Close the connection
          conn.end();
          resolve();
        });
      });
    });

    conn.on('error', (err) => {
      console.error('SSH connection error:', err.message);
      results.ssh.error = err.message;
      resolve();
    });

    // Connect to the container
    conn.connect({
      host: config.container.ip,
      port: config.ssh.port,
      username: config.ssh.username,
      password: config.ssh.password,
      readyTimeout: config.ssh.readyTimeout
    });
  });
}

/**
 * Test VNC connectivity
 */
async function testVNC() {
  return new Promise((resolve, reject) => {
    console.log(`Testing VNC connectivity to ${config.container.ip}:${config.vnc.port}...`);

    const socket = new net.Socket();
    let timeout = setTimeout(() => {
      socket.destroy();
      results.vnc.error = 'Connection timeout';
      console.error('VNC connection timeout');
      resolve();
    }, 5000);

    socket.connect(config.vnc.port, config.container.ip, () => {
      console.log(`Connected to VNC server at ${config.container.ip}:${config.vnc.port}`);
      results.vnc.connected = true;

      // Wait for the RFB handshake
      socket.once('data', (data) => {
        clearTimeout(timeout);
        const version = data.toString('utf8', 0, 12);
        results.vnc.version = version;
        console.log(`VNC Server Version: ${version}`);

        // Close the connection
        socket.end();
      });
    });

    socket.on('error', (err) => {
      clearTimeout(timeout);
      console.error('VNC connection error:', err.message);
      results.vnc.error = err.message;
      resolve();
    });

    socket.on('close', () => {
      clearTimeout(timeout);
      console.log('VNC connection closed');
      resolve();
    });
  });
}

/**
 * Setup VNC server via SSH
 */
async function setupVNC() {
  return new Promise((resolve, reject) => {
    console.log('Setting up VNC server via SSH...');

    const conn = new Client();

    conn.on('ready', () => {
      console.log('SSH connection established for VNC setup');

      // Execute commands to set up VNC one by one
      const setupVNCServer = async () => {
        const execCommand = (cmd) => {
          return new Promise((resolve, reject) => {
            console.log(`Executing: ${cmd}`);
            conn.exec(cmd, (err, stream) => {
              if (err) {
                console.error(`Failed to execute command: ${cmd}`, err.message);
                resolve(false);
                return;
              }

              let output = '';
              let stderr = '';

              stream.on('data', (data) => {
                output += data.toString();
              });

              stream.stderr.on('data', (data) => {
                stderr += data.toString();
              });

              stream.on('close', (code, signal) => {
                console.log(`Command: ${cmd}`);
                console.log(`Output: ${output}`);
                if (stderr) {
                  console.error(`Error: ${stderr}`);
                }
                console.log(`Exit code: ${code}`);
                resolve(code === 0);
              });
            });
          });
        };

        // Start Xvfb
        await execCommand('Xvfb :1 -screen 0 1024x768x16 &');
        await execCommand('sleep 1');

        // Start window manager
        await execCommand('DISPLAY=:1 openbox &');
        await execCommand('sleep 1');

        // Start VNC server
        await execCommand('x11vnc -display :1 -forever -shared -rfbport 5901 -passwd password &');
        await execCommand('sleep 1');

        // Check if VNC server is running
        return await execCommand('ps aux | grep x11vnc | grep -v grep');
      };

      // Run the setup
      setupVNCServer().then(success => {
        console.log('VNC setup success:', success);
        conn.end();
        resolve(success);
      }).catch(err => {
        console.error('Error during VNC setup:', err);
        conn.end();
        resolve(false);
      });
    });

    conn.on('error', (err) => {
      console.error('SSH connection error during VNC setup:', err.message);
      resolve(false);
    });

    // Connect to the container
    conn.connect({
      host: config.container.ip,
      port: config.ssh.port,
      username: config.ssh.username,
      password: config.ssh.password,
      readyTimeout: config.ssh.readyTimeout
    });
  });
}

/**
 * Run all tests
 */
async function runTests() {
  try {
    // Test SSH connectivity
    await testSSH();

    // If SSH is working, set up VNC
    if (results.ssh.connected) {
      const vncSetupSuccess = await setupVNC();
      console.log('VNC setup success:', vncSetupSuccess);

      // Test VNC connectivity
      if (vncSetupSuccess) {
        await testVNC();
      }
    }

    // Print results
    console.log('\nTest Results:');
    console.log('=============');
    console.log('SSH:');
    console.log(`  Connected: ${results.ssh.connected}`);
    if (results.ssh.error) {
      console.log(`  Error: ${results.ssh.error}`);
    }
    if (results.ssh.output) {
      console.log(`  Output: ${results.ssh.output}`);
    }

    console.log('VNC:');
    console.log(`  Connected: ${results.vnc.connected}`);
    if (results.vnc.error) {
      console.log(`  Error: ${results.vnc.error}`);
    }
    if (results.vnc.version) {
      console.log(`  Version: ${results.vnc.version}`);
    }

    // Overall result
    const success = results.ssh.connected && results.vnc.connected;
    console.log('\nOverall Result:');
    console.log(`  Success: ${success}`);

    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the tests
runTests();
