/**
 * Comprehensive LXD Container Test
 * 
 * This script tests both SSH connectivity and LXD REST API.
 */

const { Client } = require('ssh2');
const fs = require('fs');
const https = require('https');
const path = require('path');

// Configuration
const config = {
  container: {
    name: 'guacamole-privileged',
    ip: '*************'
  },
  ssh: {
    port: 22,
    username: 'root',
    password: 'password',
    readyTimeout: 5000
  },
  lxd: {
    apiUrl: 'https://localhost:8443',
    clientCertPath: path.join(process.env.HOME, '.config', 'lxd', 'client.crt'),
    clientKeyPath: path.join(process.env.HOME, '.config', 'lxd', 'client.key'),
    verifySSL: false
  }
};

// Test results
const results = {
  ssh: {
    connected: false,
    commandExecuted: false,
    error: null
  },
  lxd: {
    connected: false,
    instancesListed: false,
    instanceInfoRetrieved: false,
    error: null
  }
};

/**
 * Test SSH connectivity
 */
async function testSSH() {
  return new Promise((resolve, reject) => {
    console.log(`Testing SSH connectivity to ${config.container.ip}:${config.ssh.port}...`);
    
    const conn = new Client();
    
    conn.on('ready', () => {
      console.log('✅ SSH connection established');
      results.ssh.connected = true;
      
      // Execute a command
      conn.exec('uname -a', (err, stream) => {
        if (err) {
          console.error('❌ Failed to execute command:', err.message);
          results.ssh.error = err.message;
          conn.end();
          resolve();
          return;
        }
        
        // Handle command output
        let output = '';
        
        stream.on('data', (data) => {
          output += data.toString();
        });
        
        stream.stderr.on('data', (data) => {
          console.error('STDERR:', data.toString());
        });
        
        stream.on('close', (code, signal) => {
          console.log('Command output:', output);
          console.log('Command exit code:', code);
          
          if (code === 0) {
            console.log('✅ Command executed successfully');
            results.ssh.commandExecuted = true;
          } else {
            console.error('❌ Command failed with exit code:', code);
            results.ssh.error = `Command failed with exit code: ${code}`;
          }
          
          // Close the connection
          conn.end();
          resolve();
        });
      });
    });
    
    conn.on('error', (err) => {
      console.error('❌ SSH connection error:', err.message);
      results.ssh.error = err.message;
      resolve();
    });
    
    // Connect to the container
    conn.connect({
      host: config.container.ip,
      port: config.ssh.port,
      username: config.ssh.username,
      password: config.ssh.password,
      readyTimeout: config.ssh.readyTimeout
    });
  });
}

/**
 * Make a request to the LXD API
 */
async function lxdRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, config.lxd.apiUrl);
    
    const httpsAgent = new https.Agent({
      rejectUnauthorized: config.lxd.verifySSL,
      cert: fs.readFileSync(config.lxd.clientCertPath),
      key: fs.readFileSync(config.lxd.clientKeyPath)
    });
    
    const options = {
      method,
      agent: httpsAgent,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = https.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * Test LXD REST API
 */
async function testLXD() {
  try {
    console.log('\nTesting LXD REST API...');
    
    // Test 1: Get server information
    console.log('Test 1: Get server information');
    const serverInfo = await lxdRequest('GET', '/1.0');
    console.log(`✅ Server: ${serverInfo.metadata.environment.server} ${serverInfo.metadata.environment.server_version}`);
    console.log(`✅ Authentication: ${serverInfo.metadata.auth}`);
    results.lxd.connected = true;
    
    // Test 2: List instances
    console.log('\nTest 2: List instances');
    const instances = await lxdRequest('GET', '/1.0/instances');
    console.log(`✅ Found ${instances.metadata.length} instances`);
    results.lxd.instancesListed = true;
    
    // Test 3: Get instance information
    console.log('\nTest 3: Get instance information');
    const instanceInfo = await lxdRequest('GET', `/1.0/instances/${config.container.name}`);
    console.log(`✅ Instance type: ${instanceInfo.metadata.type}`);
    console.log(`✅ Instance status: ${instanceInfo.metadata.status}`);
    results.lxd.instanceInfoRetrieved = true;
  } catch (error) {
    console.error('❌ LXD API error:', error.message);
    results.lxd.error = error.message;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  try {
    // Test SSH connectivity
    await testSSH();
    
    // Test LXD REST API
    await testLXD();
    
    // Print results
    console.log('\nTest Results:');
    console.log('=============');
    console.log('SSH:');
    console.log(`  Connected: ${results.ssh.connected}`);
    console.log(`  Command Executed: ${results.ssh.commandExecuted}`);
    if (results.ssh.error) {
      console.log(`  Error: ${results.ssh.error}`);
    }
    
    console.log('LXD REST API:');
    console.log(`  Connected: ${results.lxd.connected}`);
    console.log(`  Instances Listed: ${results.lxd.instancesListed}`);
    console.log(`  Instance Info Retrieved: ${results.lxd.instanceInfoRetrieved}`);
    if (results.lxd.error) {
      console.log(`  Error: ${results.lxd.error}`);
    }
    
    // Overall result
    const success = results.ssh.connected && results.ssh.commandExecuted && 
                   results.lxd.connected && results.lxd.instancesListed && results.lxd.instanceInfoRetrieved;
    
    console.log('\nOverall Result:');
    if (success) {
      console.log('✅ All tests passed successfully!');
      process.exit(0);
    } else {
      console.log('❌ Some tests failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the tests
runTests();
